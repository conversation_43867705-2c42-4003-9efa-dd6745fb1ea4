﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Exchange", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ExchangeHealth : BaseEntity
    {
        #region Properties

        [DataMember]
        public int StorageGroupId { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string StorageGroupPR { get; set; }

        [DataMember]
        public string MailboxDatabasePR { get; set; }

        [DataMember]
        public string MailboxDatabaseStatusPR { get; set; }

        [DataMember]
        public string EdbFilePathPR { get; set; }

        [DataMember]
        public string LatestAvailableLogTimePR { get; set; }

        [DataMember]
        public string LastgeneratedLogSequencePR { get; set; }

        [DataMember]
        public string StorageGroupDR { get; set; }

        [DataMember]
        public string MailboxDatabaseDR { get; set; }

        [DataMember]
        public string MailboxDatabaseStatusDR { get; set; }

        [DataMember]
        public string EdbFilePathDR { get; set; }

        [DataMember]
        public string LastCopiedLogTimeDR { get; set; }

        [DataMember]
        public string LastInspectedLogTimeDR { get; set; }

        [DataMember]
        public string LastReplayedLogTimeDR { get; set; }

        [DataMember]
        public string LastCopiedLogSequence { get; set; }

        [DataMember]
        public string LastInspectedLogSequence { get; set; }

        [DataMember]
        public string LastReplayedLogSequence { get; set; }

        [DataMember]
        public string PREdbFileSize { get; set; }

        [DataMember]
        public string PREdbFileLastWriteTime { get; set; }

        [DataMember]
        public string DREdbFileSize { get; set; }

        [DataMember]
        public string DREdbFileLastWriteTime { get; set; }

        [DataMember]
        public string DataLagTime { get; set; }

        //[DataMember]
        //public string ApplicationName { get; set; }

        [DataMember]
        public string InfraObjectName { get; set; }

        #endregion Properties
    }
}