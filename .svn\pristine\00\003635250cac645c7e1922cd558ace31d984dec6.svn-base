﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;

namespace CP.UI
{
    public abstract class SettingBasePage : BasePage
    {
        private int _SettingId = 0;
        private IList<Settings> _setting = null;

        #region Properties

        protected int CurrentSettingId
        {
            get
            {
                if (_SettingId == 0)
                {
                    if (Helper.Url.SecureUrl[Constants.UrlConstants.Params.EmailId].IsNotNullOrEmpty())
                    {
                        _SettingId = Helper.Url.SecureUrl[Constants.UrlConstants.Params.SettingId].ToInteger();
                    }
                }
                return _SettingId;
            }
            set
            {
                _SettingId = value;
            }
        }

        protected IList<Settings> CurrentSetting
        {
            get
            {
                if (_setting == null)
                {
                    //if (CurrentSettingId > 0)
                    //{
                        _setting = Facade.GetAllSetting();
                //    }
                    if (_setting == null)
                    {
                        _setting = null;
                    }
                }
                return _setting;
            }
            set
            {
                _setting = value;
            }
        }

        #endregion

    }
}