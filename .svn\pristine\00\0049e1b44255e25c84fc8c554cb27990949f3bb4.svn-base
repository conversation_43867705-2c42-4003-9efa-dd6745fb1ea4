﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="CompanyProfileConfiguration.aspx.cs" Inherits="CP.UI.CompanyProfileConfiguration"
    Title="Continuity Patrol ::Profile-CompanyProfileConfiguration" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="ajaxToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script type="text/javascript">
       
        function Check(textBox, maxLength) {
            if (textBox.value.length > maxLength) {
                alert("Max characters allowed are " + maxLength);
                textBox.value = textBox.value.substr(0, maxLength);
            }
        }
        var uploadError = false;
        var errorMessage;
        function onUploadError(sender, args) {
         
            document.getElementById('<%=lblStatus1.ClientID%>').value = args.get_fileName();
    var imgname = document.getElementById('<%=lblStatus1.ClientID%>').value;
    var fields = imgname.split('.')
    var checkType = fields[1];
debugger
    if (checkType == "gif" || checkType == "PNG" || checkType == "png" || checkType == "jpg" || checkType == "jpeg" || checkType == "JPG" && checkType != "html") {
      
        var theControl1 = document.getElementById("<%=Button1.ClientID%>");
    theControl1.style.display = "inline-block";
    var obj = document.getElementById("<%=lblAmount.ClientID %>");
obj.style.display = 'none';

var obj1 = document.getElementById("<%=lblStatus.ClientID %>");
obj1.style.display = 'none';
}
else {
  
    var theControl = document.getElementById("<%= Button1.ClientID %>");
    theControl.style.display = "none";
    var obj = document.getElementById("<%=lblAmount.ClientID %>");
obj.style.display = 'inline-block';

var amount = document.getElementById("<%=lblAmount.ClientID %>").innerText;
amount = "Only Image file allowed";
  
document.getElementById("<%=lblAmount.ClientID%>").innerHTML = amount;

var obj1 = document.getElementById("<%=lblStatus.ClientID %>");
    obj1.style.display = 'none';
}
}

    </script>

    <style type="text/css">
        #ctl00_cphBody_AsyncUpload > div {
        margin-top:-2px !important;
        }
        .fileuploadbg input[type="text"] {
        border:0px !important
        }
    </style>
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <asp:HiddenField ID="Hiddenval" runat="server" />
    <div class="innerLR">
     
        <h3><span class="profile-company-icon"></span>
            Profile Configuration</h3>
     
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-xs-12 form-horizontal uniformjs">
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Company Name <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:TextBox ID="txtName" runat="server" class="form-control" TabIndex="1" AutoPostBack="True"
                                    autocomplete="off" OnTextChanged="TxtNameTextChanged" onChange="javascript:Check(this, 100);" />
                                <a title=" a-z A-Z 0-9 * Only & _ @ .-special characters allowed."
                                    class="error">
                                    <img class="vertical-align" src="../Images/icons/icon-tooltipQ.png"></a>
                                <asp:RequiredFieldValidator ID="rfvName" CssClass="error" runat="server" ErrorMessage="Enter Company Name"
                                    Display="Dynamic" ControlToValidate="txtName"></asp:RequiredFieldValidator>
                               
                                <asp:RegularExpressionValidator ID="regexp" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtName" ErrorMessage="Invalid Characters"
                                    ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"></asp:RegularExpressionValidator>
                               
                                <asp:Label ID="lblerror" runat="server" Visible="false" ForeColor="Red"></asp:Label>
                                <asp:Label ID="lblCompanyName" runat="server" ForeColor="Red" Text=""></asp:Label>
                                <asp:CheckBox ID="ckbIsparent" runat="server" Text="" OnCheckedChanged="OnCheckChangeStatus"
                                    AutoPostBack="True" /><label style="vertical-align: text-top;">Is Parent</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Display Name <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:TextBox ID="txtDescription" runat="server" TabIndex="2" class="form-control" onChange="javascript:Check(this, 100);"
                                    Placeholder="Like Ptechno,IBM,..etc" AutoPostBack="True" OnTextChanged="TxtDescriptionTextChanged"></asp:TextBox>
                                <asp:Label ID="lblCompName" runat="server" ForeColor="Red"></asp:Label>
                                <asp:Label ID="Label1" runat="server" ForeColor="Red" Text="Like Ptechno,IBM,..etc"
                                    Visible="false"></asp:Label>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator4" CssClass="error" runat="server" ErrorMessage="Enter Display Name"
                                    Display="Dynamic" ControlToValidate="txtDescription"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revDisplayName" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtDescription" ErrorMessage="Invalid Characters"
                                    ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>
                            </div>
                        </div>
                        <div class="form-group" id="divcmpid" runat="server">
                            <label class="col-xs-3 control-label">
                                Company Id <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:TextBox ID="txtCompanyId" runat="server"  placeholder="Company Id" class="form-control" TabIndex="3"></asp:TextBox>                           
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator5" CssClass="error" runat="server" ErrorMessage="Enter Company Id"
                                    Display="Dynamic" ControlToValidate="txtCompanyId"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Parent Company Name <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:DropDownList ID="ddlCustomer" runat="server" TabIndex="3" CssClass="selectpicker col-md-6"
                                    data-style="btn-default" Style="margin-right: -15px;">
                                    <asp:ListItem Value="0">Select Company Name</asp:ListItem>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator3" CssClass="error" runat="server" InitialValue="0"
                                    ControlToValidate="ddlCustomer" ErrorMessage="Select Company Name" Display="Dynamic"></asp:RequiredFieldValidator>
                                <asp:CustomValidator ID="cvParentCompany" CssClass="error" ErrorMessage="Select Company Name" ValidateEmptyText="True"
                                    ControlToValidate="ddlCustomer" OnServerValidate="CompanyServerValidate" Display="Dynamic"
                                    runat="server"></asp:CustomValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Address <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:TextBox ID="txtAddress" runat="server" CssClass="form-control" Width="48%" TextMode="MultiLine"
                                    TabIndex="4"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvPhone2" CssClass="error" ControlToValidate="txtAddress" runat="server"
                                    Display="Dynamic" ErrorMessage="Enter Address"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="RegularExpressionValidatortxtAddress" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtAddress" ErrorMessage="Enter Valid Address"
                                    ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"></asp:RegularExpressionValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Phone <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:TextBox ID="txtCountryCode" runat="server" class="form-control" MaxLength="5"
                                    placeholder="+91" Width="7%"></asp:TextBox>
                                <asp:TextBox ID="txtStateCode" AutoCompleteType="Disabled" class="form-control" runat="server"
                                    Width="10%" MaxLength="4" TabIndex="5"></asp:TextBox>
                                <asp:TextBox ID="txtPhone" runat="server" MaxLength="10" class="form-control" AutoCompleteType="Disabled"
                                    Width="30.2%" TabIndex="6"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvCountry2" runat="server" CssClass="error" ControlToValidate="txtCountryCode" Style="text-wrap: normal;"
                                    Display="Dynamic" ErrorMessage="Enter Country Code"></asp:RequiredFieldValidator>
                                <asp:RequiredFieldValidator ID="rfvState2" runat="server" CssClass="error" ControlToValidate="txtStateCode" Style="text-wrap: normal;"
                                    Display="Dynamic" ErrorMessage="Enter State Code"></asp:RequiredFieldValidator>
                                <asp:RequiredFieldValidator ID="rfvPhone1" CssClass="error" ControlToValidate="txtPhone" runat="server" Style="text-wrap: normal;"
                                    Display="Dynamic" ErrorMessage="Enter Phone Number"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revState" CssClass="error" Display="Dynamic" runat="server" ControlToValidate="txtStateCode" Style="text-wrap: normal;"
                                    ErrorMessage="Valid State Code is required." ValidationExpression="[0-9]{2,4}"></asp:RegularExpressionValidator>
                                <asp:RegularExpressionValidator ID="revPhone1" CssClass="error" runat="server" ControlToValidate="txtPhone" Style="text-wrap: normal;"
                                    Display="Dynamic" ErrorMessage="Enter a valid phone number"
                                    ValidationExpression="[0-9]{4,10}"></asp:RegularExpressionValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Fax <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:TextBox ID="txtCountryCode2" runat="server" class="form-control" MaxLength="5"
                                    placeholder="+91" Width="7%"></asp:TextBox>
                                <asp:TextBox ID="txtStateCode2" runat="server" class="form-control" Width="10%" MaxLength="4"
                                    TabIndex="7"></asp:TextBox>
                                <asp:TextBox ID="txtFax" runat="server" TabIndex="8" class="form-control" Width="30.2%"
                                    MaxLength="10"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvCountry" runat="server" CssClass="error" ControlToValidate="txtCountryCode2" Style="text-wrap: normal;"
                                    Display="Dynamic" ErrorMessage="Enter Country Code"></asp:RequiredFieldValidator>
                                <asp:RequiredFieldValidator ID="rfvState" CssClass="error" runat="server" ControlToValidate="txtStateCode2"
                                    Display="Dynamic" ErrorMessage="Enter State Code"></asp:RequiredFieldValidator>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" CssClass="error" ControlToValidate="txtFax"
                                    Display="Dynamic" runat="server" ErrorMessage="Enter Fax Number"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="RegularExpressionValidator1" CssClass="error" Display="Dynamic"
                                    runat="server" ControlToValidate="txtStateCode2" ErrorMessage="Valid State Code is required."
                                    ValidationExpression="[0-9]{2,4}"></asp:RegularExpressionValidator>
                                <asp:RegularExpressionValidator ID="RegularExpressionValidator2" CssClass="error" runat="server" ControlToValidate="txtFax"
                                    Display="Dynamic" ErrorMessage="Enter a valid fax number"
                                    ValidationExpression="[0-9]{4,10}"></asp:RegularExpressionValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Email <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:TextBox ID="txtEmail" runat="server" class="form-control" TabIndex="9" AutoCompleteType="Disabled"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvEmail" CssClass="error" ControlToValidate="txtEmail" runat="server"
                                    Display="Dynamic" ErrorMessage="Enter Email Address"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revEmail" CssClass="error" runat="server" ControlToValidate="txtEmail"
                                    ErrorMessage="Valid email address is required." ValidationExpression="\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
                                    Display="Dynamic"></asp:RegularExpressionValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Web Address <span class="inactive">*</span></label>
                            <div class="col-xs-9">
                                <asp:TextBox ID="txtWebsite" runat="server" class="form-control" TabIndex="10"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator2" CssClass="error" runat="server" ControlToValidate="txtWebsite"
                                    Display="Dynamic" ErrorMessage="Enter WebSite"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="RegularExpressionValidator3" CssClass="error" runat="server" ControlToValidate="txtWebsite"
                                    ErrorMessage="Valid website is required." ValidationExpression="([\w-]+\.)+[\w-]+(/[\w- ./?%&amp;=]*)?"
                                    Display="Dynamic"></asp:RegularExpressionValidator>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-xs-3 control-label">
                                Upload Company Logo</label>
                            <div class="col-xs-9">
                                <ajaxToolkit:AsyncFileUpload ID="AsyncUpload" runat="server" OnClientUploadError="onUploadError"
                                    CompleteBackColor="#70a3cf" UploadingBackColor="#70a3cf" BackColor="white"
                                    ThrobberID="Image1" UploaderStyle="Modern" ErrorBackColor="#70a3cf" CssClass="fileuploadbg" BorderWidth="1"
                                    BorderColor="#CCCCCC" />
                                <asp:Label ID="lblAmount" runat="server" Visible="true" ForeColor="Red" />
                             
                                <asp:Label ID="lblStatus1" runat="server" Style="font-family: Arial; font-size: small;"></asp:Label>
                                <asp:Button ID="Button1" runat="server" CssClass="btn btn-primary" Text="Upload File" CausesValidation="false" OnClick="BtnUploadClick" />
                                <asp:Button runat="server" ID="btn" Visible="false" Text="button" />
                                <asp:Label ID="lblStatus" runat="server" Visible="false" ForeColor="green"></asp:Label>
                                <asp:Label ID="lblErr" runat="server" Visible="false" ForeColor="red"></asp:Label>
                                <asp:Image ID="Image1" ImageUrl="../Images/running-animate.GIF" runat="server" />
                                
                            </div>
                        </div>
                    </div>
                </div>
                <hr class="separator" />
                <div class="clearfix"></div>
                <div class="form-actions row">
                    <div class="col-xs-5">
                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required Fields</span>
                        <asp:Label ID="lblupdatestatus" runat="server" Text=""></asp:Label>
                    </div>
                    <div class="col-xs-7">
                        <asp:Button ID="btnSaveProfile" CssClass="btn btn-primary" Width="15%" runat="server" Style="margin-left:9px;" OnClientClick="httpGet();"
                            Text="Save" TabIndex="11" OnClick="BtnSaveProfileClick"></asp:Button>
                        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server"
                            Text="Cancel" CausesValidation="False" TabIndex="12" OnClick="BtnCancelClick" />
                    </div>
                </div>
            </div>
        </div>
        <!--Form End -->
        <asp:HiddenField ID="hdffilepath" runat="server" />
        <script type="text/javascript">
            $(document).ready(function () {
                $('[id$=txtName]').live("blur", function () {
                    var version = jQuery.trim($('[id$=txtName]').val());
                    $.ajax({
                        type: "POST",
                        url: "CompanyProfileConfiguration.aspx/CheckProfileName",
                        data: "{'version':'" + version + "'}",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        async: true,
                        success: function (data) {
                            populateCompanyName(data.d);
                        }
                    });
                });
                function populateCompanyName(msg) {
                    if (msg) {
                        $('[id$=lblCompanyName]').html("Company Name is Exist");
                    } else {
                        $('[id$=lblCompanyName]').html("");
                    }
                }
            });
        </script>
        
    </div>
</asp:Content>
