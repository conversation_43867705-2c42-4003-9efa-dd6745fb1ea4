﻿using System;
using System.Configuration;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Text.RegularExpressions;
using System.Web.Configuration;
using log4net;
using log4net.Repository.Hierarchy;
using log4net.Core;
using log4net.Appender;
using log4net.Layout;
using System.Collections.Specialized;
using System.Xml;
using System.Web.Security;
using System.IO;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Diagnostics;
using System.Collections.Generic;
using System.Web;
using System.Net;


namespace CP.UI.Admin
{
    public partial class AdminSetting : SettingBasePage
    {
        private string key = "file";
        public Settings CurrentEntity;
        public string pwdpolicy;
        public string IPAddress;

        CP.UI.Admin.WorkflowApprovalProcess _workflowapprovalproess = new Admin.WorkflowApprovalProcess();

        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }


            if (HttpContext.Current.Session["LoggedInUserRole"].ToString() == "EnterpriseUser")
            {
                privillage.Disabled = false;
                privillage1.Disabled = false;
            }
            else
            {
                privillage.Disabled = true;
                privillage1.Disabled = true;
            }

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 


            Utility.SelectMenu(Master, "Module8");
            log4net.Repository.ILoggerRepository repo = LogManager.GetRepository();
            var file = repo.GetAppenders();
            var logfilename = ((log4net.Appender.FileAppender)(file[0])).File;

            txtLogFile.Text = logfilename;

            CurrentEntity = new Settings();
            txtPwdKey.Attributes.Add("readonly", "readonly");
            txtPasswordAgeKey.Attributes.Add("readonly", "readonly");
            txtDBRoleNameKey.Attributes.Add("readonly", "readonly");
            txtReportPathKey.Attributes.Add("readonly", "readonly");

            if (CurrentSetting.Count != 0)
            {
                foreach (var settingkey in CurrentSetting)
                {
                    if (settingkey.Key == "PwdKey")
                    {
                        btnGenrateKey.Text = "Update";
                        BinddValues(settingkey);
                    }
                    if (settingkey.Key == "PwdAge")
                    {
                        btnPasswordAge.Text = "Update";
                        BinddValues(settingkey);
                    }
                    if (settingkey.Key == "DBRoleName")
                    {
                        btnDBRole.Text = "Update";
                        BinddValues(settingkey);
                    }
                    if (settingkey.Key == "SessionKey")
                    {
                        btnTimeOut.Text = "Update";
                        BinddValues(settingkey);
                    }
                    if (settingkey.Key == "ReportPath")
                    {
                        btnPath.Text = "Update";
                        BinddValues(settingkey);
                    }
                    if (settingkey.Key == "LogfileKey")
                    {
                        btnLogFile.Text = "Update";
                        BinddValues(settingkey);
                    }
                    if (settingkey.Key == "Approval")
                    {
                        if (HttpContext.Current.Session["LoggedInUserRole"].ToString() == "EnterpriseUser")
                        {
                            chkapproval.Enabled = true;
                            btnApproval.Enabled = true;
                            btnApproval.Enabled = true;
                        }
                        else
                        {
                            chkapproval.Enabled = false;
                            btnApproval.Enabled = false;
                            btnApproval.Enabled = false;
                        }
                        btnApproval.Text = "Update";
                        BinddValues(settingkey);
                    }
                    if (settingkey.Key == "OTP")
                    {
                        if (HttpContext.Current.Session["LoggedInUserRole"].ToString() == "EnterpriseUser")
                        {
                            CheckBox1.Enabled = true;
                            btnOTP.Enabled = true;
                        }
                        else
                        {
                            CheckBox1.Enabled = false;
                            btnOTP.Enabled = false;
                        }
                        btnOTP.Text = "Update";
                        BinddValues(settingkey);
                    }
                }
            }
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

        }

        //public override void PrepareEditView()
        //{
        //    //  BindControlValues();

        //    //  btnsave_newuser.Text = "Update";

        //}

        //public override void BuildEntities()
        //{
        //    //string pwdpolicy = GenratePasswordPolicy();
        //    //CurrentEntity.Key = txtPwdKey.Text;
        //    //CurrentEntity.Value =CryptographyHelper.Md5Encrypt(pwdpolicy);

        //}

        //public override void SaveEditor()
        //{
        //    //if (CurrentEntity.IsNew)
        //    //{
        //    //    CurrentEntity = Facade.AddSetting(CurrentEntity);
        //    //    lblpath.Visible = true;
        //    //    lblpath.Text = "Save successfully";

        //    //    //  ActivityLogger.AddLog(LoggedInUser.LoginName, UserActionType.CreateAlertManager, "The Setting '" + CurrentEntity.Value + "' was Added to the Setting table", CurrentEntityId);
        //    //}
        //    //else
        //    //{
        //    //    CurrentEntity.UpdatorId = LoggedInUserId;
        //    //     CurrentEntity = Facade.UpdateSetting(CurrentEntity);
        //    //     lblpath.Visible = true;
        //    //     lblpath.Text = "Update successfully";
        //    //    //  ActivityLogger.AddLog(LoggedInUser.LoginName, UserActionType.UpdateAlertManager, "The Setting '" + CurrentEntity.Value + "' was Updated to the Setting table", CurrentEntityId);
        //    //}

        //}

        #region Bind Password policy
        private void PopulatePwdExp()
        {
            //var getValue = ConfigurationSettings.AppSettings["PwdKey"].ToString();

            //string[] expPwd = getValue.Split(')');

            //if (expPwd[0] == "")
            //{
            //    // img123.Visible = false;
            //}
            //else
            //{

            //    string min = expPwd[0];
            //    string[] minchar = min.Split('{');
            //    string[] minchar1 = min.Split(',');
            //    string lastchars = minchar1[1];

            //    string[] maxcharc = lastchars.Split('}');
            //    string maxchar = maxcharc[0];
            //    string minchars = minchar[1];
            //    string[] minch = minchars.Split(',');
            //    string minnum = minch[0];
            //    string number = expPwd[2];
            //    string characters = expPwd[5];
            //    string lower = expPwd[3];
            //    string[] lowertext = lower.Split(']');
            //    string smallchar = lowertext[1];
            //    string specchar = expPwd[8];

            //    txtMin.Text = minnum;
            //    txtMax.Text = maxchar;
            //    txtUpper.Text = characters.Trim(new Char[] { ' ', '{', '}' });
            //    txtSmall.Text = smallchar.Trim(new Char[] { ' ', '{', '}' }); ;
            //    txtNum.Text = number.Trim(new Char[] { ' ', '{', '}' }); ;
            //    txtSpecial.Text = specchar.Trim(new Char[] { ' ', '{', '}' }); ;
            //}
        }
        #endregion

        protected void btnGetPathClick(object sender, EventArgs e)
        {
            try
            {
                if ((ViewState["_token"] != null) && ValidateRequest("AdminSetting", UserActionType.CreateAdminSetting))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {

                        CurrentEntity = new Settings();
                        //if (Page.IsValid)
                        //{
                        var submitButton = (Button)sender;
                        var buttionText = " " + submitButton.Text.ToLower() + " ";


                        CurrentEntity.Key = txtReportPathKey.Text;
                        CurrentEntity.Value = txtReportPath.Text;

                        if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                        {
                            CurrentEntity = Facade.AddSetting(CurrentEntity);
                            lblpath.Visible = true;
                            lblpath.Text = "Save successfully";
                            SuccessMsg.Visible = true;
                        }
                        else if (buttionText.Contains(" update "))
                        {
                            CurrentEntity = Facade.UpdateSetting(CurrentEntity);
                            lblpath.Visible = true;
                            lblpath.Text = "update successfully";
                            SuccessMsg.Visible = true;
                        }
                        //  }

                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        protected void btnLogPathClick(object sender, EventArgs e)
        {
            try
            {
                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("AdminSetting", UserActionType.CreateAdminSetting))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {

                        Configuration webConfigApp = WebConfigurationManager.OpenWebConfiguration("~");

                        webConfigApp.AppSettings.Settings["ReportPath"].Value = txtLogFile.Text;
                        webConfigApp.Save();

                        System.Xml.XmlDocument x = new System.Xml.XmlDocument();
                        x.Load(Server.MapPath("~/Web.Config"));
                        System.Xml.XmlNode node = x.SelectSingleNode("/configuration/log4net/appender/file");
                        node.Attributes["value"].Value = webConfigApp.AppSettings.Settings["ReportPath"].Value;
                        x.Save(Server.MapPath("~/Web.Config"));
                        lbllogFile.Visible = true;
                        lbllogFile.Text = "Path changed successfully";
                        logsuccess.Visible = true;


                        var process = new Process
                        {
                            StartInfo =
                            {
                                Verb = "runas",
                                WindowStyle = ProcessWindowStyle.Hidden,
                                FileName = @"cmd.exe",
                                Arguments = "/C iisreset"
                            }
                        };
                        process.Start();
                    }
                }
            }
            catch (Exception ex)
            {

            }

            //try
            //{
            //    CurrentEntity = new Settings();
            //    //if (Page.IsValid)
            //    //{
            //        var submitButton = (Button)sender;
            //        var buttionText = " " + submitButton.Text.ToLower() + " ";


            //        CurrentEntity.Key ="LogfileKey";
            //        CurrentEntity.Value = txtLogFile.Text;

            //        if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            //        {
            //            CurrentEntity = Facade.AddSetting(CurrentEntity);
            //            lbllogFile.Visible = true;
            //            lbllogFile.Text = "Save successfully";
            //            Panel5.Visible = true;
            //        }
            //        else if (buttionText.Contains(" update "))
            //        {
            //            CurrentEntity = Facade.UpdateSetting(CurrentEntity);
            //            lbllogFile.Visible = true;
            //            lbllogFile.Text = "update successfully";
            //            Panel5.Visible = true;
            //        }
            // //   }


            //}
            //catch (Exception ex)
            //{

            //}
        }

        protected void btnDBRoleClick(object sender, EventArgs e)
        {
            try
            {
                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("AdminSetting", UserActionType.CreateAdminSetting))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {

                        CurrentEntity = new Settings();
                        if (Page.IsValid)
                        {
                            var submitButton = (Button)sender;
                            var buttionText = " " + submitButton.Text.ToLower() + " ";


                            CurrentEntity.Key = txtDBRoleNameKey.Text;
                            CurrentEntity.Value = txtDBRoleName.Text;

                            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                            {
                                CurrentEntity = Facade.AddSetting(CurrentEntity);
                                lblSucess.Visible = true;
                                lblSucess.Text = "Save successfully";
                                Panel1.Visible = true;
                            }
                            else if (buttionText.Contains(" update "))
                            {
                                CurrentEntity = Facade.UpdateSetting(CurrentEntity);
                                lblSucess.Visible = true;
                                lblSucess.Text = "update successfully";
                                Panel1.Visible = true;
                            }
                        }

                        //rfvDbase.ValidationGroup = btnDBRole.ValidationGroup;
                        //rfvTime.ValidationGroup = "";

                        //Configuration webConfigApp = WebConfigurationManager.OpenWebConfiguration("~");
                        //webConfigApp.AppSettings.Settings["DBRoleName"].Value = txtDBRoleName.Text + ".";

                        //webConfigApp.Save();

                        //lblSucess.Visible = true;
                        //lblSucess.Text = "Database changed successfully";
                        //Panel1.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        protected void btnPasswordAgeClick(object sender, EventArgs e)
        {
            try
            {
                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("AdminSetting", UserActionType.CreateAdminSetting))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {

                        CurrentEntity = new Settings();
                        if (Page.IsValid)
                        {
                            var submitButton = (Button)sender;
                            var buttionText = " " + submitButton.Text.ToLower() + " ";


                            CurrentEntity.Key = txtPasswordAgeKey.Text;
                            CurrentEntity.Value = txtPasswordAge.Text;

                            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                            {
                                CurrentEntity = Facade.AddSetting(CurrentEntity);
                                lblpwdAge.Visible = true;
                                lblpwdAge.Text = "save successfully";
                                Panel3.Visible = true;
                            }
                            else if (buttionText.Contains(" update "))
                            {
                                CurrentEntity = Facade.UpdateSetting(CurrentEntity);
                                lblpwdAge.Visible = true;
                                lblpwdAge.Text = "update successfully";
                                Panel3.Visible = true;
                            }

                            // BinddValues(CurrentEntity);

                        }


                        //Configuration webConfigApp = WebConfigurationManager.OpenWebConfiguration("~");
                        //webConfigApp.AppSettings.Settings["PwdAge"].Value = txtPasswordAge.Text;                
                        //webConfigApp.Save();

                        //lblpwdAge.Visible = true;
                        //lblpwdAge.Text = "Passowrd age changed successfully";
                        //Panel3.Visible = true;
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        protected void btnTimeOutClick(object sender, EventArgs e)
        {
            try
            {
                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("AdminSetting", UserActionType.CreateAdminSetting))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {
                        CurrentEntity = new Settings();
                        if (Page.IsValid)
                        {
                            var submitButton = (Button)sender;
                            var buttionText = " " + submitButton.Text.ToLower() + " ";


                            CurrentEntity.Key = "SessionKey";
                            CurrentEntity.Value = txtTimeOut.Text;

                            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                            {
                                CurrentEntity = Facade.AddSetting(CurrentEntity);
                                lblTime.Visible = true;
                                lblTime.Text = "Session Time Out Time Saved Successfully";
                                Panel4.Visible = true;
                            }
                            else if (buttionText.Contains(" update "))
                            {
                                CurrentEntity = Facade.UpdateSetting(CurrentEntity);
                                lblTime.Visible = true;
                                lblTime.Text = "Session Time Out Time updated Successfully";
                                Panel4.Visible = true;
                            }
                        }

                        //rfvTime.ValidationGroup = btnTimeOut.ValidationGroup;
                        //rfvDbase.ValidationGroup = "";

                        //Configuration configuration;
                        //AuthenticationSection section;

                        //configuration = WebConfigurationManager.OpenWebConfiguration("~");
                        //section = (AuthenticationSection)configuration.GetSection("system.web/authentication");

                        //if (section != null)
                        //{
                        //    section.Forms.Timeout = new TimeSpan(0, Convert.ToInt32(txtTimeOut.Text), 0);
                        //    configuration.Save();

                        //    lblTime.Visible = true;
                        //    lblTime.Text = "TimeOut changed successfully";
                        //    Panel4.Visible = true;
                        //}
                    }
                }
            }
            catch (Exception ex)
            {

            }
        }

        protected string GenratePasswordPolicy()
        {
            Int32 minvalue = Convert.ToInt32(txtMin.Text);
            Int32 maxvalue = Convert.ToInt32(txtMax.Text);
            Int32 count = (Convert.ToInt32(txtUpper.Text) + Convert.ToInt32(txtSmall.Text) + Convert.ToInt32(txtSpecial.Text) + Convert.ToInt32(txtNum.Text));
            string appendExpr = "";
            if (count >= minvalue && count <= maxvalue)
            {
                appendExpr = "(?=^.{" + txtMin.Text + "," + txtMax.Text + "}$)";

                if (txtNum.Text.Length != 0)
                {
                    appendExpr = appendExpr + "(?=(?:.*?\\d){" + txtNum.Text + "})";
                }
                if (txtSmall.Text.Length != 0)
                {
                    appendExpr = appendExpr + "(?=.*[a-z]{" + txtSmall.Text + "})";
                }
                if (txtUpper.Text.Length != 0)
                {
                    appendExpr = appendExpr + "(?=(?:.*?[A-Z]){" + txtUpper.Text + "})";
                }
                if (txtSpecial.Text.Length != 0)
                {
                    appendExpr = appendExpr + "(?=(?:.*?[!@#$%*()_+^&}{:;?.]){" + txtSpecial.Text + "})";
                }

                appendExpr = appendExpr + "(?!.*\\s)[0-9a-zA-Z!@#$%*()_+^&]*$";
            }
            else
            {
                lblerrmsg.Visible = true;
                lblerrmsg.Text = "Minimum " + txtMin.Text + " characters required";
            }
            return appendExpr;
        }

        private void BinddValues(Settings sett)
        {
            if (sett.Key == "PwdKey")
            {
                txtPwdKey.Text = sett.Key;
                string pwd = CryptographyHelper.Md5Decrypt(sett.Value);
                string[] expPwd = pwd.Split(')');

                string min = expPwd[0];
                string[] minchar = min.Split('{');
                string[] minchar1 = min.Split(',');
                string lastchars = minchar1[1];

                string[] maxcharc = lastchars.Split('}');
                string maxchar = maxcharc[0];
                string minchars = minchar[1];
                string[] minch = minchars.Split(',');
                string minnum = minch[0];
                string number = expPwd[2];
                string characters = expPwd[5];
                string lower = expPwd[3];
                string[] lowertext = lower.Split(']');
                string smallchar = lowertext[1];
                string specchar = expPwd[8];

                txtMin.Text = minnum;
                txtMax.Text = maxchar;
                txtUpper.Text = characters.Trim(new Char[] { ' ', '{', '}' });
                txtSmall.Text = smallchar.Trim(new Char[] { ' ', '{', '}' }); ;
                txtNum.Text = number.Trim(new Char[] { ' ', '{', '}' }); ;
                txtSpecial.Text = specchar.Trim(new Char[] { ' ', '{', '}' }); ;
            }
            if (sett.Key == "PwdAge")
            {
                txtPasswordAgeKey.Text = sett.Key;
                txtPasswordAge.Text = sett.Value;
            }
            if (sett.Key == "DBRoleName")
            {
                txtDBRoleNameKey.Text = sett.Key;
                txtDBRoleName.Text = sett.Value;
            }
            if (sett.Key == "ReportPath")
            {
                txtReportPathKey.Text = sett.Key;
                txtReportPath.Text = sett.Value;
            }
            if (sett.Key == "LogfileKey")
            {
                // txtReportPathKey.Text = sett.Key;
                txtLogFile.Text = sett.Value;
            }
            if (sett.Key == "SessionKey")
            {
                // txtReportPathKey.Text = sett.Key;
                txtTimeOut.Text = sett.Value;
            }
            if (sett.Key == "Approval")
            {
                if (sett.Value == "1")
                    chkapproval.Checked = true;
            }
            if (sett.Key == "OTP")
            {
                if (sett.Value == "1")
                    CheckBox1.Checked = true;
            }
        }

        protected void btnGenrateKeyClick(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("AdminSetting", UserActionType.CreateAdminSetting))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {

                    CurrentEntity = new Settings();
                    if (Page.IsValid)
                    {
                        var submitButton = (Button)sender;
                        var buttionText = " " + submitButton.Text.ToLower() + " ";
                        pwdpolicy = GenratePasswordPolicy();
                        if (pwdpolicy == "")
                        {
                            Panel2.Visible = false;
                            lblpwdPolicay.Visible = false;
                        }
                        else
                        {
                            CurrentEntity.Key = txtPwdKey.Text;
                            CurrentEntity.Value = CryptographyHelper.Md5Encrypt(pwdpolicy);

                            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                            {
                                CurrentEntity = Facade.AddSetting(CurrentEntity);
                                lblpwdPolicay.Visible = true;
                                lblpwdPolicay.Text = "Saved successfully";
                                lblerrmsg.Visible = false;
                                Panel2.Visible = true;
                            }
                            else if (buttionText.Contains(" update "))
                            {

                                CurrentEntity = Facade.UpdateSetting(CurrentEntity);
                                lblpwdPolicay.Visible = true;
                                lblpwdPolicay.Text = "Update successfully";
                                lblerrmsg.Visible = false;
                                Panel2.Visible = true;
                            }
                        }

                        //  BinddValues(CurrentEntity);

                    }

                    //BuildEntities();
                    //StartTransaction();
                    //SaveEditor();
                    //EndTransaction();



                    //Int32 minvalue = Convert.ToInt32(txtMin.Text);
                    //Int32 maxvalue = Convert.ToInt32(txtMax.Text);
                    //Int32 count = (Convert.ToInt32(txtUpper.Text) + Convert.ToInt32(txtSmall.Text) + Convert.ToInt32(txtSpecial.Text) + Convert.ToInt32(txtNum.Text));
                    //string appendExpr = "";
                    //if (count >= minvalue && count <= maxvalue)
                    //{

                    //    appendExpr = "(?=^.{" + txtMin.Text + "," + txtMax.Text + "}$)";

                    //    if (txtNum.Text.Length != 0)
                    //    {
                    //        appendExpr = appendExpr + "(?=(?:.*?\\d){" + txtNum.Text + "})";
                    //    }
                    //    if (txtSmall.Text.Length != 0)
                    //    {
                    //        appendExpr = appendExpr + "(?=.*[a-z]{" + txtSmall.Text + "})";
                    //    }
                    //    if (txtUpper.Text.Length != 0)
                    //    {
                    //        appendExpr = appendExpr + "(?=(?:.*?[A-Z]){" + txtUpper.Text + "})";
                    //    }
                    //    if (txtSpecial.Text.Length != 0)
                    //    {
                    //        appendExpr = appendExpr + "(?=(?:.*?[!@#$%*()_+^&}{:;?.]){" + txtSpecial.Text + "})";
                    //    }

                    //    appendExpr = appendExpr + "(?!.*\\s)[0-9a-zA-Z!@#$%*()_+^&]*$";

                    //    Configuration webConfigApp = WebConfigurationManager.OpenWebConfiguration("~");
                    //    webConfigApp.AppSettings.Settings["PwdKey"].Value = appendExpr;

                    //    webConfigApp.Save();

                    //    lblpwdPolicay.Visible = true;
                    //    lblErr.Visible = false;
                    //    lblpwdPolicay.Text = "Password expression changed successfully";
                    //    Panel2.Visible = true;

                    //}
                    //else
                    //{
                    //    lblerrmsg.Visible = true;
                    //    lblerrmsg.Text = "enter minimum length";
                    //}
                }
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                IgnoreIDs.Add("txtDBRoleName");
                //IgnoreIDs.Add("txtCountryCode2");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        protected void chkapproval_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                if (chkapproval.Checked)
                {

                }
            }
            catch (Exception ex)
            {
                throw (ex);
            }

        }

        protected void btnApproval_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("AdminSetting", UserActionType.CreateAdminSetting))
            {


                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;
                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");
                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);
                    Helper.Url.Redirect(returnUrl1);
                }
                else
                {
                    CurrentEntity = new Settings();
                    if (Page.IsValid)
                    {
                        var submitButton = (Button)sender;
                        var buttionText = " " + submitButton.Text.ToLower() + " ";

                        CurrentEntity.Key = "Approval";
                        CurrentEntity.Value = chkapproval.Checked ? "1" : "0";
                        if (CurrentEntity.Value == "1")
                        {
                            ActivityLogger.AddLog1(LoggedInUserName, " User Click On Four Eye Profile Approval", UserActionType.ChnageFourEye, " Enabled the CP Profile Approval", LoggedInUserId, IPAddress);
                        }
                        else
                        {
                            ActivityLogger.AddLog1(LoggedInUserName, " User Click On Four Eye Profile Approval", UserActionType.ChnageFourEye, " Disabled the CP Profile Approval ", LoggedInUserId, IPAddress);
                        }

                        if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                        {
                            CurrentEntity = Facade.AddSetting(CurrentEntity);
                            lblsuccesscheckappl.Visible = true;
                            lblsuccesscheckappl.Text = "Saved successfully";
                            lblerrorcheckapp.Visible = false;
                            Panel5.Visible = true;
                            ActivityLogger.AddLog1(LoggedInUserName, " User Saved Four Eye Profile Approval", UserActionType.ChnageFourEye, " CP Profile Approval '" + lblsuccesscheckappl.Text + "'", LoggedInUserId, IPAddress);
                        }
                        else if (buttionText.Contains(" update "))
                        {

                            CurrentEntity = Facade.UpdateSetting(CurrentEntity);
                            lblsuccesscheckappl.Visible = true;
                            lblsuccesscheckappl.Text = "Updated successfully";
                            lblerrorcheckapp.Visible = false;
                            Panel5.Visible = true;
                            string _status = string.Empty;
                            if (CurrentEntity.Value == "1")
                                _status = "Enabled";
                            else
                                _status = "Disabled";

                            string message = GetMailbody();
                            string Subject = "Four Eye Principle  : " + _status;

                            string defaultusers = Convert.ToString(ConfigurationManager.AppSettings["NotificationEmail"]);
                            if (!string.IsNullOrEmpty(defaultusers))
                            {
                                string[] _notificationusers = defaultusers.Split('$');
                                string sendmails = string.Empty;

                                if (_notificationusers != null)
                                {
                                    for (int i = 0; i < _notificationusers.Count(); i++)
                                    {
                                        sendmails = sendmails + _notificationusers[i] + ",";
                                    }

                                    _workflowapprovalproess.SendEmails(sendmails.TrimEnd(), message, Subject);


                                }
                            }

                            ActivityLogger.AddLog1(LoggedInUserName, " User " + _status + " Four Eye Profile Approval", UserActionType.ChnageFourEye, " CP Profile Approval '" + lblsuccesscheckappl.Text + "'", LoggedInUserId, IPAddress);
                        }

                    }
                }
            }
        }


        private string GetMailbody()
        {
            string mailbody = string.Empty;
            int currentUser = Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]);
            UserInfo UserInfo = new UserInfo();

            UserInfo = Facade.GetUserInfoById(currentUser);
            try
            {
                string status = string.Empty;
                if (CurrentEntity.Value == "1")
                    status = "Enabled";
                else
                    status = "Disabled";


                string _mailHeader_new = @"<html><head><title>CP</title></head><body>" + "<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"background-color:#f4f7f9;font-family:Verdana;\">" +
                           "<tbody><tr>" + "<td align=\"center\" valign=\"middle\">" + "<table width=\"800\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">" +
                           "<tbody><tr>" + "<td style=\"background: rgb(255, 255, 255); padding: 0px 30px 15px 30px;\" valign=\"top\">" + "<table><tbody>" +
                           "<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\"> Dear Team,</td>" + "</tr>" +
                           "<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\"> User " + UserInfo.UserName + " " + status + " the Four Eye principle." + "</tr>" + "<tr>";


                string _mailFooter = "</td></tr><tr><td style=\"background: rgb(255, 255, 255); \" valign=\"top\">" + "<table><tbody><tr>" +
            "<td style=\"color: rgb(51, 51, 51);padding: 0px 0px 5px;font-size:13px;\">Thank You,</td>" + "</tr><tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px; font-weight: 600;\">CP</td>" +
            "</tr></tbody></table>" + "</td></tr>" + "</tbody></table>" + "</td>" + "</tr>" + "</body></html>";

                mailbody = _mailHeader_new + _mailFooter;

            }
            catch (Exception ex)
            {
                // _logger.Error("Exception occurred in GetMailbody " + ex.Message);
            }
            return mailbody;
        }


        private string GetMailbody_OTP()
        {
            string mailbody = string.Empty;
            int currentUser = Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]);
            UserInfo UserInfo = new UserInfo();

            UserInfo = Facade.GetUserInfoById(currentUser);
            try
            {
                string status = string.Empty;
                if (CurrentEntity.Value == "1")
                    status = "Enabled";
                else
                    status = "Disabled";


                string _mailHeader_new = @"<html><head><title>CP</title></head><body>" + "<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" style=\"background-color:#f4f7f9;font-family:Verdana;\">" +
                           "<tbody><tr>" + "<td align=\"center\" valign=\"middle\">" + "<table width=\"800\" border=\"0\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\">" +
                           "<tbody><tr>" + "<td style=\"background: rgb(255, 255, 255); padding: 0px 30px 15px 30px;\" valign=\"top\">" + "<table><tbody>" +
                           "<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\"> Dear Team,</td>" + "</tr>" +
                           "<tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px\"> User " + UserInfo.UserName + " " + status + " the OTP Functionality." + "</tr>" + "<tr>";


                string _mailFooter = "</td></tr><tr><td style=\"background: rgb(255, 255, 255); \" valign=\"top\">" + "<table><tbody><tr>" +
            "<td style=\"color: rgb(51, 51, 51);padding: 0px 0px 5px;font-size:13px;\">Thank You,</td>" + "</tr><tr>" + "<td style=\"color: rgb(51, 51, 51); padding: 0px 0px 20px; font-size:13px; font-weight: 600;\">CP</td>" +
            "</tr></tbody></table>" + "</td></tr>" + "</tbody></table>" + "</td>" + "</tr>" + "</body></html>";

                mailbody = _mailHeader_new + _mailFooter;

            }
            catch (Exception ex)
            {
                // _logger.Error("Exception occurred in GetMailbody " + ex.Message);
            }
            return mailbody;
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("AdminSetting", UserActionType.CreateAdminSetting))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;
                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");
                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);
                    Helper.Url.Redirect(returnUrl1);
                }
                else
                {
                    CurrentEntity = new Settings();
                    if (Page.IsValid)
                    {
                        var submitButton = (Button)sender;
                        var buttionText = " " + submitButton.Text.ToLower() + " ";

                        CurrentEntity.Key = "OTP";
                        CurrentEntity.Value = CheckBox1.Checked ? "1" : "0";

                        string _status = string.Empty;

                        if (CurrentEntity.Value == "1")
                        {
                            _status = " Enabled";
                            ActivityLogger.AddLog1(LoggedInUserName, " User Click on Four Eye CP OTP", UserActionType.ChnageFourEye, " Enabled the CP OTP", LoggedInUserId, IPAddress);
                        }
                        else
                        {
                            _status = " Disbaled";
                            ActivityLogger.AddLog1(LoggedInUserName, " User Click on Four Eye CP OTP", UserActionType.ChnageFourEye, " Disabled the CP OTP", LoggedInUserId, IPAddress);
                        }

                        if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                        {
                            CurrentEntity = Facade.AddSetting(CurrentEntity);
                            Label1.Visible = true;
                            Label1.Text = "Saved successfully";
                            Label2.Visible = false;
                            Panel6.Visible = true;
                            ActivityLogger.AddLog1(LoggedInUserName, " User Save Four Eye CP OTP", UserActionType.ChnageFourEye, " CP OTP " + Label1.Text + "", LoggedInUserId, IPAddress);
                        }
                        else if (buttionText.Contains(" update "))
                        {

                            CurrentEntity = Facade.UpdateSetting(CurrentEntity);
                            Label1.Visible = true;
                            Label1.Text = "Updated successfully";
                            Label2.Visible = false;
                            Panel6.Visible = true;


                            string message = GetMailbody_OTP();
                            string Subject = "Four Eye Principle  : " + _status;

                            string defaultusers = Convert.ToString(ConfigurationManager.AppSettings["NotificationEmail"]);
                            if (!string.IsNullOrEmpty(defaultusers))
                            {
                                string[] _notificationusers = defaultusers.Split('$');
                                string sendmails = string.Empty;

                                if (_notificationusers != null)
                                {
                                    for (int i = 0; i < _notificationusers.Count(); i++)
                                    {
                                        sendmails = sendmails + _notificationusers[i] + ",";
                                    }

                                    _workflowapprovalproess.SendEmails(sendmails.TrimEnd(), message, Subject);


                                }
                            }
                            ActivityLogger.AddLog1(LoggedInUserName, " User Update Four Eye CP OTP", UserActionType.ChnageFourEye, "CP OTP " + Label1.Text + "", LoggedInUserId, IPAddress);
                        }

                    }
                }
            }
        }

        protected void CheckBox1_CheckedChanged(object sender, EventArgs e)
        {

        }
    }
}