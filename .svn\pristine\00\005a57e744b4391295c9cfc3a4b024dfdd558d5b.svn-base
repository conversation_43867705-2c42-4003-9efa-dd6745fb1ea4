﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class JobTypeRepTypeBuilder : IEntityBuilder<JobTypeReplicationType>
    {
        IList<JobTypeReplicationType> IEntityBuilder<JobTypeReplicationType>.BuildEntities(IDataReader reader)
        {
            var jobtypereptype = new List<JobTypeReplicationType>();

            while (reader.Read())
            {
                jobtypereptype.Add(((IEntityBuilder<JobTypeReplicationType>)this).BuildEntity(reader,
                    new JobTypeReplicationType()));
            }

            return (jobtypereptype.Count > 0) ? jobtypereptype : null;
        }

        JobTypeReplicationType IEntityBuilder<JobTypeReplicationType>.BuildEntity(IDataReader reader,
            JobTypeReplicationType jobtypereptype)
        {
            //const int FLD_ID = 0;
            //const int FLD_JOBID = 1;
            //const int FLD_REPLICATIONID = 2;
            ////const int FLD_ISACTIVE = 3;
            ////const int FLD_CREATORID = 4;
            //const int FLD_CREATEDATE = 3;
            ////const int FLD_UPDATORID = 6;
            ////const int FLD_UPDATEDATE = 7;

            //jobtypereptype.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //jobtypereptype.JobTypeId = reader.IsDBNull(FLD_JOBID) ? 0 : reader.GetInt32(FLD_JOBID);
            //jobtypereptype.ReplicationId = reader.IsDBNull(FLD_REPLICATIONID) ? 0 : reader.GetInt32(FLD_REPLICATIONID);
            ////jobtypereptype.IsActive = !reader.IsDBNull(FLD_ISACTIVE) && reader.GetBoolean(FLD_ISACTIVE);
            ////jobtypereptype.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //jobtypereptype.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);
            ////jobtypereptype.UpdatorId = reader.IsDBNull(FLD_UPDATORID) ? 0 : reader.GetInt32(FLD_UPDATORID);
            ////jobtypereptype.UpdateDate = reader.IsDBNull(FLD_UPDATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_UPDATEDATE);

            //Fields in bcms_jobtype_replicationtype table on 22/07/2013 :Id, JobTypeId, ReplicationTypeId, CreateDate

            jobtypereptype.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            jobtypereptype.JobTypeId = Convert.IsDBNull(reader["JobTypeId"]) ? 0 : Convert.ToInt32(reader["JobTypeId"]);
            jobtypereptype.ReplicationId = Convert.IsDBNull(reader["ReplicationTypeId"])
                ? 0
                : Convert.ToInt32(reader["ReplicationTypeId"]);
            jobtypereptype.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);

            return jobtypereptype;
        }
    }
}