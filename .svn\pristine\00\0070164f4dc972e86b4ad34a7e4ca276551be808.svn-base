﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RlinkMonitorRepliPerformance", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class RlinkMonitorRepliPerformance : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraobjectID { get; set; }

        [DataMember]
        public string PRRVGName { get; set; }

        [DataMember]
        public string DRRVGName { get; set; }

        [DataMember]
        public string PRDGName { get; set; }

        [DataMember]
        public string DRDGName { get; set; }

        [DataMember]
        public string PRRlinkName { get; set; }

        [DataMember]
        public string DRRlinkName { get; set; }

        [DataMember]
        public string PRLastcheckTime { get; set; }

        [DataMember]
        public string DRLastcheckTime { get; set; }

        [DataMember]
        public string PRMSGTransmit { get; set; }

        [DataMember]
        public string DRMSGTransmit { get; set; }

        [DataMember]
        public string PRBlockTransmit { get; set; }

        [DataMember]
        public string DRBlockTransmit { get; set; }

        [DataMember]
        public string PRStreamError { get; set; }

        [DataMember]
        public string DRStreamError { get; set; }

        [DataMember]
        public string PRMemoryError { get; set; }

        [DataMember]
        public string DRMemoryError { get; set; }

        #endregion Properties
    }
}
