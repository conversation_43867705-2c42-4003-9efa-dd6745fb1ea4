﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;


namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "BIAWorkflowAnalyatic", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BIAWorkflowAnalyatic : BaseEntity
    {

        [DataMember]
        public string ProfileName { get; set; }

        [DataMember]
        public int ProfileId { get; set; }

        [DataMember]
        public int WorkFlowId { get; set; }

        [DataMember]
        public string WorkFlowName { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string BusinessFunction { get; set; }

        [DataMember]
        public string BusinessService { get; set; }

        [DataMember]
        public int TotalWorkFlow { get; set; }

        [DataMember]
        public int CompletedWithSuccess { get; set; }

        [DataMember]
        public int Aborted { get; set; }

        [DataMember]
        public int WorkFlowConfiguredRTO { get; set; }

        [DataMember]
        public int WorkFlowExecutionTime { get; set; }

        [DataMember]
        public WorkflowManagement workflowtype { get; set; }

        [DataMember]
        public int CompletedWithError { get; set; }
    }
}
