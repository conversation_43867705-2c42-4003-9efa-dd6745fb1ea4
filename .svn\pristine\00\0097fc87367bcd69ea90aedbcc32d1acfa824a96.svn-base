﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.ServiceProcess;
using log4net;
using System.Web;
using System.Text;
using System.Data;
using SpreadsheetGear;
using System.Drawing;
using System.IO;
using System.Configuration;
using System.Web.UI;
//using  CP.Common.Shared.Constants.UrlConstants.Urls;

namespace CP.UI
{
    public partial class ServerList : ServerBasePage
    {
        #region variable

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.SereverConfiguration;
        public string ip;
        private readonly ILog _logger = LogManager.GetLogger(typeof(ServerList));
        private IWorkbookSet workbookSet = null;
        public String ssFile = string.Empty;
        private IWorkbook templateWorkbook = null;
        private IWorksheet templateWorksheet = null;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.SereverList;
                }
                return string.Empty;
            }
        }

        #endregion variable

        public override void PrepareView()
        {
            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }
            if (CMDBExcelName != null && CMDBAddedCount != null && CMDBTotalCount != null)
            {
                if (!string.IsNullOrEmpty(CMDBExcelName) && !string.IsNullOrEmpty(CMDBAddedCount) && !string.IsNullOrEmpty(CMDBTotalCount))
                {
                    CMDBMsg.Visible = true;
                    lblExcelName.Text = CMDBExcelName;
                    lblOut.Text = CMDBAddedCount;
                    lblOutOf.Text = CMDBTotalCount;
                }
            }
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            Utility.SelectMenu(Master, "Module3");
            paneldetails.Visible = false;
            BindList();
        }

        private void BindList()
        {
            setListViewPage();
            lvComponent.DataSource = GetServerList();
            lvComponent.DataBind();
        }

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageServerList"]) != -1) && Session["CurrentPageServerList"] != null && (Convert.ToInt32(Session["CurrentPageServerList"]) > 0))
            {
                if (Session["TotalPageRowsCount"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageServerList"]) == Convert.ToInt32(Session["TotalPageRowsCount"]) - 1)
                    {
                        Session["CurrentPageServerList"] = Convert.ToInt32(Session["CurrentPageServerList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCount"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageServerList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageServerList"] = -1;

            }
            //if ((Convert.ToInt32(Session["CurrentPageServerList"]) < 0) && (Convert.ToInt32(Session["CurrentPageServerList"]) != -1))
            //{
            //    lvComponent.DataSource = GetServerList();
            //    lvComponent.DataBind();
            //    setListViewPage();
            //}
        }

        private IList<Server> GetServerList()
        {
            if (IsSuperAdmin)
            {
                if (string.IsNullOrEmpty(CurrentServerType))
                {
                    return Facade.GetServersByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
                }
                else
                {
                    if (IsUserOperator)
                    {
                        return Facade.GetServersByUserCompanyIdAndRoleAndType(LoggedInUserCompanyId, IsUserOperator, IsParentCompnay, CurrentServerType);
                    }
                    else
                    {
                        return Facade.GetServersByUserCompanyIdAndRoleAndType(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, CurrentServerType);
                    }
                }
            }
            else
            {
                string role = Convert.ToString(LoggedInUserRole);

                string ostype = string.Empty;

                ostype = Convert.ToString(Session["OSType"]);

                if (!string.IsNullOrEmpty(ostype))
                {
                    return Facade.GetServersListByUserinfraId(LoggedInUserId, LoggedInUserCompanyId, role, ostype);
                }
                else
                {
                    return Facade.GetServersListByUserinfraId(LoggedInUserId, LoggedInUserCompanyId, role);
                }
            }
        }

        protected void LvComponentItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                _logger.Info(" start a LvComponentItemDeleting method");
                //IList<WorkflowAction> WFlist = new List<WorkflowAction>();
                IList<WorkflowAction> WFlist = null;
                List<string> WFIdlist = new List<string>();

                IList<WorkflowAction> WFlistClone = null;
                IList<DatabaseBase> DBlist = new List<DatabaseBase>();
                IList<WorkflowGet> workflow = new List<WorkflowGet>();
                IList<WorkflowGet> workflowClone = new List<WorkflowGet>();

                IList<InfraObject> infra = null;
                Session["CurrentPageServerList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCount"] = dataPager1.TotalRowCount;
                var lbl = (lvComponent.Items[e.ItemIndex].FindControl("Id")) as Label;
                var lblName = (lvComponent.Items[e.ItemIndex].FindControl("Db_Hostname")) as Label;
                if (lbl != null && lblName != null && ValidateRequest("ServerDeleting", UserActionType.DeleteServerComponent))
                {
                    string serverName = null;
                    List<string> lstworkflow = new List<string>();
                    IList<string> lstactions = new List<string>();
                    IList<string> lstinfraobject = new List<string>();
                    List<string> lstDB = new List<string>();
                    List<string> lstinfra = new List<string>();
                    StringBuilder _builderDB = new StringBuilder();
                    StringBuilder _builderInfra = new StringBuilder();
                    StringBuilder _builderWFAction = new StringBuilder();
                    StringBuilder _builderWorkflow = new StringBuilder();
                    serverName = lblName.Text;
                    lbl_Server.Text = serverName;
                    DBlist = Facade.GetDatabaseBasesByServerId(Convert.ToInt32(lbl.Text));
                    if (DBlist != null)
                    {
                        _logger.Info("DB List count: " + DBlist.Count);

                        lstDB = DBlist.Select(x => x.Name).ToList();

                    }

                    foreach (string item in lstDB)
                    {
                        _builderWFAction.AppendLine();
                        _builderDB.AppendLine(item + ",");
                        _builderDB.AppendLine(Environment.NewLine);
                    }

                    IList<InfraObject> groupDetail = Facade.GetInfraObjectByServerId_New(Convert.ToInt32(lbl.Text));
                    if (groupDetail != null)
                    {
                        _logger.Info("Infra Name: " + groupDetail.Count);
                        lstinfra = groupDetail.Select(x => x.Name).ToList();
                    }

                    foreach (string item in lstinfra)
                    {
                        _builderWFAction.AppendLine();
                        _builderInfra.AppendLine(item + ", ");
                        _builderInfra.AppendLine(Environment.NewLine);
                    }

                    WFlist = Facade.GetAllWorkflowActionsByServerId(Convert.ToInt32(lbl.Text));

                    workflow = Facade.GetWFActionDetails(Convert.ToString(lbl.Text));

                    if (WFlist != null && WFlist.Count > 0 || groupDetail != null || DBlist != null)
                    {
                        if (workflow != null && workflow.Count > 0)
                        {
                            foreach (var item in workflow)
                            {
                                //   lstworkflow.Add(item.Name);
                                _builderWorkflow.AppendLine(item.Name + ",");

                                //string str = string.Empty;
                                //var actions = Facade.GetAllActionsByWorkflowId(item.WorkflowId);
                                //foreach (var filter in actions)
                                //{
                                //    var flag = WFlist.Where(x => x.Id == Convert.ToInt32(filter.Id)).ToList();
                                //    flag.OrderBy(x => x.Name).FirstOrDefault();

                                //    foreach (var it in flag)
                                //    {
                                //        str += it.Name + ", ";
                                //    }
                                //}
                                //str = str.Trim();
                                //str = str.Remove(str.Length - 1);
                                //  lstactions.Add(str);

                                //foreach (string _item in lstactions)
                                //{
                                //    _builderWFAction.Append(_item + ",  ");
                                //}

                            }


                        }



                        if (workflow.Count == 0 || WFlist.Count > 0 || groupDetail != null)
                        {
                            string str = string.Empty;
                            string _infra = string.Empty;
                            if (WFlist != null)
                            {
                                foreach (var item in WFlist)
                                {
                                    str += item.Name + ", ";
                                }
                                str = str.Trim();
                                str = str.Remove(str.Length - 1);
                                lstactions.Add(str);

                                foreach (string item in lstactions)
                                {
                                    //  _builderWFAction.AppendLine(Environment.NewLine);
                                    _builderWFAction.AppendLine(item + ", ");
                                    // _builderWFAction.AppendLine(Environment.NewLine);

                                }

                            }
                            if (groupDetail != null)
                            {
                                foreach (var item in groupDetail)
                                {
                                    _infra += item.Name + ", ";
                                }
                                _infra = _infra.Trim();
                                _infra = _infra.Remove(_infra.Length - 1);
                                lstinfraobject.Add(_infra);
                            }

                        }

                        if (DBlist != null || groupDetail != null || WFlist != null)
                        {
                            paneldetails.Visible = true;
                            string infraName = string.Empty;
                            string dbName = string.Empty;

                            DataTable table = new DataTable();
                            table.Columns.Add("Server Attached Infraobject");
                            table.Columns.Add("Server Attached Database");
                            table.Columns.Add("Server Attached Workflow");
                            table.Columns.Add("Server Attached Workflow Actions");



                            if (DBlist != null || groupDetail != null || WFlist != null || workflow != null)
                            {
                                table.Rows.Add(_builderInfra.ToString(), _builderDB.ToString(), _builderWorkflow.ToString(), _builderWFAction.ToString());
                            }
                            // if(groupDetail != null)
                            //{
                            //    table.Rows.Add(_builderInfra.ToString(),"","","");
                            //}
                            // if(workflow != null)
                            //{
                            //    table.Rows.Add("","",_builderWorkflow.ToString(),"");
                            //}
                            //  if(WFlist != null)
                            //{
                            //    table.Rows.Add("","","",_builderWFAction.ToString());
                            //}
                            #region comment
                            //for (int i = 0; i < lstworkflow.Count; i++)
                            //{
                            //    if (i == 0 && lstDB.Count > 0 && lstinfraobject != null && lstactions.Count != 0)
                            //    {
                            //        table.Rows.Add(_builderInfra.ToString(), lstDB[i], _builderWorkflow.ToString(), _builderWFAction.ToString());
                            //    }

                            //    else if (i == 0 && lstDB.Count == 0 && lstinfraobject != null && lstactions.Count != 0)
                            //    {
                            //        table.Rows.Add(_builderInfra.ToString(), "", _builderWorkflow.ToString(), _builderWFAction.ToString());
                            //    }

                            //    else if (i == 0 && lstDB.Count > 0 && lstinfraobject == null)
                            //    {
                            //        table.Rows.Add("", lstDB[i], _builderWorkflow.ToString(), _builderWFAction.ToString());
                            //    }

                            //    else if (i > 0 && lstDB.Count > 0 && lstworkflow.Count == 0)
                            //    {
                            //        table.Rows.Add(_builderInfra.ToString(), "", "", _builderWFAction.ToString());
                            //    }


                            //    else
                            //    {
                            //        table.Rows.Add("", "", _builderWorkflow.ToString(), _builderWFAction.ToString());
                            //    }

                            //}
                            //if (lstworkflow.Count == 0 && (lstactions != null || lstinfraobject != null))
                            //{

                            //    for (int i = 0; i < lstactions.Count; i++)
                            //    {
                            //        if (i >= 0 && lstDB.Count > 0 && lstinfraobject != null)
                            //        {
                            //            table.Rows.Add(lstinfra[i], lstDB[i], "", lstactions[i]);
                            //        }
                            //        else if (i >= 0 && lstDB.Count == 0 && lstinfraobject == null && lstworkflow.Count == 0)
                            //        {
                            //            table.Rows.Add("", "", "", lstactions[i]);
                            //        }
                            //        else if (i >= 0 && lstDB.Count == 0 && lstinfraobject != null && lstworkflow.Count == 0)
                            //        {
                            //            table.Rows.Add(_builderInfra.ToString(), "", "", _builderWFAction.ToString());
                            //        }

                            //        else if (i >= 0 && lstDB.Count >= 0 && lstinfraobject == null && lstworkflow.Count == 0)
                            //        {
                            //            table.Rows.Add("", lstDB[i], "", lstactions[i]);
                            //        }

                            //    }


                            //if (lstinfraobject != null && lstactions.Count == 0)
                            //{

                            //        for (int i = 0; i <= lstinfraobject.Count; i++)
                            //        {

                            //            table.Rows.Add(lstinfra[i], "","","");

                            //        }


                            //}

                            #endregion comment
                            //}

                            Server_Info_GridView.DataSource = table;
                            Server_Info_GridView.DataBind();


                            Session["IsServerDelete"] = "True";
                            //ErrorSuccessNotifier.AddSuccessMessage("Server is in use");
                            return;
                        }
                    }
                    else
                    {
                        Facade.DeleteServerById(Convert.ToInt32(lbl.Text));

                        //ActivityLogger.AddLog(LoggedInUserName, "Server", UserActionType.DeleteServerComponent, "The Server '" + lblName.Text + "' was deleted", LoggedInUserId);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Server", UserActionType.DeleteServerComponent, "The Server '" + lblName.Text + "' was deleted", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Server" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                        BindList();
                    }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void LvComponentItemDeleting_old(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                _logger.Info(" start a LvComponentItemDeleting method");
                //IList<WorkflowAction> WFlist = new List<WorkflowAction>();
                IList<WorkflowAction> WFlist = null;
                IList<WorkflowAction> WFlistClone = null;
                IList<DatabaseBase> DBlist = new List<DatabaseBase>();
                IList<WorkflowGet> workflow = new List<WorkflowGet>();
                IList<WorkflowGet> workflowClone = new List<WorkflowGet>();

                InfraObject infra = null;
                Session["CurrentPageServerList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCount"] = dataPager1.TotalRowCount;
                var lbl = (lvComponent.Items[e.ItemIndex].FindControl("Id")) as Label;
                var lblName = (lvComponent.Items[e.ItemIndex].FindControl("Db_Hostname")) as Label;
                if (lbl != null && lblName != null && ValidateRequest("ServerDeleting", UserActionType.DeleteServerComponent))
                {
                    List<string> lstworkflow = new List<string>();
                    List<string> lstactions = new List<string>();
                    List<string> lstDB = new List<string>();

                    DBlist = Facade.GetDatabaseBasesByServerId(Convert.ToInt32(lbl.Text));
                    if (DBlist != null)
                    {
                        _logger.Info("DB List count: " + DBlist.Count);

                        lstDB = DBlist.Select(x => x.Name).ToList();

                    }

                    var groupDetail = infra = Facade.GetInfraObjectByServerId(Convert.ToInt32(lbl.Text));
                    if (infra != null)
                        _logger.Info("Infra Name: " + infra.Name);

                    //  var applicationDetail = Facade.GetApplicationGroupsByserverId(Convert.ToInt32(lbl.Text));
                    //var workflowDetailByServer = WFlist = Facade.GetAllWorkflowActionsByServerId(Convert.ToInt32(lbl.Text));
                    WFlist = Facade.GetAllWorkflowActionsByServerId(Convert.ToInt32(lbl.Text));

                    //workflow = Facade.GetWFActionDetails(Convert.ToString(WFlistitem.Id));
                    workflow = Facade.GetWFActionDetails(Convert.ToString(lbl.Text));



                    if (workflow != null)
                    {
                        foreach (var item in workflow)
                        {
                            lstworkflow.Add(item.Name);
                            var actions = Facade.GetAllActionsByWorkflowId(item.WorkflowId);

                            string str = string.Empty;
                            foreach (var itm in actions)
                            {
                                str += itm.Name + "\n";
                            }
                            lstactions.Add(str);
                        }
                    }



                    if (WFlist != null)
                    {

                        _logger.Info("WF Actions List Count: " + WFlist.Count);

                        //foreach (WorkflowAction WFlistitem in WFlist)
                        //{
                        //    if (WFlistitem != null)
                        //    {
                        //        IList<WorkflowGet> tmpworkflow = new List<WorkflowGet>();
                        //        tmpworkflow = Facade.GetWFActionDetails(Convert.ToString(WFlistitem.Id));
                        //        if (tmpworkflow != null && tmpworkflow.Count > 0)
                        //        {
                        //            foreach (WorkflowGet item in tmpworkflow)
                        //            {
                        //                workflow.Add(item);
                        //            }
                        //        }
                        //    }
                        //}

                        if (workflow != null)
                        {
                            _logger.Info("Workflow Count: " + workflow.Count);
                        }

                        //IList<WorkflowGet> fltrWFName = new List<WorkflowGet>();
                        //foreach (WorkflowGet item in workflow)
                        //{
                        //    var actid = item.ActionIds;
                        //    var lstids = actid.Split(',').ToList();

                        //    foreach (var filterids in lstids)
                        //    {
                        //        if (!string.IsNullOrWhiteSpace(filterids))
                        //        {
                        //            var flag = WFlist.Where(x => x.Id == Convert.ToInt32(filterids));

                        //            if (flag.Count() > 0)
                        //            {
                        //                // _logger.Info("WF Name added :" + item.Name);
                        //                fltrWFName.Add((item));
                        //                //  _logger.Info(item.Name + " : Added Successfully");
                        //            }
                        //        }

                        //    }
                        //}

                        if (DBlist != null || groupDetail != null || WFlist != null)
                        {
                            paneldetails.Visible = true;
                            if (infra != null)
                            {
                                _logger.Info("Infraobject Name : " + infra.Id + "," + infra.Name);

                                //    lstInfraobject.Text += Convert.ToString(infra.Name) + Environment.NewLine + "<br>";
                            }
                            else
                            {
                                //    lstInfraobject.Text = "*NA";
                                _logger.Info("Infra Data is NULL");
                            }
                            if (DBlist != null)
                            {
                                foreach (var itm in DBlist)
                                {
                                    _logger.Info("Database Name: " + +itm.Id + "," + itm.Name);
                                    //      lstDatabase.Text += Convert.ToString(itm.Name) + Environment.NewLine + "<br>";
                                }
                            }
                            else
                            {
                                //     lstDatabase.Text = "*NA";
                                _logger.Info("DB Data is NULL");
                            }

                            //List<WorkflowGet> distinct = fltrWFName.GroupBy(x => x.Name)
                            //                            .Select(g => g.First())
                            //                            .ToList();

                            if (workflow != null)
                            {
                                workflowClone = workflow.OrderBy(x => x.Name).ToList();
                                List<WorkflowGet> distinct = workflowClone.GroupBy(x => x.Name)
                                                       .Select(g => g.First())
                                                       .ToList();
                                foreach (var itm in distinct)
                                {

                                    //    lstworkflow.Text += Convert.ToString(itm.Name) + Environment.NewLine + "<br>";
                                }
                                _logger.Info("Workflows were added successfully.");

                            }
                            else
                            {
                                //     lstworkflow.Text = "*NA";
                                _logger.Info("Workflow Data is NULL");
                            }
                            if (WFlist != null)
                            {
                                WFlistClone = WFlist.OrderBy(x => x.Name).ToList();
                                List<WorkflowAction> distinct = WFlistClone.GroupBy(x => x.Name)
                                                        .Select(g => g.First())
                                                        .ToList();
                                foreach (var itm in distinct)
                                {
                                    //       lstworkflowActions.Text += Convert.ToString(itm.Name) + Environment.NewLine + "<br>";
                                }
                                _logger.Info("Workflow actions were added successfully.");
                            }
                            else
                            {
                                //     lstDatabase.Text = "*NA";
                                _logger.Info("Workflow action Data is NULL");
                            }





                            DataTable table = new DataTable();
                            table.Columns.Add("Server Attached Infraobject");
                            table.Columns.Add("Server Attached Database");
                            table.Columns.Add("Server Attached Workflow");
                            table.Columns.Add("Server Attached Workflow Actions");

                            table.Rows.Add("101", "Rameez", "Rameez", "<EMAIL>");
                            table.Rows.Add("102", "Sam Nicolus", "Rameez", "<EMAIL>");
                            table.Rows.Add("103", "Subramanium", "Rameez", "<EMAIL>");
                            table.Rows.Add("104", "Ankur Kumar", "Rameez", "<EMAIL>");
                            Server_Info_GridView.DataSource = table;
                            Server_Info_GridView.DataBind();




                            Session["IsServerDelete"] = "True";
                            //ErrorSuccessNotifier.AddSuccessMessage("Server is in use");
                            return;
                        }
                    }
                    else
                    {
                        Facade.DeleteServerById(Convert.ToInt32(lbl.Text));

                        //ActivityLogger.AddLog(LoggedInUserName, "Server", UserActionType.DeleteServerComponent, "The Server '" + lblName.Text + "' was deleted", LoggedInUserId);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Server", UserActionType.DeleteServerComponent, "The Server '" + lblName.Text + "' was deleted", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Server" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                    }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void LvComponentItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageServerList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);

            var lblName = (lvComponent.Items[e.NewEditIndex].FindControl("Db_Hostname")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Server", UserActionType.UpdateServerComponent, "The Server '" + lblName.Text + "' Opened as Editing Mode.", LoggedInUserId);


            var lbl1 = (lvComponent.Items[e.NewEditIndex].FindControl("ID")) as Label;
            if (lbl1 != null && ValidateRequest("ServerEditing", UserActionType.DeleteServerComponent))
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ServerId, lbl1.Text);
                Helper.Url.Redirect(secureUrl);
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ServerId,
                //                                     lbl1.Text);
            }

            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void LvComponentPreRender(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                BindLvComponent();
            }
        }

        private void BindLvComponent()
        {
            if (String.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvComponent.DataSource = GetServerList();
                lvComponent.DataBind();
            }
            else
            {
                lvComponent.DataSource = GetServerListBySearch(txtsearchvalue.Text);
                lvComponent.DataBind();
            }
        }

        protected void LvComponentItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ImgEdit") as ImageButton;
            var delete = e.Item.FindControl("ImgDelete") as ImageButton;

            var IsVerified = e.Item.FindControl("IsVerified") as Label;
            var Status = e.Item.FindControl("Status") as Label;
            var lblStatusIcon = e.Item.FindControl("lblStatusIcon") as Label;
            var ImgTestConnection = e.Item.FindControl("ImgTestConnection") as ImageButton;

            var lnktest = e.Item.FindControl("lnkbtntestConnection") as LinkButton;

            var lblOS = e.Item.FindControl("OS") as Label;
            ImageButton testBTN = e.Item.FindControl("ImgTestConnection") as ImageButton;

            if (lblOS != null && testBTN != null)
            {
                if (lblOS.Text == "AS400")
                    testBTN.Attributes.Add("style", "display:none");
                else
                    testBTN.Attributes.Add("style", "display:inline");
            }


            if (!IsSuperAdmin && !IsUserAdmin)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
            if (IsUserOperator || IsUserManager || IsUserCustom || IsUserExecutionUser)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    //  lnktest.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                    ImgTestConnection.Enabled = false;
                }
            }


            if (Status.Text == "Pending")
            {
                lblStatusIcon.Attributes.Add("class", "fatal");
            }
            if (Status.Text == "Up")
            {
                lblStatusIcon.Attributes.Add("class", "health-up");
            }
            if (Status.Text == "Down")
            {
                lblStatusIcon.Attributes.Add("class", "health-down");
            }

            if (IsVerified.Text == "0")
            {
                ImgTestConnection.ImageUrl = "../images/icons/test-con-icon.png";
            }
            if (IsVerified.Text == "1")
            {
                if (Status.Text == "Up")
                {
                    ImgTestConnection.ImageUrl = "../images/icons/test-con-verified-icon.png";
                }
                if (Status.Text == "Down")
                {
                    ImgTestConnection.ImageUrl = "../images/icons/test-con-nonverified-icon.png";
                }
                if (Status.Text == "Pending")
                {
                    ImgTestConnection.ImageUrl = "../images/icons/test-con-icon.png";
                }
            }

            if (IsVerified.Text == "3")
            {
                if (Status.Text == "Up")
                {
                    ImgTestConnection.ImageUrl = "../images/icons/test-con-verified-icon.png";
                }
                if (Status.Text == "Down")
                {
                    ImgTestConnection.ImageUrl = "../images/icons/test-con-nonverified-icon.png";
                }
                if (Status.Text == "Pending")
                {
                    ImgTestConnection.ImageUrl = "../images/icons/test-con-icon.png";
                }
            }

        }

        protected void LvComponentItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //if (e.CommandName == "TestConnection") 
            //{
            //    var sc = new ServiceController("CPMonitorService");
            //    if (sc.Status.ToString() != "Stopped")
            //    {
            //        //For Oracle & MySql
            //        sc.ExecuteCommand(137);
            //    }              
            //}

            try
            {
                _logger.Info("Entered In LvComponentItemCommand");

                if (e.CommandName == "TestConnection")
                {
                    _logger.Info("Entered In TestConnection");

                    var sc = new ServiceController("CPMonitorService");

                    _logger.Info("Entered In sc");


                    _logger.Info("SC Status: " + sc.Status.ToString());


                    if (sc.Status.ToString() != "Stopped")
                    {
                        //For Oracle & MySql
                        var serverId = e.Item.FindControl("ID") as Label;

                        _logger.Info("serverId: " + serverId.Text);

                        Facade.UpdateServerByIsVerified(Convert.ToInt32(serverId.Text), 3);
                        _logger.Info("Updated Server Verified=3");
                        sc.ExecuteCommand(137);
                        _logger.Info("Monitor Service Called");

                    }

                }
            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured in LvComponentItemCommand");

                _logger.Info("Exception Occured in LvComponentItemCommand: " + ex.Message.ToString());

                if (ex.InnerException != null)
                    _logger.Info("Exception Occured in LvComponentItemCommand: " + ex.InnerException.Message.ToString());
            }
        }

        protected void BtnSearchClick(object sender, EventArgs e)
        {
            try
            {
                lvComponent.DataSource = "";
                lvComponent.DataSource = GetServerListBySearch(txtsearchvalue.Text);
                lvComponent.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured while binding data", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        private IList<Server> GetServerListBySearch(string value)
        {
            var serverList = GetServerList();
            value = value.Trim();

            if (!String.IsNullOrEmpty(value) && serverList != null)
            {
                //var result = (from sites in serverList
                //              where sites.Name.ToLower().Contains(value.ToLower()) || string.IsNullOrEmpty(sites.IPAddress) ? true : CryptographyHelper.Md5Decrypt(sites.IPAddress).Contains(value)


                //              select sites).ToList();


                //var result = (from sites in serverList
                //              where sites.Name.ToLower().StartsWith(value.ToLower()) || CryptographyHelper.Md5Decrypt(sites.IPAddress).StartsWith(value)
                //              select sites).ToList();




                //var result = (from sites in serverList
                //              where sites.Name.ToLower().StartsWith(value.ToLower())
                //                    || (!string.IsNullOrEmpty(sites.IPAddress)
                //                        && CryptographyHelper.Md5Decrypt(sites.IPAddress).StartsWith(value))
                //              select sites).ToList();


                    var result = (from sites in serverList
                                  where
                                      // Search by hostname, but only include servers that do not have an IPAddress
                                      (sites.HostName.ToLower().Contains(value.ToLower())
                                       && string.IsNullOrEmpty(sites.IPAddress))
                                      // Search by decrypted IP address
                                      || (!string.IsNullOrEmpty(sites.IPAddress)
                                          && CryptographyHelper.Md5Decrypt(sites.IPAddress).ToLower().Contains(value.ToLower())) || sites.Name.ToLower().Contains(value.ToLower())
                                  select sites).ToList();



                    return result;
                }
                return null;
            }
     


        //public List<Server> GetServerListBySearch(string value)
        //{
        //    // Retrieve data from both sources
        //    var serverList = GetServerList();
        //    var business = Facade.GetAllBusinessServices();

        //    // Trim and convert search value to lowercase for case-insensitive comparison
        //    value = value.Trim().ToLower();

        //    // Initialize the result list
        //    var result = new List<Server>();

        //    // Filter and combine data from `business` list
        //    if (!String.IsNullOrEmpty(value) && business != null)
        //    {
        //        var filteredBusiness = business
        //            .Where(sites => sites.CANID.ToString().Contains(value.ToString()))
        //            .ToList();

        //        // Assuming `business` and `serverList` have similar types, or you convert them to a common type
        //        // Example: Convert business items to Server type if needed
        //        // You might need to map `business` to a compatible format if `Server` and `BusinessService` are different types

        //        result.AddRange(ConvertBusinessToServer(filteredBusiness));
        //    }

        //    // Filter and combine data from `serverList`
        //    if (!String.IsNullOrEmpty(value) && serverList != null)
        //    {
        //        var filteredServers = serverList
        //            .Where(sites => sites.Name.ToLower().Contains(value) ||
        //                            (string.IsNullOrEmpty(sites.IPAddress) ? true : CryptographyHelper.Md5Decrypt(sites.IPAddress).Contains(value)))
        //            .ToList();

        //        result.AddRange(filteredServers);
        //    }

        //    return result;
        //}

        // Optional: Method to convert business items to server items if they are different types
        private List<Server> ConvertBusinessToServer(List<BusinessService> businessList)
        {
            // Example conversion logic - this depends on your actual types
            return businessList.Select(b => new Server
            {
                // Map properties from BusinessService to Server
                Name = b.CANID, // Example property mapping
                IPAddress = null // Or map IPAddress if available
            }).ToList();
        }

        protected void lvComponent_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }


        //public string GetIP(object type)
        //{
        //    string IpAdress = string.Empty;
        //    string[] _ipaddr = type.ToString().Split(',');

        //    try
        //    {
        //        //if (!string.IsNullOrEmpty(Convert.ToString(type)))
        //        //    IpAdress = CryptographyHelper.Md5Decrypt(type.ToString());

        //        //if ((!String.IsNullOrEmpty(_ipaddr[0].ToString())))
        //        //{

        //        //    if ((!String.IsNullOrEmpty(_ipaddr[0].ToString())) && (!String.IsNullOrEmpty(_ipaddr[1].ToString())))
        //        //        IpAdress = CryptographyHelper.Md5Decrypt(_ipaddr[0].ToString());
        //        //    else if ((String.IsNullOrEmpty(_ipaddr[0].ToString())) && (!String.IsNullOrEmpty(_ipaddr[2].ToString())))
        //        //        IpAdress = Convert.ToString(_ipaddr[1].ToString());
        //        //    else if ((!String.IsNullOrEmpty(_ipaddr[0].ToString())) &&  (!String.IsNullOrEmpty(_ipaddr[1].ToString())))
        //        //        IpAdress = CryptographyHelper.Md5Decrypt(_ipaddr[0].ToString());
        //        //}
        //        //else
        //        //{

        //        //    IpAdress = Convert.ToString(_ipaddr[1].ToString());
        //        //}



        //    }
        //    catch (Exception ex)
        //    {
        //        throw (ex);
        //    }
        //    return IpAdress;
        //}

        public string GetIP(object type)
        {
            string IpAdress = string.Empty;

            var server = Facade.GetServerById(Convert.ToInt32(type));
            try
            {
                if (server!=null)
                {


                    if ((!String.IsNullOrEmpty(server.IPAddress)) && (!String.IsNullOrEmpty(server.HostName)))
                        IpAdress = CryptographyHelper.Md5Decrypt(server.IPAddress.ToString());
                    else if ((String.IsNullOrEmpty(server.IPAddress)) && (!String.IsNullOrEmpty(server.HostName)))
                        IpAdress = Convert.ToString(server.HostName);
                    else if ((!String.IsNullOrEmpty(server.IPAddress)) &&  (!String.IsNullOrEmpty(server.HostName)))
                        IpAdress = CryptographyHelper.Md5Decrypt(server.IPAddress.ToString());


                }
            }
            catch (Exception ex)
            {
                throw (ex);
            }
            return IpAdress;
        }


        public string GetCan(object type)
        {
            string BusinessServiceId = string.Empty;

            if (string.IsNullOrEmpty(Convert.ToString(type)) || (Convert.ToInt32(type)) == 0)
            {
                return "N/A";
            }


            // var BusinessServiceID1 = Facade.GetBusinessServiceById(Convert.ToInt32(type));
            else
            {
                var BusinessServiceID1 = Facade.GetBusinessServiceById(Convert.ToInt32(type));
                try
                {
                    if (!string.IsNullOrEmpty(Convert.ToString(BusinessServiceID1.CANID)))
                    {


                        if ((!String.IsNullOrEmpty(BusinessServiceID1.CANID)))
                            BusinessServiceId = BusinessServiceID1.CANID.ToString();


                    }

                    else
                    {
                        return "N/A";
                    }
                    return BusinessServiceId;
                }

                catch (Exception ex)
                {
                    throw (ex);
                }
            }
          
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void Server_Info_GridView_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.Header)
            {
                e.Row.Cells[0].Width = new Unit("25%");
                e.Row.Cells[1].Width = new Unit("25%");
                e.Row.Cells[2].Width = new Unit("25%");
                e.Row.Cells[3].Width = new Unit("25%");
            }
        }

        protected void Server_Info_GridView_DataBound(object sender, EventArgs e)
        {

        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("==== Calling ServerReport Method ====");
                ServerReport();
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in Button1_Click() method  " + ex.InnerException);

            }
        }

        private void ServerReport()
        {
            try
            {
                _logger.Info("==== Retriving all server details =====");

                string sspath = string.Empty;
                IWorkbookSet workbookSet = null;


                _logger.Info("====== Generating Server list Report ======");
                _logger.Info(Environment.NewLine);

                workbookSet = Factory.GetWorkbookSet();
                ssFile = Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
                templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                templateWorksheet = templateWorkbook.Worksheets[0];

                IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
                IWorksheet reportWorksheet = null;
                IWorksheet lastWorksheet = null;

                IRange _cells;

                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
                reportWorkbook.Worksheets["Sheet1"].Delete();

                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
                reportWorksheet.Name = "Server List Report";





                _cells["A1"].ColumnWidth = 7;
                string strr = "ICICI_Bank Server List";
                _cells["D3"].Formula = strr.ToUpper();
                _cells["D3:E3"].HorizontalAlignment = HAlign.Center;
                _cells["D3"].Font.Bold = true;
                _cells["B3:G6"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["D3"].Font.Bold = true;
                _cells["D3"].ColumnWidth = 30;
                _cells["D3"].HorizontalAlignment = HAlign.Center;
                _cells["D3"].VerticalAlignment = VAlign.Top;
                _cells["B3:G3"].Font.Size = 11;
                _cells["B5:G8"].Font.Size = 10;
                _cells["B3:G8"].Font.Color = Color.FromArgb(255, 255, 255);
                _cells["B3:G8"].Font.Name = "Cambria";






                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 50, 15, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 640, 15, 120, 13);

                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 400, 15, 121, 13);
                }

                reportWorksheet.Cells["A1:C1"].RowHeight = 27;

                var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                _cells["B5"].Formula = "Report Generated Time : " + dateTime;
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;
                _cells["B5"].Font.Size = 10;


                int row = 8;
                int i = 1;
                int j = 1;

                _cells["B8:G8"].Interior.Color = Color.FromArgb(79, 129, 189);

                _cells["B" + row.ToString()].Formula = "SR.NO.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:G8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:G8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.FromArgb(0, 0, 0);
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Server Name";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["C" + row.ToString()].ColumnWidth = 30;
                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "Server Type	";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["D" + row.ToString()].ColumnWidth = 45;

                _cells["E" + row.ToString()].Formula = "IP Address";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["E" + row.ToString()].ColumnWidth = 23;

                _cells["F" + row.ToString()].Formula = "OS Type";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["F" + row.ToString()].ColumnWidth = 23;

                _cells["G" + row.ToString()].Formula = "Status";
                _cells["G" + row.ToString()].Font.Bold = true;
                _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["G" + row.ToString()].ColumnWidth = 23;


                
                _logger.Info("====== Server List Report EXCEL Desgin Completed. ======");

                row++;
                int dataCount = 0;
                int xlRow = 9;

                IList<Server> _servers = Facade.GetAllServers();
                if ((_servers != null) && (_servers.Count > 0))
                {

                    foreach (var item in _servers)
                    {
                        dataCount++;

                        int column = 0;
                        xlRow++;
                        _cells["B" + row + ":" + "G" + row].Interior.Color = row % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.FromArgb(255, 255, 255);
                        string ndx = "B" + row.ToString();

                        _cells[ndx].Formula = i.ToString();
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 10;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        i++;
                        column++;

                        ndx = "C" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.Name) ? item.Name : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "D" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.Type) ? item.Type : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "E" + row.ToString();
                        if (!string.IsNullOrEmpty(item.IPAddress))
                        {
                            _cells[ndx].Formula = !string.IsNullOrEmpty(CryptographyHelper.Md5Decrypt(item.IPAddress)) ? CryptographyHelper.Md5Decrypt(item.IPAddress) : "NA";
                        }
                        else
                        {
                            _cells[ndx].Formula = !string.IsNullOrEmpty(item.HostName) ? item.HostName : "NA";
                        }
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 23;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "F" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.OSType) ? item.OSType : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 23;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "G" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.Status.ToString()) ? item.Status.ToString() : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 23;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;


                        row++;
                    }
                    lastWorksheet = null;
                    lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                    reportWorksheet.ProtectContents = true;

                    _logger.Info("ServerReport Method Execution Completed.");

                    //Response.Clear();
                    //Response.ContentType = "application/vnd.ms-excel";
                    //Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");

                    //var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
                    //str = "Server List Report " + "_" + str + ".xls";
                    //reportWorkbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

                    //string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
                    //string myUrl = reportPath + "/ExcelFiles/" + str;
                    //var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar= RPO SLA Report');";
                    //ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);




                    Response.Clear();
                    Response.ContentType = "application/vnd.ms-excel";
                    Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");

                    var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
                    str = "Server List Report " + "_" + str + ".xls";
                    reportWorkbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

                    string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
                    string myUrl = reportPath + "/ExcelFiles/" + str;
                    var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar= RPO SLA Report');";
                    ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
                }

            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in ServerReport " + ex.InnerException);
            }
        }

    }
}