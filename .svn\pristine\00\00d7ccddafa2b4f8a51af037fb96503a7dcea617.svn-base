﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess.ReplicatedGrpMonitor
{
    internal sealed class ReplicatedGroupMonitorBuilder : IEntityBuilder<ReplicatedGroupMontor>
    {
        IList<ReplicatedGroupMontor> IEntityBuilder<ReplicatedGroupMontor>.BuildEntities(IDataReader reader)
        {
            var sqllognative = new List<ReplicatedGroupMontor>();

            while (reader.Read())
            {
                sqllognative.Add(((IEntityBuilder<ReplicatedGroupMontor>)this).BuildEntity(reader, new ReplicatedGroupMontor()));
            }

            return (sqllognative.Count > 0) ? sqllognative : null;
        }

        ReplicatedGroupMontor IEntityBuilder<ReplicatedGroupMontor>.BuildEntity(IDataReader reader, ReplicatedGroupMontor sqllognative)
        {
            sqllognative.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            sqllognative.InfraobjectID = Convert.IsDBNull(reader["InfraobjectID"]) ? 0 : Convert.ToInt32(reader["InfraobjectID"]);
            sqllognative.PRRDSName = Convert.IsDBNull(reader["PRRDSName"])
                ? string.Empty
                : Convert.ToString(reader["PRRDSName"]);
            sqllognative.DRRDSName = Convert.IsDBNull(reader["DRRDSName"])
                ? string.Empty
                : Convert.ToString(reader["DRRDSName"]);
            sqllognative.PRDGName = Convert.IsDBNull(reader["PRDGName"])
                ? string.Empty
                : Convert.ToString(reader["PRDGName"]);
            sqllognative.DRDGName = Convert.IsDBNull(reader["DRDGName"])
                ? string.Empty
                : Convert.ToString(reader["DRDGName"]);
            sqllognative.PRRVGName = Convert.IsDBNull(reader["PRRVGName"])
                ? string.Empty
                : Convert.ToString(reader["PRRVGName"]);
            sqllognative.DRRVGName = Convert.IsDBNull(reader["DRRVGName"])
                ? string.Empty
                : Convert.ToString(reader["DRRVGName"]);
            sqllognative.PRSRL = Convert.IsDBNull(reader["PRSRL"])
                ? string.Empty
                : Convert.ToString(reader["PRSRL"]);
            sqllognative.DRSRL = Convert.IsDBNull(reader["DRSRL"])
                ? string.Empty
                : Convert.ToString(reader["DRSRL"]);
            sqllognative.PRDataVolCount = Convert.IsDBNull(reader["PRDataVolCount"])
                ? string.Empty
                : Convert.ToString(reader["PRDataVolCount"]);
            sqllognative.DRDataVolCount = Convert.IsDBNull(reader["DRDataVolCount"])
                ? string.Empty
                : Convert.ToString(reader["DRDataVolCount"]);

            sqllognative.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);


            sqllognative.PRRLinkName = Convert.IsDBNull(reader["PRRLinkName"])
                ? string.Empty
                : Convert.ToString(reader["PRRLinkName"]);
            sqllognative.DRRLinkName = Convert.IsDBNull(reader["DRRLinkName"])
                ? string.Empty
                : Convert.ToString(reader["DRRLinkName"]);
            sqllognative.PRRLinkState = Convert.IsDBNull(reader["PRRLinkState"])
                ? string.Empty
                : Convert.ToString(reader["PRRLinkState"]);
            sqllognative.DRRLinkState = Convert.IsDBNull(reader["DRRLinkState"])
                ? string.Empty
                : Convert.ToString(reader["DRRLinkState"]);

            sqllognative.PRSyncMode = Convert.IsDBNull(reader["PRSyncMode"])
                ? string.Empty
                : Convert.ToString(reader["PRSyncMode"]);
            sqllognative.DRSyncMode = Convert.IsDBNull(reader["DRSyncMode"])
                ? string.Empty
                : Convert.ToString(reader["DRSyncMode"]);

            //sqllognative.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
            //    ? DateTime.MinValue
            //    : Convert.ToDateTime(reader["UpdateDate"]);
            //sqllognative.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            //sqllognative.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            //sqllognative.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            return sqllognative;
        }
    }
}
