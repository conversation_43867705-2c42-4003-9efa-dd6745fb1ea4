﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.UI.Controls.ReportClients;
using Gios.Pdf;
using SpreadsheetGear;
using System.Collections;



namespace CP.UI.Controls
{
    public partial class MontlySummaryReport : BaseControl
    {

        #region Variables

        private List<OracleLog> datalaglist = new List<OracleLog>();
        private List<HADR> Hadrdatalaglist = new List<HADR>();
        private IWorkbookSet workbookSet = null;
        private string ssFile = null;
        private IWorkbook templateWorkbook = null;
        private IWorksheet templateWorksheet = null;
        private IRange cells = null;
        private ArrayList appnme = new ArrayList();
        private ArrayList deviated = new ArrayList();
        private ArrayList appDeviated = new ArrayList();

        #endregion Variables

        #region Events

        public override void PrepareView()
        {
            divMontly.Visible = false;
            lblMsg.Text = "";


        }

        protected void ddlType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlType.SelectedItem.Text == "Monthly")
            {
                divstrtdt.Visible = false;
                divMontly.Visible = true;
            }
            else
            {
                divstrtdt.Visible = true;
                divMontly.Visible = false;
            }
        }
        protected void ddlYear_SelectedIndexChanged(object sender, EventArgs e)
        {
            lblMsg.Text = string.Empty;
        }
        protected void ddlMonth_SelectedIndexChanged(object sender, EventArgs e)
        {
            lblMsg.Text = string.Empty;
        }

        protected void btnview_Click(object sender, EventArgs e)
        {

            try
            {

                if (ddlType.SelectedValue == "0")
                {
                    lblvalidation.Visible = true;
                    lblvalidation.Text = "Select Report Type";
                    return;
                }

                switch (ddlType.SelectedValue)
                {
                    case "1":

                        //custom 
                        string dat = DateTime.Now.ToString("yyyy-MM-dd");
                        if (Convert.ToDateTime(txtstart.Text) > Convert.ToDateTime(txtend.Text))
                        {
                            lblMsg.Visible = true;
                            lblMsg.Text = "Start Date Greater than End Date";
                            return;
                        }
                        else if (Convert.ToDateTime(dat) < Convert.ToDateTime(txtstart.Text))
                        {
                            lblMsg.Visible = true;
                            lblMsg.Text = "Start Date can't Greater than Today Date";
                            return;
                        }
                        else if (Convert.ToDateTime(dat) < Convert.ToDateTime(txtend.Text))
                        {
                            lblMsg.Visible = true;
                            lblMsg.Text = "End Date can't Greater than Today Date";
                            return;
                        }
                        CreateExcelReport();

                        break;
                    case "2":

                        //Monlty


                        DateTime dstartdate = FirstDayOfMonthFromDateTime(Convert.ToInt32(ddlYear.SelectedValue), Convert.ToInt32(ddlMonth.SelectedValue));
                        DateTime dsEnddate = LastDayOfMonthFromDateTime(Convert.ToInt32(ddlYear.SelectedValue), Convert.ToInt32(ddlMonth.SelectedValue));

                        string Sdate = dstartdate.ToString("yyyy-MM-dd");
                        string Edate = dsEnddate.ToString("yyyy-MM-dd");

                        //if (dsEnddate.Month > DateTime.Now.Month)
                        //{
                        //    lblMsg.Visible = true;
                        //    lblMsg.Text = "Selected Month is Greater than Current Month";
                        //    return;
                        //}

                        if (dsEnddate.Year > DateTime.Now.Year)
                        {
                            lblMsg.Visible = true;
                            lblMsg.Text = "Selected Year is Greater than Current Year";
                            return;

                        }
                        if (dsEnddate.Year == DateTime.Now.Year)
                        {
                            if (dsEnddate.Month > DateTime.Now.Month)
                            {
                                lblMsg.Visible = true;
                                lblMsg.Text = "Selected Month is Greater than Current Month";
                                return;
                            }


                        }

                        MCreateExcelReport(Sdate, Edate);

                        break;
                }


            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }



        public DateTime FirstDayOfMonthFromDateTime(int Year, int Month)
        {
            return new DateTime(Year, Month, 1);
        }
        public DateTime LastDayOfMonthFromDateTime(int Year, int Month)
        {
            DateTime firstDayOfTheMonth = new DateTime(Year, Month, 1);
            return firstDayOfTheMonth.AddMonths(1).AddDays(-1);
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }


        #endregion Events

        #region Methods

        private void RegisterPostBackControl()
        {
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnview);
        }

        private void CreateExcelReport()
        {
            lblMsg.Text = string.Empty;
            workbookSet = Factory.GetWorkbookSet();
            ssFile = Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (SpreadsheetGear.IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
            GenerateExcel(reportWorkbook, reportWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
            GenerateNextSheet(reportWorkbook, reportWorksheet);

            reportWorkbook.Worksheets["Sheet1"].Delete(); // Default Worksheet
            OpenExcelFile(reportWorkbook, reportWorksheet);
        }

        private void GenerateExcel(IWorkbook reportWorkbook, IWorksheet reportWorksheet)
        {

            cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "Business Service Summary Chart";
            cells["A1"].ColumnWidth = 7;

            cells["F4"].Formula = "Montly Summary Report";
            cells["F4"].Font.Color = System.Drawing.Color.Black;
            cells["F4"].Font.Size = 14;
            cells["F4"].Font.Bold = true;
            cells["F4"].ColumnWidth = 30;
            cells["F4"].HorizontalAlignment = SpreadsheetGear.HAlign.Center;
            cells["F4"].VerticalAlignment = SpreadsheetGear.VAlign.Top;
            cells["F4"].Font.Name = "Cambria";

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 50, 20, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 840, 20, 120, 13);
            reportWorksheet.Cells["A1:G1"].RowHeight = 27;
            reportWorksheet.Cells["A2:G2"].RowHeight = 25;

            var DateTme = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));//DateTime.Now.ToString("dd-MMM-yy HH:mm");
            cells["B5"].Formula = "Report Generated Time : " + DateTme;
            cells["B5"].Font.Bold = true;
            cells["B5"].Font.Size = 10;
            cells["B5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B5"].Font.Name = "Cambria";

            var DstartDate = Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"));//Convert.ToDateTime(txtstart.Text).ToString("dd-MMM-yy");
            var startDate = DstartDate; //DateTime.Now.AddDays(-7).ToString("dd-MMM-yy HH:mm");
            cells["H5"].Formula = "Start Time : " + startDate;
            cells["H5"].Font.Bold = true;
            cells["H5"].Font.Size = 10;
            cells["H5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["H5"].Font.Name = "Cambria";

            var EstartDate = Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"));//Convert.ToDateTime(txtend.Text).ToString("dd-MMM-yy");

            var endDate = EstartDate; //DateTime.Now.AddDays(-1).ToString("dd-MMM-yy HH:mm");
            cells["J5"].Formula = "End Time : " + endDate;
            cells["J5"].Font.Bold = true;
            cells["J5"].Font.Size = 10;
            cells["J5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["J5"].Font.Name = "Cambria";

            cells["B25"].Formula = "■ Rpo Met : Datalag <= RPO";
            cells["B25"].Font.Bold = true;
            cells["B25"].Font.Size = 10;
            cells["B25"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B25"].Font.Name = "Cambria";
            cells["B25"].Font.Color = System.Drawing.Color.FromArgb(79, 129, 189);

            cells["B26"].Formula = "■ Deviation : Datalag > RPO";
            cells["B26"].Font.Bold = true;
            cells["B26"].Font.Size = 10;
            cells["B26"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B26"].Font.Name = "Cambria";
            cells["B26"].Font.Color = System.Drawing.Color.FromArgb(192, 80, 77);

            cells["B27"].Formula = "■ Incident : Total number of incident raised in week against the application groups ";
            cells["B27"].Font.Bold = true;
            cells["B27"].Font.Size = 10;
            cells["B27"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B27"].Font.Name = "Cambria";
            cells["B27"].Font.Color = System.Drawing.Color.FromArgb(155, 187, 89);

            int row = 25;
            int i = 1;
            string[] xlCol = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };

            cells["G" + row.ToString()].Formula = "SR.No";
            cells["G" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["G" + row.ToString()].Font.Size = 10;
            cells["G" + row.ToString()].ColumnWidth = 8;
            cells["G" + row.ToString()].Font.Bold = true;
            cells["G" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["G" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["G" + row.ToString()].Font.Color = System.Drawing.Color.White;

            cells["H" + row.ToString()].Formula = "Business Service Name";
            cells["H" + row.ToString()].Font.Color = System.Drawing.Color.Black;
            cells["H" + row.ToString()].Font.Size = 10;
            cells["H" + row.ToString()].ColumnWidth = 30;
            cells["H" + row.ToString()].Font.Bold = true;
            cells["H" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["H" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["H" + row.ToString()].Font.Color = System.Drawing.Color.White;

            cells["I" + row.ToString()].Formula = "Rpo Met";
            cells["I" + row.ToString()].Font.Color = System.Drawing.Color.Black;
            cells["I" + row.ToString()].Font.Size = 10;
            cells["I" + row.ToString()].ColumnWidth = 20;
            cells["I" + row.ToString()].Font.Bold = true;
            cells["I" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["I" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["I" + row.ToString()].Font.Color = System.Drawing.Color.White;

            cells["J" + row.ToString()].Formula = "Deviation";
            cells["J" + row.ToString()].Font.Color = System.Drawing.Color.Black;
            cells["J" + row.ToString()].Font.Size = 10;
            cells["J" + row.ToString()].ColumnWidth = 20;
            cells["J" + row.ToString()].Font.Bold = true;
            cells["J" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["J" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["J" + row.ToString()].Font.Color = System.Drawing.Color.White;

            cells["K" + row.ToString()].Formula = "Incident";
            cells["K" + row.ToString()].Font.Color = System.Drawing.Color.Black;
            cells["K" + row.ToString()].Font.Size = 10;
            cells["K" + row.ToString()].ColumnWidth = 20;
            cells["K" + row.ToString()].Font.Bold = true;
            cells["K" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["K" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["K" + row.ToString()].Font.Color = System.Drawing.Color.White;

            SpreadsheetGear.IRange range = reportWorksheet.Cells["G24:K24"];
            IBorder border = range.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];
            border.LineStyle = SpreadsheetGear.LineStyle.Continous;
            border.Color = System.Drawing.Color.Black;
            border.Weight = SpreadsheetGear.BorderWeight.Medium;

            SpreadsheetGear.IRange rangeOne = reportWorksheet.Cells["G25:K25"];
            IBorder borderOne = rangeOne.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];
            borderOne.LineStyle = SpreadsheetGear.LineStyle.Continous;
            borderOne.Color = System.Drawing.Color.Black;
            borderOne.Weight = SpreadsheetGear.BorderWeight.Thin;
            row++;

            //int xlRow = 26;
            var Appgroups = Facade.GetAllBusinessServices();
            int dataCount = 0;
            int rowApp = 26;

            if (Appgroups != null)
            {
                foreach (var app in Appgroups)
                {
                    string[] xlColumn = { "G", "H", "I", "J", "K" };
                    IList<InfraObject> groups = new List<InfraObject>();
                    groups = Facade.GetInfraObjectByBusinessServiceId(app.Id);
                    dataCount++;

                    deviated.Clear();
                    int Cnts = 0;
                    int rpoCount = row;
                    double maint = 0;
                    int dvn = 0;
                    if (groups != null)
                    {
                        foreach (var grp in groups)
                        {
                            switch (grp.RecoveryType)
                            {
                                case (int)ReplicationType.DB2HADR:
                                case (int)ReplicationType.DB2DataSync:
                                    {
                                        #region For Recovery Type 15 and 21

                                        var Hadrlog = Facade.GetHadrMonitorByDate(grp.Id, txtstart.Text, txtend.Text);
                                        Hadrdatalaglist.Clear();

                                        #region old code

                                        //for (int k = 1; k <= 7; k++)
                                        //{
                                        //    if (Hadrlog != null)
                                        //    {
                                        //        foreach (var lg in Hadrlog)
                                        //        {
                                        //            DateTime dt = DateTime.Now.AddDays(-k);
                                        //            if (lg.CreateDate.Day.Equals(dt.Day))
                                        //                goto outs;
                                        //        }

                                        //    outs:
                                        //        DateTime dts = DateTime.Now.AddDays(-k);
                                        //        var getByday = from p in Hadrlog where p.CreateDate.Hour <= 23 && p.CreateDate.Hour >= 0 && p.CreateDate.Day == dts.Day select p;

                                        //        IList<HADR> dateLag = new List<HADR>();
                                        //        for (int j = 23; j >= 0; j--)
                                        //        {
                                        //            var getByhour = from p in getByday where p.CreateDate.Hour == j orderby p.Datalag ascending select p;

                                        //            if (getByhour.Count() >= 1)
                                        //            {
                                        //                foreach (var val in getByhour)
                                        //                {
                                        //                    var lag = new HADR
                                        //                    {
                                        //                        InfraObjectId = val.InfraObjectId,
                                        //                        PRTimestamp = val.PRTimestamp,
                                        //                        PRLogFile = val.PRLogFile,
                                        //                        DRTimestamp = val.DRTimestamp,
                                        //                        DRLogFile = val.DRLogFile,
                                        //                        Datalag = val.Datalag,
                                        //                        CreateDate = val.CreateDate
                                        //                    };
                                        //                    dateLag.Add(lag);
                                        //                }

                                        //                var datalagList = new HADR
                                        //                {
                                        //                    InfraObjectId = dateLag[0].InfraObjectId,
                                        //                    PRTimestamp = dateLag[0].PRTimestamp,
                                        //                    PRLogFile = dateLag[0].PRLogFile,
                                        //                    DRTimestamp = dateLag[0].DRTimestamp,
                                        //                    DRLogFile = dateLag[0].DRLogFile,
                                        //                    Datalag = dateLag[0].Datalag,
                                        //                    CreateDate = dateLag[0].CreateDate
                                        //                };
                                        //                Hadrdatalaglist.Add(datalagList);
                                        //            }
                                        //        }
                                        //    }
                                        //}

                                        #endregion old code

                                        #region new code.

                                        if (Hadrlog != null)
                                        {
                                            foreach (var Logs in Hadrlog)
                                            {

                                                var datalagList = new HADR
                                                {
                                                    InfraObjectId = Logs.InfraObjectId,
                                                    PRTimestamp = Logs.PRTimestamp,
                                                    PRLogFile = Logs.PRLogFile,
                                                    DRTimestamp = Logs.DRTimestamp,
                                                    DRLogFile = Logs.DRLogFile,
                                                    Datalag = Logs.Datalag,
                                                    CreateDate = Logs.CreateDate
                                                };
                                                Hadrdatalaglist.Add(datalagList);
                                            }
                                        }

                                        #endregion new code.

                                        BusinessFunction businessFtn = Facade.GetBusinessFunctionById(grp.BusinessFunctionId);
                                        TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                                        int ConHour = conDatalag.Hours;
                                        int ConMinute = conDatalag.Minutes;

                                        if (Hadrdatalaglist.Count != 0)
                                        {
                                            foreach (var lo in Hadrdatalaglist)
                                            {
                                                if (lo != null)
                                                {
                                                    string[] Datalg = lo.Datalag.Split(':');
                                                    if (Datalg[0].Contains('.'))
                                                    {
                                                        string[] splitFinal = Datalg[0].Split('.');
                                                        Datalg[0] = splitFinal[1];
                                                    }

                                                    if (Convert.ToInt32(Datalg[0]) < ConHour)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                    }
                                                    else if (Convert.ToInt32(Datalg[0]) == ConHour && Convert.ToInt32(Datalg[1]) <= ConMinute)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                    }
                                                    else
                                                    {
                                                        dvn++;
                                                        deviated.Add(grp.Name);
                                                        appDeviated.Add(app.Name);
                                                        appDeviated.Add(grp.Name);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            deviated.Add(grp.Name);
                                            appDeviated.Add(app.Name);
                                            appDeviated.Add(grp.Name);
                                        }
                                        #endregion For Recovery Type 15 and 21

                                        break;
                                    }
                                default:
                                    {
                                        #region Recovery Type reset All
                                        var log = Facade.GetOracleLogsByDate(grp.Id, txtstart.Text, txtend.Text);
                                        datalaglist.Clear();

                                        #region new code.

                                        if (log != null)
                                        {
                                            foreach (var Logs in log)
                                            {

                                                var datalagList = new OracleLog
                                                {
                                                    InfraObjectId = Logs.InfraObjectId,
                                                    PRLogTime = Logs.PRLogTime,
                                                    PRSequenceNo = Logs.PRSequenceNo,
                                                    DRLogTime = Logs.DRLogTime,
                                                    DRSequenceNo = Logs.DRSequenceNo,
                                                    CurrentDataLag = Logs.CurrentDataLag,
                                                    CreateDate = Logs.CreateDate
                                                };
                                                datalaglist.Add(datalagList);

                                            }
                                        }

                                        #endregion new code.




                                        #region Old Code

                                        //for (int k = 1; k <= 7; k++)
                                        //{
                                        //    if (log != null)
                                        //    {
                                        //        foreach (var lg in log)
                                        //        {
                                        //            DateTime dt = DateTime.Now.AddDays(-k);
                                        //            if (lg.CreateDate.Day.Equals(dt.Day))
                                        //                goto outs;
                                        //        }

                                        //    outs:
                                        //        DateTime dts = DateTime.Now.AddDays(-k);
                                        //        var getByday = from p in log where p.CreateDate.Hour <= 23 && p.CreateDate.Hour >= 0 && p.CreateDate.Day == dts.Day select p;

                                        //        IList<OracleLog> dateLag = new List<OracleLog>();
                                        //        for (int j = 23; j >= 0; j--)
                                        //        {
                                        //            var getByhour = from p in getByday where p.CreateDate.Hour == j orderby p.CurrentDataLag ascending select p;

                                        //            if (getByhour.Count() >= 1)
                                        //            {
                                        //                foreach (var val in getByhour)
                                        //                {
                                        //                    var lag = new OracleLog
                                        //                    {
                                        //                        InfraObjectId = val.InfraObjectId,
                                        //                        PRLogTime = val.PRLogTime,
                                        //                        PRSequenceNo = val.PRSequenceNo,
                                        //                        DRLogTime = val.DRLogTime,
                                        //                        DRSequenceNo = val.DRSequenceNo,
                                        //                        CurrentDataLag = val.CurrentDataLag,
                                        //                        CreateDate = val.CreateDate
                                        //                    };
                                        //                    dateLag.Add(lag);
                                        //                }

                                        //                var datalagList = new OracleLog
                                        //                {
                                        //                    InfraObjectId = dateLag[0].InfraObjectId,
                                        //                    PRLogTime = dateLag[0].PRLogTime,
                                        //                    PRSequenceNo = dateLag[0].PRSequenceNo,
                                        //                    DRLogTime = dateLag[0].DRLogTime,
                                        //                    DRSequenceNo = dateLag[0].DRSequenceNo,
                                        //                    CurrentDataLag = dateLag[0].CurrentDataLag,
                                        //                    CreateDate = dateLag[0].CreateDate
                                        //                };
                                        //                datalaglist.Add(datalagList);
                                        //            }
                                        //        }
                                        //    }
                                        //}
                                        //}
                                        #endregion Old Code


                                        BusinessFunction businessFtn = Facade.GetBusinessFunctionById(grp.BusinessFunctionId);
                                        TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                                        int ConHour = conDatalag.Hours;
                                        int ConMinute = conDatalag.Minutes;


                                        if (datalaglist.Count != 0)
                                        {
                                            foreach (var lo in datalaglist)
                                            {
                                                if (lo != null)
                                                {
                                                    string[] Datalg = lo.CurrentDataLag.Split(':');
                                                    if (Datalg[0].Contains('.'))
                                                    {
                                                        string[] splitFinal = Datalg[0].Split('.');
                                                        Datalg[0] = splitFinal[1];
                                                    }

                                                    if (Convert.ToInt32(Datalg[0]) < ConHour)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else if (Convert.ToInt32(Datalg[0]) == ConHour && Convert.ToInt32(Datalg[1]) <= ConMinute)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else
                                                    {
                                                        dvn++;
                                                        deviated.Add(grp.Name);
                                                        appDeviated.Add(app.Name);
                                                        appDeviated.Add(grp.Name);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            deviated.Add(grp.Name);
                                            appDeviated.Add(app.Name);
                                            appDeviated.Add(grp.Name);
                                        }
                                        #endregion Recovery Type reset All

                                        break;
                                    }

                            }

                        }//End of foreach 
                    }
                    else
                        maint = 6.9888;

                    Double Counts = 0;
                    Double finalCnt = 0;

                    if (maint == 6.9888)
                    {
                        Counts = 0;
                        finalCnt = 6.9888 - maint;
                    }
                    else
                    {
                        Counts = Cnts * 0.0416 / groups.Count;
                        finalCnt = dvn * 0.0416 / groups.Count;
                        //finalCnt = 6.9888 - Counts;
                    }

                    int Column = 0;
                    string Ndx = xlColumn[Column] + rowApp.ToString();
                    cells[Ndx].Formula = i.ToString();
                    cells[Ndx].Font.Size = 9;
                    cells[Ndx].ColumnWidth = 8;
                    cells[Ndx].Font.Color = System.Drawing.Color.Black;
                    cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                    i++;
                    Column++;


                    Ndx = xlColumn[Column] + rowApp.ToString();
                    cells[Ndx].Formula = app.Name;
                    cells[Ndx].Font.Size = 9;
                    cells[Ndx].ColumnWidth = 20;
                    cells[Ndx].Font.Color = System.Drawing.Color.Black;
                    cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                    Column++;


                    Ndx = xlColumn[Column] + rowApp.ToString();
                    cells[Ndx].Formula = Counts.ToString();
                    cells[Ndx].Font.Size = 9;
                    cells[Ndx].ColumnWidth = 20;
                    cells[Ndx].Font.Color = System.Drawing.Color.Black;
                    cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                    Column++;

                    Ndx = xlColumn[Column] + rowApp.ToString();
                    cells[Ndx].Formula = finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString();          //Counts.Equals(6.9887999999999995) ? "0" : finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString();
                    cells[Ndx].Font.Size = 9;
                    cells[Ndx].ColumnWidth = 20;
                    cells[Ndx].Font.Color = System.Drawing.Color.Black;
                    cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;

                    rowApp++;


                }

                int finalCount = row + 1;
                row--;
                rowApp--;


                int finalCounts = row + 3;



                //SpreadsheetGear Chart Part Started
                SpreadsheetGear.IWorksheetWindowInfo windowInfo = reportWorksheet.WindowInfo;
                SpreadsheetGear.IRange dataRange = reportWorksheet.Cells["H25" + ":" + "K" + rowApp];
                int rptHeading = finalCounts + 1;
                int rptHead = finalCount + 3;
                SpreadsheetGear.IRange titleCell = cells["F5"]; // + rptHead
                titleCell.Value = "Rpo Met And Deviation Chart";
                titleCell.HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                titleCell.VerticalAlignment = SpreadsheetGear.VAlign.Top;
                titleCell.Style = reportWorkbook.Styles["Heading 4"];
                titleCell.RowHeight = 28.0;

                double left = windowInfo.ColumnToPoints(1.0);
                double top = windowInfo.RowToPoints(6.0);
                double right = windowInfo.ColumnToPoints(11.0);
                double bottom = windowInfo.RowToPoints(20.0);

                SpreadsheetGear.Charts.IChart chart =
                reportWorksheet.Shapes.AddChart(left, top, right - left, bottom - top).Chart;

                chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                chart.ChartType = SpreadsheetGear.Charts.ChartType.ColumnClustered; //BarClustered  //XYScatterLines

                chart.Axes[SpreadsheetGear.Charts.AxisType.Value].HasTitle = true;
                chart.Axes[SpreadsheetGear.Charts.AxisType.Value].AxisTitle.Text = "Days";

                chart.PlotArea.Format.Fill.ForeColor.RGB = Color.FromArgb(216, 191, 216);
                chart.ChartArea.Format.Fill.ForeColor.RGB = Color.FromArgb(221, 217, 195);

                //int numbersCount = rptHeading + 16;

                cells["F21"].Font.Color = Color.Black;
                cells["F21"].HorizontalAlignment = HAlign.Center;
                cells["F21"].VerticalAlignment = VAlign.Top;
                cells["F21"].Font.Bold = true;
                cells["F21"].Font.Size = 9;
                cells["F21"].Formula = "Rpo Met and Deviation";
                //SpreadsheetGear Chart Part Ended
            }// End of Appgroups
            else
            {
                lblMsg.Text = "No Business Service Found";
            }
        }

        private void GenerateNextSheet(IWorkbook reportWorkbook, IWorksheet reportWorksheet)
        {
            cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "InfraObject Summary Chart";

            cells["A1"].ColumnWidth = 7;

            cells["I4"].Formula = "Montly Summary Report";
            cells["I4"].Font.Color = System.Drawing.Color.Black;
            cells["I4"].Font.Size = 14;
            cells["I4"].Font.Bold = true;
            cells["I4"].ColumnWidth = 30;
            cells["I4"].HorizontalAlignment = SpreadsheetGear.HAlign.Center;
            cells["I4"].VerticalAlignment = SpreadsheetGear.VAlign.Top;
            cells["I4"].Font.Name = "Cambria";

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 50, 20, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 2250, 20, 120, 13);
            reportWorksheet.Cells["A1:G1"].RowHeight = 27;
            reportWorksheet.Cells["A2:G2"].RowHeight = 25;

            var DateTme =Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));// DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
            cells["B5"].Formula = "Report Generated Time : " + DateTme;
            cells["B5"].Font.Bold = true;
            cells["B5"].Font.Size = 10;
            cells["B5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B5"].Font.Name = "Cambria";

            var DstartDate = Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");// Convert.ToDateTime(txtstart.Text).ToString("dd-MMM-yy");
            var startDate = DstartDate; //DateTime.Now.AddDays(-7).ToString("dd-MMM-yyyy HH:mm");
            cells["M5"].Formula = "Start Time : " + startDate;
            cells["M5"].Font.Bold = true;
            cells["M5"].Font.Size = 10;
            cells["M5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["M5"].Font.Name = "Cambria";

            var EstartDate = Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");// Convert.ToDateTime(txtend.Text).ToString("dd-MMM-yy");

            var endDate = EstartDate; //DateTime.Now.AddDays(-1).ToString("dd-MMM-yyyy HH:mm");
            cells["R5"].Formula = "End Time : " + endDate;
            cells["R5"].Font.Bold = true;
            cells["R5"].Font.Size = 10;
            cells["R5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["R5"].Font.Name = "Cambria";

            cells["B28"].Formula = "■ Rpo Met : Datalag <= RPO";
            cells["B28"].Font.Bold = true;
            cells["B28"].Font.Size = 10;
            cells["B28"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B28"].Font.Name = "Cambria";
            cells["B28"].Font.Color = System.Drawing.Color.FromArgb(79, 129, 189);

            cells["B29"].Formula = "■ Deviation : Datalag > RPO";
            cells["B29"].Font.Bold = true;
            cells["B29"].Font.Size = 10;
            cells["B29"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B29"].Font.Name = "Cambria";
            cells["B29"].Font.Color = System.Drawing.Color.FromArgb(192, 80, 77);

            cells["B30"].Formula = "■ Incident : Total number of incident raised in week against the application groups ";
            cells["B30"].Font.Bold = true;
            cells["B30"].Font.Size = 10;
            cells["B30"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B30"].Font.Name = "Cambria";
            cells["B30"].Font.Color = System.Drawing.Color.FromArgb(155, 187, 89);

            int row = 28;
            //int i = 1;
            string[] xlCol = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };

            cells["AG" + row.ToString()].Formula = "Business Service Name";
            cells["AG" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AG" + row.ToString()].Font.Size = 11;
            cells["AG" + row.ToString()].ColumnWidth = 30;
            cells["AG" + row.ToString()].Font.Bold = true;
            cells["AG" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AG" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            cells["AH" + row.ToString()].Formula = "InfraObject Name";
            cells["AH" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AH" + row.ToString()].Font.Size = 11;
            cells["AH" + row.ToString()].ColumnWidth = 30;
            cells["AH" + row.ToString()].Font.Bold = true;
            cells["AH" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AH" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            cells["AI" + row.ToString()].Formula = "Rpo Met";
            cells["AI" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AI" + row.ToString()].Font.Size = 11;
            cells["AI" + row.ToString()].ColumnWidth = 20;
            cells["AI" + row.ToString()].Font.Bold = true;
            cells["AI" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AI" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            cells["AJ" + row.ToString()].Formula = "Deviation";
            cells["AJ" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AJ" + row.ToString()].Font.Size = 11;
            cells["AJ" + row.ToString()].ColumnWidth = 20;
            cells["AJ" + row.ToString()].Font.Bold = true;
            cells["AJ" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AJ" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            cells["AK" + row.ToString()].Formula = "Incident";
            cells["AK" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AK" + row.ToString()].Font.Size = 11;
            cells["AK" + row.ToString()].ColumnWidth = 20;
            cells["AK" + row.ToString()].Font.Bold = true;
            cells["AK" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AK" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            SpreadsheetGear.IRange range = reportWorksheet.Cells["AG27:AK27"];
            IBorder border = range.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];
            border.LineStyle = SpreadsheetGear.LineStyle.Continous;
            border.Color = System.Drawing.Color.Black;
            border.Weight = SpreadsheetGear.BorderWeight.Medium;

            SpreadsheetGear.IRange rangeOne = reportWorksheet.Cells["AG28:AK28"];
            IBorder borderOne = rangeOne.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];
            borderOne.LineStyle = SpreadsheetGear.LineStyle.Continous;
            borderOne.Color = System.Drawing.Color.Black;
            borderOne.Weight = SpreadsheetGear.BorderWeight.Thin;

            row++;


            var Appgroups = Facade.GetAllBusinessServices();
            int dataCount = 0;
            int rowApp = 29;

            if (Appgroups != null)
            {
                foreach (var app in Appgroups)
                {
                    string[] xlColumn = { "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP" };
                    IList<InfraObject> groups = new List<InfraObject>();
                    groups = Facade.GetInfraObjectByBusinessServiceId(app.Id);
                    dataCount++;

                    deviated.Clear();
                    int Cnts = 0;
                    int rpoCount = row;
                    double mnt = 0;
                    int dev = 0;
                    if (groups != null)
                    {
                        foreach (var grp in groups)
                        {

                            Cnts = 0;
                            dev = 0;
                            switch (grp.RecoveryType)
                            {
                                case (int)ReplicationType.DB2HADR:
                                case (int)ReplicationType.DB2DataSync:
                                    {
                                        #region For recovery Type 15 and 21

                                        var Hadrlog = Facade.GetHadrMonitorByDate(grp.Id, txtstart.Text, txtend.Text);
                                        Hadrdatalaglist.Clear();
                                        mnt = 0;

                                        #region new code.

                                        if (Hadrlog != null)
                                        {
                                            foreach (var Logs in Hadrlog)
                                            {

                                                var datalagList = new HADR
                                                {
                                                    InfraObjectId = Logs.InfraObjectId,
                                                    PRTimestamp = Logs.PRTimestamp,
                                                    PRLogFile = Logs.PRLogFile,
                                                    DRTimestamp = Logs.DRTimestamp,
                                                    DRLogFile = Logs.DRLogFile,
                                                    Datalag = Logs.Datalag,
                                                    CreateDate = Logs.CreateDate
                                                };
                                                Hadrdatalaglist.Add(datalagList);
                                            }
                                        }
                                        else
                                            mnt = 6.9888;



                                        #endregion new code.

                                        if (Hadrdatalaglist.Count != 0)
                                        {
                                            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(grp.BusinessFunctionId);
                                            TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                                            int ConHour = conDatalag.Hours;
                                            int ConMinute = conDatalag.Minutes;


                                            foreach (var lo in Hadrdatalaglist)
                                            {
                                                if (lo != null)
                                                {
                                                    string[] Datalg = lo.Datalag.Split(':');
                                                    if (Datalg[0].Contains('.'))
                                                    {
                                                        string[] splitFinal = Datalg[0].Split('.');
                                                        Datalg[0] = splitFinal[1];
                                                    }
                                                    if (Convert.ToInt32(Datalg[0]) < ConHour)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else if (Convert.ToInt32(Datalg[0]) == ConHour && Convert.ToInt32(Datalg[1]) < ConMinute)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else
                                                    {
                                                        dev++;
                                                        deviated.Add(grp.Name);
                                                        appDeviated.Add(app.Name);
                                                        appDeviated.Add(grp.Name);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //mnt = 6.9888;
                                            deviated.Add(grp.Name);
                                            appDeviated.Add(app.Name);
                                            appDeviated.Add(grp.Name);
                                        }
                                        //Outer: ;
                                        Double Counts = 0;
                                        Double finalCnt = 0;
                                        if (mnt == 6.9888)
                                        {
                                            Counts = 0;
                                            finalCnt = 6.9888 - mnt;
                                        }
                                        else
                                        {
                                            Counts = Cnts * 0.0416;
                                            finalCnt = dev * 0.0416; //6.9888 - Counts;
                                        }

                                        int Column = 0;
                                        string Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = app.Name;
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = app.Name + "__" + grp.Name;
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 30;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = Counts.ToString(); // Counts.Equals(6.9887999999999995) ? "7" : Counts.ToString();
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString(); //Counts.Equals(6.9887999999999995) ? "0" :
                                        //finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString();
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;

                                        rowApp++;

                                        #endregion For recovery Type 15 and 21

                                        break;
                                    }
                                default:
                                    {
                                        #region For recovery Type except 15 and 21

                                        var log = Facade.GetOracleLogsByDate(grp.Id, txtstart.Text, txtend.Text);
                                        datalaglist.Clear();
                                        mnt = 0;
                                        #region new code

                                        if (log != null)
                                        {
                                            foreach (var Logs in log)
                                            {

                                                var datalagList = new OracleLog
                                                {
                                                    InfraObjectId = Logs.InfraObjectId,
                                                    PRLogTime = Logs.PRLogTime,
                                                    PRSequenceNo = Logs.PRSequenceNo,
                                                    DRLogTime = Logs.DRLogTime,
                                                    DRSequenceNo = Logs.DRSequenceNo,
                                                    CurrentDataLag = Logs.CurrentDataLag,
                                                    CreateDate = Logs.CreateDate
                                                };
                                                datalaglist.Add(datalagList);

                                            }
                                        }
                                        else
                                        {
                                            mnt = 6.9888;
                                        }
                                        #endregion



                                        if (datalaglist.Count != 0)
                                        {
                                            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(grp.BusinessFunctionId);
                                            TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                                            int ConHour = conDatalag.Hours;
                                            int ConMinute = conDatalag.Minutes;


                                            foreach (var lo in datalaglist)
                                            {
                                                if (lo != null)
                                                {
                                                    string[] Datalg = lo.CurrentDataLag.Split(':');
                                                    if (Datalg[0].Contains('.'))
                                                    {
                                                        string[] splitFinal = Datalg[0].Split('.');
                                                        Datalg[0] = splitFinal[1];
                                                    }
                                                    if (Convert.ToInt32(Datalg[0]) < ConHour)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else if (Convert.ToInt32(Datalg[0]) == ConHour && Convert.ToInt32(Datalg[1]) < ConMinute)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else
                                                    {
                                                        dev++;
                                                        deviated.Add(grp.Name);
                                                        appDeviated.Add(app.Name);
                                                        appDeviated.Add(grp.Name);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //mnt = 6.9888;
                                            deviated.Add(grp.Name);
                                            appDeviated.Add(app.Name);
                                            appDeviated.Add(grp.Name);
                                        }
                                        //Outer: ;
                                        Double Counts = 0;
                                        Double finalCnt = 0;
                                        if (mnt == 6.9888)
                                        {
                                            Counts = 0;
                                            finalCnt = 6.9888 - mnt;
                                        }
                                        else
                                        {
                                            Counts = Cnts * 0.0416;
                                            finalCnt = dev * 0.0416; //6.9888 - Counts;
                                        }

                                        int Column = 0;
                                        string Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = app.Name;
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = app.Name + "__" + grp.Name;
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 30;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = Counts.ToString(); // Counts.Equals(6.9887999999999995) ? "7" : Counts.ToString();
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString(); //Counts.Equals(6.9887999999999995) ? "0" :
                                        //finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString();
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;

                                        rowApp++;

                                        #endregion For recovery Type except 15 and 21

                                        break;
                                    }
                            }

                        }//End of Foreach

                    }//End of IF
                }

                int finalCount = row + 1;
                row--;
                rowApp--;


                int finalCounts = row + 3;



                //SpreadsheetGear Chart Part Started
                SpreadsheetGear.IWorksheetWindowInfo windowInfo = reportWorksheet.WindowInfo;
                SpreadsheetGear.IRange dataRange = reportWorksheet.Cells["AH28" + ":" + "AK" + rowApp];
                int rptHeading = finalCounts + 1;
                int rptHead = finalCount + 3;
                SpreadsheetGear.IRange titleCell = cells["I5"]; // + rptHead
                titleCell.Value = "Rpo Met And Deviation Chart";
                titleCell.HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                titleCell.VerticalAlignment = SpreadsheetGear.VAlign.Top;
                titleCell.Style = reportWorkbook.Styles["Heading 4"];
                titleCell.RowHeight = 28.0;

                double left = windowInfo.ColumnToPoints(1.0);
                double top = windowInfo.RowToPoints(6.0);
                double right = windowInfo.ColumnToPoints(37.0);
                double bottom = windowInfo.RowToPoints(24.0);

                SpreadsheetGear.Charts.IChart chart =
                reportWorksheet.Shapes.AddChart(left, top, right - left, bottom - top).Chart;

                chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                chart.ChartType = SpreadsheetGear.Charts.ChartType.ColumnClustered; //BarClustered  //XYScatterLines

                chart.Axes[SpreadsheetGear.Charts.AxisType.Value].HasTitle = true;
                chart.Axes[SpreadsheetGear.Charts.AxisType.Value].AxisTitle.Text = "Days";

                chart.PlotArea.Format.Fill.ForeColor.RGB = Color.FromArgb(216, 191, 216);
                chart.ChartArea.Format.Fill.ForeColor.RGB = Color.FromArgb(221, 217, 195);

                //int numbersCount = rptHeading + 16;

                cells["I25"].Font.Color = Color.Black;
                cells["I25"].HorizontalAlignment = HAlign.Center;
                cells["I25"].VerticalAlignment = VAlign.Top;
                cells["I25"].Font.Bold = true;
                cells["I25"].Font.Size = 9;
                cells["I25"].Formula = "Rpo Met and Deviation";
                //SpreadsheetGear Chart Part Ended
            }
            else
            {
                lblMsg.Text = "Business Service Not Found";
            }
        }



        private void MCreateExcelReport(string startDate, string enddate)
        {
            lblMsg.Text = string.Empty;
            workbookSet = Factory.GetWorkbookSet();
            ssFile = Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (SpreadsheetGear.IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
            MGenerateExcel(reportWorkbook, reportWorksheet, startDate, enddate);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
            MGenerateNextSheet(reportWorkbook, reportWorksheet, startDate, enddate);

            reportWorkbook.Worksheets["Sheet1"].Delete(); // Default Worksheet
            OpenExcelFile(reportWorkbook, reportWorksheet);
        }

        private void MGenerateExcel(IWorkbook reportWorkbook, IWorksheet reportWorksheet, string sDate, string Edate)
        {

            cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "Business Service Summary Chart";
            cells["A1"].ColumnWidth = 7;

            cells["F4"].Formula = "Montly Summary Report";
            cells["F4"].Font.Color = System.Drawing.Color.Black;
            cells["F4"].Font.Size = 14;
            cells["F4"].Font.Bold = true;
            cells["F4"].ColumnWidth = 30;
            cells["F4"].HorizontalAlignment = SpreadsheetGear.HAlign.Center;
            cells["F4"].VerticalAlignment = SpreadsheetGear.VAlign.Top;
            cells["F4"].Font.Name = "Cambria";

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 50, 20, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 840, 20, 120, 13);
            reportWorksheet.Cells["A1:G1"].RowHeight = 27;
            reportWorksheet.Cells["A2:G2"].RowHeight = 25;

            var DateTme = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")); //DateTime.Now.ToString("dd-MMM-yy HH:mm");
            cells["B5"].Formula = "Report Generated Time : " + DateTme;
            cells["B5"].Font.Bold = true;
            cells["B5"].Font.Size = 10;
            cells["B5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B5"].Font.Name = "Cambria";

            var DstartDate =Utility.Formatdate(Convert.ToDateTime(sDate).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");// Convert.ToDateTime(sDate).ToString("dd-MMM-yy");

            var startDate = DstartDate;//DateTime.Now.AddDays(-7).ToString("dd-MMM-yy HH:mm");
            cells["H5"].Formula = "Start Time : " + startDate;
            cells["H5"].Font.Bold = true;
            cells["H5"].Font.Size = 10;
            cells["H5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["H5"].Font.Name = "Cambria";

            var EstartDate = Utility.Formatdate(Convert.ToDateTime(Edate).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");//Convert.ToDateTime(Edate).ToString("dd-MMM-yy");

            var endDate = EstartDate; //DateTime.Now.AddDays(-1).ToString("dd-MMM-yy HH:mm");
            cells["J5"].Formula = "End Time : " + endDate;
            cells["J5"].Font.Bold = true;
            cells["J5"].Font.Size = 10;
            cells["J5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["J5"].Font.Name = "Cambria";

            cells["B25"].Formula = "■ Rpo Met : Datalag <= RPO";
            cells["B25"].Font.Bold = true;
            cells["B25"].Font.Size = 10;
            cells["B25"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B25"].Font.Name = "Cambria";
            cells["B25"].Font.Color = System.Drawing.Color.FromArgb(79, 129, 189);

            cells["B26"].Formula = "■ Deviation : Datalag > RPO";
            cells["B26"].Font.Bold = true;
            cells["B26"].Font.Size = 10;
            cells["B26"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B26"].Font.Name = "Cambria";
            cells["B26"].Font.Color = System.Drawing.Color.FromArgb(192, 80, 77);

            cells["B27"].Formula = "■ Incident : Total number of incident raised in week against the application groups ";
            cells["B27"].Font.Bold = true;
            cells["B27"].Font.Size = 10;
            cells["B27"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B27"].Font.Name = "Cambria";
            cells["B27"].Font.Color = System.Drawing.Color.FromArgb(155, 187, 89);

            int row = 25;
            int i = 1;
            string[] xlCol = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };

            cells["G" + row.ToString()].Formula = "SR.No";
            cells["G" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["G" + row.ToString()].Font.Size = 10;
            cells["G" + row.ToString()].ColumnWidth = 8;
            cells["G" + row.ToString()].Font.Bold = true;
            cells["G" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["G" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["G" + row.ToString()].Font.Color = System.Drawing.Color.White;

            cells["H" + row.ToString()].Formula = "Business Service Name";
            cells["H" + row.ToString()].Font.Color = System.Drawing.Color.Black;
            cells["H" + row.ToString()].Font.Size = 10;
            cells["H" + row.ToString()].ColumnWidth = 30;
            cells["H" + row.ToString()].Font.Bold = true;
            cells["H" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["H" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["H" + row.ToString()].Font.Color = System.Drawing.Color.White;

            cells["I" + row.ToString()].Formula = "Rpo Met";
            cells["I" + row.ToString()].Font.Color = System.Drawing.Color.Black;
            cells["I" + row.ToString()].Font.Size = 10;
            cells["I" + row.ToString()].ColumnWidth = 20;
            cells["I" + row.ToString()].Font.Bold = true;
            cells["I" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["I" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["I" + row.ToString()].Font.Color = System.Drawing.Color.White;

            cells["J" + row.ToString()].Formula = "Deviation";
            cells["J" + row.ToString()].Font.Color = System.Drawing.Color.Black;
            cells["J" + row.ToString()].Font.Size = 10;
            cells["J" + row.ToString()].ColumnWidth = 20;
            cells["J" + row.ToString()].Font.Bold = true;
            cells["J" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["J" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["J" + row.ToString()].Font.Color = System.Drawing.Color.White;

            cells["K" + row.ToString()].Formula = "Incident";
            cells["K" + row.ToString()].Font.Color = System.Drawing.Color.Black;
            cells["K" + row.ToString()].Font.Size = 10;
            cells["K" + row.ToString()].ColumnWidth = 20;
            cells["K" + row.ToString()].Font.Bold = true;
            cells["K" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["K" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);
            cells["K" + row.ToString()].Font.Color = System.Drawing.Color.White;

            SpreadsheetGear.IRange range = reportWorksheet.Cells["G24:K24"];
            IBorder border = range.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];
            border.LineStyle = SpreadsheetGear.LineStyle.Continous;
            border.Color = System.Drawing.Color.Black;
            border.Weight = SpreadsheetGear.BorderWeight.Medium;

            SpreadsheetGear.IRange rangeOne = reportWorksheet.Cells["G25:K25"];
            IBorder borderOne = rangeOne.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];
            borderOne.LineStyle = SpreadsheetGear.LineStyle.Continous;
            borderOne.Color = System.Drawing.Color.Black;
            borderOne.Weight = SpreadsheetGear.BorderWeight.Thin;
            row++;

            //int xlRow = 26;
            var Appgroups = Facade.GetAllBusinessServices();
            int dataCount = 0;
            int rowApp = 26;

            if (Appgroups != null)
            {
                foreach (var app in Appgroups)
                {
                    string[] xlColumn = { "G", "H", "I", "J", "K" };
                    IList<InfraObject> groups = new List<InfraObject>();
                    groups = Facade.GetInfraObjectByBusinessServiceId(app.Id);
                    dataCount++;

                    deviated.Clear();
                    int Cnts = 0;
                    int rpoCount = row;
                    double maint = 0;
                    int dvn = 0;
                    if (groups != null)
                    {
                        foreach (var grp in groups)
                        {
                            switch (grp.RecoveryType)
                            {
                                case (int)ReplicationType.DB2HADR:
                                case (int)ReplicationType.DB2DataSync:
                                    {
                                        #region For Recovery Type 15 and 21

                                        var Hadrlog = Facade.GetHadrMonitorByDate(grp.Id, sDate, Edate);
                                        Hadrdatalaglist.Clear();

                                        #region new code.

                                        if (Hadrlog != null)
                                        {
                                            foreach (var Logs in Hadrlog)
                                            {

                                                var datalagList = new HADR
                                                {
                                                    InfraObjectId = Logs.InfraObjectId,
                                                    PRTimestamp = Logs.PRTimestamp,
                                                    PRLogFile = Logs.PRLogFile,
                                                    DRTimestamp = Logs.DRTimestamp,
                                                    DRLogFile = Logs.DRLogFile,
                                                    Datalag = Logs.Datalag,
                                                    CreateDate = Logs.CreateDate
                                                };
                                                Hadrdatalaglist.Add(datalagList);
                                            }
                                        }

                                        #endregion new code.

                                        BusinessFunction businessFtn = Facade.GetBusinessFunctionById(grp.BusinessFunctionId);
                                        TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                                        int ConHour = conDatalag.Hours;
                                        int ConMinute = conDatalag.Minutes;

                                        if (Hadrdatalaglist.Count != 0)
                                        {
                                            foreach (var lo in Hadrdatalaglist)
                                            {
                                                if (lo != null)
                                                {
                                                    string[] Datalg = lo.Datalag.Split(':');
                                                    if (Datalg[0].Contains('.'))
                                                    {
                                                        string[] splitFinal = Datalg[0].Split('.');
                                                        Datalg[0] = splitFinal[1];
                                                    }

                                                    if (Convert.ToInt32(Datalg[0]) < ConHour)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                    }
                                                    else if (Convert.ToInt32(Datalg[0]) == ConHour && Convert.ToInt32(Datalg[1]) <= ConMinute)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                    }
                                                    else
                                                    {
                                                        dvn++;
                                                        deviated.Add(grp.Name);
                                                        appDeviated.Add(app.Name);
                                                        appDeviated.Add(grp.Name);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            deviated.Add(grp.Name);
                                            appDeviated.Add(app.Name);
                                            appDeviated.Add(grp.Name);
                                        }
                                        #endregion For Recovery Type 15 and 21

                                        break;
                                    }
                                default:
                                    {
                                        #region Recovery Type reset All
                                        var log = Facade.GetOracleLogsByDate(grp.Id, sDate, Edate);
                                        datalaglist.Clear();

                                        #region new code.

                                        if (log != null)
                                        {
                                            foreach (var Logs in log)
                                            {

                                                var datalagList = new OracleLog
                                                {
                                                    InfraObjectId = Logs.InfraObjectId,
                                                    PRLogTime = Logs.PRLogTime,
                                                    PRSequenceNo = Logs.PRSequenceNo,
                                                    DRLogTime = Logs.DRLogTime,
                                                    DRSequenceNo = Logs.DRSequenceNo,
                                                    CurrentDataLag = Logs.CurrentDataLag,
                                                    CreateDate = Logs.CreateDate
                                                };
                                                datalaglist.Add(datalagList);

                                            }
                                        }

                                        #endregion new code.


                                        BusinessFunction businessFtn = Facade.GetBusinessFunctionById(grp.BusinessFunctionId);
                                        TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                                        int ConHour = conDatalag.Hours;
                                        int ConMinute = conDatalag.Minutes;


                                        if (datalaglist.Count != 0)
                                        {
                                            foreach (var lo in datalaglist)
                                            {
                                                if (lo != null)
                                                {
                                                    string[] Datalg = lo.CurrentDataLag.Split(':');
                                                    if (Datalg[0].Contains('.'))
                                                    {
                                                        string[] splitFinal = Datalg[0].Split('.');
                                                        Datalg[0] = splitFinal[1];
                                                    }

                                                    if (Convert.ToInt32(Datalg[0]) < ConHour)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else if (Convert.ToInt32(Datalg[0]) == ConHour && Convert.ToInt32(Datalg[1]) <= ConMinute)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else
                                                    {
                                                        dvn++;
                                                        deviated.Add(grp.Name);
                                                        appDeviated.Add(app.Name);
                                                        appDeviated.Add(grp.Name);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            deviated.Add(grp.Name);
                                            appDeviated.Add(app.Name);
                                            appDeviated.Add(grp.Name);
                                        }
                                        #endregion Recovery Type reset All

                                        break;
                                    }

                            }

                        }//End of foreach 
                    }
                    else
                        maint = 6.9888;

                    Double Counts = 0;
                    Double finalCnt = 0;

                    if (maint == 6.9888)
                    {
                        Counts = 0;
                        finalCnt = 6.9888 - maint;
                    }
                    else
                    {
                        Counts = Cnts * 0.0416 / groups.Count;
                        finalCnt = dvn * 0.0416 / groups.Count;
                        //finalCnt = 6.9888 - Counts;
                    }

                    int Column = 0;
                    string Ndx = xlColumn[Column] + rowApp.ToString();
                    cells[Ndx].Formula = i.ToString();
                    cells[Ndx].Font.Size = 9;
                    cells[Ndx].ColumnWidth = 8;
                    cells[Ndx].Font.Color = System.Drawing.Color.Black;
                    cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                    i++;
                    Column++;


                    Ndx = xlColumn[Column] + rowApp.ToString();
                    cells[Ndx].Formula = app.Name;
                    cells[Ndx].Font.Size = 9;
                    cells[Ndx].ColumnWidth = 20;
                    cells[Ndx].Font.Color = System.Drawing.Color.Black;
                    cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                    Column++;


                    Ndx = xlColumn[Column] + rowApp.ToString();
                    cells[Ndx].Formula = Counts.ToString();
                    cells[Ndx].Font.Size = 9;
                    cells[Ndx].ColumnWidth = 20;
                    cells[Ndx].Font.Color = System.Drawing.Color.Black;
                    cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                    Column++;

                    Ndx = xlColumn[Column] + rowApp.ToString();
                    cells[Ndx].Formula = finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString();          //Counts.Equals(6.9887999999999995) ? "0" : finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString();
                    cells[Ndx].Font.Size = 9;
                    cells[Ndx].ColumnWidth = 20;
                    cells[Ndx].Font.Color = System.Drawing.Color.Black;
                    cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;

                    rowApp++;


                }

                int finalCount = row + 1;
                row--;
                rowApp--;


                int finalCounts = row + 3;



                //SpreadsheetGear Chart Part Started
                SpreadsheetGear.IWorksheetWindowInfo windowInfo = reportWorksheet.WindowInfo;
                SpreadsheetGear.IRange dataRange = reportWorksheet.Cells["H25" + ":" + "K" + rowApp];
                int rptHeading = finalCounts + 1;
                int rptHead = finalCount + 3;
                SpreadsheetGear.IRange titleCell = cells["F5"]; // + rptHead
                titleCell.Value = "Rpo Met And Deviation Chart";
                titleCell.HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                titleCell.VerticalAlignment = SpreadsheetGear.VAlign.Top;
                titleCell.Style = reportWorkbook.Styles["Heading 4"];
                titleCell.RowHeight = 28.0;

                double left = windowInfo.ColumnToPoints(1.0);
                double top = windowInfo.RowToPoints(6.0);
                double right = windowInfo.ColumnToPoints(11.0);
                double bottom = windowInfo.RowToPoints(20.0);

                SpreadsheetGear.Charts.IChart chart =
                reportWorksheet.Shapes.AddChart(left, top, right - left, bottom - top).Chart;

                chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                chart.ChartType = SpreadsheetGear.Charts.ChartType.ColumnClustered; //BarClustered  //XYScatterLines

                chart.Axes[SpreadsheetGear.Charts.AxisType.Value].HasTitle = true;
                chart.Axes[SpreadsheetGear.Charts.AxisType.Value].AxisTitle.Text = "Days";

                chart.PlotArea.Format.Fill.ForeColor.RGB = Color.FromArgb(216, 191, 216);
                chart.ChartArea.Format.Fill.ForeColor.RGB = Color.FromArgb(221, 217, 195);

                //int numbersCount = rptHeading + 16;

                cells["F21"].Font.Color = Color.Black;
                cells["F21"].HorizontalAlignment = HAlign.Center;
                cells["F21"].VerticalAlignment = VAlign.Top;
                cells["F21"].Font.Bold = true;
                cells["F21"].Font.Size = 9;
                cells["F21"].Formula = "Rpo Met and Deviation";
                //SpreadsheetGear Chart Part Ended
            }// End of Appgroups
            else
            {
                lblMsg.Text = "No Business Service Found";
            }
        }

        private void MGenerateNextSheet(IWorkbook reportWorkbook, IWorksheet reportWorksheet, string sDate, string Edate)
        {
            cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "InfraObject Summary Chart";

            cells["A1"].ColumnWidth = 7;

            cells["I4"].Formula = "Montly Summary Report";
            cells["I4"].Font.Color = System.Drawing.Color.Black;
            cells["I4"].Font.Size = 14;
            cells["I4"].Font.Bold = true;
            cells["I4"].ColumnWidth = 30;
            cells["I4"].HorizontalAlignment = SpreadsheetGear.HAlign.Center;
            cells["I4"].VerticalAlignment = SpreadsheetGear.VAlign.Top;
            cells["I4"].Font.Name = "Cambria";

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 50, 20, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 2250, 20, 120, 13);
            reportWorksheet.Cells["A1:G1"].RowHeight = 27;
            reportWorksheet.Cells["A2:G2"].RowHeight = 25;

            var DateTme = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));// DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
            cells["B5"].Formula = "Report Generated Time : " + DateTme;
            cells["B5"].Font.Bold = true;
            cells["B5"].Font.Size = 10;
            cells["B5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B5"].Font.Name = "Cambria";

            var DstartDate = Utility.Formatdate(Convert.ToDateTime(sDate).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");// Convert.ToDateTime(sDate).ToString("dd-MMM-yy");
            var startDate = DstartDate; //DateTime.Now.AddDays(-7).ToString("dd-MMM-yyyy HH:mm");
            cells["M5"].Formula = "Start Time : " + startDate;
            cells["M5"].Font.Bold = true;
            cells["M5"].Font.Size = 10;
            cells["M5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["M5"].Font.Name = "Cambria";

            var EstartDate = Utility.Formatdate(Convert.ToDateTime(Edate).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //Convert.ToDateTime(Edate).ToString("dd-MMM-yy");
            var endDate = EstartDate; //DateTime.Now.AddDays(-1).ToString("dd-MMM-yyyy HH:mm");
            cells["R5"].Formula = "End Time : " + endDate;
            cells["R5"].Font.Bold = true;
            cells["R5"].Font.Size = 10;
            cells["R5"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["R5"].Font.Name = "Cambria";

            cells["B28"].Formula = "■ Rpo Met : Datalag <= RPO";
            cells["B28"].Font.Bold = true;
            cells["B28"].Font.Size = 10;
            cells["B28"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B28"].Font.Name = "Cambria";
            cells["B28"].Font.Color = System.Drawing.Color.FromArgb(79, 129, 189);

            cells["B29"].Formula = "■ Deviation : Datalag > RPO";
            cells["B29"].Font.Bold = true;
            cells["B29"].Font.Size = 10;
            cells["B29"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B29"].Font.Name = "Cambria";
            cells["B29"].Font.Color = System.Drawing.Color.FromArgb(192, 80, 77);

            cells["B30"].Formula = "■ Incident : Total number of incident raised in week against the application groups ";
            cells["B30"].Font.Bold = true;
            cells["B30"].Font.Size = 10;
            cells["B30"].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["B30"].Font.Name = "Cambria";
            cells["B30"].Font.Color = System.Drawing.Color.FromArgb(155, 187, 89);

            int row = 28;
            //int i = 1;
            string[] xlCol = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };

            cells["AG" + row.ToString()].Formula = "Business Service Name";
            cells["AG" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AG" + row.ToString()].Font.Size = 11;
            cells["AG" + row.ToString()].ColumnWidth = 30;
            cells["AG" + row.ToString()].Font.Bold = true;
            cells["AG" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AG" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            cells["AH" + row.ToString()].Formula = "InfraObject Name";
            cells["AH" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AH" + row.ToString()].Font.Size = 11;
            cells["AH" + row.ToString()].ColumnWidth = 30;
            cells["AH" + row.ToString()].Font.Bold = true;
            cells["AH" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AH" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            cells["AI" + row.ToString()].Formula = "Rpo Met";
            cells["AI" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AI" + row.ToString()].Font.Size = 11;
            cells["AI" + row.ToString()].ColumnWidth = 20;
            cells["AI" + row.ToString()].Font.Bold = true;
            cells["AI" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AI" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            cells["AJ" + row.ToString()].Formula = "Deviation";
            cells["AJ" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AJ" + row.ToString()].Font.Size = 11;
            cells["AJ" + row.ToString()].ColumnWidth = 20;
            cells["AJ" + row.ToString()].Font.Bold = true;
            cells["AJ" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AJ" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            cells["AK" + row.ToString()].Formula = "Incident";
            cells["AK" + row.ToString()].Font.Color = System.Drawing.Color.White;
            cells["AK" + row.ToString()].Font.Size = 11;
            cells["AK" + row.ToString()].ColumnWidth = 20;
            cells["AK" + row.ToString()].Font.Bold = true;
            cells["AK" + row.ToString()].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
            cells["AK" + row.ToString()].Interior.Color = System.Drawing.Color.FromArgb(55, 95, 145);

            SpreadsheetGear.IRange range = reportWorksheet.Cells["AG27:AK27"];
            IBorder border = range.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];
            border.LineStyle = SpreadsheetGear.LineStyle.Continous;
            border.Color = System.Drawing.Color.Black;
            border.Weight = SpreadsheetGear.BorderWeight.Medium;

            SpreadsheetGear.IRange rangeOne = reportWorksheet.Cells["AG28:AK28"];
            IBorder borderOne = rangeOne.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];
            borderOne.LineStyle = SpreadsheetGear.LineStyle.Continous;
            borderOne.Color = System.Drawing.Color.Black;
            borderOne.Weight = SpreadsheetGear.BorderWeight.Thin;

            row++;


            var Appgroups = Facade.GetAllBusinessServices();
            int dataCount = 0;
            int rowApp = 29;

            if (Appgroups != null)
            {
                foreach (var app in Appgroups)
                {
                    string[] xlColumn = { "AG", "AH", "AI", "AJ", "AK", "AL", "AM", "AN", "AO", "AP" };
                    IList<InfraObject> groups = new List<InfraObject>();
                    groups = Facade.GetInfraObjectByBusinessServiceId(app.Id);
                    dataCount++;

                    deviated.Clear();
                    int Cnts = 0;
                    int rpoCount = row;
                    double mnt = 0;
                    int dev = 0;
                    if (groups != null)
                    {
                        foreach (var grp in groups)
                        {

                            Cnts = 0;
                            dev = 0;
                            switch (grp.RecoveryType)
                            {
                                case (int)ReplicationType.DB2HADR:
                                case (int)ReplicationType.DB2DataSync:
                                    {
                                        #region For recovery Type 15 and 21

                                        var Hadrlog = Facade.GetHadrMonitorByDate(grp.Id, sDate, Edate);
                                        Hadrdatalaglist.Clear();
                                        mnt = 0;

                                        #region new code.

                                        if (Hadrlog != null)
                                        {
                                            foreach (var Logs in Hadrlog)
                                            {

                                                var datalagList = new HADR
                                                {
                                                    InfraObjectId = Logs.InfraObjectId,
                                                    PRTimestamp = Logs.PRTimestamp,
                                                    PRLogFile = Logs.PRLogFile,
                                                    DRTimestamp = Logs.DRTimestamp,
                                                    DRLogFile = Logs.DRLogFile,
                                                    Datalag = Logs.Datalag,
                                                    CreateDate = Logs.CreateDate
                                                };
                                                Hadrdatalaglist.Add(datalagList);
                                            }
                                        }
                                        else
                                            mnt = 6.9888;



                                        #endregion new code.

                                        if (Hadrdatalaglist.Count != 0)
                                        {
                                            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(grp.BusinessFunctionId);
                                            TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                                            int ConHour = conDatalag.Hours;
                                            int ConMinute = conDatalag.Minutes;


                                            foreach (var lo in Hadrdatalaglist)
                                            {
                                                if (lo != null)
                                                {
                                                    string[] Datalg = lo.Datalag.Split(':');
                                                    if (Datalg[0].Contains('.'))
                                                    {
                                                        string[] splitFinal = Datalg[0].Split('.');
                                                        Datalg[0] = splitFinal[1];
                                                    }
                                                    if (Convert.ToInt32(Datalg[0]) < ConHour)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else if (Convert.ToInt32(Datalg[0]) == ConHour && Convert.ToInt32(Datalg[1]) < ConMinute)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else
                                                    {
                                                        dev++;
                                                        deviated.Add(grp.Name);
                                                        appDeviated.Add(app.Name);
                                                        appDeviated.Add(grp.Name);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //mnt = 6.9888;
                                            deviated.Add(grp.Name);
                                            appDeviated.Add(app.Name);
                                            appDeviated.Add(grp.Name);
                                        }
                                        //Outer: ;
                                        Double Counts = 0;
                                        Double finalCnt = 0;
                                        if (mnt == 6.9888)
                                        {
                                            Counts = 0;
                                            finalCnt = 6.9888 - mnt;
                                        }
                                        else
                                        {
                                            Counts = Cnts * 0.0416;
                                            finalCnt = dev * 0.0416; //6.9888 - Counts;
                                        }

                                        int Column = 0;
                                        string Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = app.Name;
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = app.Name + "__" + grp.Name;
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 30;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = Counts.ToString(); // Counts.Equals(6.9887999999999995) ? "7" : Counts.ToString();
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString(); //Counts.Equals(6.9887999999999995) ? "0" :
                                        //finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString();
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;

                                        rowApp++;

                                        #endregion For recovery Type 15 and 21

                                        break;
                                    }
                                default:
                                    {
                                        #region For recovery Type except 15 and 21

                                        var log = Facade.GetOracleLogsByDate(grp.Id, sDate, Edate);
                                        datalaglist.Clear();
                                        mnt = 0;
                                        #region new code

                                        if (log != null)
                                        {
                                            foreach (var Logs in log)
                                            {

                                                var datalagList = new OracleLog
                                                {
                                                    InfraObjectId = Logs.InfraObjectId,
                                                    PRLogTime = Logs.PRLogTime,
                                                    PRSequenceNo = Logs.PRSequenceNo,
                                                    DRLogTime = Logs.DRLogTime,
                                                    DRSequenceNo = Logs.DRSequenceNo,
                                                    CurrentDataLag = Logs.CurrentDataLag,
                                                    CreateDate = Logs.CreateDate
                                                };
                                                datalaglist.Add(datalagList);

                                            }
                                        }
                                        else
                                        {
                                            mnt = 6.9888;
                                        }
                                        #endregion



                                        if (datalaglist.Count != 0)
                                        {
                                            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(grp.BusinessFunctionId);
                                            TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
                                            int ConHour = conDatalag.Hours;
                                            int ConMinute = conDatalag.Minutes;


                                            foreach (var lo in datalaglist)
                                            {
                                                if (lo != null)
                                                {
                                                    string[] Datalg = lo.CurrentDataLag.Split(':');
                                                    if (Datalg[0].Contains('.'))
                                                    {
                                                        string[] splitFinal = Datalg[0].Split('.');
                                                        Datalg[0] = splitFinal[1];
                                                    }
                                                    if (Convert.ToInt32(Datalg[0]) < ConHour)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else if (Convert.ToInt32(Datalg[0]) == ConHour && Convert.ToInt32(Datalg[1]) < ConMinute)
                                                    {
                                                        rpoCount++;
                                                        Cnts++;
                                                        //goto Outer;
                                                    }
                                                    else
                                                    {
                                                        dev++;
                                                        deviated.Add(grp.Name);
                                                        appDeviated.Add(app.Name);
                                                        appDeviated.Add(grp.Name);
                                                    }
                                                }
                                            }
                                        }
                                        else
                                        {
                                            //mnt = 6.9888;
                                            deviated.Add(grp.Name);
                                            appDeviated.Add(app.Name);
                                            appDeviated.Add(grp.Name);
                                        }
                                        //Outer: ;
                                        Double Counts = 0;
                                        Double finalCnt = 0;
                                        if (mnt == 6.9888)
                                        {
                                            Counts = 0;
                                            finalCnt = 6.9888 - mnt;
                                        }
                                        else
                                        {
                                            Counts = Cnts * 0.0416;
                                            finalCnt = dev * 0.0416; //6.9888 - Counts;
                                        }

                                        int Column = 0;
                                        string Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = app.Name;
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = app.Name + "__" + grp.Name;
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 30;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = Counts.ToString(); // Counts.Equals(6.9887999999999995) ? "7" : Counts.ToString();
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                                        Column++;

                                        Ndx = xlColumn[Column] + rowApp.ToString();
                                        cells[Ndx].Formula = finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString(); //Counts.Equals(6.9887999999999995) ? "0" :
                                        //finalCnt.Equals(6.9888) ? "7" : finalCnt.ToString();
                                        cells[Ndx].Font.Size = 9;
                                        cells[Ndx].ColumnWidth = 20;
                                        cells[Ndx].Font.Color = System.Drawing.Color.Black;
                                        cells[Ndx].HorizontalAlignment = SpreadsheetGear.HAlign.Left;

                                        rowApp++;

                                        #endregion For recovery Type except 15 and 21

                                        break;
                                    }
                            }

                        }//End of Foreach

                    }//End of IF
                }

                int finalCount = row + 1;
                row--;
                rowApp--;


                int finalCounts = row + 3;



                //SpreadsheetGear Chart Part Started
                SpreadsheetGear.IWorksheetWindowInfo windowInfo = reportWorksheet.WindowInfo;
                SpreadsheetGear.IRange dataRange = reportWorksheet.Cells["AH28" + ":" + "AK" + rowApp];
                int rptHeading = finalCounts + 1;
                int rptHead = finalCount + 3;
                SpreadsheetGear.IRange titleCell = cells["I5"]; // + rptHead
                titleCell.Value = "Rpo Met And Deviation Chart";
                titleCell.HorizontalAlignment = SpreadsheetGear.HAlign.Left;
                titleCell.VerticalAlignment = SpreadsheetGear.VAlign.Top;
                titleCell.Style = reportWorkbook.Styles["Heading 4"];
                titleCell.RowHeight = 28.0;

                double left = windowInfo.ColumnToPoints(1.0);
                double top = windowInfo.RowToPoints(6.0);
                double right = windowInfo.ColumnToPoints(37.0);
                double bottom = windowInfo.RowToPoints(24.0);

                SpreadsheetGear.Charts.IChart chart =
                reportWorksheet.Shapes.AddChart(left, top, right - left, bottom - top).Chart;

                chart.SetSourceData(dataRange, SpreadsheetGear.Charts.RowCol.Columns);
                chart.ChartType = SpreadsheetGear.Charts.ChartType.ColumnClustered; //BarClustered  //XYScatterLines

                chart.Axes[SpreadsheetGear.Charts.AxisType.Value].HasTitle = true;
                chart.Axes[SpreadsheetGear.Charts.AxisType.Value].AxisTitle.Text = "Days";

                chart.PlotArea.Format.Fill.ForeColor.RGB = Color.FromArgb(216, 191, 216);
                chart.ChartArea.Format.Fill.ForeColor.RGB = Color.FromArgb(221, 217, 195);

                //int numbersCount = rptHeading + 16;

                cells["I25"].Font.Color = Color.Black;
                cells["I25"].HorizontalAlignment = HAlign.Center;
                cells["I25"].VerticalAlignment = VAlign.Top;
                cells["I25"].Font.Bold = true;
                cells["I25"].Font.Size = 9;
                cells["I25"].Formula = "Rpo Met and Deviation";
                //SpreadsheetGear Chart Part Ended
            }
            else
            {
                lblMsg.Text = "Business Service Not Found";
            }
        }

        private void OpenExcelFile(IWorkbook workbook, IWorksheet worksheet)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=MonthlySummaryReport.xls");
            string _str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            _str = "MonthlySummaryReport" + _str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + _str), SpreadsheetGear.FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + _str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar = Monthly Summary Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }


        #endregion Methods



    }
}