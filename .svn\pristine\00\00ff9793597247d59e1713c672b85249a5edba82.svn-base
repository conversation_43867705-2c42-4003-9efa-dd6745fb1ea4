﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "EmcISilonReplication", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class EmcISilonReplication : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();
        private EmcISilonPolicy _emcisilonpolicy = new EmcISilonPolicy();

        #endregion Member Variables

        #region properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string OneFSClusterIP { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }

        [DataMember]
        public EmcISilonPolicy EmcISilonReplicationPolicy
        {
            get { return _emcisilonpolicy; }
            set { _emcisilonpolicy = value; }
        }

        #endregion properties

    }
}
