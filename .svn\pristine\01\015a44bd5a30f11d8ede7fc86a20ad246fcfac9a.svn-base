﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class ImpactAnalysisBuilder : IEntityBuilder<ImpactAnalysis>
    {
        IList<ImpactAnalysis> IEntityBuilder<ImpactAnalysis>.BuildEntities(IDataReader reader)
        {
            var impactAnalysiss = new List<ImpactAnalysis>();

            while (reader.Read())
            {
                impactAnalysiss.Add(((IEntityBuilder<ImpactAnalysis>)this).BuildEntity(reader, new ImpactAnalysis()));
            }

            return (impactAnalysiss.Count > 0) ? impactAnalysiss : null;
        }

        ImpactAnalysis IEntityBuilder<ImpactAnalysis>.BuildEntity(IDataReader reader, ImpactAnalysis impactAnalysis)
        {
            impactAnalysis.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            impactAnalysis.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            impactAnalysis.EntityId = Convert.IsDBNull(reader["EntityId"]) ? 0 : Convert.ToInt32(reader["EntityId"]);
            impactAnalysis.ImpactType = Convert.IsDBNull(reader["ImpactType"]) ? string.Empty : Convert.ToString(reader["ImpactType"]);
            impactAnalysis.ImpactCreateDate = Convert.IsDBNull(reader["ImpactCreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["ImpactCreateDate"]);
            impactAnalysis.ImpactResolveDate = Convert.IsDBNull(reader["ImpactResolveDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["ImpactResolveDate"]);
            impactAnalysis.Status = Convert.IsDBNull(reader["Status"]) ? 0 : Convert.ToInt32(reader["Status"]);
            impactAnalysis.ImpactMessage = Convert.IsDBNull(reader["ImpactMessage"]) ? string.Empty : Convert.ToString(reader["ImpactMessage"]);
            impactAnalysis.ResolveMessage = Convert.IsDBNull(reader["ResolveMessage"]) ? string.Empty : Convert.ToString(reader["ResolveMessage"]);
            impactAnalysis.TimeTakenToResolve = Convert.IsDBNull(reader["TimeTakenToResolve"]) ? string.Empty : Convert.ToString(reader["TimeTakenToResolve"]);

            return impactAnalysis;
        }
    }
}