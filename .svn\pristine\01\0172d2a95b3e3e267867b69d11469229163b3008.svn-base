﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DatabasePostgre9xList.ascx.cs" Inherits="CP.UI.Controls.DatabasePostgresGreSqlList" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:UpdateProgress ID="updateprogress1" AssociatedUpdatePanelID="upnlsqlDatabseList"
    runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>
<asp:UpdatePanel ID="upnlsqlDatabseList" runat="server">
    <ContentTemplate>
        <div class="row">
            <div class="col-md-5 col-md-push-7 text-right">
                <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Database Name or Database SID"></asp:TextBox>
                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" OnClick="btnSearch_Click" />
            </div>
        </div>
        <hr />
        <asp:ListView ID="lvNormaldatabase" runat="server" DataKeyNames="ID" OnItemEditing="LvdatabaseItemEditing"
            OnPreRender="LvdatabasePreRender" OnItemDeleting="LvdatabaseItemDeleting" OnItemDataBound="LvdatabaseItemDataBound">
            <LayoutTemplate>
                <table id="tbldatabase" class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" width="100%" style="table-layout:fixed">
                    <thead>
                        <tr>
                            <th style="width: 4%;" class="text-center">
                                <span>
                                    <img src="../Images/icons/database.png" /></span>
                            </th>
                            <th style="width: 16%;">Name
                            </th>
                            <th style="width: 10%;">Database SID
                            </th>
                            <th style="width: 10%;">Type
                            </th>
                            <th style="width: 8%;">Version
                            </th>
                            <th style="width: 13%;">Data Directory
                            </th>
                            <th style="width: 13%;">Bin Directory
                            </th>
                            <th style="width: 10%;">SU Login
                            </th>
                             <th style="width: 10%;">Service Name
                            </th>
                            
                            <th runat="server" id="ActionHead" class="text-center" style="width: 6%;">Action
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                    </tbody>
                </table>
            </LayoutTemplate>
            <EmptyDataTemplate>
                <div class="message warning align-center bold">
                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                </div>
            </EmptyDataTemplate>

            <ItemTemplate>
                <tr>
                    <td class="text-center" style="width:4%">
                        <asp:Label ID="ID" runat="server" Text='<%#Eval("Id") %>' Visible="false"></asp:Label>
                        <%#Container.DataItemIndex+1 %>
                    </td>
                    <td style="width: 16%;" class="tdword-wrap">
                        <asp:Label ID="lblName" runat="server" Text='<%#Eval("Name") %>'></asp:Label>
                    </td>
                    <td style="width: 10%;">
                        <span>
                            <%# DataBinder.Eval(Container, "DataItem.DatabasePostgre9x.DatabaseName")%></span>
                    </td>
                    <td style="width: 10%;">
                        <%# DataBinder.Eval(Container, "DataItem.Type")%>
                    </td>
                    <td style="width: 8%;">
                        <%# DataBinder.Eval(Container, "DataItem.Version")%>
                    </td>
                    <td style="width: 13%;" class="tdword-wrap">
                        <%# DataBinder.Eval(Container, "DataItem.DatabasePostgre9x.DBDataDirectory")%>
                    </td>
                    <td style="width: 13%;" class="tdword-wrap">
                        <%# DataBinder.Eval(Container, "DataItem.DatabasePostgre9x.DBBinDirectory")%>
                    </td>
                    <td style="width: 10%;" class="tdword-wrap">
                        <%# DataBinder.Eval(Container, "DataItem.DatabasePostgre9x.SULogin")%>
                    </td>
                    <td style="width: 10%;" class="tdword-wrap">
                        <%# DataBinder.Eval(Container, "DataItem.DatabasePostgre9x.ServiceName")%>
                    </td>
                  
                    <td runat="server" id="action" class="text-center" style="width: 6%;">
                        <asp:ImageButton ID="ibtnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                            ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                        <asp:ImageButton ID="ibtnDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                            ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                    </td>
                    <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ibtnDelete"
                        ConfirmText='<%# "Are you sure want to delete " + Eval("Name") + " ? " %>' OnClientCancel="CancelClick">
                    </TK1:ConfirmButtonExtender>
                </tr>
            </ItemTemplate>
        </asp:ListView>
        <div class="row">
            <div class="col-md-6">
                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvNormaldatabase">
                    <Fields>
                        <asp:TemplatePagerField>
                            <PagerTemplate>
                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                <br />
                            </PagerTemplate>
                        </asp:TemplatePagerField>
                    </Fields>
                </asp:DataPager>
            </div>
            <div class="col-md-6 text-right">
                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvNormaldatabase" PageSize="10">
                    <Fields>
                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                            NumericButtonCssClass="btn-pagination" />
                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                    </Fields>
                </asp:DataPager>
            </div>
        </div>
        </div>

        <script type="text/javascript">
            function CancelClick() {
                return false;
            }
        </script>
    </ContentTemplate>
</asp:UpdatePanel>