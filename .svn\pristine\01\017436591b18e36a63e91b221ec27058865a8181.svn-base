﻿using System;
using System.Web.UI.WebControls;
using BCMS.CacheController;
using BCMS.Common.DatabaseEntity;
using BCMS.Common.Shared;
using BCMS.Helper;
using System.Collections.Generic;
using log4net;

namespace BCMS.UI 
{
    public partial class ExchangeGroupList : ExchangeGroupBasePage
    {

        #region variable
        private readonly ILog _logger = LogManager.GetLogger(typeof(ExchangeGroupList));
        private static CacheManager _cacheManager;
        private static string cacheKey = "Component.ExchangeGroupIntoCache";
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.EXCHANGEGROUP_CONFIGURATION;

        //private static string edit, swf, editType, editOS, editIP;
      

        #endregion


        private static CacheManager CurrentCacheManager
        {
            get { return _cacheManager ?? (_cacheManager = new CacheManager()); }
        }


        public override void PrepareView()
        {
            if (IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Error.ERROR_403);
            }
            var hd = Master.FindControl("hdSelectedMenu") as HiddenField;
            hd.Value = "Module3";
            if (Message.IsNotNullOrEmpty())
            {
                if (!IsPostBack)
                {
                    Utility.ShowMessage(ulMessage, lblMessage, Message, false);
                }
            }


            CurrentCacheManager.DataCache.RemoveItem(cacheKey);
            lvComponent.DataSource = GetExchangeGroupDetails();
            lvComponent.DataBind();
            //lvComponent.DataSource = Facade.GetAllExchangeGroup();
            //lvComponent.DataBind();
             
            
        }

        private IList<ExchangeGroup> GetExchangeGroupDetails()
        {
            var exchangeGroup = CurrentCacheManager.DataCache.GetValue<IList<ExchangeGroup>>(cacheKey);

            if (exchangeGroup != null)
            {
                return exchangeGroup;
            }
            else
            {
                var exchangeGroupList = Facade.GetAllExchangeGroup();
                if (exchangeGroupList != null)
                {
                    CurrentCacheManager.DataCache.AddItem(new CacheEntry(cacheKey, exchangeGroupList, "ExchangeGroupList"));
                }
                return exchangeGroupList;
            }
        }
       
        protected void LvComponentItemCanceling(object sender, ListViewCancelEventArgs e)
        {
            lvComponent.EditIndex = -1;
            lvComponent.DataSource = GetExchangeGroupDetails();
            lvComponent.DataBind();
        }

        protected void LvComponentItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (lvComponent.Items[e.ItemIndex].FindControl("Id")) as Label;
            var lblName = (lvComponent.Items[e.ItemIndex].FindControl("Db_InstallationFolder")) as Label;
            if (lbl != null)
            {
                int id = Convert.ToInt32(lbl.Text);
                bool isDelete = Facade.DeleteExchangeGroupById(id);

                if (isDelete)
                {
                    CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                    lvComponent.EditIndex = -1;
                    lvComponent.DataSource = GetExchangeGroupDetails();
                    lvComponent.DataBind();
                }
                else
                {
                    lvComponent.DataSource = GetExchangeGroupDetails();
                    lvComponent.DataBind();
                }
            }


        }

        protected void LvComponentItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvComponent.Items[e.NewEditIndex].FindControl("ID")) as Label;
            if (lbl1 != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.EXCHANGEGROUP_ID,
                                                     lbl1.Text);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }

        }

        protected void LvComponentPreRender(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                lvComponent.DataSource = GetExchangeGroupDetails();
                lvComponent.DataBind();
            }
        }

        protected void lvComponent_ItemDataBound(object sender, ListViewItemEventArgs e)
        {


            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator)
            {
                edit.Enabled = false;
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
            }
        }
    }
}
