﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class IBMXIVMirrorOverView : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;

        DropDownList _ddlReplicationType = new DropDownList();
        ReplicationType type;

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }


        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            //BindData();
          
        }


        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void LvReplicationPreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                BindData();
            }
        }

        private void BindData()
        {
            setListViewPage();
            lvReplication.Items.Clear();
            type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            IList<XIVConfiguration> globalMirrorList = GetGlobalMirrorListbyReplicationtype(type.ToString());
            lvReplication.DataSource = globalMirrorList;
            lvReplication.DataBind();
        }

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageGlobalMirrorList"]) != -1) && Session["CurrentPageGlobalMirrorList"] != null && (Convert.ToInt32(Session["CurrentPageGlobalMirrorList"]) > 0))
            {
                if (Session["TotalPageRowsCountGlobalMirror"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageGlobalMirrorList"]) == Convert.ToInt32(Session["TotalPageRowsCountGlobalMirror"]) - 1)
                    {
                        Session["CurrentPageGlobalMirrorList"] = Convert.ToInt32(Session["CurrentPageGlobalMirrorList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCountGlobalMirror"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageGlobalMirrorList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageGlobalMirrorList"] = -1;

            }

        }

        public IList<XIVConfiguration> GetGlobalMirrorListbyReplicationtype(string iType)
        {
            //return Facade.GetIBMXIVMirrorsByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
            //var  replicationlist = Facade.GetIBMXIVMirrorsByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
            var replicationlist = Facade.GetXIVReplicationByUserIdCompanyIdRoleAndReplicationFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
            if (replicationlist != null)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.reptype == iType
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public IList<XIVConfiguration> GetGlobalMirrorList(string searchvalue)
        {
            var replicationlist = GetGlobalMirrorListbyReplicationtype(Session["ReplicationType"].ToString());
            //var replicationlist = GetGlobalMirrorListbyReplicationtype(_ddlReplicationType.SelectedValue);
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count> 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvReplication.Items.Clear();
                lvReplication.DataSource = GetGlobalMirrorList(txtsearchvalue.Text);
                lvReplication.DataBind();
            }
        }

        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByReplicationId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Replication " + name + " attaching with group " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }

        protected void LvReplicationItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageGlobalMirrorList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCountGlobalMirror"] = dataPager1.TotalRowCount;
                var lblId = lvReplication.Items[e.ItemIndex].FindControl("Id") as Label;
                var lblName = (lvReplication.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
                var InfraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));

                if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("IBMXIVMirrorOverview  Delete", UserActionType.ReplicationList))
                {
                    if (InfraObjects != null && InfraObjects.Count > 0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The ExchangeOverview Replication component is in use");
                    }
                    else
                    {
                        Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "GlobalMirror", UserActionType.DeleteReplicationComponent,
                                             "The GlobalMirror Replication component '" + lblName.Text +
                                             "' was deleted from the replication component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("GlobalMirror Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                    }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void LvReplicationItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageGlobalMirrorList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvReplication.Items[e.NewEditIndex].FindControl("ID")) as Label;

            var lblName = (lvReplication.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "IBMXIVM", UserActionType.UpdateReplicationComponent,
                                      "The IBMXIVM Replication component '" + lblName.Text +
                                      "' Opened as Editing Mode", LoggedInUserId);
            if (lbl1 != null && lblName != null && ValidateRequest("IBMXIVMirrorOverview Edit", UserActionType.ReplicationList))
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                Helper.Url.Redirect(secureUrl);
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                //    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void LvReplication_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator || IsUserManager || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }
    }
}