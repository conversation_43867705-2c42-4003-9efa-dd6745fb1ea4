﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ApplicationMonitorDataAccess : BaseDataAccess, IApplicationMonitorDataAccess
    {
        #region Constructors

        public ApplicationMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ApplicationMonitor> CreateEntityBuilder<ApplicationMonitor>()
        {
            return (new ApplicationMonitorBuilder()) as IEntityBuilder<ApplicationMonitor>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Get <see cref="ApplicationGroup" /> from bcms_application_monitor_bak Table.
        /// </summary>
        /// <param name="applicationGropuId">applicationGropuId</param>
        /// <returns>ApplicationMonitor IList</returns>
        /// <author>Ranjith Singh</author>
        IList<ApplicationMonitor> IApplicationMonitorDataAccess.GetByBusinessserviceId(int applicationGropuId)
        {
            try
            {
                //const string sp = "Application_GetByApplicationGroupId";
                const string sp = "App_GetByBusinessServiceId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iBusinessserviceId", DbType.Int32, applicationGropuId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ApplicationMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationMonitorDataAccess.GetByBusinessserviceId(" +
                    applicationGropuId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="ApplicationMonitor" /> from bcms_application_monitor_bak Table.
        /// </summary>
        /// <param name="applicationId">applicationId</param>
        /// <returns>ApplicationMonitor</returns>
        /// <author>Ranjith Singh</author>
        ApplicationMonitor IApplicationMonitorDataAccess.GetByInfraobjectId(int applicationId)
        {
            try
            {
                const string sp = "AppMonitors_GetByInfraobjectId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraobjectId", DbType.Int32, applicationId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<ApplicationMonitor>()).BuildEntity(reader, new ApplicationMonitor())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationMonitorDataAccess.AppMonitors_GetByInfraobjectId(" +
                    applicationId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="ApplicationMonitor" /> from bcms_application_monitor_bak Table.
        /// </summary>
        /// <param name="applicationId">applicationId</param>
        /// <returns>ApplicationMonitor</returns>
        /// <author>Ranjith Singh</author>
        ApplicationMonitor IApplicationMonitorDataAccess.GetByInfraobjectIdAndAppType(int applicationId, string appType)
        {
            try
            {
                const string sp = "AppMonitors_GetByInfraIdApType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraobjectId", DbType.Int32, applicationId);
                    Database.AddInParameter(cmd, Dbstring+"iAppType", DbType.AnsiString, appType);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<ApplicationMonitor>()).BuildEntity(reader, new ApplicationMonitor())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationMonitorDataAccess.GetByInfraobjectIdAndAppType(" +
                    applicationId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="ApplicationMonitor" /> from bcms_application_monitor_bak Table.
        /// </summary>
        /// <param name="applicationId">applicationId</param>
        /// <returns>ApplicationMonitor IList</returns>
        /// <author>Ranjith Singh</author>
        IList<ApplicationMonitor> IApplicationMonitorDataAccess.Get24HrsStatusByApplicationId(int applicationId)
        {
            try
            {
                const string sp = "AppMonitor_GetByHours_AppId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iapplicationId", DbType.Int32, applicationId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ApplicationMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationMonitorDataAccess.GetById(" +
                    applicationId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="ApplicationMonitor" /> from bcms_application_monitor_bak Table.
        /// </summary>
        /// <param name="applicationGropuId">applicationid</param>
        /// <returns>ApplicationMonitor IList</returns>
        /// <author>Ranjith Singh</author>
        IList<ApplicationMonitor> IApplicationMonitorDataAccess.GetAllByInfraobjectId(int applicationid)
        {
            try
            {
                //const string sp = "AppMonitors_GetAllByInfraobjectId"; 
                const string sp = "APPMONITORS_GETALLBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraobjectId", DbType.Int32, applicationid);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ApplicationMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationMonitorDataAccess.GetAllByInfraobjectId(" +
                    applicationid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}