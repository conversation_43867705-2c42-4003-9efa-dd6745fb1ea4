﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "SRMVmwareMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class SRMVmwareMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }
        [DataMember]
        public string VCenterVersionPR { get; set; }
        [DataMember]
        public string VCenterVersionDR { get; set; }
        [DataMember]
        public string VCenterBuildPR { get; set; }
        [DataMember]
        public string VCenterBuildDR { get; set; }
        [DataMember]
        public string SRMVersionPR { get; set; }
        [DataMember]
        public string SRMVersionDR { get; set; }
        [DataMember]
        public string SRMBuildPR { get; set; }
        [DataMember]
        public string SRMBuildDR { get; set; }
        [DataMember]
        public string ProtectionGroupsNamePR { get; set; }
        [DataMember]
        public string ProtectionGroupsNameDR { get; set; }
        [DataMember]
        public string ProtectionGroupsNameTypePR { get; set; }
        [DataMember]
        public string ProtectionGroupsNameTypeDR { get; set; }
        [DataMember]
        public string ProtectionGroupsNameStatePR { get; set; }
        [DataMember]
        public string ProtectionGroupsNameStateDR { get; set; }
        [DataMember]
        public string ProtectionGroupNameVMCountPR { get; set; }
        [DataMember]
        public string ProtectionGroupNameVMCountDR { get; set; }
        [DataMember]
        public string RecoveryPlanNamePR { get; set; }
        [DataMember]
        public string RecoveryPlanNameDR { get; set; }
        [DataMember]
        public string RecoveryPlanStatePR { get; set; }
        [DataMember]
        public string RecoveryPlanStateDR { get; set; }
        [DataMember]
        public string RecoveryPlanHistoryNamePR { get; set; }
        [DataMember]
        public string RecoveryPlanHistoryNameDR { get; set; }
        [DataMember]
        public string RecoveryPlanHistoryLastRunDatePR { get; set; }
        [DataMember]
        public string RecoveryPlanHistoryLastRunDateDR { get; set; }     
        [DataMember]
        public string RecoveryPlanHistoryStatePR { get; set; }
        [DataMember]
        public string RecoveryPlanHistoryStateDR { get; set; }
        [DataMember]
        public string RecoveryPlanHistoryTotalTimePR { get; set; }
        [DataMember]
        public string RecoveryPlanHistoryTotalTimeDR { get; set; }         
        [DataMember]
        public string Createdate { get; set; }

        #endregion Properties
    }
}