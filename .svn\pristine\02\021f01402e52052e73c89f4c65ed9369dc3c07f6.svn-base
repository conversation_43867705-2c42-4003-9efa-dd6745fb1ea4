﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="RobocopyReplicList.ascx.cs" Inherits="CP.UI.Controls.RobocopyReplicList" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>


<script type="text/javascript">
    function CancelClick() {
        return false;
    }
</script>

<asp:UpdateProgress ID="UpdateProgress1" runat="server" AssociatedUpdatePanelID="UpdatePanel_RepGlobalMirrorOverview">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>
<asp:UpdatePanel ID="UpdatePanel_RepGlobalMirrorOverview" runat="server">
    <ContentTemplate>
        <div class="row">
            <div class="col-md-5 col-md-push-7 text-right">
                <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Replication Name"></asp:TextBox>
                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" OnClick="btnSearch_Click" Width="20%" Text="Search"/>  <%--OnClick="btnSearch_Click" --%>
            </div>
        </div>
        <hr />
      <asp:ListView ID="lvFastcopy" runat="server" OnItemEditing="lvFastcopy_ItemEditing" OnItemDeleting="lvFastcopy_ItemDeleting" OnItemDataBound="lvFastcopy_ItemDataBound" OnPagePropertiesChanging="lvFastcopy_PagePropertiesChanging" OnPreRender="lvFastcopy_PreRender">
            <LayoutTemplate>
                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" width="100%">
                    <thead>
                        <tr>
                            <th style="width: 4%;" class="text-center">
                                <span>
                                    <img src="../Images/icons/replication_list.png" /></span>
                            </th>
                            <th style="width: 19%;">Replication Name
                            </th>
                            <th style="width: 2%;">Local Directory
                            </th>
                            <th style="width: 20%;">Remote Directory
                            </th>
                            <th style="width: 25%;">OS Platform
                            </th>
                            <th style="width: 13%;">Action
                            </th>
                            
                        </tr>
                    </thead>
                    <tbody>
                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                    </tbody>
                </table>
            </LayoutTemplate>
            <ItemTemplate>
                <tr>
                    <td style="width: 4%;" class="text-center">
                        <asp:Label ID="ID" runat="server" Text='<%# Eval("ReplicationBase.Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                    </td>
                    <td style="width: 19%;">
                        <asp:Label ID="Rep_NAME" runat="server" Text='<%# Eval("ReplicationBase.Name") %>' />

                        <asp:Label ID="Rep_Type" runat="server" Text='<%# Eval("ReplicationBase.Type") %>' Visible="False" />
                    </td>

                    <td style="width: 20%;">
                        <asp:Label ID="Label1" runat="server"  Text='<%# GetLocalDirectory(Eval("Id")) %>' />
                    </td>

                    <td style="width: 20%;">
                        <asp:Label ID="Osplatform" runat="server" Text='<%# GetRemoteDirectory(Eval("Id")) %>' />
                    </td>
                    <td style="width: 13%;">
                        <asp:Label ID="Label2" runat="server" Text='<%#Eval("OSPlatform") %>' />
                    </td>

                    <td style="width: 4%;" class="text-center">
                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                            ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                            ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                    </td>
                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                        ConfirmText='<%# "Are you sure want to delete " + Eval("ReplicationBase.Name") + " ? " %>' OnClientCancel="CancelClick">
                    </TK1:ConfirmButtonExtender>
                </tr>
            </ItemTemplate>
            <EmptyDataTemplate>
                <div class="message warning align-center bold">
                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                </div>
            </EmptyDataTemplate>
        </asp:ListView>

                <div class="row">
                            <div class="col-md-6">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvFastcopy">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-md-6 text-right">
                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvFastcopy" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                            NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>
         
    </ContentTemplate>
</asp:UpdatePanel>
