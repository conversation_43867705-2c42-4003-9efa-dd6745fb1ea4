﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region ISiteDataAccess

    public interface ISiteDataAccess
    {
        Site Add(Site site);

        Site Update(Site site);

        Site GetById(int id);

        Site GetByName(string name);

        IList<Site> GetByType(string type);

        IList<Site> GetAll();

        IList<Site> GetSitesByCompanyId(int companyId, bool isParent);

        bool DeleteById(int id);

        bool IsExistByName(string id);

        Site GetByType(int id);

        IList<Site> GetSiteUserId(int userid);
    }

    #endregion ISiteDataAccess
}