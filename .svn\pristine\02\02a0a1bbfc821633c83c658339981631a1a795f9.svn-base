﻿var winElement = "";
$('[id$=btnMaint]').click(function (event) {
    $("#txtTime").val("");
    $("#txtReason").val("");
    $("#ddlMode").val(0);
    $("#ddlMode").show();
    $("#ddlMode").trigger("blur");
    var buttonName = $("[id$=btnMaint]").val();
    $('[id$=lblmaintenance]').html(buttonName);
    event.preventDefault ? event.preventDefault() : event.returnValue = false;
});

$('[id$=btnMaintenanceAll]').click(function () {
    $("#txtTime").val("");
    $("#txtReason").val("");
    $("#ddlMode").val(0);
    $("#ddlMode").show();
    $("#ddlMode").trigger("blur");
    var maintenanceAll = $("[id$=btnMaintenanceAll]").val();
    $('[id$=lblmaintenance]').html(maintenanceAll);
    event.preventDefault ? event.preventDefault() : event.returnValue = false;
});

$('[id$=btnLock]').click(function (event) {
    $("#txtTime").val("");
    $("#txtReason").val("");
    $("#ddlMode").val(0);
    $("#ddlMode").show();
    $("#ddlMode").trigger("blur");
    var buttonName = $("[id$=btnLock]").val();
    $('[id$=lblmaintenance]').html(buttonName);
    event.preventDefault ? event.preventDefault() : event.returnValue = false;
});
function pageLoad() {
    debugger;
    var ajaxData = "{}";
    var ajaxUrl = "InfraApplicationManagement.aspx/EnableButtonControls";
    AjaxFunction(ajaxUrl, ajaxData, EnableButtons, OnError);
}
function EnableButtons(msg) {
    debugger;
    var result = msg.d.split('/');
    var splitResult = result[1].split(',');
    if (splitResult[4] == "ExecutionAccessUser") {
        $('[id$=btnActiveAll], [id$=btnMaintenanceAll], [id$=btnLock], [id$=btnActive], [id$=btnManageOptions], [id$=btnMaint]').attr("disabled", true); //

        if (splitResult[2] == "Active")
            $('[id$=btnMaint]').attr("disabled", false);
        else if (splitResult[2] == "Maintenance")
            $('[id$=btnActive]').attr("disabled", false);
        return;
    }
    else if (result[0] == "Error") {
        $('[id$=btnActive],[id$=btnActiveAll]').attr("disabled", false);
        $('[id$=btnLock],[id$=btnMaint],[id$=btnMaintenanceAll]').attr("disabled", true);
        $('[id$=btnSwitchBack]').attr("disabled", true);
        $('[id$=btnSwitchOver]').attr("disabled", false);
        $('[id$=btnFailOver],[id$=btnCustom]').attr("disabled", false);
        return;
    }

    else if (result[0] == "Active") {
        $('[id$=btnActive],[id$=btnActiveAll],[id$=btnSwitchOver],[id$=btnFailOver],[id$=btnFailBack],[id$=btnSwitchBack],[id$=btnCustom]').attr("disabled", true);
        $('[id$=btnLock],[id$=btnMaint],[id$=btnMaintenanceAll]').attr("disabled", false);
        return;
    }
    else if (result[0] == "Maintenance") {
        $('[id$=btnActive],[id$=btnActiveAll]').attr("disabled", false);
        $('[id$=btnLock],[id$=btnMaint],[id$=btnMaintenanceAll]').attr("disabled", true);
        $('[id$=btnFailOver],[id$=btnCustom]').attr("disabled", false);
        var app = $("#trMaintenanceMessage").hasClass('message success');
        if (app) {
            $('#tblMaintainenceManage > tbody > tr').eq(0).remove();
        }
        var dispmsg = "<tr class='message success ' id='trMaintenanceMessage'> <td colspan='3'> User  <b>  " + splitResult[0] + " </b>   has put this Infra Object in  <b> Maintenance Mode </b> for reason:</td></tr>";
        $('#tblMaintainenceManage > tbody').prepend(dispmsg);
        if (splitResult[3] == "18" || splitResult[3] == "2" || splitResult[3] == "6") {
            $('[id$=btnSwitchBack]').attr("disabled", false);
            $('[id$=btnSwitchOver]').attr("disabled", true);
            $('[id$=btnFailBack]').attr("disabled", false);
            $('[id$=btnFailOver]').attr("disabled", true);
        }
        if (splitResult[3] == "20" || splitResult[3] == "0" || splitResult[3] == "3" || splitResult[3] == "5" || splitResult[3] == "11") {
            $('[id$=btnSwitchBack]').attr("disabled", true);
            $('[id$=btnSwitchOver]').attr("disabled", false);
            $('[id$=btnFailOver]').attr("disabled", false);
            $('[id$=btnFailBack]').attr("disabled", true);
        }
        if (splitResult[3] == "8") {
            $('[id$=btnSwitchBack]').attr("disabled", true);
            $('[id$=btnSwitchOver]').attr("disabled", true);
            $('[id$=btnFailOver]').attr("disabled", true);
            $('[id$=btnFailBack]').attr("disabled", false);
        }

        $('[id$=btnCustom]').attr("disabled", false);
        return;
    }
    if (splitResult[2] == "Maintenance" || splitResult[2] == "Locked") {
        if (splitResult[3] == "0" || splitResult[3] == "2" || splitResult[3] == "5" || splitResult[3] == "14" || splitResult[3] == "11") {
            $('#trMaintenanceMessage').remove();
            $('[id$=btnActive],[id$=btnActiveAll]').attr("disabled", false);

            if (splitResult[2] == "Maintenance") {
                var dispmsg = "<tr class='message success ' id='trMaintenanceMessage'> <td colspan='3'> User  <b>  " + splitResult[0] + " </b>   has put this Infra Object in  <b> Maintenance Mode </b> for reason: " + splitResult[1] + "</td></tr>";
            }
            else {
                var dispmsg = "<tr class='message success ' id='trMaintenanceMessage'> <td colspan='3'> User  <b>  " + splitResult[0] + " </b>   has put this Infra Object in  <b> Lock Mode </b> for reason: " + splitResult[1] + "</td></tr>";
            }
            $('#tblMaintainenceManage > tbody').prepend(dispmsg);
        }
        else {
            $('[id$=btnActive],[id$=btnActiveAll]').attr("disabled", false);
        }
        $('[id$=btnLock],[id$=btnMaint],[id$=btnMaintenanceAll]').attr("disabled", true);
        if (splitResult[3] == "18" || splitResult[3] == "2" || splitResult[3] == "6") {
            $('[id$=btnSwitchBack]').attr("disabled", false);
            $('[id$=btnSwitchOver]').attr("disabled", true);
            $('[id$=btnFailBack]').attr("disabled", false);
        }
        if (splitResult[3] == "20" || splitResult[3] == "0" || splitResult[3] == "3" || splitResult[3] == "5" || splitResult[3] == "11") {
            $('[id$=btnSwitchBack]').attr("disabled", true);
            $('[id$=btnSwitchOver]').attr("disabled", false);
            $('[id$=btnFailOver]').attr("disabled", false);
        }
        if (splitResult[3] == "8") {
            $('[id$=btnSwitchBack]').attr("disabled", true);
            $('[id$=btnSwitchOver]').attr("disabled", true);
            $('[id$=btnFailOver]').attr("disabled", true);
            $('[id$=btnFailBack]').attr("disabled", false);
        }
        $('[id$=btnCustom]').attr("disabled", false);
    }

    else {
        $('[id$=btnActive],[id$=btnActiveAll],[id$=btnSwitchOver],[id$=btnFailOver],[id$=btnFailBack],[id$=btnSwitchBack],[id$=btnCustom]').attr("disabled", true);
        $('[id$=btnLock],[id$=btnMaint],[id$=btnMaintenanceAll]').attr("disabled", false);
    }
}
$('[id$=btnMaintenancedemo]').click(function () {
    $('[id$=lblmaintenance]').html($('[id$=btnMaintenancedemo]').text());
});

$("#ddlMode").live("blur", function () {
    RequireDropDown($(this).attr("id"));
});

$("#txtReason").live("blur", function () {
    RequireField($(this).attr("id"));
});

$("#txtTime").live("blur", function () {
    RequireField($(this).attr("id"));
});

$("[id$=idUnlock]").hide();
$('[id$=ddlMode]').change(function () {
    if ($(this).val() == 1)
        $("[id$=idUnlock]").show();
    else
        $("[id$=idUnlock]").hide();
    $('#txtTime').datetimepicker({ dateFormat: 'yy-mm-dd', minDate: 0 });
});

$("#btnActionSetSave").click(function () {
    $("#txtReason").trigger("blur");
    $("#ddlMode").trigger("blur");
    $("#txtTime").trigger("blur");
    if ($(".error").is(":visible"))
        return false;
    var groupMode = $('[id$=lblmaintenance]').text();
    var type;
    if (groupMode == "Lock") {
        type = "1";
    }
    else if (groupMode == "Maintenance") {
        type = "2";
    }
    else if (groupMode == "MaintenanceAll") {
        type = "2";
    }
    var values = $("#txtReason").val() + "," + $("#ddlMode option:selected").val() + "," + $("#txtTime").val() + "," + type;
    var ajaxData = "{'values':'" + values + "'}";
    var button = $('[id$=lblmaintenance]').text();
    var ajaxUrl;
    if (button == "MaintenanceAll") {
        ajaxUrl = "InfraApplicationManagement.aspx/InfraObjectApplicationMaintenanceAll";
        AjaxFunction(ajaxUrl, ajaxData, MaintainenceAll, OnError);
    }
    else {
        ajaxUrl = "InfraApplicationManagement.aspx/InfraObjectApplicationMaintenance";
        AjaxFunction(ajaxUrl, ajaxData, DataSaved, OnError);
    }
    return false;
});

function OnError(msg) {
    alert(msg.d);
}

function MaintainenceAll(msg) {
    if (msg.d.indexOf("Success") > -1) {
        $('[id$=btnActive],[id$=btnSwitchOver],[id$=btnFailOver]').attr("disabled", false);
        var result = msg.d.split('/');
        var splitResult = result[1].split(',');
        if (splitResult[1] == "18" || splitResult[1] == "2" || splitResult[1] == "6") {
            $('[id$=btnSwitchBack]').attr("disabled", false);
            $('[id$=btnSwitchOver]').attr("disabled", true);
        }
        if (splitResult[1] == "20" || splitResult[1] == "0" || splitResult[1] == "3" || splitResult[1] == "5" || splitResult[3] == "11") {
            $('[id$=btnSwitchBack]').attr("disabled", true);
            $('[id$=btnSwitchOver]').attr("disabled", false);
        }
        if (splitResult[2] == "8") {
            $('[id$=btnSwitchBack]').attr("disabled", true);
            $('[id$=btnSwitchOver]').attr("disabled", true);
            $('[id$=btnFailOver]').attr("disabled", true);
            $('[id$=btnFailBack]').attr("disabled", false);
        }
        $('[id$=btnMaint]').attr("disabled", true);
        $('[id$=btnMaintenanceAll]').attr("disabled", true);
        $('[id$=btnLock]').attr("disabled", true);
        $('[id$=btnCustom]').attr("disabled", false);
        $('[id$=btnActiveAll]').attr("disabled", false);
        var dispmsg = "<tr class='message success ' id='trMaintenanceMessage'> <td colspan='3'> User  <b>  " + splitResult[0] + " </b>   has put this Infra Object in  <b> Maintenance Mode </b> for reason: " + $("#txtReason").val() +
        "</td></tr>";
        $('#tblMaintainenceManage > tbody').prepend(dispmsg);
        $("#ctl00_cphBody_ModalPopupExtenderMaintenanceAll_backgroundElement").hide();
        $("#ctl00_cphBody_ModalPopupExtenderLock_backgroundElement").hide();
        $("#ctl00_cphBody_panelMaintenance").hide();
        $("#Div4").hide();
    }
    else {
        alert(msg.d);
    }
    //	if (msg.d.indexOf("Success") > -1) {
    //		$('[id$=btnMaint],[id$=btnMaintenanceAll],[id$=btnLock]').attr("disabled", true);
    //		$("#ctl00_cphBody_ModalPopupExtenderMaintenanceAll_backgroundElement").hide();
    //        $("#Div4").hide();
    //	}
}

function DataSaved(msg) {
    if (msg.d.indexOf("Success") > -1) {
        $('[id$=btnActive],[id$=btnActiveAll],[id$=btnFailOver]').attr("disabled", false);
        var groupMode = $('[id$=lblmaintenance]').text();
        var result = msg.d.split('/');
        var splitResult = result[1].split(',');
        var dispmsg;
        if (groupMode == "Lock") {
            $('[id$=btnLock]').attr("disabled", true);
            $('[id$=btnSwitchOver],[id$=btnFailOver],[id$=btnActiveAll]').attr("disabled", true);
            dispmsg = "<tr class='message success ' id='trMaintenanceMessage'> <td colspan='3'> User  <b>  " + splitResult[0] + " </b>   has put this Infra Object in  <b> Lock Mode </b> for reason: " + $("#txtReason").val() +
	    	"</td></tr>";
        }
        else {
            if (splitResult[1] == "18" || splitResult[1] == "2" || splitResult[1] == "6") {
                $('[id$=btnSwitchBack]').attr("disabled", false);
                $('[id$=btnSwitchOver]').attr("disabled", true);
                $('[id$=btnFailBack]').attr("disabled", false);
                $('[id$=btnFailOver]').attr("disabled", true);
            }
            if (splitResult[1] == "20" || splitResult[1] == "0" || splitResult[1] == "3" || splitResult[1] == "5" || splitResult[1] == "11") {
                $('[id$=btnSwitchBack]').attr("disabled", true);
                $('[id$=btnSwitchOver]').attr("disabled", false);
                $('[id$=btnFailOver]').attr("disabled", false);
                $('[id$=btnFailBack]').attr("disabled", true);
            }
            if (splitResult[3] == "8") {
                $('[id$=btnSwitchBack]').attr("disabled", true);
                $('[id$=btnSwitchOver]').attr("disabled", true);
                $('[id$=btnFailOver]').attr("disabled", true);
                $('[id$=btnFailBack]').attr("disabled", false);
            }
            $('[id$=btnMaint]').attr("disabled", true);
            $('[id$=btnMaintenanceAll]').attr("disabled", true);
            $('[id$=btnLock]').attr("disabled", true);
            $('[id$=btnCustom]').attr("disabled", false);
            $('[id$=btnActiveAll]').attr("disabled", false);
            //$('[id$=btnMaint],[id$=btnMaintenanceAll]').attr("disabled", true);
            dispmsg = "<tr class='message success ' id='trMaintenanceMessage'> <td colspan='3'> User  <b>  " + splitResult[0] + " </b>   has put this Infra Object in  <b> Maintenance Mode </b> for reason: " + $("#txtReason").val() +
	    		"</td></tr>";
        }
        $('#tblMaintainenceManage > tbody').prepend(dispmsg);
        $("#ctl00_cphBody_ModalPopupExtenderMaintenance_backgroundElement").hide();
        $("#ctl00_cphBody_panelMaintenance").hide();
        $("#ctl00_cphBody_ModalPopupExtenderLock_backgroundElement").hide();
        $("#Div4").hide();
    }
    else {
        alert(msg.d);
    }
    var userrole = $('[id$=lbluserrole]').text();
    if (userrole == "ExecutionAccessUser")

        $('[id$=btnMaint],[id$=btnActiveAll],[id$=btnLock],[id$=btnMaintenanceAll]').attr("disabled", true);
}


$('[id$=btnActive]').click(function (event) {
    var ajaxUrl = "InfraApplicationManagement.aspx/InfraObjectApplicationActive";
    AjaxFunction(ajaxUrl, ajaxData, Active, OnError);
    event.preventDefault ? event.preventDefault() : event.returnValue = false;

});
function Active(msg, event) {
    if (msg.d == "Success") {
        $('[id$=lblRepStatus]').html("Running");
        $('[id$=Label19]').removeClass("pause float-left");
        $('[id$=Label19]').addClass("Replicating float-left");
        $('[id$=btnActive],[id$=btnActiveAll],[id$=btnSwitchOver],[id$=btnFailOver],[id$=btnFailBack],[id$=btnSwitchBack],[id$=btnCustom]').attr("disabled", true);
        $('[id$=btnLock],[id$=btnMaint],[id$=btnMaintenanceAll]').attr("disabled", false);
        var app = $("#trMaintenanceMessage").hasClass('message success');
        if (app) {
            $('#tblMaintainenceManage > tbody > tr').eq(0).remove();
        }
        event.preventDefault ? event.preventDefault() : event.returnValue = false;


        var userrole = $('[id$=lbluserrole]').text();
        if (userrole == "ExecutionAccessUser")

            $('[id$=btnActive],[id$=btnActiveAll],[id$=btnLock],[id$=btnMaintenanceAll]').attr("disabled", true);
    }
};


$('[id$=btnActiveAll]').click(function (event) {
    var ajaxUrl = "InfraApplicationManagement.aspx/InfraObjectApplicationActiveAll";
    AjaxFunction(ajaxUrl, ajaxData, ActiveAll, OnError);
    event.preventDefault ? event.preventDefault() : event.returnValue = false;
});
function ActiveAll(msg, event) {
    $('[id$=btnActive],[id$=btnActiveAll],[id$=btnSwitchOver],[id$=btnFailOver],[id$=btnFailBack],[id$=btnSwitchBack],[id$=btnCustom]').attr("disabled", true);
    $('[id$=btnLock],[id$=btnMaint],[id$=btnMaintenanceAll]').attr("disabled", false);
    var app = $("#trMaintenanceMessage").hasClass('message success');
    if (app) {
        $('#tblMaintainenceManage > tbody > tr').eq(0).remove();
    }
    event.preventDefault ? event.preventDefault() : event.returnValue = false;
};
