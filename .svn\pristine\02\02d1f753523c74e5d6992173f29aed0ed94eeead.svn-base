﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IGlobalMirrorLunsDataAccess

    public interface IGlobalMirrorLunsDataAccess
    {
        GlobalMirrorLuns Add(GlobalMirrorLuns globalMirrorLuns);

        GlobalMirrorLuns Update(GlobalMirrorLuns globalMirrorLuns);

        GlobalMirrorLuns GetById(int id);

        IList<GlobalMirrorLuns> GetByGlobalMirrorId(int id);

        IList<GlobalMirrorLuns> GetAll();

        bool DeleteById(int id);
    }

    #endregion IGlobalMirrorLunsDataAccess
}