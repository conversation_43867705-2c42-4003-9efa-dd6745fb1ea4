﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Web;

namespace CP.UI.Component
{
    public partial class Robocopyoptionslist : RoboCopyOptionsBasePage
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.RoboCopyOptionsConfiguration;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.RoboCopyOptionsList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            if (IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }

            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }    

            Utility.SelectMenu(Master, "Module3");
            BindList();
        }

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageRoboCopyOptionsList"]) != -1) && Session["CurrentPageRoboCopyOptionsList"] != null && (Convert.ToInt32(Session["CurrentPageRoboCopyOptionsList"]) > 0))
            {
                if (Session["TotalPageRowsCounRoboCopyOptionsList"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageRoboCopyOptionsList"]) == Convert.ToInt32(Session["TotalPageRowsCounRoboCopyOptionsList"]) - 1)
                    {
                        Session["CurrentPageRoboCopyOptionsList"] = Convert.ToInt32(Session["CurrentPageRoboCopyOptionsList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCounRoboCopyOptionsList"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageRoboCopyOptionsList"]), dataPager1.MaximumRows, true);
                dataPager1.DataBind();
                Session["CurrentPageRoboCopyOptionsList"] = -1;
            }
        }


        private void BindList()
        {
            setListViewPage();
            lstviewrobo.DataSource = Facade.GetAlRoboCopyOptions();
            lstviewrobo.DataBind(); 
        }

       
        private IList<RoboCopyOptions> GetRoboCopyoptionListBySearch(string value)
        {
            var RoboCopyOptionList = Facade.GetAlRoboCopyOptions();
            value = value.Trim();
            if (!String.IsNullOrEmpty(value) || RoboCopyOptionList != null)
            {
                var result = (from dsProperties in RoboCopyOptionList
                              where dsProperties.Name.ToLower().Contains(value.ToLower())
                              select dsProperties).ToList();

                return result;
            }
            return null;
        }

        protected void lstviewrobo_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ImgEdit") as ImageButton;
            var delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (!IsSuperAdmin && !IsUserAdmin)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
            if (IsUserOperator || IsUserManager || IsUserExecutionUser)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }

        protected void lstviewrobo_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {

                Session["CurrentPageRoboCopyOptionsList"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCounRoboCopyOptionsList"] = dataPager1.TotalRowCount;

                var lblId = (lstviewrobo.Items[e.ItemIndex].FindControl("lblId")) as Label;
                var lblName = (lstviewrobo.Items[e.ItemIndex].FindControl("lblName")) as Label;
              //  var RoboCopyDeatils = Facade.GetByRoboCopyOptionsId(Convert.ToInt32(lblId.Text));
                var RoboCopyDeatils = Facade.GetRoboCopyJobByRoboCopyOptionsId(Convert.ToInt32(lblId.Text));

                if (RoboCopyDeatils != null)
                {

                    ErrorSuccessNotifier.AddSuccessMessage("The Robo Copy '" + lblName.Text + "' is in use.");
                }
                  
                else if (lblId != null && ValidateRequest("RoboCopyOption Delete", UserActionType.DeleteServerComponent))
                {
                        Facade.DeleteRoboCopyOptionsById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "RoboCopy", UserActionType.DeleteServerComponent, "The RoboCopy '" + lblName.Text + "' was deleted", LoggedInUserId);
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("RoboCopy" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                }
                
            
                //if (RoboCopyDeatils != null)
                //{

                //    ErrorSuccessNotifier.AddSuccessMessage("The Robo Copy '" + lblName.Text + "' is in use.");

                //}
                // if (lblId != null && lblName != null)
                //{
                //    Facade.DeleteRoboCopyOptionsById(Convert.ToInt32(lblId.Text));
                //    ActivityLogger.AddLog(LoggedInUserName, "RoboCopy", UserActionType.DeleteServerComponent, "The RoboCopy '" + lblName.Text + "' was deleted", LoggedInUserId);
                //    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("RoboCopy" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                //}

            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }

        }

        protected void lstviewrobo_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageRoboCopyOptionsList"] = dataPager1.StartRowIndex;
            var secureUrl = new SecureUrl(CurrentURL);

            var lblId = (lstviewrobo.Items[e.NewEditIndex].FindControl("lblId")) as Label; 
            if (lblId != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.RoboCopyOptionId,
                                                     lblId.Text);
            }
            if (secureUrl != null && ValidateRequest("RoboCopyOption Editing", UserActionType.UpdateServerComponent))
            {
                Helper.Url.Redirect(secureUrl);
            }

        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            lstviewrobo.DataSource = GetRoboCopyoptionListBySearch(txtsearchvalue.Text);
            lstviewrobo.DataBind();

        }

        protected void lstviewrobo_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected void lstviewrobo_PreRender(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                if (String.IsNullOrEmpty(txtsearchvalue.Text))
                {
                    lstviewrobo.DataSource = Facade.GetAlRoboCopyOptions();
                    lstviewrobo.DataBind();
                }
                else
                {
                    lstviewrobo.DataSource = GetRoboCopyoptionListBySearch(txtsearchvalue.Text);
                    lstviewrobo.DataBind();
                }
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

    }
}