﻿<?xml version="1.0"?>
<SAMLConfiguration xmlns="urn:pts:SAML:2.0:configuration">
	<ServiceProvider
	  Name="https://jprcntyptrluat/SAMLAD/login.aspx"
	  Description="ADFS"
	  AssertionConsumerServiceUrl="~/SAML/AssertionConsumerService.aspx">
		<LocalCertificates>
			<Certificate FileName="Certificates\sp.pfx" Password="password"/>
		</LocalCertificates>
	</ServiceProvider>




	<PartnerIdentityProviders>
		<!-- Web forms example -->
		<PartnerIdentityProvider
		  Name="https://ExampleIdentityProvider"
		  Description="Example Identity Provider"
		  SingleSignOnServiceUrl="https://localhost:44390/SAML/SSOService.aspx"
		  SingleLogoutServiceUrl="https://localhost:44390/SAML/SLOService.aspx">
			<PartnerCertificates>
				<Certificate FileName="Certificates\idp.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- MVC example -->
		<PartnerIdentityProvider
		  Name="https://MvcExampleIdentityProvider"
		  Description="MVC Example Identity Provider"
		  SingleSignOnServiceUrl="https://localhost:44363/SAML/SingleSignOnService"
		  SingleLogoutServiceUrl="https://localhost:44363/SAML/SingleLogoutService">
			<PartnerCertificates>
				<Certificate FileName="Certificates\idp.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- ADFS -->
		<PartnerIdentityProvider
		  Name="http://sts.icicigroupcompanies.com/adfs/services/trust"
		  Description="ADFS"
		  WantSAMLResponseSigned="false"
		  WantAssertionSigned="false"
		  WantAssertionEncrypted="false"
		  SingleSignOnServiceUrl="https://sts.icicigroupcompanies.com/adfs/ls/"
		  SingleLogoutServiceUrl="https://sts.icicigroupcompanies.com/adfs/ls/">
			<PartnerCertificates>
				<Certificate FileName="Certificates\adfs.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- Azure AD -->
		<PartnerIdentityProvider
		  Name="https://sts.windows.net/f2f933ec-d7c9-433f-8926-d3a0732a7dcf/"
		  Description="Azure AD"
		  SingleSignOnServiceUrl="https://login.microsoftonline.com/f2f933ec-d7c9-433f-8926-d3a0732a7dcf/saml2"
		  SingleLogoutServiceUrl="https://login.microsoftonline.com/f2f933ec-d7c9-433f-8926-d3a0732a7dcf/saml2">
			<PartnerCertificates>
				<Certificate FileName="Certificates\azure.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- Okta -->
		<PartnerIdentityProvider
		  Name="http://www.okta.com/exky5euw54Xm4gyj30h7"
		  Description="Okta"
		  SingleSignOnServiceUrl="https://componentspace.oktapreview.com/app/componentspace_exampleserviceprovider_1/exky5euw54Xm4gyj30h7/sso/saml"
		  SingleLogoutServiceUrl="https://componentspace.oktapreview.com/app/componentspace_exampleserviceprovider_1/exky5euw54Xm4gyj30h7/slo/saml">
			<PartnerCertificates>
				<Certificate FileName="Certificates\okta.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- Google -->
		<PartnerIdentityProvider
		  Name="https://accounts.google.com/o/saml2?idpid=C03kl4l11"
		  Description="Google"
		  SingleSignOnServiceUrl="https://accounts.google.com/o/saml2/idp?idpid=C03kl4l11">
			<PartnerCertificates>
				<Certificate FileName="Certificates\google.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- Salesforce -->
		<PartnerIdentityProvider
		  Name="https://componentspace-dev-ed.my.salesforce.com"
		  Description="Salesforce"
		  SingleSignOnServiceUrl="https://componentspace-dev-ed.my.salesforce.com/idp/endpoint/HttpRedirect">
			<PartnerCertificates>
				<Certificate FileName="Certificates\salesforce.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- Shibboleth -->
		<PartnerIdentityProvider
		  Name="https://samltest.id/saml/idp"
		  Description="Shibboleth"
		  SingleSignOnServiceUrl="https://samltest.id/idp/profile/SAML2/Redirect/SSO"
		  SingleLogoutServiceUrl="https://samltest.id/idp/profile/SAML2/Redirect/SLO">
			<PartnerCertificates>
				<Certificate FileName="Certificates\shibboleth1.cer"/>
				<Certificate FileName="Certificates\shibboleth2.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- WSO2 Identity Server -->
		<PartnerIdentityProvider
		  Name="localhost"
		  Description="WSO2 Identity Server"
		  SingleSignOnServiceUrl="https://localhost:9443/samlsso"
		  SingleLogoutServiceUrl="https://localhost:9443/samlsso">
			<PartnerCertificates>
				<Certificate FileName="Certificates\wso2.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- OneLogin -->
		<PartnerIdentityProvider
		  Name="https://app.onelogin.com/saml/metadata/589361"
		  Description="OneLogin"
		  SingleSignOnServiceUrl="https://componentspacetest-dev.onelogin.com/trust/saml2/http-redirect/sso/589361"
		  SingleLogoutServiceUrl="https://componentspacetest-dev.onelogin.com/trust/saml2/http-redirect/slo/589361">
			<PartnerCertificates>
				<Certificate FileName="Certificates\onelogin.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- PingOne -->
		<PartnerIdentityProvider
		  Name="https://pingone.com/idp/componentspace"
		  Description="PingOne"
		  SingleSignOnServiceUrl="https://sso.connect.pingidentity.com/sso/idp/SSO.saml2?idpid=f0f2b9e9-967a-4c79-bb00-15fe88401e13"
		  SingleLogoutServiceUrl="https://sso.connect.pingidentity.com/sso/SLO.saml2">
			<PartnerCertificates>
				<Certificate FileName="Certificates\pingone.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- Bitium -->
		<PartnerIdentityProvider
		  Name="https://www.bitium.com/componentspace.com"
		  Description="Bitium"
		  SingleSignOnServiceUrl="https://www.bitium.com/componentspace.com/saml/83926/auth">
			<PartnerCertificates>
				<Certificate FileName="Certificates\bitium.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>

		<!-- Centrify -->
		<PartnerIdentityProvider
		  Name="https://aam0904.my.centrify.com/48df688f-4247-424b-b393-3f55025b5a60"
		  Description="Centrify"
		  SingleSignOnServiceUrl="https://aam0904.my.centrify.com/applogin/appKey/48df688f-4247-424b-b393-3f55025b5a60/customerId/AAM0904">
			<PartnerCertificates>
				<Certificate FileName="Certificates\centrify.cer"/>
			</PartnerCertificates>
		</PartnerIdentityProvider>
		<PartnerIdentityProvider Name="https://sts.windows.net/4d8b04bf-7a7c-48a0-b6e3-38da5008297e/" SingleLogoutServiceUrl="https://login.microsoftonline.com/4d8b04bf-7a7c-48a0-b6e3-38da5008297e/saml2" SingleSignOnServiceUrl="https://login.microsoftonline.com/4d8b04bf-7a7c-48a0-b6e3-38da5008297e/saml2">
			<PartnerCertificates>
				<Certificate Use="Signature" String="MIIC8DCCAdigAwIBAgIQHw+Cs5wVhqBJ2POqX+TIJDANBgkqhkiG9w0BAQsFADA0MTIwMAYDVQQDEylNaWNyb3NvZnQgQXp1cmUgRmVkZXJhdGVkIFNTTyBDZXJ0aWZpY2F0ZTAeFw0yMzA4MjkwNTE5NDRaFw0yNjA4MjkwNTE5NDRaMDQxMjAwBgNVBAMTKU1pY3Jvc29mdCBBenVyZSBGZWRlcmF0ZWQgU1NPIENlcnRpZmljYXRlMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAzqBRhbAFJxUOf0aueZeef/fIxbO1zDu1xUieSXfFcUDV+bgKakZDqY4qTqc1d+tk5hMTwYLHvVXy5woEEvOLazs4wIN7/2qwUPaXj7SioFVAFMMPTijde1wJo16WTmmGr2t75+mqc8T9wy6fkdv+ebswQoUpyMaIFR18RI6AT3HmTRGFQ6WNB+PW9B3zrr1YzSy+u8gZHyGVGEsjHPDXvAsXzGW75Llmh4JMbeeaL27Gfy8jU52wO0K0inUtfDQGT2vaYShDUgnXOw+NY/jxqMn3ZtKZq4glr1LtcWkvkiLp+q6JxvfyzMRFTZFgv+/Udi0xZKtJ4FKQ3Q0DPUXCZQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQCg7X03RHW9J531DlDIby5dYtCkYs7/DPjLCSuZ/D46fdizgJkdw1P62NOQJCXPB508GPbHYv8pJzxZ7WnsU1QUj1e0X+UOpGPEdpdOjBknKN7ZeK9V8evYaRExdkQIdXqKFbZ2adbLtrkn35+oTaVyIKOxU54GVyIpoThwr4D3w4t7+Lpn1VPSGI4hR0aSR/EZBq6dRsdhl+MCWO5Y1ik6ZMrq4ETwKJUfSp2v7oDUrFgFhXXmlol7rbjdp5vBvJO8aUUDebqnH5P9dpu7/JPS7l4QWrRjOw9VnKoM3a90VOi4U2EcZwmy98JRh6NZXwIdOVY0DUYD40aQVJtS+/yW" />
			</PartnerCertificates>
		</PartnerIdentityProvider>
		<!--<PartnerIdentityProvider Name="http://ADFS.testing.com/adfs/services/trust" SingleLogoutServiceUrl="https://adfs.testing.com/adfs/ls/" NameIDFormat="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress" SingleSignOnServiceUrl="https://adfs.testing.com/adfs/ls/">
			<PartnerCertificates>
				<Certificate Use="Encryption" String="MIIC4jCCAcqgAwIBAgIQQsT8SlUOuo9JQmgT7cHvdDANBgkqhkiG9w0BAQsFADAtMSswKQYDVQQDEyJBREZTIEVuY3J5cHRpb24gLSBBREZTLnRlc3RpbmcuY29tMB4XDTIzMTEwMzAwNDQyM1oXDTI0MTEwMjAwNDQyM1owLTErMCkGA1UEAxMiQURGUyBFbmNyeXB0aW9uIC0gQURGUy50ZXN0aW5nLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALsef1XtkTGs+lkXenN2tg9Lj5YLx+7drHU5TBdg8cS3cFDChStKn6PtKDIGhA+GRSnY2jSGiRc6nPyOaRS/m6hKyBLCoN65G1IQZZ3SGu/uZYtDNGZ2PbP9wvwQVVzgpRlgKO4nsD2a/fbwH3aD3Y4V+DCQ+/1pzw0Gdp8P2xfVgyQWfue+WYrakOga2LpRch0gHsrpExk6YP0A8avEpGsLBWA7K6UG3j+k7Hlw1dY37ajIBvaMxgl0fdNu0BIwDx3SBgUdoI6f8aBQ3V0XPEcmaIc6+HXCB0cWJv2AYScIGhusUMe6MA0b2PN/k4jZ6pXFDUB1kkJJfyRFiIdlcJcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAEDxR1UkmjMV3cwtutzNwkR09MN+PLP0mpVPkLjDfNK0v07izP5A8MwhiSdas54+zXBksFvynPs/X3XREKSeaRslPftTjpzdDsKhqF3W+lqj71Di25f68f9XffPdVxtgVDdC0YP6yV4olKI/q8WarQ/AN4r0hHQ5V8qogwEzBkJOF7nKBc8/Rtmbge1UxH+XcTC9Vcwi8QAUv6GtyYgT7dVUaapCo1WcingfR3zjvLGbb4widYdwm1Tv/MezCu/n9WXTpCgH29/x5LXz8xTTi40GTKaPDCdr7hbAm/V1ephsrA6J2DqRdYsHucPhdFnLJyyrSrQ5B7oAzQmIk0Y9GSQ==" />
				<Certificate Use="Signature" String="MIIC3DCCAcSgAwIBAgIQL7e7Su+ErahKPL7d+C2SmDANBgkqhkiG9w0BAQsFADAqMSgwJgYDVQQDEx9BREZTIFNpZ25pbmcgLSBBREZTLnRlc3RpbmcuY29tMB4XDTIzMTEwMzAwNDQyM1oXDTI0MTEwMjAwNDQyM1owKjEoMCYGA1UEAxMfQURGUyBTaWduaW5nIC0gQURGUy50ZXN0aW5nLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKPzCVu6mgM2aN6LdCNyAGGyczHvgO26UvwVe1HtFo0QvCJz3vDl8jVY/rU2HjXrJRDAHgxKdn540SM4n9XVMHAAeb+4GIw1blOoQrXf4hoUgPW0Ga6votZVBh13/y+ofRnHt0FMkcpCEU9QRjPGJEbk1sg29HBTKsay7aB7PDraL0GtGNiKhOEKx+Srh3++zqxyb5DaInvHbXIleVoVgzzOdd6sUBmTNyM5RhYUlIr6xiMC0BKM+tEsBMwZoI5Y9cu5IP/+LRgZiwhBRKyPsLOgDBJpZRd3XbGZIavCiQSSbV6e/knmkun0bqPQlkM63wPWcVVQ9gbwA9aMnD/Do7sCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAbTycP/Mg3caLl4t0c079gS9VZP8O2Sp8Etn4Ecl1V2q+kvXUp0aSLN8voiw6pMVzpOv4TQZQj5bM+atIgawQbAvHs4NsZcSsgeGeJPu8d2afKY4uG/T/4ZLNXFQ01h3wpEqkiWimm5qqS4HK6YfwyPeAh/q0QTdKkQDuY18sd6N9p7/vNtPUoCmojHCkp9xYvb3LMfNcpkVrGDhIdBcw93srCKgkc/b8o0E2EwjpTfPHG/xp0DyBgl3hKnzeKh672hyvUeeBG9vZ4OHFg80Us9mkp3Vx3nx/nRGIsB8mKPCG/PzD2r2Rcx6zqDAO8yIcKYAaVCMjbFICZZRak0Ly4g==" />
			</PartnerCertificates>
		</PartnerIdentityProvider>-->

		<PartnerIdentityProvider Name="http://sts.icicigroupcompanies.com/adfs/services/trust" SingleLogoutServiceUrl="https://sts.icicigroupcompanies.com/adfs/ls/" NameIDFormat="urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress" SingleSignOnServiceUrl="https://sts.icicigroupcompanies.com/adfs/ls/">
			<PartnerCertificates>
				<Certificate Use="Encryption" String="MIIC+DCCAeCgAwIBAgIQKtrj7xA2/qxE7Cpe8epKJTANBgkqhkiG9w0BAQsFADA4MTYwNAYDVQQDEy1BREZTIEVuY3J5cHRpb24gLSBzdHMuaWNpY2lncm91cGNvbXBhbmllcy5jb20wHhcNMjMwMTI0MTEwODQ2WhcNMjQwMTI0MTEwODQ2WjA4MTYwNAYDVQQDEy1BREZTIEVuY3J5cHRpb24gLSBzdHMuaWNpY2lncm91cGNvbXBhbmllcy5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDUqjoeOm/oaZPi3hETSn1OCgK6TFMEN7YMzbUWy0Aezgunl4vge+Dhgozva73zkhNMAMq/9yk5hAMeCOfkNj6ITw8EwLQtJPV2dBRNEh+l3WZPyGlBR8+nqKTR6poeUnBIDVAq1NyiJJOZrytHXkvqaAUhRuX6zKos1ASugQFkcSvo98W9pGNILdAL2GcTlfbovJba8msMaSigCBGhxuRczy18WilRTP51s4MB45c3+dQSxLk7Ok98xJ9oJAr392lQVvQAY1kW/w9ncZYHvn4zAtIrWa9wNZg2/eTOodG4P5xXOhoHhZiEGjCILEVq3GKo4lKsIaB6Q2lKd1w6n1ftAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAKXIvJ45WQDpb2eK8Z6hb5OM32APPgKditLpaRxXU6GQ+AK6E6sQLNh7UsNVht3tqMNhnf82oXYQCGGEVRrVDg++GN4T4PtLbMZelqzPdS2TTv8351gMlnpZc2TWMgK0IDLpp5CtsrahbRcNzMmq3DPchqWHYrF8YabNGyOu1e+K+rvITm4j3hgMhlGuHIf/DZ23TNwfu0c3N69G9iOywJam9K96Xgz1KWwfXvSHNrBwtj7E2LHt3IAlREQnmaEdeee4QGPKpeolIiHYcRIPe1rOB/3ZVk+dK5lLmdtHuTxYMZwB3STLjIUwfm7cnS2Zy++tJkgmEaY/aFRS8nhyMs4=" />
				<Certificate Use="Signature" String="MIIC8jCCAdqgAwIBAgIQKFv3xem9LbFM++ccxOLBsjANBgkqhkiG9w0BAQsFADA1MTMwMQYDVQQDEypBREZTIFNpZ25pbmcgLSBzdHMuaWNpY2lncm91cGNvbXBhbmllcy5jb20wHhcNMjMwMTI0MTEwODQ4WhcNMjQwMTI0MTEwODQ4WjA1MTMwMQYDVQQDEypBREZTIFNpZ25pbmcgLSBzdHMuaWNpY2lncm91cGNvbXBhbmllcy5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDdi/s1l3ev8O7dMFd3qsnD9bwJkH8P5y43Yp+0utRD0hr5nf3N50MSUjNT9EbBGf2rD2SUfOIKGN1d4V/iKzvafqbnv6DHgpT3zdPpnrsye3Ct6aEYbgaqMczkd3cjvz+jWufkZ+UQEI4Tz+t91/WmJcbW9G14x+QmGS2LV1xn7Osi4c0sIpNWwsC6fXis/5JYwJQ6Wj4TPl5LmSCieaStRewaWMSLEwVufQWu6eH+Nqqq0d2csa/UyoXNNygNFqGq+OCX8au7c3p3G2xlOWdQFG78+yig6MtJxk7QPwMuYFINev+DeC6FWV1X4TKIFlVfwCOu1o9c/1dzDZRPzRTpAgMBAAEwDQYJKoZIhvcNAQELBQADggEBANm+Q34aZr0wFSjyXFc/0kiGwr0qjTMH+aQ4k13AiLdTF9lMBcpghnVdnszgU8X/x/UCJevFAoIjS9IQlGsmRmVMPgihmwOvl8kJhJQ9z+FhNCvDNtCwbFsyZL3k8EZgUHrV5QLSjcWa24BCirdP98g+GRFE/CP3+274bJsPODp1B/Da2AkQL552hAYDG4VfCyudYrjNLU1gcDEngmezVkQlEZoDBYp2jkEhYngpuozc0h59tfZlMHkLCewE6TcrX6dAQBzXqTUIqfjt4hBNvp58/JL+kBrOvrIdvbjDgy8v74qm25SjVQ4hqBE6DBZvd28wgf0Wj3Dd0psFkulY060=" />
			</PartnerCertificates>
		</PartnerIdentityProvider>
	</PartnerIdentityProviders>
</SAMLConfiguration>
