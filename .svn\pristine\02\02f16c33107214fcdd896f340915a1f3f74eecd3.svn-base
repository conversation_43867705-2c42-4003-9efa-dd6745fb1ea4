﻿using CP.UI.Code.Replication.Component;
using CP.UI.Code.Replication.DataLag;
using CP.UI.Code.Replication.ReplicationInfo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CP.UI.Code.Replication.Clients
{
    public class OracleEmcSrdfDMXClient : Replication
    {
        public OracleEmcSrdfDMXClient()
        {
            Datalag = new OracleEmcSrdfDMXDataLag();

            ComponentInfo = new OracleEmcSrdfDMXComponent();

            ReplicationInfo = new OracleEmcSrdfDMXReplicationInfo();
        }
    }
}