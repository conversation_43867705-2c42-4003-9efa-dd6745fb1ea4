﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "SqlnativeServices", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class SqlNativeServices : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GroupId { get; set; }

        public string Server { get; set; }

        public string ServiceName { get; set; }

        public string Status { get; set; }

        public string StartMode { get; set; }

        #endregion Properties
    }
}