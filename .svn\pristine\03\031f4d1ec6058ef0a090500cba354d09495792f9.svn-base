﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess.HanaDBReplicationMode
{
    internal sealed class HanaDbReplicationModeBuilder : IEntityBuilder<HanaDbReplicationMode>
    {
        IList<HanaDbReplicationMode> IEntityBuilder<HanaDbReplicationMode>.BuildEntities(IDataReader reader)
        {
            var hanadbreplimode = new List<HanaDbReplicationMode>();

            while (reader.Read())
            {
                hanadbreplimode.Add(((IEntityBuilder<HanaDbReplicationMode>)this).BuildEntity(reader,
                    new HanaDbReplicationMode()));
            }

            return (hanadbreplimode.Count > 0) ? hanadbreplimode : null;
        }

        HanaDbReplicationMode IEntityBuilder<HanaDbReplicationMode>.BuildEntity(IDataReader reader, HanaDbReplicationMode hanadbreplimode)
        {

            hanadbreplimode.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            hanadbreplimode.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            hanadbreplimode.PRDatabase = Convert.IsDBNull(reader["PRDatabase"]) ? string.Empty : Convert.ToString(reader["PRDatabase"]);
            hanadbreplimode.DRDatabase = Convert.IsDBNull(reader["DRDatabase"]) ? string.Empty : Convert.ToString(reader["DRDatabase"]);

            hanadbreplimode.PRReplicationMode = Convert.IsDBNull(reader["PRReplicationMode"]) ? string.Empty : Convert.ToString(reader["PRReplicationMode"]);
            hanadbreplimode.DRReplicationMode = Convert.IsDBNull(reader["DRReplicationMode"]) ? string.Empty : Convert.ToString(reader["DRReplicationMode"]);

            hanadbreplimode.PRReplicationStatus = Convert.IsDBNull(reader["PRReplicationStatus"]) ? string.Empty : Convert.ToString(reader["PRReplicationStatus"]);
            hanadbreplimode.DRReplicationStatus = Convert.IsDBNull(reader["DRReplicationStatus"]) ? string.Empty : Convert.ToString(reader["DRReplicationStatus"]);

            hanadbreplimode.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
            return hanadbreplimode;

        }

    }
}
