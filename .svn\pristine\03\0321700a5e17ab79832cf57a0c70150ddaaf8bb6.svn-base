﻿using System;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    public class DnsServerDetails : BaseEntity
    {
        #region Properties

        public string Name { get; set; }

        public string Zone { get; set; }

        public string Domain { get; set; }

        public string Host { get; set; }

        public string IPAddress { get; set; }

        public DateTime DNSTime { get; set; }

        public string DNSRefFwd { get; set; }

        public int HostId { get; set; }

        #endregion Properties
    }
}