﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IBusinessFunctionBIADetailsDataAccess

    public interface IBusinessFunctionBIADetailsDataAccess
    {
        BusinessFunctionBIADetails Add(BusinessFunctionBIADetails businessfunctionBIADetails);

        BusinessFunctionBIADetails Update(BusinessFunctionBIADetails businessfunctionBIADetails);

        IList<BusinessFunctionBIADetails> GetByBusinessfunctionId(int businessfunctionid);

        IList<BusinessFunctionBIADetails> GetByParentAndChildFunctionID(int parentfunctionid, int Childfunctionid);

        IList<BusinessFunctionBIADetails> GetByFuncidandImpactedid(int functionid, int impactedid);

        bool DeleteById(int id);

        bool IsExistbyID(int id);
    }

    #endregion
  
}
