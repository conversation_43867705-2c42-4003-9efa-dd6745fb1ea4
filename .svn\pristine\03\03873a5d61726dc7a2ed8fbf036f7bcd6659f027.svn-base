﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="RSyncMonitorDetails.ascx.cs" Inherits="CP.UI.Controls.RSyncMonitorDetails" %>


<div class="widget">
    <div class="widget-head">
        <asp:Label class="heading" runat="server" Text="Rsync Replication Details" Style="padding-left: 5px !important;"></asp:Label>
    </div>
    <div class="widget-body innerAll inner-2x">
        <table id="tblRSyncMonitor" runat="server" class="table table-striped table-condensed table-bordered table-responsive monitortable font" style="display: table;">
            <thead>
                <tr>
                    <th class="col-md-4">Replication Monitor</th>
                    <th class="col-md-4"></th>
                   
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Primary Server </td>
                    <td class="text-indent">
                        <asp:Label ID="lblPRServerImg" runat="server"></asp:Label>
                        <asp:Label ID="lblPRServer" runat="server"></asp:Label>
                    </td>
                  
                </tr>
                <tr>
                    <td>Secondary Server </td>
                    <td>
                        <asp:Label ID="lblPRSecServerIcon" runat="server"></asp:Label>
                        <asp:Label ID="lblPRSecServer" runat="server"></asp:Label>
                    </td>
                 
                </tr>

                <tr id="tr41">
                    <td>Source Replication Path </td>
                    <td class="text-indent"><span id="spnPrSrcPath" runat="server">&nbsp;</span>
                        <asp:Label ID="lblPRSrcReplPath" runat="server"></asp:Label>
                    </td>
                   
                </tr>
                <tr id="tr1" runat="server">
                    <td>Destination Path </td>
                    <td class="text-indent word-wrap">
                        <span id="spnPrDestiPath" runat="server">&nbsp;</span>&nbsp;
                <asp:Label ID="lblPRDestPath" runat="server" CssClass="break-word"></asp:Label>
                    </td>
                   
                </tr>
                <tr id="tr32" runat="server">
                    <td>Number of files </td>
                    <td class="text-indent word-wrap">
                        <span id="spanPrStatus" runat="server" class="icon-log">&nbsp;</span>&nbsp;
                 <asp:Label ID="lblPRNoOfFiles" runat="server" CssClass="break-word"></asp:Label>
                    </td>
                    
                </tr>
                <tr id="tr2" runat="server">
                    <td>Total file size </td>
                    <td class="text-indent word-wrap">
                        <span class="icon-numbering pull-left">&nbsp;</span>&nbsp;
                 <asp:Label ID="lblPRTotalFileSize" runat="server" CssClass="break-word"></asp:Label>
                    </td>
                   
                </tr>
                <tr id="tr3" runat="server">
                    <td>Number of regular files transferred</td>
                    <td class="text-indent word-wrap">
                        <span class="icon-numbering pull-left">&nbsp;</span>&nbsp;
                 <asp:Label ID="lblPRNoOfFileTras" runat="server" CssClass="break-word"></asp:Label>
                    </td>
                   
                </tr>

                <tr id="tr5" runat="server">
                    <td>Total transferred file size</td>
                    <td class="text-indent word-wrap">
                        <span class="icon-numbering pull-left">&nbsp;</span>&nbsp;
               <asp:Label ID="lblPRTotalTrasFileSize" runat="server" CssClass="break-word"></asp:Label>
                    </td>
                    
                </tr>
            </tbody>
        </table>
    </div>
</div>
