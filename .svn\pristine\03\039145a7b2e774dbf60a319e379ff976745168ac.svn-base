﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IDatabaseVersionDataAccess
    {
        DatabaseVersion Add(DatabaseVersion databaseversion);

        DatabaseVersion Update(DatabaseVersion databaseversion);

        DatabaseVersion GetById(int id);

        IList<DatabaseVersion> GetAll();

        bool DeleteById(int id);

        bool IsExistByName(string id);
    }
}