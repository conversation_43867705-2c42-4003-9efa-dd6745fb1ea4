﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;


namespace CP.DataAccess
{
    public interface IZFSReplicationMonitorLogsDataAccess
    {

        IList<ZFSReplicationMonitorLogs> GetAlZFSReplicationLogs();

        ZFSReplicationMonitorLogs GetZFSRepliByInfrad(int infraId);

        IList<ZFSReplicationMonitorLogs> GetZFSRepliByInfraObjId(int infraId);

        IList<ZFSReplicationMonitorLogs> GetZFSReplicationByDate(int infraId, string startdate, string enddate); //

        IList<ZFSReplicationMonitorLogs> GetZFSReplicationByHrsByInfraObjId(int infraId);

    }
}
