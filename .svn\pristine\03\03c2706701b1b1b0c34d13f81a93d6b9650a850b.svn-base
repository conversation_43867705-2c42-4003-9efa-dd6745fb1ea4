﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class LicencekeyDataAccess : BaseDataAccess, ILicencekeyDataAccess
    {
        #region Constructors

        public LicencekeyDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<licensekey> CreateEntityBuilder<licensekey>()
        {
            return (new LicencekeyBuilder()) as IEntityBuilder<licensekey>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="Licencekey" /> bcms_licensekey Table.
        /// </summary>
        /// <param name="licensekey">Licencekey</param>
        /// <returns>Licencekey</returns>
        /// <author>Ranjith <PERSON></author>
        Licencekey ILicencekeyDataAccess.Add(Licencekey licensekey)
        {
            try
            {
                const string sp = "Licence_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring+"iKey", DbType.AnsiString, licensekey.Key);
                    Database.AddInParameter(cmd, Dbstring+"iValidFrom", DbType.DateTime, licensekey.ValidFrom);
                    Database.AddInParameter(cmd, Dbstring+"iValidTo", DbType.DateTime, licensekey.ValidTo);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, licensekey.Status);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, licensekey.CreatorId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        licensekey = reader.Read()
                            ? CreateEntityBuilder<Licencekey>().BuildEntity(reader, licensekey)
                            : null;
                    }
                    if (licensekey == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Database already exists. Please specify another database.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this database.");
                                }
                        }
                    }

                    return licensekey;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting licensekey Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Upadet <see cref="licensekey" /> bcms_licensekey Table.
        /// </summary>
        /// <param name="licensekey">Fields of the Licencekey</param>
        /// <returns>Licencekey</returns>
        /// <author>Ranjith Singh</author>
        Licencekey ILicencekeyDataAccess.Update(Licencekey licensekey)
        {
            try
            {
                const string sp = "Licence_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, licensekey.Id);
                    Database.AddInParameter(cmd, Dbstring+"iKey", DbType.AnsiString, licensekey.Key);
                    Database.AddInParameter(cmd, Dbstring+"iValidFrom", DbType.DateTime, licensekey.ValidFrom);
                    Database.AddInParameter(cmd, Dbstring+"iValidTo", DbType.DateTime, licensekey.ValidTo);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, licensekey.Status);
                    Database.AddInParameter(cmd, Dbstring+"iUpdatorId", DbType.Int32, licensekey.UpdatorId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? CreateEntityBuilder<Licencekey>().BuildEntity(reader, licensekey) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Licencekey Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     GetAll <see cref="Licencekey" /> from bcms_licensekey Table.
        /// </summary>
        /// <returns>Licencekey List</returns>
        /// <author>Ranjith Singh</author>
        IList<Licencekey> ILicencekeyDataAccess.GetAll()
        {
            try
            {
                const string sp = "Licencekey_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Licencekey>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ILicencekeyDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="Licencekey" /> bcms_licensekey Table.
        /// </summary>
        /// <param name="Key">Key of the Licencekey</param>
        /// <returns>Licencekey</returns>
        /// <author>Ranjith Singh</author>
        Licencekey ILicencekeyDataAccess.IsValidkey(string Key)
        {
            try
            {
                if (Key == string.Empty)
                {
                    throw new ArgumentNullException("Key");
                }
                const string sp = "Licence_IsValidkey";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iKey", DbType.String, Key);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return CreateEntityBuilder<Licencekey>().BuildEntity(reader, new Licencekey());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ILicencekeyDataAccess.IsExistByName (" + Key + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Upadet <see cref="Licencekey" /> bcms_licensekey Table.
        /// </summary>
        /// <param name="status">Status of the Licencekey</param>
        /// <returns>Licencekey</returns>
        /// <author>Ranjith Singh</author>
        Licencekey ILicencekeyDataAccess.UpdateStatus(int id, int status)
        {
            try
            {
                const string sp = "Licence_UpdateByStatus";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, status);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return CreateEntityBuilder<Licencekey>().BuildEntity(reader, new Licencekey());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating Licencekey Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        #endregion Methods
    }
}