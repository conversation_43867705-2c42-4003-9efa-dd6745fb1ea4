﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using CP.Common.Base;


namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "BIASuccessWFvsHI", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BIASuccessWFvsHI : BaseEntity
    {
        #region Properties

        [DataMember]
        public string ProfileName { get; set; }

        // Total Profile Run
        [DataMember]
        public int Totalrun { get; set; }

        [DataMember]
        public int ProfileId { get; set; }

        [DataMember]
        public int TotalWorkflowRun { get; set; }

        [DataMember]
        public int Abort { get; set; }

        [DataMember]
        public int Retry { get; set; }

        [DataMember]
        public int Skip { get; set; }

        [DataMember]
        public double TotalHumenIntervention { get; set; }

        [DataMember]
        public double Percentage { get; set; }

        #endregion
    }
}
