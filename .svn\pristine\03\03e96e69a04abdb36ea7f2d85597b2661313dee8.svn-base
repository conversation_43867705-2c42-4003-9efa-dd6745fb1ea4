﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class IncidentBuilder : IEntityBuilder<Incident>
    {
        IList<Incident> IEntityBuilder<Incident>.BuildEntities(IDataReader reader)
        {
            var incident = new List<Incident>();

            while (reader.Read())
            {
                incident.Add(((IEntityBuilder<Incident>)this).BuildEntity(reader, new Incident()));
            }

            return (incident.Count > 0) ? incident : null;
        }

        Incident IEntityBuilder<Incident>.BuildEntity(IDataReader reader, Incident incident)
        {
            incident.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            incident.AlertId = Convert.IsDBNull(reader["AlertId"]) ? 0 : Convert.ToInt32(reader["AlertId"]);
            incident.WorkFlowId = Convert.IsDBNull(reader["WorkFlow_Id"]) ? 0 : Convert.ToInt32(reader["WorkFlow_Id"]);
            incident.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            incident.IncidentNumber = Convert.IsDBNull(reader["IncidentNumber"])
                ? string.Empty
                : Convert.ToString(reader["IncidentNumber"]);
            incident.IncidentName = Convert.IsDBNull(reader["IncidentName"])
                ? string.Empty
                : Convert.ToString(reader["IncidentName"]);
            incident.Description = Convert.IsDBNull(reader["Description"])
                ? string.Empty
                : Convert.ToString(reader["Description"]);
            incident.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());

            return incident;
        }
    }
}