﻿using System;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;

namespace CP.UI.Code.Replication.Component
{
    public class VmWarewithHitachiComponent : IComponentInfo
    {
        private readonly IFacade _facade = new Facade();

        private ComponentInfo _componentInfo;

        public ComponentInfo CurrentComponent
        {
            get { return _componentInfo ?? (_componentInfo = new ComponentInfo()); }
        }

        #region IComponentInfo Members

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId)
        {
            _componentInfo = null;

            GetServerInformation(prServerId, true);

            GetServerInformation(drServerId, false);

            //Ram <PERSON>- GetVmWareMonitorStatusByGroupId method has been replaced by GetVmWareMonitorStatusByInfraObjId in CPV4.0

           // commenting this as vmwaremonitor entity doesn't have all required fields to map vmwarehitachi monitor status..
            //var vmwarestatus = _facade.GetVmWareMonitorStatusByInfraObjId(infraObjectId);

           

            var vmwarestatus = _facade.GetMonitorStatusByInfraObjectId(infraObjectId);
            var Infraobject = _facade.GetInfraObjectById(infraObjectId);

            var vmwarepathbyReplId = _facade.VmwarePathGetbyReplicationId(Infraobject.PRReplicationId);
            int VmConfigured = 0;
            if(vmwarepathbyReplId != null)
                VmConfigured = vmwarepathbyReplId.Count;

            if (vmwarestatus != null)
            {
                CurrentComponent.DatastoreName = vmwarestatus.DatastoreNamePR;
               // CurrentComponent.NoOfVMConfigured = Convert.ToInt32(vmwarestatus.NoofVmNetsnapMirrorConfigured);

                //CurrentComponent.NoOfVMConfigured = Convert.ToInt32(string.IsNullOrEmpty(Convert.ToString(VmConfigured)) ? "0" : Convert.ToString(VmConfigured));
                CurrentComponent.NoOfVMConfigured = VmConfigured;

                CurrentComponent.NoOfVMPowerOffState = Convert.ToInt32(vmwarestatus.NoofpowerStateoffVmNetsnapMirror);
                CurrentComponent.NoOfVMPowerOnState = Convert.ToInt32(vmwarestatus.NoofpowerStateonVmNetsnapMirror);

                CurrentComponent.DRDataStoreName = vmwarestatus.DatastoreNameDR;
                //CurrentComponent.NoofVmNetsnapMirrorConfiguredDR = vmwarestatus.NoofVmNetsnapMirrorConfiguredDR;

                CurrentComponent.NoofVmNetsnapMirrorConfiguredDR = string.IsNullOrEmpty(Convert.ToString(VmConfigured)) ? "0" : Convert.ToString(VmConfigured);

                CurrentComponent.NoofpowerStateoffVmNetsnapMirrorDR = vmwarestatus.NoofpowerStateoffVmNetsnapMirrorDR;
                CurrentComponent.NoofpowerStateonVmNetsnapMirrorDr = vmwarestatus.NoofpowerStateonVmNetsnapMirrorDr;
            }
            else
            {
                BindNullVmWareComponent();
            }
            return CurrentComponent;
        }

        private void BindNullVmWareComponent()
        {
            CurrentComponent.PRPowerState = false;
            CurrentComponent.DRPowerState = false;

            CurrentComponent.PRSnapShot = "N/A";
            CurrentComponent.DRSnapShot = "N/A";

            CurrentComponent.PRUpdatedTime = "N/A";
            CurrentComponent.DRUpdatedTime = "N/A";
        }

        private void BindNullServerComponents(bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = "N/A";
                CurrentComponent.PRServerIP = "N/A";
                CurrentComponent.PRServerOSType = "N/A";
                CurrentComponent.PRServerStatus = "Down";
            }
            else
            {
                CurrentComponent.DRServerName = "N/A";
                CurrentComponent.DRServerIP = "N/A";
                CurrentComponent.DRServerOSType = "N/A";
                CurrentComponent.DRServerStatus = "Down";
            }
        }

        private void BindServerComponents(Server server, bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = server.Name;
                CurrentComponent.PRServerIP = server.IPAddress;
                CurrentComponent.PRServerOSType = server.OSType;
                CurrentComponent.PRServerStatus = server.Status.ToString();
                CurrentComponent.PRDataStoreName = server.DataStoreName;
            }
            else
            {
                CurrentComponent.DRServerName = server.Name;
                CurrentComponent.DRServerIP = server.IPAddress;
                CurrentComponent.DRServerOSType = server.OSType;
                CurrentComponent.DRServerStatus = server.Status.ToString();
                CurrentComponent.DRDataStoreName = server.DataStoreName;
            }
        }

        private void GetServerInformation(int serverId, bool isPrimary)
        {
            try
            {
                var server = _facade.GetServerById(serverId);

                if (server != null)
                {
                    BindServerComponents(server, isPrimary);
                }
                else
                {
                    BindNullServerComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullServerComponents(isPrimary);
            }
        }

        #endregion IComponentInfo Members

        #region IComponentInfo Members

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId,int mailBoxId, string mailboxname)
        {
            throw new NotImplementedException();
        }

        #endregion IComponentInfo Members
    }
}