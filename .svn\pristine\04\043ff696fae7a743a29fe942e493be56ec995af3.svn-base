﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class BIAActionFailureHumanInterventionTrendBuilder : IEntityBuilder<BIAActionFailureHumanInterventionTrend>
    {
        IList<BIAActionFailureHumanInterventionTrend> IEntityBuilder<BIAActionFailureHumanInterventionTrend>.BuildEntities(IDataReader reader)
        {
            var BIAFA = new List<BIAActionFailureHumanInterventionTrend>();

            while (reader.Read())
            {
                BIAFA.Add(((IEntityBuilder<BIAActionFailureHumanInterventionTrend>)this).BuildEntity(reader, new BIAActionFailureHumanInterventionTrend()));
            }
            return (BIAFA.Count > 0) ? BIAFA : null;
        }

        BIAActionFailureHumanInterventionTrend IEntityBuilder<BIAActionFailureHumanInterventionTrend>.BuildEntity(IDataReader reader, BIAActionFailureHumanInterventionTrend biaActionFailureHumanInterventionTrend)
        {
            biaActionFailureHumanInterventionTrend.Cur_Month = Convert.IsDBNull(reader["Cur_Month"]) ? 0 : Convert.ToInt32(reader["Cur_Month"]);
            biaActionFailureHumanInterventionTrend.Cur_Year = Convert.IsDBNull(reader["Cur_Year"]) ? 0 : Convert.ToInt32(reader["Cur_Year"]);
            biaActionFailureHumanInterventionTrend.AbortPer = Convert.IsDBNull(reader["AbortPer"]) ? 0 : Convert.ToDouble(reader["AbortPer"]);
            biaActionFailureHumanInterventionTrend.SkipPer = Convert.IsDBNull(reader["SkipPer"]) ? 0 : Convert.ToDouble(reader["SkipPer"]);
            biaActionFailureHumanInterventionTrend.RetryPer = Convert.IsDBNull(reader["RetryPer"]) ? 0 : Convert.ToDouble(reader["RetryPer"]);
            biaActionFailureHumanInterventionTrend.TotalPer = Convert.IsDBNull(reader["TotalPer"]) ? 0 : Convert.ToDouble(reader["TotalPer"]);
            biaActionFailureHumanInterventionTrend.AbortFailcount = Convert.IsDBNull(reader["AbortFailcount"]) ? 0 : Convert.ToInt32(reader["AbortFailcount"]);
            biaActionFailureHumanInterventionTrend.SkipFailcount = Convert.IsDBNull(reader["SkipFailcount"]) ? 0 : Convert.ToInt32(reader["SkipFailcount"]);
            biaActionFailureHumanInterventionTrend.RetryFailcount = Convert.IsDBNull(reader["RetryFailcount"]) ? 0 : Convert.ToInt32(reader["RetryFailcount"]);
            biaActionFailureHumanInterventionTrend.TotalFailcount = Convert.IsDBNull(reader["TotalFailcount"]) ? 0 : Convert.ToInt32(reader["TotalFailcount"]);

            return biaActionFailureHumanInterventionTrend;
        }

    }
}
