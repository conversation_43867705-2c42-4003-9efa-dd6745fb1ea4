﻿using System;
using System.Collections.Generic;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.UI
{
    public partial class MaxDBDatabaseConfig : BaseControl
    {        
        #region Properties

        public string MaxDBSID
        {
            get
            {
                return txtMaxDBSId.Text;
            }
            set
            {
                txtMaxDBSId.Text = value;
            }
        }

        public string UserName
        {
            get
            {
                return txtUserName.Text;
            }
            set
            {
                txtUserName.Text = value;
            }
        }

        public string Password
        {
            get
            {
                return txtPassword.Text;
            }
            set
            {
                txtPassword.Text = value;
            }
        }

        public string Port
        {
            get
            {
                return txtPort.Text;
            }
            set
            {
                txtPort.Text = value;
            }
        }

        #endregion

        public override void PrepareView()
        {
            txtMaxDBSId.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");
            txtUserName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
            txtPassword.Attributes.Add("onblur", "getHashData(" + txtPassword.ClientID + "),ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            //txtPassword.Attributes.Add("onblur", "getHashData(" + txtPassword.ClientID + "),ValidatorValidate(" + rfvpassword.ClientID + ")");
            txtPort.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");
        }


    }
}