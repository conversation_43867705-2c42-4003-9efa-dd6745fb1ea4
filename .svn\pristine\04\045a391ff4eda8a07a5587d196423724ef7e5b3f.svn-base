﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IInfraObjectsLunsDataAccess

    public interface IInfraObjectsLunsDataAccess
    {
        InfraObjectsLuns Add(InfraObjectsLuns groupLuns);

        InfraObjectsLuns Update(InfraObjectsLuns groupLuns);

        IList<InfraObjectsLuns> GetById(int id);

        IList<InfraObjectsLuns> GetAll();
    }

    #endregion IInfraObjectsLunsDataAccess
}