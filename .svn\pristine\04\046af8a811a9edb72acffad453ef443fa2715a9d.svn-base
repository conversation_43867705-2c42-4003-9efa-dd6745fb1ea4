﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using CP.Common.DatabaseEntity;
using System.Web.UI.WebControls;

namespace CP.UI.Code.Base
{
    public abstract  class WFProfileApprovalProcessBasePageEditor :WFProfileApprovalProcessBasePage ,IEditor<CP.Common.DatabaseEntity.ApprovalProcess>
    {

        private string _message = string.Empty;

        #region Properties

        public ApprovalProcess CurrentEntity
        {
            get { return base.CurrentApproval; }
            set { base.CurrentApproval = value; }
        }

        public int CurrentEntityId
        {
            get { return base.CurrentWFapprovalid; }
            set { base.CurrentWFapprovalid = 0; }
        }

        public abstract string MessageInitials
        {
            get;
        }

        protected new string Message
        {
            get
            {
                return _message;
            }
            set
            {
                _message = value;
            }
        }

        public abstract string ReturnUrl
        {
            get;
        }

        public virtual Label TotalResult
        {
            get
            {
                return null;
            }
        }

        #endregion Properties

        #region Method

        public CP.Common.DatabaseEntity.ApprovalProcess BuildEntity(CP.Common.DatabaseEntity.ApprovalProcess currentEntity)
        {
            return currentEntity;
        }

        public abstract void PrepareEditView();

        public abstract void SaveEditor();

        public virtual void PrepareValidator()
        {
        }

        public abstract void BuildEntities();

        public virtual void BindList()
        {
        }

        public virtual void Delete(int entityId)
        {
        }

        public virtual void ClearForm()
        {
            CurrentEntityId = 0;
        }

        public virtual void FinalizeCommand()
        {
        }

        #endregion properties

        public override void PrepareView()
        {
            throw new System.NotImplementedException();
        }

        #region IEditor<WFProfileApprovalProcess> Members


        #endregion IEditor<AlertReceiver> Members
    }
}