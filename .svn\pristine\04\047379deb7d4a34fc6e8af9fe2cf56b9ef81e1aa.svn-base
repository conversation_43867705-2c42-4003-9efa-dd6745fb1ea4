﻿using System;
using System.Data;
using System.Data.Common;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class AlertNotificationDataAccess : BaseDataAccess, IAlertNotificationDataAccess
    {
        #region Constructors

        public AlertNotificationDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<AlertNotification> CreateEntityBuilder<AlertNotification>()
        {
            return (new AlertNotificationBuilder()) as IEntityBuilder<AlertNotification>;
        }

        #endregion Constructors

        #region IAlertNotificationDataAccess Members

        public int GetByCategoryAndType(int alertCategory, int alerttype, int infraObjectId)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("AlertNoti_GetByCateAndType"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertCategoryId", DbType.Int32, alertCategory);
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertType", DbType.Int32, alerttype);
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertCategory", DbType.Int32, infraObjectId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur_alertnoti"));
#endif

                    var myScalar = Database.ExecuteScalar(dbCommand);

                    return Convert.ToInt32(myScalar);
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, "Exception occurred while checking alert notification count for a group", exc);
            }
        }

        public bool UpdateByCategoryAndType(int alertCategory, int alerttype, bool isNotified, int infraObjectId)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("AlertNoti_UpdtByCatIdAndTyp"))
                {
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertCategoryId", DbType.Int32, alertCategory);
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertType", DbType.Int32, alerttype);
                    Database.AddInParameter(dbCommand, Dbstring+"iNotification", DbType.Int32, isNotified);
                    Database.AddInParameter(dbCommand, Dbstring+"iAlertCategory", DbType.Int32, infraObjectId);
#if ORACLE
                    dbCommand.Parameters.Add(BuildRefCursorParameter("cur_alertnoti"));
#endif
                    int success = Database.ExecuteNonQuery(dbCommand);
                    return success > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, "Exception occurred while update Alert Notification ", exc);
            }
        }

        #endregion IAlertNotificationDataAccess Members
    }
}