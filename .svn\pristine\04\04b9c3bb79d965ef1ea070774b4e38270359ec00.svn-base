﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;

namespace CP.UI
{
    public abstract class ImpactBasePage : BasePage
    {
        private int _ImpactidId = 0;
        private string _configType = "";
        private BusinessImpact _businessImpact = null;

        #region Properties

        protected int CurrentImpactId
        {
            get
            {
                if (_ImpactidId == 0)
                {
                    if (Helper.Url.SecureUrl[Constants.UrlConstants.Params.ID].IsNotNullOrEmpty())
                    {
                        _ImpactidId = Helper.Url.SecureUrl[Constants.UrlConstants.Params.ID].ToInteger();
                    }
                }
                return _ImpactidId;
            }
            set
            {
                _ImpactidId = value;
            }
        }

        protected string CurrentConfigType
        {
            get
            {
                if (string.IsNullOrEmpty(_configType))
                {
                    if (Helper.Url.SecureUrl[Constants.UrlConstants.Params.configType].IsNotNullOrEmpty())
                    {
                        _configType = Helper.Url.SecureUrl[Constants.UrlConstants.Params.configType].ToString();
                    }
                }
                return _configType;
            }
            set
            {
                _configType = value;
            }
        }

        protected BusinessImpact CurrentBusinessImpact
        {
            get
            {
                if (_businessImpact == null)
                {
                    if (CurrentImpactId > 0 && _configType == "InfraToBF")
                    {
                        _businessImpact = Facade.GetBusinessImpactInfratoBFById(CurrentImpactId);

                    }
                    else if (CurrentImpactId > 0 && _configType == "BFtoBF")
                    {
                        _businessImpact = Facade.GetBusinessImpactBFtoBFById(CurrentImpactId);

                    }
                    else if (CurrentImpactId > 0 && _configType == "BFtoBS")
                    {
                        _businessImpact = Facade.GetBusinessImpactBFtoBSById(CurrentImpactId);

                    }
                    else if (CurrentImpactId > 0 && _configType == "BStoBS")
                    {
                        _businessImpact = Facade.GetBusinessImpactBStoBSById(CurrentImpactId);

                    }
                    if (_businessImpact == null)
                    {
                        _businessImpact = new BusinessImpact();
                    }
                }
                return _businessImpact;
            }
            set
            {
                _businessImpact = value;
            }
        }

        #endregion Properties
    }
}