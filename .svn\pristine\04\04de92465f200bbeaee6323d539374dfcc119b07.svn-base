﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using CP.UI.Code.Base;

namespace CP.UI.ImpactAnalysis
{
    public partial class ImpactTypeMasterList : ImpactTypeMasterBasePage
    {
        #region variable

        public static string CurrentURL = Constants.UrlConstants.Urls.ImpactAnalysis.ImpactCategoryConfiguration;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.ImpactAnalysis.ImpactCategoryList;
                }
                return string.Empty;
            }
        }

        #endregion variable

        public override void PrepareView()
        {
            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }

            Utility.SelectMenu(Master, "Module3");
            BindList();
        }

        private void BindList()
        {
            setListViewPage();
            lvComponent.DataSource = Facade.GetAllImpactTypeMaster();
            lvComponent.DataBind();
        }

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageImpactcategoryList"]) != -1) && Session["CurrentPageImpactcategoryList"] != null && (Convert.ToInt32(Session["CurrentPageImpactcategoryList"]) > 0))
            {
                if (Session["TotalPageRowsCount"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageImpactcategoryList"]) == Convert.ToInt32(Session["TotalPageRowsCount"]) - 1)
                    {
                        Session["CurrentPageImpactcategoryList"] = Convert.ToInt32(Session["CurrentPageImpactcategoryList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCount"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageImpactcategoryList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageImpactcategoryList"] = -1;

            }
        }

        protected void LvComponentItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageImpactcategoryList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCount"] = dataPager1.TotalRowCount;
                var lbl = (lvComponent.Items[e.ItemIndex].FindControl("ID")) as Label;
                if (lbl != null)
                {
                    var ImpacttypeDetail = Facade.GetImpactTypeMasterById(Convert.ToInt32(lbl.Text));
                    var lblName = (lvComponent.Items[e.ItemIndex].FindControl("ImpactTypeName")) as Label;
                        Facade.DeleteImpactTypeMasterById(Convert.ToInt32(lbl.Text));

                        ActivityLogger.AddLog(LoggedInUserName, "Impact type", UserActionType.DeleteImpactTypeMaster, "The Impact type '" + lblName.Text + "' was deleted", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Impact type" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void LvComponentItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageImpactcategoryList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvComponent.Items[e.NewEditIndex].FindControl("ID")) as Label;
            if (lbl1 != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ImpactTypeMasterId,
                                                     lbl1.Text);
            }

            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvComponent_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }
    }
}