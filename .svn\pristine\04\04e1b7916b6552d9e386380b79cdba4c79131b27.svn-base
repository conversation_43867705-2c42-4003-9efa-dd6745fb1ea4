﻿using System.Collections.Generic;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;

namespace CP.UI.Admin.DashboardContols
{
    public partial class ServiceAndAlertStatus : BaseControl
    {
        public override void PrepareView()
        {
            GetAlertMessage();
            GetIncidentCount();
        }

        private void GetAlertMessage()
        {
            IList<Alert> alertMessage = Facade.GetAlertsByUserId(LoggedInUserId);
            IList<AlertsManager> incidentcount = Facade.GetIncidentCount();

            if (incidentcount != null && incidentcount.Count > 0)
            {
                lnkIncidentCount.Text = incidentcount.Count.ToString();
            }

            if (alertMessage != null)
            {
                lnkAlertCount.Visible = true;

                lnkAlertCount.Text = alertMessage.Count.ToString();
            }
            else
            {
                lnkAlertCount.Visible = false;
            }
        }

        private void GetIncidentCount()
        {
            IList<Incident> incidentCount = Facade.GetIncidentByUserId(LoggedInUserId);

            if (incidentCount != null)
            {
                lnkIncidentCount.Visible = true;

                lnkIncidentCount.Text = incidentCount.Count.ToString();
            }
            else
            {
                lnkIncidentCount.Visible = false;
            }
        }
    }
}