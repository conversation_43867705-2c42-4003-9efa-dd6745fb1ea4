﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class DatabaseMaxDBDataAccess : BaseDataAccess, IDatabaseMaxDBDataAccess
    {
        #region Constructors

        public DatabaseMaxDBDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DatabaseMaxDB> CreateEntityBuilder<DatabaseMaxDB>()
        {
            return (new DatabaseMaxDBBuilder()) as IEntityBuilder<DatabaseMaxDB>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="DatabaseMaxDB" /> database_maxdb table.
        /// </summary>
        /// <param name="databaseMaxDB">DatabaseMaxDB</param>
        /// <returns>DatabaseMaxDB</returns>
        /// <author><PERSON>a <PERSON></author>
        DatabaseMaxDB IDatabaseMaxDBDataAccess.Add(DatabaseMaxDB databaseMaxDB)
        {
            try
            {
                const string sp = "DatabaseMaxDB_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iBaseDatabaseId", DbType.Int32, databaseMaxDB.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseSID", DbType.String, databaseMaxDB.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, databaseMaxDB.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, databaseMaxDB.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, databaseMaxDB.Port);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, databaseMaxDB.InstanceName);
                    Database.AddInParameter(cmd, Dbstring + "iInstallationPath", DbType.String, databaseMaxDB.InstallationPath);
                    Database.AddInParameter(cmd, Dbstring + "iMediumName", DbType.String, databaseMaxDB.MediumName);
                    Database.AddInParameter(cmd, Dbstring + "iLogfileName", DbType.String, databaseMaxDB.LogfileName);
                    Database.AddInParameter(cmd, Dbstring + "iLogPath", DbType.String, databaseMaxDB.LogPath);
                    Database.AddInParameter(cmd, Dbstring + "iDataPath", DbType.String, databaseMaxDB.DataPath);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseMaxDB = reader.Read()
                            ? CreateEntityBuilder<DatabaseMaxDB>().BuildEntity(reader, databaseMaxDB)
                            : null;
                    }

                    if (databaseMaxDB == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "databaseMaxDB already exists. Please specify another database_maxdb");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this database_maxdb");
                                }
                        }
                    }

                    return databaseMaxDB;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting DatabaseMaxDB Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Create <see cref="DatabaseMaxDB" /> database_maxdb table.
        /// </summary>
        /// <param name="databaseMaxDB">DatabaseMaxDB</param>
        /// <returns>DatabaseMaxDB</returns>
        /// <author>Uma Mehavarnan</author>
        DatabaseMaxDB IDatabaseMaxDBDataAccess.Update(DatabaseMaxDB databaseMaxDB)
        {
            try
            {
                const string sp = "DatabaseMaxDB_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, databaseMaxDB.Id);
                    Database.AddInParameter(cmd, Dbstring + "iBaseDatabaseId", DbType.Int32, databaseMaxDB.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iMaxDBSID", DbType.String, databaseMaxDB.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, databaseMaxDB.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, databaseMaxDB.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, databaseMaxDB.Port);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, databaseMaxDB.InstanceName);
                    Database.AddInParameter(cmd, Dbstring + "iInstallationPath", DbType.String, databaseMaxDB.InstallationPath);
                    Database.AddInParameter(cmd, Dbstring + "iMediumName", DbType.String, databaseMaxDB.MediumName);
                    Database.AddInParameter(cmd, Dbstring + "iLogfileName", DbType.String, databaseMaxDB.LogfileName);
                    Database.AddInParameter(cmd, Dbstring + "iLogPath", DbType.String, databaseMaxDB.LogPath);
                    Database.AddInParameter(cmd, Dbstring + "iDataPath", DbType.String, databaseMaxDB.DataPath);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseMaxDB = reader.Read()
                            ? CreateEntityBuilder<DatabaseMaxDB>().BuildEntity(reader, databaseMaxDB)
                            : null;
                    }

                    if (databaseMaxDB == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "databaseMaxDB already exists. Please specify another database_maxdb");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this database_maxdb");
                                }
                        }
                    }

                    return databaseMaxDB;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While updating DatabaseMaxDB Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Create <see cref="DatabaseMaxDB" /> database_maxdb table.
        /// </summary>
        /// <param name="databaseMaxDB">DatabaseMaxDB</param>
        /// <returns>DatabaseMaxDB</returns>
        /// <author>Uma Mehavarnan</author>
        DatabaseMaxDB IDatabaseMaxDBDataAccess.UpdateByDatabaseBaseId(DatabaseMaxDB databaseMaxDB)
        {
            try
            {
                const string sp = "DatabaseMaxDB_UpdateByDBBaseId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);                    
                    Database.AddInParameter(cmd, Dbstring + "iBaseDatabaseId", DbType.Int32, databaseMaxDB.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseSID", DbType.String, databaseMaxDB.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, databaseMaxDB.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, databaseMaxDB.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, databaseMaxDB.Port);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, databaseMaxDB.InstanceName);
                    Database.AddInParameter(cmd, Dbstring + "iInstallationPath", DbType.String, databaseMaxDB.InstallationPath);
                    Database.AddInParameter(cmd, Dbstring + "iMediumName", DbType.String, databaseMaxDB.MediumName);
                    Database.AddInParameter(cmd, Dbstring + "iLogfileName", DbType.String, databaseMaxDB.LogfileName);
                    Database.AddInParameter(cmd, Dbstring + "iLogPath", DbType.String, databaseMaxDB.LogPath);
                    Database.AddInParameter(cmd, Dbstring + "iDataPath", DbType.String, databaseMaxDB.DataPath);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseMaxDB = reader.Read()
                            ? CreateEntityBuilder<DatabaseMaxDB>().BuildEntity(reader, databaseMaxDB)
                            : null;
                    }

                    if (databaseMaxDB == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "databaseMaxDB already exists. Please specify another database_maxdb");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this database_maxdb");
                                }
                        }
                    }

                    return databaseMaxDB;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While updating DatabaseMaxDB Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     GetAll <see cref="DatabaseMaxDB" />from database_maxdb table.
        /// </summary>        
        /// <returns>DatabaseMaxDB List</DatabaseMaxDB></returns>
        /// <author>Uma Mehavarnan</author>
        IList<DatabaseMaxDB> IDatabaseMaxDBDataAccess.GetAll()
        {
            try
            {
                const string sp = "DatabaseMaxDB_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {                   
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseMaxDB>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseMaxDBDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DatabaseMaxDB" />from database_maxdb table by Id.
        /// </summary>
        /// <param name="id">Id of the DatabaseMaxDB</param>
        /// <returns>DatabaseMaxDB</returns>
        /// <author>Uma Mehavarnan</author>
        DatabaseMaxDB IDatabaseMaxDBDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "DatabaseMaxDB_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DatabaseMaxDB>()).BuildEntity(reader, new DatabaseMaxDB())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseMaxDBDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DatabaseMaxDB" />from database_maxdb table by DatabaseBaseId.
        /// </summary>
        /// <param name="databaseBaseId">DatabaseBaseId of the DatabaseMaxDB</param>
        /// <returns>DatabaseMaxDB</returns>
        /// <author>Uma Mehavarnan</author>
        DatabaseMaxDB IDatabaseMaxDBDataAccess.GetByDatabaseBaseId(int databaseBaseId)
        {
            try
            {
                const string sp = "DatabaseMaxDB_GetByDBBaseId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseBaseId", DbType.Int32, databaseBaseId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DatabaseMaxDB>()).BuildEntity(reader, new DatabaseMaxDB())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseMaxDBDataAccess.GetByDatabaseBaseId(" + databaseBaseId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="DatabaseMaxDB" />from Database_maxdb table by Id.
        /// </summary>
        /// <param name="id">Id of the DatabaseMaxDB</param>
        /// <returns>bool</returns>
        /// <author>Uma Mehavarnan</author>
        bool IDatabaseMaxDBDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DatabaseMaxDB_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeSuccessDelete:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeErrorChildExists:
                            {
                                throw new ArgumentException("Cannot delete a database_maxdb which has association.");
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this database_maxdb");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting DatabaseMaxDB Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods

    }
}