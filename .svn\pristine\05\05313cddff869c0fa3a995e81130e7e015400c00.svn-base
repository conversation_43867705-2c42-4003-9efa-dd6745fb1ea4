﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System.Web;

namespace CP.UI.Controls
{
    public partial class FastCopyOverview : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;
        HttpContext Current = HttpContext.Current;
        # region
        private DropDownList _ddlReplicationType = new DropDownList();


        #endregion

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            //  Binddata();

        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public string GetDirecName(object FastCopyId)
        {
            string fastcopyname = string.Empty;
            string[] fastcopyid = FastCopyId.ToString().Split(',');
            foreach (string id in fastcopyid)
            {
                if (Convert.ToInt32(id) > 0)
                {
                    IList<FastCopyJob> objFast = Facade.GetFastCopyJobByFastCopyId(Convert.ToInt32(id));

                    foreach (var objFastCopy in objFast)
                    {
                        fastcopyname = fastcopyname + objFastCopy.SourceDirectory + " , ";
                    }
                }
            }
            fastcopyname = fastcopyname.Remove(fastcopyname.Length - 2);
            return fastcopyname;
        }

        public string GetRemoteDirecName(object FastCopyId)
        {
            string fastcopyname = string.Empty;
            string[] fastcopyid = FastCopyId.ToString().Split(',');
            foreach (string id in fastcopyid)
            {
                if (Convert.ToInt32(id) > 0)
                {
                    IList<FastCopyJob> objFast = Facade.GetFastCopyJobByFastCopyId(Convert.ToInt32(id));

                    foreach (var objFastCopy in objFast)
                    {
                        fastcopyname = fastcopyname + objFastCopy.DestinationDirectory + " , ";
                    }
                }
            }
            fastcopyname = fastcopyname.Remove(fastcopyname.Length - 2);
            return fastcopyname;
        }
        /// <summary>
        /// if deleting or updating the List of Fast Copy Overview, the page will get postback and then Listview switch to the same page
        /// where it is deleted or updated. - Goraknath Khule.
        /// </summary>
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageFastCopyList"]) != -1) && Session["CurrentPageFastCopyList"] != null && (Convert.ToInt32(Session["CurrentPageFastCopyList"]) > 0))
            {
                if (Session["TotalPageRowsCountFastCopy"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageFastCopyList"]) == Convert.ToInt32(Session["TotalPageRowsCountFastCopy"]) - 1)
                    {
                        Session["CurrentPageFastCopyList"] = Convert.ToInt32(Session["CurrentPageFastCopyList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCountFastCopy"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageFastCopyList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageFastCopyList"] = -1;

            }

        }


        private void Binddata()
        {
            setListViewPage();
            if (ReplicationType.SelectedValue == "3" || ReplicationType.SelectedValue == "13" || ReplicationType.SelectedValue == "14" || ReplicationType.SelectedValue == "21" || ReplicationType.SelectedValue == "41" || ReplicationType.SelectedValue == "64" || ReplicationType.SelectedItem.Text == "DataSync" || ReplicationType.SelectedItem.Text == "DB2DataSync" || ReplicationType.SelectedItem.Text == "OracleWithDataSync" || ReplicationType.SelectedItem.Text == "DataBase - DB2Datasync" || ReplicationType.SelectedItem.Text == "Perpetuuiti - DataSync - App" || ReplicationType.SelectedItem.Text == "Perpetuuiti - DataSync - Oracle" || ReplicationType.SelectedItem.Text == "Perpetuuiti - DataSync - SyBase" || ReplicationType.SelectedItem.Text == "Perpetuuiti - DataSync - MaxDB" || ReplicationType.SelectedItem.Text == "Perpetuuiti - DataSync - MSSQL")
            {
                IList<FastCopy> fasCopyList = new List<FastCopy>();
                var iFastList = new List<FastCopy>();
                 
                //  var fasCopyList = Facade.GetFastCopiesByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, ReplicationType.SelectedItem.Text);
                //if (IsUserOperator)
                //{
                //    fasCopyList = Facade.GetFastCopiesByCompanyIdAndRole(IsUserOperator, LoggedInUserCompanyId, IsParentCompnay, ReplicationType.SelectedItem.Text);
                //}
                //else if (IsUserManager)
                //{
                //    fasCopyList = Facade.GetFastCopiesByCompanyIdAndRole(IsUserManager, LoggedInUserCompanyId, IsParentCompnay, ReplicationType.SelectedItem.Text);
                //}
                //else
                //{
                //    fasCopyList = Facade.GetFastCopiesByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, ReplicationType.SelectedItem.Text);
                //}

                var type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
                //fasCopyList = Facade.GetReplicationByUserIdCompanyIdRoleAndReplicationFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag, ReplicationType.SelectedItem.Text);
                fasCopyList = Facade.GetReplicationByUserIdCompanyIdRoleAndReplicationFlag(Convert.ToInt32(Current.Session["LoggedInUserId"]), Convert.ToInt32(Current.Session["_companyId"]), LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag, Convert.ToString(type).Trim());
                if (fasCopyList != null)
                {
                    var list = fasCopyList.GroupBy(c => c.ReplicationBase.Id);
                    if (list != null)
                    {
                        foreach (var fastitem in list)
                        {
                            foreach (var v in fastitem)
                            {
                                iFastList.Add(v);
                            }
                        }
                        if (iFastList != null)
                        {
                            if (iFastList.Count > 0)
                            {
                                lvFastcopy.DataSource = iFastList;
                                lvFastcopy.DataBind();
                            }
                            else
                            {
                                lvFastcopy.DataSource = iFastList;
                                lvFastcopy.DataBind();
                            }
                        }
                    }
                }
                else
                {
                    lvFastcopy.DataSource = null;
                    lvFastcopy.DataBind();
                }
            }

            //else
            //{
            //    var fasCopyList = Facade.GetFastCopiesByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, "IBMGlobal Mirror");
            //    lvFastcopy.DataSource = fasCopyList;
            //    lvFastcopy.DataBind();
            //}
        }

        //private void Binddata()
        //{
        //    var result = Facade.GetFastCopiesByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId,
        //                                                  IsParentCompnay);
        //    lvFastcopy.DataSource = result;
        //    lvFastcopy.DataBind();

        //}

        private IList<FastCopy> GetFastCopyListByReplicationType(string iType)
        {
            var replicationlist = Facade.GetFastCopiesByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, ReplicationType.SelectedItem.Text);

            if (replicationlist != null)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Type.ToString() == iType
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public IList<FastCopy> GetFastCopyList(string searchvalue)
        {
            var replicationlist = GetFastCopyListByReplicationType(Session["ReplicationType"].ToString());
            //var replicationlist = GetFastCopyListByReplicationType(_ddlReplicationType.SelectedValue);

            //var replicationlist = GetFastCopyListByReplicationType(_ddlReplicationType.SelectedValue);
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count > 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvFastcopy.Items.Clear();
                lvFastcopy.DataSource = GetFastCopyList(txtsearchvalue.Text);
                lvFastcopy.DataBind();
            }
        }

        protected string GetStringValue(object str)
        {
            string strvalue = string.Empty;

            switch (str.ToString())
            {
                case "U":
                    strvalue = "Upload";
                    break;

                case "D":
                    strvalue = "Download";
                    break;

                case "S":
                    strvalue = "SourceMaster";
                    break;

                case "T":
                    strvalue = "TargerMaster";
                    break;

                case "N":
                    strvalue = "None";
                    break;
            }

            return strvalue;
        }

        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByReplicationId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Replication " + name + " attaching with group " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }

        protected void LvfastcopyDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageFastCopyList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCountFastCopy"] = dataPager1.TotalRowCount;
                var lblId = lvFastcopy.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvFastcopy.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
                //var ReplicationDetail = Facade.GetReplicationBaseById(Convert.ToInt32(lblId.Text));
                //var SiteDetail = Facade.GetSiteById(ReplicationDetail.SiteId);
                var InfraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));
                if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("FastCopyOverview Delete", UserActionType.ReplicationList))
                {
                    //var applicationDetailByReplicationId = Facade.GetApplicationGroupsByReplicationId(Convert.ToInt32(lblId.Text));
                    //if (applicationDetailByReplicationId != null)
                    //{
                    //    ErrorSuccessNotifier.AddSuccessMessage("The DataSync Replication component is in use");
                    //}
                    //else
                    //{
                    if (InfraObjects != null && InfraObjects.Count > 0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The DataSync Overview Component is in use.");

                    }
                    else
                    {
                        Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "DataSync", UserActionType.DeleteReplicationComponent,
                                              "The DataSync Replication component '" + lblName.Text +
                                              "' was deleted from the replication component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "DataSync Replication Component" + " " + '"' + lblName.Text + '"',

                                TransactionType.Delete));
                    }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                var lblRepType = (lvFastcopy.Items[e.ItemIndex].FindControl("Rep_Type")) as Label;

                if (lblRepType.Text == "DataSync")
                {
                    lblRepType.Text = "3";
                }
                else if (lblRepType.Text == "OracleWithDataSync")
                {
                    lblRepType.Text = "14";
                }
                else if (lblRepType.Text == "SyBaseWithDataSync")
                {
                    lblRepType.Text = "41";
                }
                else if (lblRepType.Text == "MaxDBWithDataSync")
                {
                    lblRepType.Text = "64";
                }
                else
                {
                    lblRepType.Text = "21";
                }

                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, lblRepType.Text);

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, lblRepType.Text);
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void LvFastcopyItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["TotalPageRowsCountFastCopy"] = dataPager1.TotalRowCount;
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvFastcopy.Items[e.NewEditIndex].FindControl("Id")) as Label;

            var lblName = (lvFastcopy.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;

            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "FastCopy", UserActionType.UpdateReplicationComponent,
                                     "The FastCopy Replication component '" + lblName.Text +
                                     "' Opened as Editing Mode", LoggedInUserId);

            var lblRepType = (lvFastcopy.Items[e.NewEditIndex].FindControl("Rep_Type")) as Label;

            if (lblRepType.Text == "DataSync")
            {
                lblRepType.Text = "3";
            }
            else if (lblRepType.Text == "SqlServerDataSync")
            {
                lblRepType.Text = "13";
            }
            else if (lblRepType.Text == "OracleWithDataSync")
            {
                lblRepType.Text = "14";
            }
            else if (lblRepType.Text == "SyBaseWithDataSync")
            {
                lblRepType.Text = "41";
            }
            else if (lblRepType.Text == "MaxDBWithDataSync")
            {
                lblRepType.Text = "64";
            }
            else
            {
                lblRepType.Text = "21";
            }

            if (lbl1 != null && lblName != null && ValidateRequest("FastCopyOverview Edit", UserActionType.ReplicationList))
            {
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                //    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, lblRepType.Text);
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, lblRepType.Text);
                Helper.Url.Redirect(secureUrl);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void LvFastcopyPreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void lvFastcopy_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator || IsUserManager || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }
    }
}