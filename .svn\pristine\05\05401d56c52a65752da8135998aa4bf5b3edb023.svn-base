﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System.Web;

namespace CP.UI.Controls
{
    public partial class DatabasePostgreSqlList : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.DatabaseConfiguration;

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.DatabaseList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            BindData();
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        private void BindData()
        {
            lvNormaldatabase.DataSource = GetPostgreSqlList();
            lvNormaldatabase.DataBind();
            setListViewPage();
        }

        /// <summary>
        /// if deleting or updating the List of PosgresSql database page will get postback and then Listview is display the same page
        /// where it is deleted or updated. - Goraknath Khule.
        /// </summary>
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPagePostgresSQLList"]) != -1) && Session["CurrentPagePostgresSQLList"] != null)
            {
                if (Convert.ToInt32(Session["CurrentPagePostgresSQLList"]) == dataPager1.TotalRowCount)
                {
                    Session["CurrentPagePostgresSQLList"] = Convert.ToInt32(Session["CurrentPagePostgresSQLList"]) - dataPager1.MaximumRows;
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPagePostgresSQLList"]), dataPager1.MaximumRows, true);
                Session["CurrentPagePostgresSQLList"] = -1;
            }
        }

        private IList<DatabaseBase> GetPostgreSqlList()
        {
            //return Facade.GetDatabaseBasesByTypeRoleAndCompanyId(DatabaseType.PostgreSQL, IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
            //string role = Convert.ToString(LoggedInUserRole);
            //return Facade.GetDatabaseBaseBytypecompanyiduserid(DatabaseType.PostgreSQL, LoggedInUserCompanyId, LoggedInUserId, role);

            return Facade.GetDatabaseBaseBytypecompanyiduserid(DatabaseType.PostgreSQL, Convert.ToInt32(HttpContext.Current.Session["_companyId"]), Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]), Convert.ToString(HttpContext.Current.Session["LoggedInUserRole"]));

        }

        public IList<DatabaseBase> GetSqlList(string searchvalue)
        {
            var Databaselist = GetPostgreSqlList();
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && Databaselist != null && Databaselist.Count> 0)
            {
                var result = (from Database in Databaselist
                              where Database.Name.ToLower().Contains(searchvalue.ToLower()) || Database.PostgreSql.DatabaseName.ToLower().Contains(searchvalue.ToLower())
                              select Database).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvNormaldatabase.Items.Clear();
                lvNormaldatabase.DataSource = GetSqlList(txtsearchvalue.Text);
                lvNormaldatabase.DataBind();
            }
        }

        protected void LvdatabasePreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                BindData();
            }
        }

        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByDataBaseId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Database " + name + " attaching with group " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }

        protected void LvdatabaseItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPagePostgresSQLList"] = (dataPager1.StartRowIndex);
                var lblDatabaseId = (lvNormaldatabase.Items[e.ItemIndex].FindControl("ID")) as Label;
                var lblName = (lvNormaldatabase.Items[e.ItemIndex].FindControl("lblName")) as Label;

                if (lblDatabaseId != null && lblName != null && ValidateRequest("PostgresSQL Delete", UserActionType.DatabaseList))
                {
                    // var groupDetailsByDatabaseId = Facade.GetGroupsByDatabaseId(Convert.ToInt32(lblDatabaseId.Text));
                    var workflowDetailsByDatabaseId =
                        Facade.GetAllWorkflowActionsByDatabaseId(Convert.ToInt32(lblDatabaseId.Text));
                    if (workflowDetailsByDatabaseId != null)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The Sql database component is in use");
                    }
                    else
                    {
                        Facade.DeleteDatabaseBaseById(Convert.ToInt32(lblDatabaseId.Text));

                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Sql", UserActionType.DeleteDatabaseComponent,
                                              "The Sql database component '" + lblName.Text +
                                              "' was deleted from the database component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "PostgreSql database Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                    }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.DatabaseType, DatabaseType.PostgreSQL.ToString());

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseType, DatabaseType.PostgreSQL.ToString());
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void LvdatabaseItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPagePostgresSQLList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);
            var lblDatabaseId = (lvNormaldatabase.Items[e.NewEditIndex].FindControl("ID")) as Label;

            var lblName = (lvNormaldatabase.Items[e.NewEditIndex].FindControl("lblName")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "PostGreSql", UserActionType.UpdateDatabaseComponent,
                                             "The PostGreSql database component '" + lblName.Text +
                                             "' Opened as Editing Mode", LoggedInUserId);


            if (lblDatabaseId != null && ValidateRequest("PostgresSQL Edit", UserActionType.DatabaseList))
            {

                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.DatabaseId, lblDatabaseId.Text,
                //Constants.UrlConstants.Params.DatabaseType, DatabaseType.PostgreSQL.ToString());
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseId, lblDatabaseId.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseType, DatabaseType.PostgreSQL.ToString());

                Helper.Url.Redirect(secureUrl);

                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.DatabaseId, lblDatabaseId.Text,
                //Constants.UrlConstants.Params.DatabaseType, DatabaseType.PostgreSQL.ToString());
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void LvdatabaseItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ibtnEdit") as ImageButton;
            var delete = e.Item.FindControl("ibtnDelete") as ImageButton;

            if (IsUserOperator || IsUserExecutionUser)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }
    }
}