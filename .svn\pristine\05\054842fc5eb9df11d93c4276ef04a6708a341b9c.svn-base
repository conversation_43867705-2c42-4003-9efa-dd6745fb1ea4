﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.DataBaseSyBaseWithSrs
{

    internal sealed class DataBaseSybaseWithSrsDBDataAccess : BaseDataAccess, IDatabaseSyBaseWithSrsDataAccess
    {
        #region Constructors

        public DataBaseSybaseWithSrsDBDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<databaseSybase> CreateEntityBuilder<databaseSybase>()
        {
            return (new DataBaseSybaseWithSrsBuilder()) as IEntityBuilder<databaseSybase>;
        }

        #endregion Constructors

        #region Methods


        /// <summary>
        ///     Create <see cref="DatabaseSql" /> into database_sybase_srs Table
        /// </summary>
        /// <param name="databaseSql">databaseSybaseWithSrs</param>
        /// <returns>databaseSybaseWithSrs</returns>
        /// <author>Sumit Wakade</author>

        DatabaseSybaseWithSrs IDatabaseSyBaseWithSrsDataAccess.Add(DatabaseSybaseWithSrs databaseSybase)
        {
            try
            {
                const string sp = "DataBaseSyBaseWithSrs_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iBaseDatabaseId", DbType.Int32, databaseSybase.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseSID", DbType.String, databaseSybase.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, databaseSybase.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, databaseSybase.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, databaseSybase.Port);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseDataServerName", DbType.String, databaseSybase.SybaseDataServerName);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseBackupServer", DbType.String, databaseSybase.SybaseBackupServer);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseEnv_Path", DbType.String, databaseSybase.SybaseEnv_Path);
                  
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseSybase = reader.Read()
                            ? CreateEntityBuilder<DatabaseSybaseWithSrs>().BuildEntity(reader, databaseSybase)
                            : null;
                    }

                    if (databaseSybase == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "Database SybaseWithSRS already exists. Please specify another database_sybase_srs.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this database_sybase_srs.");
                                }
                        }
                    }

                    return databaseSybase;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting DataBaseSyBaseWithSrs Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Create <see cref="DatabaseSql" /> into database_sybase_srs Table
        /// </summary>
        /// <param name="databaseSql">databaseSybaseWithSrs</param>
        /// <returns>databaseSybaseWithSrs</returns>
        /// <author>Sumit Wakade</author>
        /// 
        DatabaseSybaseWithSrs IDatabaseSyBaseWithSrsDataAccess.UpdateByDatabaseBaseId(DatabaseSybaseWithSrs databaseSybase)
        {
            try
            {
                const string sp = "DatabaseSybaseSRS_UpdtByDbBsId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseId", DbType.Int32, databaseSybase.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseSID", DbType.String, databaseSybase.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, databaseSybase.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, databaseSybase.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, databaseSybase.Port);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseDataServerName", DbType.String, databaseSybase.SybaseDataServerName);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseBackupServer", DbType.String, databaseSybase.SybaseBackupServer);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseEnv_Path", DbType.String, databaseSybase.SybaseEnv_Path);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseSybase = reader.Read()
                            ? CreateEntityBuilder<DatabaseSybaseWithSrs>().BuildEntity(reader, databaseSybase)
                            : null;
                    }

                    if (databaseSybase == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "Database SybaseWithSRS already exists. Please specify another database_sybase_srs.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this database_sybase_srs.");
                                }
                        }
                    }

                    return databaseSybase;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating By DatabaseBaseId DataBaseSybaseWithSrs Entry " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Create <see cref="DatabaseSql" /> into database_sybase_srs Table
        /// </summary>
        /// <param name="databaseSql">databaseSybaseWithSrs</param>
        /// <returns>databaseSybaseWithSrs</returns>
        /// <author>Sumit Wakade</author>
        /// 

        DatabaseSybaseWithSrs IDatabaseSyBaseWithSrsDataAccess.GetByDatabaseBaseId(int databaseBaseId)
        {
            try
            {
                const string sp = "DatabaseSyBaseWithSrs_ByDbBsId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseBaseId", DbType.Int32, databaseBaseId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DatabaseSybaseWithSrs>()).BuildEntity(reader, new DatabaseSybaseWithSrs())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSyBaseWithSrsDataAccess.GetByDatabaseBaseId(" +
                    databaseBaseId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        DatabaseSybaseWithSrs IDatabaseSyBaseWithSrsDataAccess.Update(DatabaseSybaseWithSrs databaseSybase)
        {
            try
            {
                const string sp = "DatabaseSybaseWithSrs_UpdtById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iBaseDatabaseId", DbType.Int32, databaseSybase.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseSID", DbType.String, databaseSybase.DatabaseSID);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, databaseSybase.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, databaseSybase.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, databaseSybase.Port);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseDataServerName", DbType.String, databaseSybase.SybaseDataServerName);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseBackupServer", DbType.String, databaseSybase.SybaseBackupServer);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseEnv_Path", DbType.String, databaseSybase.SybaseEnv_Path);
                   
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseSybase = reader.Read()
                            ? CreateEntityBuilder<DatabaseSybaseWithSrs>().BuildEntity(reader, databaseSybase)
                            : null;
                    }

                    if (databaseSybase == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "Database SybaseWithSRS already exists. Please specify another database_sybase_srs.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this DataBaseSyBase.");
                                }
                        }
                    }

                    return databaseSybase;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating databaseSybase Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        DatabaseSybaseWithSrs IDatabaseSyBaseWithSrsDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "DatabaseSybaseWithSrs_GetByID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DatabaseSybaseWithSrs>()).BuildEntity(reader, new DatabaseSybaseWithSrs())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSyBaseWithSrsDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<DatabaseSybaseWithSrs> IDatabaseSyBaseWithSrsDataAccess.GetAll()
        {
            try
            {
                const string sp = "DatabaseSybaseWithSrs_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DatabaseSybaseWithSrs>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSyBaseWithSrsDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool IDatabaseSyBaseWithSrsDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DatabaseSql_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeSuccessDelete:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeErrorChildExists:
                            {
                                throw new ArgumentException("Cannot delete a bcms_database_sql which has association.");
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this bcms_database_sql.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting DatabaseSql Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        bool IDatabaseSyBaseWithSrsDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "DatabaseSybaseWithSrs_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this companyProfile.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSyBaseWithSrsDataAccess.IsExistByName (" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}
