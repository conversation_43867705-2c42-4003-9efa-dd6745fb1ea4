﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    public class EventManagementList : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public int EventId
        {
            get;
            set;
        }

        [DataMember]
        public string Description
        {
            get;
            set;
        }

        [DataMember]
        public DateTime Time
        {
            get;
            set;
        }

        [DataMember]
        public int Trigger
        {
            get;
            set;
        }

        #endregion Properties

        #region Constructor

        public EventManagementList()
            : base()
        {
        }

        #endregion Constructor
    }
}