﻿using System;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Helper;
using CP.Common.Shared;



namespace CP.UI.Code.Replication.Component
{
    public class MaxFullDBEmcSrdfComponent : IComponentInfo
    {
        private readonly IFacade _facade = new Facade();

        private ComponentInfo _componentInfo;

        public ComponentInfo CurrentComponent
        {
            get { return _componentInfo ?? (_componentInfo = new ComponentInfo()); }
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId, int mailBoxId, string mailboxname)
        {
            _componentInfo = null;

            return CurrentComponent;
        }
        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId)
        {
            _componentInfo = null;

            GetServerInformation(prServerId, true);

            GetServerInformation(drServerId, false);

            GetDatabaseInformation(prDatabaseId, true);

            GetDatabaseInformation(drDatabaseId, false);

           // GetComponamtDetailInformation(infraObjectId);

            return CurrentComponent;
        }

        //private void GetComponamtDetailInformation(int infraObjectId)
        //{
        //    try
        //    {
        //        var details = _facade.GetMaxEmcSrdfFullDBMonitorByInfraObjectId(infraObjectId);

        //        if (details != null)
        //        {
        //            CurrentComponent.PRDatabaseState = details.PRDatabaseState;
        //            CurrentComponent.DRDatabaseState = details.DRDatabaseState;
        //            CurrentComponent.PRDatabaseVersion = details.PRDatabaseVersion;
        //            CurrentComponent.DRDatabaseVersion = details.DRDatabaseVersion;
        //            CurrentComponent.PRInstanceType = details.PRInstanceType;
        //            CurrentComponent.DRInstanceType = details.DRInstanceType;

                 

        //        }
        //        else
        //        {
        //            CurrentComponent.PRDatabaseState = "N/A";
        //            CurrentComponent.DRDatabaseState = "N/A";
        //            CurrentComponent.PRDatabaseVersion = "N/A";
        //            CurrentComponent.DRDatabaseVersion = "N/A";
        //            CurrentComponent.PRInstanceType = "N/A";
        //            CurrentComponent.DRInstanceType = "N/A";

        //        }
        //    }
        //    catch (Exception)
        //    {
        //        //CurrentComponent.InstanceName = "Not Available";
        //        //CurrentComponent.DatabaseState = "Not Available";
        //        //CurrentComponent.RestrictAccessStatus = "Not Available";
        //    }
        //}

        private void GetDatabaseInformation(int databaseId, bool isPrimary)
        {
            try
            {
                var database = _facade.GetDatabaseMaxDBByDBBaseId(databaseId);

                if (database != null)
                {
                    BindDatabaseComponents(database, isPrimary);
                }
                else
                {
                    BindNullDatabaseComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullDatabaseComponents(isPrimary);
            }
        }

        private void BindNullDatabaseComponents(bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = "N/A";
                CurrentComponent.PRInstanceName = "N/A";
                CurrentComponent.PRInstallationPath = "N/A";
                CurrentComponent.PRDataPath = "N/A";
                CurrentComponent.PRLogPath = "N/A";
            }
            else
            {
                CurrentComponent.DRDatabaseName = "N/A";
                CurrentComponent.DRInstanceName = "N/A";
                CurrentComponent.DRInstallationPath = "N/A";
                CurrentComponent.DRDataPath = "N/A";
                CurrentComponent.DRLogPath = "N/A";
            }
        }

        private void BindDatabaseComponents(DatabaseMaxDB database, bool isPrimary)
        {
            
            //int currentInfraID =Convert.ToInt32(Session["CurrentInfraID"]);
            var currentInfraID = WebHelper.CurrentSession.Get(Constants.UrlConstants.Params.InfraObjectId);
            int InfraID = Convert.ToInt32(currentInfraID);
            var MaxEmcSrdfRepli = _facade.GetMaxEmcSrdfFullDBMonitorByInfraObjectId(InfraID);
            if (isPrimary)
            {
                ////CurrentComponent.PRDatabaseName = database.Name;
                // CurrentComponent.PRDatabaseMode = "Read write";
                 CurrentComponent.PRDatabaseName = database.DatabaseSID;
                 CurrentComponent.PRDatabaseState = MaxEmcSrdfRepli.PRDatabaseState;
                 CurrentComponent.PRDatabaseVersion = MaxEmcSrdfRepli.PRDatabaseVersion;
                 CurrentComponent.PRInstanceName = database.InstanceName;
                 CurrentComponent.PRInstallationPath = database.InstallationPath;

                 CurrentComponent.PRDataPath = database.DataPath;
                 CurrentComponent.PRLogPath = database.LogPath;
               
                 
            }
            else
            {
                //// CurrentComponent.DRDatabaseName = database.Name;
                //CurrentComponent.DRDatabaseMode = "StandBy";
                CurrentComponent.DRDatabaseName = database.DatabaseSID;
                CurrentComponent.DRDatabaseState = MaxEmcSrdfRepli.DRDatabaseState;
                CurrentComponent.DRDatabaseVersion = MaxEmcSrdfRepli.DRDatabaseVersion;
                CurrentComponent.DRInstanceName = database.InstanceName;
                CurrentComponent.DRInstallationPath = database.InstallationPath;
                CurrentComponent.DRDataPath = database.DataPath;
                CurrentComponent.DRLogPath = database.LogPath;
            }
        }

        private void BindNullServerComponents(bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = "N/A";
                CurrentComponent.PRServerIP = "N/A";
                CurrentComponent.PRServerOSType = "N/A";
                CurrentComponent.PRServerStatus = "Down";
            }
            else
            {
                CurrentComponent.DRServerName = "N/A";
                CurrentComponent.DRServerIP = "N/A";
                CurrentComponent.DRServerOSType = "N/A";
                CurrentComponent.DRServerStatus = "Down";
            }
        }

        private void BindServerComponents(Server server, bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = server.Name;
                CurrentComponent.PRServerIP = server.IPAddress;
                CurrentComponent.PRServerOSType = server.OSType;
                CurrentComponent.PRServerStatus = server.Status.ToString();
            }
            else
            {
                CurrentComponent.DRServerName = server.Name;
                CurrentComponent.DRServerIP = server.IPAddress;
                CurrentComponent.DRServerOSType = server.OSType;
                CurrentComponent.DRServerStatus = server.Status.ToString();
            }
        }

        private void GetServerInformation(int serverId, bool isPrimary)
        {
            try
            {
                var server = _facade.GetServerById(serverId);

                if (server != null)
                {
                    BindServerComponents(server, isPrimary);
                }
                else
                {
                    BindNullServerComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullServerComponents(isPrimary);
            }
        }
    }
}