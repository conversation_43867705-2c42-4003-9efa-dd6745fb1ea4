﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Data.Common;
using CP.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ActionAnalyticDataAccess : BaseDataAccess, IActionAnalyticDataAccess
    {
        #region Constructors

        public ActionAnalyticDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ActionAnalytic> CreateEntityBuilder<ActionAnalytic>()
        {
            return (new ActionAnalyticBuilder()) as IEntityBuilder<ActionAnalytic>;
        }

        #endregion

        #region Methods

        IList<ActionAnalytic> IActionAnalyticDataAccess.GetAllActionAnalytic()
        {
            try
            {
                const string sp = "ActionAnalytic_Getall";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ActionAnalytic>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IActionAnalyticDataAccess.GetAllActionAnalytic" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion
    }
}
