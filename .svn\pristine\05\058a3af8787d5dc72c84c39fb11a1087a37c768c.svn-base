﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "BusinessFunctionBIAActivity", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BusinessFunctionBIAActivity : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BusinessFunctionId { get; set; }

        [DataMember]
        public string ActivityDescription { get; set; }

        #endregion Properties
    }
}