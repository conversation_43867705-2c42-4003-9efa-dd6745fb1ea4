﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class MonitorQueueDataAccess : BaseDataAccess, IMonitorQueueDataAccess
    {
        #region Constructors

        public MonitorQueueDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<MonitorQueue> CreateEntityBuilder<MonitorQueue>()
        {
            return (new MonitorQueueBuilder()) as IEntityBuilder<MonitorQueue>;
        }

        #endregion end Constructors

        #region methods

        public MonitorQueue Add(Common.DatabaseEntity.MonitorQueue monitorQueue)
        {
            try
            {
                const string sp = "MonitorQueue_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iBusinessFunctionId", DbType.Int32, monitorQueue.BusinessFunctionId);
                    Database.AddInParameter(cmd, "iInfraObjectId", DbType.Int32, monitorQueue.InfraobjectId);
                    Database.AddInParameter(cmd, "iServerId", DbType.Int32, monitorQueue.ServerId);
                    Database.AddInParameter(cmd, "iQueueName", DbType.String, monitorQueue.QueueName);
                    Database.AddInParameter(cmd, "iThreshold", DbType.String, monitorQueue.Threshold);
                    Database.AddInParameter(cmd, "iCreatorId", DbType.Int32, monitorQueue.CreatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? CreateEntityBuilder<MonitorQueue>().BuildEntity(reader, monitorQueue) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting MonitorQueue_Create Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        public MonitorQueue Update(MonitorQueue monitorQueue)
        {
            try
            {
                const string sp = "MonitorQueue_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, monitorQueue.Id);
                    Database.AddInParameter(cmd, "iBusinessFunctionId", DbType.Int32, monitorQueue.BusinessFunctionId);
                    Database.AddInParameter(cmd, "iInfraObjectId", DbType.Int32, monitorQueue.InfraobjectId);
                    Database.AddInParameter(cmd, "iServerId", DbType.Int32, monitorQueue.ServerId);
                    Database.AddInParameter(cmd, "iQueueName", DbType.String, monitorQueue.QueueName);
                    Database.AddInParameter(cmd, "iThreshold", DbType.Int32, monitorQueue.Threshold);
                    Database.AddInParameter(cmd, "iUpdatorId", DbType.Int32, monitorQueue.UpdatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        monitorQueue = reader.Read() ? CreateEntityBuilder<MonitorQueue>().BuildEntity(reader, monitorQueue) : null;
                    }

                    if (monitorQueue == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Server already exists. Please specify another server.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this server.");
                                }
                        }
                    }

                    return monitorQueue;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating Server Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        public MonitorQueue GetById(int id)
        {
            try
            {
                const string sp = "MonitorQueue_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? CreateEntityBuilder<MonitorQueue>().BuildEntity(reader, new MonitorQueue()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IMonitorQueueDataAccess.GetById(" + id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public IList<MonitorQueue> GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "MonitorQueue_GetByInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<MonitorQueue>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IMonitorServicesDataAccess.GetByInfraObjectId(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public bool DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "MonitorQueue_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting IMonitorQueueDataAccess.DeleteById Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        #endregion Methods
    }
}
