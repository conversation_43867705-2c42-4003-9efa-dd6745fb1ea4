﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using System.Web.Services;
using CP.BusinessFacade;
using System.Data;
using CP.ExceptionHandler;
using System.ServiceProcess;
using System.Configuration;
using CP.UI.Controls;
using System.Text;
using System.ComponentModel;
using System.Reflection;
using CP.Common.BusinessEntity;

namespace CP.UI.Admin
{
    public partial class EventListing : BasePage
    {
        public IList<InfraObject> InfraList { get; set; }
        public override void PrepareView()
        {
            if (LoggedInUserRole == UserRole.Operator || LoggedInUserRole == UserRole.Manager || LoggedInUserRole == UserRole.Administrator)
            {
                int test = LoggedInUser.Id;

                if (test != null)
                {
                    PopulateGroupByRole(test);
                }
            }
            else
            {
                PopulateInfraObjectName();
            }
        }

        private void PopulateGroupByRole(int id)
        {
            InfraList = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(id, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

            if (InfraList != null)
            {
                ddlgroup.DataSource = InfraList;
                ddlgroup.DataTextField = "Name";
                ddlgroup.DataValueField = "Id";
                ddlgroup.DataBind();
                ddlgroup.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
            }
            else
            {
                ddlgroup.Items.Insert(0, new ListItem("No InfraObject Assigned", "0"));
            }
        }
        protected void BindList(int id)
        {
            var eventlist = Facade.GetAllEventListing(id);
            if (eventlist != null)
            {
                lsteventlist.DataSource = eventlist;
                lsteventlist.DataBind();
            }
            else
            {
                lblRecordMessage.Text = "No Records Found";
                lsteventlist.DataSource = null;
            }
        }

        private void PopulateInfraObjectName()
        {
            IList<InfraObject> PopulateInfraObjectNameList = Facade.GetAllInfraObject();
            if (PopulateInfraObjectNameList != null)
            {
                ddlgroup.DataSource = PopulateInfraObjectNameList;
                ddlgroup.DataTextField = "Name";
                ddlgroup.DataValueField = "Id";
                ddlgroup.DataBind();
                ddlgroup.Items.Insert(0, new ListItem("- Select Infraobject Name", "0"));
            }
            else
            {
                ddlgroup.Items.Insert(0, new ListItem("No Infraobject Assigned", "0"));
            }
        }

        public string Triggername(object id)
        {
            var workflow = Facade.GetWorkflowById(Convert.ToInt32(id));
            if (workflow != null)
            {
                return workflow.Name;
            }
            return "";
        }

        protected void ddlgroup_SelectedIndexChanged1(object sender, EventArgs e)
        {
            BindList(Convert.ToInt32(ddlgroup.SelectedValue));
        }
    }
}