﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="DiscoverySummary.aspx.cs" Inherits="CP.UI.SNMP.DiscoverySummary" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <h3>
            <span class="interval-icon"></span>
            Discovery Summary</h3>
      
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <asp:ListView ID="lvScan" runat="server">
                    <LayoutTemplate>
                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                            <thead>
                                <tr>
                                    <th style="width: 4%;">#
                                    </th>
                                   
                                    <th style="width: 44%;">Discovery Date
                                    </th>
                                    <th style="width: 44%;">IPRange
                                    </th>
                                    <th style="width: 8%;" class="text-center">Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                            </tbody>
                        </table>
                    </LayoutTemplate>
                    <EmptyDataTemplate>
                        <div class="">
                            <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                        </div>
                    </EmptyDataTemplate>
                    <ItemTemplate>
                        <tr>
                            <td style="width: 4%;">
                                <asp:Label ID="Label3" runat="server"></asp:Label><%#Container.DataItemIndex+1 %>
                                <asp:Label ID="lblScanID" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>
                            </td>
                            <td style="width: 44%;">
                                <asp:Label ID="lblStartTime" runat="server" Text='<%# Eval("StartTime") %>'></asp:Label>
                            </td>
                            <td style="width: 44%;">
                                <asp:Label ID="lblIPRange" runat="server" Text='<%# Eval("IPRange") %>'></asp:Label>
                            </td>
                            <td style="width: 8%;" class="text-center">
                                <asp:Button ID="btnView" CssClass="btn btn-primary" Width="56%" runat="server"
                                    Text="View" OnClick="btnView_OnClick"></asp:Button>
                            </td>
                        </tr>
                    </ItemTemplate>
                </asp:ListView>
                <div class="row">
                    <div class="col-md-6">
                        <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvScan">
                            <Fields>
                                <asp:TemplatePagerField>
                                    <PagerTemplate>
                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                        Results
                                                <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                Out Of
                                                <%# Container.TotalRowCount %>
                                        <br />
                                    </PagerTemplate>
                                </asp:TemplatePagerField>
                            </Fields>
                        </asp:DataPager>
                    </div>
                    <div class="col-md-6 text-right">
                        <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvScan" PageSize="5">
                            <Fields>
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button"
                                    ShowFirstPageButton="false" ShowLastPageButton="false" ShowNextPageButton="false"
                                    PreviousPageText="← Prev" />
                                <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10" NextPreviousButtonCssClass="btn-pagination"
                                    CurrentPageLabelCssClass="currentlabel" NumericButtonCssClass="btn-pagination" />
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button"
                                    ShowFirstPageButton="false" ShowLastPageButton="false" ShowPreviousPageButton="false"
                                    ShowNextPageButton="true" NextPageText="Next → " />
                            </Fields>
                        </asp:DataPager>
                    </div>
                </div>
                <hr />
                <asp:ListView ID="lvScanSummary" runat="server">
                    <LayoutTemplate>
                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                            <thead>
                                <tr>
                                    <th style="width: 4%;">#

                                    </th>

                                    <th>Host Found 
                                    </th>
                                    <th>Discover Date
                                    </th>
                                    <th>Discover Status
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                            </tbody>
                        </table>
                    </LayoutTemplate>
                    <EmptyDataTemplate>
                        <div class="">
                            <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                        </div>
                    </EmptyDataTemplate>
                    <ItemTemplate>
                        <tr>
                            <td>
                                <asp:Label ID="Label3" runat="server"></asp:Label><%#Container.DataItemIndex+1 %>
                            </td>
                            <td>
                                <asp:Label ID="Label2" runat="server" Text='<%# Eval("ReachableHost") %>'></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="Label4" runat="server" Text='<%# Eval("CreateDate") %>'></asp:Label>
                            </td>

                            <td>
                                <asp:Label ID="Label1" runat="server" Text='<%# Convert.ToInt16(Eval("Status")) == 1?"New":"" %>'></asp:Label>
                            </td>

                        </tr>
                    </ItemTemplate>
                </asp:ListView>
            </div>
        </div>
    </div>
</asp:Content>
