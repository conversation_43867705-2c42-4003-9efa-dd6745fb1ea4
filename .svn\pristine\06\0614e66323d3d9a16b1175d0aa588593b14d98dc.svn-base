﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;

namespace CP.Helper
{
    public static class CryptographyHelper
    {
        public static readonly byte[] Key =
        {
            4, 93, 171, 3, 85, 23, 41, 34, 216, 14, 78, 156, 78, 3, 103, 154, 9, 150,
            65, 54, 226, 95, 68, 79, 159, 36, 246, 57, 177, 107, 116, 8
        };

        public static string Md5Key = "20Pe,rvtu!itLtPa/d10?Mah$petnThaohes%h2ktilso3*4ftMarSe(rTe)cs@ctimizhnviceP";

        [DebuggerStepThrough]
        public static string Hash(string plain)
        {
            if (plain.IsNullOrEmpty())
            {
                return string.Empty;
            }

            using (KeyedHashAlgorithm csp = HMAC.Create())
            {
                csp.Key = Key;
                byte[] data = Encoding.Default.GetBytes(plain);
                byte[] hash = csp.ComputeHash(data);

                return Convert.ToBase64String(hash);
            }
        }

        [DebuggerStepThrough]
        public static string Encrypt(string plain)
        {
            if (plain.IsNullOrEmpty())
            {
                return string.Empty;
            }

            using (SymmetricAlgorithm crypto = CreateAlgorithm())
            {
                return Convert.ToBase64String(Read(crypto.CreateEncryptor(), Encoding.Default.GetBytes(plain)));
            }
        }

        [DebuggerStepThrough]
        public static string Decrypt(string cipher)
        {
            if (cipher.IsNullOrEmpty())
            {
                return string.Empty;
            }
            using (SymmetricAlgorithm crypto = CreateAlgorithm())
            {
                return Encoding.Default.GetString(Read(crypto.CreateDecryptor(), Convert.FromBase64String(cipher)));
            }
        }

        [DebuggerStepThrough]
        private static SymmetricAlgorithm CreateAlgorithm()
        {
            SymmetricAlgorithm crypto = new RijndaelManaged();

            crypto.Key = Key;
            crypto.IV = new byte[crypto.IV.Length];

            return crypto;
        }

        [DebuggerStepThrough]
        private static byte[] Read(ICryptoTransform transformer,
            byte[] data)
        {
            using (var memoryStream = new MemoryStream())
            {
                using (var cryptoStream = new CryptoStream(memoryStream, transformer, CryptoStreamMode.Write))
                {
                    cryptoStream.Write(data, 0, data.Length);
                    cryptoStream.FlushFinalBlock();

                    return memoryStream.ToArray();
                }
            }
        }
        [DebuggerStepThrough]
        public static string Md5Encrypt(string toEncrypt)
        {
            byte[] messageBytes = ASCIIEncoding.ASCII.GetBytes(toEncrypt);

            // byte[] passwordBytes = Key;
            byte[] passwordBytes = ASCIIEncoding.ASCII.GetBytes(Md5Key);
            passwordBytes = passwordBytes.Skip(68).ToArray();
            DESCryptoServiceProvider provider = new DESCryptoServiceProvider();
            ICryptoTransform transform = provider.CreateEncryptor(passwordBytes, passwordBytes);
            CryptoStreamMode mode = CryptoStreamMode.Write;

            MemoryStream memStream = new MemoryStream();
            CryptoStream cryptoStream = new CryptoStream(memStream, transform, mode);
            cryptoStream.Write(messageBytes, 0, messageBytes.Length);
            cryptoStream.FlushFinalBlock();

            byte[] encryptedMessageBytes = new byte[memStream.Length];
            memStream.Position = 0;
            memStream.Read(encryptedMessageBytes, 0, encryptedMessageBytes.Length);

            string encryptedMessage = Convert.ToBase64String(encryptedMessageBytes);

            return encryptedMessage;
        }

        [DebuggerStepThrough]
        public static string Md5Decrypt(string cipherString)
        {
            byte[] encryptedMessageBytes = Convert.FromBase64String(cipherString);

            //byte[] passwordBytes = Key;
            byte[] passwordBytes = ASCIIEncoding.ASCII.GetBytes(Md5Key);
            passwordBytes = passwordBytes.Skip(68).ToArray();

            DESCryptoServiceProvider provider = new DESCryptoServiceProvider();
            ICryptoTransform transform = provider.CreateDecryptor(passwordBytes, passwordBytes);
            CryptoStreamMode mode = CryptoStreamMode.Write;

            MemoryStream memStream = new MemoryStream();
            CryptoStream cryptoStream = new CryptoStream(memStream, transform, mode);
            cryptoStream.Write(encryptedMessageBytes, 0, encryptedMessageBytes.Length);
            cryptoStream.FlushFinalBlock();

            byte[] decryptedMessageBytes = new byte[memStream.Length];
            memStream.Position = 0;
            memStream.Read(decryptedMessageBytes, 0, decryptedMessageBytes.Length);

            string message = ASCIIEncoding.ASCII.GetString(decryptedMessageBytes);

            return message;
        }

/*
        [DebuggerStepThrough]
        public static string Md5Encrypt(string toEncrypt)
        {
            byte[] toEncryptArray = Encoding.UTF8.GetBytes(toEncrypt);

            var hashmd5 = new MD5CryptoServiceProvider();
            byte[] keyArray = hashmd5.ComputeHash(Encoding.UTF8.GetBytes(Md5Key));
            hashmd5.Clear();

            var tdes = new TripleDESCryptoServiceProvider
            {
                Key = keyArray,
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            ICryptoTransform cTransform = tdes.CreateEncryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            tdes.Clear();
            return Convert.ToBase64String(resultArray, 0, resultArray.Length);
        }

        [DebuggerStepThrough]
        public static string Md5Decrypt(string cipherString)
        {
            byte[] toEncryptArray = Convert.FromBase64String(cipherString);

            var hashmd5 = new MD5CryptoServiceProvider();
            byte[] keyArray = hashmd5.ComputeHash(Encoding.UTF8.GetBytes(Md5Key));
            hashmd5.Clear();

            var tdes = new TripleDESCryptoServiceProvider
            {
                Key = keyArray,
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            ICryptoTransform cTransform = tdes.CreateDecryptor();
            byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
            tdes.Clear();
            return Encoding.UTF8.GetString(resultArray);
        }*/
    }
}