﻿using System.Collections.Generic;

namespace CP.CacheController
{
    public partial class CacheManager
    {
        private DataCacheManager dataCacheManager = default(DataCacheManager);
        private PageCacheManager pageCacheManager = default(PageCacheManager);

        public CacheManager()
        {
        }

        public DataCacheManager DataCache
        {
            get
            {
                if (dataCacheManager == null)
                {
                    return new DataCacheManager();
                }
                return dataCacheManager;
            }
        }

        public PageCacheManager PageCache
        {
            get
            {
                if (pageCacheManager == null)
                {
                    return new PageCacheManager();
                }
                return pageCacheManager;
            }
        }

        public void Clear()
        {
            DataCache.Clear();
            PageCache.Clear();
        }

        public List<CacheEntry> GetCachedData()
        {
            return DataCache.GetAll();
        }

        public List<CacheEntry> GetCachedPages()
        {
            return PageCache.GetAll();
        }
    }
}