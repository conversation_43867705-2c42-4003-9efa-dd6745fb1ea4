﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IParallelProfileDataAccess
    {
        ParallelProfile Add(ParallelProfile profile);

        IList<ParallelProfile> GetAll();

        ParallelProfile GetById(int id);

        IList<ParallelProfile> GetprofileByCustomIdnAccess(int id);

        bool DeleteGetById(int id);

        IList<ParallelProfile> GetByName(string profileName);

        IList<ParallelProfile> GetprofileByCustomIdnAccessView(int id);

        ParallelProfile GetByDROperationId(int droperationid);

        IList<ParallelProfile> GetParallelProfileByInfraUserId(int infraId, int userid);

        IList<ParallelProfile> GetProfileByUserrole(string userRole, int userId);

        bool ParProfileChangePassword(int ProfileId, string Password);

        IList<ParallelProfile> GetAttachParallelProfileByUserId(int userid);

        //------------------------- Approval Changes ------------------

        bool UpdateIsRequireById(int id, string ApprovalRequire);

        IList<ParallelProfile> GetByApprove(string ApprovalRequire);

        //-----------------------------------------
        IList<ParallelProfile> GetAllParallelProfile_forfoureye();

        IList<ParallelProfile> GetwfprofileByName(string name);
       
    }
}