﻿using System;
using System.Web.UI.WebControls;
using CP.Common.Shared;
using CP.Helper;

namespace CP.UI
{
    public abstract class BaseEditorPage<T> : BasePage, IEditor<T>
    {
        #region Variables

        private int _currentEntityId = 0;
        private T _currentEntity = default(T);

        #endregion Veriables

        #region Properties

        /// <summary>
        /// Expose The Label To Show Total Result In Case Of ListView
        /// </summary>
        public virtual Label TotalResult
        {
            get
            {
                return null;
            }
        }

        /// <summary>
        /// Expose The Label To Show Message To User
        /// </summary>
        public abstract Label MessageViewer
        {
            get;
        }

        /// <summary>
        /// Expose The Initials Of Message To Create & Display Dynamic Message
        /// </summary>
        public abstract string MessageInitials
        {
            get;
        }

        /// <summary>
        /// Expose The Url To Redirect After Button Click Event Or ListView Command Event
        /// </summary>
        public abstract string ReturnUrl
        {
            get;
        }

        /// <summary>
        /// The Entity Of Type (T) To Be Added Or Modified
        /// </summary>
        public virtual T CurrentEntity
        {
            get
            {
                if (_currentEntity == null)
                {
                    if (CurrentEntityId > 0)
                    {
                        _currentEntity = Facade.GetEntityById<T>(CurrentEntityId);
                    }

                    if (_currentEntity == null)
                    {
                        _currentEntity = Activator.CreateInstance<T>();
                    }
                }
                return _currentEntity;
            }
            set
            {
                _currentEntity = value;
            }
        }

        /// <summary>
        /// The Id Of The Entity Of Type(T) To Be Modified Or Displayed
        /// </summary>
        public virtual int CurrentEntityId
        {
            get
            {
                if (_currentEntityId == 0)
                {
                    if (Helper.Url.SecureUrl[Constants.UrlConstants.Params.ID].IsNotNullOrEmpty())
                    {
                        _currentEntityId = Helper.Url.SecureUrl[Constants.UrlConstants.Params.ID].ToInteger();
                    }
                    else if (Helper.ViewState.Content.CurrentEntityId > 0)
                    {
                        _currentEntityId = Helper.ViewState.Content.CurrentEntityId;
                    }
                }
                Helper.ViewState.Content.CurrentEntityId = _currentEntityId;
                return _currentEntityId;
            }
            set
            {
                Helper.ViewState.Content.CurrentEntityId = value;
                _currentEntityId = value;
            }
        }

        #endregion Properties

        #region Methods

        /// <summary>
        /// Common Entity Build Operation. Call base.BuildEntity If Override At The End Of Overriden Function
        /// </summary>
        /// <param name="currentEntity"></param>
        /// <returns></returns>
        public virtual T BuildEntity(T currentEntity)
        {
            Type currentEntityType = currentEntity.GetType();
            currentEntityType.GetProperty("UpdatorId").SetValue(CurrentEntity, LoggedInUserId, null);
            return currentEntity;
        }

        /// <summary>
        /// Call base.PrepareEditView If Override At The End Of Overriden Function
        /// </summary>
        public virtual void PrepareEditView()
        {
            BindList();
        }

        /// <summary>
        /// If The Editor Is Based On Single  Entity Then Don't Call base.SaveEditor() If Override.
        /// If The Editor Is Based On Multiple Entities Then Call base.SaveEditor() At The Beginning Of The Overriden Function
        /// Or Just Override This Function To Use The Code Of The Inherited Class
        /// </summary>
        public virtual void SaveEditor()
        {
            CurrentEntity = BuildEntity(CurrentEntity);
            Type currentEntityType = CurrentEntity.GetType();
            if ((bool)currentEntityType.GetProperty("IsNew").GetValue(CurrentEntity, null))
            {
                currentEntityType.GetProperty("CreatorId").SetValue(CurrentEntity, LoggedInUserId, null);
                CurrentEntity = Facade.AddEntity<T>(CurrentEntity);
            }
            else
            {
                CurrentEntity = Facade.UpdateEntity<T>(CurrentEntity);
            }
        }

        /// <summary>
        /// Call base.ClearForm If Override At The End Of Overriden Function
        /// </summary>
        public virtual void ClearForm()
        {
            CurrentEntityId = 0;
            MessageViewer.Text = string.Empty;
        }

        public virtual void PrepareValidator()
        {
        }

        /// <summary>
        /// Don't call base.BindList if override
        /// </summary>
        public virtual void BindList()
        {
        }

        /// <summary>
        /// Don't call base.Delete if override
        /// </summary>
        /// <param name="entityId">entity to be deleted</param>
        public virtual void Delete(int entityId)
        {
            Facade.DeleteEntityById<T>(entityId);
            BindList();
        }

        /// <summary>
        /// This function should be override if any action need to be taken after completing listview command
        /// </summary>
        public virtual void FinalizeCommand()
        {
        }

        #endregion Methods

        #region Events

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            PrepareValidator();
        }

        //protected void btnDefault_Click(object sender, EventArgs e)
        //{
        //    string returnUrl = ReturnUrl;
        //    var submitButton = (Button)sender;
        //    string buttionText = " " + submitButton.Text.ToLower() + " ";
        //    TransactionType currentTransactionType = TransactionType.Undefined;
        //    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
        //    {
        //        currentTransactionType = TransactionType.Save;
        //    }
        //    else if (buttionText.Contains(" update "))
        //    {
        //        currentTransactionType = TransactionType.Update;
        //    }
        //    else if (buttionText.Contains(" add "))
        //    {
        //        currentTransactionType = TransactionType.Add;
        //    }
        //    else if (buttionText.Contains(" delete "))
        //    {
        //        currentTransactionType = TransactionType.Delete;
        //    }

        //    try
        //    {
        //        if (currentTransactionType != TransactionType.Undefined)
        //        {
        //            StartTransaction();
        //            SaveEditor();
        //            EndTransaction();
        //        }
        //        if (returnUrl.IsNullOrEmpty())
        //        {
        //            ClearForm();
        //            BindList();
        //            Utility.ShowMessage(MessageViewer, Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials, currentTransactionType), false);
        //        }
        //    }
        //    catch (CpException ex)
        //    {
        //        DisposeTransaction();
        //        returnUrl = string.Empty;
        //        ExceptionManager.Manage(ex, MessageViewer);
        //    }
        //    catch (Exception ex)
        //    {
        //        DisposeTransaction();
        //        returnUrl = string.Empty;
        //        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
        //        {
        //            ExceptionManager.Manage((CpException)ex.InnerException, MessageViewer);
        //        }
        //        else
        //        {
        //            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex, Page);
        //            ExceptionManager.Manage(customEx, MessageViewer);
        //        }
        //    }

        //    if (returnUrl.IsNotNullOrEmpty())
        //    {
        //        var secureUrl = new SecureUrl(returnUrl);
        //        if (secureUrl.QueryStrings.Count == 0)
        //        {
        //            secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.MESSAGE, Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials, currentTransactionType));
        //        }
        //        else
        //        {
        //            secureUrl.AddParamater(Constants.UrlConstants.Params.MESSAGE, Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials, currentTransactionType));
        //        }
        //        Helper.Url.Redirect(secureUrl);
        //    }
        //}

        //protected void lsvDefault_ItemCommand(object sender, ListViewCommandEventArgs e)
        //{
        //    SecureUrl secureUrl = default(SecureUrl);
        //    try
        //    {
        //        int entityId = e.CommandArgument.ToInteger(true);
        //        TransactionType tranType = TransactionType.Undefined;
        //        if (e.CommandName.IsEqual("DeleteItem"))
        //        {
        //            Delete(entityId);
        //            tranType = TransactionType.Delete;
        //        }
        //        else if (e.CommandName.IsEqual("EditItem"))
        //        {
        //            CurrentEntityId = entityId;
        //            if (ReturnUrl.IsNullOrEmpty())
        //            {
        //                PrepareEditView();
        //            }
        //            else
        //            {
        //                secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, Request.Url.ToString(), Constants.UrlConstants.Params.ID, entityId.ToString());
        //            }
        //            tranType = TransactionType.Undefined;
        //        }
        //        if (ReturnUrl.IsNullOrEmpty() && tranType != TransactionType.Undefined)
        //        {
        //            secureUrl = null;
        //            Utility.ShowMessage(MessageViewer, Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials, tranType), false);
        //            FinalizeCommand();
        //        }
        //    }
        //    catch (CpException ex)
        //    {
        //        ExceptionManager.Manage(ex, MessageViewer);
        //    }
        //    catch (Exception ex)
        //    {
        //        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
        //        {
        //            ExceptionManager.Manage((CpException)ex.InnerException, MessageViewer);
        //        }
        //        else
        //        {
        //            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred in ListViewEventHandler", ex, Page);
        //            ExceptionManager.Manage(customEx, MessageViewer);
        //        }
        //    }
        //    if (secureUrl != null)
        //    {
        //        Helper.Url.Redirect(secureUrl);
        //    }
        //}

        //protected void odsDefault_Selected(object sender, ObjectDataSourceStatusEventArgs e)
        //{
        //    if (TotalResult != null)
        //    {
        //        if (e.ReturnValue != null)
        //        {
        //            if (e.ReturnValue.GetType() == typeof(List<T>))
        //            {
        //                TotalResult.Text = "TOTAL ROW : " + ((List<T>)e.ReturnValue).Count.ToString();
        //            }
        //            else
        //            {
        //                TotalResult.Text = "TOTAL ROW : " + e.ReturnValue;
        //            }
        //        }
        //        else
        //        {
        //            TotalResult.Text = "TOTAL ROW : 0";
        //        }
        //    }
        //}

        #endregion Events
    }
}