﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="ApplicationManagement.aspx.cs" Inherits="CP.UI.Admin.ApplicationManagement"
    Title="Continuity Patrol :: Application Management" %>

<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit, Version=3.0.30930.28736, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style type="text/css">
        pre
        {
            padding: 20px;
            background-color: #ffffcc;
            border: solid 1px #fff;
        }
        .wrapper
        {
            background-color: #ffffff;
            width: 800px;
            border: solid 1px #eeeeee;
            padding: 20px;
            margin: 0 auto;
        }
        .example-container
        {
            background-color: #f4f4f4;
            border-bottom: solid 2px #777777;
            margin: 0 0 40px 0;
            padding: 20px;
        }
        .example-container p
        {
            font-weight: bold;
        }
        .example-container dt
        {
            font-weight: bold;
            height: 20px;
        }
        .example-container dd
        {
            margin: -20px 0 10px 100px;
            border-bottom: solid 1px #fff;
        }
        .example-container input
        {
            width: 150px;
        }
        .clear
        {
            clear: both;
        }
        #ui-datepicker-div
        {
            font-size: 80%;
            z-index: 1000;
        }
        .ui-timepicker-div .ui-widget-header
        {
            margin-bottom: 8px;
        }
        .ui-timepicker-div dl
        {
            text-align: left;
        }
        .ui-timepicker-div dl dt
        {
            height: 25px;
        }
        .ui-timepicker-div dl dd
        {
            margin: -25px 10px 10px 65px;
        }
        .ui-timepicker-div td
        {
            font-size: 90%;
        }
        .ui-tpicker-grid-label
        {
            background: none;
            border: none;
            margin: 0;
            padding: 0;
        }
    </style>
    <%--   <link href="../App_Themes/CPTheme/jquery-ui-1.8.16.custom.css" rel="stylesheet"
        type="text/css" />

    <script type="text/javascript" src="../Script/jquery-ui-1.8.16.custom.min.js"></script>

    <script type="text/javascript" src="../Script/jquery-ui-timepicker-addon.js"></script>--%>
    <link href="../App_Themes/CPTheme/jquery_ui_datepicker.min.css" rel="stylesheet"
        type="text/css" />

    <script src="../Script/jquery_UI_&_Timepicker.min.js" type="text/javascript"></script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="grid_5 grid-15">
        <div class="block-content no-padding">
            <asp:UpdatePanel ID="upApplicationMange" runat="server" UpdateMode="Conditional">
                <ContentTemplate>
                    <div class="block-controls">
                        <h1>
                            <asp:Label ID="lblApplicationName" runat="server" Text=""></asp:Label></h1>
                    </div>
                    <table class="table font grid-18 no-bottom-margin" id="tblMaintainence" width="100%">
                        <thead>
                            <tr>
                                <th class="grid-27">
                                </th>
                                <th class="grid-26">
                                    Production Server
                                </th>
                                <th>
                                    DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    Server Host Name
                                </td>
                                <td>
                                    <asp:Label ID="lblPRHost" runat="server" Text="Label"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRHost" runat="server" Text="Label"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    IP Address
                                </td>
                                <td>
                                    <asp:Label ID="lblPRIP" runat="server" Text="Label"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDRIP" runat="server" Text="Label"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    App Process
                                </td>
                                <td colspan="2">
                                    <asp:Label ID="lblAppProcess" runat="server" Text="Label"></asp:Label>
                                </td>
                            </tr>
                            <tr id="trapppath" runat="server" visible="false">
                                <td>
                                    App Path
                                </td>
                                <td colspan="2">
                                    <asp:Label ID="lblAppPath" runat="server" Text="Label"></asp:Label>
                                </td>
                            </tr>
                            <tr runat="server" id="trMaintainence" visible="false">
                                <td colspan="3" class="message success font_8">
                                    <asp:Label ID="lblReasonMaintenance" runat="server"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <asp:Panel ID="panelSwitchOver" runat="server" Style="display: none" Width="450px"
                        Height="520px">
                        <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                            <ContentTemplate>
                                <div id="modal">
                                    <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
                                        <ul class="action-tabs">
                                            <li title="Close">
                                                <asp:LinkButton ID="lnkbtnClose" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                            </li>
                                        </ul>
                                        <div class="block-content  margin-right margin-top1em">
                                            <div class="block-controls">
                                                <h1>
                                                    <asp:Label ID="lblCreate" runat="server" Text="Workflow"></asp:Label>
                                                </h1>
                                            </div>
                                            <iframe id="frame1" src="ApplicationSwitchOver.aspx" height="370px" width="380px" scrolling="no"
                                                runat="server" />
                                        </div>
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </asp:Panel>
                    <asp:Panel ID="panelSwitchBack" runat="server" Style="display: none" Width="450px"
                        Height="480px">
                        <asp:UpdatePanel ID="Updatepanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                            <ContentTemplate>
                                <div id="Div1">
                                    <div class="modal-window " style="top: 25px; width: 450px; margin-left: 30%">
                                        <ul class="action-tabs">
                                            <li title="Close">
                                                <asp:LinkButton ID="LinkButton1" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                            </li>
                                        </ul>
                                        <div class="block-content  margin-right margin-top1em">
                                            <div class="block-controls">
                                                <h1>
                                                    <asp:Label ID="Label3" runat="server" Text="Workflow"></asp:Label>
                                                </h1>
                                            </div>
                                            <iframe id="Iframe1" src="ApplicationSwitchBack.aspx" height="370px" width="380px" scrolling="no"
                                                runat="server" />
                                        </div>
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </asp:Panel>
                    <asp:Panel ID="panelFail" runat="server" Style="display: none" Width="450px" Height="480px">
                        <asp:UpdatePanel ID="UpdatepanelFailOver" runat="server" UpdateMode="Conditional"
                            ChildrenAsTriggers="true">
                            <ContentTemplate>
                                <div id="Div2">
                                    <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
                                        <ul class="action-tabs">
                                            <li title="Close">
                                                <asp:LinkButton ID="LinkButton2" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                            </li>
                                        </ul>
                                        <div class="block-content  margin-right margin-top1em">
                                            <div class="block-controls">
                                                <h1>
                                                    <asp:Label ID="Label4" runat="server" Text="Workflow"></asp:Label>
                                                </h1>
                                            </div>
                                            <iframe id="Iframe2" src="FailOver.aspx" height="370px" width="380px" scrolling="no"
                                                runat="server" />
                                        </div>
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </asp:Panel>
                    <asp:Panel ID="panelFailBack" runat="server" Style="display: none" Width="450px"
                        Height="480px">
                        <asp:UpdatePanel ID="Updatepanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                            <ContentTemplate>
                                <div id="Div3">
                                    <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
                                        <ul class="action-tabs">
                                            <li title="Close">
                                                <asp:LinkButton ID="LinkButton3" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                            </li>
                                        </ul>
                                        <div class="block-content  margin-right margin-top1em">
                                            <div class="block-controls">
                                                <h1>
                                                    <asp:Label ID="Label1" runat="server" Text="Workflow"></asp:Label>
                                                </h1>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </asp:Panel>
                    <asp:Panel ID="panelCustom" runat="server" Style="display: none" Width="450px" Height="480px">
                        <asp:UpdatePanel ID="Updatepanel3" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                            <ContentTemplate>
                                <div class="modal-window " style="top: 45px; width: 450px; margin-left: 30%">
                                    <ul class="action-tabs">
                                        <li title="Close">
                                            <asp:LinkButton ID="LinkButton4" runat="server" OnClick="CloseClick" CommandName="Close">
                                     <img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                        </li>
                                    </ul>
                                    <div class="block-content  margin-right margin-top1em">
                                        <div class="block-controls">
                                            <h1>
                                                <asp:Label ID="Label2" runat="server" Text="Workflow"></asp:Label>
                                            </h1>
                                        </div>
                                        <iframe id="frameCustom" src="ApplicationCustomWorkflow.aspx" height="400px" width="380px" scrolling="auto"
                                            runat="server" />
                                    </div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </asp:Panel>
                </ContentTemplate>
            </asp:UpdatePanel>
            <asp:Panel ID="panelMaintenance" runat="server" Style="display: none" Width="450px"
                Height="480px">
                <div id="Div4">
                    <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
                        <ul class="action-tabs">
                            <li title="Close">
                                <asp:LinkButton ID="LinkButton5" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                            </li>
                        </ul>
                        <div class="block-content  margin-right margin-top1em">
                            <div class="block-controls">
                                <h1>
                                    <asp:Label ID="lblmaintenance" runat="server"></asp:Label>
                                </h1>
                            </div>
                            <div class="float-left side1">
                                <label>
                                    Reason
                                </label>
                            </div>
                            <div>
                                <textarea id="txtReason" rows="3" cols="40"></textarea><span></span>
                            </div>
                            <hr />
                            <div class="float-left side1">
                                <label>
                                    Mode</label></div>
                            <div>
                                <select id="ddlMode">
                                    <option value="000">Select Mode</option>
                                    <option value="1">Auto</option>
                                    <option value="2">Manual</option>
                                </select><span></span>
                            </div>
                            <hr />
                            <div id="idUnlock">
                                <div class="float-left side1">
                                    <label>
                                        Unlock Time</label></div>
                                <div>
                                    <input type="text" name="txtTime" id="txtTime" readonly="readonly" class="date" /><span></span>
                                </div>
                                <hr />
                            </div>
                            <div class="block-footer align-right">
                                <input id="btnActionSetSave" type="button" value="OK" class="buttonblue" />
                                <input id="btnCancleMaintaince" type="button" value="Cancel" class="buttonblue" />
                            </div>
                        </div>
                    </div>
                </div>
            </asp:Panel>
            <table class="table font grid-18 no-bottom-margin" id="tblMaintainenceManage" width="100%">
                <tr>
                    <td>
                        Manage Group
                    </td>
                    <td colspan="2" class="align-right">
                        <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenance" runat="server" TargetControlID="btnMaint"
                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                            PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="modal"
                            CancelControlID="btnCancleMaintaince">
                        </TK1:ModalPopupExtender>
                        <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenanceAll" runat="server" TargetControlID="btnMaintenanceAll"
                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                            PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="modal"
                            CancelControlID="btnCancleMaintaince">
                        </TK1:ModalPopupExtender>
                        <TK1:ModalPopupExtender ID="ModalPopupExtenderLock" runat="server" TargetControlID="btnLock"
                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                            PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="modal"
                            CancelControlID="btnCancleMaintaince">
                        </TK1:ModalPopupExtender>
                        <asp:Button ID="btnActive" runat="server" CssClass="buttonblue" Text="Active" />
                        <asp:Button ID="btnActiveAll" runat="server" CssClass="buttonblue" Text="ActiveAll" />
                        <asp:Button ID="btnLock" runat="server" CssClass="buttonblue" Text="Lock" OnClick="BtnLockClick" />
                        <asp:Button ID="btnMaint" runat="server" CssClass="buttonblue" Text="Maintenance" />
                        <asp:Button ID="btnMaintenanceAll" runat="server" Text="MaintenanceAll" CssClass="buttonblue" />
                    </td>
                </tr>
                <tr>
                    <td>
                        DR Operations
                    </td>
                    <td colspan="2" class="align-right">
                        <TK1:ModalPopupExtender ID="ModalPopupExtenderSwitchOver" runat="server" TargetControlID="btnSwitchOver"
                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelSwitchOver"
                            PopupDragHandleControlID="panelSwitchOver" Drag="true" BackgroundCssClass="modal">
                        </TK1:ModalPopupExtender>
                        <TK1:ModalPopupExtender ID="ModalPopupExtenderSwitchBack" runat="server" TargetControlID="btnSwitchBack"
                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelSwitchBack"
                            PopupDragHandleControlID="panelSwitchBack" Drag="true" BackgroundCssClass="modal">
                        </TK1:ModalPopupExtender>
                        <TK1:ModalPopupExtender ID="ModalPopupExtenderFailOver" runat="server" TargetControlID="btnFailBack"
                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelFailBack"
                            PopupDragHandleControlID="panelFailBack" Drag="true" BackgroundCssClass="modal">
                        </TK1:ModalPopupExtender>
                        <TK1:ModalPopupExtender ID="ModalPopupExtenderFailBack" runat="server" TargetControlID="btnFailOver"
                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelFail"
                            PopupDragHandleControlID="panelFail" Drag="true" BackgroundCssClass="modal">
                        </TK1:ModalPopupExtender>
                        <TK1:ModalPopupExtender ID="ModalPopupExtenderCustom" runat="server" TargetControlID="btnCustom"
                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelCustom"
                            PopupDragHandleControlID="panelCustom" Drag="true" BackgroundCssClass="modal">
                        </TK1:ModalPopupExtender>
                        <asp:LinkButton ID="LinkBtnSwitchOver" runat="server" OnClick="LinkBtnSwitchOverClick"
                            Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                        <asp:LinkButton ID="LinkBtnSwitchBack" runat="server" OnClick="LinkBtnSwitchBackClick"
                            Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                        <asp:LinkButton ID="LinkBtnFailOver" runat="server" OnClick="LinkBtnFailOverClick"
                            Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                        <asp:LinkButton ID="LinkBtnFailBack" runat="server" OnClick="LinkBtnFailBackClick"
                            Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                        <asp:LinkButton ID="LinkBtnCustom" runat="server" OnClick="LinkBtnCustomClick" Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                        <asp:Button ID="btnSwitchOver" runat="server" CssClass="buttonblue" Text="SwitchOver" />
                        <asp:Button ID="btnSwitchBack" runat="server" CssClass="buttonblue" Text="SwitchBack"
                            Enabled="false" />
                        <asp:Button ID="btnFailOver" runat="server" CssClass="buttonblue" Text="Failover"
                            Enabled="false" />
                        <asp:Button ID="btnFailBack" runat="server" CssClass="buttonblue" Text="Failback"
                            Enabled="false" />
                        <asp:Button ID="btnCustom" runat="server" CssClass="buttonblue" Text="Custom" Enabled="false" />
                    </td>
                </tr>
            </table>
        </div>
    </div>

    <script src="../Script/ApplicationMaintenance.js" type="text/javascript"></script>

</asp:Content>
