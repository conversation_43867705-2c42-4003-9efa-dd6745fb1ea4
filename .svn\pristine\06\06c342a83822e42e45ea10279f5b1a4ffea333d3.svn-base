﻿using CP.BusinessFacade;
using CP.CacheController;
using CP.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
//using System.Web.UI.Page;

namespace CP.UI.Component
{
    public partial class VeritasClusterList : VeitasClusterBasePage
    {
        public static string CurrentURL = "~/Component/VeritasClusterConfiguration.aspx";

        Facade _facade = new Facade();

        private static CacheManager _cacheManager;

        private static string cacheKey = "Component.VeritasClusterIntoCache";

        private static CacheManager CurrentCacheManager
        {
            get { return _cacheManager ?? (_cacheManager = new CacheManager()); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.VeritasClusters.VeritasClusterList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            if (IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

            BindVeritasClusterList();
            Utility.SelectMenu(Master, "Module3");
        }

        public string GetIP(object type)
        {
            if (type != null)
            {
                Server sver = Facade.GetAllServers().Where(x => x.Id == Convert.ToInt32(type)).FirstOrDefault();
                if (sver != null)
                {
                    var ip = sver.IPAddress.ToString();
                    return CryptographyHelper.Md5Decrypt(ip.ToString());
                }
                else
                    return "";
            }
            else
                return "";
        }

        protected string CheckWithServer(object name, object id)
        {
            IList<Server> sver = Facade.GetAllServers();
            if (sver != null)
            {
                var VeritasClusterByid = from sv in sver where sv.Id == Convert.ToInt32(id) select sv;
                //SiteId == Convert.ToInt32(id) select sv;
                int cont = VeritasClusterByid.Count();

                if (cont >= 1)
                    return "Veritas Cluster " + name + " Are you sure want to delete ?";
                return "Are you sure want to delete " + name + " ?";
            }
            return string.Empty;
        }

        private void BindVeritasClusterList()
        {
            lvComponent.DataSource = null;
            lvComponent.Items.Clear();


            var VeritasClusters = _facade.GetAllVeritasCluster();

            if (VeritasClusters != null)
            {
                if (VeritasClusters.Count <= 0) return;
                lvComponent.DataSource = VeritasClusters;
                lvComponent.DataBind();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            lvComponent.DataSource = GetVeritasClusterListBySearch(txtsearchvalue.Text);
            lvComponent.DataBind();

        }

        private IList<VeritasCluster> GetVeritasClusterListBySearch(string value)
        {
            var veritasclusterlist = GetVeritasClusterList();
            value = value.Trim();
            if (!String.IsNullOrEmpty(value) || veritasclusterlist != null)
            {
                var result = (from veritasclusters in veritasclusterlist
                              where value != null && veritasclusters.ClusteProfileName.ToLower().Contains(value.ToLower())
                              select veritasclusters).ToList();

                return result;
            }
            return null;
        }

        private IList<VeritasCluster> GetVeritasClusterList()
        {
            return _facade.GetAllVeritasCluster();
        }

        protected void lvComponent_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageVeritasCluster"] = (dataPager1.StartRowIndex);
                var lbl = (lvComponent.Items[e.ItemIndex].FindControl("Id")) as Label;
                var lblName = (lvComponent.Items[e.ItemIndex].FindControl("Profile_Name")) as Label;
                if (lbl != null && ValidateRequest("VeritasCluster Delete", UserActionType.DeleteServerComponent))
                {
                    _facade.DeleteByIdVeritasCluster(Convert.ToInt32(lbl.Text));
                    ActivityLogger.AddLog(LoggedInUserName, "Veritas Cluster", UserActionType.DeleteServerComponent, "The Veritas Cluster '" + lblName.Text + "' was deleted", LoggedInUserId);

                    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(" Veritas Cluster" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                }
                BindVeritasClusterList();

                if (ReturnUrl.IsNotNullOrEmpty())
                {
                    Helper.Url.Redirect(new SecureUrl(ReturnUrl));
                }
            }

            catch (Exception ex)
            {

            }
        }

        protected void lvComponent_ItemEditing(object sender, ListViewEditEventArgs e)
        {

            var secureUrl = new SecureUrl(CurrentURL);
            Session["CurrentPageVeritasCluster"] = (dataPager1.StartRowIndex);
            var lblId = (lvComponent.Items[e.NewEditIndex].FindControl("Id")) as Label;


            if (lblId != null && ValidateRequest("VeritasCluster Editing", UserActionType.UpdateServerComponent))
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.VeritasClusterId,
                                                     lblId.Text);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

    }
}