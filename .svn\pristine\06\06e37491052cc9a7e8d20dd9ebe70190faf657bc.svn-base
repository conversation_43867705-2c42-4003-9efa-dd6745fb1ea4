﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;

namespace CP.UI
{
    public abstract class RoboCopyOptionsBasePage : BasePage
    {
        private int _robocopyoptionsId = 0;
        private RoboCopyOptions _robocopyoptions = null;

        #region Properties

        protected int CurrentRoboCopyOptionsId
        {
            get
            {
                if (_robocopyoptionsId == 0)
                {
                    if (Helper.Url.SecureUrl[Constants.UrlConstants.Params.RoboCopyOptionId].IsNotNullOrEmpty())
                    {
                        _robocopyoptionsId = Helper.Url.SecureUrl[Constants.UrlConstants.Params.RoboCopyOptionId].ToInteger();
                    }
                }
                return _robocopyoptionsId;
            }
            set
            {
                _robocopyoptionsId = value;
            }
        }

        protected RoboCopyOptions CurrentRoboCopyOptions
        {
            get
            {
                if (_robocopyoptions == null)
                {
                    if (CurrentRoboCopyOptionsId > 0)
                    {
                        _robocopyoptions = Facade.GetByRoboCopyOptionsId(CurrentRoboCopyOptionsId);
                    }
                    if (_robocopyoptions == null)
                    {
                        _robocopyoptions = new RoboCopyOptions();
                    }
                }
                return _robocopyoptions;
            }
            set
            {
                _robocopyoptions = value;
            }
        }

        #endregion Properties
    }
}