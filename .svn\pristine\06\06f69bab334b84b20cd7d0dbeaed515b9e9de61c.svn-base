﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using Telerik.Web.UI;
using Telerik.Web.UI.HtmlChart;
using System.Data;
using System.Drawing;

namespace CP.UI.Controls
{
    public partial class SeriveRecoveryEffectiveness : BaseControl
    {
        string[] colors = { "#fa590b", "#ffb400", "#ce315e", "#00abdf", "#78b732", "#9949a0", "#01b7ab","#a20025","#825a2c","#5b1414","#6600ff","#0033cc","#ac0173",
                              "#531cc1","#ff0000","#ff0066","#00698c","#333333","#006d03","#0066ff","#803f3f" };
        string red = "#ed7d5c";
        string blue = "#77b1d6";
        string green = "#589e3f";
        string yellow = "#fccf89";
        string white = "#FFFFFF";
        public override void PrepareView()
        {
            BindChart();

        }
        private void BindChart()
        {
            IList<WrokFlowEfficiency> WrokFlowEfficiency = Facade.GetWrokFlowEfficiency();
            for (int i = DateTime.Now.Month - 5; i <= DateTime.Now.Month; i++)
            {
                AxisItem _item = new AxisItem();
                _item.LabelText = new DateTime(2016, i, 1).ToString("MMM");
                LineChartEffectiveness.PlotArea.XAxis.Items.Add(_item);
            }
            lblCurrentMth.Text = DateTime.Now.ToString("MMMM") + "-" + DateTime.Now.Year;
            var currentmnth = from _wf in WrokFlowEfficiency where _wf.Month.Equals(DateTime.Now.ToString("MMM").ToUpper()) select _wf.Effectiveness;
            if (currentmnth != null && currentmnth.Count() > 0)
            {
                lblcurrent.Text = currentmnth.FirstOrDefault()+"%";

            }
            else
                lblcurrent.Text = "0"+"%";
            if (WrokFlowEfficiency != null && WrokFlowEfficiency.Count > 0)
            {

                LineSeries _donutSeries = new LineSeries();
                _donutSeries.LabelsAppearance.Visible = true;
                _donutSeries.LabelsAppearance.Color = Color.White;
                _donutSeries.LabelsAppearance.TextStyle.FontSize = 0;
                _donutSeries.TooltipsAppearance.Visible = true;
                double Total = 0;
                for (int i = DateTime.Now.Month - 5; i <= DateTime.Now.Month; i++)
                {
                    var _WrokFlowEfficiency = (from _wf in WrokFlowEfficiency where Convert.ToInt32(_wf.Month) == i select _wf.Effectiveness).SingleOrDefault();
                    SeriesItem _item1 = new SeriesItem();
                    _item1.YValue = Convert.ToDecimal(_WrokFlowEfficiency);
                    _item1.Name = "Efficiency";
                    _item1.BackgroundColor = System.Drawing.ColorTranslator.FromHtml(red);
                    _donutSeries.Items.Add(_item1);
                    if (!string.IsNullOrEmpty(_WrokFlowEfficiency))
                        lblcurrent.Text = _WrokFlowEfficiency + "%";
                    Total = Total + Convert.ToDouble(_WrokFlowEfficiency);
                }
                LineChartEffectiveness.PlotArea.Series.Add(_donutSeries);
                lblOverallAvg.Text = Convert.ToString(Math.Round(Total / 7, 2)) + "%";
              
            }
        }
    }
}