﻿using System;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;

namespace CP.UI.Code.Replication.Component
{
    public class MsSqlEmcSrdfComponent : IComponentInfo
    {
        private readonly IFacade _facade = new Facade();

        private ComponentInfo _componentInfo;

        public ComponentInfo CurrentComponent
        {
            get { return _componentInfo ?? (_componentInfo = new ComponentInfo()); }
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId)
        {
            _componentInfo = null;

            GetServerInformation(prServerId, true);

            GetServerInformation(drServerId, false);

            GetDatabaseInformation(prDatabaseId, true);

            GetDatabaseInformation(drDatabaseId, false);

            GetComponamtDetailInformation(infraObjectId);

            return CurrentComponent;
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId,int mailBoxId, string mailboxname)
        {
            _componentInfo = null;

            //GetServerInformation(prServerId, true);

            //GetServerInformation(drServerId, false);

            //GetDatabaseInformation(prDatabaseId, true);

            //GetDatabaseInformation(drDatabaseId, false);

            //GetDataLagInformation(groupId);

            return CurrentComponent;
        }

        private void GetComponamtDetailInformation(int infraObjectId)
        {
            try
            {
                var details = _facade.GetMsSqlEmcSrdfFullDBMonitorByInfraObjectId(infraObjectId);



                if (details != null)
                {
                    CurrentComponent.InstanceName = details.InstanceName;
                    CurrentComponent.DatabaseState = details.DatabaseState;
                    CurrentComponent.RestrictAccessStatus = details.RestrictAccessStatus;
                   
                }
                else
                {
                
                    CurrentComponent.InstanceName = "Not Available";
                    CurrentComponent.DatabaseState = "Not Available";
                    CurrentComponent.RestrictAccessStatus = "Not Available";                  
                }
            }
            catch (Exception)
            {
                CurrentComponent.InstanceName = "Not Available";
                CurrentComponent.DatabaseState = "Not Available";
                CurrentComponent.RestrictAccessStatus = "Not Available";       
            }
        }

        private void GetDatabaseInformation(int databaseId, bool isPrimary)
        {
            try
            {
                var database = _facade.GetDatabaseSqlByDatabaseBaseId(databaseId);

                if (database != null)
                {
                    BindDatabaseComponents(database, isPrimary);
                }
                else
                {
                    BindNullDatabaseComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullDatabaseComponents(isPrimary);
            }
        }

        private void BindNullDatabaseComponents(bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = "N/A";
                CurrentComponent.PRDatabaseMode = "Down";
            }
            else
            {
                CurrentComponent.DRDatabaseName = "N/A";
                CurrentComponent.DRDatabaseMode = "Down";
            }
        }

        private void BindDatabaseComponents(DatabaseSql database, bool isPrimary)
        {
            if (isPrimary)
            {
                // CurrentComponent.PRDatabaseName = database.Name;
                CurrentComponent.PRDatabaseName = database.DatabaseSID;
                CurrentComponent.PRDatabaseMode = "Read write";
            }
            else
            {
                // CurrentComponent.DRDatabaseName = database.Name;
                CurrentComponent.DRDatabaseName = database.DatabaseSID;
                CurrentComponent.DRDatabaseMode = "StandBy";
            }
        }

        private void BindNullServerComponents(bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = "N/A";
                CurrentComponent.PRServerIP = "N/A";
                CurrentComponent.PRServerOSType = "N/A";
                CurrentComponent.PRServerStatus = "Down";
            }
            else
            {
                CurrentComponent.DRServerName = "N/A";
                CurrentComponent.DRServerIP = "N/A";
                CurrentComponent.DRServerOSType = "N/A";
                CurrentComponent.DRServerStatus = "Down";
            }
        }

        private void BindServerComponents(Server server, bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = server.Name;
                CurrentComponent.PRServerIP = server.IPAddress;
                CurrentComponent.PRServerOSType = server.OSType;
                CurrentComponent.PRServerStatus = server.Status.ToString();
            }
            else
            {
                CurrentComponent.DRServerName = server.Name;
                CurrentComponent.DRServerIP = server.IPAddress;
                CurrentComponent.DRServerOSType = server.OSType;
                CurrentComponent.DRServerStatus = server.Status.ToString();
            }
        }

        private void GetServerInformation(int serverId, bool isPrimary)
        {
            try
            {
                var server = _facade.GetServerById(serverId);

                if (server != null)
                {
                    BindServerComponents(server, isPrimary);
                }
                else
                {
                    BindNullServerComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullServerComponents(isPrimary);
            }
        }
    }
}