﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DB2DatabaseConfig.ascx.cs"
    Inherits="CP.UI.Controls.DB2DatabaseConfig" %>
<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">DB2</h4>
        </div>
        <div class="widget-body">
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Database SID<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtDatabaseSid" CssClass="form-control" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator4" ControlToValidate="txtDatabaseSid" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Database SID"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="rfeOracleSID" runat="server" CssClass="error" ControlToValidate="txtDatabaseSid"
                        ErrorMessage="Enter Valid Database SID" ValidationGroup="dbConfig" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                        Display="Dynamic"></asp:RegularExpressionValidator>
                    <asp:Label ID="lblErr" runat="server" ForeColor="Red"
                        Text="Invalid License Key" Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Username<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtUserName" CssClass="form-control" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" ControlToValidate="txtUserName" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter UserName"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="rfeUser" runat="server" CssClass="error" ControlToValidate="txtUserName"
                        ErrorMessage="Enter Valid User Name" ValidationGroup="dbConfig" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                        Display="Dynamic"></asp:RegularExpressionValidator>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Password<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtPassword" CssClass="form-control" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" ControlToValidate="txtPassword" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Password"></asp:RequiredFieldValidator>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Port<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtPort" runat="server" CssClass="form-control" MaxLength="6"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator3" ControlToValidate="txtPort" CssClass="error"
                        Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Port"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="revPhone1" runat="server" ControlToValidate="txtPort" CssClass="error"
                        ValidationGroup="dbConfig" ErrorMessage="Valid Numbers only" ValidationExpression="[0-9]{2,6}"></asp:RegularExpressionValidator>
                </div>
            </div>
        </div>
    </div>
</div>
