﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    public class MongoDBDMonitorStatus : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string MongodStatusPR { get; set; }

        [DataMember]
        public string MongodStatusDR { get; set; }

        [DataMember]
        public string HostNamePR { get; set; }

        [DataMember]
        public string HostNameDR { get; set; }

        [DataMember]
        public string DBNamesPR { get; set; }

        [DataMember]
        public string DBNamesDR { get; set; }

        //[DataMember]
        //public string SizesOnDiskPR { get; set; }

        //[DataMember]
        //public string SizesOnDiskDR { get; set; }

        //[DataMember]
        //public string CollectionsPR { get; set; }

        //[DataMember]
        //public string CollectionsDR { get; set; }

        //[DataMember]
        //public string DocumentsPR { get; set; }

        //[DataMember]
        //public string DocumentsDR { get; set; }

        //[DataMember]
        //public string IndexesPR { get; set; }

        //[DataMember]
        //public string IndexesDR { get; set; }
        [DataMember]
        public string PRhealth { get; set; }

        [DataMember]
        public string DRhealth { get; set; }


        [DataMember]
        public string PRlastHeartbeatMessage { get; set; }

        [DataMember]
        public string DRlastHeartbeatMessage { get; set; }

        [DataMember]
        public string PRreplicaSetName { get; set; }

        [DataMember]
        public string DRreplicaSetName { get; set; }

        [DataMember]
        public string PRmemberID { get; set; }

        [DataMember]
        public string DRmemberID { get; set; }

        [DataMember]
        public string PRCurrentPriority { get; set; }

        [DataMember]
        public string DRCurrentPriority { get; set; }

      [DataMember]
        public string PRDatalag { get; set; }

        [DataMember]
        public string DRDatalag { get; set; }

        [DataMember]
        public string VersionPR { get; set; }

        [DataMember]
        public string VersionDR { get; set; }
  
        [DataMember]
        public string PRStateDescription { get; set; }

        [DataMember]
        public string DRStateDescription { get; set; }

       


     

     

       
   
      
        #endregion Properties
    }
}
