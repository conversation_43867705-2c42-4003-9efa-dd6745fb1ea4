function calculateHeightForDeleteCondition(n){$("#"+n).parent().children(".line").remove();var i=$("#"+n).parent(),r=$("#"+n).attr("totalheight"),u=$("#"+n).attr("actiondifference"),t;t=parseInt(r)-56;drwaConditionLineInUpAndDownDirection(u,i,t,n)}function checkDivcondiDivAfterActionSelect(){var c=$("#ddlSelectedWFProperty option:selected").attr("actionid"),l,n,t,f,r,s,u,h;for(allActionNamesForAddCondition="",getActionIdWhenConditionAdd(),l=allActionNamesForAddCondition.split(","),n=allActionNamesForAddCondition.split(","),t=0;t<n.length;t++){var e=0,o=0,i=0;if(n[t].contains("divCondi")){for(f=n[t].split("^"),e=t,r=0;r<n.length;r++)s=n[r].split("^"),f[1]==s[0]&&(o=r);for(u=0;u<n.length;u++)h=n[u].split("^"),c==h[0]&&(i=u);e<i&&i<o?calculateHeight(f[0]):i<e&&o<=i&&calculateHeight(f[0])}}}function getActionNameWhenConditionAdd(){$(".one,.diamond").each(function(){actionDetail=$(this).text();actionId=$(this).attr("id");allActionNamesForAddCondition=allActionNamesForAddCondition+actionDetail+","})}function getActionIdWhenConditionAdd(){allActionNamesForAddCondition="";$(".one,.diamond").each(function(){var n=$(this).attr("id"),t=$(this).attr("targetaction");allActionNamesForAddCondition=allActionNamesForAddCondition+n+"^"+t+","})}function getActionIdAndName(){allActionNamesForAddCondition="";$(".one,.diamond").each(function(){var n=$(this).attr("id"),t=$(this).text(),i=$(this).attr("targetaction");allActionNamesForAddCondition=allActionNamesForAddCondition+n+"^"+t+","})}function calculateHeight(n){var r=$("#"+n).parent(),n=n,t,u,i;$(r).children(".line").remove();t=$("#"+n).attr("totalheight");u=$("#"+n).attr("actiondifference");loopCnt==0?(i=parseInt(t)+56,loopCnt++):i=parseInt(t)+56;drwaConditionLineInUpAndDownDirection(u,r,i,n)}function GetActionValue(){var t=1,n,i;$("#ddlExist1 > option").remove();$(".one,.diamond").each(function(){if(n=$(this).text(),actionId=$(this).attr("id"),actionId.contains("divCondi")?$("#ddlExist1").append("<option value='"+t+"' actionId='"+actionId+"' class='hide'>"+n+"<\/option>"):$("#ddlExist1").append("<option value='"+t+"' actionId='"+actionId+"'>"+n+"<\/option>"),allActionNames=allActionNames+n+",",conditionClick==n&&(conditionOptionValue=t),parseInt(conditionOptionValue)+1==t){var i=n.split(".");$("#txtSuccessAction").attr("value",i[1])}t++});i=$("#"+conditionClickId).attr("targetaction");$("[id$=ddlExist1] option[actionid="+$("#"+conditionClickId).attr("targetaction")+"]").attr("selected","selected")}function GetActionValueWhenWeClickOnConnectButton(){allActionNames="";$(".one,.diamond").each(function(){actionDetail=$(this).text();allActionNames=allActionNames+actionDetail+","})}function CheckWorkFlowIsAttached(n){formClose=n;workFlowId=$("#ddlExist option:selected").val();var t=$("#ddlExist option:selected").text();$.ajax({type:"POST",url:"WorkflowConfiguration.aspx/CheckWorkFlowIsAttached",data:"{'args':'"+workFlowId+"'}",contentType:"application/json; charset=utf-8",dataType:"json",success:function(i){PopulateAttachedWorkflow(i.d,n,t)},error:function(n){alert(n.d)}})}function PopulateAttachedWorkflow(n,t,i){var r=n;r=="Success"?OpenAlertModelAlert("<b class='text-success'>"+i+"<\/b> workflow is not deleting? Because it is already attached "):deleteWorkflow(t,i)}function deleteWorkflow(n,t){$.ajax({type:"POST",url:"WorkflowConfiguration.aspx/DeleteExistingWorkFLow",data:"{'args':'"+workFlowId+"','workflowName':'"+t+"'}",contentType:"application/json; charset=utf-8",dataType:"json",success:function(t){PopulateDeleteWorkflow(t.d,n)},error:function(n){alert(n.d)}})}function PopulateDeleteWorkflow(n,t){OpenAlertModelAlert("Workflow Deleted successfully !!!");CloseModel(formClose);CloseModel(t);newWorkFlowOperation()}function GetDdlExistValue(){$.ajax({type:"POST",url:"WorkflowConfiguration.aspx/GetExistingWorkFLow",data:"{'args':'GetWorkFlow'}",contentType:"application/json; charset=utf-8",dataType:"json",success:function(n){PopulateDdlExist(n.d)},error:function(n){alert(n.d)}})}function PopulateDdlExist(n){var e=n,f=e.split(","),i="-Select WorkFlow-",r="000",u="",t;for(AppendOption("ddlExist",r,i),t=0;t<f.length;t++)u=f[t].split(":"),i=u[0],r=u[1],$("#ddlExist").append("<option value='"+r+"'>"+i+"<\/option>")}function closeModelPopep(n){if($("#ddlExist").trigger("change"),$("#ddlExist .error").is(":visible"))return!1;removeItem();var i=document.getElementById("ddlExist"),t=i.options[i.selectedIndex].value;GlobalWorkFlowID=t;$("#workflowHiddenId").html(t);$.ajax({type:"POST",url:"WorkflowConfiguration.aspx/OpenWorkFlow",data:"{'parameter':'"+t+"'}",contentType:"application/json; charset=utf-8",dataType:"json",sync:!0,success:function(n){CreateWorkFlowDiagrame(n.d);WorkFlowUnChanged()}});CloseModel(n)}function ModalPopupsPrompt(){OpenAlertModelLoad("<div class='col-md-4'> WorkFlow Name<\/div><div class='col-md-8'><input id='idPrompt1' class='col-md-9 form-control' type=text  style='width:65%;'  /><span id='idPromptSpan'><\/span><\/div>",ModalPopupsPromptOk);$("button:last)").live("click",function(){globalWorkFlowOperation="New"})}function WorkflowDuplicateMessage(n){$("button:first").attr("disabled",!1);n.d=="duplicate"?($("#idPromptSpan").html(" Workflow Name is already Present !!!"),$("#idPromptSpan").attr("class","error"),$("#idPrompt1").focus(),$("button:first").attr("disabled","true")):($("#idPromptSpan").html(""),$("button:first").attr("disabled",!1),globalWorkFlowOperation="New")}function ModalPopupsPromptOk(n){if(document.getElementById("idPrompt1").value==""){document.getElementById("idPrompt1").focus();return}var t=document.getElementById("idPrompt1").value;n.closeModal();SaveWorkflow(t)}function ModalPopupsPromptCancel(){ModalPopups.Cancel("idPrompt1")}function SaveWorkflow(n){var u;$("#idPrompt1").trigger("blur");WorkFlowUnChanged();var t="",r="",i="";n=="Generic"?n=WorkFlowName:WorkFlowName=n;$(".one,.diamond").each(function(){r=$(this).parents().get(0);i=$(r).attr("Class");t=$(this).hasClass("diamond")?t+i+","+jQuery.trim($(this).text())+","+$(this).attr("id")+"^"+$(this).attr("totalHeight")+"^"+$(this).attr("actionDifference")+"^"+$(this).attr("targetAction")+":":t+i+","+jQuery.trim($(this).text())+","+$(this).attr("id")+"^"+$(this).attr("totalHeight")+"^"+$(this).attr("actionDifference")+"^"+$(this).attr("targetaction")+":"});t=t.substring(0,t.length-1);u=$("#workflowHiddenId").html();$.ajax({type:"POST",url:"WorkflowConfiguration.aspx/SaveWorkFlow",data:"{'para':'"+t+"','name':'"+n+"','hiddenId':'"+u+"'}",contentType:"application/json; charset=utf-8",dataType:"json",sync:!0,success:function(t){btnNewWorkFLowClick=="true"?(newWorkFlowOperation(),$("#idWorfkflowName").html("Workflow Editor")):$("#idWorfkflowName").html(n);var i=t.d.split("^");i[0]=="duplicate"?($("#idWorfkflowName").html("Workflow Editor"),alert("Duplicate Workflow Name")):i[0]=="Update"?OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> Workflow <b class='text-success'>"+n+"<\/b> Updated Sucessfully.<\/div>"):(alert("Workflow Saved Sucessfully"),$("#workflowHiddenId").html(i[1]));globalWorkFlowOperation="Update"}})}function WorkflowSaveAsDuplicateMessage(n){$("button:first").attr("disabled","true");n.d=="duplicate"?($("#idSaveAsSpan").html(" Workflow Name is already Present !!!"),$("#idSaveAsSpan").attr("class","error"),$("#idSaveAs").focus(),$("button:first").attr("disabled","true")):($("#idSaveAsSpan").html(""),$("button:first").attr("disabled",!1),globalWorkFlowOperation="New")}function SaveAsWorkflow(n){$("#idSaveAs").trigger("blur");WorkFlowUnChanged();var t="",r="",i="";n=="Generic"?n=WorkFlowName:WorkFlowName=n;$(".one,.diamond").each(function(){r=$(this).parents().get(0);i=$(r).attr("Class");t=$(this).hasClass("diamond")?t+i+","+jQuery.trim($(this).text())+","+$(this).attr("id")+"^"+$(this).attr("totalHeight")+"^"+$(this).attr("actionDifference")+"^"+$(this).attr("targetAction")+":":t+i+","+jQuery.trim($(this).text())+","+$(this).attr("id")+"^"+$(this).attr("totalHeight")+"^"+$(this).attr("actionDifference")+"^"+$(this).attr("targetaction")+":"});t=t.substring(0,t.length-1);$.ajax({type:"POST",url:"WorkflowConfiguration.aspx/SaveAsWorkFlow",data:"{'para':'"+t+"','name':'"+n+"'}",contentType:"application/json; charset=utf-8",dataType:"json",sync:!0,success:function(t){btnNewWorkFLowClick=="true"?(newWorkFlowOperation(),$("#idWorfkflowName").html("Workflow Editor")):($("#idWorfkflowName").html(n),newWorkFlowOperation());var i=t.d;i=="duplicate"?($("#idWorfkflowName").html("Workflow Editor"),alert("Duplicate Workflow Name")):alert("Workflow SaveAs Sucessfully")}})}function newWorkFlowOperation(){btnNewWorkFLowClick="false";WorkFlowUnChanged();$.ajax({type:"POST",url:"WorkflowConfiguration.aspx/CreateNewWorkFlow",data:"{}",contentType:"application/json; charset=utf-8",dataType:"json",sync:!0,success:function(){removeItem();GlobalWorkFlowID="";globalWorkFlowOperation="New";GlobalWorkFlowProperty="";WorkFlowName="";globalId=0;$("[id$=ddlLoadProperty]").val("000");$("[id$=ddlActionSetList]").val("00");$(propertyObj).hide(500);$(conditionDiv).hide(500);$("#idWorfkflowName").html("Workflow Editor")}})}function ElementCount(){var n=document.getElementById("dropBox"),t=n.getElementsByTagName("div");return t.length}function addActionBetweenConditionAndSelectedAction(){var c=$("#ddlSelectedWFProperty option:selected").attr("actionid"),l,n,t,f,r,s,u,h;for(allActionNamesForAddCondition="",getActionIdWhenConditionAdd(),l=allActionNamesForAddCondition.split(","),n=allActionNamesForAddCondition.split(","),t=0;t<n.length;t++){var e=0,o=0,i=0;if(n[t].contains("divCondi")){for(f=n[t].split("^"),e=t,r=0;r<n.length;r++)s=n[r].split("^"),f[1]==s[0]&&(o=r);for(u=0;u<n.length;u++)h=n[u].split("^"),c==h[0]&&(i=u);e<i&&i<o?calculateActionHeightBetweenConditionAndSelectedAction(f[0]):i<e&&o<=i&&calculateActionHeightBetweenConditionAndSelectedAction(f[0])}}}function calculateActionHeightBetweenConditionAndSelectedAction(n){var r=$("#"+n).parent(),n=n,t,u,i;$(r).children(".line").remove();t=$("#"+n).attr("totalheight");u=$("#"+n).attr("actiondifference");loopCnt==0?(i=parseInt(t)+32,loopCnt++):i=parseInt(t)+32;drwaConditionLineInUpAndDownDirection(u,r,i,n)}function ChangeButtonType(){$("#box1").show();$("#disableBox").hide()}function dropItems(){var s=ElementCount(),n="",t="",r="",o=$("#ddlPlace option:selected").val(),e=$("[id$=ddlLoadProperty] option:selected").val(),u=$("[id$=ddlSelectedWFProperty] option:selected").val(),r=$("[id$=ddlLoadProperty] option:selected").attr("profile"),i="divMain"+globalId,f;e!="00"&&e!="000"?(t=e,n=$("[id$=ddlLoadProperty] option:selected").text(),globalId==0?$("<div class='Property' id='"+i+"'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /><\/div><div class='one' id='"+t+"' profile='"+r+"' > <img  src='../images/icons/clock.png' alt='' />"+n+"  <\/div><\/div>").appendTo("div#dropBox"):o=="0"?$("#"+u).before("<div class='Property' id='"+i+"'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /><\/div><div class='one' id='"+t+"'  profile='"+r+"'><img  src='../images/icons/clock.png' />"+n+" <\/div><\/div>"):$("#"+u).after("<div class='Property' id='"+i+"'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /><\/div><div class='one' id='"+t+"'  profile='"+r+"'><img  src='../images/icons/clock.png' />"+n+" <\/div><\/div>"),changeGlobalId(i,n),WorkFlowChange()):(f=$("[id$=ddlActionSetList] option:selected").val(),f!="00"&&f!="000"&&(t=f,n=$("[id$=ddlActionSetList] option:selected").text(),globalId==0?$("<div class='PropertySet' id='"+i+"'><div class='actionboxline'><\/div><div class='one' id='"+t+"'  profile='"+r+"'>"+n+" <img Class='close' src='../images/icons/remove-icon-small.png' /><\/div><\/div>").appendTo("div#dropBox"):o=="0"?$("#"+u).before("<div class='PropertySet' id='"+i+"'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /><\/div><div class='one' id='"+t+"'  profile='"+r+"'>"+n+" <img Class='close' src='../images/icons/remove-icon-small.png' /><\/div><\/div>"):$("#"+u).after("<div class='PropertySet' id='"+i+"'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /><\/div><div class='one' id='"+t+"'  profile='"+r+"'>"+n+" <img Class='close' src='../images/icons/remove-icon-small.png' /><\/div><\/div>"),changeGlobalId(i,n),WorkFlowChange()))}function changeGlobalId(n,t){$("#ddlSelectedWFProperty").append("<option value='"+n+"' selected='selected'>"+t+"<\/option>");globalId=globalId+1;SelectedDiv(n);RearrengeDdl(n)}function removeItem(){$(".one,.diamond").each(function(){var n=$(this).parents().get(0);$(n).remove()});globalId="ParentId0";$("#ddlSelectedWFProperty option").remove();$("#txtRTO").val("");$("#txtRTO").attr("disabled",!1)}function CreateWorkFlowDiagrame(n){var e,u,t,i,r,o,f;for($("#btnCondition").show(),$("#btnConditionDisable").hide(),SelectedWorkFlowProperties(n),globalWorkFlowOperation="Update",e=n.split(":"),u=0;u<e.length;u++)if(u==0)WorkFlowName=e[u],$("#idWorfkflowName").html(WorkFlowName);else if(t=e[u].split(","),t[1].contains("divCondi"))i=t[1],$("<div class='Property' id='myDivCondition'><div class='actionboxline'><\/div><div class='diamond' id='"+t[1]+"'><div class='diamondtext'>1<\/div><img Class='close' src='../images/icons/remove-icon-small.png' style=margin-top:-24px/><div style='visibility:hidden'>"+t[1]+"<\/div><\/div><div class='diamond-line'>Success<\/div><\/div>").appendTo("div#dropBox"),r=$("#"+i).parent(),t[3]<0?($("<div class='line'>").css({height:"1px",background:"#707070",width:"269px",position:"absolute","margin-top":"-27px",float:"right",right:"21px",color:"red"}).text("Failure").appendTo(r),$("<div class='line'>").css({height:parseInt(t[2]),background:"#707070",width:"1px",position:"absolute","margin-top":"-27px",float:"right",right:"20px"}).appendTo(r),$("<div class='downthirdline line'>").css({height:"1px",background:"#707070",width:"69px",position:"absolute","margin-top":parseInt(t[2])+-27,right:"21px"}).appendTo(r),$("#"+i).attr("actionDifference",t[3]),$("#"+i).attr("totalHeight",t[2]),$("#"+i).attr("targetAction",t[4])):t[3]>0&&($("<div  class='line'>").css({height:"1px",background:"#707070",width:"269px",position:"absolute","margin-top":"-27px",left:"21px",color:"red"}).text("Failure").appendTo(r),$("<div class='line'>").css({height:parseInt(t[2]),background:"#707070",width:"1px",position:"absolute","margin-top":"-"+(27+parseInt(t[2]))+"px",float:"right",left:"21px"}).appendTo(r),$("<div class='upthirdline line'>").css({height:"1px",background:"#707070",width:"69px",position:"absolute","margin-top":"-"+(27+parseInt(t[2]))+"px",left:"21px"}).appendTo(r),$("#"+i).attr("actionDifference",t[3]),$("#"+i).attr("totalHeight",t[2]),$("#"+i).attr("targetAction",t[4]));else{$("<div class='"+t[0]+"' id='"+globalId+"'><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /><\/div><div class='one' id='"+t[1]+"' profile='"+t[3]+"'>"+t[2]+" <img Class='close' src='../images/icons/remove-icon-small.png' /><\/div><\/div>").appendTo("div#dropBox");$("#"+t[1]).attr("totalHeight",t[4]);$("#"+t[1]).attr("actionDifference",t[5]);$("#"+t[1]).attr("targetAction",t[6]);var s=globalId.substring(8),h=parseInt(s),c=h+1;globalId="ParentId"+c}o=$(".one").parents().get(0);f=$(o).attr("id");(f!=null||typeof f!="undefined")&&(SelectedDiv(f),RearrengeDdl(f))}function OpenAlertModelConfirmationClose(n){$.modal({content:'<div class="row"> <div class="col-md-2"><img src="../images/icons/Warning.png"/><\/div> <div class="col-md-10"> Are you sure to delete this activity ?  <\/div><\/div>',title:"Confirmation",maxWidth:300,buttons:{Yes:function(t){DeleteActivityConform(t,n)},No:function(n){CloseModel(n)}}})}function DeleteActivityConform(n,t){WorkFlowChange();deleteActionBetweenCondition();$("#"+t).remove();var i=$(".one").parents().get(0);t=$(i).attr("id");$("#btnCancelEdit").hide();$("#btnEdit").show();$("#btnPropertySave").hide();t!=null||typeof t!="undefined"?(SelectedDiv(t),RearrengeDdl(t)):($("#ddlSelectedWFProperty option").remove(),$("#ddlSelectedWFProperty").append("<option value='00' selected='selected'> Select Property <\/option>"),globalId=0);CloseModel(n);$(".one").length>0?($("#btnCondition").show(),$("#btnConditionDisable").hide()):($("#btnCondition").hide(),$("#btnConditionDisable").show())}function deleteActionBetweenCondition(){var c=$("#ddlSelectedWFProperty option:selected").attr("actionid"),n,t,f,r,s,u,h;for(getActionIdWhenConditionAdd(),n=allActionNamesForAddCondition.split(","),t=0;t<n.length;t++){var e=0,o=0,i=0;if(n[t].contains("divCondi")){for(f=n[t].split("^"),e=t,r=0;r<n.length;r++)s=n[r].split("^"),f[1]==s[0]&&(o=r);for(u=0;u<n.length;u++)h=n[u].split("^"),c==h[0]&&(i=u);e<i&&i<=o?calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(f[0]):i<e&&o<=i&&calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(f[0])}}}function calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(n){var t,n,i,u,r;chkReturnOrNot||(t=$("#"+n).parent(),n=n,$(t).children(".line").remove(),i=$("#"+n).attr("totalheight"),u=$("#"+n).attr("actiondifference"),loopCnt==0?(r=parseInt(i)-32,loopCnt++):r=parseInt(i)-32,drwaConditionLineInUpAndDownDirection(u,t,r,n))}function SelectedWorkFlowProperties(n){var r=document.getElementById("ddlSelectedWFProperty"),u,i,f,t,e,o;if(r.hasChildNodes())while(r.childNodes.length>=1)r.removeChild(r.firstChild);for(u=n.split(":"),t=document.createElement("option"),r.options.add(t),t.text="-Select-",i++,t.value="--000--",i=0;i<u.length;i++)i!=0&&(f=u[i].split(","),t=document.createElement("option"),r.options.add(t),e=f[2],t.text=e,o=i,t.value=o)}function SaveAsModalPopupsPrompt(){OpenAlertModelLoad("<div class='col-md-4'> WorkFlow Name<\/div><div class='col-md-8'> <input id='idSaveAs' type=text class='form-control' style='width:65%;' /><span id='idSaveAsSpan'><\/span><\/div>",SaveAsModalPopupsPromptOk)}function SaveAsModalPopupsPromptOk(n){if(document.getElementById("idSaveAs").value==""){document.getElementById("idSaveAs").focus();return}var t=document.getElementById("idSaveAs").value;n.closeModal();SaveAsWorkflow(t)}function AssignValue(n){for(var i=n.split(":"),t=0;t<txtObj.length;t++)txtObj[t]=="txtExecMode"?$("#txtExecMode").val(i[t]).attr("selected",!0):txtObj[t]=="ddlHost"?$("[id$=ddlHost]").val(i[t]).attr("selected",!0):document.getElementById(txtObj[t]).value=i[t];txtName=document.getElementById(txtObj[0]).value;$("#hdActivityName").val(txtName);DisablePropertyElement()}function callPropertyfunction(n){$.ajax({type:"POST",url:"WorkflowConfiguration.aspx/MainFunction",cache:!0,data:"{'args':'GetProperty','parameter':'"+n+"'}",contentType:"application/json; charset=utf-8",dataType:"json",success:function(n){AssignValue(n.d)}});$("#divActionSet").hide(1e3);$(propertyObj).show(1e3);hideDivError()}function dragConditionBetweenCondition(n){var l=n,t,i,e,u,h,f,c;for(getActionIdWhenConditionAdd(),t=allActionNamesForAddCondition.split(","),i=0;i<t.length;i++){var o=0,s=0,r=0;if(t[i].contains("divCondi")){for(e=t[i].split("^"),o=i,u=0;u<t.length;u++)h=t[u].split("^"),e[1]==h[0]&&(s=u);for(f=0;f<t.length;f++)c=t[f].split("^"),l==c[0]&&(r=f);o<=r&&r<=s?calculateHeightForDeleteCondition(e[0]):r<o&&s<=r&&calculateHeightForDeleteCondition(e[0])}}}function dropConditionBetweenCondition(n){var l=n,a,t,i,u,f,h,e,c;for(allActionNamesForAddCondition="",getActionIdWhenConditionAdd(),a=allActionNamesForAddCondition.split(","),t=allActionNamesForAddCondition.split(","),i=0;i<t.length;i++){var o=0,s=0,r=0;if(t[i].contains("divCondi")){for(u=t[i].split("^"),o=i,f=0;f<t.length;f++)h=t[f].split("^"),u[1]==h[0]&&(s=f);for(e=0;e<t.length;e++)c=t[e].split("^"),l==c[0]&&(r=e);u[1]!="undefined"&&(o<r&&r<s?calculateHeight(u[0]):r<o&&s<=r&&calculateHeight(u[0]))}}}function dragActionDBetweenCondition(n){var a=n,i,r,t,h,f,c,e,l;for(getActionIdWhenConditionAdd(),i=allActionNamesForAddCondition.split(","),r=0;r<i.length;r++){chkReturnOrNot=!1;var o=0,s=0,u=0;if(i[r].contains("divCondi")){for(t=i[r].split("^"),h=$("#"+t[0]).attr("targetaction"),h==n&&($("#"+t[0]).parent().prev().children(".one").removeAttr("targetaction"),$("#"+t[0]).removeAttr("targetaction"),$("#"+t[0]).parent().children(".line").remove(),chkReturnOrNot=!0),o=r,f=0;f<i.length;f++)c=i[f].split("^"),t[1]==c[0]&&(s=f);for(e=0;e<i.length;e++)l=i[e].split("^"),a==l[0]&&(u=e);t[1]!="undefined"&&(o<u&&u<=s?calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(t[0]):u<o&&s<=u&&calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(t[0]))}}}function dropActionBetweenConditionAndSelectedAction(n){var l=n,a,t,i,u,f,h,e,c;for(allActionNamesForAddCondition="",getActionIdWhenConditionAdd(),a=allActionNamesForAddCondition.split(","),t=allActionNamesForAddCondition.split(","),i=0;i<t.length;i++){var o=0,s=0,r=0;if(t[i].contains("divCondi")){for(u=t[i].split("^"),o=i,f=0;f<t.length;f++)h=t[f].split("^"),u[1]==h[0]&&(s=f);for(e=0;e<t.length;e++)c=t[e].split("^"),l==c[0]&&(r=e);u[1]!="undefined"&&(o<r&&r<s?calculateActionHeightBetweenConditionAndSelectedAction(u[0]):r<o&&s<=r&&calculateActionHeightBetweenConditionAndSelectedAction(u[0]))}}}function RearrengeDdl(n){var t="";$("#ddlSelectedWFProperty option").remove();$("#ddlSelectedWFProperty").append("<option value='00' selected='selected'> Select Property <\/option>");$(".one").each(function(){var r=$(this).parents().get(0),u=$(r).attr("id"),n,i;t=$(this).text();n=$(this).attr("id");i=$(this).attr("profile");t=t.substring(t.indexOf(".")+1);$("#ddlSelectedWFProperty").append("<option value='"+u+"' profile='"+i+"' actionId='"+n+"'>"+t+"<\/option>")});$("#ddlSelectedWFProperty").val(n).attr("selected",!0)}function ChangeName(n){var i="",r="",t,u;$("#txtName").val()==""?(i=$("#hdActivitySetName").val(),r=0):(i=$("#hdActivityName").val(),r=1);t="";$("#ddlSelectedWFProperty option").each(function(){i=jQuery.trim(i);t=jQuery.trim($(this).text());t==i&&($(this).text(n),r==0?$("#hdActivitySetName").val(n):$("#hdActivityName").val(n))});r=1;u="";$(".one").each(function(){$(this).html($(this).html().replace(/&nbsp;/gi,""));t=$(this).text();t=t.substring(t.indexOf(".")+3);t=jQuery.trim(t);i=jQuery.trim(i);t==i&&(u=r+". &nbsp;&nbsp;"+n+"<img Class='close' src='../images/icons/remove-icon-small.png' />",$(this).html(u));r++})}function OpenAlertSaveConformation(){OpenAlertModelConform("Workflow not save Do you want to save it",NewWorkflowConfirm,OldWorkflowNotSave)}function OldWorkflowNotSave(n){$("#txtRTO").val("");$("#txtRTO").attr("disabled",!1);newWorkFlowOperation();WorkFlowUnChanged();CloseModel(n)}function NewWorkflowConfirm(n){btnNewWorkFLowClick="true";$("#btnSave").trigger("click");CloseModel(n)}function SelectedDiv(n){var t="",u=1,r,i,e,f;for($(".one").each(function(){$(this).html($(this).html().replace(/&nbsp;/gi,""));t=$(this).text();t=jQuery.trim(t);t=t.substring(t.indexOf(".")+1);t=u+".&nbsp;&nbsp;"+t+"<img Class='close' src='../images/icons/remove-icon-small.png' />";$(this).html(t);$(this).css("border","1px solid #707070");$(this).css("background-color","#FAFAFA");$(this).css("color","#707070");u++}),$(".diamond").each(function(){$(this).css("border","1px solid #707070");$(this).css("background-color","#FAFAFA");$(this).css("color","#707070");u++}),getActionIdAndName(),r=allActionNamesForAddCondition.split(","),i=0;i<r.length-1;i++)if(r[i].contains("divCondi")){var o=r[i-1].split("^"),s=o[1].split("."),h=r[i].split("^");$("#"+h[0]).children(".diamondtext").html(s[0])}$("#"+n).find(".one,.diamond").css("border","1px solid #0081C2 ");$("#"+n).find(".one,.diamond").css("background-color","#aac8e2");$("#"+n).find(".one,.diamond").css("color","#111");e=$("#"+n).attr("Class");f=$("#"+n).find(".one").attr("id");e=="Property"?($("[id$=ddlLoadProperty]").val(f),$("[id$=ddlLoadProperty]").trigger("change")):CallPropertySetFunction(f);RearrengeDdl(n)}function validateActionset(){for(var t=["txtActionSetName","txtActionSetDescription","ddlActionSetProperty"],i="",r="",n=0;n<t.length;n++)n!=1&&$("#"+t[n]).val()==""&&(r=1,i=i+t[n]+"Require :"),n==0&&($("#"+t[n]).val().match(/^[a-zA-Z\s]+$/)||(r=1,i="Action Name only required Alphabet :")),n==2&&$("#"+t[n]+" option").size()==0&&(r=1,i=i+t[n]+"Please Select Activity :");return r==1?i:"No error"}function EnableImage(){$("#box1").show();$("#disableBox").hide()}function DisableImage(){$("#box1").hide();$("#disableBox").show()}function PropertyError(n,t){var r,i;for(n=n.substring(0,n.length-1),r=n.split(":"),$("#"+t).empty(),i=0;i<r.length;i++)$("#"+t).append("<div class='error'   >"+r[i]+"  <\/div>")}function checkExistActivityNameOnEdit(n,t,i,r,u){var o=jQuery.trim($("#"+i).val()),f=jQuery.trim($("#"+n).val()),e;f!=o&&(e=f,$("#"+r+" option").each(function(){if(e==jQuery.trim($(this).text()))return $("#error"+t).html(u),$("#error"+t).show(),!1}))}function checkExistActivityNameOnAdd(n,t,i,r){var u=jQuery.trim($("#"+n).val()),f=u;$("#"+i+" option").each(function(){if(f==jQuery.trim($(this).text()))return $("#error"+t).html(r),$("#error"+t).show(),!1})}function clearValidation(){$(".error").hide()}function hideDivError(){for(var t=["errortxtName","errortxtScript","errortxtRecurrenceTime","divError","errortxtActionSetName","divActionSetError"],n=0;n<t.length;n++)$("#"+t[n]).hide()}function WorkFlowChange(){isWorkFlowChange="Change"}function WorkFlowUnChanged(){isWorkFlowChange="UnChange"}function drwaConditionLineInUpAndDownDirection(n,t,i,r){parseInt(n)<0?($("<div class='line'>").css({height:"1px",background:"#707070",width:"269px",position:"absolute","margin-top":"-27px",float:"right",right:"21px",color:"red"}).text("Failure").appendTo(t),$("<div class='line'>").css({height:parseInt(i),background:"#707070",width:"1px",position:"absolute","margin-top":"-27px",float:"right",right:"20px"}).appendTo(t),$("<div class='downthirdline line'>").css({height:"1px",background:"#707070",width:"69px",position:"absolute","margin-top":parseInt(i)+-27,right:"21px"}).appendTo(t),$("#"+r).attr("totalHeight",i)):parseInt(n)>0&&($("<div  class='line'>").css({height:"1px",background:"#707070",width:"269px",position:"absolute","margin-top":"-27px",left:"21px",color:"red"}).text("Failure").appendTo(t),$("<div class='line'>").css({height:i,background:"#707070",width:"1px",position:"absolute","margin-top":"-"+(27+i)+"px",float:"right",left:"21px"}).appendTo(t),$("<div class='upthirdline line'>").css({height:"1px",background:"#707070",width:"69px",position:"absolute","margin-top":"-"+(27+i)+"px",left:"21px"}).appendTo(t),$("#"+r).attr("totalHeight",i))}var isWorkFlowChange="UnChange",btnNewWorkFLowClick="false",actionforNew="old",globalWorkFlowOperation="New",WorkFlowName="",txtObj=["txtName","txtDescription","ddlHost","txtScript","txtExecMode","txtRecurrenceTime"],propertyObj=document.getElementById("divProperties"),hdActivityAllObj=document.getElementById("hdActivityAll"),ddlLoadPropertyObj=document.getElementById("ddlLoadProperty"),conditionDiv=document.getElementById("conditionDiv"),dropContentObj=document.getElementById("dropContent"),GlobalWorkFlowProperty="",GlobalWorkFlowID="",globalId=0,txtName="",workFlowId=0,globalConditionId=1,formClose="",allActionNames="",allActionNamesForAddCondition="",divCondiCnt,ddlActivitySetObj,clickCount,dropboxClick;par="";conditionClick="";conditionOptionValue="";conditionClickId="";loopCnt=0;chkReturnOrNot=!1;idWorkflowloaded=0;divCondiCnt="divCondiNotPresent";ddlActivitySetObj=["txtActionSetName","txtActionSetDescription","ddlActionProperty","ddlActionSetProperty"];$(".diamond > .close").live("click",function(){var e=$(this).parent().attr("id"),n,t,f,r,h,u,c;for($("#"+e).parent().prev().children(".one").removeAttr("targetaction"),getActionIdWhenConditionAdd(),n=allActionNamesForAddCondition.split(","),t=0;t<n.length;t++){var o=0,s=0,i=0;if(n[t].contains("divCondi")){for(f=n[t].split("^"),o=t,r=0;r<n.length;r++)h=n[r].split("^"),f[1]==h[0]&&(s=r);for(u=0;u<n.length;u++)c=n[u].split("^"),e==c[0]&&(i=u);o<=i&&i<=s?calculateHeightForDeleteCondition(f[0]):i<o&&s<=i&&calculateHeightForDeleteCondition(f[0])}}$("#"+e).parent().remove()});$("#btnCondition").click(function(){var e=$("[id$=ddlSelectedWFProperty] option:selected").val(),n="divCondi"+globalConditionId,o=0,i=0,r=0,u,t,s,f,h;for($(".diamond").each(function(){var n,t,r;$(this).hasClass("diamond")&&(n=parseInt($(this).attr("id").replace("divCondi",""),10),n>i&&(i=n),o++);t=$(this).parent();r=$(this).attr("id")}),checkDivcondiDivAfterActionSelect(),getActionIdAndName(),u=allActionNamesForAddCondition.split(","),t=0;t<u.length;t++)s=$("#ddlSelectedWFProperty option:selected").attr("actionid"),f=u[t].split("^"),s==f[0]&&(h=f[1].split("."),r=h[0]);o>0&&idWorkflowloaded==1?(globalConditionId=i+1,n="divCondi"+globalConditionId,$("#"+e).after("<div class='Property' id='myDivCondition'><div class='actionboxline'><\/div><div class='diamond' id='"+n+"'><div class='diamondtext'>"+r+"<\/div><img Class='close' src='../images/icons/remove-icon-small.png' style=margin-top:-24px/><div style='visibility:hidden'>"+n+"<\/div><\/div><div class='diamond-line'>Success<\/div><\/div>")):($("#"+e).after("<div class='Property' id='myDivCondition'><div class='actionboxline'><\/div><div class='diamond' id='"+n+"'><div class='diamondtext'>"+r+"<\/div><img Class='close' src='../images/icons/remove-icon-small.png' style=margin-top:-24px /><div style='visibility:hidden'>"+n+"<\/div><\/div><div class='diamond-line'>Success<\/div><\/div>"),globalConditionId=globalConditionId+1)});$("[id^=divCondi]").live("click",function(){clickCount=0;par=$(this).parent();conditionClick=$(this).children().text();conditionClickId=$(this).attr("id");GetActionValue()});$("#btnConnectAction").click(function(){var v=$("#ddlExist1 option:selected").text(),w=$("#ddlExist1 option:selected").val(),c=$("#ddlExist1 option:selected").attr("actionId"),b=$("[id$=ddlSelectedWFProperty] option:selected").val(),u,y,n,f,a,p,l,e,i,r,o,s,h,t;for($("#"+conditionClickId).attr("targetaction")&&($("#"+conditionClickId).parent().children(".line").remove(),$("#"+conditionClickId).parent().prev().children(".one").attr("targetaction",c)),u=conditionOptionValue-w,GetActionValueWhenWeClickOnConnectButton(),y=allActionNames.split(","),n=allActionNames.split(","),allActionNames="",f=0,a=0;a<y.length;a++)if(v==y[a])if(u<0){for(n.splice(0,conditionOptionValue),e=0,i=0;i<n.length;i++)n[i]==v&&(e=i);for(n.splice(e,n.length),r=0;r<n.length;r++)o=n[r].substring(0,n[r].length-1),o.substring(1,o.length)=="divCondi"&&f++;s=16;h=parseInt(32*Math.abs(u));t=f>0?parseInt(s+h+23*f):parseInt(s+h);$("<div class='line'>").css({height:"1px",background:"#707070",width:"269px",position:"absolute","margin-top":"-27px",float:"right",right:"21px",color:"red"}).text("Failure").appendTo(par);$("<div class='line'>").css({height:t,background:"#707070",width:"1px",position:"absolute","margin-top":"-27px",float:"right",right:"20px"}).appendTo(par);$("<div class='downthirdline line'>").css({height:"1px",background:"#707070",width:"69px",position:"absolute","margin-top":t+-27,right:"21px"}).appendTo(par);$("#"+conditionClickId).attr("actionDifference",u);$("#"+conditionClickId).attr("totalHeight",t);$("#"+conditionClickId).attr("targetAction",c);$("#"+conditionClickId).parent().prev().children(".one").attr("actionDifference",u);$("#"+conditionClickId).parent().prev().children(".one").attr("totalHeight",t);$("#"+conditionClickId).parent().prev().children(".one").attr("targetAction",c)}else if(u>0){for(l=0;l<n.length;l++)n[l]==conditionClick&&(p=l);for(n.splice(p,n.length),e=0,i=0;i<n.length;i++)n[i]==v&&(e=i);for(n.splice(0,e),r=0;r<n.length;r++)o=n[r].substring(0,n[r].length-1),o.substring(1,o.length)=="divCondi"&&f++;s=16;h=parseInt(32*u);t=f>0?parseInt(s+h+22*f):parseInt(s+h);$("<div  class='line'>").css({height:"1px",background:"#707070",width:"269px",position:"absolute","margin-top":"-27px",left:"21px",color:"red"}).text("Failure").appendTo(par);$("<div class='line'>").css({height:t,background:"#707070",width:"1px",position:"absolute","margin-top":"-"+(27+t)+"px",float:"right",left:"21px"}).appendTo(par);$("<div class='upthirdline line'>").css({height:"1px",background:"#707070",width:"69px",position:"absolute","margin-top":"-"+(27+t)+"px",left:"21px"}).appendTo(par);$("#"+conditionClickId).attr("actionDifference",u);$("#"+conditionClickId).attr("totalHeight",t);$("#"+conditionClickId).attr("targetAction",c);$("#"+conditionClickId).parent().prev().children(".one").attr("actionDifference",u);$("#"+conditionClickId).parent().prev().children(".one").attr("totalHeight",t);$("#"+conditionClickId).parent().prev().children(".one").attr("targetAction",c)}});$("[id^=divCondi]").live({mouseover:function(){$(this).parent().children(".line").css({"background-color":"#468847","box-shadow":"2px 1px 2px #468847"})},mouseout:function(){$(this).parent().children(".line").css({background:"#707070","box-shadow":"none"})}});$("#btnLoadWorkFlow").click(function(){OpenAlertModelForLoadWorkflow("  <div class='col-md-4'> Select WorkFlow <\/div> <div class='col-md-8'><select id='ddlExist' class='col-md-10'><\/select><span><\/span><span id='idMsg'><\/span><\/div>",closeModelPopep,CheckWorkFlowIsAttached);GetDdlExistValue();idWorkflowloaded=1});$("#ddlExist").live("change",function(){RequireDropDown($(this).attr("id"))});$("[id$=idPrompt1]").live("blur",function(){var n=$("[id$=idPrompt1]").val(),r=new RegExp("\\\\]*$"),t,i;n!=""?n.indexOf(" ")>=0?($("#idPromptSpan").html("Space is not allow in Workflow Name!!!"),$("#idPromptSpan").attr("class","error"),$("#idPrompt1").focus(),$("button:first").attr("disabled","true")):r.test(n)?($("#idPromptSpan").html("Backslash is not allow in Workflow Name!!!"),$("#idPromptSpan").attr("class","error"),$("#idPrompt1").focus(),$("button:first").attr("disabled","true")):($("#idPromptSpan").html(""),$("button:first").attr("disabled",!1),t="WorkflowConfiguration.aspx/IsExistWorkflowByName",i="{'id':'"+n+"'}",AjaxFunction(t,i,WorkflowDuplicateMessage,OnError)):($("#idPromptSpan").html(""),$("button:first").attr("disabled",""),globalWorkFlowOperation="New")});$("[id$=idSaveAs]").live("blur",function(){var n=$("[id$=idSaveAs]").val(),t,i;n!=""?(t="WorkflowConfiguration.aspx/IsExistWorkflowByName",i="{'id':'"+n+"'}",AjaxFunction(t,i,WorkflowSaveAsDuplicateMessage,OnError)):($("#idSaveAsSpan").html(""),$("button:first").attr("disabled",!1),globalWorkFlowOperation="New")});$("#box1").click(function(){$(this).hide();$("#disableBox").show();var n=setTimeout(ChangeButtonType,500);dropItems();$(".one").length>0?($("#btnCondition").show(),$("#btnConditionDisable").hide()):($("#btnCondition").hide(),$("#btnConditionDisable").show());addActionBetweenConditionAndSelectedAction()});dropboxClick=0;$(".one > .close").live("click",function(){var n=$(this).parents().get(1),t=$(n).attr("id");OpenAlertModelConfirmationClose(t)});$("#btnSave").click(function(){if($("#dropBox .Property").length==0)return OpenAlertModelAlert("First add workflow action in Workflow!!!"),!1;globalWorkFlowOperation=="New"?(ModalPopupsPrompt(),globalWorkFlowOperation="Update"):SaveWorkflow("Generic")});$("#btnSaveAs").click(function(){if($("#dropBox .Property").length==0)return OpenAlertModelAlert("First add workflow action in Workflow!!!"),!1;SaveAsModalPopupsPrompt()});$(".one").live("click",function(){var t=$(this).parents().get(0),i=$(t).attr("id"),r,n;SelectedDiv(i);r=$(this).parent().attr("class");n=$(this).parent().attr("Id");$("#ddlSelectedWFProperty").val(n).attr("selected",!0);GlobalWorkFlowProperty=this.id});$(".diamond").live("click",function(){var n=$(this).attr("Id");SelectedDiv(n);$(conditionDiv).show(500);$("[id$=ddlLoadProperty]").val("000");$("#txtRTO").val("");$(".one").each(function(){$(this).css("border","1px solid #707070");$(this).css("background-color","#FAFAFA");$(this).css("color","#707070");i++});$(".diamond").each(function(){var n=this;$(this).css("border","1px solid #707070");$(this).css("background-color","#FAFAFA");$(this).css("color","#707070");i++});$("#"+n).css("border","1px solid #0081C2 ");$("#"+n).css("background-color","#aac8e2");$("#"+n).css("color","#111")});$("#btnRemoveActivity").click(function(){$("#"+GlobalWorkFlowProperty).hide(1e3)});$(function(){$("#dropBox").sortable({stop:function(n,t){$(t.item).find(".actionboxline").show();SelectedDiv($(t.item).attr("id"));RearrengeDdl($(t.item).attr("id"));WorkFlowChange();var i=$(t.item).children().eq(1).attr("id");if(i.contains("divCondi")){dropConditionBetweenCondition(i);return}dropActionBetweenConditionAndSelectedAction($(t.item).children().eq(1).attr("id"))},start:function(n,t){$(t.item).find(".actionboxline").hide();var i=$(t.item).children().eq(1).attr("id");if(i.contains("divCondi")){$("#"+i).parent().children(".line").remove();$("#"+i).parent().prev().children(".one").removeAttr("targetaction");$("#"+i).removeAttr("targetaction");$("#"+i).removeAttr("actiondifference");$("#"+i).removeAttr("totalheight");dragConditionBetweenCondition(i);return}dragActionDBetweenCondition($(t.item).children().eq(1).attr("id"))}});$("#dropBox").disableSelection()});$("#ddlSelectedWFProperty").change(function(){var n=$("#ddlSelectedWFProperty option:selected").val();SelectedDiv(n)});$("#btnNewWorkFlow").click(function(){isWorkFlowChange=="Change"?$(".one").length>0&&(OpenAlertModelConform("Workflow not save Do you want to save it",NewWorkflowConfirm,OldWorkflowNotSave),$("#btnCondition").hide(),$("#btnConditionDisable").show()):(newWorkFlowOperation(),WorkFlowUnChanged(),$("#btnCondition").hide(),$("#btnConditionDisable").show(),$("#workflowHiddenId").html(""))});$("#btnCancelEdit").click(function(){$("#ddlLoadProperty").trigger("change");$("#btnPropertySave").hide();$("#btnCancelEdit").hide();$("#btnEdit").show();EnableImage();DisablePropertyElement();hideDivError()});$(".validation").blur(function(){var n=$(this).attr("id"),t="",i;if($(this).val()=="")return $(this).attr("id")=="txtName"&&(t="Please Enter Action Name"),$(this).attr("id")=="txtScript"&&(t="Please Enter Script"),$(this).attr("id")=="txtRecurrenceTime"&&(t="Please Enter Recurrence Time"),$(this).attr("id")=="txtActionSetName"&&(t="Enter Action Set Name"),$("#error"+n).html(t),$("#error"+n).show(),!1;if($(this).attr("id")!="txtRecurrenceTime"&&$(this).attr("id")!="txtScript")if($(this).val().match(/^[a-zA-Z0-9''_'\s]{3,35}$/))$("#error"+n).hide();else return t="Only AlphaNumeric value allow",$("#error"+n).html(t),$("#error"+n).show(),!1;else $("#error"+n).hide();$(this).attr("id")=="txtName"&&(i=$("#ddlLoadProperty option:selected").val(),i=="000"?checkExistActivityNameOnAdd("txtName",n,"ddlLoadProperty","Activity Name is not Available"):checkExistActivityNameOnEdit("txtName",n,"hdActivityName","ddlLoadProperty","Activity Name is not Available"));$(this).attr("id")=="txtActionSetName"&&(i=="000"?checkExistActivityNameOnAdd("txtActionSetName",n,"ddlActionSetList","Action Set is Not Available"):checkExistActivityNameOnEdit("txtActionSetName",n,"hdActivitySetName","ddlActionSetList","Action Set is Not Available"))});$("#btnRunFlow").click(function(){var n="";$(".one").each(function(){var t=$(this).parents().get(0),i=$(t).attr("class"),r=$(this).attr("id");n=n+i+r+":"});n=n.substring(0,n.length-1)});$("#genrateXml").click(function(){function n(){document.getElementById("iframeId").src="http://localhost:4973/BCMS/WorkFlow/download.aspx"}jPrompt("Enter Name of the file:","","Confirmation",function(t){t&&$.ajax({type:"POST",url:"Workflow.aspx/CreateXmlFile",data:"{'name':'"+t+"'}",contentType:"application/json; charset=utf-8",dataType:"json",success:function(){jConfirm("Are u sure to Download?","Confirmation Dialog",function(t){t==!0&&n()})},error:function(n){alert(n.d)}})})});navigator.userAgent.indexOf("Chrome")==-1&&$("body").addClass("chromebrowser");
