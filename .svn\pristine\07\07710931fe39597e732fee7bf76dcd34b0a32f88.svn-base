﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess.RSync
{
    internal sealed class RSyncBuilder : IEntityBuilder<RSyncReplication>
    {
        IList<RSyncReplication> IEntityBuilder<RSyncReplication>.BuildEntities(IDataReader reader)
        {
            var rsync = new List<RSyncReplication>();

            while (reader.Read())
            {
                rsync.Add(((IEntityBuilder<RSyncReplication>)this).BuildEntity(reader, new RSyncReplication()));
            }

            return (rsync.Count > 0) ? rsync : null;
        }

        RSyncReplication IEntityBuilder<RSyncReplication>.BuildEntity(IDataReader reader, RSyncReplication rsync)
        {

            rsync.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["ID"]);
            rsync.ReplicationId = Convert.IsDBNull(reader["REPLICATIONID"]) ? 0 : Convert.ToInt32(reader["REPLICATIONID"]);
            rsync.RSyncPath = Convert.IsDBNull(reader["RSYNCPATH"]) ? string.Empty : Convert.ToString(reader["RSYNCPATH"]);
            rsync.ExecuteWithSudo = Convert.IsDBNull(reader["EXECUTEWITHSUDO"])
                ? string.Empty
                : Convert.ToString(reader["EXECUTEWITHSUDO"]);
            rsync.ScheduleTime = Convert.IsDBNull(reader["SCHEDULETIME"])
                ? string.Empty
                : Convert.ToString(reader["SCHEDULETIME"]);


            rsync.IsActive = Convert.IsDBNull(reader["ISACTIVE"]) ? 0 : Convert.ToInt32(reader["ISACTIVE"]);
            rsync.CreatorId = Convert.IsDBNull(reader["CREATORID"]) ? 0 : Convert.ToInt32(reader["CREATORID"]);
            rsync.CreateDate = Convert.IsDBNull(reader["CREATEDATE"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CREATEDATE"].ToString());
            rsync.UpdatorId = Convert.IsDBNull(reader["UPDATORID"]) ? 0 : Convert.ToInt32(reader["UPDATORID"]);
            rsync.UpdateDate = Convert.IsDBNull(reader["UPDATEDATE"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UPDATEDATE"].ToString());

            rsync.Password = Convert.IsDBNull(reader["PASSWORD"])
              ? string.Empty
              : Convert.ToString(reader["PASSWORD"]);

            rsync.OSPlatform = Convert.IsDBNull(reader["OSPLATFORM"])
               ? string.Empty
               : Convert.ToString(reader["OSPLATFORM"]);

            return rsync;
        }
    }
}
