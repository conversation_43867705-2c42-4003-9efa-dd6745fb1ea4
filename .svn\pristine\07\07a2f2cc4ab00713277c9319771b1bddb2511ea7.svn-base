﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using System.Web.Security;
using System.Web;

namespace CP.UI
{
    public partial class GlobalMirrorConfiguration : ReplicationControl
    {
        #region Variable

        private static readonly List<GlobalMirrorLuns> SaveLuns = new List<GlobalMirrorLuns>();
        private GlobalMirrorLuns lunsnew = new GlobalMirrorLuns();
        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        private GlobalMirror _globalMirror = null;
        private static int _chkcount;
        public string Result;
        public string Conn;
        public GlobalMirror Globallst;
        public IList<GlobalMirrorLuns> LstRep;
        private List<GlobalMirrorLuns> _lunsTable = new List<GlobalMirrorLuns>();

        private GlobalMirrorLuns _luns;

        private int _getId = 0;

        #endregion Variable

        #region Properties

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "GlobalMirror Information"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public GlobalMirror CurrentEntity
        {
            get { return _globalMirror ?? (_globalMirror = new GlobalMirror()); }
            set
            {
                _globalMirror = value;
            }
        }

        #endregion Properties

        public override void PrepareView()
        {
            ddlServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvddlServer.ClientID + ")");
            ddlPrServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvPRServer.ClientID + ")");
            ddlDrServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvDRServer.ClientID + ")");
            txtPrLssid.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
            txtLSSIDrange.Attributes.Add("onblur", "ValidatorValidate(" + PRGMLSSIDs.ClientID + ")");
            txtsessionid.Attributes.Add("onblur", "ValidatorValidate(" + rfvSessionID.ClientID + ")");
            txtPRStorageImgID.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");
            txtDRStorageImgID.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");
            txtPRWNNNo.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator5.ClientID + ")");
            txtDRWNNNo.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator6.ClientID + ")");

            Session.Remove("GlobalMirror");
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

            Utility.PopulateServerByTypeAndRole(ddlServer, "DSCLIServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            Utility.PopulateServerByTypeAndRole(ddlPrServer, "HMCServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            Utility.PopulateServerByTypeAndRole(ddlDrServer, "HMCServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);

            PrepareEditView();
            btnUploadLunsdetails.Enabled = false;
          
        }

        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
        //    {
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }

                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtSSHUser");
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }


        private void PrepareEditView()
        {
            if (CurrentGlobalMirror != null && CurrentGlobalMirror.Id > 0)
            {
                CurrentEntity = CurrentGlobalMirror;

                Session["GlobalMirror"] = CurrentEntity;

                Session.Remove("PreviousItem");

                txtDSCLIHostname.Enabled = true;
                txtDSCLIServerIP.Enabled = true;
                txtSSHUserID.Enabled = true;
                txtSSHPassword.Enabled = true;

                ddlServer.SelectedValue = CurrentEntity.DSCLIServerId.ToString();
                ServerDetails();
                txtDSCLIPath.Text = CurrentEntity.DSCLIPath.ToString();
                ddlPrServer.SelectedValue = CurrentEntity.HMCPRServerId.ToString();
                ddlDrServer.SelectedValue = CurrentEntity.HMCDRServerId.ToString();
                PrserverDetails();
                DrserverDetails();

                bool isdiscover = CurrentEntity.IsManual;
                txtPRMgtConsoleIP.Visible = true;
                txtDRMgtConsoleIP.Visible = true;
                txtPrLssid.Text = CurrentEntity.PRLSSID;
                txtLSSIDrange.Text = CurrentEntity.LSSIDRange;
                txtsessionid.Text = CurrentEntity.SessionId;
                txtReverseSessionId.Text = CurrentEntity.ReverseSessionId;
                if (isdiscover)
                {
                    EnableManual();
                    txtDRWNNNo.Text = CurrentEntity.DRWWN;
                    txtPRWNNNo.Text = CurrentEntity.PRWWN;
                    txtPRStorageImgID.Text = CurrentEntity.PRStorageImageId;
                    txtDRStorageImgID.Text = CurrentEntity.DRStorageImageId;
                    BindManualDiscover();
                    SaveRep.Text = "Update";
                    btnDiscover.Enabled = false;
                    lvprconfiguration.Visible = false;
                    SaveRep.Enabled = true;
                    lblMsg.Text = "";
                }
                else
                {
                    SaveRep.Text = "Update";
                    btnCancel.Text = "Cancel";
                    btnDiscover.Enabled = true;
                    lvprconfiguration.Visible = false;
                    SaveRep.Enabled = false;
                    lblMsg.Text = "";
                }
            }
            else
            {
                SaveLuns.Clear();
                txtSSHUserID.Text = string.Empty;
                txtSSHPassword.Attributes.Clear();
            }
        }

        public void CheckBoxHeaderCheckedChanged(object sender, EventArgs e)
        {
            var chkHeader = sender as CheckBox;
            if (chkHeader != null)
            {
                var item = (ListViewItem)chkHeader.NamingContainer;
                var dataItem = (ListViewDataItem)item;
                string code = dataItem.DataItemIndex.ToString();
                int i = Convert.ToInt32(code);
                var chk = lvprconfiguration.Items[i].FindControl("CheckBox1") as CheckBox;
                if (chk != null && (chk.Checked = chkHeader.Checked))
                {
                    var txtDvolume = lvprconfiguration.Items[i].FindControl("DVolume") as TextBox;
                    if (txtDvolume != null) txtDvolume.Enabled = true;
                    var txtEvolume = lvprconfiguration.Items[i].FindControl("EVolume") as TextBox;
                    if (txtEvolume != null) txtEvolume.Enabled = true;
                    var txtFvolume = lvprconfiguration.Items[i].FindControl("FVolume") as TextBox;
                    if (txtFvolume != null) txtFvolume.Enabled = true;
                    chk.Checked = chkHeader.Checked;
                    SaveRep.Enabled = true;
                    _chkcount++;
                }
                else
                {
                    var lblvalue = lvprconfiguration.Items[i].FindControl("lblVal1") as Label;

                    var avlm = lvprconfiguration.Items[i].FindControl("AVolume") as Label;

                    var bvlm = lvprconfiguration.Items[i].FindControl("BVolume") as Label;

                    var cvlm = lvprconfiguration.Items[i].FindControl("CVolume") as Label;

                    var txtD = lvprconfiguration.Items[i].FindControl("DVolume") as TextBox;

                    var txtE = lvprconfiguration.Items[i].FindControl("EVolume") as TextBox;

                    var txtF = lvprconfiguration.Items[i].FindControl("FVolume") as TextBox;

                    _chkcount--;
                    if (Session["GlobalMirror"] != null)
                    {
                        CurrentEntity = (GlobalMirror)Session["GlobalMirror"];

                        IList<GlobalMirrorLuns> lunces = Facade.GetGlobalMirrorLunsByGlobalMirrorId(CurrentEntity.Id);

                        if (lunces != null)
                        {
                            foreach (GlobalMirrorLuns luns in lunces)
                            {
                                //var sel = obj;
                                var avolume = new TextBox();
                                var bvolume = new TextBox();
                                var cvolume = new TextBox();
                                int lunsId = luns.Id;
                                avolume.Text = luns.AVolume;
                                bvolume.Text = luns.BVolume;
                                cvolume.Text = luns.CVolume;

                                if (cvlm != null && (bvlm != null && (avlm != null && (avlm.Text == avolume.Text && bvlm.Text == bvolume.Text && cvlm.Text == cvolume.Text))))
                                {
                                    Facade.DeleteGlobalMirrorLunsById(lunsId);
                                }
                            }
                        }
                    }
                    if (txtD != null) txtD.Enabled = false;
                    if (lblvalue != null) lblvalue.Text = "";
                    if (txtD != null) txtD.Text = string.Empty;
                    if (txtE != null)
                    {
                        txtE.Enabled = false;
                        txtE.Text = string.Empty;
                    }
                    if (txtF != null)
                    {
                        txtF.Enabled = false;
                        txtF.Text = string.Empty;
                    }
                }
            }
            if (SaveRep.Text == "Save")
            {
                SaveRep.Enabled = _chkcount != 0;
            }
        }

        protected void BtnDiscoverClick(object sender, EventArgs e)
        {
            DiscoverLuns();
            if (Session["GlobalMirror"] != null)
            {
                btnDiscover.Enabled = false;
                EditList();
                SaveRep.Enabled = true;
            }
        }

        private void DisableDSCLI()
        {
            txtDSCLIHostname.Enabled = false;
            txtDSCLIServerIP.Enabled = false;
            txtSSHUserID.Enabled = false;
            txtSSHPassword.Enabled = false;
        }

        private void DisableHMCPRServer()
        {
            txtPRMgtConsoleIP.Enabled = false;
            txtPRUserName.Enabled = false;
            txtPRPassword.Enabled = false;
        }

        private void DisableHMCDRServer()
        {
            txtDRMgtConsoleIP.Enabled = false;
            txtDRUserName.Enabled = false;
            txtDRPassword.Enabled = false;
        }

        private void ServerDetails()
        {
            if (ddlServer.SelectedValue != "0")
            {
                var server = Facade.GetServerById(CurrentEntity.Id > 0 ? CurrentEntity.DSCLIServerId : ddlServer.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtDSCLIHostname.Text = server.Name;
                    txtDSCLIServerIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtSSHUserID.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                   // txtSSHPassword.Attributes["value"] = server.SSHPassword;

                    txtSSHPassword.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                    txtSSHPassword.Attributes["value"] = Utility.getHashKeyByString(txtSSHPassword.Text, hdfStaticGuid.Value);
                

                }

                DisableDSCLI();
            }
            else
            {
                DisableDSCLI();
                txtDSCLIHostname.Text = string.Empty;
                txtDSCLIServerIP.Text = string.Empty;
                txtSSHUserID.Text = string.Empty;
                txtSSHPassword.Attributes["value"] = string.Empty;
            }
        }

        private void PrserverDetails()
        {
            if (ddlPrServer.SelectedValue != "00")
            {
                var server = Facade.GetServerById(CurrentEntity.Id > 0 ? CurrentEntity.HMCPRServerId : ddlPrServer.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtPRMgtConsoleIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtPRUserName.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                   // txtPRPassword.Attributes["value"] = server.SSHPassword;

                    txtPRPassword.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                    txtPRPassword.Attributes["value"] = Utility.getHashKeyByString(txtPRPassword.Text, hdfStaticGuid.Value);
                }

                DisableHMCPRServer();
            }
            else
            {
                txtPRMgtConsoleIP.Text = string.Empty;
                txtPRUserName.Text = string.Empty;
                txtPRPassword.Attributes["value"] = string.Empty;
            }
        }

        private void DrserverDetails()
        {
            if (ddlDrServer.SelectedValue != "00")
            {
                var server = Facade.GetServerById(CurrentEntity.Id > 0 ? CurrentEntity.HMCDRServerId : ddlDrServer.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtDRMgtConsoleIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtDRUserName.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                    //txtDRPassword.Attributes["value"] = server.SSHPassword;

                    txtDRPassword.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                    txtDRPassword.Attributes["value"] = Utility.getHashKeyByString(txtDRPassword.Text, hdfStaticGuid.Value);
                }
                DisableHMCDRServer();
            }
            else
            {
                txtDRMgtConsoleIP.Text = string.Empty;
                txtDRUserName.Text = string.Empty;
                txtDRPassword.Attributes["value"] = string.Empty;
            }
        }

        private void Enable()
        {
            lvprconfiguration.DataSource = null;
            lvprconfiguration.DataBind();
            lvprconfiguration.Visible = false;
            txtPRMgtConsoleIP.Enabled = true;
            txtDRMgtConsoleIP.Enabled = true;
            txtPRUserName.Enabled = true;
            txtDRUserName.Enabled = true;
            txtPRPassword.Enabled = true;
            txtDRPassword.Enabled = true;
            // txtPRGMLSSIDs.Enabled = true;
            // txtDRGMLSSIDs.Enabled = true;
            txtDSCLIHostname.Enabled = true;
            txtDSCLIServerIP.Enabled = true;
            txtSSHUserID.Enabled = true;
            txtSSHPassword.Enabled = true;
            // btnSave.Enabled = true;

            txtPRMgtConsoleIP.Text = string.Empty;
            txtDRMgtConsoleIP.Text = string.Empty;
            txtPRUserName.Text = string.Empty;
            txtDRUserName.Text = string.Empty;
            txtPRPassword.Text = string.Empty;
            txtDRPassword.Text = string.Empty;
            // txtPRGMLSSIDs.Text = string.Empty;
            //txtDRGMLSSIDs.Text = string.Empty;
            txtDSCLIHostname.Text = string.Empty;
            txtDSCLIServerIP.Text = string.Empty;
            txtSSHUserID.Text = string.Empty;
            txtSSHPassword.Attributes.Add("value", "");
            txtSSHPassword.Text = string.Empty;
            txtPRStorageImgID.Text = string.Empty;
            txtDRStorageImgID.Text = string.Empty;
            txtPRWNNNo.Text = string.Empty;
            txtDRWNNNo.Text = string.Empty;
            txtPRPassword.Text = string.Empty;
            txtDRPassword.Text = string.Empty;
        }

        private void EditList()
        {
            int itemcount = lvprconfiguration.Items.Count();
            IList<GlobalMirrorLuns> getall = Facade.GetAllGlobalMirrorLuns();
            var result = from g in getall where g.GlobalMirrorId == _getId select g;

            foreach (GlobalMirrorLuns obj in result)
            {
                var sel = obj;
                var av = new TextBox();
                var bv = new TextBox();
                var cv = new TextBox();
                var dv = new TextBox();
                var ev = new TextBox();
                var fv = new TextBox();
                av.Text = sel.AVolume;
                bv.Text = sel.BVolume;
                cv.Text = sel.CVolume;
                dv.Text = sel.DVolume;
                ev.Text = sel.EVolume;
                fv.Text = sel.FVolume;

                for (int i = 0; i < itemcount; i++)
                {
                    var lblav = lvprconfiguration.Items[i].FindControl("AVolume") as Label;
                    var lblbv = lvprconfiguration.Items[i].FindControl("BVolume") as Label;
                    var lblcv = lvprconfiguration.Items[i].FindControl("CVolume") as Label;
                    var txtdv = lvprconfiguration.Items[i].FindControl("DVolume") as TextBox;
                    var txtev = lvprconfiguration.Items[i].FindControl("EVolume") as TextBox;
                    var txtfv = lvprconfiguration.Items[i].FindControl("FVolume") as TextBox;
                    var check = lvprconfiguration.Items[i].FindControl("CheckBox1") as CheckBox;
                    if (lblcv != null && (lblbv != null && (lblav != null && (lblav.Text == av.Text && lblbv.Text == bv.Text && lblcv.Text == cv.Text))))
                    {
                        if (check != null) check.Checked = true;
                        if (txtdv != null) txtdv.Enabled = true;
                        if (txtev != null) txtev.Enabled = true;
                        if (txtfv != null) txtfv.Enabled = true;
                        if (txtdv != null) txtdv.Text = dv.Text;
                        if (txtev != null) txtev.Text = ev.Text;
                        if (txtfv != null) txtfv.Text = fv.Text;
                    }
                }
            }
        }

        public bool Validation()
        {
            bool isValid = true;

            foreach (ListViewDataItem lvi in lvprconfiguration.Items)
            {
                var txt = (TextBox)lvi.FindControl("DVolume");
                var txt2 = (TextBox)lvi.FindControl("EVolume");
                var txt3 = (TextBox)lvi.FindControl("FVolume");
                var lblList1 = (Label)lvi.FindControl("lblVal1");

                lblList1.Text = string.Empty;

                var lbl1 = (Label)lvi.FindControl("AVolume");
                var lbl2 = (Label)lvi.FindControl("BVolume");
                var lbl3 = (Label)lvi.FindControl("CVolume");

                var chkSelect = (CheckBox)lvi.FindControl("CheckBox1");
                if ((((chkSelect) != null)))
                {
                    if ((chkSelect.Checked))
                    {
                        if (txt.Text == "")
                        {
                            lblList1.Text = "*";
                            isValid = false;
                            txt.Focus();
                        }

                        if (txt.Text != "")
                        {
                            _luns = new GlobalMirrorLuns
                            {
                                AVolume = lbl1.Text,
                                BVolume = lbl2.Text,
                                CVolume = lbl3.Text,
                                DVolume = txt.Text,
                                EVolume = txt2.Text,
                                FVolume = txt3.Text
                            };
                            _lunsTable.Add(_luns);
                        }
                    }
                }
            }

            return isValid;
        }

        protected void TxtSshPasswordTextChanged(object sender, EventArgs e)
        {
            Session["sshDSCLIPassword"] = txtSSHPassword.Text;
        }

        protected void TxtPrPasswordTextChanged(object sender, EventArgs e)
        {
            Session["consolePRPassword"] = txtPRPassword.Text;
        }

        protected void TxtDRPasswordTextChanged(object sender, EventArgs e)
        {
            Session["consoleDRPassword"] = txtDRPassword.Text;
        }

        protected void DdlPrServerSelectedIndexChanged(object sender, EventArgs e)
        {
            PrserverDetails();
        }

        protected void DdlDrServerSelectedIndexChanged(object sender, EventArgs e)
        {
            DrserverDetails();
        }

        protected void BtnAddConsoleIPClick(object sender, System.Web.UI.ImageClickEventArgs e)
        {
            ddlDrServer.Visible = false;
            ddlPrServer.Visible = false;
            txtPRMgtConsoleIP.Visible = true;
            txtDRMgtConsoleIP.Visible = true;
            txtPRMgtConsoleIP.Text = string.Empty;
            txtPRUserName.Text = string.Empty;
            txtPRPassword.Attributes["value"] = string.Empty;
            txtDRMgtConsoleIP.Text = string.Empty;
            txtDRUserName.Text = string.Empty;
            txtDRPassword.Attributes["value"] = string.Empty;
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void SaveRepClick(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (Page.IsValid && (Session["ReplicationConfig_token"] != null) && !CheckReplicationNameExist() && ValidateRequest("GlobalMirrorConfiguration", UserActionType.CreateReplicationComponent))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {
                    if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
                    {
                        lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
                    }
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }

                    var submitButton = (Button)sender;
                    string buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }
                    try
                    {
                        if (ValidateRequest("GlobleMirrorConfiguration", UserActionType.CreateReplicationComponent))
                        {
                            BuildEntities();
                            StartTransaction();
                            SaveEditor();
                            EndTransaction();

                            string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                 currentTransactionType));

                            SaveRep.Enabled = false;
                        }
                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, Page);
                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, Page);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, Page);
                        }
                    }
                    if (returnUrl.IsNotNullOrEmpty())
                    {

                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);

                        //Helper.Url.Redirect(returnUrl);


                        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "1");
                        Helper.Url.Redirect(secureUrl);

                    }
                    //if (returnUrl.IsNotNullOrEmpty())
                    //{
                    //    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "1");

                    //    Helper.Url.Redirect(secureUrl);
                    //}
                }
            }
        }



        private void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddGlobalMirror(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "GlobalMirror", UserActionType.CreateReplicationComponent, "The GlobalMirror Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
            }
            else
            {
                CurrentEntity.Id = CurrentGlobalMirror.Id;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateGlobalMirror(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "GlobalMirror", UserActionType.UpdateReplicationComponent, "The GlobalMirror Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        private void BuildGlobalMirrorList()
        {
            foreach (var item in lvManual.Items)
            {
                var txt = (Label)item.FindControl("DVolume");
                var txt2 = (Label)item.FindControl("EVolume");
                var txt3 = (Label)item.FindControl("FVolume");

                var lbl1 = (Label)item.FindControl("AVolume");
                var lbl2 = (Label)item.FindControl("BVolume");
                var lbl3 = (Label)item.FindControl("CVolume");

                _luns = new GlobalMirrorLuns
                {
                    AVolume = lbl1.Text,
                    BVolume = lbl2.Text,
                    CVolume = lbl3.Text,
                    DVolume = txt.Text,
                    EVolume = txt2.Text,
                    FVolume = txt3.Text
                };
                CurrentEntity.GlobalMirrorLuns.Add(_luns);
            }
        }

        private void BuildEntities()
        {
            if (Session["GlobalMirror"] != null)
            {
                CurrentEntity = (GlobalMirror)Session["GlobalMirror"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;

            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;

            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);

            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);

            CurrentEntity.DSCLIServerId = Convert.ToInt32(ddlServer.SelectedValue);

            CurrentEntity.HMCPRServerId = Convert.ToInt32(ddlPrServer.SelectedValue);

            CurrentEntity.HMCDRServerId = Convert.ToInt32(ddlDrServer.SelectedValue);

            CurrentEntity.PRLSSID = txtPrLssid.Text.Trim();

            CurrentEntity.LSSIDRange = txtLSSIDrange.Text.Trim();

            CurrentEntity.SessionId = txtsessionid.Text.Trim();

            CurrentEntity.ReverseSessionId = txtReverseSessionId.Text.Trim();

            CurrentEntity.PRStorageImageId = txtPRStorageImgID.Text.Trim();

            CurrentEntity.DRStorageImageId = txtDRStorageImgID.Text.Trim();

            CurrentEntity.PRWWN = txtPRWNNNo.Text.Trim();

            CurrentEntity.DRWWN = txtDRWNNNo.Text.Trim();

            CurrentEntity.IsManual = true;

            CurrentEntity.DSCLIPath = txtDSCLIPath.Text.Trim();

            BuildGlobalMirrorList();
        }

        protected void DdlServerSelectedIndexChanged(object sender, EventArgs e)
        {
            ServerDetails();
        }

        private void DiscoverLuns()
        {
            //  IList<GlobalMirrorLuns> globalMirrorLunses = new List<GlobalMirrorLuns>();

            //if (txtDSCLIServerIP.Text != string.Empty && txtSSHUserID.Text != string.Empty &&
            //    txtSSHPassword.Text != string.Empty &&
            //    txtPRMgtConsoleIP.Text != string.Empty && txtPRUserName.Text != string.Empty &&
            //    txtPRPassword.Text != string.Empty &&
            //    txtDRMgtConsoleIP.Text != string.Empty && txtDRUserName.Text != string.Empty &&
            //    txtDRPassword.Text != string.Empty)
            ////&&txtPRGMLSSIDs.Text!=string.Empty)
            //{
            //    if (Session["sshDSCLIPassword"] == null || Session["consolePRPassword"] == null || Session["consoleDRPassword"] == null)
            //    {
            //        Session["sshDSCLIPassword"] = txtSSHPassword.Text;
            //        Session["consolePRPassword"] = txtPRPassword.Text;
            //        Session["consoleDRPassword"] = txtDRPassword.Text;
            //    }

            //    var sshInfo = new SSHInfo(txtDSCLIServerIP.Text, txtSSHUserID.Text, txtSSHPassword.Text);
            //    var objDSCLIInfoPR = new DSCLIInfo(txtPRMgtConsoleIP.Text, txtPRUserName.Text, txtPRPassword.Text,
            //                                       @"/opt/ibm/dscli/dscli");
            //    var objDSCLIInfoDR = new DSCLIInfo(txtDRMgtConsoleIP.Text, txtDRUserName.Text, txtDRPassword.Text,
            //                                       @"/opt/ibm/dscli/dscli");

            //    var objStorageInfo = new StorageInfo();
            //    if (objStorageInfo.Discover(objDSCLIInfoPR, objDSCLIInfoDR, sshInfo))
            //    {
            //        txtPRStorageImgID.Text = objStorageInfo.PRStorageImageId;
            //        txtDRStorageImgID.Text = objStorageInfo.DRStorageImageId;
            //        txtPRWNNNo.Text = objStorageInfo.PRWWN;
            //        txtDRWNNNo.Text = objStorageInfo.DRWWN;
            //        var objLun = new LunInfo();
            //        if (objLun.Discover(objStorageInfo, objDSCLIInfoPR, objDSCLIInfoDR, sshInfo, txtLSSIDrange.Text))
            //        {
            //            string[] aVloumeArray = objLun.LunA;
            //            string[] bVloumeArray = objLun.LunB;
            //            string[] cVloumeArray = objLun.LunC;

            //            //var aVloumeArray = new string[objLun.LunA.Length];
            //            //aVloumeArray = objLun.LunA;
            //            //var bVloumeArray = new string[objLun.LunA.Length];
            //            //bVloumeArray = objLun.LunB;
            //            //var cVloumeArray = new string[objLun.LunA.Length];
            //            //cVloumeArray = objLun.LunC;

            //            for (var i = 0; i < aVloumeArray.Length; i++)
            //            {
            //                var globalMirror = new GlobalMirrorLuns
            //                {
            //                    AVolume = aVloumeArray[i],
            //                    BVolume = bVloumeArray[i],
            //                    CVolume = cVloumeArray[i]
            //                };

            //                globalMirrorLunses.Add(globalMirror);
            //            }

            //            if (globalMirrorLunses.Count > 0)
            //            {
            //                lvprconfiguration.Visible = true;
            //                lvprconfiguration.DataSource = globalMirrorLunses;
            //                lvprconfiguration.DataBind();
            //                btnDiscover.Enabled = false;
            //                // btnSave.Enabled = true;

            //                txtSSHPassword.Attributes["value"] = Session["sshDSCLIPassword"].ToString();
            //                txtPRPassword.Attributes["value"] = Session["consolePRPassword"].ToString();
            //                txtDRPassword.Attributes["value"] = Session["consoleDRPassword"].ToString();
            //            }
            //        }
            //        else
            //        {
            //            MessageBody.Visible = true;
            //            MessageBody.Attributes["class"] = "message error no-margin no-bottom-margin";
            //            MessageViewer.Text = "Error Occured while Discovering Luns Information";
            //        }
            //  }
            //    else
            //    {
            //        MessageBody.Visible = true;
            //        MessageBody.Attributes["class"] = "message error no-margin no-bottom-margin";
            //        MessageViewer.Text = "Error Occured while Discovering Storage Information";
            //    }
            //}
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        #region ManualDiscover

        private void BindManualDiscover()
        {
            CurrentEntity = (GlobalMirror)Session["GlobalMirror"];

            if (CurrentEntity != null)
            {
                IList<GlobalMirrorLuns> lunses = Facade.GetGlobalMirrorLunsByGlobalMirrorId(CurrentEntity.Id);

                if (Session["PreviousItem"] == null)
                {
                    Session["PreviousItem"] = lunses;
                }

                if (lunses != null)
                {
                    lvManual.DataSource = lunses;
                    lvManual.DataBind();
                }
            }
        }

        private void ShowList()
        {
            lvManual.DataSource = SaveLuns;
            lvManual.DataBind();
        }

        private void EnableManual()
        {
            txtPRStorageImgID.Text = string.Empty;
            txtDRStorageImgID.Text = string.Empty;
            txtPRWNNNo.Text = string.Empty;
            txtDRWNNNo.Text = string.Empty;
            txtPRStorageImgID.Enabled = true;
            txtDRStorageImgID.Enabled = true;
            txtPRWNNNo.Enabled = true;
            txtDRWNNNo.Enabled = true;
            PanelManual.Visible = true;
            btnManual.Enabled = false;
            btnDiscover.Enabled = false;
        }

        protected void BtnManualClick(object sender, EventArgs e)
        {
            EnableManual();
            SaveRep.Enabled = true;
            ShowList();
        }

        protected void LvManualItemInserting(object sender, ListViewInsertEventArgs e)
        {
            if (Session["GlobalMirror"] != null)
            {
                CurrentEntity = (GlobalMirror)Session["GlobalMirror"];
            }

            bool isValid = true;
            bool isAVolumeDup = false;
            bool isBVolumeDup = false;
            bool isCVolumeDup = false;
            bool isDVolumeDup = false;
            bool isEVolumeDup = false;
            bool isFVolumeDup = false;

            var getIdLuns = new GlobalMirrorLuns();
            var aVolume = (TextBox)e.Item.FindControl("AVolume");
            var bVolume = (TextBox)e.Item.FindControl("BVolume");
            var cVolume = (TextBox)e.Item.FindControl("CVolume");
            var dVolume = (TextBox)e.Item.FindControl("DVolume");
            var eVolume = (TextBox)e.Item.FindControl("EVolume");
            var fVolume = (TextBox)e.Item.FindControl("FVolume");

            var lblA = (Label)e.Item.FindControl("lblAVolume");
            var lblB = (Label)e.Item.FindControl("lblBVolume");
            var lblC = (Label)e.Item.FindControl("lblCVolume");
            var lblD = (Label)e.Item.FindControl("lblDVolume");
            var lblE = (Label)e.Item.FindControl("lblEVolume");
            var lblF = (Label)e.Item.FindControl("lblFVolume");

            var AVolumeDup = string.Empty;
            var BVolumeDup = string.Empty;
            var CVolumeDup = string.Empty;
            var DVolumeDup = string.Empty;
            var EVolumeDup = string.Empty;
            var FVolumeDup = string.Empty;

          

            if (aVolume.Text == "")
            {
                lblA.Text = "*";
                lblA.Visible = true;
                isValid = false;
                
            }
            if (bVolume.Text == "")
            {
                lblB.Text = "*";
                lblB.Visible = true;
                isValid = false;
            }
            if (cVolume.Text == "")
            {
                lblC.Text = "*";
                lblC.Visible = true;
                isValid = false;
            }
            if (dVolume.Text == "")
            {
                lblD.Text = "*";
                lblD.Visible = true;
                isValid = false;
            }
            if (eVolume.Text == "")
            {
                lblE.Text = "";
                lblE.Visible = true;
               
            }
            if (fVolume.Text == "")
            {
                lblF.Text = "";
                lblD.Visible = true;
              
            }
            for (int i = 0; i < lvManual.Items.Count(); i++)
            {
                AVolumeDup = ((Label)(lvManual.Items[i].FindControl("AVolume"))).Text;
                BVolumeDup = ((Label)(lvManual.Items[i].FindControl("BVolume"))).Text;
                CVolumeDup = ((Label)(lvManual.Items[i].FindControl("CVolume"))).Text;
                DVolumeDup = ((Label)(lvManual.Items[i].FindControl("DVolume"))).Text;
                EVolumeDup = ((Label)(lvManual.Items[i].FindControl("EVolume"))).Text;
                FVolumeDup = ((Label)(lvManual.Items[i].FindControl("FVolume"))).Text;

                if (AVolumeDup.Equals(aVolume.Text) && AVolumeDup != string.Empty)
                {
                    lblA.Text = "Not Available";
                    isValid = false;
                    isAVolumeDup = true;
                    lblA.Visible = true;
                    
                }
                else if (!isAVolumeDup && aVolume.Text != string.Empty)
                {
                    lblA.Text = "";
                    lblA.Visible = false;
                }
                if (BVolumeDup.Equals(bVolume.Text) && BVolumeDup != string.Empty)
                {
                    lblB.Text = "Not Available"; 
                    isValid = false;
                    isBVolumeDup = true;
                    lblB.Visible = true;
                   
                }
                else if (!isBVolumeDup && bVolume.Text != string.Empty)
                {
                    lblB.Text = "";
                    lblB.Visible = false;
                }
                if (CVolumeDup.Equals(cVolume.Text) && CVolumeDup != string.Empty)
                {
                    lblC.Text = "Not Available";
                    isValid = false;
                    isCVolumeDup = true;
                    lblC.Visible = true;
                  
                }
                else if (!isCVolumeDup && cVolume.Text != string.Empty)
                {
                    lblC.Text = "";
                    lblC.Visible = false;
                }
                if (DVolumeDup.Equals(dVolume.Text) && DVolumeDup != string.Empty)
                {
                    lblD.Text = "Not Available";
                    isValid = false;
                    isDVolumeDup = true;
                    lblD.Visible = true;
                   
                }
                else if (!isDVolumeDup && dVolume.Text != string.Empty)
                {
                    lblD.Text = "";
                    lblD.Visible = false;
                }
                if (EVolumeDup.Equals(eVolume.Text) && EVolumeDup != string.Empty)
                {
                    lblE.Text = "Not Available";
                    isValid = false;
                    isEVolumeDup = true;
                    lblE.Visible = true;
                   
                }
                else if (!isEVolumeDup && eVolume.Text != string.Empty)
                {
                    lblE.Text = "";
                    lblE.Visible = false;
                }
                if (FVolumeDup.Equals(fVolume.Text) && FVolumeDup != string.Empty)
                {
                    lblF.Text = "Not Available";
                    isValid = false;
                    isFVolumeDup = true;
                    lblF.Visible = true;
          
                }
                else if (!isFVolumeDup && fVolume.Text != string.Empty)
                {
                    lblF.Text = "";
                    lblF.Visible = false;
                }
            }
           
                if (isValid)
                {
                    getIdLuns.GlobalMirrorId = CurrentEntity.Id;
                    getIdLuns.AVolume = aVolume.Text;
                    getIdLuns.BVolume = bVolume.Text;
                    getIdLuns.CVolume = cVolume.Text;
                    getIdLuns.DVolume = dVolume.Text;
                    getIdLuns.EVolume = eVolume.Text;
                    getIdLuns.FVolume = fVolume.Text;
                    getIdLuns.CreatorId = 1;
                    if (SaveRep.Text == "Update")
                    {
                        Facade.AddGlobalMirrorLuns(getIdLuns);
                        BindManualDiscover();
                    }
                    if (SaveRep.Text == "Save")
                    {
                        SaveLuns.Add(getIdLuns);

                        if (SaveLuns != null)
                        {
                            ShowList();
                        }
                    }
                }
        }

        protected void LvManualUpdating(object sender, ListViewUpdateEventArgs e)
        {
            if (Session["Globalmirror"] != null)
            {
                CurrentEntity = (GlobalMirror)Session["GlobalMirror"];
            }

            var lblUpdateId = (lvManual.Items[e.ItemIndex].FindControl("Id")) as Label;
            if (lblUpdateId != null)
            {
                lunsnew.Id = Convert.ToInt32(lblUpdateId.Text);
            }
            var txtAvolume = (lvManual.Items[e.ItemIndex].FindControl("AVolume")) as TextBox;

            if (txtAvolume != null && txtAvolume.Text != "")
            {
                lunsnew.AVolume = txtAvolume.Text.Trim();
            }
            var txtBvolume = (lvManual.Items[e.ItemIndex].FindControl("BVolume")) as TextBox;
            if (txtBvolume != null && txtBvolume.Text != "")
            {
                lunsnew.BVolume = txtBvolume.Text.Trim();
            }

            var txtCVolume = (lvManual.Items[e.ItemIndex].FindControl("CVolume")) as TextBox;
            if (txtCVolume != null && txtCVolume.Text != "")
            {
                lunsnew.CVolume = txtCVolume.Text.Trim();
            }
            var txtDVolume = (lvManual.Items[e.ItemIndex].FindControl("DVolume")) as TextBox;
            if (txtDVolume != null && txtDVolume.Text != "")
            {
                lunsnew.DVolume = txtDVolume.Text.Trim();
            }
            var txtEVolume = (lvManual.Items[e.ItemIndex].FindControl("EVolume")) as TextBox;
            if (txtEVolume != null && txtEVolume.Text != "")
            {
                lunsnew.EVolume = txtEVolume.Text.Trim();
            }
            var txtFVolume = (lvManual.Items[e.ItemIndex].FindControl("FVolume")) as TextBox;
            if (txtFVolume != null && txtFVolume.Text != "")
            {
                lunsnew.FVolume = txtFVolume.Text.Trim();
            }
            lunsnew.IsActive = 1;
            if (SaveRep.Text == "Save")
            {
                SaveLuns.Insert(e.ItemIndex, lunsnew);
                SaveLuns.RemoveAt(e.ItemIndex + 1);
                lvManual.EditIndex = -1;
                ShowList();
            }
            else
            {
                lunsnew.GlobalMirrorId = CurrentEntity.Id;
                Facade.UpdateGlobalMirrorLuns(lunsnew);
                lvManual.EditIndex = -1;
                BindManualDiscover();
            }
        }

        protected void LvManualDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lblDeleteId = (lvManual.Items[e.ItemIndex].FindControl("Id")) as Label;
            if (lblDeleteId != null)
            {
                int id = lblDeleteId.Text.ToInteger();
                if (SaveRep.Text == "Save")
                {
                    SaveLuns.RemoveAt(e.ItemIndex);
                    ShowList();
                }
                else
                {
                    Facade.DeleteGlobalMirrorLunsById(id);
                    BindManualDiscover();
                }
            }
        }

        protected void LvManualEditing(object sender, ListViewEditEventArgs e)
        {
            if (SaveRep.Text == "Save")
            {
                lvManual.EditIndex = e.NewEditIndex;
                lvManual.DataSource = SaveLuns;
                lvManual.DataBind();
            }
            else
            {
                lvManual.EditIndex = e.NewEditIndex;

                CurrentEntity = (GlobalMirror)Session["GlobalMirror"];

                if (CurrentEntity != null)
                {
                    IList<GlobalMirrorLuns> lunses = Facade.GetGlobalMirrorLunsByGlobalMirrorId(CurrentEntity.Id);

                    if (lunses != null)
                    {
                        lvManual.DataSource = lunses;
                        lvManual.DataBind();
                    }
                }
            }
        }

        protected void LvManualCanceling(object sender, ListViewCancelEventArgs e)
        {
            lvManual.EditIndex = -1;
            if (SaveRep.Text == "Save")
            {
                ShowList();
            }
            else
            {
                if (CurrentEntity != null)
                {
                    IList<GlobalMirrorLuns> lunses = Facade.GetGlobalMirrorLunsByGlobalMirrorId(CurrentEntity.Id);

                    if (lunses != null)
                    {
                        lvManual.DataSource = lunses;
                        lvManual.DataBind();
                    }
                }
            }
        }

        #endregion ManualDiscover
    }
}