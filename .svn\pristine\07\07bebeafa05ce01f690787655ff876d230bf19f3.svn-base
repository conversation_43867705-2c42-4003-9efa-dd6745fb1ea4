﻿/*===================================
===== Ram Bhomkar - UI Developer ====
===================================*/

/* --- LOADING [css.bootstrap.min] from App_Themes/CPTheme/bootstrap.min.css */
@import 'bootstrap.min.css';

/* --- LOADING [css.font-awesome.min] from App_Themes/CPTheme/core/font-icon/font-awesome.min.css */
@import 'core/font-icon/font-awesome.min.css';

/* --- LOADING [css.glyphicons_regular] from App_Themes/CPTheme/core/font-icon/glyphicons_regular.css */
@import 'core/font-icon/glyphicons_regular.css';

/* --- LOADING [less.variables] from App_Themes/CPTheme/core/less/variables.less */
@import 'core/less/variables.less';

/* --- LOADING [less.mixins] from App_Themes/CPTheme/core/less/mixins.less */
@import 'core/less/mixins.less';

/* --- LOADING [less.scaffolding] from App_Themes/CPTheme/core/less/scaffolding.less */
@import 'core/less/scaffolding.less';

/* --- LOADING [less.helpers] from App_Themes/CPTheme/core/less/helpers.less */
@import 'core/less/helpers.less';

/* --- LOADING [less.layout] from App_Themes/CPTheme/core/less/layout.less */
@import 'core/less/layout.less';

/* --- LOADING [less.menus] from App_Themes/CPTheme/core/less/menus.less */
@import 'core/less/menus.less';

/* --- LOADING [less.widgets] from App_Themes/CPTheme/core/less/widgets.less */
@import 'core/less/widgets.less';

/* --- LOADING [less.forms] from App_Themes/CPTheme/core/less/forms.less */
@import 'core/less/forms.less';

/* --- LOADING [less.alerts] from App_Themes/CPTheme/core/less/alerts.less */
@import 'core/less/alerts.less';

/* --- LOADING [less.buttons] from App_Themes/CPTheme/core/less/buttons.less */
@import 'core/less/buttons.less';

/* --- LOADING [less.responsive] from App_Themes/CPTheme/core/less/responsive.less */
@import 'core/less/responsive.less';

/* --- LOADING [less.breadcrumb] from App_Themes/CPTheme/core/less/breadcrumb.less */
@import 'core/less/breadcrumb.less';

/* --- LOADING [less.tabs] from App_Themes/CPTheme/core/less/tabs.less */
@import 'core/less/tabs.less';

/* --- LOADING [less.accordions] from App_Themes/CPTheme/core/less/accordions.less */
@import 'core/less/accordions.less';

/* --- LOADING [less.gallery] from App_Themes/CPTheme/core/less/gallery.less
@import 'core/less/gallery.less';*/

/* --- LOADING [less.modals] from App_Themes/CPTheme/core/less/modals.less */
@import 'core/less/modals.less';

/* --- LOADING [less.labels] from App_Themes/CPTheme/core/less/labels.less */
@import 'core/less/labels.less';

/* --- LOADING [less.tooltips] from App_Themes/CPTheme/core/less/tooltips.less */
@import 'core/less/tooltips.less';

/* --- LOADING [less.widget-stats] from App_Themes/CPTheme/core/less/widget-stats.less */
@import 'core/less/widget-stats.less';

/* --- LOADING [less.progress-bars] from App_Themes/CPTheme/core/less/progress-bars.less */
@import 'core/less/progress-bars.less';

/* --- LOADING [less.timeline] from from App_Themes/CPTheme/core/less/less/timeline.less */
@import 'core/less/timeline.less';

/* --- LOADING [less.widget-collapsible] from App_Themes/CPTheme/core/less/widget-collapsible.less */
@import 'core/less/widget-collapsible.less';

/* --- LOADING [less.template-options] from App_Themes/CPTheme/core/less/template-options.less */
@import 'core/less/template-options.less';

/* --- LOADING [less.choose] from App_Themes/CPTheme/core/less/choose.less */
@import 'core/less/choose.less';

/*Form Less Started */

/* --- LOADING [css.uniform.default] from App_Themes/CPTheme/uniform.default.css */
@import 'uniform.default.css';

/* --- LOADING [less.uniformjs] from App_Themes/CPTheme/core/less/uniformjs.less */
@import 'core/less/uniformjs.less';

/* --- LOADING [css.bootstrap-select] from App_Themes/CPTheme/bootstrap-select.css */
@import 'bootstrap-select.css';

/* --- LOADING [less.bootstrap-select] from App_Themes/CPTheme/core/less/bootstrap-select.less */
@import 'core/less/bootstrap-select.less';

/* --- LOADING [css.picto] from App_Themes/CPTheme/core/font-icon/picto.css */
@import 'core/font-icon/picto.css';

@import "notyfy.theme.default.css";

/*@import "jquery.notyfy.css";*/

/* For CommandCenter Page */
/* --- LOADING [less.CommandCenter] from App_Themes/CPTheme/core/less/CommandCenter.less */
@import 'core/less/CommandCenter.less';

/* --- LOADING [css.Telerik] from App_Themes/CPTheme/Telerik.css */
@import 'Telerik.css';