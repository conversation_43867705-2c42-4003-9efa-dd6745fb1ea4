﻿using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI;
using Telerik.Web.UI.HtmlChart;

namespace CP.UI.Controls
{
    public partial class ServicesNeverRecovered : BaseControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        public override void PrepareView()
        {
            BindBarChart();

        }

        private void BindBarChart()
        {
            try
            {
                IList<BusinessServiceRTOInfo> _servicelist = new List<BusinessServiceRTOInfo>();
                IList<BusinessServiceRTOInfo> _Functionlist = new List<BusinessServiceRTOInfo>();
                BusinessServiceRTOInfo obj = new BusinessServiceRTOInfo();
                IList<BSDRReadyDaily> _comp = Facade.GetAllBSDRReady_ByMonths();
                int cnt = 0;
                lblcurrentmnth.Text = DateTime.Now.ToString("MMMM") + "-" + DateTime.Now.Year;

                for (int i = DateTime.Now.Month - 5; i <= DateTime.Now.Month; i++)
                {
                    AxisItem _item = new AxisItem();
                    _item.LabelText = new DateTime(2016, i, 1).ToString("MMM");
                    BarChart.PlotArea.XAxis.Items.Add(_item);
                    obj = new BusinessServiceRTOInfo();
                    obj = Facade.GetMonthlyNotRecoveredServices("Service", i);
                    obj.Months = new DateTime(2016, i, 1).ToString("MMM");
                    obj.MonthID = i;
                    _servicelist.Add(obj);

                    obj = new BusinessServiceRTOInfo();
                    obj = Facade.GetMonthlyNotRecoveredServices("Function", i);
                    obj.Months = new DateTime(2016, i, 1).ToString("MMM");
                    obj.MonthID = i;
                    _Functionlist.Add(obj);
                }

                BindCurrentMonthDetails(_servicelist, _Functionlist);




                if (_Functionlist != null && _Functionlist.Count > 0)
                {
                    ColumnSeries _donutSeries2 = new ColumnSeries();
                    _donutSeries2.TooltipsAppearance.DataFormatString = "{0}";
                    _donutSeries2.LabelsAppearance.Visible = false;
                    _donutSeries2.LabelsAppearance.Position = BarColumnLabelsPosition.Center;
                    _donutSeries2.LabelsAppearance.Color = Color.White;
                    _donutSeries2.LabelsAppearance.TextStyle.FontSize = 9;

                    foreach (var a in _Functionlist)
                    {
                        SeriesItem _item2 = new SeriesItem();
                        _item2.YValue = Convert.ToDecimal(a.ServiceCount);
                        _item2.Name = "Functions";
                        _item2.BackgroundColor = System.Drawing.ColorTranslator.FromHtml("#77b1d6");
                        _donutSeries2.Items.Add(_item2);
                    }
                    _donutSeries2.Stacked = true;
                    BarChart.PlotArea.Series.Add(_donutSeries2);
                }

                if (_servicelist != null && _servicelist.Count > 0)
                {
                    ColumnSeries _donutSeries = new ColumnSeries();
                    _donutSeries.TooltipsAppearance.DataFormatString = "{0}";
                    _donutSeries.LabelsAppearance.Visible = false;
                    _donutSeries.LabelsAppearance.Position = BarColumnLabelsPosition.Center;
                    _donutSeries.LabelsAppearance.Color = System.Drawing.Color.White;
                    _donutSeries.LabelsAppearance.TextStyle.FontSize = 9;
                    


                    foreach (var a in _servicelist)
                    {

                        SeriesItem _item1 = new SeriesItem();

                        _item1.YValue = Convert.ToDecimal(a.ServiceCount);
                        _item1.Name = "Service";
                        _item1.BackgroundColor = System.Drawing.ColorTranslator.FromHtml("#95c761");
                        _donutSeries.Items.Add(_item1);




                    }
                    _donutSeries.Stacked = true;
                    BarChart.PlotArea.Series.Add(_donutSeries);


                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting Services Never Recovered Count ServicNeverRecoverd Control", ex);
                ExceptionManager.Manage(cpException);
            }

        }

        private void BindCurrentMonthDetails(IList<BusinessServiceRTOInfo> _servicelist, IList<BusinessServiceRTOInfo> _Functionlist)
        {
            var _currentMonthDetails = (from BusinessServiceRTOInfo sd in _servicelist
                                        where sd.MonthID == DateTime.Now.Month
                                        select sd);

            if (_currentMonthDetails != null && _currentMonthDetails.Count() > 0)
            {
                BusinessServiceRTOInfo Obj = _currentMonthDetails.FirstOrDefault();
                lblServiceCount.Text = Obj.ServiceCount.ToString();
               
            }


            var _funcCount = (from BusinessServiceRTOInfo sd in _Functionlist
                                        where sd.MonthID == DateTime.Now.Month
                                        select sd);

            if (_funcCount != null && _funcCount.Count() > 0)
            {
                BusinessServiceRTOInfo Obj = _funcCount.FirstOrDefault();
                lblFunctionCount.Text = Obj.ServiceCount.ToString();

            }
        }



        private void BindCurrentMonthDetails(IList<BSDRReadyDaily> _comp)
        {

            var _currentMonthDetails = (from BSDRReadyDaily sd in _comp
                                        where sd.MonthID == DateTime.Now.Month
                                        select sd);

            if (_currentMonthDetails != null && _currentMonthDetails.Count() > 0)
            {
                BSDRReadyDaily Obj = _currentMonthDetails.FirstOrDefault();
                lblServiceCount.Text = Obj.BServiceCount.ToString();
                lblFunctionCount.Text = Obj.FunctionCount.ToString();
            }


        }
    }
}