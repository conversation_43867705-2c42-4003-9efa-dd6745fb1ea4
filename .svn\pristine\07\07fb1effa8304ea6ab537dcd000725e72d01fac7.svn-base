﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class DatabaseSubstituteAuthenticateDataAccess : BaseDataAccess, IDatabaseAuthenticateDataAccess
    {
         #region Constructors

        public DatabaseSubstituteAuthenticateDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SubstituteAuthentication> CreateEntityBuilder<SubstituteAuthentication>()
        {
            return (new DatabaseSubstituteAuthenticateBuilder()) as IEntityBuilder<SubstituteAuthentication>;
        }

        #endregion Constructors

        #region Methods

        #region DB Config 

        SubstituteAuthentication IDatabaseAuthenticateDataAccess.AddDatabaeSubstituteAuthn(SubstituteAuthentication objSubsAuth)
        {
            try
            {
                const string sp = "DatabaseAuth_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);


                    Database.AddInParameter(cmd, "iSqlplus", DbType.AnsiString, objSubsAuth.Sqlplus);
                    Database.AddInParameter(cmd, "iCredential", DbType.Int32, objSubsAuth.Credential);
                    Database.AddInParameter(cmd, "iRole", DbType.AnsiString, objSubsAuth.Role);
                    Database.AddInParameter(cmd, "iBaseDatabaseId", DbType.Int32, objSubsAuth.BaseDatabaseId);
                    Database.AddInParameter(cmd, "iIsSubstituteAuth", DbType.Int32, objSubsAuth.IsSubstituteAuth);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        objSubsAuth = reader.Read() ? CreateEntityBuilder<SubstituteAuthentication>().BuildEntity(reader, objSubsAuth) : null;
                    }

                    //if (objSubsAuth == null)
                    //{
                    //    int returnCode = GetReturnCodeFromParameter(cmd);

                    //    switch (returnCode)
                    //    {
                    //        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                    //            {
                    //                throw new ArgumentException("Substitute Authentication already exists. Please specify another Substitute Authentication.");
                    //            }
                    //        default:
                    //            {
                    //                throw new SystemException("An unexpected error has occurred while creating this Substitute Authentication.");
                    //            }
                    //    }
                    //}

                    return objSubsAuth;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting Substitute Authentication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        SubstituteAuthentication IDatabaseAuthenticateDataAccess.UpdateDatabaseAuthn(SubstituteAuthentication objSubsAuth)
        {
            try
            {
                const string sp = "DatabaseAuth_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, "iId", DbType.Int32, objSubsAuth.Id);
                    Database.AddInParameter(cmd, "iSqlplus", DbType.AnsiString, objSubsAuth.Sqlplus);
                    Database.AddInParameter(cmd, "iCredential", DbType.Int32, objSubsAuth.Credential);
                    Database.AddInParameter(cmd, "iRole", DbType.AnsiString, objSubsAuth.Role);
                    Database.AddInParameter(cmd, "iBaseDatabaseId", DbType.Int32, objSubsAuth.BaseDatabaseId);
                    Database.AddInParameter(cmd, "iIsSubstituteAuth", DbType.Int32, objSubsAuth.IsSubstituteAuth);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        objSubsAuth = reader.Read() ? CreateEntityBuilder<SubstituteAuthentication>().BuildEntity(reader, objSubsAuth) : null;
                    }

                    if (objSubsAuth == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Substitute Authentication already exists. Please specify another Substitute Authentication.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this Substitute Authentication.");
                                }
                        }
                    }

                    return objSubsAuth;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating Substitute Authentication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        IList<SubstituteAuthentication> IDatabaseAuthenticateDataAccess.GetDatabaseAuthById(int id)
        {
            try
            {
                const string sp = "DatabaseAuth_GetByd";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SubstituteAuthentication>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISubstituteAuthenticateDataAccess.GetAllByServerId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<SubstituteAuthentication> IDatabaseAuthenticateDataAccess.GetAllDatabaseAuthn()
        {
            try
            {
                const string sp = "DatabaseAuth_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SubstituteAuthentication>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISubstituteAuthenticateDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        SubstituteAuthentication IDatabaseAuthenticateDataAccess.GetAllByDatabaseSubstituteAuthn(int id)
        {
            try
            {
                const string sp = "DatabaseAuth_GetBydbId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SubstituteAuthentication>()).BuildEntity(reader, new SubstituteAuthentication());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISubstituteAuthenticateDataAccess.GetAllByServerId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion DB Config


        #region Node Config
       
        SubstituteAuthentication IDatabaseAuthenticateDataAccess.AddDatabaeSubstituteAuthn_Node(SubstituteAuthentication objSubsAuth)
        {
            try
            {
                const string sp = "DatabaseAuth_Create_Node";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);


                    Database.AddInParameter(cmd, "iSqlplus", DbType.AnsiString, objSubsAuth.Sqlplus);
                    Database.AddInParameter(cmd, "iCredential", DbType.Int32, objSubsAuth.Credential);
                    Database.AddInParameter(cmd, "iRole", DbType.AnsiString, objSubsAuth.Role);
                    Database.AddInParameter(cmd, "iBaseDatabaseId", DbType.Int32, objSubsAuth.BaseDatabaseId);
                    Database.AddInParameter(cmd, "iIsSubstituteAuth", DbType.Int32, objSubsAuth.IsSubstituteAuth);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        objSubsAuth = reader.Read() ? CreateEntityBuilder<SubstituteAuthentication>().BuildEntity(reader, objSubsAuth) : null;
                    }

                    if (objSubsAuth == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Substitute Authentication already exists. Please specify another Substitute Authentication.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this Substitute Authentication.");
                                }
                        }
                    }

                    return objSubsAuth;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting Substitute Authentication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        SubstituteAuthentication IDatabaseAuthenticateDataAccess.UpdateDatabaseAuthn_Node(SubstituteAuthentication objSubsAuth)
        {
            try
            {
                const string sp = "DatabaseAuth_Update_Node";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, "iId", DbType.Int32, objSubsAuth.Id);
                    Database.AddInParameter(cmd, "iSqlplus", DbType.AnsiString, objSubsAuth.Sqlplus);
                    Database.AddInParameter(cmd, "iCredential", DbType.Int32, objSubsAuth.Credential);
                    Database.AddInParameter(cmd, "iRole", DbType.AnsiString, objSubsAuth.Role);
                    Database.AddInParameter(cmd, "iBaseDatabaseId", DbType.Int32, objSubsAuth.BaseDatabaseId);
                    Database.AddInParameter(cmd, "iIsSubstituteAuth", DbType.Int32, objSubsAuth.IsSubstituteAuth);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        objSubsAuth = reader.Read() ? CreateEntityBuilder<SubstituteAuthentication>().BuildEntity(reader, objSubsAuth) : null;
                    }

                    if (objSubsAuth == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Substitute Authentication already exists. Please specify another Substitute Authentication.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this Substitute Authentication.");
                                }
                        }
                    }

                    return objSubsAuth;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating Substitute Authentication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        IList<SubstituteAuthentication> IDatabaseAuthenticateDataAccess.GetDatabaseAuthById_Node(int id)
        {
            try
            {
                const string sp = "DatabaseAuth_GetByd_Node";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SubstituteAuthentication>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISubstituteAuthenticateDataAccess.GetAllByServerId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<SubstituteAuthentication> IDatabaseAuthenticateDataAccess.GetAllDatabaseAuthn_Node()
        {
            try
            {
                const string sp = "DatabaseAuth_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SubstituteAuthentication>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISubstituteAuthenticateDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        SubstituteAuthentication IDatabaseAuthenticateDataAccess.GetAllByDatabaseSubstituteAuthn_Node(int id)
        {
            try
            {
                const string sp = "DatabaseAuth_GetBydbId_Node";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SubstituteAuthentication>()).BuildEntity(reader, new SubstituteAuthentication());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISubstituteAuthenticateDataAccess.GetAllByServerId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IDatabaseAuthenticateDataAccess.DeleteDatabaseAuthn_Node(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DatabaseAuth_Delete_Node";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, "iId", DbType.Int32, id);
                    // Database.ExecuteNonQuery(cmd);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISubstituteAuthenticateDataAccess.DeleteById()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        #endregion Node Config



        bool IDatabaseAuthenticateDataAccess.DeleteDatabaseAuthn(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DatabaseAuth_Delete";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, "iId", DbType.Int32, id);
                   // Database.ExecuteNonQuery(cmd);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISubstituteAuthenticateDataAccess.DeleteById()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

       #endregion
    }
}
