﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class Sql2000ServiceBuilder : IEntityBuilder<SqlServer2000Service>
    {
        IList<SqlServer2000Service> IEntityBuilder<SqlServer2000Service>.BuildEntities(IDataReader reader)
        {
            var mssqlservice = new List<SqlServer2000Service>();
            while (reader.Read())
            {
                mssqlservice.Add(((IEntityBuilder<SqlServer2000Service>)this).BuildEntity(reader,
                    new SqlServer2000Service()));
            }
            return (mssqlservice.Count > 0) ? mssqlservice : null;
        }

        SqlServer2000Service IEntityBuilder<SqlServer2000Service>.BuildEntity(IDataReader reader,
            SqlServer2000Service mssql2000service)
        {
            //const int FLD_ID = 0;
            //const int FLD_GROUPID = 1;
            //const int FLD_SERVER = 2;
            //const int FLD_SERVICENAME = 3;
            //const int FLD_STATUS = 4;
            //const int FLD_STARTUP_MODE = 5;
            //const int FLD_CREATORID = 6;
            //const int FLD_CREATEDATE = 7;
            //const int FLD_UPDATORID = 8;
            //const int FLD_UPDATEDATE = 9;

            //mssql2000service.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //mssql2000service.GroupId = reader.IsDBNull(FLD_GROUPID) ? 0 : reader.GetInt32(FLD_GROUPID);
            //mssql2000service.Server = reader.IsDBNull(FLD_SERVER) ? 0 : reader.GetInt32(FLD_SERVER);
            //mssql2000service.ServiceName = reader.IsDBNull(FLD_SERVICENAME) ? string.Empty : reader.GetString(FLD_SERVICENAME);
            //mssql2000service.Status = reader.IsDBNull(FLD_STATUS) ? string.Empty : reader.GetString(FLD_STATUS);
            //mssql2000service.Start_Mode = reader.IsDBNull(FLD_STARTUP_MODE) ? string.Empty : reader.GetString(FLD_STARTUP_MODE);
            //mssql2000service.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //mssql2000service.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);
            //mssql2000service.UpdatorId = reader.IsDBNull(FLD_UPDATORID) ? 0 : reader.GetInt32(FLD_UPDATORID);
            //mssql2000service.UpdateDate = reader.IsDBNull(FLD_UPDATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_UPDATEDATE);

            //Fields in bcms_sql2000_service table on 23/07/2013 :Id, GroupId, Server, ServiceName, Status, Start_Mode, CreatorId, CreateDate, UpdatorId, UpdateDate

            mssql2000service.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            mssql2000service.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            mssql2000service.Server = Convert.IsDBNull(reader["Server"]) ? 0 : Convert.ToInt32(reader["Server"]);
            mssql2000service.ServiceName = Convert.IsDBNull(reader["ServiceName"])
                ? string.Empty
                : Convert.ToString(reader["ServiceName"]);
            mssql2000service.Status = Convert.IsDBNull(reader["Status"])
                ? string.Empty
                : Convert.ToString(reader["Status"]);
            mssql2000service.StartMode = Convert.IsDBNull(reader["Start_Mode"])
                ? string.Empty
                : Convert.ToString(reader["Start_Mode"]);
            mssql2000service.CreatorId = Convert.IsDBNull(reader["CreatorId"])
                ? 0
                : Convert.ToInt32(reader["CreatorId"]);
            mssql2000service.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);
            mssql2000service.UpdatorId = Convert.IsDBNull(reader["UpdatorId"])
                ? 0
                : Convert.ToInt32(reader["UpdatorId"]);
            mssql2000service.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);

            return mssql2000service;
        }
    }
}