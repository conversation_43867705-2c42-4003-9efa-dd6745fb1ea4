﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class SRMVmwareMonitorBuilder : IEntityBuilder<SRMVmwareMonitor>
    {
        #region IEntityBuilder<SRMVmwareMonitor> Members

        IList<SRMVmwareMonitor> IEntityBuilder<SRMVmwareMonitor>.BuildEntities(IDataReader reader)
        {
            var vmware = new List<SRMVmwareMonitor>();

            while (reader.Read())
            {
                vmware.Add(((IEntityBuilder<SRMVmwareMonitor>)this).BuildEntity(reader, new SRMVmwareMonitor()));
            }

            return (vmware.Count > 0) ? vmware : null;
        }

        SRMVmwareMonitor IEntityBuilder<SRMVmwareMonitor>.BuildEntity(IDataReader reader, SRMVmwareMonitor vmware)
        {
            //Fields in bcms_vmware_monitor_status table on 24/07/2013 : Id, GroupId, PowerStatePR, CurrentSnapshotPR, UpdationTimeStampPR, PowerStateDR, CurrentSnapshotDR, UpdationTimeStampDR, CurrentDataLag, IsActive, CreatorId, CreateDate

            vmware.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

            vmware.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            
            vmware.VCenterVersionPR = Convert.IsDBNull(reader["VCenterVersionPR"])? string.Empty : Convert.ToString(reader["VCenterVersionPR"]);
            vmware.VCenterVersionDR = Convert.IsDBNull(reader["VCenterVersionDR"])? string.Empty : Convert.ToString(reader["VCenterVersionDR"]);

            vmware.VCenterBuildPR = Convert.IsDBNull(reader["VCenterBuildPR"])? string.Empty : Convert.ToString(reader["VCenterBuildPR"]);
            vmware.VCenterBuildDR = Convert.IsDBNull(reader["VCenterBuildDR"])? string.Empty : Convert.ToString(reader["VCenterBuildDR"]);
           
            vmware.SRMVersionPR = Convert.IsDBNull(reader["SRMVersionPR"])? string.Empty : Convert.ToString(reader["SRMVersionPR"]);
            vmware.SRMVersionDR = Convert.IsDBNull(reader["SRMVersionDR"])? string.Empty : Convert.ToString(reader["SRMVersionDR"]);
           
            vmware.SRMBuildPR = Convert.IsDBNull(reader["SRMBuildPR"])? string.Empty : Convert.ToString(reader["SRMBuildPR"]);
            vmware.SRMBuildDR = Convert.IsDBNull(reader["SRMBuildDR"])? string.Empty : Convert.ToString(reader["SRMBuildDR"]);

            vmware.ProtectionGroupsNamePR = Convert.IsDBNull(reader["ProtectionGroupsNamePR"])? string.Empty : Convert.ToString(reader["ProtectionGroupsNamePR"]);
            vmware.ProtectionGroupsNameDR = Convert.IsDBNull(reader["ProtectionGroupsNameDR"])? string.Empty : Convert.ToString(reader["ProtectionGroupsNameDR"]);

            vmware.ProtectionGroupsNameTypePR = Convert.IsDBNull(reader["ProtectionGroupsNameTypePR"])? string.Empty : Convert.ToString(reader["ProtectionGroupsNameTypePR"]);
            vmware.ProtectionGroupsNameTypeDR = Convert.IsDBNull(reader["ProtectionGroupsNameTypeDR"])? string.Empty : Convert.ToString(reader["ProtectionGroupsNameTypeDR"]);

            vmware.ProtectionGroupsNameStatePR = Convert.IsDBNull(reader["ProtectionGroupsNameStatePR"])? string.Empty : Convert.ToString(reader["ProtectionGroupsNameStatePR"]);
            vmware.ProtectionGroupsNameStateDR = Convert.IsDBNull(reader["ProtectionGroupsNameStateDR"])? string.Empty : Convert.ToString(reader["ProtectionGroupsNameStateDR"]);

            vmware.ProtectionGroupNameVMCountPR = Convert.IsDBNull(reader["ProtectionGroupNameVMCountPR"])? string.Empty : Convert.ToString(reader["ProtectionGroupNameVMCountPR"]);
            vmware.ProtectionGroupNameVMCountDR = Convert.IsDBNull(reader["ProtectionGroupNameVMCountDR"])? string.Empty : Convert.ToString(reader["ProtectionGroupNameVMCountDR"]);

            vmware.RecoveryPlanNamePR = Convert.IsDBNull(reader["RecoveryPlanNamePR"])? string.Empty : Convert.ToString(reader["RecoveryPlanNamePR"]);
            vmware.RecoveryPlanNameDR = Convert.IsDBNull(reader["RecoveryPlanNameDR"])? string.Empty : Convert.ToString(reader["RecoveryPlanNameDR"]);

            vmware.RecoveryPlanStatePR = Convert.IsDBNull(reader["RecoveryPlanStatePR"])? string.Empty : Convert.ToString(reader["RecoveryPlanStatePR"]);
            vmware.RecoveryPlanStateDR = Convert.IsDBNull(reader["RecoveryPlanStateDR"])? string.Empty : Convert.ToString(reader["RecoveryPlanStateDR"]);

            vmware.RecoveryPlanHistoryNamePR = Convert.IsDBNull(reader["RecoveryPlanHistoryNamePR"])? string.Empty : Convert.ToString(reader["RecoveryPlanHistoryNamePR"]);
            vmware.RecoveryPlanHistoryNameDR = Convert.IsDBNull(reader["RecoveryPlanHistoryNameDR"])? string.Empty : Convert.ToString(reader["RecoveryPlanHistoryNameDR"]);

            vmware.RecoveryPlanHistoryLastRunDatePR = Convert.IsDBNull(reader["RecoveryPlanHistoryLastRDatePR"])? string.Empty : Convert.ToString(reader["RecoveryPlanHistoryLastRDatePR"]);
            vmware.RecoveryPlanHistoryLastRunDateDR = Convert.IsDBNull(reader["RecoveryPlanHistoryLastRDateDR"])? string.Empty : Convert.ToString(reader["RecoveryPlanHistoryLastRDateDR"]);

            vmware.RecoveryPlanHistoryStatePR = Convert.IsDBNull(reader["RecoveryPlanHistoryStatePR"]) ? string.Empty : Convert.ToString(reader["RecoveryPlanHistoryStatePR"]);
            vmware.RecoveryPlanHistoryStateDR = Convert.IsDBNull(reader["RecoveryPlanHistoryStateDR"]) ? string.Empty : Convert.ToString(reader["RecoveryPlanHistoryStateDR"]);

            vmware.RecoveryPlanHistoryTotalTimePR = Convert.IsDBNull(reader["RecoveryPlanHistoryTotalTimePR"]) ? string.Empty : Convert.ToString(reader["RecoveryPlanHistoryTotalTimePR"]);
            vmware.RecoveryPlanHistoryTotalTimeDR = Convert.IsDBNull(reader["RecoveryPlanHistoryTotalTimeDR"]) ? string.Empty : Convert.ToString(reader["RecoveryPlanHistoryTotalTimeDR"]);

            vmware.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);

            return vmware;
        }

        #endregion IEntityBuilder<SRMVmwareMonitor> Members
    }
}