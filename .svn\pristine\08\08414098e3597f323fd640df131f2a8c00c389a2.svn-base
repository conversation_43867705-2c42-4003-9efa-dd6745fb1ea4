﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.SybaseWithSRSMonitoring
{
    internal sealed class SybaseWithSRSMonitorDataAccess : BaseDataAccess, ISybaseWithSRSDataAccess
    {
        #region Constructors

        public SybaseWithSRSMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SybaseWithSRSMonitor> CreateEntityBuilder<SybaseWithSRSMonitor>()
        {
            return (new SybaseWithSRSBuilder()) as IEntityBuilder<SybaseWithSRSMonitor>;
        }

        #endregion Constructors

        #region Methods

        SybaseWithSRSMonitor ISybaseWithSRSDataAccess.GetCurrentSybaseWithSRSReplicatonByInfraId(int infraId)
        {
            try
            {
                const string sp = "SybaseSRSCurrmoni_byInfraID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<SybaseWithSRSMonitor>()).BuildEntity(reader, new SybaseWithSRSMonitor())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithSRSDataAccess. GetCurrentSybaseWithSRSReplicatonByInfraId (" + infraId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        SybaseWithSRSMonitor ISybaseWithSRSDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "SybaseSRSMonitor_GETBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<SybaseWithSRSMonitor>()).BuildEntity(reader, new SybaseWithSRSMonitor())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithSRSDataAccess.GetByInfraObjectId(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<SybaseWithSRSMonitor> ISybaseWithSRSDataAccess.GetSybaseWithSRSRepliHourlyByInfraObjId(int gid)
        {
            try
            {
                if (gid < 1)
                {
                    throw new ArgumentNullException("gid");
                }

                const string sp = "SybaseSRSGetByHrs_InfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, gid);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SybaseWithSRSMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithSRSMonitor. GetCurrentSybaseWithSRSReplicatonByInfraId (" + gid +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<SybaseWithSRSMonitor> ISybaseWithSRSDataAccess.GetSybaseWithSRSByDate(int InfraObjectId, string startDate, string endDate)
        {
            try
            {
                const string sp = "SybaseSRSMoni_logs_GetBYDate";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    startDate = Convert.ToDateTime(startDate).ToString("dd-MM-yy");
                    endDate = Convert.ToDateTime(endDate).ToString("dd-MM-yy");
#endif
                  
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.AnsiString, endDate);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SybaseWithSRSMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithSRSRepliDataAccess.GetSybaseWithSRSByDate(" + InfraObjectId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion

    }
}
