﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;


namespace CP.UI.Controls
{

    public partial class VeritasClusterDetailedmonitoring : System.Web.UI.UserControl
    {
        public string stimg12, stimg;
        Facade _facade = new Facade();
        int isClusterInfra = 0;
        protected void Page_Load(object sender, EventArgs e)
        {
            int infraid = Convert.ToInt32(Session["VeritasClusterInfraid"]);
            //isClusterInfra = Convert.ToInt32(Session["isCluster"]);
            
            //if (isClusterInfra == 1)
            //{
            if (infraid > 0)
            {
                dvClusterdetails.Visible = true;
                BindClusterInfo(infraid);
                BindClusterStatus(infraid);
                BindClusterResourceGroup(infraid);
                //}
                //else
                //{
                //    dvClusterdetails.Visible = false;

                //}
            }
           



        }

        public void BindClusterInfo(int id)
        {
            lvClusterInfo.DataSource = _facade.GetVeritasClusterMonitorSta(id);
            lvClusterInfo.DataBind();

        }

        public void BindClusterStatus(int id)
        {
            var ClusterNode = _facade.GetVeritasClusterNodeMonitorSta(id);

            if (ClusterNode != null)
            {
                


                var table3 = new DataTable();
                table3.Columns.Add("Id");
                table3.Columns.Add("ClusterName");
                table3.Columns.Add("ClusterNodeName");
                table3.Columns.Add("ClusterNodeState");

                //int i = 1;
                for (int i = 0; i < ClusterNode.Count; i++)
                {

                    DataRow dr = table3.NewRow();

                    dr["Id"] = ClusterNode[i].Id;
                    dr["ClusterName"] = ClusterNode[i].ClusterName;
                    dr["ClusterNodeName"] = ClusterNode[i].ClusterNodeName;
                    dr["ClusterNodeState"] = ClusterNode[i].ClusterNodeState;


                    for (int J = i; J < ClusterNode.Count; J++)
                    {
                        if (ClusterNode[i].ClusterName == ClusterNode[J].ClusterName)
                        {

                            dr["ClusterNodeName"] = dr["ClusterNodeName"] + "<hr/><span class='node_padl20'></span>" + ClusterNode[J].ClusterNodeName;

                            stimg = ClusterNode[J].ClusterNodeState;
                            if (stimg == "RUNNING")
                            {
                                stimg12 = "running_padl20";
                            }
                            else if (stimg == "STOPPED")
                            {
                                stimg12 = "stopicon_padl20";
                            }
                            dr["ClusterNodeState"] = "<span class='" + stimg12 + "'></span>" + dr["ClusterNodeState"] + "<hr/><span class='" + stimg12 + "'></span>" + ClusterNode[J].ClusterNodeState;
                            table3.Rows.Add(dr);
                        }
                        else
                        {
                            DataRow Ndr = table3.NewRow();
                            Ndr["Id"] = ClusterNode[J].Id;
                            Ndr["ClusterName"] = ClusterNode[J].ClusterName;
                            Ndr["ClusterNodeName"] = ClusterNode[J].ClusterNodeName;
                            Ndr["ClusterNodeState"] = ClusterNode[J].ClusterNodeState;
                            table3.Rows.Add(Ndr);
                        }
                    }
                }

                lvClusterStatus.DataSource = table3;

                lvClusterStatus.DataBind();
            }

        }

        public void BindClusterResourceGroup(int id)
        {

            string strTable = "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white'>";

            var lstResourceGroup = _facade.GetVeritasClusterGroupSummarySta(id).GroupBy(u => u.ClusterGroupName).Select(grp => grp.ToList()).ToList();
            if (lstResourceGroup.Count == 0)
            {
                dvResourceGroupDetails.Visible = false;
                lblEmpty1.Visible = true;
            }
            else
            {
                strTable += "<tr><th style='width:5%;'>Sr. No</th><th style='width:45%;'>Group Name</th><th  style='width:40%;'>Node Name</th><th  style='width:10%;'>Status</th></tr>";
                int i = 1;
                if (lstResourceGroup.Count > 0)
                {
                    foreach (var item in lstResourceGroup)
                    {

                        if (item != null)
                        {
                            strTable += "<tr>";
                            strTable += "<td style='width:5%;'>" + i.ToString() + "</td>";
                            strTable += "<td  style='width:32%;'>" + item.FirstOrDefault().ClusterGroupName + "</td>";
                            strTable += "<td  style='width:50%;padding:0px;' colspan='" + item.Count + "'><table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white' style='margin-bottom:0px;border:none'>";

                            foreach (var grpItem in item)
                            {
                                stimg = grpItem.ClusterGroupNodeState;
                                if (stimg == "ONLINE")
                                {
                                    stimg12 = "online_padl20";
                                }
                                else if (stimg == "OFFLINE")
                                {
                                    stimg12 = "offline_padl20";
                                }
                                strTable += "<tr>";
                                strTable += "<td> <span class='node_padl20'></span>" + grpItem.ClusterGroupNodeName + "</td>";
                                strTable += "<td style='width:31%;text-align:center'> <span class='" + stimg12 + "'></span>" + grpItem.ClusterGroupNodeState + "</td>";
                                strTable += "</tr>";

                            }
                            i++;

                            strTable += "</table></td>";

                            strTable += "</tr>";

                        }
                    }
                    strTable += "</table>";

                    dvResourceGroupDetails.InnerHtml = strTable;
                    lblEmpty1.Visible = false;
                }

            }
        }
    }
}