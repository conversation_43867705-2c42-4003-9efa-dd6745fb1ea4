﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using Gios.Pdf;
using SpreadsheetGear;
using CP.Helper;
using System.IO;
using System.Globalization;
using log4net;
using CP.Common.Shared;
using System.Collections;

namespace CP.UI.Controls
{
    public partial class DRReadyRPT : BaseControl
    {
        IRange cells = null;
        IWorkbookSet workbookSet = null;
        String ssFile = string.Empty;
        IWorkbook templateWorkbook = null;
        IWorksheet templateWorksheet = null;
        ArrayList Alist = new ArrayList();
        Server prserver;
        Server drserver;

        private readonly ILog _logger = LogManager.GetLogger(typeof(DRReadyRPT));

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void ddlBussService_SelectedIndexChanged(object sender, EventArgs e)
        {

        }


        public override void PrepareView()
        {
            try
            {
                lblMsg.Text = string.Empty;
                lblMsg.Visible = false;

                //IList<BusinessService> businessServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);

                //IList<BusinessService> businessServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);

                //if (businessServiceList != null)
                //{
                //    businessServiceList = businessServiceList.OrderBy(a => a.Name).ToList();
                //    ddlBussService.DataSource = businessServiceList;
                //    ddlBussService.DataTextField = "Name";
                //    ddlBussService.DataValueField = "Id";
                //    ddlBussService.DataBind();
                //    ddlBussService.Items.Insert(0, new ListItem("- Select Application -", "0"));
                //    //ddlBussService.Items.Insert(0, "ALL");
                //}
                //else
                //{
                //    ddlBussService.Items.Insert(0, new ListItem("No Data Found", "0"));
                //}

                IList<BusinessService> businessServiceList = Facade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
                businessServiceList = businessServiceList.OrderBy(a => a.Name).ToList();
                if (businessServiceList != null)
                {
                    ddlBussService.DataSource = businessServiceList;
                    ddlBussService.DataTextField = "Name";
                    ddlBussService.DataValueField = "Id";
                    ddlBussService.DataBind();
                    ddlBussService.Items.Insert(0, new ListItem("- Select Application -", "0"));
                    //ddlBusinessService.Items.Insert(0, "All");

                    // ddlBussService.Items.Insert(0, new ListItem("All", "0"));

                }
                else
                {
                    ddlBussService.Items.Insert(0, new ListItem("No Business Service Found", "0"));
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In PrepareView Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In PrepareView Method, InnerException Message " + ex.InnerException.Message);

            }
        }

        protected void btnview_Click(object sender, EventArgs e)
        {
            try
            {
                // ExcelReport();
                PrepareExcelReport();
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In btnview_Click Event, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In btnview_Click Event, InnerException Message " + ex.InnerException.Message);

            }
        }

        //private void ExcelReport()
        //{
        //    try
        //    {
        //        _logger.Info("ExcelReport Method Execution Start.");

        //        if (Convert.ToInt32(ddlBussService.SelectedItem.Value) <= 0)
        //        {
        //            lblMsg.Text = "Select Application";
        //            lblMsg.Visible = true;
        //            return;
        //        }
        //        lblMsg.Text = string.Empty;
        //        lblMsg.Visible = false;

        //        int bsid = Convert.ToInt32(ddlBussService.SelectedItem.Value);

        //        string BSName = ddlBussService.SelectedItem.Text;

        //        //IList<InfraObject> infraObjectList = Facade.GetInfraObjectByBusinessServiceId(bsid);

        //        IList<InfraObject> infraObjectList = Facade.GetInfraObjectByBusinessServiceIdUserId(bsid, LoggedInUserId);

        //        if (infraObjectList != null && infraObjectList.Count > 0)
        //        {
        //            IWorkbookSet workbookSet = null;
        //            String ssFile = string.Empty;
        //            IWorkbook templateWorkbook = null;
        //            IWorksheet templateWorksheet = null;
        //            IRange _cells = null;

        //            workbookSet = Factory.GetWorkbookSet();
        //            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
        //            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
        //            templateWorksheet = templateWorkbook.Worksheets[0];

        //            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
        //            IWorksheet reportWorksheet = null;
        //            IWorksheet lastWorksheet = null;

        //            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
        //            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

        //            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
        //            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

        //            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
        //            reportWorkbook.Worksheets["Sheet1"].Delete();

        //            _cells = reportWorksheet.Cells;
        //            reportWorksheet.WindowInfo.DisplayGridlines = false;
        //            reportWorksheet.Name = "DRReadyReport";

        //            _cells["A1"].ColumnWidth = 7;

        //            _cells["E3"].Formula = "DR Ready Report";
        //            _cells["E3"].HorizontalAlignment = HAlign.Center;
        //            _cells["E3"].Font.Bold = true;
        //            _cells["B3:G6"].Interior.Color = Color.FromArgb(79, 129, 189);
        //            _cells["D3"].Font.Bold = true;
        //            _cells["D3"].ColumnWidth = 30;
        //            _cells["D3"].HorizontalAlignment = HAlign.Center;
        //            _cells["D3"].VerticalAlignment = VAlign.Top;
        //            _cells["B3:G3"].Font.Size = 11;
        //            _cells["B5:G8"].Font.Size = 10;
        //            _cells["B3:G8"].Font.Color = Color.White;
        //            _cells["B3:G8"].Font.Name = "Cambria";


        //            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
        //            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 640, 10, 120, 13);

        //            string strlogo = LoggedInUserCompany.CompanyLogoPath;

        //            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
        //            {
        //                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 370, 10, 85, 38);
        //            }


        //            reportWorksheet.Cells["A1:F1"].RowHeight = 42;
        //            reportWorksheet.Cells["A2:F2"].RowHeight = 20;



        //            var dateTime = DateTime.Now.ToString("MM-dd-yyyy HH:mm:ss");
        //            _cells["B5"].Formula = "Report Generated Time";
        //            _cells["B5"].Font.Bold = true;
        //            _cells["B5"].HorizontalAlignment = HAlign.Left;

        //            _cells["C5"].Formula = ":  " + Utility.Formatdate(dateTime);
        //            _cells["C5"].Font.Bold = true;
        //            _cells["C5"].HorizontalAlignment = HAlign.Left;

        //            int row = 8;
        //            int i = 1;

        //            _cells["B" + row.ToString()].Formula = "Sr.No.";
        //            _cells["B" + row.ToString()].Font.Bold = true;
        //            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Center;
        //            _cells["B8:G8"].Interior.Color = Color.FromArgb(79, 129, 189);

        //            IRange range = reportWorksheet.Cells["B8:G8"];
        //            IBorder border = range.Borders[BordersIndex.EdgeBottom];
        //            border.LineStyle = LineStyle.Continous;
        //            border.Color = Color.Black;
        //            border.Weight = BorderWeight.Thin;

        //            _cells["C" + row.ToString()].Formula = "BusinessService Name";
        //            _cells["C" + row.ToString()].Font.Bold = true;
        //            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            _cells["C" + row.ToString()].VerticalAlignment = VAlign.Center;

        //            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
        //            productionlog.WrapText = true;

        //            _cells["D" + row.ToString()].Formula = "BusinessFunction Name";
        //            _cells["D" + row.ToString()].Font.Bold = true;
        //            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            _cells["D" + row.ToString()].VerticalAlignment = VAlign.Center;

        //            _cells["E" + row.ToString()].Formula = "Infraobject Name";
        //            _cells["E" + row.ToString()].Font.Bold = true;
        //            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            _cells["E" + row.ToString()].VerticalAlignment = VAlign.Center;


        //            _cells["F" + row.ToString()].Formula = "DR Ready";
        //            _cells["F" + row.ToString()].Font.Bold = true;
        //            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            _cells["F" + row.ToString()].VerticalAlignment = VAlign.Center;

        //            _cells["G" + row.ToString()].Formula = "Reason";
        //            _cells["G" + row.ToString()].Font.Bold = true;
        //            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            _cells["G" + row.ToString()].VerticalAlignment = VAlign.Center;


        //            row++;
        //            int dataCount = 0;
        //            int xlRow = 9;

        //            foreach (InfraObject infraobject in infraObjectList)
        //            {
        //                dataCount++;
        //                int column = 0;
        //                string[] xlColumn = { "B", "C", "D", "E", "F", "G" };
        //                xlRow++;

        //                string ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx + ":" + "G" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;

        //                _cells[ndx].Formula = i.ToString();
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 23;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Center;
        //                i++;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();


        //                BusinessService BService = Facade.GetBusinessServiceById(infraobject.BusinessServiceId);
        //                if (BService != null && !string.IsNullOrEmpty(BService.Name))
        //                {
        //                    _cells[ndx].Formula = BService.Name;
        //                }
        //                else
        //                {
        //                    _cells[ndx].Formula = "NA";
        //                }

        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 40;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;


        //                ndx = xlColumn[column] + row.ToString();
        //                BusinessFunction bf = Facade.GetBusinessFunctionById(infraobject.BusinessFunctionId);
        //                if (bf != null && !string.IsNullOrEmpty(bf.Name))
        //                {
        //                    _cells[ndx].Formula = bf.Name;
        //                }
        //                else
        //                {
        //                    _cells[ndx].Formula = "NA";
        //                }
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 40;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(infraobject.Name) ? infraobject.Name : "NA"; //rp.InfraObjectName != string.Empty ? rp.InfraObjectName : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 40;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                _cells[ndx].WrapText = true;
        //                column++;


        //                string _DRReason = string.Empty;
        //                bool _isDRready = false;
        //                bool _DRWorkFlowIsAttach = false;
        //                bool _latestSOSBDrillStatus = false;
        //                bool _drReadyExecute = false;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 23;


        //                #region IsDRReady

        //                if (infraobject.DRReady)
        //                    _isDRready = true;
        //                else
        //                    _DRReason = "DR Not Configured.";

        //                #endregion IsDRReady

        //                #region DRReady Workflow Attached

        //                if (DRWorkFlowIsAttach(infraobject.Id))
        //                {
        //                    _DRWorkFlowIsAttach = true;
        //                }
        //                else
        //                {

        //                    _DRReason = _DRReason + " DR WorkFlow Not Configured.";
        //                }

        //                #endregion DRReady Workflow Attached

        //                #region Last SO/SB

        //                //IList<GroupWorkflow> lstGrpWorkflow = Facade.GetAllGroupWorkflowByInfraObjectId(infraDetails.Id);   //GroupWorkflowDataAccess.GetAllByInfraId(infra.Id);
        //                //if (lstGrpWorkflow != null && lstGrpWorkflow.Count > 0)
        //                //{
        //                //    var sosbFound = lstGrpWorkflow.FirstOrDefault(x => x.ActionType == 1 || x.ActionType == 2);
        //                //    if (sosbFound != null)
        //                //    {
        //                //        IList<ParallelWorkflowActionResult> SOSBPWAction = Facade.GetParallelActionResultSOSBByInfra(infraDetails.Id);  //ParallelWorkflowActionResultDataAccess.GetParallelActionResultSOSBByInfra(infra.Id);
        //                //        if (SOSBPWAction != null && SOSBPWAction.Count > 0)
        //                //        {
        //                //            var UnsucceAction = SOSBPWAction.Where(x => x.Status.ToString().Equals("Error") || x.Status.ToString().Equals("Aborted")).FirstOrDefault();
        //                //            if (UnsucceAction != null)
        //                //            {
        //                //                string wfName = string.Empty;
        //                //                wfName = Facade.ParallelGroupWorkflowGetByID(UnsucceAction.ParallelGroupWorkflowId).WorkflowName;  //ParallelGroupWorkflowDataAccess.GetById(UnsucceAction.ParallelGroupWorkflowId).WorkflowName;
        //                //                //strNotSOSB += "<tr><td style='width:19%;' class='tdword-wrap' title='" + infra.Name.Split('/')[0] + "'>" + infra.Name.Split('/')[0] + "</td><td style='width:15%;'>" + funName + "</td><td style='width:38%;'>" + wfName + "</td><td style='width:18%;' class='tdword-wrap'  title='" + UnsucceAction.WorkflowActionName + "'>  " + UnsucceAction.WorkflowActionName + "</td><td style='width:12%;'>  " + errorMessage + "<b>Start time : </b>" + UnsucceAction.StartTime + "<br> <b>End Time   : </b>" + UnsucceAction.EndTime + "<br><b> Reason   : </b>" + UnsucceAction.Message.Replace('@', ' ') + "'></span> </td></tr>";

        //                //                if (string.IsNullOrEmpty(_DRReason))
        //                //                    _DRReason = " InfraObject Drill Failure Details For WorkFlow " + wfName + " Workflow Action " + UnsucceAction.WorkflowActionName + " Failed, Message  " + UnsucceAction.Message.Replace('@', ' ');
        //                //            }
        //                //            else
        //                //                _latestSOSBDrillStatus = true; // _latestDrillCount++;
        //                //        }
        //                //        else
        //                //        {
        //                //            if (string.IsNullOrEmpty(_DRReason))
        //                //                _DRReason = " SO/SB WorkFlow Configured But Drill Not Done.";
        //                //            //strNotSOSB += "<tr><td style='width:19%;' class='tdword-wrap' title='" + infra.Name.Split('/')[0] + "'>" + infra.Name + "</td><td style='width:15%;'>" + funName + "</td><td style='width:38%;'>SO/SB workFlow configured but drill not done.</td><td style='width:15%;'>NA</td><td style='width:12%;'>NA</td></tr>";
        //                //        }
        //                //    }
        //                //    else
        //                //    {
        //                //        if (string.IsNullOrEmpty(_DRReason))
        //                //            _DRReason = " SSO/SB WorkFlow Not Configured.";
        //                //        //strNotSOSB += "<tr><td style='width:19%;' class='tdword-wrap' title='" + infra.Name.Split('/')[0] + "'>" + infra.Name + "</td><td style='width:15%;'>" + funName + "</td><td style='width:38%;'>SO/SB WorkFlow Not Configured.</td><td style='width:15%;'>NA</td><td style='width:12%;'>NA</td></tr>";
        //                //    }
        //                //}
        //                //else
        //                //{
        //                //    if (string.IsNullOrEmpty(_DRReason))
        //                //        _DRReason = " WorkFlow Not Configured.";
        //                //    //   strNotSOSB += "<tr><td style='width:19%;' class='tdword-wrap' title='" + infra.Name.Split('/')[0] + "'>" + infra.Name + "</td><td style='width:15%;'>" + funName + "</td><td style='width:38%;'> WorkFlow Not Configured.</td><td style='width:15%;'>NA</td><td style='width:12%;'>NA</td></tr>";
        //                //}

        //                #endregion Last SO/SB

        //                #region LatestDrillExecution

        //                // IList<Heatmap> lstHeatmap = Facade.GetHeatMapByBusinessServiceIdandInfraObjectId(infraDetails.BusinessServiceId, infraDetails.Id);  //HeatmapDataAccess.GetByBSIdAndInfraId(objService.Id, infra.Id);
        //                ParallelWorkflowActionResult PWAction = Facade.GetActionResultByInfra(infraobject.Id, "");
        //                InfrascheduleWfdeatils _schuwfdt = Facade.GetScheduledActionResultByInfra(infraobject.Id);   //ServiceProtectionDataAccess.GetScheduledActionResultByInfra(infra.Id);

        //                List<ParallelWorkflowActionResult> lstActionResult = new List<ParallelWorkflowActionResult>();

        //                //Heatmap heatmap = null;
        //                //if (lstHeatmap != null)
        //                //    heatmap = lstHeatmap.FirstOrDefault(x => x.EntityId == infraDetails.DRServerId && x.HeatmapType.Equals("Server"));

        //                if (_schuwfdt != null)
        //                    lstActionResult.Add(new ParallelWorkflowActionResult()
        //                    {
        //                        InfraObjectId = _schuwfdt.InfraObjectId,
        //                        WorkflowActionName = _schuwfdt.CurrentActionName,
        //                        EndTime = _schuwfdt.EndTime,
        //                        StartTime = _schuwfdt.StartTime,
        //                        //Status =   _schuwfdt.Status,                            
        //                        //Status = (WorkflowActionStatus)(string.IsNullOrEmpty(_schuwfdt.Status) ? WorkflowActionStatus.Undefined : Enum.Parse(typeof(WorkflowActionStatus), _schuwfdt.Status, true)),
        //                        Status = (WorkflowActionStatus)(Enum.Parse(typeof(WorkflowActionStatus), _schuwfdt.Status, true)),
        //                        ParallelGroupWorkflowId = _schuwfdt.WorkflowId,
        //                        Message = _schuwfdt.Message == string.Empty ? GetReason(infraobject.BusinessServiceId, infraobject.Id, _schuwfdt.WorkflowId, _schuwfdt.CurrentActionId) : _schuwfdt.Message
        //                    });


        //                if (PWAction != null)
        //                    lstActionResult.Add(PWAction);
        //                if (lstActionResult != null)
        //                {
        //                    lstActionResult = (List<ParallelWorkflowActionResult>)lstActionResult.OrderByDescending(x => x.EndTime).ToList();
        //                    PWAction = lstActionResult.Take(1).FirstOrDefault();
        //                }

        //                if (!DRWorkFlowIsAttach(infraobject.Id))
        //                {

        //                    _DRReason = _DRReason + " DR Workflow Not Attached.";
        //                }
        //                else if (PWAction != null) // && heatmap != null
        //                {
        //                    //if (PWAction.EndTime < heatmap.UpdateDate)
        //                    //{
        //                    //    if (heatmap.IsAffected)
        //                    //    {
        //                    //        //strNotlatestDrill += "<tr><td style='width:19%;' class='tdword-wrap' title='" + infra.Name.Split('/')[0] + "'>" + infra.Name.Split('/')[0] + "</td><td style='width:15%;'>" + funName + "</td><td style='width:38%;'>" + "DR Server is down check in Heat map " + "</td><td style='width:15%;'>NA</td><td style='width:12%;'>NA</td></tr>";
        //                    //        if (string.IsNullOrEmpty(_DRReason))
        //                    //            _DRReason = "DR Server Is Down Check In Heat Map.";
        //                    //    }
        //                    //    else
        //                    //        _drReadyExecute = true;  //_drReadyExecuteCount++;
        //                    //}
        //                    //else
        //                    //{
        //                    //if (PWAction.Status.Equals("Error"))
        //                    if (WorkflowActionStatus.Error == PWAction.Status)
        //                    {
        //                        string msgReson = !string.IsNullOrEmpty(PWAction.Message) ? PWAction.Message : "NA";
        //                        string wfName = string.Empty;

        //                        Workflow wf = Facade.GetWorkflowById(PWAction.ParallelGroupWorkflowId);
        //                        if (wf != null)
        //                            wfName = wf.Name;
        //                        _DRReason = _DRReason + " InfraObject Drill Failure Details For Parallel WorkFlow -" + wfName + " , Workflow Action - " + PWAction.WorkflowActionName + " Failed, Message - " + PWAction.Message.Replace('@', ' ');
        //                    }
        //                    else
        //                        _drReadyExecute = true;

        //                    //}
        //                }
        //                //else if (PWAction != null)
        //                //{
        //                //    if (PWAction.Status.Equals("Error"))
        //                //    {
        //                //        string msgReson = !string.IsNullOrEmpty(PWAction.Message) ? PWAction.Message : "NA";
        //                //        string wfName = string.Empty;
        //                //        //wfName = WorkflowDataAccess.GetWorkflowById(PWAction.ParallelGroupWorkflowId).Name;
        //                //        wfName = Facade.ParallelGroupWorkflowGetByID(PWAction.ParallelGroupWorkflowId).WorkflowName;
        //                //        //strNotlatestDrill += "<tr><td style='width:19%;' class='tdword-wrap'>" + infra.Name.Split('/')[0] + "</td><td style='width:15%;'>" + funName + "</td><td style='width:38%;'>" + wfName + "</td><td style='width:15%;' class='tdword-wrap'  title='" + PWAction.WorkflowActionName + "'>  " + PWAction.WorkflowActionName + "</td><td style='width:12%;'>  " + errorMessage + "<b>Start time : </b>" + PWAction.StartTime + "<br> <b>End Time   : </b>" + PWAction.EndTime + "<br><b> Reason   : </b>" + PWAction.Message.Replace('@', ' ') + "'></span> </td></tr>";
        //                //        if (string.IsNullOrEmpty(_DRReason))
        //                //            _DRReason = " InfraObject Drill Failure Details For Parallel WorkFlow " + wfName + " , Workflow Action " + PWAction.WorkflowActionName + " Failed, Message  " + PWAction.Message.Replace('@', ' ');

        //                //    }
        //                //    else
        //                //        _drReadyExecute = true; //_drReadyExecuteCount++;
        //                //}
        //                //else if (heatmap != null)
        //                //{
        //                //    if (heatmap.IsAffected)
        //                //    {
        //                //        //strNotlatestDrill += "<tr><td style='width:19%;' class='tdword-wrap'>" + infra.Name.Split('/')[0] + "</td><td style='width:15%;'>" + funName + "</td><td style='width:38%;'>" + "DR Server is down ." + "</td><td style='width:15%;'>NA</td><td style='width:12%;'>NA</td></tr>";
        //                //        if (string.IsNullOrEmpty(_DRReason))
        //                //            _DRReason = "DR Server Is Down.";
        //                //    }
        //                //    else
        //                //        _drReadyExecute = true;//_drReadyExecuteCount++;
        //                //}
        //                else if (PWAction == null)
        //                {
        //                    _DRReason = _DRReason + " Workflow Configure But Not Executed.";
        //                }

        //                #endregion LatestDrillExecution


        //                if (_isDRready && _DRWorkFlowIsAttach && _drReadyExecute)
        //                {
        //                    //_cells[ndx].Formula = "YES";
        //                    _cells[ndx].Formula = "↑ " + "UP";
        //                    _cells[ndx].Font.Color = Color.Green;
        //                }
        //                else
        //                {
        //                    // _cells[ndx].Formula = "NO";
        //                    _cells[ndx].Formula = "↓ " + "Down";
        //                    _cells[ndx].Font.Color = Color.Red;
        //                }


        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;


        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(_DRReason) ? _DRReason : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 40;
        //                _cells[ndx].WrapText = true;

        //                if (!string.IsNullOrEmpty(_DRReason))
        //                {
        //                    _cells[ndx].Font.Color = Color.Red;
        //                }
        //                else
        //                {
        //                    _cells[ndx].Font.Color = Color.Black;
        //                }
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;

        //                column++;

        //                row++;
        //            }

        //            int finalCount = dataCount + 10;
        //            _cells["B" + finalCount].Formula = "NA : Not Available";
        //            _cells["B" + finalCount].HorizontalAlignment = HAlign.Center;
        //            _cells["B" + finalCount].Font.Name = "Cambria";
        //            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

        //            reportWorksheet.ProtectContents = true;
        //            OpenExcelFile(reportWorkbook, BSName);

        //        }
        //        else
        //        {
        //            _logger.Info("InfraObject Not Available For Selected Application - " + BSName);
        //            lblMsg.Text = "InfraObject Not Available For Selected Application.";
        //            lblMsg.Visible = true;
        //            return;
        //        }

        //        _logger.Info("ExcelReport Method Execution Completed.");
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Error("Exception Occurred In ExcelReport Method, Error Message " + ex.Message);
        //        if (ex.InnerException != null)
        //            _logger.Error("Exception Occurred In ExcelReport Method, InnerException Message " + ex.InnerException.Message);

        //    }
        //}


        public bool DRWorkFlowIsAttach(int infraId)
        {
            var _attach_Infra_Wf = Facade.GetInfraDR_Attach(infraId);  //ServiceProtectionDataAccess.InfraDR_Attach(infraId);

            if (_attach_Infra_Wf != null)
            {
                if (_attach_Infra_Wf.Count > 0)
                    return true;
                else
                    return false;
            }
            else
                return true;
        }


        //private void OpenExcelFile(IWorkbook workbook, string bsName)
        //{
        //    try
        //    {
        //        _logger.Info("OpenExcelFile Method Execution Start.");

        //        Response.Clear();
        //        Response.ContentType = "application/vnd.ms-excel";
        //        Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
        //        var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
        //        str = bsName + "DRReady" + str + ".xls";
        //        workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

        //        string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
        //        string myUrl = reportPath + "/ExcelFiles/" + str;
        //        //var myUrl = "/ExcelFiles/" + str;
        //        var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=DR Readiness Execution Log');";
        //        ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Error("Exception Occurred In OpenExcelFile , Error Message " + ex.Message);
        //        if (ex.InnerException != null)
        //            _logger.Error("Exception Occurred In OpenExcelFile , InnerException Message " + ex.InnerException.Message);
        //    }

        //}


        public string GetReason(int iBS_ID, int iInfra_ID, int iWF_ID, int iWFAction_ID)
        {
            string Message_1 = string.Empty;
            IList<InfraobjectSchedularLogs> infraschedulelist = new List<InfraobjectSchedularLogs>();

            infraschedulelist = Facade.GetDRReddynesssummary(iBS_ID, 0, 0);    //ServiceProtectionDataAccess.GetDRReddynesssummary(iBS_ID, 0, 0);

            if (infraschedulelist != null && infraschedulelist.Count > 0)
            {
                var Get_reason = (from val in infraschedulelist
                                  where
                                  val.InfraObjectId == iInfra_ID &&
                                  val.CurrentActionId == iWFAction_ID &&
                                  val.WorkflowId == iWF_ID
                                  orderby val.Id descending
                                  select val
                                      ).FirstOrDefault();
                if (Get_reason != null)
                {
                    Message_1 = Get_reason.DRReason;
                }
            }
            return !string.IsNullOrEmpty(Message_1) ? Message_1 : "NA";
        }

        private void ExcelFormat(IWorksheet worksheet, IList<ParallelWorkflowActionResult> parallelDrOperationReports, string WorkflowName, int WorkflowId)
        {
            try
            {
                _logger.Info("Workflow Summary ExcelFormat method execution start for workflow " + WorkflowName);
                cells = worksheet.Cells;
                worksheet.WindowInfo.DisplayGridlines = false;

                if (!string.IsNullOrEmpty(WorkflowName))
                {
                    if (WorkflowName.Length > 29)
                    {
                        string str = WorkflowName.Substring(0, 28);
                        worksheet.Name = str;
                    }
                    else
                    {
                        worksheet.Name = WorkflowName;
                    }
                }

                //worksheet.Name = WorkflowName;
                cells["E3"].Formula = "DR Ready Report";
                cells["E3"].Font.Size = 11;
                cells["E3"].Font.Bold = true;
                cells["E3"].ColumnWidth = 30;
                cells["E3"].HorizontalAlignment = HAlign.Center;
                cells["E3"].VerticalAlignment = VAlign.Top;

                //worksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 50, 15, 150, 22);
                //worksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 817, 15, 120, 13);
                worksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 50, 15, 150, 22);
                worksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 820, 15, 120, 15);

                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    worksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 500, 15, 121, 13);
                }

                worksheet.Cells["A1:G1"].RowHeight = 27;
                worksheet.Cells["A2:G2"].RowHeight = 25;
                IRange range = worksheet.Cells["B5:G5"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.Black;
                border.Weight = BorderWeight.Thin;

                IRange rangeOne = worksheet.Cells["B9:G9"];
                IBorder borderOne = rangeOne.Borders[BordersIndex.EdgeBottom];
                borderOne.LineStyle = LineStyle.Continous;
                borderOne.Color = Color.Black;
                borderOne.Weight = BorderWeight.Thin;

                cells["A1"].ColumnWidth = 7;
                cells["B4"].Formula = "Report Generated Time:" + " " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")); //+ DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                cells["B4"].Font.Bold = true;
                cells["B4"].HorizontalAlignment = HAlign.Left;
                cells["B4:G7"].Font.Size = 10;

                cells["A1"].ColumnWidth = 7;
                cells["B5"].Formula = "Logged in User :" + " " + (LoggedInUser.LoginName != null ? LoggedInUser.LoginName : "NA");
                cells["B5"].Font.Bold = true;
                cells["D5"].Font.Bold = true;
                cells["B5"].HorizontalAlignment = HAlign.Left;
                cells["B4:G7"].Font.Size = 10;

                cells["B3:G7"].Interior.Color = Color.FromArgb(79, 129, 189);
                cells["B3:G7"].Font.Color = Color.White;
                cells["B3:G9"].Font.Name = "Cambria";
                //////////////////

                int row = 8;
                int i = 1;

                var result = (from grp in parallelDrOperationReports
                              select new { Name = grp.InfraobjectName }).Distinct();

                var finalResult = result.ToList();

                string[] xlCol = { "A", "B", "C", "D", "E", "F", "G" };
                foreach (var groups in finalResult)
                {
                    cells["E4"].Formula = "InfraObject Name :- " + groups.Name;
                    cells["E4"].Font.Bold = true;
                    cells["B5"].Font.Bold = true;
                    cells["E4"].ColumnWidth = 30;
                    cells["E4"].HorizontalAlignment = HAlign.Center;
                    cells["E4"].VerticalAlignment = VAlign.Top;

                    cells["F5"].Formula = "Workflow Name :- " + WorkflowName;
                    cells["F5"].Font.Bold = true;
                    cells["F5"].ColumnWidth = 20;
                    cells["F5"].HorizontalAlignment = HAlign.Left;
                    cells["F5"].VerticalAlignment = VAlign.Top;
                    row++;

                    IList<InfraObject> grps = Facade.GetAllInfraObject();
                    var grpbyname = from grp in grps where grp.Name == groups.Name select grp;
                    if (grpbyname.Count() != 0)
                    {
                        foreach (var gp in grpbyname)
                        {
                            DatabaseBase prdatabase = Facade.GetDatabaseBaseByGroupIdPRServerId(gp.Id);

                            if (prdatabase != null)
                            {
                                if (prdatabase.Type.ToString() == "PRDatabase")
                                {
                                    if (prdatabase.DatabaseType.ToString() == "Oracle")
                                    {
                                        DatabaseOracle oracledb = Facade.GetDatabaseOracleByDatabaseBaseId(prdatabase.Id);
                                        if (oracledb != null)
                                            cells["F6"].Formula = "Production Database SID :- " + oracledb.OracleSID;
                                        else
                                            cells["F6"].Formula = "Production Database SID :- " + "NA";
                                    }
                                    else if (prdatabase.DatabaseType.ToString() == "Sql")
                                    {
                                        DatabaseSql sql = Facade.GetDatabaseSqlByDatabaseBaseId(prdatabase.Id);
                                        if (sql != null)
                                            cells["F6"].Formula = "Production Database SID :- " + sql.DatabaseSID;
                                        else
                                            cells["F6"].Formula = "Production Database SID :- " + "NA";
                                    }
                                    else if (prdatabase.DatabaseType.ToString() == "Exchange")
                                    {
                                        DatabaseExchange exchange = Facade.GetDatabaseExchangeByDatabaseBaseId(prdatabase.Id);
                                        if (exchange != null)
                                            cells["F6"].Formula = "Production Database SID :- " + "NA";
                                        else
                                            cells["F6"].Formula = "Production Database SID :- " + "NA";
                                    }
                                    else if (prdatabase.DatabaseType.ToString() == "DB2")
                                    {
                                        DatabaseDB2 db2 = Facade.GetDatabaseDb2ByDatabaseBaseId(prdatabase.Id);
                                        if (db2 != null)
                                            cells["F6"].Formula = "Production Database SID :- " + db2.DatabaseSID;
                                        else
                                            cells["F6"].Formula = "Production Database SID :- " + "NA";
                                    }
                                    else if (prdatabase.DatabaseType.ToString() == "OracleRac")
                                    {
                                        IList<Nodes> nodesall = Facade.GetAllNodes();
                                        if (nodesall != null && nodesall.Count > 0)
                                        {
                                            var oraclerac = from oraclenode in nodesall
                                                            where oraclenode.ServerId == gp.PRServerId
                                                            select oraclenode;

                                            if (oraclerac != null && oraclerac.Count() > 0)
                                            {
                                                string PRDBSID = oraclerac.FirstOrDefault().OracleSID != "" ? oraclerac.FirstOrDefault().OracleSID : "NA";
                                                cells["F6"].Formula = "Production Database SID :- " + PRDBSID;
                                            }
                                            else
                                            {
                                                cells["F6"].Formula = "Production Database SID :- " + "NA";
                                            }
                                        }
                                    }
                                }
                                else
                                    cells["F6"].Formula = "Production Database SID :- " + "NA";
                            }
                            else
                                cells["F6"].Formula = "Production Database SID :- " + "NA";


                            IList<GroupWorkflow> actiontype = Facade.GetAllGroupWorkflowByWorkFlowId(WorkflowId);

                            if (actiontype != null)
                            {

                                switch (actiontype[0].ActionType)
                                {
                                    case 1:
                                        cells["D5"].Formula = "Action Type :- " + "Switch Over";
                                        break;
                                    case 2:
                                        cells["D5"].Formula = "Action Type :- " + "Switch Back";
                                        break;
                                    case 3:
                                        cells["D5"].Formula = "Action Type :- " + "Fail Over";
                                        break;
                                    case 4:
                                        cells["D5"].Formula = "Action Type :- " + "Fail Back";
                                        break;
                                    case 5:
                                        cells["D5"].Formula = "Action Type :- " + "Custom WorkFlow";
                                        break;
                                }

                                foreach (var action in actiontype)
                                {
                                    if (action.GroupId == gp.Id)
                                    {
                                        if (actiontype[0] != null)
                                        {
                                            cells["B5"].Font.Bold = true;
                                            cells["B5"].ColumnWidth = 30;
                                            cells["B5"].HorizontalAlignment = HAlign.Left;
                                            cells["B5"].VerticalAlignment = VAlign.Top;

                                            Server prserver = actiontype[0].ActionType == 2 ? Facade.GetServerById(gp.DRServerId) : Facade.GetServerById(gp.PRServerId);
                                            if (prserver != null)
                                            {
                                                cells["B6"].Formula = "Production IP Address :- " + CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                                                cells["D6"].Formula = "Production Server Name :-" + prserver.Name;
                                            }
                                            else
                                            {
                                                cells["B6"].Formula = "Production IP Address :- " + "NA";
                                                cells["D6"].Formula = "Production Server Name :- " + "NA";
                                            }

                                            Server drserver = actiontype[0].ActionType == 2 ? Facade.GetServerById(gp.PRServerId) : Facade.GetServerById(gp.DRServerId);
                                            if (drserver != null)
                                            {
                                                cells["B7"].Formula = "DR IP Address :- " + CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                                                cells["D7"].Formula = "DR Server Name :-" + drserver.Name;
                                            }
                                            else
                                            {
                                                cells["B7"].Formula = "DR IP Address :- " + "NA";
                                                cells["D7"].Formula = "DR Server Name :- " + "NA";
                                            }
                                        }
                                        else
                                        {
                                            //cells["B5"].Formula = "Action Type :- " + "NA";

                                            Server prserver = action.ActionType == 2 ? Facade.GetServerById(gp.DRServerId) : Facade.GetServerById(gp.PRServerId);

                                            if (prserver != null)
                                            {
                                                cells["B6"].Formula = "Production IP Address :- " + CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                                                cells["D6"].Formula = "Production Server Name :-" + prserver.Name;
                                            }
                                            else
                                            {
                                                cells["B6"].Formula = "Production IP Address :- " + "NA";
                                                cells["D6"].Formula = "Production Server Name :- " + "NA";
                                            }

                                            Server drserver = action.ActionType == 2 ? Facade.GetServerById(gp.PRServerId) : Facade.GetServerById(gp.DRServerId);
                                            if (drserver != null)
                                            {
                                                cells["B7"].Formula = "DR IP Address :- " + CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                                                cells["D7"].Formula = "DR Server Name :-" + drserver.Name;
                                            }
                                            else
                                            {
                                                cells["B7"].Formula = "DR IP Address :- " + "NA";
                                                cells["D7"].Formula = "DR Server Name :- " + "NA";
                                            }
                                        }
                                    }
                                    else
                                    {
                                        //cells["B5"].Formula = "Action Type :- " + "NA";
                                        //cells["B7"].Formula = "DR IP Address :- " + "NA";
                                        //cells["D7"].Formula = "DR Server Name :- " + "NA";
                                        //cells["B6"].Formula = "Production IP Address :- " + "NA";
                                        //cells["D6"].Formula = "Production Server Name :- " + "NA";

                                        Server prserver = action.ActionType == 2 ? Facade.GetServerById(gp.DRServerId) : Facade.GetServerById(gp.PRServerId);
                                        if (prserver != null)
                                        {
                                            cells["B6"].Formula = "Production IP Address :- " + CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                                            cells["D6"].Formula = "Production Server Name :-" + prserver.Name;
                                        }
                                        else
                                        {
                                            cells["B6"].Formula = "Production IP Address :- " + "NA";
                                            cells["D6"].Formula = "Production Server Name :- " + "NA";
                                        }

                                        Server drserver = action.ActionType == 2 ? Facade.GetServerById(gp.PRServerId) : Facade.GetServerById(gp.DRServerId);
                                        if (drserver != null)
                                        {
                                            cells["B7"].Formula = "DR IP Address :- " + CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                                            cells["D7"].Formula = "DR Server Name :-" + drserver.Name;
                                        }
                                        else
                                        {
                                            cells["B7"].Formula = "DR IP Address :- " + "NA";
                                            cells["D7"].Formula = "DR Server Name :- " + "NA";
                                        }
                                    }
                                }
                            }
                            else
                            {
                                Server prserver = Facade.GetServerById(gp.PRServerId);
                                if (prserver != null)
                                {
                                    cells["B6"].Formula = "Production IP Address :- " + CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                                    cells["D6"].Formula = "Production Server Name :-" + prserver.Name;
                                }
                                else
                                {
                                    cells["B6"].Formula = "Production IP Address :- " + "NA";
                                    cells["D6"].Formula = "Production Server Name :- " + "NA";
                                }

                                Server drserver = Facade.GetServerById(gp.DRServerId);
                                if (drserver != null)
                                {
                                    cells["B7"].Formula = "DR IP Address :- " + CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                                    cells["D7"].Formula = "DR Server Name :-" + drserver.Name;
                                }
                                else
                                {
                                    cells["B7"].Formula = "DR IP Address :- " + "NA";
                                    cells["D7"].Formula = "DR Server Name :- " + "NA";
                                }

                                cells["D5"].Formula = "Action Type :-  NA";
                            }
                        }
                    }
                    else
                    {
                        cells["B6"].Formula = "Production IP Address :-  NA";
                        cells["D6"].Formula = "Production Server Name :-  NA";
                        cells["B7"].Formula = "DR IP Address :-  NA";
                        cells["D7"].Formula = "DR Server Name :-  NA";
                        cells["D5"].Formula = "Action Type :-  NA";
                        cells["F6"].Formula = "Production Database SID :-  NA";
                    }

                    cells["B6"].ColumnWidth = 30;
                    cells["B6"].HorizontalAlignment = HAlign.Left;
                    cells["B6"].VerticalAlignment = VAlign.Top;
                    cells["B6:G7"].Font.Bold = true;

                    cells["D6"].ColumnWidth = 30;
                    cells["D6"].HorizontalAlignment = HAlign.Left;
                    cells["D6"].VerticalAlignment = VAlign.Top;

                    cells["F6"].ColumnWidth = 20;
                    cells["F6"].HorizontalAlignment = HAlign.Left;
                    cells["F6"].VerticalAlignment = VAlign.Top;

                    IList<InfraObject> gr = Facade.GetAllInfraObject();
                    var grpbynme = from grp in gr where grp.Name == groups.Name select grp;
                    if (grpbynme.Count() != 0)
                    {
                        foreach (var gp in grpbynme)
                        {
                            DatabaseBase drdatabase = Facade.GetDatabaseBaseByGroupIdDrServerId(gp.Id);

                            if (drdatabase != null)
                            {
                                if (drdatabase.Type.ToString() == "DRDatabase")
                                {
                                    if (drdatabase.DatabaseType.ToString() == "Oracle")
                                    {
                                        DatabaseOracle ora = Facade.GetDatabaseOracleByDatabaseBaseId(drdatabase.Id);
                                        if (ora != null)
                                            cells["F7"].Formula = "DR Database SID :- " + ora.OracleSID;
                                        else
                                            cells["F7"].Formula = "DR Database SID :- " + "NA";
                                    }
                                    else if (drdatabase.DatabaseType.ToString() == "Sql")
                                    {
                                        DatabaseSql sql = Facade.GetDatabaseSqlByDatabaseBaseId(drdatabase.Id);
                                        if (sql != null)
                                            cells["F7"].Formula = "DR Database SID :- " + sql.DatabaseSID;
                                        else
                                            cells["F7"].Formula = "DR Database SID :- " + "NA";
                                    }
                                    else if (drdatabase.DatabaseType.ToString() == "Exchange")
                                    {
                                        DatabaseExchange exchange = Facade.GetDatabaseExchangeByDatabaseBaseId(drdatabase.Id);
                                        if (exchange != null)
                                            cells["F7"].Formula = "DR Database SID :- " + "NA";
                                        else
                                            cells["F7"].Formula = "DR Database SID :- " + "NA";
                                    }
                                    else if (drdatabase.DatabaseType.ToString() == "DB2")
                                    {
                                        DatabaseDB2 db2 = Facade.GetDatabaseDb2ByDatabaseBaseId(drdatabase.Id);
                                        if (db2 != null)
                                            cells["F7"].Formula = "DR Database SID :- " + db2.DatabaseSID;
                                        else
                                            cells["F7"].Formula = "DR Database SID :- " + "NA";
                                    }
                                    else if (drdatabase.DatabaseType.ToString() == "OracleRac")
                                    {
                                        IList<Nodes> nodesall = Facade.GetAllNodes();
                                        if (nodesall != null && nodesall.Count > 0)
                                        {
                                            var oraclerac = from oraclenode in nodesall
                                                            where oraclenode.ServerId == gp.DRServerId
                                                            select oraclenode;

                                            if (oraclerac != null && oraclerac.Count() > 0)
                                            {
                                                string DRDBSID = oraclerac.FirstOrDefault().OracleSID != "" ? oraclerac.FirstOrDefault().OracleSID : "NA";
                                                cells["F7"].Formula = "DR Database SID :- " + DRDBSID;
                                            }
                                            else
                                            {
                                                cells["F7"].Formula = "DR Database SID :- " + "NA";
                                            }
                                        }
                                    }
                                }
                                else
                                    cells["F7"].Formula = "DR Database SID :- " + "NA";

                            }
                            else
                                cells["F7"].Formula = "DR Database SID :- " + "NA";
                        }
                    }
                    else
                        cells["F7"].Formula = "DR Database SID :- " + "NA";



                    cells["B7"].ColumnWidth = 30;
                    cells["B7"].HorizontalAlignment = HAlign.Left;
                    cells["B7"].VerticalAlignment = VAlign.Top;

                    cells["D7"].ColumnWidth = 30;
                    cells["D7"].HorizontalAlignment = HAlign.Left;
                    cells["D7"].VerticalAlignment = VAlign.Top;


                    cells["F7"].ColumnWidth = 20;
                    cells["F7"].HorizontalAlignment = HAlign.Left;
                    cells["F7"].VerticalAlignment = VAlign.Top;

                    //lbTitle.Text = WorkflowName;
                    //lbContent.Text = "<div style=width:100%;> <div class=divNumber>SR.No</div>" + "    " + "<div class=divWN>WorkflowAction Name</div>" + "    "
                    //    + "<div class=div>Start Time</div>" + "    " + "<div class=div>End Time</div>" + "    "
                    //    + "<div class=div>Total Time (hh:mm:ss)</div>" + "    " + "<div class=div>Status</div> </div>";

                    cells["B" + row.ToString()].Formula = "Sr.No.";
                    cells["B" + row.ToString()].ColumnWidth = 7;
                    cells["B" + row.ToString()].Font.Bold = true;
                    cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    cells["B" + row.ToString() + ":" + "G" + row.ToString()].Font.Color = Color.White;
                    cells["B" + row.ToString() + ":" + "G" + row.ToString()].Font.Size = 10;
                    cells["B" + row.ToString() + ":" + "G" + row.ToString()].Interior.Color = Color.FromArgb(79, 129, 189);

                    cells["C" + row.ToString()].Formula = "Workflow Action Name";
                    cells["C" + row.ToString()].ColumnWidth = 20;
                    cells["C" + row.ToString()].Font.Bold = true;
                    cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    var workflowname = cells.Range["C" + row.ToString()].EntireRow;
                    workflowname.WrapText = true;

                    cells["D" + row.ToString()].Formula = "Start Time";
                    cells["D" + row.ToString()].ColumnWidth = 20;
                    cells["D" + row.ToString()].Font.Bold = true;
                    cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    cells["E" + row.ToString()].Formula = "End Time";
                    cells["E" + row.ToString()].ColumnWidth = 20;
                    cells["E" + row.ToString()].Font.Bold = true;
                    cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    cells["F" + row.ToString()].Formula = "Total Time (hh:mm:ss)";
                    cells["F" + row.ToString()].ColumnWidth = 20;
                    cells["F" + row.ToString()].Font.Bold = true;
                    cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    cells["G" + row.ToString()].Formula = "Status";
                    cells["G" + row.ToString()].ColumnWidth = 20;
                    cells["G" + row.ToString()].Font.Bold = true;
                    cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    row++;

                    TimeSpan totalWorkflowExecTime = TimeSpan.FromSeconds(0);
                    foreach (ParallelWorkflowActionResult parallelDrOperation in parallelDrOperationReports)
                    {
                        if (groups.Name == parallelDrOperation.InfraobjectName)
                        {
                            int column = 0;
                            // _parallelDrOperation.GroupName 
                            int k = 6;
                            string[] xlColumn = { "B", "C", "D", "E", "F", "G" };

                            string ndx = xlColumn[column] + row.ToString();
                            cells[ndx + ":" + "G" + row.ToString()].Interior.Color = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                            cells[ndx].Formula = i.ToString();
                            cells[ndx].Font.Size = 10;
                            cells[ndx].ColumnWidth = 7;
                            cells[ndx].Font.Color = Color.Black;
                            cells[ndx].HorizontalAlignment = HAlign.Left;
                            column++;

                            ndx = xlColumn[column] + row.ToString();
                            cells[ndx].Formula = parallelDrOperation.WorkflowActionName;
                            cells[ndx].Font.Size = 10;
                            cells[ndx].ColumnWidth = 20;
                            cells[ndx].Font.Color = Color.Black;
                            cells[ndx].HorizontalAlignment = HAlign.Left;

                            var workflowactionname = cells.Range[ndx].EntireRow;
                            workflowactionname.WrapText = true;
                            column++;

                            ndx = xlColumn[column] + row.ToString();
                            cells[ndx].EntireColumn.NumberFormat = "@";
                            if (parallelDrOperation.StartTime != null)
                            {

                                cells[ndx].Formula = Utility.Formatdate(Convert.ToDateTime(parallelDrOperation.StartTime).ToString("MM-dd-yyyy HH:mm:ss"));
                                //string[] Time = parallelDrOperation.StartTime.ToString().Split(' ');

                                //if (Time[0].Contains('/'))
                                //{
                                //    string[] Date = Time[0].Split('/');
                                //    cells[ndx].Formula = "  " + Date[0] + "/" + Date[1] + "/" + Date[2] + "  " + Time[1];
                                //}
                                //else
                                //{
                                //    string[] Date = Time[0].Split('-');
                                //    cells[ndx].Formula = "  " + Date[0] + "/" + Date[1] + "/" + Date[2] + "  " + Time[1];
                                //}
                            }
                            else
                            {
                                cells[ndx].Formula = "NA";
                            }
                            cells[ndx].Font.Size = 10;
                            cells[ndx].ColumnWidth = 20;
                            cells[ndx].Font.Color = Color.Black;
                            cells[ndx].HorizontalAlignment = HAlign.Left;
                            column++;

                            ndx = xlColumn[column] + row.ToString();
                            cells[ndx].EntireColumn.NumberFormat = "@";
                            if (parallelDrOperation.EndTime != null)
                            {
                                cells[ndx].Formula = Utility.Formatdate(Convert.ToDateTime(parallelDrOperation.EndTime).ToString("MM-dd-yyyy HH:mm:ss"));
                                //string[] Time = parallelDrOperation.EndTime.ToString().Split(' ');

                                //if (Time[0].Contains('/'))
                                //{
                                //    string[] Date = Time[0].Split('/');
                                //    cells[ndx].Formula = "  " + Date[0] + "/" + Date[1] + "/" + Date[2] + "  " + Time[1];
                                //}
                                //else
                                //{
                                //    string[] Date = Time[0].Split('-');
                                //    cells[ndx].Formula = "  " + Date[0] + "/" + Date[1] + "/" + Date[2] + "  " + Time[1];
                                //}
                            }
                            else
                            {
                                cells[ndx].Formula = "NA";
                            }
                            cells[ndx].Font.Size = 10;
                            cells[ndx].ColumnWidth = 20;
                            cells[ndx].Font.Color = Color.Black;
                            cells[ndx].HorizontalAlignment = HAlign.Left;
                            column++;

                            TimeSpan span = parallelDrOperation.EndTime - parallelDrOperation.StartTime;
                            ndx = xlColumn[column] + row.ToString();
                            cells[ndx].Formula = span.ToString();
                            cells[ndx].Font.Size = 10;
                            cells[ndx].ColumnWidth = 20;
                            cells[ndx].Font.Color = Color.Black;
                            cells[ndx].HorizontalAlignment = HAlign.Left;

                            if (i == 1)
                                totalWorkflowExecTime = span;
                            else
                                totalWorkflowExecTime = totalWorkflowExecTime + span;

                            column++;

                            ndx = xlColumn[column] + row.ToString();
                            cells[ndx].Formula = parallelDrOperation.Status.ToString();
                            cells[ndx].Font.Size = 10;
                            cells[ndx].ColumnWidth = 20;
                            cells[ndx].Font.Color = Color.Black;
                            cells[ndx].HorizontalAlignment = HAlign.Left;

                            //lbContent.Text = lbContent.Text + "<br />" + "<div class=divchildNum>" + i.ToString() + "<hr /></div>" + "<div class=divchildWN>" + parallelDrOperation.WorkflowActionName + "<hr /></div>" + "<div class=divchild>" + parallelDrOperation.StartTime + "<hr /></div>"
                            //+ "<div class=divchild>" + parallelDrOperation.EndTime + "<hr /></div>" + "<div class=divchild>" + span + "<hr /></div>" + "<div class=divchild>" + parallelDrOperation.Status + "<hr /></div>";
                            column++;

                            column = k;
                            row++;
                            i++;
                        }

                    } // foreach closed

                    //row++;

                    //cells["F" + row.ToString()].Formula = "Total Workflow Execution Time : ";
                    //cells["F" + row.ToString()].Font.Size = 11;
                    //cells["F" + row.ToString()].ColumnWidth = 30;
                    //cells["F" + row.ToString()].Font.Color = Color.Black;
                    ////cells["F" + row.ToString()].Font.Bold = true;
                    //cells["F" + row.ToString()].HorizontalAlignment = HAlign.Center;
                    //cells["F" + row.ToString()].VerticalAlignment = VAlign.Top;

                    //cells["G" + row.ToString()].Formula = totalWorkflowExecTime.ToString();  //tmax.ToString();  
                    //cells["G" + row.ToString()].NumberFormat = " h:mm:ss";
                    //cells["G" + row.ToString()].Font.Size = 10;
                    //cells["G" + row.ToString()].ColumnWidth = 21;
                    //cells["G" + row.ToString()].Font.Color = Color.Blue;
                    //cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    //cells["G" + row.ToString()].VerticalAlignment = VAlign.Top;

                    row++;
                    cells["C" + row.ToString()].Formula = "NA : Not Available";
                    cells["C" + row.ToString()].Font.Size = 10;
                    cells["C" + row.ToString()].ColumnWidth = 30;
                    cells["C" + row.ToString()].Font.Color = Color.Black;
                    cells["C" + row.ToString()].Font.Name = "Cambria";
                    cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    cells["C" + row.ToString()].VerticalAlignment = VAlign.Top;
                    //row++;
                }

                //pn.HeaderContainer.Controls.Add(lbTitle);
                //pn.ContentContainer.Controls.Add(lbContent);
                //AccParelleldr.Panes.Add(pn);
                _logger.Info("Workflow Summary ExcelFormat method execution complete.");

            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in ExcelFormat method of DRReady excel Report error message " + ex.Message);
                if (ex.InnerException != null)
                {
                    _logger.Error("Exception occurred in ExcelFormat method of DRReady excel Report InnerException " + ex.InnerException.Message);
                    _logger.Error("Exception occurred in ExcelFormat method of DRReady excel Report  StackTrace" + ex.InnerException.StackTrace);
                }
            }
        }

        private void PrepareExcelReport()
        {
            try
            {
                lblMsg.Text = string.Empty;
                lblMsg.Visible = false;

                _logger.Info("======Generating DR Ready Report EXCEL View ======");
                _logger.Info(Environment.NewLine);
                // int parallelDrOperationId = Convert.ToInt32(ddlParallelDrOperation.SelectedValue);

                int bsid = Convert.ToInt32(ddlBussService.SelectedItem.Value);

                string BSName = ddlBussService.SelectedItem.Text;


                IList<InfraObject> infraObjectList = Facade.GetInfraObjectByBusinessServiceIdUserId(bsid, LoggedInUserId);
                int DrInfraNotFound = 0;
               
                
                if (infraObjectList != null && infraObjectList.Count > 0)
                {
                    workbookSet = Factory.GetWorkbookSet();
                    ssFile = Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
                    templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                    templateWorksheet = templateWorkbook.Worksheets[0];

                    IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
                    IWorksheet reportWorksheet = null;
                    IWorksheet lastWorksheet = null;


                    lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                    reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);


                    cells = reportWorksheet.Cells;
                    reportWorksheet.WindowInfo.DisplayGridlines = false;
                    reportWorksheet.Name = "DR Ready Report";

                    cells["F3"].Formula = "DR Ready Report";
                    cells["F3"].Font.Color = Color.White;
                    cells["F3"].Font.Size = 11;
                    cells["F3"].Font.Bold = true;
                    //cells["F3"].ColumnWidth = 30;
                    cells["B3:J6"].Font.Name = "Cambria";
                    cells["F3"].HorizontalAlignment = HAlign.Center;
                    cells["F3"].VerticalAlignment = VAlign.Top;

                    //reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 50, 15, 150, 22);
                    //reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 800, 15, 120, 13);

                    cells["A1"].ColumnWidth = 7;
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 50, 15, 150, 22);
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 580, 15, 120, 15);
                    //worksheet.Cells["A1:F1"].RowHeight = 27;
                    //worksheet.Cells["A2:F2"].RowHeight = 25;


                    string strlogo = LoggedInUserCompany.CompanyLogoPath;

                    if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                    {
                        reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 500, 15, 121, 13);
                    }


                    reportWorksheet.Cells["A1:G1"].RowHeight = 27;
                    reportWorksheet.Cells["A2:G2"].RowHeight = 25;

                    IRange rangeOne = reportWorksheet.Cells["B6:J6"];
                    IBorder borderOne = rangeOne.Borders[BordersIndex.EdgeBottom];
                    borderOne.LineStyle = LineStyle.Continous;
                    borderOne.Color = Color.Black;
                    borderOne.Weight = BorderWeight.Thin;

                    cells["A1"].ColumnWidth = 7;

                    cells["B4"].Formula = "Report Generated Time:" + " " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));// + DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                    cells["B4"].Font.Bold = true;
                    cells["B4"].HorizontalAlignment = HAlign.Left;
                    cells["B4"].Font.Color = Color.White;
                    cells["B4"].Font.Size = 10;

                    cells["I4"].Formula = "Logged in User :" + " " + (LoggedInUser.LoginName != null ? LoggedInUser.LoginName : "NA");
                    cells["I4"].Font.Bold = true;
                    cells["I4"].HorizontalAlignment = HAlign.Left;
                    cells["I4"].Font.Color = Color.White;
                    cells["I4"].Font.Size = 10;

                    cells["B3:J4"].Interior.Color = Color.FromArgb(79, 129, 189);
                    int row = 6;
                    int i = 1;


                    cells["B" + row.ToString()].Formula = "Sr.No.";
                    cells["B" + row.ToString()].Font.Color = Color.White;
                    cells["B" + row.ToString()].Font.Size = 11;
                    cells["B" + row.ToString()].ColumnWidth = 6;
                    cells["B" + row.ToString()].Font.Bold = true;
                    cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    cells["B" + row.ToString() + ":" + "J" + row.ToString()].Font.Size = 10;
                    cells["B" + row.ToString() + ":" + "J" + row.ToString()].Interior.Color = Color.FromArgb(79, 129, 189);

                    cells["C" + row.ToString()].Formula = "InfraObject Name";
                    cells["C" + row.ToString()].Font.Color = Color.White;
                    cells["C" + row.ToString()].ColumnWidth = 25;
                    cells["C" + row.ToString()].Font.Bold = true;
                    cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    var grpname = cells.Range["C" + row.ToString()].EntireRow;
                    grpname.WrapText = true;

                    cells["D" + row.ToString()].Formula = "Workflow Name";
                    cells["D" + row.ToString()].Font.Color = Color.White;
                    cells["D" + row.ToString()].ColumnWidth = 25;
                    cells["D" + row.ToString()].Font.Bold = true;
                    cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    cells["E" + row.ToString()].Formula = "Start Time";
                    cells["E" + row.ToString()].Font.Color = Color.White;
                    cells["E" + row.ToString()].ColumnWidth = 20;
                    cells["E" + row.ToString()].Font.Bold = true;
                    cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;


                    cells["F" + row.ToString()].Formula = "End Time";
                    cells["F" + row.ToString()].Font.Color = Color.White;
                    cells["F" + row.ToString()].ColumnWidth = 20;
                    cells["F" + row.ToString()].Font.Bold = true;
                    cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    cells["G" + row.ToString()].Formula = "Total Time (hh:mm:ss)";
                    cells["G" + row.ToString()].Font.Color = Color.White;
                    cells["G" + row.ToString()].ColumnWidth = 14;
                    cells["G" + row.ToString()].Font.Bold = true;
                    cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    cells["H" + row.ToString()].Formula = "Production IP Address";
                    cells["H" + row.ToString()].Font.Color = Color.White;
                    cells["H" + row.ToString()].ColumnWidth = 20;
                    cells["H" + row.ToString()].Font.Bold = true;
                    cells["H" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    cells["I" + row.ToString()].Formula = "DR IP Address";
                    cells["I" + row.ToString()].Font.Color = Color.White;
                    cells["I" + row.ToString()].ColumnWidth = 20;
                    cells["I" + row.ToString()].Font.Bold = true;
                    cells["I" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    cells["J" + row.ToString()].Formula = "Status";
                    cells["J" + row.ToString()].Font.Color = Color.White;
                    cells["J" + row.ToString()].ColumnWidth = 17;
                    cells["J" + row.ToString()].Font.Bold = true;
                    cells["J" + row.ToString()].HorizontalAlignment = HAlign.Left;

                    row++;
                    int rindex = 1;

                    IList<GroupWorkflow> getdrwfdetails = new List<GroupWorkflow>();

                    foreach (var inf in infraObjectList)
                    {
                        //InfrascheduleWfdeatils WFScheduleByInfrId = Facade.GetScheduledActionResultByInfra(inf.Id);
                        InfrascheduleWfdeatils sch1 = new InfrascheduleWfdeatils();

                        bool parallel = false;
                        bool schedule = false;

                       
                        IList<GroupWorkflow> groupworkflowdetails = Facade.GetInfraDR_AttachByInfraID(inf.Id);

                        IList<GroupWorkflow> InfraDrdetails = new List<GroupWorkflow>();

                        if (groupworkflowdetails != null && groupworkflowdetails.Count > 0)
                        {
                            foreach (GroupWorkflow grpflwresult in groupworkflowdetails)
                            {
                                IList<WorkflowAction> DrReadyWFList = Facade.GetAllActionsDRReadyByWorkflowId(grpflwresult.WorkflowId);

                                if (DrReadyWFList != null && DrReadyWFList.Count > 0 && !string.IsNullOrEmpty(grpflwresult.Status))
                                {
                                    InfraDrdetails.Add(grpflwresult);
                                    parallel = true;
                                }
                            }

                        }
                        
                        
                        IList<InfraObjectscheduleWorkFlows> GetSchwfbyInfrId = Facade.GetAllInfraObjectScheduleByInfraObjectId(inf.Id);
                        IList<InfraObjectscheduleWorkFlows> GetSchDrWFByInfra = new List<InfraObjectscheduleWorkFlows>();

                        if (GetSchwfbyInfrId != null && GetSchwfbyInfrId.Count > 0)
                        {
                            foreach (InfraObjectscheduleWorkFlows drsc in GetSchwfbyInfrId)
                            {
                                IList<WorkflowAction> DrReadyWFList = Facade.GetAllActionsDRReadyByWorkflowId(drsc.WorkFlowId);

                                if (DrReadyWFList != null && DrReadyWFList.Count > 0)
                                {
                                    GetSchDrWFByInfra.Add(drsc);
                                    schedule = true;
                                }
                            }

                            if (GetSchDrWFByInfra != null && GetSchDrWFByInfra.Count > 0)
                            {
                                InfraObjectscheduleWorkFlows d = GetSchDrWFByInfra.FirstOrDefault();

                                IList<InfrascheduleWfdeatils> sch = Facade.GetWFSchExecutionDetailsByInfraScheduleIdWfid(d.Id, d.InfraObjectId, d.WorkFlowId);

                                //InfrascheduleWfdeatils sch1 = new InfrascheduleWfdeatils();

                                //if (sch != null && sch.Count > 0)
                                //{
                                //    sch1.InfraObjectId = sch.FirstOrDefault().InfraObjectId;
                                //    sch1.InfraObjectName = sch.FirstOrDefault().InfraObjectName;
                                //    sch1.WorkflowId = sch.FirstOrDefault().WorkflowId;
                                //    sch1.WorkflowName = sch.FirstOrDefault().WorkflowName;
                                //    sch1.StartTime = sch.FirstOrDefault().StartTime;
                                //    sch1.ScheduleId = sch.FirstOrDefault().ScheduleId;
                                //    sch1.EndTime = sch.LastOrDefault().EndTime;
                                //    sch1.Status = sch.LastOrDefault().Status;

                                //}

                                if (sch != null && sch.Count > 0)
                                {
                                    //var schdate = sch.LastOrDefault().CreateDate;
                                    DateTime createdate = sch.LastOrDefault().CreateDate;

                                    IList<InfrascheduleWfdeatils> schdaterec = (from data in sch where data.CreateDate.Date.Equals(createdate.Date) select data).ToList();

                                    sch1.InfraObjectId = schdaterec.FirstOrDefault().InfraObjectId;
                                    sch1.InfraObjectName = schdaterec.FirstOrDefault().InfraObjectName;
                                    sch1.WorkflowId = schdaterec.FirstOrDefault().WorkflowId;
                                    sch1.WorkflowName = schdaterec.FirstOrDefault().WorkflowName;
                                    sch1.StartTime = schdaterec.FirstOrDefault().StartTime;
                                    sch1.ScheduleId = schdaterec.FirstOrDefault().ScheduleId;
                                    sch1.EndTime = schdaterec.LastOrDefault().EndTime;
                                    sch1.Status = schdaterec.LastOrDefault().Status;

                                }
                            }

                        }



                        if (parallel == false && schedule == false)
                            DrInfraNotFound++;


                        GroupWorkflow details = null;
                        InfrascheduleWfdeatils _details = null;
                        GroupWorkflow dd = new GroupWorkflow();
                        InfrascheduleWfdeatils _dd = new InfrascheduleWfdeatils();

                        if (InfraDrdetails != null && InfraDrdetails.Count > 0 && sch1 != null)
                        {
                            #region paralleldroperationsummaryData


                            if (InfraDrdetails[0].Endtime > sch1.EndTime)
                            {
                                details = InfraDrdetails.FirstOrDefault();
                                ExcelFormat(reportWorksheet, details, true, row, rindex);
                                dd.WorkflowId = details.WorkflowId;
                                dd.InfraObjectId = details.InfraObjectId;
                                dd.InfraName = details.InfraName;
                                dd.WorkflowName = details.WorkflowName;
                                dd.ParallelDROperationId = details.ParallelDROperationId;
                                dd.RPTTYPE = "P";
                                getdrwfdetails.Add(dd);

                                row++;
                                rindex++;
                            }
                            else
                            {
                                _details = sch1;
                                InfraObject inff = Facade.GetInfraObjectById(_details.InfraObjectId);

                                GroupWorkflow gw = new GroupWorkflow();
                                gw.WorkflowId = _details.WorkflowId;
                                gw.InfraObjectId = _details.InfraObjectId;
                                gw.InfraName = _details.InfraObjectName;
                                gw.WorkflowName = _details.WorkflowName;
                                gw.Starttime = _details.StartTime;
                                gw.Endtime = _details.EndTime;
                                gw.Status = _details.Status;
                                gw.PRIPAddress = Facade.GetServerById(inff.PRServerId).IPAddress;
                                gw.DRIPAddress = Facade.GetServerById(inff.DRServerId).IPAddress;
                                gw.RPTTYPE = "S";
                                gw.ParallelDROperationId = _details.ScheduleId;
                                ExcelFormat(reportWorksheet, gw, true, row, rindex);


                                getdrwfdetails.Add(gw);

                                row++;
                                rindex++;

                            }




                            #endregion paralleldroperationsummaryData

                        }
                        else if (InfraDrdetails != null && InfraDrdetails.Count > 0 && sch1 == null)
                        {
                            details = InfraDrdetails.FirstOrDefault();
                            ExcelFormat(reportWorksheet, details, true, row, rindex);
                            dd.WorkflowId = details.WorkflowId;
                            dd.InfraObjectId = details.InfraObjectId;
                            dd.InfraName = details.InfraName;
                            dd.WorkflowName = details.WorkflowName;
                            dd.ParallelDROperationId = details.ParallelDROperationId;
                            dd.RPTTYPE = "P";
                            getdrwfdetails.Add(dd);

                            row++;
                            rindex++;


                        }
                        //else if (sch1 != null && InfraDrdetails == null )
                        else if (sch1 != null)
                        {
                            if (sch1.InfraObjectId != 0)
                            {
                                _details = sch1;
                                InfraObject inff = Facade.GetInfraObjectById(_details.InfraObjectId);

                                GroupWorkflow gw = new GroupWorkflow();
                                gw.WorkflowId = _details.WorkflowId;
                                gw.InfraObjectId = _details.InfraObjectId;
                                gw.InfraName = _details.InfraObjectName;
                                gw.WorkflowName = _details.WorkflowName;
                                gw.Starttime = _details.StartTime;
                                gw.Endtime = _details.EndTime;
                                gw.Status = _details.Status;
                                gw.PRIPAddress = Facade.GetServerById(inff.PRServerId).IPAddress;
                                gw.DRIPAddress = Facade.GetServerById(inff.DRServerId).IPAddress;
                                gw.RPTTYPE = "S";
                                gw.ParallelDROperationId = _details.ScheduleId;
                                ExcelFormat(reportWorksheet, gw, true, row, rindex);


                                getdrwfdetails.Add(gw);

                                row++;
                                rindex++;
                            }

                        }
                       

                    }// infra foreach loop closed

                    if (infraObjectList.Count == DrInfraNotFound)
                    {
                        lblMsg.Text = "DR Ready Execution Not Done For Business Service " + ddlBussService.SelectedItem.Text;
                        lblMsg.Visible = true;
                        _logger.Info("DR Ready Execution Not Done For Business Service " + ddlBussService.SelectedItem.Text);
                        return;
                    }

                    // Start Workfolw Sheet creation.

                    foreach (var Workflow in getdrwfdetails)
                    {
                        lastWorksheet = null;
                        IList<ParallelWorkflowActionResult> parallelDrOperationReports = new List<ParallelWorkflowActionResult>();

                        if (Workflow.RPTTYPE == "P")
                        {
                            parallelDrOperationReports = Facade.GetparallelDrOperationdataGetByInfraWID(Workflow.InfraObjectId, Workflow.ParallelDROperationId);

                        }
                        else
                        {
                            /// IList<InfrascheduleWfdeatils> WFScheduleByInfrId = Facade.GetScheduleWFDetailsByInfraID(Workflow.InfraObjectId);
                            IList<InfrascheduleWfdeatils> WFScheduleByInfrId = Facade.GetWFSchExecutionDetailsByInfraScheduleIdWfid(Workflow.ParallelDROperationId, Workflow.InfraObjectId, Workflow.WorkflowId);

                            DateTime createdate = WFScheduleByInfrId.LastOrDefault().CreateDate;

                            IList<InfrascheduleWfdeatils> schdaterecd = (from data in WFScheduleByInfrId where data.CreateDate.Date.Equals(createdate.Date) select data).ToList();

                            foreach (InfrascheduleWfdeatils v in schdaterecd)
                            {
                                ParallelWorkflowActionResult p = new ParallelWorkflowActionResult();

                                p.WorkflowActionName = v.CurrentActionName;
                                p.StartTime = v.StartTime;
                                p.EndTime = v.EndTime;
                                p.Status = (WorkflowActionStatus)Enum.Parse(typeof(WorkflowActionStatus), Convert.ToString(v.Status), true);
                                p.InfraobjectName = v.InfraObjectName;

                                parallelDrOperationReports.Add(p);

                            }

                        }

                        #region paralleldroperationData
                        string WorkflowName = Workflow.WorkflowName;
                        lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                        reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
                        ExcelFormat(reportWorksheet, parallelDrOperationReports, WorkflowName, Workflow.WorkflowId);
                        #endregion
                    }



                    reportWorkbook.Worksheets["Sheet1"].Delete();
                    reportWorksheet.ProtectContents = true;
                    OpenExcelFile(reportWorkbook, reportWorksheet);
                }
                else 
                {
                    lblMsg.Text = "InfraObject Not Get For Selected Business Service.";
                    lblMsg.Visible = true;
                    _logger.Info("InfraObject Not Get For Selected Business Service.");
                }

            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In PrepareExcelReport Metod,Error Message" + ex.Message);
                if(ex.InnerException != null)
                    _logger.Error("Exception Occurred In PrepareExcelReport Metod,InnerException Message" + ex.InnerException.Message);

                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured while Prepare DR Ready Excel Report", ex);
                ExceptionManager.Manage(customEx);
            }
        }


        private void ExcelFormat(IWorksheet worksheet, GroupWorkflow parallelDrOperation, bool isinfradr, int row, int i)
        {
            try
            {
                _logger.Info("ExcelFormat report method execution start.");


                cells = worksheet.Cells;
                TimeSpan totalspan = TimeSpan.FromSeconds(0);

                int column = 0;

                string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J" };


                if (isinfradr)
                {

                    string ndx = xlColumn[column] + row.ToString();
                    cells[ndx].Formula = i.ToString();
                    cells[ndx + ":" + "J" + row.ToString()].Interior.Color = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    cells[ndx].ColumnWidth = 25;
                    cells[ndx].Formula = parallelDrOperation.InfraName;
                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    cells[ndx].ColumnWidth = 25;
                    cells[ndx].Formula = parallelDrOperation.WorkflowName;
                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    cells[ndx].EntireColumn.NumberFormat = "@";
                    cells[ndx].ColumnWidth = 20;
                    if (parallelDrOperation.Starttime != null)
                    {

                        cells[ndx].Formula = Utility.Formatdate(Convert.ToDateTime(parallelDrOperation.Starttime).ToString("MM-dd-yyyy HH:mm:ss"));

                    }
                    else
                    {
                        cells[ndx].Formula = "NA";
                    }

                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    cells[ndx].EntireColumn.NumberFormat = "@";
                    cells[ndx].ColumnWidth = 20;
                    if (parallelDrOperation.Endtime != null)
                    {

                        cells[ndx].Formula = Utility.Formatdate(Convert.ToDateTime(parallelDrOperation.Endtime).ToString("MM-dd-yyyy HH:mm:ss"));

                    }
                    else
                    {
                        cells[ndx].Formula = "NA";
                    }

                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    TimeSpan span = parallelDrOperation.Endtime - parallelDrOperation.Starttime;
                    ndx = xlColumn[column] + row.ToString();
                    totalspan = totalspan.Add(span);
                    cells[ndx].ColumnWidth = 14;
                    cells[ndx].Formula = span.ToString();
                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    int list = 0;
                    ndx = xlColumn[column] + row.ToString();
                    cells[ndx].ColumnWidth = 17;
                    // IList<InfraObject> groups = Facade.GetAllInfraObject();
                    string prserverip = "NA";

                    if (!string.IsNullOrEmpty(parallelDrOperation.PRIPAddress))
                    {
                        cells[ndx].Formula = CryptographyHelper.Md5Decrypt(parallelDrOperation.PRIPAddress);
                        prserverip = CryptographyHelper.Md5Decrypt(parallelDrOperation.PRIPAddress);
                    }
                    else
                    {
                        cells[ndx].Formula = "NA";
                        prserverip = "NA";
                    }



                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    // IList<GroupWorkflow> actiontype1 = Facade.GetAllGroupWorkflowByWorkFlowId(Convert.ToInt32(Alist[list]));
                    ndx = xlColumn[column] + row.ToString();
                    // IList<InfraObject> grps = Facade.GetAllInfraObject();
                    string drserverip = "NA";

                    if (!string.IsNullOrEmpty(parallelDrOperation.DRIPAddress))
                    {
                        cells[ndx].Formula = CryptographyHelper.Md5Decrypt(parallelDrOperation.DRIPAddress);
                        drserverip = CryptographyHelper.Md5Decrypt(parallelDrOperation.DRIPAddress);
                    }
                    else
                    {
                        cells[ndx].Formula = "NA";
                        drserverip = "NA";
                    }

                    list++;
                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    cells[ndx].Formula = parallelDrOperation.Status.ToString();
                    cells[ndx].Font.Size = 10;
                    cells[ndx].Font.Color = Color.Black;
                    cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in ExcelFormat method of DRReady excel Report error message " + ex.Message);
                if (ex.InnerException != null)
                {
                    _logger.Error("Exception occurred in ExcelFormat method of DRReady excel Report InnerException " + ex.InnerException.Message);
                    _logger.Error("Exception occurred in ExcelFormat method of DRReady excel Report  StackTrace" + ex.InnerException.StackTrace);
                }
            }
        }

        private void OpenExcelFile(IWorkbook workbook, IWorksheet worksheet)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename= ParallelDROperation.xls");
            string _str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            // _str = "DRReadyReport" + _str + ".xls";

            _str = ddlBussService.SelectedItem.Text + "DRReadyReport" + _str + ".xls";

            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + _str), FileFormat.Excel8);
            //string myUrl = "/PdfFiles/" + _str;
            //string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            // string myUrl = "/CP/ExcelFiles/" + _str;
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + _str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=DR Drill Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }

    }
}