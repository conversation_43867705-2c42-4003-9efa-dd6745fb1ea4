﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="LogViewer.aspx.cs" Inherits="CP.UI.Admin.LogViewer" Title="Continuity Patrol :: Log Viewer" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<%@ Register TagPrefix="asp" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/bootstrap-multiselect.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script type="text/javascript">
        function jsDecimals(e) {

            var evt = (e) ? e : window.event;
            var key = (evt.keyCode) ? evt.keyCode : evt.which;
            if (key != null) {
                key = parseInt(key, 10);
                if ((key < 48 || key > 57) && (key < 96 || key > 105)) {

                    if (!jsIsUserFriendlyChar(key, "Decimals")) {
                        return false;
                    }
                }
                else {
                    if (evt.shiftKey) {
                        return false;
                    }
                }
            }
            return true;
        }
    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            var daysToAdd = 4;
            $("#txtFromDate").datepicker({
                onSelect: function (selected) {
                    var dtMax = new Date(selected);
                    dtMax.setDate(dtMax.getDate() + daysToAdd);
                    var dd = dtMax.getDate();
                    var mm = dtMax.getMonth() + 1;
                    var y = dtMax.getFullYear();
                    var dtFormatted = mm + '/' + dd + '/' + y;
                    $("#txtToDate").datepicker("option", "minDate", dtFormatted);
                }
            });
            $("#txtToDate").datepicker({
                onSelect: function (selected) {
                    var dtMax = new Date(selected);
                    dtMax.setDate(dtMax.getDate() - daysToAdd);
                    var dd = dtMax.getDate();
                    var mm = dtMax.getMonth() + 1;
                    var y = dtMax.getFullYear();
                    var dtFormatted = mm + '/' + dd + '/' + y;
                    $("#txtFromDate").datepicker("option", "maxDate", dtFormatted)
                }
            });
        });
    </script>
    <style>
        .navigation-tab .nav-tabs > li {
            margin-right: 9px;
        }

            .navigation-tab .nav-tabs > li::after {
                right: -6px;
            }

        .morecontent {
            display: none;
            cursor: pointer;
            color: #0967b5;
        }

        a.morelink {
            color: #00bbf0;
            text-decoration: underline;
        }

        .more {
            font-size: 14px;
        }

        .wrap {
            width: 95%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
        }

        div#ctl00_cphBody_updtbtnsearch {
            display: block;
            min-width: 210px;
        }

        #ctl00_cphBody_btnSearch, #ctl00_cphBody_txtsearchvalue {
            padding: 5px 5px;
            font-size: 12px;
        }

        input#ctl00_cphBody_txtStartDate, input#ctl00_cphBody_txtEndDate {
            pointer-events: none;
        }

        .error_new {
            position: absolute;
            color: white;
            background-color: red;
            top: -28px;
            padding: 3px 10px;
            border-radius: 5px;
        }

            .error_new:after {
                content: "";
                display: inline-block;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 7px solid red;
                position: absolute;
                top: 24px;
                left: 8px;
            }

        div#ctl00_cphBody_ddlNodes_chosen, #ctl00_cphBody_ddlfilename_chosen {
            width: 100% !important;
        }
    </style>
    <script type="text/javascript">

        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:UpdatePanel ID="UpdatePanel1" runat="server">
        <ContentTemplate>
            <input type="hidden" id="hdfStaticGuid" runat="server" />
            <asp:HiddenField ID="hdtokenKey" runat="server" />
            <div class="innerLR">
                <h3><span class="archive-log-blue" style="background-size: 14px 16px; background-position: 0px 0px; padding: 4px 0px 8px 20px !important;"></span>Log Viewer </h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group col-md-3" style="width: 27%;">
                                    <label class="col-md-4 control-label" style="padding-left: 0;">
                                        File Name <span class="inactive">*</span></label>
                                    <div class="col-md-8" style="padding-left: 0;">
                                        <asp:DropDownList ID="ddlfilename" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddlfilename_SelectedIndexChanged"
                                            CssClass="chosen-select" data-style="btn-default" Width="90%">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator6" runat="server" CssClass="error" ControlToValidate="ddlfilename" Display="Dynamic" ValidationGroup="AddGroup" InitialValue="0" ErrorMessage="*" Text="Please Select the File Name"></asp:RequiredFieldValidator>
                                    </div>
                                </div>

                                <div class="form-group col-md-3" style="width: 27%;">
                                    <label class="col-md-4 control-label" style="padding-left: 0;">
                                        Node Name <span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-8" style="padding-left: 0;">
                                        <asp:DropDownList ID="ddlNodes" runat="server" CssClass="chosen-select" data-style="btn-default" OnSelectedIndexChanged="ddlNodes_SelectedIndexChanged"
                                            AutoPostBack="True">
                                        </asp:DropDownList>
                                    </div>
                                </div>
                                <div class="form-group calendar-icon col-md-3" style="width: 24.5%;">
                                    <i class="icon-calendar"></i>
                                    <label class="col-md-4 control-label text-right" style="padding-left: 0;">
                                        Start Date <span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-8" style="padding-left: 0;">
                                        <TK1:CalendarExtender ID="ceStartDate" runat="server" TargetControlID="txtStartDate" PopupButtonID="imgBtnStartDate" Format="dd-MMM-yyyy">
                                        </TK1:CalendarExtender>
                                        <asp:TextBox ID="txtStartDate" runat="server" Width="86%" CssClass="form-control" AutoPostBack="true" autocomplete="off" OnTextChanged="txtStartDate_TextChanged" TabIndex="-1"></asp:TextBox>
                                        <img src="../images/icons/calendar-month.png" width="16" id="imgBtnStartDate" style="margin-left: 4px;" />
                                        <asp:RequiredFieldValidator ID="rfvStartDate" CssClass="error" runat="server" ErrorMessage="Select Date" ControlToValidate="txtStartDate"
                                            ValidationGroup="vlGroup1" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group calendar-icon col-md-3" style="width: 24.5%;">
                                    <i class="icon-calendar"></i>
                                    <label class="col-md-4 control-label text-right" style="padding-left: 0;">
                                        End Date <span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-8" style="padding-left: 0;">
                                        <asp:TextBox ID="txtEndDate" runat="server" Width="86%" CssClass="form-control" TabIndex="-1" AutoPostBack="true" autocomplete="off" OnTextChanged="txtEndDate_TextChanged"></asp:TextBox><%--AutoPostBack="True" OnTextChanged="txtEndDate_TextChanged"--%>
                                        <img src="../images/icons/calendar-month.png" width="16" id="imgBtnEndDate" style="margin-left: 4px;" />

                                        <TK1:CalendarExtender ID="ceEndDate" runat="server" TargetControlID="txtEndDate" PopupButtonID="imgBtnEndDate" Format="dd-MMM-yyyy">
                                        </TK1:CalendarExtender>
                                        <asp:RequiredFieldValidator ID="rfvEndDate" CssClass="error" runat="server" ErrorMessage="Select Date"
                                            ControlToValidate="txtEndDate" ValidationGroup="vlGroup1" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group col-md-4" style="width: 8%; padding-right: 0;">
                                    <asp:Button ID="btnView" CssClass="btn btn-primary view-btn" runat="server" Style="margin-top: 0px"
                                        Text="View" ValidationGroup="vlGroup1" AutoPostBack="True" OnClick="btnView_Click" />
                                </div>
                                <div id="divresult" runat="server" visible="false">
                                    <div class="form-group col-md-3" style="width: 27%;">
                                        <label class="col-md-4 control-label" style="padding-left: 0;">
                                            Total Files <span class="inactive"></span>
                                        </label>
                                        <div class="col-md-8" style="padding-left: 0;">
                                            <asp:DropDownList ID="ddlresult" runat="server" AutoPostBack="True" OnSelectedIndexChanged="ddlresult_SelectedIndexChanged"
                                                CssClass="chosen-select" data-style="btn-default" Width="170%">
                                            </asp:DropDownList>
                                            <asp:RequiredFieldValidator ID="rfvresult" runat="server" CssClass="error" ControlToValidate="ddlresult" Display="Dynamic" ValidationGroup="AddGroup" InitialValue="0" ErrorMessage="*" Text="Please Select the File Name"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>
                                </div>
                                <div class="clearfix"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="widget widget-heading-simple widget-body-white clear margin-bottom8" id="ccc" runat="server">
                    <div class="widget-body" style="height: 410px; overflow: auto">
                        <div class="text-center">
                        <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false" Text="Log details not available."></asp:Label>
                        </div>
                        <div id="divFilesDetails" runat="server" visible="false">
                            <asp:ListView ID="lvFiles" runat="server" OnPagePropertiesChanging="lvFiles_PagePropertiesChanging" OnPreRender="lvFiles_PreRender">
                                <LayoutTemplate>
                                    <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                                        <thead>
                                            <tr>
                                                <th style="width: 8%;" class="text-center">Sr.No.</th>
                                                <th style="width: 92%;" class="text-left">Activity Details </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                        </tbody>
                                    </table>
                                </LayoutTemplate>
                                <EmptyDataTemplate>
                                </EmptyDataTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td style="width: 8%;" class="text-center">
                                            <%#Container.DataItemIndex+1 %>
                                            <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                        </td>
                                        <td style="width: 92%;">
                                            <asp:Label ID="Label1" runat="server" Text='<%# Eval("Activity") %>' ToolTip='<%# Eval("Activity") %>' CssClass="wrap" />
                                            <a class="morecontent"><span>more</span></a>
                                        </td>
                                    </tr>
                                </ItemTemplate>
                            </asp:ListView>
                            <div class="row" style="padding-bottom: 15px;">
                                <asp:UpdatePanel ID="updtbtnsearch" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <div class="col-xs-3" style="width: 20%; padding-right: 0;">
                                            <asp:DataPager ID="DataPagerMonitoring2" runat="server" PagedControlID="lvFiles">
                                                <Fields>
                                                    <asp:TemplatePagerField>
                                                        <PagerTemplate>
                                                            <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                            Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                            <br />
                                                        </PagerTemplate>
                                                    </asp:TemplatePagerField>
                                                </Fields>
                                            </asp:DataPager>
                                        </div>
                                        <div class="col-xs-9 text-right" style="width: 80%;">
                                            <asp:Label ID="lblpage" runat="server" Text="Your Page Search is Out of Range" Visible="false" CssClass="error_new"></asp:Label>
                                            <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" MaxLength="9" onkeydown="return jsDecimals(event);" placeholder="Enter Page Number" AutoPostBack="true" OnTextChanged="txtsearchvalue_TextChanged" Style="width: 175px;"></asp:TextBox>
                                            <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Go" Visible="false" AutoPostBack="true" OnClick="btnSearch_Click" ValidationGroup="vlGroup1" />
                                            <asp:DataPager ID="DataPagerMonitoring1" runat="server" PagedControlID="lvFiles" PageSize="10">
                                                <Fields>
                                                    <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                        ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="←" />
                                                    <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                        NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                        NumericButtonCssClass="btn-pagination" />
                                                    <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                        ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="→" />
                                                </Fields>
                                            </asp:DataPager>
                                        </div>
                                        <div class="clearfix"></div>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </ContentTemplate>
    </asp:UpdatePanel>
    <asp:UpdateProgress ID="UpdateProgress1" runat="server">
        <ProgressTemplate>
            <div id="imgLoading" class="loading-mask">
                <span>Loading...</span>
            </div>
        </ProgressTemplate>
    </asp:UpdateProgress>
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <script type="text/javascript">
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        })
    </script>
</asp:Content>

