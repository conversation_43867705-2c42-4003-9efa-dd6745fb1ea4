﻿using System;
using System.Data;
using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class ImpactTypeMasterBuilder : IEntityBuilder<ImpactTypeMaster>
    {
        IList<ImpactTypeMaster> IEntityBuilder<ImpactTypeMaster>.BuildEntities(IDataReader reader)
        {
            var impactTypeMaster = new List<ImpactTypeMaster>();

            while (reader.Read())
            {
                impactTypeMaster.Add(((IEntityBuilder<ImpactTypeMaster>)this).BuildEntity(reader, new ImpactTypeMaster()));
            }

            return (impactTypeMaster.Count > 0) ? impactTypeMaster : null;
        }

        ImpactTypeMaster IEntityBuilder<ImpactTypeMaster>.BuildEntity(IDataReader reader, ImpactTypeMaster impactTypeMaster)
        {
            impactTypeMaster.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            impactTypeMaster.ImpactTypeName = Convert.IsDBNull(reader["ImpactTypeName"]) ? string.Empty : Convert.ToString(reader["ImpactTypeName"]);
            impactTypeMaster.ImpactTypeDescription = Convert.IsDBNull(reader["ImpactTypeDescription"]) ? string.Empty : Convert.ToString(reader["ImpactTypeDescription"]);
            impactTypeMaster.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            impactTypeMaster.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            impactTypeMaster.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            impactTypeMaster.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            impactTypeMaster.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());
            impactTypeMaster.ImpactOrder = Convert.IsDBNull(reader["ImpactOrder"]) ? string.Empty : Convert.ToString(reader["ImpactOrder"]);
            return impactTypeMaster;
        }
    }
}
