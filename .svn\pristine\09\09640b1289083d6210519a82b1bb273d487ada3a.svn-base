﻿using System;
using CP.BusinessFacade;

namespace CP.UI.Code.Replication.DataLag
{
    public class OracleWithFastcopyDataLag : IDataLag
    {
        #region IDataLag Members

        private readonly IFacade _facade = new Facade();

        public InfraObjectInfo GetDataLag(int infraobjectid)
        {
            var infraobjectInfo = new InfraObjectInfo();
            var infraobject = _facade.GetInfraObjectById(infraobjectid);
            var businessfunction = _facade.GetBusinessFunctionByInfraObjectId(infraobjectid);

            // Kuntesh Thakker - 05-05-2014 - As discussed with <PERSON> for CP 4.0v, Oracle Fast copy datalag details need to get from businessservicerpoinfo table so its doesnt need below method
            // var dataLag = _facade.GetFastCopyMonitorByInfraObjectId(infraobjectid);
            // var group = _facade.GetInfraObjectById(groupId);

            var dataLag = _facade.GetCurrentRPOInfraInfobyInfraObjectId(infraobjectid, infraobject.BusinessFunctionId);

            if (dataLag != null && infraobject != null && businessfunction != null)
            {
                if (string.IsNullOrEmpty(dataLag.CurrentRPO))
                {
                    infraobjectInfo.DataLag = "N/A";

                    infraobjectInfo.Health = 0;

                    infraobjectInfo.DataLagCreateDate = "NA";
                }
                else
                {
                    infraobjectInfo.DataLag = dataLag.CurrentRPO;

                    infraobjectInfo.Health = Utility.GetDatlagHealth(dataLag.CurrentRPO, Convert.ToInt32(Utility.ConvertRPOValues(businessfunction.ConfiguredRPO))) ? 1 : 0;

                    infraobjectInfo.DataLagCreateDate = dataLag.CreateDate.ToString();
                }
            }
            else
            {
                infraobjectInfo.DataLag = "N/A";

                infraobjectInfo.Health = 0;

                infraobjectInfo.DataLagCreateDate = "NA";
            }

            return infraobjectInfo;
        }

        #endregion IDataLag Members
    }
}