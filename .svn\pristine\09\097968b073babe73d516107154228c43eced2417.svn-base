﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "VMWarePathDetails", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class VMWarePathDetails : BaseEntity
    {

        #region Properties
        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string PRVmFilePath { get; set; }

        [DataMember]
        public string DRVmFilePath { get; set; }

        [DataMember]
        public string VmwareName { get; set; }

        [DataMember]
        public string PRDataStoreName { get; set; }

        [DataMember]
        public string DRDataStoreName { get; set; }

        #endregion
    }
}
