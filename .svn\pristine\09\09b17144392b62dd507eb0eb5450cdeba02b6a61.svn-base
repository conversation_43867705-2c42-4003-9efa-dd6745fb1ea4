﻿using System;
using System.Linq;
using System.Web.UI.WebControls;
using CP.ExceptionHandler;
using CP.Helper;
using CP.Common.Shared;
namespace CP.UI
{
    public partial class MSExchDAGMonitoring : GroupBasePage
    {
        //protected void Page_Load(object sender, EventArgs e)
        //{
        //    Utility.SelectMenu(Master, "Module2");
        //    if (!IsPostBack)
        //    {
        //        try
        //        {
        //            var group = Facade.GetInfraObjectById(CurrentGroup.Id);
        //            FillMailboxDBDropdown(group.PRServerId);
        //            FillDAGReplicationCompHealthStatus();
        //            FillComponentMonitor();
        //            FillReplicationMonitor();

        //        }
        //        catch(BcmsException ex)
        //        {
        //            ExceptionManager.Manage(ex);
        //        }
        //        catch (Exception ex)
        //        {
        //            var bcmsException = new BcmsException(BcmsExceptionType.CommonUnhandled, "Error occure while binding exchange monitor information", ex);
        //            ExceptionManager.Manage(bcmsException);
        //        }
        //    }
        //}

        public override void PrepareView()
        {
            Utility.SelectMenu(Master, "Module1");
            try
            {
                var group = Facade.GetInfraObjectById(CurrentGroup.Id);
                FillMailboxDBDropdown(group.PRServerId);
                FillDAGReplicationCompHealthStatus();
                FillComponentMonitor();
                FillReplicationMonitor();
                BindWindowsServiceData();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var bcmsException = new CpException(CpExceptionType.CommonUnhandled, "Error occure while binding exchange monitor information", ex);
                ExceptionManager.Manage(bcmsException);
            }
        }

        private void FillDAGReplicationCompHealthStatus()
        {
            try
            {
                //var healthStatus = Facade.GetAllExchangeDAGReplHSByGroupId(CurrentGroup.Id);

                var healthStatus = Facade.GetAllExchangeDAGReplHsByInfraObjectId(CurrentGroup.Id);

                if (healthStatus != null && healthStatus.Count() > 0)
                {
                    var rHealthStatus = healthStatus.FirstOrDefault();

                    lblClusterServiceResultPR.Text = rHealthStatus.ClusterServicePrResult.ToString();
                    Label38.CssClass = (lblClusterServiceResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblClusterServiceErrPR.Text = rHealthStatus.ClusterServicePrError.ToString();
                    lblClusterServiceResultDR.Text = rHealthStatus.ClusterServiceDrResult.ToString();
                    Label209.CssClass = (lblClusterServiceResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";

                    lblClusterServiceErrDR.Text = rHealthStatus.ClusterServiceDrError.ToString();
                    lblReplayServiceResultPR.Text = rHealthStatus.ReplayServicePrResult.ToString();
                    Label22.CssClass = (lblReplayServiceResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblReplayServiceErrPR.Text = rHealthStatus.ReplayServicePrError.ToString();
                    lblReplayServiceResultDR.Text = rHealthStatus.ReplayServiceDrResult.ToString();

                    Label213.CssClass = (lblReplayServiceResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblReplayServiceErrDR.Text = rHealthStatus.ReplayServiceDrError.ToString();
                    lblActiveManagerResultPR.Text = rHealthStatus.ActiveManagerPrResult.ToString();
                    Label31.CssClass = (lblActiveManagerResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblActiveManagerErrPR.Text = rHealthStatus.ActiveManagerPrError.ToString();
                    lblActiveManagerResultDR.Text = rHealthStatus.ActiveManagerDrResult.ToString();
                    Label217.CssClass = (lblActiveManagerResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblActiveManagerErrDR.Text = rHealthStatus.ActiveManagerDrError.ToString();

                    lblTasksRPCListenerResultPR.Text = rHealthStatus.TasksRpcListenerPrResult.ToString();
                    Label35.CssClass = (lblTasksRPCListenerResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblTasksRPCListenerErrPR.Text = rHealthStatus.TasksRpcListenerPrError.ToString();
                    lblTasksRPCListenerResultDR.Text = rHealthStatus.TasksRpcListenerDrResult.ToString();
                    Label221.CssClass = (lblTasksRPCListenerResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblTasksRPCListenerErrDR.Text = rHealthStatus.TasksRpcListenerDrError.ToString();

                    lblTCPListenerResultPR.Text = rHealthStatus.TcpListenerPrResult.ToString();
                    Label46.CssClass = (lblTCPListenerResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblTCPListenerErrPR.Text = rHealthStatus.TcpListenerPrError.ToString();
                    lblTCPListenerResultDR.Text = rHealthStatus.TcpListenerDrResult.ToString();
                    Label225.CssClass = (lblTCPListenerResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblTCPListenerErrDR.Text = rHealthStatus.TcpListenerDrError.ToString();

                    lblDAGMembersUpResultPR.Text = rHealthStatus.DAGMembersUpPrResult.ToString();
                    Label50.CssClass = (lblDAGMembersUpResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDAGMembersUpErrPR.Text = rHealthStatus.DAGMembersUpPrError.ToString();
                    lblDAGMembersUpResultDR.Text = rHealthStatus.DAGMembersUpDrResult.ToString();
                    Label229.CssClass = (lblDAGMembersUpResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDAGMembersUpErrDR.Text = rHealthStatus.DAGMembersUpDrError.ToString();

                    lblClusterNetworkResultPR.Text = rHealthStatus.ClusterNetworkPrResult.ToString();
                    Label54.CssClass = (lblClusterNetworkResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblClusterNetworkErrPR.Text = rHealthStatus.ClusterNetworkPrError.ToString();
                    lblClusterNetworkResultDR.Text = rHealthStatus.ClusterNetworkDrResult.ToString();
                    Label233.CssClass = (lblClusterNetworkResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblClusterNetworkErrDR.Text = rHealthStatus.ClusterNetworkDrError.ToString();

                    lblQuorumGroupResultPR.Text = rHealthStatus.QuorumGroupPrResult.ToString();
                    Label58.CssClass = (lblQuorumGroupResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblQuorumGroupErrPR.Text = rHealthStatus.QuorumGroupPrError.ToString();
                    lblQuorumGroupResultDR.Text = rHealthStatus.QuorumGroupDrResult.ToString();
                    Label237.CssClass = (lblQuorumGroupResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblQuorumGroupErrDR.Text = rHealthStatus.QuorumGroupDrError.ToString();

                    lblDBCopySuspendedResultPR.Text = rHealthStatus.DBCopySuspendedPrResult.ToString();
                    Label62.CssClass = (lblDBCopySuspendedResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBCopySuspendedErrPR.Text = rHealthStatus.DBCopySuspendedPrError.ToString();
                    lblDBCopySuspendedResultDR.Text = rHealthStatus.DBCopySuspendedDrResult.ToString();
                    Label241.CssClass = (lblDBCopySuspendedResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBCopySuspendedErrDR.Text = rHealthStatus.DBCopySuspendedDrError.ToString();

                    lblDBCopyFailedResultPR.Text = rHealthStatus.DBCopyFailedPrResult.ToString();
                    Label66.CssClass = (lblDBCopyFailedResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBCopyFailedErrPR.Text = rHealthStatus.DBCopyFailedPrError.ToString();
                    lblDBCopyFailedResultDR.Text = rHealthStatus.DBCopyFailedDrResult.ToString();
                    Label245.CssClass = (lblDBCopyFailedResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBCopyFailedErrDR.Text = rHealthStatus.DBCopyFailedDrError.ToString();

                    lblDBInitializingResultPR.Text = rHealthStatus.DBInitializingPrResult.ToString();
                    Label70.CssClass = (lblDBInitializingResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBInitializingErrPR.Text = rHealthStatus.DBInitializingPrError.ToString();
                    lblDBInitializingResultDR.Text = rHealthStatus.DBInitializingDrResult.ToString();
                    Label249.CssClass = (lblDBInitializingResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBInitializingErrDR.Text = rHealthStatus.DBInitializingDrError.ToString();

                    //lblDBDisconnectedResultPR.Text = rHealthStatus.DBDisconnectedPrResult.ToString();
                    //lblDBDisconnectedErrPR.Text = rHealthStatus.DBDisconnectedPrError.ToString();
                    //lblDBDisconnectedResultDR.Text = rHealthStatus.DBDisconnectedDrResult.ToString();
                    //lblDBDisconnectedErrDR.Text = rHealthStatus.DBDisconnectedDrError.ToString();

                    //lblDBLogCopyKeepingUpResultPR.Text = rHealthStatus.DBLogCopyKeepingUpPrResult.ToString();
                    //lblDBLogCopyKeepingUpErrPR.Text = rHealthStatus.DBLogCopyKeepingUpPrError.ToString();
                    //lblDBLogCopyKeepingUpResultDR.Text = rHealthStatus.DBLogCopyKeepingUpDrResult.ToString();
                    //lblDBLogCopyKeepingUpErrDR.Text = rHealthStatus.DBLogCopyKeepingUpDrError.ToString();
                }
                else
                {
                    lblClusterServiceResultPR.Text = "N/A";
                    Label38.CssClass = (lblClusterServiceResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblClusterServiceErrPR.Text = "N/A";
                    lblClusterServiceResultDR.Text = "N/A";
                    Label209.CssClass = (lblClusterServiceResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";

                    lblClusterServiceErrDR.Text = "N/A";
                    lblReplayServiceResultPR.Text = "N/A";
                    Label22.CssClass = (lblReplayServiceResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblReplayServiceErrPR.Text = "N/A";
                    lblReplayServiceResultDR.Text = "N/A";

                    Label213.CssClass = (lblReplayServiceResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblReplayServiceErrDR.Text = "N/A";
                    lblActiveManagerResultPR.Text = "N/A";
                    Label31.CssClass = (lblActiveManagerResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblActiveManagerErrPR.Text = "N/A";
                    lblActiveManagerResultDR.Text = "N/A";
                    Label217.CssClass = (lblActiveManagerResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblActiveManagerErrDR.Text = "N/A";

                    lblTasksRPCListenerResultPR.Text = "N/A";
                    Label35.CssClass = (lblTasksRPCListenerResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblTasksRPCListenerErrPR.Text = "N/A";
                    lblTasksRPCListenerResultDR.Text = "N/A";
                    Label221.CssClass = (lblTasksRPCListenerResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblTasksRPCListenerErrDR.Text = "N/A";

                    lblTCPListenerResultPR.Text = "N/A";
                    Label46.CssClass = (lblTCPListenerResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblTCPListenerErrPR.Text = "N/A";
                    lblTCPListenerResultDR.Text = "N/A";
                    Label225.CssClass = (lblTCPListenerResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblTCPListenerErrDR.Text = "N/A";

                    lblDAGMembersUpResultPR.Text = "N/A";
                    Label50.CssClass = (lblDAGMembersUpResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDAGMembersUpErrPR.Text = "N/A";
                    lblDAGMembersUpResultDR.Text = "N/A";
                    Label229.CssClass = (lblDAGMembersUpResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDAGMembersUpErrDR.Text = "N/A";
                    lblClusterNetworkResultPR.Text = "N/A";
                    Label54.CssClass = (lblClusterNetworkResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblClusterNetworkErrPR.Text = "N/A";
                    lblClusterNetworkResultDR.Text = "N/A";
                    Label233.CssClass = (lblClusterNetworkResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblClusterNetworkErrDR.Text = "N/A";

                    lblQuorumGroupResultPR.Text = "N/A";
                    Label58.CssClass = (lblQuorumGroupResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblQuorumGroupErrPR.Text = "N/A";
                    lblQuorumGroupResultDR.Text = "N/A";
                    Label237.CssClass = (lblQuorumGroupResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblQuorumGroupErrDR.Text = "N/A";

                    lblDBCopySuspendedResultPR.Text = "N/A";
                    Label62.CssClass = (lblDBCopySuspendedResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBCopySuspendedErrPR.Text = "N/A";
                    lblDBCopySuspendedResultDR.Text = "N/A";
                    Label241.CssClass = (lblDBCopySuspendedResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBCopySuspendedErrDR.Text = "N/A";

                    lblDBCopyFailedResultPR.Text = "N/A";
                    Label66.CssClass = (lblDBCopyFailedResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBCopyFailedErrPR.Text = "N/A";
                    lblDBCopyFailedResultDR.Text = "N/A";
                    Label245.CssClass = (lblDBCopyFailedResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBCopyFailedErrDR.Text = "N/A";

                    lblDBInitializingResultPR.Text = "N/A";
                    Label70.CssClass = (lblDBInitializingResultPR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBInitializingErrPR.Text = "N/A";
                    lblDBInitializingResultDR.Text = "N/A";
                    Label249.CssClass = (lblDBInitializingResultDR.Text).ToLower().Contains("passed") ? "Active" : "InActive";
                    lblDBInitializingErrDR.Text = "N/A";

                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var bcmsException = new CpException(CpExceptionType.CommonUnhandled, "Error occure while binding exchange monitor information", ex);
                ExceptionManager.Manage(bcmsException);
            }
        }

        private void FillReplicationMonitor()
        {
            try
            {
                var replMonitor = Facade.GetExchangeDAGMonitorByInfraObjectId(CurrentGroup.Id);

                if (replMonitor != null && replMonitor.Count() > 0)
                {
                    var replMonitorMBName = from rMonitor in replMonitor
                                            where (rMonitor.PRMailboxDatabaseName.ToLower().Contains(ddlMailBoxDatabase.SelectedItem.ToString().ToLower()))
                                            select rMonitor;

                    if (replMonitorMBName != null && replMonitorMBName.Count() > 0)
                    {
                        var rMonitor = replMonitorMBName.FirstOrDefault();

                        lblDagName.Text = rMonitor.DAGName.ToString();
                        lblDagNameDR.Text = rMonitor.DAGName.ToString(); 
                        //if (CurrentGroup.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                        //{
                        //    lblDatabasePR.Text = rMonitor.DRMailboxDatabaseName.ToString();
                        //    lblDatabaseDR.Text = rMonitor.PRMailboxDatabaseName.ToString();
                        //}
                        //else
                        //{
                            lblDatabasePR.Text = rMonitor.PRMailboxDatabaseName.ToString();
                            lblDatabaseDR.Text = rMonitor.DRMailboxDatabaseName.ToString();
                        //}
                        lblCopyQueueLength.Text = rMonitor.CopyQueueLength.ToString();
                        lblReplayQueueLength.Text = rMonitor.ReplayQueueLength.ToString();
                        lblPRDatabaseStatus.Text = rMonitor.ReplicationStatus.ToString();
                        lblPrDbStatusIcon.Attributes.Add("class", rMonitor.ReplicationStatus == "Down" ? "health-down" : "health-up");
                        lblPrDbStatusIcon.Attributes.Add("class", rMonitor.ReplicationStatus == "Down" ? "health-down" : "health-up");
                    }
                    else
                    {
                        lblDagName.Text = "N/A";
                        lblDatabasePR.Text = "N/A";
                        lblDatabaseDR.Text = "N/A";
                        lblCopyQueueLength.Text = "N/A";
                        lblReplayQueueLength.Text = "N/A";
                        lblPRDatabaseStatus.Text = "N/A";
                    }
                }
                else
                {
                    lblDagName.Text = "N/A";
                    lblDatabasePR.Text = "N/A";
                    lblDatabaseDR.Text = "N/A";
                    lblCopyQueueLength.Text = "N/A";
                    lblReplayQueueLength.Text = "N/A";
                    lblPRDatabaseStatus.Text = "N/A";
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var bcmsException = new CpException(CpExceptionType.CommonUnhandled, "Error occure while binding exchange monitor information", ex);
                ExceptionManager.Manage(bcmsException);
            }
        }

        private void FillComponentMonitor()
        {
            try
            {
                if (CurrentGroup.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                {
                    var prServerDetails = Facade.GetServerById(CurrentGroup.DRServerId);
                    lblPRIPAddressIcon.CssClass = prServerDetails.Status.ToString().ToLower() == "down" ? "health-down" : prServerDetails.Status.ToString().ToLower() == "up" ? "health-up" : prServerDetails.Status.ToString().ToLower() == "pending" ? "fatal" : "icon-NA";
                    lblPRIPAddress.Text = prServerDetails.IPAddress != "N/A" ? CryptographyHelper.Md5Decrypt(prServerDetails.IPAddress) : "";

                    var DrServerDetails = Facade.GetServerById(CurrentGroup.PRServerId);
                    lblDRIPAddressIcon.CssClass = DrServerDetails.Status.ToString().ToLower() == "down" ? "health-down" : DrServerDetails.Status.ToString().ToLower() == "up" ? "health-up" : DrServerDetails.Status.ToString().ToLower() == "pending" ? "fatal" : "icon-NA";
                    lblDRIPAddress.Text = DrServerDetails.IPAddress != "N/A" ? CryptographyHelper.Md5Decrypt(DrServerDetails.IPAddress) : "";
                }
                else
                {
                    var prServerDetails = Facade.GetServerById(CurrentGroup.PRServerId);
                    lblPRIPAddressIcon.CssClass = prServerDetails.Status.ToString().ToLower() == "down" ? "health-down" : prServerDetails.Status.ToString().ToLower() == "up" ? "health-up" : prServerDetails.Status.ToString().ToLower() == "pending" ? "fatal" : "icon-NA";
                    lblPRIPAddress.Text = prServerDetails.IPAddress != "N/A" ? CryptographyHelper.Md5Decrypt(prServerDetails.IPAddress) : "";

                    var DrServerDetails = Facade.GetServerById(CurrentGroup.DRServerId);
                    lblDRIPAddressIcon.CssClass = DrServerDetails.Status.ToString().ToLower() == "down" ? "health-down" : DrServerDetails.Status.ToString().ToLower() == "up" ? "health-up" : DrServerDetails.Status.ToString().ToLower() == "pending" ? "fatal" : "icon-NA";
                    lblDRIPAddress.Text = DrServerDetails.IPAddress != "N/A" ? CryptographyHelper.Md5Decrypt(DrServerDetails.IPAddress) : "";
                }
                var compMonitor = Facade.GetAllExchangeDAGComponantMonitorByInfraObjectID(CurrentGroup.Id);

                if (compMonitor != null && compMonitor.Count() > 0)
                {
                    var compMonitorMBName = from ctrMonitor in compMonitor
                                            where (ctrMonitor.PRMailboxDatabase.ToLower().Contains(ddlMailBoxDatabase.SelectedItem.ToString().ToLower()))
                                            select ctrMonitor;

                    if (compMonitorMBName != null && compMonitorMBName.Count() > 0)
                    {
                        var cMonitor = compMonitorMBName.FirstOrDefault();
                        //if (CurrentGroup.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                        //{
                        //    lblPRHostName.Text = cMonitor.DRIPAddress.ToString();//IpAddress contains HostName in monitor table
                        //    lblDRHostName.Text = cMonitor.PRIPAddress.ToString();//IpAddress contains HostName in monitor table

                        //    lblPRDatabase.Text = cMonitor.DRMailboxDatabase.ToString();
                        //    lblDRDatabase.Text = cMonitor.PRMailboxDatabase.ToString();
                        //}
                        //else
                        //{
                        lblPRHostName.Text = cMonitor.PRIPAddress.ToString();//IpAddress contains HostName in monitor table
                        lblDRHostName.Text = cMonitor.DRIPAddress.ToString();//IpAddress contains HostName in monitor table

                        lblPRDatabase.Text = cMonitor.PRMailboxDatabase.ToString();
                        lblDRDatabase.Text = cMonitor.DRMailboxDatabase.ToString();
                        //}                     
                        lblPRDBState.Text = cMonitor.PRMailboxDBStatus.ToString();
                        //lblPRStatus.CssClass = cMonitor.PRMailboxDBStatus.ToString().ToLower() == "mounted" ? "icon-sync" : "health-up";
                        switch (lblPRDBState.Text.ToString().ToLower())
                        {
                            case "mounted":
                                lblPRStatus.CssClass = "icon-sync";
                                break;
                            case "dismounted":
                                lblPRStatus.CssClass = "icon-async";
                                break;
                            case "suspended":
                                lblPRStatus.CssClass = "InActive";
                                break;
                            case "healthy":
                                lblPRStatus.CssClass = "health-up";
                                break;
                            case "failed":
                                lblPRStatus.CssClass = "health-down";
                                break;
                            case "servicedown":
                                lblPRStatus.CssClass = "health-down";
                                break;
                        }
                        lblDRDBState.Text = cMonitor.DRMailboxDBStatus.ToString();
                        //lblDRState.CssClass = cMonitor.DRMailboxDBStatus.ToString().ToLower() == "mounted" ? "icon-sync" : "health-up";                       
                        switch (lblDRDBState.Text.ToString().ToLower())
                        {
                            case "mounted":
                                lblDRState.CssClass = "icon-sync";
                                break;
                            case "dismounted":
                                lblDRState.CssClass = "icon-async";
                                break;
                            case "suspended":
                                lblDRState.CssClass = "InActive";
                                break;
                            case "healthy":
                                lblDRState.CssClass = "health-up";
                                break;
                            case "failed":
                                lblDRState.CssClass = "health-down";
                                break;
                            case "servicedown":
                                lblDRState.CssClass = "health-down";
                                break;
                        }
                        lblPRDBRecovery.Text = cMonitor.PRLastGenLog.ToString();
                        lblDRDBRecovery.Text = cMonitor.DRLastLogReplayed.ToString();
                        lblLogGenrRepyTimePR.Text = cMonitor.PRLastGenLogTime.ToString();
                        lblLogGenrRepyTimeDR.Text = cMonitor.DRLastReplayedLogTime.ToString();
                        lblContentIndexStatePR.Text = cMonitor.PRContentIndexState.ToString();
                        lblContentIndexStatePRIcon.CssClass = cMonitor.PRContentIndexState.ToString().ToLower() == "healthy" ? "health-up" : "health-down";
                        lblContentIndexStateDR.Text = cMonitor.DRContentIndexState.ToString();
                        lblContentIndexStateDRIcon.CssClass = cMonitor.DRContentIndexState.ToString().ToLower() == "healthy" ? "health-up" : "health-down";
                        lblContentIndexErrorMessagePR.Text = cMonitor.PRContIndexErrorMsg.ToString();

                        lblContentIndexErrorMessageDR.Text = cMonitor.DRContIndexErrorMsg.ToString();
                        lblLatestFullBackupTimePR.Text = cMonitor.PRLatestFullbkpTime.ToString();
                        lblLatestFullBackupTimeDR.Text = cMonitor.DRLatestFullbkpTime.ToString();
                        LastLogCopyNotifiedPR.Text = "--";//cMonitor.PRLastLogCopyNotified.ToString();
                        LastLogCopyNotifiedDR.Text = cMonitor.DRLastLogCopyNotified.ToString();
                        LastLogCopiedPR.Text = "--"; //cMonitor.PRLastLogCopied.ToString();
                        LastLogCopiedDR.Text = cMonitor.DRLastLogCopied.ToString();

                        lblCurrentDatalag.Text = cMonitor.CurrentDatalag;
                    }
                    else
                    {
                        //lblPRIPAddress.Text = "N/A";
                        //lblDRIPAddress.Text = "N/A";
                        lblPRDatabase.Text = "N/A";
                        lblDRDatabase.Text = "N/A";
                        lblPRDBState.Text = "N/A";
                        lblDRDBState.Text = "N/A";
                        lblPRDBRecovery.Text = "N/A";
                        lblDRDBRecovery.Text = "N/A";
                        lblLogGenrRepyTimePR.Text = "N/A";
                        lblLogGenrRepyTimeDR.Text = "N/A";
                        lblContentIndexStatePR.Text = "N/A";
                        lblContentIndexStateDR.Text = "N/A";
                        lblContentIndexErrorMessagePR.Text = "N/A";
                        lblContentIndexErrorMessageDR.Text = "N/A";
                        lblLatestFullBackupTimePR.Text = "N/A";
                        lblLatestFullBackupTimeDR.Text = "N/A";
                        LastLogCopyNotifiedPR.Text = "N/A";
                        LastLogCopyNotifiedDR.Text = "N/A";
                        LastLogCopiedPR.Text = "N/A";
                        LastLogCopiedDR.Text = "N/A";
                    }
                }
                else
                {
                    //lblPRIPAddress.Text = "N/A";
                    //lblDRIPAddress.Text = "N/A";
                    lblPRDatabase.Text = "N/A";
                    lblDRDatabase.Text = "N/A";
                    lblPRDBState.Text = "N/A";
                    lblDRDBState.Text = "N/A";
                    lblPRDBRecovery.Text = "N/A";
                    lblDRDBRecovery.Text = "N/A";
                    lblLogGenrRepyTimePR.Text = "N/A";
                    lblLogGenrRepyTimeDR.Text = "N/A";
                    lblContentIndexStatePR.Text = "N/A";
                    lblContentIndexStateDR.Text = "N/A";
                    lblContentIndexErrorMessagePR.Text = "N/A";
                    lblContentIndexErrorMessageDR.Text = "N/A";
                    lblLatestFullBackupTimePR.Text = "N/A";
                    lblLatestFullBackupTimeDR.Text = "N/A";
                    LastLogCopyNotifiedPR.Text = "N/A";
                    LastLogCopyNotifiedDR.Text = "N/A";
                    LastLogCopiedPR.Text = "N/A";
                    LastLogCopiedDR.Text = "N/A";
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var bcmsException = new CpException(CpExceptionType.CommonUnhandled, "Error occure while binding exchange monitor information", ex);
                ExceptionManager.Manage(bcmsException);
            }
        }

        private void FillMailboxDBDropdown(int iServerId)
        {
            var dbNameList = Facade.GetDatabaseExchangeDAGByServerId(iServerId);

            if (dbNameList != null)
            {
                ddlMailBoxDatabase.DataSource = dbNameList;
                ddlMailBoxDatabase.DataTextField = "MailBoxDBName";
                ddlMailBoxDatabase.DataValueField = "BaseDatabaseId";
                ddlMailBoxDatabase.DataBind();
            }
        }

        private void BindWindowsServiceData()
        {
            if (CurrentGroupId > 0)
            {
                var serviceData = Facade.GeExchangeDAGServiceMonitoringInfraObjectId(CurrentGroupId);
                if (serviceData != null && serviceData.Id > 0)
                {
                    lblPRTopologyMode.Text = serviceData.ActiveDirectoryTopologyPRMode;   //ActiveDirectoryTopology_PR_Mode;
                    lblPRTopologystatus.Text = serviceData.ActiveDirectoryTopologyPRStatus;
                    lblDRTopologyMode.Text = serviceData.ActiveDirectoryTopologyDRMode;
                    lblDRTopologystatus.Text = serviceData.ActiveDirectoryTopologyDRStatus;

                    lblPRAddressBookMode.Text = serviceData.AddressBookPRMode;
                    lblPRAddressBookStatus.Text = serviceData.ActiveDirectoryTopologyPRStatus;
                    lblDRAddressBookMode.Text = serviceData.AddressBookDRMode;
                    lblDRAddressBookStatus.Text = serviceData.ActiveDirectoryTopologyDRStatus;

                    lblPRAntispamMode.Text = serviceData.AntispamUpdatePRMode;
                    lblPRAntispamStatus.Text = serviceData.AntispamUpdatePRStatus;
                    lblDRAntispamMode.Text = serviceData.AntispamUpdateDRMode;
                    lblDRAntispamStatus.Text = serviceData.AntispamUpdateDRStatus;

                    lblPREdgeSyncMode.Text = serviceData.EdgeSyncPRMode;
                    lblPREdgeSyncstatus.Text = serviceData.EdgeSyncPRStatus;
                    lblDREdgeSyncMode.Text = serviceData.EdgeSyncDRMode;
                    lblDREdgeSyncstatus.Text = serviceData.EdgeSyncDRStatus;

                    lblPRFileDistributionMode.Text = serviceData.FileDistributionPRMode;
                    lblPRFileDistributionstatus.Text = serviceData.FileDistributionPRStatus;
                    lblDRFileDistributionMode.Text = serviceData.FileDistributionDRMode;
                    lblDRFileDistributionstatus.Text = serviceData.FileDistributionDRStatus;

                    lblPRAuthenticationMode.Text = serviceData.AuthenticationServicePRMode;
                    lblPRAuthenticationStatus.Text = serviceData.AuthenticationServicePRStatus;
                    lblDRAuthenticationMode.Text = serviceData.AuthenticationServiceDRMode;
                    lblDRAuthenticationStatus.Text = serviceData.AuthenticationServiceDRStatus;

                    lblPRIMAP4Mode.Text = serviceData.Imap4PRMode;
                    lblPRIMAP4Status.Text = serviceData.Imap4PRStatus;
                    lblDRIMAP4Mode.Text = serviceData.Imap4DRMode;
                    lblDRIMAP4Status.Text = serviceData.Imap4DRStatus;

                    lblPRInformationStoreMode.Text = serviceData.InformationStorePRMode;
                    lblPRInformationStorestatus.Text = serviceData.InformationStorePRStatus;
                    lblDRInformationStoreMode.Text = serviceData.InformationStoreDRMode;
                    lblDRInformationStorestatus.Text = serviceData.SearchIndexerDRStatus;

                    lblPRMailSubmissionMode.Text = serviceData.MailSubmissionPRMode;
                    lblPRMailSubmissionStatus.Text = serviceData.MailSubmissionPRStatus;
                    lblDRMailSubmissionMode.Text = serviceData.MailSubmissionDRMode;
                    lblDRMailSubmissionStatus.Text = serviceData.MailSubmissionDRStatus;

                    lblPRMailboxAssistantsMode.Text = serviceData.MailboxAssistantsPRMode;
                    lblPRMailboxAssistantsStatus.Text = serviceData.MailboxAssistantsPRStatus;
                    lblDRMailboxAssistantsMode.Text = serviceData.MailboxAssistantsDRMode;
                    lblDRMailboxAssistantsStatus.Text = serviceData.MailboxAssistantsDRStatus;

                    lblPRMailboxReplicationMode.Text = serviceData.MailboxReplicationPRMode;
                    lblPRMailboxReplicationStatus.Text = serviceData.MailboxReplicationPRStatus;
                    lblDRMailboxReplicationMode.Text = serviceData.MailboxReplicationDRMode;
                    lblDRMailboxReplicationStatus.Text = serviceData.MailboxReplicationDRStatus;

                    lblPRExchangeMonitoringMode.Text = serviceData.MonitoringPRMode;
                    lblPRExchangeMonitoringStatus.Text = serviceData.MonitoringPRStatus;
                    lblDRExchangeMonitoringMode.Text = serviceData.MonitoringDRMode;
                    lblDRExchangeMonitoringStatus.Text = serviceData.MonitoringDRStatus;

                    lblPrPOP3Mode.Text = serviceData.Pop3PRMode;
                    lblPrPOP3Status.Text = serviceData.Pop3PRStatus;
                    lblDrPOP3Mode.Text = serviceData.Pop3DRMode;
                    lblDrPOP3Status.Text = serviceData.Pop3DRStatus;

                    lblPRHostMode.Text = serviceData.ProtectedServiceHostPRMode;
                    lblPRHostStatus.Text = serviceData.ProtectedServiceHostPRStatus;
                    lblDRHostMode.Text = serviceData.ProtectedServiceHostDRMode;
                    lblDRHostStatus.Text = serviceData.ProtectedServiceHostDRStatus;

                    lblPrExchangeReplicationMode.Text = serviceData.ReplicationPRMode;
                    lblPrExchangeReplicationStatus.Text = serviceData.ReplicationPRStatus;
                    lblDrExchangeReplicationMode.Text = serviceData.ReplicationDRMode;
                    lblDrExchangeReplicationStatus.Text = serviceData.ReplicationDRStatus;

                    lblPRRPCClientAccessMode.Text = serviceData.RpcClientAccessPRMode;
                    lblPRRPCClientAccessStatus.Text = serviceData.RpcClientAccessPRStatus;
                    lblDRRPCClientAccessMode.Text = serviceData.RpcClientAccessDRMode;
                    lblDRRPCClientAccessStatus.Text = serviceData.RpcClientAccessDRStatus;

                    lblPRIndexerMode.Text = serviceData.SearchIndexerPRMode;
                    lblPRIndexerStatus.Text = serviceData.SearchIndexerPRStatus;
                    lblDRIndexerMode.Text = serviceData.SearchIndexerDRMode;
                    lblDRIndexerStatus.Text = serviceData.SearchIndexerDRStatus;

                    lblPRBackupMode.Text = serviceData.WindowsServerBackupPRMode;
                    lblPRBackupStatus.Text = serviceData.WindowsServerBackupPRStatus;
                    lblDRBackupMode.Text = serviceData.WindowsServerBackupDRMode;
                    lblDRBackupStatus.Text = serviceData.WindowsServerBackupDRStatus;

                    lblPRServiceHostMode.Text = serviceData.ServiceHostPRMode;
                    lblPRServiceHostStatus.Text = serviceData.ServiceHostPRStatus;
                    lblDRServiceHostMode.Text = serviceData.ServiceHostDRMode;
                    lblDRServiceHostStatus.Text = serviceData.ServiceHostDRStatus;

                    lblPRAttendantMode.Text = serviceData.SystemAttendantPRMode;
                    lblPRAttendantStatus.Text = serviceData.SystemAttendantPRStatus;
                    lblDRAttendantMode.Text = serviceData.SystemAttendantDRMode;
                    lblDRAttendantStatus.Text = serviceData.SystemAttendantDRStatus;

                    lblPRThrottlingMode.Text = serviceData.ThrottlingPRMode;
                    lblPRThrottlingStatus.Text = serviceData.ThrottlingPRStatus;
                    lblDRThrottlingMode.Text = serviceData.ThrottlingDRMode;
                    lblDRThrottlingStatus.Text = serviceData.ThrottlingDRStatus;

                    lblPRTransportMode.Text = serviceData.TransportPRMode;
                    lblPRTransportStatus.Text = serviceData.TransportPRStatus;
                    lblDRTransportMode.Text = serviceData.TransportDRMode;
                    lblDRTransportStatus.Text = serviceData.TransportDRStatus;

                    lblPRLogSearchMode.Text = serviceData.TransportLogSearchPRMode;
                    lblPRLogSearchStatus.Text = serviceData.TransportLogSearchPRStatus;
                    lblDRLogSearchMode.Text = serviceData.TransportLogSearchDRMode;
                    lblDRLogSearchStatus.Text = serviceData.TransportLogSearchDRStatus;

                    lblPRClusterServiceMode.Text = serviceData.ClusterServicePRMode;
                    lblPRClusterServiceStatus.Text = serviceData.ClusterServicePRStatus;
                    lblDRClusterServiceMode.Text = serviceData.ClusterServiceDRMode;
                    lblDRClusterServiceStatus.Text = serviceData.ClusterServiceDRStatus;

                    lblPRFirewallMode.Text = serviceData.WindowsFirewallPRMode;
                    lblPRFirewallStatus.Text = serviceData.WindowsFirewallPRStatus;
                    lblDRFirewallMode.Text = serviceData.WindowsFirewallDRMode;
                    lblDRFirewallStatus.Text = serviceData.WindowsFirewallDRStatus;
                }
            }
        }

        protected void LvServiceDetails(object sender, ListViewItemEventArgs e)
        {
            var lblPRArrow = e.Item.FindControl("PRARROW") as Label;
            var lblPRStatus = e.Item.FindControl("PRServiceSTATUS") as Label;

            var lblDRArrow = e.Item.FindControl("DRARROW") as Label;
            var lblDRStatus = e.Item.FindControl("DRServiceSTATUS") as Label;

            if (lblPRStatus != null && lblPRStatus.Text == "Running")
            {
                lblPRStatus.CssClass = "active";
                if (lblPRArrow != null) lblPRArrow.CssClass = "Active float-left";
            }
            if (lblPRStatus != null && lblPRStatus.Text == "Stopped")
            {
                lblPRStatus.CssClass = "inactive";
                if (lblPRArrow != null) lblPRArrow.CssClass = "InActive float-left";
            }
            if (lblDRStatus != null && lblDRStatus.Text == "Running")
            {
                lblDRStatus.CssClass = "active";
                if (lblDRArrow != null) lblDRArrow.CssClass = "Active float-left";
            }
            if (lblDRStatus != null && lblDRStatus.Text == "Stopped")
            {
                lblDRStatus.CssClass = "inactive";
                if (lblDRArrow != null) lblDRArrow.CssClass = "InActive float-left";
            }
        }

        protected void DdlMailBoxDatabaseIndexChanged(object sender, EventArgs e)
        {
            //string selectedMailboxDB = ddlMailBoxDatabase.SelectedItem.ToString();
            FillComponentMonitor();
            FillReplicationMonitor();
        }
    }
}