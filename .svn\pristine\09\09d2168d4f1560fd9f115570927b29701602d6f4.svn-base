﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Helper;

namespace CP.DataAccess
{
    internal sealed class ApplicationGroupBuilder : IEntityBuilder<ApplicationGroup>
    {
        IList<ApplicationGroup> IEntityBuilder<ApplicationGroup>.BuildEntities(IDataReader reader)
        {
            var applicationGroups = new List<ApplicationGroup>();

            while (reader.Read())
            {
                applicationGroups.Add(((IEntityBuilder<ApplicationGroup>)this).BuildEntity(reader,
                    new ApplicationGroup()));
            }

            return (applicationGroups.Count > 0) ? applicationGroups : null;
        }

        ApplicationGroup IEntityBuilder<ApplicationGroup>.BuildEntity(IDataReader reader,
            ApplicationGroup applicationgroups)
        {
            //Fields in bcms_group table on 16/07/2013 : Id, Name, Details, GroupId, ApplicationGroupGroupId, PRAppServerId, DRAppServerId, Severity, Process, Path, IsMonitor, IsReplication, Method, Status, ReplicationId, IsActive, CreatorId, CreateDate, UpdatorId, UpdateDate

            applicationgroups.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            applicationgroups.Name = Convert.IsDBNull(reader["Name"])
                ? string.Empty
                : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Name"]));
            applicationgroups.Details = Convert.IsDBNull(reader["Details"])
                ? string.Empty
                : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Details"]));
            applicationgroups.GroupId = Convert.IsDBNull(reader["GroupId"]) ? 0 : Convert.ToInt32(reader["GroupId"]);
            applicationgroups.BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"])
                ? 0
                : Convert.ToInt32(reader["BusinessServiceId"]);
            applicationgroups.ApplicationId = Convert.IsDBNull(reader["ApplicationId"])
                ? 0
                : Convert.ToInt32(reader["ApplicationId"]);
            applicationgroups.PRApplicationServerId = Convert.IsDBNull(reader["PRAppServerId"])
                ? 0
                : Convert.ToInt32(reader["PRAppServerId"]);
            applicationgroups.DRApplicationServerId = Convert.IsDBNull(reader["DRAppServerId"])
                ? 0
                : Convert.ToInt32(reader["DRAppServerId"]);
            applicationgroups.Severity = Convert.IsDBNull(reader["Severity"])
                ? string.Empty
                : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Severity"]));
            applicationgroups.Process = Convert.IsDBNull(reader["Process"])
                ? string.Empty
                : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Process"]));
            applicationgroups.Output = Convert.IsDBNull(reader["Output"])
                ? string.Empty
                : Convert.ToString(reader["Output"]);
            applicationgroups.IsMonitor = Convert.IsDBNull(reader["IsMonitor"])
                ? 0
                : Convert.ToInt32(reader["IsMonitor"]);
            applicationgroups.IsReplication = Convert.IsDBNull(reader["IsReplication"])
                ? 0
                : Convert.ToInt32(reader["IsReplication"]);
            applicationgroups.Method = Convert.IsDBNull(reader["Method"])
                ? string.Empty
                : CryptographyHelper.Md5Decrypt(Convert.ToString(reader["Method"]));
            applicationgroups.Status = Convert.IsDBNull(reader["Status"])
                ? string.Empty
                : Convert.ToString(reader["Status"]);
            applicationgroups.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                ? 0
                : Convert.ToInt32(reader["ReplicationId"]);
            applicationgroups.RecoveryTime = Convert.IsDBNull(reader["RecoveryTime"])
                ? 0
                : Convert.ToInt32(reader["RecoveryTime"]);
            applicationgroups.ConfigureDataLag = Convert.IsDBNull(reader["ConfigureDataLag"])
                ? 0
                : Convert.ToInt32(reader["ConfigureDataLag"]);
            applicationgroups.MTPOD = Convert.IsDBNull(reader["MTPOD"]) ? 0 : Convert.ToInt32(reader["MTPOD"]);
            applicationgroups.DROperationStatus = Convert.IsDBNull(reader["DROperationStatus"])
                ? 0
                : Convert.ToInt32(reader["DROperationStatus"]);
            applicationgroups.DROperationId = Convert.IsDBNull(reader["DROperationId"])
                ? 0
                : Convert.ToInt32(reader["DROperationId"]);

            applicationgroups.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            applicationgroups.CreatorId = Convert.IsDBNull(reader["CreatorId"])
                ? 0
                : Convert.ToInt32(reader["CreatorId"]);
            applicationgroups.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            applicationgroups.UpdatorId = Convert.IsDBNull(reader["UpdatorId"])
                ? 0
                : Convert.ToInt32(reader["UpdatorId"]);
            applicationgroups.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());

            return applicationgroups;
        }
    }
}