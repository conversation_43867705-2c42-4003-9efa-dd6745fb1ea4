﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ExchangeDatabaseConfig.ascx.cs"
    Inherits="CP.UI.Controls.ExchangeDatabaseConfig" %>
<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">Exchange Database</h4>
        </div>
        <div class="widget-body">
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Database Group Name<span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtDbGroupName" CssClass="form-control" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" Display="Dynamic" CssClass="error"
                        ValidationGroup="dbConfig" ControlToValidate="txtDbGroupName" ErrorMessage="Enter Group Name"></asp:RequiredFieldValidator>
                      <asp:Label ID="lblErr" runat="server" ForeColor="Red"
                                            Text="Invalid License Key" Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label">
                    Mailbox Database <span class="inactive">*</span></label>
                <div class="col-md-9">
                    <asp:TextBox ID="txtMailboxDb" CssClass="form-control" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" Display="Dynamic" CssClass="error"
                        ValidationGroup="dbConfig" ControlToValidate="txtMailboxDb" ErrorMessage="Enter Mailbox Database"></asp:RequiredFieldValidator>
                </div>
            </div>
        </div>
    </div>
</div>