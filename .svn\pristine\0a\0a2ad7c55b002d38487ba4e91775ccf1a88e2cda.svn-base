﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class DatabasePostgreSqlBuilder : IEntityBuilder<PostgreSql>
    {
        IList<PostgreSql> IEntityBuilder<PostgreSql>.BuildEntities(IDataReader reader)
        {
            var databasepostgresqls = new List<PostgreSql>();

            while (reader.Read())
            {
                databasepostgresqls.Add(((IEntityBuilder<PostgreSql>)this).BuildEntity(reader, new PostgreSql()));
            }

            return (databasepostgresqls.Count > 0) ? databasepostgresqls : null;
        }

        PostgreSql IEntityBuilder<PostgreSql>.BuildEntity(IDataReader reader, PostgreSql databasepostgresql)
        {
            databasepostgresql.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            databasepostgresql.BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"])
                ? 0
                : Convert.ToInt32(reader["BaseDatabaseId"]);
            databasepostgresql.DatabaseName = Convert.IsDBNull(reader["DatabaseName"])
                ? string.Empty
                : Convert.ToString(reader["DatabaseName"]);
            databasepostgresql.UserName = Convert.IsDBNull(reader["UserName"])
                ? string.Empty
                : Convert.ToString(reader["UserName"]);
            databasepostgresql.Password = Convert.IsDBNull(reader["Password"])
                ? string.Empty
                : Convert.ToString(reader["Password"]);
            databasepostgresql.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);

            databasepostgresql.ArchiveLog = Convert.IsDBNull(reader["Archive"])
                ? string.Empty
                : Convert.ToString(reader["Archive"]);

            return databasepostgresql;
        }
    }
}