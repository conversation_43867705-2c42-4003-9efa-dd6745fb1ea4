﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CP.UI.Admin
{
    public class CustomHeaderModule:IHttpModule
    {
        public void Init(HttpApplication context)
        {
            context.PreSendRequestHeaders += OnPreSendRequestHeaders;
        }

        public void Dispose() { }

        void OnPreSendRequestHeaders(object sender, EventArgs e)
        {
            HttpContext.Current.Response.Headers.Remove("Server");
            // Or you can set something funny
            //HttpContext.Current.Response.Headers.Set("Server", "abc");
        }
    }
}