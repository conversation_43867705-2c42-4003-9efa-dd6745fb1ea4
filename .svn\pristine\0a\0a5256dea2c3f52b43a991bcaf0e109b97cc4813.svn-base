﻿using CP.Common.DatabaseEntity;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI
{
    public partial class BusinessServiceDRReadiness : InfraObjectsBasePage
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(BusinessServiceDRReadiness));

        public override void PrepareView()
        {
            try
            {
                BindBSDRReadinessList();
            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured in PrepareView");

                _logger.Info("Exception Occured in PrepareView: " + ex.Message.ToString());

                if (ex.InnerException != null)
                    _logger.Info("Exception Occured in PrepareView: " + ex.InnerException.Message.ToString());
            }
        }

        public void BindBSDRReadinessList()
        {
            try
            {
                //IList<BSDRReadiness> _lstbsdrreadiness = BSDRReadinessList();
                //lvComponent.DataSource = _lstbsdrreadiness;
                //lvComponent.DataBind();

                lvComponent.DataSource = BSDRReadinessList();
                lvComponent.DataBind();
            }
            catch (Exception ex)
            {
                throw (ex);
            }
        }

        private IList<BSDRReadiness> BSDRReadinessList()
        {
            IList<BSDRReadiness> _lstbsdrreadiness = new List<BSDRReadiness>();
            try
            {
                //bool result = false;
                //Business service
                var _bsdetails = Facade.GetAllBusinessServices();
                if (_bsdetails != null)
                {
                    foreach (var _strBsdetails in _bsdetails)
                    {
                        BSDRReadiness _strbsdetails = new BSDRReadiness();
                        string _strinfranames = string.Empty;
                        string InfraId = string.Empty;
                        string WFIds = string.Empty;
                        string _strwfnames = string.Empty;
                        if (_strBsdetails != null)
                        {
                            //Getting infraobject list from business services list
                            var _strinfradetails = Facade.GetInfraObjectByBusinessServiceId(_strBsdetails.Id);
                            if (_strinfradetails != null)
                            {
                                foreach (var _strinfra in _strinfradetails)
                                {
                                    _strinfranames = _strinfranames + GetInfraName(Convert.ToString(_strinfra.Id)) + ",";
                                    InfraId = InfraId + _strinfra.Id + ",";

                                    //Workflowationlist
                                    var wfactionlist = Facade.GetAllGroupWorkflowByInfraObjectId(Convert.ToInt32(_strinfra.Id));
                                    if (wfactionlist != null)
                                    {
                                        foreach (var _strwf in wfactionlist)
                                        {
                                            if (_strwf.WorkflowId > 0)
                                            {
                                                var WFDetails = Facade.GetDRReadyWorkflowbyWFId(_strwf.WorkflowId);
                                                if (WFDetails != null)
                                                {
                                                    _strwfnames = _strwfnames + WFDetails.Name + ",";
                                                     WFIds = WFIds + _strwf.WorkflowId + ",";

                                                }
                                            }
                                            //        var wfdetails = Facade.GetWorkflowById(Convert.ToInt32(_strwf.WorkflowId));
                                            //        if (wfdetails != null)
                                            //        {
                                            //            _strwfnames = _strwfnames + wfdetails.Name + ",";
                                            //            WFIds = WFIds + _strwf.WorkflowId + ",";
                                            //            if (!string.IsNullOrEmpty(wfdetails.ActionIds))
                                            //            {
                                            //                string[] actionsids = (wfdetails.ActionIds).Split(',');
                                            //                foreach (string str in actionsids)
                                            //                {
                                            //                    if (!string.IsNullOrEmpty(str))
                                            //                    {
                                            //                        var wfactions = Facade.GetWorkflowActionById(Convert.ToInt32(str));
                                            //                        if (wfactions != null)
                                            //                        {
                                            //                            if (wfactions.ActionType == 994)
                                            //                            {
                                            //                                //result = true;
                                            //                            }
                                            //                        }
                                            //                    }
                                            //                }
                                            //            }
                                            //        }
                                        }

                                    }
                                }
                            }
                        }
                        _strbsdetails.BusinessserviceId = Convert.ToString(_strBsdetails.Id);
                        _strbsdetails.InfraobjectId = InfraId;
                        _strbsdetails.InfraName = !string.IsNullOrEmpty(_strinfranames) ? _strinfranames.Trim(',') : "NA";
                        _strbsdetails.WorkflowId = WFIds;
                        _strbsdetails.WorkflowName = !string.IsNullOrEmpty(_strwfnames) ? _strwfnames.Trim(',') : "NA";
                        if (_strBsdetails.Id > 0)
                        {
                            var bsdetails = Facade.BSReadinessDetailsGetById(_strBsdetails.Id);
                            if (bsdetails != null)
                            {
                                _strbsdetails.Status = bsdetails.Status;
                            }
                            else
                            {
                                _strbsdetails.Status = "0";
                            }
                        }
                        _lstbsdrreadiness.Add(_strbsdetails);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured in BSDRReadinessList");

                _logger.Info("Exception Occured in BSDRReadinessList: " + ex.Message.ToString());

                if (ex.InnerException != null)
                    _logger.Info("Exception Occured in BSDRReadinessList: " + ex.InnerException.Message.ToString());
            }
            return _lstbsdrreadiness;
        }

        private IList<BSDRReadiness> GetBSDRReadinessListBySearch(string value)
        {
            var BSDRreadyList = BSDRReadinessList();
            value = value.Trim();

            if (!String.IsNullOrEmpty(value) && BSDRreadyList != null)
            {
                BusinessService _bsdetails = Facade.GetBusinessServiceByName(value);
                if (_bsdetails != null)
                {
                    var result = (from sites in BSDRreadyList
                                  where sites.BusinessserviceId == Convert.ToString(_bsdetails.Id)
                                  select sites).ToList();

                    return result;
                }
            }
            return null;
        }

        protected void lvComponent_PreRender(object sender, EventArgs e)
        {
            try
            {
                if (IsPostBack)
                {
                    if (String.IsNullOrEmpty(txtsearchvalue.Text))
                    {
                        lvComponent.DataSource = BSDRReadinessList();
                        lvComponent.DataBind();
                    }
                    else
                    {
                        lvComponent.DataSource = GetBSDRReadinessListBySearch(txtsearchvalue.Text);
                        lvComponent.DataBind();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured in lvComponent_PreRender");

                _logger.Info("Exception Occured in lvComponent_PreRender: " + ex.Message.ToString());

                if (ex.InnerException != null)
                    _logger.Info("Exception Occured in lvComponent_PreRender: " + ex.InnerException.Message.ToString());
            }
        }

        protected void lvComponent_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            try
            {
            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured in lvComponent_ItemDataBound");

                _logger.Info("Exception Occured in lvComponent_ItemDataBound: " + ex.Message.ToString());

                if (ex.InnerException != null)
                    _logger.Info("Exception Occured in lvComponent_ItemDataBound: " + ex.InnerException.Message.ToString());
            }

        }

        protected void lvComponent_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "Disable")
                {
                    Label lblID = e.Item.FindControl("ID") as Label;
                    Label lblbsId = e.Item.FindControl("lblbsId") as Label;
                    Label lblbsname = e.Item.FindControl("lblbsname") as Label;
                    Label lblinfraId = e.Item.FindControl("lblinfraId") as Label;
                    Label lblinfraname = e.Item.FindControl("lblinfraname") as Label;
                    Label lblwfId = e.Item.FindControl("lblwfId") as Label;
                    Label lblwfname = e.Item.FindControl("lblwfname") as Label;
                    ImageButton lblStatusIcon = e.Item.FindControl("lblStatusIcon") as ImageButton;

                    if (lblbsId != null && lblbsname != null && lblinfraId != null && lblinfraname != null && lblwfId != null && lblwfname != null && lblStatusIcon != null)
                    {
                        BSDRReadiness _bsdrreadiness = new BSDRReadiness();

                        _bsdrreadiness.BusinessserviceId = lblbsId.Text;
                        _bsdrreadiness.InfraobjectId = string.IsNullOrEmpty(lblinfraId.Text) ? string.Empty : (lblinfraId.Text).TrimEnd();
                        _bsdrreadiness.InfraName = string.IsNullOrEmpty(lblinfraname.Text) ? string.Empty : (lblinfraname.Text).TrimEnd();
                        _bsdrreadiness.WorkflowId = string.IsNullOrEmpty(lblwfId.Text) ? string.Empty : (lblwfId.Text).TrimEnd();
                        _bsdrreadiness.WorkflowName = string.IsNullOrEmpty(lblwfname.Text) ? string.Empty : (lblwfname.Text).TrimEnd();
                        _bsdrreadiness.CreatorId = LoggedInUserId;
                        if (lblStatusIcon.ToolTip == "Enable")
                        {
                            _bsdrreadiness.Status = "1";
                            _bsdrreadiness = Facade.AddBSReadinessDetails(_bsdrreadiness);
                        }
                        else
                        {
                            _bsdrreadiness.Status = "0";
                            if (!string.IsNullOrEmpty(lblbsId.Text))
                            {
                                BSDRReadiness bsdetails = Facade.BSReadinessDetailsGetById(Convert.ToInt32(lblbsId.Text));
                                if (bsdetails != null)
                                {
                                    _bsdrreadiness.Id = Convert.ToInt32(bsdetails.Id);
                                    _bsdrreadiness = Facade.UpdateBSReadinessDetails(_bsdrreadiness);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured in LvComponentItemCommand");

                _logger.Info("Exception Occured in LvComponentItemCommand: " + ex.Message.ToString());

                if (ex.InnerException != null)
                    _logger.Info("Exception Occured in LvComponentItemCommand: " + ex.InnerException.Message.ToString());
            }
        }

        protected void lvComponent_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            try
            {
                Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
            }
            catch (Exception ex)
            {
                throw (ex);
            }
        }

        public string GetInfraName(object type)
        {
            string infraname = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(Convert.ToString(type)))
                {
                    var infradetails = Facade.GetInfraObjectById(Convert.ToInt32(type));
                    if (infradetails != null)
                    {
                        infraname = infradetails.Name;
                    }
                }
            }
            catch (Exception ex)
            {
                throw (ex);
            }
            return infraname;
        }

        public string GetBSName(object type)
        {
            string BSname = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(Convert.ToString(type)))
                {
                    var BSdetails = Facade.GetBusinessServiceById(Convert.ToInt32(type));
                    if (BSdetails != null)
                    {
                        BSname = BSdetails.Name;
                    }
                }
            }
            catch (Exception ex)
            {
                throw (ex);
            }
            return BSname;
        }

        protected string CheckDisable(object act)
        {
            string val = Convert.ToString(act);
            string strActive = string.Empty;
            switch (val)
            {
                case "0":
                case "":
                    strActive = "../Images/icons/cross-circle.png";
                    break;

                case "1":
                    strActive = "../Images/icons/cross-circle_disable.png";
                    break;
            }
            return strActive;
        }

        protected string CheckToolTip(object tool)
        {
            string toolid = Convert.ToString(tool);
            string strtool = string.Empty;
            switch (toolid)
            {
                case "0":
                case "":
                    strtool = "Enable";
                    break;

                case "1":
                    strtool = "Disable";
                    break;
            }
            return strtool;
        }

    }
}