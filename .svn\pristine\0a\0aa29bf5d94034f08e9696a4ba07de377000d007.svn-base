﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IRoboCopyOptionsDataAccess
    {
        RoboCopyOptions AddRoboCopyOption(RoboCopyOptions robocopy);

        RoboCopyOptions GetByRoboCopyOptionId(int id);

        RoboCopyOptions UpdateRobocopyoptionss(RoboCopyOptions robocopy);

        IList<RoboCopyOptions> GetAllRoboCopyOptions();

        bool DeleteRoboCopyOptionById(int id);

        bool IsExistRoboCopyOptionByNamee(string name);

        IList<RoboCopyOptions> GetRobooptionsListUserinfraId(int userid, int companyid, string role);
    }
}
