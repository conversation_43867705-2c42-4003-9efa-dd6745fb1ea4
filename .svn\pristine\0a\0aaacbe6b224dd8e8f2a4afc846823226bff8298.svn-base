﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.UI.Admin.DashboardContols
{
    public partial class DataCenter : BaseControl
    {
        #region Variable

        private static int _currentLoginUserId;
        private static int _companyId;
        private static bool _isParent;

        #endregion Variable

        #region Events

        /// <summary>
        /// Page load event
        /// </summary>
        /// <author><PERSON>-30/04/2014</author>
        public override void PrepareView()
        {
            if (LoggedInUserId > 0)
            {
                _currentLoginUserId = LoggedInUserId;
                _companyId = LoggedInUserCompanyId;
                _isParent = IsParentCompnay;
            }
            if (!IsPostBack)
            {
                GetSiteLocations(_companyId, _isParent);
            }
        }

        #endregion Events

        #region Methods

        #region Private Methods

        /// <summary>
        /// Get site location for given comapanyId
        /// </summary>
        /// <param name="companyId">companyId</param>
        /// <param name="isParent">isParent</param>
        /// <author><PERSON>-30/04/2014</author>
        private void GetSiteLocations(int companyId, bool isParent)
        {
            IList<Site> listSite = Facade.GetSitesByCompanyId(companyId, isParent);

            if (listSite != null)
            {
                var prSiteName = listSite.Where(a => Convert.ToString(a.Type).Equals(SiteType.PRSite.ToString(), StringComparison.OrdinalIgnoreCase)).ToList();
                var drSiteName = listSite.Where(a => Convert.ToString(a.Type).Equals(SiteType.DRSite.ToString(), StringComparison.OrdinalIgnoreCase)).ToList();

                if (prSiteName.Count() > 0)
                {
                    lblPRSite.Text = prSiteName[0].Location;
                    lblPRSite.ToolTip = prSiteName[0].Location;
                }
                else
                    lblPRSite.Text = "N/A";

                if (drSiteName.Count() > 0)
                {
                    lblDRSite.Text = drSiteName[0].Location;
                    lblDRSite.ToolTip = drSiteName[0].Location;
                }
                else
                    lblDRSite.Text = "N/A";
            }
        }

        #endregion Private Methods

        #endregion Methods
    }
}