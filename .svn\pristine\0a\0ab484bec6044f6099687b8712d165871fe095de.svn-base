﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
    internal sealed class Postgre9xMonitorStatusBuilder : IEntityBuilder<Postgre9xMonitorStatus>
    {
        IList<Postgre9xMonitorStatus> IEntityBuilder<Postgre9xMonitorStatus>.BuildEntities(IDataReader reader)
        {
            var postgrereplication = new List<Postgre9xMonitorStatus>();
            while (reader.Read())
            {
                postgrereplication.Add(((IEntityBuilder<Postgre9xMonitorStatus>)this).BuildEntity(reader, new Postgre9xMonitorStatus()));
            }
            return postgrereplication;
        }

        Postgre9xMonitorStatus IEntityBuilder<Postgre9xMonitorStatus>.BuildEntity(IDataReader reader, Postgre9xMonitorStatus postgrereplication)
        {

            postgrereplication.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            postgrereplication.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            postgrereplication.ReplicationStatusPR = Convert.IsDBNull(reader["ReplicationStatusPR"]) ? string.Empty : Convert.ToString(reader["ReplicationStatusPR"]);
            postgrereplication.ReplicationStatusDR = Convert.IsDBNull(reader["ReplicationStatusDR"]) ? string.Empty : Convert.ToString(reader["ReplicationStatusDR"]);
            postgrereplication.DataDirectoryPathPR = Convert.IsDBNull(reader["DataDirectoryPathPR"]) ? string.Empty : Convert.ToString(reader["DataDirectoryPathPR"]);
            postgrereplication.DataDirectoryPathDR = Convert.IsDBNull(reader["DataDirectoryPathDR"]) ? string.Empty : Convert.ToString(reader["DataDirectoryPathDR"]);
            postgrereplication.Current_xlog_location = Convert.IsDBNull(reader["Current_xlog_location"]) ? string.Empty : Convert.ToString(reader["Current_xlog_location"]);
            postgrereplication.Last_xlog_receive_location = Convert.IsDBNull(reader["Last_xlog_receive_location"]) ? string.Empty : Convert.ToString(reader["Last_xlog_receive_location"]);
            postgrereplication.Last_xlog_replay_location = Convert.IsDBNull(reader["Last_xlog_replay_location"]) ? string.Empty : Convert.ToString(reader["Last_xlog_replay_location"]);
            postgrereplication.DataLag_MB = Convert.IsDBNull(reader["DataLag_MB"]) ? string.Empty : Convert.ToString(reader["DataLag_MB"]);
            postgrereplication.DataLag_HHMMSS = Convert.IsDBNull(reader["DataLag_HHMMSS"]) ? string.Empty : Convert.ToString(reader["DataLag_HHMMSS"]);
            postgrereplication.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            postgrereplication.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
            postgrereplication.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            postgrereplication.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]);
            postgrereplication.CurrentXlogFileName = Convert.IsDBNull(reader["CurrentXlogFileName"]) ? string.Empty : Convert.ToString(reader["CurrentXlogFileName"]);
            postgrereplication.XlogReceiveFileName = Convert.IsDBNull(reader["XlogReceiveFileName"]) ? string.Empty : Convert.ToString(reader["XlogReceiveFileName"]);
            postgrereplication.XlogReplayFileName = Convert.IsDBNull(reader["XlogReplayFileName"]) ? string.Empty : Convert.ToString(reader["XlogReplayFileName"]);
            return postgrereplication;
        }
    }
}
