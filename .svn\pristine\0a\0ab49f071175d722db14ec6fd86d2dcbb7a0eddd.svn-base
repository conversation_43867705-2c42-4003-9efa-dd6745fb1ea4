﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAgEAAAAAAAD/7gAOQWRvYmUAZAAAAAAB/9sAQwAGBAQEBQQGBQUGCQYFBgkLCAYG
        CAsMCgoLCgoMEAwMDAwMDBAMDg8QDw4MExMUFBMTHBsbGxwfHx8fHx8fHx8f/9sAQwEHBwcNDA0YEBAY
        GhURFRofHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgA
        LQDIAwERAAIRAQMRAf/EABsAAQACAwEBAAAAAAAAAAAAAAAFBgQHCAMC/8QALxAAAQMEAQMDAwMEAwAA
        AAAAAgEDBAARBQYSITEHQSITUWEIMkIUcYFSI5EkFv/EABoBAQADAQEBAAAAAAAAAAAAAAACAwQFBgH/
        xAAuEQACAgIABAQFBAMBAAAAAAAAAQIDEQQhMRIFQVEiE/BhcYHRocHxQpEyFAb/2gAMAwEAAhEDEQA/
        AOqaAUAoBQFK8h+VcBpjHxvf9zLuDyYxzZIhcf8AN0uqNh916r6ItVWXRj9To6PbbNh8OEfMpeb3fzFF
        1L/2byYvFY0iaNjEuA47JNp4kFvkVxG68kLj0W307VXKc1Hq4I30aepK32V1ylx9XJZRcPE/kdd1xEg5
        TIRsrjzFuY02qq2QmN23Qv2Q+JdPRUqyqzqRh7nof880k8xlyLzVpzRQCgFAKAUAoBQCgFAKAUAoBQCg
        FAKAUAoBQCgKz5D3NjU9cfyKohy1RQhsr+5xeyr9krJt7HtpJf7yeF+fojf27SexZj+q4s591Hxtt/kW
        fIzM55Y8GU4pSctIRSV4r8SGOHTlxtxv0FLW+1U1UuX5PUbfcatWKhFZa/qvD6nr5sk5mNsjWvzM47l4
        cBht1oHBbbVpxxFSzqNCAmfFLoSp0Rale3nGSHZowdbsUFFt4/jJc/xlxcgWM9lTRUjvGxFZX0ImkM3P
        +PlFKnq+Jz//AEVizCP1f+f4N4VsPNigFAKAUAoBQCgFAKAUAoBQCgFAKAUAoBQCgFAc+fkHknpk1Y6K
        qxobjbFkva6gpkv9yVE/tXnbbuvdaf8ASOF+57TsdPRR1eMnn8Hv+P8Andhn7BkjyOQfexGNxjbQsmVo
        7NjRGuLY2AVRtoutrr6119eTbeTF3uiuEIqMUpSk/q/hs127Fy+97tPkxBJw8lMMkd6qgNqXFoUVf8Wx
        SyelYNnY6ZcOM5PgvjwO1Uo69CUuCjHj8fU6SGJA8feN5KRksGJhuvEaIiqbyCpKS3te5/Wutpa7ilF8
        ZN8X8zwvcNx2zlY/t+xo7xf5I2PEYPYdv2LKzcqzCBmBjYEl8ybenSVU7ChdPYIopW7Cq13NnXi5RhFY
        ODr7ElGU5Ph4ExpuA8reUIjufyu0ycNh3jMIjEJSbFxQVRJQbBRRAEkUbkqqq1XbOql9Kjl/MsqjZcup
        vESX0fUvOWsbi0EmeeU1UXialFKlC6hxrLZ5ttxScbMVstkX7LVd1tM4cFiRZTXdCXF5iVTTPI2eyvkr
        MZ+fm5o6niAm5R2F8xCysdq7UZpG0XhyJSFUH1Wr7deMa1FL1vBRTfKVjbfpRm69lvKfl7OTXYmZd1rW
        4ZKJfxFUVHldW2riok64o/rLkgp9PSo2QroSyuqRKudl7eH0xM8vHHnTV9kad1rOP5nFtk27eZKQQcRV
        s406w8Rp2/cP16daj79M4+pYfyJKi6EvS8xI/cNv2zPec2NbxGYmQsa3KjQHo8V42QVGkR6WSoK/qT3j
        f7VKqqMaHJriQsulK9RT4G0fOO0ytc8dzpMGQcbIyzbhw321sYm6XvIV7ootiaoqetY9StTsSfI2bdvR
        W2uZo+DP2R/Q3tkleT5UPIgDps4MnyN5xWyUQDkjqEhOW6ezpeujKMVZ0qvK8znxlJ19Tnx+PmXvwvt+
        z4vV8xsm/wCTea1vkymNlZJVVwjXkh/Ff/YQFcUTot1/T61m264OSjWvUadOyfQ5TfAsB/kdoIC2+UbK
        pj3jVtnJLCNIxqPfgaqilb6Il6q/4Z8uGfLJa9yCWXnBN7P5k0bXYWKmy5LkiNmWSkQDiNq9yaBB9xIl
        lG/NES/rf6VXXqzm2kuRZZswhht8yKyf5E+NYEtlhZEmS26ImUuPHM2AQu9zW3Lj+7hyt271OOlY0Qlu
        Vp4Kl+Qu/wCaZla7iNWyUiK7OaWaTsNxW1eF9Uaiohj1VCXmtv6Vfo0xfVKS5FG7e04qL5l1leWdP1V3
        HatMmy8xnmWWY7wRWjlvm+goHFxQ7umqXUe/1rOtac05JYiaffjFqOcyNhR3VdYbdVsmlcFCVpyyGN0v
        xKyql09bLWU0H3QCgFAKAUBqXyXpst7JyZIxDm46eiE8jYqagaIiKioNyT9KEhJXk+66d0L/AHa02n5e
        DPW9n36/aVcmoyj5+KJnxf49Z1/V5zfBxiXmVI3vl6uA3wUGgX+iKpW79a7mjG105n6Zy/TyOV3XdVl6
        xxjD9fMmNI0HF6vEBGhE5nDgTgjxAE9RbH0Tp1Veq180e3qn1SfVY+b/AAU9w7nPYeOUPL8lT/I+fPHQ
        wxMCM9Kfy0lttwWGXHVFllfmMlUEXj1ER69713dFL3MvwODvN+3heJriX4r2h/wbhHMbEecn/wA17LZH
        GW4ukDwE024IFx97bQh7e/VbfSta2Y++8vhjGTJLVl7CS58yU0Xy7sms6hE1tNJyMrJY9sm2TBp1ts0V
        VIScH41JOhe63fvULtaM5uXWsMsp2ZQh09LyjOiZXzWzpWxZzbScBl6C5FxOIbjj/JOTKsAOqLImbYtI
        S2Quv1+8JRp64xh58WTUrehuX2RWfHXjLY8n4q27+LHdjZKe5HYiRZAqyTzcFReIP9iDbmR8UXtdOtX3
        7EVbHyRn19aTql5s9PFe9bJ48gTsHP0/JS3JElZDatMuCfPgLagvsJCT/X0UVps0xtakpI+6tsqk4uLL
        lqOf81ZzPP5/MNngdPiqco8ebAk+402NxjtCoq8Slb3Gop68fSs1sKYx6V6pmmqdsnl8IlW/H3AZjJ+S
        MntGVgyIqMtPSBJ9txu8ie4vRPkRFKzfOr92yKrUU/hFGnW3Y5NEn+UD+XnysJhcfCkSW2QdmPEyw64K
        Gf8AqbRSAVHshdKj27pWW2S7jl4SRRs6eCnarHw2E8dTo2dFtlssycd35SJtE+VywD7ic4rfl061or6l
        PqlNYKLMOHTGDyZ2VmeUtL0bC4RyE8ISzdnq+/HSb/GUi4tRg5i622Qgimo2/dZPWoRVVljln9s/MlJ2
        11qODA8ix89Mx2Lj45dlysJ4VeVcjEVlhXRRARY8ZpsfjTkXdfr0v3qWu4ptvpX3I7MZNJLqaJPatQym
        X8i6zpzUGUuLw8eDjHpCsn8SCKI/Lc+RE4KnFe9+9RquUa5Tz6nklbS5WRjj0om/yJg5TN7bgNaxONeK
        LEYFsXmIzitg5McRtB5iPDi2DaLb0vVehJRjKTfEs3ouUoxS4EXN1Kftnm1MUDMyBhscKQo2QaZcaFlr
        Gx7ATZmPFLyOy/epxtVdOeDk/wByEqXZdjlFHx4gYyel+T3oexYR50n3CgLlljuuIw+R+18HlFU+N+/u
        LvZUVVtem01ZUnF8vAasZV2NSX3Oo6451xQCgFAKAUAoBQCgFAKAUAoBQCgFAKAWoBagCiJJZURU+i0A
        slALJQCgFkoBZKAUAoBQCgFAKAUAoBQCgFAKAUAoBQCgFAKAUAoBQCgFAKAUAoBQCgP/2Q==
</value>
  </data>
  <data name="pictureBox2.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAAAAAAAD/7gAOQWRvYmUAZAAAAAAB/9sAQwAGBAQEBQQGBQUGCQYFBgkLCAYG
        CAsMCgoLCgoMEAwMDAwMDBAMDg8QDw4MExMUFBMTHBsbGxwfHx8fHx8fHx8f/9sAQwEHBwcNDA0YEBAY
        GhURFRofHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgA
        HgDcAwERAAIRAQMRAf/EABwAAAEEAwEAAAAAAAAAAAAAAAYAAwUHAQQIAv/EAEMQAAEDAwIDBAYECgsB
        AAAAAAECAwQRBQYAEiETBzFBIhRRcYEyFQhhkTMXQlKCI5Ojs9MWV7FyorLDJDR01HU2N//EABoBAQEB
        AQEBAQAAAAAAAAAAAAABBQYEAgP/xAAtEQEAAQMCAgkDBQAAAAAAAAAAARECAwQFMRMhQVFhwdEScjPw
        IgZxkaHSNP/aAAwDAQACEQMRAD8A3umWG5nnlsn3H+NLjb/Ky1RuUFuu7qJSvdXmop79KaoMFdBs3CSU
        dQ7gVU8O4OgV9j2gYwXqhecaZy6yZnKN0exNPManJNVvBSw2Gtx4kqWtG0niKmvZoNCyWzrH1RjG/u34
        41ZHVK+HxI29O5KSRuAQUKUAeG5SuPcKaCR+4nqL/MKZ9b/73QaPUHG8mwPpXNddyadcbrKuEYGbzXUF
        DKd1G0VWoipqVcePs1B6tPRbqFcbVDuCc+mNpmMNvhvc+SkOoC6V5vGldUN33pv1XxCzS8hg5y/LVbWz
        JejOl3attsblCji3UK4dxHHQQkq/ZP1EzbGIkW9SrF8XtQckeVW4GkvMKeDiktpUj31NenQGP3C5p/MS
        4/U7+/0EU6c96V5hYGp+RO5BYr9I8o7HkFZWklSUlSUrUvaU8wEFKuPYdBu5HnWd5zmkzDMDkJtkC2qU
        i5Xn8KqFbVkLAO1O/wAKQnxK9IGg9fcV1GPE9Qple/i/2/pdBM4h0fyy05DFuV4zKbdIcUlfkd7yUuLp
        4N9XFApB40px0Ee90JzNx5xwdQrikLWpQTR3gCa0+20AT01wvNM2Yurv8a3GD8MlmJTe65voK7vtU09W
        gLMtx7KOnHTG+3JrKZ1xvDzsVLM11ShyW+aAUtpWp0Aq3HcdQXHYXnX7Jbn3lFbrsVlxxZ7SpTYJJ9Z0
        G9oOZ7z1Gzlm53ppq8vobj5SxCZSNvhjK525ocPdO0aDpjQeXSQ2sjtCSQfZoKAxvMcpuPS9q4zbpIen
        fG3WPMbtq+UluoRVO3w11r7PitvyXRdET9vk538lz348Ns2zNv39X6SsOX07v0qU9JRk0llD61OJaTvo
        kKNQkUcHZr6s3PFbbEcq2afXY/PJsee+6boz3RWa06f7BjHMfyC83a72/wDiGUx8Ld5XM3OK3+Jaa03i
        nua0dVqcWKyy/l2z647u7uY+g0WfUZcmPnXRy5p19PGO3uZ6kWO/Yp04vtxbv8t+aoR0R3gtbZaSX079
        viVxV2V9GsTWay3NERbZFlOx0+27bfp5mb8l2Svb1fzIxsVjYyHDsYmXApelswm3fMPIS64pT0RTZO5X
        H3nN/rGvA10d9zcLm8z4q/XyPwquxNfIV/09a+7s8Pp0AZ8vdrcunT/I4LUx+3vPXJYamxVbHWlhlspU
        k9/HtB7dWRv/AHUdaK0PUV3bXt2u1p9eoI3NOjTWN9K7+5DkvXa9yFsTLnOdHjcbYc3rCU1UQBuKzxJO
        qIbpv02veU4tDn2jqBLiNoRy3ra0Xf8ALLTw5dEvp4d44Co1AU/cNnH8xLh+v/5Gg0+seNSMb6Iotkq5
        SLtKTPZckT5SlKWtayo8N6llKQKACugJslx/N7h09sk7DrxIgXWHboxMFtSQ3JRyUkp8QO1z8U9/YdBW
        mE2bMOpseTbL1nElkRXNt1sTjZTICUqpWlUJUK8DUGh7RqgiTZLfYvmExWz25BbhQbQWmUk1VQNv8VHv
        UTxOgJMl6ZdSpV5fk2DOpUG2vHe3DfK3FNKPvJSoHij0V46gbx3ojcjkEO/5rkb+RS7aoKgsKCktIUk7
        kklRUTRXGgA49tdBUuG4ZdZee5BjSsnkYzdm5C1IS2VjzYDij2pcaqdqgodtQdUWT9w2cfzEuH6//kag
        KOn3TC+Yzd3rjdcpm30KaLTEZ5TqWkFRBUspU44FGgoPRoLC79BTHy0/6HK/+2X/AHdAQfMJEfk9KLwG
        k7i0WHVgdyEPIKj7BoCrBrpCumIWeZCdS6w5DZG5JrRSWwlST6ClQodBNPvssMrefcS0y2kqccWQlKUj
        iSSeAGg5DuTpl2m/ZQ0Cu1Ky5h4PAGhbAeVuHsWn69UddRJcaZFalxXUvRn0Bxl1BqlSVCoII1Bq366w
        rTZ5lxnPJYixmVuOOLIA8KSace89w0HPGHMPI6MQXnElKZV8edaqO1Owor9aTrb2P5bvb5OX/Kvgt9/h
        LpRr7JH9Uf0axZdPbwV904/9Zlv+5/xXdbe6fBh9vhDlth/1aj3eNxv5g/8A5RePWx+3RrDdUJ+nn/g8
        e/66N+yToCDQBvTDpyjBbVNgInqniZKVK5imw1tqhKNtApVfd7dAZaDC0JWkoWApKgQpJFQQeBBGgqG9
        /Ldjz9wdnY9dpmPOPElbMc7mgTxogVQtKfo3U0ESflwyOvDOptO7wu/v9WokXPl9kuYdPsj+SSJc6dJY
        e89JStxDbbG6jaW1OKpUrJKq6C2LNbzbrRBt5XzDDjtR+ZSm7lICK07q01AE5j0mFzyaHleOXH4DkcZQ
        58pDfMbkIpSjrdU1PcfSO3Qbb3TdyT1GtmcSLgBKgxPKuwW2vza1FC0laVlW5Iq52UOgN9AtAC9QejuK
        5q8ibLLsG7NJCEXGKQlZSPdDiSCF7e7vHp0AO58t98CqMZ1PCO4KS5UD2P6tRJ4p0GuNoyCHdLllk25R
        oa+b5Il1CXFp9zeS6vwhXEinHQW9qAN6bdOm8JZurSJ6p3xOWZZKmw3y6imzgpVfXoCyZEjTYj0SU0l6
        NIQpp5pYqlSFiikn1jQU1N+W9yNIdOMZVOtEJ1e/yVVqSn1KbW3X8oV+nVqGU/Lfd5n5m9ZrOmQq1XHA
        Wd36R1af7J0FmRenWJRsPViCIQNkcQUOtKJK1qUal1S+3mbhu3agrRz5cLpEUWrHmc+FAqS3GVv8Ffpb
        cbSfXtGqPUf5bJEx5v8AiTLZ1zhtqCvKjcK/lOLd2+xOgsK/YBBnY7b7FbFIt0K3LQWG0oK0hCElO3tH
        E7q117dBrORfN1K1ijL3bbp1eOLYu9NLqitCSlsJPcKfVrwy04ikIDHMSTZrrdrgJJeN0d5pb2bQjxKV
        StTX39e3U6zm2WW0p6I/dmaHbI0+XJf6q8ya8OHHzN9RMQVl+JTMfTKEMyy2fMFHMCeW4F+7VNa7fTrx
        NRKY5aTZ7BbrUXecYMZqOXqbd3KQE7qcaVpoJHQLQLQLQLQLQLQLQLQLQLQLQLQLQLQQF5YYcuadklbE
        nY39ml0qoHK/ghSePZoGH4N6LBBuRSslICg0soC+bxNAn8enf2fRoHJLMzzzmx4eZUy0UV5oUgJ+1S34
        CDv41UPEPRoGZMG9bUl64ulW47wlDgRxaWB7iOwHia8K+zQP+TyFLKx5/ehX2qloUCniCrYNnEUqBoHL
        YxeUy9qpC1w6OlS3EqrzSohCUhYSdu019HAUPHQNXmOhcthKpam5oaQC42h3cqj7ZJAbCkitCPb6NA05
        BvBYc3XJQUaCoaWUBXeogJ4nft7+zQZuUSW5MQWpy2biI7QbSlDigdpXzSSEn3k9nDtp9Ggy9DyXzBba
        nLA5Sj5haCU8yquGxKPxdvf29ldB7ZiZANyUTVKUSjmLdSvwiiCdgKAk8ARw9PHiNBh6Ldy6lT8spc/C
        Q2l0oA2rA3EIoeNDxpoCMaBaD//Z
</value>
  </data>
  <data name="pictureBox9.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAADpSURBVEhLtZTBEYMwDAR5pJpUk3rSCQWkLkpxfA7n
        OQvhYAGPZRiNpLWUmCmldCtu8Ep+j3ke5ZFJK+81tiEqeGWWDAV4B5vcqAAnZnMC6TPT5EYFenoraXIj
        Aq7DEyDeSEYFWAHwmpNmTdEJvMYkPAFOxvUQNrXxT6bUjQhwMv2n4F0Fbt6IwLInaFCBjngUFbiogMl/
        iwy2TukKsII66g7I6QlPTWBlgMLaoyfoQYk3pX6nTgvwybYC3IGaNypg43qRDLyMNf+IgE1B74fHNNq8
        1KpAGyneGixeXaEK7sQNXokbvI40fQFbkZN6Qsb9PgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pictureBox8.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAEFSURBVDhPrdK9SsNgFIdxraBeh6sgdHBQEHFR2sGh
        Ct3Ezb03IHXQ2UGcnMQLKPQivAVBsBZsoVPBz0rh9XngDYTQNil44Je8Pcn5p/lYCCHoBkXrHs4s4tbF
        NZ4xin6nGOMVR3DuBAMXloNn2MP+FBWU4cwaTtFKAj6wAX/ncXAI/0nJhvWJLWRPzjrGD56wjWWbVpGA
        GnwODm/G3pUbKy+gine8YCf2VtB3Yc0KOMQ3fAO7sadVdF1YkwJ8zz7tB3RxgPTx3ABP8Kp+ZOsoIXt8
        ZsASGvAW0v1EbkCat5PtzRUwyf8GzPMpp3XcJHWHc1wU0MQlvgyoo40e3uK+iH4I4fEPdjuU1XCEtXYA
        AAAASUVORK5CYII=
</value>
  </data>
  <data name="pictureBox4.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAFeSURBVDhPpdLLSgJxFMfxM4440c3WkSVCEWREjxAZ
        dtn0Fl0Q0SJaxVRU4jJ0U1CbFr1B6hiZY0VET+CtxIVtmmxTVDr/fhMux9GhxWdzDvNlOPyJMfYvukMz
        dIdmUPXjh7t/qo7LeWUACAZhGqaMXGVfPSXlc4iuc8qYU5QrQkA661xLEojAWuFWYiyUKBxQOqv0ukR5
        syMoLXatJwlm4QSOjNBq7DQkFRdIzik0vJOhbnxs37hsG/niFE4WSQvYEQgiMN9YzkAEDo0gEEFgTgu4
        EXhD4BwLLbANrBUEGAJhLWBBwI1APxZawAle0P6kKQS8CLj+bjCCG/TgBn0IAAdW4I0gYEWAo1RWcTq2
        0inen9izBSQCH1SgbISWLl7244UgPZTeHZ7oY3widCtOhu8IlqEIecg1kR/dzTwf35T9pKqMvmuq5atW
        pwYeBLC1INTqKq/7vs3QHbaP0S8oW1YQ6uHSkwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pictureBox3.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAHNSURBVDhPfZPLK0VRFMaPgbwpEpEyNRAjMjCSPDL0
        KsqjlFfExMjEBN0YiIG/AAPcKwNR4uwTA4wUyoi6RpTyftT1+8496roug19nnW/v85211l7bqvfZHiYG
        9hlcxVqrmtyz/MdBS5s8fmyogCn0R3glnoFKb80lwuBbdEQ2rEEIntB5CgcjV9uEPMBg3/Kf3PwwyIJT
        +IBeyEDHzGyFY6cLnuEScmUQiDJYgU8oB70noMskjTje00rgBTajDYoRleIIaGM/DKGTiekmHoNBb60H
        QhiURRqMI76D/qZNo5CMztMMExeCDFJA2TxiML1+HIxjs30R7rYhfRMAfdgCfejtoBgTNxuVtAzvdT77
        qXX+8EIG16CjksE28BfTAJ3o1VBLLIM2kIF+8oHBW9PcwbXSJyUzQVpvkA4qQTWnoutDlaBTUC+0lggq
        YXb1KBgvA1GKqCaqedo0AK3o1K1MnEb4bmIHqIkVG1HH6AdKcY9K75noSUBPFLtaETzADgZx0XOQAxoS
        JtCheUrVLAE1u2k3wz3QM6cAg1+DJPJBk6dy7tB5uqN862m7oCONHOWYl6kKFtAZXZvmmkWo8dZcwpfp
        bwMP+xz+uc5B6wsO8vfXq7OD9gAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pictureBox7.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAIQSURBVDhPddLfS1NhHMfxxzJsSFf1N/QHhFe1LJL8
        UXRRVpYUVBBB1E3YfxC6lpKVjQoaVBdeZYMu6sLGNq0lKGEXZWYZK7fG+rGJ5mibp/fncB44CF284Oz7
        fJ/PeZ7vjtl3LbUB25HBB5yE6qdRwm8c92qmI5wye0NJ8zZTrHMcx6hYj004gxWU0YTbcDx3YAOCbeFU
        /Wxu0ZQrNTdgh7e4Hl+gDVEoyAas4iWeEOAcGBiLRpPzgZnsYp02fsNDPILd8AC6jtZ0NVt37elNOLHJ
        hWZ7hfNrG6C7X0QAW7ATo3B0gv39YyOTn381ZH4suyeQXdAphlGDQpZwCLZHYQk47eHU+N34J7NUrron
        OIVBFDGFy6hAIZqDP0QvqrZeTTqhp+8j8Xf5oyraZslhI45g2atp/SzUuxmai7P7StzpvPHq+9qAr1CT
        6q34CbvWg0YU9Ls9nHS6htJZNZ6A/c+rCEJ10fD8ITcRQ6qlN9EXGZ07bBtlHGp6AQ3M1hWYhw2ZYAYT
        3ZHX50p//rpDlAaMeA0SRzP0FypMg3aPLh1oY5DPpnPdNkBfo93sp5lk8Qb2BfN8C00tfYmeweezIf8J
        7nsNatR19PnaoFk89p6H2WxiUwumUltdZ5imX9D3fA/utHEd21DQ0bnCwHSmGMgVV4w5NpT+n4P4iDl0
        erWtXbfSlxjghdRMoTFfKpt/qGw7rZc06QsAAAAASUVORK5CYII=
</value>
  </data>
  <data name="pictureBox6.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAADcSURBVDhPlZJBCsJADEV7BWtdCqKgN/AKglfzSOJm
        PIEnUAsWFy7EC9SfNhl+Y9vBxWsm+T+faWm2PwQmuL6PjkceN8XOPKuIXg8PBE6fgKlWm3lPG1DXdcZg
        NgdrqV5joFc/AX8sy83aAKUEb/DRKr1pjPleIAbsQAE2YKHnIUQXXw7iR1yCRhy4rl9ufKgxwAyFW1yJ
        Tn1c1j4ZcAYl9Z0bok8GbMGFZwy0ZIC884lnDLTxADUd/cyQ3WTAGPDH/2BmAUTet2SIDmLAVetThor0
        HvkL7+AB4AnVF/hXXHMxmSJrAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="pictureBox5.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAADoSURBVDhPjdLNCcJAEAVgW/DvKIiCdmALgiXYiAdL
        8G4Lgi2Il1iBFfgDCR48iBX4nswszzEJOXyZ7O7MIya2FptMZWFd5qeHl6vxe90rRGmPbpCmt6Fr1fdi
        z1+AG8DEajxTBS8xoOkwnywF0B1e8LbKtZ8p73tCCphDD6YwtPsqPGdfB9I7GIEfxgHlw6zfWQ+IjSs4
        W+V6DOzT4dqApWEI1yfgb9fh2gDaww44NAOG6XBtgD+BPvYRGgesgftb8Mc+WFWVARyKL6xM+h/0IYbw
        O8cBxfMUcLH64KbhOuKXuEEO6MmKD+IR61SkCpolAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="textBox21.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAHRSURBVDhPhdNPKINhHAfwZyXlJCkXB3F1WpEIRcKw
        mF04mcOQlcldbJPY/L0IpSgOZJtGXGxrthGxJP/KRcqBFIrZ7LXX97feVzMvDp966v19v3ue933Gaiw7
        Ih3cAS9FAUUGJ1/Yv82VmJwB1bhfHgxzTAx3wyEYoRU08RDW1I16NX3W0xaj/axdN380jbKjgbXzfAq3
        QwByQSz8ptrsYeoJP+N5PuYlFGE9S8f6UpPLTQP3YIgPJKKCRhS8vXNfJcF3TtYw7nPRAJ1RGx9IlLgD
        EopwVLpCA1TQJgwngQKaIBvyoEVh9mjrx3xq19ld2t7VA/uIRlkIu/mtwALXQAXbMAtavMiZYqPTXTnk
        yQpHPhiRKiCNsC6st6BCWNNRZhCa+q+gGShI602oF9ZUoETI/l8BnZ+CYoFSWFNBL0ILfxXIoBMOIAXc
        QM8yoQs38KBq2COXKhA/YzLQnViGfNDDIl7gvHLUazbYT3PMG5cswuErJBR0AK0lSd2DKB9l6km/lQb2
        gT7Vj6CICvBrsW2LBRe3z+l4tksDcqD/Am2XjvBrweNrOHadT26eMupGvI6yQfecOFQATqCLswo2KaoJ
        nw1FttqRHV85wo7Abeon6BUeXh1SD9oAAAAASUVORK5CYII=
</value>
  </data>
  <data name="textBox22.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAHRSURBVDhPhdNPKINhHAfwZyXlJCkXB3F1WpEIRcKw
        mF04mcOQlcldbJPY/L0IpSgOZJtGXGxrthGxJP/KRcqBFIrZ7LXX97feVzMvDp966v19v3ue933Gaiw7
        Ih3cAS9FAUUGJ1/Yv82VmJwB1bhfHgxzTAx3wyEYoRU08RDW1I16NX3W0xaj/axdN380jbKjgbXzfAq3
        QwByQSz8ptrsYeoJP+N5PuYlFGE9S8f6UpPLTQP3YIgPJKKCRhS8vXNfJcF3TtYw7nPRAJ1RGx9IlLgD
        EopwVLpCA1TQJgwngQKaIBvyoEVh9mjrx3xq19ld2t7VA/uIRlkIu/mtwALXQAXbMAtavMiZYqPTXTnk
        yQpHPhiRKiCNsC6st6BCWNNRZhCa+q+gGShI602oF9ZUoETI/l8BnZ+CYoFSWFNBL0ILfxXIoBMOIAXc
        QM8yoQs38KBq2COXKhA/YzLQnViGfNDDIl7gvHLUazbYT3PMG5cswuErJBR0AK0lSd2DKB9l6km/lQb2
        gT7Vj6CICvBrsW2LBRe3z+l4tksDcqD/Am2XjvBrweNrOHadT26eMupGvI6yQfecOFQATqCLswo2KaoJ
        nw1FttqRHV85wo7Abeon6BUeXh1SD9oAAAAASUVORK5CYII=
</value>
  </data>
  <data name="textBox30.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAGvSURBVDhPldPPK8NxHMfxz5BxkNAk/4KDdpDQiFJ+
        HYw/wBz8qJVJ7rNNEmNzk1IuLrKRi8N+tV9EjORHy03twBSK2Y/v9vV6r776mu9kh8ft83629+fzHetb
        9gm08Ai8lF5oNbj4ljknpzK5QmpLUBlLcEwYnoYzMMIoaMQwrBlY8Wv0tusR497NhHbrfB2x8/n92yYa
        noAQNIAQ/KFnycuGrUHG83zWezzFZrYvde0mt4cOPIFBPJCLAkMIJFLp70gsyckGLQE3HaAdx0QDMqiB
        KpBDOQL1CNS9fCQYl85kA/EUR9EdITAOQoCGvHABzeCDbTjsXvSuHt8/V1DgM5k/QKZgA6rhFEqgqM3o
        8vnD0b7/BGZhExRwDMVQqTK5jwLhaEchAboLWoVWcCCgR0Be6Ar0fTSCAiswrJC9xL8C4ktsAQfQizBc
        IsMlSgb+esYyPKMMh5n4GXMDkyAEfpH6kDJ8hg2vBW104ARo31+DAqnAXeSNfuURHVAC/Rd0UAp5A7QC
        /fSrh9faAbP/oHPBsykcoi/OBU7YBbsUtTVgR8jeb/YFujB8EIpUfgEM7h9Ncg1MwgAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <data name="textBox3.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAGvSURBVDhPldPPK8NxHMfxz5BxkNAk/4KDdpDQiFJ+
        HYw/wBz8qJVJ7rNNEmNzk1IuLrKRi8N+tV9EjORHy03twBSK2Y/v9vV6r776mu9kh8ft83629+fzHetb
        9gm08Ai8lF5oNbj4ljknpzK5QmpLUBlLcEwYnoYzMMIoaMQwrBlY8Wv0tusR497NhHbrfB2x8/n92yYa
        noAQNIAQ/KFnycuGrUHG83zWezzFZrYvde0mt4cOPIFBPJCLAkMIJFLp70gsyckGLQE3HaAdx0QDMqiB
        KpBDOQL1CNS9fCQYl85kA/EUR9EdITAOQoCGvHABzeCDbTjsXvSuHt8/V1DgM5k/QKZgA6rhFEqgqM3o
        8vnD0b7/BGZhExRwDMVQqTK5jwLhaEchAboLWoVWcCCgR0Be6Ar0fTSCAiswrJC9xL8C4ktsAQfQizBc
        IsMlSgb+esYyPKMMh5n4GXMDkyAEfpH6kDJ8hg2vBW104ARo31+DAqnAXeSNfuURHVAC/Rd0UAp5A7QC
        /fSrh9faAbP/oHPBsykcoi/OBU7YBbsUtTVgR8jeb/YFujB8EIpUfgEM7h9Ncg1MwgAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <metadata name="sqlDataSource1.ParameterValues" xml:space="preserve">
    <value>{@iInfraObjectId:256},{@iStartDate:2013-08-01},{@iEndDate:2014-08-06}</value>
  </metadata>
</root>