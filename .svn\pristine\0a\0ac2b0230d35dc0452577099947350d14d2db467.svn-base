﻿using System;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;

namespace CP.UI.Code.Replication.DataLag
{
    public class OracleDataLag : IDataLag
    {
        private readonly IFacade _facade = new Facade();

        public InfraObjectInfo GetDataLag(int infraobjectId)
        {
            var infraObjectInfo = new InfraObjectInfo();

            InfraObject infraObject = _facade.GetInfraObjectById(infraobjectId);
            var objbusinessfunction = _facade.GetBusinessFunctionByInfraObjectId(infraobjectId);

            // Kuntesh Thakker - 05-05-2014 - As discussed with <PERSON> for CP 4.0v  , DB2Datasync datalag details need to get from businessservicerpoinfo table so its doesnt need below method
            // var dataLag = _facade.GetOracleLogByInfraObjectId(infraobjectId);
            var BusinessServicerpoinfo = _facade.GetCurrentRPOInfraInfobyInfraObjectId(infraobjectId, infraObject.BusinessFunctionId);

            if (BusinessServicerpoinfo != null)
            {
                if (string.IsNullOrEmpty(BusinessServicerpoinfo.CurrentRPO))
                {
                    infraObjectInfo.DataLag = "N/A";

                    infraObjectInfo.Health = 0;

                    infraObjectInfo.DataLagCreateDate = "NA";
                }
                else
                {
                    infraObjectInfo.DataLag = BusinessServicerpoinfo.CurrentRPO;

                    infraObjectInfo.Health = Utility.GetDatlagHealth(BusinessServicerpoinfo.CurrentRPO, Convert.ToInt32(Utility.ConvertRPOValues(objbusinessfunction.ConfiguredRPO))) ? 1 : 0;

                    infraObjectInfo.DataLagCreateDate = BusinessServicerpoinfo.CreateDate.ToString();
                }
            }
            else
            {
                infraObjectInfo.DataLag = "N/A";

                infraObjectInfo.Health = 0;

                infraObjectInfo.DataLagCreateDate = "NA";
            }

            return infraObjectInfo;
        }
    }
}