﻿    <%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="GroupManagement.aspx.cs" Inherits="CP.UI.GroupManagement" Title="Continuity Patrol :: Admin-GroupManagement" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit, Version=3.0.30930.28736, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <style type="text/css">
        pre
        {
            padding: 20px;
            background-color: #ffffcc;
            border: solid 1px #fff;
        }
        .wrapper
        {
            background-color: #ffffff;
            width: 800px;
            border: solid 1px #eeeeee;
            padding: 20px;
            margin: 0 auto;
        }
        .example-container
        {
            background-color: #f4f4f4;
            border-bottom: solid 2px #777777;
            margin: 0 0 40px 0;
            padding: 20px;
        }
        .example-container p
        {
            font-weight: bold;
        }
        .example-container dt
        {
            font-weight: bold;
            height: 20px;
        }
        .example-container dd
        {
            margin: -20px 0 10px 100px;
            border-bottom: solid 1px #fff;
        }
        .example-container input
        {
            width: 150px;
        }
        .clear
        {
            clear: both;
        }
        #ui-datepicker-div
        {
            font-size: 80%;
            z-index: 1000;
        }
        .ui-timepicker-div .ui-widget-header
        {
            margin-bottom: 8px;
        }
        .ui-timepicker-div dl
        {
            text-align: left;
        }
        .ui-timepicker-div dl dt
        {
            height: 25px;
        }
        .ui-timepicker-div dl dd
        {
            margin: -25px 10px 10px 65px;
        }
        .ui-timepicker-div td
        {
            font-size: 90%;
        }
        .ui-tpicker-grid-label
        {
            background: none;
            border: none;
            margin: 0;
            padding: 0;
        }
        .style1
        {
            height: 29px;
        }
    </style>
    <link href="../App_Themes/CPTheme/jquery-ui-1.8.16.custom.css" rel="stylesheet"
        type="text/css" />

    <script type="text/javascript" src="../Script/jquery-ui-1.8.16.custom.min.js"></script>

    <script type="text/javascript" src="../Script/jquery-ui-timepicker-addon.js"></script>
 
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="grid_5 grid-15">
     
                <div class="block-content no-padding">
                
      <asp:UpdatePanel ID="UpdatePanel_ProfileOverview" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                    <div class="block-controls"style="height: 25px;">
                        <h1>
                            <asp:Label ID="lblGroupName" runat="server" Text=""></asp:Label></h1> 
                                    
                    <div class="float-right side1">
                  
                                <%--<label>MailBox Database</label>--%>
                                <asp:Label ID="lblMailboxName" runat="server" Text="MailBox Database"></asp:Label>
                      
                                 <asp:DropDownList ID="ddlMailBoxDatabase" runat="server" TabIndex="1" 
                                    AutoPostBack="True" OnSelectedIndexChanged="DdlMailBoxDatabaseIndexChanged">
                                </asp:DropDownList>
                       
                    </div>
           
                </div>
        <table class="table font grid-18 no-bottom-margin" id="tblMaintainence" width="100%" runat ="Server">
                        <thead>
                            <tr>
                                <th class="grid-27">
                                </th>
                                <th class="grid-26">
                                    Production Server
                                </th>
                                <th>
                                    DR Server
                                </th>
                            </tr>
                        </thead>
                        <tr>
                            <td >
                                Archive Log Sequence Name
                            </td>
                            <td >
                                <asp:Label ID="lblPrseqName" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblDrseqName" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Archive Log Sequence Number
                            </td>
                            <td>
                                <asp:Label ID="lblPrseqNo" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblDrseqNo" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Generation Time / Apply Time
                            </td>
                            <td>
                                <asp:Label ID="lblPrTime" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblDrTime" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Transaction ID
                            </td>
                            <td >
                                <asp:Label ID="lblPrTransID" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblDrTransID" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <%--<td>                              
                                  Enable DataSync via WorkFlow                                
                            </td>     --%>                       
                            <td visible ="false" >
                            <asp:CheckBox ID="chkWorkFlow" runat="server" Text="" AutoPostBack="True" Visible ="false" 
                                    oncheckedchanged="ChkWorkFlowCheckedChanged" />
                            </td>                            
                        </tr>
                        <tr runat="server" id="trMaintainence" visible="false">
                            <td colspan="3" class="message success font_8">                                
                                <asp:Label ID="lblReasonMaintenance" runat="server"></asp:Label>
                            </td>
                        </tr>                        
                    </table>
        <table id="tblVmWareApplicationTable" runat="server" class="dtable font"  width="100%" style="display: none;" >
                                   <thead>
                                         <tr>
                                            <th style="width:34% "> Virtual Machine </th>
                                            <th style="width:32% "> 
                                                Production Server
                                            </th>
                                            <th>
                                                DR Server
                                            </th>
                        
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                ESXI Host Name
                                            </td>
                                            <td class="text-indent">
                                                <asp:Label ID="lblESXIHostNamePr" runat="server" ></asp:Label>
                                            </td>
                                            <td class="text-indent">
                                                <asp:Label ID="lblESXIHostNameDr" runat="server" ></asp:Label>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                Virtual Machine
                                            </td>
                                            <td >
                                                <asp:Label ID="lblMchineNamePr" runat="server" ></asp:Label>                                    
                                            </td>
                                            <td >
                                                <asp:Label ID="lblMchineNameDr" runat="server" ></asp:Label>                                                                                </td>           
                                        </tr> 
                                         <tr>
                                            <td>
                                                Power State
                                            </td>
                                            <td >
                                                <asp:Label ID="lblpowerstatePR" runat="server" ></asp:Label>                                    
                                            </td>
                                            <td >
                                                <asp:Label ID="lblpowerstateDR" runat="server" ></asp:Label>                                                                                </td>           
                                        </tr>   
                                          <tr>
                                            <td>
                                               Current Snapshot
                                            </td>
                                            <td >
                                                <asp:Label ID="lblcurrentsnapshotPR" runat="server"></asp:Label>                                    
                                            </td>
                                            <td >
                                                <asp:Label ID="lblcurrentsnapshotDR" runat="server"></asp:Label>                                                                                </td>           
                                        </tr>  
                                           <tr>
                                            <td>
                                               Updated Time Stamp
                                            </td>
                                            <td class="text-indent">
                                                <asp:Label ID="lblupdationtimestampPr" runat="server" ></asp:Label>
                                            </td>
                                            <td class="text-indent">
                                                <asp:Label ID="lblupdationtimestampDr" runat="server" ></asp:Label>
                                            </td>
                                        </tr>                                                                                                            
                                    </tbody>
                                </table>
        <table class="table font grid-18 no-bottom-margin" id="tbSCRMaintainece" width="100%" runat ="Server">
                        <thead>
                            <tr>
                                <th class="grid-27">
                                </th>
                                <th class="grid-26">
                                    Production Server
                                </th>
                                <th>
                                    DR Server
                                </th>
                            </tr>
                        </thead>
                        <tr>
                            <td>
                                Log Sequence No
                            </td>
                            <td>
                                <asp:Label ID="lblPRLogSequence" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblDRLogSequence" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Generation Time / Apply Time
                            </td>
                            <td>
                                <asp:Label ID="lblPRLastLogApplyTime" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblDRLastLogApplyTime" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        
                        <tr>
                            <td>                              
                                  Enable DataSync via WorkFlow                                
                            </td>                            
                            <td colspan="2">
                            <asp:CheckBox ID="CheckBox1" runat="server" Text="" AutoPostBack="True" 
                                    oncheckedchanged="ChkWorkFlowCheckedChanged" />
                            </td>                            
                        </tr>
                        
                        <tr runat="server" id="tr1" visible="false">
                            <td colspan="3" class="message success font_8">
                                <asp:Label ID="lblReasonMaintenance2" runat="server"></asp:Label>
                            </td>
                        </tr>
                    </table>
        <table class="table font grid-18 no-bottom-margin" id="tblSqlServerNative" width="100%" runat ="Server">
                        <thead>
                            <tr>
                                <th class="grid-27">
                                </th>
                                <th class="grid-26">
                                    Production Server
                                </th>
                                <th>
                                    DR Server
                                </th>
                            </tr>
                        </thead>
                         <tr>
                            <td>
                               Log Sequence Name
                            </td>
                            <td>
                                <asp:Label ID="lblbackupfile" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblrestorefile" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Log Sequence No
                            </td>
                            <td>
                                <asp:Label ID="lblbackuplsn" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblrestorelsn" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Generation Time / Apply Time
                            </td>
                            <td>
                                <asp:Label ID="lblbackuptime" runat="server" Text=""></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblrestoretime" runat="server" Text=""></asp:Label>
                            </td>
                        </tr>
                        
                        <tr runat="server" id="tr2" visible="false">
                            <td colspan="3" class="message success font_8">
                                <asp:Label ID="Label9" runat="server"></asp:Label>
                            </td>
                        </tr>
                    </table>  
        <%--<table class="dtable font grid-18 no-bottom-margin" id ="tboraclewithfastcopy" runat="server" width="100%" style="display:none;">
                        <thead>
                            <tr>
                            <th class="side1"> Component Monitor </th>
                                <th > 
                                    Production Server
                                </th>
                                <th>
                                    DR Server
                                </th>
                                
                            </tr>
                        </thead>
                        
                        <tbody>
                        
                                                                             
                            <tr>
                                 <td>
                                    IP Address
                                </td>
                                <td ><span class="health_up">&nbsp;</span>
                                <asp:Label ID="LblOFastcopy_Health_Up" runat="server" class="health_up"></asp:Label>
                                    <asp:Label ID="lbloraclefastcopyPRhealth_up" runat="server" ></asp:Label>                                    
                                </td>
                                <td ><asp:Label ID="LblOFastcopyHealth_Down" runat="server" class="health_down"></asp:Label>
                                
                                    <asp:Label ID="lbloraclefastcopyDRhealth_down" runat="server" ></asp:Label>
                                         </td>         
                            </tr>
                            <tr >
                                 <td> 
                                    Database Name
                                </td>
                                <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                    <asp:Label ID="lblPrdb" runat="server" ></asp:Label>     
                                </td>
                                <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                    <asp:Label ID="lbldrdb" runat="server" ></asp:Label>
                                </td>
                            </tr> 
                           
                          
                            <tr id="tr5" runat="server">
                                <td>
                                 Last Log Generated at Production
                                </td>
                                <td class="text-indent"><span class="icon-log">&nbsp;</span>
                                    <asp:Label ID="lblOFastcopyLastLogSeq" runat="server" ></asp:Label>
                                </td>
                                
                                <td>
                                  --
                                </td>
                            </tr>
                            
                            <tr id="tr6" runat="server">
                                <td>
                             Last Log Applied at DR
                                </td>
                                <td>
                                    --
                                </td>
                                
                                <td class="text-indent"><span class="icon-log">&nbsp;</span> 
                                    <asp:Label ID="lblFastcopyAppliedLog" runat="server" ></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                            Datalag
                                </td>
                                <td>
                                <span class="icon-log">&nbsp;</span> 
                                    <asp:Label ID="lbldatalag" runat="server" ></asp:Label>
                                </td>
                                
                                <td class="text-indent">
                                    <asp:Label ID="Label24" runat="server" Text ="--" ></asp:Label>
                                </td>
                            </tr>
                          
                        </tbody>
                    </table> --%>
        <%--<table class="dtable font grid-18 no-bottom-margin" id="tblOraclewithfastcopyReplication" width="100%" runat="server"  style="display: none;">
                        <thead>
                            <tr>
                                <th class="side1">
                                    Replication Monitor (DataSync)
					 </th>
					 
					 <th >Production Server</th>
					 <th>DR Server</th>
                            </tr>
                        </thead>
                        <tbody>
                            
                             <tr>
                                <td >
                                  IP Address
                                </td>
                                <td >
                                <asp:Label ID="lblhealthup_status" runat="server" CssClass="health_up" Text=""></asp:Label>
                                   <asp:Label ID="lblPRIPhealth_up" runat="server" ></asp:Label>
                                </td>
                                <td>
                                  <asp:Label ID="lblhealthdown_status" runat="server" CssClass="health_down" Text=""></asp:Label>
                                   <asp:Label ID="lblDRIPhealth_down" runat="server" ></asp:Label></td>
                            </tr>
                            <tr>
                                <td >
                                   Replication Status
                                </td>
                                <td colspan="2">
                                <asp:Label ID="Label19" runat="server" CssClass="Replicating vertical-align" ></asp:Label>
                                   <asp:Label ID="lblRepStatus" runat="server" ></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                
                                   <td >
                                   Last Successful Repl. Time
                                </td>
                                <td colspan="2">
                                <span class="icon-Time">&nbsp;</span>
                                   <asp:Label ID="lblLastRepTime" runat="server" ></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                
                                    <td >
                                   Next Scheduled Repl. Time
                                </td>
                                <td colspan="2">
                                <span class="icon-Time">&nbsp;</span>
                                   <asp:Label ID="lblNextSchTime" runat="server" ></asp:Label>
                                </td>
                            </tr>

                        </tbody>
                    </table>  --%>
        <table id ="tboraclewithfastcopy" runat="server" class="monitortable font" width="100%" style="display:none;">
                        <thead>
                            <tr>
                            <th style="width:34% "> Component Monitor </th>
                                <th style="width:32% "> 
                                    Production Server
                                </th>
                                <th>
                                    DR Server
                                </th>
                                
                            </tr>
                        </thead>
                          <tbody>
                                           
                            <tr>
                                 <td>
                                  Server IP 
                                </td>
                                <td ><%--<span class="health_up">&nbsp;</span>--%>
                                <asp:Label ID="healthPRIP" runat="server" class="health_up"></asp:Label>
                                    <asp:Label ID="lblOracleFastcopyPRIP" runat="server" ></asp:Label>                                    
                                </td>
                                <td ><asp:Label ID="healthDRIP" runat="server" class="health_down"></asp:Label>
                                    <asp:Label ID="lblOracleFastcopyDRIP" runat="server" ></asp:Label>
                                </td>         
                            </tr>
                            
                             <tr>
                                <td>
                                    Server Name
                                </td>
                             <td >
                              <span class="icon-storagePR">&nbsp;</span>&nbsp;
                               <asp:Label ID="lblPRserver" runat="server" ></asp:Label>
                             </td>
                             <td >
                              <span class="icon-storageDR">&nbsp;</span>&nbsp;
                               <asp:Label ID="lblDRServer" runat="server" ></asp:Label>
                              </td>
                            </tr>
                            <tr >
                                 <td> 
                                    Database Name
                                </td>
                                <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                    <asp:Label ID="lblPrdb" runat="server" ></asp:Label>     
                                </td>
                                <td class="text-indent"><span class="icon-database">&nbsp;</span>
                                    <asp:Label ID="lbldrdb" runat="server" ></asp:Label>
                                </td>
                            </tr> 
                                          
                            <tr id="tr5" runat="server">
                                <td>
                                 Last Log Generated at Production
                                </td>
                                <td class="text-indent"><span class="icon-log">&nbsp;</span>
                                    <asp:Label ID="lblOFastcopyLastLogSeq" runat="server" ></asp:Label>
                                </td>
                                
                                <td>
                                  --
                                </td>
                            </tr>
                            
                            <tr id="tr6" runat="server">
                                <td>
                             Last Log Applied at DR
                                </td>
                                <td>
                                    --
                                </td>
                                
                                <td class="text-indent"><span class="icon-log">&nbsp;</span> 
                                    <asp:Label ID="lblFastcopyAppliedLog" runat="server" ></asp:Label>
                                </td>
                            </tr>
                       
                        </tbody>
                    </table> 
        <table id="tblOraclewithfastcopyReplication" class="monitortable font" width="100%" runat="server"  style="display: none;">
                        <thead>
                            <tr>
                             <th colspan="2">
                                    Replication Monitor (DataSync)
					        </th>
					 
					 
                            </tr>
                        </thead>
                        <tbody>
                           <tr>
                                <td class="side1">
                                   Replication Status
                                </td>
                                <td >
                                <asp:Label ID="Label19" runat="server" CssClass="Replicating float-left" ></asp:Label>&nbsp;<asp:Label ID="lblRepStatus" runat="server" ></asp:Label>
                                </td>
                            </tr>
                            
                            <tr>
                            <td >
                               Source Archive Path
                            </td>
                            <td class="text-indent">
                            <span class="icon-storagePR">&nbsp;</span>&nbsp;<asp:Label ID="lblOFastcopySourceDirectory" runat="server" ></asp:Label>
                            </td>
                        </tr>
                       <tr>
                         <td>
                            Target Archive Path
                            </td>
                            <td>
                            <span class="icon-storageDR">&nbsp;</span>&nbsp;<asp:Label ID="lblOFastcopyDestinationDirectory" runat="server" ></asp:Label>
                            </td>
                         </tr>
                          <tr>
                             <td >
                                  Last Replication Count
                                </td>
                               <td >
                                <span class="">&nbsp;</span>
                                   <asp:Label ID="lbllastRepCount" runat="server" ></asp:Label>
                                </td>
                            
                            
                            </tr>
                            <tr>
                                
                                   <td >
                                   Last Successful Repl. Time
                                </td>
                                <td colspan="2">
                                <span class="icon-Time">&nbsp;</span>&nbsp;<asp:Label ID="lblLastRepTime" runat="server" ></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td >
                                   Next Scheduled Repl. Time
                                </td>
                                <td >
                                <span class="icon-Time">&nbsp;</span>&nbsp;<asp:Label ID="lblNextSchTime" runat="server" ></asp:Label>
                                </td>
                            </tr>
                           

                        </tbody>
                    </table>    
    
        <table id="tblFastCopyJobMonitor" class="dtable font grid-18 no-bottom-margin" width="100%" runat="server" style="display:table;">
                        <thead>
                            <tr>
                                <th class="side1">
                                    Replication Monitor (DataSync)
					 </th>
					 
					 <th >Production Server</th>
					 <th>DR Server</th>
                            </tr>
                        </thead>
                        <tbody>
                            
                             <tr>
                                <td >
                                  IP Address
                                </td>
                                <td >
                                <asp:Label ID="Labelhealth_up" runat="server" CssClass="health_up" Text=""></asp:Label>
                                   <asp:Label ID="lblPRserverhealthup" runat="server" ></asp:Label>
                                </td>
                                <td>  <asp:Label ID="Labelhealth_down" runat="server" CssClass="health_down" Text=""></asp:Label>
                                   <asp:Label ID="lblDRserverhealthdown" runat="server" ></asp:Label></td>
                            </tr>
                            <tr>
                                <td >
                                   Replication Status
                                </td>
                                <td colspan="2">
                                <asp:Label ID="LabelReplicating" runat="server" CssClass="Replicating" ></asp:Label>
                                   <asp:Label ID="lblFastcopyRstatus" runat="server" ></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                
                                 <td >
                                   Total Replication Jobs
                                </td>
                                <td colspan="2"><span class="icon-disks ">&nbsp;</span>
                                   <asp:Label ID="lbltotreplicationJobPairs" runat="server" ></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                
                                   <td >
                                   Last Successful Repl. Time
                                </td>
                                <td colspan="2">
                                <span class="icon-Time">&nbsp;</span>
                                   <asp:Label ID="lbllreptime" runat="server" ></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                
                                    <td >
                                   Next Scheduled Repl. Time
                                </td>
                                <td colspan="2">
                                <span class="icon-Time">&nbsp;</span>
                                   <asp:Label ID="lblnreptime" runat="server" ></asp:Label>
                                </td>
                            </tr>

                        </tbody>
                    </table>    
             
        <table id="tblhadrComponentmonitor" class="monitortable font" width="100%" runat="server"
                        style="display: none;">
                        <thead>
                            <tr>
                                <th style="width: 30%">
                                    Database (DB2) Monitor
                                </th>
                                <th>
                                    Primary
                                </th>
                                <th>
                                    Stand By DR
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    IP Address
                                </td>
                                <td>
                                    <asp:Label ID="lblDB2Health_PR" runat="server" CssClass="health_up"></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblPRIp" runat="server" Text="***********"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblDB2Health_DR" runat="server" CssClass="health_down"></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblDRIp" runat="server" Text="***********"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Database Instance
                                </td>
                                <td>
                                    <asp:Label ID="Label7" runat="server" CssClass="icon-database" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblPRDatabaseInstance" runat="server" Text="DB1"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label8" runat="server" CssClass="icon-database" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblDRDatabaseInstance" runat="server" Text="DB1"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Database Status
                                </td>
                                <td>
                                    <asp:Label ID="lblikonPR" runat="server" CssClass="icon-standby" Text=""> </asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblPRDatabaseStatus" runat="server" Text="lblPRDatabaseStatus"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="lblikonDR" runat="server" CssClass="Active float-left" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblDRDatabaseStatus" runat="server" Text="lblDRDatabaseStatus"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Last Log Generated / Last Log Applied
                                </td>
                                <td>
                                    <asp:Label ID="Label10" runat="server" CssClass="icon-log" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblPRLogFile" runat="server" Text="22321"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label11" runat="server" CssClass="icon-log" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblDRLogFile" runat="server" Text=" 22321"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Current LSN
                                </td>
                                <td>
                                    <asp:Label ID="Label5" runat="server" CssClass="icon-currentlsn" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblPRCurrentLsn" runat="server" Text="23"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label20" runat="server" CssClass="icon-currentlsn" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblDRCurrentLsn" runat="server" Text="22"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    LSN
                                </td>
                                <td>
                                    <asp:Label ID="Label15" runat="server" CssClass="icon-numbering" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblPRLsn" runat="server" Text="32"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label18" runat="server" CssClass="icon-numbering" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblDRLsn" runat="server" Text="31"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Time Stamp
                                </td>
                                <td>
                                    <asp:Label ID="Label12" runat="server" CssClass="icon-Time" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblPRTimestamp" runat="server" Text="02:33:45"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="Label13" runat="server" CssClass="icon-Time" Text=""></asp:Label>&nbsp;&nbsp;<asp:Label
                                        ID="lblDRTimestamp" runat="server" Text="02:09:12"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Datalag
                                </td>
                                <td colspan="2">
                                    <asp:Label ID="lblDataHadrLag" CssClass="icon-clock orange" Text=""></asp:Label>&nbsp;&nbsp;
                                    <asp:Label ID="lblHadrLag" runat="server" Text="Label"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
         
           <table id="tblMSExchDAGCompSum" class="table" runat ="server" style="display:table;" width="100%">
                                      <thead>
                                      <tr>
                                      <th style="width:30%"> MS Exchange DAG</th>
                                       <th>Production Server</th>
                                       <th> DR Server</th>
                                      </tr></thead>
                                      <tbody>
                                                                      
                                                  <tr>
                                <td>
                                    IP Address
                                </td>
                                <td>
                                    <span class="health_up">&nbsp;</span>
                                    <asp:Label ID="lblPRIPAddress" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    <span class="health_up">&nbsp;</span>
                                    <asp:Label ID="lblDRIPAddress" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    MailBox Database Name
                                </td>
                                <td class="text-indent">
                                    <span class="icon-database float-left">&nbsp;</span>
                                    <asp:Label ID="lblPRDatabase" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span class="icon-database float-left">&nbsp;</span>
                                    <asp:Label ID="lblDRDatabase" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    MailBox Database Status
                                </td>
                                <td class="text-indent">
                                    <span id="spnPRState" class="Replicating float-left" runat="server">&nbsp;</span>
                                    <asp:Label ID="lblPRDBState" CssClass="active" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <span id="spnDRState" class="InActive float-left" runat="server">&nbsp;</span>
                                    <asp:Label ID="lblDRDBState" runat="server" CssClass="inactive" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Last Log Generated at Production
                                </td>
                                <td>
                                    <asp:Label ID="Label24" CssClass="icon-numbering" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lblPRDBRecovery" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td>
                                    --
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Last Log Replayed at DR
                                </td>
                                <td>
                                    --
                                </td>
                                <td>
                                    <asp:Label ID="Label26" CssClass="icon-numbering" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lblDRDBRecovery" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    Log Generation/Replayed Time
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="Label25" CssClass="icon-Time" runat="server"></asp:Label>
                                    <asp:Label ID="lblLogGenrRepyTimePR" runat="server" Text="NA"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="Label27" CssClass="icon-Time" runat="server"></asp:Label>
                                    <asp:Label ID="lblLogGenrRepyTimeDR" runat="server" Text="NA"></asp:Label>
                                </td>
                            </tr>
                                    
                                      </tbody>
                                      
                                      </table>   
               
                <asp:Panel ID="panelSwitchOver" runat="server" Style="display: none" Width="450px"
                    Height="520px">
                    <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                        <ContentTemplate>
                            <div id="modal">
                                <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
                                    <ul class="action-tabs">
                                        <li title="Close">
                                            <asp:LinkButton ID="lnkbtnClose" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                         </li></ul>
                                    <div class="block-content  margin-right margin-top1em">
                                        <div class="block-controls">
                                            <h1>
                                                <asp:Label ID="lblCreate" runat="server" Text="Workflow"></asp:Label>
                                            </h1>
                                        </div>
                                        <iframe id="frame1" src="SwitchOver.aspx" height="370px" width="380px" scrolling="no"
                                            runat="server" />
                                    </div>
                                </div>
                            </div>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </asp:Panel>
                <asp:Panel ID="panelSwitchBack" runat="server" Style="display: none" Width="450px"
                    Height="480px">
                    <asp:UpdatePanel ID="Updatepanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                        <ContentTemplate>
                            <div id="Div1">
                                <div class="modal-window " style="top: 25px; width: 450px; margin-left: 30%">
                                    <ul class="action-tabs">
                                        <li title="Close">
                                            <asp:LinkButton ID="LinkButton1" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                            </li></ul>
                                    <div class="block-content  margin-right margin-top1em">
                                        <div class="block-controls">
                                            <h1>
                                                <asp:Label ID="Label3" runat="server" Text="Workflow"></asp:Label>
                                            </h1>
                                        </div>
                                        <iframe id="Iframe1" src="SwitchBack.aspx" height="370px" width="380px" scrolling="no"
                                            runat="server" />
                                    </div>
                                </div>
                            </div>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </asp:Panel>
                <asp:Panel ID="panelFail" runat="server" Style="display: none" Width="450px" Height="480px">
                    <asp:UpdatePanel ID="UpdatepanelFailOver" runat="server" UpdateMode="Conditional"
                        ChildrenAsTriggers="true">
                        <ContentTemplate>
                            <div id="Div2">
                                <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
                                    <ul class="action-tabs">
                                        <li title="Close">
                                            <asp:LinkButton ID="LinkButton2" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                           </li></ul>
                                    <div class="block-content  margin-right margin-top1em">
                                        <div class="block-controls">
                                            <h1>
                                                <asp:Label ID="Label4" runat="server" Text="Workflow"></asp:Label>
                                            </h1>
                                        </div>
                                        <iframe id="Iframe2" src="FailOver.aspx" height="370px" width="380px" scrolling="no"
                                            runat="server" />
                                    </div>
                                </div>
                            </div>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </asp:Panel>
                <asp:Panel ID="panelFailBack" runat="server" Style="display: none" Width="450px"
                    Height="480px">
                    <asp:UpdatePanel ID="Updatepanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                        <ContentTemplate>
                            <div id="Div3">
                                <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
                                    <ul class="action-tabs">
                                        <li title="Close">
                                            <asp:LinkButton ID="LinkButton3" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                           </li></ul>
                                    <div class="block-content  margin-right margin-top1em">
                                        <div class="block-controls">
                                            <h1>
                                                <asp:Label ID="Label1" runat="server" Text="Workflow"></asp:Label>
                                            </h1>
                                        </div>
                                     
                                    </div>
                                </div>
                            </div>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </asp:Panel>
                <asp:Panel ID="panelCustom" runat="server" Style="display: none" Width="450px" Height="480px">
                    <asp:UpdatePanel ID="Updatepanel3" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                        <ContentTemplate>
                            <div class="modal-window " style="top: 45px; width: 450px; margin-left: 30%">
                                <ul class="action-tabs">
                                    <li title="Close">
                                        <asp:LinkButton ID="LinkButton4" runat="server" OnClick="CloseClick" CommandName="Close">
                                     <img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
                                        </li></ul>
                                <div class="block-content  margin-right margin-top1em">
                                    <div class="block-controls">
                                        <h1>
                                            <asp:Label ID="Label2" runat="server" Text="Workflow"></asp:Label>
                                        </h1>
                                    </div>
                                    <iframe id="frameCustom" src="CustomWorkflow.aspx" height="400px" width="380px" scrolling="auto"
                                        runat="server" />
                                </div>
                            </div>
                        </ContentTemplate>
                    </asp:UpdatePanel>
                </asp:Panel>
                <%--This panel is added by shivraj For Maintenance--%>
                
            </ContentTemplate>
        </asp:UpdatePanel>
      <asp:Panel ID="panelMaintenance" runat="server" Style="display: none" Width="450px" Height="480px">
<div id="Div4">
  <div class="modal-window " style="top: 59px; width: 450px; margin-left: 30%">
    <ul class="action-tabs">
      <li title="Close">
         <asp:LinkButton ID="LinkButton5" runat="server" OnClick="CloseClick" CommandName="Close"><img src="../images/icons/btn_close.png" width="32px" height="32px" /></asp:LinkButton>
            </li>
              </ul>
                <div class="block-content  margin-right margin-top1em">
                  <div class="block-controls">
                     <h1>
                       <asp:Label ID="lblmaintenance" runat="server"></asp:Label>
                        </h1>
                           </div>
                              <div class="float-left side1">
                                            <label>
                                                Reason
                                            </label>
                                        </div>
                                        <div>
                                            <textarea id="txtReason" rows="3" cols="40"></textarea><span></span>
                                        </div>
                                        <hr />
                                        <div class="float-left side1">
                                            <label>
                                                Mode</label></div>
                                        <div>
                                            <select id="ddlMode">
                                                <option value="000">Select Mode</option>
                                                <option value="1">Auto</option>
                                                <option value="2">Manual</option>
                                            </select><span></span>
                                        </div>
                                        <hr />
                                        <div id="idUnClock">
                                            <div class="float-left side1">
                                                <label>
                                                    Unlock Time</label></div>
                                            <div>
                                                <input type="text" name="txtTime" id="txtTime" readonly="true" class="date" /><span></span>
                                            </div>
                                            <hr />
                                        </div>
                                        <div class="block-footer align-right">
                                            <input id="btnActionSetSave" type="button" value="OK" class="buttonblue" />
                                            <input id="btnCancleMaintaince" type="button" value="Cancel" class="buttonblue" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                  
                </asp:Panel>
        <table class="table font grid-18 no-bottom-margin" id="tblMaintainenceManage" width="100%">
            <tr>
                <td>
                    Manage Group
                </td>
                <td colspan="2" class="align-right">
                    <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenance" runat="server" TargetControlID="btnMaint"
                        RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                        PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="modal"
                        CancelControlID="btnCancleMaintaince">
                    </TK1:ModalPopupExtender>
                    <TK1:ModalPopupExtender ID="ModalPopupExtenderMaintenanceAll" runat="server" TargetControlID="btnMaintenanceAll"
                        RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                        PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="modal"
                        CancelControlID="btnCancleMaintaince">
                    </TK1:ModalPopupExtender>
                    <TK1:ModalPopupExtender ID="ModalPopupExtenderLock" runat="server" TargetControlID="btnLock"
                        RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelMaintenance"
                        PopupDragHandleControlID="panelMaintenance" Drag="true" BackgroundCssClass="modal"
                        CancelControlID="btnCancleMaintaince">
                    </TK1:ModalPopupExtender>
                    <asp:Button ID="btnActive" runat="server" CssClass="buttonblue" Text="Active"/>
                    <asp:Button ID="btnActiveAll" runat="server" CssClass="buttonblue" Text="ActiveAll"
                         />
                    <asp:Button ID="btnLock" runat="server" CssClass="buttonblue" Text="Lock" OnClick="BtnLockClick" />
                    <asp:Button ID="btnMaint" runat="server" CssClass="buttonblue" 
                        Text="Maintenance" />
                    <asp:Button ID="btnMaintenanceAll" runat="server" Text="MaintenanceAll" CssClass="buttonblue" />
                </td>
            </tr>
            <tr>
                <td>
                    DR Operations
                </td>
                <td colspan="2" class="align-right">
                    <TK1:ModalPopupExtender ID="ModalPopupExtenderSwitchOver" runat="server" TargetControlID="btnSwitchOver"
                        RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelSwitchOver"
                        PopupDragHandleControlID="panelSwitchOver" Drag="true" BackgroundCssClass="modal">
                    </TK1:ModalPopupExtender>
                    <TK1:ModalPopupExtender ID="ModalPopupExtenderSwitchBack" runat="server" TargetControlID="btnSwitchBack"
                        RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelSwitchBack"
                        PopupDragHandleControlID="panelSwitchBack" Drag="true" BackgroundCssClass="modal">
                    </TK1:ModalPopupExtender>
                    <TK1:ModalPopupExtender ID="ModalPopupExtenderFailOver" runat="server" TargetControlID="btnFailBack"
                        RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelFailBack"
                        PopupDragHandleControlID="panelFailBack" Drag="true" BackgroundCssClass="modal">
                    </TK1:ModalPopupExtender>
                    <TK1:ModalPopupExtender ID="ModalPopupExtenderFailBack" runat="server" TargetControlID="btnFailOver"
                        RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelFail"
                        PopupDragHandleControlID="panelFail" Drag="true" BackgroundCssClass="modal">
                    </TK1:ModalPopupExtender>
                    <TK1:ModalPopupExtender ID="ModalPopupExtenderCustom" runat="server" TargetControlID="btnCustom"
                        RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="panelCustom"
                        PopupDragHandleControlID="panelCustom" Drag="true" BackgroundCssClass="modal">
                    </TK1:ModalPopupExtender>
                    <asp:LinkButton ID="LinkBtnSwitchOver" runat="server" OnClick="LinkBtnSwitchOverClick"
                        Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                    <asp:LinkButton ID="LinkBtnSwitchBack" runat="server" OnClick="LinkBtnSwitchBackClick"
                        Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                    <asp:LinkButton ID="LinkBtnFailOver" runat="server" OnClick="LinkBtnFailOverClick"
                        Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                    <asp:LinkButton ID="LinkBtnFailBack" runat="server" OnClick="LinkBtnFailBackClick"
                        Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                    <asp:LinkButton ID="LinkBtnCustom" runat="server" OnClick="LinkBtnCustomClick" Visible="false"><span class="Replicating">&nbsp; </span> Executing Workflow</asp:LinkButton>
                    <asp:Button ID="btnSwitchOver" runat="server" CssClass="buttonblue" Text="SwitchOver" />
                    <asp:Button ID="btnSwitchBack" runat="server" CssClass="buttonblue" Text="SwitchBack"
                        Enabled="false" />
                    <asp:Button ID="btnFailOver" runat="server" CssClass="buttonblue" Text="Failover"
                        Enabled="false" />
                    <asp:Button ID="btnFailBack" runat="server" CssClass="buttonblue" Text="Failback"
                        Enabled="false" />
                    <asp:Button ID="btnCustom" runat="server" CssClass="buttonblue" Text="Custom" 
                        Enabled="false" />
                </td>
            </tr>
            <tr>
           <%-- <td>
             Create WorkFlow Execution Log
            </td>--%>
            <td  colspan="2" class="align-left" visible ="false">
                <asp:DropDownList ID="ddlWorlFlow" runat="server" Visible = "false"  
                    onselectedindexchanged="DdlWorlFlowSelectedIndexChanged" 
                    AutoPostBack="True">
                    <asp:ListItem Value="-1">No Workflow is defined</asp:ListItem>
                </asp:DropDownList>&nbsp;&nbsp;
                 <asp:Button ID="btnCreateLog" runat="server" CssClass="buttonblue" Visible="false"
                    Text="Create Log" Enabled="True" onclick="BtnCreateLogClick" />&nbsp;&nbsp;&nbsp;
                    <asp:Label ID="lblInfo" runat="server"></asp:Label>
            </td>
            
            </tr>
        </table> 
         </div>
        <div id = "Div5">
   
        </div>
    </div>

    <script src="../Script/ExecuteWorkFlow.js" type="text/javascript"></script>

    <script src="../Script/Maintenance.js" type="text/javascript"></script>

</asp:Content>
