﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using BCMS.Helper;
using BCMS.BusinessFacade;
using BCMS.Common.Shared;
namespace BCMS.UI.Controls
{
    public partial class NormalDatabaseList : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.DATABASE_CONFIGURATION;
        public override void PrepareView()
        {
          Binddata();
        }
      
        private void Binddata()
        {
            var result = Facade.GetAllDatabase();
            if (result != null)
            {
                lvNormaldatabase.DataSource = result;
                lvNormaldatabase.DataBind();
            }
        }
      
        protected void LvdatabasePreRender(object sender, EventArgs e)
        {
            Binddata();
        }

        protected void LvdatabaseItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lblDatabaseId = (lvNormaldatabase.Items[e.ItemIndex].FindControl("ID")) as Label;
            var lblDatabaseName = (lvNormaldatabase.Items[e.ItemIndex].FindControl("BaseName")) as Label;
            if (lblDatabaseId == null) return;
            var id = Convert.ToInt32(lblDatabaseId.Text);
            var isDelete = Facade.DeleteDatabaseById(id);

            if (isDelete)
            {
                //CurrentCacheManager.DataCache.RemoveItem(CacheKey);
                //lvdatabase.EditIndex = -1;
                //lvdatabase.DataSource = Facade.GetAllDatabase();
                //lvdatabase.DataBind();
                Binddata();
                //if (lblDatabaseName != null)
                //    _logger.DebugFormat("{0} - Delete {1} Database component - {2}", HostAddress, lblDatabaseName.Text, LoggedInUserName);
            }
            else
            {
                Binddata();
            }
        }

        protected void LvdatabaseItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentURL);

            var lblDatabaseId = (lvNormaldatabase.Items[e.NewEditIndex].FindControl("ID")) as Label;
            if (lblDatabaseId != null)
            {
                //secureUrl = UrlHelper.BuildSecureUrl(ReturnURL, string.Empty, Constants.UrlConstants.Params.DATABASE_ID,
                //lblDatabaseId.Text);
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.DATABASE_ID, lblDatabaseId.Text,
                Constants.UrlConstants.Params.DATABASE_TYPE, "1");
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvdatabase_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ibtnEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ibtnDelete") as ImageButton;

            if (IsUserOperator)
            {
                edit.Enabled = false;
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
            }
        }
    }
}