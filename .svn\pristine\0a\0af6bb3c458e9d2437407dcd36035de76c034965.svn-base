﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    public interface IInfraobjectCGDetailsDataAccess
    {
        InfraobjectCGDetails Add(InfraobjectCGDetails _infraobjectCG);

        InfraobjectCGDetails Update(InfraobjectCGDetails _infraobjectCG);

        IList<InfraobjectCGDetails> GetById(int id);

        IList<InfraobjectCGDetails> GetAll();

        IList<InfraobjectCGDetails> GetByXIVId(int id);

        IList<InfraobjectCGDetails> GetByBaseReplicationId(int id);
    }
}
