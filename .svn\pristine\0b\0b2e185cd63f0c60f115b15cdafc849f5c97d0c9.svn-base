﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class DataGuardMonitorDataAccess : BaseDataAccess, IDataGuardMonitorDataAccess
    {
        #region Constructors

        public DataGuardMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DataGuardReplication> CreateEntityBuilder<DataGuardReplication>()
        {
            return (new DataGuardMonitorBuilder()) as IEntityBuilder<DataGuardReplication>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="DataGuardMonitor" /> bcms_datagaurd_monitor_logs table.
        /// </summary>
        /// <param name="dataGuardMonitor">DataGuardMonitor</param>
        /// <returns>DataGuardMonitor</returns>
        /// <author><PERSON></author>
        DataGuardMonitor IDataGuardMonitorDataAccess.Add(DataGuardMonitor dataGuardMonitor)
        {
            try
            {
                const string sp = "DataGuardMonitorLogs_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iPRStatus", DbType.AnsiString, dataGuardMonitor.PRStatus);
                    Database.AddInParameter(cmd, Dbstring+"iDRStatus", DbType.AnsiString, dataGuardMonitor.DRStatus);
                    Database.AddInParameter(cmd, Dbstring+"iPRErrorReason", DbType.AnsiString, dataGuardMonitor.PRErrorReason);
                    Database.AddInParameter(cmd, Dbstring+"iDRErrorReason", DbType.AnsiString, dataGuardMonitor.DRErrorReason);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, dataGuardMonitor.InfraObjectId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_datagaurd_monitor"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        dataGuardMonitor = reader.Read()
                            ? CreateEntityBuilder<DataGuardMonitor>().BuildEntity(reader, dataGuardMonitor)
                            : null;
                    }

                    if (dataGuardMonitor == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DataGuardReplication already exists. Please specify another oracleDataGuardReplication.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this oracleDataGuardReplication.");
                                }
                        }
                    }

                    return dataGuardMonitor;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting DataGuardMonitor Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Update <see cref="DataGuardMonitor" />into bcms_datagaurd_monitor_logs table.
        /// </summary>
        /// <param name="dataGuardMonitor">DataGuardMonitor</param>
        /// <returns>DataGuardMonitor</returns>
        /// <author>Kiran Ghadge</author>
        /// <remarks> This method is Not in Use </remarks>
        DataGuardMonitor IDataGuardMonitorDataAccess.Update(DataGuardMonitor dataGuardMonitor)
        {
            try
            {
                const string sp = "DataGuardMonitor_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, dataGuardMonitor.Id);
                    Database.AddInParameter(cmd, Dbstring+"iPRStatus", DbType.AnsiString, dataGuardMonitor.PRStatus);
                    Database.AddInParameter(cmd, Dbstring+"iDRStatus", DbType.AnsiString, dataGuardMonitor.DRStatus);
                    Database.AddInParameter(cmd, Dbstring+"iPRErrorReason", DbType.AnsiString, dataGuardMonitor.PRErrorReason);
                    Database.AddInParameter(cmd, Dbstring+"iDRErrorReason", DbType.AnsiString, dataGuardMonitor.DRErrorReason);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, dataGuardMonitor.InfraObjectId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        dataGuardMonitor = reader.Read()
                            ? CreateEntityBuilder<DataGuardMonitor>().BuildEntity(reader, dataGuardMonitor)
                            : null;
                    }

                    if (dataGuardMonitor == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DataGuardReplication already exists. Please specify another oracleDataGuardReplication.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this oracleDataGuardReplication.");
                                }
                        }
                    }

                    return dataGuardMonitor;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating DataGuardMonitor Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DataGuardMonitor" />From bcms_datagaurd_monitor_logs table by Id.
        /// </summary>
        /// <param name="id">Id of the DataGuardMonitor</param>
        /// <returns>DataGuardMonitor</returns>
        /// <author>Kiran Ghadge</author>
        DataGuardMonitor IDataGuardMonitorDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DataGuardMonitor_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DataGuardMonitor>()).BuildEntity(reader, new DataGuardMonitor());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDataGuardMonitorDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DataGuardMonitor" />From bcms_datagaurd_monitor_logs table by InfraObjectId.
        /// </summary>
        /// <param name="id">Id of the DataGuardMonitor</param>
        /// <returns>DataGuardMonitor</returns>
        /// <author>Kiran Ghadge</author>
        DataGuardMonitor IDataGuardMonitorDataAccess.GetByInfraObjectId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DataGuardMonitor_GetByInfrId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DataGuardMonitor>()).BuildEntity(reader, new DataGuardMonitor());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDataGuardMonitorDataAccess.GetByInfraObjectId(" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DataGuardMonitor" />From bcms_datagaurd_monitor_logs table by InfraObjectId.
        /// </summary>
        /// <param name="id">Id of the DataGuardMonitor</param>
        /// <returns>DataGuardMonitor</returns>
        /// <author>Kiran Ghadge</author>
        DataGuardMonitor IDataGuardMonitorDataAccess.GetByInfraObjectId(int InfraObjectID, int NodeID)
        {
            try
            {
                DataGuardMonitor dataGuardMonitor = new DataGuardMonitor();
                if (InfraObjectID < 1)
                {
                    throw new ArgumentNullException("InfraObjectID");
                }

                const string sp = "DataGuardMonitor_GetByInfrDBId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraObjectID);
                    Database.AddInParameter(cmd, Dbstring + "iNodeID", DbType.Int32, NodeID);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DataGuardMonitor>()).BuildEntity(reader, new DataGuardMonitor());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDataGuardMonitorDataAccess.GetByInfraObjectId (" + InfraObjectID +
                   "NodeID" + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DataGuardMonitor" />From bcms_datagaurd_monitor_logs table by Name.
        /// </summary>
        /// <param name="name">Name of the DataGuardMonitor</param>
        /// <returns>DataGuardMonitor</returns>
        /// <author>Kiran Ghadge</author>
        DataGuardMonitor IDataGuardMonitorDataAccess.GetByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "DataGuardMonitor_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DataGuardMonitor>()).BuildEntity(reader, new DataGuardMonitor())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDataGuardMonitorDataAccess.GetByName(" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     GetAll <see cref="DataGuardMonitor" />From bcms_datagaurd_monitor_logs table.
        /// </summary>
        /// <returns>DataGuardMonitorc list</returns>
        /// <author>Kiran Ghadge</author>
        IList<DataGuardMonitor> IDataGuardMonitorDataAccess.GetAll()
        {
            try
            {
                const string sp = "DataGuardMonitor_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DataGuardMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDataGuardMonitorDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="DataGuardMonitor" />From bcms_datagaurd_monitor_logs table by id.
        /// </summary>
        /// <param name="id">Id of the DataGuardMonitor</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IDataGuardMonitorDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DataGuardMonitor_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeSuccessDelete:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeErrorChildExists:
                            {
                                throw new ArgumentException(
                                    "Cannot delete a oracleDataGuardReplication which has association.");
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this oracleDataGuardReplication.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting DataGuardMonitor Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Check <see cref="DataGuardMonitor" />From bcms_datagaurd_monitor_logs table by Name.
        /// </summary>
        /// <param name="name">Name of the DataGuardMonitor</param>
        /// <returns>bool</returns>
        /// <author>Kiran Ghadge</author>
        bool IDataGuardMonitorDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "DataGuardMonitor_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this companyProfile.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDataGuardMonitorDataAccess.IsExistByName (" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}