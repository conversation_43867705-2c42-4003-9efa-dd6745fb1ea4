﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ConfigureBIARule.aspx.cs" Inherits="CP.UI.ImpactAnalysis.ConfigureBIARule" %>

<%@ Register Assembly="Telerik.Web.UI" Namespace="Telerik.Web.UI" TagPrefix="telerik" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <style>
        .RadComboBoxDropDown .rcbList li {
            font-size: 12px;
        }

            .RadComboBoxDropDown .rcbList li:nth-child(even) {
                background-color: rgba(0,0,3,0.1);
            }

        .RadComboBox table {
            background-color: #efefef !important;
            background-image: linear-gradient(to bottom, #f4f4f4, #e7e7e7) !important;
            background-repeat: repeat-x !important;
            border: 1px solid #cecece !important;
            color: rgba(0, 0, 0, 0.6) !important;
        }

        .RadComboBox .rcbEmptyMessage {
            font-style: normal !important;
        }

        .RadComboBoxDropDown {
            border: 1px solid #cecece !important;
        }

        .importexportbtn {
            margin-top: 5px;
            margin-right: 10px;
        }
    </style>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>

</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="widget widget-heading-simple widget-body-white margin-bottom-none">
        <div class="widget-body">
            <telerik:RadWindowManager ID="RadWindowManager1" runat="server">
                <Windows>
                    <telerik:RadWindow ID="TelRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false" OnClientClose="OnChildWindowClosed"
                        Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="420"
                        Width="1260" Skin="Metro" ShowContentDuringLoad="false" />
                </Windows>
                <Windows>
                    <telerik:RadWindow ID="TelImpRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false"
                        Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="620" ShowContentDuringLoad="false"
                        Width="1260" Skin="Metro" />
                </Windows>
                <Windows>
                    <telerik:RadWindow ID="TelImpBrtoBfRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false"
                        Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="620" ShowContentDuringLoad="false"
                        Width="1260" Skin="Metro" />
                </Windows>
            </telerik:RadWindowManager>

            <telerik:RadMultiPage runat="server" ID="RadMultiPage3" SelectedIndex="0">

                <telerik:RadPageView ID="RadPageView8" runat="server" Selected="true">
                    <%--  <div class="innerLR">
                        <h3>
            <i class="icon-projector-screen-line text-primary vertical-sub"></i>
            Entity Impact Relationship</h3>--%>
                    <div class="widget widget-heading-simple widget-body-white margin-bottom-none">
                        <div class="widget-body padding-none-LR">

                            <telerik:RadTabStrip runat="server" ID="RadTabStrip1" MultiPageID="RadMultiPage2" SelectedIndex="0" Skin="Silk" Width="1329">
                                <Tabs>
                                    <telerik:RadTab Text="InfraObject to Business Function" Width="300px"></telerik:RadTab>
                                    <telerik:RadTab Text=" Business Function To Business Function" Width="300px"></telerik:RadTab>
                                    <telerik:RadTab Text="Business Service to Business Service" Width="300px"></telerik:RadTab>
                                </Tabs>
                            </telerik:RadTabStrip>

                            <telerik:RadMultiPage runat="server" ID="RadMultiPage2" SelectedIndex="0" CssClass="outerMultiPage">
                                <telerik:RadPageView runat="server" ID="RadPageView2">
                                    <telerik:RadMultiPage runat="server" ID="RadMultiPage1" SelectedIndex="0">
                                        <telerik:RadPageView runat="server" ID="RadPageView1">
                                            <telerik:RadSplitter ID="RadSplitter2" runat="server" Height="450" Width="1329" Orientation="Horizontal">
                                                <telerik:RadPane ID="RadPane3" runat="server" Height="400" MinHeight="350" MaxHeight="750" Width="1329">
                                                    <%--<h3>
                                    <i class="icon-projector-screen-line text-primary vertical-sub"></i>
                                    InfraObject To Business Function </h3>--%>

                                                    <asp:UpdatePanel ID="UpFilter" runat="server" UpdateMode="Conditional" class="col-md-12 form-horizontal uniformjs">
                                                        <ContentTemplate>
                                                            <%--   <div class="form-group padding bg-white">
                                                          <label class="col-md-3 control-label">Buiness Service </label>
                                                            <div class="col-md-9">--%>
                                                            <telerik:RadComboBox ID="RadComboBox1" runat="server" Width="350" CssClass="margin-top" DropDownCssClass="widget border"
                                                                DropDownWidth="350" EmptyMessage="Search" HighlightTemplatedItems="true"
                                                                EnableLoadOnDemand="true" AllowCustomText="true" MarkFirstMatch="true" Filter="Contains"
                                                                Label="Select Business Service to view Rules" LabelCssClass="control-label" Skin="" EnableEmbeddedSkins="false" OnSelectedIndexChanged="RadComboBox1_SelectedIndexChanged" AutoPostBack="true">
                                                                <ItemTemplate>
                                                                    <div class="row">
                                                                        <div class="form-group">
                                                                            <div class="col-md-3">
                                                                                <%--<%# DataBinder.Eval(Container, "Attributes['Infracomponent']")%>--%>
                                                                                <%# Eval("businesService")%>
                                                                            </div>
                                                                            <div class="col-md-3" style="display: none">
                                                                                <%# Eval("bsid")%>
                                                                            </div>
                                                                            <%--<div class="col-md-4">
                                                                                <%# Eval("Infraobject")%>
                                                                            </div>
                                                                            <div class="col-md-5">
                                                                               <%# Eval("Infracomponent")%>
                                                                            </div>--%>
                                                                        </div>
                                                                    </div>
                                                                </ItemTemplate>

                                                            </telerik:RadComboBox>

                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>

                                                    <hr class="margin-bottom-none" />

                                                    <asp:UpdatePanel ID="udpgraph" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                            <div id="divemptyFunctionPopup"></div>
                                                            <div id="divbsView" style="display: none;">
                                                                <div id="bsbody" runat="server">
                                                                </div>
                                                                <div id="bsbody2" runat="server">
                                                                </div>
                                                                <div class="col-md-4">
                                                                    <a id="btnReload" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                                                    <a id="btnReset" class="reset-icon margin-top" title="Back to initial zoom position">&nbsp;</a>
                                                                </div>
                                                                <div class="col-md-8 text-right">
                                                                    <img src="../Images/icons/yellow-dot.png" />
                                                                    <label class="margin-right">Partial Impact</label>
                                                                    <img src="../Images/icons/orange-dot.png" />
                                                                    <label class="margin-right">Major Impact</label>
                                                                    <img src="../Images/icons/red-dot.png" />
                                                                    <label class="margin-right">Total Impact</label>
                                                                </div>
                                                            </div>

                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>

                                                </telerik:RadPane>
                                                <telerik:RadSplitBar ID="Radsplitbar2" runat="server">
                                                </telerik:RadSplitBar>
                                                <telerik:RadPane ID="RadPane4" runat="server" MaxHeight="250" Height="150">

                                                    <%--                                        <div class="row">--%>
                                                    <div class="col-md-12">

                                                        <div class="widget-head">
                                                            <h4 class="heading padding-none-LR">Impact Relationship View</h4>
                                                            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional" class="text-right">
                                                                <ContentTemplate>
                                                                    <asp:Label ID="lblErr" runat="server" CssClass="error"></asp:Label>
                                                                    <asp:ImageButton runat="server" ID="imgRefreshInfraToBF" OnClick="imgRefreshInfraToBF_Click" AlternateText=" " Width="0" Height="0" Visible="true" />

                                                                    <asp:ImageButton runat="server" class="vertical-sub importexportbtn" ID="imgBtnImportBIARule" OnClientClick="javascript:void(0)" ToolTip="Import All BIA Rules" ImageUrl="../Images/icons/import-icon.png" />

                                                                    <asp:ImageButton runat="server" class="vertical-sub importexportbtn" ID="imgBtnExportBIARule" OnClick="imgBtnExportBIARule_Click" ToolTip="Export All BIA Rules" ImageUrl="../Images/icons/export-icon.png" />

                                                                    <asp:ImageButton runat="server" class="vertical-sub" ID="imgBtnInfraToBF" OnClick="imgBtnInfraToBF_Click" ToolTip="Configure BIA InfratoBF Rule" ImageUrl="../Images/plus-icon.png" />
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>
                                                        </div>
                                                        <div class="widget-body padding-none">

                                                            <asp:UpdatePanel ID="upnl" runat="server" UpdateMode="Conditional">
                                                                <ContentTemplate>

                                                                    <asp:ListView ID="lvInfratoBSlst" runat="server" OnItemDataBound="lvInfratoBSlst_ItemDataBound" OnItemEditing="lvInfratoBSlst_ItemEditing" OnItemCommand="lvInfratoBSlst_ItemCommand" OnItemDeleting="lvInfratoBSlst_ItemDeleteing">
                                                                        <LayoutTemplate>
                                                                            <table id="tblebia3" class="table table-bordered table-condensed EntityImptTable margin-bottom-none" style="table-layout: fixed;">
                                                                                <thead>

                                                                                    <tr>
                                                                                        <th style="width: 3%;" class="text-center">#</th>
                                                                                        <%--<th>Rule</th>--%>
                                                                                        <th style="width: 10%;">Infraobject</th>
                                                                                        <th style="width: 10%;">JobName</th>
                                                                                        <th style="width: 11%;">Process Name</th>
                                                                                        <th style="width: 15%;">If Infraobject Component</th>
                                                                                        <th style="width: 10%;">Business Function</th>
                                                                                        <th style="width: 10%;">Impact</th>
                                                                                        <th style="width: 15%;">Business Service</th>
                                                                                        <th style="width: 10%;">Impact</th>

                                                                                        <th style="width: 6%;" class="text-center">Action</th>
                                                                                    </tr>
                                                                                </thead>
                                                                            </table>
                                                                            <div class=" ">
                                                                                <table class="table table-bordered table-condensed EntityImptTable " style="table-layout: fixed;">
                                                                                    <tbody>
                                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                                    </tbody>

                                                                                </table>
                                                                            </div>
                                                                        </LayoutTemplate>

                                                                        <ItemTemplate>
                                                                            <tr>
                                                                                <td style="width: 3%;" class="text-center">
                                                                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %></td>
                                                                                <td style="width: 10%" class="tdword-wrap">
                                                                                    <asp:Label ID="lblinfraName" runat="server" class="text-bold " Text='<%# Eval("InfraObjectName") %>'></asp:Label></td>
                                                                                <td style="width: 10%" class="tdword-wrap">
                                                                                    <asp:Label ID="lbljobname" runat="server" class="text-bold " Text='<%# Eval("JOBNAME") %>'></asp:Label></td>
                                                                                <td style="width: 11%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblprocessName" runat="server" class="text-bold " Text='<%# Eval("ProcessName") %>'></asp:Label>
                                                                                </td>
                                                                                <td style="width: 14.8%;" class="tdword-wrap"><%--IF Infraobject Component --%>
                                                                                    <asp:Label ID="lblInfraComponentId" runat="server" class="label label-red " Text='<%# Eval("InfraComponentId") %>'></asp:Label>
                                                                                    <asp:Label ID="lblComponentID" Visible="false" runat="server" class="label label-red " Text='<%# Eval("InfraComponentId") %>'></asp:Label>
                                                                                    <asp:Label ID="lblInfraComponentType" runat="server" class="label label-primary " Text='<%# Eval("InfraComponentType") %>' Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblInfraID" runat="server" class="label label-primary " Text='<%# Eval("InfraID") %>' Visible="false"></asp:Label>
                                                                                    is not available then 
                                                                                </td>
                                                                                <td style="width: 10.8%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblBFID" runat="server" class="text-bold " Text='<%# Eval("BFID") %>'></asp:Label>
                                                                                    will be   
                                                                                </td>
                                                                                <td style="width: 10.8%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblBFImpactID" runat="server" CssClass="label label-yellow" Text='<%# Eval("BFImpactID") %>'></asp:Label>
                                                                                    and  
                                                                                </td>
                                                                                <td style="width: 14%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblBSID" runat="server" class="text-bold " Text='<%# Eval("BSId") %>' Visible="false"></asp:Label>
                                                                                    <asp:Label ID="lblBSName" runat="server" class="text-bold " ToolTip='<%# Eval("BSName") + "  will be" %> ' Text='<%# Eval("BSName") %>'></asp:Label>
                                                                                    <%-- <label class="label label-primary ">BS Object1</label>--%>
                                                                                will be   
                                                                                </td>
                                                                                <td style="width: 10%;">
                                                                                    <asp:Label ID="lblBSImpactId" runat="server" class="label label-yellow " Text='<%# Eval("BSImpactID") %>'></asp:Label>
                                                                                </td>

                                                                                <td style="width: 6%;" class="text-center">
                                                                                    <asp:ImageButton ID="btnSaveProfile" runat="server" ImageUrl="../Images/icons/pencil.png" CommandName="Edit" ToolTip="Edit" Enabled="<%# IsSuperAdmin %>"></asp:ImageButton>
                                                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                                                    <asp:ImageButton ID="btnShowDiagram" ImageUrl="../images/report-icon.png" runat="server" CommandName="View" ToolTip="View"></asp:ImageButton>
                                                                                    <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ImgDelete" ConfirmText='<%# "Please confirm if you want to Delete " + Eval("RuleCode") +" " + "Rule?" %>'
                                                                                        OnClientCancel="CancelClick">
                                                                                    </TK1:ConfirmButtonExtender>
                                                                                </td>
                                                                            </tr>
                                                                        </ItemTemplate>
                                                                    </asp:ListView>
                                                                    <div class="row">
                                                                        <div class="col-xs-6">
                                                                            <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvInfratoBSlst">
                                                                                <Fields>
                                                                                    <asp:TemplatePagerField>
                                                                                        <PagerTemplate>
                                                                                            <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                                            Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                                            <br />
                                                                                        </PagerTemplate>
                                                                                    </asp:TemplatePagerField>
                                                                                </Fields>
                                                                            </asp:DataPager>
                                                                        </div>
                                                                        <div class="col-xs-6 text-right">
                                                                            <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvInfratoBSlst" PageSize="100">
                                                                                <Fields>
                                                                                    <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                                                        ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                                                    <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                                                        NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                                                        NumericButtonCssClass="btn-pagination" />
                                                                                    <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                                                        ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                                                </Fields>
                                                                            </asp:DataPager>
                                                                        </div>
                                                                    </div>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>
                                                        </div>
                                                    </div>

                                                    <%--   </div>--%>
                                                </telerik:RadPane>
                                            </telerik:RadSplitter>
                                        </telerik:RadPageView>
                                    </telerik:RadMultiPage>
                                </telerik:RadPageView>
                                <telerik:RadPageView runat="server" ID="RadPageView3">



                                    <telerik:RadSplitter ID="RadSplitter1" runat="server" Height="450" Width="1329" Orientation="Horizontal">
                                        <telerik:RadPane ID="RadPane2" runat="server" Height="400" MinHeight="350" MaxHeight="750" Width="1329">
                                            <%--<h3>
                                    <i class="icon-projector-screen-line text-primary vertical-sub"></i>
                                    InfraObject To Business Function </h3>--%>

                                            <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional" class="col-md-12 form-horizontal uniformjs">
                                                <ContentTemplate>
                                                    <%--   <div class="form-group padding bg-white">
                                                          <label class="col-md-3 control-label">Buiness Service </label>
                                                            <div class="col-md-9">--%>
                                                    <telerik:RadComboBox ID="RadComboBox2" runat="server" Width="350" CssClass="margin-top" DropDownCssClass="widget border"
                                                        DropDownWidth="350" EmptyMessage="Search" HighlightTemplatedItems="true"
                                                        EnableLoadOnDemand="true" AllowCustomText="true" MarkFirstMatch="true" Filter="Contains"
                                                        Label="Select Business Service to view Rules" LabelCssClass="control-label" Skin="" EnableEmbeddedSkins="false" OnSelectedIndexChanged="RadComboBox2_SelectedIndexChanged" AutoPostBack="true">
                                                        <ItemTemplate>
                                                            <div class="row">
                                                                <div class="form-group">
                                                                    <div class="col-md-3">
                                                                        <%# Eval("businesService")%>
                                                                    </div>
                                                                    <div class="col-md-3" style="display: none">
                                                                        <%# Eval("bsid")%>
                                                                    </div>
                                                                    <%--<div class="col-md-4">
                                                                        <%# Eval("Infraobject")%>
                                                                    </div>
                                                                    <div class="col-md-5">
                                                                        <%# Eval("Infracomponent")%>
                                                                    </div>--%>
                                                                </div>
                                                            </div>
                                                        </ItemTemplate>

                                                    </telerik:RadComboBox>

                                                </ContentTemplate>
                                            </asp:UpdatePanel>
                                            <hr class="margin-bottom-none" />

                                            <asp:UpdatePanel ID="udpgraphBftoBf" runat="server" UpdateMode="Conditional">
                                                <ContentTemplate>
                                                    <div id="divemptyFunctionPopupBftoBf"></div>
                                                    <div id="divbsViewBFtoBF" style="display: none;">
                                                        <div id="bsbodyBftoBf" runat="server">
                                                        </div>

                                                        <div class="col-md-4">
                                                            <a id="btnReloadBftoBF" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                                            <a id="btnResetbftobf" class="reset-icon margin-top" title="Back to initial zoom position">&nbsp;</a>
                                                        </div>
                                                        <div class="col-md-8 text-right">
                                                            <img src="../Images/icons/yellow-dot.png" />
                                                            <label class="margin-right">Partial Impact</label>
                                                            <img src="../Images/icons/orange-dot.png" />
                                                            <label class="margin-right">Major Impact</label>
                                                            <img src="../Images/icons/red-dot.png" />
                                                            <label class="margin-right">Total Impact</label>
                                                        </div>
                                                    </div>

                                                </ContentTemplate>
                                            </asp:UpdatePanel>

                                        </telerik:RadPane>
                                        <telerik:RadSplitBar ID="Radsplitbar1" runat="server">
                                        </telerik:RadSplitBar>
                                        <telerik:RadPane ID="RadPane1" runat="server" MaxHeight="250" Height="150">
                                            <%--                   <h3>
                                    <i class="icon-projector-screen-line text-primary vertical-sub"></i>
                                    Business Function To Business Function</h3>
                                <div class="row">--%>
                                            <div class="col-md-12">

                                                <div class="widget-head">
                                                    <h4 class=" heading padding-none-LR">Impact Relationship View</h4>
                                                    <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional" class="text-right">
                                                        <ContentTemplate>
                                                            <asp:Label ID="lblErr2" runat="server" CssClass="error"></asp:Label>
                                                            <asp:ImageButton runat="server" class="vertical-sub importexportbtn" ID="imgBtnImportBFtoBfBIARule" OnClientClick="javascript:void(0)" ToolTip="Import BF to BF  BIA Rules" ImageUrl="../Images/icons/import-icon.png" />

                                                            <asp:ImageButton runat="server" Style="margin-top: 5px;" class="vertical-sub" ID="ImgBtnBFtoBF" ToolTip="Configure BIA BFtoBF Rule" ImageUrl="../Images/plus-icon.png" OnClick="ImgBtnBFtoBF_Click" />
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>
                                                <div class="widget-body padding-none">
                                                    <asp:UpdatePanel ID="upnlBFtoBFlst" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>

                                                            <asp:ListView ID="lvBFtoBFlst" runat="server" OnItemDataBound="lvBFtoBFlst_ItemDataBound" OnItemEditing="lvBFtoBFlst_ItemEditing" OnItemCommand="lvBFtoBFlst_ItemCommand" OnItemDeleting="lvBFtoBFlst_ItemDeleteing">
                                                                <LayoutTemplate>
                                                                    <table id="Table1" class="table table-bordered table-condensed EntityImptTable margin-bottom-none" width="100%">
                                                                        <thead>

                                                                            <tr>
                                                                                <th style="width: 3%;">#</th>
                                                                                <%--<th>Rule</th>--%>
                                                                                <th style="width: 10%;">Rule Code</th>
                                                                                <th style="width: 20%;">If Business Function</th>

                                                                                <th style="width: 15%;">Impact</th>
                                                                                <th style="width: 20%;">Business Function</th>
                                                                                <th style="width: 15%;">Impact</th>
                                                                                <th style="width: 10%;">Effective Date</th>
                                                                                <th style="width: 7%;">Action</th>
                                                                            </tr>
                                                                        </thead>
                                                                    </table>
                                                                    <div class=" ">
                                                                        <table class="table table-bordered table-condensed EntityImptTable " width="100%">
                                                                            <tbody>
                                                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                            </tbody>
                                                                        </table>
                                                                    </div>

                                                                </LayoutTemplate>
                                                                <ItemTemplate>
                                                                    <tr>
                                                                        <td style="width: 3%;">
                                                                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %></td>
                                                                        <td style="width: 10%;">
                                                                             <asp:Label ID="lblBfrulecode" runat="server" class="text-bold " Text='<%# Eval("RuleCode") %>'></asp:Label>

                                                                        </td>

                                                                        <td style="width: 20%;">
                                                                            <asp:Label ID="lblParentBFID" runat="server" class="text-bold " Text='<%# Eval("ParentBFId") %>'></asp:Label>
                                                                            is 
                                                                        </td>
                                                                        <td style="width: 15%;">
                                                                            <asp:Label ID="lblParentImpactID" runat="server" class="label label-red " Text='<%# Eval("ParentBFImpactID") %>'></asp:Label>
                                                                            then  
                                                                        </td>
                                                                        <td style="width: 20%;">
                                                                            <asp:Label ID="lblChildBFID" runat="server" class="text-bold" Text='<%# Eval("ChildBFId") %>'></asp:Label>
                                                                            <%-- <label class="label label-primary ">BS Object1</label>--%>
                                        will be   
                                                                        </td>
                                                                        <td style="width: 15%;">
                                                                            <asp:Label ID="lblChildImpactID" runat="server" class="label label-orange " Text='<%# Eval("ChildBFImpactID") %>'></asp:Label>
                                                                            <%-- <label class="label label-warning ">Partially</label>--%>
                                                                        </td>




                                                                        <td style="width: 10%;">
                                                                            <asp:Label ID="lblEffectiveDateFrom_InfraToBF" runat="server" Text='<%# Eval("EffectiveDateFrom") %>'></asp:Label><%--25-02-2015 16:26:10--%>
                                                                        </td>
                                                                        <td style="width: 7%;">
                                                                            <%--<asp:Button ID="btnSaveProfile_BFToBF" CssClass="btn btn-primary" runat="server" CommandName="Edit"
                                                                            Text="Edit"></asp:Button>--%>
                                                                            <asp:ImageButton ID="btnSaveProfile_BFToBF" runat="server" ImageUrl="../Images/icons/pencil.png" CommandName="Edit" ToolTip="Edit" Enabled="<%# IsSuperAdmin %>"></asp:ImageButton>
                                                                            <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                                            <asp:ImageButton ID="btnShowDiagram" ImageUrl="../images/report-icon.png" runat="server" CommandName="View" ToolTip="View"></asp:ImageButton></td>
                                                                        <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ImgDelete" ConfirmText='<%# "Please confirm if you want to Delete " + Eval("RuleCode") +" " + "Rule?" %>'
                                                                            OnClientCancel="CancelClick">
                                                                        </TK1:ConfirmButtonExtender>


                                                                    </tr>
                                                                </ItemTemplate>
                                                            </asp:ListView>
                                                            <div class="row">
                                                                <div class="col-xs-6">
                                                                    <asp:DataPager ID="dataPager3" runat="server" PagedControlID="lvBFtoBFlst">
                                                                        <Fields>
                                                                            <asp:TemplatePagerField>
                                                                                <PagerTemplate>
                                                                                    <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                                                    Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                                                    <br />
                                                                                </PagerTemplate>
                                                                            </asp:TemplatePagerField>
                                                                        </Fields>
                                                                    </asp:DataPager>
                                                                </div>
                                                                <div class="col-xs-6 text-right">
                                                                    <asp:DataPager ID="dataPager4" runat="server" PagedControlID="lvBFtoBFlst" PageSize="100">
                                                                        <Fields>
                                                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                                                ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                                            <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                                                NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                                                NumericButtonCssClass="btn-pagination" />
                                                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                                                ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                                                        </Fields>
                                                                    </asp:DataPager>
                                                                </div>
                                                            </div>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>

                                            </div>
                                            <%--</div>--%>
                                        </telerik:RadPane>
                                    </telerik:RadSplitter>
                                </telerik:RadPageView>

                                <telerik:RadPageView runat="server" ID="RadPageView4">
                                    <telerik:RadSplitter ID="RadSplitter3" runat="server" Height="450" Width="1329" Orientation="Horizontal">
                                        <telerik:RadPane ID="RadPane5" runat="server" Height="400" MinHeight="350" MaxHeight="750" Width="1329">
                                            <%--<h3>
                                    <i class="icon-projector-screen-line text-primary vertical-sub"></i>
                                    InfraObject To Business Function </h3>--%>

                                            <asp:UpdatePanel ID="upBStoBS" runat="server" UpdateMode="Conditional" class="col-md-12 form-horizontal uniformjs">
                                                <ContentTemplate>
                                                    <%--   <div class="form-group padding bg-white">
                                                          <label class="col-md-3 control-label">Buiness Service </label>
                                                            <div class="col-md-9">--%>
                                                    <telerik:RadComboBox ID="RadComboBox3" runat="server" Width="350" CssClass="margin-top" DropDownCssClass="widget border"
                                                        DropDownWidth="350" EmptyMessage="Search" HighlightTemplatedItems="true"
                                                        EnableLoadOnDemand="true" AllowCustomText="true" MarkFirstMatch="true" Filter="Contains"
                                                        Label="Select Business Service to view Rules" LabelCssClass="control-label" Skin="" EnableEmbeddedSkins="false" OnSelectedIndexChanged="RadComboBox3_SelectedIndexChanged" AutoPostBack="true">
                                                        <ItemTemplate>
                                                            <div class="row">
                                                                <div class="form-group">
                                                                    <div class="col-md-3">
                                                                        <%# Eval("businesService")%>
                                                                    </div>
                                                                    <div class="col-md-3" style="display: none">
                                                                        <%# Eval("bsid")%>
                                                                    </div>
                                                                    <%--<div class="col-md-4">
                                                                        <%# Eval("Infraobject")%>
                                                                    </div>
                                                                    <div class="col-md-5">
                                                                        <%# Eval("Infracomponent")%>
                                                                    </div>--%>
                                                                </div>
                                                            </div>
                                                        </ItemTemplate>

                                                    </telerik:RadComboBox>

                                                </ContentTemplate>
                                            </asp:UpdatePanel>

                                            <hr class="margin-bottom-none" />

                                            <asp:UpdatePanel ID="upnlBStoBSlst" runat="server" UpdateMode="Conditional">
                                                <ContentTemplate>
                                                    <div id="divemptyServicePopup"></div>
                                                    <div id="divbstobsView" style="display: none;">
                                                       <%-- <div id="bstobsbody3" runat="server">
                                                        </div>--%>
                                                        <div id="bsbody3" runat="server">
                                                        </div>
                                                        <div class="col-md-4">
                                                            <a id="A1" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                                            <a id="A2" class="reset-icon margin-top" title="Back to initial zoom position">&nbsp;</a>
                                                        </div>
                                                        <div class="col-md-8 text-right">
                                                            <img src="../Images/icons/yellow-dot.png" />
                                                            <label class="margin-right">Partial Impact</label>
                                                            <img src="../Images/icons/orange-dot.png" />
                                                            <label class="margin-right">Major Impact</label>
                                                            <img src="../Images/icons/red-dot.png" />
                                                            <label class="margin-right">Total Impact</label>
                                                        </div>
                                                    </div>

                                                </ContentTemplate>
                                            </asp:UpdatePanel>

                                        </telerik:RadPane>
                                        <telerik:RadSplitBar ID="Radsplitbar3" runat="server">
                                        </telerik:RadSplitBar>
                                        <telerik:RadPane ID="RadPane6" runat="server" MaxHeight="250" Height="150">

                                            <%--                                        <div class="row">--%>
                                            <div class="col-md-12">

                                                <div class="widget-head">
                                                    <h4 class=" heading">Impact Relationship View</h4>
                                                    <asp:UpdatePanel ID="UpdatePanel6" runat="server" UpdateMode="Conditional" class="text-right">
                                                        <ContentTemplate>
                                                            <asp:ImageButton runat="server" class="vertical-sub" ID="imgBtnBstoBS" OnClick="imgBtnBstoBS_Click" ToolTip="Configure BIA BStoBS Rule" ImageUrl="../Images/plus-icon.png" />
                                                            <%--<asp:ImageButton runat="server" class="vertical-sub" ID="ImageButton2" OnClick="imgBtnInfraToBF_Click" ImageUrl="../Images/plus-icon.png" />--%>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>

                                                </div>
                                                <div class="widget-body padding-none">
                                                    <asp:UpdatePanel ID="upBStoBSRuleList" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                             <asp:ListView ID="lvBStoBSlst" runat="server" OnItemDataBound="lvBStoBSlst_ItemDataBound" OnItemEditing="lvBStoBSlst_ItemEditing" OnItemCommand="lvBStoBSlst_ItemCommand" OnItemDeleting="lvBStoBSlst_ItemDeleting">
                                                                        <LayoutTemplate>
                                                                            <table id="tblebia3" class="table table-bordered table-condensed EntityImptTable margin-bottom-none" width="100%">
                                                                                <thead>

                                                                                    <tr>
                                                                                        <th style="width: 3%;" class="text-center">#</th>
                                                                                        <%--<th>Rule</th>--%>
                                                                                        <th style="width: 10%;">Rule Code</th>
                                                                                        <th style="width: 25%;">If Business Service</th>

                                                                                        <th style="width: 18%;">Impact</th>
                                                                                        <th style="width: 20%;">Business Service</th>
                                                                                        <%--<th style="width: 15%;">Impact</th>--%>
                                                                                        <th style="width: 13%;">Effective Date</th>
                                                                                        <th style="width: 7%;" class="text-center">Action</th>
                                                                                    </tr>
                                                                                </thead>
                                                                            </table>
                                                                            <div class="table-scroll-SVG" style="max-height:80px;">
                                                                                <table class="table table-bordered table-condensed EntityImptTable" width="100%">
                                                                                    <tbody>
                                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                                    </tbody>

                                                                                </table>
                                                                            </div>
                                                                        </LayoutTemplate>

                                                                        <ItemTemplate>
                                                                            <tr>
                                                                                <td style="width: 3%;" class="text-center">
                                                                                    <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %></td>
                                                                               <td style="width: 10%;">
                                                                                    <asp:Label ID="Label1" runat="server" Text='<%# Eval("RuleCode") %>'></asp:Label>
                                                                                </td>
                                                                                <td style="width: 25%;">
                                                                                    <asp:Label ID="lblParentBSID" runat="server" class="text-bold " Text='<%# Eval("ParentBSId") %>'></asp:Label>
                                                                                    &nbsp;&nbsp;  is 
                                                                                </td>
                                                                                <td style="width: 18%;">
                                                                                    <asp:Label ID="lblParentBSImpactID" runat="server" class="label label-red " Text='<%# Eval("ParentBSImpactID") %>'></asp:Label>
                                                                                    &nbsp;&nbsp;  By  
                                                                                </td>
                                                                                <td style="width: 20%;">
                                                                                    <asp:Label ID="lblChildBSID" runat="server" class="text-bold" Text='<%# Eval("ChildBSId") %>'></asp:Label>
                                                                                    <%-- <label class="label label-primary ">BS Object1</label>--%>
                                                                                </td>
                                                                                <%-- <td style="width: 15%;">
                                                                            <asp:Label ID="lblChildBSImpactID" runat="server" class="label label-orange " Text='<%# Eval("ChildBSImpactID") %>'></asp:Label>
                                                                            <%-- <label class="label label-warning ">Partially</label>
                                                                        </td>--%>
                                                                                <td style="width: 13%;">
                                                                                    <asp:Label ID="lblEffectiveDateFrom_InfraToBF" runat="server" Text='<%# Eval("EffectiveDateFrom") %>'></asp:Label><%--25-02-2015 16:26:10--%>
                                                                                </td>

                                                                                <td style="width: 7%;" class="text-center">
                                                                                    <asp:ImageButton ID="btnBSSaveProfile" runat="server" ImageUrl="../Images/icons/pencil.png" CommandName="Edit" ToolTip="Edit" Enabled="<%# IsSuperAdmin %>"></asp:ImageButton>
                                                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete" ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                                                    <asp:ImageButton ID="btnBSShowDiagram" ImageUrl="../images/report-icon.png" runat="server" CommandName="View" ToolTip="View"></asp:ImageButton></td>
                                                                                    <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ImgDelete" ConfirmText='<%# "Please confirm if you want to Delete " + Eval("RuleCode") +" " + "Rule?" %>'
                                                                                            OnClientCancel="CancelClick">
                                                                                       </TK1:ConfirmButtonExtender>
                                                                            </tr>
                                                                        </ItemTemplate>
                                                                    </asp:ListView>
                                                            
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>
                                            </div>

                                            <%--   </div>--%>
                                        </telerik:RadPane>
                                    </telerik:RadSplitter>
                                </telerik:RadPageView>
                            </telerik:RadMultiPage>

                            <%--<h3>
            <i class="icon-projector-screen-line text-primary vertical-sub"></i>
            Business Function To Business Service</h3>--%>
                        </div>
                    </div>
                    <%--</div>--%>
                </telerik:RadPageView>
            </telerik:RadMultiPage>

        </div>
    </div>

    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script>

        function openRadWindow(url) {
            window.radopen(url, "TelRadWindow");
        }

        function openImpRadWindow(Url) {
            window.radopen(Url, "TelImpRadWindow");
        }

        function openImpBftoBfRadWindow(Url) {
            window.radopen(Url, "TelImpBrtoBfRadWindow");
        }

        $(document).ready(function () {
            context.init({ preventDoubleContext: false });
            $('.entity-list-scroll').mCustomScrollbar({
                axis: "y",
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });
        });
        function OnChildWindowClosed(sender, eventArgs) {
            // document.location.reload(); 

            <%= Page.ClientScript.GetPostBackEventReference(imgRefreshInfraToBF, String.Empty) %>;

            window["<%= upnl.ClientID %>"].submit;
            window["<%= upnlBFtoBFlst.ClientID %>"].submit;


        }
        Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
        function EndRequestHandler(sender, args) {
            $('.entity-list-scroll').mCustomScrollbar({
                axis: "y",
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });
        }


        $(".scroll-pane ").mCustomScrollbar({
            axis: "yx",
            setHeight: "450px",
            setWidth: "1297px",
            advanced: {
                updateOnContentResize: true,
                autoExpandHorizontalScroll: true
            },

        });

        $(".scroll-pane-bottom").mCustomScrollbar({
            axis: "yx",
            setHeight: "150px",
            advanced: {
                updateOnContentResize: true,
                autoExpandHorizontalScroll: true
            },

        });


        $(document).ready(function () {
            //paneScrollWH();
            //var paneHeight = "";
            //var bottompaneHeight = "";
            //function paneScrollWH() {
            //    paneHeight = $("div[id$=RadPane1]").height();
            //    bottompaneHeight = $("div[id$=RadPane6]").height();
            //    $(".scroll-pane").css("height", (paneHeight - 50));
            //    $(".scroll-pane-bottom").css("height", (bottompaneHeight - 50));
            //}
            //$(".rspResizeBar,.rspResizeBarHorizontal, [id*=RadPane]").click(function () {
            //    paneScrollWH();

            //});
            //$(".rspResizeBar,.rspResizeBarHorizontal,[id*=RadPane]").hover(function () {
            //    paneScrollWH();
            //}, function () {

            //    paneScrollWH();
            //});
        });

    </script>
    <script src="../Script/context.js"></script>
    <%--  <script src="../Script/ContextBS.js"></script>--%>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="../Script/d3.min.js"></script>

    <script src="../Script/EntityRelationshipListBFtoBF.js"></script>

    <script src="../Script/ImpactDigramBFtoBF.js"></script>

    <script src="../Script/EntityRelationshipForAllBS.js"></script>

    <script src="../Script/EntityRelationshipList.js"></script>

     <script src="../Script/EntityRelationshipListBStoBS.js"></script>

    <script src="../Script/ImpactDiagramBStoBS.js"></script>

</asp:Content>

