﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Group", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class Group : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public int ApplicationId { get; set; }

        [DataMember]
        public int Type { get; set; }

        [DataMember]
        public int RecoveryType { get; set; }

        [DataMember]
        public int PRServerId { get; set; }

        [DataMember]
        public int DRServerId { get; set; }

        [DataMember]
        public int PRDatabaseId { get; set; }

        [DataMember]
        public int DRDatabaseId { get; set; }

        [DataMember]
        public int PRReplicationId { get; set; }

        [DataMember]
        public int DRReplicationId { get; set; }

        [DataMember]
        public int RecoveryTime { get; set; }

        [DataMember]
        public int ConfigureDataLag { get; set; }

        [DataMember]
        public int MTPOD { get; set; }

        [DataMember]
        public int Priority { get; set; }

        [DataMember]
        public string State { get; set; }

        [DataMember]
        public int ReplicationStatus { get; set; }

        [DataMember]
        public bool EnableDataSync { get; set; }

        [DataMember]
        public int DROperationStatus { get; set; }

        [DataMember]
        public int DROperationId { get; set; }

        [DataMember]
        public int Health { get; set; }

        [DataMember]
        public int SiteSolutionTypeId { get; set; }

        [DataMember]
        public int NearGroupId { get; set; }

        [DataMember]
        public int ApplicationGroupId { get; set; }

        //[DataMember]
        //public int NearDRDatabaseId
        //{
        //    get;
        //    set;
        //}

        //[DataMember]
        //public int NearDRReplicationId
        //{
        //    get;
        //    set;
        //}

        #endregion Properties
    }
}