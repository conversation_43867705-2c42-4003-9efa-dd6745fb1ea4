﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class CGMonitoringDataAccess : BaseDataAccess, ICGMonitoringDataAccess
    {

        #region Constructors

        public CGMonitoringDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<CGMonitor> CreateEntityBuilder<CGMonitor>()
        {
            return (new CGMonitoringBuilder()) as IEntityBuilder<CGMonitor>;
        }

        #endregion Constructors


        #region Methods

        CGMonitor ICGMonitoringDataAccess.GetByCGId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "CG_Moni_Status_GETByCGId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCGId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<CGMonitor>()).BuildEntity(reader, new CGMonitor());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICGMonitoringDataAccess.GetByCGId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods


    }
}
