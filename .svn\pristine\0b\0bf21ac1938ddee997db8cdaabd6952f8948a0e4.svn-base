﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "AlertManager", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class AlertsManager : BaseEntity
    {
        #region Properties

        [DataMember]
        public string InfraObjectName { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public string AlertType { get; set; }

        [DataMember]
        public string SNMPStatus { get; set; }

        [DataMember]
        public string AlertName { get; set; }

        [DataMember]
        public string AlertDesc { get; set; }

        [DataMember]
        public bool IsActiveSNMP { get; set; }

        [DataMember]
        public string CurrentUserId { get; set; }

        [DataMember]
        public bool Incident { get; set; }

        [DataMember]
        public bool IncidentManage { get; set; }

        public string IncidentName { get; set; }

        [DataMember]
        public string Description { get; set; }

        [DataMember]
        public int WorkFlowId { get; set; }

        [DataMember]
        public int AlertId { get; set; }

        #endregion Properties
    }
}