﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.DataAccess.DatabaseMirrorBuilder;


namespace CP.DataAccess
{
    internal sealed class DatabaseMirrorMonitorDataAccess : BaseDataAccess, IDatabaseMirrorMonitorDataAccess
    {
        #region Constructors

        public DatabaseMirrorMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DatabaseMirrorMonitor> CreateEntityBuilder<DatabaseMirrorMonitor>()
        {
            return (new DatabaseMirrorMonitorBuilder()) as IEntityBuilder<DatabaseMirrorMonitor>;
        }

        #endregion Constructors

        #region Methods

        DatabaseMirrorMonitor IDatabaseMirrorMonitorDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                string sp = DbRoleName + "Databasemirrormonitr_GetInfrId";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iInfraObjectId", DbType.Int32, infraObjectId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<DatabaseMirrorMonitor>()).BuildEntity(reader, new DatabaseMirrorMonitor());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata, "Error In DAL While Executing Function Signature IGlobalMirrorReplicationDataAccess.GetByInfraObjectId(" + infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        DatabaseMirrorMonitor IDatabaseMirrorMonitorDataAccess.Add(DatabaseMirrorMonitor databasemirror)
        {
            try
            {
                const string sp = "DatabaseMirrormontrLogs_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iPRDatabaseName", DbType.AnsiString, databasemirror.PRDatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iDRDatabaseName", DbType.AnsiString, databasemirror.DRDatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iPRRoleofDB", DbType.AnsiString, databasemirror.PRRoleofDB);
                    Database.AddInParameter(cmd, Dbstring + "iDRRoleofDB", DbType.AnsiString, databasemirror.DRRoleofDB);
                    Database.AddInParameter(cmd, Dbstring + "iPRMirroringState", DbType.AnsiString, databasemirror.PRMirroringState);
                    Database.AddInParameter(cmd, Dbstring + "iDRMirroringState", DbType.AnsiString, databasemirror.DRMirroringState);
                    Database.AddInParameter(cmd, Dbstring + "iPRLogGenerateRate", DbType.AnsiString, databasemirror.PRLogGenerateRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRLogGenerateRate", DbType.AnsiString, databasemirror.DRLogGenerateRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRUnsentLog", DbType.AnsiString, databasemirror.PRUnsentLog);
                    Database.AddInParameter(cmd, Dbstring + "iDRUnsentLog", DbType.AnsiString, databasemirror.DRUnsentLog);
                    Database.AddInParameter(cmd, Dbstring + "iPRSentRate", DbType.AnsiString, databasemirror.PRSentRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRSentRate", DbType.AnsiString, databasemirror.DRSentRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRUnrestoredLog", DbType.AnsiString, databasemirror.PRUnrestoredLog);
                    Database.AddInParameter(cmd, Dbstring + "iDRUnrestoredLog", DbType.AnsiString, databasemirror.DRUnrestoredLog);
                    Database.AddInParameter(cmd, Dbstring + "iPRRecoveryRate", DbType.AnsiString, databasemirror.PRRecoveryRate);
                    Database.AddInParameter(cmd, Dbstring + "iDRRecoveryRate", DbType.AnsiString, databasemirror.DRRecoveryRate);
                    Database.AddInParameter(cmd, Dbstring + "iPRTransactionDelay", DbType.AnsiString, databasemirror.PRTransactionDelay);
                    Database.AddInParameter(cmd, Dbstring + "iDRTransactionDelay", DbType.AnsiString, databasemirror.DRTransactionDelay);
                    Database.AddInParameter(cmd, Dbstring + "iPRTransactionPerSecond", DbType.AnsiString, databasemirror.PRTransactionPerSecond);
                    Database.AddInParameter(cmd, Dbstring + "iDRTransactionPerSecond", DbType.AnsiString, databasemirror.DRTransactionPerSecond);
                    Database.AddInParameter(cmd, Dbstring + "iPRAverageDelay", DbType.AnsiString, databasemirror.PRAverageDelay);
                    Database.AddInParameter(cmd, Dbstring + "iDRAverageDelay", DbType.AnsiString, databasemirror.DRAverageDelay);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, databasemirror.InfraObjectId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_datagaurd_monitor"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databasemirror = reader.Read()
                            ? CreateEntityBuilder<DatabaseMirrorMonitor>().BuildEntity(reader, databasemirror)
                            : null;
                    }

                    if (databasemirror == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "databasemirror already exists. Please specify anotherdatabasemirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this databasemirror.");
                                }
                        }
                    }

                    return databasemirror;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting DataGuardMonitor Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }


        #endregion Methods

    }
}
