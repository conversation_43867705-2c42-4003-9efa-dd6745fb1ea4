﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class Sql2000LogDataAccess : BaseDataAccess, ISql2000LogDataAccess
    {
        #region Constructors

        public Sql2000LogDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SqlServer2000Log> CreateEntityBuilder<SqlServer2000Log>()
        {
            return (new Sql2000LogBuilder()) as IEntityBuilder<SqlServer2000Log>;
        }

        #endregion Constructors

        #region Methods

        SqlServer2000Log ISql2000LogDataAccess.GetByInfraObjectId(int id)
        {
            try
            {
                const string sp = "Sql2000Log_GetbyInfraObjectId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<SqlServer2000Log>()).BuildEntity(reader, new SqlServer2000Log())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISql2000LogDataAccess.GetByInfraObjectId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<SqlServer2000Log> ISql2000LogDataAccess.GetByDate(int infraObjectid, string startdate, string enddate)
        {
            try
            {
                const string sp = "Sql2000Log_GetbyDate";

                //var fmdt = Convert.ToDateTime(startdate).ToString("dd-MMM-yy");
                //var todt = Convert.ToDateTime(enddate).ToString("dd-MMM-yy");

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectid);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.String, startdate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.String, enddate);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SqlServer2000Log>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISql2000LogDataAccess.GetByDate(" + infraObjectid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion Methods
    }
}