﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI.WebControls;

namespace CP.UI.Code.Base
{
    public abstract class RSyncOptionsBasePageEditor : RSyncOptionsBasePage, IEditor<RSyncOptions>
    {
        private string _message = string.Empty;

        #region Properties

        public RSyncOptions CurrentEntity
        {
            get
            {
                return CurrentRSyncOptions;
            }
            set
            {
                CurrentRSyncOptions = value;
            }
        }

        public int CurrentEntityId
        {
            get { return CurrentRSyncOptionsId; }
            set { CurrentRSyncOptionsId = 0; }
        }

        public abstract string MessageInitials
        {
            get;
        }

        protected new string Message
        {
            get
            {
                return _message;
            }
            set
            {
                _message = value;
            }
        }

        public abstract string ReturnUrl
        {
            get;
        }

        public virtual Label TotalResult
        {
            get
            {
                return null;
            }
        }

        #endregion Properties


        #region Method

        public RSyncOptions BuildEntity(RSyncOptions currentEntity)
        {
            return currentEntity;
        }

        public abstract void PrepareEditView();

        public abstract void SaveEditor();

        public virtual void PrepareValidator()
        {
        }

        public abstract void BuildEntities();

        public virtual void BindList()
        {
        }

        public virtual void Delete(int entityId)
        {
        }

        public virtual void ClearForm()
        {
            CurrentEntityId = 0;
        }

        public virtual void FinalizeCommand()
        {
        }

        #endregion Method
    }
}