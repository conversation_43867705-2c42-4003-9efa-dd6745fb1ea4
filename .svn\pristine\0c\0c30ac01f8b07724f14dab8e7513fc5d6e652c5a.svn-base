﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System.Web;

namespace CP.UI.Controls
{
    public partial class HP3PARStorageList : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;
        DropDownList _ddlReplicationType = new DropDownList();
        ReplicationType type;

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {

        }

   

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        private void Binddata()
        {
            //setListViewPage();

            type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            var result = GetlvHP3StorageByReplicationType(type.ToString());
            lvHP3PARStorage.DataSource = result;
            lvHP3PARStorage.DataBind();

        }

        private IList<HP3PARStorage> GetlvHP3StorageByReplicationType(string iType)
        {
            //var replicationlist = Facade.HP3PARStorage_GetAllByReplicationType(iType);
            var replicationlist = Facade.GetHP3ReplicationByUserIdCompanyIdRoleAndReplicationFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag, Convert.ToString(type));
            if (replicationlist != null)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.reptype == iType
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public IList<HP3PARStorage> GetHP3PARStorageList(string searchvalue)
        {
            var replicationlist = GetlvHP3StorageByReplicationType(Session["HP3PARStorage"].ToString());
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count > 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvHP3PARStorage.Items.Clear();
                lvHP3PARStorage.DataSource = GetHP3PARStorageList(txtsearchvalue.Text);
                lvHP3PARStorage.DataBind();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void lvHP3PARStorage_PreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void lvHP3PARStorage_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPagelvHP3PARStorage"] = dataPager1.StartRowIndex;
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvHP3PARStorage.Items[e.NewEditIndex].FindControl("Id")) as Label;

            var lblName = (lvHP3PARStorage.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "HP3PARStorage", UserActionType.UpdateReplicationComponent,
                                    "The HP3PARStorage Replication component '" + lblName.Text +
                                    "' Opened as Editing Mode", LoggedInUserId);
            // if (lbl1 != null && lblName != null && ValidateRequest("HP3PARStorage Edit", UserActionType.ReplicationList))
            if (lbl1 != null && lblName != null && ValidateRequest("HP3PARStorage Edit", UserActionType.ReplicationList))
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                Helper.Url.Redirect(secureUrl);
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                //    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void lvHP3PARStorage_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPagelvHP3PARStorage"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCountlvHP3PARStorage"] = dataPager1.TotalRowCount;
                var lblId = lvHP3PARStorage.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvHP3PARStorage.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
                var InfraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));
                if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("HP3PARStorage Delete", UserActionType.ReplicationList))
                {
                    if (InfraObjects != null && InfraObjects.Count > 0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The lvHP3PARStorage Replication component is in use.");

                    }
                    else
                    {
                        Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "lvHP3PARStorage", UserActionType.DeleteReplicationComponent,
                                              "The lvHP3PARStorage Replication component '" + lblName.Text +
                                              "' was deleted from the replication component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "HP3PAR Storage Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));

                    }
                }

            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);

            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);

                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "32");

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "32");
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvHP3PARStorage_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator || IsUserManager || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }
    }

}