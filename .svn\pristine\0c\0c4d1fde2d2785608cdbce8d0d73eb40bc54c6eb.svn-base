﻿using CP.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class ApprovalsMappingBuilder : IEntityBuilder<ApprovalMapping>
    {
        #region IEntityBuilder<ApprovalProcess> Members

        public IList<ApprovalMapping> BuildEntities(IDataReader reader)
        {
            var _ApprovalMapping = new List<ApprovalMapping>();

            while (reader.Read())
            {
                _ApprovalMapping.Add(((IEntityBuilder<ApprovalMapping>)this).BuildEntity(reader, new ApprovalMapping()));
            }

            return (_ApprovalMapping.Count > 0) ? _ApprovalMapping : null;
        }

        ApprovalMapping IEntityBuilder<ApprovalMapping>.BuildEntity(IDataReader reader, ApprovalMapping _ApprovalMapping)
        {
            _ApprovalMapping.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]);
            _ApprovalMapping.ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]);
            _ApprovalMapping.Approvals = Convert.IsDBNull(reader["Approvals"]) ? string.Empty : Convert.ToString(reader["Approvals"]);
            _ApprovalMapping.IsActive = Convert.IsDBNull(reader["ISACTIVE"]) ? 0 : Convert.ToInt32(reader["ISACTIVE"]);
            _ApprovalMapping.CreatorId = Convert.IsDBNull(reader["CREATORID"]) ? 0 : Convert.ToInt32(reader["CREATORID"]);
            _ApprovalMapping.CreateDate = Convert.IsDBNull(reader["CREATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CREATEDATE"].ToString());
            _ApprovalMapping.UpdatorId = Convert.IsDBNull(reader["UPDATORID"]) ? 0 : Convert.ToInt32(reader["UPDATORID"]);
            _ApprovalMapping.UpdateDate = Convert.IsDBNull(reader["UPDATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UPDATEDATE"].ToString());
            _ApprovalMapping.ApprovalRequire = Convert.IsDBNull(reader["ApprovalRequire"]) ? string.Empty : Convert.ToString(reader["ApprovalRequire"]);
            return _ApprovalMapping;
        }

        #endregion IEntityBuilder<ApprovalMapping> Members
    }
}
