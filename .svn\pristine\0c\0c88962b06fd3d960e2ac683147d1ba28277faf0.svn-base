﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.UI.Code.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Helper;
using CP.ExceptionHandler;

namespace CP.UI.Controls
{
    public partial class EmcMirrorViewConfiguration : ReplicationControl //System.Web.UI.UserControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }
        #region Variable

        TextBox _txtReplicationName = new TextBox();
        DropDownList _ddlReplicationType = new DropDownList();
        DropDownList _ddlSiteId = new DropDownList();
        TextBox _DataGroup = new TextBox();
        TextBox _MimixLibrary = new TextBox();
        DropDownList _ddlManager = new DropDownList();
        DropDownList _ddlHealth = new DropDownList();
        DropDownList _ddlAvail = new DropDownList();
        DropDownList _ddlAlert = new DropDownList();
        DropDownList _ddlDatalag = new DropDownList();
        private EmcMirrorView _emcmirrorview;

        #endregion

        #region Properties

        public EmcMirrorView CurrentEntity
        {
            get { return _emcmirrorview ?? (_emcmirrorview = new EmcMirrorView()); }
            set
            {
                _emcmirrorview = value;
            }
        }


        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "Emc MirrorView"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion
        public override void PrepareView()
        {
            // Session.Remove("SVCGlobalMirror");

            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            PopulateServer();


            PrepareEditView();
        }

        void PopulateServer()
        {
            try
            {
                IList<Server> lstServer = Facade.GetAllServers();
                if (lstServer.Count > 0)
                {
                    ddlServer.DataSource = lstServer.Where(x => x.Type.Equals("PRNaviseccliServer") || x.Type.Equals("DRNaviseccliServer")).ToList();
                    ddlServer.DataTextField = "Name";
                    ddlServer.DataValueField = "Id";
                    ddlServer.DataBind();
                    ddlServer.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "- Select Server Name -"));
                }
            }
            catch (Exception ex)
            {
                ddlServer.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "- Select Server Name -"));
            }
        }

        public void PrepareEditView()
        {

            if (CurrentEmcMirrorViewReplication != null)
            {
                CurrentEntity = CurrentEmcMirrorViewReplication;

                Session["EmcMirrorView"] = CurrentEntity;

                ddlServer.SelectedValue = CurrentEntity.ServerId.ToString();
                txtNavisperePath.Text = CurrentEntity.NavispherePath;
                txtCGName.Text = CurrentEntity.CGName;
                txtSPNameIP.Text = CurrentEntity.SPNameOrIP;
                txtSPUserName.Text = CurrentEntity.SPUserName;
                //txtSP_Password.Text=CurrentEntity.SPPassword;
               // txtSP_Password.Attributes["value"] = CurrentEntity.SPPassword;
                txtSP_Password.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentEntity.SPPassword), hdfStaticGuid.Value);
                btnSave.Text = "Update";
            }
        }

        public void BuildEntities()
        {
            if (Session["emcmirrorview"] != null)
            {
                CurrentEntity = (EmcMirrorView)Session["emcmirrorview"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.ServerId = Convert.ToInt32(ddlServer.SelectedValue);
            CurrentEntity.NavispherePath = txtNavisperePath.Text;
            CurrentEntity.CGName = txtCGName.Text;
            CurrentEntity.SPNameOrIP = txtSPNameIP.Text;
            CurrentEntity.SPUserName = txtSPUserName.Text;
            // CurrentEntity.SPPassword = txtSP_Password.Text;
            //CurrentEntity.SPPassword = Utility.IsMD5EncryptedString(txtSP_Password.Text) ? txtSP_Password.Text : CryptographyHelper.Md5Encrypt(txtSP_Password.Text);//txtASMPassword.Text == "" ? string.Empty : txtASMPassword.Text;
            if (txtSP_Password.Text != string.Empty)
            {
               //CurrentEntity.SPPassword = Utility.IsMD5EncryptedString(txtSP_Password.Text) ? txtSP_Password.Text : CryptographyHelper.Md5Encrypt(txtSP_Password.Text);//txtASMPassword.Text == "" ? string.Empty : txtASMPassword.Text;

                CurrentEntity.SPPassword = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtSP_Password.Text, hdfStaticGuid.Value));
            }
            else
            {
                CurrentEntity.SPPassword = txtSP_Password.Text;
            }

        }

        public void SaveEditor()
        {
            // if (CurrentEntity.IsNew)
            if (btnSave.Text == "Save")
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;

                CurrentEntity = Facade.AddEmcMirrorView(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Emc MirrorView", UserActionType.CreateReplicationComponent, "The Emc MirrorView Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);

            }
            else
            {
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateEmcMirrorView(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Mimix", UserActionType.UpdateReplicationComponent, "The Mimix Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {

            if (Page.IsValid && (Session["ReplicationConfig_token"] != null) && !CheckReplicationNameExist() && ValidateRequest("Emc MirrorView ", UserActionType.CreateReplicationComponent))
            {
                if (!ValidateInput())
                {
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");
                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl);
                }
                else
                {
                    Label lblPrName = Parent.FindControl("lblPrName") as Label;
                    if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
                    {
                        lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
                    }
                    var submitButton = (Button)sender;
                    string buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }

                    if (Page.IsValid)
                    {
                        string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                        if (returnUrl.IsNullOrEmpty())
                        {
                            returnUrl = ReturnUrl;
                        }

                        try
                        {
                            if (currentTransactionType != TransactionType.Undefined)
                            {
                                StartTransaction();
                                BuildEntities();
                                SaveEditor();
                                ReplicationName.Text = string.Empty;
                                EndTransaction();

                                string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                        currentTransactionType));
                                btnSave.Enabled = false;
                            }
                        }
                        catch (CpException ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            ExceptionManager.Manage(ex, Page);
                        }
                        catch (Exception ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                            {
                                ExceptionManager.Manage((CpException)ex.InnerException, Page);
                            }
                            else
                            {
                                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                                ExceptionManager.Manage(customEx, Page);
                            }
                        }
                        if (returnUrl.IsNotNullOrEmpty())
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                            //Helper.Url.Redirect(secureUrl);

                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                            Helper.Url.Redirect(secureUrl);
                        }
                    }

                }
            }

        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }

                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtSSHUser");
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }
    }
}