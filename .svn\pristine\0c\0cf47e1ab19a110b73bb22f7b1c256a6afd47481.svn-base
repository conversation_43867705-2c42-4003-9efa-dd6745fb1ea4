﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ParallelWorkflowActionResult", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class Humintervention : BaseEntity
    {
        //#region Properties

        //[DataMember]
        //public int InfraObjectId { get; set; }

        //[DataMember]
        //public string WorkflowActionName { get; set; }

        //[DataMember]
        //public DateTime StartTime { get; set; }

        //[DataMember]
        //public DateTime EndTime { get; set; }

        //[DataMember]
        //public WorkflowActionStatus Status { get; set; }

        //[DataMember]
        //public string Message { get; set; }

        //[DataMember]
        //public int ParallelDROperationId { get; set; }

        //[DataMember]
        //public int ParallelGroupWorkflowId { get; set; }

        //[DataMember]
        //public int ActionId { get; set; }

        //[DataMember]
        //public string InfraobjectName { get; set; }

        //[DataMember]
        //public string WorkflowName { get; set; }

        //[DataMember]
        //public string WorkflowExecutionTime { get; set; }

        //[DataMember]
        //public bool SkipStep
        //{
        //    get;
        //    set;
        //}

        //[DataMember]
        //public int ConditionActionId
        //{
        //    get;
        //    set;
        //}

        //[DataMember]
        //public string Direction
        //{
        //    get;
        //    set;
        //}

        //[DataMember]
        //public int ActionCount
        //{
        //    get;
        //    set;
        //}

        //#endregion Properties

        [DataMember]
        public string ActionName { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public int ActionCount { get; set; }

        [DataMember]
        public int Skip { get; set; }

        [DataMember]
        public int Abort { get; set; }

        [DataMember]
        public int Retry { get; set; }
    }
}
