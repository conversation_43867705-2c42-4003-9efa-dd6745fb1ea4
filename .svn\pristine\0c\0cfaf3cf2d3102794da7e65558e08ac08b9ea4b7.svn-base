﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EMCSRDFStarConfiguration.ascx.cs" Inherits="CP.UI.Controls.EMCSRDFStarConfiguration" %>


<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>


<asp:UpdatePanel ID="upnlGlobalMirror" runat="server" UpdateMode="Conditional">
    <ContentTemplate>
      <input type="hidden" id="hdfStaticGuid" runat="server" />
          <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-head">
                    <h4 class="heading">EMC Management Console</h4>
                </div>
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-replication">
                            Select Server <span class="inactive">*</span>
                        </label>
                        <div class="col-md-9">
                            <asp:DropDownList ID="ddlServer" runat="server" AutoPostBack="true" OnSelectedIndexChanged="DdlServerSelectedIndexChanged"
                                 CssClass="selectpicker col-replication-dropdown" data-style="btn-default">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvddlServer" runat="server" ControlToValidate="ddlServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select Server"></asp:RequiredFieldValidator>
                            <asp:Label ID="lblServer" runat="server" ForeColor="Red"></asp:Label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            Server Hostname
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDSCLIHostname" CssClass="form-control" Style="width: 49% !important;" Enabled="False" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            Server IP
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDSCLIServerIP" CssClass="form-control" Style="width: 49% !important;" Enabled="False" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            SSH User ID
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSSHUserID" Enabled="False" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            SSH Password
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSSHPassword" CssClass="form-control" Enabled="False" Style="width: 49% !important;" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-head">
                    <h4 class="heading">EMCSRDF - Star info</h4>
                </div>
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-replication">
                            SymCLI Path <span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSymCLIPath" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                          <asp:RequiredFieldValidator ID="rfvSymCLIPath" runat="server" ControlToValidate="txtSymCLIPath" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter SymCLI Path"></asp:RequiredFieldValidator>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            CG Name<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtCGName" CssClass="form-control" Style="width: 49% !important;" Enabled="True" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvCGName" runat="server" ControlToValidate="txtCGName" CssClass="error"
                                Display="Dynamic" ErrorMessage="Please Enter Group Name"></asp:RequiredFieldValidator>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-actions row">

            <div class="col-lg-7" style="margin-left: 37%;">
                <asp:Button ID="btnSave" runat="server" CssClass="btn btn-primary" Width="20%" OnClick="btnSave_Click"
                    Text="Save" CausesValidation="true" />
                <asp:Button ID="btnCancel" runat="server" CssClass="btn btn-default" Width="20%" OnClick="btnCancel_Click"
                    Text="Cancel" CausesValidation="false" />
            </div>
        </div>
    </ContentTemplate>
    
</asp:UpdatePanel>
