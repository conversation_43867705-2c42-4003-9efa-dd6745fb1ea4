﻿using CP.BusinessFacade;

namespace CP.UI.Code.Replication.ReplicationInfo
{
    public class GlobalMirrorReplicationInfo : IReplicationInfo
    {
        private readonly IFacade _facade = new Facade();

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectid)
        {
            var currentEntityType = currentEntity.GetType();

            var globalMirror = _facade.GetGlobalMirrorByReplicationId(replicationId);

            if (globalMirror != null)
            {
                var replication = _facade.GetCurrentGlobalMirrorMonitorByinfraId(infraObjectid);
                //var replication = _facade.GetCurrentGlobalMirrorMonitorByStorageImageId(globalMirror.PRStorageImageId);

                currentEntityType.GetProperty("PRStorageImageId").SetValue(currentEntity, globalMirror.PRStorageImageId, null);

                currentEntityType.GetProperty("DRStorageImageId").SetValue(currentEntity, globalMirror.DRStorageImageId, null);

                if (replication != null)
                {
                    currentEntityType.GetProperty("CGTime").SetValue(currentEntity, replication.CGTime, null);
                    currentEntityType.GetProperty("CopyState").SetValue(currentEntity, replication.CopyState, null);
                }
                else
                {
                    currentEntityType.GetProperty("CGTime").SetValue(currentEntity, "N/A", null);
                    currentEntityType.GetProperty("CopyState").SetValue(currentEntity, "N/A", null);
                }
            }
            else
            {
                currentEntityType.GetProperty("PRStorageImageId").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRStorageImageId").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("CGTime").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("CopyState").SetValue(currentEntity, "N/A", null);
            }
            return currentEntity;
        }

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectid,int mailBoxId, string mailboxname)
        {
            return currentEntity;
        }
    }
}