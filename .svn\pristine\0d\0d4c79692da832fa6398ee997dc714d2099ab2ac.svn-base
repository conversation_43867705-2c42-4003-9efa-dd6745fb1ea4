﻿using System;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Helper;
namespace CP.UI.Code.Replication.Component
{
    public class ExchangeDAGComponent : IComponentInfo
    {
        private readonly IFacade _facade = new Facade();

        private ComponentInfo _componentInfo;

        public ComponentInfo CurrentComponent
        {
            get { return _componentInfo ?? (_componentInfo = new ComponentInfo()); }
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId)
        {
            _componentInfo = null;

            GetServerInformation(prServerId, true);

            GetServerInformation(drServerId, false);

            GetDatabaseInformation(prDatabaseId, true);

            GetDatabaseInformation(drDatabaseId, false);

            //GetDataLagInformation(groupId, prDatabaseId);

            return CurrentComponent;
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId,int mailBoxId, string mailboxname)
        {
            _componentInfo = null;

            GetServerInformation(prServerId, true);

            GetServerInformation(drServerId, false);

            //GetDatabaseInformation(prDatabaseId, true);

            //GetDatabaseInformation(drDatabaseId, false);

            GetComponamtMonitorInformationInformation(infraObjectId, mailBoxId, mailboxname + @"\" + (CurrentComponent.PRServerIP.Contains("N/A") ? "N/A" : CryptographyHelper.Md5Decrypt(CurrentComponent.PRServerIP)));

            //GetDataLagInformation(groupId, prDatabaseId);

            return CurrentComponent;
        }

        private void GetDataLagInformation(int infraObjectId)
        {
            try
            {
                //var datalag = _facade.GetExchangeDAGHealthByGroupId(groupId);
                //if (datalag != null)
                //{
                //    CurrentComponent.PRLogSequence = datalag.LastgeneratedLogSequencePR;
                //    CurrentComponent.DRLogSequence = datalag.LastCopiedLogSequence;
                //}
                //else
                //{
                CurrentComponent.PRLogSequence = "Not Available";
                CurrentComponent.DRLogSequence = "Not Available";
                //}
            }
            catch (Exception)
            {
                CurrentComponent.PRLogSequence = "Not Available";
                CurrentComponent.DRLogSequence = "Not Available";
            }
        }

        //private void GetDataLagInformation(int groupId,int prDatabaseId)
        //{
        //    try
        //    {
        //        var datalag = _facade.GetExchangeDAGComMonitorByGroupIdandPrDatabaseId(groupId,prDatabaseId);
        //        if (datalag != null)
        //        {
        //            CurrentComponent.PRLastLogGenereated = datalag.PRLastGenLog;
        //            CurrentComponent.DRLastLogReplayed = datalag.DRLastLogReplayed;
        //            CurrentComponent.PRLastGenereatedLogTime = datalag.PRLastGenLogTime;
        //            CurrentComponent.DRLastReplayedLogTime = datalag.DRLastReplayedLogTime;
        //            CurrentComponent.CurrentDatalag = datalag.CurrentDatalag;
        //        }
        //        else
        //        {
        //            CurrentComponent.PRLastLogGenereated = "Not Available";
        //            CurrentComponent.DRLastLogReplayed = "Not Available";
        //            CurrentComponent.PRLastGenereatedLogTime = "Not Available";
        //            CurrentComponent.DRLastReplayedLogTime = "Not Available";
        //            CurrentComponent.CurrentDatalag = "Not Available";
        //        }
        //    }
        //    catch (Exception)
        //    {
        //        CurrentComponent.PRLastLogGenereated = "Not Available";
        //        CurrentComponent.DRLastLogReplayed = "Not Available";
        //        CurrentComponent.PRLastGenereatedLogTime = "Not Available";
        //        CurrentComponent.DRLastReplayedLogTime = "Not Available";
        //        CurrentComponent.CurrentDatalag = "Not Available";
        //    }
        //}

        private void GetDatabaseInformation(int databaseId, bool isPrimary)
        {
            try
            {
                var database = _facade.GetDatabaseExchangeDAGByDatabaseBaseId(databaseId);

                if (database != null)
                {
                    BindDatabaseComponents(database, isPrimary);
                }
                else
                {
                    BindNullDatabaseComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullDatabaseComponents(isPrimary);
            }
        }

        private void BindNullDatabaseComponents(bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = "N/A";
                CurrentComponent.PRDatabaseMode = "Down";

                CurrentComponent.DRDatabaseName = "N/A";
                CurrentComponent.DRDatabaseMode = "Down";
                CurrentComponent.PRMailBoxDbStatus = "N/A";
                CurrentComponent.DRMailBoxDbStatus = "N/A";

                CurrentComponent.PRLastLogGenereated = "N/A";
                CurrentComponent.DRLastLogReplayed = "N/A";

                CurrentComponent.PRLastGenereatedLogTime = "N/A";
                CurrentComponent.DRLastReplayedLogTime = "N/A";
            }
            else
            {
                CurrentComponent.DRDatabaseName = "N/A";
                CurrentComponent.DRDatabaseMode = "Down";
            }
        }

        //private void BindDatabaseComponents(DatabaseBase database, bool isPrimary)
        //{
        //    if (isPrimary)
        //    {
        //        CurrentComponent.PRStorageGroupName = database.Name;
        //        CurrentComponent.PRDatabaseMode = database.Mode.ToString();
        //    }
        //    else
        //    {
        //        CurrentComponent.DRStorageGroupName = database.Name;
        //        CurrentComponent.DRDatabaseMode = database.Mode.ToString();
        //    }
        //}

        private void GetComponamtMonitorInformationInformation(int InfraObjectID,int mailBoxId, string mailboxname)
        {
            try
            {
                //if(mailboxname.Contains("\\"))
                //mailboxname = mailboxname.Replace('\\','\n');
                var database = _facade.ExchangeDAGComponantMonitorByInfraObjectIDAndMailBoxName(InfraObjectID,mailBoxId, mailboxname);

                if (database != null)
                {
                    BindComponamtMonitorInformation(database, true);
                }
                else
                {
                    BindNullDatabaseComponents(true);
                }
            }
            catch (Exception)
            {
                BindNullDatabaseComponents(true);
            }
        }

        private void BindDatabaseComponents(DataBaseExchangeDAG database, bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = database.MailBoxDBName;
                CurrentComponent.PRDatabaseMode = "Mounted";
            }
            else
            {
                CurrentComponent.DRDatabaseName = database.MailBoxDBName;
                CurrentComponent.DRDatabaseMode = "Not Exist";
            }
        }

        private void BindComponamtMonitorInformation(ExchangeDAGComponantMonitor database, bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = database.PRMailboxDatabase;
                CurrentComponent.DRDatabaseName = database.DRMailboxDatabase;

                CurrentComponent.PRMailBoxDbStatus = database.PRMailboxDBStatus;
                CurrentComponent.DRMailBoxDbStatus = database.DRMailboxDBStatus;

                CurrentComponent.PRLastLogGenereated = database.PRLastGenLog;
                CurrentComponent.DRLastLogReplayed = database.DRLastLogReplayed;

                CurrentComponent.PRLastGenereatedLogTime = database.PRLastGenLogTime;
                CurrentComponent.DRLastReplayedLogTime = database.DRLastReplayedLogTime;

                CurrentComponent.PRDatabaseMode = "Mounted";
            }
        }

        private void BindNullServerComponents(bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = "N/A";
                CurrentComponent.PRServerIP = "N/A";
                CurrentComponent.PRServerOSType = "N/A";
                CurrentComponent.PRServerStatus = "Down";
            }
            else
            {
                CurrentComponent.DRServerName = "N/A";
                CurrentComponent.DRServerIP = "N/A";
                CurrentComponent.DRServerOSType = "N/A";
                CurrentComponent.DRServerStatus = "Down";
            }
        }

        private void BindServerComponents(Server server, bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = server.Name;
                CurrentComponent.PRServerIP = server.IPAddress;
                CurrentComponent.PRServerHostName = server.HostName;
                CurrentComponent.PRServerOSType = server.OSType;
                CurrentComponent.PRServerStatus = server.Status.ToString();
            }
            else
            {
                CurrentComponent.DRServerName = server.Name;
                CurrentComponent.DRServerIP = server.IPAddress;
                CurrentComponent.DRServerHostName = server.HostName;
                CurrentComponent.DRServerOSType = server.OSType;
                CurrentComponent.DRServerStatus = server.Status.ToString();
            }
        }

        private void GetServerInformation(int serverId, bool isPrimary)
        {
            try
            {
                var server = _facade.GetServerById(serverId);

                if (server != null)
                {
                    BindServerComponents(server, isPrimary);
                }
                else
                {
                    BindNullServerComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullServerComponents(isPrimary);
            }
        }
    }
}