﻿using System;
using System.Data;
using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class BusinessFunctionBIAActivityBuilder : IEntityBuilder<BusinessFunctionBIAActivity>
    {
        IList<BusinessFunctionBIAActivity> IEntityBuilder<BusinessFunctionBIAActivity>.BuildEntities(IDataReader reader)
        {
            var businessfunctionBiaActivity = new List<BusinessFunctionBIAActivity>();

            while (reader.Read())
            {
                businessfunctionBiaActivity.Add(((IEntityBuilder<BusinessFunctionBIAActivity>)this).BuildEntity(reader, new BusinessFunctionBIAActivity()));
            }

            return (businessfunctionBiaActivity.Count > 0) ? businessfunctionBiaActivity : null;
        }

        BusinessFunctionBIAActivity IEntityBuilder<BusinessFunctionBIAActivity>.BuildEntity(IDataReader reader, BusinessFunctionBIAActivity businessfunctionbiaActivity)
        {
            businessfunctionbiaActivity.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            businessfunctionbiaActivity.BusinessFunctionId = Convert.IsDBNull(reader["BusinessFunctionId"]) ? 0 : Convert.ToInt32(reader["BusinessFunctionId"]);
            businessfunctionbiaActivity.ActivityDescription = Convert.IsDBNull(reader["ActivityDescription"]) ? string.Empty : Convert.ToString(reader["ActivityDescription"]);
            businessfunctionbiaActivity.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            businessfunctionbiaActivity.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            businessfunctionbiaActivity.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            businessfunctionbiaActivity.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            businessfunctionbiaActivity.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());
            return businessfunctionbiaActivity;
        }
    }  
}
