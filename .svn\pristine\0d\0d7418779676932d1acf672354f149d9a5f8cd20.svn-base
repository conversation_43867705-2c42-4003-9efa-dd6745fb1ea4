﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using Gios.Pdf;
using log4net;
using SpreadsheetGear;
using CP.ExceptionHandler;
using CP.BusinessFacade;
using System.IO;

namespace CP.UI
{
    public partial class AlertDashBoard : BasePage
    {
        private bool _isNoRecord;

        public string str = "";
        private readonly ILog _logger = LogManager.GetLogger(typeof(AlertDashBoard));
        private int _maxAlertId;

        public IList<InfraObject> InfraList { get; set; }
        public IList<Alert> _alertType { get; set; }



        protected void CalendarExtender1_DayRender(object sender, DayRenderEventArgs e)
        {

            if (e.Day.Date < DateTime.Today)
            {

                e.Day.IsSelectable = false;
            }

        }
        protected void tm_Tick(object sender, EventArgs e)
        {
            PrepareView();
        }

        public override void PrepareView()
        {
            //if (IsUserCustom)
            //{
            //    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
            //    return;
            //}
            Utility.SelectMenu(Master, "Module5");

            txtToDate.Attributes.Add("readonly", "readonly");
            txtFromDate.Attributes.Add("readonly", "readonly");

            //2 
            //Session["Alerts"] = null;

            //if (Session["LastAlertId"] == null)
            //{
            //    Session["LastAlertId"] = LoggedInUser.LastAlertId;
            //}


            if (LoggedInUserRole == UserRole.Operator || LoggedInUserRole == UserRole.Manager || LoggedInUserRole == UserRole.Administrator)
            {
                int test = LoggedInUser.Id;

                if (test != null)
                {
                    PopulateGroupByRole(test);
                }
            }
            else
            {
                PopulateGroupName();
                PopulateTypeName();
            }

            InfraList = null;

            IList<Alert> alerts = GetAlertDetails();

            //3
            // Session["Alerts"] = alerts;

            if (alerts != null)
            {
                if (alerts.Count != 0)
                {
                    lvAlert.DataSource = Session["FilterAlerts"] = alerts;
                    lvAlert.DataBind();
                    //  _maxAlertId = alerts[0].Id;
                    // Facade.UpdateUserByAlertId(LoggedInUserId, _maxAlertId);
                    // Session["LastAlertId"] = _maxAlertId;
                    btnExportPdf.Enabled = true;
                    btnExportExcel.Enabled = true;
                }
                else
                {
                    btnExportPdf.Enabled = false;
                    btnExportExcel.Enabled = false;
                }
            }
            else
            {
                btnExportPdf.Enabled = false;
                btnExportExcel.Enabled = false;
            }

            //   var AlertId = Request.QueryString["AlertId"].ToString();

            string raw = Request.RawUrl;
            int index = raw.IndexOf("AlertId=");
            string AlertId = string.Empty;
            if (index > -1)
                AlertId = raw.Substring(index).Replace("AlertId=", string.Empty);


            if (!string.IsNullOrEmpty(AlertId))
            {
                AlertId = CP.Helper.CryptographyHelper.Md5Decrypt(AlertId);
                if (!string.IsNullOrEmpty(AlertId) && AlertId != "0")
                {
                    Alert _alerts = Facade.GetAlertById(Convert.ToInt32(AlertId));
                    var infra = Facade.GetInfraObjectById(_alerts.InfraObjectId);
                    _alerts.InfraObjectName = infra.Name;
                    List<Alert> list = new List<Alert>(new Alert[] { _alerts });
                    lvAlert.DataSource = list;
                    lvAlert.DataBind();

                    btnExportPdf.Enabled = true;
                    btnExportExcel.Enabled = true;
                }
            }
            btnExportPdf.Enabled = false;
            btnExportExcel.Enabled = false;
        }

        private void PopulateGroupByRole(int id)
        {
            InfraList = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(id, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

            if (InfraList != null)
            {
                ddlGroup.DataSource = InfraList;
                ddlGroup.DataTextField = "Name";
                ddlGroup.DataValueField = "Id";
                ddlGroup.DataBind();
                ddlGroup.Items.Insert(0, "All");
                ddlGroup.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
            }
            else
            {
                ddlGroup.Items.Insert(0, new ListItem("No InfraObject Assigned", "0"));
            }
        }

        private void PopulateGroupName()
        {
            InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);

            if (InfraList != null)
            {
                ddlGroup.DataSource = InfraList;
                ddlGroup.DataTextField = "Name";
                ddlGroup.DataValueField = "Id";
                ddlGroup.DataBind();
                ddlGroup.Items.Insert(0, "All");
                ddlGroup.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
            }
            else
            {
                ddlGroup.Items.Insert(0, new ListItem("No InfraObject Assigned", "0"));
            }
        }

        private void PopulateTypeName()
        {
            _alertType = Facade.GetAlertByType();

            //var result = (from item in _alertType
            //              select new
            //              {
            //                  Type = item.Type,

            //              })
            //  .ToList()
            //  .Distinct();

            if (InfraList != null)
            {
                ddlAlertType.DataSource = _alertType;
                ddlAlertType.DataTextField = "Type";
                // ddlAlertType.DataValueField = "Id";
                ddlAlertType.DataBind();
                //   ddlAlertType.Items.Insert(0, "All");
                ddlAlertType.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectType, "0"));

            }
            else
            {
                ddlAlertType.Items.Insert(0, new ListItem("No InfraObject Assigned", "0"));
            }
        }

        protected void BtnDateClick(object sender, EventArgs e)
        {

            Session["FilterAlerts"] = GetAlertDetails();
            lvAlert.DataSource = Session["FilterAlerts"];
            lvAlert.DataBind();

            var alerts = Session["FilterAlerts"];
            if (alerts != null)
            {

                btnExportPdf.Enabled = true;
                btnExportExcel.Enabled = true;
            }
            else
            {

                btnExportPdf.Enabled = false;
                btnExportExcel.Enabled = false;
            }

            #region Old Code

            //if (DataPager1 != null)
            //{
            //    DataPager1.SetPageProperties(0, DataPager1.MaximumRows, false);
            //}
            //ulmessage.Visible = false;

            //string dat = DateTime.Now.ToString("yyyy-MM-dd");
            ////4
            ////Session["LastAlertId"] = null;
            ////Session["Alerts"] = null;

            //lblddlGroupValidation.Visible = false;
            //if (ddlGroup.SelectedIndex == 1)
            //{
            //    if (txtToDate.Text.Length == 0 && txtFromDate.Text.Length == 0 && (txtUserMsg.Text == null || txtUserMsg.Text == ""))
            //    {

            //        var alerts = Facade.GetAllAlert24Hrs();
            //        var nAlert = new List<Alert>();

            //        InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);

            //        if (alerts != null)
            //        {
            //            foreach (Alert alert in alerts)
            //            {
            //                if (!alert.JobName.Equals("Domain Authentication"))
            //                {
            //                    if (InfraList != null && InfraList.Count > 0)
            //                    {
            //                        foreach (InfraObject @infra in InfraList.Where(@infra => alert.InfraObjectId == @infra.Id))
            //                        {
            //                            alert.InfraObjectName = @infra.Name;
            //                            nAlert.Add(alert);
            //                            break;
            //                        }
            //                    }
            //                }
            //                else if (alert.JobName.Equals("Domain Authentication"))
            //                {
            //                    alert.InfraObjectName = alert.UserDefinedMessage;
            //                    nAlert.Add(alert);
            //                }
            //            }
            //        }

            //        if (alerts.Count != 0)
            //        {
            //            lvAlert.DataSource = nAlert;
            //            lvAlert.DataBind();
            //            btnExportPdf.Enabled = true;
            //            btnExportExcel.Enabled = true;
            //        }
            //        else
            //        {
            //            lvAlert.DataSource = null;
            //            lvAlert.DataBind();
            //            var lblErr = (Label)lvAlert.FindControl("lblError");
            //            if (lblErr != null)
            //            {
            //                dataPager2.Visible = false;
            //            }
            //            btnExportPdf.Enabled = false;
            //            btnExportExcel.Enabled = false;
            //        }
            //    }
            //    else if (ddlGroup.SelectedValue == "All" && txtFromDate.Text.Length > 0 && txtToDate.Text.Length > 0 && (txtUserMsg.Text == null || txtUserMsg.Text == ""))
            //    {
            //        IList<Alert> alerts = Facade.GetAllAlertsByDate(txtToDate.Text, txtFromDate.Text);
            //        Session["BoundData"] = alerts;
            //        if (alerts != null)

            //            if (alerts.Count != 0)
            //            {
            //                lvAlert.DataSource = alerts;
            //                lvAlert.DataBind();
            //                btnExportPdf.Enabled = true;
            //                btnExportExcel.Enabled = true;
            //            }
            //            else
            //            {
            //                lvAlert.DataSource = null;
            //                lvAlert.DataBind();
            //                var lblErr = (Label)lvAlert.FindControl("lblError");
            //                if (lblErr != null)
            //                {
            //                    dataPager2.Visible = false;
            //                }
            //                btnExportPdf.Enabled = false;
            //                btnExportExcel.Enabled = false;
            //            }
            //    }
            //    else if ((txtToDate.Text.Length == 0 && txtFromDate.Text.Length > 0))
            //    {
            //        ulmessage.Visible = true;
            //        lblDatemsg.Text = "Select Start Date";
            //        btnExportPdf.Visible = false;
            //        btnExportExcel.Visible = false;
            //        return;
            //    }
            //    else if ((txtToDate.Text.Length > 0 && txtFromDate.Text.Length == 0))
            //    {
            //        ulmessage.Visible = true;
            //        lblDatemsg.Text = "Select End Date";
            //        btnExportPdf.Visible = false;
            //        btnExportExcel.Visible = false;
            //        return;
            //    }
            //    else
            //    {
            //        var nAlert = Facade.GetAlertsByusermsgnall(txtUserMsg.Text, txtToDate.Text, txtFromDate.Text);


            //        if (nAlert.Count != 0)
            //        {
            //            lvAlert.DataSource = nAlert;
            //            lvAlert.DataBind();
            //            btnExportPdf.Enabled = true;
            //            btnExportExcel.Enabled = true;
            //        }
            //        else
            //        {
            //            lvAlert.DataSource = null;
            //            lvAlert.DataBind();
            //            var lblErr = (Label)lvAlert.FindControl("lblError");
            //            if (lblErr != null)
            //            {
            //                dataPager2.Visible = false;
            //            }
            //            btnExportPdf.Enabled = false;
            //            btnExportExcel.Enabled = false;
            //        }
            //    }
            //    if (ddlGroup.SelectedValue == "All" && txtFromDate.Text.Length > 0 && txtToDate.Text.Length > 0 && (txtUserMsg.Text == null || txtUserMsg.Text == ""))
            //    {
            //        IList<Alert> alerts = Facade.GetAllAlertsByDate(txtToDate.Text, txtFromDate.Text);
            //        Session["BoundData"] = alerts;
            //        if (alerts != null)

            //            if (alerts.Count != 0)
            //            {
            //                lvAlert.DataSource = alerts;
            //                lvAlert.DataBind();
            //                btnExportPdf.Enabled = true;
            //                btnExportExcel.Enabled = true;
            //            }
            //            else
            //            {
            //                lvAlert.DataSource = null;
            //                lvAlert.DataBind();
            //                var lblErr = (Label)lvAlert.FindControl("lblError");
            //                if (lblErr != null)
            //                {
            //                    dataPager2.Visible = false;
            //                }
            //                btnExportPdf.Enabled = false;
            //                btnExportExcel.Enabled = false;
            //            }

            //    }
            //    else if (ddlGroup.SelectedValue == "All" && txtFromDate.Text.Length > 0 && txtToDate.Text.Length > 0 && (txtUserMsg.Text != null || txtUserMsg.Text != ""))
            //    {
            //        IList<Alert> alerts = Facade.GetAlertsByusermsgnall(txtUserMsg.Text, txtToDate.Text, txtFromDate.Text);
            //        Session["BoundData"] = alerts;
            //        if (alerts != null)

            //            if (alerts.Count != 0)
            //            {
            //                lvAlert.DataSource = alerts;
            //                lvAlert.DataBind();
            //                btnExportPdf.Enabled = true;
            //                btnExportExcel.Enabled = true;

            //            }
            //            else
            //            {
            //                lvAlert.DataSource = null;
            //                lvAlert.DataBind();
            //                var lblErr = (Label)lvAlert.FindControl("lblError");
            //                if (lblErr != null)
            //                {
            //                    dataPager2.Visible = false;
            //                }
            //                btnExportPdf.Enabled = false;
            //                btnExportExcel.Enabled = false;
            //            }
            //    }
            //    else if ((txtToDate.Text.Length == 0 && txtFromDate.Text.Length > 0))
            //    {
            //        ulmessage.Visible = true;
            //        lblDatemsg.Text = "Select Start Date";
            //        btnExportPdf.Visible = false;
            //        btnExportExcel.Visible = false;
            //        return;
            //    }
            //    else if ((txtToDate.Text.Length > 0 && txtFromDate.Text.Length == 0))
            //    {
            //        ulmessage.Visible = true;
            //        lblDatemsg.Text = "Select End Date";
            //        btnExportPdf.Visible = false;
            //        btnExportExcel.Visible = false;
            //        return;
            //    }
            //}
            //else
            //{
            //    if (txtToDate.Text.Length == 0 && txtFromDate.Text.Length == 0 && ddlGroup.SelectedValue == "0")
            //    {

            //        var alerts = Facade.GetAllAlert24Hrs();
            //        var nAlert = new List<Alert>();

            //        if (LoggedInUserRole == UserRole.Operator || LoggedInUserRole == UserRole.Manager)
            //        {
            //            int id = LoggedInUser.Id;
            //            InfraList = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(id, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

            //        }
            //        else
            //        {
            //            InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);
            //        }

            //        if (alerts != null)
            //        {
            //            foreach (Alert alert in alerts)
            //            {
            //                if (!alert.JobName.Equals("Domain Authentication"))
            //                {
            //                    if (InfraList != null && InfraList.Count > 0)
            //                    {
            //                        foreach (InfraObject @infra in InfraList.Where(@infra => alert.InfraObjectId == @infra.Id))
            //                        {
            //                            alert.InfraObjectName = @infra.Name;
            //                            nAlert.Add(alert);
            //                            break;
            //                        }
            //                    }
            //                }
            //                else if (alert.JobName.Equals("Domain Authentication"))
            //                {
            //                    alert.InfraObjectName = alert.UserDefinedMessage;
            //                    nAlert.Add(alert);
            //                }
            //            }
            //        }

            //        if (alerts.Count != 0)
            //        {
            //            lvAlert.DataSource = nAlert;
            //            lvAlert.DataBind();
            //            btnExportPdf.Enabled = true;
            //            btnExportExcel.Enabled = true;
            //        }
            //        else
            //        {
            //            lvAlert.DataSource = null;
            //            lvAlert.DataBind();
            //            var lblErr = (Label)lvAlert.FindControl("lblError");
            //            if (lblErr != null)
            //            {
            //                dataPager2.Visible = false;
            //            }
            //            btnExportPdf.Enabled = false;
            //            btnExportExcel.Enabled = false;
            //        }
            //    }

            //    else if (txtToDate.Text.Length == 0 && txtFromDate.Text.Length == 0)
            //    {
            //        IList<Alert> alerts = GetAlertDetails();

            //        if (alerts.Count != 0)
            //        {
            //            lvAlert.DataSource = alerts;
            //            lvAlert.DataBind();
            //            btnExportPdf.Enabled = true;
            //            btnExportExcel.Enabled = true;
            //        }
            //        else
            //        {
            //            lvAlert.DataSource = null;
            //            lvAlert.DataBind();
            //            var lblErr = (Label)lvAlert.FindControl("lblError");
            //            if (lblErr != null)
            //            {
            //                dataPager2.Visible = false;
            //            }
            //            btnExportPdf.Enabled = false;
            //            btnExportExcel.Enabled = false;
            //        }
            //    }
            //    else if (txtToDate.Text.Length > 0 && txtFromDate.Text.Length > 0)
            //    {
            //        if (Convert.ToDateTime(dat) < Convert.ToDateTime(txtToDate.Text))
            //        {
            //            ulmessage.Visible = true;
            //            lblDatemsg.Text = "Start Date" + " " + '(' + txtToDate.Text + ')' + " can not greater than Today Date";
            //            btnExportPdf.Visible = false;
            //            btnExportExcel.Visible = false;
            //            return;
            //        }

            //        if (Convert.ToDateTime(txtToDate.Text) > Convert.ToDateTime(txtFromDate.Text))
            //        {
            //            ulmessage.Visible = true;
            //            lblDatemsg.Text = "Start date" + " " + '(' + txtToDate.Text + ')' + " can not be greater than End date" + '(' + txtFromDate.Text + ')';
            //            btnExportPdf.Visible = false;
            //            btnExportExcel.Visible = false;
            //            return;
            //        }
            //        if (Convert.ToDateTime(dat) < Convert.ToDateTime(txtFromDate.Text))
            //        {
            //            ulmessage.Visible = true;
            //            lblDatemsg.Text = "End Date" + " " + '(' + txtFromDate.Text + ')' + " can not Greater than Today Date";
            //            btnExportPdf.Visible = false;
            //            btnExportExcel.Visible = false;
            //        }
            //    }
            //    else if ((txtToDate.Text.Length == 0 && txtFromDate.Text.Length > 0))
            //    {
            //        ulmessage.Visible = true;
            //        lblDatemsg.Text = "Select Start Date";
            //        btnExportPdf.Visible = false;
            //        btnExportExcel.Visible = false;
            //    }
            //    else if ((txtToDate.Text.Length > 0 && txtFromDate.Text.Length == 0))
            //    {
            //        ulmessage.Visible = true;
            //        lblDatemsg.Text = "Select End Date";
            //        btnExportPdf.Visible = false;
            //        btnExportExcel.Visible = false;
            //    }
            //}


            #endregion old code

        }

        private IList<Alert> GetAlertDetails()
        {
            IList<Alert> alerts = new List<Alert>();
            IList<Alert> newAlert = new List<Alert>();
            newAlert.Clear();
            //5
            //if (Session["Alerts"] != null)
            //{
            //    return Session["Alerts"] as IList<Alert>;
            //}
            //else if (Session["LastAlertId"] != null)
            //{
            //    alerts = Facade.GetAlertsByLastAlertId(Convert.ToInt32(Session["LastAlertId"]));
            //}

            //else if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty)
            //6
            if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty)
            {
                alerts = Facade.GetAllAlert24Hrs();
                // alerts = Facade.GetAllAlert();
            }
            if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty && ddlGroup.SelectedValue == "All")
            {
                //  alerts = Facade.GetAllAlert24Hrs();
                alerts = Facade.GetAllAlert();
            }
            else if (ddlGroup.SelectedValue == "All" && txtFromDate.Text.Length > 0 && txtToDate.Text.Length > 0)
            {
                alerts = Facade.GetAllAlertsByDate(txtToDate.Text, txtFromDate.Text);
            }
            else if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty && ddlGroup.SelectedValue != "0" && ddlAlertType.SelectedValue != "0" && ddlSeverity.SelectedValue != "0")
            {
                alerts = Facade.GetAlertsBySeverity(ddlSeverity.SelectedItem.Text, Convert.ToInt32(ddlGroup.SelectedValue));
            }

            else if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty && ddlGroup.SelectedValue != "0" && ddlAlertType.SelectedValue != "0")
            {
                alerts = Facade.GetAlertsByInfraType(ddlAlertType.SelectedItem.Text, Convert.ToInt32(ddlGroup.SelectedValue));
            }
            else if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty && ddlGroup.SelectedValue != "0")
            {
                alerts = Facade.GetAlertsByInfraObjectId(Convert.ToInt32(ddlGroup.SelectedValue));
            }
            else if (txtToDate.Text != string.Empty && txtFromDate.Text != string.Empty && ddlGroup.SelectedValue != "0")
            {
                alerts = Facade.GetAlertsByDate(Convert.ToInt32(ddlGroup.SelectedValue), txtToDate.Text, txtFromDate.Text);
            }
            else if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty && ddlGroup.SelectedValue != "0")
            {
                alerts = Facade.GetAlertsByInfraObjectId(Convert.ToInt32(ddlGroup.SelectedValue));
            }
            else if (txtToDate.Text != string.Empty && txtFromDate.Text != string.Empty && ddlGroup.SelectedValue != "0")
            {
                alerts = Facade.GetAlertsByDate(Convert.ToInt32(ddlGroup.SelectedValue), txtToDate.Text, txtFromDate.Text);
            }
            else if (txtToDate.Text != string.Empty && txtFromDate.Text != string.Empty)
            {
                alerts = Facade.GetAllAlertsByDate(txtToDate.Text, txtFromDate.Text);
            }


            if (LoggedInUserRole == UserRole.Operator || LoggedInUserRole == UserRole.Manager || LoggedInUserRole == UserRole.Administrator)
            {
                int id = LoggedInUser.Id;
                InfraList = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(id, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

            }
            else
            {
                InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);
            }

            if (alerts != null)
            {
                foreach (Alert alert in alerts)
                {
                    if (!alert.JobName.Equals("Domain Authentication"))
                    {
                        if (InfraList != null)
                        {
                            if (InfraList.Count > 0)
                            {

                                if (ddlGroup.SelectedIndex > 0 && ddlAlertType.SelectedIndex > 0 && ddlSeverity.SelectedIndex > 0)
                                {
                                    foreach (InfraObject @infraType in InfraList.Where(@infraType => alert.InfraObjectId == @infraType.Id && ddlAlertType.SelectedItem.Text == alert.Type && ddlSeverity.SelectedItem.Text == alert.Severity))
                                    {
                                        alert.InfraObjectName = @infraType.Name;
                                        newAlert.Add(alert);
                                        break;
                                    }
                                }

                                else if (ddlGroup.SelectedIndex > 0 && ddlAlertType.SelectedIndex > 0 && ddlSeverity.SelectedIndex <= 0)
                                {
                                    foreach (InfraObject @infraType in InfraList.Where(@infraType => alert.InfraObjectId == @infraType.Id && ddlAlertType.SelectedItem.Text == alert.Type))
                                    {
                                        alert.InfraObjectName = @infraType.Name;
                                        newAlert.Add(alert);
                                        break;
                                    }
                                }

                                else if (ddlGroup.SelectedIndex > 0 && ddlAlertType.SelectedIndex <= 0 && ddlSeverity.SelectedIndex > 0)
                                {
                                    foreach (InfraObject @infraType in InfraList.Where(@infraType => alert.InfraObjectId == @infraType.Id && ddlSeverity.SelectedItem.Text == alert.Severity))
                                    {
                                        alert.InfraObjectName = @infraType.Name;
                                        newAlert.Add(alert);
                                        break;
                                    }
                                }

                                else
                                {
                                    foreach (InfraObject @infra in InfraList.Where(@infra => alert.InfraObjectId == @infra.Id))
                                    {
                                        alert.InfraObjectName = @infra.Name;
                                        newAlert.Add(alert);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    else if (alert.JobName.Equals("Domain Authentication"))
                    {
                        alert.InfraObjectName = alert.UserDefinedMessage;
                        newAlert.Add(alert);
                    }
                }
            }
            return newAlert;
        }

        protected void ddlGroup_SelectedIndexChanged(object sender, EventArgs e)
        {
            Session["LastAlertId"] = null;
            if (DataPager1 != null)
            {
                DataPager1.SetPageProperties(0, DataPager1.PageSize, false);
            }
        }

        //protected void ddlType_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    string test = ddlType.SelectedItem.Text;
        //    if (test != "")
        //    {
        //    }
        //    Session["LastAlertId"] = null;
        //    if (DataPager1 != null)
        //    {
        //        DataPager1.SetPageProperties(0, DataPager1.PageSize, false);
        //    }
        //}

        //protected void ddlApplication_SelectedIndexChanged(object sender, EventArgs e)
        //{
        //    //Session["LastAlertId"] = null;
        //    //if (DataPager1 != null)
        //    //{
        //    //    DataPager1.SetPageProperties(0, DataPager1.PageSize, false);
        //    //}
        //    if (ddlApplication.SelectedValue == "All")
        //    {
        //        ddlGroup.Enabled = false;
        //    }
        //    else if (ddlApplication.SelectedValue == "DBGroup")
        //    {
        //        ddlGroup.Enabled = true;
        //       // Utility.PopulateGroup(ddlGroup, true, LoggedInUserId, LoggedInUser.GroupAllFlag);
        //        Utility.PopulateInfraObject(ddlGroup, true, LoggedInUserId, LoggedInUser.GroupAllFlag);
        //    }
        //    else if (ddlApplication.SelectedValue == "Application")
        //    {
        //        ddlGroup.Enabled = true;
        //        Utility.PopulateApplication(ddlGroup, true, LoggedInUserId, LoggedInUser.ApplicationAllFlag);
        //    }
        //    else
        //    {
        //        ddlGroup.Enabled = false;
        //    }
        //}

        protected void lvAlert_PreRender(object sender, EventArgs e)
        {
            //if (!IsPostBack)
            //{
            //    if(1==1)
            //    {
            //        lblmsg.Text = "Empty";
            //    }
            //}

            var alertData = GetAlertDetails();
            IList<Alert> ualertData = new List<Alert>();

            if (ulmessage.Visible == false)
            {
                lblmsg.Text = "";
                if (IsPostBack)
                {
                    if (!_isNoRecord)
                    {
                        if (!string.IsNullOrEmpty(txtUserMsg.Text))
                        {
                            if (ddlGroup.SelectedItem.Text == "All" && txtUserMsg.Text != "")
                            {
                                var aler = Facade.GetAlertsByusermsgnall(txtUserMsg.Text, txtToDate.Text, txtFromDate.Text);
                                var alertByJobName = (from alert in alertData
                                                      where alert.JobName.ToLower().Contains(txtUserMsg.Text.Trim().ToLower())
                                                      select alert).ToList();

                                var alertByType = (from alert in alertData
                                                   where alert.Type.ToLower().Contains(txtUserMsg.Text.Trim().ToLower())
                                                   select alert).ToList();
                                if (alertByJobName != null && alertByJobName.Count > 0)
                                {
                                    lvAlert.DataSource = alertByJobName;
                                    alertByJobName = null;
                                }
                                else if (alertByType != null && alertByType.Count > 0)
                                {
                                    lvAlert.DataSource = alertByType;
                                    alertByType = null;
                                }
                                else
                                {
                                    lvAlert.DataSource = aler;
                                }


                                lvAlert.DataBind();
                                dataPager2.Visible = false;
                            }
                            else
                            {
                                int infraid = Convert.ToInt32(ddlGroup.SelectedValue);
                                ualertData = Facade.GetAllAlertByUserMessage(txtUserMsg.Text, txtToDate.Text, txtFromDate.Text, infraid);

                                if (ualertData != null)
                                {
                                    foreach (Alert alert in ualertData)
                                    {
                                        InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);
                                        if (InfraList != null && InfraList.Count > 0)
                                        {
                                            if (!alert.JobName.Equals("Domain Authentication"))
                                            {
                                                foreach (InfraObject @infra in InfraList.Where(@infra => alert.InfraObjectId == @infra.Id))
                                                {
                                                    alert.InfraObjectName = @infra.Name;
                                                }
                                            }
                                            else if (alert.JobName.Equals("Domain Authentication"))
                                            {
                                                alert.InfraObjectName = alert.UserDefinedMessage;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (alertData != null)
                            if (alertData.Count > 0 && (txtUserMsg.Text == "" || txtUserMsg.Text == null))
                            {
                                lvAlert.DataSource = alertData;
                                lvAlert.DataBind();
                                btnExportPdf.Enabled = true;
                                btnExportExcel.Enabled = true;
                                btnExportPdf.Visible = true;
                                btnExportExcel.Visible = true;
                                dataPager2.Visible = true;
                                DataPager1.Visible = true;
                            }
                            else if (alertData.Count > 0 && (txtUserMsg.Text != "" || txtUserMsg.Text != null))
                            {
                                var aler = Facade.GetAlertsByusermsgnall(txtUserMsg.Text, txtToDate.Text, txtFromDate.Text);
                                var alertByJobName = (from alert in alertData
                                                      where alert.JobName.ToLower().Contains(txtUserMsg.Text.Trim().ToLower())
                                                      select alert).ToList();
                                var alertByType = (from alert in alertData
                                                   where alert.Type.ToLower().Contains(txtUserMsg.Text.Trim().ToLower())
                                                   select alert).ToList();

                                if (alertByJobName != null && alertByJobName.Count > 0)
                                {
                                    lvAlert.DataSource = alertByJobName;
                                    alertByJobName = null;
                                }
                                else if (alertByType != null && alertByType.Count > 0)
                                {
                                    lvAlert.DataSource = alertByType;
                                    alertByType = null;
                                }
                                else
                                {
                                    lvAlert.DataSource = aler;
                                }
                                lvAlert.DataBind();
                                btnExportPdf.Enabled = true;
                                btnExportExcel.Enabled = true;
                                btnExportExcel.Visible = true;
                                btnExportPdf.Visible = true;
                                dataPager2.Visible = true;
                                DataPager1.Visible = true;
                            }
                            else
                            {
                                //lvAlert.DataSource = null;
                                lvAlert.DataSource = null;
                                lvAlert.DataBind();
                                btnExportPdf.Enabled = false;
                                btnExportExcel.Enabled = false;
                                dataPager2.Visible = false;
                                DataPager1.Visible = false;
                            }

                        if (ualertData != null && ualertData.Count > 0)
                        {
                            lvAlert.DataSource = ualertData;
                            lvAlert.DataBind();
                            btnExportPdf.Enabled = true;
                            btnExportExcel.Enabled = true;
                            btnExportExcel.Visible = true;
                            btnExportPdf.Visible = true;
                            dataPager2.Visible = true;
                            DataPager1.Visible = true;
                        }
                    }
                    else
                    {
                        lvAlert.DataSource = null;
                        lvAlert.DataBind();
                        dataPager2.Visible = false;
                    }
                }
            }
            else
            {
                lvAlert.DataSource = null;
                lvAlert.DataBind();
                lblmsg.Text = "No Record Found";
                lblmsg.CssClass = "error";
                dataPager2.Visible = false;
                DataPager1.Visible = false;
            }
        }

        protected string GetSeverityTypeCss(object item)
        {
            string severity = string.Empty;
            switch (Convert.ToString(item))
            {
                case "High":
                    severity = "../Images/icons/high_16.png";
                    break;

                case "Low":
                    severity = "../Images/icons/low_16.png";
                    break;

                case "VeryHigh":
                    severity = "../Images/icons/veryhigh_16.png";
                    break;

                case "Normal":
                    severity = "../Images/icons/normal_16.png";
                    break;

                case "Information":
                    severity = "../Images/icons/low_16.png";
                    break;

                case "Informative":
                    severity = "../Images/icons/low_16.png";
                    break;

                case "Critical":
                    severity = "../Images/icons/veryhigh_16.png";
                    break;
            }
            return severity;
        }

        protected string GetRowStyle(object item)
        {
            string alert = string.Empty;
            switch (Convert.ToString(item))
            {
                case "High":
                    alert = "High";
                    break;

                case "Low":
                    alert = "Low";
                    break;

                case "VeryHigh":
                    alert = "VeryHigh";
                    break;

                case "Normal":
                    alert = "Normal";
                    break;

                case "Information":
                    alert = "Information";
                    break;

                case "Informative":
                    alert = "Informative";
                    break;

                case "Critical":
                    alert = "critical";
                    break;
            }
            return alert;
        }

        protected void btn_ExportToPdf_Click(object sender, EventArgs e)
        {
            ShowTable();
            if (ddlGroup.SelectedValue != "0")
            {
                _logger.DebugFormat("{0} - Generate {1} Alert summary report - {2}", HostAddress, ddlGroup.SelectedItem.Text, LoggedInUserName);
            }
        }

        protected void btn_ExportToExcel_Click(object sender, EventArgs e)
        {
            try
            {

                ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
            if (ddlGroup.SelectedValue != "0")
            {
                _logger.DebugFormat("{0} - Generate {1} Alert summary report - {2}", HostAddress, ddlGroup.SelectedItem.Text, LoggedInUserName);
            }
        }

        private void ExcelReport()
        {
            _logger.Info("======Generating Alert Logs Report EXCEL View ======");
            _logger.Info(Environment.NewLine);

            IList<Alert> data = GetAlertDetails();
            if (data != null)
            {
                lvAlert.DataSource = null;
                lvAlert.DataSource = data;
                lvAlert.DataBind();
            }
            else
            {
                lvAlert.DataSource = null;
                dataPager2.Visible = false;
                lblmsg.Text = "No Record Found";
                _isNoRecord = true;
            }


            if (data != null)
            {

                IWorkbookSet workbookSet = null;
                String ssFile = string.Empty;
                IWorkbook templateWorkbook = null;
                IWorksheet templateWorksheet = null;
                IRange _cells = null;

                workbookSet = Factory.GetWorkbookSet();
                ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
                templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                templateWorksheet = templateWorkbook.Worksheets[0];

                IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
                IWorksheet reportWorksheet = null;
                IWorksheet lastWorksheet = null;

                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

                reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
                reportWorkbook.Worksheets["Sheet1"].Delete();

                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
                reportWorksheet.Name = "Alert Logs";

                _cells["A1"].ColumnWidth = 7;

                _cells["E3"].Formula = "Alert Logs";
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].Font.Bold = true;
                _cells["B3:H6"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["D3"].Font.Bold = true;
                _cells["D3"].ColumnWidth = 30;
                _cells["D3"].HorizontalAlignment = HAlign.Center;
                _cells["D3"].VerticalAlignment = VAlign.Top;
                _cells["B3:H3"].Font.Size = 11;
                _cells["B5:H8"].Font.Size = 10;
                _cells["B3:H8"].Font.Color = Color.White;
                _cells["B3:H8"].Font.Name = "Cambria";


                IFacade _facade = new Facade();

                if (!string.IsNullOrEmpty(txtToDate.Text))
                {
                    string startdt = Utility.getFormatedDate_New(txtToDate.Text);
                }
                else
                {
                    string startdt = string.Empty;
                }


                if (!string.IsNullOrEmpty(txtFromDate.Text))
                {
                    string enddt = Utility.getFormatedDate_New(txtFromDate.Text);
                }
                else
                {
                    string enddt = string.Empty;
                }

                //string startdt = Utility.getFormatedDate_New(txtToDate.Text);//string startdt = Utility.getFormatedDate(txtstart.Text);
                //string enddt = Utility.getFormatedDate_New(txtFromDate.Text);//string enddt = Utility.getFormatedDate(txtend.Text);


                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 650, 10, 120, 13);
                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 380, 10, 121, 13);
                }

                reportWorksheet.Cells["A1:F1"].RowHeight = 27;

                _cells["B6"].Formula = "From Date";
                _cells["B6"].Font.Bold = true;
                _cells["B6"].HorizontalAlignment = HAlign.Left;

                //_cells["C6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtToDate.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtstart.Text;
                if (!string.IsNullOrEmpty(txtToDate.Text))
                {_cells["C6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtToDate.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtstart.Text;
                }
                else
                {
                    _cells["C6"].Formula = ":  " + "NA";
                }
                _cells["C6"].Font.Bold = true;
                _cells["C6"].HorizontalAlignment = HAlign.Left;

                // var dateTime = DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss");
                _cells["B5"].Formula = "Report Generated Time";
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;

                _cells["C5"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));// dateTime;
                _cells["C5"].Font.Bold = true;
                _cells["C5"].HorizontalAlignment = HAlign.Left;

                _cells["F5"].Formula = "InfraObject Name";
                _cells["F5"].Font.Bold = true;
                _cells["F5"].HorizontalAlignment = HAlign.Left;

                _cells["G5"].Formula = ":  " + ddlGroup.SelectedItem.Text;
                _cells["G5"].Font.Bold = true;
                _cells["G5"].HorizontalAlignment = HAlign.Left;

                _cells["F6"].Formula = "To Date";
                _cells["F6"].Font.Bold = true;
                _cells["F6"].HorizontalAlignment = HAlign.Left;

                //_cells["G6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtFromDate.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtend.Text;
                if (!string.IsNullOrEmpty(txtFromDate.Text))
                {
                     _cells["G6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtFromDate.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtend.Text;
                }
                else
                {
                    _cells["G6"].Formula = ":  " + "NA";
                }
                _cells["G6"].Font.Bold = true;
                _cells["G6"].HorizontalAlignment = HAlign.Left;

                int row = 8;
                int i = 1;

                _cells["B" + row.ToString()].Formula = "No.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:H8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:H8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.Black;
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Type";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;


                _cells["D" + row.ToString()].Formula = "Severity";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["E" + row.ToString()].Formula = "Description";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["F" + row.ToString()].Formula = "Job Name";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["G" + row.ToString()].Formula = "Job Name";
                _cells["G" + row.ToString()].Font.Bold = true;
                _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["H" + row.ToString()].Formula = "Create Date";
                _cells["H" + row.ToString()].Font.Bold = true;
                _cells["H" + row.ToString()].HorizontalAlignment = HAlign.Left;


                row++;
                int dataCount = 0;
                int xlRow = 9;

                //if (data != null)
                //{
                var countData = data.Count;
                if (countData <= 0)
                {
                    lblmsg.Visible = true;
                    lblmsg.Text = "No Records";
                    return;
                }


                foreach (Alert rp in data)
                {
                    dataCount++;
                    int column = 0;
                    string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H" };
                    xlRow++;

                    string ndx = xlColumn[column] + row.ToString();
                    _cells[ndx + ":" + "H" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;



                    _cells[ndx].Formula = i.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    i++;
                    column++;



                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.Type.Count() > 25 ? (rp.Type.Insert(25, " ")) : (rp.Type);
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.Severity.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    //_cells[ndx].Formula = rp.SystemMessage.ToString();
                    _cells[ndx].Formula = rp.UserDefinedMessage.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    //_cells[ndx].Formula = rp.SystemMessage.ToString();
                    _cells[ndx].Formula = rp.IPAddress != null && rp.IPAddress != "" ? rp.IPAddress : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.JobName.Count() > 25 ? (rp.JobName.Insert(25, " ")) : (rp.JobName);
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(rp.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].WrapText = true;
                    column++;

                    row++;
                }

                int finalCount = dataCount + 10;
                _cells["B" + finalCount].Formula = "NA : Not Available";
                _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
                _cells["B" + finalCount].Font.Name = "Cambria";
                _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

                reportWorksheet.ProtectContents = true;
                OpenExcelFile(reportWorkbook);

                _logger.Info("====== Alert Logs EXCEL Report generated ======");
                _logger.Info(Environment.NewLine);

            }
        }

        private void OpenExcelFile(IWorkbook workbook)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "AlertLogs" + str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + str;
            //var myUrl = "/ExcelFiles/" + str;
            _logger.Info("ReportPath" + myUrl);
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=User Activity Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }


        private void ShowTable()
        {
            IList<Alert> data = GetAlertDetails();
            if (data != null)
            {
                lvAlert.DataSource = null;
                lvAlert.DataSource = data;
                lvAlert.DataBind();
            }
            else
            {
                lvAlert.DataSource = null;
                dataPager2.Visible = false;
                lblmsg.Text = "No Record Found";
                _isNoRecord = true;
            }

            if (data != null)
            {
                var countData = data.Count;
                if (countData <= 0)
                {
                    lblmsg.Visible = true;
                    lblmsg.Text = "No Records";
                    return;
                }
                var d1T = new DataTable();
                d1T.Columns.Add("No.");
                d1T.Columns.Add("Type");
                d1T.Columns.Add("Severity");
                d1T.Columns.Add("Description");
                d1T.Columns.Add("IPAddress");
                d1T.Columns.Add("Job Name");
                d1T.Columns.Add("Create Date");

                int i = 1;
                foreach (Alert rp in data)
                {
                    DataRow dr = d1T.NewRow();
                    dr["No."] = i.ToString();
                    dr["Type"] = rp.Type.Count() > 25 ? (rp.Type.Insert(25, " ")) : (rp.Type);
                    dr["Severity"] = rp.Severity;
                    //dr["Description"] = rp.SystemMessage;
                    dr["Description"] = rp.UserDefinedMessage;
                    dr["IPAddress"] = rp.IPAddress != null && rp.IPAddress != "" ? rp.IPAddress : "NA";
                    dr["Job Name"] = rp.JobName.Count() > 25 ? (rp.JobName.Insert(25, " ")) : (rp.JobName);
                    dr["Create Date"] = rp.CreateDate;
                    i++;
                    d1T.Rows.Add(dr);
                }

                var myPdfDocument = new PdfDocument(PdfDocumentFormat.InCentimeters(21, 29.7));

                PdfTable myPdfTable = myPdfDocument.NewTable(new Font("Verdana", 7), countData, 7, 4);

                myPdfTable.ImportDataTable(d1T);
                myPdfTable.HeadersRow.SetColors(Color.White, Color.FromArgb(55, 95, 145));
                myPdfTable.SetBorders(Color.Gray, 0.5, BorderType.ColumnsAndBounds);

                //if (d1t.Rows.Find ("Normal"))
                //{
                //    myPdfTable.SetColors(Color.Red, Color.Red, Color.Red);
                //}

                myPdfTable.SetColors(Color.Black, Color.White, Color.Beige);
                myPdfTable.SetColumnsWidth(new[] { 4, 17, 9, 26, 12, 13, 12 });
                myPdfTable.SetContentAlignment(ContentAlignment.MiddleCenter);
                myPdfTable.Columns[3].SetContentAlignment(ContentAlignment.TopLeft);

                PdfImage logoImage = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"));
                PdfImage logoBcms = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"));

                var pta = new PdfTextArea(new Font("Verdana", 15, FontStyle.Bold), Color.DodgerBlue, new PdfArea(myPdfDocument, 0, 20, 595, 80), ContentAlignment.MiddleCenter, "Alert Logs");

                var ptaGroup = new PdfTextArea(new Font("Verdana", 9, FontStyle.Bold), Color.Black, new PdfArea(myPdfDocument, 170, 0, 220, 160), ContentAlignment.MiddleRight, "InfraObject Name : " + ddlGroup.SelectedItem.Text);

                var from = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black, new PdfArea(myPdfDocument, 50, 0, 100, 190), ContentAlignment.MiddleRight, "From : ");

                var fromDate = new PdfTextArea(new Font("Verdana", 8, FontStyle.Underline), Color.Black, new PdfArea(myPdfDocument, 50, 0, 143, 190), ContentAlignment.MiddleRight, txtToDate.Text);

                var to = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black, new PdfArea(myPdfDocument, 50, 0, 360, 190), ContentAlignment.MiddleRight, "To : ");

                var toDate = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black, new PdfArea(myPdfDocument, 50, 0, 403, 190), ContentAlignment.MiddleRight, txtFromDate.Text);

                int pgNo = 1;
                while (!myPdfTable.AllTablePagesCreated)
                {
                    PdfPage newPdfPage = myPdfDocument.NewPage();
                    PdfTablePage newPdfTablePage =
                        myPdfTable.CreateTablePage(new PdfArea(myPdfDocument, 48, 100, 500, 670));

                    PdfTextArea pageNumber = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                                                             , new PdfArea(myPdfDocument, 50, 0, 450, 1600), ContentAlignment.MiddleRight, "Page Number :   " + pgNo++.ToString());

                    newPdfPage.Add(logoImage, 460, 25, 180);
                    newPdfPage.Add(logoBcms, 50, 25, 110);
                    newPdfPage.Add(newPdfTablePage);
                    newPdfPage.Add(pta);

                    newPdfPage.Add(ptaGroup);
                    //newPdfPage.Add(ptaGroupName);

                    newPdfPage.Add(from);
                    newPdfPage.Add(fromDate);

                    newPdfPage.Add(to);
                    newPdfPage.Add(toDate);

                    newPdfPage.Add(pageNumber);

                    newPdfPage.SaveToDocument();
                }

                str = DateTime.Now.ToString().Replace("/", "");
                str = str.Replace(":", "");
                str = str.Substring(0, str.Length - 5);
                str = System.Text.RegularExpressions.Regex.Replace(str, @"\s", "");
                str = ddlGroup.SelectedItem.Text + "Alert" + str + ".pdf";
                myPdfDocument.SaveToFile(HttpContext.Current.Server.MapPath(@"~/PdfFiles/" + str));
            }

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/PdfFiles/" + str;

            string fullURL = "window.open('" + myUrl + "', '_blank','height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Alert Report' );";

            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
            lblmsg.Text = "";
        }



        protected void ddlAlertType_SelectedIndexChanged(object sender, EventArgs e)
        {
            string test = ddlAlertType.SelectedItem.Text;
            if (test != "")
            {
            }
            Session["LastAlertId"] = null;
            if (DataPager1 != null)
            {
                DataPager1.SetPageProperties(0, DataPager1.PageSize, false);
            }
        }

        protected void ddlSeverity_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlSeverity.SelectedItem != null)
            {

                string alertType = ddlSeverity.SelectedItem.Text;
                Utility.PopulateAlertType(ddlAlertType, true, alertType);
            }

            string test = ddlSeverity.SelectedItem.Text;
            if (test != "")
            {
            }
            Session["LastAlertId"] = null;
            if (DataPager1 != null)
            {
                DataPager1.SetPageProperties(0, DataPager1.PageSize, false);
            }
        }

        protected void lvAlert_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            DataPager1.SetPageProperties(e.StartRowIndex, e.MaximumRows, false);

            lvAlert.DataSource = Session["FilterAlerts"];
            lvAlert.DataBind();
        }


    }
}
