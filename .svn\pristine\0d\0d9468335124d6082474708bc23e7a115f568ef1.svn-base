﻿
using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IHP3PARStorageDataAccess
    {
        HP3PARStorage Add(HP3PARStorage HP3PARStorageReplication);

        HP3PARStorage Update(HP3PARStorage HP3PARStorageReplication);

        HP3PARStorage GetHP3PARStoragebyReplicationId(int id);

        IList<HP3PARStorage> HP3PARStorage_GetAllByReplicationType(string replicationtype);


        IList<HP3PARStorage> GetByLoginId(int id);


        //HitachiUrReplication GetByReplicationId(int id);

        //HitachiUrReplication GetById(int id);

       
        //IList<HitachiUrReplication> GetAll();

        //HitachiUrReplication GetByName(string name);

        //IList<HitachiUrReplication> GetByCompanyId(int companyId, bool isParent);

        //bool DeleteById(int id);

        //bool IsExistByName(string name);
    }
}