﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox1.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAPYAAAAhCAYAAADqKVZfAAAABGdBTUEAALGPC/xhBQAAH8ZJREFUeF7t
        XQdYFNfa/hR7L0mwgz0mKigWQKyxxMRo7B011ly7xm4UFUWx964xWIIC0sRYEEGwYO8lYqyJiYrALh13
        /u9d5+gwmV1Azb2/CT7P+8zsnDJnhvOer56RPva6TxmgIJfbMT5EvYq7f6dpx/xo9cnlNDvMnSYEu9DQ
        /RNL9A4c2aO935Cg5t797tTb03NOnT09qa6MKj59acqmliTNKk/xc6wzhGGeFUWMqU52DRuTo6NTNv4F
        78CpQQMquiyUKCCRyOd5NrTegZ+emo9yoT5dOlOPHr2oZ8/e1LFjZ2rW/DNycHCk0qXLUO/efY1lGZG6
        A5P5PENiLKq0+xE1DbhOa0570PITK2l2qHvBicGzxo88MPWPwfvHS0xuqYPfEKm5t7Nk79W7PoPqe/ch
        HE8srk1psytkSGoQP5vY/77FLJvYZhezXLzYFSa/eGo1fi4N6NmD+vbtR87O/albtx7UslVrcnJqTKVk
        Yvfp42yS2PmYyJtlQoPUUpU9D76s5XOPVp4OoB/Pbaalx1d9Pu/Y4msuR+dLU47MkcYcnC4N2T/BSO6v
        fIcYWvkMqNPSZwA5+A2h7WubkORSLlOkzib2v4/U0Mqyif0XYg9nMv/A2MG4TntjN5LXM6re6muyq1Wz
        sW2dugvr1K07r2at2tOq16jRwrpixQFlypajPr2dC9g7OIzTktgfMZEPKkldbc+DsEq7H5LLiXDyu7LT
        YvPZH9zWRW40rDixRnKPWCax5JamHnE1kntw0HdSj4ARF772G2bRZt8oWrb1S0plSZ04xyqb2P8ClfpN
        TScjsZcfy1bFX6vgLZjQOxmSEXtj1pC/nsiuFfE/y5y5LLpY5M61iywsqhUtXuxr/n2nvJVViQ4dOn5e
        qEjh39XELsWEPg1SV2dUZVTzuq+33v2wzowTZyn4F38rryvex3dd3H3jh3MeHdef3lRp9an1xxZFrJDm
        hC00Sm5Wy6VvgsbN7XpgIq39sROlzKpASbMzT+q3kNi5eFJVYjgxejO+Y0xm/IfRl9GB8RmjMcOeUehN
        J+F/sV1hvpcj48P/4j0z8mlY81gaMvK8yzE51a9HJRYfJvpZYvs6Rmljl+CJ3ZLRjIHJ3pkxhDGdsZCx
        jrGGMZXxFaMSI8c/xE6vyM8RL5N7PgWlUA7XfZQntwUVKJDfOl/+fO5MYipTtuxnhQoXCqxYqdIEG1vb
        74uXKL5XSexCTOhQkJrJLNVi1Pd5IFXZ/Wjw8LBrdOLXww0O3Tp4a9+NoIl7LnvTtvPbiclNW85uC2Jy
        SwvDl0suofOlCYdnPhkY7FJ2taezUUonvbSryzG6M1YxZjJKmnOiwcY+Oa461bNvTA4ZSzlMsPaMSIYk
        I42Pzxl/MuIYBkUZ6qQyPn2XE/Nv6muLPG7fv6n/jEisLi/J47gjj+mbdzkmpwYNybZDHyq47hyRfwJL
        7iRB7mo8sbcx/nglvV5KsTjGTcYVxiOGQS5PleuX/QeQG4vaM/m5XEBsi7n7qEDunFSgYAHrvPnzLS5c
        tCjb1qW/srK2/k+VqlV/LFKs2DT+PVtJbG9Baiffh9KY4D+kEYeeTJ0WcZNOP4joeuJeRGTonVB7JjZ5
        XfEh7yt7yeeqb41dFz2TN53ZKq04uVaaF7ZImhi2YNpq3xEUN7cyJc8q78gE3sNIYkgKbDNHbMmtAnkP
        /ZTqNMjQK16XJ9dpBZmD+BzSuhSjACMfoyCjBmMO44lcF6T/6F1OzL+pr7PyeG/yMe/fdI+skBvakFg8
        177r8Tg1bEiNGtpT9f6TqPCqEy/V8r2xguB1eYInyZM8ho/WKuLa8e/9CvLf5fOq7zm5lcSeQoFJlHPJ
        McqXNzfly5+3UJ58eesVKFSQipcoUd66UsWKjo6NbAoUKlTVsnTp2oLYI19Jau/70qjDf0ibzsZ+/9Pl
        23T24Unn84/O+Ec+OG15NOoo/XzzZzr0y2EKvn2EDtw64Olzda/kcX6HxDa3NP/4qvCVh2bSc7fq+VNc
        yn3G5B3C2KoiNQgeZiT2XGtKmG+VDkkLWG13syLnzxtmFO5qxhMLBMVEu8dokYmJ9gnXecR4wMififpZ
        mfRZqZtTXlg68bG6mXGMkZ/P9X84VuVzQTvC4hnPgEmjLMvNv6syerzVu3VoRI3r1aXG9etThXErWHrH
        C9U8D5P0hkzcW3zMa4K0ngpyB7wDYn/IfXRitH0HfWU1jKck9mS2s4kCEyl3066Ulw3t3PnyUv6CBakI
        S20mNjVizzhU8w8tLY1e8fKMp4LYdj4P0iaHRA8NuB5FZx4cH3zht3MBF367UOjU/VMU/ms4XztN5x6e
        o1MPIp1DokKkgOuB0k+XdksstZMWn9lcx3vfREqdWTofEze/QiqfUZF7Y7ybNeknV6LYntUptvdrJPep
        Rie71qQGPGnMqOEf8+T5Q570z/hYMwsTvz/XvcHIkYU2WSFtZupW43tj3FiUQG5TbbAAfMCw+B+OVT02
        LIhQydXXh8jPg79L0bcdbyOOyzaxs6UP5vkT7UsGIWA3X5BJC/UbISAtolTg69EKckOSZ5VQyvpH5b6W
        vWU/bzKG9MTGcwSlUs4FhyhvrpyUmyV3emI7pSP2aOEBr7rn/u2aXr81X3rqCkX8GtL19INI/0u/XSpx
        /tF5AqKeRdHd53fplye/1OTfT479Gi7tv/mztPuyl7T17I+j17Ld7RPsSgnsLANkYufm47V0xHa1bhU/
        y5qeN/+Unn5iS09r2bxCDJ/fsrGlVrxyNzQ94XfJkwjE6JzFSVSG63tmQBZMTKjzUOUzQ1RlPZARk9/c
        wgHNAXY+xt8xk/fQGgcID0kpynCeGZUdYyxm5r7oJ6vkFNrF72/QVvMdN65nR7U7D3xJiL0xIPZ5mWRX
        zRAb9SMUxO6qIiT6KcnAAmCZQT/o64Tc1+IsEjunvBhpERraRzkZODdF+r8SG45FtrVzt+xNeVhq52dV
        /LXETk/sQUzsMxzSmlRx96PCw0PP0MFbgc3C7oSGs5S2jLwfyTb2aYp6GkWPdY8BqzvRdy5f+v2SdPzu
        cenArYMS29zLd178iTwue5HfibWkY/s64XUySh0m9QsFsc+zqp0vtlt1evpp7ZzP6tbOwSCBaD4H+to7
        kJ2jpo0N6QznGEhxhgFveGbIJ+pgUrdm4Khu15yv7Wckyv0/5uMCmeTKup/ztemMiQw/xhpGEcZsBuzh
        p4yfGV9p3APSeoTiGVz4HJ7vlgx46/E8tvI57gPPflu5HxB5kHxtBh+PM1ox4EPwYIBUMDPgdINWoxwz
        +p7KQLTgCAOLo7L8C/49hYHnwntdoSgHyR0YjRhd5Ptj4RPtbfh8MwN/k2gGIhAwlfCeYWrAiw4H4Dq5
        30A+dlfdH3V8GKsZ2+Rj6SZ2dajo0qOwt7NCbB8FsbvLxCnGx2GMSIZwtMEJd5HxDQNEVBKsNP+Glx0O
        OtTzZcAr347RhlGdMZQxmDFFLi/PRyvGBsY1Bmx+e0W/GMNExj3F+ND/t4x8qvtjLBrE5jGyBpNz6THK
        nSsX5c+Zg4ldhFXxSqyKpyc2Vd3zkKrseUSjQo/R3iuelYNuBN48EhXiePTOUQLOPTpnlNZP9H+W/FP3
        x0kmuXTxt4tSxN0IJvaBYL/r/nn9rvmRV9RhOh04kRJdyipj1qtV0nqA/vuKFN3Ehp7Z29aPtrepzCCB
        53wew/jG0SSxR8kTCJMIEzErpDZXd4Lc72U+jmQMYIAAuM9VhpXiXnZ8DkILR9JFPkc7qPg/MEAMUabW
        KCIUZaKOOKJPjBGLCbz54vpQxb278fl1RZk/n0P9DZEJAR8C2kUxoJ2IZ0YoEJqK6HOlogx16sh9iHJ3
        RXllPgcZRXRBx+fl5XJrPsaaeaaxcj140UXfF/hcy8exXq4TxsfScKbVa92ecu+4S+SrywqxgxXEaSQT
        BmExEDREJhfIhASQFPn6ZBWxpin6eBlLfo3f+dyWsVLR/gmff81Qe+9Hyv2CpGFyH6zaPv+SgYXmF/ka
        PP/qMJ02sbEAwZE2YiXlbdSeCoPYFWFjK4htvfsx1fGJoqnHAmnnhS25d17cFe57zX88284EhLDDTFbD
        P3oU+yjibvRd6fqf16WzD89IYXfCLjKxLYNuBlHAnRA6dnID6edWoYTXOeGQ1vEKYh+In2eVSzehKkV/
        bs9oODC6pZ0zgwSe83ksY6CTI9XVJu0+xQTpqZqcb0pyqMOYdAjlwKZV9oMJjTJIR6WtC686JDPKIOGH
        KdpB6op2WBSEuoz2kM6Y5EIVh9SHFgLprPQVoD9BhK9VY4JdLspAckhjMWZoBJDcKFdKXZRbMoRtD01B
        /b7Ys+qkl9tqle+RyyCVhZ2N99CUgYUC98Q7gebTgAEtoITiPiCs0LS0/lab5PI2aAMnWrlJG4x2pUyO
        0zIJzKniRbiOkIjn+NxCbruLj5C6arUXBAdpES+GFBbln/A5pO1lBfEgkRsy6ivqYXFA+0SZ1Ev5+AVj
        EQNx9vxyXRAX9XBdOQbcU/gEEItXlpkmNurt5/fCanmBmvZkXdYyvfNswKGfyS1iK62PXElrIjes4Pj0
        aQ5hEeB/3Z8ldiiIXfPGnzdu33xyU7r6+KrEv6GGX2YveSkmNgXePkyHOAT2bIkdJc0srZTWQQpSJ/J5
        vfi5FShucH2K6dycYro0mRHdzsE1+ksmuQL6dvY0rEkjquugKY2V0hAT503JLNrB03tenlCTNPrDPQSJ
        +qnKRewcKqR6HFBDRTuE5ZTlmbGxEWcXJofaDofqK6Snlo9hlXxvPJfS5MACc80McZG0c99MORYhPBMW
        ByVh8Wyj5TJzNvYSuQ7CjuqkG9wbmgcWQotGbIo1bN6G8my/R+SnE5P9lEwOkM2U82yGXAckAmlF2wV8
        7qRB7MoyKVEfyS9q4p+U+zNlY09S3A9poFr2cmu5DrQDxOXVdXbL5Q/5CC+8KDdPbNmZRtM8qWIZS2rs
        4PDaebYofD65HXMnzvtut/zEamnjmS3tN53ZQh4XdrLEDiAmb1e2tZ+eeXiWpfRZKfJBJFTwa2G/hlUK
        +zWUjt4NpxDGo03tKfn7j5SknqHyhM+Id2WHmksVih3UmmK/aUux/dp4P+/SdM/zTo1JifjOjWlIMyct
        YsMhJUiICdbhHRAbKqggoJZNXIzLf5PrqO3SYPm6t8Y4oKIiLIS+EVvPKrGh6grpqSY2bOoUuW+1NMd9
        oP5qERCaBIiDMi2JjJg/QoemygV5tYg9JhPERuafeNew1ZXvBIsVyrAoGXPHbdv3ohzez5SxbEHspzz5
        ezJqMJCIUorxsSwhhcq8SkUgSFshPZXEwnWdTKwBGqQT94Qk1iLtGLltLB9hl2vVwaKCcSFMp2VLww8g
        xv2fLBEbITB2qBVeHkY2TVtTkQJ56cNSHO6acmQ2UHzmUbcb7uHLONFkTY11kZto+4Vdtr7X/PbC6x18
        O1hilVxikktHoo6cZLvbiolNoXcj6CiHwe579adk1w8pgWPQCe6MedZfGB1mszkpBZhjDcmdI961HOkm
        OVHciK4UN7LTR3HD2uti+rY8FtOrBSmR3KcFTW7VhGztNaVxuGJyDHwHxEYfYrLBdtbSAISWAFIonXWC
        2FoSW0lMdZZWZiR2BTPEzqi9IJmagG9LbFP94p1lhtiwq2/L73uj6l1DW8LfwRgfb8wppjW7DVPGsUEY
        QTI4v4QDLI3PARDjBQM2dHsTBNMiXROui2w1tH8bYsfIC4zWPeCwQ/8HTYwLDje9XGd9loj9MmJAdFii
        vFO3U/FC+emjD0oSDT8wmUYenDpt8pHZnO/tLi0+viJszakN+7ee+zGRPd2S12VviR1jHK8OYOzbx17w
        YqHsUAu9d5xC2Fv+ILg/GbYXoZSfylDK9rKUtL78x4mrKzxOXFVBSlxWQWKiX493tf4g3rU86WfXJN2U
        3gxn0k3u0zNudFcpdmDbU7HOLMH7vUbagDbk0aEF2dg30iIZPKuCiEvfAbHhqEJ/kICQhFr3RFIG6sBJ
        pPQGmyO2kphwxGVVYv8TiY13AI833iVMAmW+ORxq0MaM7wne8IrDF7AHOEVJFEFseJyRVYYc8m4MSDzY
        tcitNhU6Ul4vzvVGM+DMEqR+W2JDYkNzUN+/KF97LJPWy8T4CvH1Bxp1MlbFlfcLSKA8i49SiQqViZwD
        R1kODhr/eOyh76VpIa7SXE4LXXx8pYT8b6SKbju3Xdp+YScnoXhuYAme/+CtgxTCpA67c4zu7RtELzxK
        UFrgR5TqX4pSvMuUS/EsczXVy1JK9S4lMdl1THSHxJXlKXE1e8rdujC5h5N+zlDSzRzkrZvQU4r9tv3R
        2CHtSImUYV/R4Z5tqK42sdspiI3VH+rj29jZy+T+zOWP/yDXgTe4nOJ+2cR+/e4zI7Hxd4JjTSzMwkdS
        lq+9YMAGN/4tEcP+cJ4fe3+NySlqGzujOLYpcpfhvlYz4NWGN3omw1khLd9GYpsitpKc+0wQG+q58I4j
        VJd5GzvdQsKS+6BEeYYvI/rab8g32EM9lPdSC3JjG+YCVsuXsc296tS6WLa7++/gODVCWvvvHqPjV3zp
        ydoWlLq0JKXsLENpAZYgd6m0/SXPvggtJhmOlwRSXxwu2SXV35JSA4tR0g/NSL9wEukXj6P4xWPr612/
        faGb6izFjeq8LW5ER1bPXyNpVCe6Mqg9NW7kRPZ/daDBG/tQMTmymqCCiQNpKJw3sDXFRGtiYpHYLtdB
        jFi5K+x9IzacZ29jY7+tKo53r3TgCXVcqOEvTSFOTkLmWeG1pzl+zRtC3g2xEZMG+RCWasUQcWskqgg1
        +O8gNuz627I0hkquteiA2KIOvOdvSGy5HdJwW/j0O9zRb6jUN3D0K3JDLZ9x1E1yCV96dl7khtqrL2yn
        bVf3cpz6CB0/uZ5iFtqw99uSWN1mYpelNH/L0i8OF480RFpKhov1JMOVL/ho0+9FcAFK21+QpXkNSlg3
        lRLWzKCEtdMpfuXkPfr5IyTd9wOkuHHdJseNZZtbgfhxXemPUV2oXdMm1ID/yBpkQ9KGICOcPVnd0AEJ
        LJxOItSF/tRJE+Leh+T7HVaN5X0ktjmveEbOs3dBbLzThfL7RNwfWXvY7IK4uzGcCFI7NG5O+bdcY484
        70H+K7HNpZRqEQdZXnC4Qd2G6q6skxGxhX1syisunGfmbGx/+d4Ywwca5EaI7je5jutbExs2N3+f7JeW
        Pv2ljn7DjF8/GRQ0XhpxYMrj0UfnjlsbOD5XyM4+FOw7ko4cdKHzfmNIN68qk7oMsd0siP1x2s8lbxgi
        SzGhv5QMt8ZJhqjvRxmu9yDDGSdKC2tOSdunU+KPCynJYz4l/ejWJmHttBd699GSbuZASTexVzfdhF6k
        hJ5/A31btSA7bXUcEzBEQW5fPs/s/mCofYinIqaLSYa4b5Lc11yNRQThIiSggPiYkMqFJrPE7q9qB1te
        xLHV3mHRvzmv+Js6z9C3cD4iGUS9aL4NsYUnHiGrYhp9K++ljEQghJbAmCfaIDGlfqt2lNPzMSemxClJ
        KEiGbC1Tm0C0iD1BJg3IhzTSrBBbpKdiz3dGXnEtGxttkKQivN5aueu1uFw4AxEnfzuJjfaVPLu62e3p
        KbHk5k8aDb7aM2DE2D6Hplmu8h5COtfKJQwzLAumMJGTXBgco36VKvqS2G1SPEv/bggryhK6gWT45bs0
        w73l3xruupPh7gIy3FtEqSFrKHk3w3sVUDRpx6JLCeu+l/SLxkg6l0ExrI5bMUgNw4z+tKR7O7Jp4GjK
        fgZB4XARkhtEN7dTSvQDD/YO1cTbKfcD6V9cVYZ4MkiI0BN2LynHIzLTtMJd5pxnIC3sdYwdKZ5az2gt
        T3itsF5G4S4RlnrOfai1GeEsRLxZ6QjEGEDsu/K4YKKox2VOYiMjTvwtkB5q6u8mrosMPLxXxOtrizbY
        /FFu8kZjdpVikiPRRCSLYI+yMt6bkcNsjoJYIhNNtEG4y5wqjh1iICUIrnWfcXJ5Mh+VCS7KuvB6i4w0
        2PjqftzlPrxVZViEsBjh/viwREbP+bq8wk9d8lT37Obk4NXbvs3egfk7BI2hpbv6UOzcKhYpL/dTW2ju
        nXa1nsze75RUH1a/T5SA+v3CEDVrqOH+Sib0fDLcd6W0SCZzwDZK2b/ViGS/9VuSdiyUjMReyMSeNdhb
        N+Mb0kLqrEEUObqXURU3s8sL5FamdiK8gwwo5DQjdxtqHjZFFGUgs+ugPPmcVRMPGV8ii0yZSom2SNnE
        hFWnr0JtvCSXIS9cPZHFgoC28zXKT8htke2GuiCVMgUUzyCI0k/Vvr6iDLnj6nsLmxXt0Y+yHLnlol94
        +7EYIgwG3wGkJ5xYKF+r0e8cuQzRgdKqcixkMXJ5AB/x/uEPwbvXIjm+bCPGcYrPjYk0Trxd06Zjf8qJ
        +HV6aY0vo4hJjoneIwsTHembQmJiUwe857CxoRYLUqHcRaPP+XJbeM/7MWAPI2YuNm9ARRd9Iw/cFPkG
        yvUgmbENVNRrxufIWkP2GbLdlO1t+TdCeOh/cxael8jqpy70sWc3YmJTE/+htMijk/HLJ/yRhHZM6IEa
        pK7J1w6yKi4xsSWZ2DGGK227MrHJ8HAFS+v5lHp8FaUc2E4ph3dRSjDjoMf45L1rpCSPBVLCmqmS3n0U
        E3tIBwZpIWH2UPp9+kBq26wpNdBWx5WTBSmIoQwhBTFhkOaJL6ggyylZMYnO8blQw5V9YJODcCxBap1m
        QOJBTYcEVNZF6iU2S4iJiVAZ8rlFyikWhOWKcowBEg3XRT/ITBM51pBYAAiB8qaMk4r2MAVwDWXIYhOp
        nbg/7FRlSikIBqKIsUH1tlGNX9i4qAMiY1FDjjkWG5GOiufGphY8E0gPcwLvU/QLxxfyz5XvBRtTRDna
        45nwnrSIjUVMJP689G3An8L2ddGVx8VWTUxykA/JKNisgQkuwlPP+RykU0tgU8TyUBAQGWBwosE7fpgh
        vlIC5xoyyZR9QDMQJgDuj5j5fYYNAwsBJLW4jnPcBxlsBTWIiEw4LE4gN8wJ7C9Hf5cYahUd8XWkxKJv
        1Af5oR1oJbj89ZlB7Cq7u1NN3/60dHMb44cH5c8ZHWICz1IQ24bPsaEjRWSUJS63klgVv2EIL17bcKUN
        S2omdtRyJvMySvbdzGRmYofsBgakBG2VknYvlxK3ukrxKydJ7Dzz0bsO49zyb03ixfzhNKHjF2RrWh1X
        TxgQFnnUsxjIYILUwRGSBpOnCkO5zVHdHsTrwYD9id1K2BgCrUBdD1IQselmDKidCMHhN6QU6qINEl9a
        yuXYnYVytZpvxddABsTmcS+MH0TqxYDtDcJiwcGiAECy4vnwLLiOvHPky7dXjBE52tBIkOiB9mj3paJc
        PAvK3BhrGLBv68l1sEgi0tCCgefDeCCdsXDhOaAtYJGBpoD7q98N7ruIgV1avRn4ko0ptRwmDDa7GPPO
        G7Ftbde2E1l4PVVmm0G6wkbF9kvkbtdkQJIhTRNZWlppoFrkxgaL5ozZDGSR4Yh+UBeppsj/ri1fU+/0
        KsDXIa2XMLBZBKo1Fpy+cp91+IgsuAYMJMdAm0D8WmscaIfNH1DJ0d/nDPX90A47yNAXxohnbppBv+nv
        VXxPd/qUJfb21Q4yqY37qD+Rt1oiv9uTcZSRmi5FlDPKOPlkf/KOMmUN4cVIutGK0s7MpKSf3BkrKDlw
        E6UcYmIf8XROOeDxwiitoYZvmCnpl4yP17sNt2OQOUiLRtHWQd3Ipr5DRvZadnnGNu3/t3cEByCk++JX
        xEeYy96RikBipw9zZd62zIod+k+uO4D/h44Ti2oav/ud8Pp/6RiryvNWfq8M50mcKjqeP2uUI2WnJRki
        irMN3YVDWTMocasbJXmB2JDYHqPZtk5L3rtWStq1WErcPPultHYfPU6/YCTp3c3DsGQMeQ/vzd8+M+lA
        +/82WbPHk/kFRpgq6TbIIOOs7JQt6oyzbGJndREysOqt8T90BJgh9jEuqxXPdniCezlK3sLbNFd2Jv2C
        8RynRljLjZK9VuZL9t+wLdlvncQkN0rqxC1M6lVTJP3iseuZ2JyskjFeLB1HB8b2o7oNs4ltRp19HxcT
        OOwgrX3Vz4WMs096j3r5pdL0nyHOJndWyK2Q0sqdWR4axEbOdz/eT50bWy/1M6tyzndjDlP1If0sVqkX
        jTUmnzCxHZN2LbkACZ20w11K/GGelLBxJkvqySD1OvaG52YwsTMGiB0xaSDVyyb2+0he9Zhhq8PehnMP
        jk6EEGGvp6uH7ZoNWnzOH1fg7Zq+r7ZrZpM6K6RGXc1Q1hzrqnzdy+j9nmO9hQn9FX/OyCLexZp0/L3v
        uKH1eetlW96h1c24oQO530zsagmrp25NWD/DADvaiLXTWUozoZeMT2EJPTEzZFbWAbHDJw7kr5VmS+z3
        XGJjuyY8+0gFFhGKb7WeyZh15tSUCmy8JO/sykLsNquT/59c3wSxXxOePwWMOrG9q/FXTmwpuq0DxXRt
        QbEDvyTO886hm9LXXj97yAq92wgdkxdSmYk87iU4CYVt6Uj2gDd/qX5nLKWziZ1hYsf7Jr0RxxYfb4D6
        DWKrcwJePdNrYl/MJvbbLDxmic3f/dZ/V5met/yUntWysYhuVKdidDv7z2K6NxvFH0rYEjf860tx47pL
        ukl9JN30/i9TRGcNlnSzhxgYoSzJe+nn/Sevfj6cZNnEfs+l7psuKFC/xzBcGaMY8Iab7CtbYr8jDcUs
        sVlax3SoYfxEMH9FNGe0o22N6Nb1pzxv7xgR0735rzH9Wt/n7ZZRccM7Xosb0+VM3Hc9PHmf9UDdtH4f
        M8lzsSQnJjZlE/sfJ4XflOQZtssm9rsh9v8BGbpg4cxKcEwAAAAASUVORK5CYII=
</value>
  </data>
  <data name="pictureBox2.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAAAAAAAD/7gAOQWRvYmUAZAAAAAAB/9sAQwAGBAQEBQQGBQUGCQYFBgkLCAYG
        CAsMCgoLCgoMEAwMDAwMDBAMDg8QDw4MExMUFBMTHBsbGxwfHx8fHx8fHx8f/9sAQwEHBwcNDA0YEBAY
        GhURFRofHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgA
        HgDcAwERAAIRAQMRAf/EABwAAAEEAwEAAAAAAAAAAAAAAAYAAwUHAQQIAv/EAEMQAAEDAwIDBAYECgsB
        AAAAAAECAwQRBQYAEiETBzFBIhRRcYEyFQhhkTMXQlKCI5Ojs9MWV7FyorLDJDR01HU2N//EABoBAQEB
        AQEBAQAAAAAAAAAAAAABBQYEAgP/xAAtEQEAAQMCAgkDBQAAAAAAAAAAARECAwQFMRMhQVFhwdEScjPw
        IgZxkaHSNP/aAAwDAQACEQMRAD8A3umWG5nnlsn3H+NLjb/Ky1RuUFuu7qJSvdXmop79KaoMFdBs3CSU
        dQ7gVU8O4OgV9j2gYwXqhecaZy6yZnKN0exNPManJNVvBSw2Gtx4kqWtG0niKmvZoNCyWzrH1RjG/u34
        41ZHVK+HxI29O5KSRuAQUKUAeG5SuPcKaCR+4nqL/MKZ9b/73QaPUHG8mwPpXNddyadcbrKuEYGbzXUF
        DKd1G0VWoipqVcePs1B6tPRbqFcbVDuCc+mNpmMNvhvc+SkOoC6V5vGldUN33pv1XxCzS8hg5y/LVbWz
        JejOl3attsblCji3UK4dxHHQQkq/ZP1EzbGIkW9SrF8XtQckeVW4GkvMKeDiktpUj31NenQGP3C5p/MS
        4/U7+/0EU6c96V5hYGp+RO5BYr9I8o7HkFZWklSUlSUrUvaU8wEFKuPYdBu5HnWd5zmkzDMDkJtkC2qU
        i5Xn8KqFbVkLAO1O/wAKQnxK9IGg9fcV1GPE9Qple/i/2/pdBM4h0fyy05DFuV4zKbdIcUlfkd7yUuLp
        4N9XFApB40px0Ee90JzNx5xwdQrikLWpQTR3gCa0+20AT01wvNM2Yurv8a3GD8MlmJTe65voK7vtU09W
        gLMtx7KOnHTG+3JrKZ1xvDzsVLM11ShyW+aAUtpWp0Aq3HcdQXHYXnX7Jbn3lFbrsVlxxZ7SpTYJJ9Z0
        G9oOZ7z1Gzlm53ppq8vobj5SxCZSNvhjK525ocPdO0aDpjQeXSQ2sjtCSQfZoKAxvMcpuPS9q4zbpIen
        fG3WPMbtq+UluoRVO3w11r7PitvyXRdET9vk538lz348Ns2zNv39X6SsOX07v0qU9JRk0llD61OJaTvo
        kKNQkUcHZr6s3PFbbEcq2afXY/PJsee+6boz3RWa06f7BjHMfyC83a72/wDiGUx8Ld5XM3OK3+Jaa03i
        nua0dVqcWKyy/l2z647u7uY+g0WfUZcmPnXRy5p19PGO3uZ6kWO/Yp04vtxbv8t+aoR0R3gtbZaSX079
        viVxV2V9GsTWay3NERbZFlOx0+27bfp5mb8l2Svb1fzIxsVjYyHDsYmXApelswm3fMPIS64pT0RTZO5X
        H3nN/rGvA10d9zcLm8z4q/XyPwquxNfIV/09a+7s8Pp0AZ8vdrcunT/I4LUx+3vPXJYamxVbHWlhlspU
        k9/HtB7dWRv/AHUdaK0PUV3bXt2u1p9eoI3NOjTWN9K7+5DkvXa9yFsTLnOdHjcbYc3rCU1UQBuKzxJO
        qIbpv02veU4tDn2jqBLiNoRy3ra0Xf8ALLTw5dEvp4d44Co1AU/cNnH8xLh+v/5Gg0+seNSMb6Iotkq5
        SLtKTPZckT5SlKWtayo8N6llKQKACugJslx/N7h09sk7DrxIgXWHboxMFtSQ3JRyUkp8QO1z8U9/YdBW
        mE2bMOpseTbL1nElkRXNt1sTjZTICUqpWlUJUK8DUGh7RqgiTZLfYvmExWz25BbhQbQWmUk1VQNv8VHv
        UTxOgJMl6ZdSpV5fk2DOpUG2vHe3DfK3FNKPvJSoHij0V46gbx3ojcjkEO/5rkb+RS7aoKgsKCktIUk7
        kklRUTRXGgA49tdBUuG4ZdZee5BjSsnkYzdm5C1IS2VjzYDij2pcaqdqgodtQdUWT9w2cfzEuH6//kag
        KOn3TC+Yzd3rjdcpm30KaLTEZ5TqWkFRBUspU44FGgoPRoLC79BTHy0/6HK/+2X/AHdAQfMJEfk9KLwG
        k7i0WHVgdyEPIKj7BoCrBrpCumIWeZCdS6w5DZG5JrRSWwlST6ClQodBNPvssMrefcS0y2kqccWQlKUj
        iSSeAGg5DuTpl2m/ZQ0Cu1Ky5h4PAGhbAeVuHsWn69UddRJcaZFalxXUvRn0Bxl1BqlSVCoII1Bq366w
        rTZ5lxnPJYixmVuOOLIA8KSace89w0HPGHMPI6MQXnElKZV8edaqO1Owor9aTrb2P5bvb5OX/Kvgt9/h
        LpRr7JH9Uf0axZdPbwV904/9Zlv+5/xXdbe6fBh9vhDlth/1aj3eNxv5g/8A5RePWx+3RrDdUJ+nn/g8
        e/66N+yToCDQBvTDpyjBbVNgInqniZKVK5imw1tqhKNtApVfd7dAZaDC0JWkoWApKgQpJFQQeBBGgqG9
        /Ldjz9wdnY9dpmPOPElbMc7mgTxogVQtKfo3U0ESflwyOvDOptO7wu/v9WokXPl9kuYdPsj+SSJc6dJY
        e89JStxDbbG6jaW1OKpUrJKq6C2LNbzbrRBt5XzDDjtR+ZSm7lICK07q01AE5j0mFzyaHleOXH4DkcZQ
        58pDfMbkIpSjrdU1PcfSO3Qbb3TdyT1GtmcSLgBKgxPKuwW2vza1FC0laVlW5Iq52UOgN9AtAC9QejuK
        5q8ibLLsG7NJCEXGKQlZSPdDiSCF7e7vHp0AO58t98CqMZ1PCO4KS5UD2P6tRJ4p0GuNoyCHdLllk25R
        oa+b5Il1CXFp9zeS6vwhXEinHQW9qAN6bdOm8JZurSJ6p3xOWZZKmw3y6imzgpVfXoCyZEjTYj0SU0l6
        NIQpp5pYqlSFiikn1jQU1N+W9yNIdOMZVOtEJ1e/yVVqSn1KbW3X8oV+nVqGU/Lfd5n5m9ZrOmQq1XHA
        Wd36R1af7J0FmRenWJRsPViCIQNkcQUOtKJK1qUal1S+3mbhu3agrRz5cLpEUWrHmc+FAqS3GVv8Ffpb
        cbSfXtGqPUf5bJEx5v8AiTLZ1zhtqCvKjcK/lOLd2+xOgsK/YBBnY7b7FbFIt0K3LQWG0oK0hCElO3tH
        E7q117dBrORfN1K1ijL3bbp1eOLYu9NLqitCSlsJPcKfVrwy04ikIDHMSTZrrdrgJJeN0d5pb2bQjxKV
        StTX39e3U6zm2WW0p6I/dmaHbI0+XJf6q8ya8OHHzN9RMQVl+JTMfTKEMyy2fMFHMCeW4F+7VNa7fTrx
        NRKY5aTZ7BbrUXecYMZqOXqbd3KQE7qcaVpoJHQLQLQLQLQLQLQLQLQLQLQLQLQLQLQQF5YYcuadklbE
        nY39ml0qoHK/ghSePZoGH4N6LBBuRSslICg0soC+bxNAn8enf2fRoHJLMzzzmx4eZUy0UV5oUgJ+1S34
        CDv41UPEPRoGZMG9bUl64ulW47wlDgRxaWB7iOwHia8K+zQP+TyFLKx5/ehX2qloUCniCrYNnEUqBoHL
        YxeUy9qpC1w6OlS3EqrzSohCUhYSdu019HAUPHQNXmOhcthKpam5oaQC42h3cqj7ZJAbCkitCPb6NA05
        BvBYc3XJQUaCoaWUBXeogJ4nft7+zQZuUSW5MQWpy2biI7QbSlDigdpXzSSEn3k9nDtp9Ggy9DyXzBba
        nLA5Sj5haCU8yquGxKPxdvf29ldB7ZiZANyUTVKUSjmLdSvwiiCdgKAk8ARw9PHiNBh6Ldy6lT8spc/C
        Q2l0oA2rA3EIoeNDxpoCMaBaD//Z
</value>
  </data>
  <data name="pictureBox3.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAD7SURBVEhL1ZXLDcMwDENz6DSdJvN0kwzQuTKKGxaV
        QcuSYhnwoQcjfz+R+mQrpWwr19LNEfgXsB1Hdj2ub8pvvbzvZwH7teFJAJxjdUHOAhCxRC9HQJ8aMgvg
        6BkESKNiBiB2aAW4xrMGkgXAAixrc7nX2JQFQL5nD+ei2pQBIDKxR9uk778lFxkAvOVKwblEzSXavJcB
        6Br3AG4VaZkj16zAfJ8V6MoYAdwl/IwAXdMYo0BGBgfDgYaAOwXWcOxmVKQgaiaBWyp5ToUKRgAY2VoJ
        eqAmP6tAIq+NpADSjCkA5yJKPNToqmos8pJq2aBtcQuiWvT/P/2VCj5bkZN6U8LHKwAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <data name="pictureBox4.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAEOSURBVDhPY/j//z8DEE8FYmLBYqgeRiA9HaR5IhDf
        AeKfUPwLSGPDf4DiD4E4GGpAHJB+BTIABECa04HYEYjdcGBPoLghVLMSkE4A4o0wA74AOXpQSZAYPgzS
        +AHqEiaYAV+BApZEGBACVPMDiG8CsRUQs5FiQBBQAygcQJpNoZa1E2uAF1DDZyC+D8S2UM3sQPo5MQb4
        ARV+B2JQDNgjeZMDyH6EzwBQPINCeylIIRC7o4URQQNACkC2ghKZNhAzkWoAM1BDERCDvIAtWgm6AFkT
        yDvohpBkAMUuIGgAKUkZ2bAHsGgEevH/XCCuB+ImInAjUE0bEH8DGRAOxFuA+BkQP4XSIDYh/Byo5gQA
        djuU1XU47p4AAAAASUVORK5CYII=
</value>
  </data>
  <data name="textBox38.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAICSURBVDhPhZNJSFtRFIZvXbioUZRuTBaCIq60C6F1
        4UZE4hBaR1qFFkFwiDjjIjtxSCRGUHFCTARDBdGoa8GhMcZZEQdEEStE0JUEpzi+1//ge/J4vODig/cO
        93znnnvuZRmWBaZAKGJG4AU8uEtqmbM1Tu6rbx+eGc/zb8iTA7C4AFiBGdSBElCd1uY0ppqd1l99K/rD
        8yuVKJEKPmJhv5AcL91VepuT/eheZgNzxzEprX8tWrNzbM/j1ZBEFAQioRc0K7WE6iyva4m9cBy79j2x
        iuGt0mTTvOPo4jpEFOQj0QY+yATh+HdAUJ7T6Wb3j6/939w/scKB1aasjkUDCcKEbccpVCfBJgTNJPAJ
        ApJMbZyFJRnn7SSg07YoVNcglgt+Q/BFLiBJbtdSLQkuQY1C9WjEBoEPgj9yAe0GsUwS0JxpVEr3gdri
        IJjwI9BS0i2o8iNIQPwZgnE/gm8kGAKmdwQOuYDjOZyBW08CtTCFKAVJLGIv2MGIXLDr8Qbp2hdsYt96
        YRLSc4hErIfOCIIDCPKkYzSM7lQkNs62igkqLBwDZZJdROC7GPyEoCi7051BN5HGN+z6p0tomJl2rHk0
        0orUygRoAp+k7dBVLuhZZhsnl6r6ke3Kr0i2u04/S9+CKApGogHYAb3ETKAF33XtrnI8Imuxbd00uX6m
        Fl/jf0/OGblYirv0AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="textBox7.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAKvSURBVDhPY/j//z8DLvz7z1/W779+y3z7+VsQlxqc
        mq89eqOS2Lu5J7pzfXlY69rehsUHfT9+/YGhHqsBQIVcQU2rFykmTFmllzGrFIh7FeImH5q44ZQWukuw
        GrDpxG0pJaBmk9y5IPzfNHfuebWkacurF+zzJ8qAr99/MeRO31GjmjR1v3HOnF9AF1zVzZi1ePPxW9w4
        Dfj77x/Dsv1XDIB+dVt+4KqzV+3yXYZZs2+bF8w/Y1+66IZ87KQLBdN3Zrz68JUd2RC4F37+/sPgWb18
        tnTUhL/qydOOVs3fH3Px3kt534aVuXN3XtCft+uiY0T7us1WhQs2H7j0kA9mCEoYlMzeU2FXsugS0Maa
        By8/MIIUBTevSbv19B04GoOaV4eJhfeftiycP//O83dgl8ANuHDvhYhe5qxzqw9ft1558Jrq6w9feb58
        /8nnXbfi4PYzd7X//v3HOG/neYMNx25KaaXOON2//oQ3igFTNp9J9KxdvuH5+y9wQ4GxIS0TPfFK3aKD
        TjAnAw1iiOvZVFq9YP9MFAMW7blUZZQ95xgw8Lyfv/sCdv7ivZeNeQO6fgEDthzE//TtJ0N89yY7nfSZ
        29tXHJ2OYsC8nRcqHcsW725edlj/wxdIijt+/YmcWd68NasOXfMA8YHJmgGUmNyqls1vWnZ4NooBc3ac
        d7Msmn/k09efnP8g+QPsiqdvPjGD6H/AaAbGFBOI7VO3sr9y/v42FAPef/nOCoyiPfE9m4pjujYE7rtw
        Xxw5voGamUvn7A7KmbYjQDttxuU1h69roBgA4uw5f98YmGTvKidO7T9y9TEnSGzd0RuWF+6+ZAMFXs7U
        HREcvu0vgQGYgTUdgAQPXX5k5V27fJt/w6pFkzedTrUpXriyduGBktDWtd3WRQsOAMMo8SVSTAEA9rWb
        LTIrnr4AAAAASUVORK5CYII=
</value>
  </data>
  <data name="textBox20.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAALESURBVDhPY/j//z8DNvz33z8Gl4olufJxk6/JxExc
        1LX6GC82dVg1gxRO3njay6duRVXzssO2UR0bkpzKF2e/eP+VA90QrAacvf1cUDd9ZpV9yaL6pL7Ne33r
        Vk5VTpjS3L/uhD5RBhy7/oTPrmRhoU7GzCrN1Bm/9DJnbdVImV7RtuKIJkEDfvz6w/Dn7z+Gklm7w4H+
        32iYPfuBZtqM8/pZsxquP3rDhtMAUKD1rjkR4ly+pM6lcmmpae7cFeFt67q9apbXpk3Y2mBeMH+hc8WS
        WXvO31dCNgQeBr///GVwKV+SIRHR/8+nbmXXxXsvJZ++/cwQ2b7ecsrmM9yX7r9kT+zbnKkUP/nAgUsP
        4V6BG/D52y8G14ql+d51K1rjeze5vPr4lQdk07O3n5nvPX/P8vvvX4aCGbsMgPJFloXzl9559o4LJA83
        YOWhq9I66TM3Xnn4WhxoM8e3H79YkJ0KCpeHLz9wvvv8ncG2eOGcnrXH/VAMmLblbKhnzfJekLNhGq8+
        fM1x7NoTaRj/9cevTMAwYJ2w4VRc+dy9rSgGLNh9McO6aMG02K4Nji8/fGUCSS7YfUlDOnrCRaCfo0H8
        d5++s0R1rPcxzpnT2bb8SBOKAbO3n48B+q/nyoNXnKAYAUl++PKDJXXi1hyZ6ImP9114EAv0FsPKQ9fE
        2lceTalZeKANxYB5uy6oARPL2vsvPnD/+v2X8c/fv4wgBV+BmlqWHra2LlxwCOhNj5fvvzJ4167orZq/
        PxbFgPdfvjPYlSyaEte9qSh32g7bx68/CSIH4o4zd6QcShfHZk3Z7q2aNHXTmiPXRVAMAHGAASSjGD95
        r2ft8mpgaIMzzpZTtxV//YG4Jm/aTk+x8L5ji/decoIZjpGZDl15pOhevWyqV+2KCZM3nQ4DZqiSnjUn
        YsNa19bbly5avOLgVTtklwEAnDmTwpRQ5VYAAAAASUVORK5CYII=
</value>
  </data>
  <data name="textBox23.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAMTSURBVDhPY/j//z8DOv75+w+DR83yfJnoib8koyZ8
        b112OASbOpAYhua///4x9K076erfuGpV7cKDmWVz9hZ5VC9fdfz6E2NshmAY8OPXH06booVL7EsXTYnv
        2XQgtmvjbq20Gaviuje2fvvxG0M9hsCfv//Yw1vXTdNMnT5FK3XGZ+30mR/VkqYtr5q/v5KgC379/gs2
        sGT2nlDlhKk39TJnvTDImv1FJmrClZblh81xGgAKtMYlh1J861cuzZu2s0s7bcYp54oli4Jb1y4K79yw
        wL9n+0qbqlVnimftTv/y/ScTskFgG3//+cuQPXV7jVh433+7kkV7Vhy4qv/k9SeGZXsvmSa3r3G9unAZ
        Q255h5147KS72dN2tX398QvudTADaCpDVMf6JofSxWsCm1YXLdxzSRok/njlOuVVUhqnz0jJziqx9Q9U
        T59QoBjTc7p7zYlomCvABmw/c1dFKXHqpe2n7yg/fv2JF4g5QeKfVqxQOCws8fywus7/Xar6N1faOQu1
        zNgQalm+7Mij1x/54OlgzeFriRop09Y9efOZGWby159/GJZ3TXfaJSn/8ria9v/D8ir/bxuZVuzecoTT
        snL5lWuP3hjBDdh4/GaOYvzk3cEta8oevvooCJJYd+wmp0Tc5MuxEdX/jwNdcE5R9f8qbePNwWWzmu2r
        Vt669eQtOGGBvbD+6I00daALTt16Kv737z9wKL/99I0RGLBRMrlLHsZH1P4/oqT5/7iece+xfSdl/No2
        XLn28LUpsgFG6snTzr/7/I0fGKVM33/+Bntl86YNDBpWXsrqZSs3ZTsmvjtvYa64bO1+S+OSJWdvP30n
        BjfgzcdvjMB4XwFMulNjuzemTN9yRhkk+fP7V4Y3zx4y3H/5gaVs1s7suPqF0VaF83eUzd1b8Q+YZ1Ay
        094L92WVEqZcdK9atub+8/dCIMnbz97Ln779QhHEPnXnhapi8owrfg2rVj9/95kdJRphnENXHsn71K1Y
        CHTNzvI5e4vzpu+cBEwXk6I7NtTZlSw8nDJhS9eL9194kFMiAGW0giScIu2SAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="textBox26.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAFDSURBVDhPY/DqPsSABTMBxRKAeBsQPwTiNiDmAqmz
        b9nHkL/oPMOvP38Z/v//z4BNM0gsBYgvA/EbIP4OxPeBeDYQqxBjgBZQ4RUg/o8FzwMawEvIBf5AjR9w
        GHAVaIA8JQbsARogQcgAY6DtN3C4YDrQAC5CBthBQx5bGCwHGsBPyABLoAEPcLhgGdAAAUIGgKKxCYj/
        oBnyHMi3JSYaQQZIA/F5NAN6gXwmYg0AGbIUyYB/QHY0KSkRZMBaNAOSiDWAA6gwA4hfo3nhNJBvCvQC
        Y8Hi8wz//v3DyAvcQAWBQLwRiL/iiIUnDq37J2TOP2v04sN3hj9//8EzkwZQwwog/o1DIzxNeALziHfP
        oVeuHQfKp++5IwDyqw4Q7yKkEVnes+vQf9eOg0B8oApkAChwNgPxWSA+SiwGuuKKR9fBPgCJCFD/Yf0y
        PgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="textBox29.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAGJSURBVDhPdZPLK8VBGIbFkZBOLqFsyELJJblEtsTC
        mkRhbSOX/8FCVjYWdrJ0SS5xWFhIoSQ2Lge5lA1KFqR43tM3mqZj8TQz73zfO7/55vulRCdiKUloRtuA
        H+OTcQ7Kw9gwuYiAKXiAQ1iFBTM7ZbyCMchyRr5BFWIcLqAXCoPTSlmPwAfEIE/7zkDB17AHBV6i9DrI
        9rRq5o+wBhFnMG2fl+sF9jA/h1c4gBZvr8G+ZEgGKtgTdHsBtcxfwBVR4xnkeDE6dF8Gus+xu5MFDAbJ
        zqg+OOReBtpc8TakDfxjoHq4a2cwv3QG84FBJevnwOSI9d/zMU+DExl8gSoa9kQXmpLUE9tQE8ToZeJK
        0umqdnESk0y0iiS68lrhVpMyuIFhC4wytsE4lJimZxuFJkg3TR266T5b7fkGM6AX+QYVdwnU3ne2fmfc
        hVlQR3Y6AxVn3YL8t9d/EYGtJHuT+hK/cPkIi16gGsldoT0wUHLiKmHlUxH7YQfUeY123z5G/WjL0GFa
        IvcXHdynrS+iNwcAAAAASUVORK5CYII=
</value>
  </data>
  <data name="textBox30.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAH5SURBVDhPY/DqPsQAxdlA+iUQ/8eGPYHiVo17/1s2
        7Plj27z3XGD/UcNvP/8wwDQXADWdAeImIE4E4gRkDNSc4NN7OKF+7ZX4pvVX07MXnJ0BNOxsy4ZrpiAD
        0oH4HBBrI7kGZjCY9ug6yBA84SjD////wfjLj98MRUsv5Ns179sPUvAKiBtxaYYZEAQ04PuvP3BDvv36
        wxjQf2QfyACQn1NJNeDH7z8MQENXwQwAeQPF2ch8dC+AvAF0AYoBsUANAUC8BIgnAzGIvxCIlwPxJGAg
        Ovv2Hl4ADMAFdWsuF647/YTpz99/KAZEAhXWAnEYNCZ6gbQKEK8CYmMgPgTEHvYt+2SMa3Ytb1h7NQbk
        CmQvgAwoA+JdQLwTiDWAmAdkOxBbAfEWmJeAhmS1b7pej80AkAtAiakOiPOAWAKI5wMxFxCvA3qjDxht
        eUbVu/YD04MlugFJQEXqQCwOxMxA7AR1gQHIZs+ugwL+fUdi9197mXLg+iv9G88+Mfz68xfFC2kwJ2Kj
        scUCKE0gh0EGIQPQE9K///8YgiceXQuK+5NAPIsYA37+/gtPideffhQG6jkGMsAQiEF5IR+I2XB5AeSC
        919/gpPzpUcfxHx6Dm9ybNs/F5b6zIEa9wLxHiBeAwp1bDhwwpF1QIPWefccOuIE1Lzp3FN+AKF6HfOg
        oKnxAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="textBox33.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAPCAYAAADtc08vAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAL9SURBVDhPY/DqPsSABasCxUI9uw4Je3QdYnjw5ivD
        ////sWJsmj2BmicDcTPQgElAA3RhBvwDGvLn7z+Gv//+wQ1DN0AFqHE5ECuDXAU0wNOz+9Cslx9/sH38
        9oth2bGHVpFTj1dvu/hMHOYidANcgBo7QZo9ug4C8SFhr+7D8VvOPxMuXHLeW69y536XjgMHztx7K4Bu
        gDhQEycQcwFt9XZuP1ju3nlook/v0V6f3iOtjm0HJkdMPTFz4eEHgdHTTsx88/knB7IBIUCNbUDM79l1
        0Nu759CS0mXnU3acf6KZOW1vrEXJsvSUKXtcZ++8zLT55F0BoKsmxUw70f7q0w9OkCEgLyyAGmDl2X14
        lVfHPsXGxQcYpm8+zWNZMG+revLUV/bF86sDGlYylM3dw/Dh22+GwiUXumJnnMwCBSjIAAEgrgLi0949
        h83d2vYwBDSu1MmZuj0jsGnNIvvSRfvdqpb2Ny45qNy34RznnAP3jXZdfqFm3bh3bcO6K1GwQFQDucS9
        8yBX9NRjDDnTdhT7N6yaO2XjaaXpW88aNy87bOdRvbSvdMFhB9PaPSv3XX2lcPb+O42QiccmwQzQABow
        HegFtqD+Q6JhbWv71hy+LrTmyHUdu+KFXXN3XmConLc3rHLBgZLjd94yAdMDI8j/M/be8YEZwAc0YD7Q
        AAX/3oPSvvUrFlTM2ydy+Moj+dxpO3Kmbz7DEN25IWLihpMNII0vPvxgyJx/Js22ed9S5HQQBwzh6UF9
        h7jTJ24pCm1dHT9/1wWGfefvMZTP3SPmUrGkecney+4gA5rXX0u3ati7YPP5p2ooCQloQE3oxCNFjUsO
        C3jWrqjJmLIjonL+fk/P2pUZ8T0b4/ZdesSQvfBcUkDfkQVn7r2TgEUjsiGh3t2HWvz7DjMETThS7dq+
        b5Vjy56plvW7F6468dDm9N03jLqVO9YtPHxfC1dStgaGxUSgS5qBeaDZs+ewhl/fEdPq1VecAiYcnRk0
        4Vhl1LQTfdeefuSDGQAALv0E+vx1PtkAAAAASUVORK5CYII=
</value>
  </data>
  <data name="textBox35.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAPCAYAAADtc08vAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAIISURBVDhPjdNLaBNBHMfx2V2zSWwtERERRfDoxQco
        KFqsVKFNjd4qgocKKh4FT70IWmvzqAgeBAs9iCLUBwhRUdA8NhVaIhjqIypNo42KQSu+WqtNu37/YYUQ
        Inr48J+ZzPxmZnejDvWnFVzQHWbFmFFjTO84P6yHolnTtm2ldvamhFZVdfoeZ8ygCunXB3pTetPJeMPF
        wVfm1x8zSvkj1hK0YT/2OVqpPuxBOzwwMU/Gm08lvF03nrmt5x+8EhDHFOwKc7Rv4iBGMYRlToh3W3fc
        E4xmfaXZOU0CCphGEAfwrSLoNu0AxvAYK6G4gu+ClTce5j+ZEjDuLJJjL0ex6jT36DfhKbJY1dyTUEcv
        Z3zjE5PlgNeQI793dqq8yp+2xW+yQaYtYuUau2KrB4YKqvhlWpeAW5is2rVWyDBz/Ehv70nkj117soYr
        lN/CCiek1qLqsQxzd7WErQec5F06N7FWAsRSyDH/J+Qci9dvPn7fPnLp0XVZrDkh8j3c/UfIW3mgLaFk
        KHA6VRp8+bFdAlxwOyGLqdG/hMjiLewe2dod+9UXy+0dLX4vPwMhX9lCp72IerUqJE9/E86ixGvc3Tkw
        okYKn03VGrZcqIeBBpiowxXYeION6MOUP2z5G0/EVH9yrO7nzKymuIsOqfPhhQE3FqAD66DhMDbI3B3B
        pPfMnRc6/wX3b0Q1Bg2+mJRhAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="textBox36.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAJWSURBVDhPY/j//z8DCH/58oXhw5efjO8/fGJ69+4d
        AwgD2Qwfv/4C8t8zovLfMf78+ROsD0yAcFpmDoNp9voEdcsAVykpKQYQBrJZnSv3NsnIKQpKSUkyqJp6
        MNiX7akVF+bj2LxxPaoBwWGRDDIRyyZxyRgnM0ABj4IVj07G1gMMjCwSDAyMDMp+ra4+dfvfSTqWFGw5
        eJERxQXBIcEMkiELWkVM46JhBvAr23DrZW3bxcjMJsbAwMIQ0LBv5ZsPX/5XLrr+ZNOp5zKoXmhaJudU
        vOliSPvpY8L6wUAbGRiQDBBnZGJlME5bEVcx9+wfi5x1i7afesSJYkDJ3Mtxu08//H/o+sf/Cr5tUcgG
        MLFyiIP4fCoOHKopm/cyMLJJrFm7FjUM8qceU/as3nMrqG7HKU4JbbAGmAtgBvArWXMBvbQTKCW2evVK
        VAPCohMYxEKX9HIo2CaghwHcAGiYAOXFV69ehWpAaHgUg1zUsh6gLXADeOVNWbXSt24DuR7iIltwoGI3
        ICIaZEAv0NmJYBcwMTOImsWrWxbuvCik4eLGwsnPwKdoQYIBjGwMXtV75l68/ep/9arn99kkjUV4pA1Y
        iXYBIxM7g2vFrpZ1Rx//j27fv5OZX46XV86UjQQDmBnEzOLFlJK3nWEVUdMmNgz64GEA1AEMRA69rK3b
        GRgZhZCidTfWQPTyC2QQCFgwgUHUIAUWjUA2t2Lixv1APjhlQvkHQPxly5aiRuOUaTMYCqcft8ooadGO
        i4tjAGEgm6l64cWAhKRkbiR+GJDNc+rUSbABAIYkiN5e/KVjAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="pictureBox5.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAgEAAAAAAAD/7gAOQWRvYmUAZAAAAAAB/9sAQwABAQEBAQEBAQEBAQEBAQEBAQEB
        AQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAgICAgICAgICAgIDAwMDAwMDAwMD/9sAQwEBAQEBAQECAQEC
        AgIBAgIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMD/8AAEQgA
        CgAKAwERAAIRAQMRAf/EABcAAAMBAAAAAAAAAAAAAAAAAAcICQr/xAArEAAABAMECgMAAAAAAAAAAAAD
        BAUGAjYHADM3CRFkFTVVZVZmCDgYWDn/xAAZAQABBQAAAAAAAAAAAAAAAAAIBQYHCQr/xAA0EQABAgIE
        CgkFAAAAAAAAAAACAQMEBQAzNQYRMRIiMmITNDYIIWGBklNjZFUHFWUXVzj/2gAMAwEAAhEDEQA/AFCz
        r15ffeZ95LOJSppUSmh9dVKaJhBnVESkcm6h4GvSRgMGFWKQNZfdjfV0JyqLYFNpJsgoGwjhEYGPTAJF
        GEGFPyY67FX4jXjYeYMybRAcRELNabbwpkkYqJKKqCiSooqi9C4UTTzyOS+XyDlYuxLYWaS2aQ8O1Gmc
        TBuOEwm3mEXF7Mlfah3m3WAfFuIbdZbJt0THOFBMtmfylqT9BvOb0m25I9ErngmP0/dqzTy6xJfXY32m
        a2ZlaDPdr6zyq3UpSj+KLs/sG4XHOy3qZ4/FsndPX7j6mgdzEP1Hyr/T2ZHljLi1dj7r1ToPvyybfDjq
        Q2dpnXVvZ1eB59Hpy4fyn8s8Z7rDWbZ+NNPW91+1UvJaVqABT//Z
</value>
  </data>
  <data name="pictureBox6.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAgEAAAAAAAD/7gAOQWRvYmUAZAAAAAAB/9sAQwABAQEBAQEBAQEBAQEBAQEBAQEB
        AQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAgICAgICAgICAgIDAwMDAwMDAwMD/9sAQwEBAQEBAQECAQEC
        AgIBAgIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMD/8AAEQgA
        CgAKAwERAAIRAQMRAf/EABYAAQEBAAAAAAAAAAAAAAAAAAoIC//EACcQAAADBgQHAAAAAAAAAAAAAAQF
        BgACAwc3CAETZAlUdBU1V7c5/8QAFQEBAQAAAAAAAAAAAAAAAAAACAn/xAAuEQACAQAIAQwDAAAAAAAA
        AAACAwEAEWIENAUGBxIhUXEiMlJyEzNTtDcUVAj/2gAMAwEAAhEDEQA/AKG3H79b3pT7jhwMGL0lRp3b
        kZlYJAoSXx0dn0rwyXWSZTqujlqhcOiZJilwOVhCcBYR9HFgA+OMeHlhMqEGCvujvX+ttY5Xr8zN4Jdl
        5RClqIiTAMADmC4hCWSYlEMkhjlioaoEZpNfeDdLcrId32MZel3e85OYwhCDM7tC2rW2RZxgqXS0DGHy
        QD1oqXwiATC2+r3i+PLaKVZtZJpVv4ChNKtX3fTMovN1Z7GXYb33Yjmw3o2u3Zo9fyNwv1MmwNeLvOM7
        uCw1r1bFBJ7030zub5uVHouWLD3d37EzHpT8dNJtf0b9z514rr8K7U0A2clKsU//2Q==
</value>
  </data>
  <data name="textBox39.Style.BackgroundImage.ImageData" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAHgSURBVDhPhZNNKINxHMf/SspJUi4O4uq0IhGKhLHl
        ZRdO5uAlCrmLjcSYl4tQiuJAtmnExbZmGxFL8lYuUg6kUF42e+zx/a3nrwcPO3z61fp/P33//+c3Vj68
        zSTaMG+BqIQav+caHGJO75aQ3+fwV4/5VK9BgfFwJ0IHwAgagF4OwnqN2aPvsZzUG22nzW1zh1OQHfav
        nmWRoBn4QYasDRdHZpnJzXTjPiaKYoTnQIh1LR51FPQ5XXTgDhj+CnNBDQRv78KX5PVdiKka8zpJQHdu
        jCaQN6AWgZDAIF3mgiZJEIupBrUgDWSCerXJ3Vg56tU5T28Tdy/v2Uc4zAJo85dgGKErSbCFOUMN8ZDT
        eUaHq2TQnRoMfTBCSUCNasCa1GgTs5hfD485jdBkNEEdAhQk2QaolAm0ENiiCej+FOQCrUzQDcH8f4IY
        HG4F+yAeuAA9cApoxwbulw65VUoC/hnjcJB2YglkgQ6wgAec05o9JoPtJN20fsFCAr7Cj0ds4TWV5s9N
        pD0Ii2Gmm/BZ6J570qf6tr5yEQloE6k6X+fzm6cknNmhkArQf4Hq0hV+ibjg4SUYWefj68dkzYjHXjjg
        muWHsxF0AFqcFWBVonrca0UTa8XItrcIYbv/JuET6BUeXsK1TF8AAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="sqlDataSource1.ParameterValues" xml:space="preserve">
    <value>{@iID:5}</value>
  </metadata>
</root>