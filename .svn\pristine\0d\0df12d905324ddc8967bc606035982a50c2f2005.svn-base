﻿using System;
using System.Collections.Generic;
using CP.Helper.Interface;
using log4net;
using log4net.Config;

namespace CP.Helper
{
    public class Log : ILogger
    {
        private readonly object _lock = new object();
        private readonly Dictionary<Type, ILog> _loggers = new Dictionary<Type, ILog>();
        private bool _logInitialized;

        public string SerializeException(Exception e)
        {
            return SerializeException(e, string.Empty);
        }

        /* Log a message object */

        public void Debug(object source, object message)
        {
            Debug(source.GetType(), message);
        }

        public void Debug(Type source, object message)
        {
            GetLogger(source).Debug(message);
        }

        public void Info(object source, object message)
        {
            Info(source.GetType(), message);
        }

        public void Info(Type source, object message)
        {
            GetLogger(source).Info(message);
        }

        public void Warn(object source, object message)
        {
            Warn(source.GetType(), message);
        }

        public void Warn(Type source, object message)
        {
            GetLogger(source).Warn(message);
        }

        public void Error(object source, object message)
        {
            Error(source.GetType(), message);
        }

        public void Error(Type source, object message)
        {
            GetLogger(source).Error(message);
        }

        public void Fatal(object source, object message)
        {
            Fatal(source.GetType(), message);
        }

        public void Fatal(Type source, object message)
        {
            GetLogger(source).Fatal(message);
        }

        /* Log a message object and exception */

        public void Debug(object source, object message, Exception exception)
        {
            Debug(source.GetType(), message, exception);
        }

        public void Debug(Type source, object message, Exception exception)
        {
            GetLogger(source).Debug(message, exception);
        }

        public void Info(object source, object message, Exception exception)
        {
            Info(source.GetType(), message, exception);
        }

        public void Info(Type source, object message, Exception exception)
        {
            GetLogger(source).Info(message, exception);
        }

        public void Warn(object source, object message, Exception exception)
        {
            Warn(source.GetType(), message, exception);
        }

        public void Warn(Type source, object message, Exception exception)
        {
            GetLogger(source).Warn(message, exception);
        }

        public void Error(object source, object message, Exception exception)
        {
            Error(source.GetType(), message, exception);
        }

        public void Error(Type source, object message, Exception exception)
        {
            GetLogger(source).Error(message, exception);
        }

        public void Fatal(object source, object message, Exception exception)
        {
            Fatal(source.GetType(), message, exception);
        }

        public void Fatal(Type source, object message, Exception exception)
        {
            GetLogger(source).Fatal(message, exception);
        }

        public void EnsureInitialized()
        {
            if (!_logInitialized)
            {
                Initialize();
                _logInitialized = true;
            }
        }

        private string SerializeException(Exception e, string exceptionMessage)
        {
            if (e == null)
            {
                return string.Empty;
            }
            exceptionMessage = string.Format(
                "{0}{1}{2}\n{3}",
                exceptionMessage,
                (exceptionMessage == string.Empty) ? string.Empty : "\n\n",
                e.Message,
                e.StackTrace);
            if (e.InnerException != null)
            {
                exceptionMessage = SerializeException(e.InnerException, exceptionMessage);
            }
            return exceptionMessage;
        }

        private ILog GetLogger(Type source)
        {
            lock (_lock)
            {
                if (_loggers.ContainsKey(source))
                {
                    return _loggers[source];
                }
                ILog logger = LogManager.GetLogger(source);
                _loggers.Add(source, logger);
                return logger;
            }
        }

        private void Initialize()
        {
            XmlConfigurator.Configure();
        }
    }
}