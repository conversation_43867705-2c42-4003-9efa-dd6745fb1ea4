﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System.Web;

namespace CP.UI.Controls
{
    public partial class SVCGlobalMirrorORMetroMirrorOverview : ReplicationControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;

        private DropDownList _ddlReplicationType = new DropDownList();
        string reptype = "";
        ReplicationType type;
        
        #region Properties

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion Properties

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvReplication.Items.Clear();
                lvReplication.DataSource = GetSVCGlobalMirrorList(txtsearchvalue.Text);
                lvReplication.DataBind();
            }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public IList<SVCGlobalMirrorORMetroMirror> GetSVCGlobalMirrorList(string searchvalue)
        {
            type= (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            var replicationlist = Facade.GetSVCGlobalMirrorsByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, type.ToString()); 
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count> 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        private IList<SVCGlobalMirrorORMetroMirror> GetSVCGlobalMirrorList()
        {
            //return Facade.GetSVCGlobalMirrorsByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, reptype);
            IList<SVCGlobalMirrorORMetroMirror> SVCGlobalMirrorList = new List<SVCGlobalMirrorORMetroMirror>();
            return SVCGlobalMirrorList = Facade.GetSVCGMReplicationByUserIdCompanyIdRoleAndReplicationFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag, reptype);

        }

        private void BindData()
        {
            setListViewPage();
            Session["CurrentPageSVCGlobalMirrorList"] = (dataPager1.StartRowIndex);
            Session["TotalPageRowsCountSVCGlobalMirror"] = dataPager1.TotalRowCount;
            lvReplication.Items.Clear();
            if (ReplicationType.SelectedValue == "44" || ReplicationType.SelectedValue == "43" || ReplicationType.SelectedValue == "53" || ReplicationType.SelectedValue == "55" || ReplicationType.SelectedValue == "61" || ReplicationType.SelectedValue == "96" || ReplicationType.SelectedValue == "97")
             {                  
                if (ReplicationType.SelectedValue == "44")
                {
                    reptype = ReplicationType.SelectedValue == "44"
                                  ? "SVCGlobalMirrorORMetroMirror"
                                  : ReplicationType.SelectedValue;

                }
                else if (ReplicationType.SelectedValue == "43")
                {
                    reptype = ReplicationType.SelectedValue == "43"
                                  ? "OracleFullDBSVCGlobalMirror"
                                  : ReplicationType.SelectedValue;
                }
                else if (ReplicationType.SelectedValue == "53")
                {
                    reptype = ReplicationType.SelectedValue == "53"
                                  ? "SVCMSSQLFullDB"
                                  : ReplicationType.SelectedValue;
                }
                else if (ReplicationType.SelectedValue == "55")
                {
                    reptype = ReplicationType.SelectedValue == "55"
                                  ? "VMWareWithSVC"
                                  : ReplicationType.SelectedValue;
                }
                else if (ReplicationType.SelectedValue == "61")
                {
                    reptype = ReplicationType.SelectedValue == "61"
                                  ? "SVCGlobalMirrorOracleFullDBRac"
                                  : ReplicationType.SelectedValue;
                }

                else if (ReplicationType.SelectedValue == "96")
                {
                    reptype = ReplicationType.SelectedValue == "96"
                                  ? "DB2FullDBSVC"
                                  : ReplicationType.SelectedValue;
                }

                else if (ReplicationType.SelectedValue == "97")
                {
                    reptype = ReplicationType.SelectedValue == "97"
                                  ? "PostgressFullDBSVC"
                                  : ReplicationType.SelectedValue;
                }
             }
            
          
            IList<SVCGlobalMirrorORMetroMirror> globalMirrorList = GetSVCGlobalMirrorList();
            lvReplication.DataSource = globalMirrorList;
            lvReplication.DataBind();
        }

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageSVCGlobalMirrorList"]) != -1) && Session["CurrentPageSVCGlobalMirrorList"] != null && (Convert.ToInt32(Session["CurrentPageSVCGlobalMirrorList"]) > 0))
            {
                if (Session["TotalPageRowsCountSVCGlobalMirror"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageSVCGlobalMirrorList"]) == Convert.ToInt32(Session["TotalPageRowsCountSVCGlobalMirror"]) - 1)
                    {
                        Session["CurrentPageSVCGlobalMirrorList"] = Convert.ToInt32(Session["CurrentPageSVCGlobalMirrorList"]) - dataPager2.MaximumRows;
                        Session["TotalPageRowsCountSVCGlobalMirror"] = null;
                    }
                }
                dataPager2.SetPageProperties(Convert.ToInt32(Session["CurrentPageSVCGlobalMirrorList"]), dataPager2.MaximumRows, true);
                Session["CurrentPageSVCGlobalMirrorList"] = -1;

            }

        }

        public override void PrepareView()
        {
            BindData();
           
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }


        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void lvReplication_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageSVCGlobalMirrorList"] = (dataPager2.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvReplication.Items[e.NewEditIndex].FindControl("ID")) as Label;

            var lblName = (lvReplication.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "SVCGlobalMirror", UserActionType.UpdateReplicationComponent,
                                  "The SVCGlobalMirror Replication component '" + lblName.Text +
                                  "' Opened as Editing Mode", LoggedInUserId);
            var lblRepType = (lvReplication.Items[e.NewEditIndex].FindControl("Rep_Type")) as Label;
            if (lblRepType.Text == "SVCGlobalMirrorORMetroMirror")
            {
                lblRepType.Text = "44";
            }
            else if (lblRepType.Text == "OracleFullDBSVCGlobalMirror")
            {
                lblRepType.Text = "43";
            }
            else if (lblRepType.Text == "SVCMSSQLFullDB")
            {
                lblRepType.Text = "53";
            }
            else if (lblRepType.Text == "VMWareWithSVC")
            {
                lblRepType.Text = "55";
            }
            else if (lblRepType.Text == "SVCGlobalMirrorOracleFullDBRac")
            {
                lblRepType.Text = "61";
            }
            else if (lblRepType.Text == "DB2FullDBSVC")
            {
                lblRepType.Text = "96";
            }

            else if (lblRepType.Text == "PostgressFullDBSVC")
            {
                lblRepType.Text = "97";
            }
            if (lbl1 != null && lblName != null && ValidateRequest("SVCGlobalMirrorOrMetro Edit", UserActionType.ReplicationList))
            {
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                //    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, lblRepType.Text);

                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, lblRepType.Text);
                Helper.Url.Redirect(secureUrl);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void lvReplication_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }

        protected void lvReplication_PreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                BindData();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void lvReplication_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                //DeleteSVCGlobalMirrorById
                Session["CurrentPageSVCGlobalMirrorList"] = (dataPager2.StartRowIndex);
                Session["TotalPageRowsCountSVCGlobalMirror"] = dataPager2.TotalRowCount;
                var lblId = lvReplication.Items[e.ItemIndex].FindControl("Id") as Label;
                var lblSVCID = lvReplication.Items[e.ItemIndex].FindControl("lblSVCID") as Label;
                var lblName = (lvReplication.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;

                var ReplicationDetail = Facade.GetReplicationBaseById(Convert.ToInt32(lblId.Text));
                var SiteDetail = Facade.GetSiteById(ReplicationDetail.SiteId);
                //if (lblSVCID!=null)
                //    Facade.DeleteSVCGlobalMirrorById(Convert.ToInt32(lblSVCID.Text));
                if (lblId != null && lblId.Text != null && lblName != null && SiteDetail != null && ValidateRequest("SVCGlobalMirrorOrMetro  Delete", UserActionType.ReplicationList))
                {
                    //var applicationDetailByReplicationId = Facade.GetApplicationGroupsByReplicationId(Convert.ToInt32(lblId.Text));
                    //if (applicationDetailByReplicationId != null)
                    //{
                    //    ErrorSuccessNotifier.AddSuccessMessage("The GlobalMirror Replication component is in use");
                    //}
                    //else
                    //{
                    if (SiteDetail.IsActive == 1)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The ExchangeOverview Replication component is in use");
                    }
                    else
                    {
                        Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "SVCGlobalMirror", UserActionType.DeleteReplicationComponent,
                                             "The SVCGlobalMirror Replication component '" + lblName.Text +
                                             "' was deleted from the replication component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("SVCGlobalMirror Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                    }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                Helper.Url.Redirect(secureUrl);
            }
        }
    }
}