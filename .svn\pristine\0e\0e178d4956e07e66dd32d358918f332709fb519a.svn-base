﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DB2DataSyncMonitor", Namespace = "http://www.BCMS.com/types")]
    public class DB2DataSyncMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public string PR_Ip
        {
            get;
            set;
        }

        [DataMember]
        public string DR_Ip
        {
            get;
            set;
        }

        [DataMember]
        public string PR_DatabaseInstance
        {
            get;
            set;
        }

        [DataMember]
        public string DR_DatabaseInstance
        {
            get;
            set;
        }

        [DataMember]
        public string PR_DatabaseStatus
        {
            get;
            set;
        }

        [DataMember]
        public string DR_DatabaseStatus
        {
            get;
            set;
        }

        [DataMember]
        public string PR_LogFile
        {
            get;
            set;
        }

        [DataMember]
        public string DR_LogFile
        {
            get;
            set;
        }

        [DataMember]
        public string PR_CurrentLSN
        {
            get;
            set;
        }

        [DataMember]
        public string DR_CurrentLSN
        {
            get;
            set;
        }

        [DataMember]
        public string PR_LSN
        {
            get;
            set;
        }

        [DataMember]
        public string DR_LSN
        {
            get;
            set;
        }

        [DataMember]
        public string PR_Timestamp
        {
            get;
            set;
        }

        [DataMember]
        public string DR_Timestamp
        {
            get;
            set;
        }

        [DataMember]
        public string Datalag
        {
            get;
            set;
        }

        [DataMember]
        public string LogGap
        {
            get;
            set;
        }

        #endregion Properties
    }
}