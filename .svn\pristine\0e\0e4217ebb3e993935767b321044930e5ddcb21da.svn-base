﻿using CP.Common.Shared;
using <PERSON><PERSON>Helper;

namespace CP.UI
{
    public abstract class ApplicationGroupBasePage : BasePage
    {
        private int _applicationGroupId = 0;

        #region

        protected int CurrentApplicationGroupId
        {
            get
            {
                if (_applicationGroupId == 0 && Helper.Url.SecureUrl[Constants.UrlConstants.Params.ApplicationGroupId].IsNotNullOrEmpty())
                {
                    _applicationGroupId = Helper.Url.SecureUrl[Constants.UrlConstants.Params.ApplicationGroupId].ToInteger();
                }
                return _applicationGroupId;
            }
            set
            {
                _applicationGroupId = value;
            }
        }

        #endregion
    }
}