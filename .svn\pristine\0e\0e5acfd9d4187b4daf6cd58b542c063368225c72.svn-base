﻿using System;
using System.Data;
using System.Data.Common;
using System.Collections.Generic;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class BusinessFunctionBIADetailsDataAccess : BaseDataAccess, IBusinessFunctionBIADetailsDataAccess
    {
        #region Constructors

        public BusinessFunctionBIADetailsDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<BusinessFunctionBIADetails> CreateEntityBuilder<BusinessFunctionBIADetails>()
        {
            return (new BusinessFunctionBIADetailsBuilder()) as IEntityBuilder<BusinessFunctionBIADetails>;
        }

        #endregion

        #region  Methods

        /// <summary>
        /// Create <see cref="BusinessFunctionBIADetails" /> into BusinessFunctionBIADetails table.
        /// </summary>
        /// <param name="BusinessFunctionBIADetails">BusinessFunctionBIADetails</param>
        /// <returns>BusinessFunctionBIADetails</returns>
        /// <author><PERSON><PERSON><PERSON> Thakker</author>
        BusinessFunctionBIADetails IBusinessFunctionBIADetailsDataAccess.Add(BusinessFunctionBIADetails businessFunctionBIADetails)
        {
            try
            {
                const string sp = "BFBIADetails_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iRelationID", DbType.Int32, businessFunctionBIADetails.RelationID);
                    Database.AddInParameter(cmd, Dbstring+"ibusinessfunctionID", DbType.Int32, businessFunctionBIADetails.BusinessFunctionID);
                    Database.AddInParameter(cmd, Dbstring+"iInBound", DbType.Int32, businessFunctionBIADetails.InBound);
                    Database.AddInParameter(cmd, Dbstring+"iDataSource", DbType.String, businessFunctionBIADetails.DataSource);
                    Database.AddInParameter(cmd, Dbstring+"iIsRealTime", DbType.Int32, businessFunctionBIADetails.IsRealTime);
                    Database.AddInParameter(cmd, Dbstring+"iAreaofFocus", DbType.String, businessFunctionBIADetails.AreaofFocus);
                    Database.AddInParameter(cmd, Dbstring+"iCriticalForBCM", DbType.Int32, businessFunctionBIADetails.CriticalForBCM);
                    Database.AddInParameter(cmd, Dbstring+"iBCMScope", DbType.Int32, businessFunctionBIADetails.BCMScope);
                    Database.AddInParameter(cmd, Dbstring+"iSupported", DbType.Int32, businessFunctionBIADetails.Supported);
                    Database.AddInParameter(cmd, Dbstring+"iRTO", DbType.Int32, businessFunctionBIADetails.RTO);
                    Database.AddInParameter(cmd, Dbstring+"iIsCritical", DbType.Int32, businessFunctionBIADetails.IsCritical);
                    Database.AddInParameter(cmd, Dbstring+"iIsDRRequired", DbType.Int32, businessFunctionBIADetails.IsDRRequired);
                    Database.AddInParameter(cmd, Dbstring+"iInBondAppID", DbType.Int32, businessFunctionBIADetails.InBondAppID);
                    Database.AddInParameter(cmd, Dbstring+"iOutBondAppID", DbType.Int32, businessFunctionBIADetails.OutBondAppID);
                    Database.AddInParameter(cmd, Dbstring+"iRelTypeID", DbType.Int32, businessFunctionBIADetails.RelTypeID);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, businessFunctionBIADetails.CreatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_BusinessFunctionBIADetails"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        businessFunctionBIADetails = reader.Read() ? CreateEntityBuilder<BusinessFunctionBIADetails>().BuildEntity(reader, businessFunctionBIADetails) : null;
                    }

                    if (businessFunctionBIADetails == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("businessFunctionBIADetails already exists. Please specify another businessFunctionBIADetails.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this businessFunctionBIADetails.");
                                }
                        }
                    }

                    return businessFunctionBIADetails;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Executing Function Signature IBusinessFunctionBIADetailsDataAccess.Add"
                     , exc);
            }
        }

        /// <summary>
        ///     Update <see cref="BusinessFunctionBIADetails" /> into BusinessFunctionBIADetails table.
        /// </summary>
        /// <param name="BusinessFunctionBIADetails">BusinessFunctionBIADetails</param>
        /// <returns>BusinessFunctionBIADetails</returns>
        /// <author>Kuntesh Thakker</author>
        BusinessFunctionBIADetails IBusinessFunctionBIADetailsDataAccess.Update(BusinessFunctionBIADetails businessFunctionBIADetails)
        {
            try
            {
                const string sp = "BFBIADetails_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iID", DbType.Int32, businessFunctionBIADetails.Id);
                    Database.AddInParameter(cmd, Dbstring+"iRelationID", DbType.Int32, businessFunctionBIADetails.RelationID);
                    Database.AddInParameter(cmd, Dbstring+"ibusinessfunctionID", DbType.Int32, businessFunctionBIADetails.BusinessFunctionID);
                    Database.AddInParameter(cmd, Dbstring+"iInBound", DbType.Int32, businessFunctionBIADetails.InBound);
                    Database.AddInParameter(cmd, Dbstring+"iDataSource", DbType.String, businessFunctionBIADetails.DataSource);
                    Database.AddInParameter(cmd, Dbstring+"iIsRealTime", DbType.Int32, businessFunctionBIADetails.InBound);
                    Database.AddInParameter(cmd, Dbstring+"iAreaofFocus", DbType.String, businessFunctionBIADetails.AreaofFocus);
                    Database.AddInParameter(cmd, Dbstring+"iCriticalForBCM", DbType.Int32, businessFunctionBIADetails.CriticalForBCM);
                    Database.AddInParameter(cmd, Dbstring+"iBCMScope", DbType.Int32, businessFunctionBIADetails.BCMScope);
                    Database.AddInParameter(cmd, Dbstring+"iSupported", DbType.Int32, businessFunctionBIADetails.Supported);
                    Database.AddInParameter(cmd, Dbstring+"iRTO", DbType.Int32, businessFunctionBIADetails.RTO);
                    Database.AddInParameter(cmd, Dbstring+"iIsCritical", DbType.Int32, businessFunctionBIADetails.IsCritical);
                    Database.AddInParameter(cmd, Dbstring+"iIsDRRequired", DbType.Int32, businessFunctionBIADetails.IsDRRequired);
                    Database.AddInParameter(cmd, Dbstring+"iInBondAppID", DbType.Int32, businessFunctionBIADetails.InBondAppID);
                    Database.AddInParameter(cmd, Dbstring+"iOutBondAppID", DbType.Int32, businessFunctionBIADetails.OutBondAppID);
                    Database.AddInParameter(cmd, Dbstring+"iRelTypeID", DbType.Int32, businessFunctionBIADetails.RelTypeID);
                    Database.AddInParameter(cmd, Dbstring+"iUpdatorId", DbType.Int32, businessFunctionBIADetails.UpdatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_BusinessFunctionBIADetails"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        businessFunctionBIADetails = reader.Read() ? CreateEntityBuilder<BusinessFunctionBIADetails>().BuildEntity(reader, businessFunctionBIADetails) : null;
                    }

                    if (businessFunctionBIADetails == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("BusinessFunctionBIADetails already exists. Please specify another BusinessFunctionBIADetails.");
                                }
                        }
                    }

                    return businessFunctionBIADetails;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Executing Function Signature IBusinessFunctionBIADetailsDataAccess.Update"
                     , exc);
            }

        }

        /// <summary>
        ///     DeleteById <see cref="BusinessFunctionBIADetails" /> into BusinessFunctionBIADetails table.
        /// </summary>       
        /// <returns>bool </returns>
        /// <author>Kuntesh Thakker</author>
        bool IBusinessFunctionBIADetailsDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 0)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "BFBIADetails_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int64, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    if (returnCode == -1)
                        return true;
                    else
                        return false;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation, ExceptionManager.CommonMessage.UserAlertMessageDeletedata, "Error In DAL While Deleting IBusinessFunctionBIADetailsDataAccess Entry ", ex);
            }
        }

        /// <summary>
        ///     GetByBusinessfunctionId <see cref="BusinessFunctionBIADetails" /> into BusinessFunctionBIADetails table.
        /// </summary>
        /// <param name="businessfunctionId">businessfunctionId</param>
        /// <returns>IList<BusinessFunctionBIADetails></returns>
        /// <author>Kuntesh Thakker</author>
        IList<BusinessFunctionBIADetails> IBusinessFunctionBIADetailsDataAccess.GetByBusinessfunctionId(int businessfunctionId)
        {
            try
            {
                if (businessfunctionId < 0)
                {
                    throw new ArgumentNullException("businessfunctionId");
                }

                const string sp = "BFBIADetails_GetBybusifuncId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iBusinessFunctionId", DbType.Int32, businessfunctionId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_BusinessFunctionBIADetails"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessFunctionBIADetails>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessFunctionBIADetailsDataAccess.GetByBusinessfunctionId(" + businessfunctionId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BusinessFunctionBIADetails> IBusinessFunctionBIADetailsDataAccess.GetByFuncidandImpactedid(int functionid, int impactedid)
        {
            try
            {
                if (functionid < 0)
                {
                    throw new ArgumentNullException("functionid");
                }
                if (impactedid < 0)
                {
                    throw new ArgumentNullException("impactedid");
                }

                const string sp = "BFBIADETAILS_GETBYFUNCNIMPCTID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"ifunctionid", DbType.Int32, functionid);
                    Database.AddInParameter(cmd, Dbstring+"iimpactedid", DbType.Int32, impactedid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_BusinessFunctionBIADetails"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildBusinessFunctionBIADetailsRelations(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessFunctionBIADetailsDataAccess.GetAllBussinessFunctionRelations", exc);
            }
        }

        private IList<BusinessFunctionBIADetails> BuildBusinessFunctionBIADetailsRelations(IDataReader reader)
        {
            var lstBusinessFunctionBIADetails = new List<BusinessFunctionBIADetails>();
            while (reader.Read())
            {
                var _businessFunctionBIADetails = new BusinessFunctionBIADetails();
                _businessFunctionBIADetails.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                _businessFunctionBIADetails.ParentAppID = Convert.IsDBNull(reader["ParentAppID"]) ? 0 : Convert.ToInt32(reader["ParentAppID"]);
                _businessFunctionBIADetails.ParentAppName = Convert.IsDBNull(reader["ParentAppName"]) ? string.Empty : Convert.ToString(reader["ParentAppName"]);
                _businessFunctionBIADetails.ChildAppID = Convert.IsDBNull(reader["ChildAppID"]) ? 0 : Convert.ToInt32(reader["ChildAppID"]);
                _businessFunctionBIADetails.ChildAppName = Convert.IsDBNull(reader["ChildAppName"]) ? string.Empty : Convert.ToString(reader["ChildAppName"]);
                _businessFunctionBIADetails.RelTypeID = Convert.IsDBNull(reader["RelTypeID"]) ? 0 : Convert.ToInt32(reader["RelTypeID"]);
                _businessFunctionBIADetails.RelTypeValue = Convert.IsDBNull(reader["RelTypeValue"]) ? string.Empty : Convert.ToString(reader["RelTypeValue"]);
                _businessFunctionBIADetails.RelTypeDescription = Convert.IsDBNull(reader["RelTypeDescription"]) ? string.Empty : Convert.ToString(reader["RelTypeDescription"]);
                _businessFunctionBIADetails.RTO = Convert.IsDBNull(reader["RTO"]) ? 0 : Convert.ToInt32(reader["RTO"]);
                lstBusinessFunctionBIADetails.Add(_businessFunctionBIADetails);
            }
            return (lstBusinessFunctionBIADetails.Count > 0) ? lstBusinessFunctionBIADetails : null;
        }


        /// <summary>
        ///     GetByParentAndChildAppID <see cref="BusinessFunctionBIADetails" /> into BusinessFunctionBIADetails table.
        /// </summary>
        /// <param name="parentappid">parentappid</param>
        ///  <param name="Childappid">Childappid</param>
        /// <returns>IList<BusinessFunctionBIADetails></returns>
        /// <author>Kuntesh Thakker</author>
        IList<BusinessFunctionBIADetails> IBusinessFunctionBIADetailsDataAccess.GetByParentAndChildFunctionID(int parentfunctionid, int Childfunctionid)
        {
            try
            {
                if (parentfunctionid < 0)
                {
                    throw new ArgumentNullException("parentfunctionid");
                }
                if (Childfunctionid < 0)
                {
                    throw new ArgumentNullException("Childfunctionid");
                }

                const string sp = "BFBIADetails_ByPrntnChildFunID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInBondAppID", DbType.Int32, parentfunctionid);
                    Database.AddInParameter(cmd, Dbstring+"iOutBondAppID", DbType.Int32, Childfunctionid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_BusinessFunctionBIADetails"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessFunctionBIADetails>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessFunctionBIADetailsDataAccess.GetByParentAndChildAppID(" + parentfunctionid +
                    "), ( " + Childfunctionid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     check <see cref="BusinessFunctionBIADetails" />BusinessFunctionBIADetails is Exist.
        /// </summary>
        /// <param name="id">name</param>
        /// <returns>True if name exist otherwise false</returns>
        /// <author>Kuntesh Thakker</author>
        bool IBusinessFunctionBIADetailsDataAccess.IsExistbyID(int id)
        {
            try
            {
                if (id < 0)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "BFBIADetails_IsExistByID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring+"iid", DbType.AnsiString, id);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this Application.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessFunctionBIADetailsDataAccess.IsExistbyID (" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        #endregion
    }
}
