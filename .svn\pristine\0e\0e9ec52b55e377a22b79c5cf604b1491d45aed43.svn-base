﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;


namespace CP.UI
{
    public partial class SingleSignOnList : SingleSignOnBasePage
    {
        #region variable
        
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.SingleSignOnConfiguration;
        public SingleSignOnType type;
        #endregion

        #region Properties

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin)
                {
                    return Constants.UrlConstants.Urls.Component.SingleSignOnList;
                }
                return string.Empty;
            }
        }

        #endregion

        #region Methods

        public override void PrepareView()
        {

            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }

            Utility.SelectMenu(Master, "Module3");
            //  Utility.PopulateSingleSignOnType(ddlSignOnType, false);
            //BindList();
            EnumHelper.PopulateEnumDescriptionIntoList(ddlSignOnType, typeof(SingleSignOnType), "All");
            if (Request.QueryString["param"] != null)
            {
                BindReplicationType();
            }
            ddlSignOnType_SelectedIndexChanged(null, null);

        }
        private void BindReplicationType()
        {
            SingleSignOnType eVal = (SingleSignOnType)Enum.Parse(typeof(SingleSignOnType), CurrentRepliType);
            switch (eVal)
            {
                case SingleSignOnType.CyberArk:
                    ddlSignOnType.SelectedValue = "2";
                    break;

                case SingleSignOnType.TPAM:
                    ddlSignOnType.SelectedValue = "1";
                    break;
                case SingleSignOnType.Arcos:
                    ddlSignOnType.SelectedValue = "3";
                    break;
            }
        }

        private void BindList()
        {
            setListViewPage();

            lvSinglesignon.DataSource = Facade.GetAllSinglesignOn();
            lvSinglesignon.DataBind();

        }



        /// <summary>
        /// if deleting or updating the List of Server, the page will get postback and then Listview switch to the same page
        /// where it is deleted or updated. - Goraknath Khule.
        /// </summary>
        private void setListViewPage()
        {
            if (Session["SeleIndSignOnType"] != null && Convert.ToInt32(Session["ClickedOnEdit"]) == 1)
            {
                ddlSignOnType.SelectedIndex = Convert.ToInt32(Session["SeleIndSignOnType"]);

                Session["ClickedOnEdit"] = -1;
            }
        }

        /// <summary>
        /// To get IPAddress in Decrypted format.
        /// </summary>
        /// <param name="type"></param>
        /// <returns>string</returns>
        public string GetIP(object type)
        {
            return CryptographyHelper.Md5Decrypt(type.ToString());
        }

        /// <summary>
        /// Get Single SignOn type as per SSOTypeID
        /// </summary>
        /// <param name="type"></param>
        /// <returns>string</returns>
        protected string GetType(object type)
        {
            int typeid = Convert.ToInt32(type);
            string stringtype = string.Empty;
            switch (typeid)
            {
                case 1:
                    stringtype = "TPAM";
                    break;

                case 2:
                    stringtype = "CyberArk";
                    break;
                case 3:
                    stringtype = "Arcos";
                    break;

            }
            return stringtype;
        }

        /// <summary>
        /// To get search result.
        /// </summary>
        /// <param name="value"></param>
        /// <returns>IList<SSOConfiguration></returns>
        private IList<SSOConfiguration> GetSingleSignOnBySearch(string value)
        {

            var SingleSignOnList = Facade.GetAllSinglesignOn();
            value = value.Trim();
            if (!string.IsNullOrEmpty(value) || SingleSignOnList != null)
            {
                var result = (from sso in SingleSignOnList
                              where sso.ProfileName.ToLower().Contains(value.ToLower()) || sso.IpAddress.Contains(CryptographyHelper.Md5Encrypt(value))
                              select sso).ToList();
                return result;

            }
            return null;
        }

        #endregion

        #region Events

        #region Dropdown events
        protected void ddlSignOnType_SelectedIndexChanged(object sender, EventArgs e)
        {
            setListViewPage();

            type = (SingleSignOnType)Enum.Parse(typeof(SingleSignOnType), ddlSignOnType.SelectedValue);
            if (type == 0)
            {
                BindList();
            }
            else
            {                
                IList<SSOConfiguration> SingleSignonList = Facade.GetSingleSignonBySSOTypeid(Convert.ToInt32(ddlSignOnType.SelectedValue));
                if (SingleSignonList != null)
                {

                    lvSinglesignon.DataSource = SingleSignonList;
                    lvSinglesignon.DataBind();
                }
                else
                {
                    lvSinglesignon.DataSource = null;
                    lvSinglesignon.DataBind();
                }
            }

            #region commented
            //switch (type)
            //{
            //    case SingleSignOnType.CyberArk:
            //        IList<SSOConfiguration> SingleSignonList = Facade.GetSingleSignonBySSOTypeid(Convert.ToInt32(ddlSignOnType.SelectedValue));
            //        if (SingleSignonList != null)
            //        {

            //            lvSinglesignon.DataSource = SingleSignonList;
            //            lvSinglesignon.DataBind();
            //        }
            //        else
            //        {
            //            lvSinglesignon.DataSource = null;
            //            lvSinglesignon.DataBind();
            //        }
            //        break;

            //    case SingleSignOnType.TPAM:
            //        IList<SSOConfiguration> SingleSignonList1 = Facade.GetSingleSignonBySSOTypeid(Convert.ToInt32(ddlSignOnType.SelectedValue));
            //        if (SingleSignonList1 != null)
            //        {

            //            lvSinglesignon.DataSource = SingleSignonList1;
            //            lvSinglesignon.DataBind();
            //        }
            //        else
            //        {
            //            lvSinglesignon.DataSource = null;
            //            lvSinglesignon.DataBind();
            //        }
            //        break;

            //    case SingleSignOnType.Arcos:
            //        IList<SSOConfiguration> SingleSignonList2 = Facade.GetSingleSignonBySSOTypeid(Convert.ToInt32(ddlSignOnType.SelectedValue));
            //        if (SingleSignonList2 != null)
            //        {

            //            lvSinglesignon.DataSource = SingleSignonList2;
            //            lvSinglesignon.DataBind();
            //        }
            //        else
            //        {
            //            lvSinglesignon.DataSource = null;
            //            lvSinglesignon.DataBind();
            //        }
            //        break;
            //}
            #endregion commented

        }

        #endregion

        #region ListView Events
        protected void lvSinglesignon_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageSingleSignOnList"] = (dataPager1.StartRowIndex);
            Session["SeleIndSignOnType"] = ddlSignOnType.SelectedIndex;
            Session["ClickedOnEdit"] = 1;
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvSinglesignon.Items[e.NewEditIndex].FindControl("ID")) as Label;
            if (lbl1 != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.SingleSignOnId, lbl1.Text);
            }

            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvSinglesignon_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageSingleSignOnList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCount"] = dataPager1.TotalRowCount;
                var lbl = (lvSinglesignon.Items[e.ItemIndex].FindControl("Id")) as Label;
                var lblName = (lvSinglesignon.Items[e.ItemIndex].FindControl("Db_Hostname")) as Label;

                Facade.DeleteSingleSignOnById(Convert.ToInt32(lbl.Text));

                ActivityLogger.AddLog(LoggedInUserName, "Single SignOn", UserActionType.DeleteServerComponent, "The Single SignOn '" + lblName.Text + "' was deleted", LoggedInUserId);

                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Single SignOn" + " " + '"' + lblName.Text + '"', TransactionType.Delete));

            }


            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void lvSinglesignon_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected void lvSinglesignon_PreRender(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                if (String.IsNullOrEmpty(txtsearchvalue.Text))
                {
                    //lvSinglesignon.DataSource = Facade.GetAllSinglesignOn();
                    //lvSinglesignon.DataBind();
                    ddlSignOnType_SelectedIndexChanged(null, null);

                }
                else
                {
                    lvSinglesignon.DataSource = GetSingleSignOnBySearch(txtsearchvalue.Text);
                    lvSinglesignon.DataBind();
                }

            }
        }

        protected void lvSinglesignon_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ImgEdit") as ImageButton;
            var delete = e.Item.FindControl("ImgDelete") as ImageButton;


            if (IsUserOperator)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }

        #endregion

        #region Button Events
        protected void btnSearch_Click(object sender, EventArgs e)
        {
            lvSinglesignon.DataSource = GetSingleSignOnBySearch(txtsearchvalue.Text.ToString());
            lvSinglesignon.DataBind();
        }
        #endregion

        #region TextBox Events
        protected void txtsearchvalue_TextChanged(object sender, EventArgs e)
        {
            if (txtsearchvalue.Text == string.Empty || txtsearchvalue.Text.Length <= 0)
            {
                ddlSignOnType_SelectedIndexChanged(null, null);
            }
        }
        #endregion

        #endregion
    
    }
}