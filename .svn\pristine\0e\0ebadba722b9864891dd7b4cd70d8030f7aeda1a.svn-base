﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.DataAccess;
namespace CP.DataAccess.WFProfileApprovalLevelProcesses
{
    class WFProfileApprovalLevelProcessDataAccess : BaseDataAccess, IApprovalLevelProcessDataAccess
    {
        #region Constructors

        public WFProfileApprovalLevelProcessDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<WFProfileApprovalLevelProcessDataAccess> CreateEntityBuilder<WFProfileApprovalLevelProcessDataAccess>()
        {
            return (new WFProfileApprovalLevelProcessBuilder()) as IEntityBuilder<WFProfileApprovalLevelProcessDataAccess>;
        }

        #endregion Constructors

        AppLevelProcess IApprovalLevelProcessDataAccess.AddApprovalLevel(Common.DatabaseEntity.AppLevelProcess approvalProcess)
        {
            try
            {
                const string sp = "ApproLevelProcesCreate";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, approvalProcess.ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalProcessId", DbType.Int32, approvalProcess.ApprovalProcessId);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalLevel", DbType.String, approvalProcess.ApprovalLevel);
                    //Database.AddInParameter(cmd, Dbstring + "iLevelNumber", DbType.Int32, approvalProcess.LevelNumber);
                    Database.AddInParameter(cmd, Dbstring + "iApprover", DbType.String, approvalProcess.Approver);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalState", DbType.String, approvalProcess.ApprovalState);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalBy", DbType.String, approvalProcess.ApprovalBy);
                    Database.AddInParameter(cmd, Dbstring + "iApproverEmail", DbType.String, approvalProcess.ApproverEmail);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.String, approvalProcess.Message);
                    Database.AddInParameter(cmd, Dbstring + "iNote", DbType.String, approvalProcess.Note);
                    Database.AddInParameter(cmd, Dbstring + "iIsApprovalReceived", DbType.Int32, approvalProcess.IsApprovalReceived);
                    Database.AddInParameter(cmd, Dbstring + "iIsApprove", DbType.Int32, approvalProcess.IsApprove);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, approvalProcess.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, approvalProcess.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iReason", DbType.String, approvalProcess.Reason);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    // change  CreateEntityBuilder
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        approvalProcess = reader.Read() ? CreateEntityBuilder<AppLevelProcess>().BuildEntity(reader, approvalProcess) : null;
                    }

                }
                return approvalProcess;
            }

            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting ApprovalLevelProcess Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        bool IApprovalLevelProcessDataAccess.Update(AppLevelProcess approvalProcess)
        {
            try
            {
                const string sp = "ApproLevelProcesUpdate";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, approvalProcess.Id);
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, approvalProcess.ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalProcessId", DbType.Int32, approvalProcess.ApprovalProcessId);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalLevel", DbType.String, approvalProcess.ApprovalLevel);
                    //Database.AddInParameter(cmd, Dbstring + "iLevelNumber", DbType.Int32, approvalProcess.LevelNumber);
                    Database.AddInParameter(cmd, Dbstring + "iApprover", DbType.String, approvalProcess.Approver);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalState", DbType.String, approvalProcess.ApprovalState);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalBy", DbType.String, approvalProcess.ApprovalBy);
                    Database.AddInParameter(cmd, Dbstring + "iApproverEmail", DbType.String, approvalProcess.ApproverEmail);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.String, approvalProcess.Message);
                    Database.AddInParameter(cmd, Dbstring + "iNote", DbType.String, approvalProcess.Note);
                    Database.AddInParameter(cmd, Dbstring + "iIsApprovalReceived", DbType.Int32, approvalProcess.IsApprovalReceived);
                    Database.AddInParameter(cmd, Dbstring + "iIsApprove", DbType.Int32, approvalProcess.IsApprove);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, approvalProcess.UpdatorId);
                    //Database.AddInParameter(cmd, Dbstring + "iVerifiedDate", DbType.DateTime, approvalProcess.VerifiedDate);

                    int value = Database.ExecuteNonQuery(cmd);

#if ORACLE || MSSQL
                    return value < 0;
#endif
                    return value > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating Approval Level Process Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        AppLevelProcess IApprovalLevelProcessDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "ApproLevelProcesGetbyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<AppLevelProcess>()).BuildEntity(reader, new AppLevelProcess());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalLevelProcess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IApprovalLevelProcessDataAccess.DeleteById(int id)
        {
            try
            {
                const string sp = "ApproLevelProcesDelete";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

#if ORACLE || MSSQL
                    return returnCode < 0;
#endif

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalLevelProcessDataAccess.DeleteById()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<AppLevelProcess> IApprovalLevelProcessDataAccess.GetByApprovalProcessLevelByApId(int apId)
        {
            IList<AppLevelProcess> WfProfileLevelApprovals = new List<AppLevelProcess>();
            try
            {

                const string sp = "ApproLevelProcesGetbyAPId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iApprovalProcessId", DbType.Int32, apId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var wfpfapproval = new AppLevelProcess
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]),
                                ApprovalProcessId = Convert.IsDBNull(reader["ApprovalProcessId"]) ? 0 : Convert.ToInt32(reader["ApprovalProcessId"]),
                                ApprovalLevel = Convert.IsDBNull(reader["ApprovalLevel"]) ? string.Empty : Convert.ToString(reader["ApprovalLevel"]),
                                Approver = Convert.IsDBNull(reader["Approver"]) ? string.Empty : Convert.ToString(reader["Approver"]),
                                ApproverEmail = Convert.IsDBNull(reader["APPROVEREMAIL"]) ? string.Empty : Convert.ToString(reader["APPROVEREMAIL"]),
                                ApprovalState = Convert.IsDBNull(reader["ApprovalState"]) ? string.Empty : Convert.ToString(reader["ApprovalState"]),
                                ApprovalBy = Convert.IsDBNull(reader["ApprovalBy"]) ? string.Empty : Convert.ToString(reader["ApprovalBy"]),
                                Message = Convert.IsDBNull(reader["Message"]) ? string.Empty : Convert.ToString(reader["Message"]),
                                Note = Convert.IsDBNull(reader["Note"]) ? string.Empty : Convert.ToString(reader["Note"]),
                                IsApprovalReceived = Convert.IsDBNull(reader["IsApprovalReceived"]) ? false : Convert.ToBoolean(reader["IsApprovalReceived"]),
                                IsApprove = Convert.IsDBNull(reader["IsApprove"]) ? false : Convert.ToBoolean(reader["IsApprove"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]),
                                UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]),
                                UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]),
                                VerifiedDate = Convert.IsDBNull(reader["Verifieddate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["Verifieddate"]),
                                Verifiedby = Convert.IsDBNull(reader["verifiedby"]) ? string.Empty : Convert.ToString(reader["verifiedby"]),
                            };
                            WfProfileLevelApprovals.Add(wfpfapproval);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature WFProfileApprovalLevelProcess.GetByApprovalProcessLevelByApId(" + apId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return WfProfileLevelApprovals;
        }

        IList<AppLevelProcess> IApprovalLevelProcessDataAccess.GetAll()
        {
            try
            {
                IList<AppLevelProcess> WfProfileLevelApprovals = new List<AppLevelProcess>();
                const string sp = "GetAllApproLevelProces";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<AppLevelProcess>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature WFProfileApprovalLevelProcess.GetALL" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }

        }

        AppLevelProcess IApprovalLevelProcessDataAccess.GetApprovalLevelProcess_UserById(int id)
        {
            AppLevelProcess _applevel = new AppLevelProcess();
            try
            {
                const string sp = "ApproLevelProces_UserGetbyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            AppLevelProcess _app = new AppLevelProcess
                            {
                                ApproverEmail = Convert.IsDBNull(reader["Email"]) ? string.Empty : Convert.ToString(reader["Email"]),
                                Approver = Convert.IsDBNull(reader["APPROVEREMAIL"]) ? string.Empty : Convert.ToString(reader["APPROVEREMAIL"]),
                                ApprovalState = Convert.IsDBNull(reader["PROFILE_OR_WORKFLOW"]) ? string.Empty : Convert.ToString(reader["PROFILE_OR_WORKFLOW"]),
                                ApprovalLevel = Convert.IsDBNull(reader["EXECUTE_OR_MODIFY"]) ? string.Empty : Convert.ToString(reader["EXECUTE_OR_MODIFY"]),
                                ApprovalBy = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Message = Convert.IsDBNull(reader["APPROVALPROCESSNUMBER"]) ? string.Empty : Convert.ToString(reader["APPROVALPROCESSNUMBER"]),
                            };
                            _applevel = _app;
                        }
                        //    return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalLevelProcess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return _applevel;
        }

        AppLevelProcess IApprovalLevelProcessDataAccess.GetApprovalLevelProcess_UserById_new(int id)
        {
            AppLevelProcess _applevel = new AppLevelProcess();
            try
            {
                const string sp = "ApproLevelProces_UserGetbyId_new";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            AppLevelProcess _app = new AppLevelProcess
                            {
                                ApproverEmail = Convert.IsDBNull(reader["Email"]) ? string.Empty : Convert.ToString(reader["Email"]),
                                Approver = Convert.IsDBNull(reader["APPROVEREMAIL"]) ? string.Empty : Convert.ToString(reader["APPROVEREMAIL"]),
                                ApprovalState = Convert.IsDBNull(reader["PROFILE_OR_WORKFLOW"]) ? string.Empty : Convert.ToString(reader["PROFILE_OR_WORKFLOW"]),
                                ApprovalLevel = Convert.IsDBNull(reader["EXECUTE_OR_MODIFY"]) ? string.Empty : Convert.ToString(reader["EXECUTE_OR_MODIFY"]),
                                ApprovalBy = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Message = Convert.IsDBNull(reader["APPROVALPROCESSNUMBER"]) ? string.Empty : Convert.ToString(reader["APPROVALPROCESSNUMBER"]),
                                Requester = Convert.IsDBNull(reader["PROFILEEXECUTIONENDTIME"]) ? string.Empty : Convert.ToString(reader["PROFILEEXECUTIONENDTIME"]),
                            };
                            _applevel = _app;
                        }
                        //    return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalLevelProcess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return _applevel;
        }

        AppLevelProcess IApprovalLevelProcessDataAccess.GetApprovalLevelProcess_ToVerify(int id)
        {
            AppLevelProcess _applevel = new AppLevelProcess();
            try
            {
                const string sp = "ApproLevelProces_ToVerify";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            AppLevelProcess _app = new AppLevelProcess
                            {
                                ApproverEmail = Convert.IsDBNull(reader["Email"]) ? string.Empty : Convert.ToString(reader["Email"]),
                                Approver = Convert.IsDBNull(reader["APPROVEREMAIL"]) ? string.Empty : Convert.ToString(reader["APPROVEREMAIL"]),
                                ApprovalState = Convert.IsDBNull(reader["PROFILE_OR_WORKFLOW"]) ? string.Empty : Convert.ToString(reader["PROFILE_OR_WORKFLOW"]),
                                ApprovalLevel = Convert.IsDBNull(reader["EXECUTE_OR_MODIFY"]) ? string.Empty : Convert.ToString(reader["EXECUTE_OR_MODIFY"]),
                                ApprovalBy = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Message = Convert.IsDBNull(reader["APPROVALPROCESSNUMBER"]) ? string.Empty : Convert.ToString(reader["APPROVALPROCESSNUMBER"]),
                                Requester = Convert.IsDBNull(reader["PROFILEEXECUTIONENDTIME"]) ? string.Empty : Convert.ToString(reader["PROFILEEXECUTIONENDTIME"]),
                            };
                            _applevel = _app;
                        }
                        //    return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalLevelProcess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return _applevel;
        }

        bool IApprovalLevelProcessDataAccess.updateverifystatus(int id, string status, int value, DateTime date, string verifiedby)
        {
            try
            {
                const string sp = "UpdateVerifyStatus";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "istatus", DbType.String, status);
                    Database.AddInParameter(cmd, Dbstring + "ivalue", DbType.Int32, value);
                    Database.AddInParameter(cmd, Dbstring + "iverifieddate", DbType.DateTime, date);
                    Database.AddInParameter(cmd, Dbstring + "iverifiedby", DbType.String, verifiedby);

                    int _value = Database.ExecuteNonQuery(cmd);

#if ORACLE || MSSQL
                    return _value < 0;
#endif
                    return _value > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating Approval Level Process Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        bool IApprovalLevelProcessDataAccess.ApproLevelProcesUpdate_Details(AppLevelProcess approvalProcess)
        {
            try
            {
                const string sp = "ApproLevelProcesUpdate_Details";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, approvalProcess.Id);
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, approvalProcess.ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalProcessId", DbType.Int32, approvalProcess.ApprovalProcessId);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalLevel", DbType.String, approvalProcess.ApprovalLevel);
                    //Database.AddInParameter(cmd, Dbstring + "iLevelNumber", DbType.Int32, approvalProcess.LevelNumber);
                    Database.AddInParameter(cmd, Dbstring + "iApprover", DbType.String, approvalProcess.Approver);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalState", DbType.String, approvalProcess.ApprovalState);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalBy", DbType.String, approvalProcess.ApprovalBy);
                    Database.AddInParameter(cmd, Dbstring + "iApproverEmail", DbType.String, approvalProcess.ApproverEmail);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.String, approvalProcess.Message);
                    Database.AddInParameter(cmd, Dbstring + "iNote", DbType.String, approvalProcess.Note);
                    Database.AddInParameter(cmd, Dbstring + "iIsApprovalReceived", DbType.Int32, approvalProcess.IsApprovalReceived);
                    Database.AddInParameter(cmd, Dbstring + "iIsApprove", DbType.Int32, approvalProcess.IsApprove);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, approvalProcess.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iVerifiedDate", DbType.DateTime, approvalProcess.VerifiedDate);
                    Database.AddInParameter(cmd, Dbstring + "iVerifiedBy", DbType.String, approvalProcess.Verifiedby);

                    int value = Database.ExecuteNonQuery(cmd);

#if ORACLE || MSSQL
                    return value < 0;
#endif
                    return value > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating Approval Level Process Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }
    }
}
