﻿var CollapseWidgetJS = function () {
    //
    //sessionStorage.setItem("colapseIds", "");
 
    $("#divparallelProfile").children(".widget").attr("data-collapse-closed", "true").children(".widget-body").removeClass("in").addClass("collapse");
    $("#divparallelProfile").children(".widget:first-child").attr("data-collapse-closed", "false").children(".widget-body").removeClass("collapse").addClass("in");
    
    $('.widget[data-toggle="collapse-widget"] .widget-body')
        .on('show.bs.collapse', function () {
            $(this).parents('.widget:first').attr('data-collapse-closed', "false");
        })
        //.on('shown.bs.collapse', function () {
        //    setTimeout(function () { $(window).resize(); }, 500);
        //})
        .on('hidden.bs.collapse', function () {
            $(this).parents('.widget:first').attr('data-collapse-closed', "true");
        });

    $('.widget[data-toggle="collapse-widget"]').each(function () {
      
        if (!$(this).find('.widget-head > .collapse-toggle').length)
            $('<span class="collapse-toggle"></span>').appendTo($(this).find('.widget-head'));

      
        $(this).find('.widget-body').not('.collapse').addClass('collapse');

     
        if ($(this).attr('data-collapse-closed') !== "true")
            $(this).find('.widget-body').addClass('in');

       
        $(this).find('.collapse-toggle').on('click', function () {
          
            var selectId = $(this).parents(".widget").attr("id");
            var selectVal = $("#" + selectId).attr("data-collapse-closed");

            if (selectVal == "true") {
                selectVal = "false";
            } else {
                selectVal = "true";
            }
            var idVal = selectId + "^" + selectVal;
            $(this).parents('.widget:first').find('.widget-body').collapse('toggle');
            if (sessionStorage.getItem("colapseIds") != null) {
                var sessonVal = sessionStorage.getItem("colapseIds");
                sessonVal = sessonVal + "," + idVal;
                sessionStorage.setItem("colapseIds", sessonVal);
            } else {
                sessionStorage.setItem("colapseIds", idVal);
            }
       

        });

    });
};