﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="InfrastructureMonitorList.aspx.cs" Inherits="CP.UI.InfrastructureMonitorList"
    Title="Continuity Patrol :: Workflow-InfrastructureMonitorList" %>

<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:Label ID="Label1" runat="server" CssClass="grid-details bold" Text=""></asp:Label>
 
    <div class="innerLR">
       
        <h3>
            <img src="../Images/monitor.png">
            Infrastructure Monitor List</h3>

       
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-md-5 col-md-push-7 text-right">

                        <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Infrastructure Monitor"></asp:TextBox>
                        <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" OnClick="BtnSearchClick" />
                </div>
                </div>
                <hr />
                <asp:ListView ID="lvInfrastructureMonitor" DataKeyNames="Id" runat="server" OnItemDataBound="lvInfrastructureMonitor_ItemDataBound" OnItemEditing="lvInfrastructureMonitorItemEditing"
                    OnItemDeleting="lvInfrastructureMonitorItemDeleting" OnPreRender="lvInfrastructureMonitorPreRender" OnPagePropertiesChanging="lvInfrastructureMonitor_PagePropertiesChanging">
                    <LayoutTemplate>
                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                            <thead>
                                <tr>
                                    <th style="width: 4%;">
                                        <span>
                                            <img src="../Images/monitor_white.png"></span>
                                    </th>
                                    <th>InfraObject
                                    </th>
                                    <th>Server
                                    </th>
                                    <th>CPU Threshold
                                    </th>
                                    <th>Memory Threshold
                                    </th>
                                    <th>MountPoint Threshold
                                    </th>
                                    <th>User Names
                                    </th>
                                     <th>File Names
                                    </th>
                                    <th>Action
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                            </tbody>
                        </table>
                    </LayoutTemplate>
                    <EmptyDataTemplate>
                        <div class="">
                            <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                        </div>
                    </EmptyDataTemplate>
                    <ItemTemplate>
                        <tr>
                            <td class="th table-check-cell">
                                <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                            </td>
                            <td>
                                <asp:Label ID="lblInfraObject" runat="server" Text='<%# Eval("InfraObjectId") %>' />
                            </td>
                            <td>
                                <asp:Label ID="lblServer" runat="server" Text='<%# Eval("ServerId") %>' />
                            </td>
                            <td>
                                <asp:Label ID="lblCPUThreshold" runat="server" Text='<%# Eval("CPUThreshold") %>' />
                            </td>
                            <td>
                                <asp:Label ID="lblMemoryThreshold" runat="server" Text='<%# Eval("MemoryThreshold") %>' />
                            </td>
                            <td>
                                <asp:Label ID="lblMountPointThreshold" runat="server" Text='<%# Eval("MountPointThreshold") %>' />
                            </td>
                             <td>
                                <asp:Label ID="lblUserNames" runat="server" Text='<%# Eval("UserNames") %>' />
                            </td>

                             <td>
                                <asp:Label ID="Label2" runat="server" Text='<%# Eval("FileNames") %>' />
                            </td>
                            <td>
                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                    ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                            </td>
                            <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText='<%# "Are you sure want to delete " + Eval("Id") + " ? " %>'
                                TargetControlID="ImgDelete" OnClientCancel="CancelClick">
                            </cc1:ConfirmButtonExtender>
                        </tr>
                    </ItemTemplate>
                </asp:ListView>

                <div class="row">
                    <div class="col-md-6">
                        <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvInfrastructureMonitor">
                            <Fields>
                                <asp:TemplatePagerField>
                                    <PagerTemplate>
                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                        Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                        <br />
                                    </PagerTemplate>
                                </asp:TemplatePagerField>
                            </Fields>
                        </asp:DataPager>
                    </div>
                    <div class="col-md-6 text-right">
                        <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvInfrastructureMonitor" PageSize="10">
                            <Fields>
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                    ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                    NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                    NumericButtonCssClass="btn-pagination" />
                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                    ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                            </Fields>
                        </asp:DataPager>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>