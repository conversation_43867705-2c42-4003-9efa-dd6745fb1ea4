﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="NEWParallelDrOperationReport.ascx.cs" Inherits="CP.UI.Controls.NEWParallelDrOperationReport" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<style type="text/css">
    .HeaderCSS {
        color: Snow;
        background-color: rgb(61,61,61);
        border: solid 1px white;
        font-weight: bold;
        height: 17px;
    }


    .HeaderSelectedCSS {
        color: Snow;
        background-color: rgb(61,61,61);
        font-weight: bold;
        height: 17px;
    }

    .divsummary {
        background-color: rgb(55,95,145);
        float: left;
        width: 11.11%;
        color: White;
        height: 20px;
        font-weight: bold;
    }

    .divsummaryNum {
        background-color: rgb(55,95,145);
        float: left;
        width: 6%;
        color: White;
        height: 20px;
        font-weight: bold;
    }

    .divsummaryWN {
        background-color: rgb(55,95,145);
        float: left;
        width: 16.22%;
        color: White;
        height: 20px;
        font-weight: bold;
    }

    .div {
        background-color: rgb(55,95,145);
        float: left;
        width: 16.66%;
        color: White;
        height: 20px;
        font-weight: bold;
    }

    .divNumber {
        background-color: rgb(55,95,145);
        float: left;
        width: 7%;
        color: White;
        height: 20px;
        font-weight: bold;
    }

    .divWN {
        background-color: rgb(55,95,145);
        float: left;
        width: 26.32%;
        color: White;
        height: 20px;
        font-weight: bold;
    }

    .divchild {
        float: left;
        width: 16.66%;
        color: Black;
        height: 20px;
    }

    .divchildNum {
        float: left;
        width: 7%;
        color: Black;
        height: 20px;
    }

    .divchildWN {
        float: left;
        width: 26.32%;
        color: Black;
        height: 20px;
    }

    .divsummchild {
        float: left;
        width: 11.11%;
        color: Black;
        height: 20px;
    }

    .divsummchildNum {
        float: left;
        width: 6%;
        color: Black;
        height: 20px;
    }

    .divsummchildWN {
        float: left;
        width: 16.22%;
        color: Black;
        height: 20px;
    }
</style>


<script src="../Script/Report.js"></script>

<%--<div class="form-group">
    <label class="col-md-3 control-label">
        Application Name <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlBussService" runat="server" OnSelectedIndexChanged="ddlBussService_SelectedIndexChanged" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="true"></asp:DropDownList>
        <asp:Label ID="lblbuzvalidation" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
    </div>
</div>--%>


<%--<div id="newdr" class="form-group">
    <label class="col-md-3 control-label">
        Action Type <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlactiontypeddl" runat="server" CssClass="chosen-select col-md-6"
            data-placeholder="Select options..." multiple="true" 
            data-style="btn-default" AutoPostBack="false">
            <asp:ListItem Selected="True" Value="">Select options</asp:ListItem>
        </asp:DropDownList>     

        <asp:Label ID="lblactiontype" runat="server" Text="select Action Type" Visible="false" ForeColor="Red"></asp:Label>
    </div>
</div>--%>

<div id="newdr" class="form-group">
    <label class="col-md-3 control-label">
        Action Type <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:CheckBoxList ID="chkActionType" runat="server" AutoPostBack="true" OnSelectedIndexChanged="chkActionType_SelectedIndexChanged" CssClass="checkbox-list col-md-6" RepeatDirection="Vertical">
        </asp:CheckBoxList>
        <asp:Label ID="lblactiontype" runat="server" Text="Select Action Type" Visible="false" ForeColor="Red"></asp:Label>
    </div>
</div>


<div class="form-group">
    <label class="col-md-3 control-label">
        Environment Type <span class="inactive"></span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlenvtype" OnSelectedIndexChanged="ddlenvtype_SelectedIndexChanged" runat="server" CssClass="chosen-select col-md-6"
            data-style="btn-default" AutoPostBack="true">
        </asp:DropDownList>
    </div>
</div>

<div class="form-group">
    <label class="col-md-3 control-label">
        Start Date <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <TK1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtstart" PopupPosition="Right"
            PopupButtonID="imgFromDate" Format="yyyy-MM-dd">
        </TK1:CalendarExtender>
        <asp:TextBox ID="txtstart" runat="server" CssClass="form-control" onkeydown="return false"></asp:TextBox>
        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgFromDate" style="margin-left: 5px;" />--%>
        <asp:ImageButton ID="imgFromDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
        <asp:RequiredFieldValidator ID="rfvSic" ForeColor="Red" runat="server" ErrorMessage="Select Start Date" ControlToValidate="txtstart" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
        <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ForeColor="Red" ErrorMessage="Select valid date." ValidationGroup="vlGroupSite"
            ControlToValidate="txtstart" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
    </div>
</div>




<div class="form-group">
    <label class="col-md-3 control-label">
        End Date <span class="inactive">*</span>
    </label>

    <div class="col-md-9">
        <asp:TextBox Style="display: none !important" ID="hdnactiontype" runat="server" />
        <asp:TextBox ID="txtend" runat="server" CssClass="form-control" OnTextChanged="txtend_TextChanged" AutoPostBack="true" onkeydown="return true"></asp:TextBox>
        <%--<img
            src="../images/icons/calendar-month.png" width="16" id="img1" style="margin-left: 5px;" />--%>
        <asp:ImageButton ID="imgBtnEndDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />

        <TK1:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtend" PopupPosition="Right"
            PopupButtonID="imgBtnEndDate" Format="yyyy-MM-dd">
        </TK1:CalendarExtender>
        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" ForeColor="Red" runat="server" ErrorMessage="Select Date"
            ControlToValidate="txtend" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
        <asp:RegularExpressionValidator ID="RegularExpressionValidator2" ForeColor="Red" runat="server" ErrorMessage="Select valid date." ValidationGroup="vlGroupSite"
            ControlToValidate="txtend" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
    </div>
</div>


<div class="form-group">
    <label class="col-md-3 control-label" for="complex-en-style">
        ParallelDROperation Name <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlParallelDrOperation" runat="server" Width="162px"
            AutoPostBack="false" CssClass="chosen-select col-md-6" data-style="btn-default">
        </asp:DropDownList>
        <asp:Label ID="lblvalidation" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
    </div>
</div>

<%--<asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
    <ContentTemplate>
        <div class="form-group">
            <label class="col-md-3 control-label" for="complex-en-style">
                ParallelDROperation Name <span class="inactive">*</span>
            </label>
            <div class="col-md-9">
                <asp:DropDownList ID="ddlParallelDrOperation" runat="server" Width="162px"
                    AutoPostBack="true" CssClass="chosen-select col-md-6" data-style="btn-default"
                  <%--  OnSelectedIndexChanged="ddlParallelDrOperation_SelectedIndexChanged"--%>
<%-- EnableViewState="true">
                </asp:DropDownList>
                <asp:Label ID="lblvalidation" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
            </div>
        </div>
    </ContentTemplate>
    <Triggers>
        <asp:AsyncPostBackTrigger ControlID="ddlParallelDrOperation" EventName="SelectedIndexChanged" />
        <!-- Add triggers for other controls if needed -->
    </Triggers>
</asp:UpdatePanel>--%>


<hr />



<div class="form-group">
    <div id="divlable" class="col-xs-6" visible="false">
        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>

        <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false"></asp:Label>
    </div>
    <div class="col-xs-6">
        <asp:Button ID="btnViewReport" runat="server" Text="View" CssClass="btn btn-primary" Width="20%" OnClick="btnViewReport_Click" Style="margin-left: 5px" />
    </div>
</div>


<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div id="imgLoading" class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>

