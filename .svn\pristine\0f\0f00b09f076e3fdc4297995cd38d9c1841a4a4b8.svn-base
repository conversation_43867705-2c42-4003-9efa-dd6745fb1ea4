﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Data;
using System.Data.Common;

namespace CP.DataAccess.ServiceDiagramDetails
{
    internal sealed class ServiceDiagramDataAccess : BaseDataAccess, IServiceDiagramDataAccess
    {
          #region Constructors

        public ServiceDiagramDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ServiceDiagram> CreateEntityBuilder<ServiceDiagram>()
        {
            return (new ServiceDiagramBuilder()) as IEntityBuilder<ServiceDiagram>;
        }
        //private static readonly ILog _logger = LogManager.GetLogger(typeof(AlertDataAccess));
        #endregion Constructors

        ServiceDiagram IServiceDiagramDataAccess.Add(ServiceDiagram servicediagram)
        {
            try
            {
                const string sp = "ServiceDiagramDet_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, servicediagram.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceName", DbType.String, servicediagram.BusinessServiceName);
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, servicediagram.UserId);
                    Database.AddInParameter(cmd, Dbstring + "iServiceJSON", DbType.String, servicediagram.ServiceJSON);
                    Database.AddInParameter(cmd, Dbstring + "iServiceString", DbType.String, servicediagram.ServiceString);
                   

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        servicediagram = reader.Read()
                            ? CreateEntityBuilder<ServiceDiagram>().BuildEntity(reader, servicediagram)
                            : null;
                    }

                    if (servicediagram == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "Service Diagram already exists. Please specify another Service Diagram.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this Service Diagram.");
                                }
                        }
                    }

                    return servicediagram;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting Service Diagram Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        ServiceDiagram IServiceDiagramDataAccess.GetByUserIdAndServiceId(int UserId,int ServiceId)
        {
            try
            {
                const string sp = "ServiceDiag_GetByUserServId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, UserId);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, ServiceId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<ServiceDiagram>()).BuildEntity(reader, new ServiceDiagram())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServiceDiagramDataAccess.GetByUserIdAndServiceId(" + UserId +"And"+ServiceId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
    }


}
