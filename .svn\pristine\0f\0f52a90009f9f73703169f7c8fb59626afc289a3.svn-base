﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class LogVolumeBuilder : IEntityBuilder<LogVolume>
    {
        IList<LogVolume> IEntityBuilder<LogVolume>.BuildEntities(IDataReader reader)
        {
            var logvolumes = new List<LogVolume>();

            while (reader.Read())
            {
                logvolumes.Add(((IEntityBuilder<LogVolume>)this).BuildEntity(reader, new LogVolume()));
            }

            return (logvolumes.Count > 0) ? logvolumes : null;
        }

        LogVolume IEntityBuilder<LogVolume>.BuildEntity(IDataReader reader, LogVolume logvolume)
        {
            //const int FLD_ID = 0;
            //const int FLD_LOGVOLUME = 1;
            //const int FLD_GROUPID = 2;
            //const int FLD_CREATEDATE = 3;

            //logvolume.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //logvolume.TotalLogVolume = reader.IsDBNull(FLD_LOGVOLUME) ? 0 : reader.GetInt32(FLD_LOGVOLUME);
            //logvolume.GroupId = reader.IsDBNull(FLD_GROUPID) ? 0 : reader.GetInt32(FLD_GROUPID);
            //logvolume.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);

            //Fields in bcms_logvolume table on 22/07/2013 : Id, TotalLogVolume, GroupId, CreateDate
            // Field Named GroupId Modified To InfraObjectId By Mahesh Singh Chauhan On 08.05.2014

            logvolume.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            logvolume.TotalLogVolume = Convert.IsDBNull(reader["TotalLogVolume"])
                ? 0
                : Convert.ToInt32(reader["TotalLogVolume"]);
            logvolume.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            logvolume.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);

            return logvolume;
        }
    }
}