﻿using System;
using System.Collections.Generic;
using System.Data;

namespace CP.DataAccess.MySqlNativeMonitor
{
    internal sealed class MySqlNativeMonitorBuilder : IEntityBuilder<CP.Common.DatabaseEntity.MySqlNative>
    {
        IList<CP.Common.DatabaseEntity.MySqlNative> IEntityBuilder<CP.Common.DatabaseEntity.MySqlNative>.BuildEntities(IDataReader reader)
        {
            var MySqlNativeReplication = new List<CP.Common.DatabaseEntity.MySqlNative>();

            while (reader.Read())
            {
                MySqlNativeReplication.Add(((IEntityBuilder<CP.Common.DatabaseEntity.MySqlNative>)this).BuildEntity(reader, new CP.Common.DatabaseEntity.MySqlNative()));
            }

            return (MySqlNativeReplication.Count > 0) ? MySqlNativeReplication : null;
        }

        CP.Common.DatabaseEntity.MySqlNative IEntityBuilder<CP.Common.DatabaseEntity.MySqlNative>.BuildEntity(IDataReader reader, CP.Common.DatabaseEntity.MySqlNative mysql)
        {
            mysql.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            mysql.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            mysql.ReplicationId = Convert.IsDBNull(reader["ReplicationID"]) ? 0 : Convert.ToInt32(reader["ReplicationID"]);
            mysql.LOGNO = Convert.IsDBNull(reader["LogNo"]) ? string.Empty : Convert.ToString(reader["LogNo"]);
            mysql.RepStatus = Convert.IsDBNull(reader["REPSTATUS"]) ? string.Empty : Convert.ToString(reader["REPSTATUS"]);

            return mysql;
        }
    }
}