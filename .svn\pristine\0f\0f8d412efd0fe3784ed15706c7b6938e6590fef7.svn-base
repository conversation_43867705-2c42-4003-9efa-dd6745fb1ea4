﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ParallelProfileDataAccess : BaseDataAccess, IParallelProfileDataAccess
    {
        #region Cosntructor

        public ParallelProfileDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ParallelProfile> CreateEntityBuilder<ParallelProfile>()
        {
            return (new ParallelProfileBuilder()) as IEntityBuilder<ParallelProfile>;
        }

        #endregion Cosntructor

        #region Method

        ParallelProfile IParallelProfileDataAccess.Add(ParallelProfile profile)
        {
            try
            {
                const string sp = "ParallelProfile_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileName", DbType.AnsiString, profile.ProfileName);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, profile.Status);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.AnsiString, profile.Password);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, profile.CreatorId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        profile = reader.Read()
                            ? CreateEntityBuilder<ParallelProfile>().BuildEntity(reader, profile)
                            : null;
                    }

                    if (profile == null)
                    {
                        ExceptionManager.Manage(new CpException(CpExceptionType.DataAccessInsertOperation, "Error While Adding Workflow Profile return Null Value"));
                    }
                    //if (profile == null)
                    //{
                    //    int returnCode = GetReturnCodeFromParameter(cmd);

                    //    switch (returnCode)
                    //    {
                    //        case Constants.MySqlConstants.DB_STATUS_CODE_ERROR_DUPLICATE_DATA:
                    //            {
                    //                throw new ArgumentException("Profile Name already exists.");
                    //            }
                    //        default:
                    //            {
                    //                throw new SystemException("An unexpected error has occurred while creating this Profile.");
                    //            }
                    //    }
                    //}

                    return profile;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting ParallelProfile Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ParallelProfile> IParallelProfileDataAccess.GetAll()
        {
            try
            {
                IList<ParallelProfile> pProfiles = new List<ParallelProfile>();

                const string sp = "ParallelProfile_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var pro = new ParallelProfile
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"])
                            };

                            pProfiles.Add(pro);
                        }
                    }
                }

                return pProfiles;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature  IParallelProfileDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ParallelProfile> IParallelProfileDataAccess.GetprofileByCustomIdnAccess(int userId)
        {
            try
            {
                IList<ParallelProfile> pProfiles = new List<ParallelProfile>();

                const string sp = "GetCustomAccess_profileId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, userId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var pro = new ParallelProfile
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"])
                            };

                            pProfiles.Add(pro);
                        }
                    }
                }

                return pProfiles;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature  IParallelProfileDataAccess.GetprofileByCustomIdnAccess()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        ParallelProfile IParallelProfileDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "ParallelProfile_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<ParallelProfile>()).BuildEntity(reader, new ParallelProfile())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool IParallelProfileDataAccess.DeleteGetById(int id)
        {
            try
            {
                const string sp = "ParallelProfile_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ParallelProfile> IParallelProfileDataAccess.GetByName(string profileName)
        {
            try
            {
                IList<ParallelProfile> pProfiles = new List<ParallelProfile>();
                const string sp = "PARALLELPROFILE_GETBYNAME";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileName", DbType.AnsiString, profileName);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var pro = new ParallelProfile();

                            pro.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            pro.ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]);
                            pro.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
                            pProfiles.Add(pro);
                        }
                    }
                    return pProfiles;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetByProfileName(" +
                    profileName + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        ParallelProfile IParallelProfileDataAccess.GetByDROperationId(int droperationid)
        {
            try
            {
                const string sp = "PARLELPROFILE_GETBYDROPERTONID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDroperationId", DbType.Int32, droperationid);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<ParallelProfile>()).BuildEntity(reader, new ParallelProfile())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetByDROperationId(" + droperationid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ParallelProfile> IParallelProfileDataAccess.GetParallelProfileByInfraUserId(int infraId, int userid)
        {
            try
            {
                IList<ParallelProfile> pProfiles = new List<ParallelProfile>();
                const string sp = "ParallPrfl_GetByInfraUserId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, infraId);
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, userid);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var pro = new ParallelProfile
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"])
                            };

                            pProfiles.Add(pro);
                        }
                    }
                    return pProfiles;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetParallelProfileByInfraUserId(" +
                    infraId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ParallelProfile> IParallelProfileDataAccess.GetProfileByUserrole(string userRole, int userId)
        {
            try
            {
                IList<ParallelProfile> pProfiles = new List<ParallelProfile>();

                const string sp = "GetProfileByuserRole";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iuserRole", DbType.AnsiString, userRole);
                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, userId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var pro = new ParallelProfile
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"])
                            };

                            pProfiles.Add(pro);
                        }
                    }
                }

                return pProfiles;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature  IParallelProfileDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool IParallelProfileDataAccess.ParProfileChangePassword(int ProfileId, string Password)
        {
            try
            {
                const string sp = "UpdtParallelProfPass_byIdName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.AnsiString, Password);

                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While User ChangePassword by aler name : " + ProfileId, ex);
            }
        }

        IList<ParallelProfile> IParallelProfileDataAccess.GetAttachParallelProfileByUserId(int userid)
        {
            try
            {
                IList<ParallelProfile> pProfiles = new List<ParallelProfile>();
                const string sp = "GETATTACHPROFILEBYUSERID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, userid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var pro = new ParallelProfile
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"])
                            };

                            pProfiles.Add(pro);
                        }
                    }
                    return pProfiles;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetAttachParallelProfileByUserId(" +
                    userid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ParallelProfile> IParallelProfileDataAccess.GetprofileByCustomIdnAccessView(int userId)
        {
            try
            {
                IList<ParallelProfile> pProfiles = new List<ParallelProfile>();

                const string sp = "GetCustomAccessView_profileId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, userId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var pro = new ParallelProfile
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                            };

                            pProfiles.Add(pro);
                        }
                    }
                }

                return pProfiles;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature  IParallelProfileDataAccess.GetprofileByCustomIdnAccess()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        //-------------------Approval Changes-----------------

        bool IParallelProfileDataAccess.UpdateIsRequireById(int id, string ApprovalRequire)
        {
            try
            {
                const string sp = "ParallelProfile_UpdateAR";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalRequire", DbType.String, ApprovalRequire);
                    int returnCode = Database.ExecuteNonQuery(cmd);

#if ORACLE
                    if (returnCode < 0)
                    {
                        return true;
                    }
#endif
                    if (returnCode > 0)
                    {
                        return true;
                    }
                    return false;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<ParallelProfile> IParallelProfileDataAccess.GetByApprove(string ApprovalRequire)
        {
            try
            {
                IList<ParallelProfile> pProfiles = new List<ParallelProfile>();

                const string sp = "ParallelProfile_GetByApprove";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iApprovalRequire", DbType.AnsiString, ApprovalRequire);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var pro = new ParallelProfile
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]),
                                Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                ApprovalRequire = Convert.IsDBNull(reader["ApprovalRequire"]) ? string.Empty : Convert.ToString(reader["ApprovalRequire"]),

                            };

                            pProfiles.Add(pro);
                        }
                    }
                }

                return pProfiles;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature  IParallelProfileDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        //-------------------------------------


        IList<ParallelProfile> IParallelProfileDataAccess.GetAllParallelProfile_forfoureye()
        {
            IList<ParallelProfile> pProfiles = new List<ParallelProfile>();
            try
            {
               

                const string sp = "GetAllParallelProfile_forfoureye";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            ParallelProfile _pProfiles = new ParallelProfile();
                            _pProfiles.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            _pProfiles.ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]);
                            //_pProfiles.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
                            //_pProfiles.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
                            _pProfiles.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            _pProfiles.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
                            _pProfiles.IsFourEye = Convert.IsDBNull(reader["Isfoureye"]) ? 0 : Convert.ToInt32(reader["Isfoureye"]);
                            pProfiles.Add(_pProfiles);
                        }
                    }
                }

                return pProfiles;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature  IParallelProfileDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<ParallelProfile> IParallelProfileDataAccess.GetwfprofileByName(string name)
        {
            IList<ParallelProfile> Profiles = new List<ParallelProfile>();
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "ParallelWorkflowProfile_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfilename", DbType.String, name);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            ParallelProfile _pProfiles = new ParallelProfile();
                            _pProfiles.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            _pProfiles.ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]);
                            _pProfiles.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
                            _pProfiles.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
                            _pProfiles.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            _pProfiles.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
                            _pProfiles.IsFourEye = Convert.IsDBNull(reader["Isfoureye"]) ? 0 : Convert.ToInt32(reader["Isfoureye"]);
                            Profiles.Add(_pProfiles);
                        }
                            
                    }

                }
                return Profiles;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetwfprofileByName(" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        

        }


        #endregion Method
    }
}