﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
   
    internal sealed class VirtualMonitoringDataAccess : BaseDataAccess, IVirtualMonitoringDataAccess
    {
        #region Constructors

        public VirtualMonitoringDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SQLNative2008Monitor> CreateEntityBuilder<SQLNative2008Monitor>()
        {
            return (new VirtualMonitoringBuilder()) as IEntityBuilder<SQLNative2008Monitor>;
        }

        #endregion Constructors

        #region Methods

        VirtualVolumeMonitoring IVirtualMonitoringDataAccess.GetVirtualMonitorByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "VirtualMonitor_GetbyInfraID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<VirtualVolumeMonitoring>()).BuildEntity(reader, new VirtualVolumeMonitoring())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IVirtualMonitoringDataAccess.GetByInfraObjectId(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion Methods
    }
}

