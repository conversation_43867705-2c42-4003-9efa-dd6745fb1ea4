﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="NormalDatabaseList.ascx.cs" Inherits="BCMS.UI.Controls.NormalDatabaseList" %>
 <%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit, Version=3.0.30930.28736, Culture=neutral, PublicKeyToken=28f01b0e84b6d53e" %>

<asp:UpdateProgress ID="updateprogress1" AssociatedUpdatePanelID="upnlNormalDatabseList" runat="server">
 <progresstemplate>
                          <div class="loading-mask">
                          <span>Loading...</span>
                          </div>
                        </progresstemplate>
</asp:UpdateProgress>
<asp:UpdatePanel ID="upnlNormalDatabseList" runat="server">

<ContentTemplate>

<div class="block-content no-padding">
 <asp:ListView ID="lvNormaldatabase" runat="server" DataKeyNames="ID" 
                            onitemediting="LvdatabaseItemEditing" onprerender="LvdatabasePreRender" 
                            onitemdeleting="LvdatabaseItemDeleting" 
                            onitemdatabound="lvdatabase_ItemDataBound" >
                           
                            <LayoutTemplate>
                                <table id="tbldatabase" class="table font no-bottom-margin" width="100%">
                                    <thead>
                                        <tr>
                                            <th class="sorting_disabled" style="width: 4%;">
                                                <span>
                                                    <img src="../Images/icons/database.png" /></span>
                                            </th>
                                            
                                            <th>
                                              Database Name
                                            </th>
                                            <th>
                                              BaseDatabase Name
                                            </th>
                                            <th>
                                                Database Type
                                            </th>
                                           
                                            <th>
                                                Port Number
                                            </th>
                                            
                                            <th runat="server" id="ActionHead">
                                                Action
                                            </th>
                                            
                                            
                                        </tr>
                                    </thead>
                                    <tbody>
                                      
                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                       
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr class="<%# Container.DisplayIndex % 2 == 0 ? "row" : "alternateRow" %>">
                                    <td>
                                        <asp:Label ID="ID" runat="server" Text='<%#Eval("Id") %>' Visible="false"></asp:Label>
                                        <%--<asp:Label ID="Label2" runat="server" Text='<%#Eval("login_name") %>' Visible="false"></asp:Label>--%>
                                        <%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td>                                       
                                        <span> <%# DataBinder.Eval(Container, "DataItem.DatabaseName")%></span>
                                    </td>
                                    <td>                                       
                                        <span> <%# DataBinder.Eval(Container, "DataItem.BaseName")%></span>
                                    </td>                                    
                                    <td>
                                        <%# DataBinder.Eval(Container, "DataItem.Category")%>
                                    </td>
                                    <td>
                                        <%# DataBinder.Eval ( Container, "DataItem.Port" )%>
                                    </td>                              
                                    
                                    
                                    <td runat="server" id="action">
                                        <asp:ImageButton ID="ibtnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                            ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                                        <asp:ImageButton ID="ibtnDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                            ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                                    </td>
                                   <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ibtnDelete"
                                        ConfirmText='<%# "Are you sure want to delete " + Eval("basename") + " ? " %>' OnClientCancel="CancelClick">
                                    </TK1:ConfirmButtonExtender> 
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                        
                    <div class="message no-bottom-margin">
                            <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvNormaldatabase" PageSize="4">
                                <Fields>
                                    <asp:TemplatePagerField>
                                        <PagerTemplate>
                                            <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                            Results
                                            <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                            Out Of
                                            <%# Container.TotalRowCount %>
                                            <br />
                                        </PagerTemplate>
                                    </asp:TemplatePagerField>
                                </Fields>
                            </asp:DataPager>
                        </div>
                        <div class="block-footer no-margin">
                            &nbsp;
                            <div class="float-right margin-right">
                                <asp:DataPager ID="DataPager1" runat="server" PagedControlID="lvNormaldatabase" PageSize="10">
                                    <Fields>                                       
                                        <asp:NextPreviousPagerField ButtonCssClass="buttonblue" ShowFirstPageButton="false" ButtonType="Button"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="Prev" />
                                        <asp:NumericPagerField NextPageText=".." PreviousPageText=".." ButtonCount="10"
                                            NextPreviousButtonCssClass="buttonblue" CurrentPageLabelCssClass="buttonblue"
                                            NumericButtonCssClass="button" />
                                        <asp:NextPreviousPagerField ButtonCssClass="buttonblue" ShowFirstPageButton="false" ButtonType="Button"
                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>
                    </div>
<script type="text/javascript">
function CancelClick()
{
    return false;
}
</script>
 
 </ContentTemplate>
 </asp:UpdatePanel> 