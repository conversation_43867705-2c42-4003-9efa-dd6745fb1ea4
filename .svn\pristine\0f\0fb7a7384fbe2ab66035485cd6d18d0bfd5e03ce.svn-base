﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class MssqlVMAXEmcSrdfMonitorBuilder : IEntityBuilder<MsSqlVMAXEmcSrdfFullDB>
    {
        IList<MsSqlVMAXEmcSrdfFullDB> IEntityBuilder<MsSqlVMAXEmcSrdfFullDB>.BuildEntities(IDataReader reader)
        {
            var mssqlVMAXEmcSrdflog = new List<MsSqlVMAXEmcSrdfFullDB>();

            while (reader.Read())
            {
                mssqlVMAXEmcSrdflog.Add(((IEntityBuilder<MsSqlVMAXEmcSrdfFullDB>)this).BuildEntity(reader, new MsSqlVMAXEmcSrdfFullDB()));
            }

            return (mssqlVMAXEmcSrdflog.Count > 0) ? mssqlVMAXEmcSrdflog : null;
        }

        MsSqlVMAXEmcSrdfFullDB IEntityBuilder<MsSqlVMAXEmcSrdfFullDB>.BuildEntity(IDataReader reader, MsSqlVMAXEmcSrdfFullDB mssqlVMAXEmcSrdflog)
        {

            mssqlVMAXEmcSrdflog.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            mssqlVMAXEmcSrdflog.InfraobjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
            mssqlVMAXEmcSrdflog.InstanceName = Convert.IsDBNull(reader["InstanceName"]) ? string.Empty : Convert.ToString(reader["InstanceName"]);
            mssqlVMAXEmcSrdflog.DatabaseState = Convert.IsDBNull(reader["DatabaseState"]) ? string.Empty : Convert.ToString(reader["DatabaseState"]);
            mssqlVMAXEmcSrdflog.RestrictAccessStatus = Convert.IsDBNull(reader["RestrictAccessStatus"]) ? string.Empty : Convert.ToString(reader["RestrictAccessStatus"]);
            mssqlVMAXEmcSrdflog.DRDatabaseState = Convert.IsDBNull(reader["DRDatabaseState"]) ? string.Empty : Convert.ToString(reader["DRDatabaseState"]);
            mssqlVMAXEmcSrdflog.DRRestrictAccessStatus = Convert.IsDBNull(reader["DRRestrictAccessStatus"]) ? string.Empty : Convert.ToString(reader["DRRestrictAccessStatus"]);
            //mssqlEmcSrdflog.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            mssqlVMAXEmcSrdflog.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            mssqlVMAXEmcSrdflog.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
            mssqlVMAXEmcSrdflog.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            mssqlVMAXEmcSrdflog.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]);

            return mssqlVMAXEmcSrdflog;
        }
    }
}
