﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.BusinessEntity;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;

namespace CP.UI
{
    public partial class GroupConfiguration : GroupBasePage
    {
        private static IFacade _facade = new Facade();
        private static int _currentLoggedUserId;
        private static string _currentLoggedUserName;
        private static int _companyId;
        private static int currentgroupId;
        private static int dbcount = 0;
        private static int PRservercount = 0;
        private static int drservercount = 0;
        private static int drsitecount = 0;
        private static int prsitecount = 0;
        private static bool _isUserSuperAdmin;
        private static bool _isParent;
        public bool IsRac;

        protected void BtnWfClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Workflow.WorkflowConfiguration);
        }

        public override void PrepareView()
        {
            Utility.SelectMenu(Master, "Module4");
            groupID.Value = CurrentGroupId.ToString();
            SolType.Value = Solutiontype;
            currentgroupId = CurrentGroupId;
            _currentLoggedUserName = LoggedInUserName;
            _currentLoggedUserId = LoggedInUserId;
            _companyId = LoggedInUserCompanyId;
            _isUserSuperAdmin = IsUserSuperAdmin;
            _isParent = LoggedInUserCompany.IsParent;

            //Utility.PopulateApplicationGroup(ddlApplication, true);
            var appGroupList = Facade.GetBusinessServiceByCompanyIdAndRole(_currentLoggedUserId, _companyId, LoggedInUserRole, _isParent,
                                                                            LoggedInUser.InfraObjectAllFlag);
            ddlBusinessService.DataSource = appGroupList;
            ddlBusinessService.DataTextField = "Name";
            ddlBusinessService.DataValueField = "Id";
            ddlBusinessService.DataBind();
            ddlBusinessService.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectApplicationName, "000"));

            //ddlGroupList.DataSource = Facade.GetAllGroupsByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin,
            //                                                LoggedInUserCompany.IsParent);
            var groupList = Facade.GetAllGroupsByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin,
                                                            LoggedInUserCompany.IsParent);

            if (groupList != null)
            {
                var twoSiteGroup = (from data1 in groupList
                                    where data1.SiteSolutionTypeId == 1
                                    select data1).ToList();
                ddlGroupList.DataSource = twoSiteGroup;
                ddlGroupList.DataTextField = "Name";
                ddlGroupList.DataValueField = "Id";
                ddlGroupList.DataBind();
                ddlGroupList.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName, "000"));
            }
            else
            {
                ddlGroupList.DataSource = groupList;
                ddlGroupList.DataTextField = "Name";
                ddlGroupList.DataValueField = "Id";
                ddlGroupList.DataBind();
                ddlGroupList.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectGroupName, "000"));
            }
        }

        private void ValidateGroupConfiguratione()
        {
            if (AllowGroupConfigure())
            {
                lbldatabase.Visible = false;
                lblPRserver.Visible = false;
                lblDRserver.Visible = false;
                lblPrsite.Visible = false;
                lbldrSite.Visible = false;
                divcontent.Style.Add("display", "block");
                DivNotification.Style.Add("display", "none");
            }
            else
            {
                DivNotification.Style.Add("display", "block");
                divcontent.Style.Add("display", "none");
                lbldatabase.Visible = true;
                lbldatabase.Text = Convert.ToString(dbcount);
                lblPRserver.Visible = true;
                lblPRserver.Text = Convert.ToString(PRservercount);
                lblDRserver.Visible = true;
                lblDRserver.Text = Convert.ToString(drservercount);
                lblPrsite.Visible = true;
                lblPrsite.Text = Convert.ToString(prsitecount);
                lbldrSite.Visible = true;
                lbldrSite.Text = Convert.ToString(drsitecount);
            }
        }

        private bool AllowGroupConfigure()
        {
            var serverList = Facade.GetServersByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin,
                                                           LoggedInUserCompany.IsParent);
            var databaseList = Facade.GetAllDatabaseBases();

            if (serverList != null && databaseList != null)
            {
                return serverList.Count >= 2 && databaseList.Count >= 2;
            }
            return false;
        }

        [WebMethod]
        public static string GetInfraObjectById(string data)
        {
            int id = Convert.ToInt32(data);
            var group = _facade.GetInfraObjectById(id);
            string str = group.Name + "," + group.Description + "," + group.BusinessServiceId.ToString() + "," + group.Type.ToString() + "," + group.RecoveryType.ToString() + "," + group.Priority.ToString();
            str = str + "," + group.PRServerId.ToString() + "," + group.DRServerId.ToString() + "," + group.PRDatabaseId.ToString() + "," + group.DRDatabaseId.ToString() + "," + group.PRReplicationId.ToString() + "," + group.DRReplicationId.ToString() + "," + group.Id.ToString() + "," + group.SiteSolutionTypeId.ToString() + "," + group.NearGroupId.ToString();
            return str;
        }

        [WebMethod]
        public static List<RecoveryTypeName> FetchRecoveryType(int id)
        {
            var baseActionType = new RecoveryTypeName();
            return baseActionType.FetchRecoveryType(id);
        }

        [WebMethod]
        public static string GetAllGroup()
        {
            var allGroup = _facade.GetAllGroupsByUserCompanyIdAndRoleAndIsSuperAdmin(_companyId, _isUserSuperAdmin, _isParent);
            var valueToReturn = "";
            if (allGroup != null)
            {
                valueToReturn = allGroup.Aggregate(valueToReturn, (current, gr) => string.Format("{0}{1},", current, gr.Name.ToUpper().Trim()));
            }
            return valueToReturn.TrimEnd(',');
        }

        [WebMethod]
        public static string GetAllGroupForEdit()
        {
            var allGroup = _facade.GetAllGroupsByUserCompanyIdAndRoleAndIsSuperAdmin(_companyId, _isUserSuperAdmin, _isParent);
            var twoSiteGroup = (from data1 in allGroup
                                where data1.SiteSolutionTypeId == 1
                                select data1).ToList();
            var valueToReturn = "";
            if (allGroup != null)
            {
                valueToReturn = twoSiteGroup.Aggregate(valueToReturn, (current, gr) => string.Format("{0}{1}:{2},", current, gr.Name, gr.Id));
                //returnValuePr = string.Format("{0}{1}:{2}:" + "ddlServerPr" + ",", returnValuePr, server.Name, Convert.ToString(server.Id));
            }
            return valueToReturn.TrimEnd(',');
        }

        #region GetRelationship Values for DropDowns

        [WebMethod]
        public static string GetServerComponent()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var serverList = _facade.GetServersByUserCompanyIdAndRole(_companyId, _isUserSuperAdmin,
                                                          _isParent);
            if (serverList != null)
            {
                foreach (var server in serverList)
                {
                    var serverType = server.Type.ToUpper();
                    switch (serverType)
                    {
                        case "PRESXISERVER":
                            returnValuePr = string.Format("{0}{1}:{2}:" + "ddlServerPr" + ",", returnValuePr, server.Name, Convert.ToString(server.Id));
                            break;

                        case "DRESXISERVER":
                            returnValueDr = string.Format("{0}{1}:{2}:" + "ddlServerDr" + ",", returnValueDr, server.Name, Convert.ToString(server.Id));
                            //  returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrServer" + ",", returnValueNearDr, server.Name, Convert.ToString(server.Id));// For Near Dr Server
                            break;

                        case "PRAPPSERVER":
                            returnValueDr = string.Format("{0}{1}:{2}:" + "ddlServerDr" + ",", returnValueDr, server.Name, Convert.ToString(server.Id));
                            //  returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrServer" + ",", returnValueNearDr, server.Name, Convert.ToString(server.Id));// For Near Dr Server
                            break;

                        case "DRAPPSERVER":
                            returnValuePr = string.Format("{0}{1}:{2}:" + "ddlServerPr" + ",", returnValuePr, server.Name, Convert.ToString(server.Id));
                            break;

                        case "PRDBSERVER":
                            returnValuePr = string.Format("{0}{1}:{2}:" + "ddlServerPr" + ",", returnValuePr, server.Name, Convert.ToString(server.Id));
                            break;

                        case "DRDBSERVER":
                            returnValueDr = string.Format("{0}{1}:{2}:" + "ddlServerDr" + ",", returnValueDr, server.Name, Convert.ToString(server.Id));
                            // returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrServer" + ",", returnValueNearDr, server.Name, Convert.ToString(server.Id));// For Near Dr Server
                            break;

                        case "DSCLISERVER":
                            returnValuePr = string.Format("{0}{1}:{2}:" + "ddlServerPr" + ",", returnValuePr, server.Name, Convert.ToString(server.Id));
                            returnValueDr = string.Format("{0}{1}:{2}:" + "ddlServerDr" + ",", returnValueDr, server.Name, Convert.ToString(server.Id));
                            // returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrServer" + ",", returnValueNearDr, server.Name, Convert.ToString(server.Id));// For Near Dr Server
                            break;

                        case "HMCSERVER":
                            returnValuePr = string.Format("{0}{1}:{2}:" + "ddlServerPr" + ",", returnValuePr, server.Name, Convert.ToString(server.Id));
                            returnValueDr = string.Format("{0}{1}:{2}:" + "ddlServerDr" + ",", returnValueDr, server.Name, Convert.ToString(server.Id));
                            // returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrServer" + ",", returnValueNearDr, server.Name, Convert.ToString(server.Id));// For Near Dr Server
                            break;

                        case "DNSSERVER":
                            returnValuePr = string.Format("{0}{1}:{2}:" + "ddlServerPr" + ",", returnValuePr, server.Name, Convert.ToString(server.Id));
                            returnValueDr = string.Format("{0}{1}:{2}:" + "ddlServerDr" + ",", returnValueDr, server.Name, Convert.ToString(server.Id));
                            // returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrServer" + ",", returnValueNearDr, server.Name, Convert.ToString(server.Id));// For Near Dr Server
                            break;

                        case "SYMCLISERVER":
                            returnValuePr = string.Format("{0}{1}:{2}:" + "ddlServerPr" + ",", returnValuePr, server.Name, Convert.ToString(server.Id));
                            returnValueDr = string.Format("{0}{1}:{2}:" + "ddlServerDr" + ",", returnValueDr, server.Name, Convert.ToString(server.Id));
                            // returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrServer" + ",", returnValueNearDr, server.Name, Convert.ToString(server.Id));// For Near Dr Server
                            break;
                    }
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',');// +"::" + returnValueNearDr.TrimEnd(',');
            }
            return string.Empty;
        }

        [WebMethod]
        public static string GetMsSqlById(string data)
        {
            int id = Convert.ToInt32(data);
            var mssqllog = _facade.GetSqlNativeById(id);
            if (mssqllog != null)
            {
                string str = mssqllog.BackupFolderName + "," + mssqllog.BackupFolderSharedName + "," + mssqllog.CopyRestoreName + "," + mssqllog.CopyRestoreSharedName + "," + mssqllog.BackupInterval.ToString() + "," + mssqllog.CopyInterval.ToString() + "," + mssqllog.RestoreInterval.ToString();
                return str;
            }
            return string.Empty;
        }

        [WebMethod]
        public static string UpdateMssqlNatives(string values)
        {
            var returnstr = "";
            var valueToreturn1 = "";
            var datavalue = values.Split('$');
            returnstr = UpdateGroup(datavalue[0], "");
            if (returnstr.Contains("Success"))
            {
                var param = datavalue[1].Split(',');
                var mssql = new SqlNative();
                mssql.InfraObjectID = Convert.ToInt32(param[0]);
                mssql.BackupFolderName = param[1];
                mssql.BackupFolderSharedName = param[3];
                mssql.CopyRestoreName = param[2];
                mssql.CopyRestoreSharedName = param[4];
                mssql.BackupInterval = Convert.ToInt32(param[5]);
                mssql.CopyInterval = Convert.ToInt32(param[6]);
                mssql.RestoreInterval = Convert.ToInt32(param[7]);
                mssql.UpdatorId = _currentLoggedUserId;
                try
                {
                    mssql = _facade.UpdateSqlNative(mssql);
                    valueToreturn1 = "Success ";
                }
                catch
                {
                    valueToreturn1 = "error";
                }
            }
            return valueToreturn1;
        }

        [WebMethod]
        public static string GetHitachiUrLunsGetById(string data)
        {
            int id = Convert.ToInt32(data);
            var hitachilunsvalue = _facade.GetHitachiUrLunsByInfraObjectId(id);
            if (hitachilunsvalue != null)
            {
                // string str = mssqllog.BackupFolderName + "," + mssqllog.BackupFolderSharedName + "," + mssqllog.CopyRestoreName + "," + mssqllog.CopyRestoreSharedName + "," + mssqllog.BackupInterval.ToString() + "," + mssqllog.CopyInterval.ToString() + "," + mssqllog.RestoreInterval.ToString();
                string str = hitachilunsvalue.ArchVGName + "," + hitachilunsvalue.ArchMountPoint + "," + hitachilunsvalue.ArchDevicetype + "," + hitachilunsvalue.ArchHURTrueCopySource + "," + hitachilunsvalue.ArchShadowimagePR + "," + hitachilunsvalue.ArchTarget;
                str = str + "," + hitachilunsvalue.RedoVGName + "," + hitachilunsvalue.RedoMountPoint + "," + hitachilunsvalue.RedoDeviceType.ToString() + "," + hitachilunsvalue.RedoHURTrueCopySource + "," + hitachilunsvalue.RedoShadowimagePR + "," + hitachilunsvalue.RedoTarget + "," + hitachilunsvalue.Id.ToString();
                return str;
            }
            return string.Empty;
        }

        [WebMethod]
        public static string GetDataGuardReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allReplication = _facade.GetAllReplicationBase();
            if (allReplication != null)
            {
                var datagardReplication = from rep in allReplication
                                          where rep.Type == (ReplicationType)2
                                          select rep;
                foreach (var rep in datagardReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValueDr, rep.Name,
                                                  Convert.ToString(rep.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValuePr, rep.Name,
                                                  Convert.ToString(rep.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, rep.Name,
                                                  Convert.ToString(rep.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }
            return string.Empty;
        }

        [WebMethod]
        public static string GetDataBase(string data)
        {
            var value = data.Split(',');
            var returnValue = "";
            var control = "";
            if (value[1].Contains("ddlNearDrServer"))//For Near Dr Database Bind
            {
                control = "ddlNearDrDatabase";
            }
            else
            {
                control = value[1].Contains("ddlServerPr") ? "ddlDatabasePr" : "ddlDatabaseDr";
            }
            var databaseList = _facade.GetAllDatabaseBases();
            var result = from database in databaseList where database.ServerId == Convert.ToInt32(value[0]) select database;
            foreach (var database in result)
            {
                returnValue = string.Format("{0}{1}:{2}:" + control + ",", returnValue, database.Name, Convert.ToString(database.Id));
            }
            return returnValue + "," + value[2] + ":" + control;
        }

        //For Exchange DAG
        [WebMethod]
        public static string GetExchangeDagMailboxDatabase(string data)
        {
            var value = data.Split(',');
            var returnValue = "";
            var control = "";
            if (value[1].Contains("ddlNearDrServer"))//For Near Dr Database Bind
            {
                control = "ddlNearDrDatabase";
            }
            else
            {
                control = value[1].Contains("ddlServerPr") ? "lstExcDAGPR" : "lstExcDAGDR";
            }
            var exchangeDagMailboxDatabaseList = _facade.GetDatabaseExchangeDAGByServerId(Convert.ToInt32(value[0]));
            if (exchangeDagMailboxDatabaseList != null)
            {
                var result = from exchangeDagMailboxDatabase in exchangeDagMailboxDatabaseList select exchangeDagMailboxDatabase;

                foreach (var exchangeDagMailboxDatabase in result)
                {
                    returnValue = string.Format("{0}{1}:{2}:" + control + ",", returnValue, exchangeDagMailboxDatabase.MailBoxDBName, Convert.ToString(0));
                }
            }
            else
            {
                return "emptyValue" + "," + control;
            }
            return returnValue + "," + value[2] + ":" + control;
        }

        //End Exchange DAG
        //For Rac
        [WebMethod]
        public static string CheckIsRac(string prdatabase, string drdatabase)
        {
            var prdb = new DatabaseBase();
            var drdb = new DatabaseBase();
            if (prdatabase != "000")
            {
                prdb = _facade.GetDatabaseBaseById(Convert.ToInt32(prdatabase));
            }
            if (drdatabase != "000")
            {
                drdb = _facade.GetDatabaseBaseById(Convert.ToInt32(drdatabase));
            }
            if (prdb.IsPartofRac & drdb.IsPartofRac)
            {
                return "1";
            }
            return "0";
        }

        [WebMethod]
        public static string GetDataBaseNodes(string data)
        {
            var value = data.Split(',');
            var returnValue = "";
            var control = "";
            control = value[1].Contains("ddlDatabasePr") ? "ddlNodePr" : "ddlNodeDr";
            var databaseList = _facade.GetAllDataBaseNodesByDatabaseId(Convert.ToInt32(value[0]));
            if (databaseList != null)
            {
                foreach (var database in databaseList)
                {
                    var nodedetails = _facade.GetNodesById(database.NodeId);
                    if (nodedetails != null)
                    {
                        if (nodedetails.Id > 0)
                        {
                            returnValue = string.Format("{0}{1}:{2}:" + control + ",", returnValue, nodedetails.Name,
                                                        Convert.ToString(nodedetails.Id));
                        }
                    }
                }
            }
            return returnValue + "," + value[2] + ":" + control;
        }

        //[WebMethod]
        //public static string GetAllNodesByGroupId(string data)
        //{
        //    var grpIdvalue = data;
        //    var returnValue = "";
        //    var databaseNodesList = _facade.GetGroupDatabaseNodesByGroupId(Convert.ToInt32(grpIdvalue));
        //    if (databaseNodesList != null)
        //    {
        //        foreach (var grpdbnode in databaseNodesList)
        //        {
        //            var nodedetailspr = _facade.GetNodesById(grpdbnode.PrNodeId);

        //            if (nodedetailspr != null)
        //            {
        //                if (nodedetailspr.Id > 0)
        //                {
        //                    returnValue += string.Format("{0}:{1},", Convert.ToString(nodedetailspr.Id),
        //                                                 nodedetailspr.Name);
        //                }
        //            }
        //            var nodedetailsdr = _facade.GetNodesById(grpdbnode.DrNodeId);
        //            if (nodedetailspr != null)
        //            {
        //                if (nodedetailsdr.Id > 0)
        //                {
        //                    returnValue += string.Format("{0}:{1}?", Convert.ToString(nodedetailsdr.Id),
        //                                                 nodedetailsdr.Name);
        //                }
        //            }
        //        }
        //    }
        //    return returnValue;
        //}
        //For Rac
        [WebMethod]
        public static string GetSqlServerbyid(string data)
        {
            var returnValue = "";
            var sqldata = data.Split(',');
            if (sqldata[0] == "000")
            {
                return returnValue;
            }
            var sqlnative = _facade.GetDatabaseSqlByDatabaseBaseId(Convert.ToInt32(sqldata[0]));
            if (sqlnative == null)
            {
                return returnValue;
            }
            returnValue = sqlnative.BackupRestorePath.Replace("\\", "//") + "," + sqlnative.NetworkSharedPath.Replace("\\", "//") + "," + sqldata[1];
            return returnValue;
        }

        [WebMethod]
        public static string GetGlobalMirrorReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allGlobalMirrorReplication = _facade.GetAllReplicationBase();
            var globalMirrorReplication = from rep in allGlobalMirrorReplication
                                          where rep.Type == (ReplicationType)1
                                          select rep;
            if (allGlobalMirrorReplication != null)
            {
                foreach (var gm in globalMirrorReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        #endregion GetRelationship Values for DropDowns

        [WebMethod]
        public static string GetVMwareFastCopyReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allvmwareFastCopyReplication = _facade.GetAllReplicationBase();
            var globalMirrorReplication = from rep in allvmwareFastCopyReplication
                                          where rep.Type == (ReplicationType)10
                                          select rep;

            if (allvmwareFastCopyReplication != null)
            {
                foreach (var gm in globalMirrorReplication)
                {
                    returnValueDr = string.Format("{0}{1}+{2}+" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}+{2}+" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}+{2}+" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "^" + returnValuePr.TrimEnd(',') + "^" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string GetFastCopyReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allFastCopyReplication = _facade.GetAllReplicationBase();
            var fastcopyreplication = from rep in allFastCopyReplication
                                      where rep.Type == (ReplicationType)3
                                      select rep;
            if (allFastCopyReplication != null)
            {
                foreach (var gm in fastcopyreplication)
                {
                    returnValueDr = string.Format("{0}{1}+{2}+" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}+{2}+" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}+{2}+" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "^" + returnValuePr.TrimEnd(',') + "^" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string GetOracleWithFastCopyReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var alloraclewithFastCopyReplication = _facade.GetAllReplicationBase();
            var oraclefastcopyreplication = from rep in alloraclewithFastCopyReplication
                                            where rep.Type == (ReplicationType)14
                                            select rep;
            if (alloraclewithFastCopyReplication != null)
            {
                foreach (var gm in oraclefastcopyreplication)
                {
                    returnValueDr = string.Format("{0}{1}+{2}+" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}+{2}+" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}+{2}+" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "^" + returnValuePr.TrimEnd(',') + "^" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string GetHitachiURReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allHitachiURReplication = _facade.GetAllReplicationBase();
            var allhitachiURReplication = from rep in allHitachiURReplication
                                          where rep.Type == (ReplicationType)9
                                          select rep;
            if (allHitachiURReplication != null)
            {
                foreach (var gm in allhitachiURReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string GetExchangeDAGReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allExchageDAGReplication = _facade.GetAllReplicationBase();
            var allexchageDAGReplication = from rep in allExchageDAGReplication
                                           where rep.Type == (ReplicationType)20
                                           select rep;
            if (allExchageDAGReplication != null)
            {
                foreach (var gm in allexchageDAGReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string GetHitachiUROracleLogShippingReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allHitachiURReplication = _facade.GetAllReplicationBase();
            var allhitachiURReplication = from rep in allHitachiURReplication
                                          where rep.Type == (ReplicationType)16
                                          select rep;
            if (allHitachiURReplication != null)
            {
                foreach (var gm in allhitachiURReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string GetVmwareHitachiURReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allHitachiURReplication = _facade.GetAllReplicationBase();
            var allhitachiURReplication = from rep in allHitachiURReplication
                                          where rep.Type == (ReplicationType)19
                                          select rep;
            if (allHitachiURReplication != null)
            {
                foreach (var gm in allhitachiURReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string GetEMCSRDFReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allEmcsrdfReplication = _facade.GetAllReplicationBase();
            var emcSrdfReplication = from rep in allEmcsrdfReplication
                                     where rep.Type == (ReplicationType)7
                                     select rep;
            if (allEmcsrdfReplication != null)
            {
                foreach (var gm in emcSrdfReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }
            return string.Empty;
        }

        [WebMethod]
        public static string GetEMCSRDFOracleFullDBReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allEmcsrdfReplication = _facade.GetAllReplicationBase();
            var emcSrdfReplication = from rep in allEmcsrdfReplication
                                     where rep.Type == (ReplicationType)17
                                     select rep;
            if (allEmcsrdfReplication != null)
            {
                foreach (var gm in emcSrdfReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }
            return string.Empty;
        }

        [WebMethod]
        public static string GetEMCSRDFOracleLogShippingReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allEmcsrdfReplication = _facade.GetAllReplicationBase();
            var emcSrdfReplication = from rep in allEmcsrdfReplication
                                     where rep.Type == (ReplicationType)18
                                     select rep;
            if (allEmcsrdfReplication != null)
            {
                foreach (var gm in emcSrdfReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name, Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }
            return string.Empty;
        }

        [WebMethod]
        public static string GetLunsValue(string id)
        {
            int replicationId = Convert.ToInt32(id);
            GlobalMirror gm = _facade.GetGlobalMirrorByReplicationId(replicationId);

            var valueToReturn = "";

            var allluns = _facade.GetGlobalMirrorLunsByGlobalMirrorId(gm.Id);
            if (allluns != null)
            {
                valueToReturn = allluns.Aggregate(valueToReturn, (current, luns) => string.Format("{0}{1},", current, luns.AVolume));
                return valueToReturn.TrimEnd(',');
            }
            return string.Empty;
        }

        [WebMethod]
        public static string GetLunsUpdate(string id)
        {
            var valueToReturn = "";
            var allluns = _facade.GetGroupLunsById(Convert.ToInt32(id));
            if (allluns != null)
            {
                valueToReturn = allluns.Aggregate(valueToReturn, (current, luns) => string.Format("{0}{1};{2};{3};{4},", current, luns.Id, luns.Luns, luns.AGroup, luns.AMount));
                return valueToReturn.TrimEnd(',');
            }
            return string.Empty;
        }

        [WebMethod]
        public static string SaveGroup(string values, string racvalues)
        {
            try
            {
                var para = values.Split(',');
                var group = new Group();
                group.Name = para[0];
                group.Description = para[1];
                group.BusinessServiceId = Convert.ToInt32(para[2]);
                group.Type = Convert.ToInt32(para[3]);
                group.RecoveryType = Convert.ToInt32(para[4]);
                group.RecoveryTime = Convert.ToInt32(para[5]);
                group.ConfigureDataLag = Convert.ToInt32(para[6]);

                group.MTPOD = Convert.ToInt32(para[7]);
                group.Priority = Convert.ToInt32(para[8]);
                //Server Component
                group.PRServerId = Convert.ToInt32(para[9]);
                group.DRServerId = Convert.ToInt32(para[10]);

                //DataBase Component
                group.PRDatabaseId = Convert.ToInt32(para[11]);
                group.DRDatabaseId = Convert.ToInt32(para[12]);

                // For replication
                group.PRReplicationId = Convert.ToInt32(para[13]);
                group.DRReplicationId = Convert.ToInt32(para[14]);
                group.SiteSolutionTypeId = Convert.ToInt32(para[19]);
                group.NearGroupId = Convert.ToInt32(para[20]);
                //for Site Solution Dropdown
                //group.SiteSolutionTypeId = Convert.ToInt32(para[15]);
                ////For Near Dr Site Configuration(Server, Database, Replication)
                //group.NearDRServerId = Convert.ToInt32(para[16]);
                //group.NearDRDatabaseId = Convert.ToInt32(para[17]);
                //group.NearDRReplicationId = Convert.ToInt32(para[18]);

                group.State = EnumHelper.GetDescription(InfraObjectState.Maintenance);
                group.ReplicationStatus = (int)InfraObjectReplicationStatus.Maintenance;
                group.CreatorId = _currentLoggedUserId;
                group = _facade.AddGroup(group);
                HttpContext.Current.Session["GroupId"] = group.Id;
                HttpContext.Current.Session["GroupName"] = group.Name;
                ErrorSuccessNotifier.AddSuccessMessage("Group information has been successfully created");
                ActivityLogger.AddLog(_currentLoggedUserName, "Group", UserActionType.CreateGroup, "The Group '" + group.Name + "' was added to the group table", _currentLoggedUserId);

                if (racvalues != "")
                {
                    var racpara = racvalues.Split(',');
                    if (racpara.Length > 5)
                    {
                        var groupdbnodes = new GroupDatabaseNodes();
                        groupdbnodes.InfraObjectId = group.Id;

                        //Server Component
                        groupdbnodes.PrServerId = Convert.ToInt32(racpara[0]);
                        groupdbnodes.DrServerId = Convert.ToInt32(racpara[1]);

                        //DataBase Component
                        groupdbnodes.PrDbseId = Convert.ToInt32(racpara[2]);
                        groupdbnodes.DrDbseId = Convert.ToInt32(racpara[3]);

                        //For Nodes
                        var nodescount = Convert.ToInt32(racpara[4]);
                        for (var i = 1; i <= nodescount - 1; i++)
                        {
                            var j = 4;
                            var nodedatapara = racpara[j + i];
                            var nodedata = nodedatapara.Split(':');
                            groupdbnodes.PrNodeId = Convert.ToInt32(nodedata[0]);
                            groupdbnodes.DrNodeId = Convert.ToInt32(nodedata[1]);
                            _facade.AddInfraObjectDatabaseNodes(groupdbnodes);
                        }
                    }
                }
                return "Success ";
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);

                return ex.Message;
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, "Unhandeled exception occurred while inserting Application Group information", ex);

                ExceptionManager.Manage(bcms);

                return ex.Message;
            }
        }

        [WebMethod]
        public static string SaveHitachiLunsGroup(string value)
        {
            try
            {
                var values = value.Split(':');
                var hitachiluns = new HitachiUrLuns();
                // var values = value.Split('$');
                string groupValue = SaveGroup(values[1], "");
                var valueToReturn = "";
                if (groupValue.Contains("Success"))
                {
                    string[] hitachiUr = values[0].Split(',');
                    hitachiluns.InfraObjectId = Convert.ToInt32(HttpContext.Current.Session["GroupId"].ToString());
                    hitachiluns.ArchVGName = hitachiUr[0];
                    hitachiluns.ArchMountPoint = hitachiUr[1];
                    hitachiluns.ArchDevicetype = hitachiUr[2];
                    hitachiluns.ArchHURTrueCopySource = hitachiUr[3];
                    hitachiluns.ArchShadowimagePR = hitachiUr[4];
                    hitachiluns.ArchTarget = hitachiUr[5];
                    hitachiluns.RedoVGName = hitachiUr[6];
                    hitachiluns.RedoMountPoint = hitachiUr[7];
                    hitachiluns.RedoDeviceType = hitachiUr[8];
                    hitachiluns.RedoHURTrueCopySource = hitachiUr[9];
                    hitachiluns.RedoShadowimagePR = hitachiUr[10];
                    hitachiluns.RedoTarget = hitachiUr[11];

                    hitachiluns.CreatorId = _currentLoggedUserId;
                    try
                    {
                        _facade.AddHitachiUrLuns(hitachiluns);
                        valueToReturn = "sucess";
                    }
                    catch (Exception)
                    {
                        valueToReturn = "failure";
                    }
                }
                return valueToReturn;
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);

                return ex.Message;
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, "Unhandeled exception occurred while inserting Application Group information", ex);

                ExceptionManager.Manage(bcms);

                return ex.Message;
            }
        }

        [WebMethod]
        public static string UpdateHitachiLunsGroup(string values)
        {
            var returnstr = "";
            var valueToreturn1 = "";
            var datavalue = values.Split(':');
            returnstr = UpdateGroup(datavalue[0], "");
            if (returnstr.Contains("Success"))
            {
                var param = datavalue[1].Split(',');
                var hitachiluns = new HitachiUrLuns();
                hitachiluns.InfraObjectId = Convert.ToInt32(HttpContext.Current.Session["GroupId"].ToString());
                hitachiluns.ArchVGName = param[1];
                hitachiluns.ArchMountPoint = param[2];
                hitachiluns.ArchDevicetype = param[3];
                hitachiluns.ArchHURTrueCopySource = param[4];
                hitachiluns.ArchShadowimagePR = param[5];
                hitachiluns.ArchTarget = param[6];
                hitachiluns.RedoVGName = param[7];
                hitachiluns.RedoMountPoint = param[8];
                hitachiluns.RedoDeviceType = param[9];
                hitachiluns.RedoHURTrueCopySource = param[10];
                hitachiluns.RedoShadowimagePR = param[11];
                hitachiluns.RedoTarget = param[12];
                hitachiluns.Id = Convert.ToInt32(param[13]);
                hitachiluns.UpdatorId = _currentLoggedUserId;

                try
                {
                    hitachiluns = _facade.UpdateHitachiUrLuns(hitachiluns);
                    valueToreturn1 = "Success ";
                }
                catch
                {
                    valueToreturn1 = "error";
                }
            }
            return valueToreturn1;
        }

        [WebMethod]
        public static string UpdateGroup(string values, string racvalues)
        {
            try
            {
                var para = values.Split(',');
                var group = new Group();
                group.Id = Convert.ToInt32(para[0]);
                group.Name = para[1];
                group.Description = para[2];
                group.BusinessServiceId = Convert.ToInt32(para[3]);
                group.Type = Convert.ToInt32(para[4]);
                group.RecoveryType = Convert.ToInt32(para[5]);
                group.RecoveryTime = Convert.ToInt32(para[6]);
                group.ConfigureDataLag = Convert.ToInt32(para[7]);

                group.MTPOD = Convert.ToInt32(para[8]);
                group.Priority = Convert.ToInt32(para[9]);

                //Server Component
                group.PRServerId = Convert.ToInt32(para[10]);
                group.DRServerId = Convert.ToInt32(para[11]);

                //DataBase Component
                group.PRDatabaseId = Convert.ToInt32(para[12]);
                group.DRDatabaseId = Convert.ToInt32(para[13]);

                // for replication
                group.PRReplicationId = Convert.ToInt32(para[14]);
                group.DRReplicationId = Convert.ToInt32(para[15]);
                group.SiteSolutionTypeId = Convert.ToInt32(para[20]);
                group.NearGroupId = Convert.ToInt32(para[21]);

                //group.SiteSolutionTypeId = Convert.ToInt32(para[16]);
                //group.NearDRServerId = Convert.ToInt32(para[17]);
                //group.NearDRDatabaseId = Convert.ToInt32(para[18]);
                //group.NearDRReplicationId = Convert.ToInt32(para[19]);
                group.State = EnumHelper.GetDescription(InfraObjectState.Maintenance);
                group.ReplicationStatus = (int)InfraObjectReplicationStatus.Maintenance;
                group.UpdatorId = _currentLoggedUserId;

                group = _facade.UpdateGroup(group);

                HttpContext.Current.Session["GroupId"] = group.Id;
                HttpContext.Current.Session["GroupName"] = group.Name;
                ErrorSuccessNotifier.AddSuccessMessage("Group information has been successfully updated");
                ActivityLogger.AddLog(_currentLoggedUserName, "Group", UserActionType.UpdateGroup, "The Group '" + group.Name + "' was update to the group table", _currentLoggedUserId);

                if (racvalues != "")
                {
                    _facade.DeleteInfraObjectDatabaseNodesByInfraObjectId(group.Id);
                    var racpara = racvalues.Split(',');
                    if (racpara.Length > 5)
                    {
                        var groupdbnodes = new GroupDatabaseNodes();
                        groupdbnodes.InfraObjectId = group.Id;

                        //Server Component
                        groupdbnodes.PrServerId = Convert.ToInt32(racpara[1]);
                        groupdbnodes.DrServerId = Convert.ToInt32(racpara[2]);

                        //DataBase Component
                        groupdbnodes.PrDbseId = Convert.ToInt32(racpara[3]);
                        groupdbnodes.DrDbseId = Convert.ToInt32(racpara[4]);

                        //For Nodes
                        var nodescount = Convert.ToInt32(racpara[5]);
                        for (var i = 1; i <= nodescount - 1; i++)
                        {
                            var j = 5;
                            var nodedatapara = racpara[j + i];
                            var nodedata = nodedatapara.Split(':');
                            groupdbnodes.PrNodeId = Convert.ToInt32(nodedata[0]);
                            groupdbnodes.DrNodeId = Convert.ToInt32(nodedata[1]);
                            _facade.AddInfraObjectDatabaseNodes(groupdbnodes);
                        }
                    }
                }

                return "Success ";
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);

                return ex.Message;
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, "Unhandeled exception occurred while inserting Application Group information", ex);

                ExceptionManager.Manage(bcms);

                return ex.Message;
            }
        }

        [WebMethod]
        public static string GetExchangeReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allExchangeReplication = _facade.GetAllReplicationBase();
            var exchangeReplication = from rep in allExchangeReplication
                                      where rep.Type == (ReplicationType)5
                                      select rep;
            if (allExchangeReplication != null)
            {
                foreach (var gm in exchangeReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name,
                                                  Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string GetSnapMirrorReplication()
        {
            var returnValueDr = "";
            var returnValuePr = "";
            var returnValueNearDr = "";
            var allsnapmirrorReplication = _facade.GetAllReplicationBase();
            var snapmirrorReplication = from rep in allsnapmirrorReplication
                                        where rep.Type == (ReplicationType)6
                                        select rep;
            if (allsnapmirrorReplication != null)
            {
                foreach (var gm in snapmirrorReplication)
                {
                    returnValueDr = string.Format("{0}{1}:{2}:" + "ddlReplicationDr" + ",", returnValueDr, gm.Name, Convert.ToString(gm.Id));
                    returnValuePr = string.Format("{0}{1}:{2}:" + "ddlReplicationPr" + ",", returnValuePr, gm.Name, Convert.ToString(gm.Id));
                    returnValueNearDr = string.Format("{0}{1}:{2}:" + "ddlNearDrReplication" + ",", returnValueNearDr, gm.Name,
                                                  Convert.ToString(gm.Id));
                }
                return returnValueDr.TrimEnd(',') + "::" + returnValuePr.TrimEnd(',') + "::" + returnValueNearDr.TrimEnd(',');
            }

            return string.Empty;
        }

        [WebMethod]
        public static string SaveLuns(string parameter)
        {
            var valueToReturn = "";
            var values = parameter.Split('&');
            var groupValue = SaveGroup(values[0], "");
            if (groupValue.Contains("Success"))
            {
                values[1] = values[1].TrimEnd(',');
                var para = values[1].Split(',');
                var grLuns = new GroupLuns();

                foreach (string[] lunsvalue in para.Select(luns => luns.Split(':')))
                {
                    grLuns.GroupId = Convert.ToInt32(HttpContext.Current.Session["GroupId"].ToString());
                    grLuns.Luns = lunsvalue[0];
                    grLuns.Logs = Convert.ToInt32(lunsvalue[1]);
                    grLuns.AGroup = lunsvalue[2];
                    grLuns.AMount = lunsvalue[3];
                    grLuns.CreatorId = _currentLoggedUserId;
                    _facade.AddGroupLuns(grLuns);
                    valueToReturn = "Success";
                }
            }
            return valueToReturn;
        }

        [WebMethod]
        public static string SaveNativeGroup(string value)
        {
            var values = value.Split('$');
            string groupValue = SaveGroup(values[1], "");
            var valueToReturn = "";
            if (groupValue.Contains("Success"))
            {
                string[] nativelog = values[0].Split(',');
                var msSqlNative = new SqlNative();
                msSqlNative.InfraObjectID = Convert.ToInt32(HttpContext.Current.Session["GroupId"].ToString());
                msSqlNative.BackupFolderName = nativelog[0];
                msSqlNative.CopyRestoreName = nativelog[1];
                msSqlNative.BackupFolderSharedName = nativelog[2];
                msSqlNative.CopyRestoreSharedName = nativelog[3];
                msSqlNative.BackupInterval = Convert.ToInt32(nativelog[4]);
                msSqlNative.CopyInterval = Convert.ToInt32(nativelog[5]);
                msSqlNative.RestoreInterval = Convert.ToInt32(nativelog[6]);
                msSqlNative.CreatorId = _currentLoggedUserId;
                try
                {
                    _facade.AddSqlNativeTranslog(msSqlNative);
                    valueToReturn = "sucess";
                }
                catch (Exception)
                {
                    valueToReturn = "failure";
                }
            }
            return valueToReturn;
        }

        [WebMethod]
        public static string SaveApp(string value)
        {
            try
            {
                var splitString = value.Split(',');
                var applicationGroup = _facade.AddBusinessService(new BusinessService
                {
                    Name = Convert.ToString(splitString[0]),
                    Description = Convert.ToString(splitString[1]),
                    CompanyId = Convert.ToInt32(splitString[2]),
                    CreatorId = _currentLoggedUserId
                });
                if (applicationGroup != null)
                    return applicationGroup.Id + ":" + applicationGroup.Name;
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);

                return ex.Message;
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, "Unhandeled exception occurred while inserting Application Group information", ex);

                ExceptionManager.Manage(bcms);

                return ex.Message;
            }
            return string.Empty;
        }

        [WebMethod]
        public static string UpdateLunsGroup(string data)
        {
            var returnstr = "";
            var datavalue = data.Split('$');
            returnstr = UpdateGroup(datavalue[1], "");
            if (returnstr.Contains("Success"))
            {
                UpdateLuns(datavalue[0]);
            }
            return "Update Sucessfull";
        }

        public static string UpdateLuns(string data)
        {
            var lunsRow = data.TrimEnd(',').Split(',');
            for (int i = 0; i < lunsRow.Length; i++)
            {
                var lunsdata = lunsRow[i].Split(':');
                GroupLuns grpluns = new GroupLuns();
                grpluns.Id = Convert.ToInt32(lunsdata[4]);
                grpluns.AGroup = lunsdata[2];
                grpluns.AMount = lunsdata[3];
                grpluns.BGroup = lunsdata[2];
                grpluns.BMount = lunsdata[3];
                grpluns.CGroup = lunsdata[2];
                grpluns.CMount = lunsdata[3];
                grpluns.DGroup = lunsdata[2];
                grpluns.DMount = lunsdata[3];
                grpluns.EGroup = lunsdata[2];
                grpluns.EMount = lunsdata[3];
                grpluns.FGroup = lunsdata[2];
                grpluns.FMount = lunsdata[3];
                grpluns.UpdatorId = _currentLoggedUserId;
                _facade.UpdateGroupLuns(grpluns);
            }
            return "Update Sucessfull";
        }

        [WebMethod]
        public static string GetCompanyList()
        {
            var returnValue = "";

            var allCompanyName = _facade.GetCompanyProfileByUserCompanyIdAndRole(_companyId, _isUserSuperAdmin, _isParent);
            if (allCompanyName != null)
            {
                returnValue = allCompanyName.Aggregate(returnValue, (current, company) => string.Format("{0}{1}:{2},", current, company.Name, company.Id));
                return returnValue.TrimEnd(',');
            }
            return returnValue;
        }

        [WebMethod]
        public static string GetGroupListDetailsById(string data)
        {
            var groupDetail = _facade.GetInfraObjectById(Convert.ToInt32(data));
            if (groupDetail == null)
            {
                return string.Empty;
            }
            var strValues = groupDetail.PRServerId + ":" + groupDetail.PRDatabaseId + ":" + groupDetail.PRReplicationId; //serverType.Aggregate("", (current, objworkfLow) => current + objworkfLow.Name + ":" + objworkfLow.Id + ",");
            return strValues;
        }
    }
}