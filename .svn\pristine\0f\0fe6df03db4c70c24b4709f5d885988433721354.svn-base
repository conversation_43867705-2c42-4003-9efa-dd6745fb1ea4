﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class VCenterMonitorProfileBuilder : IEntityBuilder<vCenterProfile>
    {
        #region IEntityBuilder<vCenterProfile> Members

        IList<vCenterProfile> IEntityBuilder<vCenterProfile>.BuildEntities(IDataReader reader)
        {
            var vCenter = new List<vCenterProfile>();

            while (reader.Read())
            {
                vCenter.Add(((IEntityBuilder<vCenterProfile>)this).BuildEntity(reader, new vCenterProfile()));
            }

            return (vCenter.Count > 0) ? vCenter : null;
        }

        vCenterProfile IEntityBuilder<vCenterProfile>.BuildEntity(IDataReader reader, vCenterProfile vCenter)
        {
            

            vCenter.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            vCenter.ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]);

            if (!Convert.IsDBNull(reader["IsSelected"]))
                vCenter.IsSelected = Convert.ToInt32(reader["IsSelected"]);

            vCenter.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            vCenter.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            vCenter.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);

            vCenter.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            vCenter.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);

            if (!Convert.IsDBNull(reader["ServerId"]))
                vCenter.IsSelected = Convert.ToInt32(reader["ServerId"]);

            vCenter.ProfileType = Convert.IsDBNull(reader["ProfileType"]) ? string.Empty : Convert.ToString(reader["ProfileType"]);

            return vCenter;
        }

        #endregion IEntityBuilder<vCenterProfile> Members
    }
}
