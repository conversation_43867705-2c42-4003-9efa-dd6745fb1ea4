﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class Base24RepliMonitorBuilder : IEntityBuilder<Base24RepliMonitor>
    {
        IList<Base24RepliMonitor> IEntityBuilder<Base24RepliMonitor>.BuildEntities(IDataReader reader)
        {
            var Base24Repli = new List<Base24RepliMonitor>();

            while (reader.Read())
            {
                Base24Repli.Add(((IEntityBuilder<Base24RepliMonitor>)this).BuildEntity(reader, new Base24RepliMonitor()));
            }

            return (Base24Repli.Count > 0) ? Base24Repli : null;
        }

        Base24RepliMonitor IEntityBuilder<Base24RepliMonitor>.BuildEntity(IDataReader reader, Base24RepliMonitor Base24Repli)
        {
            Base24Repli.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            Base24Repli.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            Base24Repli.DRApplicationStatus = Convert.IsDBNull(reader["DRApplicationStatus"]) ? string.Empty : Convert.ToString(reader["DRApplicationStatus"]);
            Base24Repli.DRReplicationAccessed = Convert.IsDBNull(reader["DRReplicationAccessed"]) ? string.Empty : Convert.ToString(reader["DRReplicationAccessed"]);
            Base24Repli.DRReplicationMode = Convert.IsDBNull(reader["DRReplicationMode"]) ? string.Empty : Convert.ToString(reader["DRReplicationMode"]);
            Base24Repli.DRReplicationProcess = Convert.IsDBNull(reader["DRReplicationProcess"]) ? string.Empty : Convert.ToString(reader["DRReplicationProcess"]);
            Base24Repli.DRReplicationQueue = Convert.IsDBNull(reader["DRReplicationQueue"]) ? string.Empty : Convert.ToString(reader["DRReplicationQueue"]);
            Base24Repli.DRReplicationStatus = Convert.IsDBNull(reader["DRReplicationStatus"]) ? string.Empty : Convert.ToString(reader["DRReplicationStatus"]);
            Base24Repli.DRReplicationType = Convert.IsDBNull(reader["DRReplicationType"]) ? string.Empty : Convert.ToString(reader["DRReplicationType"]);
            Base24Repli.DRServerName = Convert.IsDBNull(reader["DRServerName"]) ? string.Empty : Convert.ToString(reader["DRServerName"]);
            Base24Repli.PRApplicationStatus = Convert.IsDBNull(reader["PRApplicationStatus"]) ? string.Empty : Convert.ToString(reader["PRApplicationStatus"]);
            Base24Repli.PRReplicationAccessed = Convert.IsDBNull(reader["PRReplicationAccessed"]) ? string.Empty : Convert.ToString(reader["PRReplicationAccessed"]);
            Base24Repli.PRReplicationMode = Convert.IsDBNull(reader["PRReplicationMode"]) ? string.Empty : Convert.ToString(reader["PRReplicationMode"]);
            Base24Repli.PRReplicationProcess = Convert.IsDBNull(reader["PRReplicationProcess"]) ? string.Empty : Convert.ToString(reader["PRReplicationProcess"]);
            Base24Repli.PRReplicationQueue = Convert.IsDBNull(reader["PRReplicationQueue"]) ? string.Empty : Convert.ToString(reader["PRReplicationQueue"]);
            Base24Repli.PRReplicationStatus = Convert.IsDBNull(reader["PRReplicationStatus"]) ? string.Empty : Convert.ToString(reader["PRReplicationStatus"]);
            Base24Repli.PRReplicationType = Convert.IsDBNull(reader["PRReplicationType"]) ? string.Empty : Convert.ToString(reader["PRReplicationType"]);
            Base24Repli.PRServerName = Convert.IsDBNull(reader["PRServerName"]) ? string.Empty : Convert.ToString(reader["PRServerName"]);
            Base24Repli.PRAuditFilename = Convert.IsDBNull(reader["PRAuditFilename"]) ? string.Empty : Convert.ToString(reader["PRAuditFilename"]);
            Base24Repli.DRAuditFilename = Convert.IsDBNull(reader["DRAuditFilename"]) ? string.Empty : Convert.ToString(reader["DRAuditFilename"]);
            Base24Repli.DataLag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty : Convert.ToString(reader["DataLag"]);

            return Base24Repli;
        }



    }
}
