﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class SnapMirrorDataAccess : BaseDataAccess, ISnapMirrorDataAccess
    {
        #region Constructors

        public SnapMirrorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SnapMirror> CreateEntityBuilder<SnapMirror>()
        {
            return (new SnapMirrorBuilder()) as IEntityBuilder<SnapMirror>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="SnapMirror" />into snapmirror table.
        /// </summary>
        /// <param name="snapmirror">snapmirror</param>
        /// <returns>SnapMirror</returns>
        /// <author>Pratik <PERSON></author>
        SnapMirror ISnapMirrorDataAccess.Add(SnapMirror snapmirror)
        {
            try
            {
                const string sp = "SnapMirror_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.Int32, snapmirror.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring+"iMCPRServerId", DbType.Int32, snapmirror.MCPRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iMCDRServerId", DbType.Int32, snapmirror.MCDRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iPRStorageId", DbType.String, snapmirror.PRStorageId);
                    Database.AddInParameter(cmd, Dbstring+"iDRStorageId", DbType.String, snapmirror.DRStorageId);
                    Database.AddInParameter(cmd, Dbstring+"iMode", DbType.Int32, snapmirror.Mode);
                    Database.AddInParameter(cmd, Dbstring+"iPRVolume", DbType.String, snapmirror.PRVolume);
                    Database.AddInParameter(cmd, Dbstring+"iDRVolume", DbType.String, snapmirror.DRVolume);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunPath", DbType.String, snapmirror.PRLunSerialNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunPath", DbType.String, snapmirror.DRLunSerialNo);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunMapId", DbType.String, snapmirror.PRLunmapId);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunMapId", DbType.String, snapmirror.DRLunmapId);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunSerialNo", DbType.String, snapmirror.PRLunSerialNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunSerialNo", DbType.String, snapmirror.DRLunSerialNo);
                   


#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        snapmirror = reader.Read()
                            ? CreateEntityBuilder<SnapMirror>().BuildEntity(reader, snapmirror)
                            : null;
                    }

                    if (snapmirror == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "SnapMirror already exists. Please specify another snapmirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this snapmirror.");
                                }
                        }
                    }

                    return snapmirror;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting SnapMirror Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        /// <summary>
        ///     Update <see cref="snapmirror" />into snapmirror table.
        /// </summary>
        /// <param name="snapmirror">snapmirror</param>
        /// <returns>snapmirror</returns>
        /// <author>Pratik Thorve</author>
        SnapMirror ISnapMirrorDataAccess.Update(SnapMirror snapmirror)
        {
            try
            {
                const string sp = "SnapMirror_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, snapmirror.Id);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.Int32, snapmirror.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring+"iMCPRServerId", DbType.Int32, snapmirror.MCPRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iMCDRServerId", DbType.Int32, snapmirror.MCDRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iPRStorageId", DbType.String, snapmirror.PRStorageId);
                    Database.AddInParameter(cmd, Dbstring+"iDRStorageId", DbType.String, snapmirror.DRStorageId);
                    Database.AddInParameter(cmd, Dbstring+"iMode", DbType.Int32, snapmirror.Mode);
                    Database.AddInParameter(cmd, Dbstring+"iPRVolume", DbType.String, snapmirror.PRVolume);
                    Database.AddInParameter(cmd, Dbstring+"iDRVolume", DbType.String, snapmirror.DRVolume);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunPath", DbType.String, snapmirror.PRLunSerialNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunPath", DbType.String, snapmirror.DRLunSerialNo);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunMapId", DbType.String, snapmirror.PRLunmapId);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunMapId", DbType.String, snapmirror.DRLunmapId);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunSerialNo", DbType.String, snapmirror.PRLunSerialNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunSerialNo", DbType.String, snapmirror.DRLunSerialNo);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        snapmirror = reader.Read()
                            ? CreateEntityBuilder<SnapMirror>().BuildEntity(reader, snapmirror)
                            : null;
                    }

                    if (snapmirror == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "SnapMirror already exists. Please specify another snapmirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this snapmirror.");
                                }
                        }
                    }

                    return snapmirror;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While updating SnapMirror Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        SnapMirror ISnapMirrorDataAccess.UpdateByReplicationId(SnapMirror snapmirror)
        {
            try
            {
                const string sp = "SNAPMIRROR_UPDATEREPLICATIONID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.Int32, snapmirror.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring+"iMCPRServerId", DbType.Int32, snapmirror.MCPRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iMCDRServerId", DbType.Int32, snapmirror.MCDRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iPRStorageId", DbType.String, snapmirror.PRStorageId);
                    Database.AddInParameter(cmd, Dbstring+"iDRStorageId", DbType.String, snapmirror.DRStorageId);
                    Database.AddInParameter(cmd, Dbstring+"iMode", DbType.Int32, snapmirror.Mode);
                    Database.AddInParameter(cmd, Dbstring+"iPRVolume", DbType.String, snapmirror.PRVolume);
                    Database.AddInParameter(cmd, Dbstring+"iDRVolume", DbType.String, snapmirror.DRVolume);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunPath", DbType.String, snapmirror.PRLunPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunPath", DbType.String, snapmirror.DRLunPath);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunMapId", DbType.String, snapmirror.PRLunmapId);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunMapId", DbType.String, snapmirror.DRLunmapId);
                    Database.AddInParameter(cmd, Dbstring+"iPRLunSerialNo", DbType.String, snapmirror.PRLunSerialNo);
                    Database.AddInParameter(cmd, Dbstring+"iDRLunSerialNo", DbType.String, snapmirror.DRLunSerialNo);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        snapmirror = reader.Read()
                            ? CreateEntityBuilder<SnapMirror>().BuildEntity(reader, snapmirror)
                            : null;
                    }

                    if (snapmirror == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "SnapMirror already exists. Please specify another snapmirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this snapmirror.");
                                }
                        }
                    }

                    return snapmirror;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While updating SnapMirror Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="SnapMirror" />into snapmirror table.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>snapmirr</returns>
        /// <author>Pratik Thorve</author>
        SnapMirror ISnapMirrorDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "SnapMirror_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SnapMirror>()).BuildEntity(reader, new SnapMirror());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISnapMirrorDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        SnapMirror ISnapMirrorDataAccess.GetByReplicationId(int id)
        {
            try
            {
                const string sp = "SnapMirror_GetByReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SnapMirror>()).BuildEntity(reader, new SnapMirror());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISnapMirrorDataAccess.GetByReplicationId(" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="SnapMirror" />into snapmirror table.
        /// </summary>
        /// <param name="groupid">infraObjectId</param>
        /// <returns>SnapMirror</returns>
        /// <author>Pratik Thorve</author>
        SnapMirror ISnapMirrorDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                if (infraObjectId < 1)
                {
                    throw new ArgumentNullException("infraObjectId");
                }

                const string sp = "SnapMirror_GetByInfraObjectId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SnapMirror>()).BuildEntity(reader, new SnapMirror());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISnapMirrorDataAccess.GetByInfraObjectId(" + infraObjectId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="SnapMirror" />into snapmirror table.
        /// </summary>
        /// <param name="name">name</param>
        /// <returns>SnapMirror</returns>
        /// <author>Pratik Thorve</author>
        SnapMirror ISnapMirrorDataAccess.GetByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "SnapMirror_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<SnapMirror>()).BuildEntity(reader, new SnapMirror())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISnapMirrorDataAccess.GetByName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="SnapMirror" />into snapmirror table.
        /// </summary>
        /// <returns>Snapmirro List</returns>
        /// <author>Pratik Thorve</author>
        IList<SnapMirror> ISnapMirrorDataAccess.GetAll()
        {
            try
            {
                const string sp = "SnapMirror_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                   
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildSnapMirrorEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISnapMirrorDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<SnapMirror> ISnapMirrorDataAccess.GetByCompanyId(int companyId, bool isParent)
        {
            try
            {
                const string sp = "SnapMirror_GetByCompanyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, companyId);
                    Database.AddInParameter(cmd, Dbstring+"iisParent", DbType.Int32, isParent);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return BuildSnapMirrorEntities(reader);
                        return BuildSnapMirrorEntitiesNew(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISnapMirrorDataAccess.GetByCompanyId(" + companyId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="SnapMirror" />into snapmirror table.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>bool</returns>
        /// <author>Pratik Thorve</author>
        bool ISnapMirrorDataAccess.DeleteById(int id)
        {
            try
            {
                const string sp = "SnapMirror_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    return Database.ExecuteNonQuery(cmd) > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting SnapMirror Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        /// <summary>
        ///     Fetch <see cref="SnapMirror" />into snapmirror table.
        /// </summary>
        /// <param name="name">name</param>
        /// <returns>bool</returns>
        /// <author>Pratik Thorve</author>
        bool ISnapMirrorDataAccess.IsExistByName(string name)
        {
            try
            {
                const string sp = "SnapMirror_IsExistByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this companyProfile.");
                            }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISnapMirrorDataAccess.IsExistByName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        private IList<SnapMirror> BuildSnapMirrorEntities(IDataReader reader)
        {
            var snapMirrors = new List<SnapMirror>();

            while (reader.Read())
            {
                var snapMirror = new SnapMirror
                {
                    ReplicationBase =
                    {
                        Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                        Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                        reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"])
                    },
                    PRStorageId = Convert.IsDBNull(reader["PRStorageId"])
                ? string.Empty
                : Convert.ToString(reader["PRStorageId"]),
                    DRStorageId = Convert.IsDBNull(reader["DRStorageId"])
                ? string.Empty
                : Convert.ToString(reader["DRStorageId"]),
                    PRVolume = Convert.IsDBNull(reader["PRVolume"])
                ? string.Empty
                : Convert.ToString(reader["PRVolume"]),
                    DRVolume = Convert.IsDBNull(reader["DRVolume"])
                ? string.Empty
                : Convert.ToString(reader["DRVolume"]),
                    PRLunPath = Convert.IsDBNull(reader["PRLunPath"])
                   ? string.Empty
                   : Convert.ToString(reader["PRLunPath"]),
                    DRLunPath = Convert.IsDBNull(reader["DRLunPath"])
                  ? string.Empty
                  : Convert.ToString(reader["DRLunPath"]),

                    PRLunmapId = Convert.IsDBNull(reader["PRLunmapId"])
                   ? string.Empty
                   : Convert.ToString(reader["PRLunmapId"]),
                    DRLunmapId = Convert.IsDBNull(reader["DRLunmapId"])
                   ? string.Empty
                   : Convert.ToString(reader["DRLunmapId"]),

                    PRLunSerialNo = Convert.IsDBNull(reader["PRLunSerialNo"])
                   ? string.Empty
                   : Convert.ToString(reader["PRLunSerialNo"]),
                    DRLunSerialNo = Convert.IsDBNull(reader["DRLunSerialNo"])
                    ? string.Empty
                    : Convert.ToString(reader["DRLunSerialNo"]),
                };

                snapMirrors.Add(snapMirror);
            }
            return (snapMirrors.Count > 0) ? snapMirrors : null;
        }

        private IList<SnapMirror> BuildSnapMirrorEntitiesNew(IDataReader reader)
        {
            var snapMirrors = new List<SnapMirror>();

            while (reader.Read())
            {
                var snapMirror = new SnapMirror
                {
                    ReplicationBase =
                    {
                        Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                        Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                        reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"])
                    },
                    PRStorageId = Convert.IsDBNull(reader["PRStorageId"])
                ? string.Empty
                : Convert.ToString(reader["PRStorageId"]),
                    DRStorageId = Convert.IsDBNull(reader["DRStorageId"])
                ? string.Empty
                : Convert.ToString(reader["DRStorageId"]),
                    PRVolume = Convert.IsDBNull(reader["PRVolume"])
                ? string.Empty
                : Convert.ToString(reader["PRVolume"]),
                    DRVolume = Convert.IsDBNull(reader["DRVolume"])
                ? string.Empty
                : Convert.ToString(reader["DRVolume"])
                  //  PRLunPath = Convert.IsDBNull(reader["PRLunPath"])
                  // ? string.Empty
                  // : Convert.ToString(reader["PRLunPath"]),
                  //  DRLunPath = Convert.IsDBNull(reader["DRLunPath"])
                  //? string.Empty
                  //: Convert.ToString(reader["DRLunPath"]),

                  //  PRLunmapId = Convert.IsDBNull(reader["PRLunmapId"])
                  // ? string.Empty
                  // : Convert.ToString(reader["PRLunmapId"]),
                  //  DRLunmapId = Convert.IsDBNull(reader["DRLunmapId"])
                  // ? string.Empty
                  // : Convert.ToString(reader["DRLunmapId"]),

                  //  PRLunSerialNo = Convert.IsDBNull(reader["PRLunSerialNo"])
                  // ? string.Empty
                  // : Convert.ToString(reader["PRLunSerialNo"]),
                  //  DRLunSerialNo = Convert.IsDBNull(reader["DRLunSerialNo"])
                  //  ? string.Empty
                  //  : Convert.ToString(reader["DRLunSerialNo"]),
                };

                snapMirrors.Add(snapMirror);
            }
            return (snapMirrors.Count > 0) ? snapMirrors : null;
        }

        #endregion Methods
    }
}