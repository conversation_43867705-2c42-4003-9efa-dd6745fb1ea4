﻿namespace CP.UI.Code.Replication
{
    public interface IComponentInfo
    {
        ComponentInfo GetComponentInformation(int infraobjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId);

        ComponentInfo GetComponentInformation(int infraobjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId, int mailBoxId, string mailboxname);

      //  ComponentInfo GetComponentInformation(int infraObjectId, int prServerId);
    }
}