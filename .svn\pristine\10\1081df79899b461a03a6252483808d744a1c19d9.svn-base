﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class MaxDBMonitorDataAccess : BaseDataAccess, IMaxDBMonitorDataAccess
    {
        #region Constructors

        public MaxDBMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<MaxDBMonitor> CreateEntityBuilder<MaxDBMonitor>()
        {
            return (new MaxDBMonitorBuilder()) as IEntityBuilder<MaxDBMonitor>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Get <see cref="maxdb_monitor" />from maxdb_monitor_logs table by Id.
        /// </summary>
        /// <param name="InfraobjectId">InfraobjectId of the maxdb_monitor</param>
        /// <returns>maxdb_monitor</returns>
        /// <author><PERSON><PERSON></author>
        MaxDBMonitor IMaxDBMonitorDataAccess.LogsGetByInfraId(int infraobjectId)
        {
            try
            {
                const string sp = "MaxDB_Moni_Logs_GetByInfratId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, infraobjectId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<MaxDBMonitor>()).BuildEntity(reader, new MaxDBMonitor()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IMaxDBMonitorDataAccess.LogsGetByInfraId(" + infraobjectId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="maxdb_monitor" />from maxdb_monitor_status table by Id.
        /// </summary>
        /// <param name="InfraobjectId">InfraobjectId of the maxdb_monitor</param>
        /// <returns>maxdb_monitor</returns>
        /// <author>Uma Mehavarnan</author>
        MaxDBMonitor IMaxDBMonitorDataAccess.StatusGetByInfraId(int infraobjectId)
        {
            try
            {
                const string sp = "MaxDB_Moni_Status_GetByInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, infraobjectId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<MaxDBMonitor>()).BuildEntity(reader, new MaxDBMonitor()) : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IMaxDBMonitorDataAccess.StatusGetByInfraId(" + infraobjectId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="maxdb_monitor" />from maxdb_monitor_logs table
        /// </summary>
        /// <param name="InfraobjectId", StartDate, LastDate>InfraobjectId, StartDate, LastDate</param>
        /// <returns>maxd_monitor</returns>
        /// <author>Uma Mehavarnan</author>
        IList<MaxDBMonitor> IMaxDBMonitorDataAccess.LogsGetByDate(int infraobjectId, DateTime startDate, DateTime lastDate)
        {
            try
            {
                const string sp = "MaxDB_Moni_Logs_GetBYDate";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, infraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.DateTime, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.DateTime, lastDate);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<MaxDBMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IMaxDBMonitorDataAccess.LogsGetByDate(" + infraobjectId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods

    }
}


