﻿<?xml version="1.0" encoding="utf-8"?>
<log4net debug="false">
  <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
    <file value="Logs/log4net.log" />
    <appendToFile value="true" />
    <rollingStyle value="Size" />
    <maxSizeRollBackups value="10" />
    <maximumFileSize value="1000KB" />
    <staticLogFileName value="true" />
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%d [%t] %-5p %c - %m%n" />
    </layout>
  </appender>
  <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%d [%t] %-5p %c - %m%n" />
    </layout>
  </appender>
  <appender name="OutputDebugStringAppender" type="log4net.Appender.OutputDebugStringAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%-5p %m - %c -%n" />
    </layout>
  </appender>
  <appender name="TraceAppender" type="log4net.Appender.TraceAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%d [%t] %-5p %c - %m%n" />
    </layout>
  </appender>
  <appender name="AspNetTraceAppender" type="log4net.Appender.AspNetTraceAppender">
    <layout type="log4net.Layout.PatternLayout">
      <conversionPattern value="%d [%t] %-5p %c - %m%n" />
    </layout>
  </appender>
  <root>
    <level value="DEBUG" />
    <appender-ref ref="RollingFileAppender" />
    <appender-ref ref="OutputDebugStringAppender" />
    <appender-ref ref="ConsoleAppender" />
    <appender-ref ref="TraceAppender" />
    <appender-ref ref="AspNetTraceAppender" />
  </root>
  <logger name="StructureMap" additivity="false">
    <level value="WARN" />
    <appender-ref ref="OutputDebugStringAppender" />
    <appender-ref ref="ConsoleAppender" />
  </logger>
  <logger name="NHibernate" additivity="false">
    <level value="INFO" />
    <appender-ref ref="AspNetTraceAppender" />
  </logger>
</log4net>