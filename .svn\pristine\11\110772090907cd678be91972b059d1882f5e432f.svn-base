﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IApprovalLevelProcessDataAccess
    {
        AppLevelProcess AddApprovalLevel(AppLevelProcess approval);

        bool Update(AppLevelProcess approval);

        AppLevelProcess GetById(int id);

        IList<AppLevelProcess> GetByApprovalProcessLevelByApId(int apId);

        bool DeleteById(int id);

        IList<AppLevelProcess> GetAll();


        AppLevelProcess GetApprovalLevelProcess_UserById(int id);

        AppLevelProcess GetApprovalLevelProcess_UserById_new(int id);
        AppLevelProcess GetApprovalLevelProcess_ToVerify(int id);

        bool updateverifystatus(int id, string status,int value , DateTime date , string VerifiedBy);

        bool ApproLevelProcesUpdate_Details(AppLevelProcess approval);

        

    }
}
