﻿using Microsoft.Practices.EnterpriseLibrary.Data;
using System.Collections.Generic;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System.Linq;
using System.Configuration;
using System;
using Npgsql;


namespace CP.DataAccess
{
    internal sealed class EBDRReplicationDataAcess : BaseDataAccess, IeBDRProfileDataAccess
    {
    
        #region Constructors

        public EBDRReplicationDataAcess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<EBDRProfileDetails> CreateEntityBuilder<EBDRProfileDetails>()
        {
            return (new eBDRProfileDetailsBuilder()) as IEntityBuilder<EBDRProfileDetails>;
        }

        #endregion Constructors


        public IList<EBDRProfileDetails> GetAlleBDRProfileDetails()
        {

            try
            {

                IList<EBDRProfileDetails> _EBDRProfileDetails = new List<EBDRProfileDetails>();

                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_reports_db;User name=********;Password=********";

                string ss = GetConnectionDB("report");

                cn.ConnectionString = ss;

                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                string Query = "SELECT migration_name, src_fmt,tgt_fmt, machine, os_name, action FROM migration_reports where ismd=" + false + " and rep_id=0";

                var cmmmd = new NpgsqlCommand(Query, cn);
                cn.Open();
                dr = cmmmd.ExecuteReader();
                //    dr.Read();

                while (dr.Read())
                {
                    var _EBDRProfile = new EBDRProfileDetails();

                    //infraObject.Id = Convert.ToInt32(dr[0].ToString());
                    _EBDRProfile.Profile_Name = dr[0].ToString();
                    string Source = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32((dr[1].ToString())));

                    _EBDRProfile.Source_Host = Source;

                    string Target = Enum.GetName(typeof(OsTypeeBDR), Convert.ToInt32(dr[2].ToString()));

                    _EBDRProfile.Target_Host = Target;
                    // infraObject.MachinName = (dr[3].ToString());

                    _EBDRProfile.OsType = (dr[4].ToString());

                    string stringValue = Enum.GetName(typeof(ActionTypeeBDR), Convert.ToInt32((dr[5].ToString())));
                    //   infraObject.Status = stringValue;

                    if ((dr[5].ToString()) == "36")
                    {
                        _EBDRProfile.Status = "PAUSED";
                        _EBDRProfile.Health = "UP";
                    }
                    else if ((dr[5].ToString()) == "17" || (dr[5].ToString()) == "4")
                    {
                        _EBDRProfile.Status = "Failed";
                        _EBDRProfile.Health = "Down";
                    }
                    else
                    {
                        _EBDRProfile.Status = "Running";
                        _EBDRProfile.Health = "UP";
                    }

                    _EBDRProfile.RPO = "00:00:00";

                    _EBDRProfileDetails.Add(_EBDRProfile);

                }
                return _EBDRProfileDetails;


            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                  ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                  "Error In DAL While Executing Function Signature GetAlleBDRProfileDetails" + Environment.NewLine +
                  "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }

        public bool ResumeeBDRProfile(string ProfileName, string Connection)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                cn.ConnectionString = Connection;// GetConnectionDB("event");//txteventcon.Text;
                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;


                //int Actiondetails = CheckPerformResume(ProfileName);


                //if (Actiondetails == 36)
                //{
                string query = "UPDATE status SET event=35 ,is_trigger=" + true + " WHERE migration_name='" + ProfileName + "'";

                var cmmmd1 = new NpgsqlCommand(query, cn);
                cn.Open();
                int isuccess = cmmmd1.ExecuteNonQuery();
                cn.Close();

                return true;

                //  }
                //else
                //{
                //    return false;
                //}


            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                  ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                  "Error In DAL While Executing Function Signature ResumeeBDRProfile" + Environment.NewLine +
                  "SYSTEM MESSAGE : " + ex.Message, ex);
            }

            return false;
        }


        public bool PauseeBDRProfile(string ProfileName, string Connection)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //    cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                cn.ConnectionString = Connection; //GetConnectionDB("event");// txteventcon.Text;
                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;
                //string chk = CheckPerformPause(para[i]);

                //if (!string.IsNullOrEmpty(chk))
                //{
                string query = "UPDATE status SET event=32 ,is_trigger=" + true + " WHERE migration_name='" + ProfileName + "'";

                var cmmmd = new NpgsqlCommand(query, cn);
                cn.Open();
                int isuccess = cmmmd.ExecuteNonQuery();
                cn.Close();

                return true;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                  ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                  "Error In DAL While Executing Function Signature ResumeeBDRProfile" + Environment.NewLine +
                  "SYSTEM MESSAGE : " + ex.Message, ex);
            }

            return false;

        }


        public int CheckPerformResume(string para)
        {
            try
            {
                var cn = new NpgsqlConnection();
                NpgsqlDataReader dr;

                //cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                //  cn.ConnectionString = "SERVER=localhost;Database=src_event_db;User name=********;Password=********";
                cn.ConnectionString = GetConnectionDB("report");//txtreportcon.Text;
                //var cmmmd = new NpgsqlCommand("select * from action", cn);

                //          SELECT migration_name, src_fmt, 
                //     tgt_fmt, machine, os_name, action
                //FROM migration_reports;

                int Action = 0;

                string ChkQuery = "select action FROM migration_reports where migration_name='" + para + "' and ismd=" + true + ";";
                var cmmmd = new NpgsqlCommand(ChkQuery, cn);

                cn.Open();
                dr = cmmmd.ExecuteReader();

                while (dr.Read())
                {
                    Action = Convert.ToInt32(dr[0].ToString());
                }
                cn.Close();


                return Action;
            }
            catch (Exception ex)
            {
                return 0;
            }

        }





        protected string GetConnectionDB(string Connection)
        {
            try
            {
                if (Connection.Equals("event"))
                {
                    string encrypt = ConfigurationManager.ConnectionStrings["EBDRConnectionEvent"].ConnectionString;

                    if (!String.IsNullOrEmpty(encrypt))
                    {
                        return (CryptographyHelper.Md5Decrypt(encrypt));
                    }
                }
                else
                {

                    string encrypt = ConfigurationManager.ConnectionStrings["EBDRConnectionReport"].ConnectionString;

                    if (!String.IsNullOrEmpty(encrypt))
                    {
                        return (CryptographyHelper.Md5Decrypt(encrypt));
                    }
                }

                return "";
            }
            catch (Exception ex)
            {
                return "";
            }
        }




    }
}
