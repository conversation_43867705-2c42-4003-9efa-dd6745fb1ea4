﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using log4net;

namespace CP.UI
{
    public partial class IncidentManager : BasePage
    {
        private bool _isNoRecord;

        public string str = "";
        private readonly ILog _logger = LogManager.GetLogger(typeof(IncidentManager));
        private int _maxIncidentId;

        public IList<InfraObject> InfraList { get; set; }

        public override void PrepareView()
        {
            Utility.SelectMenu(Master, "Module5");
            if (LoggedInUserRole == UserRole.Operator || LoggedInUserRole == UserRole.Manager || LoggedInUserRole == UserRole.Administrator)
            {
                int test = LoggedInUser.Id;

                if (test != null)
                {
                    PopulateGroupByRole(test);
                }
            }
            else
            {
               
                PopulateGroupName(); ;
            }

            Session["Incident"] = null;

            if (Session["LastIncidentId"] == null)
            {
                Session["LastIncidentId"] = LoggedInUser.LastIncidentId;
            }

            InfraList = null;

           // PopulateGroupName();

            IList<Incident> incidents = GetIncidentDetails();

            IList<Incident> incidents1 = GetIncidentDetails1();

            // Session["Incident"] = incidents;

            if (incidents1.Count != 0)
            {
                lvIncident.DataSource = incidents1;
                lvIncident.DataBind();
            }

            if (incidents != null)
            {
                if (incidents.Count != 0)
                {
                    lvIncident.DataSource = incidents;
                    lvIncident.DataBind();
                    _maxIncidentId = incidents[0].Id;
                    Facade.UpdateUserByIncidentId(LoggedInUserId, _maxIncidentId);
                    Session["LastIncidentId"] = _maxIncidentId;
                }
            }
        }

        private void PopulateGroupName()
        {
            InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);

            if (InfraList != null)
            {
                ddlInfra.DataSource = InfraList;
                ddlInfra.DataTextField = "Name";
                ddlInfra.DataValueField = "Id";
                ddlInfra.DataBind();
                ddlInfra.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
            }
            else
            {
                ddlInfra.Items.Insert(0, new ListItem("No InfraObject Assigned", "0"));
            }
        }

        private void PopulateGroupByRole(int id)
        {
            InfraList = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(id, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

            if (InfraList != null)
            {
                ddlInfra.DataSource = InfraList;
                ddlInfra.DataTextField = "Name";
                ddlInfra.DataValueField = "Id";
                ddlInfra.DataBind();
                ddlInfra.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
            }
            else
            {
                ddlInfra.Items.Insert(0, new ListItem("No InfraObject Assigned", "0"));
            }
        }

        protected void ddlInfra_SelectedIndexChanged(object sender, EventArgs e)
        {
            Session["LastIncidentId"] = null;
            if (DataPager1 != null)
            {
                DataPager1.SetPageProperties(0, DataPager1.PageSize, false);
            }
        }

        protected void BtnDateClick(object sender, EventArgs e)
        {
            if (DataPager1 != null)
            {
                DataPager1.SetPageProperties(0, DataPager1.MaximumRows, false);
            }
            ulmessage.Visible = false;

            string dat = DateTime.Now.ToString("yyyy-MM-dd");
            Session["LastIncidentId"] = null;
            Session["Incident"] = null;

            if (txtToDate.Text.Length == 0 && txtFromDate.Text.Length == 0 && ddlInfra.SelectedValue == "0")
            {
                //IList<Alert> alerts = GetAlertDetails();
                var incidents = Facade.GetAllIncident();
                var nIncident = new List<Incident>();

                InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);

                if (incidents != null)
                {
                    foreach (Incident incident in incidents)
                    {
                        if (InfraList.Count > 0)
                        {
                            foreach (InfraObject @infra in InfraList.Where(@infra => incident.InfraObjectId == @infra.Id))
                            {
                                incident.GroupName = @infra.Name;
                                nIncident.Add(incident);
                                break;
                            }
                        }
                    }
                }

                if (incidents.Count != 0)
                {
                    lvIncident.DataSource = nIncident;
                    lvIncident.DataBind();
                }
                else
                {
                    lvIncident.DataSource = null;
                    lvIncident.DataBind();
                    var lblErr = (Label)lvIncident.FindControl("lblError");
                    if (lblErr != null)
                    {
                        dataPager2.Visible = false;
                    }
                }
            }
            else if (txtToDate.Text.Length == 0 && txtFromDate.Text.Length == 0)
            {
                IList<Incident> incidents = GetIncidentDetails();

                if (incidents.Count != 0)
                {
                    lvIncident.DataSource = incidents;
                    lvIncident.DataBind();
                }
                else
                {
                    lvIncident.DataSource = null;
                    lvIncident.DataBind();
                    var lblErr = (Label)lvIncident.FindControl("lblError");
                    if (lblErr != null)
                    {
                        dataPager2.Visible = false;
                    }
                }
            }
            else if (txtToDate.Text.Length > 0 && txtFromDate.Text.Length > 0)
            {
                if (Convert.ToDateTime(dat) < Convert.ToDateTime(txtToDate.Text))
                {
                    ulmessage.Visible = true;
                    lblDatemsg.Text = "Start Date" + " " + '(' + txtToDate.Text + ')' + "can not greater than Today Date";
                    return;
                }

                if (Convert.ToDateTime(txtToDate.Text) > Convert.ToDateTime(txtFromDate.Text))
                {
                    ulmessage.Visible = true;
                    lblDatemsg.Text = "Start date" + " " + '(' + txtToDate.Text + '"' + "can not be greater than End date" + ')' + txtFromDate.Text + '"';
                    return;
                }
                if (Convert.ToDateTime(dat) < Convert.ToDateTime(txtFromDate.Text))
                {
                    ulmessage.Visible = true;
                    lblDatemsg.Text = "End Date" + " " + '(' + txtFromDate.Text + ')' + " can not greater than Today Date";
                }
            }
            else if ((txtToDate.Text.Length == 0 && txtFromDate.Text.Length > 0) || (txtToDate.Text.Length > 0 && txtFromDate.Text.Length == 0))
            {
                ulmessage.Visible = true;
                lblDatemsg.Text = "Please Choose Date";
            }
        }

        protected string GetSeverityTypeCss(object item)
        {
            string severity = string.Empty;
            switch (Convert.ToString(item))
            {
                case "High":
                    severity = "../Images/icons/high_16.png";
                    break;

                case "Low":
                    severity = "../Images/icons/low_16.png";
                    break;

                case "VeryHigh":
                    severity = "../Images/icons/veryhigh_16.png";
                    break;

                case "Normal":
                    severity = "../Images/icons/normal_16.png";
                    break;
            }
            return severity;
        }

        protected string GetRowStyle(object item)
        {
            string incident = string.Empty;
            switch (Convert.ToString(item))
            {
                case "High":
                    incident = "High";
                    break;

                case "Low":
                    incident = "Low";
                    break;

                case "VeryHigh":
                    incident = "VeryHigh";
                    break;

                case "Normal":
                    incident = "Normal";
                    break;
            }
            return incident;
        }

        private IList<Incident> GetIncidentDetails1()
        {
            IList<Incident> incidents = new List<Incident>();
            IList<Incident> newIncident = new List<Incident>();

            if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty)
            {
                incidents = Facade.GetAllIncident();
            }
            if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty && ddlInfra.SelectedValue != "0")
            {
                incidents = Facade.GetIncidentByGroupId(Convert.ToInt32(ddlInfra.SelectedValue));
            }
            else if (txtToDate.Text != string.Empty && txtFromDate.Text != string.Empty && ddlInfra.SelectedValue != "0")
            {
                incidents = Facade.GetIncidentByDate(Convert.ToInt32(ddlInfra.SelectedValue), txtToDate.Text, txtFromDate.Text);
            }

            InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);

            if (incidents != null)
            {
                foreach (Incident incident in incidents)
                {
                    if (InfraList != null)
                    {
                        if (InfraList.Count > 0)
                        {
                            foreach (InfraObject @infra in InfraList.Where(@infra => incident.InfraObjectId == @infra.Id))
                            {
                                incident.InfraObjectName = @infra.Name;
                                newIncident.Add(incident);
                                break;
                            }
                        }
                    }
                }
            }
            return newIncident;
        }

        private IList<Incident> GetIncidentDetails()
        {
            IList<Incident> incidents = new List<Incident>();
            IList<Incident> newIncident = new List<Incident>();

            if (Session["Incident"] != null)
            {
                return Session["Incident"] as IList<Incident>;
            }
            else if (Session["LastIncidentId"] != null)
            {
                incidents = Facade.GetIncidentByLastIncidentId(Convert.ToInt32(Session["LastIncidentId"]));
            }
            else if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty)
            {
                incidents = Facade.GetAllIncident();
            }
            if (txtToDate.Text == string.Empty && txtFromDate.Text == string.Empty && ddlInfra.SelectedValue != "0")
            {
                incidents = Facade.GetIncidentByGroupId(Convert.ToInt32(ddlInfra.SelectedValue));
            }
            else if (txtToDate.Text != string.Empty && txtFromDate.Text != string.Empty && ddlInfra.SelectedValue != "0")
            {
                incidents = Facade.GetIncidentByDate(Convert.ToInt32(ddlInfra.SelectedValue), txtToDate.Text, txtFromDate.Text);
            }

            InfraList = LoggedInUser.InfraObjectAllFlag ? Facade.GetAllInfraObject() : Facade.GetInfraObjectByLoginId(LoggedInUserId);

            if (incidents != null)
            {
                foreach (Incident incident in incidents)
                {
                    if (InfraList != null)
                    {
                        if (InfraList.Count > 0)
                        {
                            foreach (InfraObject @infra in InfraList.Where(@infra => incident.InfraObjectId == @infra.Id))
                            {
                                incident.InfraObjectName = @infra.Name;
                                newIncident.Add(incident);
                                break;
                            }
                        }
                    }
                }
            }
            return newIncident;
        }

        protected void lvIncident_PreRender(object sender, EventArgs e)
        {
            if (ulmessage.Visible == false)
            {
                lblmsg.Text = "";
                if (IsPostBack)
                {
                    if (!_isNoRecord)
                    {
                        var alertData = GetIncidentDetails();
                        if (alertData.Count > 0)
                        {
                            lvIncident.DataSource = alertData;
                            lvIncident.DataBind();

                            dataPager2.Visible = true;
                            DataPager1.Visible = true;
                        }
                        else
                        {
                            lvIncident.DataSource = null;
                            lvIncident.DataBind();

                            dataPager2.Visible = false;
                            DataPager1.Visible = false;
                        }
                    }
                    else
                    {
                        lvIncident.DataSource = null;
                        lvIncident.DataBind();
                        dataPager2.Visible = false;
                    }
                }
            }
            else
            {
                lvIncident.DataSource = null;
                lvIncident.DataBind();
                lblmsg.Text = "No Record Found";
                lblmsg.CssClass = "error";
                dataPager2.Visible = false;
                DataPager1.Visible = false;
            }
        }
    }
}