﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;


namespace CP.DataAccess
{
    internal sealed class DefaultMoniServiceBuilder : IEntityBuilder<DefaultMoniService>
    {


        IList<DefaultMoniService> IEntityBuilder<DefaultMoniService>.BuildEntities(IDataReader reader)
        {
            List<DefaultMoniService> DefaultMoniService = new List<DefaultMoniService>();

            while (reader.Read())
            {
                DefaultMoniService.Add(((IEntityBuilder<DefaultMoniService>)this).BuildEntity(reader, new DefaultMoniService()));
            }

            return (DefaultMoniService.Count > 0) ? DefaultMoniService : null;
        }


        DefaultMoniService IEntityBuilder<DefaultMoniService>.BuildEntity(IDataReader reader, DefaultMoniService dsmon)
        {

            dsmon.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            dsmon.OSType = Convert.IsDBNull(reader["OSType"]) ? string.Empty : Convert.ToString(reader["OSType"]);
            dsmon.MonitorServices = Convert.IsDBNull(reader["MonitorServices"]) ? string.Empty : Convert.ToString(reader["MonitorServices"]);

            return dsmon;
        }

    }
}
