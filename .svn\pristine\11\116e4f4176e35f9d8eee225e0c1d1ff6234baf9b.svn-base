﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess.TPRCMonitoring
{
    internal sealed class TPRCMonitorDataAccess : BaseDataAccess, ITPRCMonitorDataAccess
    {
        #region Constructors

        public TPRCMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<TPRCMonitors> CreateEntityBuilder<TPRCMonitors>()
        {
            return (new TPRCMonitoringBuilder()) as IEntityBuilder<TPRCMonitors>;
        }

        #endregion Constructors

        #region methods

        TPRCMonitor ITPRCMonitorDataAccess.GetByInfraObjectId(int infraobjectid)
        {
            try
            {
                const string sp = "TPRCMONITOR_GETBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iinfraObjectId", DbType.Int32, infraobjectid);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //if (reader.Read())
                        //{
                        //    return (CreateEntityBuilder<TPRCMonitor>()).BuildEntity(reader,
                        //        new TPRCMonitor());
                        return reader.Read() ? (CreateEntityBuilder<TPRCMonitor>()).BuildEntity(reader, new TPRCMonitor()) : null;
                        //}
                        //return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISnapMirrorMonitorDataAccess.GetByGroupId(" +
                    infraobjectid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<TPRCMonitor> ITPRCMonitorDataAccess.GetByDate(int infraObjectid, string startdate, string enddate)
        {
            try
            {
                const string sp = "TPRCMONITOR_LOGS_GETBYDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "IINFRAOBJECTID", DbType.Int32, infraObjectid);
                    Database.AddInParameter(cmd, Dbstring + "ISTARTDATE", DbType.String, startdate);
                    Database.AddInParameter(cmd, Dbstring + "IENDDATE", DbType.String, enddate);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<TPRCMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ITPRCMonitor.GetByDate(" + infraObjectid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }



        IList<TPRCMonitor> ITPRCMonitorDataAccess.GetHourlyByInfraObjectId(int infraobjectId)
        {
            try
            {
                if (infraobjectId < 1)
                {
                    throw new ArgumentNullException("infraobjectId");
                }

                const string sp = "TPRCMONILOGS_HOURBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraobjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<TPRCMonitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IExchangeHealthDataAccess.ITPRCMonitorDataAccess(" +
                    infraobjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion methods
    }
}
