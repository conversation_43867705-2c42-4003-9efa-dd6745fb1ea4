﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using log4net;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI;

namespace CP.UI
{
    public partial class AccessManager : AccessManagerBagePageEditor
    {
        readonly IList<ListItem> _previousSelectedItems = new List<ListItem>();
        readonly IList<ListItem> _previousApplicationSelectedItems = new List<ListItem>();
        readonly IList<ListItem> _previousRightSelectedItems = new List<ListItem>();
        private UserControl _userControl = new UserControl();
        HttpContext Current = HttpContext.Current;
        private static readonly ILog _logger = LogManager.GetLogger(typeof(AccessManager));
        public override string MessageInitials
        {
            get { return "AccessManager"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.User.UserList;
                }
                return string.Empty;
            }
        }

        public override void SaveEditor()
        {

            IList<InfraObject> SelectedInfraobjects = ShowCheckedNodes(RadTreeView1);

            if (CurrentEntityId > 0)
            {
                CurrentEntity.CreatorId = Convert.ToInt32(Current.Session["LoggedInUserId"]);
                CurrentEntity.UpdatorId = Convert.ToInt32(Current.Session["LoggedInUserId"]);
                CurrentEntity.UserInformation.CreatorId = Convert.ToInt32(Current.Session["LoggedInUserId"]);
                var userInfraObject = new UserInfraObject { UserId = CurrentEntity.Id };
                var selectedItem = Utility.GetSelectedItem(cskgoup);
                var selectedProfiles = Utility.GetSelectedItem(cskgoup);
                if (selectedItem != null)
                {
                    foreach (var listItem in selectedItem)
                    {
                        if (listItem.Text == "ALL")
                        {
                            continue;
                        }
                        userInfraObject.InfraObjectId = Convert.ToInt32(listItem.Value);
                        userInfraObject.CreatorId = Convert.ToInt32(Current.Session["LoggedInUserId"]);
                        Facade.AddUseInfraObject(userInfraObject);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "User InfraObject", UserActionType.CreateApplicationUserGroup, "The User InfraObject '" + userInfraObject.UserId + "' was added to the user_InfraObject table", LoggedInUserId);
                    }
                }
                else
                {
                    if (CurrentEntity != null)
                    {
                        var previousItems = Session["PreviousItem"] as IList<ListItem>;
                        var previousapplicationItems = Session["ApplicationPreviousItem"] as IList<ListItem>;

                        var currentSelectedItems = Utility.GetSelectedItem(cskgoup);
                        if (previousItems != null && currentSelectedItems != null)
                        {
                            CompareToCollection(previousItems, currentSelectedItems, 0);
                        }
                    }
                }
            }
        }

        public override void BuildEntities()
        {
            CurrentEntity.UserId = Convert.ToInt32(ddlusers.SelectedValue);
            if (LoggedInUserRole.ToString() == "SuperAdmin")
            {
                if (ddlroles.SelectedIndex == 1 || ddlroles.SelectedIndex == 2)//|| ddlRole.SelectedIndex == 3 || ddlRole.SelectedIndex == 4
                {
                    CurrentEntity.InfraObjectAllFlag = true;
                    CurrentEntity.ApplicationAllFlag = true;
                    Session["InfraObject"] = "T";
                }
                else if (cskgoup.SelectedItem == null)
                {
                    Session["InfraObject"] = "F";

                }
                else
                {
                    if (cskgoup.SelectedItem != null)
                    {
                        CurrentEntity.InfraObjectAllFlag = cskgoup.SelectedItem.Text == "ALL";
                        Session["InfraObject"] = "T";
                    }
                    else
                    {
                        Session["InfraObject"] = "F";
                        CurrentEntity.InfraObjectAllFlag = false;
                    }
                }
            }
            if (LoggedInUserRole.ToString() == "Administrator")
            {
                if (ddlroles.SelectedIndex == 0)
                {
                    CurrentEntity.InfraObjectAllFlag = true;
                    CurrentEntity.ApplicationAllFlag = true;
                    Session["InfraObject"] = "T";
                }
                else if (cskgoup.SelectedItem == null)
                {
                    Session["InfraObject"] = "F";

                }
                else
                {
                    if (cskgoup.SelectedItem != null)
                    {
                        CurrentEntity.InfraObjectAllFlag = cskgoup.SelectedItem.Text == "ALL";
                        Session["InfraObject"] = "T";
                    }
                    else
                    {
                        Session["InfraObject"] = "F";
                        CurrentEntity.InfraObjectAllFlag = false;
                    }
                }
                CurrentEntity.Role = (UserRole)Enum.Parse(typeof(UserRole), ddlroles.SelectedValue, true);
                if (CurrentEntity.Role == UserRole.Custom)
                {
                    CurrentEntity.Role = (UserRole)Enum.Parse(typeof(UserRole), txtRolesubtype.Text, true);
                }
            }

        }

        public override void PrepareEditView()
        {
            if (CurrentEntityId <= 0) return;
            rfvusers.Visible = false;
            rfvusers.Enabled = false;
            rfvroles.Visible = false;
            rfvroles.Enabled = false;
            ddlusers.SelectedValue = Convert.ToString(CurrentUser.UserId);
            ddlroles.SelectedValue = Convert.ToString(CurrentUser.Role);
            CheckGroupBoxList();
        }

        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            Utility.SelectMenu(Master, "Module8");

            ddlusers.Attributes.Add("onblur", "ValidatorValidate(" + rfvusers.ClientID + ")");
            ddlroles.Attributes.Add("onblur", "ValidatorValidate(" + rfvroles.ClientID + ")");
            txtRolesubtype.Attributes.Add("onblur", "ValidatorValidate(" + rfvrolesubtype.ClientID + ")");
            //Utility.PopulateUsers(ddlusers, true);
            ddlusers.ClearItems();
            ddlroles.ClearItems();
            var userlist = Facade.GetAllUsers();

            if (!Convert.ToBoolean(HttpContext.Current.Session["_isUserSuperAdmin"]))
                userlist = (from users in userlist
                            where users.CreatorId == Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"])
                          select users).ToList();


            if (userlist != null)
            {
               // userlist = userlist.Except(userlist.Where(x => x.Role == UserRole.SuperAdmin)).ToList();
                userlist = (from usrlst in userlist where usrlst.Role == UserRole.Custom select usrlst).ToList();
                ddlusers.DataSource = userlist;
                ddlusers.DataTextField = "UserName";
                ddlusers.DataValueField = "Id";
                ddlusers.DataBind();

                ddlusers.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectUser, "0"));
            }
            else
            {
                ddlusers.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectUser, "0"));
            }

           
            if (LoggedInUserRole.ToString() == "SuperAdmin")
            {
                ddlroles.AddItem("Select Role", "0000");
                ddlroles.AddItem("SuperAdmin", "SuperAdmin");
                ddlroles.AddItem("Administrator", "Administrator");
                ddlroles.AddItem("Operator", "Operator");
                ddlroles.AddItem("Manager", "Manager");
                ddlroles.AddItem("Custom", "Custom");
            }
            else
            {
                ddlroles.AddItem("Select Role", "0000");
                ddlroles.AddItem("Administrator", "Administrator");
                ddlroles.AddItem("Operator", "Operator");
                ddlroles.AddItem("Manager", "Manager");
                ddlroles.AddItem("Custom", "Custom");
            }
            PrepareEditView();
        }

        private IList<InfraObject> ShowCheckedNodes(RadTreeView treeView)
        {
            //UpdatePanel2.Update();
            //  string message = string.Empty;
            IList<RadTreeNode> nodeCollection = treeView.CheckedNodes;
            int cnt = 0;

            //if(nodeCollection == null)
            //{
            //    Lblradtree.Text = "Please check at least one node";
            //                    return;
            //}
            //ArrayList[] coll = nodeCollection.ToArray();

            IList<InfraObject> infralist = new List<InfraObject>();
            foreach (RadTreeNode node in nodeCollection)
            {

                if (node.Text == "All")
                {
                    IFacade _facade = new Facade();
                    infralist = _facade.GetAllInfraObject();
                    node.Checked = true;

                }
                else
                {

                    cnt++;
                    //  message += node.FullPath + "<br/>";

                    if (cnt > 2)
                    {
                        string dsa = node.FullPath;

                        if (dsa.Contains("/"))
                        {

                            string[] ff = dsa.Split('/');

                            if (ff.Length > 2)
                            {

                                if (ff[2] != null && ff[2] != "")
                                {
                                    string infraname = ff[2];

                                    if (infraname != null)
                                    {
                                        IFacade _facade = new Facade();
                                        var infr = _facade.GetInfraObjectByName(infraname);
                                        infralist.Add(infr);
                                    }
                                }
                            }
                        }
                        // cnt = 0;


                    }
                }

                // string fd = message.Split('<br/>');
                // infralist.Add(node.FullPath.ToString());

            }
            return infralist;
            //  label.Text = message;
        }

        protected void ddlusers_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {

                _logger.Info("====== Please Select User=====");

                var dashboard1 = ucnAccessmangerConfiguration.FindControl("chksDashboard") as CheckBoxList;
                var Configuration1 = ucnAccessmangerConfiguration.FindControl("chkConfiguration") as CheckBoxList;
                var View1 = ucnAccessmangerConfiguration.FindControl("chkView") as CheckBoxList;
                var Manage1 = ucnAccessmangerConfiguration.FindControl("chkManage") as CheckBoxList;
                var Alerts1 = ucnAccessmangerConfiguration.FindControl("chkAlerts") as CheckBoxList;
                var ITOrchestration1 = ucnAccessmangerConfiguration.FindControl("chkITOrchestration") as CheckBoxList;
                var Reports1 = ucnAccessmangerConfiguration.FindControl("chkReports") as CheckBoxList;
                var ParallelProfile = ucnAccessmangerConfiguration.FindControl("ddlProfiles") as ListBox;
                Utility.PopulateProfiles(ParallelProfile, Convert.ToInt32(ddlusers.SelectedValue));

                var uservalue1 = Facade.GetUserById(Convert.ToInt32(ddlusers.SelectedValue));
                _logger.Info("Select User");

                int entid = uservalue1.Id;
                IList<BusinessService> bsObject = null;

                if (!Convert.ToBoolean(HttpContext.Current.Session["_isUserSuperAdmin"]))
                {

                    _logger.Info("Particular user selected");
                    var getcurrentuserDetails = Facade.GetUserById(Convert.ToInt32(Current.Session["LoggedInUserId"]));
                    bsObject = Facade.GetBusinessServiceByCompanyIdAndRole(Convert.ToInt32(Current.Session["LoggedInUserId"]), getcurrentuserDetails.CompanyId, getcurrentuserDetails.Role, IsParentCompnay, getcurrentuserDetails.InfraObjectAllFlag);
                }
                else
                {
                    bsObject = Facade.GetAllBusinessServices();
                }
                //var bsObject = Facade.GetAllBusinessServices();

                RadTreeView1.Nodes.Clear();

                RadTreeNode all = new RadTreeNode
                {
                    Text = "All"

                };

                RadTreeView1.Nodes.Add(all);

                RadTreeNode node5 = RadTreeView1.FindNodeByText(all.Text);

                node5.Enabled = false;
                node5.Checkable = false;
                node5.Expanded = true;

                foreach (var bs in bsObject)
                {

                    RadTreeNode Rootbsname = new RadTreeNode
                    {
                        Text = "(BusinessService) " + bs.Name,
                        Value = bs.Id.ToString()
                    };

                    RadTreeView1.Nodes.Add(Rootbsname);
                    RadTreeNode node1 = RadTreeView1.FindNodeByText(Rootbsname.Text);

                    node1.Enabled = false;
                    node1.Checkable = false;
                    node1.Expanded = true;


                    var bfList = Facade.GetBusinessFunctionsByBusinessServiceId(bs.Id);
                    if (bfList != null && bfList.Count > 0)
                    {
                        //   Node1.Nodes.Add(Node3);
                        _logger.Info("User is already selected");

                        foreach (var bf in bfList)
                        {

                            RadTreeNode Rootbfname = new RadTreeNode
                            {
                                Text = "(BusinessFunction) " + bf.Name,
                                Value = bf.Id.ToString()
                            };



                            Rootbsname.Nodes.Add(Rootbfname);

                            RadTreeNode node2 = RadTreeView1.FindNodeByText(Rootbfname.Text);
                            node2.Enabled = false;
                            node2.Checkable = false;
                            node2.Expanded = true;

                            //var ioList = Facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(bs.Id, bf.Id);
                            IList<InfraObject> ioList = null;
                            if (!Convert.ToBoolean(HttpContext.Current.Session["_isUserSuperAdmin"]))
                            {
                                ioList = Facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionIdUserId(bs.Id, bf.Id, Convert.ToInt32(Current.Session["LoggedInUserId"]));
                            }
                            else
                            {
                                ioList = Facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(bs.Id, bf.Id);
                            }
                            if (ioList != null && ioList.Count > 0)
                            {

                                foreach (var infra in ioList)
                                {

                                    RadTreeNode Rootinfraname = new RadTreeNode
                                    {
                                        Text = infra.Name,
                                        Value = infra.Id.ToString()
                                    };

                                    Rootbfname.Nodes.Add(Rootinfraname);
                                    RadTreeNode node4 = RadTreeView1.FindNodeByText(infra.Name);

                                    node4.Enabled = false;
                                    node4.Checkable = true;
                                    node4.Expanded = true;

                                    IList<InfraObject> infras = new List<InfraObject>();

                                    IList<UserInfraObject> userinfrass = Facade.GetUserInfraObjectByUserId(entid);

                                    foreach (var inf in userinfrass)
                                    {
                                        int infraid = inf.InfraObjectId;
                                        var infrr = Facade.GetInfraObjectById(infraid);

                                        if (infrr != null)
                                        {

                                            infras.Add(infrr);
                                        }


                                    }

                                    foreach (var inff in infras)
                                    {


                                        if (inff.Name == Convert.ToString(Rootinfraname.Text))
                                        {
                                            string infraname = Convert.ToString(Rootinfraname.Text);
                                            RadTreeNode node = RadTreeView1.FindNodeByText(infraname);
                                            node.Checked = true;

                                            // RadTreeNode node3 = RadTreeView1.FindNodeByText(Rootinfraname.Text);
                                            node.Enabled = false;
                                            //node.Checkable = false;
                                            node.Expanded = true;
                                        }
                                    }

                                }
                            }
                        }
                    }

                }

                IList<InfraObject> SelectedInfraobjects = ShowCheckedNodes(RadTreeView1);

               
                ddlroles.SelectedValue = (uservalue1.Role).ToString();
                var CustomRoleSubType = Facade.GetCustomSubRoleTypebyUserId(Convert.ToInt32(ddlusers.SelectedValue));
                var Accessmanagerlist = Facade.GetAccessManagerByUserId(Convert.ToInt32(ddlusers.SelectedValue));
                if (Convert.ToInt32(ddlusers.SelectedValue) > 0 && ddlroles.SelectedValue == "Custom" && Accessmanagerlist != null && CustomRoleSubType != null)
                {

                    _logger.InfoFormat(" Unable to Select with user");
                    Panel1.Visible = true;
                    DivRoleSubType.Visible = true;
                    txtRolesubtype.Text = CustomRoleSubType.RoleSubtype;
                    ucnAccessmangerConfiguration.Visible = true;
                    Facade.GetUserById(Convert.ToInt32(ddlusers.SelectedValue));
                    var userInfraList = Facade.GetUserInfraObjectByUserId(Convert.ToInt32(ddlusers.SelectedValue));
                    var companydetail1 = Facade.GetCompanyProfileById(Convert.ToInt32(uservalue1.CompanyId));
                    if (companydetail1 != null)
                    {
                        var infralist = Facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(uservalue1.CompanyId, IsSuperAdmin, companydetail1.IsParent);

                        if (infralist != null)
                        {
                            cskgoup.DataSource = infralist;
                            cskgoup.DataTextField = "Name";
                            cskgoup.DataValueField = "Id";
                            cskgoup.DataBind();
                        }

                        if (companydetail1.IsParent)
                        {
                            if (infralist != null)
                            {
                                cskgoup.Items.Insert(0, new ListItem("ALL", "0"));
                            }
                        }
                    }
                    if (userInfraList != null)
                    {
                        foreach (var listItem in from ListItem listItem in cskgoup.Items from userInfraItem in userInfraList where listItem.Value == userInfraItem.InfraObjectId.ToString() && userInfraItem.IsApplication == 0 select listItem)
                        {
                            listItem.Selected = true;
                            _previousSelectedItems.Add(listItem);
                        }
                    }
                    var currentSelectedItems = Utility.GetSelectedItem(cskgoup);
                    var previousItems = _previousSelectedItems;
                    if (previousItems != null && currentSelectedItems != null)
                    {
                        CompareToCollection(previousItems, currentSelectedItems, 0);
                    }
                    Utility.PopulateDashboard(dashboard1);
                    Utility.PopulateConfiguration(Configuration1);
                    Utility.PopulateView(View1);
                    Utility.PopulateManage(Manage1);
                    ////Utility.PopulateAlerts(Alerts1);
                    ////Utility.PopulateITOrchestration(ITOrchestration1);
                    ////Utility.PopulateReports(Reports1);
                    EnumHelper.PopulateEnumDescriptionIntoList(Alerts1, typeof(SubAlerts), "");
                    Utility.PopulateITOrchestration(ITOrchestration1);
                    EnumHelper.PopulateEnumDescriptionIntoList(Reports1, typeof(SubReports), "");

                    if (Accessmanagerlist != null)
                    {
                        foreach (var listItem in from ListItem listItem in dashboard1.Items from customlist in Accessmanagerlist where listItem.Value == Convert.ToString(customlist.AccessSubMenuType) && customlist.AccessMenuType == "Dashboard" select listItem)
                        {
                            listItem.Selected = true;
                            _previousRightSelectedItems.Add(listItem);
                        }
                        foreach (var listItem in from ListItem listItem in Configuration1.Items from customlist in Accessmanagerlist where listItem.Value == Convert.ToString(customlist.AccessSubMenuType) && customlist.AccessMenuType == "Configuration" select listItem)
                        {
                            listItem.Selected = true;
                            _previousRightSelectedItems.Add(listItem);
                        }

                        foreach (var listItem in from ListItem listItem in View1.Items from customlist in Accessmanagerlist where listItem.Value == Convert.ToString(customlist.AccessSubMenuType) && customlist.AccessMenuType == "View" select listItem)
                        {
                            listItem.Selected = true;
                            _previousRightSelectedItems.Add(listItem);
                        }

                        foreach (var listItem in from ListItem listItem in Manage1.Items from customlist in Accessmanagerlist where listItem.Value == Convert.ToString(customlist.AccessSubMenuType) && customlist.AccessMenuType == "Manage" select listItem)
                        {
                            listItem.Selected = true;
                            _previousRightSelectedItems.Add(listItem);
                        }

                        foreach (var listItem in from ListItem listItem in Alerts1.Items from customlist in Accessmanagerlist where listItem.Value == Convert.ToString(customlist.AccessSubMenuType) && customlist.AccessMenuType == "Alerts" select listItem)
                        {
                            listItem.Selected = true;
                            _previousRightSelectedItems.Add(listItem);
                        }

                        foreach (var listItem in from ListItem listItem in ITOrchestration1.Items from customlist in Accessmanagerlist where listItem.Value == Convert.ToString(customlist.AccessSubMenuType) && customlist.AccessMenuType == "ITOrchestration" select listItem)
                        {
                            listItem.Selected = true;
                            _previousRightSelectedItems.Add(listItem);
                        }

                        foreach (var listItem in from ListItem listItem in Reports1.Items from customlist in Accessmanagerlist where listItem.Value == Convert.ToString(customlist.AccessSubMenuType) && customlist.AccessMenuType == "Reports" select listItem)
                        {
                            listItem.Selected = true;
                            _previousRightSelectedItems.Add(listItem);
                        }
                        foreach (var listItem in from ListItem listItem in ParallelProfile.Items from customlist in Accessmanagerlist where (customlist.ProfileAttach.Split(',').Contains(listItem.Value)) && customlist.AccessMenuType == "ITOrchestration" select listItem)
                        {
                            listItem.Selected = true;
                            _previousRightSelectedItems.Add(listItem);
                        }

                    }
                    var ButtonNameConvention = ucnAccessmangerConfiguration.FindControl("Save") as Button;
                    ButtonNameConvention.Text = "Update";
                }
                else
                {

                    if (ddlroles.SelectedValue != "Custom")
                    {
                        DivRoleSubType.Visible = false;
                        txtRolesubtype.Text = string.Empty;
                        ucnAccessmangerConfiguration.Visible = true;
                    }

                    //else if (ddlroles.SelectedValue != "SubCustom")
                    //{

                    //    txtRolesubtype.Text = string.Empty;
                    //    ucnAccessmangerConfiguration.Visible = false;
                    //}

                    else
                    {
                        if (CustomRoleSubType != null)
                        {
                            Panel1.Visible = true;
                            DivRoleSubType.Visible = false;
                            txtRolesubtype.Text = CustomRoleSubType.RoleSubtype;
                           
                        }
                        else
                        {
                            Panel1.Visible = true;
                            DivRoleSubType.Visible = false;
                            txtRolesubtype.Text = string.Empty;
                            ucnAccessmangerConfiguration.Visible = false;
                        }

                        UncheckAll(dashboard1);
                        UncheckAll(Configuration1);
                        UncheckAll(View1);
                        UncheckAll(Manage1);
                        UncheckAll(Alerts1);
                        UncheckAll(ITOrchestration1);
                        UncheckAll(Reports1);
                        ucnAccessmangerConfiguration.Visible = true;
                    }

                    var companydetails = Facade.GetCompanyProfileById(Convert.ToInt32(uservalue1.CompanyId));
                    if (companydetails != null)
                    {
                        var infralist = Facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(uservalue1.CompanyId, IsSuperAdmin, companydetails.IsParent);
                        if (infralist != null)
                        {
                            cskgoup.DataSource = infralist;
                            cskgoup.DataTextField = "Name";
                            cskgoup.DataValueField = "Id";
                            cskgoup.DataBind();
                        }

                        if (companydetails.IsParent)
                        {
                            if (infralist != null)
                            {
                                cskgoup.Items.Insert(0, new ListItem("ALL", "0"));
                            }
                        }

                        var userInfraList = Facade.GetUserInfraObjectByUserId(Convert.ToInt32(ddlusers.SelectedValue));

                        if (userInfraList != null)
                        {
                            foreach (var listItem in from ListItem listItem in cskgoup.Items from userInfraItem in userInfraList where listItem.Value == userInfraItem.InfraObjectId.ToString() && userInfraItem.IsApplication == 0 select listItem)
                            {
                                listItem.Selected = true;
                                _previousSelectedItems.Add(listItem);
                            }
                        }
                        var currentSelectedItems = Utility.GetSelectedItem(cskgoup);
                        var previousItems = _previousSelectedItems;
                        if (previousItems != null && currentSelectedItems != null)
                        {
                            CompareToCollection(previousItems, currentSelectedItems, 0);
                        }
                    }
                }

                chkITOrchestration_pnael(ITOrchestration1);
                //if (uservalue1.Id <= 0) return;
                //if (!CurrentEntity.IsNew)
                //{
                //}
            }
            catch (Exception ex)
            {
                _logger.Info("Exception While Select User :" + ex.Message);
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
            }
        }

        protected void ddlroles_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (ddlroles.SelectedValue == "Custom")
                {
                    for (int i = 0; i < cskgoup.Items.Count; i++)
                    {
                        cskgoup.Items[i].Enabled = true;
                        cskgoup.Items[i].Selected = false;
                    }
                    var userInfraList = Facade.GetUserInfraObjectByUserId(Convert.ToInt32(ddlusers.SelectedValue));
                    if (userInfraList != null)
                    {
                        foreach (var listItem in from ListItem listItem in cskgoup.Items from userInfraItem in userInfraList where listItem.Value == userInfraItem.InfraObjectId.ToString() && userInfraItem.IsApplication == 0 select listItem)
                        {
                            listItem.Selected = true;
                            _previousSelectedItems.Add(listItem);
                        }
                    }
                    Panel1.Enabled = true;
                    DivRoleSubType.Visible = true;

                    var dashboard1 = ucnAccessmangerConfiguration.FindControl("chksDashboard") as CheckBoxList;
                    var Configuration1 = ucnAccessmangerConfiguration.FindControl("chkConfiguration") as CheckBoxList;
                    var View1 = ucnAccessmangerConfiguration.FindControl("chkView") as CheckBoxList;
                    var Manage1 = ucnAccessmangerConfiguration.FindControl("chkManage") as CheckBoxList;
                    var Alerts1 = ucnAccessmangerConfiguration.FindControl("chkAlerts") as CheckBoxList;
                    var ITOrchestration1 = ucnAccessmangerConfiguration.FindControl("chkITOrchestration") as CheckBoxList;
                    var Reports1 = ucnAccessmangerConfiguration.FindControl("chkReports") as CheckBoxList;
                    var ParallelProfile = ucnAccessmangerConfiguration.FindControl("ddlProfiles") as ListBox;
                    UncheckAll(dashboard1);
                    UncheckAll(Configuration1);
                    UncheckAll(View1);
                    UncheckAll(Manage1);
                    UncheckAll(Alerts1);
                    UncheckAll(ITOrchestration1);
                    UncheckAll(Reports1);
                    UncheckAllDropdown(ParallelProfile);
                    chkITOrchestration_pnael(ITOrchestration1);
                    ucnAccessmangerConfiguration.Visible = true;
                }
                else
                {
                    if (ddlroles.SelectedValue == "SuperAdmin")
                    {
                        for (int i = 0; i < cskgoup.Items.Count; i++)
                        {
                            cskgoup.Items[i].Selected = true;
                        }
                        Panel1.Enabled = false;
                    }
                    else
                    {
                        for (int i = 0; i < cskgoup.Items.Count; i++)
                        {
                            cskgoup.Items[i].Enabled = true;
                            cskgoup.Items[i].Selected = false;
                        }
                        Panel1.Enabled = true;
                        var userInfraList = Facade.GetUserInfraObjectByUserId(Convert.ToInt32(ddlusers.SelectedValue));
                        if (userInfraList != null)
                        {
                            foreach (var listItem in from ListItem listItem in cskgoup.Items from userInfraItem in userInfraList where listItem.Value == userInfraItem.InfraObjectId.ToString() && userInfraItem.IsApplication == 0 select listItem)
                            {
                                listItem.Selected = true;
                                _previousSelectedItems.Add(listItem);
                            }
                        }
                    }
                    ucnAccessmangerConfiguration.Visible = false;
                }
            }
            catch (Exception ex)
            {
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
            }
        }

        private void CheckBoxListAllSelected(CheckBoxList grpOrAppList)
        {
            int selectedKwt = 0;

            for (int i = 1; i < grpOrAppList.Items.Count; i++)
            {
                if (grpOrAppList.Items[i].Selected)
                {
                    selectedKwt++;
                }
                grpOrAppList.Items[i].Enabled = true;
            }

            if (selectedKwt == grpOrAppList.Items.Count - 1)
            {
                grpOrAppList.Items[0].Selected = true;
            }
            else
            {
                grpOrAppList.Items[0].Selected = false;
            }
        }

        private void CheckGroupBoxList()
        {
            Session.Remove("PreviousItem");
            Session.Remove("ApplicationPreviousItem");
            Facade.GetUserById(CurrentUserId);
            var userInfraList = Facade.GetUserInfraObjectByUserId(CurrentUserId);

            if (userInfraList != null)
            {
                foreach (var listItem in from ListItem listItem in cskgoup.Items from userInfraItem in userInfraList where listItem.Value == userInfraItem.InfraObjectId.ToString() && userInfraItem.IsApplication == 0 select listItem)
                {
                    listItem.Selected = true;
                    _previousSelectedItems.Add(listItem);
                }
            }

            CheckBoxListAllSelected(cskgoup);

            Session["PreviousItem"] = _previousSelectedItems;
            Session["ApplicationPreviousItem"] = _previousApplicationSelectedItems;
        }

        protected void cskgoup_SelectedIndexChanged(object sender, EventArgs e)
        {
            labelSDErrormessage.Visible = false;
            string result = Request.Form["__EVENTTARGET"];
            string[] checkedBox = result.Split('$');
            int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);

            if (checkedIndex == 0)
            {
                if (cskgoup.Items[0].Selected)
                {
                    for (int i = 1; i < cskgoup.Items.Count; i++)
                    {
                        cskgoup.Items[i].Selected = true;
                        cskgoup.Items[i].Enabled = true;
                    }
                }
                else
                {
                    for (int i = 1; i < cskgoup.Items.Count; i++)
                    {
                        cskgoup.Items[i].Selected = true;
                        cskgoup.Items[i].Enabled = true;
                    }
                }
            }
            else
            {
                CheckBoxListAllSelected(cskgoup);
            }


        }

        private void CompareToCollection(IEnumerable<ListItem> previousItems, IList<ListItem> currentSelectedItems, int isApplication)
        {
            var deleteListInfra = new List<ListItem>();
            var addListGroups = currentSelectedItems;
            foreach (var prItem in previousItems)
            {
                var itemFind = false;

                foreach (var crItem in currentSelectedItems)
                {
                    if (prItem.Text != crItem.Text) continue;
                    if (addListGroups.Contains(crItem))
                    {
                        addListGroups.Remove(crItem);
                    }
                    itemFind = true;
                    break;
                }
                if (!itemFind)
                {
                    deleteListInfra.Add(prItem);
                }
            }

            try
            {
                if (deleteListInfra.Count > 0)
                {
                    foreach (var userInfra in deleteListInfra.Select(deleteItem => new UserInfraObject
                    {
                        UserId = Convert.ToInt32(CurrentEntity.Id),
                        InfraObjectId = Convert.ToInt32(deleteItem.Value),
                    }))
                    {
                        if (userInfra.InfraObjectId != 0 && userInfra.UserId != 0)
                            Facade.DeleteUserInfraObjectByUserId(userInfra);
                    }
                }

                if (addListGroups.Count > 0)
                {

                    foreach (var userInfraObject in addListGroups.Select(addItem => new UserInfraObject
                    {
                        UserId = Convert.ToInt32(CurrentEntity.Id),
                        InfraObjectId = Convert.ToInt32(addItem.Value),
                        IsApplication = Convert.ToInt32(isApplication),
                        CreatorId = Convert.ToInt32(LoggedInUserId)
                    }))
                    {
                        if (userInfraObject.InfraObjectId != 0 && userInfraObject.UserId != 0)
                            Facade.AddUseInfraObject(userInfraObject);
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occurred while comparing user group list", ex);
                ExceptionManager.Manage(cpException);
            }
        }

        protected void txtRolesubtype_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (txtRolesubtype.Text != string.Empty)
                {
                    var linuxpath = ucnAccessmangerConfiguration.FindControl("rfvrolesubtype") as RequiredFieldValidator;
                    rfvrolesubtype.Validate();
                    linuxpath.Visible = false;
                }

            }
            catch (CpException)
            {
            }
            catch (Exception)
            {
            }
        }

        protected void UncheckAll(CheckBoxList CheckBoxList1)
        {
            foreach (ListItem listItem in CheckBoxList1.Items)
            {
                listItem.Selected = false;
            }
        }
        protected void UncheckAllDropdown(ListBox CheckBoxList1)
        {
            foreach (ListItem listItem in CheckBoxList1.Items)
            {
                listItem.Selected = false;
            }
        }

        protected void chkITOrchestration_pnael(CheckBoxList ITOrchestration)
        {
            var pnlworflowprofiles = ucnAccessmangerConfiguration.FindControl("pnlworflowprofiles") as Panel;
             int i = 0;
            foreach (ListItem listitem in ITOrchestration.Items)
            {
                if (listitem.Selected && listitem.ToString() == "Execute")
                {
                    i = 1;
                    pnlworflowprofiles.Visible = true;
                }
                else if (listitem.Selected && listitem.ToString() == "View" && i==1)
                {
                    i = 0;
                 pnlworflowprofiles.Visible = true;
                }
                //else if (i == 1 && listitem.ToString() == "View")
                //{
                //    i = 0;
                //    pnlworflowprofiles.Visible = true;
                //}
                else{
                    if (i == 1)
                    {
                        pnlworflowprofiles.Visible = true;
                    }
                    else
                    {
                        pnlworflowprofiles.Visible = false;
                    }
                    }
               
            }

        }

    }
    
}