﻿
// Starts =====================
var globalstring = null;
var JsonForFullView = null;
var isbtnReloadHide = false;
var level = 0;
function renderAjaxData(businessid, level1, breadCrum) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'businessid': businessid }),
        url: "ImpactSummary.aspx/NodeRelationConverter",
        success: function (msg) {
            var value = msg.d.split('#');
            globalstring = value[1];
            var JsonObj = eval('(' + value[0] + ')');
            JsonForFullView = JsonObj;
            var type = "BS";
            //alert(type);
            treeShow(JsonObj, level1, type, breadCrum);
        }
    });
}

function renderAjaxDataForDashboard(businessid, level1, breadCrum) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'businessid': businessid }),
        url: "DashboardImpactSummary.aspx/NodeRelationConverter",
        success: function (msg) {
            var value = msg.d.split('#');
            globalstring = value[1];
            var JsonObj = eval('(' + value[0] + ')');
            JsonForFullView = JsonObj;
            var type = "BS";
            //alert(type);
            treeShow(JsonObj, level1, type, breadCrum);
        }
    });
}

function renderAjaxDataForInfraObjects(infraObjectNodeRelation, commonLevel, type, breadCrum) {
    $.ajax({
        type: "POST",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: true,
        data: JSON.stringify({ 'infraObjectNodeRelation': infraObjectNodeRelation }),
        url: "ImpactSummary.aspx/NodeRelationConverterForInfraObjects",
        success: function (msg) {
            var JsonObj = eval('(' + msg.d + ')');
            if (type == "BF" || type == "IO")
                JsonForFullView = JsonObj;
            //alert(type);
            treeShow(JsonObj, commonLevel, type, breadCrum);
        }
    });
}

function treeShow(root1, level1, type, breadCrum) {
    $("#divbsView").css("display", "block");
    $("#divIncView").css("display", "none");
    $('#divemptyFunctionPopup').css("display", "none");
    //$('#body').html("");
    i = 0, root;
    level = parseInt(level1);
    var duration = 750;
    var viewerWidth = 670;
    var viewerHeight = 320;

    ShowBreadCrum("Business-IT Relationship  - " + breadCrum);

    function zoom() {
        vis.attr("transform", "translate(" + d3.event.translate + ")scale(" + d3.event.scale + ")");
    }

    function resetZoom() {
        d3.select("[id$=bsbody]").select("svg").select("g")
       .transition().duration(750)
       .attr("transform", root1.name.length < 10 ? "translate(30,30)scale(0.90)" : "translate(90,30)scale(0.90)");
    };
    d3.select("#btnReset").on("click", resetZoom);

    function centerNode(source) {
        scale = zoomListener.scale();
        x = -source.y0;
        y = -source.x0;
        x = 0;
        //y = y * scale + viewerHeight / 2; 
        y = 0;
        d3.select('g').transition()
        .duration(duration)
        .attr("transform", "translate(" + x + "," + y + ")scale(" + scale + ")");
        zoomListener.scale(scale);
        zoomListener.translate([x, y]);
    }

    var zoomListener = d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom);

    var tree = d3.layout.tree()
    .size([viewerHeight, viewerWidth]);

    var diagonal = d3.svg.diagonal()
    .projection(function (d) { return [d.y, d.x]; });

    var vis = d3.select("[id$=bsbody]").append("svg:svg")
    .attr("width", viewerWidth)
    .attr("height", viewerHeight)
    .call(d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom)).on("dblclick.zoom", null)
    .append("g")
    //zoomListener.translate([100, 50]).scale(0.85);
    .attr("transform", root1.name.length < 10 ? "translate(30,30)scale(0.90)" : "translate(90,30)scale(0.90)");

    var root = root1;
    root.x0 = viewerHeight / 2;
    root.y0 = 0;

    function toggleAll(d) {
        if (d.children) {
            d.children.forEach(toggleAll);
            toggleOnLoad(d);
        }
    }
    root.children.forEach(toggleAll);
    update(root);
    //centerNode(root);

    function update(source) {
        var duration = d3.event && d3.event.altKey ? 5000 : 500;

        var nodes = tree.nodes(root).reverse();

        nodes.forEach(function (d) { d.y = d.depth * 160; });

        var node = vis.selectAll("g.node")
        .data(nodes, function (d) { return d.id || (d.id = ++i); });

        var nodeEnter = node.enter().append("svg:g")
        .attr("class", "node")
        .attr("transform", function (d) { return "translate(" + source.y0 + "," + source.x0 + ")"; })
        .on("click", function (d) { GetJsonFromNodeRelation(d); toggleOnClick(d); update(d); });

        nodeEnter.append("svg:circle")
        .attr("r", 1e-6)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        if (level == 0) {
            nodeEnter.append("svg:text")
           .attr("x", function (d) { return returnx(d); })
           .attr("dy", function (d) { return returny(d); })
           .attr("text-anchor", function (d) { return returnTextAnchor(d); })
           .text(function (d) { return d.name.split('/')[0]; })
           .style("fill-opacity", 1e-6);
        }
        else {
            nodeEnter.append("svg:text")
           .attr("x", function (d) { return d.children || d._children ? 15 : 10; })
           .attr("dy", function (d) { return d.children || d._children ? "-1em" : ".35em"; })
           .attr("text-anchor", function (d) { return d.children || d._children ? "end" : "start"; })
           .text(function (d) { return d.name.split('/')[0]; })
           .style("fill-opacity", 1e-6);
        }
        nodeEnter.append("svg:image")
                .attr("xlink:href", function (d) { return d.logo; })
                .attr("x", function (d) { return d.children || d._children ? -6 : 10; })
                .attr("y", function (d) { return d.children || d._children ? "-0.5em" : ".35em"; })
                .attr("height", function (d) { return d.logoheight || 12; })
                .attr("width", function (d) { return d.logowidth || 12; });

        var nodeUpdate = node.transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + d.y + "," + d.x + ")"; });

        nodeUpdate.select("circle")
        .attr("r", 5.0)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeUpdate.select("text")
        .style("fill-opacity", 1);

        var nodeExit = node.exit().transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + source.y + "," + source.x + ")"; })
        .remove();

        nodeExit.select("circle")
        .attr("r", 1e-6);

        nodeExit.select("text")
        .style("fill-opacity", 1e-6);

        var link = vis.selectAll("path.link")
        .data(tree.links(nodes), function (d) { return d.target.id; });

        link.enter().insert("svg:path", "g")
        .attr("class", "link")
        .style("stroke", function (d) { return d.target.level; })
        .attr("d", function (d) {
            var o = { x: source.x0, y: source.y0 };
            return diagonal({ source: o, target: o });
        })
        .transition()
        .duration(duration)
        .attr("d", diagonal);

        link.transition()
        .duration(duration)
        .attr("d", diagonal);

        link.exit().transition()
        .duration(duration)
        .attr("d", function (d) {
            var o = { x: source.x, y: source.y };
            return diagonal({ source: o, target: o });
        })
        .remove();

        nodes.forEach(function (d) {
            d.x0 = d.x;
            d.y0 = d.y;
        });
    }

    function toggleOnLoad(d) {
        if (d.children) {
            if (d.hide == "hide") {
                d._children = d.children;
                d.children = null;
            }
        } else {
            d.children = d._children;
            d._children = null;
        }
    }

    function toggleOnClick(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
        if (level == 1) {
            if (d.parent) {
                if (d.children) {
                    d.parent.children.forEach(function (element) {
                        if (d != element) {
                            collapse(element);
                        }
                    });
                }
            }
        }
    }

    function returnx(d) {
        var xPos = null;
        if (d.hide == "hide") {
            xPos = 10;
        }
        else if (d.children && d.hide != "hide") {
            xPos = 15;
        }
        else {
            xPos = 10;
        }
        return xPos;
    }

    function returny(d) {
        var yPos = null;
        if (d.hide == "hide") {
            yPos = ".35em";
        }
        else if (d.children && d.hide != "hide") {
            yPos = "-1em"
        }
        else {
            yPos = ".35em";
        }
        return yPos;
    }

    function returnTextAnchor(d) {
        var textAnchor = null;
        if (d.hide == "hide") {
            textAnchor = "start";
        }
        else if (d.children && d.hide != "hide") {
            textAnchor = "end";
        }
        else {
            textAnchor = "start";
        }
        return textAnchor;
    }

    function GetJsonFromNodeRelation(d) {
        if (d.hide == "hide" && level == 0) {
            level = 1;
            var requiredClickedName = null;
            if (d.logo)
                requiredClickedName = d.name + "/Hide/logo/red" + ":";
            else
                requiredClickedName = d.name + "/Hide" + ":";

            var splitNodeRelation = globalstring.split(";");
            if (splitNodeRelation.length > 0) {
                for (var i = 0; i < splitNodeRelation.length; i++) {
                    var index = splitNodeRelation[i].indexOf(requiredClickedName);
                    if (index == 0) {
                        var infraObjectRel = splitNodeRelation[i];
                        var splitInfraObjectRel = infraObjectRel.split(":");
                        var componentArray = splitInfraObjectRel[1].split(",");
                        if (componentArray.length > 0) {
                            for (var j = 0 ; j < componentArray.length; j++) {
                                var requiredComponentName = componentArray[j] + ":";
                                for (var k = i; k < splitNodeRelation.length; k++) {
                                    var componentArrayIndex = splitNodeRelation[k].indexOf(requiredComponentName)
                                    if (componentArrayIndex == 0) {
                                        infraObjectRel = infraObjectRel + ";" + splitNodeRelation[k];
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                    }
                }
                var requiredNodeRelation = infraObjectRel;
                if (requiredNodeRelation != null) {
                    $('[id$=bsbody]').html("");
                    renderAjaxDataForInfraObjects(requiredNodeRelation, level, "BFIO", breadCrum);
                    isbtnReloadHide = true;
                    d3.select("#btnReload").attr("style", "display:inline-block");
                }
            }
        }
    }

    function backToFullView() {
        $('[id$=bsbody]').html("");
        level = 0;
        if (isbtnReloadHide)
            d3.select("#btnReload").attr("style", "display:none");
        treeShow(JsonForFullView, level);
    }
    d3.select("#btnReload").on("click", backToFullView);

    function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d._children.forEach(collapse);
            d.children = null;
        }
    }
}
// Ends ======================

function clearGraph(type,breadCrum) {

    $("#spnBreadCrum").text(breadCrum);

    $("[id$=bsbody]").html("");
       
    $("#divbsView").css("display", "none");
    $("#divIncView").css("display", "none");

    $('#divemptyFunctionPopup').css("display", "block");

    if (type == "BF")
        // $("#divemptyFunctionPopup").text("Need to configure InfraObjects");
        $("#divemptyFunctionPopup").html('<div class="row well center "><div class="col-md-3"><a class="glyphicons warning_sign pull-left"><i></i></a>' +
                           '</div><div class="col-md-9">No InfraObjects Configured for this Business Function.<br>' +
                            'Please <a href="../Group/InfraObjectsConfiguration.aspx">Click Here</a> to Configure Infra Objects.' +
                           '</div></div>');
                  
    else if (type == "BS") {
        $("#divemptyFunctionPopup").html('<div class="row well center "><div class="col-md-3"><a class="glyphicons warning_sign pull-left"><i></i></a>' +
                           '</div><div class="col-md-9">No Business Functions Configured for this Business Service.<br>' +
                            'Please <a href="../BusinessFunction/BusinessFunctionConfig.aspx">Click Here</a> to Configure Business Functions Or Click On Another Business Service .' +
                           '</div></div>');
    }
    else if (type == "IO") {
        $("#divemptyFunctionPopup").html('<div class="row well center "><div class="col-md-3"><a class="glyphicons warning_sign pull-left"><i></i></a>' +
                           '</div><div class="col-md-9">No InfraObject Components Configured for this Infra Object.<br>' +
                            'Please <a href="../Component/ServerConfiguration.aspx">Click Here</a> to Configure Server Infra Component.' +
                           '</div></div>');
    }
}

function ShowBreadCrum(breadCrum) {
    $("#spnBreadCrum").text(breadCrum);
}

$(".scroll-pane ").mCustomScrollbar({
    axis: "yx",
    setHeight: "280px",
    advanced: {
        updateOnContentResize: true,
        autoExpandHorizontalScroll: true
    }

});
$(".scroll-pane-bottom").mCustomScrollbar({
    axis: "yx",
    setHeight: "180px",
    advanced: {
        updateOnContentResize: true,
        autoExpandHorizontalScroll: true
    }

});
  
$(document).ready(function () {
    paneScrollWH();
    var paneHeight = "";
    var bottompaneHeight = "";
    function paneScrollWH() {
        paneHeight = $("div[id$=RadPane1]").height();
        bottompaneHeight = $("div[id$=RadPane4]").height();
        $(".scroll-pane").css("height", (paneHeight - 50));
        $(".scroll-pane-bottom").css("height", (bottompaneHeight - 50));
    }
    $(".rspResizeBar,.rspResizeBarHorizontal, [id*=RadPane]").click(function () {
        paneScrollWH();

    });
    $(".rspResizeBar,.rspResizeBarHorizontal,[id*=RadPane]").hover(function () {
        paneScrollWH();
    }, function () {

        paneScrollWH();
    });
});

  
Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
function EndRequestHandler(sender, args) {
    $(".scroll-pane ").mCustomScrollbar({
        axis: "yx",
        setHeight: "280px",
        advanced: {
            updateOnContentResize: true,
            autoExpandHorizontalScroll: true
        }

    });
    $(".scroll-pane-bottom").mCustomScrollbar({
        axis: "yx",
       
        setHeight: "180px",
        advanced: {
            updateOnContentResize: true,
            autoExpandHorizontalScroll: true
        },

    });
    paneScrollWH();
    var paneHeight = "";
    var bottompaneHeight = "";
    function paneScrollWH() {
        paneHeight = $("div[id$=RadPane1]").height();
        bottompaneHeight = $("div[id$=RadPane4]").height();
        $(".scroll-pane").css("height", (paneHeight - 50));
        $(".scroll-pane-bottom").css("height", (bottompaneHeight - 50));
    }
}


                       