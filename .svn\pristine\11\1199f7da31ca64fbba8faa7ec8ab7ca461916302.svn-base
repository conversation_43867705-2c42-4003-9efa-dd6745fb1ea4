﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace CP.UI {
    
    
    public partial class DatabaseList {
        
        /// <summary>
        /// hdUserType control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.HiddenField hdUserType;
        
        /// <summary>
        /// UpdatePanel2 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.UpdatePanel UpdatePanel2;
        
        /// <summary>
        /// ddldatabaseList control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::System.Web.UI.WebControls.DropDownList ddldatabaseList;
        
        /// <summary>
        /// ucDatabaseOracleList control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabaseOracleList ucDatabaseOracleList;
        
        /// <summary>
        /// DatabaseSqlList1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabaseSqlList DatabaseSqlList1;
        
        /// <summary>
        /// DatabaseExchangeList1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabaseExchangeList DatabaseExchangeList1;
        
        /// <summary>
        /// DatabaseOracleRacList control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabaseOracleRacList DatabaseOracleRacList;
        
        /// <summary>
        /// DatabaseDb2List1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabaseDb2List DatabaseDb2List1;
        
        /// <summary>
        /// DatabaseExchangeDAGList1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabaseExchangeDAGList DatabaseExchangeDAGList1;
        
        /// <summary>
        /// DatabaseMySqlList1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabaseMySqlList DatabaseMySqlList1;
        
        /// <summary>
        /// PostgreSql1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabasePostgreSqlList PostgreSql1;
        
        /// <summary>
        /// Postgre9x1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DatabasePostgresGreSqlList Postgre9x1;
        
        /// <summary>
        /// MSSql control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DataBaseMSSql MSSql;
        
        /// <summary>
        /// ucSybasedadb control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DataBaseSybaseList ucSybasedadb;
    }
}
