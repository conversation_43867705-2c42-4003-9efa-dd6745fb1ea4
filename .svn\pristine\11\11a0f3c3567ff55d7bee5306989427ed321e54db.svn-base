﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region ICustomExceptionDataAccess

    public interface ICustomExceptionDataAccess
    {
        CustomException Add(CustomException group);

        CustomException Update(CustomException gGroup);

        CustomException GetById(int id);

        IList<CustomException> GetAll();

        bool DeleteById(int id);

        bool IsExistByName(string name);
    }

    #endregion ICustomExceptionDataAccess
}