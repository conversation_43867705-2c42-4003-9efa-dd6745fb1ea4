﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class SmtpConfigurationBuilder : IEntityBuilder<SmtpConfiguration>
    {
        IList<SmtpConfiguration> IEntityBuilder<SmtpConfiguration>.BuildEntities(IDataReader reader)
        {
            var smtpConfigurations = new List<SmtpConfiguration>();

            while (reader.Read())
            {
                smtpConfigurations.Add(((IEntityBuilder<SmtpConfiguration>)this).BuildEntity(reader,
                    new SmtpConfiguration()));
            }

            return (smtpConfigurations.Count > 0) ? smtpConfigurations : null;
        }

        SmtpConfiguration IEntityBuilder<SmtpConfiguration>.BuildEntity(IDataReader reader, SmtpConfiguration smtp)
        {
            //const int FLD_ID = 0;
            //const int FLD_SMTPHOST = 1;
            //const int FLD_PORT = 2;
            //const int FLD_USERNAME = 3;
            //const int FLD_PASSWORD = 4;
            //const int FLD_ENABLESSL = 5;
            //const int FLD_ISBODYHTML = 6;
            //const int FLD_CREATORID = 7;
            //const int FLD_CREATEDATE = 8;
            //const int FLD_UPDATORID = 9;
            //const int FLD_UPDATEDATE = 10;

            //smtp.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //smtp.SmtpHost = reader.IsDBNull(FLD_SMTPHOST) ? string.Empty : reader.GetString(FLD_SMTPHOST);
            //smtp.Port = reader.IsDBNull(FLD_PORT) ? 0 : reader.GetInt32(FLD_PORT);
            //smtp.UserName = reader.IsDBNull(FLD_USERNAME) ? string.Empty : reader.GetString(FLD_USERNAME);
            //smtp.Password = reader.IsDBNull(FLD_PASSWORD) ? string.Empty : reader.GetString(FLD_PASSWORD);
            //smtp.EnableSSL = !reader.IsDBNull(FLD_ENABLESSL) && reader.GetBoolean(FLD_ENABLESSL);
            //smtp.IsBodyHTML = !reader.IsDBNull(FLD_ISBODYHTML) && reader.GetBoolean(FLD_ISBODYHTML);
            //smtp.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //smtp.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);
            //smtp.UpdatorId = reader.IsDBNull(FLD_UPDATORID) ? 0 : reader.GetInt32(FLD_UPDATORID);
            //smtp.UpdateDate = reader.IsDBNull(FLD_UPDATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_UPDATEDATE);

            //Fields in bcms_smtp table on 23/07/2013 : Id, SmtpHost, Port, UserName, Password, EnableSSL, IsBodyHTML, CreatorId, CreateDate, UpdatorId, UpdateDate

            smtp.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            smtp.SmtpHost = Convert.IsDBNull(reader["SmtpHost"]) ? string.Empty : Convert.ToString(reader["SmtpHost"]);
            smtp.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
            smtp.UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]);
            smtp.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);

            if (!Convert.IsDBNull(reader["EnableSSL"]))
                smtp.EnableSSL = Convert.ToBoolean(reader["EnableSSL"]);

            if (!Convert.IsDBNull(reader["IsBodyHTML"]))
                smtp.IsBodyHTML = Convert.ToBoolean(reader["IsBodyHTML"]);

            smtp.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            smtp.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);
            smtp.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            smtp.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);

            return smtp;
        }
    }
}