﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class SCRDataAccess : BaseDataAccess, ISCRDataAccess
    {
        #region Constructors

        public SCRDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SCR> CreateEntityBuilder<SCR>()
        {
            return (new SCRBuilder()) as IEntityBuilder<SCR>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="SCR" />into bcms_ms_scr table.
        /// </summary>
        /// <param name="scr">Insert scr Details</param>
        /// <returns>SCR</returns>
        /// <author><PERSON><PERSON><PERSON></author>
        SCR ISCRDataAccess.Add(SCR scr)
        {
            try
            {
                const string sp = "SCR_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.AnsiString, scr.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring+"iPRServerID", DbType.Int32, scr.PRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iDRServerID", DbType.Int32, scr.DRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iPRInstallationPath", DbType.AnsiString, scr.PRInstallationPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRInstallationPath", DbType.AnsiString, scr.DRInstallationPath);
                    Database.AddInParameter(cmd, Dbstring+"iPRNewMailboxPath", DbType.AnsiString, scr.PRNewMailboxPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRNewMailboxPath", DbType.AnsiString, scr.DRNewMailboxPath);
                    Database.AddInParameter(cmd, Dbstring+"iPRComponentPath", DbType.AnsiString, scr.PRComponentPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRComponentPath", DbType.AnsiString, scr.DRComponentPath);
                    Database.AddInParameter(cmd, Dbstring+"iExeFileName", DbType.AnsiString, scr.ExeFileName);
                    Database.AddInParameter(cmd, Dbstring+"iReplaylagTime", DbType.AnsiString, scr.ReplayLagTime);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        scr = reader.Read() ? CreateEntityBuilder<SCR>().BuildEntity(reader, scr) : null;
                    }

                    if (scr == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this SCR.");
                                }
                        }
                    }

                    return scr;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting SCR Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="SCR" />from bcms_ms_scr table by id.
        /// </summary>
        /// <param name="id">Pass id</param>
        /// <returns>SCR</returns>
        /// <author>Shivraj Mujumale</author>
        SCR ISCRDataAccess.GetById(int id)
        {
            try
            {
                const string sp = "SCR_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SCR>()).BuildEntity(reader, new SCR());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating SCR Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message,
                    exc);
            }
        }

        SCR ISCRDataAccess.GetByReplicationId(int id)
        {
            try
            {
                const string sp = "SCR_GetByReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SCR>()).BuildEntity(reader, new SCR());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating SCR Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message,
                    exc);
            }
        }

        /// <summary>
        ///     Update <see cref="SCR" />into bcms_ms_scr table.
        /// </summary>
        /// <param name="scr">update scr Details</param>
        /// <returns>SCR</returns>
        /// <author>Shivraj Mujumale</author>
        SCR ISCRDataAccess.Update(SCR scr)
        {
            try
            {
                const string sp = "SCR_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.AnsiString, scr.Id);
                    Database.AddInParameter(cmd, Dbstring+"iPRServerID", DbType.Int32, scr.PRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iDRServerID", DbType.Int32, scr.DRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iPRInstallationPath", DbType.AnsiString, scr.PRInstallationPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRInstallationPath", DbType.AnsiString, scr.DRInstallationPath);
                    Database.AddInParameter(cmd, Dbstring+"iPRNewMailboxPath", DbType.AnsiString, scr.PRNewMailboxPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRNewMailboxPath", DbType.AnsiString, scr.DRNewMailboxPath);
                    Database.AddInParameter(cmd, Dbstring+"iPRComponentPath", DbType.AnsiString, scr.PRComponentPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRComponentPath", DbType.AnsiString, scr.DRComponentPath);
                    Database.AddInParameter(cmd, Dbstring+"iExeFileName", DbType.AnsiString, scr.ExeFileName);
                    Database.AddInParameter(cmd, Dbstring+"iReplaylagTime", DbType.AnsiString, scr.ReplayLagTime);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        scr = reader.Read() ? CreateEntityBuilder<SCR>().BuildEntity(reader, scr) : null;
                    }

                    if (scr == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this SCR.");
                                }
                        }
                    }

                    return scr;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating SCR Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message,
                    exc);
            }
        }

        SCR ISCRDataAccess.UpdateByReplicationId(SCR scr)
        {
            try
            {
                const string sp = "SCR_UpdateByReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iReplicationId", DbType.AnsiString, scr.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring+"iPRServerID", DbType.Int32, scr.PRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iDRServerID", DbType.Int32, scr.DRServerId);
                    Database.AddInParameter(cmd, Dbstring+"iPRInstallationPath", DbType.AnsiString, scr.PRInstallationPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRInstallationPath", DbType.AnsiString, scr.DRInstallationPath);
                    Database.AddInParameter(cmd, Dbstring+"iPRNewMailboxPath", DbType.AnsiString, scr.PRNewMailboxPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRNewMailboxPath", DbType.AnsiString, scr.DRNewMailboxPath);
                    Database.AddInParameter(cmd, Dbstring+"iPRComponentPath", DbType.AnsiString, scr.PRComponentPath);
                    Database.AddInParameter(cmd, Dbstring+"iDRComponentPath", DbType.AnsiString, scr.DRComponentPath);
                    Database.AddInParameter(cmd, Dbstring+"iExeFileName", DbType.AnsiString, scr.ExeFileName);
                    Database.AddInParameter(cmd, Dbstring+"iReplaylagTime", DbType.AnsiString, scr.ReplayLagTime);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        scr = reader.Read() ? CreateEntityBuilder<SCR>().BuildEntity(reader, scr) : null;
                    }

                    if (scr == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this SCR.");
                                }
                        }
                    }

                    return scr;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating SCR Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message,
                    exc);
            }
        }

        /// <summary>
        ///     Get <see cref="SCR" />from bcms_ms_scr table.
        /// </summary>
        /// <returns>SCR List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<SCR> ISCRDataAccess.GetAll()
        {
            try
            {
                const string sp = "SCR_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildSCREntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISCRDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<SCR> ISCRDataAccess.GetByCompanyId(int companyId, bool isParent)
        {
            try
            {
                const string sp = "SCR_GetByCompanyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iCompanyId", DbType.Int32, companyId);
                    Database.AddInParameter(cmd, Dbstring+"iisParent", DbType.Int32, isParent);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildSCREntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISCRDataAccess.GetByCompanyId(" + companyId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Delete <see cref="SCR" />from bcms_ms_scr table by id.
        /// </summary>
        /// <param name="id">pass id</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool ISCRDataAccess.DeleteById(int id)
        {
            try
            {
                const string sp = "SCR_DeleteById";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISCRDataAccess.DeleteById()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Check <see cref="SCR" /> name is Exist or not into  bcms_ms_scr table by name.
        /// </summary>
        /// <param name="name">pass name</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool ISCRDataAccess.IsExistByName(string name)
        {
            try
            {
                const string sp = "SCR_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iName", DbType.AnsiString, name);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISCRDataAccess.IsExistByName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        private IList<SCR> BuildSCREntities(IDataReader reader)
        {
            var scrs = new List<SCR>();

            while (reader.Read())
            {
                var scr = new SCR
                {
                    ReplicationBase =
                    {
                        Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                        Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"])
                    },
                    PRInstallationPath = Convert.IsDBNull(reader["PRInstallationPath"])
                ? string.Empty
                : Convert.ToString(reader["PRInstallationPath"]),
                    DRInstallationPath = Convert.IsDBNull(reader["DRInstallationPath"])
                ? string.Empty
                : Convert.ToString(reader["DRInstallationPath"]),
                    PRServerName = Convert.IsDBNull(reader["PRServer"]) ? string.Empty : Convert.ToString(reader["PRServer"]),
                    DRServerName = Convert.IsDBNull(reader["DRServer"]) ? string.Empty : Convert.ToString(reader["DRServer"])
                };

                scrs.Add(scr);
            }

            return (scrs.Count > 0) ? scrs : null;
        }
        IList<SCR> ISCRDataAccess.GetByLoginId(int id)
        {
            try
            {
                const string sp = "SCRReplication_GetByLoginId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildSCREntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISCRDataAccess.GetByLoginId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Mthods
    }
}