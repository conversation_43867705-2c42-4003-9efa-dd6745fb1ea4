﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class MaxFullDBEmcSrdfMonitorBuilder : IEntityBuilder<MaxEmcSrdfFullDB>
    {
        IList<MaxEmcSrdfFullDB> IEntityBuilder<MaxEmcSrdfFullDB>.BuildEntities(IDataReader reader)
        {
            var maxEmcSrdflog = new List<MaxEmcSrdfFullDB>();

            while (reader.Read())
            {
                maxEmcSrdflog.Add(((IEntityBuilder<MaxEmcSrdfFullDB>)this).BuildEntity(reader, new MaxEmcSrdfFullDB()));
            }

            return (maxEmcSrdflog.Count > 0) ? maxEmcSrdflog : null;
        }

        MaxEmcSrdfFullDB IEntityBuilder<MaxEmcSrdfFullDB>.BuildEntity(IDataReader reader, MaxEmcSrdfFullDB maxEmcSrdflog)
        {

            maxEmcSrdflog.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            maxEmcSrdflog.InfraobjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
            maxEmcSrdflog.PRDatabaseName = Convert.IsDBNull(reader["PRDBName"]) ? string.Empty : Convert.ToString(reader["PRDBName"]);
            maxEmcSrdflog.DRDatabaseName = Convert.IsDBNull(reader["DRDBName"]) ? string.Empty : Convert.ToString(reader["DRDBName"]);
            maxEmcSrdflog.PRDatabaseState = Convert.IsDBNull(reader["PRDBState"]) ? string.Empty : Convert.ToString(reader["PRDBState"]);
            maxEmcSrdflog.DRDatabaseState = Convert.IsDBNull(reader["DRDBState"]) ? string.Empty : Convert.ToString(reader["DRDBState"]);
            maxEmcSrdflog.PRDatabaseVersion = Convert.IsDBNull(reader["PRDBVersion"]) ? string.Empty : Convert.ToString(reader["PRDBVersion"]);
            maxEmcSrdflog.DRDatabaseVersion = Convert.IsDBNull(reader["DRDBVersion"]) ? string.Empty : Convert.ToString(reader["DRDBVersion"]);

            //maxEmcSrdflog.PRClientProgPath = Convert.IsDBNull(reader["PRClientProgPath"]) ? string.Empty : Convert.ToString(reader["PRClientProgPath"]);
            //maxEmcSrdflog.DRClientProgPath = Convert.IsDBNull(reader["DRClientProgPath"]) ? string.Empty : Convert.ToString(reader["DRClientProgPath"]);


            //maxEmcSrdflog.PRGlobalProgPath = Convert.IsDBNull(reader["PRGlobalProgPath"]) ? string.Empty : Convert.ToString(reader["PRGlobalProgPath"]);
            //maxEmcSrdflog.DRGlobalProgPath = Convert.IsDBNull(reader["DRGlobalProgPath"]) ? string.Empty : Convert.ToString(reader["DRGlobalProgPath"]);
            //maxEmcSrdflog.PRGlobalDataPath = Convert.IsDBNull(reader["PRGlobalDataPath"]) ? string.Empty : Convert.ToString(reader["PRGlobalDataPath"]);
            //maxEmcSrdflog.DRGlobalDataPath = Convert.IsDBNull(reader["DRGlobalDataPath"]) ? string.Empty : Convert.ToString(reader["DRGlobalDataPath"]);
            //maxEmcSrdflog.PRDataVolumes = Convert.IsDBNull(reader["PRDataVolumes"]) ? string.Empty : Convert.ToString(reader["PRDataVolumes"]);
            //maxEmcSrdflog.DRDataVolumes = Convert.IsDBNull(reader["DRDataVolumes"]) ? string.Empty : Convert.ToString(reader["DRDataVolumes"]);

            //maxEmcSrdflog.PRMaxDataVolumes = Convert.IsDBNull(reader["PRMaxDataVolumes"]) ? string.Empty : Convert.ToString(reader["PRMaxDataVolumes"]);
            //maxEmcSrdflog.DRMaxDataVolumes = Convert.IsDBNull(reader["DRMaxDataVolumes"]) ? string.Empty : Convert.ToString(reader["DRMaxDataVolumes"]);

            //maxEmcSrdflog.PRDataSize = Convert.IsDBNull(reader["PRDataSize"]) ? string.Empty : Convert.ToString(reader["PRDataSize"]);
            //maxEmcSrdflog.DRDataSize = Convert.IsDBNull(reader["DRDataSize"]) ? string.Empty : Convert.ToString(reader["DRDataSize"]);
            //maxEmcSrdflog.PRDataUsedSpace = Convert.IsDBNull(reader["PRDataUsedSpace"]) ? string.Empty : Convert.ToString(reader["PRDataUsedSpace"]);
            //maxEmcSrdflog.DRDataUsedSpace = Convert.IsDBNull(reader["DRDataUsedSpace"]) ? string.Empty : Convert.ToString(reader["DRDataUsedSpace"]);
            //maxEmcSrdflog.PRLogPath = Convert.IsDBNull(reader["PRLogPath"]) ? string.Empty : Convert.ToString(reader["PRLogPath"]);
            //maxEmcSrdflog.DRLogPath = Convert.IsDBNull(reader["DRLogPath"]) ? string.Empty : Convert.ToString(reader["DRLogPath"]);

            //maxEmcSrdflog.PRLogVolumes = Convert.IsDBNull(reader["PRLogVolumes"]) ? string.Empty : Convert.ToString(reader["PRLogVolumes"]);
            //maxEmcSrdflog.DRLogVolumes = Convert.IsDBNull(reader["DRLogVolumes"]) ? string.Empty : Convert.ToString(reader["DRLogVolumes"]);



            //maxEmcSrdflog.PRMaxLogVolumes = Convert.IsDBNull(reader["PRMaxLogVolumes"]) ? string.Empty : Convert.ToString(reader["PRMaxLogVolumes"]);
            //maxEmcSrdflog.DRMaxLogVolumes = Convert.IsDBNull(reader["DRMaxLogVolumes"]) ? string.Empty : Convert.ToString(reader["DRMaxLogVolumes"]);
            //maxEmcSrdflog.PRLogUsedSpace = Convert.IsDBNull(reader["PRLogUsedSpace"]) ? string.Empty : Convert.ToString(reader["PRLogUsedSpace"]);
            //maxEmcSrdflog.DRLogUsedSpace = Convert.IsDBNull(reader["DRLogUsedSpace"]) ? string.Empty : Convert.ToString(reader["DRLogUsedSpace"]);
            //maxEmcSrdflog.PRLogMirrored = Convert.IsDBNull(reader["PRLogMirrored"]) ? string.Empty : Convert.ToString(reader["PRLogMirrored"]);
            //maxEmcSrdflog.DRLogMirrored = Convert.IsDBNull(reader["DRLogMirrored"]) ? string.Empty : Convert.ToString(reader["DRLogMirrored"]);

            //maxEmcSrdflog.PRLogWriting = Convert.IsDBNull(reader["PRLogWriting"]) ? string.Empty : Convert.ToString(reader["PRLogWriting"]);
            //maxEmcSrdflog.DRLogWriting = Convert.IsDBNull(reader["DRLogWriting"]) ? string.Empty : Convert.ToString(reader["DRLogWriting"]);

            //maxEmcSrdflog.PRLogAutoOverwrite = Convert.IsDBNull(reader["PRLogAutoOverwrite"]) ? string.Empty : Convert.ToString(reader["PRLogAutoOverwrite"]);
            //maxEmcSrdflog.DRLogAutoOverwrite = Convert.IsDBNull(reader["DRLogAutoOverwrite"]) ? string.Empty : Convert.ToString(reader["DRLogAutoOverwrite"]);

            //maxEmcSrdflog.PRInstanceType = Convert.IsDBNull(reader["PRInstanceType"]) ? string.Empty : Convert.ToString(reader["PRInstanceType"]);
            //maxEmcSrdflog.DRInstanceType = Convert.IsDBNull(reader["DRInstanceType"]) ? string.Empty : Convert.ToString(reader["DRInstanceType"]);


           
            return maxEmcSrdflog;
        }
    }
}
