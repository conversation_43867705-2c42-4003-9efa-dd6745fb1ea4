﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Licencekey", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class Licencekey : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Key { get; set; }

        [DataMember]
        public DateTime ValidFrom { get; set; }

        [DataMember]
        public DateTime ValidTo { get; set; }

        [DataMember]
        public int Status { get; set; }

        #endregion Properties
    }
}