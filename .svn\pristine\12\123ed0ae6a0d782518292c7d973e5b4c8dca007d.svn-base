﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EMCSRDFSGConfiguration.ascx.cs" Inherits="CP.UI.Controls.EMCSRDFSGConfiguration" %>

<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>


<asp:UpdatePanel ID="upnlGlobalMirror" runat="server" UpdateMode="Conditional">
    <ContentTemplate>
         <input type="hidden" id="hdfStaticGuid" runat="server" />
        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-head">
                    <h4 class="heading">EMC Management Console</h4>
                </div>
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-replication">
                            Select Server <span class="inactive">*</span>
                        </label>
                        <div class="col-md-9">
                            <asp:DropDownList ID="ddlServer" runat="server" AutoPostBack="true" OnSelectedIndexChanged="DdlServerSelectedIndexChanged"
                                CssClass="selectpicker col-replication-dropdown" data-style="btn-default">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvddlServer" runat="server" ControlToValidate="ddlServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select Server"></asp:RequiredFieldValidator>
                            <asp:Label ID="lblServer" runat="server" ForeColor="Red"></asp:Label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            Server Hostname
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDSCLIHostname" CssClass="form-control" Style="width: 49% !important;" Enabled="False" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            Server IP
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtDSCLIServerIP" CssClass="form-control" Style="width: 49% !important;" Enabled="False" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            SSH User ID
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSSHUserID" Enabled="False" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-replication">
                            SSH Password
                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSSHPassword" CssClass="form-control" Enabled="False" Style="width: 49% !important;" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-head">
                    <h4 class="heading">EMCSRDF - SG info</h4>
                </div>
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-md-3 control-label">
                            No.of Sites<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:DropDownList ID="ddlnoofsites" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlnoofsites_SelectedIndexChanged"
                                CssClass="selectpicker col-md-6" data-style="btn-default">
                                <asp:ListItem Selected="True">2</asp:ListItem>
                                <asp:ListItem>3</asp:ListItem>
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvnoofsites" runat="server" ControlToValidate="ddlnoofsites" CssClass="error"
                                InitialValue="0" Display="Dynamic" ErrorMessage="Please Select Site"></asp:RequiredFieldValidator>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-replication">
                            SymCLI Path

                        </label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtSymCLIPath" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvSymCLIPath" runat="server" ControlToValidate="txtSymCLIPath" CssClass="error"
                                Display="Dynamic" ErrorMessage="Please Enter SymCLI Path"></asp:RequiredFieldValidator>
                        </div>
                    </div>

                    <div id="twosite" runat="server" visible="false">
                        <div class="form-group">
                            <label class="col-replication">
                                SID <span class="inactive">*</span></label>

                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 21%">
                                        PR</label>
                                    <asp:TextBox ID="txtSIDPR" CssClass="form-control" Style="width: 78% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvSIDPR" runat="server" ControlToValidate="txtSIDPR" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter PR SID"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 21%">
                                        DR</label>
                                    <asp:TextBox ID="txtSIDDR" CssClass="form-control" Style="width: 78% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvSIDDR" runat="server" ControlToValidate="txtSIDDR" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter DR SID"></asp:RequiredFieldValidator>
                                </div>
                            </div>

                        </div>
                        <div class="form-group">
                            <label class="col-replication">
                                RDFG<span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <asp:TextBox ID="txtRDFG" CssClass="form-control" Style="width: 49% !important;" Enabled="True" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvRDFG" runat="server" ControlToValidate="txtRDFG" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter RDFG"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-replication">
                                SGName<span class="inactive">*</span></label>
                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 21%">
                                        PR</label>
                                    <asp:TextBox ID="txtSGnamePR" CssClass="form-control" Style="width: 78% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvSGnamePR" runat="server" ControlToValidate="txtSGnamePR" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter PR SG Names"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-4" style="width: 18%">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 22.5%">
                                        FDR</label>
                                    <asp:TextBox ID="txtSGNamefdr" CssClass="form-control" Style="width: 77% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txtSGNamefdr" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter FDR SG Names"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-replication">
                                Pair File Path <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <asp:TextBox ID="txtpairfilepath" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ControlToValidate="txtpairfilepath" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter File Path"></asp:RequiredFieldValidator>

                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-replication">
                                Pair File Name <span class="inactive">*</span></label>
                            <div class="col-md-9">

                                <asp:TextBox ID="txtpairfilename" CssClass="form-control" Style="width: 49% !important;" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvtxtpairfilename" runat="server" ControlToValidate="txtpairfilename" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter File Name"></asp:RequiredFieldValidator>

                            </div>
                        </div>
                    </div>

                    <div id="threesites" runat="server" visible="false">
                        <div class="form-group">
                            <label class="col-md-3 control-label">
                                Mode of Replication<span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <asp:DropDownList ID="ddlReplicationMode" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlReplicationMode_SelectedIndexChanged"
                                    CssClass="selectpicker col-md-6" data-style="btn-default">
                                    <%-- <asp:ListItem Selected="True">Concurrent</asp:ListItem>
                                    <asp:ListItem>Cascaded</asp:ListItem>--%>
                                </asp:DropDownList>
                                <asp:RequiredFieldValidator ID="rfvReplicationMode" runat="server" ControlToValidate="ddlReplicationMode" CssClass="error"
                                    InitialValue="0" Display="Dynamic" ErrorMessage="Please Select Replication Mode"></asp:RequiredFieldValidator>
                            </div>
                        </div>

                    </div>

                    <div id="Concurrent" runat="server" visible="false">
                        <div class="form-group">
                            <label class="col-replication">
                                SGName<span class="inactive">*</span></label>
                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 20%">
                                        PR</label>
                                    <asp:TextBox ID="txtconcPRSGname" CssClass="form-control" Style="width: 77% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvconcPRSGname" runat="server" ControlToValidate="txtconcPRSGname" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter PR SG Name"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-4" style="width: 18%">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 26.5%">
                                        NDR</label>
                                    <asp:TextBox ID="txtConcNDRSGName" CssClass="form-control" Style="width: 71% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcNDRSGName" runat="server" ControlToValidate="txtConcNDRSGName" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter NDR SG Name"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 22%">
                                        FDR</label>
                                    <asp:TextBox ID="txtConcFDRSGName" CssClass="form-control" Style="width: 71% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcFDRSGName" runat="server" ControlToValidate="txtConcFDRSGName" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter FDR SG Name"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-replication">
                                SID <span class="inactive">*</span></label>
                            <%--<div class="col-md-9">--%>
                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 20%">
                                        PR</label>
                                    <asp:TextBox ID="txtConcPRSID" CssClass="form-control" Style="width: 77% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcPRSID" runat="server" ControlToValidate="txtConcPRSID" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter PR SID"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 26.5%">
                                        NDR</label>
                                    <asp:TextBox ID="txtConcNDRSID" CssClass="form-control" Style="width: 71% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcDRSID" runat="server" ControlToValidate="txtConcNDRSID" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter NDR SID"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 22%">
                                        FDR</label>
                                    <asp:TextBox ID="txtConcFDRSID" CssClass="form-control" Style="width: 71% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcFDRSID" runat="server" ControlToValidate="txtConcFDRSID" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter FDR SID"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <%--</div>--%>
                        </div>

                        <div class="form-group">
                            <label class="col-replication">
                                <%--Pair File Path <span class="inactive">*</span>--%></label>
                            <div class="col-md-9">
                                <div class="col-md-4  padding-none">
                                    <asp:Label ID="lblTextPR" Text="PR to Near Site" runat="server"></asp:Label>
                                </div>
                                <div class="col-md-4  padding-none">
                                    <asp:Label ID="lblTextDR" Text="PR to FDR Site" runat="server"></asp:Label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-replication">
                                RDFG<span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <div class="col-md-4  padding-none">
                                    <asp:TextBox ID="txtConcPRRDFG" CssClass="form-control" Style="width: 90% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcPRRDFG" runat="server" ControlToValidate="txtConcPRRDFG" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter RDFG"></asp:RequiredFieldValidator>
                                </div>
                                <div class="col-md-4  padding-none">
                                    <asp:TextBox ID="txtConcDRRDFG" CssClass="form-control" Style="width: 90% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcDRRDFG" runat="server" ControlToValidate="txtConcDRRDFG" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter RDFG"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-replication">
                                Pair File Path <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <div class="col-md-4  padding-none">
                                    <asp:TextBox ID="txtConcPRPairfilepath" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcPRPairfilepath" runat="server" ControlToValidate="txtConcPRPairfilepath" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter PR File Path"></asp:RequiredFieldValidator>
                                </div>
                                <div class="col-md-4  padding-none">
                                    <asp:TextBox ID="txtConcDRPairfilepath" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvConcDRPairfilepath" runat="server" ControlToValidate="txtConcDRPairfilepath" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter DR File Path"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-replication">
                                Pair File Name <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <div class="col-md-4  padding-none">
                                    <asp:TextBox ID="txtconcPRpairfilename" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvconcPRpairfilename" runat="server" ControlToValidate="txtconcPRpairfilename" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter PR Pair File Name"></asp:RequiredFieldValidator>
                                </div>
                                <div class="col-md-4  padding-none">
                                    <asp:TextBox ID="txtconcDRpairfilename" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvconcDRpairfilename" runat="server" ControlToValidate="txtconcDRpairfilename" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter DR Pair File Name"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>
                    </div>

                    <%--<div id="Cascaded" runat="server" visible="false">
                        <div class="form-group">
                            <label class="col-replication">
                                SGName</label>
                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 20%">
                                        PR</label>
                                    <asp:TextBox ID="txtCascPRSGName" CssClass="form-control" Style="width: 77% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvCascPRSGName" runat="server" ControlToValidate="txtCascPRSGName" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter PR SG Name"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-4" style="width: 18%">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 26.5%">
                                        NDR</label>
                                    <asp:TextBox ID="txtCascNDRSGName" CssClass="form-control" Style="width: 71% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator16" runat="server" ControlToValidate="txtCascNDRSGName" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter NDR SG Name"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            <div class="col-md-4" style="width: 18%;">
                                <div class="form-group">
                                    <label class="col-replication" style="width: 22%">
                                        FDR</label>
                                    <asp:TextBox ID="txtCascFDRSGName" CssClass="form-control" Style="width: 71% !important;" Enabled="True" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvCascFDRSGName" runat="server" ControlToValidate="txtCascFDRSGName" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter FDR SG Name"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-replication">
                               
                            <div class="col-md-9">
                                <div class="col-md-4  padding-none">
                                    <asp:Label ID="lbltextPR" Text="PR to Near Site" runat="server"></asp:Label>
                                </div>
                                <div class="col-md-4  padding-none">
                                    <asp:Label ID="lbltextDR" Text="Near Site to Far Site" runat="server"></asp:Label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-replication">
                                SID <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <div class="col-md-4  padding-none">
                                    <asp:TextBox ID="txtcascSID" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="rfvcascSID" runat="server" ControlToValidate="txtcascSID" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter SID"></asp:RequiredFieldValidator>
                                </div>
                                <div class="col-md-4  padding-none">
                                      <asp:TextBox ID="TextBox5" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator7" runat="server" ControlToValidate="txtcascSID" CssClass="error"
                                        Display="Dynamic" ErrorMessage="Please Enter SID"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-replication">
                                RDFG</label>
                            <div class="col-md-9">
                                <div class="col-md-4  padding-none">
                                <asp:TextBox ID="txtCascRDFG" CssClass="form-control" Style="width: 90% !important;" Enabled="True" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvCascRDFG" runat="server" ControlToValidate="txtCascRDFG" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter RDFG"></asp:RequiredFieldValidator>
                                    </div>
                                <div class="col-md-4  padding-none">
                                    <asp:TextBox ID="TextBox6" CssClass="form-control" Style="width: 90% !important;" Enabled="True" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator8" runat="server" ControlToValidate="txtCascRDFG" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter RDFG"></asp:RequiredFieldValidator>
                                    </div>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-replication">
                                Pair File Path <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                <div class="col-md-4  padding-none">
                                <asp:TextBox ID="txtCascpairfilepath" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvCascpairfilepath" runat="server" ControlToValidate="txtCascpairfilepath" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter File Path"></asp:RequiredFieldValidator>
                            </div>
                                <div class="col-md-4  padding-none">
                                <asp:TextBox ID="TextBox10" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator12" runat="server" ControlToValidate="txtCascpairfilepath" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter File Path"></asp:RequiredFieldValidator>
                            </div>
                        </div>
                       
                        </div>
                         <div class="form-group">
                            <label class="col-replication">
                                Pair File Name <span class="inactive">*</span></label>
                            <div class="col-md-9">
                                 <div class="col-md-4  padding-none">
                                <asp:TextBox ID="txtCascpairfileName" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="rfvCascpairfileName" runat="server" ControlToValidate="txtCascpairfileName" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter File Name"></asp:RequiredFieldValidator>
                                </div>
                            <div class="col-md-4  padding-none">
                                 <asp:TextBox ID="TextBox7" CssClass="form-control" Style="width: 90% !important;" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator9" runat="server" ControlToValidate="txtCascpairfileName" CssClass="error"
                                    Display="Dynamic" ErrorMessage="Please Enter File Name"></asp:RequiredFieldValidator>
                                </div>
                            </div>
                            </div>
                    </div>--%>
                </div>
            </div>
        </div>

        <div class="form-actions row">

            <div class="col-lg-7" style="margin-left: 37%;">
                <asp:Button ID="btnSave" runat="server" CssClass="btn btn-primary" Width="20%" OnClick="btnSave_Click"
                    Text="Save" CausesValidation="true" />
                <asp:Button ID="btnCancel" runat="server" CssClass="btn btn-default" Width="20%" OnClick="btnCancel_Click"
                    Text="Cancel" CausesValidation="false" />
            </div>
        </div>
    </ContentTemplate>

</asp:UpdatePanel>
