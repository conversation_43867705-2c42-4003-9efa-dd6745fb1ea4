﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IBIAGetAlertDetailsDataAccess
    {
        IList<BIAGetAlertDetails> GetBIAAlertDetailsBusinessServiceWise(int Businessserviceid);

        IList<BIAGetAlertDetails> GetBIAAlertDetailsBusinessFunctionWise(int BusinesssFunctionid);

        IList<BIAGetAlertDetails> GetBIAAlertDetailsCategoryWise();

        IList<BIAGetAlertDetails> GetBIAAlertDetailsComponentWise();
    }
}
