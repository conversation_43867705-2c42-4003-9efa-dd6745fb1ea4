﻿using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;

namespace CP.UI
{
    public abstract class BusinessServiceBasePageEditor : BusinessServiceBasePage, IEditor<BusinessService>
    {
        private string _message = string.Empty;

        #region Properties

        public BusinessService CurrentEntity
        {
            get
            {
                return CurrentBusinessService;
            }
            set
            {
                CurrentBusinessService = value;
            }
        }

        public int CurrentEntityId
        {
            get { return CurrentBusinessServiceId; }
            set { CurrentBusinessServiceId = 0; }
        }

        public abstract string MessageInitials
        {
            get;
        }

        protected new string Message
        {
            get
            {
                return _message;
            }
            set
            {
                _message = value;
            }
        }

        public abstract string ReturnUrl
        {
            get;
        }

        public virtual Label TotalResult
        {
            get
            {
                return null;
            }
        }

        #endregion Properties

        #region Method

        public BusinessService BuildEntity(BusinessService currentEntity)
        {
            return currentEntity;
        }

        public abstract void PrepareEditView();

        public abstract void SaveEditor();

        public virtual void PrepareValidator()
        {
        }

        public abstract void BuildEntities();

        public virtual void BindList()
        {
        }

        public virtual void Delete(int entityId)
        {
        }

        public virtual void ClearForm()
        {
            CurrentEntityId = 0;
        }

        public virtual void FinalizeCommand()
        {
        }

        #endregion Method
    }
}