//
// Modals
// --------------------------------------------------

// Backdrop
// -------------------------

.modal-backdrop {
	z-index: 1060;
	background: #000000;
	&, &.fade.in {
		opacity: 0.9;
		filter: alpha(opacity=90);
	}
}

// Modal
// -------------------------

.modal {
	z-index: 1060;
	.modal-content {
		border-color: @contentBorder;
		box-shadow: 0 3px 0 0 @contentShadow1;
		-moz-box-shadow: 0 3px 0 0 @contentShadow1;
		-webkit-box-shadow: 0 3px 0 0 @contentShadow1;
		background: @widgetBg;
	}
	.modal-header {
		padding: 0 10px;
		height: 39px;
		line-height: 39px;
		border-color: @contentBorder;
		#gradient > .vertical(@contentGradient1, @contentGradient2);
		.rounded(5px,5px,0,0);
		.modal-title {
			font-size: 18px;
			line-height: 32px;
			height: 39px;
			margin: 0;
			padding: 0;
			font-weight: 400;
		}
		.close {
			position: absolute;
			top: 5px;
			right: 10px;
			padding: 0;
			margin: 0;
			opacity: 1;
			filter: alpha(opacity=100);
			color: @bodyText;
			text-shadow: @bodyTextShadow;
		}
	}
	.modal-body { padding: 10px; p:last-child { margin: 0; } }
	.modal-footer {
		.rounded(0,0,5px,5px);
		.bevelEmboss(0,0);
		border-color: @contentBorder2;
		padding: 10px;
		background: @contentBg1;
	}
}