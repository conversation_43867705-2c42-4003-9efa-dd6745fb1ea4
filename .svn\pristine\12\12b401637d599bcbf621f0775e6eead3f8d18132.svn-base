﻿using CP.UI.Code.Replication.Component;
using CP.UI.Code.Replication.DataLag;
using CP.UI.Code.Replication.ReplicationInfo;

namespace CP.UI.Code.Replication.Clients
{
    public class MySqlGlobalMirrorClient : Replication
    {
        public MySqlGlobalMirrorClient()
        {
            Datalag = new OracleDataLag();

            ComponentInfo = new MySqlGlobalMirrorComponent();

            ReplicationInfo = new GlobalMirrorReplicationInfo();
        }
    }
}