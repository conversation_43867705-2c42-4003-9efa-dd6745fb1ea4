﻿using System.Web.UI.WebControls;
using BCMS.Common.DatabaseEntity;

namespace BCMS.UI 
{
    public abstract class OracleDataGuardBaseControlEditor:OracleDataGuardBaseControl,IEditor<OracleDataGuard>
    {

        private string _message = string.Empty;

        #region Properties

        public OracleDataGuard CurrentEntity
        {
            get
            {
                return base.CurrentOracleDataGuard;
            }
            set
            {
                base.CurrentOracleDataGuard = value;
            }
        }

        public int CurrentEntityId
        {
            get { return base.CurrentOracleDataGuardId; }
            set { base.CurrentOracleDataGuardId = 0; }
        }


        public abstract Label MessageViewer
        {
            get;
        }

        public abstract string MessageInitials
        {
            get;
        }
        protected new string Message
        {
            get
            {
                return _message;
            }
            set
            {
                _message = value;
            }
        }

        public abstract string ReturnUrl
        {
            get;
        }
        public virtual Label TotalResult
        {
            get
            {
                return null;
            }
        }

        #endregion

        #region Method

        public OracleDataGuard BuildEntity(OracleDataGuard currentEntity)
        {
            return currentEntity;
        }

        public abstract void PrepareEditView();

        public abstract void SaveEditor();

        public virtual void PrepareValidator()
        {

        }

        public abstract void BuildEntities();

        public virtual void BindList() { }

        public virtual void Delete(int entityId)
        {

        }

        public virtual void ClearForm()
        {
            CurrentEntityId = 0;
        }

        public virtual void FinalizeCommand()
        {

        }

        #endregion
    }
}
