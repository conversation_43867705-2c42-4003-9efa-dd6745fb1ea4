﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess.LoadMaster
{
    internal sealed class CPLoadMasterDataAccess : BaseDataAccess, ICPLoadMasterDataAccess
    {
        #region Constructors
        

        public CPLoadMasterDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<CPLoadMaster> CreateEntityBuilder<CPLoadMaster>()
        {
            return (new CPLoadMasterBuilder()) as IEntityBuilder<CPLoadMaster>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="Nodes" />into CPLoadMaster table.
        /// </summary>

        CPLoadMaster ICPLoadMasterDataAccess.Add(CPLoadMaster nodes)
        {
            try
            {
                const string sp = "CPLoadMaster_Create";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iNodeID", DbType.String, nodes.NodeID);
                    // Database.AddInParameter(cmd, Dbstring + "iBServiceID", DbType.Int32, nodes.BServiceID);
                    // Database.AddInParameter(cmd, Dbstring + "iBFunctionID", DbType.Int32, nodes.BFunctionID);
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.String, nodes.InfraID);
                    Database.AddInParameter(cmd, Dbstring + "iIPHostName", DbType.String, nodes.IPHostName);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, nodes.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, nodes.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, nodes.Port);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.String, nodes.Status);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.String, nodes.Name);
                    Database.AddInParameter(cmd, Dbstring + "iTransferNodeId", DbType.String, nodes.TransferNodeId);
                    Database.AddInParameter(cmd, Dbstring + "iIsTransferLoad", DbType.Int32, nodes.IsTransferLoad);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.Int32, nodes.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorID", DbType.Int32, nodes.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, nodes.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "ibusinessserviceid", DbType.String, nodes.BServiceID);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                  int rowsafftected=  Database.ExecuteNonQuery(cmd);

                    //using (IDataReader reader = Database.ExecuteReader(cmd))
                    //{
                    //    nodes = reader.Read() ? CreateEntityBuilder<CPLoadMaster>().BuildEntity(reader, nodes) : null;
                    //}

                    //if (nodes == null)
                    //{
                    //    //int _nodes = (int)(decimal)cmd.ExecuteScalar();
                    //    var returnCode = (int)(decimal)GetReturnCodeFromParameter(cmd);
                    //    int Result = Convert.ToInt32(returnCode);
                    //    switch (Result)
                    //    {
                    //        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                    //            {
                    //                throw new ArgumentException("Nodes already exists. Please specify another Nodes.");
                    //            }
                    //        default:
                    //            {
                    //                throw new SystemException(
                    //                    "An unexpected error has occurred while creating this Nodes.");
                    //            }
                    //    }
                    //}

                   
                    return nodes;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting node Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }




     

        /// <summary>
        ///     Update <see cref="Nodes" />into CPLoadMaster table.
        /// </summary>

        CPLoadMaster ICPLoadMasterDataAccess.CPLodeUpdate(CPLoadMaster lode)
        {
            try
            {
                const string sp = "CPLoadMaster_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, lode.Id);
                    Database.AddInParameter(cmd, Dbstring + "iNodeID", DbType.String, lode.NodeID);
                    Database.AddInParameter(cmd, Dbstring + "iBServiceID", DbType.String, lode.BServiceID);
                    //  Database.AddInParameter(cmd, Dbstring + "iBFunctionID", DbType.Int32, lode.BFunctionID);
                    Database.AddInParameter(cmd, Dbstring + "iInfraID", DbType.String, lode.InfraID);
                    Database.AddInParameter(cmd, Dbstring + "iIPHostName", DbType.String, lode.IPHostName);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, lode.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, lode.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, lode.Port);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.String, lode.Status);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.String, lode.Name);
                    Database.AddInParameter(cmd, Dbstring + "iTransferNodeId", DbType.String, lode.TransferNodeId);
                    Database.AddInParameter(cmd, Dbstring + "iIsTransferLoad", DbType.Int32, lode.IsTransferLoad);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, lode.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorID", DbType.Int32, lode.UpdatorId);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        lode = reader.Read() ? CreateEntityBuilder<CPLoadMaster>().BuildEntity(reader, lode) : null;
                    }

                    //if (lode == null)
                    //{
                    //    int returnCode = GetReturnCodeFromParameter(cmd);

                    //    switch (returnCode)
                    //    {
                    //        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                    //            {
                    //                throw new ArgumentException("Group already exists. Please specify another CPSLScript.");
                    //            }
                    //        default:
                    //            {
                    //                throw new SystemException("An unexpected error has occurred while updating this CPSLScript.");
                    //            }
                    //    }
                    //}

                    return lode;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Executing Function Signature CPLoadMaster.Update"
                     , exc);
            }
        }

        CPLoadMaster ICPLoadMasterDataAccess._GetCPLoadByNodeIdd(int iNodeID)
        {
            try
            {
                const string sp = "CPLoadMaster_GetNodeDetailsById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iNodeID", DbType.Int32, iNodeID);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<CPLoadMaster>().BuildEntities(reader);
                        return reader.Read() ? (CreateEntityBuilder<CPLoadMaster>()).BuildEntity(reader, new CPLoadMaster()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPNodeMasterDataCcess._GetCPLoadByNodeIdd()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


      


        bool ICPLoadMasterDataAccess.DeleteById(int id, int status)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "CPLoadMaster_DeleteById";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.Int32, status);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting infraobject_scheduler Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        /// <summary>
        ///     Get <see cref="Nodes" />from CPLoadMaster table.
        /// </summary>
        /// <returns>Nodes</returns>

        bool ICPLoadMasterDataAccess.DeleteNodeById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "CPLoadMaster_DeleteNodeById";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting infraobject_scheduler Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<CPLoadMaster> ICPLoadMasterDataAccess.GetByNodeId(string iNodeID)
        {
            try
            {
                //if (iNodeID < 1)
                //{
                //    throw new ArgumentNullException("iNodeID");
                //}
                const string sp = "CPLoadMaster_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iNodeID", DbType.String, iNodeID);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<CPLoadMaster>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPNodeMasterDataCcess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<CPLoadMaster> ICPLoadMasterDataAccess.GetAllByNodeId(string iNodeID)
        {
            try
            {
                //if (iNodeID < 1)
                //{
                //    throw new ArgumentNullException("iNodeID");
                //}
                const string sp = "CPLoadMasteGetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iNodeID", DbType.Int32, iNodeID);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<CPLoadMaster>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPNodeMasterDataCcess.GetAllByNodeId()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        CPLoadMaster ICPLoadMasterDataAccess.GetByNodeByNodeName(string iNodeName)
        {
            try
            {
                //if (iNodeID < 1)
                //{
                //    throw new ArgumentNullException("iNodeID");
                //}
                const string sp = "CPLoadMaster_GetNodeByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.String, iNodeName);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<CPLoadMaster>().BuildEntities(reader);
                        return reader.Read() ? (CreateEntityBuilder<CPLoadMaster>()).BuildEntity(reader, new CPLoadMaster()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPNodeMasterDataCcess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }



        bool ICPLoadMasterDataAccess.UpdateTransferNodeIdById(int id, string TransferNodeId)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "CPLoadUpdateNodeTransById";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iTransferNodeId", DbType.String, TransferNodeId);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting infraobject_scheduler Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<CPLoadMaster> ICPLoadMasterDataAccess.GetAllCPLoadMasterByServerID(int ServerId)
        {
            try
            {
                //if (iNodeID < 1)
                //{
                //    throw new ArgumentNullException("iNodeID");
                //}
                const string sp = "CPLoadMasterGetByServerID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServerID", DbType.Int32, ServerId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<CPLoadMaster>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPNodeMasterDataCcess.GetAllCPLoadMasterByServerID()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        #endregion

    }
}
