﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IInfraObjectJobDataAccess
    {
        InfraObjectJob Add(InfraObjectJob InfraObjectJob);

        InfraObjectJob GetById(int id);

        InfraObjectJob GetInfraObjectByInfraId(int id);

        IList<InfraObjectJob> GetByInfraObjectId(int infraObjectId);

        IList<InfraObjectJob> GetByBusinessServiceId(int applicationId);

        IList<InfraObjectJob> GetByStorageImageId(string storageImageId);

        IList<InfraObjectJob> GetByCategory(string category);

        IList<InfraObjectJob> GetByJobId(int id);

        IList<InfraObjectJob> GetAll();

        IList<InfraObjectJob> GetInfraObjectJobByTime(int repid);

        IList<InfraObjectJob> GetInfraObjectByBusinessId(int infratype);

        bool DeleteById(int id);

        bool UpdateById(InfraObjectJob infraObjectJob);

        bool FillCronById(int id, string cronExp, string cronTime, int changecorn, int jobid);

        bool FillInfraObjectJobAllCronById(int id, string cronExp, string cronTime, int changecorn);

        InfraObjectJob Update(InfraObjectJob infraObjectJob);

        bool DelteByInfraObjectId(InfraObjectJob infraObjectJob);

        bool DeleteByBusinessServiceId(InfraObjectJob infraObjectJob);

        bool DeleteByStorageImageId(InfraObjectJob infraObjectJob);

        bool DeleteByInfraIdandJobId(int infraId, int JobId);

        bool DeleteJobByInfraObjectIdandJobId(string jobname, int jobId);

        bool ChangeStatusById(int Id);

        bool FillCronByInfraId(int id, string cronExp, string cronTime, int changecorn);

        InfraObjectJob GetInfraObjectByJobType(int id, string job);

        InfraObjectJob GetInfraObjectBusinessByJobType(int id, string job);

        bool FillBusinessJobCronById(int businessid, string cronExp, string cronTime, int changecorn, int jobid);

        bool FillDRReadyJobCronById(int jobid, string cronExp, string cronTime, int changecorn);

        bool DeleteInfraObjectJob(int jobId);

        bool DeleteInfraJob(int jobId);
    }
}