﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class SybaseDatabaseConfig : System.Web.UI.UserControl
    {
        public string DatabaseSID
        {
            get
            {
                return txtname.Text;
            }
            set
            {
                txtname.Text = value;
            }
        }

        public string UserName
        {
            get
            {
                return txtUserName.Text;
            }
            set
            {
                txtUserName.Text = value;
            }
        }

        public string Password
        {
            get
            {
                return txtPassword.Text;
            }
            set
            {
                txtPassword.Text = value;
            }
        }

        public string Port
        {
            get
            {
                return txtPort.Text;
            }
            set
            {
                txtPort.Text = value;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            txtname.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidatortxtdtname.ClientID + ")");
            txtUserName.Attributes.Add("onblur", "ValidatorValidate(" + rfvusername.ClientID + ")");
            txtPassword.Attributes.Add("onblur", "ValidatorValidate(" + rfvpassword.ClientID + ")");
            txtPort.Attributes.Add("onblur", "ValidatorValidate(" + rfvport.ClientID + ")");
        }
    }
}