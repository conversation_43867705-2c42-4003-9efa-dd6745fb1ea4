﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess.AppDepMapLinks
{
    internal sealed class AppDepMappingLinksBuilder : IEntityBuilder<AppDependencyLinks>
    {
        IList<AppDependencyLinks> IEntityBuilder<AppDependencyLinks>.BuildEntities(IDataReader reader)
        {
            var appDependencyLinks = new List<AppDependencyLinks>();

            while (reader.Read())
            {
                appDependencyLinks.Add(((IEntityBuilder<AppDependencyLinks>)this).BuildEntity(reader,
                    new AppDependencyLinks()));
            }

            return (appDependencyLinks.Count > 0) ? appDependencyLinks : null;
        }

        AppDependencyLinks IEntityBuilder<AppDependencyLinks>.BuildEntity(IDataReader reader,
            AppDependencyLinks appDependencyLinks)
        {

            appDependencyLinks.SourceHost = Convert.IsDBNull(reader["SourceHost"]) ? string.Empty : Convert.ToString(reader["SourceHost"]);

            appDependencyLinks.TargetHost = Convert.IsDBNull(reader["TargetHost"]) ? string.Empty : Convert.ToString(reader["TargetHost"]);

            appDependencyLinks.DependencyProfileName = Convert.IsDBNull(reader["DependencyProfileName"]) ? string.Empty : Convert.ToString(reader["DependencyProfileName"]);

            return appDependencyLinks;
        }
    }
}
