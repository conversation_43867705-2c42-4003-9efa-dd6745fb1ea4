﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Helper;
using SpreadsheetGear;
using System.Web.UI.WebControls;
using System.Data;
using Gios.Pdf;
using System.Drawing;
using System.Collections;
using System.Globalization;
using System.Configuration;
using CP.UI;
using System.IO;
using log4net;

namespace CP.UI.Controls
{
    public partial class ApplicationDiscovery : BaseControl
    {


        #region Variables

        private string[] xlColumn = { "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "Z" };
        IWorkbookSet workbookSet = null;
        String ssFile = string.Empty;
        IWorkbook templateWorkbook = null;
        IWorksheet templateWorksheet = null;
        IRange cells = null;
        private readonly ILog _logger = LogManager.GetLogger(typeof(ApplicationDiscovery));
        #endregion Variables

        public override void PrepareView()
        {
            var profilelist = Facade.GetAllApplicationDiscoveryProfiles();
            if (profilelist != null)
            {
                ddldiscoveryprofile.DataSource = profilelist;
                //ddldiscoveryprofile.DataValueField = profilelist;
                ddldiscoveryprofile.DataBind();

            }
            else

                ddldiscoveryprofile.Items.Insert(0, new ListItem("No Profile Found", "0"));

        }


        private void ExcelReport()
        {
            _logger.Info("======Generating Application Discovery Report EXCEL View ======");
            _logger.Info(Environment.NewLine);

            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "Application Discovery Details";

            _cells["A1"].ColumnWidth = 7;

            _cells["E3"].Formula = "Application Discovery  Report";
            _cells["E3"].HorizontalAlignment = HAlign.Center;
            _cells["E3"].Font.Bold = true;
            _cells["B3:H6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:H3"].Font.Size = 11;
            _cells["B5:H8"].Font.Size = 10;
            _cells["B3:H8"].Font.Color = Color.White;
            _cells["B3:H8"].Font.Name = "Cambria";





            //var stdt = Convert.ToDateTime(txtstart.Text);
            //var startdt = stdt.ToString("dd-MMM-yy");
            //var endt = Convert.ToDateTime(txtend.Text);
            //var enddt = endt.ToString("dd-MMM-yy");

            IList<CP.Common.DatabaseEntity.ApplicationDiscovery> Appdiscovery = new List<CP.Common.DatabaseEntity.ApplicationDiscovery>();

            Appdiscovery = Facade.GetAllApplicationDiscoveryDetails(ddldiscoveryprofile.SelectedItem.Text);

            if (Appdiscovery != null && Appdiscovery.Count > 0)
            {

                //if (ddlusename.SelectedItem.Text == "All")
                //{
                //    lstUser = Facade.GetUserActivityByStartEndDate(startdt, enddt);
                //}
                //else
                //{
                //    string loginname = ddlusename.SelectedItem.ToString();
                //    lstUser = Facade.GetUserActivityByDate(loginname, startdt, enddt);
                //}

                //var getdetail = _facade.GetUserActivityByStartEndDate(startdt, enddt);

                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 48, 10, 150, 22);
               
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 690, 10, 120, 13);
                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 400, 10, 121, 13);
                }

                reportWorksheet.Cells["A1:F1"].RowHeight = 27;
                reportWorksheet.Cells["A2:F2"].RowHeight = 25;



                var dateTime = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")); //DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                _cells["B5"].Formula = "Report Generation Time";
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;

                _cells["C5"].Formula = ":  " + dateTime;
                _cells["C5"].Font.Bold = true;
                _cells["C5"].HorizontalAlignment = HAlign.Left;



                _cells["D5"].Formula = "Discovery Profile Name";
                _cells["D5"].Font.Bold = true;
                _cells["D5"].HorizontalAlignment = HAlign.Left;

                _cells["E5"].Formula = ": " + ddldiscoveryprofile.SelectedItem.Text.ToString();
                _cells["E5"].Font.Bold = true;
                _cells["E5"].HorizontalAlignment = HAlign.Left;


                int row = 8;
                int i = 1;

                _cells["B" + row.ToString()].Formula = "Sr.No.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:H8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:H8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.Black;
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Host";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "State";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["E" + row.ToString()].Formula = "LastBoot";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["F" + row.ToString()].Formula = "OperatingSystem";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["G" + row.ToString()].Formula = "Application/Services";
                _cells["G" + row.ToString()].Font.Bold = true;
                _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;

                //_cells["H" + row.ToString()].Formula = "Discovery ProfileName";
                //_cells["H" + row.ToString()].Font.Bold = true;
                //_cells["H" + row.ToString()].HorizontalAlignment = HAlign.Center;


                row++;
                int dataCount = 0;
                int xlRow = 9;
                _logger.Info("======" + Appdiscovery.Count + " Records Retrieve for Application Discovery Report ======");
                _logger.Info(Environment.NewLine);
                foreach (var rp in Appdiscovery)
                {
                    dataCount++;
                    int column = 0;
                    string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H" };
                    xlRow++;

                    string ndx = xlColumn[column] + row.ToString();
                    _cells[ndx + ":" + "H" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;



                    _cells[ndx].Formula = i.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    i++;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.Host != null ? rp.Host : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.State != null ? rp.State : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                   // _cells[ndx].Formula = rp.LastBoot != null ? rp.LastBoot.ToString() : "NA";
                    _cells[ndx].Formula = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")) != null ? Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")): "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.OperatingSystem != null ? rp.OperatingSystem : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.AllApps != null ? rp.AllApps : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;


                    //ndx = xlColumn[column] + row.ToString();
                    //_cells[ndx].Formula = rp.DiscoveryProfileName != null ? rp.DiscoveryProfileName.ToShortDateString() : "NA";
                    //_cells[ndx].Font.Size = 10;
                    //_cells[ndx].ColumnWidth = 23;
                    //_cells[ndx].Font.Color = Color.Black;
                    //_cells[ndx].HorizontalAlignment = HAlign.Center;
                    //column++;

                    row++;
                }

                int finalCount = dataCount + 10;
                _cells["B" + finalCount].Formula = "NA : Not Available";
                _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
                _cells["B" + finalCount].Font.Name = "Cambria";
                _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

                reportWorksheet.ProtectContents = true;
                OpenExcelFile(reportWorkbook);
                _logger.Info("====== Application Discovery EXCEL Report generated ======");
                _logger.Info(Environment.NewLine);
            }
            else
            {

                lblMsg.Visible = true;
                lblMsg.Text = "No Records Found";
                _logger.Info("====== Application Discovery EXCEL Report not generated ======");
                _logger.Info(Environment.NewLine);
            }

        }


        private void OpenExcelFile(IWorkbook workbook)
        {
            //txtReportMessage.Text = string.Empty;
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=report.xls");
            string _str = DateTime.Now.ToString("ddMMyyy");
            _str = "ApplicationDiscovery" + _str + ".xls";

            foreach (IWorksheet ws in workbook.Worksheets)
                ws.ProtectContents = true;

            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + _str), FileFormat.Excel8);
            //string myUrl = "/ExcelFiles/" + _str;
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + _str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Application Discovery Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }

        protected void btnViewReport_Click(object sender, EventArgs e)
        {
            try
            {
                ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void ddldiscoveryprofile_SelectedIndexChanged(object sender, EventArgs e)
        {

        }
    }
}