﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class MaintenanceBuilder : IEntityBuilder<Maintenance>
    {
        IList<Maintenance> IEntityBuilder<Maintenance>.BuildEntities(IDataReader reader)
        {
            var Maintenance = new List<Maintenance>();
            while (reader.Read())
            {
                Maintenance.Add(((IEntityBuilder<Maintenance>)this).BuildEntity(reader, new Maintenance()));
            }
            return Maintenance;
        }

        Maintenance IEntityBuilder<Maintenance>.BuildEntity(IDataReader reader, Maintenance maintenance)
        {
            //const int FLD_ID = 0;
            //const int FLD_REASON  = 1;
            //const int FLD_GROUPID =2;
            //const int FLD_APPLICATIONID = 3;
            //const int FLD_MODE = 4;
            //const int FLD_UNLOCKTIME =5;
            //const int FLD_EVENTNAME = 6;
            //const int FLD_Type = 7;
            //const int FLD_ISGROUP = 8;
            //const int FLD_CREATORID = 9;
            //const int FLD_CREATEDDATE =  10;

            //maintenance.Id = reader.IsDBNull ( FLD_ID ) ? 0 : reader.GetInt32 ( FLD_ID );
            //maintenance.Reason = reader.IsDBNull ( FLD_REASON ) ? string.Empty : reader.GetString ( FLD_REASON );
            //maintenance.GroupId = reader.IsDBNull ( FLD_GROUPID ) ? 0 : reader.GetInt32 ( FLD_GROUPID );
            //maintenance.ApplicationId = reader.IsDBNull(FLD_APPLICATIONID) ? 0 : reader.GetInt32(FLD_APPLICATIONID);
            //maintenance.Mode = reader.IsDBNull ( FLD_MODE ) ? 0 : reader.GetInt32 ( FLD_MODE );
            //maintenance.UnlockTime = reader.IsDBNull ( FLD_UNLOCKTIME ) ? string.Empty : reader.GetString ( FLD_UNLOCKTIME );
            //maintenance.EventName = reader.IsDBNull ( FLD_EVENTNAME ) ? string.Empty : reader.GetString ( FLD_EVENTNAME );
            //maintenance.Type = reader.IsDBNull(FLD_Type) ? 0 : reader.GetInt32(FLD_Type);
            //maintenance.IsGroup = !reader.IsDBNull(FLD_ISGROUP) && Convert.ToBoolean(reader.GetInt32(FLD_ISGROUP));
            //maintenance.CreatorId = reader.IsDBNull ( FLD_CREATORID ) ? 0 : reader.GetInt32 ( FLD_CREATORID );
            //maintenance.CreateDate = reader.IsDBNull ( FLD_CREATEDDATE ) ? DateTime.MinValue : reader.GetDateTime ( FLD_CREATEDDATE );

            //Fields in bcms_maintenance table on 23/07/2013 : Id, Reason, GroupId, ApplicationId, Mode, UnlockTime, EventName, Type, IsGroup, CreatorId, CreateDate

            maintenance.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            maintenance.Reason = Convert.IsDBNull(reader["Reason"]) ? string.Empty : Convert.ToString(reader["Reason"]);
            maintenance.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"])
                ? 0
                : Convert.ToInt32(reader["InfraObjectId"]);
            maintenance.Mode = Convert.IsDBNull(reader["ModeType"]) ? 0 : Convert.ToInt32(reader["ModeType"]);
            maintenance.UnlockTime = Convert.IsDBNull(reader["UnlockTime"])
                ? string.Empty
                : Convert.ToString(reader["UnlockTime"]);
            maintenance.EventName = Convert.IsDBNull(reader["EventName"])
                ? string.Empty
                : Convert.ToString(reader["EventName"]);
            maintenance.Type = Convert.IsDBNull(reader["Type"]) ? 0 : Convert.ToInt32(reader["Type"]);

            if (!Convert.IsDBNull(reader["IsInfraObject"]))
                maintenance.IsInfraObject = Convert.ToBoolean(reader["IsInfraObject"]);

            maintenance.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            maintenance.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);

            return maintenance;
        }
    }
}