﻿using System;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess.MySqlNativeMonitor
{
    internal sealed class MySqlNativeMonitorDataAccess : BaseDataAccess, CP.DataAccess.IMySqlNativeMonitorDataAccess
    {
        #region Constructors

        public MySqlNativeMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<MySqlNative> CreateEntityBuilder<MySqlNative>()
        {
            return (new MySqlNativeMonitorBuilder()) as IEntityBuilder<MySqlNative>;
        }

        #endregion Constructors

        /// <summary>
        /// Get <see cref="MySqlNative" /> From mysqlnative_replication table by infraObjectId.
        /// </summary>
        /// <param name="InfraObjectID">InfraObjectID of the MySqlNative</param>
        /// <returns>MySqlNative</returns>
        /// <author><PERSON></author>
        /// <Modified> <PERSON><PERSON><PERSON> - 08-04-2014 - </Modified>

        #region Methods

        CP.Common.DatabaseEntity.MySqlNative IMySqlNativeMonitorDataAccess.GetbyInfraObjectId(int infraObjectId)
        {
            try
            {
                string sp = DbRoleName + "MySQLNative_GetInfraId";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iInfraObjectId", DbType.Int32, infraObjectId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<CP.Common.DatabaseEntity.MySqlNative>()).BuildEntity(reader, new CP.Common.DatabaseEntity.MySqlNative());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata, "Error In DAL While Executing Function Signature IGlobalMirrorReplicationDataAccess.GetbyInfraObjectId(" + infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}