﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ServicesMeetingRTO.ascx.cs" Inherits="CP.UI.Controls.ServicesMeetingRTO" %>
<div class="widget widget-heading-simple widget-body-white">
    <div class="widget-body">
        <div class="cioheader">
            <div class="title">Services
                 <asp:ImageButton ID="imgBtnReport" runat="server" Visible="false" ImageUrl="../Images/CIO/report-icon.png" style="float: right;" />
            </div>
            <asp:Label ID="lblcurrentmnth" runat="server" CssClass="subhead" Text="July-16"></asp:Label>
            <span class="subhead">(Current Month)</span>
            
        </div>
        <div class="ciocontent">

            <div class="col-md-12 padding-none-LR" style="display: table; margin-bottom: 2px;">
                <div class="col-md-6 padding-none-LR">
                    <div>
                        <asp:Label ID="lblMeetingRTO" runat="server" CssClass="count" Text="0"></asp:Label>
                    </div>
                    <span class="subhead">Meeting RTO</span>
                </div>
                <div class="col-md-6 padding-none-LR text-right">
                    <div>
                        <asp:Label ID="lblNotMeetingRTO" runat="server" CssClass="count" Text="0"></asp:Label>
                    </div>
                    <span class="subhead">Not Meeting RTO</span>
                </div>
            </div>
            <div class="text-center">
                <%--<img src="../Images/CIO/bar-graph-05.png" />--%>
                <telerik:RadHtmlChart runat="server" ID="BarChart" Width="190px" Height="120px" Transitions="true" Skin="Silk" >
                    <PlotArea>

                        <XAxis AxisCrossingValue="0" Color="white" MajorTickType="none" MinorTickType="none"
                            Reversed="false"  >
                           <MajorGridLines Visible="false" />
                            <MinorGridLines Visible="false" />
                        <LabelsAppearance DataFormatString="{0}" RotationAngle="0" Skip="0" Step="1" Color="#222222"></LabelsAppearance>
                           
                        </XAxis>
                        <YAxis AxisCrossingValue="0" Color="black" MajorTickSize="1" MajorTickType="Outside"
                            MinorTickType="None" Reversed="false" Visible="false">
                            <MajorGridLines Visible="false" />
                            <MinorGridLines Visible="false" />
                            <LabelsAppearance DataFormatString="{0}" RotationAngle="0" Skip="0" Step="1" Visible="false"></LabelsAppearance>
                            <%-- <TitleAppearance Position="Center" RotationAngle="0" Text="Sum"></TitleAppearance>--%>
                        </YAxis>
                    </PlotArea>
                    <Appearance>
                        <FillStyle BackgroundColor="Transparent"></FillStyle>
                    </Appearance>

                    <Legend>
                        <Appearance BackgroundColor="Transparent" Position="Bottom"></Appearance>
                    </Legend>
                </telerik:RadHtmlChart>
                <div>
                    <asp:Label ID="Label50" runat="server" CssClass="leg1"></asp:Label>
                <span class="legtext">Meet RTO</span>
                <asp:Label ID="Label51" runat="server" CssClass="leg2"></asp:Label>
                <span class="legtext">Not Meet RTO</span>
                </div>
            </div>
        </div>
        <div class="ciofooter">
            <div class="col-md-10 padding-none-LR">
                 <img src="../Images/CIO/services-apps-icon.png" />
            </div>
            <div class="col-md-2 padding-none-LR text-right">
                <%--<asp:ImageButton ID="ImageButton7" runat="server" ImageUrl="~/Images/CIO/i_icon.png" />--%>
                <span class="ciopopover" data-toggle="popover" data-title="Services/Functions Meeting RTO v/s not Meeting RTO"
                    data-content="1) Finds Services where drills have been conducted and if during Orchestration Automation execution, did orchestration workflows meet RTO/not meet RTO.<br /> 
                                  2) Also shows services where drills were not conducted during this month" data-placement="top" data-html="true"></span>
            </div>
        </div>
    </div>
</div>
