﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Ec2s3dsReplicationMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class Ec2s3dsReplicationMonitor : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _replicationBase = new ReplicationBase();

        #endregion

        #region Properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string ReplicationType { get; set; }
      
        [DataMember]
        public string ReplicationStatus { get; set; }

        [DataMember]
        public string SourceServerPath { get; set; }

        [DataMember]
        public string DestinationS3Path { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string SyncStatus { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }
        }

        #endregion Properties
    }
}
