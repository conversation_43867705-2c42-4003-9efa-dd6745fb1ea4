﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Diagnostics;
using System.Linq;
using CP.Helper;

namespace CP.Common.Shared
{
    [Serializable]
    public sealed class PagedRequest
    {
        private StringDictionary _conditions;
        private int _pageIndex;
        private int _rowPerPage;
        private string _sortColumn;
        private string _sortOrder;

        public PagedRequest()
        {
        }

        public PagedRequest(int pageIndex,
            int rowPerPage)
        {
            _pageIndex = pageIndex;
            _rowPerPage = rowPerPage;
        }

        public int PageIndex
        {
            [DebuggerStepThrough]
            get { return _pageIndex; }
            [DebuggerStepThrough]
            set { _pageIndex = value; }
        }

        public int RowPerPage
        {
            [DebuggerStepThrough]
            get { return _rowPerPage; }
            [DebuggerStepThrough]
            set { _rowPerPage = value; }
        }

        public StringDictionary Conditions
        {
            [DebuggerStepThrough]
            get { return _conditions ?? (_conditions = new StringDictionary()); }
        }

        public string SortColumn
        {
            [DebuggerStepThrough]
            get { return _sortColumn; }
            [DebuggerStepThrough]
            set { _sortColumn = value; }
        }

        public string SortOrder
        {
            [DebuggerStepThrough]
            get { return _sortOrder; }
            [DebuggerStepThrough]
            set { _sortOrder = value; }
        }

        public string[] GetParametersForCacheKey()
        {
            var parameters = new List<string> { "page=" + _pageIndex };
            if (SortColumn.IsNotNullOrEmpty())
            {
                parameters.Add("sort=" + SortColumn + SortOrder);
            }
            else
            {
                parameters.Add("sort=none");
            }
            if (Conditions != null && Conditions.Count > 0)
            {
                parameters.AddRange(from DictionaryEntry entry in Conditions select entry.Key + "=" + entry.Value);
            }

            return parameters.ToArray();
        }
    }
}