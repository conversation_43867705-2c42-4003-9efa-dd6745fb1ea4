﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class BIAGetAlertDetailsBuilder : IEntityBuilder<BIAGetAlertDetails>
    {
        IList<BIAGetAlertDetails> IEntityBuilder<BIAGetAlertDetails>.BuildEntities(IDataReader reader)
        {
            var BIAFA = new List<BIAGetAlertDetails>();

            while (reader.Read())
            {
                BIAFA.Add(((IEntityBuilder<BIAGetAlertDetails>)this).BuildEntity(reader, new BIAGetAlertDetails()));
            }
            return (BIAFA.Count > 0) ? BIAFA : null;
        }
        BIAGetAlertDetails IEntityBuilder<BIAGetAlertDetails>.BuildEntity(IDataReader reader, BIAGetAlertDetails biaBIAGetAlertDetails)
        {
            biaBIAGetAlertDetails.BusinessService = Convert.IsDBNull(reader["businessservice"]) ? string.Empty : Convert.ToString(reader["businessservice"]);
            biaBIAGetAlertDetails.Alertype = Convert.IsDBNull(reader["type"]) ? string.Empty : Convert.ToString(reader["type"]);
            biaBIAGetAlertDetails.CompanyName = Convert.IsDBNull(reader["name"]) ? string.Empty : Convert.ToString(reader["name"]);
            biaBIAGetAlertDetails.Alertid = Convert.IsDBNull(reader["alertid"]) ? 0 : Convert.ToInt32(reader["alertid"]);
            biaBIAGetAlertDetails.Companyid = Convert.IsDBNull(reader["Companyid"]) ? 0 : Convert.ToInt32(reader["Companyid"]);
            biaBIAGetAlertDetails.BusinessserviceId = Convert.IsDBNull(reader["businessserviceid"]) ? 0 : Convert.ToInt32(reader["AlertCount"]);
            biaBIAGetAlertDetails.AlertCount = Convert.IsDBNull(reader["AlertCount"]) ? 0 : Convert.ToInt32(reader["businessserviceid"]);
            biaBIAGetAlertDetails.TotalCount = Convert.IsDBNull(reader["Total"]) ? 0 : Convert.ToInt32(reader["Total"]);
            biaBIAGetAlertDetails.Percentage = Convert.IsDBNull(reader["Percentage"]) ? 0 : Convert.ToDouble(reader["Percentage"]);
            biaBIAGetAlertDetails.businessfunctionid = Convert.IsDBNull(reader["businessfunctionid"]) ? 0 : Convert.ToInt32(reader["businessfunctionid"]);
            biaBIAGetAlertDetails.businessfunction = Convert.IsDBNull(reader["businessfunction"]) ? string.Empty : Convert.ToString(reader["businessfunction"]);
            biaBIAGetAlertDetails.Category = Convert.IsDBNull(reader["alertcategoryid"]) ? AlertCategory.Group : (AlertCategory)Enum.Parse(typeof(AlertCategory), Convert.ToString(reader["alertcategoryid"]));
            biaBIAGetAlertDetails.Component = Convert.IsDBNull(reader["Component"]) ? HeatmapType.Os : (HeatmapType)Enum.Parse(typeof(HeatmapType), Convert.ToString(reader["Component"]));
            biaBIAGetAlertDetails.Total = Convert.IsDBNull(reader["Total"]) ? 0 : Convert.ToInt32(reader["Total"]);
            biaBIAGetAlertDetails.TotalPercentage = Convert.IsDBNull(reader["TotalPercentage"]) ? 0 : Convert.ToDouble(reader["TotalPercentage"]);
            biaBIAGetAlertDetails.Alertcountt = Convert.IsDBNull(reader["Alertcountt"]) ? 0 : Convert.ToInt32(reader["Alertcountt"]);

            return biaBIAGetAlertDetails;
        }
    }

}
