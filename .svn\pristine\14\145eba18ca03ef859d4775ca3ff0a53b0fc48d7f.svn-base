﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess.OTPConfig
{
    internal sealed class OTPConfigBuilder : IEntityBuilder<OTPConfiguration>
    {
        IList<OTPConfiguration> IEntityBuilder<OTPConfiguration>.BuildEntities(IDataReader reader)
        {
            var oTPConfigurations = new List<OTPConfiguration>();

            while (reader.Read())
            {
                oTPConfigurations.Add(((IEntityBuilder<OTPConfiguration>)this).BuildEntity(reader, new OTPConfiguration()));
            }

            return (oTPConfigurations.Count > 0) ? oTPConfigurations : null;
        }

        OTPConfiguration IEntityBuilder<OTPConfiguration>.BuildEntity(IDataReader reader, OTPConfiguration oTPConfiguration)
        {
            oTPConfiguration.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            oTPConfiguration.UserId = Convert.IsDBNull(reader["UserId"]) ? 0 : Convert.ToInt32(reader["UserId"]);
            oTPConfiguration.URL = Convert.IsDBNull(reader["URL"]) ? string.Empty : Convert.ToString(reader["URL"]);

            oTPConfiguration.MessageType = Convert.IsDBNull(reader["MessageType"]) ? string.Empty : Convert.ToString(reader["MessageType"]);
            oTPConfiguration.ProcCode = Convert.IsDBNull(reader["ProcCode"]) ? string.Empty : Convert.ToString(reader["ProcCode"]);
            oTPConfiguration.VerifyMessageType = Convert.IsDBNull(reader["VerifyMessageType"]) ? string.Empty : Convert.ToString(reader["VerifyMessageType"]);
            oTPConfiguration.VerifyProcCode = Convert.IsDBNull(reader["VerifyProcCode"]) ? string.Empty : Convert.ToString(reader["VerifyProcCode"]);

            oTPConfiguration.Channel = Convert.IsDBNull(reader["Channel"]) ? string.Empty : Convert.ToString(reader["Channel"]);
            oTPConfiguration.ApplicationName = Convert.IsDBNull(reader["ApplicationName"]) ? string.Empty : Convert.ToString(reader["ApplicationName"]);
            oTPConfiguration.TransactionCode = Convert.IsDBNull(reader["TransactionCode"]) ? string.Empty : Convert.ToString(reader["TransactionCode"]);
            oTPConfiguration.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            oTPConfiguration.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"].ToString());
            oTPConfiguration.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"].ToString());
            return oTPConfiguration;
        }
    }
}