﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
namespace CP.UI.Code.Base
{
    public abstract class ImpactMasterBasePage : BasePage
    {
        private int _impactmasterId = 0;
        private ImpactMaster _impactmaster = null;

        #region Properties

        protected int CurrentimpactmasterId
        {
            get
            {
                if (_impactmasterId == 0)
                {
                    if (Helper.Url.SecureUrl[Constants.UrlConstants.Params.ImpactMasterId].IsNotNullOrEmpty())
                    {
                        _impactmasterId = Helper.Url.SecureUrl[Constants.UrlConstants.Params.ImpactMasterId].ToInteger();
                    }
                }
                return _impactmasterId;
            }
            set
            {
                _impactmasterId = value;
            }
        }

        protected ImpactMaster CurrentImpactMaster
        {
            get
            {
                if (_impactmaster == null)
                {
                    if (CurrentimpactmasterId > 0)
                    {
                        _impactmaster = Facade.GetImpactMasterById(CurrentimpactmasterId);
                    }
                    if (_impactmaster == null)
                    {
                        _impactmaster = new ImpactMaster();
                    }
                }
                return _impactmaster;
            }

            set
            {
                _impactmaster = value;
            }
        }

        #endregion Properties
    }
}