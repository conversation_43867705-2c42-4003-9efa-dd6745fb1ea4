﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class SqlNativeServicesDataAccess : BaseDataAccess, ISqlNativeServicesDataAccess
    {
        #region Constructors

        public SqlNativeServicesDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SqlnativeServices> CreateEntityBuilder<SqlnativeServices>()
        {
            return (new SqlNativeServicesBuilder()) as IEntityBuilder<SqlnativeServices>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Get <see cref="ParallelDROperation" />from bcms_parallel_droperation table by id.
        /// </summary>
        /// <param name="groupId">Pass id</param>
        /// <returns>SqlNativeServices</returns>
        /// <author><PERSON><PERSON><PERSON></author>
        IList<SqlNativeServices> ISqlNativeServicesDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "SQLNATIVESERVICES_GETBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SqlNativeServices>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISqlNativeServicesDataAccess.GetByInfraObjectId(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion Methods
    }
}