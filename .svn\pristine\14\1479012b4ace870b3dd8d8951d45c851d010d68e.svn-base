﻿using System;
using System.Data;
using System.Data.Common;
using System.Collections.Generic;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class BIAWorkflowFailureWorkflowTypeDataAccess : BaseDataAccess, IBIAWorkflowFailureWorkflowTypeTrendDataAccess
    {
        #region Constructors

        public BIAWorkflowFailureWorkflowTypeDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<BIAWorkflowFailureWorkflowType> CreateEntityBuilder<BIAWorkflowFailureWorkflowType>()
        {
            return (new BIAWorkflowFailureWorkflowTypeBuilder()) as IEntityBuilder<BIAWorkflowFailureWorkflowType>;
        }

        #endregion

        #region Methods

        IList<BIAWorkflowFailureWorkflowType> IBIAWorkflowFailureWorkflowTypeTrendDataAccess.GetBIAWorkflowFailureWorkflowTypeTrend()
        {
            try
            {
                const string sp = "BIAWORKFLOWTREND_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BIAWorkflowFailureWorkflowType>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBIAWorkflowFailureWorkflowTypeTrendDataAccess.GetBIAWorkflowFailureWorkflowTypeTrend()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion
    }
}
