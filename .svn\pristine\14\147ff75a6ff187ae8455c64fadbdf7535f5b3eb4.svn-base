﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="BusinessProcessAuto.aspx.cs" Inherits="CP.UI.Admin.BusinessProcessAuto"
    Title="Continuity Patrol :: Admin-Business Process Automation" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
     <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
      <style>
        .chosen-select + .chosen-container {
            width: 48% !important;
            opacity: 1 !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />

    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server">
            <ContentTemplate>
                <ul id="ulMessage" runat="server" visible="false">
                    <li>
                        <asp:Label ID="lblMessage" runat="server" Text="ddddd"></asp:Label></li>
                    <li class="close-bt"></li>
                </ul>

               
                <h3>
                    <img src="../Images/business-process-icon.png">
                    Business Process Automation</h3>
               
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtDescription">
                                        InfraObject Name <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList runat="server" CssClass="chosen-select col-md-6" data-style="btn-default"
                                            ID="ddlgrouplst" Width="155" AutoPostBack="True" OnSelectedIndexChanged="DdlgrouplstSelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ErrorMessage="Select InfraObject Name"
                                            Display="Dynamic" CssClass="error" ControlToValidate="ddlgrouplst" InitialValue="0"></asp:RequiredFieldValidator>
                                        <asp:Label ID="lblGrpmsg" runat="server" Font-Size="Smaller" ForeColor="Red"></asp:Label>
                                    </div>
                                </div>
                                <hr />
                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                                    <thead>
                                        <tr>
                                            <th style="width: 25%;">Business Process
                                            </th>
                                            <th style="width: 25%;">Host Name
                                            </th>
                                            <th style="width: 17%;">Host IP Address
                                            </th>
                                            <th style="width: 17%;">Source Directory
                                            </th>
                                            <th style="width: 20%;">
                                                <div id="lbldestdir" runat="server">
                                                    Destination Directory
                                                </div>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <span class="inactive">*</span>
                                                <asp:DropDownList runat="server" CssClass="selectpicker col-md-9" data-style="btn-default"
                                                    ID="ddlBPType" AutoPostBack="True" OnSelectedIndexChanged="DdlBPTypeSelectedIndexChanged" Enabled="false">
                                                    <asp:ListItem Value="1">Compress Files</asp:ListItem>
                                                    <asp:ListItem Value="2">UnCompress Files</asp:ListItem>
                                                    <asp:ListItem Value="3">Delete Files</asp:ListItem>
                                                    <asp:ListItem Value="4">Move Files</asp:ListItem>
                                                    <asp:ListItem Value="5">Copy Files</asp:ListItem>
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="ddlBPType" CssClass="error"
                                                    ErrorMessage="Select BPType" InitialValue="0"></asp:RequiredFieldValidator>
                                            </td>
                                            <td>
                                                <asp:DropDownList ID="ddlBPhostname" runat="server" CssClass="chosen-select col-md-9"
                                                    data-style="btn-default" OnSelectedIndexChanged="DdlBPhostnameSelectedIndexChanged"
                                                    AutoPostBack="True">
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" ErrorMessage="*"
                                                    ControlToValidate="ddlBPhostname" InitialValue="0" CssClass="error"></asp:RequiredFieldValidator>
                                                <asp:Label ID="lblBg" runat="server" Font-Size="Smaller" ForeColor="Red" CssClass="error"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:TextBox ID="txtPRIp" runat="server" CssClass="form-control" Width="90%" Enabled="False"></asp:TextBox>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" ErrorMessage="*"
                                                    ControlToValidate="txtPRIp" CssClass="error"></asp:RequiredFieldValidator>
                                                <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" Display="Dynamic" CssClass="error"
                                                    ErrorMessage="* InValid IP" ValidationExpression="^(([01]?\d\d?|2[0-4]\d|25[0-5])\.){3}([01]?\d\d?|25[0-5]|2[0-4]\d)$"
                                                    ControlToValidate="txtPRIp"></asp:RegularExpressionValidator>
                                            </td>
                                            <td>
                                                <asp:TextBox ID="txtsourcedir" runat="server" MaxLength="100" Width="90%" class="form-control"></asp:TextBox>

                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator5" CssClass="error" runat="server" ErrorMessage="*"
                                                    ControlToValidate="txtsourcedir"></asp:RequiredFieldValidator>
                                              <%--  <asp:RegularExpressionValidator ID="regexp" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtsourcedir" ErrorMessage="*"
                                                    ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>--%>
                                            </td>
                                            <td>
                                                <asp:TextBox ID="txtdestdir" MaxLength="100" runat="server" Width="90%" class="form-control"></asp:TextBox>

                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator6" CssClass="error" Display="Dynamic" runat="server"
                                                    ErrorMessage="*" ControlToValidate="txtdestdir"></asp:RequiredFieldValidator>
                                                <asp:RegularExpressionValidator ID="regeprdest" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtdestdir" ErrorMessage="*"
                                                    ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Run Scripts
                                            </td>
                                            <td>
                                                <asp:DropDownList ID="ddlscripthost" runat="server" CssClass="chosen-select col-md-9"
                                                    data-style="btn-default" AutoPostBack="True" OnTextChanged="DdlscripthostTextChanged">
                                                </asp:DropDownList>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator7" runat="server" ErrorMessage="*"
                                                    ControlToValidate="ddlscripthost" CssClass="error" InitialValue="0"></asp:RequiredFieldValidator>
                                                <asp:Label ID="lblSg" runat="server" Font-Size="Smaller" ForeColor="Red" CssClass="error"></asp:Label>
                                            </td>
                                            <td>
                                                <asp:TextBox ID="txtDRIp" runat="server" Width="90%" CssClass="form-control" Enabled="False"></asp:TextBox>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator8" CssClass="error" runat="server" ErrorMessage="*"
                                                    ControlToValidate="txtDRIp"></asp:RequiredFieldValidator>
                                             <%--   <asp:RegularExpressionValidator ID="RegularExpressionValidator2" Display="Dynamic" CssClass="error"
                                                    runat="server" ErrorMessage="* InValid IP" ValidationExpression="^(([01]?\d\d?|2[0-4]\d|25[0-5])\.){3}([01]?\d\d?|25[0-5]|2[0-4]\d)$"
                                                    ControlToValidate="txtDRIp"></asp:RegularExpressionValidator>--%>
                                            </td>
                                            <td>
                                                <asp:TextBox ID="txtpath" MaxLength="100" runat="server" Width="90%" class="form-control"></asp:TextBox>
                                                <asp:RequiredFieldValidator ID="RequiredFieldValidator9" CssClass="error" runat="server" ErrorMessage="*"
                                                    ControlToValidate="txtpath"></asp:RequiredFieldValidator>
                                              <%--  <asp:RegularExpressionValidator ID="rgepath" runat="server" CssClass="error" Display="Dynamic" ControlToValidate="txtpath" ErrorMessage="*"
                                                    ValidationExpression="^[a-zA-Z0-9 ,.'@_-]+$"></asp:RegularExpressionValidator>--%>
                                            </td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                               
                                <div class="form-actions row">
                                    <div class="col-lg-9">
                                        Active :
                                <asp:Label ID="Label1" Font-Size="Large" ForeColor="Green" Text="•" runat="server"></asp:Label>
                                        &nbsp;&nbsp;&nbsp;&nbsp; InActive :
                                <asp:Label ID="Label2" Font-Size="Large" ForeColor="Red" Text="•" runat="server"></asp:Label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;<asp:LinkButton ID="lnlACmp" CausesValidation="false" runat="server"
                                            Font-Size="Smaller" ForeColor="#339933" OnClick="LnlACmpClick"></asp:LinkButton>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:LinkButton ID="lnkAUCmp" CausesValidation="false" runat="server" Font-Size="Smaller"
                                    ForeColor="#339933" OnClick="LnkAuCmpClick"></asp:LinkButton>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:LinkButton ID="lnlADlte" CausesValidation="false" runat="server" Font-Size="Smaller"
                                    ForeColor="#339933" OnClick="LnlADlteClick"></asp:LinkButton>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:LinkButton ID="lnkAMve" CausesValidation="false" runat="server" Font-Size="Smaller"
                                    ForeColor="#339933" OnClick="LnkAMveClick"></asp:LinkButton>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:LinkButton ID="lnkACpy" CausesValidation="false" runat="server" Font-Size="Smaller"
                                    ForeColor="#339933" OnClick="LnkACpyClick"></asp:LinkButton>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:Label ID="lblInactcompfle" ForeColor="Red" runat="server" Font-Size="Smaller"></asp:Label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:Label ID="lblinactUncmpfle" ForeColor="Red" runat="server" Font-Size="Smaller"></asp:Label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:Label ID="lblInactDeltefle" ForeColor="Red" runat="server" Font-Size="Smaller"></asp:Label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:Label ID="lblInactMovefle" ForeColor="Red" runat="server" Font-Size="Smaller"></asp:Label>
                                        &nbsp;&nbsp;&nbsp;&nbsp;
                                <asp:Label ID="lblInactCopyfle" ForeColor="Red" runat="server" Font-Size="Smaller"></asp:Label>
                                    </div>
                                    <div class="col-md-3 text-right">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span> &nbsp;
                                    </div>
                                </div>
                                <hr class="separator" />
                                <div class="form-actions row">
                                    <div class="col-lg-6">
                                        <span class="bold">Total Process -</span>
                                        <asp:Label ID="lblCompfle" runat="server" Text="Compress Files" Font-Size="X-Small"></asp:Label>
                                        &nbsp;
                                <asp:Label ID="lblUncompfle" runat="server" Text="UnCompress Files" Font-Size="X-Small"></asp:Label>
                                        &nbsp;
                                <asp:Label ID="lblDelfle" runat="server" Text="Delete Files" Font-Size="X-Small"></asp:Label>
                                        &nbsp;
                                <asp:Label ID="lblMvefle" runat="server" Text="Move Files" Font-Size="X-Small"></asp:Label>
                                        &nbsp;
                                <asp:Label ID="lblCopyfle" runat="server" Text="Copy Files" Font-Size="X-Small"></asp:Label>
                                        <asp:HiddenField ID="hiddenid" runat="server" />
                                    </div>
                                    <div class="col-md-6 text-right">
                                         <asp:Button ID="btnDelete" Visible="false" CssClass="btn btn-primary" Width="20%" runat="server" Text="Delete"
                                            OnClick="BtnDeleteClick" />
                                        <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save"
                                            OnClick="BtnSaveClick" />
                                        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server"
                                            Text="Cancel" CausesValidation="False" OnClick="BtnCancelClick" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>