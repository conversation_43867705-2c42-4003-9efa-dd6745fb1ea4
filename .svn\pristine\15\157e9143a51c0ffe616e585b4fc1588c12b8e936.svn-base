﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "SqlnativeHealth", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class SqlNativeHealth : BaseEntity
    {
        #region Properties

        [DataMember]
        public int GroupId { get; set; }

        [DataMember]
        public string MSSQLDatabaseStatePR { get; set; }

        [DataMember]
        public string MSSQLDatabaseStateDR { get; set; }

        [DataMember]
        public string MSSQLDatabaseRecoveryModelPR { get; set; }

        [DataMember]
        public string MSSQLDatabaseRecoveryModelDR { get; set; }

        [DataMember]
        public string TransactionLogShippingStatusPR { get; set; }

        [DataMember]
        public string TransactionLogShippingStatusDR { get; set; }

        [DataMember]
        public string DatabaseRestrictAccessStatusPR { get; set; }

        [DataMember]
        public string DatabaseRestrictAccessStatusDR { get; set; }

        [DataMember]
        public string BackupJobStatus { get; set; }

        [DataMember]
        public string CopyJobStatus { get; set; }

        [DataMember]
        public string RestoreJobStatus { get; set; }

        [DataMember]
        public string BackupJobExecutionStatus { get; set; }

        [DataMember]
        public string CopyJobExecutionStatus { get; set; }

        [DataMember]
        public string RestoreJobExecutionStatus { get; set; }

        #endregion Properties
    }
}