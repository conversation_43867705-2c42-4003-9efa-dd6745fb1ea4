﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class HP3PARmonitorDetails : BaseControl //System.Web.UI.UserControl
    {
        InfraObject CurrentInfraObject;
        private ComponentInfo CurrentComponentInfo { [DebuggerStepThrough] get; set; }
        // InfraObjectsBasePage infra = new InfraObjectsBasePage();
        protected void Page_Load(object sender, EventArgs e)
        {

        }
        public override void PrepareView()
        {
            //int currentInfraId = Convert.ToInt32(Session["InfraObjectId"]);
            int currentInfraId = Convert.ToInt32(Session["Hp3parDBinfraid"]);
            
            if (currentInfraId > 0)
            {
                CurrentInfraObject = Facade.GetInfraObjectById(currentInfraId);
                GetHP3PARDeatils(currentInfraId);
            }
            //CurrentInfraObject = Facade.GetInfraObjectById(currentInfraId);
            //GetHP3PARDeatils(currentInfraId);
        }
        private void GetHP3PARDeatils(int infraObjectId)
        {
            divMongoDB.Visible = true;
            divHP3PARVirtualMonitoring.Visible = true;
            var infraobjects = Facade.GetInfraObjectById(infraObjectId);

            var MongoDBRepli = Facade.GetHP3PARMonitorByInfraObjectId(infraObjectId);

            var Repliserver = Facade.GetHP3PARStoragebyReplicationId(CurrentInfraObject.PRReplicationId);
            var serverPR = Facade.GetServerById(Repliserver.PrServerId);
            var serverDR = Facade.GetServerById(Repliserver.DrServerId);

            try
            {
                if (CurrentInfraObject != null)
                {
                    var prserver = Facade.GetServerById(CurrentInfraObject.PRServerId);
                    var drserver = Facade.GetServerById(CurrentInfraObject.DRServerId);
                    if (prserver != null)
                    {

                        if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                        {

                            lblHP3PARPRIP.Text = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                            if (drserver.Status == ServerStatus.Up)
                                prspan.CssClass = "health-up";
                            else if (drserver.Status == ServerStatus.Down)
                            {
                                prspan.CssClass = "health-down";
                            }
                            else
                                prspan.CssClass = "fatal";


                            if (serverPR != null)
                            {

                                lblHP3PARDRIP.Text = CryptographyHelper.Md5Decrypt(serverPR.IPAddress);
                                if (serverPR.Status == ServerStatus.Up)
                                    drspan.CssClass = "health-up";
                                else if (serverPR.Status == ServerStatus.Down)
                                {
                                    drspan.CssClass = "health-down";
                                }
                                else
                                    drspan.CssClass = "fatal";

                            }

                        }
                        else
                        {

                            lblHP3PARPRIP.Text = CryptographyHelper.Md5Decrypt(serverPR.IPAddress);
                            if (serverPR.Status == ServerStatus.Up)
                                prspan.CssClass = "health-up";
                            else if (serverPR.Status == ServerStatus.Down)
                            {
                                prspan.CssClass = "health-down";
                            }
                            else
                                prspan.CssClass = "fatal";


                            lblHP3PARDRIP.Text = CryptographyHelper.Md5Decrypt(serverDR.IPAddress);
                            if (serverDR.Status == ServerStatus.Up)
                                drspan.CssClass = "health-up";
                            else if (serverDR.Status == ServerStatus.Down)
                            {
                                drspan.CssClass = "health-down";
                            }
                            else
                                drspan.CssClass = "fatal";


                        }

                    }


                    var prdataabase = Facade.GetDatabaseSqlByDatabaseBaseId(CurrentInfraObject.PRDatabaseId);
                    if (prdataabase != null)
                    {
                        //CurrentComponentInfo.PRDatabaseName = prdataabase.DatabaseSID;
                    }
                    var drdataabase = Facade.GetDatabaseSqlByDatabaseBaseId(CurrentInfraObject.DRDatabaseId);
                    if (drdataabase != null)
                    {
                        //CurrentComponentInfo.DRDatabaseName = drdataabase.DatabaseSID;
                    }
                }


            }
            catch (Exception)
            {

            }

            if (MongoDBRepli != null)
            {

                lblHP3PARPRIP.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.PRStorageIPAddress)) ? "NA" : MongoDBRepli.PRStorageIPAddress;
                lblHP3PARDRIP.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.DRStorageIPAddress)) ? "NA" : MongoDBRepli.DRStorageIPAddress;

                lblPR3PARStorageName.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.PRStorageName)) ? "NA" : MongoDBRepli.PRStorageName;
                lblDR3PARStorageName.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.DRStorageName)) ? "NA" : MongoDBRepli.DRStorageName;
                lblPRRemoteCopyGrpName.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.PRGroupName)) ? "NA" : MongoDBRepli.PRGroupName;
                lblDRRemoteCopyGrpName.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.DRGroupName)) ? "NA" : MongoDBRepli.DRGroupName;
                lblPR3PARRole.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.PRRole)) ? "NA" : MongoDBRepli.PRRole;
                lblDR3PARRole.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.DRRole)) ? "NA" : MongoDBRepli.DRRole;

                lblPR3PARRepliMode.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.PRReplicationMode)) ? "NA" : MongoDBRepli.PRReplicationMode;
                lblDR3PARRepliMode.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.DRReplicationMode)) ? "NA" : MongoDBRepli.DRReplicationMode;

                lblPR3PARState.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.PRState)) ? "NA" : MongoDBRepli.PRState;
                lblDR3PARState.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.DRState)) ? "NA" : MongoDBRepli.DRState;

                lblPR3PARGrpState.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.PRGroupState)) ? "NA" : MongoDBRepli.PRGroupState;
                lblDR3PARGrpState.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.DRGroupState)) ? "NA" : MongoDBRepli.DRGroupState;

                lblPR3PARSyncTime.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.PRLastSyncTime)) ? "NA" : MongoDBRepli.PRLastSyncTime;

                if (MongoDBRepli.DRLastSyncTime.Contains("0001"))
                {
                    lblDR3PARSyncTime.Text = "NA";
                }
                else
                {
                    //lblDR3PARSyncTime.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRLastSyncTime)) ? "NA" : MSSqlRepli.DRLastSyncTime;
                    lblDR3PARSyncTime.Text = MongoDBRepli.DRLastSyncTime;
                }
                lblHP3PARDatalag.Text = string.IsNullOrEmpty(Convert.ToString(MongoDBRepli.DataLag)) ? "NA" : MongoDBRepli.DataLag;

            }
            else
            {
                lblHP3PARPRIP.Text = "NA";
                lblHP3PARDRIP.Text = "NA";

                lblPR3PARStorageName.Text = "NA";
                lblDR3PARStorageName.Text = "NA";
                lblPRRemoteCopyGrpName.Text = "NA";
                lblDRRemoteCopyGrpName.Text = "NA";
                lblPR3PARRole.Text = "NA";
                lblDR3PARRole.Text = "NA";

                lblPR3PARRepliMode.Text = "NA";
                lblDR3PARRepliMode.Text = "NA";

                lblPR3PARState.Text = "NA";
                lblDR3PARState.Text = "NA";

                lblPR3PARGrpState.Text = "NA";
                lblDR3PARGrpState.Text = "NA";

                lblPR3PARSyncTime.Text = "NA";
                lblDR3PARSyncTime.Text = "NA";

                lblHP3PARDatalag.Text = "NA";
            }
            var Virtualdetails = Facade.GetVirtualVolumemonitorbyInfraobjectId(infraObjectId);
            if (Virtualdetails != null)
            {
                lblvvname.Text = Virtualdetails.LocalVVName;
                lblremotevvname.Text = Virtualdetails.RemoteVVName;
                lblsyncstate.Text = Virtualdetails.SyncState;
                lbllstsynstate.Text = Virtualdetails.LastSyncTime;
            }
            else
            {
                lblvvname.Text = "NA";
                lblremotevvname.Text = "NA";
                lblsyncstate.Text = "NA";
                lbllstsynstate.Text = "NA";
            }
        }
    }
}



