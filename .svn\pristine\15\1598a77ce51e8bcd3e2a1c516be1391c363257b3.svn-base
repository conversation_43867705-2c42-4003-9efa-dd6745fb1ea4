﻿using System;
using System.Diagnostics;
using System.Runtime.Remoting.Messaging;
using System.Transactions;
using System.Web;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;

namespace CP.UI
{
    //public abstract class BaseControl : System.Web.UI.UserControl, IBase
    public abstract class BaseControl : System.Web.UI.UserControl, IBase
    {
        #region Member Variables

        private WebHelper _helper;
        private IFacade _facade;
        private User _loggedInUser;
        private UserRole _loggedInUserRole = UserRole.Undefined;
        private TransactionScope _currentTransaction;
        private CompanyProfile _companyProfile;

        #endregion Member Variables

        #region Properties

        public User LoggedInUser
        {
            [DebuggerStepThrough]
            get
            {
                if (_loggedInUser == null)
                {
                    if (WebHelper.CurrentSession.UserInfo.User == null)
                    {
                        _loggedInUser = _facade.GetUserByLoginName(LoggedInUserName);
                        WebHelper.CurrentSession.UserInfo.User = _loggedInUser;
                    }
                    else
                    {
                        _loggedInUser = (User)WebHelper.CurrentSession.UserInfo.User;
                    }
                }
                return _loggedInUser;
            }
            set
            {
                WebHelper.CurrentSession.UserInfo.User = value;
            }
        }

        public string LoggedInUserName
        {
            [DebuggerStepThrough]
            get
            {
                return IsUserAuthenticated ? Page.User.Identity.Name : "Anonymous";
            }
        }

        public UserRole LoggedInUserRole
        {
            [DebuggerStepThrough]
            get
            {
                if (_loggedInUserRole == UserRole.Undefined)
                {
                    if (WebHelper.CurrentSession.UserInfo.Role != null)
                    {
                        _loggedInUserRole = (UserRole)WebHelper.CurrentSession.UserInfo.Role;
                    }
                }
                return _loggedInUserRole;
            }
            set
            {
                WebHelper.CurrentSession.UserInfo.Role = value;
            }
        }

        public CompanyProfile LoggedInUserCompany
        {
            [DebuggerStepThrough]
            get
            {
                if (LoggedInUserCompanyId > 0)
                {
                    if (_companyProfile == null)
                    {
                        _companyProfile = Facade.GetCompanyProfileById(LoggedInUserCompanyId);
                    }
                }

                return _companyProfile;
            }
        }

        public bool IsUserSuperMonitor
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.SuperMonitor;
            }
        }

        public int LoggedInUserId
        {
            [DebuggerStepThrough]
            get
            {
                return !IsUserAuthenticated ? 0 : LoggedInUser.Id;
            }
        }

        public string LoggedInUserPassword
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUser != null ? LoggedInUser.LoginPassword : string.Empty;
            }
        }

        public int LoggedInUserCompanyId
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUser != null ? LoggedInUser.CompanyId : 0;
            }
        }

        public bool IsUserAuthenticated
        {
            get
            {
                return Page.User.Identity.IsAuthenticated;
            }
        }

        public bool IsUserAdmin
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.Administrator;
            }
        }

        public bool IsUserSuperAdmin
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.EnterpriseUser;
            }
        }

        public bool IsUserManager
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.Manager;
            }
        }

        public bool IsUserOperator
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.Operator;
            }
        }

        public bool IsUserCustom
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.Custom;
            }
        }

        public bool IsUserExecutionUser
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.ExecutionAccessUser;
            }
        }

        public bool IsUserEnterpriseUser
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.EnterpriseUser;
            }
        }

        public bool IsParentCompnay
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserCompany.IsParent;
            }
        }



        public bool IsUserDPCLAM
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.DPCLAMUser;
            }
        }

        public IFacade Facade
        {
            get
            {
                if (_facade == null)
                {
                    _facade = Context.Items[Constants.ContextConstants.Facade] as IFacade;
                    if (_facade == null)
                    {
                        _facade = new Facade();
                        Context.Items[Constants.ContextConstants.Facade] = _facade;
                    }
                }
                return _facade;
            }
        }

        public WebHelper Helper
        {
            get { return _helper ?? (_helper = new WebHelper(Page, ViewState)); }
        }

        public string Message
        {
            [DebuggerStepThrough]
            get
            {
                return Helper.Url.SecureUrl["msg"];
            }
        }

        public string HostAddress
        {
            get
            {
                return HttpContext.Current.Request.UserHostAddress;
            }
        }

        #endregion Properties

        public abstract void PrepareView();

        public void StartTransaction()
        {
            _currentTransaction = new TransactionScope();
        }

        public void EndTransaction()
        {
            if (_currentTransaction != null)
            {
                _currentTransaction.Complete();
                _currentTransaction.Dispose();
            }
        }

        public void DisposeTransaction()
        {
            if (_currentTransaction != null)
            {
                _currentTransaction.Dispose();
            }
        }

        public void InvalidateTransaction()
        {
            if (_currentTransaction != null)
            {
                _currentTransaction.Dispose();
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            try
            {
                base.OnLoad(e);
                if (_helper == null)
                {
                    _helper = new WebHelper(Page, ViewState);
                }
                if (!IsPostBack)
                {
                    PrepareView();
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while loading the page", ex, Page);
                    ExceptionManager.Manage(customEx, Page);
                }
            }
        }
    }
}