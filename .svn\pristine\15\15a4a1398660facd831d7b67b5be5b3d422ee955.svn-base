﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using CP.UI.Code.Replication.Component;
using CP.UI.Code.Replication.DataLag;
using CP.UI.Code.Replication.ReplicationInfo;

namespace CP.UI.Code.Replication.Clients
{
    public class MSSQLAlwaysOnServerClient : Replication
    {
        public MSSQLAlwaysOnServerClient()
        {
            Datalag = new Sql2000DataLag();
            ComponentInfo = new MSSQLAlwaysOnComponent();
            ReplicationInfo = new MSSQLAlwaysOnReplicationInfor();
        }
    }
}