﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class BPAutomationBuilder : IEntityBuilder<BPAutomation>
    {
        IList<BPAutomation> IEntityBuilder<BPAutomation>.BuildEntities(IDataReader reader)
        {
            var bpautomations = new List<BPAutomation>();

            while (reader.Read())
            {
                bpautomations.Add(((IEntityBuilder<BPAutomation>)this).BuildEntity(reader, new BPAutomation()));
            }

            return (bpautomations.Count > 0) ? bpautomations : null;
        }

        BPAutomation IEntityBuilder<BPAutomation>.BuildEntity(IDataReader reader, BPAutomation bpautomation)
        {
            //const int FLD_ID = 0;
            //const int FLD_GROUPNAME = 1;
            //const int FLD_BPCOMPONENT = 2;
            //const int FLD_PRHOSTNAME =3;
            //const int FLD_SOURCEDIR =4;
            //const int FLD_DESTINATIONDIR =5;
            //const int FLD_DRHOSTNAME = 6;
            //const int FLD_SCRIPTPATH = 7;
            //const int FLD_PRIP = 8;
            //const int FLD_DRIP = 9;
            //const int FLD_ISACTIVE =10;
            //const int FLD_CREATORID = 11;
            //const int FLD_CREATEDATE = 12;
            //const int FLD_UPDATORID = 13;
            //const int FLD_UPDATEDATE = 14;

            //bpautomation.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //bpautomation.GroupName = reader.IsDBNull(FLD_GROUPNAME) ? string.Empty : reader.GetString(FLD_GROUPNAME);
            //bpautomation.BPComponent = reader.IsDBNull(FLD_BPCOMPONENT) ? string.Empty : reader.GetString(FLD_BPCOMPONENT);
            //bpautomation.PRHostId = reader.IsDBNull(FLD_PRHOSTNAME) ? 0 : reader.GetInt32(FLD_PRHOSTNAME);
            //bpautomation.SourceDir = reader.IsDBNull(FLD_SOURCEDIR) ? string.Empty : reader.GetString(FLD_SOURCEDIR);
            //bpautomation.DestinationDir = reader.IsDBNull(FLD_DESTINATIONDIR) ? string.Empty : reader.GetString(FLD_DESTINATIONDIR);
            //bpautomation.DRHostId = reader.IsDBNull(FLD_DRHOSTNAME) ? 0 : reader.GetInt32(FLD_DRHOSTNAME);
            //bpautomation.ScriptPath = reader.IsDBNull(FLD_SCRIPTPATH) ? string.Empty : reader.GetString(FLD_SCRIPTPATH);
            //bpautomation.PRIPAddress = reader.IsDBNull(FLD_PRIP) ? string.Empty : reader.GetString(FLD_PRIP);
            //bpautomation.DRIPAddress = reader.IsDBNull(FLD_DRIP) ? string.Empty : reader.GetString(FLD_DRIP);
            //bpautomation.IsActive = !reader.IsDBNull(FLD_ISACTIVE) && reader.GetBoolean(FLD_ISACTIVE);
            //bpautomation.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //bpautomation.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);
            //bpautomation.UpdatorId = reader.IsDBNull(FLD_UPDATORID) ? 0 : reader.GetInt32(FLD_UPDATORID);
            //bpautomation.UpdateDate = reader.IsDBNull(FLD_UPDATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_UPDATEDATE);

            //Fields in bcms_bpautomation table on 16/07/2013 :Id, GroupName, BPComponent, PRHostId, SourceDir, DestinationDir, DRHostId, ScriptPath, PRIPAddress, DRIPAddress, IsActive, CreatorId, CreateDate, UpdatorId, UpdateDate

            bpautomation.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            bpautomation.InfraObjectName = Convert.IsDBNull(reader["InfraObjectName"])
                ? string.Empty
                : Convert.ToString(reader["InfraObjectName"]);
            bpautomation.BPComponent = Convert.IsDBNull(reader["BPComponent"])
                ? string.Empty
                : Convert.ToString(reader["BPComponent"]);
            bpautomation.PRHostId = Convert.IsDBNull(reader["PRHostId"]) ? 0 : Convert.ToInt32(reader["PRHostId"]);
            bpautomation.SourceDir = Convert.IsDBNull(reader["SourceDir"])
                ? string.Empty
                : Convert.ToString(reader["SourceDir"]);
            bpautomation.DestinationDir = Convert.IsDBNull(reader["DestinationDir"])
                ? string.Empty
                : Convert.ToString(reader["DestinationDir"]);
            bpautomation.DRHostId = Convert.IsDBNull(reader["DRHostId"]) ? 0 : Convert.ToInt32(reader["DRHostId"]);
            bpautomation.ScriptPath = Convert.IsDBNull(reader["ScriptPath"])
                ? string.Empty
                : Convert.ToString(reader["ScriptPath"]);
            bpautomation.PRIPAddress = Convert.IsDBNull(reader["PRIPAddress"])
                ? string.Empty
                : Convert.ToString(reader["PRIPAddress"]);
            bpautomation.DRIPAddress = Convert.IsDBNull(reader["DRIPAddress"])
                ? string.Empty
                : Convert.ToString(reader["DRIPAddress"]);

            bpautomation.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            bpautomation.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            bpautomation.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            bpautomation.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            bpautomation.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());

            return bpautomation;
        }
    }
}