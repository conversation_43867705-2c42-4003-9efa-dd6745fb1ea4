﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Web;

namespace CP.UI
{
    public partial class SiteList : SiteBasePage
    {
        #region variable

        public static string CurrentURL = Constants.UrlConstants.Urls.Site.SiteConfiguration;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Site.SiteList;
                }
                return string.Empty;
            }
        }

        #endregion variable

        public override void PrepareView()
        {
            if (IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            //if (IsUserManager)
            //{
            //    Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);
            //}

            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager3.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }

            Utility.SelectMenu(Master, "Module3");
            BindList();

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            hdGuid.Value = Guid.NewGuid().ToString();

            //if (IsUserOperator || IsUserManager)
            //{
            //    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
            //    return;
            //}
        }

        private void BindList()
        {
            lvSite.DataSource = GetSiteList();
            lvSite.DataBind();
            //if deleting or updating the Site the page will get postback and then Listview is display the same page where it is deleted or updated. - Goraknath Khule.
            if (Session["CurrentPageSite"] != null)
            {
                if (Convert.ToInt32(Session["CurrentPageSite"]) != -1)
                {
                    dataPager3.SetPageProperties(Convert.ToInt32(Session["CurrentPageSite"]), dataPager3.MaximumRows, true);
                    Session["CurrentPageSite"] = -1;
                }
            }
        }

        private IList<Site> GetSiteList()
        {
            if (IsSuperAdmin)
            {
                return Facade.GetSitesByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
            }
            else
            {
                return Facade.GetSiteByUserId(LoggedInUserId);
            }
        }

        protected string CheckWithServer(object name, object id)
        {
            IList<Server> sver = Facade.GetAllServers();
            if (sver != null)
            {
                var siteByid = from sv in sver where sv.SiteId == Convert.ToInt32(id) select sv;
                int cont = siteByid.Count();

                if (cont >= 1)
                    return "Site " + name + " attaching with servers Are you sure want to delete ?";
                return "Are you sure want to delete " + name + " ?";
            }
            return string.Empty;
        }

        protected void LvSiteItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageSite"] = (dataPager3.StartRowIndex);
                var lbl = (lvSite.Items[e.ItemIndex].FindControl("Id")) as Label;
                var lblSiteName = (lvSite.Items[e.ItemIndex].FindControl("SITE_NAME")) as Label;
                if (lbl != null && lblSiteName != null && ValidateRequest("SiteProfileListDelete", UserActionType.DeleteSite))
                {
                    var site = Facade.GetServersBySiteId(Convert.ToInt32(lbl.Text));
                    var siteIdInReplication = Facade.GetReplicationBasesBySiteId(Convert.ToInt32(lbl.Text));
                    if (site != null || siteIdInReplication != null)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("Site is in use");
                    }
                    else
                    {
                        Facade.DeleteSiteById(Convert.ToInt32(lbl.Text));
                        //ActivityLogger.AddLog(LoggedInUserName, "Site", UserActionType.DeleteSite, "The Site '" + lblSiteName.Text + "' was deleted", LoggedInUserId);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Site", UserActionType.DeleteSite, "The Site '" + lblSiteName.Text + "' was deleted", LoggedInUserId);
                       
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Site" + " " + '"' + lblSiteName.Text + '"', TransactionType.Delete));
                    }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void LvSiteItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentURL);
            Session["CurrentPageSite"] = (dataPager3.StartRowIndex);
            var lblId = (lvSite.Items[e.NewEditIndex].FindControl("ID")) as Label;

            var lblSiteName = (lvSite.Items[e.NewEditIndex].FindControl("SITE_NAME")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "SiteProfile", UserActionType.UpdateSite, "The SiteProfile '" + lblSiteName.Text + "' Opened as Editing Mode.", LoggedInUserId);


            if (lblId != null  && ValidateRequest("SiteProfileListEdit", UserActionType.CreateSite))
            {
                 secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.SiteId, lblId.Text);
                Helper.Url.Redirect(secureUrl);

                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.SiteId,
                //                                     lblId.Text);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void LvSitePreRender(object sender, EventArgs e)
        {
            if (String.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvSite.DataSource = GetSiteList();
                lvSite.DataBind();
            }
            else
            {
                lvSite.DataSource = GetSiteListBySearch(txtsearchvalue.Text);
                lvSite.DataBind();
            }
        }

        protected void LvSiteItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ImgEdit") as ImageButton;
            var delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (!IsSuperAdmin && !IsUserAdmin)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
            if (IsUserOperator || IsUserManager || IsUserExecutionUser)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
           
            
        }

        protected void BtnSearchClick(object sender, EventArgs e)
        {
            lvSite.DataSource = GetSiteListBySearch(txtsearchvalue.Text);
            lvSite.DataBind();
        }

        private IList<Site> GetSiteListBySearch(string value)
        {
            var sitelist = GetSiteList();
            value = value.Trim();
            if (!String.IsNullOrEmpty(value) || sitelist != null)
            {
                var result = (from sites in sitelist
                              where value != null && sites.Name.ToLower().Contains(value.ToLower())
                              select sites).ToList();

                return result;
            }
            return null;
        }

        protected void LvSitePagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

    }
}