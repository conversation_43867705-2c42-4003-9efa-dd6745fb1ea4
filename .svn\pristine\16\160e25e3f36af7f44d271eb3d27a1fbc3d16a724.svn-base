﻿using System.Linq;
using System.Web.Services;
using System.Web.UI.WebControls;
using CP.BusinessFacade;

namespace CP.UI
{
    public partial class MonitorNetwork : BasePage
    {
        private static IFacade _facade = new Facade();

        public override void PrepareView()
        {
            var hd = Master.FindControl("hdSelectedMenu") as HiddenField;
            hd.Value = "Module8";
        }

        [WebMethod]
        public static string GetNetworkIPCircle()
        {
            var allCircle = _facade.GetNetworkIPCircles();
            var returnValue = string.Empty;
            if (allCircle != null)
            {
                returnValue = allCircle.Aggregate(returnValue, (current, networkIp) => string.Format("{0}{1},", current, networkIp.Circle));

                var companyProfile = _facade.GetAllCompanyProfiles();
                if (companyProfile != null)
                    return returnValue.TrimEnd(',') + "/" + companyProfile[0].Name;
                return string.Empty;
            }
            return returnValue;
        }

        [WebMethod]
        public static string GetNetworkIPByCircle(string circle)
        {
            /*  from f in FundUserRoles
                group f by new {f.RoleId, f.UserName}
                into myGroup
                where myGroup.Count() > 0
                select new { myGroup.Key.RoleId, myGroup.Key.UserName, FundCount = myGroup.Count()} */

            var allIP = _facade.GetNetworkIPsByCircle(circle.Trim());
            var returnValue = string.Empty;
            var ipValues = string.Empty;
            if (allIP != null)
            {
                var categories = from p in allIP group p by p.Switch into g select new { switches = g.Key };

                foreach (var category in categories)
                {
                    returnValue = returnValue + category.switches + ",";
                    ipValues = allIP.Where(networkIp => networkIp.Switch == category.switches).Aggregate(ipValues, (current, networkIp) => current + networkIp.IP + "|" + networkIp.Status + ":");
                    if (categories.Count() > 1)
                        ipValues = ipValues + "/";
                    else
                        ipValues = ipValues.TrimEnd(':') + "/";
                }

                returnValue = returnValue.TrimEnd(',') + ";" + ipValues.TrimEnd('/');
                return returnValue;
            }
            return returnValue;
        }
    }
}