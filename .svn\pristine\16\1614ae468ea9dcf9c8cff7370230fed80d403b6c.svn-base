﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseMySql", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseMySql : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public int ServerId
        {
            get;
            set;
        }

        [DataMember]
        public string DatabaseID
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }

        [DataMember]
        public int Port
        {
            get;
            set;
        }

        #endregion Properties

        #region Constructor

        public DatabaseMySql()
            : base()
        {
        }

        #endregion Constructor
    }
}