﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "EC2S3DataSyncComponentDetails", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class EC2S3DataSyncComponentDetails : BaseEntity
    {
        #region Properties

        public int InfraObjectId { get; set; }

        public string EC2InstanceId { get; set; }

        public string EC2InstanceStatus { get; set; }

        public string Ec2InstanceType { get; set; }

        public string SourceDataPath { get; set; }

        public string S3BucketTimeStamp { get; set; }

        public string S3BucketLocation { get; set; }

        #endregion Properties
    }
}
