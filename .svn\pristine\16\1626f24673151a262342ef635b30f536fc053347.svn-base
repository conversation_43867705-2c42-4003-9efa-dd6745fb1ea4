﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;
namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseSqlNative2008", Namespace = "http://www.BCMS.com/types")]
    public class DatabaseSqlNative2008 : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId
        {
            get;
            set;
        }

        [DataMember]
        public string DatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public string InstanceName
        {
            get;
            set;
        }

        [DataMember]
        public string UserName
        {
            get;
            set;
        }

        [DataMember]
        public string Password
        {
            get;
            set;
        }

        [DataMember]
        public int Port
        {
            get;
            set;
        }
        [DataMember]
        public SqlAuthenticateType AuthenticationMode { get; set; }


        [DataMember]
        public int SSOEnabled
        {
            get;
            set;
        }

        [DataMember]
        public int SSOProfileId
        {
            get;
            set;
        }


        #endregion Properties

        #region Constructor

        public DatabaseSqlNative2008()
            : base()
        {

        }

        #endregion Constructor
    }
}
