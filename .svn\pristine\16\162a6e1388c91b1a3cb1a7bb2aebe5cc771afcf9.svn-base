﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "GlobalMirrorMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class GlobalMirrorMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public string GmrResultId { get; set; }

        [DataMember]
        public string MasterCount { get; set; }

        [DataMember]
        public string MasterSessionId { get; set; }

        [DataMember]
        public string CopyState { get; set; }

        [DataMember]
        public string FatalReason { get; set; }

        [DataMember]
        public string CGIntervalTime { get; set; }

        [DataMember]
        public string CoordTime { get; set; }

        [DataMember]
        public string MaxCGDrainTime { get; set; }

        [DataMember]
        public string CurrentTime { get; set; }

        [DataMember]
        public string CGTime { get; set; }

        [DataMember]
        public string SuccessfulCGPercentage { get; set; }

        [DataMember]
        public string FlashCopySequenceNumber { get; set; }

        [DataMember]
        public string MasterId { get; set; }

        [DataMember]
        public string SubordinateCount { get; set; }

        [DataMember]
        public string MasterSubordinateAssoc { get; set; }

        [DataMember]
        public string ApplicationName { get; set; }

        [DataMember]
        public string PRStorageImageId { get; set; }

        [DataMember]
        public string DRStorageImageId { get; set; }

        #endregion Properties
    }
}