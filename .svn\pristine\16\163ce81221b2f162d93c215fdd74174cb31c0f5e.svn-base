﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;


namespace CP.UI
{
    public abstract class BIAFunctionReportBasePage : BasePage
    {
        #region "Global Variable"
        private int _businessfunctionId = 0;
        private string _biamode = string.Empty;
        private BusinessFunction _businessfunction = null;
        #endregion

        protected int CurrentBusinessFunctionId
        {
            get
            {
                if (_businessfunctionId == 0)
                {
                    if (Helper.Url.SecureUrl[Constants.UrlConstants.Params.BusinessfunctionId].IsNotNullOrEmpty())
                    {
                        _businessfunctionId = Helper.Url.SecureUrl[Constants.UrlConstants.Params.BusinessfunctionId].ToInteger();
                    }
                }
                return _businessfunctionId;
            }
            set
            {
                _businessfunctionId = value;
            }
        }

        protected string CurrentbiaMode
        {
            get
            {
                if (string.IsNullOrEmpty(_biamode))
                {
                    if (Helper.Url.SecureUrl[Constants.UrlConstants.Params.biamode].IsNotNullOrEmpty())
                    {
                        CurrentbiaMode = Helper.Url.SecureUrl[Constants.UrlConstants.Params.biamode].ToString();
                    }
                }
                return _biamode;
            }
            set
            {
                _biamode = value;
            }
        }

        protected BusinessFunction CurrentBusinessFunction
        {
            get
            {
                if (_businessfunction == null)
                {
                    if (CurrentBusinessFunctionId > 0)
                    {
                        _businessfunction = Facade.GetBusinessFunctionById(CurrentBusinessFunctionId);
                    }
                    if (_businessfunction == null)
                    {
                        _businessfunction = new BusinessFunction();
                    }
                }
                return _businessfunction;
            }
            set
            {
                _businessfunction = value;
            }
        }
    }
}