﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess.HP3PARRepliMonitor
{

    internal sealed class HP3PARMonitorBuilder : IEntityBuilder<HP3PAR_Monitor>
    {
        IList<HP3PAR_Monitor> IEntityBuilder<HP3PAR_Monitor>.BuildEntities(IDataReader reader)
        {
            var sqllognative = new List<HP3PAR_Monitor>();

            while (reader.Read())
            {
                sqllognative.Add(((IEntityBuilder<HP3PAR_Monitor>)this).BuildEntity(reader, new HP3PAR_Monitor()));
            }

            return (sqllognative.Count > 0) ? sqllognative : null;
        }



        HP3PAR_Monitor IEntityBuilder<HP3PAR_Monitor>.BuildEntity(IDataReader reader, HP3PAR_Monitor sqllognative)
        {
            sqllognative.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            sqllognative.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            sqllognative.PRStorageIPAddress = Convert.IsDBNull(reader["PRStorageIPAddress"])
                ? string.Empty
                : Convert.ToString(reader["PRStorageIPAddress"]);
            sqllognative.PRStorageName = Convert.IsDBNull(reader["PRStorageName"])
                ? string.Empty
                : Convert.ToString(reader["PRStorageName"]);
            sqllognative.PRGroupName = Convert.IsDBNull(reader["PRGroupName"])
                ? string.Empty
                : Convert.ToString(reader["PRGroupName"]);
            sqllognative.PRRole = Convert.IsDBNull(reader["PRRole"])
                ? string.Empty
                : Convert.ToString(reader["PRRole"]);
            sqllognative.PRReplicationMode = Convert.IsDBNull(reader["PRReplicationMode"])
                ? string.Empty
                : Convert.ToString(reader["PRReplicationMode"]);
            sqllognative.PRState = Convert.IsDBNull(reader["PRState"])
                ? string.Empty
                : Convert.ToString(reader["PRState"]);
            sqllognative.PRGroupState = Convert.IsDBNull(reader["PRGroupState"])
                ? string.Empty
                : Convert.ToString(reader["PRGroupState"]);
            sqllognative.PRLastSyncTime = Convert.IsDBNull(reader["PRLastSyncTime"])
                ? string.Empty
                : Convert.ToString(reader["PRLastSyncTime"]);



            sqllognative.DRStorageIPAddress = Convert.IsDBNull(reader["DRStorageIPAddress"])
                ? string.Empty
                : Convert.ToString(reader["DRStorageIPAddress"]);
            sqllognative.DRStorageName = Convert.IsDBNull(reader["DRStorageName"])
                ? string.Empty
                : Convert.ToString(reader["DRStorageName"]);
            sqllognative.DRGroupName = Convert.IsDBNull(reader["DRGroupName"])
                ? string.Empty
                : Convert.ToString(reader["DRGroupName"]);
            sqllognative.DRRole = Convert.IsDBNull(reader["DRRole"])
                ? string.Empty
                : Convert.ToString(reader["DRRole"]);


            sqllognative.DRReplicationMode = Convert.IsDBNull(reader["DRReplicationMode"])
             ? string.Empty
             : Convert.ToString(reader["DRReplicationMode"]);
            sqllognative.DRState = Convert.IsDBNull(reader["DRState"])
                ? string.Empty
                : Convert.ToString(reader["DRState"]);
            sqllognative.DRGroupState = Convert.IsDBNull(reader["DRGroupState"])
                ? string.Empty
                : Convert.ToString(reader["DRGroupState"]);
            sqllognative.DRLastSyncTime = Convert.IsDBNull(reader["DRLastSyncTime"])
                ? string.Empty
                : Convert.ToString(reader["DRLastSyncTime"]);


            sqllognative.DataLag = Convert.IsDBNull(reader["DataLag"])
                ? string.Empty
                : Convert.ToString(reader["DataLag"]);

            sqllognative.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);
            //sqllognative.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            //sqllognative.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            //sqllognative.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            //sqllognative.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
            //    ? DateTime.MinValue
            //    : Convert.ToDateTime(reader["UpdateDate"]);

            return sqllognative;
        }
    }
}
