﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;



namespace CP.DataAccess
{
    internal sealed class VVRReplicationBuilder : IEntityBuilder<VVRReplication>
    {
        IList<VVRReplication> IEntityBuilder<VVRReplication>.BuildEntities(IDataReader reader)
        {
            var vvrReplication = new List<VVRReplication>();

            while (reader.Read())
            {
                vvrReplication.Add(((IEntityBuilder<VVRReplication>)this).BuildEntity(reader, new VVRReplication()));
            }

            return (vvrReplication.Count > 0) ? vvrReplication : null;
        }

        VVRReplication IEntityBuilder<VVRReplication>.BuildEntity(IDataReader reader, VVRReplication VVRBuildReplication)
        {
            VVRBuildReplication.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]);
            //  hitachiUrReplication.ReplicationBase.reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
            VVRBuildReplication.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                ? 0
                : Convert.ToInt32(reader["ReplicationId"]);
            VVRBuildReplication.PrServerId = Convert.IsDBNull(reader["PrServerId"])
                ? 0
                : Convert.ToInt32(reader["PrServerId"]);
            VVRBuildReplication.DrServerId = Convert.IsDBNull(reader["DrServerId"])
                ? 0
                : Convert.ToInt32(reader["DrServerId"]);

            VVRBuildReplication.PRDGName = Convert.IsDBNull(reader["PRDGName"]) ? string.Empty : Convert.ToString(reader["PRDGName"]);
            VVRBuildReplication.PRRVGName = Convert.IsDBNull(reader["PRRVGName"]) ? string.Empty : Convert.ToString(reader["PRRVGName"]);

            VVRBuildReplication.PRLinkName = Convert.IsDBNull(reader["PRLinkName"])
                ? string.Empty
                : Convert.ToString(reader["PRLinkName"]);
            VVRBuildReplication.DRDGName = Convert.IsDBNull(reader["DRDGName"])
                ? string.Empty
                : Convert.ToString(reader["DRDGName"]);

            VVRBuildReplication.DRRVGName = Convert.IsDBNull(reader["DRRVGName"])
                ? string.Empty
                : Convert.ToString(reader["DRRVGName"]);
            VVRBuildReplication.DRLinkName = Convert.IsDBNull(reader["DRLinkName"])
                ? string.Empty
                : Convert.ToString(reader["DRLinkName"]);
            VVRBuildReplication.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            VVRBuildReplication.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            VVRBuildReplication.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            VVRBuildReplication.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());
            VVRBuildReplication.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            return VVRBuildReplication;
        }
    }
}
