﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="DataSyncPropertiesConfig.aspx.cs"
    Inherits="CP.UI.DataSyncPropertiesConfig" Title="Continuity Patrol :: Component-DataSyncPropertiesConfig" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
 <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
               
                <h3>
                    <img src="../Images/replication_cc.png" />
                    Datasync Properties</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">

                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Name<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtName" runat="server" class="form-control" TabIndex="1" AutoPostBack="true" MaxLength="100"
                                            autocomplete="off" OnTextChanged="txtDSName_TextChanged"></asp:TextBox>
                                        <asp:Label ID="lblDSName" runat="server" ForeColor="Red" Text=""></asp:Label>
                                        <asp:RequiredFieldValidator ID="rfvDSName" runat="server" ValidationGroup="saveDataSyncGrp" ErrorMessage="Enter DataSync Properties Name"
                                            ControlToValidate="txtName" Display="Dynamic" CssClass="error"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="revDSName" runat="server" ControlToValidate="txtName" ValidationGroup="saveDataSyncGrp"
                                            ErrorMessage="Enter Valid DataSync Properties Name" ValidationExpression="^(([a-zA-Z_-]+)|([a-zA-Z_-]+[0-9_-]+))$"
                                            Display="Dynamic" CssClass="error"></asp:RegularExpressionValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Replication Type<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlReplType" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlReplType_onSelectedIndexChanged"
                                            CssClass="selectpicker col-md-6" data-style="btn-default">
                                            <asp:ListItem Text="- Select Replication Type -" Value="0" Selected="True" />
                                            <asp:ListItem Text="Application" Value="1" />
                                            <asp:ListItem Text="Database" Value="2" />
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="rfvReplType" runat="server" ControlToValidate="ddlReplType" InitialValue="0" ValidationGroup="saveDataSyncGrp"
                                            Display="Dynamic" ForeColor="Red" CssClass="error" ErrorMessage="Select Replication Type"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group" id="divFilterOption" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        Filter Option<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:RadioButtonList ID="equalsOrLike" runat="server" RepeatLayout="Flow" TabIndex="2" RepeatDirection="Horizontal" AutoPostBack="true" OnSelectedIndexChanged="equalsOrLike_SelectedIndexChanged">
                                            <asp:ListItem Text="None" Value="0" Selected="True" />
                                            <asp:ListItem Text="Exclude" Value="1" />
                                            <asp:ListItem Text="Include" Value="2" />
                                        </asp:RadioButtonList>
                                    </div>
                                </div>
                                <div class="form-group" id="divFilterExpnexclude" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        File To Exclude<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtFilterExpn" AutoPostBack="false" MaxLength="500" runat="server" CssClass="form-control" TextMode="MultiLine"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvFilterExpn" runat="server" ControlToValidate="txtFilterExpn"
                                            Display="Dynamic" ValidationGroup="saveDataSyncGrp" ForeColor="Red" CssClass="error" ErrorMessage="Enter Filter Value(s)"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator runat="server" ID="revFilterExpn" ControlToValidate="txtFilterExpn" Display="Dynamic"
                                            ForeColor="Red" CssClass="error" ValidationGroup="saveDataSyncGrp" ValidationExpression="^[\s\S]{0,500}$" ErrorMessage="Maximum 500 characters allowed">
                                        </asp:RegularExpressionValidator>
                                    </div>
                                </div>

                                <div class="form-group" id="divFilterExpninclude" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        File To Include<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtFilterExpn1" AutoPostBack="false" MaxLength="100" runat="server" CssClass="form-control" TextMode="MultiLine"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvFilterExpn1" runat="server" ControlToValidate="txtFilterExpn1"
                                            Display="Dynamic" ValidationGroup="saveDataSyncGrp" ForeColor="Red" CssClass="error" ErrorMessage="Enter Filter Value(s)"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator runat="server" ID="RegularExpressionValidator1" ControlToValidate="txtFilterExpn1" Display="Dynamic"
                                            ForeColor="Red" CssClass="error" ValidationGroup="saveDataSyncGrp" ValidationExpression="^[\s\S]{0,100}$" ErrorMessage="Maximum 100 characters allowed">
                                        </asp:RegularExpressionValidator>
                                    </div>
                                </div>

                                <div class="form-group" id="divDelFilterOption" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        Deletion Filter Option <span class="inactive"></span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkDelFilterOption" CssClass="vertical-align" runat="server" Text="" TabIndex="3" AutoPostBack="true" OnCheckedChanged="chkDelFilterOption_OnCheckedChanged"></asp:CheckBox>
                                    </div>
                                </div>
                                <div class="form-group" id="divDelFilterExpn" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        File not To Delete<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtDelFilterExpn" MaxLength="100" runat="server" CssClass="form-control" TextMode="MultiLine"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvDelFilterExpn" runat="server" ControlToValidate="txtDelFilterExpn"
                                            Display="Dynamic" ForeColor="Red" CssClass="error" ErrorMessage="Enter Filter Value(s)"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator runat="server" ID="revDelFilterExpn" ControlToValidate="txtDelFilterExpn" Display="Dynamic"
                                            ForeColor="Red" CssClass="error" ValidationExpression="^[\s\S]{0,100}$" ErrorMessage="Maximum 100 characters allowed">
                                        </asp:RegularExpressionValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Enable SSH Private key (Production) <span class="inactive"></span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkSSHPrivateKeyPR" CssClass="vertical-align" runat="server" Text="" TabIndex="3" AutoPostBack="true" OnCheckedChanged="chkSSHPrivateKeyPR_OnCheckedChanged"></asp:CheckBox>
                                    </div>
                                </div>
                                <div class="form-group" id="divSSHKeyPathPR" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        SSH Private key Path (Production) <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtSSHKeyPathPR" runat="server" class="form-control" TabIndex="4"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvSSHKeyPathPR" runat="server" ControlToValidate="txtSSHKeyPathPR"
                                            ErrorMessage="Enter SSH Private KeyPath (Production)" CssClass="error" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Enable SSH Private key (DR) <span class="inactive"></span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkSSHPrivateKeyDR" CssClass="vertical-align" runat="server" Text="" TabIndex="3" AutoPostBack="true" OnCheckedChanged="chkSSHPrivateKeyDR_OnCheckedChanged"></asp:CheckBox>
                                    </div>
                                </div>
                                <div class="form-group" id="divSSHKeyPathDR" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        SSH Private key Path (DR) <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtSSHKeyPathDR" runat="server" class="form-control" TabIndex="4"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvSSHKeyPathDR" runat="server" ControlToValidate="txtSSHKeyPathDR"
                                            ErrorMessage="Enter SSH Private KeyPath (DR)" CssClass="error" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>
                                <div class="form-group" id="divFolderPermission" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        Retain Folder Permission<span class="inactive"></span></label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkFolderPermission" CssClass="vertical-align" runat="server" TabIndex="5" />
                                    </div>
                                </div>
                                <div class="form-group" id="divChecksumCompare" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        Enable Checksum Compare <span class="inactive"></span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkChecksumCompare" CssClass="vertical-align" runat="server" TabIndex="6" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Production Shell Prompt<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtShellPromptPR" runat="server" MaxLength="10" class="form-control" TabIndex="7"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvShellPromptPR" runat="server" ControlToValidate="txtShellPromptPR" ValidationGroup="saveDataSyncGrp"
                                            ErrorMessage="Enter Shell Prompt (Production)" Display="Dynamic" CssClass="error"></asp:RequiredFieldValidator>
                                         <asp:RegularExpressionValidator runat="server" ID="revShellPromptPR" ControlToValidate="txtShellPromptPR" Display="Dynamic"
                                            ForeColor="Red" CssClass="error" ValidationGroup="saveDataSyncGrp" ValidationExpression="^[$#>:]$" ErrorMessage="Enter valid Shell Prompt (Production)">
                                         </asp:RegularExpressionValidator>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        DR Shell Prompt<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtShellPromptDR" MaxLength="10" runat="server" class="form-control" TabIndex="7"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvShellPromptDR" runat="server" ControlToValidate="txtShellPromptDR" ValidationGroup="saveDataSyncGrp"
                                            ErrorMessage="Enter Shell Prompt (DR)" Display="Dynamic" CssClass="error"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator runat="server" ID="revShellPromptDR" ControlToValidate="txtShellPromptDR" Display="Dynamic"
                                            ForeColor="Red" CssClass="error" ValidationGroup="saveDataSyncGrp" ValidationExpression="^[$#>:]$" ErrorMessage="Enter valid Shell Prompt (DR)">
                                         </asp:RegularExpressionValidator>

                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Enable Parallel Replication<span class="inactive"></span></label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkParallelRepl" CssClass="vertical-align" runat="server" TabIndex="8" AutoPostBack="true" OnCheckedChanged="chkParallelRepl_OnCheckedChanged" />
                                    </div>
                                </div>
                                <div class="form-group" id="divNoThreads" runat="server" visible="false">
                                    <label class="col-md-3 control-label">
                                        No. of Threads<span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtNoThreads" MaxLength="3" runat="server" class="form-control" TabIndex="9"></asp:TextBox>
                                        <asp:RequiredFieldValidator ID="rfvNoThreads" runat="server" ControlToValidate="txtNoThreads" ValidationGroup="saveDataSyncGrp"
                                            ErrorMessage="Enter Number of Threads" CssClass="error" Display="Dynamic"></asp:RequiredFieldValidator>
                                        <asp:RegularExpressionValidator ID="revtxtNoThreads" runat="server" CssClass="error" ControlToValidate="txtNoThreads"
                                            ErrorMessage="Enter only Numbers" ValidationGroup="saveDataSyncGrp" ValidationExpression="\d+" SetFocusOnError="True" Font-Size="9pt" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Enable Incremental Replication<span class="inactive"></span></label>
                                    <div class="col-md-9">
                                        <asp:CheckBox ID="chkIncrementalRepl" CssClass="vertical-align" runat="server" TabIndex="10" />
                                    </div>
                                </div>
                                <hr class="separator" />
                                <div class="form-actions row">
                                    <div class="col-lg-5">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                                Fields</span>
                                    </div>
                                    <div class="col-lg-7" >
                                        <asp:Button ID="btnSave" CssClass="btn btn-primary" ValidationGroup="saveDataSyncGrp" TabIndex="11" CausesValidation="true" Style="margin-left:10px"
                                            Width="15%" runat="server" Text="Save" OnClick="BtnSaveClick" OnClientClick="" />
                                        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server"
                                            Text="Cancel" OnClick="BtnCancelClick" CausesValidation="False" TabIndex="12" />
                                    </div>
                                </div>
                              
                            </div>
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>