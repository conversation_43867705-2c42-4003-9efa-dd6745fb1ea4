﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{

    [Serializable]
    [DataContract(Name = "CGVolumeMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class CGVolumeMonitor : BaseEntity
    {

        #region Properties
        [DataMember]
        public string PRVolume { get; set; }

        [DataMember]
        public string DRVolume { get; set; }

        [DataMember]
        public string PRStorageName { get; set; }

        [DataMember]
        public string DRStorageName { get; set; }

        [DataMember]
        public string ActiveSatus { get; set; }

        [DataMember]
        public string RPOStatus { get; set; }

        [DataMember]
        public string LinkUp { get; set; }

        [DataMember]
        public int CGID { get; set; }

        #endregion Properties
    }
}
