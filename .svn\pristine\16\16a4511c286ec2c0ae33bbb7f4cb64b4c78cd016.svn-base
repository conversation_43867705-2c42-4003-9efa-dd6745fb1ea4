﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="VeritasClusterDetailedmonitoring.ascx.cs" Inherits="CP.UI.Controls.VeritasClusterDetailedmonitoring" %>

<style>
    .table hr {
        margin: 5px 0;
    }
</style>

<div class="form-horizontal margin-none" runat ="server" id="dvClusterdetails">

    <div class="widget-head" style="overflow: visible;">
        <span class="heading">Cluster Information</span>
    </div>
    <div class="widget-body">
        <asp:ListView ID="lvClusterInfo" runat="server" DataKeyNames="Id">
            <LayoutTemplate>
                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                    <thead>
                        <tr>
                            <th style="width: 4%;">Sr.No.</th>
                            <th style="width: 32%;">Cluster Name
                            </th>
                            <th style="width: 32%;">Cluster Address
                            </th>
                            <th style="width: 32%;">State</th>
                        </tr>
                    </thead>
                    <tbody>
                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                    </tbody>
                </table>
            </LayoutTemplate>
            <EmptyDataTemplate>
                <div class="message warning align-center bold">
                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                </div>
            </EmptyDataTemplate>
            <ItemTemplate>
                <tr>
                    <td style="width: 4%;" class="text-center">
                        <%#Container.DataItemIndex + 1%>
                    </td>
                    <asp:Label ID="ID" runat="server" Text='<%#Eval("Id")%>' Visible="false" />
                    <td style="width: 30%;" class="tdword-wrap">
                        <asp:Label ID="Cluster_Name" runat="server" Text='<%#Eval("ClusterName")%>' />
                    </td>
                    <td style="width: 20%;" class="tdword-wrap">
                        <asp:Label ID="Server_IP" runat="server" CssClass="ipaddress_padl20" Text='<%# Eval("ClusterIP")%>' />
                    </td>
                    <td style="width: 20%;">
                        <asp:Label ID="Cluster_State" runat="server" CssClass="running_padl20" Text='<%#Eval("ClusterState")%>' />
                    </td>
                </tr>
            </ItemTemplate>
        </asp:ListView>
    </div>

    <div class="widget-head" style="overflow: visible;">
        <span class="heading">Cluster Status</span>
    </div>
    <div class="widget-body">
        <asp:ListView ID="lvClusterStatus" runat="server" DataKeyNames="Id">
            <LayoutTemplate>
                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                    <thead>
                        <tr>
                            <th style="width: 4%;">Sr.No.</th>
                            <th style="width: 32%;">Cluster Name
                            </th>
                            <th style="width: 32%;">Node Name
                            </th>
                            <th style="width: 32%;">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                    </tbody>
                </table>
            </LayoutTemplate>
            <EmptyDataTemplate>
                <div class="message warning align-center bold">
                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                </div>
            </EmptyDataTemplate>
            <ItemTemplate>
                <tr>
                    <td style="width: 4%;" class="text-center">
                        <%#Container.DataItemIndex + 1%>
                    </td>
                    <asp:Label ID="ID" runat="server" Text='<%#Eval("Id")%>' Visible="false" />
                    <td style="width: 30%;" class="tdword-wrap">
                        <asp:Label ID="Cluster_Name" runat="server" Text='<%#Eval("ClusterName")%>' />
                    </td>
                    <td style="width: 20%;" class="tdword-wrap">
                        <span class="node_padl20"></span>
                        <asp:Label ID="Cluster_NodeName" runat="server" CssClass="" Text='<%# Eval("ClusterNodeName")%>' />
                    </td>
                    <td style="width: 20%;">
                        <span class="" id="clustimg" runat="server"></span>
                        <asp:Label ID="Cluster_NodeState" runat="server" CssClass="" Text='<%#Eval("ClusterNodeState")%>' />
                    </td>
                </tr>
            </ItemTemplate>
        </asp:ListView>



    </div>

    <div class="widget-head" style="overflow: visible;">
        <span class="heading">Resource Group</span>
    </div>
    <div class="widget-body">
        <asp:ListView ID="lvResourceGroup" runat="server" DataKeyNames="Id">
            <LayoutTemplate>
                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                    <thead>
                        <tr>
                            <th style="width: 4%;">Sr.No.</th>
                            <th style="width: 32%;">Group Name
                            </th>
                            <th style="width: 32%;">Node Name
                            </th>
                            <th style="width: 32%;">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                    </tbody>
                </table>
            </LayoutTemplate>
            <EmptyDataTemplate>
                <div class="message warning align-center bold">
                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                </div>
            </EmptyDataTemplate>
            <ItemTemplate>
                <tr>
                    <td style="width: 4%;" class="text-center">
                        <%#Container.DataItemIndex + 1%>
                    </td>
                    <asp:Label ID="ID" runat="server" Text='<%#Eval("Id")%>' Visible="false" />
                    <td style="width: 30%;" class="tdword-wrap">
                        <asp:Label ID="Cluster_GroupName" runat="server" Text='<%#Eval("ClusterGroupName")%>' />
                    </td>
                    <td style="width: 20%;" class="tdword-wrap">

                        <asp:Label ID="Cluster_GroupNodeName" runat="server" CssClass="node_padl20" Text='<%# Eval("ClusterGroupNodeName")%>' />
                    </td>
                    <td style="width: 20%;">
                        <asp:Label ID="Cluster_GroupNodeState" runat="server" Text='<%#Eval("ClusterGroupNodeState")%>' />
                    </td>
                </tr>
            </ItemTemplate>
        </asp:ListView>

        <div>
            <div id="dvResourceGroupDetails" runat="server">
            </div>
            <div class="message warning align-center bold">
                <asp:Label ID="lblEmpty1" Text="No Record Found" runat="server" Visible="false"></asp:Label>
            </div>
        </div>


    </div>

</div>
