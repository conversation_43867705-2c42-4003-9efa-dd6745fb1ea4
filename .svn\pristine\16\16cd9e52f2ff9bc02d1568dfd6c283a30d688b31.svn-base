﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CP.Common.DatabaseEntity;
using System.Data;

namespace CP.DataAccess.WFProfileApprovalLevelProcesses
{
    class WFProfileApprovalLevelProcessBuilder : IEntityBuilder<AppLevelProcess>
    {
        IList<CP.Common.DatabaseEntity.AppLevelProcess> IEntityBuilder<CP.Common.DatabaseEntity.AppLevelProcess>.BuildEntities(IDataReader reader)
        {
            var approvals = new List<CP.Common.DatabaseEntity.AppLevelProcess>();

            while (reader.Read())
            {
                approvals.Add(((IEntityBuilder<CP.Common.DatabaseEntity.AppLevelProcess>)this).BuildEntity(reader, new CP.Common.DatabaseEntity.AppLevelProcess()));
            }

            return (approvals.Count > 0) ? approvals : null;
        }

        CP.Common.DatabaseEntity.AppLevelProcess IEntityBuilder<CP.Common.DatabaseEntity.AppLevelProcess>.BuildEntity(IDataReader reader, CP.Common.DatabaseEntity.AppLevelProcess obj)
        {

            obj.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            obj.ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]);
            obj.ApprovalProcessId = Convert.IsDBNull(reader["ApprovalProcessId"]) ? 0 : Convert.ToInt32(reader["ApprovalProcessId"]);
            obj.ApprovalLevel = Convert.IsDBNull(reader["ApprovalLevel"]) ? string.Empty : Convert.ToString(reader["ApprovalLevel"]);
            //obj.LevelNumber = Convert.IsDBNull(reader["LevelNumber"]) ? 0 : Convert.ToInt32(reader["LevelNumber"]);
            obj.Approver = Convert.IsDBNull(reader["Approver"]) ? string.Empty : Convert.ToString(reader["Approver"]);
            obj.ApproverEmail = Convert.IsDBNull(reader["APPROVEREMAIL"]) ? string.Empty : Convert.ToString(reader["APPROVEREMAIL"]);
            obj.ApprovalState = Convert.IsDBNull(reader["ApprovalState"]) ? string.Empty : Convert.ToString(reader["ApprovalState"]);
            obj.ApprovalBy = Convert.IsDBNull(reader["ApprovalBy"]) ? string.Empty : Convert.ToString(reader["ApprovalBy"]);
            obj.Message = Convert.IsDBNull(reader["Message"]) ? string.Empty : Convert.ToString(reader["Message"]);
            obj.Note = Convert.IsDBNull(reader["Note"]) ? string.Empty : Convert.ToString(reader["Note"]);
            obj.IsApprovalReceived = Convert.IsDBNull(reader["IsApprovalReceived"]) ? false : Convert.ToBoolean(reader["IsApprovalReceived"]);
            obj.IsApprove = Convert.IsDBNull(reader["IsApprove"]) ? false : Convert.ToBoolean(reader["IsApprove"]);
            obj.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            obj.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
            obj.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            obj.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]);
            return obj;


        }

    }
}

