﻿using System;
using CP.BusinessFacade;
using CP.Common.Shared;

namespace CP.UI.Code.Replication.DataLag
{
    public class HADR9XDataLag : IDataLag
    {
        private readonly IFacade _facade = new Facade();

        public InfraObjectInfo GetDataLag(int groupId)
        {
            var infraObjectInfo = new InfraObjectInfo();

            // var dataLag = _facade.GetHADRByGroupId(groupId);

            var dataLag = _facade.GetHADRReplicationByInfraObjectId(groupId);

            var group = _facade.GetInfraObjectById(groupId);

            var objbusinessfunction = _facade.GetBusinessFunctionByInfraObjectId(groupId);

            var BusinessServicerpoinfo = _facade.GetCurrentRPOInfraInfobyInfraObjectId(groupId, group.BusinessFunctionId);

            if (dataLag != null && group != null && BusinessServicerpoinfo != null && objbusinessfunction != null)
            {
                if (string.IsNullOrEmpty(dataLag.LogGap))
                {
                    infraObjectInfo.DataLag = "N/A";

                    infraObjectInfo.Health = 0;

                    infraObjectInfo.DataLagCreateDate = "N/A";
                }
                else
                {
                    int gap = 0;

                    if (dataLag.LogGap.Contains("N/A"))
                    {
                        infraObjectInfo.DataLag = "N/A";

                        infraObjectInfo.Health = 0;

                        infraObjectInfo.DataLagCreateDate = "N/A";
                        infraObjectInfo.ReplicationStatus = group.ReplicationStatus;
                        infraObjectInfo = CheckState(dataLag.State, infraObjectInfo);
                    }
                    else
                    {
                        infraObjectInfo.DataLag = dataLag.LogGap + " bytes";

                        if (int.TryParse(dataLag.LogGap, out gap))
                        {
                            int health = Utility.GetDatlagHealth(BusinessServicerpoinfo.CurrentRPO, Convert.ToInt32(Utility.ConvertRPOValues(objbusinessfunction.ConfiguredRPO))) ? 1 : 0;
                            infraObjectInfo.Health = Convert.ToInt32(gap > health);
                            infraObjectInfo.DataLagCreateDate = dataLag.CreateDate.ToString();
                            infraObjectInfo.State = dataLag.State;
                            infraObjectInfo.ConnectionStatus = dataLag.ConnectionStatus;
                            infraObjectInfo.ReplicationStatus = group.ReplicationStatus;
                            infraObjectInfo = CheckState(infraObjectInfo.State, infraObjectInfo);
                        }
                        else
                        {
                            infraObjectInfo.DataLag = "N/A";

                            infraObjectInfo.Health = 0;

                            infraObjectInfo.DataLagCreateDate = "N/A";
                        }
                    }
                }
            }
            else
            {
                infraObjectInfo.DataLag = "N/A";

                infraObjectInfo.Health = 0;

                infraObjectInfo.DataLagCreateDate = "N/A";
            }

            return infraObjectInfo;
        }

        private InfraObjectInfo CheckState(string state, InfraObjectInfo groupInfo)
        {
            if ((state == "Not Active" || groupInfo.ConnectionStatus.Contains("Disconnected")) && groupInfo.ReplicationStatus != Convert.ToInt32(InfraObjectReplicationStatus.Maintenance))
            {
                groupInfo.DataLag = "N/A";

                groupInfo.Health = 0;

                groupInfo.DataLagCreateDate = "N/A";

                groupInfo.ReplicationStatus = Convert.ToInt32(InfraObjectReplicationStatus.NoLogsAvailable);

                groupInfo.State = "InActive";
            }
            return groupInfo;
        }
    }
}