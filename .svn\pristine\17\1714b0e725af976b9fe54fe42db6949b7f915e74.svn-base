﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "MaxEmcSrdfFullDB", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class MaxEmcSrdfFullDB : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public string PRDatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseName
        {
            get;
            set;
        }

        [DataMember]
        public string PRDatabaseState
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseState
        {
            get;
            set;
        }


        [DataMember]
        public string PRDatabaseVersion
        {
            get;
            set;
        }

        [DataMember]
        public string DRDatabaseVersion
        {
            get;
            set;
        }

        [DataMember]
        public string PRInstanceType
        {
            get;
            set;
        }

        [DataMember]
        public string DRInstanceType
        {
            get;
            set;
        }

        [DataMember]
        public string PRClientProgPath
        {
            get;
            set;
        }

        [DataMember]
        public string DRClientProgPath
        {
            get;
            set;
        }

        [DataMember]
        public string PRGlobalProgPath
        {
            get;
            set;
        }

        [DataMember]
        public string DRGlobalProgPath
        {
            get;
            set;
        }

        [DataMember]
        public string PRGlobalDataPath
        {
            get;
            set;
        }

        [DataMember]
        public string DRGlobalDataPath
        {
            get;
            set;
        }

        [DataMember]
        public string PRDataVolumes
        {
            get;
            set;
        }

        [DataMember]
        public string DRDataVolumes
        {
            get;
            set;
        }

        [DataMember]
        public string PRMaxDataVolumes
        {
            get;
            set;
        }

        [DataMember]
        public string DRMaxDataVolumes
        {
            get;
            set;
        }

        [DataMember]
        public string PRDataSize
        {
            get;
            set;
        }

        [DataMember]
        public string DRDataSize
        {
            get;
            set;
        }

        [DataMember]
        public string PRDataUsedSpace
        {
            get;
            set;
        }

        [DataMember]
        public string DRDataUsedSpace
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogPath
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogPath
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogVolumes
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogVolumes
        {
            get;
            set;
        }

        [DataMember]
        public string PRMaxLogVolumes
        {
            get;
            set;
        }

        [DataMember]
        public string DRMaxLogVolumes
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogUsedSpace
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogUsedSpace
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogMirrored
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogMirrored
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogWriting
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogWriting
        {
            get;
            set;
        }

        [DataMember]
        public string PRLogAutoOverwrite
        {
            get;
            set;
        }

        [DataMember]
        public string DRLogAutoOverwrite
        {
            get;
            set;
        }

        #endregion
    }
}
