﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DBSqlNative2008.ascx.cs" Inherits="CP.UI.Controls.DBSqlNative2008" %>

<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">MS-SQL2kX Database</h4>
        </div>
        <div class="widget-body">

            <asp:Panel ID="oraclePanel" runat="server">
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        DataBase Name<span class="inactive">*</span></label>
                    <div class="col-md-9">
                        <asp:TextBox ID="txtDBName" CssClass="form-control" runat="server"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="RequiredFieldValidator4" ControlToValidate="txtDBName" CssClass="error"
                            Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter DataBase Name"></asp:RequiredFieldValidator>
                        <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ValidationGroup="dbConfig" CssClass="error" ControlToValidate="txtDBName"
                            ErrorMessage="Enter Valid DataBase Name" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                            Display="Dynamic"></asp:RegularExpressionValidator>
                          <asp:Label ID="lblErr" runat="server" ForeColor="Red"
                                            Text="Invalid License Key" Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                    </div>
                </div>

                  <div class="form-group">
                    <label class="col-md-3 control-label" for="txtIPAddress" id="Label3" runat="server">
                        SSO Enabled</label>
                    <div class="col-md-9">
                        <asp:CheckBox ID="ChkSSOEnable" runat="server" TabIndex="13"
                            AutoPostBack="True" OnCheckedChanged="ChkSSOEnable_CheckedChanged"/> <%--OnCheckedChanged="ChkSSOEnableCheckedChanged"--%>
                    </div>
                </div>

                <asp:Panel ID="pnlSSoProfilrDrp" runat="server" Visible="false">
                    <div class="form-group">
                        <label class="col-md-3 control-label" for="ddlServerType">
                           SSO Arcos Profile <span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:DropDownList ID="ddlSSOProfile" runat="server" AutoPostBack="true" CssClass="col-md-6"> <%--chosen-select--%>
                            </asp:DropDownList>
                            <asp:Label ID="lblSsoProfileerrorMsg" runat="server" ForeColor="Red" Text="Select SSO Profile" Visible="false"></asp:Label>
                           <%-- <asp:RequiredFieldValidator ID="rfvssoprofile" runat="server" CssClass="error" ControlToValidate="ddlSSOProfile"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select SSO Profile"></asp:RequiredFieldValidator>--%>
                        </div>
                    </div>
                </asp:Panel>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        Configured instance Name<span class="inactive"></span></label>
                    <div class="col-md-9">
                        <asp:CheckBox ID="chkconfig" runat="server" AutoPostBack="True" OnCheckedChanged="chkconfig_CheckedChanged" CssClass="vertical-align" />

                    </div>
                </div>

                <div id="divinstance" class="form-group" runat="server" visible="false">
                    <label class="col-md-3 control-label">
                        Instance Name<span class="inactive">*</span></label>
                    <div class="col-md-9">
                        <asp:TextBox ID="txtinstaname" CssClass="form-control" runat="server"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" ControlToValidate="txtinstaname" CssClass="error"
                            Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Instance Name"></asp:RequiredFieldValidator>

                    </div>
                </div>

                <div id="Div1" class="form-group" runat="server">
                    <label class="col-md-3 control-label">Authentication Mode</label>
                    <div id="Div2" class="col-md-9" runat="server">
                        <asp:RadioButtonList ID="rbtnListAuthenticationMode" runat="server" AutoPostBack="true" RepeatDirection="Horizontal" OnSelectedIndexChanged="rbtnListAuthenticationMode_SelectedIndexChanged">
                            <asp:ListItem Selected="True">Windows</asp:ListItem>
                            <asp:ListItem>SqlServer</asp:ListItem>
                        </asp:RadioButtonList>
                        <asp:RequiredFieldValidator ID="RequiredFieldValidator5" CssClass="error" ControlToValidate="rbtnListAuthenticationMode" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="*"></asp:RequiredFieldValidator>
                    </div>
                </div>
                <div class="form-group" id="dvusername" runat="server">

                    <asp:Label ID="lblUsername11" Text="User Name" runat="server" class="col-md-3 control-label">
                    </asp:Label>
                    <div class="col-md-9">
                        <asp:TextBox ID="txtUserName" CssClass="form-control" runat="server"></asp:TextBox>

                        <asp:RegularExpressionValidator ID="rfeUser" runat="server" ValidationGroup="dbConfig" CssClass="error" ControlToValidate="txtUserName"
                            ErrorMessage="Enter Valid User Name" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                            Display="Dynamic"></asp:RegularExpressionValidator>
                    </div>
                </div>
                <div class="form-group" id="dvpassword" runat="server">

                    <label id="lblPassword11" runat="server" class="col-md-3 control-label">
                        Password</label>
                    <div class="col-md-9">
                        <asp:TextBox ID="txtPassword" CssClass="form-control" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>

                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label">
                        Port<span class="inactive">*</span></label>
                    <div class="col-md-9">
                        <asp:TextBox ID="txtPort" runat="server" CssClass="form-control" MaxLength="6"></asp:TextBox>
                        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" ControlToValidate="txtPort" CssClass="error"
                            Display="Dynamic" runat="server" ValidationGroup="dbConfig" ErrorMessage="Enter Port"></asp:RequiredFieldValidator>
                        <asp:RegularExpressionValidator ID="revPhone1" runat="server" ControlToValidate="txtPort" CssClass="error"
                            ValidationGroup="dbConfig" ErrorMessage="Valid Numbers only" ValidationExpression="[0-9]{2,6}"></asp:RegularExpressionValidator>
                    </div>
                </div>
            </asp:Panel>
        </div>
    </div>
</div>

