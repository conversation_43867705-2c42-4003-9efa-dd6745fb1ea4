﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "BusinessFunctionBIARelation", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BusinessFunctionBIARelation : BaseEntity
    {
        #region Properties
        [DataMember]
        public int ParentAppID { get; set; }

        [DataMember]
        public int ChildAppID { get; set; }

        [DataMember]
        public int RelDirID { get; set; }

        [DataMember]
        public int RelTypeID { get; set; }      

        #endregion
    }
}
