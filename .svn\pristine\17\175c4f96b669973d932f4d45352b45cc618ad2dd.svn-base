﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
   public interface IVeritasClusterMonitoringDataAccess
    {
        #region VeritasClusterMonitoring

       IList<VeritasClusterMonitor> GetVeritasClusterMonitorStatus(int infraObjectId);

       IList<VeritasClusterMonitor> GetVeritasClusterNodeMonitorStatus(int infraObjectId);

       IList<VeritasClusterMonitor> GetVeritasClusterGroupSummaryStatus(int infraObjectId);

        #endregion
    }
}
