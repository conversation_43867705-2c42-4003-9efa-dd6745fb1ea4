﻿using System;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;

namespace CP.UI.Code.Replication.DataLag
{
    public class RecoverPointAppDataLag : IDataLag
    {
        #region IDataLag Members
        private readonly IFacade _facade = new Facade();

        public InfraObjectInfo GetDataLag(int infraobjectid)
        {
            var infraObjectInfo = new InfraObjectInfo();

            InfraObject infraObject = _facade.GetInfraObjectById(infraobjectid);
            var objbusinessfunction = _facade.GetBusinessFunctionByInfraObjectId(infraobjectid);

            if (infraObject != null)
            {
                var emce = _facade.GetCurrentRPOInfraInfobyInfraObjectId(infraobjectid, infraObject.BusinessFunctionId);

                if (emce != null)
                {
                    if (string.IsNullOrEmpty(emce.CurrentRPO))
                    {
                        infraObjectInfo.DataLag = "N/A";

                        infraObjectInfo.Health = 0;

                        infraObjectInfo.DataLagCreateDate = "N/A";
                    }
                    else
                    {
                        infraObjectInfo.DataLag = emce.CurrentRPO;

                        infraObjectInfo.Health = Utility.GetDatlagHealth(emce.CurrentRPO, Convert.ToInt32(Utility.ConvertRPOValues(objbusinessfunction.ConfiguredRPO))) ? 1 : 0; ;

                        infraObjectInfo.DataLagCreateDate = Convert.ToString(emce.CreateDate);
                    }
                }
                else
                {
                    infraObjectInfo.DataLag = "N/A";

                    infraObjectInfo.Health = 0;

                    infraObjectInfo.DataLagCreateDate = "N/A";
                }
            }

            return infraObjectInfo;
        }
        #endregion
    }
}