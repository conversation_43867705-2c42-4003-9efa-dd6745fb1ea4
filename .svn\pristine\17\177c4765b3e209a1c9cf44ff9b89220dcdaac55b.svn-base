﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    # region IAlertManager

    public interface IAlertManagerDataAccess
    {
        AlertsManager GetById(int id);

        AlertsManager Add(AlertsManager alertmanager);

        AlertsManager Update(AlertsManager alertmanager);

        IList<AlertsManager> GetAll();

        IList<AlertsManager> GetAlertCount();

        IList<AlertsManager> GetIncidentCount();

        //  AlertsManager GetAlertCount(string name);
    }

    #endregion
}