﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.CacheController;
using CP.Common.BusinessEntity;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Controls;

namespace CP.UI
{
    public partial class GroupList : GroupBasePage
    {
        #region Variable

        public static string CurrentURL = Constants.UrlConstants.Urls.Group.DbGroupConfig;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Group.DbGroupList;
                }
                return string.Empty;
            }
        }

        private IFacade facade = new Facade();
        private static CacheManager _cacheManager;
        private static string cacheKey = "Group.GroupListIntoCache";
        //private static int applicationId;

        #endregion Variable

        private static CacheManager CurrentCacheManager
        {
            get
            {
                if (_cacheManager == null)
                {
                    _cacheManager = new CacheManager();
                }

                return _cacheManager;
            }
        }

        public string GetRecoveryType(object type, object typeid)
        {
            var recoveryType = string.Empty;
            var recovery = Convert.ToInt32(type);
            var Type = Convert.ToInt32(typeid);
            RecoveryTypeName Rtype = new RecoveryTypeName();
            var RecoveryType = Rtype.RecoveryTypebyId(recovery, Type);
            return RecoveryType;
        }

        public object IsEnable(object st)
        {
            int i = Convert.ToInt32(st);
            string typeRet = i == 1 ? "~/Images/Lock.png" : "~/Images/unlock.png";
            return typeRet;
        }

        protected bool DisableEdit(object item)
        {
            string groupStatus = Convert.ToString(item);
            return groupStatus == "Replicating" ? false : true;
        }

        protected string GetStatusType(object item)
        {
            string groupStatus = string.Empty;
            switch (Convert.ToString(item))
            {
                case "Active":
                    groupStatus = "../Images/icons/tick-circle.png";
                    break;

                case "Locked":
                    groupStatus = "../Images/icons/Lock.png";
                    break;

                case "Replicating":
                    groupStatus = "../Images/icons/loader.gif";
                    break;

                case "Maintenance":
                    groupStatus = "../Images/icons/maintenance.png";
                    break;
            }
            return groupStatus;
        }

        protected string GetRowStyle(object item)
        {
            string status = string.Empty;
            switch (Convert.ToString(item))
            {
                case "Active":
                    status = "Active";
                    break;

                case "Locked":
                    status = "Locked";
                    break;

                case "Replicating":
                    status = "Replicating";
                    break;

                case "Maintenance":
                    status = "Maintenance";
                    break;
            }
            return status;
        }

        protected string GetType(object type)
        {
            int typeid = Convert.ToInt32(type);
            string stringtype = string.Empty;
            switch (typeid)
            {
                case 1:
                    stringtype = "Application Group";
                    break;

                case 2:
                    stringtype = "DB Group";
                    break;

                case 3:
                    stringtype = "Virtual Group";
                    break;

                case 4:
                    stringtype = "Exchange Server";
                    break;
            }
            return stringtype;
        }

        public override void PrepareView()
        {
            if (IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);
            }
            Utility.SelectMenu(Master, "Module3");
            GetGroup();
        }

        public void GetGroup()
        {
            //Utility.PopulateGroupByParentAndRole(lvGroupList,LoggedInUserCompanyId, IsUserSuperAdmin,
            //                                               LoggedInUserCompany.IsParent);

            lvGroupList.DataSource = null;
            IList<Group> groupList;
            if (IsSuperAdmin)
            {
                groupList = facade.GetAllGroups();
            }
            else
            {
                groupList = facade.GetAllGroupsByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin, LoggedInUserCompany.IsParent);
            }
            if (groupList == null) return;
            lvGroupList.DataSource = groupList;
            lvGroupList.DataBind();
        }

        // added by kiran
        public void GetGroup(string searchvalue)
        {
            IList<Group> groupList = facade.GetAllGroupsByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin, LoggedInUserCompany.IsParent);

            if (groupList != null && (searchvalue != null && searchvalue.Length > 0))
            {
                var result = (from grouplistitems in groupList
                              where grouplistitems.Name.ToLower().StartsWith(txtsearchvalue.Text.ToLower())
                              select grouplistitems).ToList();
                if (result != null)
                {
                    lvGroupList.DataSource = result;
                    lvGroupList.DataBind();
                }
            }
            else
            {
                if (groupList != null)
                {
                    lvGroupList.DataSource = groupList;
                    lvGroupList.DataBind();
                }
                else
                {
                    lvGroupList.DataSource = groupList;
                    lvGroupList.DataBind();
                    Response.Redirect(Constants.UrlConstants.Urls.Group.DbGroupList);
                }
            }
        }

        private void DeleteGlobalMirrorDetails(int groupId)
        {
            bool isDelete = facade.DeleteGlobalMirrorGroupById(groupId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvGroupList.EditIndex = -1;
                GetGroup();
                //lvGroupList.DataSource = facade.GetAllGroup();
                //lvGroupList.DataBind();
            }
        }

        private void DeleteEMCSRDFDetails(int groupId)
        {
            bool isDelete = facade.DeleteGroupById(groupId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvGroupList.EditIndex = -1;
                GetGroup();
                //lvGroupList.DataSource = facade.GetAllGroup();
                //lvGroupList.DataBind();
            }
        }

        private void DeleteOracleDataGuardDetails(int groupId)
        {
            bool isDelete = facade.DeleteGroupById(groupId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvGroupList.EditIndex = -1;
                GetGroup();
                //lvGroupList.DataSource = facade.GetAllGroup();
                //lvGroupList.DataBind();
            }
        }

        private void DeleteHitachiURDetails(int groupId)
        {
            bool isDelete = facade.DeleteHitachiGroupById(groupId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvGroupList.EditIndex = -1;
                GetGroup();
                //lvGroupList.DataSource = facade.GetAllGroup();
                //lvGroupList.DataBind();
            }
        }

        private void DeleteGroupDetails(int groupId)
        {
            bool isDelete = facade.DeleteGroupById(groupId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvGroupList.EditIndex = -1;
                GetGroup();
            }
        }

        protected void lvGroupList_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (lvGroupList.Items[e.ItemIndex].FindControl("ID")) as Label;
            var lblrecoveryType = (lvGroupList.Items[e.ItemIndex].FindControl("RECOVERY_TYPE")) as Label;
            int gId = Convert.ToInt32(lbl.Text);
            var groupDetails = facade.GetInfraObjectById(gId);
            var applicationDetail = facade.GetApplicationGroupByGroupId(gId);
            var workflowDetail = facade.GetAllWorkflowActionsByInfraobjectId(gId);
            if (applicationDetail != null || workflowDetail != null)
            {
                ErrorSuccessNotifier.AddSuccessMessage("Group is in use");
            }
            else
            {
                switch (lblrecoveryType.Text)
                {
                    case "GlobalMirror": DeleteGlobalMirrorDetails(gId);
                        break;

                    case "EMC SRDF": DeleteEMCSRDFDetails(gId);
                        break;

                    case "DataGuard": DeleteOracleDataGuardDetails(gId);
                        ActivityLogger.AddLog(LoggedInUserName, "Group", UserActionType.DeleteGroup, "The Group was deleted", LoggedInUserId);
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Group", TransactionType.Delete));
                        break;

                    case "HITACHI UR_OracleFullDB": DeleteHitachiURDetails(gId);
                        break;

                    case "HITACHI UR_OracleLogShipping": DeleteHitachiURDetails(gId);
                        break;

                    default: DeleteGroupDetails(gId);
                        break;
                }
                var groupDetailsByApplicationGroup = facade.GetGroupsByBusinessServiceId(groupDetails.BusinessServiceId);
                if (groupDetailsByApplicationGroup == null)
                {
                    facade.DeleteApplicationGroupById(groupDetails.BusinessServiceId);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
            //if (lbl != null && lblrecoveryType.Text == "GlobalMirror")
            //{
            //  //  int gId = Convert.ToInt32(lbl.Text);

            //    bool isDelete = facade.DeleteGlobalMirrorGroupById(gId);

            //    if (isDelete)
            //    {
            //        CurrentCacheManager.DataCache.RemoveItem(cacheKey);
            //        lvGroupList.EditIndex = -1;
            //        GetGroup();
            //        //lvGroupList.DataSource = facade.GetAllGroup();
            //        //lvGroupList.DataBind();
            //    }
            //}

            //else
            //{
            //  //  int gId = Convert.ToInt32(lbl.Text);

            //    bool isDelete = facade.DeleteGroupById(gId);

            //    if (isDelete)
            //    {
            //        CurrentCacheManager.DataCache.RemoveItem(cacheKey);
            //        lvGroupList.EditIndex = -1;
            //        GetGroup();
            //        //lvGroupList.DataSource = facade.GetAllGroup();
            //        //lvGroupList.DataBind();
            //    }
            //}
        }

        protected void lvGroupList_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentURL);
            var lbl1 = (lvGroupList.Items[e.NewEditIndex].FindControl("ID")) as Label;
            var lblSol = (lvGroupList.Items[e.NewEditIndex].FindControl("RECOVERY_TYPE")) as Label;
            if (lbl1 != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.GroupId,
                                                     lbl1.Text, Constants.UrlConstants.Params.SolutionType, lblSol.Text);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvGroupList_PreRender(object sender, EventArgs e)
        {
            //lvGroupList.DataSource = facade.GetAllGroup();
            //lvGroupList.DataBind();
            if (!IsPostBack)
            {
                GetGroup();
            }
            else
            {
                GetGroup(txtsearchvalue.Text);
            }
        }

        protected void lvGroupList_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            Label lbl = e.Item.FindControl("state") as Label;

            if (IsUserOperator)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }

            if (lbl.Text == "Replicating")
            {
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.ImageUrl = "../Images/icons/cross-circle_disable.png";
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            GetGroup(txtsearchvalue.Text);
        }
    }
}