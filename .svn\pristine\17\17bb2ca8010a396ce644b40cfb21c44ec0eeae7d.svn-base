﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class FastCopyMonitorBuilder : IEntityBuilder<FastCopyMonitor>
    {
        IList<FastCopyMonitor> IEntityBuilder<FastCopyMonitor>.BuildEntities(IDataReader reader)
        {
            var fastCopyMonitors = new List<FastCopyMonitor>();

            while (reader.Read())
            {
                fastCopyMonitors.Add(((IEntityBuilder<FastCopyMonitor>)this).BuildEntity(reader, new FastCopyMonitor()));
            }

            return (fastCopyMonitors.Count > 0) ? fastCopyMonitors : null;
        }

        FastCopyMonitor IEntityBuilder<FastCopyMonitor>.BuildEntity(IDataReader reader, FastCopyMonitor fastCopyMonitor)
        {
            //const int FLD_ID = 0;
            //const int FLD_JOBID = 1;
            //const int FLD_SOURCEIP = 2;
            //const int FLD_DESTINATIONIP = 3;
            //const int FLD_SOURCEPATH = 4;
            //const int FLD_DESTINATIONPATH = 5;
            //const int FLD_LASTFILENAME = 6;
            //const int FLD_LASTFILESIZE = 7;
            //const int FLD_TOTALFILESCOUNT = 8;
            //const int FLD_TOTALFILESIZE = 9;
            //const int FLD_LOCKFILESCOUNT = 10;
            //const int FLD_INCREMENTALFILESCOUNT = 11;
            //const int FLD_SKIPPEDFILESCOUNT = 12;
            //const int FLD_GROUPID = 13;
            //const int FLD_ISGROUP = 14;
            //const int FLD_STARTTIME = 15;
            //const int FLD_ENDTIME = 16;
            //const int FLD_CREATEDATE = 17;

            //fastCopyMonitor.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //fastCopyMonitor.JobId = reader.IsDBNull(FLD_JOBID) ? 0 : reader.GetInt32(FLD_JOBID);
            //fastCopyMonitor.SourceIP = reader.IsDBNull(FLD_SOURCEIP) ? string.Empty : reader.GetString(FLD_SOURCEIP);
            //fastCopyMonitor.DestinationIP = reader.IsDBNull(FLD_DESTINATIONIP) ? string.Empty : reader.GetString(FLD_DESTINATIONIP);
            //fastCopyMonitor.SourcePath = reader.IsDBNull(FLD_SOURCEPATH) ? string.Empty : reader.GetString(FLD_SOURCEPATH);
            //fastCopyMonitor.DestinationPath = reader.IsDBNull(FLD_DESTINATIONPATH) ? string.Empty : reader.GetString(FLD_DESTINATIONPATH);
            //fastCopyMonitor.LastFileName = reader.IsDBNull(FLD_LASTFILENAME) ? string.Empty : reader.GetString(FLD_LASTFILENAME);
            //fastCopyMonitor.LastFileSize = reader.IsDBNull(FLD_LASTFILESIZE) ? string.Empty : reader.GetString(FLD_LASTFILESIZE);
            //fastCopyMonitor.TotalFilesCount = reader.IsDBNull(FLD_TOTALFILESCOUNT) ? string.Empty : reader.GetString(FLD_TOTALFILESCOUNT);
            //fastCopyMonitor.TotalFilesSize = reader.IsDBNull(FLD_TOTALFILESIZE) ? string.Empty : reader.GetString(FLD_TOTALFILESIZE);
            //fastCopyMonitor.LockFilesCount = reader.IsDBNull(FLD_LOCKFILESCOUNT) ? string.Empty : reader.GetString(FLD_LOCKFILESCOUNT);
            //fastCopyMonitor.IncrementalFilesCount = reader.IsDBNull(FLD_INCREMENTALFILESCOUNT) ? string.Empty : reader.GetString(FLD_INCREMENTALFILESCOUNT);
            //fastCopyMonitor.SkippedFilesCount = reader.IsDBNull(FLD_SKIPPEDFILESCOUNT) ? string.Empty : reader.GetString(FLD_SKIPPEDFILESCOUNT);
            //fastCopyMonitor.GroupId = reader.IsDBNull(FLD_GROUPID) ? 0 : reader.GetInt32(FLD_GROUPID);
            //fastCopyMonitor.IsGroup = !reader.IsDBNull(FLD_ISGROUP) && reader.GetBoolean(FLD_ISGROUP);
            //fastCopyMonitor.StartTime = reader.IsDBNull(FLD_STARTTIME) ? string.Empty : reader.GetString(FLD_STARTTIME);
            //fastCopyMonitor.EndTime = reader.IsDBNull(FLD_ENDTIME) ? string.Empty : reader.GetString(FLD_ENDTIME);
            //fastCopyMonitor.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);

            //Fields in bcms_fastcopy_monitor_status table on 19/07/2013 : Id, JobId, SourceIP, DestinationIP, SourcePath, DestinationPath, LastFileName, LastFileSize, TotalFilesCount, TotalFilesSize, LockFilesCount, IncrementalFilesCount, SkippedFilesCount, GroupId, IsGroup, StartTime, EndTime, CreateDate, UpdateDate

            fastCopyMonitor.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            fastCopyMonitor.JobId = Convert.IsDBNull(reader["JobId"]) ? 0 : Convert.ToInt32(reader["JobId"]);
            fastCopyMonitor.SourceIP = Convert.IsDBNull(reader["SourceIP"])
                ? string.Empty
                : Convert.ToString(reader["SourceIP"]);
            fastCopyMonitor.DestinationIP = Convert.IsDBNull(reader["DestinationIP"])
                ? string.Empty
                : Convert.ToString(reader["DestinationIP"]);
            fastCopyMonitor.SourcePath = Convert.IsDBNull(reader["SourcePath"])
                ? string.Empty
                : Convert.ToString(reader["SourcePath"]);
            fastCopyMonitor.DestinationPath = Convert.IsDBNull(reader["DestinationPath"])
                ? string.Empty
                : Convert.ToString(reader["DestinationPath"]);
            fastCopyMonitor.LastFileName = Convert.IsDBNull(reader["LastFileName"])
                ? string.Empty
                : Convert.ToString(reader["LastFileName"]);
            fastCopyMonitor.LastFileSize = Convert.IsDBNull(reader["LastFileSize"])
                ? string.Empty
                : Convert.ToString(reader["LastFileSize"]);
            fastCopyMonitor.TotalFilesCount = Convert.IsDBNull(reader["TotalFilesCount"])
                ? string.Empty
                : Convert.ToString(reader["TotalFilesCount"]);
            fastCopyMonitor.TotalFilesSize = Convert.IsDBNull(reader["TotalFilesSize"])
                ? string.Empty
                : Convert.ToString(reader["TotalFilesSize"]);
            fastCopyMonitor.LockFilesCount = Convert.IsDBNull(reader["LockFilesCount"])
                ? string.Empty
                : Convert.ToString(reader["LockFilesCount"]);
            fastCopyMonitor.IncrementalFilesCount = Convert.IsDBNull(reader["IncrementalFilesCount"])
                ? string.Empty
                : Convert.ToString(reader["IncrementalFilesCount"]);
            fastCopyMonitor.SkippedFilesCount = Convert.IsDBNull(reader["SkippedFilesCount"])
                ? string.Empty
                : Convert.ToString(reader["SkippedFilesCount"]);
            fastCopyMonitor.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);

            if (!Convert.IsDBNull(reader["IsInfraObject"]))
                fastCopyMonitor.IsInfraObject = Convert.ToBoolean(reader["IsInfraObject"]);

            fastCopyMonitor.StartTime = Convert.IsDBNull(reader["StartTime"])
                ? string.Empty
                : Convert.ToString(reader["StartTime"]);
            fastCopyMonitor.EndTime = Convert.IsDBNull(reader["EndTime"])
                ? string.Empty
                : Convert.ToString(reader["EndTime"]);
            fastCopyMonitor.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);

            return fastCopyMonitor;
        }
    }
}