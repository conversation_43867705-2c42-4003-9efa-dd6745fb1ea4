﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="CompanyProfileList.aspx.cs" Inherits="CP.UI.CompanyProfileList"
    Title="Continuity Patrol :: Profile-Company Profile List" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <asp:HiddenField ID="hdGuid" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="upnlProfileOverview" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

               
                <h3>
                    <span class="profile-company-icon"></span>
                    Profile List</h3>
               
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-xs-5 col-md-push-7 text-right">

                                <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Company Name"></asp:TextBox>
                                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Width="20%" Text="Search" OnClick="BtnSearchClick" />
                            </div>
                        </div>
                        <hr />
                        <asp:ListView ID="lvProfile" runat="server" OnItemDeleting="LvProfileItemDeleting"
                            OnItemEditing="LvProfileItemEditing" OnPreRender="LvProfilePreRender"
                            OnItemDataBound="LvProfileItemDataBound"
                            OnPagePropertiesChanging="lvProfile_PagePropertiesChanging">
                            <LayoutTemplate>
                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed;">
                                    <thead>
                                        <tr>
                                            <th class="text-center" style="width: 4%;">
                                                <span>
                                                    <img src="../Images/profile-company-icon-white.png" /></span>
                                            </th>
                                            <th style="width: 32%;">Company Name
                                            </th>
                                            <th style="width: 15%;">Phone
                                            </th>
                                            <th style="width: 20%;">Email
                                            </th>
                                            <th style="width: 15%;">WebAddress
                                            </th>
                                            <th class="text-center" style="width: 8%;">Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <EmptyDataTemplate>
                                <div class="">
                                    <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                                </div>
                            </EmptyDataTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td class="th table-check-cell text-center" style="width: 4%;">
                                        <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td style="width: 32%;" class="tdword-wrap">
                                        <asp:Label ID="lblCompanyName" runat="server" Text='<%# Eval("Name") %>' ToolTip='<%# Eval("Name") %>'/>
                                    </td>
                                    <td style="width: 15%;">
                                        <asp:Label ID="lblPhone" runat="server" Text='<%# Eval("CompanyInformation.Phone") %>' />
                                    </td>
                                    <td style="width: 20%;">
                                        <asp:Label ID="lblEmail" runat="server" Text='<%# Eval("CompanyInformation.Email") %>' />
                                    </td>
                                    <td style="width: 15%;">
                                        <asp:Label ID="lblWebAddress" runat="server" Text='<%# Eval("CompanyInformation.WebAddress") %>' />
                                    </td>
                                    <td class="text-center" style="width: 8%;">
                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                            ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                    </td>
                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                        ConfirmText='<%# CheckWithLoginCompeny(Eval("Name")) %>' OnClientCancel="CancelClick">
                                    </TK1:ConfirmButtonExtender>
                                </tr>
                            </ItemTemplate>
                        </asp:ListView>
                        <div class="row">
                            <div class="col-xs-6">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvProfile">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-xs-6 text-right">
                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvProfile" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                            NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>