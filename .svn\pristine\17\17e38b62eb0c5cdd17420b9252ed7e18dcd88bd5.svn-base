namespace CP.UI.Report.TelerikReports
{
    using System;
    using System.ComponentModel;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;
    using System.Collections.Generic;
    using System.Linq;
    using System.Web;
    using System.Web.UI;
    using CP.Common.DatabaseEntity;
    using CP.ExceptionHandler;
    using CP.Helper;
    using SpreadsheetGear;
    using System.Web.UI.WebControls;
    using System.Data;
    using Gios.Pdf;
    using System.Collections;
    using System.Globalization;
    using System.Configuration;
    using CP.UI;
    using CP.Common.Shared;
    using CP.UI.Component;
    using System.IO;
    using log4net;

    /// <summary>
    /// Summary description for ImportCMDBReport.
    /// </summary>
    public partial class ImportCMDBReport : Telerik.Reporting.Report
    {
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();

        public ImportCMDBReport()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }

        private void showtable()
        {
            var dataTable = new DataTable();

            var dataTable1 = new DataTable();
            string iExcelName = this.ReportParameters["iExcelName"].Value.ToString();
            string iTimeStamp = this.ReportParameters["iTimeStamp"].Value.ToString();
            string iTimeStampText = this.ReportParameters["iTimeStampText"].Value.ToString();

            
            var importdbcountlist = Facade.GetCMDBfilecountbyID(Convert.ToInt32(iTimeStamp));
            if (importdbcountlist != null)
            {
                //DataRow dr = dataTable.NewRow();
                dataTable.Columns.Add("Sr.No.");
                dataTable.Columns.Add("CMDBFileName");
                dataTable.Columns.Add("GenerationTimestamp");
                dataTable.Columns.Add("TotalCMDBRecords");
                dataTable.Columns.Add("SuccessfullyImportedRecords");
                dataTable.Columns.Add("FailedRecords");
                dataTable.Columns.Add("Server");
                dataTable.Columns.Add("IPAddress");
                dataTable.Columns.Add("FailedReason");                             


                var importcmdfilelist = Facade.GetCMDBfiletimebyID(Convert.ToInt32(iTimeStamp));
                int i = 0;
                if (importcmdfilelist != null)//&& importcmdfilelist.Count > 0
                {
                    foreach (var cmdbfiles in importcmdfilelist)
                    {
                        DataRow dr = dataTable.NewRow();

                        dr["Sr.No."] = i.ToString();
                        dr["CMDBFileName"] = iExcelName;
                        dr["GenerationTimestamp"] = iTimeStampText;
                        dr["TotalCMDBRecords"] = importdbcountlist.TotalCount != 0 ? importdbcountlist.TotalCount.ToString() : "0";
                        dr["SuccessfullyImportedRecords"] = importdbcountlist.ConfiguredCount != 0 ? importdbcountlist.ConfiguredCount.ToString() : "0";
                        dr["FailedRecords"] = importdbcountlist.FailedCount != 0 ? importdbcountlist.FailedCount.ToString() : "0";

                        dr["Server"] = cmdbfiles.ServerName != null ? cmdbfiles.ServerName : "N/A";
                        dr["IPAddress"] = cmdbfiles.IPAddress != null ? cmdbfiles.IPAddress : "N/A";
                        dr["FailedReason"] = cmdbfiles.FailedReason != null ? cmdbfiles.FailedReason : "N/A";
                        i++;
                        dataTable.Rows.Add(dr);
                        //dataTable.ImportRow(dr);
                    }                    
                }                
            }
            this.DataSource = dataTable; 
        }

        private void ImportCMDBReport_NeedDataSource(object sender, EventArgs e)
        {
            showtable();
        }
    }
}