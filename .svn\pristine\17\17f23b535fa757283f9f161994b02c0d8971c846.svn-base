﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.CacheController;
using CP.Common.BusinessEntity;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Controls;
using System.Web;

namespace CP.UI
{
    public partial class InfraObjectsList : InfraObjectsBasePage
    {
        #region Variable

        public static string CurrentURL = Constants.UrlConstants.Urls.Group.InfraObjectsConfig;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.ExecutionAccessUser)
                {
                    return Constants.UrlConstants.Urls.Group.InfraObjectsList;
                }
                return string.Empty;
            }
        }

        private IFacade facade = new Facade();
        private static CacheManager _cacheManager;
        private static string cacheKey = "Group.InfraObjectsListIntoCache";
        //private static int applicationId;

        #endregion Variable

        private static CacheManager CurrentCacheManager
        {
            get
            {
                if (_cacheManager == null)
                {
                    _cacheManager = new CacheManager();
                }

                return _cacheManager;
            }
        }

        public string GetRecoveryType(object type, object typeid)
        {
            var recoveryType = string.Empty;
            var recovery = Convert.ToInt32(type);
            var Type = Convert.ToInt32(typeid);
            RecoveryTypeName Rtype = new RecoveryTypeName();
            var RecoveryType = Rtype.RecoveryTypebyId(recovery, Type);
            if (RecoveryType == "")
            {
                RecoveryType = "No Recovery";
            }
            return RecoveryType;
        }

        public object IsEnable(object st)
        {
            int i = Convert.ToInt32(st);
            string typeRet = i == 1 ? "~/Images/Lock.png" : "~/Images/unlock.png";
            return typeRet;
        }

        protected bool DisableEdit(object item, object Iid)
        {
            //string infraObjectsStatus = Convert.ToString(item);
            ////Comment and Modify By JP
            ////return infraObjectsStatus == "Replicating" ? false : true;

            //return infraObjectsStatus == "Maintenance" ? true : false;

            int id = Convert.ToInt32(Iid);

            var GetInfraStatus = Facade.GetInfraObjectById(id);

            if (GetInfraStatus != null)
            {
                string infraObjectsStatus = GetInfraStatus.State;

                if (infraObjectsStatus == "Maintenance")
                {
                    if (GetInfraStatus.DROperationStatus == 1 || GetInfraStatus.DROperationStatus == 4 || GetInfraStatus.DROperationStatus == 13)
                    {
                        return false;
                    }
                    else
                    {
                        return true;
                    }
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }

        protected string GetStatusType(object item)
        {
            string infraObjectsStatus = string.Empty;
            switch (Convert.ToString(item))
            {
                case "Active":
                    infraObjectsStatus = "../Images/icons/tick-circle.png";
                    break;

                case "Locked":
                    infraObjectsStatus = "../Images/icons/Lock.png";
                    break;

                case "Replicating":
                    infraObjectsStatus = "../Images/running-animate.gif";

                    break;

                case "Maintenance":
                    infraObjectsStatus = "../Images/icons/maintenance.png";
                    break;
            }
            return infraObjectsStatus;
        }

        protected string GetRowStyle(object item)
        {
            string status = string.Empty;
            switch (Convert.ToString(item))
            {
                case "Active":
                    status = "Active";
                    break;

                case "Locked":
                    status = "Locked";
                    break;

                case "Replicating":
                    status = "Replicating";
                    break;

                case "Maintenance":
                    status = "Maintenance";
                    break;
            }
            return status;
        }

        protected string GetType(object type)
        {
            int typeid = Convert.ToInt32(type);
            string stringtype = string.Empty;
            switch (typeid)
            {
                case 1:
                    stringtype = "Application Group";
                    break;

                case 2:
                    stringtype = "DB Group";
                    break;

                case 3:
                    stringtype = "Virtual Group";
                    break;

                case 4:
                    stringtype = "Exchange Server";
                    break;
            }
            return stringtype;
        }

        public override void PrepareView()
        {
            if (IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            //if (IsUserManager)
            //{
            //    Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);

            //}
            //}
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }

            Utility.SelectMenu(Master, "Module3");
            GetInfraObjects();
            setListViewPage();

        }

        public void GetInfraObjects()
        {
            lvInfraObjectsList.DataSource = null;
            IList<InfraObject> infraObjectsList;
            if (IsSuperAdmin)
            {
                infraObjectsList = facade.GetAllInfraObject();
            }
            else
            {
                infraObjectsList = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
                //infraObjectsList = facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin, LoggedInUserCompany.IsParent);
            }
            if (infraObjectsList == null) return;
            if (infraObjectsList.Count > 0)
            {
                lvInfraObjectsList.DataSource = infraObjectsList;
                lvInfraObjectsList.DataBind();
                setListViewPage();
            }
        }
        /// <summary>
        /// if deleting or updating the List of Infra Objects the page will get postback and then Listview is display the same page
        /// where it is deleted or updated. - Goraknath Khule.
        /// </summary>
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageInfraObjectList"]) != -1) && Session["CurrentPageInfraObjectList"] != null)
            {
                if (Convert.ToInt32(Session["CurrentPageInfraObjectList"]) == dataPager1.TotalRowCount)
                {
                    Session["CurrentPageInfraObjectList"] = Convert.ToInt32(Session["CurrentPageInfraObjectList"]) - dataPager1.MaximumRows;
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageInfraObjectList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageInfraObjectList"] = -1;

            }
        }


        public void GetInfraObjects(string searchvalue)
        {
            IList<InfraObject> infraObjectsList = facade.GetAllInfraObjectByUserCompanyIdAndRoleAndIsSuperAdmin(LoggedInUserCompanyId, IsUserSuperAdmin, LoggedInUserCompany.IsParent);
            searchvalue = searchvalue.Trim();
            if (infraObjectsList != null && (searchvalue != null && searchvalue.Length > 0))
            {
                var result = (from infraObjectslistitems in infraObjectsList
                              where infraObjectslistitems.Name.ToLower().Contains(txtsearchvalue.Text.Trim().ToLower())
                              select infraObjectslistitems).ToList();
                if (result != null)
                {
                    lvInfraObjectsList.DataSource = result;
                    lvInfraObjectsList.DataBind();
                }
            }
            else
            {
                if (infraObjectsList != null)
                {
                    lvInfraObjectsList.DataSource = infraObjectsList;
                    lvInfraObjectsList.DataBind();
                    setListViewPage();
                }
                else
                {
                    lvInfraObjectsList.DataSource = infraObjectsList;
                    lvInfraObjectsList.DataBind();
                    setListViewPage();
                    Response.Redirect(Constants.UrlConstants.Urls.Group.InfraObjectsList);
                }
            }
        }

        private void DeleteGlobalMirrorDetails(int infraObjectsId)
        {
            bool isDelete = facade.DeleteGlobalMirrorInfraObjectById(infraObjectsId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvInfraObjectsList.EditIndex = -1;
                GetInfraObjects();
            }
        }

        private void DeleteEMCSRDFDetails(int infraObjectsId)
        {
            bool isDelete = facade.DeleteInfraObjectById(infraObjectsId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvInfraObjectsList.EditIndex = -1;
                GetInfraObjects();
            }
        }

        private void DeleteOracleDataGuardDetails(int infraObjectsId)
        {
            bool isDelete = facade.DeleteInfraObjectById(infraObjectsId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvInfraObjectsList.EditIndex = -1;
                GetInfraObjects();
            }
        }

        private void DeleteHitachiURDetails(int infraObjectsId)
        {
            bool isDelete = facade.DeleteHitachiInfraObjectById(infraObjectsId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvInfraObjectsList.EditIndex = -1;
                GetInfraObjects();
            }
        }

        private void DeleteInfraObjectsDetails(int infraObjectsId)
        {
            bool isDelete = facade.DeleteInfraObjectById(infraObjectsId);

            if (isDelete)
            {
                CurrentCacheManager.DataCache.RemoveItem(cacheKey);
                lvInfraObjectsList.EditIndex = -1;
                GetInfraObjects();
            }
        }

        protected void lvInfraObjectsList_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            Session["CurrentPageInfraObjectList"] = (dataPager1.StartRowIndex);
            var lbl = (lvInfraObjectsList.Items[e.ItemIndex].FindControl("ID")) as Label;
            var lblrecoveryType = (lvInfraObjectsList.Items[e.ItemIndex].FindControl("RECOVERY_TYPE")) as Label;
            var lblName = (lvInfraObjectsList.Items[e.ItemIndex].FindControl("Name")) as Label;

            if (lbl != null && lblName != null && ValidateRequest("InfraObject Delete", UserActionType.DeleteInfraObjects))
            {
                int gId = Convert.ToInt32(lbl.Text);
                var infraObjectsDetails = facade.GetInfraObjectById(gId);
                //var applicationDetail = facade.GetApplcationGroupByGroupId(gId);
                var workflowDetail = facade.GetAllWorkflowActionsByInfraobjectId(gId);
                if (workflowDetail != null)//applicationDetail != null ||
                {
                    ErrorSuccessNotifier.AddSuccessMessage("InfraObject " + infraObjectsDetails.Name + " is in use");
                }
                else
                {
                    switch (lblrecoveryType.Text)
                    {
                        case "GlobalMirror": DeleteGlobalMirrorDetails(gId);
                            break;

                        case "EMC SRDF": DeleteEMCSRDFDetails(gId);
                            break;

                        case "DataGuard": DeleteOracleDataGuardDetails(gId);
                            //ActivityLogger.AddLog(LoggedInUserName, "InfraObject", UserActionType.DeleteInfraObjects, "The InfraObject was deleted", LoggedInUserId);
                            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "InfraObject", UserActionType.DeleteInfraObjects, "The InfraObject was deleted", LoggedInUserId);
                           
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("InfraObject" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                            break;

                        case "HITACHI UR_OracleFullDB": DeleteHitachiURDetails(gId);
                            break;

                        case "HITACHI UR_OracleLogShipping": DeleteHitachiURDetails(gId);
                            break;

                        default: DeleteInfraObjectsDetails(gId);
                            Session["CurrentPageInfraObjectList"] = (dataPager1.StartRowIndex);
                            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "InfraObject", UserActionType.DeleteInfraObjects, "The InfraObject was deleted", LoggedInUserId);
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("InfraObject" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                            break;
                    }
                    //var infraObjectsDetailsByApplicationinfraObjects = facade.GetInfraObjectssByBusinessServiceId(infraObjectsDetails.BusinessServiceId);
                    //if (infraObjectsDetailsByApplicationinfraObjects==null)
                    //{
                    //    facade.DeleteApplicationInfraObjectsById(infraObjectsDetails.BusinessServiceId);
                    //}
                }
                if (ReturnUrl.IsNotNullOrEmpty())
                {
                    Helper.Url.Redirect(new SecureUrl(ReturnUrl));
                }
            }
        }

        protected void lvInfraObjectsList_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageInfraObjectList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);
            var lbl1 = (lvInfraObjectsList.Items[e.NewEditIndex].FindControl("ID")) as Label;
            var lblSol = (lvInfraObjectsList.Items[e.NewEditIndex].FindControl("RECOVERY_TYPE")) as Label;
            var lblApplicationName = (lvInfraObjectsList.Items[e.NewEditIndex].FindControl("NAME")) as Label;
            ActivityLogger.AddLog(LoggedInUserName, "InfraObject", UserActionType.UpdateInfraObjects, "InfraObject '" + lblApplicationName.Text + "' Opened as Editing Mode.", LoggedInUserId);


            if (lbl1 != null && ValidateRequest("InfraObject Edit", UserActionType.UpdateInfraObjects))
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.InfraObjectId, lbl1.Text);
                Helper.Url.Redirect(secureUrl);
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.InfraObjectId, lbl1.Text, Constants.UrlConstants.Params.SolutionType, lblSol.Text);
            }
        }

        protected void lvInfraObjectsList_PreRender(object sender, EventArgs e)
        {
            //if (!IsPostBack)
            //{
            //    GetInfraObjects();
            //}

            //else
            //{
            //    GetInfraObjects(txtsearchvalue.Text);
            //}

            if (txtsearchvalue.Text != "")
            {
                GetInfraObjects(txtsearchvalue.Text);
            }
            else
            {
                GetInfraObjects();
            }
        }

        protected void lvInfraObjectsList_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;


            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                Label BsServiceName = (Label)e.Item.FindControl("lblBsServiceName");
                Label BsFunctionName = (Label)e.Item.FindControl("lblBsFunctionName");

                ListViewDataItem dataItem = (ListViewDataItem)e.Item;
                InfraObject rowView = dataItem.DataItem as InfraObject;

                int _bsnsserviceId = rowView.BusinessServiceId;
                var Getbssrvc = Facade.GetBusinessServiceById(_bsnsserviceId);
                if (Getbssrvc != null)
                {
                    BsServiceName.Text = Getbssrvc.Name;
                }

                else
                {
                    BsServiceName.Text = "NA";
                }


                int _bsnsfunctionId = rowView.BusinessFunctionId;
                var Getbsfunction = Facade.GetBusinessFunctionById(_bsnsfunctionId);
                if (Getbsfunction != null)
                {

                    BsFunctionName.Text = Getbsfunction.Name;
                }
                else
                {
                    BsFunctionName.Text = "NA";
                }

                if (rowView.State == "Maintenance")
                {
                    if (rowView.DROperationStatus == 1)
                    {
                        edit.ToolTip = "SwitchOver Workflow Started,Unable To Edit";
                        edit.ImageUrl = "../images/icons/pencil_disable.png";

                        delete.Enabled = false;
                        delete.ImageUrl = "../Images/icons/cross-circle_disable.png";
                        delete.ToolTip = "SwitchOver Workflow Started,Unable To Delete";
                    }
                    else if (rowView.DROperationStatus == 4)
                    {
                        edit.ToolTip = "SwitchBack Workflow Started ,Unable To Edit";
                        edit.ImageUrl = "../images/icons/pencil_disable.png";

                        delete.Enabled = false;
                        delete.ImageUrl = "../Images/icons/cross-circle_disable.png";
                        delete.ToolTip = "SwitchBack Workflow Started ,Unable To Delete";
                    }
                    else if (rowView.DROperationStatus == 13)
                    {
                        edit.ToolTip = "Custom Workflow Started ,Unable To Edit";
                        edit.ImageUrl = "../images/icons/pencil_disable.png";

                        delete.Enabled = false;
                        delete.ImageUrl = "../Images/icons/cross-circle_disable.png";
                        delete.ToolTip = "Custom Workflow Started ,Unable To Delete";
                    }
                    else
                    {
                        edit.ToolTip = "edit";
                    }
                }

            }

            Label lbl = e.Item.FindControl("state") as Label;

            if (IsUserOperator || IsUserManager || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }

            if (lbl.Text == "Replicating")
            {
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.ImageUrl = "../Images/icons/cross-circle_disable.png";
            }
            if (lbl.Text == "Active")
            {
                edit.ToolTip = "You must put InfraObject in Maintainence mode for enable editing";
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.ImageUrl = "../Images/icons/cross-circle_disable.png";
                delete.ToolTip = "You must put InfraObject in Maintainence mode for enable deleting";
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            GetInfraObjects(txtsearchvalue.Text);
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

    }
}