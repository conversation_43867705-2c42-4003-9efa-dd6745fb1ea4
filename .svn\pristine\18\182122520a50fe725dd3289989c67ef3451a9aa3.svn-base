﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;

namespace CP.UI.Code.Replication.Component
{
    public class RoboCopyComponent : IComponentInfo
    {

        private readonly IFacade _facade = new Facade();

        private ComponentInfo _componentInfo;

        public ComponentInfo CurrentComponent
        {
            get { return _componentInfo ?? (_componentInfo = new ComponentInfo()); }
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId)
        {
            _componentInfo = null;

            //var robocopy = _facade.GetRoboCopyLogsByInfrad(infraObjectId);

            var robocopy1 = _facade.GetRoboLogsByInfraObjId(infraObjectId);

            if (robocopy1 != null && robocopy1.Count > 0)
            {

                var robocopy = robocopy1.FirstOrDefault();

                // var robocopy = robocopy1.FirstOrDefault();


                if (robocopy != null)
                {
                    var PR_ServerID = _facade.GetInfraObjectById(infraObjectId);
                    var prServer = _facade.GetServerById(PR_ServerID.PRServerId);

                    var DR_ServerID = _facade.GetInfraObjectById(infraObjectId);
                    var drServer = _facade.GetServerById(DR_ServerID.DRServerId);
                    GetServerInformation(PR_ServerID.PRServerId, true);

                    GetServerInformation(DR_ServerID.DRServerId, false);


                    CurrentComponent.SourcePath = robocopy.SourcePath;
                    CurrentComponent.DestinationPath = robocopy.DestinationPath;


                }
            }
            else
            {
                BindNullServerComponents(true);

                BindNullServerComponents(false);

                CurrentComponent.SourcePath = "N/A";
                CurrentComponent.DestinationPath = "N/A";

            }

            return CurrentComponent;
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId, int mailBoxId, string mailboxname)
        {
            _componentInfo = null;

            return CurrentComponent;
        }

        private void GetServerInformation(int serverId, bool isPrimary)
        {
            try
            {
                var server = _facade.GetServerById(serverId);

                if (server != null)
                {
                    BindServerComponents(server, isPrimary);
                }
                else
                {
                    BindNullServerComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullServerComponents(isPrimary);
            }
        }

        private void BindNullServerComponents(bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = "N/A";
                CurrentComponent.PRServerIP = "N/A";
                CurrentComponent.PRServerOSType = "N/A";
                CurrentComponent.PRServerStatus = "Down";
            }
            else
            {
                CurrentComponent.DRServerName = "N/A";
                CurrentComponent.DRServerIP = "N/A";
                CurrentComponent.DRServerOSType = "N/A";
                CurrentComponent.DRServerStatus = "Down";
            }
        }

        private void BindServerComponents(Server server, bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = server.Name;
                CurrentComponent.PRServerIP = server.IPAddress;

                CurrentComponent.PRServerHostName = server.HostName;
                //CurrentComponent.PRServerOSType = server.OSType;
                //CurrentComponent.PRServerStatus = server.Status.ToString();
            }
            else
            {
                CurrentComponent.DRServerName = server.Name;
                CurrentComponent.DRServerIP = server.IPAddress;

                CurrentComponent.DRServerHostName = server.HostName;

                //CurrentComponent.DRServerOSType = server.OSType;
                //CurrentComponent.DRServerStatus = server.Status.ToString();
            }
        }

    }
}