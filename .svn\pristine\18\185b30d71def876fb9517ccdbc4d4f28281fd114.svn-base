﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "JobTypeReplicationType", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class JobTypeReplicationType : BaseEntity
    {
        #region Property

        [DataMember]
        public int JobTypeId { get; set; }

        [DataMember]
        public int ReplicationId { get; set; }

        #endregion Property
    }
}