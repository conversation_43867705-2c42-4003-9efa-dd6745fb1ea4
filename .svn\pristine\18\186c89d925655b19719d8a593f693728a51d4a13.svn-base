﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;
using System;

namespace CP.DataAccess
{
    public interface IIncidentManagementDataAccess
    {
        IList<Incident> GetByLastIncidentId(int incidentid);

        IList<Incident> GetByUserIncidentId(int userid);

        IList<Incident> GetAllByGroupId(int id);

        IList<Incident> GetByDate(int groupId, string startDate, string endDate);

        IList<Incident> GetAll();

        Int32 GetDRstatusbyInfraid(int infraid);

        IList<IncidentCIOMontly> GetIncidentMontly();

        int GetServiceImpactByIncidentId(int GroupServiceID);
    }
}