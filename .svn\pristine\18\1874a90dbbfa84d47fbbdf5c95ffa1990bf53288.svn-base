﻿var globalstring = null;
var JsonForFullView = null;

// Start =====================

function treeShow(finalJsonString, height) {
    $('#body').html("");
    $("#body").append('<a id="btnReset" class="reset-icon margin-top absoluteWrap" title="reset to initial zoom position" style="display: inline-block; "> </a>');
    i = 0, root;
    var duration = 750;
    var viewerWidth = "100%";
    var viewerHeight = height;

    var JsonObj = eval('(' + finalJsonString + ')');
    JsonForFullView = JsonObj;
    var root = JsonObj;


    function zoom() {
        vis.attr("transform", "translate(" + d3.event.translate + ")scale(" + d3.event.scale + ")");
    }


    function resetZoom() {
        d3.select("#body").select("svg").select("g")
       .transition().duration(750)
       .attr("transform", "translate(30,30)scale(0.85)");
    };
    d3.select("#btnReset").on("click", resetZoom);

    var zoomListener = d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom);

    var tree = d3.layout.tree()
    .size([viewerHeight, viewerWidth]);

    var diagonal = d3.svg.diagonal()
    .projection(function (d) { return [d.y, d.x]; });

    var vis = d3.select("#body").append("svg:svg")
    .attr("width", viewerWidth)
    .attr("height", viewerHeight)
    .call(d3.behavior.zoom().scaleExtent([0.1, 3]).on("zoom", zoom)).on("dblclick.zoom", null)
    .append("g")
    .attr("transform", "translate(30,30)scale(0.85)");

    root.x0 = viewerHeight / 2;
    root.y0 = 0;

    function toggleAll(d) {
        if (d.children) {
            d.children.forEach(toggleAll);
            toggle(d);
        }
    }
  
    root.children.forEach(toggleAll);
    update(root);

   
    //d3.select("#btnReset").attr("style", "display:inline-block");

 
    function update(source) {
        var duration = d3.event && d3.event.altKey ? 5000 : 500;

      
        var nodes = tree.nodes(root).reverse();
 
        nodes.forEach(function (d) { d.y = d.depth * 220; });

     
        var node = vis.selectAll("g.node")
        .data(nodes, function (d) { return d.id || (d.id = ++i); });

       
        var nodeEnter = node.enter().append("svg:g")
        .attr("class", "node")
        .attr("transform", function (d) { return "translate(" + source.y0 + "," + source.x0 + ")"; })
        .on("click", function (d) { toggle(d); update(d); });

        nodeEnter.append("svg:circle")
        .attr("r", 1e-6)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeEnter.append("svg:text")
        .attr("x", function (d) { return d.children || d._children ? 15 : 10; })
        .attr("dy", function (d) { return d.children || d._children ? "-1em" : ".35em"; })
        .attr("text-anchor", function (d) { return d.children || d._children ? "end" : "start"; })
        .text(function (d) { return d.name; })
        .style("fill-opacity", 1e-6);

      
        //nodeEnter.append("svg:image")
        //        .attr("xlink:href", function (d) { return d.logo; })
        //        .attr("x", function (d) { return d.children || d._children ? -8 : -8; })
        //        .attr("y", function (d) { return d.children || d._children ? "-0.6em" : "-0.6em"; })
        //        .attr("height", function (d) { return d.logoheight || 16; })
        //        .attr("width", function (d) { return d.logowidth || 14; });

        nodeEnter.append("svg:image")
                .attr("xlink:href", function (d) { return d.logo; })
                .attr("x", function (d) {
                    if ((d.logo == "../Images/icons/serverIP-icon.png"))
                        return d.children || d._children ? -6 : -6;
                    else
                        return d.children || d._children ? -8 : -8;
                })
                .attr("y", function (d) {
                    if ((d.logo == "../Images/icons/serverIP-icon.png"))
                        return d.children || d._children ? "-0.7em" : "-0.7em";
                    else
                        return d.children || d._children ? "-0.6em" : "-0.6em";
                })
                .attr("height", function (d) { return d.logoheight || 16; })
                .attr("width", function (d) {
                    if ((d.logo == "../Images/icons/serverIP-icon.png"))
                        return d.logowidth || 12;
                    else if ((d.logo == "../Images/icons/app-icon.png"))
                        return d.logowidth || 15;
                    else
                        return d.logowidth || 16;
                });

       
        var nodeUpdate = node.transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + d.y + "," + d.x + ")"; });

        nodeUpdate.select("circle")
        .attr("r", 4.5)
        .style("fill", function (d) { return d._children ? "lightsteelblue" : "#fff"; });

        nodeUpdate.select("text")
        .style("fill-opacity", 1);

      
        var nodeExit = node.exit().transition()
        .duration(duration)
        .attr("transform", function (d) { return "translate(" + source.y + "," + source.x + ")"; })
        .remove();

        nodeExit.select("circle")
        .attr("r", 1e-6);

        nodeExit.select("text")
        .style("fill-opacity", 1e-6);

     
        var link = vis.selectAll("path.link")
        .data(tree.links(nodes), function (d) { return d.target.id; });

      
        link.enter().insert("svg:path", "g")
        .attr("class", "link")
        .style("stroke", function (d) { return d.target.level; })
        .attr("d", function (d) {
            var o = { x: source.x0, y: source.y0 };
            return diagonal({ source: o, target: o });
        })
        .transition()
        .duration(duration)
        .attr("d", diagonal);

      
        link.transition()
        .duration(duration)
        .attr("d", diagonal);

      
        link.exit().transition()
        .duration(duration)
        .attr("d", function (d) {
            var o = { x: source.x, y: source.y };
            return diagonal({ source: o, target: o });
        })
        .remove();

       
        nodes.forEach(function (d) {
            d.x0 = d.x;
            d.y0 = d.y;
        });

    }

  
    function toggle(d) {
        if (d.children) {
            d._children = d.children;
            d.children = null;
        } else {
            d.children = d._children;
            d._children = null;
        }
     
        if (d.parent) {
            if (d.children) {
                d.parent.children.forEach(function (element) {
                    if (d != element) {
                        collapse(element);
                    }
                });
            }
        }
    }

  
    function collapse(d) {
        if (d.children) {
            d._children = d.children;
            d._children.forEach(collapse);
            d.children = null;
        }
    }
}
// Ends ======================

// Start =====================

function isNumberKey(evt) {
    var charCode = (evt.which) ? evt.which : event.keyCode
    if (charCode != 46 && charCode > 31
      && (charCode < 48 || charCode > 57))
        return false;
    return true;
}
// Ends ======================