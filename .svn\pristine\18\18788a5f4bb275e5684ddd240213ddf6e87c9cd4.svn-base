﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess.BusinessProfileTimeInterval
{
    internal sealed class BIAProfileTimeIntervalsBuilder : IEntityBuilder<BIAProfileTimeInterval>
    {
        IList<BIAProfileTimeInterval> IEntityBuilder<BIAProfileTimeInterval>.BuildEntities(IDataReader reader)
        {
            var businessTimeInterval = new List<BIAProfileTimeInterval>();

            while (reader.Read())
            {
                businessTimeInterval.Add(((IEntityBuilder<BIAProfileTimeInterval>)this).BuildEntity(reader, new BIAProfileTimeInterval()));
            }

            return (businessTimeInterval.Count > 0) ? businessTimeInterval : null;
        }

        BIAProfileTimeInterval IEntityBuilder<BIAProfileTimeInterval>.BuildEntity(IDataReader reader, BIAProfileTimeInterval businesstimeIntervalBIA)
        {
            businesstimeIntervalBIA.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

            businesstimeIntervalBIA.PID = Convert.IsDBNull(reader["ProfileID"]) ? 0 : Convert.ToInt32(reader["ProfileID"]);
            businesstimeIntervalBIA.TID = Convert.IsDBNull(reader["TimeIntervalID"]) ? 0 : Convert.ToInt32(reader["TimeIntervalID"]);
           
            //businesstimeIntervalBIA.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            businesstimeIntervalBIA.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            businesstimeIntervalBIA.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            businesstimeIntervalBIA.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            businesstimeIntervalBIA.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());
            return businesstimeIntervalBIA;
        }

    }
}
