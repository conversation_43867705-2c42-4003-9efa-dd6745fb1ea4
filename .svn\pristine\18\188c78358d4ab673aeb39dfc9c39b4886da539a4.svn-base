﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region SSOConfigurationDataAccess
    public interface ISSOConfigurationDataAccess
    {
        SSOConfiguration Add(SSOConfiguration ssoconfiguration);

        SSOConfiguration Update(SSOConfiguration ssoconfiguration);

        SSOConfiguration GetById(int id);

        IList<SSOConfiguration> GetAll();

        IList<SSOConfiguration> GetBySSOTypeId(int issotypeid);

        bool DeleteById(int iId);

        bool IsExistBySSOName(string name);
    }
    #endregion
}
