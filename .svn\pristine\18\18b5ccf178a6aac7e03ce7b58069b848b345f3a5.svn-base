﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Controls;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI;

namespace CP.UI
{
    public partial class ConfigFourEyeApprovers : BasePage
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(FourEyeApprovers));

        static IFacade _facade = new Facade();

        public static string CurrentURL = Constants.UrlConstants.Urls.User.FourEyeApprovers;

        public IList<CP.Common.DatabaseEntity.User> approverlist = new List<CP.Common.DatabaseEntity.User>();

        List<ListItem> _Existingusers = new List<ListItem>();

        IList<ApprovalProcess> approvalProcess_new = new List<ApprovalProcess>();

        private static string _isLoggedInUserRole;

        public override void PrepareView()
        {
            try
            {
                _isLoggedInUserRole = HttpContext.Current.Session["LoggedInUserRole"].ToString();
                if (Request.QueryString["Listitems"] != null)
                {
                    string btnText = Request.QueryString["Listitems"];

                }
                ViewState["_token"] = UrlHelper.AddTokenToRequest();
                if (ViewState["_token"] != null)
                {
                    hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
                }
                hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

                Utility.SelectMenu(Master, "Module8");

                var userlist = Facade.GetAllUsers();

                BindApproverdetails();

                BindValidators();

                up_approversdetails.Update();

            }
            catch (Exception ex)
            {
                _logger.Error("Exception in PrepareView of FourEyeApprovers " + ex.InnerException);
            }

        }


        private void BindValidators()
        {
            try
            {
                var validators = Facade.GetAllValidators();
                if ((validators != null) && (validators.Count > 0))
                {
                    foreach (var item in validators)
                    {

                        if (item.Approver.ToString().Contains(","))
                        {
                            string[] _s = item.Approver.ToString().Split(',');

                            if (_s != null && _s.Count() > 0)
                            {
                                string approveruser = string.Empty;
                                foreach (var _napp in _s)
                                {
                                    if (_napp.ToString().Contains("$"))
                                        approveruser = approveruser + _napp.ToString().Substring(0, _napp.ToString().IndexOf('$')) + ",";

                                    else
                                        approveruser = approveruser + _napp.ToString() + ",";

                                    item.Approver = approveruser.TrimEnd(',');
                                }

                            }


                        }
                        else
                        {
                            if (item.Approver.ToString().Contains("$"))
                                item.Approver = item.Approver.ToString().Substring(0, item.Approver.ToString().IndexOf('$'));
                            else
                                item.Approver = item.Approver.ToString();
                        }



                    }
                    grplst.DataSource = validators;
                    grplst.DataBind();
                }
                else
                {
                    Label5.Visible = true;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in BindValidators Method " + ex.Message);
            }
        }

        private void BindApproverdetails()
        {
            try
            {

                RadTreeView1.Nodes.Clear();

                var _users = Facade.GetAllUsers_FourEyeGroup();

                var approverlist_new = Facade.GetAllApprovers();

                approverlist = approverlist_new;

                if (_users != null && _users.Count > 0)
                {
                    RadTreeNode Rootbsname = new RadTreeNode
                    {
                        Text = "Users",
                        Value = "1"
                    };

                    RadTreeView1.Nodes.Add(Rootbsname);

                    foreach (var bs in _users)
                    {
                        RadTreeNode Rootbfname = new RadTreeNode
                        {
                            Text = bs.UserName,
                            Value = bs.Id.ToString()
                        };

                        Rootbsname.Nodes.Add(Rootbfname);

                    }

                }

                var _usergroup = Facade.GetAllUsergroups();


                if ((_usergroup != null) && (_usergroup.Count > 0))
                {
                    RadTreeNode Rootbfname = new RadTreeNode();

                    RadTreeNode Rootbsname = new RadTreeNode();

                    RadTreeNode Rootgrpname = new RadTreeNode();

                    Rootbsname = new RadTreeNode
                    {
                        Text = "Groups",
                        Value = "2"
                    };

                    RadTreeView1.Nodes.Add(Rootbsname);

                    foreach (var _grp in _usergroup)
                    {
                        Rootbfname = new RadTreeNode
                        {
                            Text = _grp.LoginName + "(Group)",
                            Value = _grp.Id.ToString()
                        };

                        Rootbsname.Nodes.Add(Rootbfname);

                        string[] _usernames = _grp.LoginPassword.Split(',');
                        for (int i = 0; i < _usernames.Length; i++)
                        {

                            var userdetails = Facade.GetUserById_WithUserName(Convert.ToInt32(_usernames[i]));
                            Rootgrpname = new RadTreeNode
                            {

                                Text = userdetails.UserName,
                                Value = userdetails.Id.ToString()
                            };

                            Rootbfname.Nodes.Add(Rootgrpname);

                        }


                    }

                }


            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in BindApproverdetails " + ex.Message);
            }
        }

        protected void saveapprovers_Click(object sender, EventArgs e)
        {
            try
            {
                ApprovalProcess _approvalProcess = new ApprovalProcess();
                UserInfo _userdetails = new UserInfo();

                IList<CP.Common.DatabaseEntity.User> SelectedUsers = ShowCheckedNodes(RadTreeView1);
                if (SelectedUsers.Count < 3)
                {
                    ScriptManager.RegisterStartupScript(this, this.GetType(), "", "OpenAlertModelAlert_WithHeader('Information','Please select atleast Three approvers.');", true);
                    return;
                }



                string users = string.Empty;
                string userids = string.Empty;
                string grpname = string.Empty;

                string useremails = string.Empty;


                if (SelectedUsers != null)
                {

                    foreach (var item in SelectedUsers)
                    {
                        users = users + item.UserName + ",";
                        userids = userids + Convert.ToInt32(item.Id) + ",";
                        _userdetails = _facade.GetUserInfoById(Convert.ToInt32(item.Id));
                        if ((_userdetails != null) && (!string.IsNullOrEmpty(_userdetails.Email)))
                            useremails = useremails + _userdetails.Email + ",";
                    }


                    _approvalProcess.ProfileName = rdo_Workflowpf.SelectedValue;
                    _approvalProcess.EXECUTE_OR_MODIFY = useremails;
                    _approvalProcess.Approver = userids;
                    _approvalProcess.ApprovalStatus = users;
                    _approvalProcess.CreatorId = Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]);

                    Facade.AddWorkflowValidators(_approvalProcess);
                }


                string message = "Approvers for new workflow validation ";
                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, TransactionType.Save));
                Response.Redirect(Constants.UrlConstants.Urls.User.ConfigureFourEyeApprovers);


            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in saveapprovers_Click method" + ex.InnerException);
            }

            Response.Redirect(Constants.UrlConstants.Urls.User.ConfigureFourEyeApprovers);
        }

        protected void btnBack_Click(object sender, EventArgs e)
        {
            try
            {
                Response.Redirect(Constants.UrlConstants.Urls.User.ConfigureFourEyeApprovers);
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in btnBack_Click");
            }
        }

        private IList<CP.Common.DatabaseEntity.User> ShowCheckedNodes(RadTreeView treeView)
        {
            IList<CP.Common.DatabaseEntity.User> infralist = new List<CP.Common.DatabaseEntity.User>();
            string groupids = string.Empty;
            try
            {
                IList<RadTreeNode> nodeCollection = treeView.CheckedNodes;
                int cnt = 0;
                int count = 0;

                if ((nodeCollection != null) && (nodeCollection.Count > 0))
                {
                    foreach (RadTreeNode node in nodeCollection)
                    {
                        string _dsa = node.FullPath;

                        _logger.Info("Node is   " + _dsa);
                        if (_dsa.Contains("Users"))
                        {
                            cnt++;
                            if (cnt >= 2)
                            {
                                string dsa = node.FullPath;

                                if (dsa.Contains("/"))
                                {
                                    int index = dsa.IndexOf("/");
                                    string substring1 = dsa.Substring(0, index);
                                    //string[] ff = path.Split('/');
                                    //  var result = path.Split('/').Skip(1).FirstOrDefault();

                                    //int firstIndex = path.IndexOf('/');
                                    //int secondIndex = path.IndexOf('/', firstIndex + 1);

                                    string[] ff = dsa.Split(new[] { '/' }, 2);

                                    //string[] ff = dsa.Split('/');

                                    if (ff.Length >= 2)
                                    {

                                        if (ff[1] != null && ff[1] != "")
                                        {
                                            string infraname = ff[1];

                                            if (infraname != null)
                                            {
                                                IFacade _facade = new Facade();
                                                string[] _user = infraname.Split('(');
                                                var infr = _facade.GetUserWithUserName(_user[0]);
                                                infr.UserName = infr.UserName + "$user";
                                                infr.LoginPassword = ff[0];
                                                infralist.Add(infr);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        else
                        {
                            string _path = node.FullPath;

                            _logger.Info("Node is   " + _path);

                            count++;


                            if (count == 2)
                            {
                                if (!(groupids.Contains(node.Value)))
                                {
                                    groupids = groupids + node.Value + ",";
                                }

                            }
                            else if (count > 2)
                            {
                                if (!(node.ParentNode.Text.Contains("group")))
                                {
                                    if (!(groupids.Contains(node.ParentNode.Value)))
                                    {
                                        groupids = groupids + node.ParentNode.Value + ",";
                                    }
                                }
                                else
                                {
                                    if (!(groupids.Contains(node.Value)))
                                    {
                                        groupids = groupids + node.Value + ",";
                                    }
                                }

                            }


                            if (count > 2)
                            {
                                string path = node.FullPath;

                                if (path.Contains("/"))
                                {
                                    int index = path.IndexOf("/");
                                    string substring1 = _path.Substring(0, index);


                                    string[] ff = path.Split(new[] { '/' }, 3);


                                    // string[] ff = path.Split('/');

                                    if (ff.Length > 2)
                                    {

                                        if (ff[2] != null && ff[2] != "")
                                        {
                                            string infraname = ff[2];

                                            if (infraname != null)
                                            {
                                                IFacade _facade = new Facade();
                                                string[] _user = infraname.Split('(');
                                                //var infr = _facade.GetUserByLoginName(_user[0]);
                                                var infr = _facade.GetUserWithUserName(_user[0]);
                                                // infr.UserName = infr.UserName + "$" + groupids.TrimEnd(',');
                                                infr.UserName = infr.UserName + "$" + node.ParentNode.Value;
                                                infr.LoginPassword = groupids;
                                                infralist.Add(infr);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                }



            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in ShowCheckedNodes method " + ex.InnerException);
            }
            groupids.TrimEnd(',');

            return infralist.ToList();

        }

        protected void grplst_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            try
            {

                var lbl1 = (grplst.Items[e.NewEditIndex].FindControl("Label1")) as Label;

                if (lbl1.Text == "Profile")
                {
                    rdo_Workflowpf.Items[0].Selected = true;
                    rdo_Workflowpf.Items[1].Selected = false;
                }
                else
                {
                    rdo_Workflowpf.Items[0].Selected = false;
                    rdo_Workflowpf.Items[1].Selected = true;
                }
                rdo_Workflowpf.Items[0].Enabled = false;
                rdo_Workflowpf.Items[1].Enabled = false;
                _logger.Info("==== calling grplst_ItemEditing ====");
                saveapprovers.Text = "Update";
                BindExsitingDetails(lbl1.Text);
                _logger.Info("==== calling grplst_ItemEditing ====");
            }
            catch (Exception ex)
            {
                _logger.Error("Exception ocuured in grplst_ItemEditing " + ex.Message);
            }
        }

        protected void grplst_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {

                var lbl1 = (grplst.Items[e.ItemIndex].FindControl("Label1")) as Label;
                bool delete = Facade.Deleteworkflowvalidators(lbl1.Text);



                string message = "workflow validators  ";
                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, TransactionType.Delete));
                Response.Redirect(Constants.UrlConstants.Urls.User.ConfigureFourEyeApprovers);
            }
            catch (Exception ex)
            {
                _logger.Error("Exception ocuured in grplst_ItemEditing " + ex.Message);
            }
        }


        private void BindExsitingDetails(string name)
        {
            try
            {

                RadTreeView1.Nodes.Clear();

                var _users = Facade.GetAllUsers_FourEyeGroup();

                var approverlist_new = Facade.GetAllValidatorsByType(name);

                string _usersnew = string.Empty;

                _usersnew = approverlist_new.Approver;
                //foreach (var item in approverlist_new)
                //{
                //    _usersnew = _usersnew + item.Approver + ",";
                //}

                if (_users != null && _users.Count > 0)
                {
                    RadTreeNode Rootbsname = new RadTreeNode
                    {
                        Text = "Users",
                        Value = "1"
                    };

                    RadTreeView1.Nodes.Add(Rootbsname);

                    foreach (var bs in _users)
                    {
                        RadTreeNode Rootbfname = new RadTreeNode
                        {
                            Text = bs.UserName,
                            Value = bs.Id.ToString()
                        };

                        Rootbsname.Nodes.Add(Rootbfname);

                        if ((approverlist_new != null))
                        {

                            string[] usernames = _usersnew.TrimEnd(',').Split(',');

                            if ((usernames != null) && (usernames.Count() > 0))
                            {
                                foreach (var inff in usernames)
                                {
                                    string[] _usrdetails = inff.Split('$');
                                    if (_usrdetails != null)
                                    {
                                        if ((_usrdetails[0].Equals(Rootbfname.Text)) && ((_usrdetails[1].Contains("user"))))
                                        {
                                            if ((_usrdetails[0].TrimEnd() == Convert.ToString(Rootbfname.Text)))
                                            {
                                                string infraname = Convert.ToString(Rootbfname.Text);
                                                RadTreeNode node = RadTreeView1.FindNodeByText(infraname);
                                                node.Checked = true;
                                            }

                                        }
                                    }

                                }
                            }

                        }


                    }

                }

                var _usergroup = Facade.GetAllUsergroups();


                if ((_usergroup != null) && (_usergroup.Count > 0))
                {
                    RadTreeNode Rootbfname = new RadTreeNode();

                    RadTreeNode Rootbsname = new RadTreeNode();

                    RadTreeNode Rootgrpname = new RadTreeNode();

                    Rootbsname = new RadTreeNode
                    {
                        Text = "Groups",
                        Value = "2"
                    };

                    RadTreeView1.Nodes.Add(Rootbsname);

                    foreach (var _grp in _usergroup)
                    {
                        Rootbfname = new RadTreeNode
                        {
                            Text = _grp.LoginName + "(Group)",
                            Value = _grp.Id.ToString()
                        };

                        Rootbsname.Nodes.Add(Rootbfname);

                        string[] _usernames = _grp.LoginPassword.Split(',');

                        for (int i = 0; i < _usernames.Length; i++)
                        {
                            var userdetails = Facade.GetUserById_WithUserName(Convert.ToInt32(_usernames[i]));
                            Rootgrpname = new RadTreeNode
                            {
                                Text = userdetails.UserName,
                                Value = userdetails.Id.ToString()
                            };

                            Rootbfname.Nodes.Add(Rootgrpname);

                            if ((approverlist_new != null))
                            {
                                string[] usernamesnew = _usersnew.TrimEnd(',').Split(',');
                                if ((usernamesnew != null) && (usernamesnew.Count() > 0))
                                {
                                    foreach (var inff in usernamesnew)
                                    {
                                        string[] _usrdetails = inff.Split('$');
                                        if (_usrdetails != null)
                                        {
                                            if ((_usrdetails[1].Equals(Rootbfname.Value)) && (!(_usrdetails[1].Contains("user"))))
                                            {
                                                if ((_usrdetails[0].TrimEnd() == Convert.ToString(Rootgrpname.Text)))
                                                {
                                                    string infraname = Convert.ToString(Rootgrpname.Text);
                                                    RadTreeNode node = RadTreeView1.FindNodeByValue(_usrdetails[1].ToString());
                                                    RadTreeNode _subnode = node.Nodes.FindNodeByText(infraname);
                                                    if (_subnode != null)
                                                        _subnode.Checked = true;
                                                }

                                            }
                                        }

                                    }
                                }

                            }

                        }


                    }

                }


            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in BindApproverdetails " + ex.Message);
            }
        }

        protected void RadTreeView1_NodeClick(object sender, RadTreeNodeEventArgs e)
        {
            UpdatePanel3.Update();
        }

    }
}