﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="GroupConfiguration.aspx.cs" Inherits="CP.UI.GroupConfiguration"
    Title="Continuity Patrol :: Group Configuration" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    

    <style type="text/css">
        .divtbl .tblestblishrel tbody tr
        {
            float: left !important;
            width: 40% !important;
        }

        .tblestblishrel tbody tr td, .tblestblishrel tbody tr th
        {
            display: block !important;
            padding: 8px 5px !important;
            text-align: left !important;
            height: 50px;
        }

        .tblestblishrel tbody tr:first-child
        {
            width: 20% !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <div class="innerLR">

        <ul class="breadcrumb show">
            <li>You are here</li>
            <li><a href="#" class="glyphicons settings"><i></i>Group</a></li>
            <li class="divider"></li>
            <li>Group Configuration</li>
        </ul>
        <h3>Group Configuration
        </h3>

        <div class="wizard">
            <div class="widget widget-tabs widget-tabs-double">

               
                <div class="widget-head">
                    <ul>
                        <li class="active"><a href="#tab1-2" class="glyphicons user" data-toggle="tab"><i></i><span class="strong">Step 1</span><span>Create Group</span></a></li>
                        <li><a href="#tab2-2" class="glyphicons calculator" data-toggle="tab"><i></i><span class="strong">Step 2</span><span>Establish Relationship</span></a></li>
                        <li><a href="#tab3-2" class="glyphicons credit_card" data-toggle="tab"><i></i><span class="strong">Step 3</span><span>Summary</span></a></li>
                        <li><a href="#tab4-2" class="glyphicons credit_card" data-toggle="tab"><i></i><span class="strong">Step 4</span><span>Luns Summary</span></a></li>
                        <li><a href="#tab5-2" class="glyphicons credit_card" data-toggle="tab"><i></i><span class="strong">Step 5</span><span>Native Log Shipping</span></a></li>
                        <li><a href="#tab6-2" class="glyphicons credit_card" data-toggle="tab"><i></i><span class="strong">Step 6</span><span>Archive Redo</span></a></li>
                    </ul>
                </div>
             

                <div class="widget-body">
                    <div class="tab-content">

                       
                        <div class="tab-pane active" id="tab1-2">
                            <div class="task padding-5">
                                <h2>Create Infra Object</h2>
                            </div>
                            <div class="row">
                                <div class="col-md-12 form-horizontal uniformjs">

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Infra Object Name <span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">
                                            <input id="txtGroupName" type="text" class="form-control" /><span>Enter Object Name</span><span></span><span
                                                id="spnGroupName" class="error" style="display: none">Group Name All ready Exit
                                            </span>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Description <span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">
                                            <textarea id="txtGroupDescription" cols="20" rows="2" class="form-control" style="width: 200px;"></textarea>
                                            <span>Enter Group Description</span><span></span>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Business Service <span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlBusinessService" runat="server" CssClass="selectpicker col-md-6"
                                                data-style="btn-default">
                                            </asp:DropDownList>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Business Function <span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">
                                            <asp:DropDownList ID="ddlFunction" runat="server" CssClass="selectpicker col-md-6"
                                                data-style="btn-default">
                                            </asp:DropDownList>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Type<span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">
                                            <select id="ddlType" class="selectpicker col-md-6" data-style="btn-default">
                                                <option value="000">Select Type</option>
                                                <option value="1">Application Group</option>
                                                <option value="2">DB Group</option>
                                                <option value="3">Virtual Group</option>
                                                <option value="4">Exchange Server</option>
                                                <option value="5">Full DB</option>
                                            </select>
                                            <span>Select Type</span>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            DR Ready<span class="inactive">*</span>
                                        </label>
                                        <div class="col-md-9">

                                            <asp:CheckBox ID="chkDRReady" runat="server" CssClass="vertical-align" />
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Recovery Solution Type<span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">
                                            <select id="ddlSolutionType" class="selectpicker col-md-6" data-style="btn-default">
                                                <option value="000">Select Solution Type</option>
                                            </select>
                                            <span>Select Solution Type</span>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Near Site<span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">

                                            <asp:CheckBox ID="chkNearSite" runat="server" CssClass="vertical-align" />
                                        </div>
                                    </div>

                                    <div id="divGroupDetail" style="display: none">

                                        <div class="float-left side1">
                                            <label>
                                                Group</label><span class="inactive">*</span>
                                        </div>
                                        <div>
                                            <asp:DropDownList ID="ddlGroupList" runat="server">
                                            </asp:DropDownList>
                                            
                                            <span>Select Site Solution Type</span>
                                        </div>
                                        <hr />
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Command<span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">
                                            <input id="txtCommand" type="text" style="width: 52px;" class="form-control" />
                                            <span>Enter Command</span><span></span>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Output<span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">
                                            <input id="txtOutput" type="text" style="width: 52px;" class="form-control" />
                                            <span>Enter Output</span><span></span>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-md-3 control-label">
                                            Priority<span class="inactive">*</span>
                                        </label>

                                        <div class="col-md-9">

                                            <select id="ddlPriority" class="selectpicker col-md-6" data-style="btn-default">
                                                <option value="000">Select Priority</option>
                                                <option value="1">High </option>
                                                <option value="2">Medium </option>
                                                <option value="3">Low </option>
                                            </select>
                                            <span>Select Priority</span>
                                        </div>
                                    </div>

                                    <div class="form-actions row">
                                        <div class="col-md-12 text-right">
                                            <ul class="pagination margin-bottom-none pull-right">
                                                <li class="primary previous first"><a href="#" class="no-ajaxify">First</a></li>
                                                <li class="last primary"><a href="#" class="no-ajaxify">Last</a></li>
                                                <li class="primary previous"><a href="#" class="no-ajaxify">Previous</a></li>
                                                <li class="next primary"><a href="#" class="no-ajaxify">Next</a></li>
                                                <li class="next finish primary" style="display: none;"><a href="#" class="no-ajaxify">Finish</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                       
                        <div class="tab-pane" id="tab2-2">
                            <div class="task padding-5">
                                <h2>Establish Relation</h2>
                            </div>

                            <div id="divNearDr" class="divtbl">

                               

                                <table class="tblestblishrel dynamicTable colVis table table-striped table-bordered table-condensed table-white dataTablezz" style="width: 100%">
                                    <tbody>
                                        <tr>
                                            <th><b>Components</b></th>
                                            <td>Server<span class="inactive">*</span></td>
                                            <td>DataBase<span class="inactive">*</span> </td>
                                            <td id="tdReplication">Replication<span class="inactive">*</span></td>
                                        </tr>

                                        <tr>
                                            <th><b>Production </b></th>
                                            <td>
                                                <select id="ddlServerPr" class="selectpicker col-md-6" data-style="btn-default"></select><span>Select Production Server</span></td>
                                            <td>
                                                <select id="ddlDatabasePr" class="selectpicker col-md-6" data-style="btn-default"></select><span>Select Production Database</span></td>
                                            <td id="tdReplicationPr">
                                                <select id="ddlReplicationPr" class="selectpicker col-md-6" data-style="btn-default"></select><span>Select Production Replication</span> </td>
                                        </tr>

                                        <tr id="trNearDr">
                                            <th><b>Near DR </b></th>
                                            <td>
                                                <select id="ddlNearDrServer" class="selectpicker col-md-6" data-style="btn-default"></select><span>Select DR Server</span></td>
                                            <td>
                                                <select id="ddlNearDrDatabase" class="selectpicker col-md-6" data-style="btn-default"></select><span>Select DR Database</span></td>
                                            <td>
                                                <select id="ddlNearDrReplication" class="selectpicker col-md-6" data-style="btn-default"></select>
                                                <span>Select DR Replication</span></td>
                                        </tr>

                                        <tr style="display: none;">
                                            <th><b>DR</b></th>
                                            <td>
                                                <select id="ddlServerDr" class="selectpicker col-md-6" data-style="btn-default"></select>
                                                <span>Select DR Server</span> </td>
                                            <td>
                                                <select id="ddlDatabaseDr" class="selectpicker col-md-6" data-style="btn-default"></select><span>Select DR Database</span> </td>
                                            <td id="tdReplicationDr">
                                                <select id="ddlReplicationDr" class="selectpicker col-md-6" data-style="btn-default"></select><span>Select DR Replication</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div id="divNodes" style="display: none;">
                                <div class="modal">
                                    <div class="modal-window " style="top: 150px; width: 750px; margin-left: 25%">
                                        <ul class="action-tabs">
                                            <li>
                                                <a id="aClosePopup" href="#" title="Close Window" style="margin-left: -1.2em;">
                                                    <img alt="close" id="imgClosePopup" src="../images/icons/btn_close.png" width="32px" /></a>
                                            </li>
                                        </ul>
                                        <div class="block-content no-padding margin-right margin-top1em">
                                            <div class="block-controls">
                                                <h1>
                                                    <asp:Label ID="lblRelatedNode" runat="server" Text="Relate Nodes"></asp:Label></h1>
                                            </div>
                                            <table class="dtable font" width="100%">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 15%">Components
                                                        </th>
                                                        <th style="width: 35%">Production Nodes
                                                            <label id="prHeadDb"></label>
                                                        </th>
                                                        <th style="width: 35%">DR Nodes<label id="drHeadDb"></label>
                                                        </th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                            </table>
                                            <div class="scrollL">
                                                <table class="dtable font" width="100%">
                                                    <tbody id="tbNodes">
                                                        <tr>
                                                            <td id="tdNodes" style="width: 15%">
                                                                <label>
                                                                    Rac Nodes
                                                                </label>
                                                                <span class="inactive">*</span>
                                                            </td>
                                                            <td id="tdNodePr" style="width: 36%">
                                                                <select id="ddlNodePr">
                                                                </select>
                                                                <span>Select Node</span>
                                                            </td>
                                                            <td id="tdNodeDr" style="width: 36%">
                                                                <select id="ddlNodeDr">
                                                                </select>
                                                                <span>Select Node</span> </td>
                                                            <td>
                                                                <img alt="Add" id="imgAddNodes" src="../Images/icons/plus-circle.png" />
                                                                <div id="nodepallet">
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="block-footer align-right no-margin no-bottom-margin">
                                                <input id="btnOK" type="button" value="OK" class="buttonblue" /></div>
                                        </div>
                                        <div class="modal-resize-tl"></div>
                                        <div class="modal-resize-t"></div>
                                        <div class="modal-resize-tr"></div>
                                        <div class="modal-resize-r"></div>
                                        <div class="modal-resize-br"></div>
                                        <div class="modal-resize-b"></div>
                                        <div class="modal-resize-bl"></div>
                                        <div class="modal-resize-l"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions row">
                                <div class="col-lg-3">
                                    <span class="float-right padding-5">
                                        <input id="btnSaveVmWareFastCopy" type="button" value="Save Group" class="btn btn-primary" />
                                    </span>
                                </div>
                                <div class="col-lg-8 text-right">
                                    <ul class="pagination margin-bottom-none pull-right">
                                        <li class="primary previous first"><a href="#" class="no-ajaxify">First</a></li>
                                        <li class="last primary"><a href="#" class="no-ajaxify">Last</a></li>
                                        <li class="primary previous"><a href="#" class="no-ajaxify">Previous</a></li>
                                        <li class="next primary"><a href="#" class="no-ajaxify">Next</a></li>
                                        <li class="next finish primary" style="display: none;"><a href="#" class="no-ajaxify">Finish</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                       
                        <div class="tab-pane" id="tab3-2">
                            <div class="task ">
                                <span class="number">3</span><h2>Summary</h2>
                            </div>
                            <table class="table table-striped table-bordered table-condensed" width="100%">
                                <thead>
                                    <tr>
                                        <th>Group Summary</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>

                                    <tr>
                                        <td style="width: 34%;">Group Name</td>
                                        <td>
                                            <asp:Label ID="SGroupName" runat="server" Text=""></asp:Label>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>Description</td>
                                        <td>
                                            <asp:Label ID="SGroupDesc" runat="server" Text=""></asp:Label></td>
                                    </tr>

                                    <tr>
                                        <td>Type</td>
                                        <td>
                                            <asp:Label ID="SGroupType" runat="server" Text=""></asp:Label></td>
                                    </tr>

                                    <tr>
                                        <td>Recovery Solution Type</td>
                                        <td>
                                            <asp:Label ID="SGroupRecType" runat="server" Text=""></asp:Label></td>
                                    </tr>

                                    <tr>
                                        <td>Configured Data Lag</td>
                                        <td>
                                            <asp:Label ID="SGroupDL" runat="server" Text=""></asp:Label></td>
                                    </tr>

                                    <tr>
                                        <td>Configured Data Recovery</td>
                                        <td>
                                            <asp:Label ID="SGroupDR" runat="server" Text=""></asp:Label></td>
                                    </tr>

                                    <tr>
                                        <td>Configured Data MTPOD</td>
                                        <td>
                                            <asp:Label ID="SGroupDM" runat="server" Text=""></asp:Label></td>
                                    </tr>

                                    <tr>
                                        <td>Priority</td>
                                        <td>
                                            <asp:Label ID="SGroupPriority" runat="server" Text=""></asp:Label></td>
                                    </tr>
                                </tbody>
                            </table>

                            <div id="divSummaryNearDr" class="divtbl">
                                <table class="table table-striped table-bordered table-white table-responsive table-primary" width="100%">
                                    <tbody>

                                        <tr id="trComponents">
                                            <td style="width: 20%;"><b>Components</b></td>
                                            <td style="width: 20%;"><b>Production </b></td>
                                            <td style="width: 20%;"><b>Near DR </b></td>
                                            <td style="width: 20%;">
                                                <label id="lblSummaryNearDr">DR</label></td>

                                            <td>
                                                <label>
                                                    Replication
                                                </label>
                                            </td>
                                        </tr>
                                        <tr id="trPrimary">
                                            <td>
                                                <label>Server</label></td>
                                            <td>
                                                <asp:Label ID="SGrServerp" runat="server" Text=""></asp:Label></td>
                                            <td id="Td1" ïd="tdGRDatabasePR" runat="server">
                                                <asp:Label ID="SGrDatap" runat="server" Text=""></asp:Label></td>
                                            <td id="tdSummaryExchangeDAGPR">
                                                <asp:ListBox ID="lstSummaryExchangeDAGPR" runat="server" Enabled="false" Style="height: 40px;"></asp:ListBox></td>
                                            <td>
                                                <asp:Label ID="SGrRepp" runat="server" Text=""></asp:Label>
                                            </td>
                                        </tr>

                                        <tr id="trSummaryNearDR">
                                            <td>
                                                <label>DataBase</label></td>
                                            <td>
                                                <asp:Label ID="SGrServerNearDr" runat="server" Text=""></asp:Label></td>
                                            <td>
                                                <asp:Label ID="SGrDatabaseNearDr" runat="server" Text=""></asp:Label></td>
                                            <td>
                                                <asp:ListBox ID="lstSummaryExchangeDAGNearDR" runat="server" Enabled="false" Style="height: 40px;"></asp:ListBox></td>
                                            <td>
                                                <asp:Label ID="SGrReplicationNearDr" runat="server" Text=""></asp:Label>
                                            </td>
                                        </tr>

                                        <tr id="trSecondary">
                                            <td>
                                                <label>Mailbox Database</label></td>
                                            <td>
                                                <asp:Label ID="SGrServerd" runat="server" Text=""></asp:Label></td>
                                            <td id="tdDatabaseD" runat="server">
                                                <asp:Label ID="SGrDatad" runat="server" Text=""></asp:Label><a id="btnRacNodeSummary" href="javascript:void(0);" title="Relate Nodes">(Nodes Relation Summary)</a></td>
                                            <td>
                                                <asp:ListBox ID="lstSummaryExchangeDAGDR" runat="server" Enabled="false" Style="height: 40px;"></asp:ListBox></td>
                                            <td>
                                                <asp:Label ID="SGrRepd" runat="server" Text=""></asp:Label></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div id="divnodesum" style="display: none;">
                                <div class="modal">
                                    <div class="modal-window " style="top: 150px; width: 750px; margin-left: 25%">
                                        <ul class="action-tabs">
                                            <li>
                                                <a id="aCloseSummary" href="#" title="Close Window" style="margin-left: -1.2em;">
                                                    <img alt="close" id="img1" src="../images/icons/btn_close.png" width="32px" height="32px" /></a>
                                            </li>
                                        </ul>
                                        <div class="block-content no-padding margin-right margin-top1em">
                                            <div class="block-controls">
                                                <h1>
                                                    <asp:Label ID="Label1" runat="server" Text="Relate Nodes"></asp:Label></h1>
                                            </div>
                                            <table class="table table-striped table-bordered table-condensed" width="100%">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 15%;">Components
                                                        </th>
                                                        <th style="width: 35%;">Production Nodes
                                                            <label id="Label2"></label>
                                                        </th>
                                                        <th>DR Nodes<label id="Label3"></label>
                                                        </th>
                                                    </tr>
                                                </thead>
                                            </table>
                                            <div class="slim-scroll chat-items" data-scroll-height="190px" data-scroll-size="0">
                                                <table class="table table-striped table-bordered table-condensed" width="100%">
                                                    <tbody id="tNodeSummaryBody">
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="block-footer align-right no-margin no-bottom-margin">
                                                <input id="btnCloseSummary" type="button" value="OK" class="buttonblue" /></div>
                                        </div>
                                        <div class="modal-resize-tl"></div>
                                        <div class="modal-resize-t"></div>
                                        <div class="modal-resize-tr"></div>
                                        <div class="modal-resize-r"></div>
                                        <div class="modal-resize-br"></div>
                                        <div class="modal-resize-b"></div>
                                        <div class="modal-resize-bl"></div>
                                        <div class="modal-resize-l"></div>
                                    </div>
                                </div>
                            </div>
                           
                            <hr class="separator" />
                            <div class="form-actions row">
                                <div class="col-lg-3">
                                    <input id="btnSave" class="btn btn-primary" type="button" value="Save" />
                                    <asp:Button ID="btnWf" runat="server" CssClass="btn btn-primary" Text="Create WorkFlow" OnClick="BtnWfClick" />
                                </div>
                                <div class="col-lg-8 text-right">
                                    <ul class="pagination margin-bottom-none pull-right">
                                        <li class="primary previous first"><a href="#" class="no-ajaxify">First</a></li>
                                        <li class="last primary"><a href="#" class="no-ajaxify">Last</a></li>
                                        <li class="primary previous"><a href="#" class="no-ajaxify">Previous</a></li>
                                        <li class="next primary"><a href="#" class="no-ajaxify">Next</a></li>
                                        <li class="next finish primary" style="display: none;"><a href="#" class="no-ajaxify">Finish</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="tab-pane" id="tab4-2">
                            <div class="task padding-5">
                                <span class="number">3</span>
                                <h2>Luns Summary</h2>
                            </div>
                            <table class="table table-striped table-bordered table-condensed" width="100%">
                                <thead>
                                    <tr>
                                        <th>Lun No.
                                        </th>
                                        <th>&nbsp;&nbsp;&nbsp;&nbsp;Logs&nbsp;&nbsp;&nbsp;&nbsp;
                                        </th>
                                        <th>PPRC Source volume(A) Volume Group/ Mount Point
                                        </th>
                                        <th>PPRC Target volume(B)
                                        </th>
                                        <th>Target Flash Copy (C) volume
                                        </th>
                                        <th>Target Flash Copy (D) volume
                                        </th>
                                        <th>Target Flash Copy (E) volume
                                        </th>
                                        <th>Target Flash Copy (F) volume
                                        </th>
                                    </tr>
                                </thead>
                            </table>
                            <div class="slim-scroll chat-items" data-scroll-height="190px" data-scroll-size="0">
                                <table id="borderhead" class="table table-striped table-bordered table-condensed" width="100%">
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                            <hr class="separator" />
                            <div class="form-actions row">
                                <div class="col-lg-3">
                                    <span class="float-right padding-5">
                                        <input id="btnSaveGrGM" type="button" value="Save Group" class="btn btn-primary" />
                                    </span>
                                </div>
                                <div class="col-lg-8 text-right">
                                    <ul class="pagination margin-bottom-none pull-right">
                                        <li class="primary previous first"><a href="#" class="no-ajaxify">First</a></li>
                                        <li class="last primary"><a href="#" class="no-ajaxify">Last</a></li>
                                        <li class="primary previous"><a href="#" class="no-ajaxify">Previous</a></li>
                                        <li class="next primary"><a href="#" class="no-ajaxify">Next</a></li>
                                        <li class="next finish primary" style="display: none;"><a href="#" class="no-ajaxify">Finish</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                       
                        <div class="tab-pane" id="tab5-2">
                            <div class="task padding-5">
                                <span class="number">3</span>
                                <h2>Native Log Shipping</h2>
                            </div>
                            <div class="float-left grey-bg input-height grid-23">
                                <b>Production Server</b>
                            </div>
                            <div class=" grey-bg input-height">
                                <b>DR Server</b>
                            </div>
                            <hr />
                            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                                <ContentTemplate>
                                    <div class="float-left grid-17">
                                        <div class="float-left side1">
                                            Backup Folder Name
                                        </div>
                                        <div>
                                            <asp:TextBox ID="txtBackup" runat="server" Width="65%" Enabled="false" CssClass="form-control"></asp:TextBox><span>*</span><span></span>
                                        </div>
                                    </div>
                                    <div class="float-left grid-17">
                                        <div class="float-left side1">
                                            Restore Folder
                                        </div>
                                        <div>
                                            <asp:TextBox ID="txtRestore" runat="server" Width="65%" Enabled="false" CssClass="form-control"></asp:TextBox><span>*</span><span></span>
                                        </div>
                                    </div>
                                    <div class="clear">
                                    </div>
                                    <hr />
                                    <div class="float-left grid-17">
                                        <div class="float-left side1">
                                            Backup Network Path
                                        </div>
                                        <div>
                                            <asp:TextBox ID="txtBackupNetwork" runat="server" Width="65%" Enabled="false" CssClass="form-control"></asp:TextBox><span>*</span><span></span>
                                        </div>
                                    </div>
                                    <div class="float-left grid-17">
                                        <div class="float-left side1">
                                            Restore Network Path
                                        </div>
                                        <div>
                                            <asp:TextBox ID="txtRestoreNetwork" runat="server" Width="65%" Enabled="false" CssClass="form-control"></asp:TextBox><span>*</span><span></span>
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                            <div class="clear">
                            </div>
                            <hr />
                            <table class="dtable" width="100%">
                                <thead>
                                    <tr>
                                        <th colspan="2">Job Interval Time
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Backup
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txtbackuptime" runat="server" CssClass="form-control"></asp:TextBox>
                                            Min <span>Insert BackUp Interval Time</span><span></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Copy
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txtcopytime" runat="server" CssClass="form-control"></asp:TextBox>
                                            Min <span>Insert Copy Interval Time</span><span></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Restore
                                        </td>
                                        <td>
                                            <asp:TextBox ID="txtrestoretime" runat="server" CssClass="form-control"></asp:TextBox>
                                            Min <span>Insert Restore Interval Time</span><span></span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <span id="divsplchar" class="error" style="display: none;">* No Special Character</span>

                            <div class="form-actions row">
                                <div class="col-lg-3">
                                    <span class="float-right padding-5">
                                        <input id="btnNativeLog" type="button" value="Save Group" class="btn btn-primary" />
                                    </span>
                                </div>
                                <div class="col-lg-8 text-right">
                                    <ul class="pagination margin-bottom-none pull-right">
                                        <li class="primary previous first"><a href="#" class="no-ajaxify">First</a></li>
                                        <li class="last primary"><a href="#" class="no-ajaxify">Last</a></li>
                                        <li class="primary previous"><a href="#" class="no-ajaxify">Previous</a></li>
                                        <li class="next primary"><a href="#" class="no-ajaxify">Next</a></li>
                                        <li class="next finish primary" style="display: none;"><a href="#" class="no-ajaxify">Finish</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                       
                        <div class="tab-pane" id="tab6-2">
                            <div class="task padding-5">
                                <span class="number">3</span>
                                <h2>Archive Redo</h2>
                            </div>
                            <div class="form paddingLR margin-top25">
                                <fieldset class="float-left grid-23 margin-right">
                                    <legend><a href="#"><b>Archive Info</b></a></legend>
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">VG Name</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtArchVGName" runat="server" CssClass="form-control"></asp:TextBox>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">Mount Point(s)</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtArchMountPoint" runat="server" CssClass="form-control"></asp:TextBox>
                                                    <a title="Mount Point(s)">
                                                        <img src="../Images/icons/icon-tooltipQ.png" class="vertical-align" /></a>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">Device Type</label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlarchdevicetype" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default">
                                                        <asp:ListItem runat="server">Lun Numbers</asp:ListItem>
                                                        <asp:ListItem runat="server">Disks</asp:ListItem>
                                                    </asp:DropDownList>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">HUR/ True Copy Source</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtArchHurTrueCopysource" runat="server" CssClass="form-control"></asp:TextBox>
                                                    P-VOL
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">Shadow Image Production</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtArchShadowImagePR" runat="server" CssClass="form-control"></asp:TextBox>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">Target</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtArchTarget" runat="server" CssClass="form-control"></asp:TextBox>
                                                    S-VOL
                                                </div>
                                            </div>

                                           
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">VG Name</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtRedoVGName" runat="server" CssClass="form-control"></asp:TextBox>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">Mount Point(s)</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtRedoMountPoint" runat="server" CssClass="form-control"></asp:TextBox>
                                                    <a title="Mount Point(s)">
                                                        <img src="../Images/icons/icon-tooltipQ.png" class="vertical-align" /></a>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">Device Type</label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlRedoDevicetype" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default">
                                                        <asp:ListItem runat="server">Lun Numbers</asp:ListItem>
                                                        <asp:ListItem runat="server">Disks</asp:ListItem>
                                                    </asp:DropDownList>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">HUR/ True Copy Source</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtRedohurtruecopysource" runat="server" CssClass="form-control"></asp:TextBox>
                                                    P-VOL
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">Shadow Image Production</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtRedoshadowimagePR" runat="server" CssClass="form-control"></asp:TextBox>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">Target</label>
                                                <div class="col-md-9">
                                                    <asp:TextBox ID="txtRedoTarget" runat="server" CssClass="form-control"></asp:TextBox>
                                                    S-VOL
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                            </div>
                            <div class="clear"></div>
                            <hr class="separator" />
                            <div class="form-actions row">
                                <div class="col-lg-3">
                                    <span class="float-right padding-5">
                                        <input id="btnArchiveRedoSave" type="button" value="Save Group" class="btn btn-primary" />
                                    </span>
                                </div>
                                <div class="col-lg-8 text-right">
                                    <ul class="pagination margin-bottom-none pull-right">
                                        <li class="primary previous first"><a href="#" class="no-ajaxify">First</a></li>
                                        <li class="last primary"><a href="#" class="no-ajaxify">Last</a></li>
                                        <li class="primary previous"><a href="#" class="no-ajaxify">Previous</a></li>
                                        <li class="next primary" style="display: none;"><a href="#" class="no-ajaxify">Next</a></li>
                                        <li class="next finish primary"><a href="#" class="no-ajaxify">Finish</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                       
                    </div>
                </div>
            </div>
        </div>

        <div class="grid_5 grid-15">
            <div id="divcontent" class="block-content" runat="server" style="display: block;">
                <div id="wrapper">
                    <div id="steps">
                        <div id="formElem">
                            <asp:HiddenField ID="groupID" runat="server" />
                            <asp:HiddenField ID="SolType" runat="server" />
                            <asp:HiddenField ID="hdId" runat="server" />
                            <asp:HiddenField ID="hdnPrRac" runat="server" />
                            <asp:HiddenField ID="hdnDrRac" runat="server" />
                        </div>
                    </div>
                    <hr />
                </div>
                <div class="wizard-steps no-bottom-margin no-padding">
                    <ul>
                        <li><a id="create" href="#">Create Group <span class="number">1</span> </a></li>
                        <li><a id="estRel" href="#">Establish Relationship <span class="number">2</span> </a>
                        </li>
                        <li id="lisummary" style="display: none"><a id="summary" href="#">Summary <span class="number">3</span> </a></li>
                        <li><a id="luns" href="#">Luns <span class="number">3</span> </a></li>
                        <li id="linative" style="display: none;"><a id="linativelog" href="#">Native Log Shipping
                        <span class="number">3</span> </a></li>
                        <li id="liArchiveRedo" style="display: none;"><a id="ArchiveRedo" href="#">Archive Redo
                        <span class="number">3</span> </a></li>
                    </ul>
                </div>
            </div>
            <div runat="server" id="DivNotification" class="block-content" style="display: none;">
                <div class="block-controls no-bottom-margin">
                    <h1>&nbsp;</h1>
                    <h1>Group Configuration</h1>
                </div>
                <table class="dtable margin5 " width="100%">
                    <thead>
                        <tr>
                            <td colspan="3" class="padding-5 bold text-indent">Currently configured Components
                            </td>
                        </tr>
                        <tr>
                            <th>Component
                            </th>
                            <th>Production
                            </th>
                            <th>DR
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>DataBase
                            </td>
                            <td>
                                <asp:Label ID="lbldatabase" runat="server" Text="" Visible="false"></asp:Label>
                            </td>
                            <td>--
                            </td>
                        </tr>
                        <tr>
                            <td>Server
                            </td>
                            <td>
                                <asp:Label ID="lblPRserver" runat="server" Text="" Visible="false"></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lblDRserver" runat="server" Text="" Visible="false"></asp:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>Site
                            </td>
                            <td>
                                <asp:Label ID="lblPrsite" runat="server" Text="" Visible="false"></asp:Label>
                            </td>
                            <td>
                                <asp:Label ID="lbldrSite" runat="server" Text="" Visible="false"></asp:Label>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="message no-margin">
                    <span class="icon-info">&nbsp;</span> <b class="inactive">In order to configure a group
                    you need to have atleast 2 Servers ( 1 Production and 1 DR ), 2 Database Components
                    and 2 Site Components. </b>
                </div>
            </div>
        </div>

        
    </div>
</asp:Content>