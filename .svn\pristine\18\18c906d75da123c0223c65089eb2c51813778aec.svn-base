﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IGroupDatabaseNodes

    public interface IGroupDatabaseNodesDataAccess
    {
        GroupDatabaseNodes Add(GroupDatabaseNodes infraObjectdatabasenode);

        GroupDatabaseNodes Update(GroupDatabaseNodes infraObjectdatabasenode);

        GroupDatabaseNodes UpdateInfraId(GroupDatabaseNodes infraObjectdatabasenode);

        GroupDatabaseNodes GetById(int id);

        IList<GroupDatabaseNodes> GetAll();

        IList<GroupDatabaseNodes> GetByInfraobjectId(int infraObjectId);

        bool DeleteById(int id);

        bool DeleteByInfraObjectId(int id);
    }

    #endregion IGroupDatabaseNodes
}