﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="DashboardBIAFunctionReport.aspx.cs" Inherits="CP.UI.ImpactAnalysis.DashboardBIAFunctionReport" %>

<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
<head id="Head1" runat="server">
    <title><%--<asp:Literal runat="server" ID="ltrTitle" Text="What-If Analysis - Business Service Impact"></asp:Literal>--%></title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />

    <script src="../Script/jquery.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>

    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>



    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="../Script/d3.min.js"></script>
    <script src="../Script/WhatIfAnalysis.js"></script>
    <style type="text/css">
        #ddlImpactTime {
            width: 100% !important;
        }

        .col-md-8 .btn-group.bootstrap-select {
            width: 40% !important;
        }

        .rptimpacttable {
            margin-top: 50px;
        }

            .rptimpacttable th {
                font-size: 13px !important;
            }

        .btn.btn-default.dropdown-toggle.dropup.open.col-md-12 > option {
            background: #FFFFFF;
            color: #333333;
            font-weight: normal;
            text-shadow: transparent;
            text-align: left;
            line-height: 30px !important;
        }
    </style>
</head>
<body>
     <telerik:RadWindowManager ID="RadWindowManager1" runat="server" IconUrl="~/Images/icons/alerts/sign_add.png">
        <Windows>
            <telerik:RadWindow ID="TelRadWindow" AutoSize="false" runat="server" VisibleStatusbar="false" Title="Business Functions Impact Analysis1"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="290" ShowContentDuringLoad="false"
                Width="1200" Skin="Metro" />
        </Windows>
    </telerik:RadWindowManager> 
    <form id="form1" runat="server">
        <asp:ScriptManager ID="sp" runat="server"></asp:ScriptManager>
        <div class="innerLR">

            <div class="widget margin-top">
                <%-- <div class="widget-head">
                    <h4 class=" heading">Business Function Impact</h4>
                </div>--%>
                <div class="widget-body">
                    <%--<div class="row">--%>
                    <div class="col-md-12 form-horizontal">
                        <%--<div class="row">--%>
                        <div class="form-group" style="margin-bottom: 20px !important;">
                            <div class="col-md-4 padding-none-LR">
                                <label class="col-md-5 control-label npr">Business Service</label>
                                <div class="col-md-7 npl npr">
                                    <asp:DropDownList ID="ddlBusinessService" runat="server" CssClass="selectpicker col-md-12" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlBusinessService_SelectedIndexChanged"></asp:DropDownList>
                                </div>
                            </div>

                            <div class="col-md-3 padding-none-LR">
                                <label class="col-md-4 control-label npr">Impact Time</label>
                                <div class="col-md-8 npl npr">
                                    <asp:TextBox ID="txtImpactTime" runat="server" CssClass="form-control" Width="40%" Text="2"></asp:TextBox>
                                    <asp:DropDownList ID="ddlImpactTime" runat="server" CssClass="selectpicker" Width="40%" data-style="btn-default">
                                        <asp:ListItem Text="Hour" Value="1" Selected="True"></asp:ListItem>
                                        <asp:ListItem Text="Days" Value="2"></asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:RequiredFieldValidator ID="rfvImpactTime" runat="server"
                                        ControlToValidate="txtImpactTime" ErrorMessage="*" CssClass="error"
                                        Display="Dynamic" ValidationGroup="vgImpactDetails"></asp:RequiredFieldValidator>

                                </div>
                            </div>
                            <div class="col-md-5 padding-none-LR">
                                <label class="col-md-3 control-label npr">BIA Method</label>
                                <div class="col-md-9">
                                    <asp:DropDownList ID="ddlBIAMethod" runat="server" CssClass="selectpicker col-md-8" data-style="btn-default">
                                        <asp:ListItem Text="BIA Data" Value="1"></asp:ListItem>
                                        <asp:ListItem Text="BIA Data-Exponential" Value="2"></asp:ListItem>
                                        <asp:ListItem Text="BIA Data-Linear" Value="3"></asp:ListItem>
                                        <asp:ListItem Text="BIA Data-Common Log" Value="4"></asp:ListItem>
                                        <asp:ListItem Text="BIA Data-Natural Log" Value="5"></asp:ListItem>
                                    </asp:DropDownList>

                                    <asp:Button ID="btnShowImpact" runat="server" Text="Show Impact" CssClass="btn btn-primary" OnClick="btnShowImpact_Click" ValidationGroup="vgImpactDetails" />


                                </div>
                            </div>
                        </div>
                        <%--   <hr class="separator" />
                        <div class="form-group">--%>
                        <%--<asp:UpdatePanel runat="server" ID="upImpactDetails" UpdateMode="Conditional">
                        <ContentTemplate>--%>
                        <div class="form-group margin-bottom-none">
                              <div class="message warning align-center bold no-bottom-margin">
                                        <asp:Label ID="lblError" Text="" ForeColor="Red" runat="server" Visible="false"></asp:Label>
                                    </div>
                            <div class="col-md-10 padding-none-LR">
                                <asp:ListView ID="rptImpactDetails" runat="server" OnItemCommand="rptImpactDetails_ItemCommand">
                                    <LayoutTemplate>
                                        <table class="table table-bordered table-striped table-white margin-bottom-none" id="tablerptImpactDetails">
                                            <thead>
                                                <tr>
                                                    <th style="width: 15%;">Impacted Business Function</th>
                                                    <th style="width: 6%;">Impact Time</th>
                                                    <th style="width: 16%;">Impact Type</th>
                                                    <th style="width: 6%;">Currency</th>
                                                    <th style="width: 7%;">Financial Impact</th>
                                                    <th style="width: 10%;">App Cost Impact(%)</th>
                                                    <th style="width: 9%;">View App FIA Details</th>
                                                    <th style="width: 23%;">Remark</th>

                                                </tr>
                                            </thead>
                                        </table>

                                        <div id="notifyscroll">
                                            <table class="table table-bordered table-striped table-white margin-bottom-none" id="tablebodyrptImpactDetails">
                                                <tbody>
                                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                </tbody>
                                            </table>
                                        </div>

                                    </LayoutTemplate>

                                    <ItemTemplate>
                                        <tr>
                                            <td style="width: 15%;">

                                                <asp:Label ID="lblBusinessFunctionName" runat="server" Text='<%#Eval("BFName")%>'></asp:Label></td>
                                            <td style="width: 6%;">
                                                <asp:Label ID="lblImpactTime" runat="server" Text='<%#Eval("ImpactTime") %>'></asp:Label>
                                                <%--  <asp:Label ID="lblImpactTimeUnit" runat="server" Text=""></asp:Label>--%>
                                            </td>
                                            <td style="width: 16%;">
                                                <asp:DropDownList ID="ddlImpactType" runat="server" CssClass="btn btn-default dropdown-toggle dropup open col-md-12" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlImpactType_SelectedIndexChanged">
                                                    <asp:ListItem Text="Partially Impacted" Value="1" Selected="True"></asp:ListItem>
                                                    <asp:ListItem Text="Majorly Impacted" Value="2"></asp:ListItem>
                                                    <asp:ListItem Text="Totally Impacted" Value="3"></asp:ListItem>
                                                </asp:DropDownList>
                                            </td>

                                            <td style="width: 6%;">
                                                <asp:Label ID="lblCurrency" runat="server" Text="$"></asp:Label></td>
                                            <td style="width: 7%;">
                                                <asp:Label ID="lblFinancialImpact" runat="server" Text='<%#Eval("BFCost")%>'></asp:Label></td>

                                            <td style="width: 10%;">
                                                <asp:Label ID="lblAppCostImpact" runat="server" Text='<%#Eval("CostInPer") %>'></asp:Label>
                                                <%--  <asp:Label ID="lblImpactTimeUnit" runat="server" Text=""></asp:Label>--%>
                                            </td>
                                            <td class="text-center" style="width: 9%; vertical-align: middle">
                                                <asp:ImageButton ID="imgBtnViewBIA" runat="server" ImageUrl="~/Images/icons/icon-searcg.png" CommandName="ViewBIAprofile"/></td>
                                            <td style="width: 23%;">
                                                <asp:Label ID="lblRemark" runat="server" Text='<%#Eval("Remark")%>' ForeColor="Red"></asp:Label></td>


                                        </tr>

                                    </ItemTemplate>
                                </asp:ListView>
                            </div>
                            <div class="col-md-2 npr" runat="server" id="PnlTotalcost">
                                <div class="widget-stats widget-stats-2 widget-stats-gray widget-stats-easy-pie" id="dbbiafr" style="height: 136px; padding: 5px">
                                    <h4 style="margin-top: 5px;">Total Financial Impact</h4>
                                    <div class="informer" style="margin-top: 10px;">
                                        <a href="#" class="informera">
                                            <span class="fa fa-dollar text-primary text-large"></span>
                                            <asp:Label ID="lblTotalImpactCost" runat="server" Text="" CssClass="text-large"></asp:Label>
                                        </a>

                                    </div>
                                </div>
                            </div>
                        </div>
                        <%--</ContentTemplate>
                    </asp:UpdatePanel>--%>
                        <%--   </div>
                    </div>--%>
                    </div>
                    <%-- </div>--%>
                </div>
            </div>
        </div>

    </form>
    <script type="text/javascript">
        if ($('.selectpicker').length)
            $('.selectpicker').selectpicker();
    </script>

    <script type="text/javascript">
        $(document).ready(function () {
            $("#notifyscroll").mCustomScrollbar({
                axis: "y",
                setHeight: "92px",
            });
        });

        function pageLoad() {

            $("#notifyscroll").mCustomScrollbar({
                axis: "y",
                setHeight: "92px",
            });
        }

        function openRadWindow1(Url) {
            window.radopen(Url, "TelRadWindow");
        }
    </script>
   
</body>
</html>
