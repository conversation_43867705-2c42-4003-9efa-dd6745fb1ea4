﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class DataSyncPropertiesBuilder : IEntityBuilder<DataSyncProperties>
    {
        IList<DataSyncProperties> IEntityBuilder<DataSyncProperties>.BuildEntities(IDataReader reader)
        {
            var dataSyncProperties = new List<CP.Common.DatabaseEntity.DataSyncProperties>();

            while (reader.Read())
            {
                dataSyncProperties.Add(((IEntityBuilder<DataSyncProperties>)this).BuildEntity(reader, new DataSyncProperties()));
            }

            return (dataSyncProperties.Count > 0) ? dataSyncProperties : null;
        }

        DataSyncProperties IEntityBuilder<DataSyncProperties>.BuildEntity(IDataReader reader, DataSyncProperties dataSyncProperties)
        {
            dataSyncProperties.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

            dataSyncProperties.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : reader["Name"].ToString();
            dataSyncProperties.ReplicationType = Convert.IsDBNull(reader["ReplicationType"]) ? string.Empty : reader["ReplicationType"].ToString();

            dataSyncProperties.FilterOption = Convert.IsDBNull(reader["FilterOption"]) ? 0 : Convert.ToInt32(reader["FilterOption"]);
            dataSyncProperties.FilterExpression = Convert.IsDBNull(reader["FilterExpression"]) ? string.Empty : reader["FilterExpression"].ToString();

            dataSyncProperties.DeleteFilesOption = Convert.IsDBNull(reader["DeleteFilterOption"]) ? false : Convert.ToBoolean(reader["DeleteFilterOption"]);
            dataSyncProperties.DeleteFilesExpression = Convert.IsDBNull(reader["DeleteFilterExpression"]) ? string.Empty : reader["DeleteFilterExpression"].ToString();

            dataSyncProperties.EnableSSHPrivateKeyPR = Convert.IsDBNull(reader["EnableSSHPrivateKeyPR"]) ? false : Convert.ToBoolean(reader["EnableSSHPrivateKeyPR"]);
            dataSyncProperties.SSHPrivateKeyPathPR = Convert.IsDBNull(reader["SSHPrivateKeyPathPR"]) ? string.Empty : reader["SSHPrivateKeyPathPR"].ToString();

            dataSyncProperties.EnableSSHPrivateKeyDR = Convert.IsDBNull(reader["EnableSSHPrivateKeyDR"]) ? false : Convert.ToBoolean(reader["EnableSSHPrivateKeyDR"]);
            dataSyncProperties.SSHPrivateKeyPathDR = Convert.IsDBNull(reader["SSHPrivateKeyPathDR"]) ? string.Empty : reader["SSHPrivateKeyPathDR"].ToString();

            dataSyncProperties.RetainFolderPermission = Convert.IsDBNull(reader["RetainFolderPermission"]) ? false : Convert.ToBoolean(reader["RetainFolderPermission"]);
            dataSyncProperties.EnableChecksumCompare = Convert.IsDBNull(reader["EnableChecksumCompare"]) ? false : Convert.ToBoolean(reader["EnableChecksumCompare"]);
            dataSyncProperties.ShellPromptPR = Convert.IsDBNull(reader["ShellPromptPR"]) ? string.Empty : reader["ShellPromptPR"].ToString();
            dataSyncProperties.ShellPromptDR = Convert.IsDBNull(reader["ShellPromptDR"]) ? string.Empty : reader["ShellPromptDR"].ToString();

            dataSyncProperties.EnableParallelReplication = Convert.IsDBNull(reader["EnableParallelReplication"]) ? false : Convert.ToBoolean(reader["EnableParallelReplication"]);
            dataSyncProperties.NumberOfThreads = Convert.IsDBNull(reader["NumberOfThreads"]) ? 0 : Convert.ToInt32(reader["NumberOfThreads"]);
            dataSyncProperties.EnableIncrementalReplication = Convert.IsDBNull(reader["EnableIncrementalReplication"]) ? false : Convert.ToBoolean(reader["EnableIncrementalReplication"]);

            dataSyncProperties.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            dataSyncProperties.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            dataSyncProperties.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            dataSyncProperties.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            dataSyncProperties.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());

            return dataSyncProperties;
        }
    }
}