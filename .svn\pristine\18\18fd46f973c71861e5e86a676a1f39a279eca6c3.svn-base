﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Telerik.Web.UI;
using Telerik.Web.UI.HtmlChart;

namespace CP.UI.Controls
{
    public partial class ServicesMeetingRTO : BaseControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }


        public override void PrepareView()
        {
            BindBarChart();
            //BarSeries barSeries = BarChart.PlotArea.Series[0] as BarSeries;
            //barSeries.Stacked = true;
        }

        private void BindBarChart()
        {

            IList<ServiceRTODaily> _comp = Facade.GetRTOMeetDetails_ByMonths();
            int cnt = 0;
            lblcurrentmnth.Text = DateTime.Now.ToString("MMMM") + "-" + DateTime.Now.Year;

            for (int i = DateTime.Now.Month - 5; i <= DateTime.Now.Month; i++)
            {
                AxisItem _item = new AxisItem();
                _item.LabelText = new DateTime(2016, i, 1).ToString("MMM");
                BarChart.PlotArea.XAxis.Items.Add(_item);
            }

            BindCurrentMonthDetails(_comp);

            if (_comp != null && _comp.Count > 0)
            {
               

                ColumnSeries _donutSeries2 = new ColumnSeries();
                _donutSeries2.TooltipsAppearance.DataFormatString = "{0}";
                _donutSeries2.LabelsAppearance.Visible = false;
                _donutSeries2.LabelsAppearance.Position = BarColumnLabelsPosition.Center;
                _donutSeries2.LabelsAppearance.Color = Color.White;
                _donutSeries2.LabelsAppearance.TextStyle.FontSize = 9;

                ColumnSeries _donutSeries = new ColumnSeries();
                _donutSeries.TooltipsAppearance.DataFormatString = "{0}";
                _donutSeries.LabelsAppearance.Visible = false;
                _donutSeries.LabelsAppearance.Position = BarColumnLabelsPosition.Center;
                _donutSeries.LabelsAppearance.Color = System.Drawing.Color.White;
                _donutSeries.LabelsAppearance.TextStyle.FontSize = 9;

                foreach (var a in _comp)
                {

                    SeriesItem _item2 = new SeriesItem();
                    _item2.YValue = Convert.ToDecimal(a.RTONotMeet);
                    _item2.Name = "Not Meeting RTO";
                    _item2.BackgroundColor = System.Drawing.ColorTranslator.FromHtml("#77b1d6");
                    _donutSeries2.Items.Add(_item2);


                    SeriesItem _item1 = new SeriesItem();

                    _item1.YValue = Convert.ToDecimal(a.RTOMeet);
                    _item1.Name = "Meeting RTO";
                    _item1.BackgroundColor = System.Drawing.ColorTranslator.FromHtml("#95c761");
                    _donutSeries.Items.Add(_item1);
                }
                _donutSeries2.Stacked = true;
                _donutSeries.Stacked = true;
                BarChart.PlotArea.Series.Add(_donutSeries2);
                BarChart.PlotArea.Series.Add(_donutSeries);
                

            }
        }



        private void BindCurrentMonthDetails(IList<ServiceRTODaily> _comp)
        {

            var _currentMonthDetails = (from ServiceRTODaily sd in _comp
                                        where sd.MonthID == DateTime.Now.Month
                                        select sd);

            if (_currentMonthDetails != null && _currentMonthDetails.Count() > 0)
            {
                ServiceRTODaily Obj = _currentMonthDetails.FirstOrDefault();
                lblMeetingRTO.Text = Obj.RTOMeet.ToString();
                lblNotMeetingRTO.Text = Obj.RTONotMeet.ToString();
            }


        }
    }
}