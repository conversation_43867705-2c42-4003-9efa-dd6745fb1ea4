﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess.BusinessServiceRTO
{
    internal sealed class BusinessServiceRTOInfoBuilder : IEntityBuilder<BusinessServiceRTOInfo>
    {
        IList<BusinessServiceRTOInfo> IEntityBuilder<BusinessServiceRTOInfo>.BuildEntities(IDataReader reader)
        {
            var businessServicesRTOInfo = new List<BusinessServiceRTOInfo>();

            while (reader.Read())
            {
                businessServicesRTOInfo.Add(((IEntityBuilder<BusinessServiceRTOInfo>)this).BuildEntity(reader, new BusinessServiceRTOInfo()));
            }

            return (businessServicesRTOInfo.Count > 0) ? businessServicesRTOInfo : null;
        }

        BusinessServiceRTOInfo IEntityBuilder<BusinessServiceRTOInfo>.BuildEntity(IDataReader reader, BusinessServiceRTOInfo businessServiceRTOInfo)
        {
            businessServiceRTOInfo.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

            businessServiceRTOInfo.BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"]) ? 0 : Convert.ToInt32(reader["BusinessServiceId"]);

            businessServiceRTOInfo.BusinessFunctionId = Convert.IsDBNull(reader["BusinessFunctionId"]) ? 0 : Convert.ToInt32(reader["BusinessFunctionId"]);

            businessServiceRTOInfo.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);

            businessServiceRTOInfo.CurrentRTO = Convert.IsDBNull(reader["CurrentRTO"])
               ? string.Empty
               : Convert.ToString(reader["CurrentRTO"]);

            businessServiceRTOInfo.CriticalityLevel = Convert.IsDBNull(reader["CriticalityLevel"]) ? string.Empty : Convert.ToString(reader["CriticalityLevel"]);

            businessServiceRTOInfo.CreateDate = Convert.IsDBNull(reader["CreateTime"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateTime"].ToString());

            return businessServiceRTOInfo;
        }
    }
}