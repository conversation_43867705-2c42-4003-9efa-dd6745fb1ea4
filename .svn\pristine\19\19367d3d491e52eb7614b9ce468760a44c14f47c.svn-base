﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class SQLNative2008MonitorBuilder : IEntityBuilder<SQLNative2008Monitor>
    {
       IList<SQLNative2008Monitor> IEntityBuilder<SQLNative2008Monitor>.BuildEntities(IDataReader reader)
        {
            var sqllognative = new List<SQLNative2008Monitor>();

            while (reader.Read())
            {
               //sqllognative.Add(((IEntityBuilder<SQLNative2008Monitor>)this).BuildEntity(reader, new SQLNative2008Monitor()));
                sqllognative.Add(((IEntityBuilder<SQLNative2008Monitor>)this).BuildEntity(reader, new SQLNative2008Monitor()));
                
            }

            return (sqllognative.Count > 0) ? sqllognative : null;
        }

       SQLNative2008Monitor IEntityBuilder<SQLNative2008Monitor>.BuildEntity(IDataReader reader, SQLNative2008Monitor sqllognative)
        {
           

            sqllognative.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            sqllognative.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            sqllognative.PRServer = Convert.IsDBNull(reader["PRServer"])
                ? string.Empty
                : Convert.ToString(reader["PRServer"]);
            sqllognative.DRServer = Convert.IsDBNull(reader["DRServer"])
                ? string.Empty
                : Convert.ToString(reader["DRServer"]);
            sqllognative.DatabaseName = Convert.IsDBNull(reader["DatabaseName"])
                ? string.Empty
                : Convert.ToString(reader["DatabaseName"]);
            sqllognative.LastGenLog = Convert.IsDBNull(reader["Last_GenLog"])
                ? string.Empty
                : Convert.ToString(reader["Last_GenLog"]);
            sqllognative.LastApplyLog = Convert.IsDBNull(reader["Last_ApplyLog"])
                ? string.Empty
                : Convert.ToString(reader["Last_ApplyLog"]);
            sqllognative.LastCopiedLog = Convert.IsDBNull(reader["Last_CopiedLog"])
                ? string.Empty
                : Convert.ToString(reader["Last_CopiedLog"]);
            sqllognative.LSNLastBackupLog = Convert.IsDBNull(reader["LSN_last_backupLog"])
                ? string.Empty
                : Convert.ToString(reader["LSN_last_backupLog"]);
            sqllognative.LSNLastRestoredLog = Convert.IsDBNull(reader["LSN_last_restoredLog"])
                ? string.Empty
                : Convert.ToString(reader["LSN_last_restoredLog"]);
            sqllognative.LSNLastCopiedLog = Convert.IsDBNull(reader["LSN_Last_CopiedLog"])
                ? string.Empty
                : Convert.ToString(reader["LSN_Last_CopiedLog"]);
            sqllognative.LastLogGenTime = Convert.IsDBNull(reader["Last_Log_Gen_Time"])
                ? string.Empty
                : Convert.ToString(reader["Last_Log_Gen_Time"]);
            sqllognative.LastLogApplTime = Convert.IsDBNull(reader["Last_Log_Appl_Time"])
                ? string.Empty
                : Convert.ToString(reader["Last_Log_Appl_Time"]);
            sqllognative.LastLogCopyTime = Convert.IsDBNull(reader["Last_Log_Copy_Time"])
                ? string.Empty
                : Convert.ToString(reader["Last_Log_Copy_Time"]);
            sqllognative.DataLag = Convert.IsDBNull(reader["DataLag"])
                ? string.Empty
                : Convert.ToString(reader["DataLag"]);

            sqllognative.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            sqllognative.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            sqllognative.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);
            sqllognative.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            sqllognative.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);

            return sqllognative;
        }
    }
}
