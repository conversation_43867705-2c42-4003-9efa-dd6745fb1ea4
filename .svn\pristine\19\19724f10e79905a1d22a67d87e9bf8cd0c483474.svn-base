﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "SybaseWithSRSMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class SybaseWithSRSMonitor : BaseEntity
    {
        #region Properties

        public int Id { get; set; }

        public int InfraObjectId { get; set; }

        public string ReplicationServerVersion { get; set; }

        public string DatabaseStatusPR { get; set; }

        public string DatabaseStatusDR { get; set; }

        public string DatadeviceDetailsPR { get; set; }

        public string DatadeviceDetailsDR { get; set; }

        public string LogDeviceDetailsPR { get; set; }

        public string LogDeviceDetailsDR { get; set; }

        public string TransactionOriginTimePR { get; set; }

        public string TransactionCommitTimeDR { get; set; }

        public string Latency { get; set; }

        public string Health { get; set; }

        public string LogicalStatus { get; set; }

        public string QueueState { get; set; }

        public string QuiesceState { get; set; }

        public string CurrentTransaction { get; set; }

        public string FailedTransactions { get; set; }

        public string DuplicateTransactions { get; set; }

        public string DataLag { get; set; }

        public string BusinessServiceName { get; set; }

        public string DataSpaceUsedPR { get; set; }

        public string DataSpaceUsedDR { get; set; }

        public string LogSpaceUsedPR { get; set; }

        public string LogSpaceUsedDR { get; set; }

        public string Rep_agentStatusPR { get; set; }

        public string ReplicationServerStatus { get; set; }

        public string RSSDDataDeviceDetails { get; set; }

        public string RSSDDATASpaceUsed { get; set; }

        public string RSSDLogDeviceDetails { get; set; }

        public string RSSDLogSpaceUsed { get; set; }

        public string PhysicalConnectionState { get; set; }

        public string ActiveConnectionState { get; set; }

        public string StandbyConnectionState { get; set; }

        public string OperationinProgress { get; set; }

        public string StableDeviceStatus { get; set; }

        public string DatabaseLoggingsStatusPR { get; set; }

        public string DatabaseLoggingsStatusDR { get; set; }

        public string DataServerStatusPR { get; set; }

        public string DataServerStatusDR { get; set; }

        public string BackupServerStatusPR { get; set; }

        public string BackupServerStatusDR { get; set; }

        #endregion
    }
}

