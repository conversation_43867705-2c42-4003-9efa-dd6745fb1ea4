﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;

namespace CP.UI
{
    public partial class DataSyncPropertiesList : DataSyncPropertiesBasePage
    {
        #region variable

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.Datasyncproperties;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin)
                {
                    return Constants.UrlConstants.Urls.Component.DatasyncpropertiesList;
                }
                return string.Empty;
            }
        }

        #endregion variable

        public override void PrepareView()
        {
            //if (IsUserManager)
            //{
            //    Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);
            //}

            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }

            Utility.SelectMenu(Master, "Module3");
            BindList();
        }

        private void BindList()
        {
            setListViewPage();
            lvComponent.DataSource = GetDataSyncPropertiesList();
            lvComponent.DataBind();
        }
        /// <summary>
        /// if deleting or updating the List of Data Sync Proerties the page will get postback and then Listview  swap to the same page
        /// where it is deleted or updated. - Goraknath Khule.
        /// </summary>
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageDataSynPropertiesList"]) != -1) && Session["CurrentPageDataSynPropertiesList"] != null && (Convert.ToInt32(Session["CurrentPageDataSynPropertiesList"]) > 0))
            {
                if (Session["TotalPageRowsCountDataSysnPropertList"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageDataSynPropertiesList"]) == Convert.ToInt32(Session["TotalPageRowsCountDataSysnPropertList"]) - 1)
                    {
                        Session["CurrentPageDataSynPropertiesList"] = Convert.ToInt32(Session["CurrentPageDataSynPropertiesList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCountDataSysnPropertList"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageDataSynPropertiesList"]), dataPager1.MaximumRows, true);
                dataPager1.DataBind();
                Session["CurrentPageDataSynPropertiesList"] = -1; 
            }
        }
        private IList<DataSyncProperties> GetDataSyncPropertiesList()
        {
            //return Facade.GetServersByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
            return Facade.GetAllDataSyncProperties();
        }

        protected void LvComponentItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageDataSynPropertiesList"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCountDataSysnPropertList"] = dataPager1.TotalRowCount;
                var lblId = (lvComponent.Items[e.ItemIndex].FindControl("lblId")) as Label;
                var lblName = (lvComponent.Items[e.ItemIndex].FindControl("lblName")) as Label;
                var FastCopyJobDeatils = Facade.GetAllFastCopyJobByDataSynchProertyId(Convert.ToInt32(lblId.Text));
                if (FastCopyJobDeatils != null)
                {
                    if (FastCopyJobDeatils.Count > 0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The DataSync Properties '" + lblName.Text + "' is in use.");
                    }
                    else if (lblId != null && lblName != null)
                    {
                     Facade.DeleteDataSyncPropertiesById(Convert.ToInt32(lblId.Text));
                     ActivityLogger.AddLog(LoggedInUserName, "DataSync Properties", UserActionType.DeleteServerComponent, "The DataSync Properties '" + lblName.Text + "' was deleted", LoggedInUserId);
                     ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("DataSync Properties" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                    }
                }
                else if (lblId != null && lblName != null)
                {
                    Facade.DeleteDataSyncPropertiesById(Convert.ToInt32(lblId.Text));
                    ActivityLogger.AddLog(LoggedInUserName, "DataSync Properties", UserActionType.DeleteServerComponent, "The DataSync Properties '" + lblName.Text + "' was deleted", LoggedInUserId);
                    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("DataSync Properties" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                }

            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void LvComponentItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageDataSynPropertiesList"] = dataPager1.StartRowIndex;
            var secureUrl = new SecureUrl(CurrentURL);

            var lblId = (lvComponent.Items[e.NewEditIndex].FindControl("lblId")) as Label;
            if (lblId != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.DataSyncPropertiesId,
                                                     lblId.Text);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void LvComponentPreRender(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                if (String.IsNullOrEmpty(txtsearchvalue.Text))
                {
                    lvComponent.DataSource = GetDataSyncPropertiesList();
                    lvComponent.DataBind();
                }
                else
                {
                    lvComponent.DataSource = GetDataSyncPropertiesListBySearch(txtsearchvalue.Text);
                    lvComponent.DataBind();
                }
            }
        }

        protected void LvComponentItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ImgEdit") as ImageButton;
            var delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (!IsSuperAdmin && !IsUserAdmin)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
            if (IsUserOperator||IsUserManager)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }

        protected void BtnSearchClick(object sender, EventArgs e)
        {
            lvComponent.DataSource = GetDataSyncPropertiesListBySearch(txtsearchvalue.Text);
            lvComponent.DataBind();
        }

        private IList<DataSyncProperties> GetDataSyncPropertiesListBySearch(string value)
        {
            var dataSyncPropertiesList = GetDataSyncPropertiesList();
            value = value.Trim();
            if (!String.IsNullOrEmpty(value) || dataSyncPropertiesList != null)
            {
                var result = (from dsProperties in dataSyncPropertiesList
                              where dsProperties.Name.ToLower().Contains(value.ToLower())
                              select dsProperties).ToList();

                return result;
            }
            return null;
        }

        protected void lvComponent_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }
    }
}