﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Server", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class Server : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public int SiteId { get; set; }

        [DataMember]
        public string Type { get; set; }

        [DataMember]
        public string IPAddress { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public string SSHUserName { get; set; }

        [DataMember]
        public string SSHPassword { get; set; }

        [DataMember]
        public int EnableSudoAccess { get; set; }

        [DataMember]
        public string SudoUser { get; set; }

        [DataMember]
        public string SudoPassword { get; set; }

        [DataMember]
        public string DSIPAddress { get; set; }

        [DataMember]
        public string DSSSHUserName { get; set; }

        [DataMember]
        public string DSSSHPassword { get; set; }

        [DataMember]
        public string OSType { get; set; }

        [DataMember]
        public bool IsPartOfCluster { get; set; }

        [DataMember]
        public ServerStatus Status { get; set; }

        [DataMember]
        public string DataStoreName { get; set; }

        [DataMember]
        public string VmPath { get; set; }

        [DataMember]
        public string Disk { get; set; }

        [DataMember]
        public int IsUseSshKeyAuthentication { get; set; }

        [DataMember]
        public int IsUseSshKeyAuth { get; set; }

        [DataMember]
        public string SshKeyPath { get; set; }

        [DataMember]
        public string SshKeyPassword { get; set; }

        [DataMember]
        public int ServerOSTypeCount { get; set; }

        [DataMember]
        public string ShellPrompt { get; set; }

        [DataMember]
        public string LicenseKey { get; set; }

        [DataMember]
        public string InfraObjectName { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public int IsVerified { get; set; }

        [DataMember]
        public string ErrorMessage { get; set; }

        [DataMember]
        public int SSOTypeId { get; set; }

        [DataMember]
        public int SSOEnabled { get; set; }

        [DataMember]
        public string HostName { get; set; }



        //[DataMember]
        //public bool IsSsoEnable { get; set; }

        //[DataMember]
        //public int SsoTypeID { get; set; }

        [DataMember]
        public string Safe { get; set; }

        [DataMember]
        public string Object { get; set; }

        [DataMember]
        public string Folder { get; set; }

        [DataMember]
        public string Reason { get; set; }

        [DataMember]
        public int SSOProfileId { get; set; }

        [DataMember]
        public int ServerRole { get; set; }

        [DataMember]
        public int IsVirtualGuestOS { get; set; }

        [DataMember]
        public int IsASMGrid { get; set; }

        [DataMember]
        public int ASMInstanceId { get; set; }

        [DataMember]
        public int WinRMPort { get; set; }

        [DataMember]
        public string ProxyAccessType { get; set; }

        [DataMember]
        public string CANID { get; set; }

        #endregion Properties
    }
}