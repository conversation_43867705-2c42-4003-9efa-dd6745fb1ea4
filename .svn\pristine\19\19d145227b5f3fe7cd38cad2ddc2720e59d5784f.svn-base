﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "MySqlNative", Namespace = "http://www.BCMS.com/types")]
    public class MySqlNative : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId
        {
            get;
            set;
        }

        [DataMember]
        public int ReplicationId
        {
            get;
            set;
        }

        [DataMember]
        public string LOGNO
        {
            get;
            set;
        }

        [DataMember]
        public string RepStatus
        {
            get;
            set;
        }

        #endregion Properties

        #region Constructor

        public MySqlNative()
            : base()
        {
        }

        #endregion Constructor
    }
}