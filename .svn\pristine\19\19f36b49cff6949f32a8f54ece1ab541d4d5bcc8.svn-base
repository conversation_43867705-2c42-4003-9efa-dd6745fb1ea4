﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated. 
// </auto-generated>
//------------------------------------------------------------------------------

namespace CP.UI {
    
    
    public partial class CIODashboard {
        
        /// <summary>
        /// SeriveRecoveryEfficiency control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.SeriveRecoveryEfficiency SeriveRecoveryEfficiency;
        
        /// <summary>
        /// ServicesNeverRecovered1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.ServicesNeverRecovered ServicesNeverRecovered1;
        
        /// <summary>
        /// ComponentFailureDetails1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.ComponentFailureDetails ComponentFailureDetails1;
        
        /// <summary>
        /// AlertsByMonth control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.AlertsByMonth AlertsByMonth;
        
        /// <summary>
        /// CurrentStatus control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.CurrentStatus CurrentStatus;
        
        /// <summary>
        /// SeriveRecoveryEffectiveness control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.SeriveRecoveryEffectiveness SeriveRecoveryEffectiveness;
        
        /// <summary>
        /// ServiceWithoutDRInfra1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.ServiceWithoutDRInfra ServiceWithoutDRInfra1;
        
        /// <summary>
        /// ServicesMeetingRTO1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.ServicesMeetingRTO ServicesMeetingRTO1;
        
        /// <summary>
        /// RPOSLAbreachbyMonth control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.RPOSLAbreachbyMonth RPOSLAbreachbyMonth;
        
        /// <summary>
        /// DrillsExecution1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.DrillsExecution DrillsExecution1;
        
        /// <summary>
        /// AvgDataLag control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.AvgDataLag AvgDataLag;
        
        /// <summary>
        /// ActionIntervention1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.ActionIntervention ActionIntervention1;
        
        /// <summary>
        /// RpoSlaBreachSixMonth control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.Controls.RpoSlaBreachSixMonth RpoSlaBreachSixMonth;
        
        /// <summary>
        /// IncidentByMonth control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.IncidentByMonth IncidentByMonth;
        
        /// <summary>
        /// DownBusinessServices1 control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::CP.UI.DownBusinessServices DownBusinessServices1;
    }
}
