﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "hypervclustersummaryMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class HypervClusterNodeMonitor : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string PRClusterID { get; set; }

        [DataMember]
        public string DRClusterID { get; set; }

        [DataMember]
        public string PRClusterName { get; set; }

        [DataMember]
        public string DRClusterName { get; set; }

        [DataMember]
        public string PRClusterState { get; set; }

        [DataMember]
        public string DRClusterState { get; set; }
       
        #endregion Properties
    }
}
