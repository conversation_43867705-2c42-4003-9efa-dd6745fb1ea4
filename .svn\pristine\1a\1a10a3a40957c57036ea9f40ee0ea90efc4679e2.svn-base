﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace BCMS.Helper
{
    public sealed class ControlHelper
    {
        private HttpRequest _request;

        public ControlHelper(HttpRequest request)
        {
            _request = request;
        }

        [DebuggerStepThrough]
        public static Control GetControlOfType(ControlCollection controls,
        Type type)
        {
            if (controls.Count == 0)
            {
                return null;
            }

            if (controls[0].GetType() == type)
            {
                return controls[0];
            }

            return controls.Cast<Control>().FirstOrDefault(c => (c.GetType() == type) || (c.GetType().BaseType == type));
        }

        [DebuggerStepThrough]
        public string GetControlValue(string control)
        {
            return FormValue(control);
        }

        [DebuggerStepThrough]
        public string GetControlValue(Control control)
        {
            return FormValue(control.UniqueID);
        }

        [DebuggerStepThrough]
        public string GetControlValue(Control control,
        string defaultValue)
        {
            string returnValue = FormValue(control.UniqueID);

            if (returnValue == null)
            {
                return defaultValue;
            }

            return returnValue;
        }

        [DebuggerStepThrough]
        private string FormValue(string control)
        {
            return _request.Form[control];
        }

        [DebuggerStepThrough]
        public static void SetDefaultButton(WebControl[] controls, Control button)
        {
            if ((controls != null) && (controls.Length > 0))
            {
                foreach (WebControl t in controls)
                {
                    SetDefaultButton(t, button);
                }
            }
        }

        [DebuggerStepThrough]
        public static void SetDefaultButton(WebControl control, Control button)
        {
            if ((control != null) && (button != null))
            {
                control.Attributes.Add("onkeydown", "SetDefaultButton('" + button.ClientID + "')");
            }
        }

        [DebuggerStepThrough]
        public static void SetHyperLink(HyperLink link,
        string url)
        {
            link.NavigateUrl = url;
        }

        [DebuggerStepThrough]
        public static void SetHyperLink(HyperLink link,
        string url,
        string text)
        {
            if (!string.IsNullOrEmpty(text))
            {
                link.Text = text;
            }

            link.NavigateUrl = url;
        }

        [DebuggerStepThrough]
        public static void SetHyperLink(HyperLink link,
        string url,
        string returnUrl,
        string text,
        params string[] parameters)
        {
            SecureUrl secureUrl = UrlHelper.BuildSecureUrl(url, returnUrl, parameters);
            SetHyperLink(link, secureUrl, text);
        }

        [DebuggerStepThrough]
        public static void SetHyperLink(HyperLink link,
        string url,
        string returnUrl,
        string text)
        {
            SetHyperLink(link, url, returnUrl, text, null);
        }

        [DebuggerStepThrough]
        public static void SetHyperLink(HyperLink link,
        SecureUrl url,
        string text)
        {
            SetHyperLink(link, url.ToString(), text);
        }

        [DebuggerStepThrough]
        public static void SetHyperLink(HyperLink link,
        SecureUrl url)
        {
            SetHyperLink(link, url.ToString());
        }

        [DebuggerStepThrough]
        public static bool IsItemDataRow(ListItemType itemType)
        {
            return (
                     (itemType == ListItemType.Item) ||
                     (itemType == ListItemType.AlternatingItem)
                    );
        }

        //[DebuggerStepThrough]
        //public static bool IsListItemDataRow(ListViewItemType itemType)
        //{
        //    return (
        //             (itemType == ListViewItemType.DataItem)
        //            );
        //}

        [DebuggerStepThrough]
        public static void SelectListByText(ListControl list,
        string text)
        {
            list.SelectedIndex = list.Items.IndexOf(list.Items.FindByText(text));
        }

        [DebuggerStepThrough]
        public static void SelectListByValue(ListControl list,
        string value)
        {
            list.SelectedIndex = list.Items.IndexOf(list.Items.FindByValue(value));
        }

        [DebuggerStepThrough]
        public static void SelectListMultipleByValues(ListControl list,
        string value)
        {
            if (value.IsNotNullOrEmpty())
            {
                string[] values = value.ToStringArray(',');
                if (values != null)
                {
                    foreach (string currentValue in values)
                    {
                        ListItem lstItem = list.Items.FindByValue(currentValue);
                        if (lstItem != null)
                        {
                            lstItem.Selected = true;
                        }
                    }
                }
            }
        }

        [DebuggerStepThrough]
        public static void ClearListSelection(ListControl list)
        {
            list.SelectedIndex = -1;

            foreach (ListItem item in list.Items)
            {
                item.Selected = false;
            }
        }

        [DebuggerStepThrough]
        public static void RemoveSelectedFromList(ListControl list)
        {
            for (int i = list.Items.Count - 1; i > -1; i--)
            {
                if (list.Items[i].Selected)
                {
                    list.Items.RemoveAt(i);
                }
            }
        }
    }
}
