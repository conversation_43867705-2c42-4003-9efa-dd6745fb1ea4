﻿using System;
using System.Collections.Generic;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using System.Linq;
using System.Web;

namespace CP.UI.Controls
{
    public partial class EMCSRDFSGConfiguration : ReplicationControl
    {
        #region Variable

        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        private static readonly IFacade _facade = new Facade();
        private EMCSRDFSG _EMCSRDFSGRepli = null;
        private static bool _isParent;
        // IList<VMWarePathDetails> VmPathList = new List<VMWarePathDetails>();

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public EMCSRDFSG CurrentEntity
        {
            get { return _EMCSRDFSGRepli ?? (_EMCSRDFSGRepli = new EMCSRDFSG()); }
            set
            {
                _EMCSRDFSGRepli = value;
            }
        }

        public string MessageInitials
        {
            get { return "EMCSRDFSG Replication Information "; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion Variable

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        public override void PrepareView()
        {
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            PupulateDropDown();
            ddlServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvddlServer.ClientID + ")");
            //txtCGName.Attributes.Add("onblur", "ValidatorValidate(" + rfvCGName.ClientID + ")");
            //txtSymCLIPath.Attributes.Add("onblur", "ValidatorValidate(" + rfvSymCLIPath.ClientID + ")");
            twosite.Visible = true;
            Session.Remove("EMCSRDFSG");


            Utility.PopulateServerByTypeAndRole(ddlServer, "SymCLIServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);

            PrepareEditView();
        }

        public void PupulateDropDown()
        {
            Utility.PopulateReplicationModes(ddlReplicationMode);
        }

        private void PrepareEditView()
        {

            if (CurrentEMCSRDFSGReplication != null)
            {
                if (CurrentEMCSRDFSGReplication.NoofSites == 3 && CurrentEMCSRDFSGReplication.ReplicationMode == "1")
                {
                    Concurrent.Visible = true;
                    threesites.Visible = true;
                    twosite.Visible = false;

                    CurrentEntity = CurrentEMCSRDFSGReplication;

                    Session["EMCSRDFSG"] = CurrentEntity;

                    ddlServer.SelectedValue = CurrentEntity.ServerId.ToString();
                    DdlServerSelectedIndexChanged(null, null);

                    txtDSCLIHostname.Enabled = false;
                    txtDSCLIServerIP.Enabled = false;
                    txtSSHUserID.Enabled = false;
                    txtSSHPassword.Enabled = false;

                    ddlnoofsites.SelectedValue = CurrentEntity.NoofSites.ToString();
                    ddlReplicationMode.SelectedValue = CurrentEntity.ReplicationMode.ToString();
                    txtConcPRSID.Text = CurrentEntity.PRSID;
                    txtConcNDRSID.Text = CurrentEntity.NEARDRSID;
                    txtConcFDRSID.Text = CurrentEntity.FDRSID;
                    txtConcPRRDFG.Text = CurrentEntity.PRRDFG;
                    txtConcDRRDFG.Text = CurrentEntity.FDRDFG;
                    txtconcPRSGname.Text = CurrentEntity.PRSGName;
                    txtConcNDRSGName.Text = CurrentEntity.NEARDRSGName;
                    txtConcFDRSGName.Text = CurrentEntity.FDRSGName;
                    txtConcPRPairfilepath.Text = CurrentEntity.PRPairFilePath;
                    txtConcDRPairfilepath.Text = CurrentEntity.FDRPairFilePath;
                    txtconcPRpairfilename.Text = CurrentEntity.PRPairFileName;
                    txtconcDRpairfilename.Text = CurrentEntity.FDRPairFileName;
                    txtSymCLIPath.Text = CurrentEntity.SymCLIPath;


                    btnSave.Text = "Update";
                }
                else if (CurrentEMCSRDFSGReplication.NoofSites == 3 && CurrentEMCSRDFSGReplication.ReplicationMode == "2")
                {
                    Concurrent.Visible = true;
                    threesites.Visible = true;
                    twosite.Visible = false;

                    CurrentEntity = CurrentEMCSRDFSGReplication;

                    Session["EMCSRDFSG"] = CurrentEntity;

                    ddlServer.SelectedValue = CurrentEntity.ServerId.ToString();

                    DdlServerSelectedIndexChanged(null, null);

                    txtDSCLIHostname.Enabled = false;
                    txtDSCLIServerIP.Enabled = false;
                    txtSSHUserID.Enabled = false;
                    txtSSHPassword.Enabled = false;

                    ddlnoofsites.SelectedValue = CurrentEntity.NoofSites.ToString();
                    ddlReplicationMode.SelectedValue = CurrentEntity.ReplicationMode.ToString();
                    txtConcPRSID.Text = CurrentEntity.PRSID;
                    txtConcNDRSID.Text = CurrentEntity.NEARDRSID;
                    txtConcFDRSID.Text = CurrentEntity.FDRSID;
                    txtConcPRRDFG.Text = CurrentEntity.PRRDFG;
                    txtConcDRRDFG.Text = CurrentEntity.FDRDFG;
                    txtconcPRSGname.Text = CurrentEntity.PRSGName;
                    txtConcNDRSGName.Text = CurrentEntity.NEARDRSGName;
                    txtConcFDRSGName.Text = CurrentEntity.FDRSGName;
                    txtConcPRPairfilepath.Text = CurrentEntity.PRPairFilePath;
                    txtConcDRPairfilepath.Text = CurrentEntity.FDRPairFilePath;
                    txtconcPRpairfilename.Text = CurrentEntity.PRPairFileName;
                    txtconcDRpairfilename.Text = CurrentEntity.FDRPairFileName;
                    txtSymCLIPath.Text = CurrentEntity.SymCLIPath;

                    btnSave.Text = "Update";
                }
                else
                {
                    twosite.Visible = true;
                    Concurrent.Visible = false;
                    threesites.Visible = false;

                    CurrentEntity = CurrentEMCSRDFSGReplication;

                    Session["EMCSRDFSG"] = CurrentEntity;

                    ddlServer.SelectedValue = CurrentEntity.ServerId.ToString();

                    DdlServerSelectedIndexChanged(null, null);

                    txtDSCLIHostname.Enabled = false;
                    txtDSCLIServerIP.Enabled = false;
                    txtSSHUserID.Enabled = false;
                    txtSSHPassword.Enabled = false;

                    ddlnoofsites.SelectedValue = CurrentEntity.NoofSites.ToString();
                    ddlReplicationMode.SelectedValue = CurrentEntity.ReplicationMode.ToString();
                    txtSIDPR.Text = CurrentEntity.PRSID.ToString();
                    txtSIDDR.Text = CurrentEntity.FDRSID.ToString();
                    txtRDFG.Text = CurrentEntity.PRRDFG;
                    txtSGnamePR.Text = CurrentEntity.PRSGName;
                    txtSGNamefdr.Text = CurrentEntity.FDRSGName;
                    txtpairfilepath.Text = CurrentEntity.PRPairFilePath;
                    txtpairfilename.Text = CurrentEntity.PRPairFileName;
                    txtSymCLIPath.Text = CurrentEntity.SymCLIPath;

                    btnSave.Text = "Update";
                }
            }
        }

        protected void DdlServerSelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlServer.SelectedValue != "0")
            {
                var server = Facade.GetServerById(Convert.ToInt32(ddlServer.SelectedValue));
                txtDSCLIHostname.Text = server.Name;
                txtDSCLIServerIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                txtSSHUserID.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                ////RSM
                //txtSSHPassword.Attributes.Add("value", CryptographyHelper.Md5Decrypt(server.SSHPassword));

               // txtSSHPassword.Attributes.Add("value", Utility.IsMD5EncryptedString(server.SSHPassword) ? server.SSHPassword : CryptographyHelper.Md5Encrypt(server.SSHPassword));
                txtSSHPassword.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                txtSSHPassword.Attributes["value"] = Utility.getHashKeyByString(txtSSHPassword.Text, hdfStaticGuid.Value);

            
            
            }
            else
            {
                txtDSCLIHostname.Text = string.Empty;
                txtDSCLIServerIP.Text = string.Empty;
                txtSSHUserID.Text = string.Empty;
                txtSSHPassword.Attributes["value"] = string.Empty;
            }
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {

            if (Page.IsValid && (Session["ReplicationConfig_token"] != null) && !CheckReplicationNameExist() && ValidateRequest("EMCSRDFSG ", UserActionType.CreateReplicationComponent))
            {
                if (!ValidateInput())
                {
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");
                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl);
                }
                else
                {
                    Label lblPrName = Parent.FindControl("lblPrName") as Label;
                    if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
                    {
                        lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
                    }
                    var submitButton = (Button)sender;
                    string buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }

                    if (Page.IsValid)
                    {
                        string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                        if (returnUrl.IsNullOrEmpty())
                        {
                            returnUrl = ReturnUrl;
                        }

                        try
                        {
                            if (ValidateRequest("EMCSRDFConfiguration", UserActionType.CreateReplicationComponent))
                            {
                                BuildEntities();
                                StartTransaction();
                                SaveEditor();
                                EndTransaction();
                                string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                      currentTransactionType));
                                btnSave.Enabled = false;
                            }
                        }
                        catch (CpException ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            ExceptionManager.Manage(ex, Page);
                        }
                        catch (Exception ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                            {
                                ExceptionManager.Manage((CpException)ex.InnerException, Page);
                            }
                            else
                            {
                                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                                ExceptionManager.Manage(customEx, Page);
                            }
                        }
                        //if (returnUrl.IsNotNullOrEmpty())
                        //{
                        //    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "7");

                        //    Helper.Url.Redirect(secureUrl);
                        //}
                        if (returnUrl.IsNotNullOrEmpty())
                        {
                            if (ReplicationType.SelectedValue == "7")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "7");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "7");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "17")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "17");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "17");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "26")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "26");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "26");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "37")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "37");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "37");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "38")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "38");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "38");
                                Helper.Url.Redirect(secureUrl);
                            }

                            else if (ReplicationType.SelectedValue == "39")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "39");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "39");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "40")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "40");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "40");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "46")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "46");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "46");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "47")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "47");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "47");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "63")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "63");
                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "63");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "77")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "77");
                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "77");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "79")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "79");
                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "79");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else if (ReplicationType.SelectedValue == "81")
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "81");
                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "81");
                                Helper.Url.Redirect(secureUrl);
                            }
                            else
                            {
                                //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "18");

                                //Helper.Url.Redirect(secureUrl);
                                var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "18");
                                Helper.Url.Redirect(secureUrl);
                            }
                        }
                    }
                }
            }
        }

        private void BuildEntities()
        {
            if (Session["EMCSRDFSG"] != null)
            {
                CurrentEntity = (EMCSRDFSG)Session["EMCSRDFSG"];
            }
            if (ddlnoofsites.SelectedValue == "2")
            {
                CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
                CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
                CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
                CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
                CurrentEntity.ServerId = Convert.ToInt32(ddlServer.SelectedValue);
                CurrentEntity.NoofSites = Convert.ToInt32(ddlnoofsites.SelectedValue);
                CurrentEntity.ReplicationMode = null;
                CurrentEntity.PRSID = txtSIDPR.Text;
                CurrentEntity.FDRSID = txtSIDDR.Text;
                CurrentEntity.PRRDFG = txtRDFG.Text;
                CurrentEntity.PRSGName = txtSGnamePR.Text;
                CurrentEntity.FDRSGName = txtSGNamefdr.Text;
                //CurrentEntity.PRRDFG = txtConcPRRDFG.Text;
                //CurrentEntity.FDRDFG = txtConcDRRDFG.Text;
                CurrentEntity.PRPairFilePath = txtpairfilepath.Text;
                CurrentEntity.PRPairFileName = txtpairfilename.Text;
                CurrentEntity.SymCLIPath = txtSymCLIPath.Text;
            }
            else
            {
                CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
                CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
                CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
                CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
                CurrentEntity.ServerId = Convert.ToInt32(ddlServer.SelectedValue);
                CurrentEntity.NoofSites = Convert.ToInt32(ddlnoofsites.SelectedValue);
                CurrentEntity.ReplicationMode = ddlReplicationMode.SelectedItem.Value;
                CurrentEntity.PRSID = txtConcPRSID.Text;
                CurrentEntity.NEARDRSID = txtConcNDRSID.Text;
                CurrentEntity.FDRSID = txtConcFDRSID.Text;
                CurrentEntity.PRRDFG = txtConcPRRDFG.Text;
                CurrentEntity.FDRDFG = txtConcDRRDFG.Text;
                CurrentEntity.PRSGName = txtconcPRSGname.Text;
                CurrentEntity.FDRSGName = txtConcFDRSGName.Text;
                CurrentEntity.NEARDRSGName = txtConcNDRSGName.Text;
                CurrentEntity.PRPairFilePath = txtConcPRPairfilepath.Text;
                CurrentEntity.FDRPairFilePath = txtConcDRPairfilepath.Text;
                CurrentEntity.PRPairFileName = txtconcPRpairfilename.Text;
                CurrentEntity.FDRPairFileName = txtconcDRpairfilename.Text;
                CurrentEntity.SymCLIPath = txtSymCLIPath.Text;
            }
        }

        private void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddEMCSRDFSG(CurrentEntity);

                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "EMCSRDFSG", UserActionType.CreateReplicationComponent, "The EMCSRDFSG Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
            }
            else
            {
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateEMCSRDFSG(CurrentEntity);

                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "EMCSRDFSG", UserActionType.UpdateReplicationComponent, "The EMCSRDFSG Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["ReplicationConfig_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }

                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtSSHUser");
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        protected void ddlnoofsites_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlnoofsites.SelectedValue == "2")
            {
                twosite.Visible = true;
                threesites.Visible = false;
                Concurrent.Visible = false;

            }
            if (ddlnoofsites.SelectedValue == "3")
            {
                twosite.Visible = false;
                threesites.Visible = true;
                Concurrent.Visible = true;

            }
        }

        protected void ddlReplicationMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlReplicationMode.SelectedValue == "1")
            {
                Concurrent.Visible = true;

                twosite.Visible = false;
                threesites.Visible = true;
                lblTextDR.Text = "PR Site to FDR Site";
            }
            if (ddlReplicationMode.SelectedValue == "2")
            {
                Concurrent.Visible = true;
                twosite.Visible = false;
                threesites.Visible = true;
                lblTextDR.Text = "Near Site to Far Site";
            }
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }
    }
}