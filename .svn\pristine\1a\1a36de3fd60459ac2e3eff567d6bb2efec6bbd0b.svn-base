﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.LoadMasterLogs
{
    internal sealed class CPLoadMasterLogsDataAccess : BaseDataAccess , ICPLoadMasterLogs
    {
          #region Constructors


        public CPLoadMasterLogsDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<CPLoadMasterLogs> CreateEntityBuilder<CPLoadMasterLogs>()
        {
            return (new CPLoadMasterLogsBuilder()) as IEntityBuilder<CPLoadMasterLogs>;
        }

        #endregion Constructors


        CPLoadMasterLogs ICPLoadMasterLogs.Addlogs(CPLoadMasterLogs nodes)
        {
            try
            {
                const string sp = "CPLoadMasterlogs_Create";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                   // AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iNodeID", DbType.String, nodes.NodeID);
                    Database.AddInParameter(cmd, Dbstring + "ibusinessserviceid", DbType.String, nodes.BServiceID);
                    Database.AddInParameter(cmd, Dbstring + "ioperation", DbType.String, nodes.Opeartion);
                   // Database.AddInParameter(cmd, Dbstring + "iServiceStatus", DbType.String, nodes.ServiceStatus);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        nodes = reader.Read() ? CreateEntityBuilder<CPLoadMasterLogs>().BuildEntity(reader, nodes) : null;
                    }

                   

                    return nodes;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting node Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

       

    }
}
