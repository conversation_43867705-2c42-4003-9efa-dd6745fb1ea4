﻿using CP.BusinessFacade;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CP.UI.Code.Replication.ReplicationInfo
{
    public class SybaseReplicationInfo : IReplicationInfo
    {
        private readonly IFacade _facade = new Facade();

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectid)
        {
            var currentEntityType = currentEntity.GetType();

            var Sybasesmonitr = _facade.GetSybaseMonitorByInfraObjectId(infraObjectid);

            if (Sybasesmonitr != null)
            {
                currentEntityType.GetProperty("LastDumpedSequence").SetValue(currentEntity, Sybasesmonitr.LastDumpedSequence, null);
                currentEntityType.GetProperty("LastLoadedSequence").SetValue(currentEntity, Sybasesmonitr.LastLoadedSequence, null);
               
            }
            else
            {
                currentEntityType.GetProperty("LastDumpedSequence").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("LastLoadedSequence").SetValue(currentEntity, "N/A", null);
           
            }

            return currentEntity;
        }

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectid, int mailBoxId, string mailboxname)
        {
            return currentEntity;
        }
    }
}