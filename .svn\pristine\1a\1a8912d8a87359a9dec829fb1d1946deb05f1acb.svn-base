﻿using System;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;

namespace CP.UI.Code.Base
{
    public abstract class AccessManagerBagePageEditor : AccessManagerBasePage, IEditor<CP.Common.DatabaseEntity.User>
    {
        private string _message = string.Empty;

        #region Properties

        public User CurrentEntity
        {
            get { return CurrentUser; }
            set { CurrentUser = value; }
        }

        public int CurrentEntityId
        {
            get { return CurrentUserId; }
            set { CurrentUserId = 0; }
        }

        public abstract string MessageInitials
        {
            get;
        }

        protected new string Message
        {
            get
            {
                return _message;
            }
            set
            {
                _message = value;
            }
        }

        public abstract string ReturnUrl
        {
            get;
        }

        public virtual Label TotalResult
        {
            get
            {
                return null;
            }
        }

        #endregion Properties

        #region Method

        public abstract void PrepareEditView();

        public abstract void SaveEditor();

        public virtual void PrepareValidator()
        {
        }

        public CP.Common.DatabaseEntity.User BuildEntity(CP.Common.DatabaseEntity.User currentEntity)
        {
            return currentEntity;
        }

        public abstract void BuildEntities();

        public virtual void BindList()
        {
        }

        public virtual void Delete(int entityId)
        {
        }

        public virtual void ClearForm()
        {
            CurrentEntityId = 0;
        }

        public virtual void FinalizeCommand()
        {
        }

        #endregion Method

        #region Event

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            PrepareValidator();
        }

        #endregion Event
    }
}