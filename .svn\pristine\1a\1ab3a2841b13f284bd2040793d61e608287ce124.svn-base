﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IUserActivityDataAccess

    public interface IUserActivityDataAccess
    {
        UserActivity Add(UserActivity userActivity);

        IList<UserActivity> GetByStartEndDate(string startDate, string endDate);

        IList<UserActivity> GetActivityByDate(int loginId, string startDate, string endDate);

        IList<UserActivity> GetAllUsersWithout_EnterpriseUser(string startDate, string endDate);

        IList<UserActivity> GetAllUsersWithout_EnterpriseUserByDate(string loginname, string startDate, string endDate);

        IList<DashboardAlert> GetDashboardAlert();

         IList<UserActivity> GetActivityByDate_New(int loginname, string startDate, string endDate);

         IList<UserActivity> GetAllUsersWithout_EnterpriseUserByDate_new(int loginname, string startDate, string endDate);
       //UserActivity Update(UserActivity userActivity);

        //UserActivity GetById(int id);

        //UserActivity GetByName(string name);

        //IList<UserActivity> GetAll();

        //bool DeleteById(int id);

        //bool IsExistByName(string id);
    }

    #endregion IUserActivityDataAccess
}