﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class DiscoveryHostLogsDataAccess : BaseDataAccess, IDiscoveryHostLogsDataAccess
    {
        #region Constructors

        public DiscoveryHostLogsDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DiscoveryHostLogs> CreateEntityBuilder<DiscoveryHostLogs>()
        {
            return (new DiscoveryHostLogsBuilder()) as IEntityBuilder<DiscoveryHostLogs>;
        }

        #endregion

        #region Methods
        /// <summary>
        /// Create <see cref="DiscoveryHostLogs" /> into discovery_host_logs table.
        /// </summary>
        /// <param name="objDiscoveryHostLogs"></param>
        /// <returns></returns>
        DiscoveryHostLogs IDiscoveryHostLogsDataAccess.Add(DiscoveryHostLogs objDiscoveryHostLogs)
        {
            try
            {
                const string sp = "DiscoveryHostLogs_Create";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iDiscoverScanId", DbType.Int32, objDiscoveryHostLogs.DiscoverScanId);
                    Database.AddInParameter(cmd, Dbstring+"iReachableHost", DbType.String, objDiscoveryHostLogs.ReachableHost);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, objDiscoveryHostLogs.Status);                                    
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_discovery_host_logs"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        objDiscoveryHostLogs = reader.Read()
                            ? CreateEntityBuilder<DiscoveryHostLogs>().BuildEntity(reader, objDiscoveryHostLogs)
                            : null;
                    }

                    if (objDiscoveryHostLogs == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DiscoveryHostLogs already exists. Please specify another DiscoveryHostLogs.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating thisDiscoveryHostLogs.");
                                }
                        }
                    }

                    return objDiscoveryHostLogs;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting DiscoveryConfiguration Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        /// Create <see cref="DiscoveryHostLogs" /> into discovery_host_logs table.
        /// </summary>
        /// <param name="objDiscoveryConfiguration"></param>
        /// <returns></returns>
        DiscoveryHostLogs IDiscoveryHostLogsDataAccess.Update(DiscoveryHostLogs objDiscoveryHostLogs)
        {
            try
            {
                const string sp = "DiscoveryHostLogs_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, objDiscoveryHostLogs.Id);
                    Database.AddInParameter(cmd, Dbstring+"iDiscoverScanId", DbType.Int32, objDiscoveryHostLogs.DiscoverScanId);
                    Database.AddInParameter(cmd, Dbstring+"iReachableHost", DbType.String, objDiscoveryHostLogs.ReachableHost);
                    Database.AddInParameter(cmd, Dbstring+"iStatus", DbType.Int32, objDiscoveryHostLogs.Status);                   

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_discovery_host_logs"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        objDiscoveryHostLogs = reader.Read()
                            ? CreateEntityBuilder<DiscoveryHostLogs>().BuildEntity(reader, objDiscoveryHostLogs)
                            : null;
                    }

                    if (objDiscoveryHostLogs == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DiscoveryConfiguration already exists. Please specify another DiscoveryHostLogs.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this DiscoveryHostLogs.");
                                }
                        }
                    }

                    return objDiscoveryHostLogs;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating DiscoveryHostLogs Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }


        /// <summary>
        /// Create <see cref="DiscoveryHostLogs" /> into discovery_host_logs table.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        DiscoveryHostLogs IDiscoveryHostLogsDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DiscoveryHostLogs_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_discovery_host_logs"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DiscoveryHostLogs>()).BuildEntity(reader, new DiscoveryHostLogs())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDiscoveryHostLogsDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DiscoveryHostLogs" /> from discovery_host_logs table.
        /// </summary>
        /// <returns>DiscoveryHostLogs List</returns>       
        IList<DiscoveryHostLogs> IDiscoveryHostLogsDataAccess.GetAll()
        {
            try
            {
                const string sp = "DiscoveryHostLogs_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_discovery_host_logs"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DiscoveryHostLogs>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDiscoveryHostLogsDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="DiscoveryHostLogs" /> from discovery_host_logs table.
        /// </summary>
        /// <returns>DiscoveryHostLogs List</returns>       
        IList<DiscoveryHostLogs> IDiscoveryHostLogsDataAccess.GetAllByScanId(int scanid)
        {
            try
            {
                if (scanid < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DiscoveryHost_GetAllByScanId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iScanID", DbType.Int32, scanid);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_discovery_host_logs"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DiscoveryHostLogs>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDiscoveryHostLogsDataAccess.GetAllByScanId" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        #endregion
    }
}
