﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class SVCGlobalMirrorORMetroConfigDataAccess : BaseDataAccess, ISVCGlobalMirrorORMetroConfigDataAccess
    {
         #region Constructors

        public SVCGlobalMirrorORMetroConfigDataAccess(Context context)
            : base(context)
        {
        }
         protected override IEntityBuilder<SVCGlobalMirror> CreateEntityBuilder<SVCGlobalMirror>()
        {
            return (new SVCGlobalMirrorORMetroConfigBuilder()) as IEntityBuilder<SVCGlobalMirror>;
        }

        #endregion Constructors

         SVCGlobalMirrorORMetroMirror ISVCGlobalMirrorORMetroConfigDataAccess.Add(SVCGlobalMirrorORMetroMirror svcglobalMirror)
        {
            try
            {
                const string sp = "SVC_GLOBALMIRROR_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, "iReplicationId", DbType.Int32, svcglobalMirror.ReplicationId);
                    Database.AddInParameter(cmd, "iPRServerId", DbType.Int32, svcglobalMirror.PRServerId);
                    Database.AddInParameter(cmd, "iDRServerId", DbType.Int32, svcglobalMirror.DRServerId);
                    Database.AddInParameter(cmd, "iPRIPADDRESS", DbType.AnsiString, svcglobalMirror.PRIPADDRESS);
                    Database.AddInParameter(cmd, "iDRIPADDRESS", DbType.AnsiString, svcglobalMirror.DRIPADDRESS);
                    Database.AddInParameter(cmd, "iPRProductID", DbType.AnsiString, svcglobalMirror.PRProductID);
                    Database.AddInParameter(cmd, "iDRProductID", DbType.AnsiString, svcglobalMirror.DRProductID);
                    Database.AddInParameter(cmd, "iPRRELEASE", DbType.AnsiString, svcglobalMirror.PRRELEASE);
                    Database.AddInParameter(cmd, "iDRRELEASE", DbType.AnsiString, svcglobalMirror.DRRELEASE);
                    Database.AddInParameter(cmd, "iPRLOGIN", DbType.AnsiString, svcglobalMirror.PRLOGIN);
                    Database.AddInParameter(cmd, "iDRLOGIN", DbType.AnsiString, svcglobalMirror.DRLOGIN);
                    Database.AddInParameter(cmd, "iPRCONSISTENTGROUPID", DbType.Int32, svcglobalMirror.PRCONSISTENTGROUPID);
                    Database.AddInParameter(cmd, "iDRCONSISTENTGROUPID", DbType.Int32, svcglobalMirror.DRCONSISTENTGROUPID);
                    Database.AddInParameter(cmd, "iPRRC_REL_NAME", DbType.AnsiString, svcglobalMirror.PRRC_REL_NAME);
                    Database.AddInParameter(cmd, "iDRRC_REL_NAME", DbType.AnsiString, svcglobalMirror.DRRC_REL_NAME);
                    Database.AddInParameter(cmd, "iPRDISKCONTROLLERID", DbType.Int32, svcglobalMirror.PRDISKCONTROLLERID);
                    Database.AddInParameter(cmd, "iDRDISKCONTROLLERID", DbType.Int32, svcglobalMirror.DRDISKCONTROLLERID);
                    Database.AddInParameter(cmd, "iPRCLUSTEREDSYSNODENAME", DbType.AnsiString, svcglobalMirror.PRCLUSTEREDSYSNODENAME);
                    Database.AddInParameter(cmd, "iDRCLUSTEREDSYSNODENAME", DbType.AnsiString, svcglobalMirror.DRCLUSTEREDSYSNODENAME);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        svcglobalMirror = reader.Read() ? CreateEntityBuilder<SVCGlobalMirrorORMetroMirror>().BuildEntity(reader, svcglobalMirror) : null;
                    }

                    if (svcglobalMirror == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "SVCGlobalMirror already exists. Please specify another svcglobalMirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this svcglobalMirror.");
                                }
                        }
                    }

                    return svcglobalMirror;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting SVCGlobalMirror Entry ", ex);
            }
        }

         SVCGlobalMirrorORMetroMirror ISVCGlobalMirrorORMetroConfigDataAccess.Update(SVCGlobalMirrorORMetroMirror svcglobalMirror)
        {
            try
            {
                const string sp = "SVC_GLOBALMIRROR_UPDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, "iReplicationId", DbType.Int32, svcglobalMirror.ReplicationId);
                    Database.AddInParameter(cmd, "iPRServerId", DbType.Int32, svcglobalMirror.PRServerId);
                    Database.AddInParameter(cmd, "iDRServerId", DbType.Int32, svcglobalMirror.DRServerId);
                    Database.AddInParameter(cmd, "iPRIPADDRESS", DbType.AnsiString, svcglobalMirror.PRIPADDRESS);
                    Database.AddInParameter(cmd, "iDRIPADDRESS", DbType.AnsiString, svcglobalMirror.DRIPADDRESS);
                    Database.AddInParameter(cmd, "iPRProductID", DbType.AnsiString, svcglobalMirror.PRProductID);
                    Database.AddInParameter(cmd, "iDRProductID", DbType.AnsiString, svcglobalMirror.DRProductID);
                    Database.AddInParameter(cmd, "iPRRELEASE", DbType.AnsiString, svcglobalMirror.PRRELEASE);
                    Database.AddInParameter(cmd, "iDRRELEASE", DbType.AnsiString, svcglobalMirror.DRRELEASE);
                    Database.AddInParameter(cmd, "iPRLOGIN", DbType.AnsiString, svcglobalMirror.PRLOGIN);
                    Database.AddInParameter(cmd, "iDRLOGIN", DbType.AnsiString, svcglobalMirror.DRLOGIN);
                    Database.AddInParameter(cmd, "iPRCONSISTENTGROUPID", DbType.Int32, svcglobalMirror.PRCONSISTENTGROUPID);
                    Database.AddInParameter(cmd, "iDRCONSISTENTGROUPID", DbType.Int32, svcglobalMirror.DRCONSISTENTGROUPID);
                    Database.AddInParameter(cmd, "iPRRC_REL_NAME", DbType.AnsiString, svcglobalMirror.PRRC_REL_NAME);
                    Database.AddInParameter(cmd, "iDRRC_REL_NAME", DbType.AnsiString, svcglobalMirror.DRRC_REL_NAME);
                    Database.AddInParameter(cmd, "iPRDISKCONTROLLERID", DbType.Int32, svcglobalMirror.PRDISKCONTROLLERID);
                    Database.AddInParameter(cmd, "iDRDISKCONTROLLERID", DbType.Int32, svcglobalMirror.DRDISKCONTROLLERID);
                    Database.AddInParameter(cmd, "iPRCLUSTEREDSYSNODENAME", DbType.AnsiString, svcglobalMirror.PRCLUSTEREDSYSNODENAME);
                    Database.AddInParameter(cmd, "iDRCLUSTEREDSYSNODENAME", DbType.AnsiString, svcglobalMirror.DRCLUSTEREDSYSNODENAME);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            svcglobalMirror = CreateEntityBuilder<SVCGlobalMirrorORMetroMirror>().BuildEntity(reader, svcglobalMirror);
                        }
                        else
                        {
                            svcglobalMirror = null;
                        }
                    }

                    if (svcglobalMirror == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "SVCGlobalMirror already exists. Please specify another svcglobalMirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this svcglobalMirror.");
                                }
                        }
                    }

                    return svcglobalMirror;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While updatinging SVCGlobalMirror Entry ", ex);
            }
        }

         SVCGlobalMirrorORMetroMirror ISVCGlobalMirrorORMetroConfigDataAccess.UpdateByReplicationId(SVCGlobalMirrorORMetroMirror svcglobalMirror)
         {
             try
             {
                 const string sp = "SVCGLOBALMIR_UPDATEBYREPID";

                 using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                 {
                     AddOutputParameter(cmd);
                     Database.AddInParameter(cmd, "iReplicationId", DbType.Int32, svcglobalMirror.ReplicationId);
                     Database.AddInParameter(cmd, "iPRServerId", DbType.Int32, svcglobalMirror.PRServerId);
                     Database.AddInParameter(cmd, "iDRServerId", DbType.Int32, svcglobalMirror.DRServerId);
                     Database.AddInParameter(cmd, "iPRIPADDRESS", DbType.AnsiString, svcglobalMirror.PRIPADDRESS);
                     Database.AddInParameter(cmd, "iDRIPADDRESS", DbType.AnsiString, svcglobalMirror.DRIPADDRESS);
                     Database.AddInParameter(cmd, "iPRProductID", DbType.AnsiString, svcglobalMirror.PRProductID);
                     Database.AddInParameter(cmd, "iDRProductID", DbType.AnsiString, svcglobalMirror.DRProductID);
                     Database.AddInParameter(cmd, "iPRRELEASE", DbType.AnsiString, svcglobalMirror.PRRELEASE);
                     Database.AddInParameter(cmd, "iDRRELEASE", DbType.AnsiString, svcglobalMirror.DRRELEASE);
                     Database.AddInParameter(cmd, "iPRLOGIN", DbType.AnsiString, svcglobalMirror.PRLOGIN);
                     Database.AddInParameter(cmd, "iDRLOGIN", DbType.AnsiString, svcglobalMirror.DRLOGIN);
                     Database.AddInParameter(cmd, "iPRCONSISTENTGROUPID", DbType.Int32, svcglobalMirror.PRCONSISTENTGROUPID);
                     Database.AddInParameter(cmd, "iDRCONSISTENTGROUPID", DbType.Int32, svcglobalMirror.DRCONSISTENTGROUPID);
                     Database.AddInParameter(cmd, "iPRRC_REL_NAME", DbType.AnsiString, svcglobalMirror.PRRC_REL_NAME);
                     Database.AddInParameter(cmd, "iDRRC_REL_NAME", DbType.AnsiString, svcglobalMirror.DRRC_REL_NAME);
                     Database.AddInParameter(cmd, "iPRDISKCONTROLLERID", DbType.Int32, svcglobalMirror.PRDISKCONTROLLERID);
                     Database.AddInParameter(cmd, "iDRDISKCONTROLLERID", DbType.Int32, svcglobalMirror.DRDISKCONTROLLERID);
                     Database.AddInParameter(cmd, "iPRCLUSTEREDSYSNODENAME", DbType.AnsiString, svcglobalMirror.PRCLUSTEREDSYSNODENAME);
                     Database.AddInParameter(cmd, "iDRCLUSTEREDSYSNODENAME", DbType.AnsiString, svcglobalMirror.DRCLUSTEREDSYSNODENAME);

#if ORACLE
                     cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                     using (IDataReader reader = Database.ExecuteReader(cmd))
                     {
                         if (reader.Read())
                         {
                             svcglobalMirror = CreateEntityBuilder<SVCGlobalMirrorORMetroMirror>().BuildEntity(reader, svcglobalMirror);
                         }
                         else
                         {
                             svcglobalMirror = null;
                         }
                     }

                     if (svcglobalMirror == null)
                     {
                         int returnCode = GetReturnCodeFromParameter(cmd);

                         switch (returnCode)
                         {
                             case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                 {
                                     throw new ArgumentException(
                                         "SVCGlobalMirror already exists. Please specify another svcglobalMirror.");
                                 }
                             default:
                                 {
                                     throw new SystemException(
                                         "An unexpected error has occurred while updating this svcglobalMirror.");
                                 }
                         }
                     }

                     return svcglobalMirror;
                 }
             }
             catch (Exception ex)
             {
                 throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                     ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                     "Error In DAL While updatinging SVCGlobalMirror Entry ", ex);
             }
         }
         public bool DeleteById(int id)
         {
             try
             {
                 if (id < 1)
                 {
                     throw new ArgumentNullException("id");
                 }

                 const string sp = "SVC_GLOBALMIRROR_DeleteById";

                 using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                 {
                     Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                     int returnCode = Database.ExecuteNonQuery(cmd);

                     return returnCode > 0;
                 }
             }
             catch (Exception exc)
             {
                 throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                     ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                     "Error In DAL While Deleting BusinessService Entry : " + id + Environment.NewLine +
                     "SYSTEM MESSAGE : " + exc.Message, exc);
             }
         }
         SVCGlobalMirrorORMetroMirror ISVCGlobalMirrorORMetroConfigDataAccess.GetByReplicationId(int id)
         {
             try
             {
                 const string sp = "SVC_GLOBALMIRROR_GETBYREPID";

                 using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                 {
                     Database.AddInParameter(cmd, "iId", DbType.Int32, id);

#if ORACLE
                     cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                     using (IDataReader reader = Database.ExecuteReader(cmd))
                     {
                         return reader.Read()
                             ? (CreateEntityBuilder<SVCGlobalMirrorORMetroMirror>()).BuildEntity(reader, new SVCGlobalMirrorORMetroMirror())
                             : null;
                     }
                 }
             }
             catch (Exception ex)
             {
                 throw new CpException(CpExceptionType.DataAccessFetchOperation,
                     ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                     "Error In DAL While Executing Function Signature ISVCGlobalMirrorORMetroConfigDataAccess.GetByReplicationId", ex);
             }
         }

         IList<SVCGlobalMirrorORMetroMirror> ISVCGlobalMirrorORMetroConfigDataAccess.GetAll(string replicationtype)
         {
             try
             {
                 const string sp = "SVC_GLOBALMIRROR_GETALL";

                 using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                 {
                     Database.AddInParameter(cmd, "iTypeId", DbType.AnsiString, replicationtype);
#if ORACLE
                     cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                     using (IDataReader reader = Database.ExecuteReader(cmd))
                     {
                         return BuildSVCGlobalMirrorEntities(reader);
                     }
                 }
             }
             catch (Exception ex)
             {
                 throw new CpException(CpExceptionType.DataAccessFetchOperation,
                     ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                     "Error In DAL While Executing Function Signature IGlobalMirrorDataAccess.GetAll", ex);
             }
         }

         IList<SVCGlobalMirrorORMetroMirror> ISVCGlobalMirrorORMetroConfigDataAccess.GetByCompanyId(int companyId, bool isParent, string replicationtype)
         {
             try
             {
                 const string sp = "SVCGLOBALMIRROR_GETBYCOMPANYID";

                 using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                 {
                     Database.AddInParameter(cmd, "iCompanyId", DbType.Int32, companyId);
                     Database.AddInParameter(cmd, "iisParent", DbType.Int32, isParent);
                     Database.AddInParameter(cmd, "TypeId", DbType.String, replicationtype);
#if ORACLE
                     cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                     using (IDataReader reader = Database.ExecuteReader(cmd))
                     {
                         //return CreateEntityBuilder<GlobalMirror>().BuildEntities(reader);
                         return BuildSVCGlobalMirrorEntities(reader);
                     }
                 }
             }
             catch (Exception ex)
             {
                 throw new CpException(CpExceptionType.DataAccessFetchOperation,
                     ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                     "Error In DAL While Executing Function Signature IGlobalMirrorDataAccess.GetByCompanyId(" +
                     companyId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
             }
         }

        private IList<SVCGlobalMirrorORMetroMirror> BuildSVCGlobalMirrorEntities(IDataReader reader)
        {
            var globalMirrors = new List<SVCGlobalMirrorORMetroMirror>();

            while (reader.Read())
            {
                var svcglobalMirror = new SVCGlobalMirrorORMetroMirror();

                svcglobalMirror.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                svcglobalMirror.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
                svcglobalMirror.ReplicationBase.Type = Convert.IsDBNull(reader["Type"]) ? ReplicationType.Undefined : (ReplicationType)Enum.Parse(typeof(ReplicationType), Convert.ToString(reader["Type"]), true);
                        

                /*
                svcglobalMirror.ReplicationId = Convert.IsDBNull(reader["ReplicationId"])
                    ? 0
                    : Convert.ToInt32(reader["ReplicationId"]);                

                svcglobalMirror.PRServerId = Convert.IsDBNull(reader["PRServerId"])
                    ? 0
                    : Convert.ToInt32(reader["PRServerId"]);

                svcglobalMirror.DRServerId = Convert.IsDBNull(reader["DRServerId"])
                    ? 0
                    : Convert.ToInt32(reader["DRServerId"]); */
                //svcglobalMirror.Id = Convert.IsDBNull(reader["SVCID"]) ? 0 : Convert.ToInt32(reader["SVCID"]);
                svcglobalMirror.PRIPADDRESS = Convert.IsDBNull(reader["PRIPADDRESS"])
                    ? string.Empty
                    : Convert.ToString(reader["PRIPADDRESS"]);

                svcglobalMirror.DRIPADDRESS = Convert.IsDBNull(reader["DRIPADDRESS"])
                    ? string.Empty
                    : Convert.ToString(reader["DRIPADDRESS"]);
               
                svcglobalMirror.PRProductID = Convert.IsDBNull(reader["PRProductID"])
                  ? string.Empty
                     : Convert.ToString(reader["PRProductID"]);

                svcglobalMirror.DRProductID = Convert.IsDBNull(reader["DRProductID"])
                    ? string.Empty
                    : Convert.ToString(reader["DRProductID"]);

                /*
               svcglobalMirror.PRRELEASE = Convert.IsDBNull(reader["PRRELEASE"])
                    ? string.Empty
                  : Convert.ToString(reader["PRRELEASE"]);

               svcglobalMirror.DRRELEASE = Convert.IsDBNull(reader["DRRELEASE"])
                    ? string.Empty
                  : Convert.ToString(reader["DRRELEASE"]);

               svcglobalMirror.PRLOGIN = Convert.IsDBNull(reader["PRLOGIN"])
                   ? string.Empty
                   : Convert.ToString(reader["PRLOGIN"]);
               svcglobalMirror.DRLOGIN = Convert.IsDBNull(reader["DRLOGIN"])
                   ? string.Empty
                   : Convert.ToString(reader["DRLOGIN"]);
               svcglobalMirror.PRCONSISTENTGROUPID = Convert.IsDBNull(reader["PRCONSISTENTGROUPID"])
                   ? 0
                   : Convert.ToInt32(reader["PRCONSISTENTGROUPID"]);
               svcglobalMirror.DRCONSISTENTGROUPID = Convert.IsDBNull(reader["DRCONSISTENTGROUPID"])
                   ? 0
                   : Convert.ToInt32(reader["DRCONSISTENTGROUPID"]);
               svcglobalMirror.PRRC_REL_NAME = Convert.IsDBNull(reader["PRRC_REL_NAME"])
                   ? string.Empty
                   : Convert.ToString(reader["PRRC_REL_NAME"]);
               svcglobalMirror.DRRC_REL_NAME = Convert.IsDBNull(reader["DRRC_REL_NAME"])
                   ? string.Empty
                   : Convert.ToString(reader["DRRC_REL_NAME"]);
               svcglobalMirror.PRDISKCONTROLLERID = Convert.IsDBNull(reader["PRDISKCONTROLLERID"])
                   ? 0
                   : Convert.ToInt32(reader["PRDISKCONTROLLERID"]);
               svcglobalMirror.DRDISKCONTROLLERID = Convert.IsDBNull(reader["DRDISKCONTROLLERID"])
                   ? 0
                   : Convert.ToInt32(reader["DRDISKCONTROLLERID"]);
                           svcglobalMirror.PRCLUSTEREDSYSNODENAME = Convert.IsDBNull(reader["PRCLUSTEREDSYSNODENAME"])
                   ? string.Empty
                   : Convert.ToString(reader["PRCLUSTEREDSYSNODENAME"]);
                           svcglobalMirror.DRCLUSTEREDSYSNODENAME = Convert.IsDBNull(reader["DRCLUSTEREDSYSNODENAME"])
                   ? string.Empty
                   : Convert.ToString(reader["DRCLUSTEREDSYSNODENAME"]); */


                            globalMirrors.Add(svcglobalMirror);
            }

            return (globalMirrors.Count > 0) ? globalMirrors : null;
        }

        IList<SVCGlobalMirrorORMetroMirror> ISVCGlobalMirrorORMetroConfigDataAccess.GetSVCGMReplicationByLoginId(string replicationtype, int id)
        {
            try
            {
                const string sp = "SVCReplication_GetByLoginId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iTypeId", DbType.String, replicationtype);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildSVCGlobalMirrorEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IReplicationBaseDataAccess.GetByLoginId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

    }
}
