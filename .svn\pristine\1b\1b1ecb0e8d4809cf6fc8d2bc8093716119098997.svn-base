div.datepicker {
	position: relative;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 10px;
	width: 196px;
	height: 147px;
	position: absolute;
	cursor: default;
	top: 0;
	left: 0;
	z-index:999982;
	display: none;
}
.datepickerContainer {
	background: #ffffff;
	position: absolute;

	border:1px solid #000;
	padding:5px;
}

.datepickerHidden {
	display: none;
}
div.datepicker table {
	border-collapse:collapse;
}
div.datepicker a {
	color: #000;
	text-decoration: none;
	cursor: default;
	outline: none;
}
div.datepicker table td {
	text-align: right;
	padding: 0;
	margin: 0;
}
div.datepicker th {
	text-align: center;
	color: #999;
	font-weight: normal;
}
div.datepicker tbody th {
	text-align: left;
}
div.datepicker tbody a {
	display: block;
}
.datepickerDays a {
	width: 20px;
	line-height: 16px;
	height: 16px;
	padding-right: 2px;
}
.datepickerYears a,
.datepickerMonths a{
	width: 43px;
	line-height: 36px;
	height: 35px;
	text-align: center;
}
td.datepickerNotInMonth a {
	color: #666;
}
tbody.datepickerDays td.datepickerSelected{
	border:1px solid #06c;
	background:rgba(0,102,204,0.2);
}
tbody.datepickerDays td.datepickerNotInMonth.datepickerSelected {
	background: #17384d;
}
tbody.datepickerYears td.datepickerSelected,
tbody.datepickerMonths td.datepickerSelected{
	background: #17384d;
}
div.datepicker a:hover,
div.datepicker a:hover {
		color:#06c;
}
div.datepicker td.datepickerNotInMonth a:hover {
	color: #999;
}
div.datepicker tbody th {
	text-align: left;
}
.datepickerSpace div {
	width: 20px;
}
.datepickerGoNext a,
.datepickerGoPrev a,
.datepickerMonth a {
	text-align: center;
	height: 20px;
	line-height: 20px;
}
.datepickerGoNext a {
	float: right;
	width: 20px;
}
.datepickerGoPrev a {
	float: left;
	width: 20px;
}
table.datepickerViewDays tbody.datepickerMonths,
table.datepickerViewDays tbody.datepickerYears {
	display: none;
}
table.datepickerViewMonths tbody.datepickerDays,
table.datepickerViewMonths tbody.datepickerYears,
table.datepickerViewMonths tr.datepickerDoW {
	display: none;
}
table.datepickerViewYears tbody.datepickerDays,
table.datepickerViewYears tbody.datepickerMonths,
table.datepickerViewYears tr.datepickerDoW {
	display: none;
}
td.datepickerDisabled a,
td.datepickerDisabled.datepickerNotInMonth a{
	color: #333;
}
td.datepickerDisabled a:hover {
	color: #333;
}
td.datepickerSpecial a {
	background: #700;
}
td.datepickerSpecial.datepickerSelected a {
	background: #a00;
}
div.datepicker thead tr[class=datepickerDoW] th {
	text-align: right;
}

div.datepicker thead tr[class=datepickerDoW] th:first-child {
text-align: left;
}