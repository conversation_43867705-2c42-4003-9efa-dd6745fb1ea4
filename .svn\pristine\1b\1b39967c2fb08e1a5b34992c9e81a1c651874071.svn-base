﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IEventManagementDataAccess
    {
        EventManagements Add(EventManagements eventmanagement);

        EventManagements Update(EventManagements eventmanagement);

        EventManagements GetbyId(int id);

        IList<EventManagements> GetAll();

        bool DeletebyId(int id);
    }
}