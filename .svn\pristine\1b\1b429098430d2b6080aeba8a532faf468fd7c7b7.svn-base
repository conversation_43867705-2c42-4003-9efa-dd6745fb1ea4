﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IDatabaseOracleRacDataAccess
    {
        DatabaseOracleRac Add(DatabaseOracleRac database);

        DatabaseOracleRac Update(DatabaseOracleRac database);

        DatabaseOracleRac GetByDatabaseBaseId(int databaseBaseId);

        DatabaseOracleRac UpdateByDatabaseBaseId(DatabaseOracleRac database);

        DatabaseOracleRac GetById(int id);

        IList<DatabaseOracleRac> GetAll();

        bool DeleteById(int id);

        bool IsExistByName(string id);
    }
}