﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using Gios.Pdf;
using SpreadsheetGear;
using CP.Helper;
using System.IO;


namespace CP.UI.Controls
{
    public partial class DRReaddynesssummary : BaseControl
    {

        public override void PrepareView()
        {
            IList<BusinessService> businessServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);
            if (businessServiceList != null)
            {
                ddlBussService.DataSource = businessServiceList;
                ddlBussService.DataTextField = "Name";
                ddlBussService.DataValueField = "Id";
                ddlBussService.DataBind();
                ddlBussService.Items.Insert(0, "ALL");
            }
            else
            {
                ddlBussService.Items.Insert(0, new ListItem("No Data Found", "0"));
            }

            Pan1.Visible = false;
            btnview.Visible = true;
            btnExcel.Visible = false;
            btnPdf.Visible = false;

        }

        private DataTable screenreport()
        {
            var table = new DataTable();
            table.Columns.Add("Sr.No.");
            table.Columns.Add("BusinessService Name");
            table.Columns.Add("BusinessFunction Name");
            table.Columns.Add("Infraobject Name");
            table.Columns.Add("WorkFlow Name");
            table.Columns.Add("Start Time");
            table.Columns.Add("End Time");
            table.Columns.Add("DR Ready");

            int bsid = Convert.ToInt32(ddlBussService.SelectedValue);
            int bfid = Convert.ToInt32(ddlfn.SelectedValue); ;
            int infraid = 0;

            //GetInfraSchedularDetails
            IList<InfraobjectSchedularLogs> infraschedulelist = new List<InfraobjectSchedularLogs>();

            if (ddlfn.SelectedItem.Text == "ALL")
                infraschedulelist = Facade.GetDRReddynesssummary(bsid, 0, 0);

            else if (ddlfn.SelectedItem.Text != "ALL" && ddlinfra.SelectedItem.Text == "ALL")
                infraschedulelist = Facade.GetDRReddynesssummary(bsid, bfid, 0);

            else if (ddlfn.SelectedItem.Text != "ALL" && ddlinfra.SelectedItem.Text != "ALL")
                infraschedulelist = Facade.GetDRReddynesssummary(bsid, bfid, Convert.ToInt32(ddlinfra.SelectedValue));




            if (infraschedulelist != null && infraschedulelist.Count != 0)
            {
                var trow = new TableRow();
                tbl.Rows.Add(trow);
                trow.Height = 30;

                var sno = new TableCell { Text = "Sr.No", CssClass = "RowStyleHeaderNo bold" };
                trow.Cells.Add(sno);
                var bsname = new TableCell { Text = "BusinessService Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(bsname);
                var bfname = new TableCell { Text = "BusinessFunction Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(bfname);
                var infraname = new TableCell { Text = "Infraobject Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(infraname);
                var workflowname = new TableCell { Text = "WorkFlow Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(workflowname);
                var stime = new TableCell { Text = "Start Time", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(stime);
                var etime = new TableCell { Text = "End Time", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(etime);
                var drready = new TableCell { Text = "DR Ready", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(drready);

                int i = 1;
                foreach (var avtivitydata in infraschedulelist)
                {

                    var tbrow = new TableRow();
                    tbrow.Height = 20;
                    tbrow.BackColor = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                    tbl.Rows.Add(tbrow);
                    DataRow dr = table.NewRow();


                    dr["Sr.No."] = i.ToString();
                    var no = new TableCell { Text = i.ToString(), CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(no);

                    dr["BusinessService Name"] = avtivitydata.BusinessServiceName != string.Empty ? avtivitydata.BusinessServiceName : "NA";
                    var bs = new TableCell { Text = avtivitydata.BusinessServiceName != string.Empty ? avtivitydata.BusinessServiceName : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(bs);

                    dr["BusinessFunction Name"] = avtivitydata.BusinessFunctionName != string.Empty ? avtivitydata.BusinessFunctionName : "NA";
                    var bf = new TableCell { Text = avtivitydata.BusinessFunctionName != string.Empty ? avtivitydata.BusinessFunctionName : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(bf);

                    dr["Infraobject Name"] = avtivitydata.InfraObjectName != string.Empty ? avtivitydata.InfraObjectName.ToString() : "NA";
                    var infname = new TableCell { Text = avtivitydata.InfraObjectName != string.Empty ? avtivitydata.InfraObjectName.ToString() : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(infname);

                    dr["WorkFlow Name"] = avtivitydata.WorkflowName != string.Empty ? avtivitydata.WorkflowName : "NA";
                    var wn = new TableCell { Text = avtivitydata.WorkflowName != string.Empty ? avtivitydata.WorkflowName : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(wn);

                    dr["Start Time"] = avtivitydata.StartTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(avtivitydata.StartTime).ToString("MM-DD-YYYY hh:mm:ss tt")) : "NA";
                    var sdate = new TableCell { Text = avtivitydata.StartTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(avtivitydata.StartTime).ToString("MM-DD-YYYY hh:mm:ss tt")) : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(sdate);

                    dr["End Time"] = avtivitydata.EndTime != DateTime.MinValue ? Convert.ToString(avtivitydata.EndTime) : "NA";
                    var edate = new TableCell { Text = avtivitydata.EndTime != DateTime.MinValue ? Convert.ToString(avtivitydata.EndTime) : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(edate);

                    dr["DR Ready"] = avtivitydata.DRReady != string.Empty ? Convert.ToString(avtivitydata.DRReady) : "NA";
                    var drrdy = new TableCell { Text = avtivitydata.DRReady != string.Empty ? Convert.ToString(avtivitydata.DRReady) : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(drrdy);

                    i++;
                    table.Rows.Add(dr);
                }

            }
            else
            {
                lblMsg.Visible = true;
                lblMsg.Text = "No Records Found";
            }

            return table;
        }

        private void CreatePdfReport(DataTable table)
        {
            var myPdfDocument = new PdfDocument(PdfDocumentFormat.InCentimeters(21, 29.7));
            var count = table.Rows.Count;
            PdfTable myPdfTable = myPdfDocument.NewTable(new Font("Verdana", 7), count, 8, 3);
            myPdfTable.ImportDataTable(table);
            myPdfTable.HeadersRow.SetColors(Color.White, Color.FromArgb(79, 129, 189));

            myPdfTable.SetBorders(Color.Black, 0.1, BorderType.None);
            myPdfTable.SetColors(Color.Black, Color.FromArgb(219, 229, 241), Color.White);
            myPdfTable.SetColumnsWidth(new[] { 5, 20, 16, 20, 20, 20, 20, 20 });
            myPdfTable.SetContentAlignment(ContentAlignment.MiddleCenter);
            myPdfTable.Columns[1].SetContentAlignment(ContentAlignment.MiddleCenter);

            //var getuseractvdate = Facade.GetUserActivityByStartEndDate(txtstart.Text, txtend.Text);


            PdfImage logoImage = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"));
            PdfImage logoBcms = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"));
            ////By RD-26-2015 
            PdfImage logoIBM = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/ReportLogo/Customer_Logo.jpg"));
            PdfImage logoIDEA = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/ReportLogo/Partner_Logo.jpg"));
            ////upto this 

            var pta = new PdfTextArea(new Font("Verdana", 9, FontStyle.Bold), Color.Black
                    , new PdfArea(myPdfDocument, 0, 20, 595, 80), ContentAlignment.MiddleCenter, "DR Readiness Summary Report");


            var RGT = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 14, 00, 200, 190), ContentAlignment.MiddleRight, "Report Generated Time : " +Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")));// + DateTime.Now.ToString("dd-MMM-yyyy HH:mm"));

            //var from = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
            //         , new PdfArea(myPdfDocument, 180, 00, 150, 190), ContentAlignment.MiddleRight, "From Date: " + txtstart.Text);

            //var to = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
            //         , new PdfArea(myPdfDocument, 230, 00, 200, 190), ContentAlignment.MiddleRight, "To Date: " + txtend.Text);

            var notavailable = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 17, 15, 100, 190), ContentAlignment.MiddleRight, "NA : Not Available");



            int pgNo = 1;
            while (!myPdfTable.AllTablePagesCreated)
            {
                PdfPage newPdfPage = myPdfDocument.NewPage();
                PdfTablePage newPdfTablePage =
                        myPdfTable.CreateTablePage(new PdfArea(myPdfDocument, 48, 115, 500, 670));

                var pageNumber = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                   , new PdfArea(myPdfDocument, 50, 0, 450, 1600), ContentAlignment.MiddleRight, "Page Number :   " + pgNo++.ToString());

                newPdfPage.Add(logoImage, 460, 25, 180);
                newPdfPage.Add(logoBcms, 50, 25, 110);
                ////By RD-26-2015
                newPdfPage.Add(logoIDEA, 460, 42, 160);
                newPdfPage.Add(logoIBM, 50, 42, 160);
                ////upto this

                newPdfPage.Add(newPdfTablePage);
                newPdfPage.Add(pta);


                //newPdfPage.Add(from);
                //newPdfPage.Add(to);
                newPdfPage.Add(RGT);
                newPdfPage.Add(notavailable);
                newPdfPage.Add(pageNumber);
                newPdfPage.SaveToDocument();
            }

            string str = DateTime.Now.ToString().Replace("/", "");
            str = str.Replace(":", "");
            str = str.Substring(0, str.Length - 5);
            str = System.Text.RegularExpressions.Regex.Replace(str, @"\s", "");
            str = "DRReadinessSummary" + str + ".pdf";
            string filePath = HttpContext.Current.Server.MapPath(@"~/PdfFiles/" + str);
            //string myUrl = "/PdfFiles/" + str;
            myPdfDocument.SaveToFile(filePath);
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/PdfFiles/" + str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=DRReadiness Summary Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);

        }

        public static IWorkbook ExcelReport(int bsid, int bfid, int infraid, string drready, string strlogo)
        {
            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "DR Readiness Summary Report";

            _cells["A1"].ColumnWidth = 7;

            _cells["E3"].Formula = "DR Readiness Summary Report";
            _cells["E3"].HorizontalAlignment = HAlign.Center;
            _cells["E3"].Font.Bold = true;
            _cells["B3:K6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:K3"].Font.Size = 11;
            _cells["B5:K8"].Font.Size = 10;
            _cells["B3:K8"].Font.Color = Color.White;
            _cells["B3:K8"].Font.Name = "Cambria";


            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 48, 10, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 690, 10, 120, 13);

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 410, 10, 121, 13);
            }

            reportWorksheet.Cells["A2:F2"].RowHeight = 29;
            ////upto this
            reportWorksheet.Cells["A1:F1"].RowHeight = 27;

         
            var dateTime = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));///DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss");
            _cells["B5"].Formula = "Report Generated Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            _cells["C5"].NumberFormat = "@";
            _cells["C5"].Formula = ":  " + (dateTime);
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;

          

            int row = 8;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "Sr.No.";
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["B8:K8"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B8:I8"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "BusinessService Name";
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["C" + row.ToString()].VerticalAlignment = VAlign.Center;

            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
            productionlog.WrapText = true;

            _cells["D" + row.ToString()].Formula = "BusinessFunction Name";
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["D" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["E" + row.ToString()].Formula = "Infraobject Name";
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["E" + row.ToString()].VerticalAlignment = VAlign.Center;


            _cells["F" + row.ToString()].Formula = "DR Ready WorkFlow Name";
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["F" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["G" + row.ToString()].Formula = "DR Action Name";
            _cells["G" + row.ToString()].Font.Bold = true;
            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["G" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["H" + row.ToString()].Formula = "Execution Start Time";
            _cells["H" + row.ToString()].Font.Bold = true;
            _cells["H" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["H" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["I" + row.ToString()].Formula = "Execution End Time";
            _cells["I" + row.ToString()].Font.Bold = true;
            _cells["I" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["I" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["J" + row.ToString()].Formula = "DR Ready";
            _cells["J" + row.ToString()].Font.Bold = true;
            _cells["J" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["J" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["K" + row.ToString()].Formula = "Execution Failure Reason";
            _cells["K" + row.ToString()].Font.Bold = true;
            _cells["K" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["K" + row.ToString()].VerticalAlignment = VAlign.Center;

            row++;
            int dataCount = 0;
            int xlRow = 9;

                IFacade _facade = new Facade();
                string strDrilUnSuccess = string.Empty;

            IList<InfraObject> allInfraList = new List<InfraObject>();
            IList<ParallelWorkflowActionResult> PWAction = new List<ParallelWorkflowActionResult>();

            IList<InfraobjectSchedularLogs> infraschedulelist = new List<InfraobjectSchedularLogs>();

            if (bsid > 0)
            {
                allInfraList = _facade.GetInfraObjectByBusinessServiceId(bsid);
                if (bfid > 0)
                {
                    allInfraList = _facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(bsid, bfid);
                    if (infraid > 0)
                    {
                        allInfraList = (from infra in allInfraList
                                        where infra.Id == infraid
                                        select infra).ToList();
                    }
                }
                else
                {
                    if (infraid > 0)
                    {
                        allInfraList = (from infra in allInfraList
                                        where infra.Id == infraid
                                        select infra).ToList();
                    }
                }
            }
            else
            {
                allInfraList = _facade.GetAllInfraObject();
            }

            InfrascheduleWfdeatils _schuwfdtls = null;

            if (allInfraList != null)
            {
                foreach (var infraItem in allInfraList)
                {
                    _schuwfdtls = null;
                    string actions = _facade.GetInfraActionById(infraItem.Id);
                    var funObj = _facade.GetBusinessFunctionById(infraItem.BusinessFunctionId);
                    string funName = funObj != null ? funObj.Name : "";
                    bool _isScheduleWF = false;
                    if (!string.IsNullOrEmpty(actions))
                    {
                        PWAction = _facade.GetInfraDrillCount(infraItem.Id, actions);
                        //var PW_Filter = _facade.GetInfraDrillCount(infraItem.Id, actions);
                        //string[] id = actions.Split(',');
                        //if (PW_Filter != null)
                        //{
                        //    PWAction = (from PA in PW_Filter
                        //                where id.Contains(PA.ActionId.ToString())
                        //                select PA).ToList();
                        //}

                        var _schuwfdtlslist = _facade.GetscheduleByInfraId(infraItem.Id);
                        if (_schuwfdtlslist != null)
                            _schuwfdtls = _schuwfdtlslist.ToList().FirstOrDefault();

                        if (PWAction != null && PWAction.Count > 0 && _schuwfdtls != null)
                        {
                            var chkPWAction = PWAction.GroupBy(x => x.ParallelGroupWorkflowId).ToList().FirstOrDefault();
                            if (chkPWAction != null)
                            {
                                //var UnsucceAction = chkPWAction.Where(x => x.Status.ToString().Equals("Error")).FirstOrDefault();
                                var UnsucceAction = chkPWAction.OrderByDescending(x => x.EndTime).FirstOrDefault();
                                if (UnsucceAction != null)
                                {
                                    if (UnsucceAction.EndTime < _schuwfdtls.EndTime && _schuwfdtls.Status != null)     //if (UnsucceAction.CreateDate < _schuwfdtls.CreateDate && _schuwfdtls.Status != null)
                                        _isScheduleWF = true;
                                    else
                                        _isScheduleWF = false;
                                }
                                else
                                    _isScheduleWF = true;
                            }
                        }
                        else if (PWAction != null && _schuwfdtls == null)
                            _isScheduleWF = false;
                        else
                            _isScheduleWF = true;

                        int column = 0;
                        string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };
                        string ndx = xlColumn[column] + row.ToString();

                        if (_isScheduleWF)
                        {
                            if (_schuwfdtls != null)
                            {
                                if (_schuwfdtls.Status == "Error"  || _schuwfdtls.Status == "Completed" || _schuwfdtls.Status == "Success")
                                {
                                    //strDrilUnSuccess += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow Confgured Drill Execution Failed." + "_" + "<b>Start time : </b>" + _schuwfdtls.StartTime + "<br> <b>End Time   : </b>" + _schuwfdtls.EndTime + "<br><b> Reason   : </b>" + _schuwfdtls.Message.Replace('@', ' ') + "'></span></div></td></tr>";

                                    #region Bind Excel Values

                                    dataCount++;
                                    //int column = 0;
                                    //string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };
                                    xlRow++;

                                    //string ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx + ":" + "K" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;

                                    _cells[ndx].Formula = i.ToString();
                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 23;
                                    _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    i++;
                                    column++;

                                    var value = _facade.GetBusinessServiceById(infraItem.BusinessServiceId);

                                    ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx].Formula = value != null ? value.Name : "NA";
                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 23;
                                    _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    column++;

                                    ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx].Formula =  !string.IsNullOrEmpty(funName )? funName : "NA";
                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 23;
                                    _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    column++;

                                    ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx].Formula =  !string.IsNullOrEmpty(infraItem.Name) ? infraItem.Name : "NA";
                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 23;
                                    _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    _cells[ndx].WrapText = true;
                                    column++;

                                    ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx].Formula =  !string.IsNullOrEmpty(_schuwfdtls.WorkflowName)? _schuwfdtls.WorkflowName : "NA";
                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 23;
                                    _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    column++;

                                    ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx].Formula =  !string.IsNullOrEmpty(_schuwfdtls.CurrentActionName)  ? _schuwfdtls.CurrentActionName : "NA";
                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 30;
                                    _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].WrapText = true;

                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    column++;

                                    ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx].EntireColumn.NumberFormat = "@";
                                    _cells[ndx].Formula = _schuwfdtls.StartTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(_schuwfdtls.StartTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";

                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 23;
                                    _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    _cells[ndx].WrapText = true;
                                    column++;


                                    ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx].EntireColumn.NumberFormat = "@";
                                    _cells[ndx].Formula = _schuwfdtls.EndTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(_schuwfdtls.EndTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                                    _cells[ndx].Font.Size = 10;

                                    _cells[ndx].ColumnWidth = 23;
                                    _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    column++;

                                    #region Calculate DR Ready

                                    IList<InfraObject> InfraObjectList_1 = null;
                                    int infraObjectCount_1 = 0;
                                    int drReadynessCount_1 = 0;
                                    int allInfraObjectCount_1 = 0;

                                    int businessServiceDRReadyCount_1 = 0;
                                    string serviceTable_1 = GetInfraDrillInfo_1(infraItem.BusinessServiceId);


                                    InfraObjectList_1 = new List<InfraObject>();
                                    InfraObjectList_1 = _facade.GetInfraObjectByBusinessServiceId(infraItem.BusinessServiceId);

                                    string isDRReady = string.Empty;
                                    string infraObjNameList = string.Empty;
                                    if (InfraObjectList_1 != null && InfraObjectList_1.Count() > 0)
                                    {

                                        allInfraObjectCount_1 = InfraObjectList_1.Count;

                                        infraObjectCount_1 = infraObjectCount_1 + allInfraObjectCount_1;
                                        drReadynessCount_1 = Convert.ToInt16(InfraObjectList_1.Count(a => a.IsDRReady == 1));

                                        //FilterBSlist.Add(bService);

                                        bool ready = false;
                                        if (!string.IsNullOrEmpty(serviceTable_1))
                                        {
                                            string[] tempArr = serviceTable_1.Split('^');
                                            if (string.IsNullOrEmpty(tempArr[0]))
                                                ready = true;
                                        }
                                        // Considered DRReadyness at business service level i.e.if anyone of infraObject is not having DRReady then that businessService is not DRReady.
                                        if (allInfraObjectCount_1 == drReadynessCount_1 && ready == true)
                                        {
                                            businessServiceDRReadyCount_1 = businessServiceDRReadyCount_1 + 1;
                                            isDRReady = "Y";
                                        }
                                        else
                                        {
                                            isDRReady = "N";
                                        }
                                    }
                                    #endregion Calculate DR Ready

                                    ndx = xlColumn[column] + row.ToString();
                                    string drr = isDRReady.Equals("Y") ? "YES" : "NO";
                                    _cells[ndx].Formula = drr;
                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 15;

                                    if (drr == "YES")
                                        _cells[ndx].Font.Color = Color.Green;
                                    else if (drr == "NO")
                                        _cells[ndx].Font.Color = Color.Red;
                                    else
                                        _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    column++;


                                    string Message_1 = string.Empty;

                                    infraschedulelist = _facade.GetDRReddynesssummary(infraItem.BusinessServiceId, 0, 0);
                                    if (infraschedulelist != null && infraschedulelist.Count > 0)
                                    {
                                        var Get_reason = (from val in infraschedulelist
                                                          where
                                                          val.InfraObjectId == infraItem.Id &&
                                                          val.CurrentActionId == _schuwfdtls.CurrentActionId &&
                                                          val.WorkflowId == _schuwfdtls.WorkflowId
                                                          orderby val.Id descending
                                                          select val
                                                              ).FirstOrDefault();
                                        if (Get_reason != null)
                                        {
                                            Message_1 = Get_reason.DRReason;
                                        }
                                    }

                                    ndx = xlColumn[column] + row.ToString();
                                    _cells[ndx].Formula = !string.IsNullOrEmpty(Message_1) ? Message_1 : "NA"; // !string.IsNullOrEmpty(_schuwfdtls.Message) ? _schuwfdtls.Message : "NA";
                                    _cells[ndx].Font.Size = 10;
                                    _cells[ndx].ColumnWidth = 40;
                                    _cells[ndx].WrapText = true;
                                    if (drr == "YES")
                                    {
                                        _cells[ndx].Font.Color = Color.Green;
                                        _cells[ndx].Formula = "";
                                    }
                                    else if (drr == "NO")
                                        _cells[ndx].Font.Color = Color.Red;
                                    else
                                        _cells[ndx].Font.Color = Color.Black;
                                    _cells[ndx].HorizontalAlignment = HAlign.Center;
                                    column++;

                                    row++;

                                    #endregion Bind Excel Values
                                }
                            }
                        }
                        else
                        {
                            if (PWAction != null && PWAction.Count > 0)
                            {
                                var tempPWAction = PWAction.GroupBy(x => x.ParallelGroupWorkflowId).ToList().FirstOrDefault();
                                if (tempPWAction != null)
                                {
                                    var UnsucceAction_E = tempPWAction.Where(x => x.Status.ToString().Equals("Error")).FirstOrDefault();
                                    var UnsucceAction_c = tempPWAction.Where(x => x.Status.ToString().Equals("Completed")).FirstOrDefault();
                                    var UnsucceAction_s = tempPWAction.Where(x => x.Status.ToString().Equals("Success")).FirstOrDefault();
                                    if (UnsucceAction_E != null)
                                    { 
                                        //strDrilUnSuccess += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow Confgured Drill Execution Failed." + "" + "<b>Start time : </b>" + UnsucceAction.StartTime + "<br> <b>End Time   : </b>" + UnsucceAction.EndTime + "<br><b> Reason   : </b>" + UnsucceAction.Message.Replace('@', ' ') + "'></span></div></td></tr>";

                                        #region Bind Excel Values

                                        dataCount++;
                                        //int column = 0;
                                        //string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };
                                        xlRow++;

                                        //string ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx + ":" + "K" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;

                                        _cells[ndx].Formula = i.ToString();
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        i++;
                                        column++;

                                        var value = _facade.GetBusinessServiceById(infraItem.BusinessServiceId);

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = value != null ? value.Name : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(funName) ? funName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(infraItem.Name) ? infraItem.Name : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        _cells[ndx].WrapText = true;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_E.WorkflowName) ? UnsucceAction_E.WorkflowName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_E.WorkflowActionName) ? UnsucceAction_E.WorkflowActionName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 30;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].WrapText = true;

                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].EntireColumn.NumberFormat = "@";
                                        _cells[ndx].Formula = UnsucceAction_E.StartTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(UnsucceAction_E.StartTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";

                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        _cells[ndx].WrapText = true;
                                        column++;


                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].EntireColumn.NumberFormat = "@";
                                        _cells[ndx].Formula = UnsucceAction_E.EndTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(UnsucceAction_E.EndTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                                        _cells[ndx].Font.Size = 10;

                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;


                                        #region Calculate DR Ready

                                        IList<InfraObject> InfraObjectList_1 = null;
                                        int infraObjectCount_1 = 0;
                                        int drReadynessCount_1 = 0;
                                        int allInfraObjectCount_1 = 0;

                                        int businessServiceDRReadyCount_1 = 0;
                                        string serviceTable_1 = GetInfraDrillInfo_1(infraItem.BusinessServiceId);


                                        InfraObjectList_1 = new List<InfraObject>();
                                        InfraObjectList_1 = _facade.GetInfraObjectByBusinessServiceId(infraItem.BusinessServiceId);

                                        string isDRReady = string.Empty;
                                        string infraObjNameList = string.Empty;
                                        if (InfraObjectList_1 != null && InfraObjectList_1.Count() > 0)
                                        {

                                            allInfraObjectCount_1 = InfraObjectList_1.Count;

                                            infraObjectCount_1 = infraObjectCount_1 + allInfraObjectCount_1;
                                            drReadynessCount_1 = Convert.ToInt16(InfraObjectList_1.Count(a => a.IsDRReady == 1));

                                            //FilterBSlist.Add(bService);

                                            bool ready = false;
                                            if (!string.IsNullOrEmpty(serviceTable_1))
                                            {
                                                string[] tempArr = serviceTable_1.Split('^');
                                                if (string.IsNullOrEmpty(tempArr[0]))
                                                    ready = true;
                                            }
                                            // Considered DRReadyness at business service level i.e.if anyone of infraObject is not having DRReady then that businessService is not DRReady.
                                            if (allInfraObjectCount_1 == drReadynessCount_1 && ready == true)
                                            {
                                                businessServiceDRReadyCount_1 = businessServiceDRReadyCount_1 + 1;
                                                isDRReady = "Y";
                                            }
                                            else
                                            {
                                                isDRReady = "N";
                                            }
                                        }
                                        #endregion Calculate DR Ready

                                        ndx = xlColumn[column] + row.ToString();
                                        string drr = isDRReady.Equals("Y") ? "YES" : "NO";
                                        _cells[ndx].Formula = drr;
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 15;

                                        if (drr == "YES")
                                            _cells[ndx].Font.Color = Color.Green;
                                        else if (drr == "NO")
                                            _cells[ndx].Font.Color = Color.Red;
                                        else
                                            _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;


                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_E.Message) ? UnsucceAction_E.Message : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 40;
                                        _cells[ndx].WrapText = true;
                                        if (drr == "YES")
                                        {
                                            _cells[ndx].Font.Color = Color.Green;
                                            _cells[ndx].Formula = "";
                                        }
                                        else if (drr == "NO")
                                            _cells[ndx].Font.Color = Color.Red;
                                        else
                                            _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        row++;

                                        #endregion Bind Excel Values

                                    }
                                    
                                    else if (UnsucceAction_c != null)
                                    {
                                        //strDrilUnSuccess += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow Confgured Drill Execution Failed." + "" + "<b>Start time : </b>" + UnsucceAction.StartTime + "<br> <b>End Time   : </b>" + UnsucceAction.EndTime + "<br><b> Reason   : </b>" + UnsucceAction.Message.Replace('@', ' ') + "'></span></div></td></tr>";

                                        #region Bind Excel Values

                                        dataCount++;
                                        //int column = 0;
                                        //string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };
                                        xlRow++;

                                        //string ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx + ":" + "K" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;

                                        _cells[ndx].Formula = i.ToString();
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        i++;
                                        column++;

                                        var value = _facade.GetBusinessServiceById(infraItem.BusinessServiceId);

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = value != null ? value.Name : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(funName) ? funName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(infraItem.Name) ? infraItem.Name : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        _cells[ndx].WrapText = true;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_c.WorkflowName) ? UnsucceAction_c.WorkflowName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_c.WorkflowActionName) ? UnsucceAction_c.WorkflowActionName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 30;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].WrapText = true;

                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].EntireColumn.NumberFormat = "@";
                                        _cells[ndx].Formula = UnsucceAction_c.StartTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(UnsucceAction_c.StartTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";

                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        _cells[ndx].WrapText = true;
                                        column++;


                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].EntireColumn.NumberFormat = "@";
                                        _cells[ndx].Formula = UnsucceAction_c.EndTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(UnsucceAction_c.EndTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                                        _cells[ndx].Font.Size = 10;

                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;


                                        #region Calculate DR Ready

                                        IList<InfraObject> InfraObjectList_1 = null;
                                        int infraObjectCount_1 = 0;
                                        int drReadynessCount_1 = 0;
                                        int allInfraObjectCount_1 = 0;

                                        int businessServiceDRReadyCount_1 = 0;
                                        string serviceTable_1 = GetInfraDrillInfo_1(infraItem.BusinessServiceId);


                                        InfraObjectList_1 = new List<InfraObject>();
                                        InfraObjectList_1 = _facade.GetInfraObjectByBusinessServiceId(infraItem.BusinessServiceId);

                                        string isDRReady = string.Empty;
                                        string infraObjNameList = string.Empty;
                                        if (InfraObjectList_1 != null && InfraObjectList_1.Count() > 0)
                                        {

                                            allInfraObjectCount_1 = InfraObjectList_1.Count;

                                            infraObjectCount_1 = infraObjectCount_1 + allInfraObjectCount_1;
                                            drReadynessCount_1 = Convert.ToInt16(InfraObjectList_1.Count(a => a.IsDRReady == 1));

                                            //FilterBSlist.Add(bService);

                                            bool ready = false;
                                            if (!string.IsNullOrEmpty(serviceTable_1))
                                            {
                                                string[] tempArr = serviceTable_1.Split('^');
                                                if (string.IsNullOrEmpty(tempArr[0]))
                                                    ready = true;
                                            }
                                            // Considered DRReadyness at business service level i.e.if anyone of infraObject is not having DRReady then that businessService is not DRReady.
                                            if (allInfraObjectCount_1 == drReadynessCount_1 && ready == true)
                                            {
                                                businessServiceDRReadyCount_1 = businessServiceDRReadyCount_1 + 1;
                                                isDRReady = "Y";
                                            }
                                            else
                                            {
                                                isDRReady = "N";
                                            }
                                        }
                                        #endregion Calculate DR Ready

                                        ndx = xlColumn[column] + row.ToString();
                                        string drr = isDRReady.Equals("Y") ? "YES" : "NO";
                                        _cells[ndx].Formula = drr;
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 15;

                                        if (drr == "YES")
                                            _cells[ndx].Font.Color = Color.Green;
                                        else if (drr == "NO")
                                            _cells[ndx].Font.Color = Color.Red;
                                        else
                                            _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;


                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_c.Message) ? UnsucceAction_c.Message : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 40;
                                        _cells[ndx].WrapText = true;
                                        if (drr == "YES")
                                        {
                                            _cells[ndx].Font.Color = Color.Green;
                                            _cells[ndx].Formula = "";
                                        }
                                        else if (drr == "NO")
                                            _cells[ndx].Font.Color = Color.Red;
                                        else
                                            _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        row++;

                                        #endregion Bind Excel Values

                                    }
                                   
                                    else if (UnsucceAction_s != null)
                                    {
                                        //strDrilUnSuccess += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow Confgured Drill Execution Failed." + "" + "<b>Start time : </b>" + UnsucceAction.StartTime + "<br> <b>End Time   : </b>" + UnsucceAction.EndTime + "<br><b> Reason   : </b>" + UnsucceAction.Message.Replace('@', ' ') + "'></span></div></td></tr>";

                                        #region Bind Excel Values

                                        dataCount++;
                                        //int column = 0;
                                        //string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };
                                        xlRow++;

                                        //string ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx + ":" + "K" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;

                                        _cells[ndx].Formula = i.ToString();
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        i++;
                                        column++;

                                        var value = _facade.GetBusinessServiceById(infraItem.BusinessServiceId);

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = value != null ? value.Name : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(funName) ? funName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(infraItem.Name) ? infraItem.Name : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        _cells[ndx].WrapText = true;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_s.WorkflowName) ? UnsucceAction_s.WorkflowName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_s.WorkflowActionName) ? UnsucceAction_s.WorkflowActionName : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 30;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].WrapText = true;

                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].EntireColumn.NumberFormat = "@";
                                        _cells[ndx].Formula = UnsucceAction_s.StartTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(UnsucceAction_s.StartTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";

                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        _cells[ndx].WrapText = true;
                                        column++;


                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].EntireColumn.NumberFormat = "@";
                                        _cells[ndx].Formula = UnsucceAction_s.EndTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(UnsucceAction_s.EndTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                                        _cells[ndx].Font.Size = 10;

                                        _cells[ndx].ColumnWidth = 23;
                                        _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;


                                        #region Calculate DR Ready

                                        IList<InfraObject> InfraObjectList_1 = null;
                                        int infraObjectCount_1 = 0;
                                        int drReadynessCount_1 = 0;
                                        int allInfraObjectCount_1 = 0;

                                        int businessServiceDRReadyCount_1 = 0;
                                        string serviceTable_1 = GetInfraDrillInfo_1(infraItem.BusinessServiceId);


                                        InfraObjectList_1 = new List<InfraObject>();
                                        InfraObjectList_1 = _facade.GetInfraObjectByBusinessServiceId(infraItem.BusinessServiceId);

                                        string isDRReady = string.Empty;
                                        string infraObjNameList = string.Empty;
                                        if (InfraObjectList_1 != null && InfraObjectList_1.Count() > 0)
                                        {

                                            allInfraObjectCount_1 = InfraObjectList_1.Count;

                                            infraObjectCount_1 = infraObjectCount_1 + allInfraObjectCount_1;
                                            drReadynessCount_1 = Convert.ToInt16(InfraObjectList_1.Count(a => a.IsDRReady == 1));

                                            //FilterBSlist.Add(bService);

                                            bool ready = false;
                                            if (!string.IsNullOrEmpty(serviceTable_1))
                                            {
                                                string[] tempArr = serviceTable_1.Split('^');
                                                if (string.IsNullOrEmpty(tempArr[0]))
                                                    ready = true;
                                            }
                                            // Considered DRReadyness at business service level i.e.if anyone of infraObject is not having DRReady then that businessService is not DRReady.
                                            if (allInfraObjectCount_1 == drReadynessCount_1 && ready == true)
                                            {
                                                businessServiceDRReadyCount_1 = businessServiceDRReadyCount_1 + 1;
                                                isDRReady = "Y";
                                            }
                                            else
                                            {
                                                isDRReady = "N";
                                            }
                                        }
                                        #endregion Calculate DR Ready

                                        ndx = xlColumn[column] + row.ToString();
                                        string drr = isDRReady.Equals("Y") ? "YES" : "NO";
                                        _cells[ndx].Formula = drr;
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 15;

                                        if (drr == "YES")
                                            _cells[ndx].Font.Color = Color.Green;
                                        else if (drr == "NO")
                                            _cells[ndx].Font.Color = Color.Red;
                                        else
                                            _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;


                                        ndx = xlColumn[column] + row.ToString();
                                        _cells[ndx].Formula = !string.IsNullOrEmpty(UnsucceAction_s.Message) ? UnsucceAction_s.Message : "NA";
                                        _cells[ndx].Font.Size = 10;
                                        _cells[ndx].ColumnWidth = 40;
                                        _cells[ndx].WrapText = true;
                                        if (drr == "YES")
                                        {
                                            _cells[ndx].Font.Color = Color.Green;
                                            _cells[ndx].Formula = "";
                                        }
                                        else if (drr == "NO")
                                            _cells[ndx].Font.Color = Color.Red;
                                        else
                                            _cells[ndx].Font.Color = Color.Black;
                                        _cells[ndx].HorizontalAlignment = HAlign.Center;
                                        column++;

                                        row++;

                                        #endregion Bind Excel Values

                                    }
                                    else
                                    { }
                                    //drillInfra++;
                                }
                            }
                        }

                    }





                }
            }


            return reportWorkbook;
        }

        public static string GetInfraDrillInfo_1(int ServiceId)
        {
            try
            {

                IList<InfraObject> allInfraList = new List<InfraObject>();
                IList<ParallelWorkflowActionResult> PWAction = new List<ParallelWorkflowActionResult>();
                IFacade _facade = new Facade();
                int totalInfra = 0;
                int drillInfra = 0;
                string strNotConfig = string.Empty;
                string strNotDrill = string.Empty;
                string strDrilUnSuccess = string.Empty;
                string strHTMLTable = string.Empty;
                InfrascheduleWfdeatils _schuwfdtls = null;
                string drillInfo = "<div style='float:right;padding-right:13px;'><span class='ciopopover' data-toggle='popover' data-placement='left' data-html='true' data-title='InfraObject Drill Failure Details' data-content='";
                

                allInfraList = _facade.GetInfraObjectByBusinessServiceId(ServiceId);
                if (allInfraList != null)
                {
                    totalInfra = allInfraList.Count;
                    foreach (var infraItem in allInfraList)
                    {
                        _schuwfdtls = null;
                        string actions = _facade.GetInfraActionById(infraItem.Id);//GetAllInfraAction(infraItem.Id);
                        var funObj = _facade.GetBusinessFunctionById(infraItem.BusinessFunctionId);
                        string funName = funObj != null ? funObj.Name : "";
                        bool _isScheduleWF = false;
                        if (!string.IsNullOrEmpty(actions))
                        {

                            //actions = actions.Substring(0, actions.Length - 1);
                            PWAction = _facade.GetInfraDrillCount(infraItem.Id, actions);
                            //var PW_Filter = _facade.GetInfraDrillCount(infraItem.Id, actions);

                            //string[] id = actions.Split(',');
                            //if (PW_Filter != null)
                            //{
                            //    PWAction = (from PA in PW_Filter
                            //                where id.Contains(PA.ActionId.ToString())
                            //                select PA).ToList();
                            //}

                            var _schuwfdtlslist = _facade.GetscheduleByInfraId(infraItem.Id);
                            if (_schuwfdtlslist != null)
                                _schuwfdtls = _schuwfdtlslist.ToList().FirstOrDefault(); ;

                            if (PWAction != null  && PWAction.Count > 0 && _schuwfdtls != null)
                            {

                                var chkPWAction = PWAction.GroupBy(x => x.ParallelGroupWorkflowId).ToList().FirstOrDefault();
                                if (chkPWAction != null)
                                {
                                    //var UnsucceAction = chkPWAction.Where(x => x.Status.ToString().Equals("Error")).FirstOrDefault();
                                    var UnsucceAction = chkPWAction.OrderByDescending(x => x.EndTime).FirstOrDefault();
                                    if (UnsucceAction != null)
                                    {
                                        if (UnsucceAction.EndTime < _schuwfdtls.EndTime && _schuwfdtls.Status != null)//if (UnsucceAction.CreateDate < _schuwfdtls.CreateDate && _schuwfdtls.Status != null)
                                            _isScheduleWF = true;

                                        else
                                            _isScheduleWF = false;
                                    }
                                    else
                                        _isScheduleWF = true;
                                }
                            }
                            else if (PWAction != null && PWAction.Count > 0 && _schuwfdtls == null)
                                _isScheduleWF = false;
                            else
                                _isScheduleWF = true;


                            if (_isScheduleWF)
                            {
                                if (_schuwfdtls != null)
                                {


                                    if (_schuwfdtls.Status == "Error")
                                        strDrilUnSuccess += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow Confgured Drill Execution Failed." + drillInfo + "<b>Start time : </b>" + _schuwfdtls.StartTime + "<br> <b>End Time   : </b>" + _schuwfdtls.EndTime + "<br><b> Reason   : </b>" + _schuwfdtls.Message.Replace('@', ' ') + "'></span></div></td></tr>";
                                    else
                                        drillInfra++;


                                }
                                else
                                    strNotDrill += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow  Configured  But Drill Not Done.</div></td></tr>";

                            }
                            else
                            {
                                if (PWAction != null && PWAction.Count > 0)
                                {
                                    var tempPWAction = PWAction.GroupBy(x => x.ParallelGroupWorkflowId).ToList().FirstOrDefault();
                                    if (tempPWAction != null)
                                    {
                                        var UnsucceAction = tempPWAction.Where(x => x.Status.ToString().Equals("Error")).FirstOrDefault();
                                        if (UnsucceAction != null)
                                            strDrilUnSuccess += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow Confgured Drill Execution Failed." + drillInfo + "<b>Start time : </b>" + UnsucceAction.StartTime + "<br> <b>End Time   : </b>" + UnsucceAction.EndTime + "<br><b> Reason   : </b>" + UnsucceAction.Message.Replace('@', ' ') + "'></span></div></td></tr>";
                                        else
                                            drillInfra++;
                                    }
                                }
                                else
                                    strNotDrill += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow  Configured  But Drill Not Done.</div></td></tr>";
                            }
                        }
                        else
                            strNotConfig += "<tr><td>" + infraItem.Name + "</td><td>" + funName + "</td><td>DR WorkFlow Not Configured.</div></td></tr>";
                    }

                }
                strHTMLTable += strNotConfig + strNotDrill + strDrilUnSuccess + "^" + totalInfra.ToString() + "^" + drillInfra.ToString() + "^" + ServiceId.ToString();
                return strHTMLTable;
            }

            catch (Exception ex)
            {

                throw ex;
            }
        }

        public static IWorkbook ExcelReport_Old(int bsid, int bfid, int infraid, string drready, string strlogo)
        {
            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "DR Readiness Summary Report";

            _cells["A1"].ColumnWidth = 7;

            _cells["E3"].Formula = "DR Readiness Summary Report";
            _cells["E3"].HorizontalAlignment = HAlign.Center;
            _cells["E3"].Font.Bold = true;
            _cells["B3:K6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:K3"].Font.Size = 11;
            _cells["B5:K8"].Font.Size = 10;
            _cells["B3:K8"].Font.Color = Color.White;
            _cells["B3:K8"].Font.Name = "Cambria";

            IFacade _facade = new Facade();

            IList<InfraobjectSchedularLogs> infraschedulelist = new List<InfraobjectSchedularLogs>();
           
            #region Comment
            /*  IList<ParallelWorkflowActionResult> PWAction = null;
                            IList<ParallelWorkflowActionResult> PWAction_New = new  List<ParallelWorkflowActionResult>(); ;

                            infraschedulelist = _facade.GetDRReddynesssummary(bsid, bfid, infraid);
                            IList<InfraObject> Infra_List = null;

                            IList<InfraobjectSchedularLogs> infra_manual_List = new List<InfraobjectSchedularLogs>();

                            InfraobjectSchedularLogs obj_INfraSchLogs = new InfraobjectSchedularLogs();


                            if (bsid == 0)
                            {
                                Infra_List = _facade.GetAllInfraObject();
                            }
                            else
                            {
                             Infra_List =   _facade.GetInfraObjectByBusinessServiceId(bsid);
                            }
                            PWAction = null;
            

                            if (Infra_List != null && Infra_List.Count > 0)
                            {
                                foreach (var infra_1 in Infra_List)
                                {
                                    string actions = _facade.GetInfraActionById(infra_1.Id);
                                    if (!string.IsNullOrEmpty(actions))
                                    {
                                        PWAction = _facade.GetInfraDrillCount(infra_1.Id, actions);
                                        if (PWAction != null)
                                        {
                                            var chkPWAction = PWAction.GroupBy(x => x.ParallelGroupWorkflowId).ToList().FirstOrDefault();
                                            if (chkPWAction != null)
                                            {
                                                var UnsucceAction = chkPWAction.Where(x => x.Status.ToString().Equals("Error")).FirstOrDefault();

                                                if (UnsucceAction != null)
                                                {
                                                    PWAction_New.Add(UnsucceAction);

                                                    // strTempRowforUnsucess += "<tr><td>" + infraItem.Name.Split('/')[0] + "</td><td>" + strfunName + "</td><td>" + UnsucceAction.WorkflowName + "</td><td>" + UnsucceAction.StartTime + "\n" + UnsucceAction.EndTime + "</td><td>  " + UnsucceAction.WorkflowActionName + "<br>Reason : " + UnsucceAction.Message + "</td><td>  " + "Failed" + "  <span class='icon-log' style='float: right; margin-right: 10px;margin-top:8px;cursor: pointer;' onclick='javascript:ShowReport(" + bServiceObj.Id + "," + funcId + "," + infraItem.Id + ");'></span></td></tr>";
                                                }
                                                else
                                                {

                                                }
                                                // strTempRowforUnsucess += "<tr><td>" + infraItem.Name.Split('/')[0] + "</td><td>" + strfunName + "</td><td>" + "NA" + "</td><td>" + "NA" + "</td><td>  " + "NA" + "</td><td>  " + "NA" + "  <span class='icon-log' style='float: right; margin-right: 10px;margin-top:8px;cursor: pointer;' onclick='javascript:ShowReport(" + bServiceObj.Id + "," + funcId + "," + infraItem.Id + ");'></span></td></tr>";
                                            }
                                        }
                                    }
                                }
                            }


                            var Sched_List_Time = infraschedulelist.Max(x => x.EndTime);
                            var Manual_List_Time = PWAction_New.Max(x => x.EndTime);

                            if (Manual_List_Time != null)
                            { 
            
                            }
                            if (Sched_List_Time != null)
                            { 
            
                            }
            */

#endregion Comment

            if (infraschedulelist == null || infraschedulelist.Count <= 0)
            {
                return null;
            }

            if (drready == "DR Ready")
                infraschedulelist = (from inf in infraschedulelist where inf.DRReady == "YES" select inf).ToList();
            else if (drready == "DR Not Ready")
                infraschedulelist = (from inf in infraschedulelist where inf.DRReady == "NO" select inf).ToList();

            if (infraschedulelist == null || infraschedulelist.Count <= 0)
            {
                return null;
            }
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 48, 10, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 690, 10, 120, 13);

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 410, 10, 121, 13);
            }
            reportWorksheet.Cells["A2:F2"].RowHeight = 29;
            ////upto this
            reportWorksheet.Cells["A1:F1"].RowHeight = 27;

            var dateTime = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));///DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss");
            _cells["B5"].Formula = "Report Generated Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            _cells["C5"].NumberFormat = "@";
            _cells["C5"].Formula = ":  " + (dateTime);
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;

            int row = 8;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "Sr.No.";
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["B8:K8"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B8:K8"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "BusinessService Name";
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["C" + row.ToString()].VerticalAlignment = VAlign.Center;

            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
            productionlog.WrapText = true;

            _cells["D" + row.ToString()].Formula = "BusinessFunction Name";
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["D" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["E" + row.ToString()].Formula = "Infraobject Name";
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["E" + row.ToString()].VerticalAlignment = VAlign.Center;


            _cells["F" + row.ToString()].Formula = "DR Ready WorkFlow Name";
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["F" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["G" + row.ToString()].Formula = "DR Action Name";
            _cells["G" + row.ToString()].Font.Bold = true;
            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["G" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["H" + row.ToString()].Formula = "Execution Start Time";
            _cells["H" + row.ToString()].Font.Bold = true;
            _cells["H" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["H" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["I" + row.ToString()].Formula = "Execution End Time";
            _cells["I" + row.ToString()].Font.Bold = true;
            _cells["I" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["I" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["J" + row.ToString()].Formula = "DR Ready";
            _cells["J" + row.ToString()].Font.Bold = true;
            _cells["J" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["J" + row.ToString()].VerticalAlignment = VAlign.Center;

            _cells["K" + row.ToString()].Formula = "Execution Failure Reason";
            _cells["K" + row.ToString()].Font.Bold = true;
            _cells["K" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["K" + row.ToString()].VerticalAlignment = VAlign.Center;

            row++;
            int dataCount = 0;
            int xlRow = 9;

            foreach (var rp in infraschedulelist)
            {
                
                dataCount++;
                int column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" };
                xlRow++;

                string ndx = xlColumn[column] + row.ToString();
                _cells[ndx + ":" + "K" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;
                
                _cells[ndx].Formula = i.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                i++;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.BusinessServiceName != string.Empty ? rp.BusinessServiceName : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.BusinessFunctionName != string.Empty ? rp.BusinessFunctionName : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.InfraObjectName != string.Empty ? rp.InfraObjectName : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                _cells[ndx].WrapText = true;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.WorkflowName != string.Empty ? rp.WorkflowName : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.CurrentActionName != string.Empty ? CryptographyHelper.Md5Decrypt(rp.CurrentActionName) : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 30;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].WrapText = true;

                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].EntireColumn.NumberFormat = "@";
                _cells[ndx].Formula = rp.StartTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(rp.StartTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";

                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                _cells[ndx].WrapText = true;
                column++;


                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].EntireColumn.NumberFormat = "@";
                _cells[ndx].Formula = rp.EndTime != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(rp.EndTime).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                _cells[ndx].Font.Size = 10;

                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;

                ndx = xlColumn[column] + row.ToString();
                string drr = rp.DRReady != string.Empty ? rp.DRReady : "NA";
                _cells[ndx].Formula = drr;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 15;

                if (drr == "YES")
                    _cells[ndx].Font.Color = Color.Green;
                else if (drr == "NO")
                    _cells[ndx].Font.Color = Color.Red;
                else
                    _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;


                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.DRReason != string.Empty ? rp.DRReason : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 40;
                _cells[ndx].WrapText = true;
                if (drr == "YES")
                {
                    _cells[ndx].Font.Color = Color.Green;
                    _cells[ndx].Formula = "";
                }
                else if (drr == "NO")
                    _cells[ndx].Font.Color = Color.Red;
                else
                    _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;

                row++;
            }

            int finalCount = dataCount + 10;
            _cells["B" + finalCount].Formula = "NA : Not Available";
            _cells["B" + finalCount].HorizontalAlignment = HAlign.Center;
            _cells["B" + finalCount].Font.Name = "Cambria";
            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

            reportWorksheet.ProtectContents = true;



            return reportWorkbook;
            // OpenExcelFile(reportWorkbook);

        }

        private void OpenExcelFile(IWorkbook workbook)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "DRReadinessSummary" + str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + str;
            //var myUrl = "/ExcelFiles/" + str;
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=DRReadiness Summary Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }

        protected void btnview_Click(object sender, EventArgs e)
        {
            try
            {

                lblMsg.Visible = false;
                IWorkbook rw = null;

                string drr = "";



                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (ddlBussService.SelectedItem.Text == "ALL")

                    rw = ExcelReport(0, 0, 0, ddldr.SelectedItem.Text, strlogo);

                else if (ddlBussService.SelectedItem.Text != "ALL" && ddlfn.SelectedItem.Text == "ALL")

                    rw = ExcelReport(Convert.ToInt32(ddlBussService.SelectedValue), 0, 0, ddldr.SelectedItem.Text, strlogo);

                else if (ddlBussService.SelectedItem.Text != "ALL" && ddlfn.SelectedItem.Text != "ALL" && ddlinfra.SelectedItem.Text == "ALL")

                    rw = ExcelReport(Convert.ToInt32(ddlBussService.SelectedValue), Convert.ToInt32(ddlfn.SelectedValue), 0, ddldr.SelectedItem.Text, strlogo);

                else if (ddlBussService.SelectedItem.Text != "ALL" && ddlfn.SelectedItem.Text != "ALL" && ddlinfra.SelectedItem.Text != "ALL")

                    rw = ExcelReport(Convert.ToInt32(ddlBussService.SelectedValue), Convert.ToInt32(ddlfn.SelectedValue), Convert.ToInt32(ddlinfra.SelectedValue), ddldr.SelectedItem.Text, strlogo);

                if (rw != null)
                    OpenExcelFile(rw);
                else
                {

                    lblMsg.Visible = true;
                    lblMsg.Text = "No Records Found";


                    return;
                }





                //var stdt = Convert.ToDateTime(txtstart.Text);
                //var startdt = stdt.ToString("yyyy-MM-dd");
                //var endt = Convert.ToDateTime(txtend.Text);
                //var enddt = endt.ToString("yyyy-MM-dd");

                //string dat = DateTime.Now.ToString("yyyy-MM-dd");
                //if (Convert.ToDateTime(stdt) > Convert.ToDateTime(endt))
                //{
                //    lblMsg.Visible = true;
                //    lblMsg.Text = "Start Date Greater than End Date";
                //    Pan1.Visible = false;
                //    return;
                //}
                //else if (Convert.ToDateTime(dat) < Convert.ToDateTime(stdt))
                //{
                //    lblMsg.Visible = true;
                //    lblMsg.Text = "Start Date can't Greater than Today Date";
                //    Pan1.Visible = false;
                //    return;
                //}
                //else if (Convert.ToDateTime(dat) < Convert.ToDateTime(endt))
                //{
                //    lblMsg.Visible = true;
                //    lblMsg.Text = "End Date can't Greater than Today Date";
                //    Pan1.Visible = false;
                //    return;
                //}

                //  Pan1.Visible = true;


                // screenreport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }

            btnPdf.Visible = true;
            btnExcel.Visible = true;
        }

        protected void btnExcel_Click(object sender, EventArgs e)
        {
            try
            {
                //  screenreport();
                //  ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void btnPdf_Click(object sender, EventArgs e)
        {
            try
            {
                var table = screenreport();
                CreatePdfReport(table);
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void txtstart_TextChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
            Pan1.Visible = false;
        }

        protected void txtend_TextChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
            Pan1.Visible = false;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            //RegisterPostBackControl();
        }

        protected void ddlusename_SelectedIndexChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
        }

        private void RegisterPostBackControl()
        {
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnview);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(ddlBussService);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(ddlfn);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(ddlinfra);
            //ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(txtstart);
            //ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(txtend);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnPdf);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnExcel);
        }

        protected void ddlBussService_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                ddlfn.DataSource = null;
                ddlfn.DataBind();

                ddlinfra.DataSource = null;
                ddlinfra.DataBind();

                dvbf.Visible = true;
                IList<BusinessFunction> businessFunctionList = new List<BusinessFunction>();

                businessFunctionList = Facade.GetBusinessFunctionsByBusinessServiceId(Convert.ToInt32(ddlBussService.SelectedValue.ToString()));

                if (businessFunctionList != null)
                {
                    ddlfn.DataSource = businessFunctionList;
                    ddlfn.DataTextField = "Name";
                    ddlfn.DataValueField = "Id";
                    ddlfn.DataBind();
                    ddlfn.Items.Insert(0, "ALL");
                }
                else
                {
                    ddlfn.Items.Insert(0, new ListItem("No Data Found", "0"));
                }

                var InfraList = Facade.GetAllInfraObject();

                if (InfraList != null && InfraList.Count > 0)
                {
                    if (ddlBussService.SelectedValue != "0")
                    {
                        var infralist_1 = (from infra in InfraList
                                           where infra.BusinessServiceId == Convert.ToInt32(ddlBussService.SelectedValue)
                                           select infra).ToList();
                        if (infralist_1 != null && infralist_1.Count > 0)
                        {
                            ddlinfra.DataSource = infralist_1;
                            ddlinfra.DataTextField = "Name";
                            ddlinfra.DataValueField = "Id";
                            ddlinfra.DataBind();
                            ddlinfra.Items.Insert(0, "ALL");
                        }
                        else
                        {
                            ddlinfra.Items.Insert(0, new ListItem("No Infraobjects Found", "0"));
                        }
                    }
                    else
                    {
                        ddlinfra.DataSource = InfraList;
                        ddlinfra.DataTextField = "Name";
                        ddlinfra.DataValueField = "Id";
                        ddlinfra.DataBind();
                        ddlinfra.Items.Insert(0, "ALL");
                    }
                }
            }
            catch (Exception ex)
            {


            }

        }

        protected void ddlfn_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                ddlinfra.DataSource = null;
                ddlinfra.DataBind();
                dvinfra.Visible = true;
                IList<InfraObject> Infralist = new List<InfraObject>();

                Infralist = Facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(Convert.ToInt32(ddlBussService.SelectedValue.ToString()), (Convert.ToInt32(ddlfn.SelectedValue.ToString())));

                if (Infralist != null)
                {
                    ddlinfra.DataSource = Infralist;
                    ddlinfra.DataTextField = "Name";
                    ddlinfra.DataValueField = "Id";
                    ddlinfra.DataBind();
                    ddlinfra.Items.Insert(0, "ALL");
                }
                else
                {
                    ddlinfra.Items.Insert(0, new ListItem("No Data Found", "0"));
                }
            }
            catch (Exception ex)
            {


            }
        }
    }
}