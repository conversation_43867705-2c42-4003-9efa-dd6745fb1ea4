﻿using CP.UI.Code.Replication.Component;
using CP.UI.Code.Replication.DataLag;
using CP.UI.Code.Replication.ReplicationInfo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CP.UI.Code.Replication.Clients
{
    public class EmcUnityClient : Replication
    {
        public EmcUnityClient()
        {
            Datalag = new EmcUnityDataLag();

            ComponentInfo = new EmcUnityComponentMonitor();

            ReplicationInfo = new EmcUnityReplicationInfo();
        }
    }
}