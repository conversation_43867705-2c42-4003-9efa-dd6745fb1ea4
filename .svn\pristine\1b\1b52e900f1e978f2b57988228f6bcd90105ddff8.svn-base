﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
    internal sealed class ServiceProfileBuilder : IEntityBuilder<ServiceProfile>
    {
         IList<ServiceProfile> IEntityBuilder<ServiceProfile>.BuildEntities(IDataReader reader)
        {
            var servers = new List<ServiceProfile>();

            while (reader.Read())
            {
                servers.Add(((IEntityBuilder<ServiceProfile>)this).BuildEntity(reader, new ServiceProfile()));
            }

            return (servers.Count > 0) ? servers : null;
        }
         ServiceProfile IEntityBuilder<ServiceProfile>.BuildEntity(IDataReader reader, ServiceProfile serviceProfile)
         {
            
             serviceProfile.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"].ToString());
             //serviceProfile.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"].ToString());
             serviceProfile.ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"].ToString());
             serviceProfile.CreatedBy = Convert.IsDBNull(reader["CreatedBy"]) ? 0 : Convert.ToInt32(reader["CreatedBy"].ToString());
             serviceProfile.Role = Convert.IsDBNull(reader["Role"]) ? string.Empty : Convert.ToString(reader["Role"].ToString());

             return serviceProfile;
         }
    }
}
