﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="UserList.aspx.cs" Inherits="CP.UI.UserList" Title="Continuity Patrol :: User-UserList" %>

<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
      <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="upnlUserConfiguration" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

                <div id="ulMessage" runat="server" visible="false">
                    <asp:Label ID="lblMessage" runat="server" Text=""></asp:Label>
                    <span class="close-bt"></span>
                </div>


                <h3>
                    <img src="../Images/user-icon.png">
                    User List</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-7 text-left">
                                <asp:Button ID="btnupdateuser" runat="server" CssClass="btn btn-primary" Text="Update AD User" Visible="false" OnClick="btnupdateuser_Click"/>
                                </div>
                            <div class="col-md-5 text-right">
                                <asp:TextBox ID="txtsearchvalue" placeholder="Login Name" CssClass="form-control" runat="server"></asp:TextBox>
                                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="BtnSearchClick" />
                            </div>
                        </div>
                        <hr />
                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <asp:ListView ID="lvUser" runat="server" OnItemCommand="LvUserItemCommand" DataKeyNames="ID" OnPreRender="LvUserPreRender"
                                    OnItemDataBound="LvUserItemDataBound" OnItemDeleting="LvUserItemDeleting" OnItemEditing="LvUserItemEditing"
                                    OnPagePropertiesChanging="LvUserPagePropertiesChanging">
                                    <LayoutTemplate>
                                        <table id="tblUser" class="table table-striped table-bordered table-condensed table-white" width="100%" style="table-layout: fixed">
                                            <thead>
                                                <tr>
                                                    <th style="width: 4%;" class="text-center">
                                                        <span>
                                                            <img src="../images/icons/user.png" /></span>
                                                    </th>
                                                    <th style="width: 15%;">Login Name
                                                    </th>
                                                    <th style="width: 15%;">User Name
                                                    </th>

                                                    <th style="width: 11%;">Company
                                                    </th>
                                                    
                                                    <th style="width: 11%;">User Type
                                                    </th>
                                                    <th style="width: 10%;">Role Type
                                                    </th>
                                                    <th style="width: 20%;">Email
                                                    </th>
                                                    <th style="width: 10%;">Mobile Number
                                                    </th>
                                                    <th style="width: 14%;">Communication Mode
                                                    </th>
                                                     <%--<th style="width: 14%;">User Group
                                                    </th>--%>
                                                    <th style="width: 4%;">Mode
                                                    </th>
                                                    <th runat="server" id="ActionHead" style="width: 8%;" class="text-center">Action
                                                    </th>

                                                </tr>
                                            </thead>
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                            </tbody>
                                        </table>
                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td style="width: 4%;" class="text-center">
                                                <asp:Label ID="Id" runat="server" Text='<%#Eval("Id") %>' Visible="false"></asp:Label>
                                                <asp:Label ID="Name" runat="server" Text='<%#Eval("LoginName") %>'  Visible="false"></asp:Label>
                                                <asp:Label ID="LoginType" runat="server" Text='<%#Eval("LoginType") %>' Visible="false"></asp:Label>
                                                <%#Container.DataItemIndex+1 %>
                                            </td>
                                            <td style="width: 4%; word-wrap: break-word; !important" >                                                
                                                <span title='<%# DataBinder.Eval ( Container, "DataItem.LoginName" )%>'>
                                                    <%# DataBinder.Eval ( Container, "DataItem.LoginName" )%></span>
                                            </td>

                                             
                                             <td style="width: 11%; word-wrap: break-word; !important">
                                                <asp:Label ID="Label1" runat="server" Text='<%#(Eval("UserName").ToString())%>'></asp:Label>
                                            </td>
                                            <td style="width: 11%;" class="tdword-wrap" >
                                                <asp:Label ID="lblcompany" runat="server" Text='<%#(Eval("CompanyId").ToString())%>'></asp:Label>
                                            </td>

                                            <td style="width: 11%;" class="tdword-wrap">
                                                <asp:Label ID="lblUserType" runat="server" Text='<%#GetUserType(Eval("LoginType").ToString())%>' ToolTip='<%#GetUserType(Eval("LoginType").ToString())%>'></asp:Label>
                                            </td>
                                            <td style="width: 10%;" class="tdword-wrap">
                                                <asp:Label ID="lblCategory" runat="server" Text='<%# Eval("Role") %>' ToolTip='<%# Eval("Role") %>'></asp:Label>
                                            </td>
                                            <td style="width: 17%;" class="tdword-wrap">
                                              <span title='<%# DataBinder.Eval(Container, "DataItem.UserInformation.Email")%>'>  <%# DataBinder.Eval(Container, "DataItem.UserInformation.Email")%>  </span>
                                            </td>
                                            <td style="width: 10%;" class="tdword-wrap">
                                                 <span title='<%# DataBinder.Eval ( Container, "DataItem.UserInformation.Mobile" )%>'>
                                                <%# DataBinder.Eval ( Container, "DataItem.UserInformation.Mobile" )%></span>
                                            </td>
                                           

                                            <td style="width: 14%;" class="tdword-wrap">
                                                <asp:Label ID="Label3" runat="server" Text='<%#Eval("UserInformation.AlertMode")%>' ToolTip='<%#Eval("UserInformation.AlertMode")%>'></asp:Label>
                                            </td>
                                             <%--<td style="width: 11%;" class="tdword-wrap">
                                                <asp:Label ID="Label1" runat="server" Text='<%#Eval("CurrentLoginIP")%>' ToolTip='<%#Eval("CurrentLoginIP")%>'></asp:Label>
                                            </td>--%>
                                            <td style="width: 4%;">
                                                <div style="text-align: center;">
                                                    <asp:ImageButton ID="imgActive" CommandName="ActiveState" Enabled='<%#CheckEnabled()%>' ImageUrl='<%# CheckActive(Eval("IsActive")) %>' ToolTip='<%# CheckToolTip(Eval("IsActive")) %>'
                                                        runat="server" />
                                                    <asp:HiddenField ID="hdnIsActive" runat="server" Value='<%# Eval("IsActive")%>' />
                                                </div>
                                            </td>
                                            <td runat="server" id="action" style="width: 8%;" class="text-center">
                                                <asp:ImageButton ID="ibtnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                    ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                                                <%--<asp:ImageButton ID="ibtnDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                    ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                                                <asp:ImageButton ID="ibtnChangePassword" runat="server" CommandArgument='<%# Eval("Id") %>'
                                                    CommandName="ChangePassword" Enabled='<%# ChangePassword(Eval("LoginName")) %>'
                                                    AlternateText="Reset Password" ToolTip="Reset Password" ImageUrl='<%# CheckPassword(Eval("IsActive"),Eval("LoginName")) %>' />--%>
                                            </td>
                                        </tr>
                                       <%-- <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ibtnDelete"
                                            ConfirmText='<%# "Please confirm if you want to Delete " + Eval("LoginName") +" " + "user?" %>'
                                            OnClientCancel="CancelClick">
                                        </TK1:ConfirmButtonExtender>
                                        <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender2" runat="server" TargetControlID="ibtnChangePassword"
                                            ConfirmText='<%# "Please confirm if you want to Reset Password for " + Eval("LoginName") +" " + "user?" %>'
                                            OnClientCancel="CancelClick">
                                        </TK1:ConfirmButtonExtender>--%>
                                        <%--<TK1:ConfirmButtonExtender ID="ConfirmButtonExtender3" runat="server" TargetControlID="imgActive"
                                            ConfirmText='<%# "Please confirm if you want to Activate " + Eval("LoginName") +" " + "user?" %>'
                                            OnClientCancel="CancelClick">--%>
                                        <%--</TK1:ConfirmButtonExtender>--%>
                                    </ItemTemplate>
                                </asp:ListView>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                        <div class="row">
                            <div class="col-md-6">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvUser">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                                <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                Out Of
                                                <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-md-6 text-right">
                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvUser" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button"
                                            ShowFirstPageButton="false" ShowLastPageButton="false" ShowNextPageButton="false"
                                            PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10" NextPreviousButtonCssClass="btn-pagination"
                                            CurrentPageLabelCssClass="currentlabel" NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button"
                                            ShowFirstPageButton="false" ShowLastPageButton="false" ShowPreviousPageButton="false"
                                            ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>
                    </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</asp:Content>
