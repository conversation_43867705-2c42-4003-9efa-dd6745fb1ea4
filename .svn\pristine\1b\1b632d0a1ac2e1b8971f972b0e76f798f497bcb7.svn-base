﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System.Web;

namespace CP.UI.Controls
{
    public partial class ExchangeDAGOverview : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        private DropDownList _ddlReplicationType = new DropDownList();

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        private void Binddata()
        {
            //var result = Facade.GetExchangeDAGReplicationByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId,
            //                                              IsParentCompnay);
            var result = Facade.GetExchangeDAGReplicationByUserIdCompanyIdRoleAndReplicationFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
            lvExchangedagReplication.DataSource = result;
            lvExchangedagReplication.DataBind();
            lvExchangedagReplication.Visible = true;
        }

        private IList<ExchangeDAGReplication> GetExchageDagList()
        {
            return Facade.GetExchangeDAGReplicationByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
        }

        public IList<ExchangeDAGReplication> GetExchangeDAGList(string searchvalue)
        {
            var replicationlist = GetExchageDagList();
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count> 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvExchangedagReplication.Items.Clear();
                lvExchangedagReplication.DataSource = GetExchangeDAGList(txtsearchvalue.Text);
                lvExchangedagReplication.DataBind();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void lvExchangedagReplicationItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                var lblId = lvExchangedagReplication.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvExchangedagReplication.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;

                if (lblId != null && lblId.Text != null && lblName != null  && ValidateRequest("ExchangeDAGOverview Delete", UserActionType.ReplicationList))
                 {
                    //var applicationDetailByReplicationId =
                    //    Facade.GetApplicationGroupsByReplicationId(Convert.ToInt32(lblId.Text));
                    //if (applicationDetailByReplicationId != null)
                    //{
                    //    ErrorSuccessNotifier.AddSuccessMessage("The ExchangeDAG Replication component is in use");
                    //}
                    //else
                    //{
                    Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "ExchangeDAGUrReplication",
                                          UserActionType.DeleteReplicationComponent,
                                          "TheExchageDAGUr Replication component '" + lblName.Text +
                                          "' was deleted from the replication component", LoggedInUserId);

                    ErrorSuccessNotifier.AddSuccessMessage(
                        Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                            "ExchageDAG Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));

                    //  }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "9");

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "9");
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByReplicationId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Replication " + name + " attaching with Site " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }

        protected void LvExchangedagItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvExchangedagReplication.Items[e.NewEditIndex].FindControl("Id")) as Label;

            var lblName = (lvExchangedagReplication.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;

            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "ExchangeDag", UserActionType.UpdateReplicationComponent,
                                     "The ExchangeDag Replication component '" + lblName.Text +
                                     "' Opened as Editing Mode", LoggedInUserId);

            if (lbl1 != null && lblName != null && ValidateRequest("ExchangeDAGOverview Edit", UserActionType.ReplicationList))
            {
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                //    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, "20");
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "20");
                Helper.Url.Redirect(secureUrl);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void LvExchangedagPreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }

        protected void LvExchangedagItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                var lblId = lvExchangedagReplication.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvExchangedagReplication.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
               // var ReplicationDetail = Facade.GetReplicationBaseById(Convert.ToInt32(lblId.Text));
                //var SiteDetail = Facade.GetSiteById(ReplicationDetail.SiteId);
                var InfraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));
                if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("ExchangeDAGOverview Delete", UserActionType.ReplicationList))
                {

                    if (InfraObjects!=null && InfraObjects.Count>0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The ExchangeDAG Replication component is in use");
                    }
                    else
                    {
                    Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "ExchangeDAGReplication",
                                          UserActionType.DeleteReplicationComponent,
                                          "The ExchangeDAG Replication component '" + lblName.Text +
                                          "' was deleted from the replication component", LoggedInUserId);

                    ErrorSuccessNotifier.AddSuccessMessage(
                        Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                            "ExchangeDAG Replication Component", TransactionType.Delete));

                     }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "20");

                Helper.Url.Redirect(secureUrl);
            }
        }

        public override void PrepareView()
        {
           
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }


        protected void LvReplication_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator||IsUserManager || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }
    }
}