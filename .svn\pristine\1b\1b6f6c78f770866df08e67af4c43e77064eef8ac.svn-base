﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RoboCopy", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class RoboCopy : BaseEntity
    {

        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties


        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public string RoboCopyPath { get; set; }

        [DataMember]
        public string OSPlatform { get; set; }

        [DataMember]
        public string ScheduleTime { get; set; }

        [DataMember]
        public int LastReplicationCount { get; set; }

        [DataMember]
        public string Datalag { get; set; }

        [DataMember]
        public string ModeType { get; set; }

        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }

        [DataMember]
        public string LocalDirectory { get; set; }

        [DataMember]
        public string RemoteDirectory { get; set; }


        #endregion Properties
    }
}
