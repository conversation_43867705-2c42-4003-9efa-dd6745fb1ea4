﻿using CP.Common.Base;
using System.Runtime.Serialization;
using System;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "InfraobjectGlobalMirrorLunsDetails", Namespace = "http://www.ContinuityPatrol.com/types")]
    public class InfraobjectGlobalMirrorLunsDetails: BaseEntity
    {
        
       
        [DataMember]
        public int InfraLunsId { get; set; }

        [DataMember]
        public string Hdisk { get; set; }

        [DataMember]
        public string PVID { get; set; }

        [DataMember]
        public string LUNID { get; set; }


    }
}
