﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.ServiceProcess;
using System.Threading;
using System.Web.UI.WebControls;
using System.Xml;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;

namespace CP.UI.Admin
{
    public partial class CustomWorkflow : BasePage
    {
        private readonly IList<WorkflowAction> _listwork = new List<WorkflowAction>();
        private int _workflowselectedId;
        private static int _actioncount;
        private static string _popControlId = "";

        private DROperation DrList()
        {
            var groupid = Convert.ToInt32(Session["infraobjectid"]);
            var drid = Facade.GetInfraObjectById(groupid);
            if (drid.DROperationId > 0)
            {
                var drlastid = Facade.GetDROperationById(drid.DROperationId);
                return drlastid;
            }
            return null;
        }

        public override void PrepareView()
        {
            if (IsUserSuperAdmin)
            {
                var groupid = Convert.ToInt32(Session["infraobjectid"]);

                if (IsPostBack)
                {
                    if (groupid > 0)
                    {
                        var drid = Facade.GetInfraObjectById(groupid);
                        var drlastid = Facade.GetDROperationById(drid.DROperationId);
                        if (drlastid != null)
                        {
                            //foreach (DROperation drid in drlastid)
                            //{
                            var status = drlastid.Status;
                            Session["ActionModeCustom"] = drlastid.ActionMode;
                            if (status == "Running")
                            {
                                BtnStartCustom.Enabled = false;
                                Session["checkstatus"] = "None";
                                Timerworkflow.Enabled = true;
                            }

                            //}
                        }
                    }
                }

                var grpName = Facade.GetInfraObjectById(groupid);
                //var getCustom = Facade.GetGroupWorkflowByGroupIdAndCustomType(groupid, (int)WorkflowManagement.Custom);
                var getCustom = Facade.GetGroupWorkflowsByinfraObjectIdAndCustomType(groupid, (int)WorkflowManagement.Custom);

                if (getCustom != null && getCustom.Count() != 0)
                {
                    var listworkflow = new List<Workflow>();
                    if (!IsPostBack)
                    {
                        foreach (var workflowName in getCustom)
                        {
                            var groupworkflow = new Workflow();
                            var work = Facade.GetWorkflowById(workflowName.WorkflowId);
                            groupworkflow.Id = work.Id;
                            groupworkflow.Name = work.Name;
                            listworkflow.Add(groupworkflow);
                        }
                        dllCustomWorkflow.DataSource = listworkflow;
                        dllCustomWorkflow.DataTextField = "Name";
                        dllCustomWorkflow.DataValueField = "Id";
                        dllCustomWorkflow.DataBind();
                        dllCustomWorkflow.Items.Insert(0, new ListItem("Select Workflow", "0"));
                        var drOperationId = DrList();

                        if (drOperationId != null)
                        {
                            if (drOperationId.Status == "Running")
                            {
                                GetWorkFlowValueStart();
                            }
                        }
                    }
                    //ddlActionSetList.Items.Insert(ddlActionSetList.Items.Count, new ListItem("Insert ActionSet Property", "000"));

                    if (IsPostBack)
                    {
                        GetWorkFlowValueStart();
                    }
                }
                else
                {
                    errorMsgabort.Visible = true;
                    lblabort.Text = "Custom Workflow does not assign for " + grpName.Name + " Group";
                    BtnStartCustom.Visible = false;
                }
            }
            else
            {
                errorMsgabort.Visible = true;
                lblabort.Text = "Sorry You don't have permission to access";
            }
        }

        public void GetWorkFlowValue()
        {
            var returnValue = "";
            var workflow = Facade.GetWorkflowById(_workflowselectedId);
            var rawXml = workflow.Xml;
            var reader = XmlReader.Create(new StringReader(rawXml));
            while (reader.Read())
            {
                switch (reader.NodeType)
                {
                    case XmlNodeType.Element:

                        if (reader.HasAttributes)
                        {
                            returnValue = returnValue + reader.Name + ",";
                            returnValue = returnValue + reader.GetAttribute(0) + ",";
                            //switch (reader.Name)
                            //{
                            //    case "Property":
                            //        foreach (WorkflowProperty WorkFlowProperty in AllWorkflowProperty)
                            //        {
                            //            if (WorkFlowProperty.Id == Convert.ToInt32(reader.GetAttribute(0)))
                            //            {
                            //                returnValue = returnValue + WorkFlowProperty.Id.ToString() + "," + CryptographyHelper.Md5Decrypt(WorkFlowProperty.Action) + ":";
                            //                break;
                            //            }
                            //        }
                            //        break;
                            //    default:
                            //        foreach (ActionSet ac in allActionSet)
                            //        {
                            //            if (ac.Id == Convert.ToInt32(reader.GetAttribute(0)))
                            //            {
                            //                returnValue = returnValue + ac.Name + ":";
                            //                break;
                            //            }
                            //        }
                            //        break;

                            //}
                        }
                        break;
                }
            }
            returnValue = returnValue.TrimEnd(':');
            var str = workflow.Name + ":" + returnValue;
            CreateWorkflowDiagram(str);
        }

        private void CreateWorkflowDiagram(string str)
        {
            Session["retry"] = null;
            var resultArray = str.Split(':');
            var count = resultArray.Length;
            for (var i = 0; i < count; i++)
            {
                if (i == 0) continue;
                var setProperties = resultArray[i].Split(',');
                for (var j = 1; j < setProperties.Length; j++)
                {
                    if (!string.IsNullOrEmpty(setProperties[j].ToString()))
                    {
                        bool allDigits = setProperties[j].ToString().All(char.IsDigit);
                        if (allDigits)
                        {
                            var sublistwork = new WorkflowAction();
                            var actionname = Facade.GetWorkflowActionById(setProperties[j].ToInteger());
                            sublistwork.Id = setProperties[j].ToInteger();
                            sublistwork.Name = actionname.Name;
                            //sublistwork.Id = j;
                            //------------------------------------------------------------------start-----------------------------------------------------------------------------------

                            //if (DrLastid == null || Convert.ToInt32(DrLastid) == 0)
                            //{
                            //    var drlastid = Facade.GetLastDROperation();

                            //    if (drlastid != null)
                            //    {
                            //        foreach (DROperation drid in drlastid)
                            //        {
                            //            Session["drlastid"] = drid.Id;
                            //            if (drid.Id == 1)
                            //            {
                            //                Session["drlastid"] = 0;
                            //            }
                            //        }
                            //    }
                            //    DrLastid = Session["drlastid"];
                            //}
                            //if (IsPostBack)
                            //{
                            //    if (DrLastid != null)
                            //    {
                            //        var getall = Facade.GetAllDROperationResult();
                            //        if (getall != null)
                            //        {
                            //            var result = from drOperation in getall where drOperation.DROperationId == drRunningId select drOperation;
                            //            var countresult = result.Count();
                            //            var loadresultcount = countresult + 1;

                            //            if (countresult > 0)
                            //            {
                            //                var rcount = 1;

                            //                foreach (var dr in result)
                            //                {
                            //                    if (findCount == rcount)
                            //                    {
                            //                        sublistwork.Description = dr.Status;
                            //                        if (sublistwork.Description == "Retry")
                            //                        {
                            //                            Session["retry"] = "Ok";
                            //                        }
                            //                        if (sublistwork.Description == "Error")
                            //                        {
                            //                            Session["actionId"] = sublistwork.Id;
                            //                        }
                            //                    }
                            //                    rcount++;
                            //                }
                            //            }

                            //            if (countresult < setProperties.Length / 2)
                            //            {
                            //                if (findCount == loadresultcount)
                            //                {
                            //                    if (((string)Session["checkstatus"] != "incomplete" && (string)Session["retry"] != "Ok"))
                            //                    {
                            //                        sublistwork.Description = "Load";
                            //                    }
                            //                    //if(retry==false)
                            //                    //{
                            //                    //    if (Session["retry"] != "Ok")
                            //                    //    {
                            //                    //        sublistwork.Description = "Load";
                            //                    //    }
                            //                    //}

                            //                }
                            //            }
                            //            if (countresult == setProperties.Length / 2)
                            //            {
                            //                Session["checkstatus"] = "completed";
                            //            }

                            //        }
                            //        else if (j == 1)
                            //        {
                            //            sublistwork.Description = "Load";

                            //        }

                            //    }
                            //    else if (j == 1)
                            //    {
                            //        sublistwork.Description = "Load";
                            //    }

                            //}
                            //-------------------------------------------------------------end-------------------------------------------------------------------------------------------
                            _listwork.Add(sublistwork);
                            j++;
                        }
                    }
                }
            }
            lvworkflow.DataSource = _listwork;
            lvworkflow.DataBind();
        }

        public void GetWorkFlowValueStart()
        {
            var returnValue = "";
            var group = Session["infraobjectid"];
            var groupid = Convert.ToInt32(group);
            var grpName = Facade.GetInfraObjectById(groupid);
            var drId = DrList();
            int wId = 0;

            wId = drId.WorkflowId;

            if (wId != 0)
            {
                var workflow = Facade.GetWorkflowById(wId);
                dllCustomWorkflow.SelectedValue = Convert.ToString(wId);
                var rawXml = workflow.Xml;
                var reader = XmlReader.Create(new StringReader(rawXml));
                while (reader.Read())
                {
                    switch (reader.NodeType)
                    {
                        case XmlNodeType.Element:

                            if (reader.HasAttributes)
                            {
                                returnValue = returnValue + reader.Name + ",";
                                returnValue = returnValue + reader.GetAttribute(0) + ",";
                            }
                            break;
                    }
                }
                returnValue = returnValue.TrimEnd(':');
                string str = workflow.Name + ":" + returnValue;
                CreateWorkflowDiagramStart(str);
            }
            else
            {
                errorMsgabort.Visible = true;

                lblabort.Text = "Custom Workflow does not assign for " + grpName.Name + " Group";
                BtnStartCustom.Visible = false;
            }
        }

        private void CreateWorkflowDiagramStart(string str)
        {
            Session["retry"] = null;
            var resultArray = str.Split(':');

            var count = resultArray.Length;
            var numAction = resultArray[1].Replace("Property,", "");
            var strarray = numAction.TrimEnd(',');
            var getArray = strarray.Split(',');
            _actioncount = getArray.Count();
            var containList = new List<string>();
            containList.Clear();
            for (var i = 0; i < count; i++)
            {
                if (i == 0) continue;
                var setProperties = resultArray[i].Split(',');
                var findCount = 1;
                var getActionnumber = 0;
                for (var j = 1; j < setProperties.Length; j++)
                {
                    var sublistwork = new WorkflowAction();
                    var actionname = Facade.GetWorkflowActionById(setProperties[j].ToInteger());
                    sublistwork.Id = setProperties[j].ToInteger();
                    var setModeId = 0;
                    var drRunningId = 0;

                    var ldrid = DrList();
                    drRunningId = ldrid.Id;
                    setModeId = ldrid.ActionMode;
                    sublistwork.Name = actionname.Name;

                    if (IsPostBack)
                    {
                        Workflowstatus(setModeId, sublistwork, findCount, drRunningId, getActionnumber, actionname, getArray, setProperties, j);
                    }
                    else
                    {
                        var drid = DrList();

                        if (drid.Status == "Running")
                        {
                            Workflowstatus(setModeId, sublistwork, findCount, drRunningId, getActionnumber, actionname, getArray, setProperties, j);
                        }
                    }

                    _listwork.Add(sublistwork);
                    j++;
                    getActionnumber++;
                    findCount++;
                }
            }
            lvworkflow.DataSource = _listwork;
            lvworkflow.DataBind();
        }

        private void Workflowstatus(int mode, WorkflowAction sAction, int fcount, int drRid, int getNum, WorkflowAction action, string[] array, string[] setarray, int intcrement)
        {
            WorkflowAction sublistwork = sAction;
            var drRunningId = drRid;
            var findCount = fcount;
            var actionname = action;
            var getArray = array;
            var getActionnumber = 0;
            var j = intcrement;
            getActionnumber = getNum;
            var setProperties = setarray;

            switch (mode)
            {
                case 2:
                    {
                        //Step Mode
                        sublistwork.IsActive = 1;
                        BtnAutoAbort.Visible = false;
                        Session["SetMode"] = 2;

                        var getall = Facade.GetAllDROperationResults();
                        if (getall != null)
                        {
                            var getFirst = from drOperation in getall
                                           where drOperation.DROperationId == drRunningId
                                           select drOperation;
                            var totCount = getFirst.Count();
                            //var loadresultcount = totCount + 1;
                            var result = from drOperation in getall
                                         where
                                             drOperation.DROperationId == drRunningId &&
                                             drOperation.ActionId == actionname.Id
                                         select drOperation;
                            var countresult = result.Count();
                            if (drRunningId != 0)
                            {
                                var rcount = 1;
                                if (totCount > 0)
                                {
                                    if (countresult > 0)
                                    {
                                        foreach (var dr in result)
                                        {
                                            if (findCount == rcount)
                                            {
                                                if (dr.ActionId == actionname.Id)
                                                {
                                                    sublistwork.Description = dr.Status;
                                                }
                                                else
                                                {
                                                    sublistwork.Description = "Next";
                                                    BtnCompleted.Visible = true;
                                                }
                                            }

                                            if (dr.Status == "Error")
                                            {
                                                Session["Status"] = "Error";
                                                sublistwork.Description = dr.Status;
                                                BtnCompleted.Visible = true;
                                                if (findCount != getArray.Count())
                                                {
                                                    Session["ActId"] =
                                                        getArray[getActionnumber + 1].ToInteger();
                                                }
                                                else
                                                {
                                                    Session["ActId"] = 0;
                                                }
                                            }
                                            if (dr.Status == "Running")
                                            {
                                                sublistwork.Description = dr.Status;
                                                Session["Status"] = "Running";
                                                BtnCompleted.Visible = false;
                                            }
                                            if (dr.Status == "Success")
                                            {
                                                sublistwork.Description = dr.Status;
                                                Session["Status"] = "Success";
                                                BtnCompleted.Visible = true;
                                                if (findCount != getArray.Count())
                                                {
                                                    Session["ActId"] =
                                                        getArray[getActionnumber + 1].ToInteger();
                                                }
                                                else
                                                {
                                                    Session["ActId"] = 0;
                                                }
                                            }
                                            if (dr.Status == "Pending")
                                            {
                                                sublistwork.Description = dr.Status;
                                                Session["Status"] = "Pending";
                                                BtnCompleted.Visible = false;
                                            }
                                            rcount++;
                                        }
                                    }

                                    var aId = setProperties[j].ToInteger();
                                    var getAId = getArray[getActionnumber].ToInteger();

                                    if (aId == getAId && Convert.ToString(Session["Status"]) == "Success" &&
                                        Convert.ToInt32(Session["ActId"]) == getAId)
                                    {
                                        sublistwork.Description = "Next";
                                        BtnCompleted.Visible = true;
                                    }
                                    if (aId == getAId && Convert.ToString(Session["Status"]) == "Error" &&
                                        Convert.ToInt32(Session["ActId"]) == getAId)
                                    {
                                        sublistwork.Description = "Next";
                                        BtnCompleted.Visible = true;
                                    }

                                    if (_actioncount == totCount)
                                    {
                                        BtnCompleted.Visible = true;
                                    }
                                    else
                                    {
                                        //if (findCount == rcount)
                                        //{
                                        //    sublistwork.Description = "Next";
                                        //    BtnCompleted.Visible = true;
                                        //}
                                    }
                                }
                                else
                                {
                                    if (findCount == 1)
                                    {
                                        sublistwork.Description = "Next";
                                        BtnCompleted.Visible = true;
                                    }
                                }
                            }
                            else
                            {
                                if (findCount == 1)
                                {
                                    sublistwork.Description = "Next";
                                    BtnCompleted.Visible = true;
                                }
                            }
                        }
                        else
                        {
                            if (findCount == 1)
                            {
                                sublistwork.Description = "Next";
                                BtnCompleted.Visible = true;
                            }
                        }
                        break;
                    }
                case 1:
                    {
                        sublistwork.IsActive = 0;
                        Session["SetMode"] = 1;

                        var getall = Facade.GetAllDROperationResults();
                        if (getall != null)
                        {
                            BtnAutoAbort.Visible = true;
                            var result = from drOperation in getall where drOperation.DROperationId == drRunningId select drOperation;
                            var countresult = result.Count();
                            var loadresultcount = countresult + 1;
                            if (drRunningId != 0 && countresult != 0)
                            {
                                if (countresult > 0)
                                {
                                    var rcount = 1;

                                    foreach (var dr in result)
                                    {
                                        if (findCount == rcount)
                                        {
                                            sublistwork.Description = dr.Status;
                                            if (sublistwork.Description == "Retry")
                                            {
                                                Session["retry"] = "Ok";
                                            }
                                            if (sublistwork.Description == "Error")
                                            {
                                                Session["CustomactionId"] = null;
                                                if (string.IsNullOrEmpty(_popControlId))
                                                {
                                                    Session["checkstatus"] = "incomplete";
                                                    Session["CustomactionId"] = sublistwork.Id;
                                                }
                                                else
                                                {
                                                    var resetId = _popControlId.Split(',');

                                                    if (Convert.ToInt32(resetId[0]) == sublistwork.Id && Convert.ToInt32(resetId[1]) == 1)
                                                    {
                                                        Session["checkstatus"] = "incomplete_refresh";
                                                    }
                                                    else
                                                    {
                                                        Session["checkstatus"] = "incomplete";
                                                        Session["CustomactionId"] = sublistwork.Id;
                                                        _popControlId = string.Empty;
                                                    }
                                                }
                                            }

                                            if (sublistwork.Description == "Running")
                                            {
                                                Session["checkstatus"] = "NextLoading";
                                            }
                                            if (sublistwork.Description == "Skip")
                                            {
                                                Session["checkstatus"] = "Skip";
                                            }
                                            if (sublistwork.Description == "Success")
                                            {
                                                Session["checkstatus"] = "Success";
                                            }
                                        }

                                        rcount++;
                                    }
                                    if (loadresultcount == findCount && (string)Session["checkstatus"] != "incomplete" && (string)Session["retry"] != "Ok" && (string)Session["checkstatus"] != "NextLoading")
                                    {
                                        sublistwork.Description = "Load";
                                    }
                                }
                                else
                                {
                                    sublistwork.Description = "Load";
                                }
                                if (countresult < setProperties.Length / 2)
                                {
                                    if (findCount == loadresultcount - 1)
                                    {
                                        if (((string)Session["checkstatus"] != "incomplete" && (string)Session["retry"] != "Ok") && (string)Session["checkstatus"] == "NextLoading")
                                        {
                                            //sublistwork.Description = "Load";
                                            sublistwork.Description = "Load";
                                        }
                                        //if(retry==false)
                                        //{
                                        //    if (Session["retry"] != "Ok")
                                        //    {
                                        //        sublistwork.Description = "Load";
                                        //    }
                                        //}
                                    }
                                }
                                if (countresult == setProperties.Length / 2)
                                {
                                    if ((string)Session["checkstatus"] != "NextLoading" && (string)Session["checkstatus"] != "incomplete" && (string)Session["retry"] != "Ok" && (string)Session["checkstatus"] == "Success")
                                    {
                                        Session["checkstatus"] = "completed";
                                    }
                                }
                            }
                            else if (j == 1)
                            {
                                sublistwork.Description = "Load";
                            }
                        }
                        else if (j == 1)
                        {
                            sublistwork.Description = "Load";
                        }
                    }
                    break;
            }
        }

        protected string ActiveProcess(object act)
        {
            var val = Convert.ToString(act);

            var strActive = string.Empty;

            switch (val)
            {
                case "Load":
                    strActive = "../images/icons/loader-icon.gif";
                    break;

                case "Success":
                    strActive = "../images/icons/tick-circle.png";
                    break;

                case "Error":
                    strActive = "../images/icons/cross-small.png";
                    Session["checkstatus"] = "incomplete";
                    return strActive;

                case "Skip":
                    strActive = "../images/icons/icon-skip.png";
                    //Session["checkstatus"] = "skip";
                    break;

                case "Retry":
                    strActive = "../images/icons/loader-icon.gif";
                    Session["checkstatus"] = "retry";
                    BtnCompleted.Visible = false;
                    return strActive;

                case "Abort":
                    strActive = "../images/icons/icon-abort.png";
                    Session["checkstatus"] = "abort";
                    return strActive;

                case "Pending":
                    Session["checkstatus"] = "next";
                    strActive = "../images/icons/loader-icon.gif";
                    BtnCompleted.Visible = false;
                    return strActive;

                case "Running":
                    strActive = "../images/icons/loader-icon.gif";
                    BtnCompleted.Visible = false;
                    break;
            }
            return strActive;
        }

        protected bool ActiveErrorIcon(object act)
        {
            var val = Convert.ToString(act);

            var strActive = false;

            switch (val)
            {
                case "Success":
                    strActive = true;
                    break;

                case "Error":
                    strActive = true;
                    break;

                case "Skip":
                    strActive = true;
                    break;

                case "Abort":
                    strActive = true;
                    break;

                case "Retry":
                    strActive = true;
                    break;
            }
            return strActive;
        }

        protected string ActiveImageIcon(object act)
        {
            var val = Convert.ToString(act);

            var strActive = string.Empty;
            switch (val)
            {
                case "Success":
                    strActive = "../Images/icons/fatal-icon.png";
                    break;

                case "Error":
                    strActive = "../Images/icons/fatal-icon.png";
                    break;

                case "Skip":
                    strActive = "../Images/icons/fatal-icon.png";
                    break;

                case "Abort":
                    strActive = "../Images/icons/fatal-icon.png";
                    break;

                case "Retry":
                    strActive = "../Images/icons/fatal-icon.png";
                    break;
            }
            return strActive;
        }

        protected string BgActions(object act)
        {
            var val = Convert.ToString(act);
            string strActive = string.Empty;

            switch (val)
            {
                case "Load":
                    strActive = "yellowbg";
                    break;

                case "Success":
                    strActive = "greenbg";
                    break;

                case "Error":
                    strActive = "redbg";
                    Session["checkstatus"] = "incomplete";
                    return strActive;

                case "Skip":
                    strActive = "bluebg";
                    //Session["checkstatus"] = "skip";
                    break;

                case "Retry":
                    strActive = "yellowbg";
                    Session["checkstatus"] = "retry";
                    BtnCompleted.Visible = false;
                    return strActive;

                case "Abort":
                    strActive = "redbg";
                    Session["checkstatus"] = "abort";
                    return strActive;

                case "Pending":
                    Session["checkstatus"] = "next";
                    strActive = "yellowbg";
                    BtnCompleted.Visible = false;
                    return strActive;

                case "Running":
                    strActive = "yellowbg";
                    BtnCompleted.Visible = false;
                    break;
            }
            if (strActive == "")
            {
                strActive = "one";
            }
            return strActive;
        }

        protected bool ModeChaging(object act)
        {
            var val = Convert.ToString(act);

            var strActive = false;

            switch (val)
            {
                case "True":
                    strActive = true;
                    break;

                case "False":
                    break;
            }
            return strActive;
        }

        protected string ClassChaging(object act)
        {
            var val = Convert.ToString(act);

            var strActive = string.Empty;

            switch (val)
            {
                case "True":
                    strActive = "width250 float-left";
                    break;

                case "False":
                    strActive = "width340 float-left";
                    break;
            }
            return strActive;
        }

        protected bool ActiveButtonIcon(object act)
        {
            var val = Convert.ToString(act);

            var strActive = false;
            switch (val)
            {
                case "Success":
                    break;

                case "Error":
                    if (Convert.ToInt32(Session["SetMode"]) == 2)
                    {
                        strActive = true;
                    }
                    break;

                case "Pending":
                    break;

                case "Running":
                    break;

                case "Next":
                    strActive = true;
                    break;
            }
            return strActive;
        }

        protected void DllCustomWorkflowSelectedIndexChanged(object sender, EventArgs e)
        {
            if (dllCustomWorkflow.SelectedValue != "0")
            {
                _workflowselectedId = dllCustomWorkflow.SelectedValue.ToInteger();
                BtnStartCustom.Visible = true;
                GetWorkFlowValue();
                _listwork.Clear();
            }
        }

        protected void UpdateTimerTick(object sender, EventArgs e)
        {
            PrepareView();
            var errorAaction = string.Empty;
            var mode = 0;
            var condtion = DrList();
            mode = condtion.ActionMode;
            if (mode == 1)
            {
                var check = (string)Session["checkstatus"];
                var str = check;
                if (Session["checkstatus"].ToString() == "completed")
                {
                    popupmessage.Visible = true;
                    lblMsg.Text = "Custom Workflow Completed Successfully";
                    Timerworkflow.Enabled = false;
                    return;
                }
                if (Session["checkstatus"].ToString() == "incomplete")
                {
                    int actionid = Convert.ToInt32(Session["CustomactionId"]);
                    if (actionid != 0)
                    {
                        var action = Facade.GetWorkflowActionById(actionid);
                        errorAaction = action.Name;
                    }
                    if (string.IsNullOrEmpty(_popControlId))
                    {
                        popuperrormessage.Visible = true;
                        lblerrorMsg.Text = errorAaction + " action Failed";
                        ModalPopupExtenderCustom.Show();
                        Timerworkflow.Enabled = false;
                    }
                }
                if (Session["checkstatus"].ToString() == "incomplete_refresh")
                {
                    Timerworkflow.Enabled = true;
                    popuperrormessage.Visible = false;
                    _popControlId = string.Empty;
                }
                else if (Session["checkstatus"].ToString() == "skip")
                {
                    Timerworkflow.Enabled = true;
                    popuperrormessage.Visible = false;
                }
                else if (Session["checkstatus"].ToString() == "retry")
                {
                    Timerworkflow.Enabled = true;
                    popuperrormessage.Visible = false;
                }
                else if (Session["checkstatus"].ToString() == "abort")
                {
                    Timerworkflow.Enabled = false;
                    errorMsgabort.Visible = true;
                    lblabort.Text = "Workflow Aborted try again";
                }
                if (Session["checkstatus"].ToString() == "running")
                {
                    Timerworkflow.Enabled = true;
                    popuperrormessage.Visible = false;
                }
                else
                {
                    popupmessage.Visible = false;
                    popuperrormessage.Visible = false;
                }
            }
        }

        protected void BtnAutoAbortClick(object sender, EventArgs e)
        {
            //var drStatus = new DROperation();
            var drStatus = DrList();
            if (drStatus.Status == "Running")
            {
                drStatus.Status = "Abort";
                Facade.UpdateDROperation(drStatus);
                Session["Completed"] = "Yes";
                popupmessage.Visible = true;
                lblMsg.Text = "Workflow Aborted Successfully";
                BtnCompleted.Enabled = false;
            }
        }

        protected bool WorkflowRunningStatus()
        {
            var drOperationId = new DROperation();
            var groupId = Convert.ToInt32(Session["infraobjectid"]);
            bool running = true;

            if (DrList() != null)
            {
                drOperationId = DrList();
            }
            if (drOperationId.Status == "Running" && drOperationId.InfraObjectId == groupId)
            {
                running = false;
            }
            return running;
        }

        protected void BtnPasswordOkClick(object sender, EventArgs e)
        {
            if (txtpassword.Text != "")
            {
                string str = txtpassword.Text;
                string pswd = string.Empty;
                var getPsd = str.Split(',');
                var count = getPsd.Count();
                if (count >= 1)
                {
                    if (count == 1)
                    {
                        pswd = getPsd[count - 1];
                    }
                    else
                    {
                        pswd = getPsd[count - 1];
                    }
                }
                if (pswd == "Bcmssadmin")
                {
                    //if (txtpassword.Text.Replace(",", "") == "Bcmssadmin")
                    //{
                    divPsdErrMsg.Visible = false;
                    lblPsdErrorMsg.Visible = false;

                    BtnStartCustom.Enabled = false;
                    ModalPopupExtenderPassword.Hide();
                    Session["SetMode"] = RadioWorkflowExecution.SelectedValue;
                    txtpassword.Text = string.Empty;
                    txtpassword.Attributes.Clear();
                    UpdatepanelAdd.Update();
                    var runningStatus = WorkflowRunningStatus();
                    if (runningStatus)
                    {
                        InsertDrOperation();
                        BtnAutoAbort.Visible = false;
                    }
                    PrepareView();
                }
                else
                {
                    ModalPopupExtenderPassword.Show();
                    txtpassword.Text = string.Empty;
                    txtpassword.Attributes.Clear();
                    divPsdErrMsg.Visible = true;
                    lblPsdErrorMsg.Visible = true;
                    lblPsdErrorMsg.Text = "Authentication Failed";
                }
            }
            else
            {
                divPsdErrMsg.Visible = true;
                txtpassword.Text = string.Empty;
                lblPsdErrorMsg.Visible = true;
                PanelCustomWorkflow.Enabled = false;
                lblPsdErrorMsg.Text = "Authentication Failed";
            }
        }

        protected void BtnCloseClick(object sender, EventArgs e)
        {
            ModalPopupExtenderPassword.Hide();
        }

        protected void BtnSkipClick(object sender, EventArgs e)
        {
            _popControlId = string.Empty;
            var lastId = DrList();
            _popControlId = Session["CustomactionId"].ToString();
            if (lastId.Id == 0) return;
            var sconfirm = Facade.UpdateDROperationByConditionalAction(lastId.Id, Convert.ToInt32(ConditionalOperationType.Skip));
            if (sconfirm)
            {
                _popControlId = _popControlId + ",1";
            }

            ModalPopupExtenderCustom.Hide();

            Timerworkflow.Enabled = true;
        }

        protected void BtnRetryClick(object sender, EventArgs e)
        {
            _popControlId = string.Empty;
            var lastId = DrList();
            _popControlId = Session["CustomactionId"].ToString();
            if (lastId.Id == 0) return;
            var sconfirm = Facade.UpdateDROperationByConditionalAction(lastId.Id, Convert.ToInt32(ConditionalOperationType.Retry));
            ModalPopupExtenderCustom.Hide();
            if (sconfirm)
            {
                _popControlId = _popControlId + ",1";
            }
            Timerworkflow.Enabled = true;
        }

        protected void BtnAbortClick(object sender, EventArgs e)
        {
            var lastId = DrList();
            if (lastId.Id == 0) return;
            Facade.UpdateDROperationByConditionalAction(lastId.Id, Convert.ToInt32(ConditionalOperationType.Abort));
            ModalPopupExtenderCustom.Hide();
            Timerworkflow.Enabled = true;
        }

        protected void BtnNextClick(object sender, EventArgs e)
        {
            var groupId = Convert.ToInt32(Session["infraobjectid"]);
            var lastId = Facade.GetDROperationByLastInfraObjectID(groupId);
            Facade.UpdateDROperationByConditionalAction(lastId.Id, (int)ConditionalOperationType.Next);
            Thread.Sleep(5000);
            Timerworkflow.Enabled = true;
        }

        protected void BtnStartClick(object sender, EventArgs e)
        {
            BtnStartCustom.Enabled = false;
            ModalPopupExtenderPassword.Show();
        }

        protected void BtnModeClick(object sender, EventArgs e)
        {
            ModalPopupExtenderPassword.Show();
        }

        protected void LvcustomItemCommand(object sender, ListViewCommandEventArgs e)
        {
            var lblAppId = (Label)e.Item.FindControl("lblActionId");
            var lblName = (Label)e.Item.FindControl("actionName");
            var btnRun = (Button)e.Item.FindControl("ibtnStartAction");
            var check = e.Item.FindControl("ChkRunAction") as CheckBox;
            if (e.CommandName == "StartAction")
            {
                var sc = new ServiceController("CPParallelService");

                if (sc.Status.ToString() != "Stopped")
                {
                    if (check != null && check.Checked)
                    {
                        check.Checked = false;
                    }
                    btnRun.Enabled = false;

                    ChkRunActionCheckedChanged(null, null);

                    var group = Session["infraobjectid"];

                    var groupid = Convert.ToInt32(group);
                    var droperationId = DrList();
                    Facade.UpdateGroupByWorkflowOperation(groupid, (int)InfraWorkflowOperation.CustomStart, droperationId.Id);

                    var droperation = Facade.GetDROperationByLastInfraObjectID(groupid);
                    var drResult = new DROperationResult
                                       {
                                           ActionId = lblAppId.Text.ToInteger(),
                                           ActionName = lblName.Text,
                                           ElapsedTime = Convert.ToString(DateTime.Now),
                                           StartTime = DateTime.Now,
                                           EndTime = DateTime.Now,
                                           DROperationId = droperation.Id,
                                           Status = "Pending",
                                           CreatorId = 1
                                       };
                    Facade.AddDROperationResult(drResult);

                    Timerworkflow.Enabled = true;

                    sc.ExecuteCommand(134);
                }
                else
                {
                    throw new CpException(CpExceptionType.BWSServiceStopped, "CP Parallel Service Stopped state");
                }
            }

            if (e.CommandName == "ErrorMsg")
            {
                var drid = DrList();
                var drRunningId = 0;
                if (drid != null)
                {
                    drRunningId = drid.Id;
                }
                var getall = Facade.GetAllDROperationResults();
                var result = from drOperation in getall where drOperation.DROperationId == drRunningId && drOperation.ActionId == lblAppId.Text.ToInteger() select drOperation;
                foreach (var drmsg in result)
                {
                    lblMessage.Text = drmsg.Message;
                }
                Timerworkflow.Enabled = false;
            }

            PrepareView();
        }

        private void InsertDrOperation()
        {
            lblMessage.Text = string.Empty;
            var group = Session["infraobjectid"];
            BtnStartCustom.Enabled = false;
            Session["checkstatus"] = "None";
            var groupid = Convert.ToInt32(group);
            const int work = (int)InfraWorkflowOperation.CustomStart;
            var drObject = new DROperation();

            drObject.Type = work;
            drObject.StartTime = DateTime.Now;
            drObject.EndTime = DateTime.Now;
            drObject.Status = "Running";
            drObject.WorkflowId = dllCustomWorkflow.SelectedValue.ToInteger();
            drObject.ActionMode = RadioWorkflowExecution.SelectedValue.ToInteger();
            drObject.InfraObjectId = groupid;
            drObject.Direction = 0;
            var drOperationId = Facade.AddDROperation(drObject);
            Facade.UpdateGroupByWorkflowOperation(groupid, work, drOperationId.Id);
            var sc = new ServiceController("CPParallelService");
            if (Convert.ToInt32(Session["SetMode"]) == 1)
            {
                if (sc.Status.ToString() != "Stopped")
                {
                    Timerworkflow.Enabled = true;
                    sc.ExecuteCommand(136);
                    PrepareView();
                }
                else
                {
                    throw new CpException(CpExceptionType.BWSServiceStopped, "CP Parallel Service Stopped state");
                }
            }
            else
            {
                Timerworkflow.Enabled = false;
                BtnCompleted.Visible = true;
                PrepareView();
            }
        }

        protected void ChkRunActionCheckedChanged(object sender, EventArgs e)
        {
            Timerworkflow.Enabled = false;
            var chkHeader = sender as CheckBox;
            var drid = DrList();
            var drRunningId = 0;
            if (drid != null)
            {
                drRunningId = drid.Id;
            }
            var getall = Facade.GetAllDROperationResults();
            if (getall != null)
            {
                var result = from drOperation in getall
                             where drOperation.DROperationId == drRunningId
                             select drOperation;
                var strStatus = "";
                foreach (
                    var drstatus in
                        result.Where(drstatus => drstatus.Status == "Running" || drstatus.Status == "Pending"))
                {
                    strStatus = drstatus.Status;
                }
                if (strStatus == "Running" || strStatus == "Pending")
                {
                    if (chkHeader != null) chkHeader.Checked = false;
                    Timerworkflow.Enabled = true;
                }
                else
                {
                    if (chkHeader != null)
                    {
                        var item = (ListViewItem)chkHeader.NamingContainer;
                        var dataItem = (ListViewDataItem)item;
                        var code = dataItem.DataItemIndex.ToString(CultureInfo.InvariantCulture);
                        var i = Convert.ToInt32(code);
                        var chk = lvworkflow.Items[i].FindControl("ChkRunAction") as CheckBox;

                        if (chk != null && (chk.Checked = chkHeader.Checked))
                        {
                            for (var j = 0; j < _actioncount; j++)
                            {
                                if (i == j)
                                {
                                    var btnAction = lvworkflow.Items[i].FindControl("ibtnStartAction") as Button;
                                    if (btnAction != null) btnAction.Enabled = true;
                                    chk.Checked = chkHeader.Checked;
                                }
                                else
                                {
                                    var btnAction = lvworkflow.Items[j].FindControl("ibtnStartAction") as Button;
                                    if (btnAction != null) btnAction.Enabled = false;
                                    chk.Checked = false;
                                }
                            }
                        }
                        else
                        {
                            var btnAction = lvworkflow.Items[i].FindControl("ibtnStartAction") as Button;
                            if (btnAction != null) btnAction.Enabled = false;
                        }
                    }
                }
            }
            else
            {
                if (chkHeader != null)
                {
                    var item = (ListViewItem)chkHeader.NamingContainer;
                    var dataItem = (ListViewDataItem)item;
                    var code = dataItem.DataItemIndex.ToString(CultureInfo.InvariantCulture);
                    var i = Convert.ToInt32(code);
                    var chk = lvworkflow.Items[i].FindControl("ChkRunAction") as CheckBox;

                    if (chk != null && (chk.Checked = chkHeader.Checked))
                    {
                        for (var j = 0; j < _actioncount; j++)
                        {
                            if (i == j)
                            {
                                var btnAction = lvworkflow.Items[i].FindControl("ibtnStartAction") as Button;
                                if (btnAction != null) btnAction.Enabled = true;
                                chk.Checked = chkHeader.Checked;
                            }
                            else
                            {
                                var btnAction = lvworkflow.Items[j].FindControl("ibtnStartAction") as Button;
                                if (btnAction != null) btnAction.Enabled = false;
                                chk.Checked = false;
                            }
                        }
                    }
                    else
                    {
                        var btnAction = lvworkflow.Items[i].FindControl("ibtnStartAction") as Button;
                        if (btnAction != null) btnAction.Enabled = false;
                    }
                }
            }
        }

        protected void BtnCompletedClick(object sender, EventArgs e)
        {
            var drStatus = DrList();

            if (drStatus.Status == "Running")
            {
                drStatus.Status = "Success";
                Facade.UpdateDROperation(drStatus);
                Session["Completed"] = "Yes";
                popupmessage.Visible = true;
                lblMsg.Text = "Workflow Completed";
                BtnCompleted.Enabled = false;
            }
        }
    }
}