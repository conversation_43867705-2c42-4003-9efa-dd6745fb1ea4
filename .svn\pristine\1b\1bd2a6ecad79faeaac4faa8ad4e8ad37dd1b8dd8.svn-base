﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;


namespace CP.DataAccess
{
    #region IBFBIAMatrixDetailsDataAccess

    public interface IBFBIAMatrixDetailsDataAccess
    {

        BFBIAMatrixDetails Add(BFBIAMatrixDetails bfBIAMatrix);

        IList<BFBIAMatrixDetails> GetByBFMatrixID(int iBFMatID);

        BFBIAMatrixDetails GetByID(int iID);

        IList<BFBIAMatrixDetails> GetALL();

        IList<BFBIAMatrixDetails> GetBytimeIntervalId(int id);

    }

    #endregion

    
}
