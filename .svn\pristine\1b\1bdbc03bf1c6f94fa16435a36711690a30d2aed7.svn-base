﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="SNMPDiscovery.aspx.cs" Inherits="CP.UI.SNMPDiscovery" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <h3>
            <img src="../Images/business-process-icon.png" alt="Network Discovery ">
            Network Discovery</h3>
       
        <asp:UpdatePanel ID="updPnlScan" runat="server" UpdateMode="Always">
            <ContentTemplate>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="margin-right">IP Address From</label>
                                 <span class="up-arrow-icon"></span> <asp:TextBox ID="txtIPAddressFrom" runat="server" class="form-control" Placeholder="IP Address From"></asp:TextBox>

                            </div>
                            <div class="col-md-3">
                                <label class="margin-right">IP Address To</label>
                               <span class="down-arrow-icon"></span> <asp:TextBox ID="txtIPAddressTo" runat="server" class="form-control"></asp:TextBox>

                            </div>
                            <div class="col-md-3" style="display: none">
                                <label class="margin-right">Community</label>
                                <asp:TextBox ID="txtCommunity" runat="server" class="form-control" Text="public"></asp:TextBox>

                            </div>
                            <div class="col-md-2">
                                <asp:Button ID="btnscan" runat="server" CssClass="btn btn-primary" Text="Discover" OnClick="btnScanNetwork_Click" />

                            </div>

                            <div class="col-md-4">
                               <label>
                                <div>
                                    <span class="count-icon"></span> Host Found : 
                               <asp:Label ID="lblCount" runat="server" CssClass="text-danger" Text=""></asp:Label>
                                </div>

                          
                                <div  >
                                    <span class="icon-Time"></span>
                                    Discovery Time :  
                               <asp:Label ID="lblTime" runat="server" Text="" CssClass="text-danger"></asp:Label></div></label>
                            </div>
                        </div>
                        <hr class="margin-none margin-top" />
                        <asp:GridView ID="gvNetworkDevices" runat="server" CssClass="table table-striped table-bordered table-condensed table-white"
                            AutoGenerateColumns="false">
                            <Columns>

                                <asp:TemplateField HeaderText="ID" ItemStyle-Width="15px">
                                    <ItemTemplate>

                                        <asp:Label ID="lblID" runat="server" Text='<%# Eval("ID")%>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>

                                <asp:TemplateField HeaderText="IP Address">
                                    <ItemTemplate>
                                        <asp:Label ID="lblIcon" runat="server" CssClass='<%# Eval("ImageURL")%>'></asp:Label>
                                        <asp:Label ID="lblIPAddress" runat="server" Text='<%# Eval("IpAddress")%>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Host Name">
                                    <ItemTemplate>
                                        <asp:Label ID="lblHostName" runat="server" Text='<%# Eval("HostName")%>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Location">
                                    <ItemTemplate>
                                        <asp:Label ID="lblLocation" runat="server" Text='<%# Eval("Location")%>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                                <asp:TemplateField HeaderText="Device Type">
                                    <ItemTemplate>
                                        <asp:Label ID="lblType" runat="server" CssClass="label label-primary" Text='<%# Eval("Type")%>'></asp:Label>
                                    </ItemTemplate>
                                </asp:TemplateField>
                            </Columns>
                        </asp:GridView>

                    </div>
                </div>
          
                <asp:UpdateProgress ID="UpdateProgress1" AssociatedUpdatePanelID="updPnlScan" runat="server">
                    <ProgressTemplate>
                        <div id="imgLoading" class="loading-mask">
                            <span>Discovering...</span>
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
