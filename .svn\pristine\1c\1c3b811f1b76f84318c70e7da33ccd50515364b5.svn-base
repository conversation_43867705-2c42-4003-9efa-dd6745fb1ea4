﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="ParallelServerManagement.aspx.cs" Inherits="CP.UI.ParallelServerManagement"
    Title="Continuity Patrol :: Configure -ParallelServerManagement" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <link href="../App_Themes/CPTheme/chart.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript">
        var tblNetwork;
        var circleId = "";
        $(document).ready(function () {
           
            tblNetwork = $("#tblNetwork tbody");
            var ajaxUrl = "ParallelServerManagement.aspx/GetParallelView";
            var ajaxData = "{}";
            AjaxFunction(ajaxUrl, ajaxData, CreateCircle, OnError);
        });

        function CreateCircle(msg) {
            var element = msg.d.length;
            var header = "BCMS Server";
            appendMainHeader(element, header);
            AppendRow(msg.d);
        }

        function SetActivation(parameters) {
            var status = parameters.d.split(",");
            var id = document.getElementById(status[1]);
            if (status[0] == "true") {
                $(id).removeClass('parrallelserver-disabled');
                $(id).addClass('parrallelserver-back');
            }
            else {
                $(id).removeClass('parrallelserver-back');
                $(id).addClass('parrallelserver-disabled');
            }

        }

        var AppendRow = function (values) {
            var colValue = values;
            var tr = "<tr>";
            var endtr = "</tr>";
            var tdValue = "";
            var upperRow = "";
            var cssclass = "";
            for (var index = 0; index < colValue.length; index++) {
                if (colValue[index].Status)
                    cssclass = "parrallelserver-back";
                else
                    cssclass = "parrallelserver-disabled";
                tdValue = tdValue + '<td > <div id="' + colValue[index].IPAddress + '" class= "' + cssclass + '"> <span class="icon-parrallelserver margin-left">&nbsp;</span> <span>' + colValue[index].IPAddress + ' </span> </div></td>';
                upperRow = upperRow + '<td><div class="arrow-chart-line">&nbsp;</div></td>';
            }
            $(tblNetwork).append(tr + upperRow + endtr);
            $(tblNetwork).append(tr + tdValue + endtr);

        };

      

        var appendMainHeader = function (colspan, value) {
            var chartHeader = '<tr><td colspan=" ' + colspan + ' " align="center"><div class="company-back ">' + value + '</div></td></tr> ';
            $(tblNetwork).append(chartHeader);
            colspan = colspan - 2;
            if (navigator.userAgent.indexOf("Chrome") == -1) {
                var headerLine = '<tr><td ><img src="../images/chart_line.png" style="margin-left:49.5%;margin-bottom:-4px; vertical-align:bottom;" />' +
                '               </td> ' +
                ' <td colspan="' + colspan + '" align="center" style="border-bottom: 1px solid #2e80b8;"> ' +
                '    <div class="chartmain-line">  &nbsp;</div> ' +
                '       </td> ' +
                '         <td > ' +
                '               <img src="../Images/chart_line.png" style="margin-bottom:-4px; margin-left:-1px;" width="55px" /> ' +
                '            </td> ' +
                '        </tr>';
            }
            else {

                var headerLine = '<tr><td ><img src="../images/chart_line.png" style="margin-left:49.5%;margin-bottom:-16px; vertical-align:bottom;" />' +
                    '               </td> ' +
                        ' <td colspan="' + colspan + '" align="center" style="border-bottom: 1px solid #2e80b8;"> ' +
                            '    <div class="chartmain-line">  &nbsp;</div> ' +
                                '       </td> ' +
                                    '         <td > ' +
                                        '               <img src="../images/chart_line.png" style="margin-bottom:-16px; margin-left:-1px;  vertical-align:bottom;" width="55px" /> ' +
                                            '            </td> ' +
                                                '        </tr>';
            }

            $(tblNetwork).append(headerLine);
        };
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        
        <ul class="breadcrumb show">
            <li>You are here</li>
            <li><a href="#" class="glyphicons settings"><i></i>Configuration</a></li>
            <li class="divider"></li>
            <li>Parallel server Configuration</li>
        </ul>
        
        <h3>Parallel server Configuration</h3>
        
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-md-12 form-horizontal uniformjs">
                        <div class="form-group ">
                            <label class="col-md-3 control-label">
                                IP Address <span class="inactive">*</span>
                            </label>
                            <div class="col-md-9">
                                <asp:TextBox ID="TextBox1" CssClass="form-control" runat="server"></asp:TextBox>
                                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="TextBox1"
                                    ErrorMessage="Enter IP Address" Display="Dynamic" ValidationGroup="IP"></asp:RequiredFieldValidator>
                                <asp:Button ID="btnAdd" CssClass="btn btn-primary" Width="20%" runat="server" Text="Add"
                                    OnClick="BtnAddClick" ValidationGroup="IP" CausesValidation="True" />
                                <asp:RequiredFieldValidator ID="rfvtxtbox1" runat="server" ControlToValidate="TextBox1"
                                    ErrorMessage="*" Display="Dynamic" ValidationGroup="IP"></asp:RequiredFieldValidator>
                                <asp:RegularExpressionValidator ID="revIPAddress" runat="server" ControlToValidate="TextBox1"
                                    ErrorMessage="Enter Valid IP format" ValidationGroup="IP" ValidationExpression="^(([01]?\d\d?|2[0-4]\d|25[0-5])\.){3}([01]?\d\d?|25[0-5]|2[0-4]\d)$"
                                    Display="Dynamic"></asp:RegularExpressionValidator>
                                <asp:CustomValidator ID="CustomValidator1" runat="server" ErrorMessage="IP already configured"
                                    OnServerValidate="CustomValidator1ServerValidate" ControlToValidate="TextBox1"
                                    ValidationGroup="IP"></asp:CustomValidator>
                            </div>
                        </div>
                    </div>
                </div>
                <table cellspacing="0" cellpadding="0" align="center" id="tblNetwork">
                    <tbody>
                    </tbody>
                </table>

               
                <div id="divErrorMsg">
                    <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span>
                    <span class="black">Required Fields</span>
                </div>
            </div>
        </div>
       
    </div>
</asp:Content>