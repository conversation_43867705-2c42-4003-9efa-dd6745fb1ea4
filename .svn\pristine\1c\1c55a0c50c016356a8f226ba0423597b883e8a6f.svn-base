﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using BCMS.BusinessFacade;
using BCMS.Common.Shared;
using BCMS.ExceptionHandler;
using BCMS.Helper;
using BCMS.Common.DatabaseEntity;
using BCMS.UI.Controls;

namespace BCMS.UI
{
    public partial class ApplicationList : ApplicatinBasePage
    {
        #region Variable

        public static string CurrentURL = Constants.UrlConstants.Urls.Group.APPGROUP_CONFIG;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin)
                {
                    return Constants.UrlConstants.Urls.Group.APPGROUP_LIST;
                }
                return string.Empty;
            }
        }

        #endregion

        public string IsEnable(object st)
        {
            return Convert.ToInt32(st) == 1 ? "~/Images/Lock.png" : "~/Images/unlock.png";
        }

        protected bool DisableEdit(object item)
        {
            return Convert.ToString(item) != "Replicating";
        }

        protected string GetStatusType(object item)
        {
            switch (Convert.ToString(item))
            {
                case "Active":
                    return "../Images/icons/tick-circle.png";
                case "Locked":
                    return "../Images/icons/Lock.png";
                case "Replicating":
                    return "../Images/icons/loader.gif";
                case "Maintenance":
                    return "../Images/icons/maintenance.png";
            }
            return string.Empty;
        }

        protected void LvAppGroupListItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                var lbl = (lvAppgroupList.Items[e.ItemIndex].FindControl("ID")) as Label;

                var lblName = (lvAppgroupList.Items[e.ItemIndex].FindControl("NAME")) as Label;

                if (lbl != null && lblName != null)
                {
                    Facade.DeleteApplicationGroupById(Convert.ToInt32(lbl.Text));

                    ActivityLogger.AddLog(LoggedInUserName, "Application", UserActionType.DeleteCompanyProfile, "The Application '" + lblName.Text + "' was deleted");

                    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Application", TransactionType.Delete));

                }

            }
            catch (BcmsException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(BcmsException))
                {
                    ExceptionManager.Manage((BcmsException)ex.InnerException, Page);

                }
                else
                {
                    var customEx = new BcmsException(BcmsExceptionType.CommonUnhandled, "Unhandled exception occured while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void LvAppGroupListItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentURL);
            var lbl1 = (lvAppgroupList.Items[e.NewEditIndex].FindControl("ID")) as Label;
            var lblSol = (lvAppgroupList.Items[e.NewEditIndex].FindControl("Name")) as Label;
            if (lbl1 != null && lblSol != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.APPLICATION_ID,
                                                     lbl1.Text, Constants.UrlConstants.Params.SOLUTION_TYPE, lblSol.Text);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void LvAppGroupListPreRender(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                lvAppgroupList.DataSource = GetApplicationList();
                lvAppgroupList.DataBind();
            }
            else
            {
                GetSearchApplicationList();
            }
        }

        protected void LvAppGroupListItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;
            if (IsUserOperator)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }

        }


        public override void PrepareView()
        {
            if (IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Error.ERROR_403);
            }

            Utility.SelectMenu(Master, "Module3");

            BindList();


        }

        private void BindList()
        {
            var applicationlist = GetApplicationList();
            lvAppgroupList.DataSource = applicationlist;
            lvAppgroupList.DataBind();
        }

        private IList<ApplicationGroup> GetApplicationList()
        {
            return Facade.GetApplicationGroupsByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
        }
        private void GetSearchApplicationList()
        {
            var applicationlist = Facade.GetApplicationGroupsByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
            if (applicationlist != null && (txtsearchvalue.Text != "" || string.IsNullOrEmpty(txtsearchvalue.Text)))
            {

                var result = (from applicationlistitems in applicationlist
                              where applicationlistitems.Name.ToLower().StartsWith(txtsearchvalue.Text.ToLower())
                              select applicationlistitems).ToList();
                lvAppgroupList.DataSource = result;
                lvAppgroupList.DataBind();
            }

            else
            {
                if (applicationlist != null)
                {
                    lvAppgroupList.DataSource = applicationlist;
                    lvAppgroupList.DataBind();
                }

            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            GetSearchApplicationList();
        }
    }
}
