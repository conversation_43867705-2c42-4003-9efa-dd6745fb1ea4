﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;


namespace CP.DataAccess
{
    public interface ISQLNative2008MonitorDataAccess
    {
        IList<SQLNative2008Monitor> GetAll();

        SQLNative2008Monitor GetByInfraObjectId(int infraObjectId);

        IList<SQLNative2008Monitor> GetByDate(int infraObjectId, string startdate, string enddate);

        IList<SQLNative2008Monitor> GetHourlyByInfraObjectId(int infraObjectId);

        IList<SQLNative2008Monitor> GetHourlyByMinDataLag(int infraObjectId);

        SQLNative2008Monitor GetByCurrentInfraObjectId(int infraId);
       
    }
}
