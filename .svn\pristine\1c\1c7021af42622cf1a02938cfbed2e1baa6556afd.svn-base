﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Alert", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class CustomException : BaseEntity
    {
        #region Properties

        [DataMember]
        public string AlertType { get; set; }

        [DataMember]
        public string Message { get; set; }

        [DataMember]
        public string Code { get; set; }

        [DataMember]
        public string Severity { get; set; }

        [DataMember]
        public bool SendMail { get; set; }

        #endregion Properties
    }
}