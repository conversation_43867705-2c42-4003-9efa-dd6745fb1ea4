﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess.MSSQLDBMirrorsReplication
{
    internal sealed class MsSqlDBMirrorReplicationBuilder : IEntityBuilder<MSSQLDBMirrorReplication>
    {
        #region IEntityBuilder<MSSQLDBMirrorReplication> Members

        public IList<MSSQLDBMirrorReplication> BuildEntities(IDataReader reader)
        {
            var MSSQLDBMirrorReplicatin = new List<MSSQLDBMirrorReplication>();

            while (reader.Read())
            {

                MSSQLDBMirrorReplicatin.Add(((IEntityBuilder<MSSQLDBMirrorReplication>)this).BuildEntity(reader,
                  new MSSQLDBMirrorReplication()));

            }

            return (MSSQLDBMirrorReplicatin.Count > 0) ? MSSQLDBMirrorReplicatin : null;
        }

        MSSQLDBMirrorReplication IEntityBuilder<MSSQLDBMirrorReplication>.BuildEntity(IDataReader reader,
           MSSQLDBMirrorReplication MSSQLDBMirrorReplication)
        {

            MSSQLDBMirrorReplication.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            MSSQLDBMirrorReplication.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
            MSSQLDBMirrorReplication.ReplicationBase.reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]); 
            MSSQLDBMirrorReplication.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]);
            MSSQLDBMirrorReplication.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0 : Convert.ToInt32(reader["ReplicationId"]);
            MSSQLDBMirrorReplication.PRServerNwkAddress = Convert.IsDBNull(reader["PRServerNwkAddress"]) ? string.Empty : Convert.ToString(reader["PRServerNwkAddress"]);
            MSSQLDBMirrorReplication.DRServerNwkAddress = Convert.IsDBNull(reader["DRServerNwkAddress"])? string.Empty: Convert.ToString(reader["DRServerNwkAddress"]);
            return MSSQLDBMirrorReplication;
        }

        #endregion IEntityBuilder<MSSQLDBMirrorReplication> Members
    }
}
