﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class InfraScheWFDataAccess : BaseDataAccess, IInfrascheduleWfDataAccess
    {
        public InfraScheWFDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<InfrascheduleWfdeatils> CreateEntityBuilder<InfrascheduleWfdeatils>()
        {
            return (new InfraScheWFBuilder()) as IEntityBuilder<InfrascheduleWfdeatils>;
        }



        IList<InfrascheduleWfdeatils> IInfrascheduleWfDataAccess.GetscheduleById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "GetInfraschuWfdeatilsBySchId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iScheduleId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfrascheduleWfdeatils>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfrascheduleWfDataAccess.GetscheduleById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<InfrascheduleWfdeatils> IInfrascheduleWfDataAccess.GetByInfraobjectId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "GetWfdeatilsByInfraobjectId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<InfrascheduleWfdeatils>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfrascheduleWfDataAccess.GetByInfraobjectId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


//        IList<InfrascheduleWfdeatils> IInfrascheduleWfDataAccess.GetscheduleById(int id)
//        {
//            try
//            {
//                if (id < 1)
//                {
//                    throw new ArgumentNullException("id");
//                }

//                const string sp = "GetInfraschuWfdeatilsBySchId ";

//                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
//                {
//                    Database.AddInParameter(cmd, Dbstring + "iScheduleId", DbType.Int32, id);
//#if ORACLE
//                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
//#endif
//                    using (IDataReader reader = Database.ExecuteReader(cmd))
//                    {
//                        if (reader.Read())
//                        {
//                            return CreateEntityBuilder<InfrascheduleWfdeatils>().BuildEntities(reader);
//                        }                    

//                        return null;
//                    }
//                }
//            }
//            catch (Exception ex)
//            {
//                throw new CpException(CpExceptionType.DataAccessFetchOperation,
//                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
//                    "Error In DAL While Executing Function Signature IInfrascheduleWfDataAccess.GetscheduleById(" + id + ")" +
//                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
//            }
//        }

        InfrascheduleWfdeatils IInfrascheduleWfDataAccess.GetScheduledActionResultByInfra(int infraId)
        {
            try
            {
                if (infraId < 1)
                {
                    throw new ArgumentNullException("infraId");
                }

                const string sp = "GetWfdeatilsByInfraobjectId";
                InfrascheduleWfdeatils SchedularLogs = null;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, infraId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        while (reader.Read())
                        {

                            SchedularLogs = new InfrascheduleWfdeatils();
                            SchedularLogs.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

                            SchedularLogs.InfraObjectName = Convert.IsDBNull(reader["InfraObjectName"])
                                ? string.Empty
                                : Convert.ToString(reader["InfraObjectName"]);
                            SchedularLogs.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
                            SchedularLogs.WorkflowName = Convert.IsDBNull(reader["WorkflowName"])
                              ? string.Empty
                              : Convert.ToString(reader["WorkflowName"]);
                            SchedularLogs.WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]);
                            SchedularLogs.CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"])
                            ? string.Empty
                            : Convert.ToString(reader["CurrentActionName"]);
                            SchedularLogs.CurrentActionId = Convert.IsDBNull(reader["CurrentActionId"]) ? 0 : Convert.ToInt32(reader["CurrentActionId"]);
                            SchedularLogs.ScheduleId = Convert.IsDBNull(reader["ScheduleId"]) ? 0 : Convert.ToInt32(reader["ScheduleId"]);

                            SchedularLogs.ScheduleType = Convert.IsDBNull(reader["ScheduleType"])
                          ? string.Empty
                          : Convert.ToString(reader["ScheduleType"]);

                            SchedularLogs.Status = Convert.IsDBNull(reader["Status"])
                       ? string.Empty
                       : Convert.ToString(reader["Status"]);

                            SchedularLogs.Type = Convert.IsDBNull(reader["Type"])
                   ? string.Empty
                   : Convert.ToString(reader["Type"]);


                            SchedularLogs.StartTime = Convert.IsDBNull(reader["StartTime"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["StartTime"]);
                            SchedularLogs.EndTime = Convert.IsDBNull(reader["EndTime"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["EndTime"]);
                            if (SchedularLogs.StartTime != null && SchedularLogs.EndTime != null)
                            {
                                TimeSpan tm = ((SchedularLogs.EndTime) - (SchedularLogs.StartTime));
                                //   string ss = Convert.ToString((SchedularLogs.EndTime) - (SchedularLogs.StartTime));
                                SchedularLogs.RTO = tm.ToString();
                                //  SchedularLogs.RTO = ss;
                            }
                            else
                            {
                                SchedularLogs.RTO = null;
                            }

                            SchedularLogs.Message = Convert.IsDBNull(reader["Message"])
                                ? string.Empty
                                : Convert.ToString(reader["Message"]);



                        }
                        return SchedularLogs;
                    }
                }

            }
            catch (Exception exc)
            {

                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                   "Error In DAL While Executing Function Signature IInfrascheduleWfDataAccess.GetScheduledActionResultByInfra(" + infraId + ")" +
                   Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);

            }
            return null;
        }




        IList<InfrascheduleWfdeatils> IInfrascheduleWfDataAccess.GetScheduleWFDetailsByInfraID(int infraId)
        {
            try
            {
                IList<InfrascheduleWfdeatils> infrschedulelst = new List<InfrascheduleWfdeatils>();

                if (infraId < 1)
                {
                    throw new ArgumentNullException("infraId");
                }

                const string sp = "GetSchWfdeatilsByInfraobjectId_new";
               // InfrascheduleWfdeatils SchedularLogs = null;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, infraId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        while (reader.Read())
                        {
                           
                            InfrascheduleWfdeatils SchedularLogs = new InfrascheduleWfdeatils();
                            SchedularLogs.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["ID"]);

                            SchedularLogs.InfraObjectName = Convert.IsDBNull(reader["InfraObjectName"])
                                ? string.Empty
                                : Convert.ToString(reader["InfraObjectName"]);
                            SchedularLogs.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
                            SchedularLogs.WorkflowName = Convert.IsDBNull(reader["WorkflowName"])
                              ? string.Empty
                              : Convert.ToString(reader["WorkflowName"]);
                            SchedularLogs.WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]);
                            SchedularLogs.CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"])
                            ? string.Empty
                            : Convert.ToString(reader["CurrentActionName"]);
                            SchedularLogs.CurrentActionId = Convert.IsDBNull(reader["CurrentActionId"]) ? 0 : Convert.ToInt32(reader["CurrentActionId"]);
                            SchedularLogs.ScheduleId = Convert.IsDBNull(reader["ScheduleId"]) ? 0 : Convert.ToInt32(reader["ScheduleId"]);

                            SchedularLogs.ScheduleType = Convert.IsDBNull(reader["ScheduleType"])
                          ? string.Empty
                          : Convert.ToString(reader["ScheduleType"]);

                            SchedularLogs.Status = Convert.IsDBNull(reader["Status"])
                       ? string.Empty
                       : Convert.ToString(reader["Status"]);

                            SchedularLogs.Type = Convert.IsDBNull(reader["Type"])
                   ? string.Empty
                   : Convert.ToString(reader["Type"]);


                            SchedularLogs.StartTime = Convert.IsDBNull(reader["StartTime"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["StartTime"]);
                            SchedularLogs.EndTime = Convert.IsDBNull(reader["EndTime"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["EndTime"]);


                            SchedularLogs.Message = Convert.IsDBNull(reader["Message"])
                                ? string.Empty
                                : Convert.ToString(reader["Message"]);


                            infrschedulelst.Add(SchedularLogs);
                        }
                        return infrschedulelst;
                    }
                }

            }
            catch (Exception exc)
            {

                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                   "Error In DAL While Executing Function Signature IInfrascheduleWfDataAccess.GetScheduleWFDetailsByInfraID(" + infraId + ")" +
                   Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);

            }
            
        }





        IList<InfrascheduleWfdeatils> IInfrascheduleWfDataAccess.GetWFSchExecutionDetailsByInfraScheduleIdWfid(int scheduleId, int infraId, int WfId)
        {
            try
            {
                IList<InfrascheduleWfdeatils> infrschedulelst = new List<InfrascheduleWfdeatils>();

                if (infraId < 1)
                {
                    throw new ArgumentNullException("infraId");
                }

                if (scheduleId < 1)
                {
                    throw new ArgumentNullException("scheduleId");
                }
                if (WfId < 1)
                {
                    throw new ArgumentNullException("WfId");
                }

                const string sp = "GetSchWfdeatilsByInfSchWfId";
                // InfrascheduleWfdeatils SchedularLogs = null;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "ischeduleid", DbType.Int32, scheduleId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectID", DbType.Int32, infraId);
                    Database.AddInParameter(cmd, Dbstring + "iwfid", DbType.Int32, WfId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        while (reader.Read())
                        {

                            InfrascheduleWfdeatils SchedularLogs = new InfrascheduleWfdeatils();
                            SchedularLogs.Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["ID"]);

                            SchedularLogs.InfraObjectName = Convert.IsDBNull(reader["InfraObjectName"])
                                ? string.Empty
                                : Convert.ToString(reader["InfraObjectName"]);
                            SchedularLogs.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
                            SchedularLogs.WorkflowName = Convert.IsDBNull(reader["WorkflowName"])
                              ? string.Empty
                              : Convert.ToString(reader["WorkflowName"]);
                            SchedularLogs.WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]);
                            SchedularLogs.CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"])
                            ? string.Empty
                            : Convert.ToString(reader["CurrentActionName"]);
                            SchedularLogs.CurrentActionId = Convert.IsDBNull(reader["CurrentActionId"]) ? 0 : Convert.ToInt32(reader["CurrentActionId"]);
                            SchedularLogs.ScheduleId = Convert.IsDBNull(reader["ScheduleId"]) ? 0 : Convert.ToInt32(reader["ScheduleId"]);

                            SchedularLogs.ScheduleType = Convert.IsDBNull(reader["ScheduleType"])
                          ? string.Empty
                          : Convert.ToString(reader["ScheduleType"]);

                            SchedularLogs.Status = Convert.IsDBNull(reader["Status"])
                       ? string.Empty
                       : Convert.ToString(reader["Status"]);

                            SchedularLogs.Type = Convert.IsDBNull(reader["Type"])
                   ? string.Empty
                   : Convert.ToString(reader["Type"]);


                            SchedularLogs.StartTime = Convert.IsDBNull(reader["StartTime"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["StartTime"]);
                            SchedularLogs.EndTime = Convert.IsDBNull(reader["EndTime"])
                                ? DateTime.MinValue
                                : Convert.ToDateTime(reader["EndTime"]);

                            SchedularLogs.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                              ? DateTime.MinValue
                              : Convert.ToDateTime(reader["CreateDate"]);

                            SchedularLogs.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                             ? DateTime.MinValue
                             : Convert.ToDateTime(reader["UpdateDate"]);

                            infrschedulelst.Add(SchedularLogs);
                        }
                        return infrschedulelst;
                    }
                }

            }
            catch (Exception exc)
            {

                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                   "Error In DAL While Executing Function Signature IInfrascheduleWfDataAccess.GetWFSchExecutionDetailsByInfraScheduleIdWfid(" + infraId + ")" +
                   Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);

            }

        }
    
    }
}
