﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Group", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DomainDetails : BaseEntity
    {
        #region Properties

        [DataMember]
        public int CompanyId { get; set; }

        [DataMember]
        public string DomainName { get; set; }

        #endregion Properties
    }
}