﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class AlertManagerBuilder : IEntityBuilder<AlertsManager>
    {
        IList<AlertsManager> IEntityBuilder<AlertsManager>.BuildEntities(IDataReader reader)
        {
            var alerts = new List<AlertsManager>();

            while (reader.Read())
            {
                alerts.Add(((IEntityBuilder<AlertsManager>)this).BuildEntity(reader, new AlertsManager()));
            }

            return (alerts.Count > 0) ? alerts : null;
        }

        AlertsManager IEntityBuilder<AlertsManager>.BuildEntity(IDataReader reader, AlertsManager alertmanager)
        {
            //const int FLD_ID = 0;
            //const int FLD_EmailAddress = 1;
            //const int FLD_Name = 2;
            //const int FLD_GroupId = 3;
            //const int FLD_IsActive = 4;
            //const int FLD_CREATEDATE = 5;

            //alert.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //alert.EmailAddress = reader.IsDBNull(FLD_EmailAddress) ? string.Empty : reader.GetString(FLD_EmailAddress);
            //alert.Name = reader.IsDBNull(FLD_Name) ? string.Empty : reader.GetString(FLD_Name);
            //alert.GroupId = reader.IsDBNull(FLD_GroupId) ? string.Empty : reader.GetString(FLD_GroupId);
            //alert.IsActive = Convert.ToBoolean(!reader.IsDBNull(FLD_IsActive) && reader.GetBoolean(FLD_IsActive));
            //alert.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);

            //Fields in bcms_alert_receiver table on 16/07/2013 : : Id, EmailAddress, Name, GroupId, IsActive, CreateDate

            alertmanager.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            alertmanager.InfraObjectName = Convert.IsDBNull(reader["InfraObjectName"])
                ? string.Empty
                : Convert.ToString(reader["InfraObjectName"]);
            alertmanager.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            alertmanager.AlertName = Convert.IsDBNull(reader["AlertName"])
                ? string.Empty
                : Convert.ToString(reader["AlertName"]);
            alertmanager.AlertId = Convert.IsDBNull(reader["AlertId"]) ? 0 : Convert.ToInt32(reader["AlertId"]);
            alertmanager.Status = Convert.IsDBNull(reader["Priority"])
                ? string.Empty
                : Convert.ToString(reader["Priority"]);
            alertmanager.AlertDesc = Convert.IsDBNull(reader["Description"])
                ? string.Empty
                : Convert.ToString(reader["Description"]);
            alertmanager.AlertType = Convert.IsDBNull(reader["AlertType"])
                ? string.Empty
                : Convert.ToString(reader["AlertType"]);
            alertmanager.SNMPStatus = Convert.IsDBNull(reader["SNMPStatus"])
                ? string.Empty
                : Convert.ToString(reader["SNMPStatus"]);
            alertmanager.Incident = Convert.ToBoolean(reader["Incident"]);
            alertmanager.CurrentUserId = Convert.IsDBNull(reader["UserId"])
                ? string.Empty
                : Convert.ToString(reader["UserId"]);

            alertmanager.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            alertmanager.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());

            return alertmanager;
        }
    }
}