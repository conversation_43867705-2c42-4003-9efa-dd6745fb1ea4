﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class IncidentDataAccess : BaseDataAccess, IIncidentDataAccess
    {
        #region Constructors

        public IncidentDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<Incident> CreateEntityBuilder<Incident>()
        {
            return (new IncidentBuilder()) as IEntityBuilder<Incident>;
        }

        #endregion Constructors

        Incident IIncidentDataAccess.Add(Incident incidentmanager)
        {
            try
            {
                const string sp = "IncidentManager_Create";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iAlertId", DbType.AnsiString, incidentmanager.AlertId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkFlow_Id", DbType.AnsiString, incidentmanager.WorkFlowId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, incidentmanager.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iIncidentNumber", DbType.AnsiString, incidentmanager.IncidentNumber);
                    Database.AddInParameter(cmd, Dbstring + "iIncidentName", DbType.AnsiString, incidentmanager.IncidentName);
                    Database.AddInParameter(cmd, Dbstring + "iDescription", DbType.AnsiString, incidentmanager.Description);
                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, incidentmanager.IsActive);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        incidentmanager = reader.Read()
                            ? CreateEntityBuilder<Incident>().BuildEntity(reader, incidentmanager)
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting AlertManager Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
            return incidentmanager;
        }

        Incident IIncidentDataAccess.Update(Incident incidentmanager)
        {
            try
            {
                const string sp = "IncidentManager_Update";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    // Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, incidentmanager.Id);
                    Database.AddInParameter(cmd, Dbstring + "iAlertId", DbType.AnsiString, incidentmanager.AlertId);
                    Database.AddInParameter(cmd, Dbstring + "WorkFlow_Id", DbType.AnsiString, incidentmanager.WorkFlowId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.AnsiString, incidentmanager.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iIncidentNumber", DbType.AnsiString, incidentmanager.IncidentNumber);
                    Database.AddInParameter(cmd, Dbstring + "IncidentName", DbType.AnsiString, incidentmanager.IncidentName);
                    Database.AddInParameter(cmd, Dbstring + "Description", DbType.AnsiString, incidentmanager.Description);
                    Database.AddInParameter(cmd, Dbstring + "iIsActive", DbType.Int32, incidentmanager.IsActive);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        incidentmanager = reader.Read()
                            ? CreateEntityBuilder<Incident>().BuildEntity(reader, incidentmanager)
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While inserting AlertManager Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
            return incidentmanager;
        }

        //IList<Incident> IIncidentDataAccess.Update(int id)
        //{
        //    try
        //    {
        //        if (id < 1)
        //        {
        //            throw new ArgumentNullException("id");
        //        }
        //        const string SP = "IncidentManager_Update";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(SP))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                return CreateEntityBuilder<Incident>().BuildEntities(reader);
        //            }

        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        throw new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.USER_ALERT_MESSAGE_FETCHDATA, "Error In DAL While Executing Function Signature IAlertDataAccess.GetAllByGroupId(" + id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
        //    }

        //}

        Incident IIncidentDataAccess.GetByGroupId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "Incident_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);


#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<Incident>()).BuildEntity(reader, new Incident())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByGroupId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        Incident IIncidentDataAccess.GetByAlertId(int id, int infraid)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "Incident_GetByAlertId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, infraid);


#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<Incident>()).BuildEntity(reader, new Incident())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByGroupId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        Incident IIncidentDataAccess.GetAllByGroupId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "Incident_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        // return CreateEntityBuilder<Incident>().BuildEntities(reader);

                        return reader.Read()
                            ? (CreateEntityBuilder<Incident>()).BuildEntity(reader, new Incident())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAllByGroupId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Incident> IIncidentDataAccess.GetByUserId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "Incident_GetByUserId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Incident>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAllByGroupId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Incident> IIncidentDataAccess.GetByUserName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }
                const string sp = "Alert_GetByUserName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Incident>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByUserName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Incident> IIncidentDataAccess.GetByLastAlertId(int id)
        {
            try
            {
                const string sp = "Alert_GetByLastAlertId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Incident>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByLastAlertId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        Incident IIncidentDataAccess.GetByInfraId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "Incident_GetAllByInfraObjectId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        // return CreateEntityBuilder<Incident>().BuildEntities(reader);

                        return reader.Read()
                            ? (CreateEntityBuilder<Incident>()).BuildEntity(reader, new Incident())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetAllByGroupId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

    }
}