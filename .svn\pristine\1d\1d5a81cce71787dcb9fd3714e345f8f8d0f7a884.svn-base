﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IApplicationGroupDataAccess
    {
        ApplicationGroup Add(ApplicationGroup applicationGroup);

        ApplicationGroup Update(ApplicationGroup applicationGroup);

        ApplicationGroup GetByGroupId(int groupId);

        //ApplicationGroup GetById(int id);

        IList<ApplicationGroup> GetAll();

        IList<ApplicationGroup> GetByBusinessServiceId(int businessServiceId);

        IList<ApplicationGroup> GetByCompanyIdAndRole(int companyId, bool isSuperAdmin);

        IList<ApplicationGroup> GetByServerId(int serverId);

        IList<ApplicationGroup> GetByReplicationId(int replicationId);

        bool DeleteById(int id);

        bool UpdateByStatus(int id, string status);

        bool UpdateAllByStatus(int id, string status);

        bool UnlockByTime(int applicationgroupId, int minutes, string eventName);

        bool IsExistByName(string name);

        bool UpdateWorkflowOperation(int applicationgroupId, int workflow, int droperationId);

        IList<ApplicationGroup> GetAllByIsReplication();

        IList<ApplicationGroup> GetByLoginId(int id);
    }
}