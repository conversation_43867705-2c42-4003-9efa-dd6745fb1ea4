﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class RoboCopyLogsBuilder : IEntityBuilder<RoboCopyLogs>
    {
        IList<RoboCopyLogs> IEntityBuilder<RoboCopyLogs>.BuildEntities(IDataReader reader)
        {
            var robocopylogs = new List<RoboCopyLogs>();

            while (reader.Read())
            {
                robocopylogs.Add(((IEntityBuilder<RoboCopyLogs>)this).BuildEntity(reader, new RoboCopyLogs()));
            }

            return (robocopylogs.Count > 0) ? robocopylogs : null;
        }

        RoboCopyLogs IEntityBuilder<RoboCopyLogs>.BuildEntity(IDataReader reader, RoboCopyLogs robocopy)
        {

            robocopy.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            robocopy.RoboCopyJobId = Convert.IsDBNull(reader["RoboCopyJobId"]) ? 0 : Convert.ToInt32(reader["RoboCopyJobId"]);
            robocopy.SourceIP = Convert.IsDBNull(reader["SourceIP"])
                ? string.Empty
                : Convert.ToString(reader["SourceIP"]);
            robocopy.DestinationIP = Convert.IsDBNull(reader["DestinationIP"])
                ? string.Empty
                : Convert.ToString(reader["DestinationIP"]);

            robocopy.SourcePath = Convert.IsDBNull(reader["SourcePath"])
                ? string.Empty
                : Convert.ToString(reader["SourcePath"]);
            robocopy.DestinationPath = Convert.IsDBNull(reader["DestinationPath"])
                ? string.Empty
                : Convert.ToString(reader["DestinationPath"]);

            robocopy.RepStartTime = Convert.IsDBNull(reader["RepStartTime"])
           ? string.Empty
           : Convert.ToString(reader["RepStartTime"]);
            robocopy.RepEndTime = Convert.IsDBNull(reader["RepEndTime"])
                ? string.Empty
                : Convert.ToString(reader["RepEndTime"]);


            robocopy.TotalDirCount = Convert.IsDBNull(reader["TotalDirCount"]) ? 0 : Convert.ToInt32(reader["TotalDirCount"]);
            robocopy.TotalDirCopiedCount = Convert.IsDBNull(reader["TotalDirCopiedCount"]) ? 0 : Convert.ToInt32(reader["TotalDirCopiedCount"]);
            robocopy.TotalSkippedDirCount = Convert.IsDBNull(reader["TotalSkippedDirCount"]) ? 0 : Convert.ToInt32(reader["TotalSkippedDirCount"]);
            robocopy.TotalMisMatchedDirCount = Convert.IsDBNull(reader["TotalMisMatchedDirCount"]) ? 0 : Convert.ToInt32(reader["TotalMisMatchedDirCount"]);
            robocopy.TotalFailedDirCount = Convert.IsDBNull(reader["TotalFailedDirCount"]) ? 0 : Convert.ToInt32(reader["TotalFailedDirCount"]);
            robocopy.TotalExtrasDirCount = Convert.IsDBNull(reader["TotalExtrasDirCount"]) ? 0 : Convert.ToInt32(reader["TotalExtrasDirCount"]);

            robocopy.TotalFilesCount = Convert.IsDBNull(reader["TotalFilesCount"]) ? 0 : Convert.ToInt32(reader["TotalFilesCount"]);
            robocopy.TotalFilesCopiedCount = Convert.IsDBNull(reader["TotalFilesCopiedCount"]) ? 0 : Convert.ToInt32(reader["TotalFilesCopiedCount"]);
            robocopy.TotalSkippedFilesCount = Convert.IsDBNull(reader["TotalSkippedFilesCount"]) ? 0 : Convert.ToInt32(reader["TotalSkippedFilesCount"]);
            robocopy.TotalMisMatchedFilesCount = Convert.IsDBNull(reader["TotalMisMatchedFilesCount"]) ? 0 : Convert.ToInt32(reader["TotalMisMatchedFilesCount"]);
            robocopy.TotalFailedFilesCount = Convert.IsDBNull(reader["TotalFailedFilesCount"]) ? 0 : Convert.ToInt32(reader["TotalFailedFilesCount"]);
            robocopy.TotalExtrasFilesCount = Convert.IsDBNull(reader["TotalExtrasFilesCount"]) ? 0 : Convert.ToInt32(reader["TotalExtrasFilesCount"]);

            robocopy.TotalBytesCount = Convert.IsDBNull(reader["TotalBytesCount"]) ? string.Empty : Convert.ToString(reader["TotalBytesCount"]);
            robocopy.TotalBytesCopiedCount = Convert.IsDBNull(reader["TotalBytesCopiedCount"]) ? string.Empty : Convert.ToString(reader["TotalBytesCopiedCount"]);
            robocopy.TotalSkippedBytesCount = Convert.IsDBNull(reader["TotalSkippedBytesCount"]) ? string.Empty : Convert.ToString(reader["TotalSkippedBytesCount"]);
            robocopy.TotalMisMatchedBytesCount = Convert.IsDBNull(reader["TotalMisMatchedBytesCount"]) ? string.Empty : Convert.ToString(reader["TotalMisMatchedBytesCount"]);
            robocopy.TotalFailedBytesCount = Convert.IsDBNull(reader["TotalFailedBytesCount"]) ? string.Empty : Convert.ToString(reader["TotalFailedBytesCount"]);
            robocopy.TotalExtrasBytesCount = Convert.IsDBNull(reader["TotalExtrasBytesCount"]) ? string.Empty : Convert.ToString(reader["TotalExtrasBytesCount"]);

            robocopy.TotalTimesCount = Convert.IsDBNull(reader["TotalTimesCount"]) ? string.Empty : Convert.ToString(reader["TotalTimesCount"]);
            robocopy.TotalTimesCopiedCount = Convert.IsDBNull(reader["TotalTimesCopiedCount"]) ? string.Empty : Convert.ToString(reader["TotalTimesCopiedCount"]);
            robocopy.TotalSkippedTimesCount = Convert.IsDBNull(reader["TotalSkippedTimesCount"]) ? string.Empty : Convert.ToString(reader["TotalSkippedTimesCount"]);
            robocopy.TotalMisMatchedTimesCount = Convert.IsDBNull(reader["TotalMisMatchedTimesCount"]) ? string.Empty : Convert.ToString(reader["TotalMisMatchedTimesCount"]);
            robocopy.TotalFailedTimesCount = Convert.IsDBNull(reader["TotalFailedTimesCount"]) ? string.Empty : Convert.ToString(reader["TotalFailedTimesCount"]);
            robocopy.TotalExtrasTimesCount = Convert.IsDBNull(reader["TotalExtrasTimesCount"]) ? string.Empty : Convert.ToString(reader["TotalExtrasTimesCount"]);

            robocopy.SpeedBytesPerSeconds = Convert.IsDBNull(reader["SpeedBytesPerSeconds"]) ? string.Empty : Convert.ToString(reader["SpeedBytesPerSeconds"]);
            robocopy.SpeedMBPerMinute = Convert.IsDBNull(reader["SpeedMBPerMinute"]) ? string.Empty : Convert.ToString(reader["SpeedMBPerMinute"]);

            robocopy.InfraObjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);

            robocopy.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            robocopy.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            robocopy.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            robocopy.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            robocopy.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());

            robocopy.SelectedOptions = Convert.IsDBNull(reader["SelectedOptions"]) ? string.Empty : Convert.ToString(reader["SelectedOptions"]);

            robocopy.Datalag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty : Convert.ToString(reader["DataLag"]);
            return robocopy;
        }
    }
}
