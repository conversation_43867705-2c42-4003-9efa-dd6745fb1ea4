﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
//using CP.DataAccess.SQLNative2008Monitors;

namespace CP.DataAccess
{
    internal sealed class SQLNative2008MonitorDataAccess : BaseDataAccess, ISQLNative2008MonitorDataAccess
    {
         #region Constructors

        public SQLNative2008MonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SQLNative2008Monitor> CreateEntityBuilder<SQLNative2008Monitor>()
        {
            return (new SQLNative2008MonitorBuilder()) as IEntityBuilder<SQLNative2008Monitor>;
        }

        #endregion Constructors

        #region Methods   
        IList<SQLNative2008Monitor> ISQLNative2008MonitorDataAccess.GetAll()
        {
            try
            {
                const string sp = "SqlNativeMonitor_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SQLNative2008Monitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISqlNativeMonitorDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        SQLNative2008Monitor ISQLNative2008MonitorDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "SQLNATIVEMONITOR_GETBYINFRATID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<SQLNative2008Monitor>()).BuildEntity(reader, new SQLNative2008Monitor())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISqlNativeMonitorDataAccess.GetByInfraObjectId(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<SQLNative2008Monitor> ISQLNative2008MonitorDataAccess.GetByDate(int infraObjectId, string startdate, string enddate)
        {
            try
            {
                const string sp = "SqlNativeMonitor_GetByDate";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    startdate = Convert.ToDateTime(startdate).ToString("dd-MMM-yy");
                    enddate = Convert.ToDateTime(enddate).ToString("dd-MMM-yy");
#endif
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iStartDate", DbType.String, startdate);
                    Database.AddInParameter(cmd, Dbstring+"iEndDate", DbType.String, enddate);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SQLNative2008Monitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISqlNativeMonitorDataAccess.GetByDate(" + startdate +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        //Ram Mahajan-GetHourlyByGroupId method has been changed by GetHourlyByInfraObjectId in CPV4.0
        IList<SQLNative2008Monitor> ISQLNative2008MonitorDataAccess.GetHourlyByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "SQLNATIVEMONITOR_HOURBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<SQLNative2008Monitor>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISqlNativeMonitorDataAccess.GetHourlyByInfraObjectId(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<SQLNative2008Monitor> ISQLNative2008MonitorDataAccess.GetHourlyByMinDataLag(int infraObjectId)
        {
            try
            {
                const string sp = "SQLNATIVEMONITOR_HOURBYDATALAG";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    var sqlnative2008Monitor = new List<SQLNative2008Monitor>();
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var oracleLog = new SQLNative2008Monitor
                            {
                                DataLag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty : Convert.ToString(reader["DataLag"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };

                            sqlnative2008Monitor.Add(oracleLog);
                        }

                        return sqlnative2008Monitor;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISqlNativeMonitorDataAccess.GetHourlyByMinDataLag(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        SQLNative2008Monitor ISQLNative2008MonitorDataAccess.GetByCurrentInfraObjectId(int infraId)
        {
            try
            {
                const string sp = "SqlNtivMtorGetCrrntBy_InfId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, infraId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<SQLNative2008Monitor>()).BuildEntity(reader, new SQLNative2008Monitor())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISqlNativeMonitorDataAccess.GetByInfraObjectId(" +
                    infraId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion Methods
    }
}
