﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "approvaltimelineviwe", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ApprovalTimelineViwe : BaseEntity
    {
         public int Id { get; set; }
        [DataMember]
         public string ApprovalProcessNumber { get; set; }
        [DataMember]
        public string Activity { get; set; }
         [DataMember]
        public DateTime Createdate { get; set; }
     
    }
}
