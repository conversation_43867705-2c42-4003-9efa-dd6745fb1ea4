﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ConfigureDefaultMonitoringServices.aspx.cs" Inherits="CP.UI.Component.ConfigureDefaultMonitoringServices" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/jquery.tagit.css" rel="stylesheet" type="text/css" />
    <link href="../App_Themes/CPTheme/tagit.ui-zendesk.css" rel="stylesheet" type="text/css" />
    <%-- <link href="../App_Themes/CPTheme/select2.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/module.admin.page.index.min.css" rel="stylesheet" />
    <script src="../Script/select2.js"></script>
    <script src="../Script/select2.init.js"></script>--%>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
     <asp:HiddenField ID="hdtokenKey" runat="server" />
    <asp:UpdatePanel ID="updpnlMonProfileConfig" runat="server" UpdateMode="Conditional">
        <ContentTemplate>
            <div class="innerLR">
                <h3>
                    <img src="../Images/monitor.png" style="margin-right: 5px; vertical-align: text-top;">Default Monitoring Services Configuration</h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <asp:Label ID="lblsuccess" runat="server" ForeColor="Green" Visible="false"></asp:Label>
                        <%--<asp:Label ID="lblError" runat="server" ForeColor="Red Visible="false"></asp:Label>--%>
                        <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">

                                    <label class="col-md-3">
                                        OS Type <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlOSType" runat="server" CssClass="selectpicker col-md-6"
                                            data-style="btn-default">
                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" CssClass="error" ControlToValidate="ddlOSType"
                                            ErrorMessage="Please Select OS Type" ValidationGroup="Validate" Display="Dynamic" InitialValue="0"></asp:RequiredFieldValidator>
                                    </div>

                                </div>
                                <div class="form-group">

                                    <label class="col-md-3">
                                        Monitoring Services </label>
                                    <div class="col-md-5" id="divMonServices" style="width: 36%; padding-right: 0px;">
                                        <asp:TextBox ID="txtMonServers" runat="server" CssClass="form-control" name="tags"></asp:TextBox>
                                    </div>

                                </div>

                                <hr class="separator" />
                                <div class="form-actions row">
                                    <div class="col-lg-5">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
                                    </div>
                                    <div class="col-lg-7">
                                        <asp:Label ID="lblSuccessFull" runat="server" ForeColor="Green"></asp:Label>
                                        <asp:Button ID="btnSave" runat="server" Text="Save" CssClass="btn btn-primary" ValidationGroup="Validate" OnClick="btnSave_Click" Width="15%" Style="margin-left: 9px;" />
                                        <asp:Button ID="btnCancle" runat="server" Text="Cancel" CssClass="btn btn-default" OnClick="btnCancle_Click" Width="15%" />

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <h3 style="line-height: 25px; font-size: 18px;">Default Monitoring Services List</h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <asp:ListView ID="lstProfiles" runat="server" OnItemDataBound="lstProfiles_ItemDataBound" OnItemEditing="lstProfiles_ItemEditing" OnItemDeleting="lstProfiles_ItemDeleting">
                            <LayoutTemplate>
                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white margin-bottom-none"
                                    style="width: 100%; table-layout: fixed">
                                    <thead>
                                        <tr>
                                            <th style="width: 4%">Sr.No
                                            </th>
                                            <th style="width: 20%">OS Type
                                            </th>
                                            <th style="width: 68%">Monitoring Services
                                            </th>

                                            <th style="width: 8%" class="text-center">Action
                                            </th>
                                        </tr>
                                    </thead>
                                </table>
                                <div class="notifyscroll" style="max-height: 325px;">
                                    <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white margin-bottom-none"
                                        style="width: 100%; table-layout: fixed">
                                        <tbody>
                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                        </tbody>
                                    </table>
                                </div>
                            </LayoutTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td class="th table-check-cell" style="width: 4%">
                                        <%#Container.DataItemIndex+1%>
                                    </td>

                                    <td style="width: 20%">
                                        <asp:Label ID="lblId" Visible="False" runat="server" Text='<%# Eval("Id") %>' />
                                        <asp:Label ID="lblOSIcon" runat="server" CssClass=""></asp:Label>
                                        <%--<asp:Label ID="lblOSType" Visible="true" runat="server" Text='<%#SelectOsTypefromId(Eval("OSType")) %>' />--%>
                                        <asp:Label ID="lblOSType" Visible="true" runat="server" Text='<%# Eval("OSType") %>' />
                                    </td>

                                    <td class="tdbdn monservice" style="width: 68%; padding: 1px 5px 1px 5px  !important">

                                        <asp:TextBox ID="txtMonServersList" runat="server" CssClass="form-control" name="tags"></asp:TextBox>

                                    </td>
                                    <td style="width: 6%;" class="text-center">

                                        <%--<asp:Button ID="btnNEw1" runat="server" CssClass="icon-edit-pen"  CommandName="Edit" OnClick="btnNEw1_Click" />--%>
                                        <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />

                                        <asp:ImageButton ID="ImgDelete" runat="server" CommandName="delete"  Visible="true" Enabled="true" AlternateText="Delete"
                                            ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />

                                        <TK1:ConfirmButtonExtender ID="ConfirmProfileDelete" runat="server" TargetControlID="ImgDelete"
                                            ConfirmText="Are you sure you want to delete selected Services?" OnClientCancel="CancelClick">
                                        </TK1:ConfirmButtonExtender>

                                    </td>
                                </tr>
                            </ItemTemplate>

                            <EmptyDataTemplate>
                                <asp:Label ID="lblNoProfileList" runat="server" CssClass="error" ForeColor="Red" Text="No Services Configured....   "></asp:Label>
                            </EmptyDataTemplate>

                        </asp:ListView>

                    </div>
                </div>

            </div>
        </ContentTemplate>
    </asp:UpdatePanel>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script>
        $(document).ready(function () {
            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
                //setHeight: "190px",
            });

            $('[id$=ctl00_cphBody_txtMonServers]').tagit();

            $('[id$=txtMonServersList]').tagit({
                readOnly: true
            });


        });

        function pageLoad() {

            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
                //setHeight: "190px",
            });

            $('[id$=ctl00_cphBody_txtMonServers]').tagit();


            $('[id$=txtMonServersList]').tagit({
                readOnly: true
            });


        }



        $("[id^=txtMonServers]").live({
            mouseover: function () {
                alert("HI");
                $("#lblSuccessFull").html("");
            }
        });

        $("#divMonServices").mouseover(function () {
            $("#ctl00_cphBody_lblSuccessFull").text("");
        });


        $(document).click(function () {
            $("#ctl00_cphBody_lblSuccessFull").text("");
        });

    </script>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>

</asp:Content>

