﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DataBaseSybaseList.ascx.cs" Inherits="CP.UI.Controls.DataBaseSybaseList" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:UpdateProgress ID="updateprogress1" AssociatedUpdatePanelID="upnlNormalDatabseList"
    runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>
<asp:UpdatePanel ID="upnlNormalDatabseList" runat="server">
    <ContentTemplate>
        <div class="row">
            <div class="col-md-5 col-md-push-7 text-right">
                <asp:TextBox ID="txtsearchvalue" CssClass="form-control" runat="server" placeholder="Database Name"></asp:TextBox>
                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="btnSearch_Click" />
            </div>
        </div>
        <hr />
        <asp:ListView ID="lvSybasedatabase" runat="server" DataKeyNames="ID" OnItemEditing ="lvSybasedatabase_ItemEditing"
            OnPreRender ="lvSybasedatabase_PreRender" OnItemDeleting ="lvSybasedatabase_ItemDeleting" OnItemDataBound ="lvSybasedatabase_ItemDataBound">
            <LayoutTemplate>
                <table id="tbldatabase" class="table font no-bottom-margin dynamicTable tableTools table table-striped table-bordered table-condensed table-white" width="100%" style="table-layout:fixed">
                    <thead>
                        <tr>
                            <th style="width: 4%;" class="text-center">
                                <span>
                                    <img src="../Images/icons/database.png" /></span>
                            </th>
                            <th style="width: 24%;">Name
                            </th>
                            <th style="width: 20%;">Database SID
                            </th>
                            <th style="width: 20%;">Type
                            </th>
                            <th style="width: 12%;">Version
                            </th>
                            <th style="width: 12%;">Port Number
                            </th>
                            <th runat="server" id="ActionHead" class="text-center" style="width:8%">Action
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                    </tbody>
                </table>
            </LayoutTemplate>
            <EmptyDataTemplate>
                <div class="message warning align-center bold">
                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                </div>
            </EmptyDataTemplate>
            <ItemTemplate>
                <tr>
                    <td class="text-center" style="width:4%">
                        <asp:Label ID="ID" runat="server" Text='<%#Eval("ID") %>' Visible="false"></asp:Label>
                        <%#Container.DataItemIndex+1 %>
                    </td>
                    <td style="width: 24%;" class="tdword-wrap">
                        <asp:Label ID="lblName" runat="server" Text='<%#Eval("Name") %>'></asp:Label>
                    </td>
                     <td style="width: 20%;">
                        <span>
                            <%# DataBinder.Eval(Container, "DataItem.DatabaseSyBase.DatabaseSID")%></span>
                    </td>
                    <td style="width: 20%;">
                        <%# DataBinder.Eval(Container, "DataItem.Type")%>
                    </td>
                    <td style="width: 12%;">
                        <%# DataBinder.Eval(Container, "DataItem.Version")%>
                    </td>
                    <td style="width: 12%;">
                        <%# DataBinder.Eval(Container, "DataItem.DatabaseSyBase.Port")%>
                    </td>
                    <td runat="server" id="action" class="text-center" style="width:8%">
                        <asp:ImageButton ID="ibtnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                            ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                        <asp:ImageButton ID="ibtnDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                            ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                    </td>
                    <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ibtnDelete"
                        ConfirmText='<%# "Are you sure want to delete " + Eval("Name") + " ? " %>' OnClientCancel="CancelClick">
                    </TK1:ConfirmButtonExtender>
                </tr>
            </ItemTemplate>
        </asp:ListView>
        <div class="row">
            <div class="col-md-6">
                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvSybasedatabase" PageSize="4">
                    <Fields>
                        <asp:TemplatePagerField>
                            <PagerTemplate>
                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                Results
                            <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                            Out Of
                            <%# Container.TotalRowCount %>
                                <br />
                            </PagerTemplate>
                        </asp:TemplatePagerField>
                    </Fields>
                </asp:DataPager>
            </div>

            <div class="col-md-6 text-right">
                <asp:DataPager ID="DataPager1" runat="server" PagedControlID="lvSybasedatabase" PageSize="10">
                    <Fields>
                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ShowFirstPageButton="false"
                            ButtonType="Button" ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="Prev" />
                        <asp:NumericPagerField NextPageText=".." PreviousPageText=".." ButtonCount="10"
                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="buttonblue"
                            NumericButtonCssClass="btn-pagination" />
                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ShowFirstPageButton="false"
                            ButtonType="Button" ShowLastPageButton="false" ShowPreviousPageButton="false"
                            ShowNextPageButton="true" />
                    </Fields>
                </asp:DataPager>
            </div>
        </div>
        </div>

        <script type="text/javascript">
            function CancelClick() {
                return false;
            }
        </script>
    </ContentTemplate>
</asp:UpdatePanel>