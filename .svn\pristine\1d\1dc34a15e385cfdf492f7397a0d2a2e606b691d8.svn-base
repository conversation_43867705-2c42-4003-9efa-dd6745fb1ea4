﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
    internal sealed class SiteBuilder : IEntityBuilder<Site>
    {
        IList<Site> IEntityBuilder<Site>.BuildEntities(IDataReader reader)
        {
            var sites = new List<Site>();

            while (reader.Read())
            {
                sites.Add(((IEntityBuilder<Site>)this).BuildEntity(reader, new Site()));
            }

            return (sites.Count > 0) ? sites : null;
        }

        Site IEntityBuilder<Site>.BuildEntity(IDataReader reader, Site site)
        {
            //const int FLD_ID = 0;
            //const int FLD_NAME = 1;
            //const int FLD_LOCATION = 2;
            //const int FLD_TYPE = 3;
            //const int FLD_STATUS = 4;
            //const int FLD_COMPANYID = 5;
            //const int FLD_ISACTIVE = 6;
            //const int FLD_CREATORID = 7;
            //const int FLD_CREATEDATE = 8;
            //const int FLD_UPDATORID = 9;
            //const int FLD_UPDATEDATE = 10;

            //site.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //site.Name = reader.IsDBNull(FLD_NAME) ? string.Empty : reader.GetString(FLD_NAME);
            //site.Location = reader.IsDBNull(FLD_LOCATION) ? string.Empty : reader.GetString(FLD_LOCATION);
            //site.Type = reader.IsDBNull(FLD_TYPE) ? SiteType.Undefined : (SiteType)Enum.Parse(typeof(SiteType),reader.GetString(FLD_TYPE),true);
            //site.Status = reader.IsDBNull(FLD_STATUS) ? SiteStatus.Undefined : (SiteStatus)Enum.Parse(typeof(SiteStatus),reader.GetString(FLD_STATUS),true);
            //site.CompanyId = reader.IsDBNull(FLD_COMPANYID) ? 0 : reader.GetInt32(FLD_COMPANYID);
            //site.IsActive = !reader.IsDBNull(FLD_ISACTIVE) && reader.GetBoolean(FLD_ISACTIVE);
            //site.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //site.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);
            //site.UpdatorId = reader.IsDBNull(FLD_UPDATORID) ? 0 : reader.GetInt32(FLD_UPDATORID);
            //site.UpdateDate = reader.IsDBNull(FLD_UPDATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_UPDATEDATE);

            //Fields in bcms_site table on 23/07/2013 : Id, Name, Location, Type, Status, CompanyId, IsActive, CreatorId, CreateDate, UpdatorId, UpdateDate

            site.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            site.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
            site.Location = Convert.IsDBNull(reader["Location"]) ? string.Empty : Convert.ToString(reader["Location"]);
            //site.Type = Convert.IsDBNull(reader["Type"])
            //    ? SiteType.Undefined
              // : (SiteType)Enum.Parse(typeof(SiteType), Convert.ToString(reader["Type"]), true);

            site.Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);


            site.Status = Convert.IsDBNull(reader["Status"])
                ? SiteStatus.Undefined
                : (SiteStatus)Enum.Parse(typeof(SiteStatus), Convert.ToString(reader["Status"]), true);
            site.CompanyId = Convert.IsDBNull(reader["CompanyId"]) ? 0 : Convert.ToInt32(reader["CompanyId"]);

            site.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            site.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            site.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);
            site.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            site.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);

            return site;
        }
    }
}