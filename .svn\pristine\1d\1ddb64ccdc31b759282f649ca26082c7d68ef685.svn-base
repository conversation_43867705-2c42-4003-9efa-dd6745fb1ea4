﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.SybaseWithRSHADR
{
    internal sealed class SybaseWithRSHADRDataAccess : BaseDataAccess, ISybaseWithRSHADRDataAccess
    {
        #region Constructors

        public SybaseWithRSHADRDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SybaseWithRSHADRReplication> CreateEntityBuilder<SybaseWithRSHADRReplication>()
        {
            return (new SysbaseWithRSHADRBuilder()) as IEntityBuilder<SybaseWithRSHADRReplication>;
        }

        #endregion Constructors

        SybaseWithRSHADRReplication ISybaseWithRSHADRDataAccess.Add(SybaseWithRSHADRReplication _SybaseWithRSHADRReplication)
        {
            try
            {
                const string sp = "SybaseWithRSHADR_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, _SybaseWithRSHADRReplication.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationServerName", DbType.AnsiString, _SybaseWithRSHADRReplication.ReplicationServerName);
                    Database.AddInParameter(cmd, Dbstring + "iRSPort", DbType.AnsiString, _SybaseWithRSHADRReplication.RSPort);
                    Database.AddInParameter(cmd, Dbstring + "iRSUserName", DbType.AnsiString, _SybaseWithRSHADRReplication.RSUserName);
                    Database.AddInParameter(cmd, Dbstring + "iRSPassword", DbType.AnsiString, _SybaseWithRSHADRReplication.RSPassword);
                    Database.AddInParameter(cmd, Dbstring + "iRMAServerPort", DbType.AnsiString, _SybaseWithRSHADRReplication.RMAServerPort);
                    Database.AddInParameter(cmd, Dbstring + "iRMAUserName", DbType.AnsiString, _SybaseWithRSHADRReplication.RMAUserName);
                    Database.AddInParameter(cmd, Dbstring + "iRMAPassword", DbType.AnsiString, _SybaseWithRSHADRReplication.RMAPassword);
                    Database.AddInParameter(cmd, Dbstring + "iLogicalHostName", DbType.AnsiString, _SybaseWithRSHADRReplication.LogicalHostName);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseEnvPath", DbType.AnsiString, _SybaseWithRSHADRReplication.SybaseEnvPath);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        _SybaseWithRSHADRReplication = reader.Read()
                            ? CreateEntityBuilder<SybaseWithRSHADRReplication>().BuildEntity(reader, _SybaseWithRSHADRReplication)
                            : null;
                    }

                    if (_SybaseWithRSHADRReplication == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "SybaseWithRSHADR already exists. Please specify another SybaseWithRSHADRReplication.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this SybaseWithRSHADRReplication.");
                                }
                        }
                    }

                    return _SybaseWithRSHADRReplication;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting SybaseWithRSHADRReplication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        SybaseWithRSHADRReplication ISybaseWithRSHADRDataAccess.Update(SybaseWithRSHADRReplication _SybaseWithRSHADRReplication)
        {
            try
            {
                const string sp = "SybaseRSHADR_UpdateByRepID";


                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    //Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, _SybaseWithSRSReplication.Id);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationId", DbType.Int32, _SybaseWithRSHADRReplication.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iReplicationServerName", DbType.AnsiString, _SybaseWithRSHADRReplication.ReplicationServerName);
                    Database.AddInParameter(cmd, Dbstring + "iRSPort", DbType.AnsiString, _SybaseWithRSHADRReplication.RSPort);
                    Database.AddInParameter(cmd, Dbstring + "iRSUserName", DbType.AnsiString, _SybaseWithRSHADRReplication.RSUserName);
                    Database.AddInParameter(cmd, Dbstring + "iRSPassword", DbType.AnsiString, _SybaseWithRSHADRReplication.RSPassword);
                    Database.AddInParameter(cmd, Dbstring + "iRMAServerPort", DbType.AnsiString, _SybaseWithRSHADRReplication.RMAServerPort);
                    Database.AddInParameter(cmd, Dbstring + "iRMAUserName", DbType.AnsiString, _SybaseWithRSHADRReplication.RMAUserName);
                    Database.AddInParameter(cmd, Dbstring + "iRMAPassword", DbType.AnsiString, _SybaseWithRSHADRReplication.RMAPassword);
                    Database.AddInParameter(cmd, Dbstring + "iLogicalHostName", DbType.AnsiString, _SybaseWithRSHADRReplication.LogicalHostName);
                    Database.AddInParameter(cmd, Dbstring + "iSybaseEnvPath", DbType.AnsiString, _SybaseWithRSHADRReplication.SybaseEnvPath);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        _SybaseWithRSHADRReplication = reader.Read()
                            ? CreateEntityBuilder<SybaseWithRSHADRReplication>().BuildEntity(reader, _SybaseWithRSHADRReplication)
                            : null;
                    }

                    if (_SybaseWithRSHADRReplication == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "SybaseWithRSHADR already exists. Please specify another SybaseWithRSHADRReplication.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while updating this SybaseWithRSHADRReplication.");
                                }
                        }
                    }

                    return _SybaseWithRSHADRReplication;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating SybaseWithRSHADRReplication Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        //SybaseWithSRSReplication ISybaseWithSRSRepliDataAccess.UpdateByReplicationId(SybaseWithSRSReplication _SybaseWithSRSReplication)
        //{
        //    throw new NotImplementedException();
        //}

        SybaseWithRSHADRReplication ISybaseWithRSHADRDataAccess.GetByReplicationId(int id)
        {
            try
            {
                const string sp = "SybaseWithRSHADR_GetByRepli";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SybaseWithRSHADRReplication>()).BuildEntity(reader, new SybaseWithRSHADRReplication());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithRSHADRDataAccess.GetByReplicationId(" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        //bool ISybaseWithSRSRepliDataAccess.IsExistByName(string name)
        //{
        //    try
        //    {
        //        if (name == string.Empty)
        //        {
        //            throw new ArgumentNullException("name");
        //        }

        //        const string sp = "SybaseWithSRS_IsExistByName";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            AddOutputParameter(cmd);

        //            Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

        //            Database.ExecuteNonQuery(cmd);

        //            int returnCode = GetReturnCodeFromParameter(cmd);

        //            switch (returnCode)
        //            {
        //                case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
        //                    {
        //                        return true;
        //                    }
        //                case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
        //                    {
        //                        return false;
        //                    }
        //                default:
        //                    {
        //                        throw new SystemException(
        //                            "An unexpected error has occurred while deleting this companyProfile.");
        //                    }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new CpException(CpExceptionType.DataAccessFetchOperation,
        //            ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
        //            "Error In DAL While Executing Function Signature ISybaseWithSRSRepliDataAccess.IsExistByName (" + name + ")" +
        //            Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //    }
        //}

        IList<SybaseWithRSHADRReplication> ISybaseWithRSHADRDataAccess.GetAll()
        {
            try
            {
                const string sp = "SyabaseWithRsHadr_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        return BuildSybaseWithRSHADREntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithRSHADRDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool ISybaseWithRSHADRDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "SybaseRSHadr_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeSuccessDelete:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeErrorChildExists:
                            {
                                throw new ArgumentException("Cannot delete a SybaseWithRSHADRReplication which has association.");
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this SybaseWithRSHADRReplication.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting SybaseWithRSHADRReplication Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        SybaseWithRSHADRReplication ISybaseWithRSHADRDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "SybaseRSHadr_GetByCompanyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<SybaseWithRSHADRReplication>()).BuildEntity(reader, new SybaseWithRSHADRReplication());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithRSHADRDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        //        IList<SybaseWithSRSReplication> ISybaseWithSRSRepliDataAccess.GetByCompanyId(int companyId, bool isParent)
        //        {
        //            try
        //            {
        //                const string sp = "SybaseWithSRSRepli_GetByCompanyId";

        //                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //                {
        //                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, companyId);
        //                    Database.AddInParameter(cmd, Dbstring + "iisParent", DbType.Int32, isParent);
        //#if ORACLE
        //                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
        //#endif
        //                    using (IDataReader reader = Database.ExecuteReader(cmd))
        //                    {
        //                        return BuildSybaseWithSRSEntities(reader);
        //                    }
        //                }
        //            }
        //            catch (Exception ex)
        //            {
        //                throw new CpException(CpExceptionType.DataAccessFetchOperation,
        //                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
        //                    "Error In DAL While Executing Function Signature ISybaseWithSRSRepliDataAccess.GetByCompanyId(" + companyId +
        //                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
        //            }
        //        }

        private IList<SybaseWithRSHADRReplication> BuildSybaseWithRSHADREntities(IDataReader reader)
        {
            var SybaseWithRSHADR = new List<SybaseWithRSHADRReplication>();

            while (reader.Read())
            {
                var _sybaseWithRSHADRReplication = new SybaseWithRSHADRReplication
                {
                    ReplicationBase =
                    {
                        Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                        Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),


                    },
                    Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                    ReplicationServerName = Convert.IsDBNull(reader["ReplicationServerName"]) ? string.Empty : Convert.ToString(reader["ReplicationServerName"]),
                    RSPort = Convert.IsDBNull(reader["RSPort"]) ? string.Empty : Convert.ToString(reader["RSPort"]),
                    RSUserName = Convert.IsDBNull(reader["RSUserName"]) ? string.Empty : Convert.ToString(reader["RSUserName"]),
                    RSPassword = Convert.IsDBNull(reader["RSPassword"]) ? string.Empty : Convert.ToString(reader["RSPassword"]),
                    RMAServerPort = Convert.IsDBNull(reader["RMAServerPort"]) ? string.Empty : Convert.ToString(reader["RMAServerPort"]),
                    RMAUserName = Convert.IsDBNull(reader["RMAUserName"]) ? string.Empty : Convert.ToString(reader["RMAUserName"]),
                    RMAPassword = Convert.IsDBNull(reader["RMAPassword"]) ? string.Empty : Convert.ToString(reader["RMAPassword"]),
                    LogicalHostName = Convert.IsDBNull(reader["LogicalHostName"]) ? string.Empty : Convert.ToString(reader["LogicalHostName"]),
                    SybaseEnvPath = Convert.IsDBNull(reader["SybaseEnvPath"]) ? string.Empty : Convert.ToString(reader["SybaseEnvPath"]),

                };
                SybaseWithRSHADR.Add(_sybaseWithRSHADRReplication);
            }

            return (SybaseWithRSHADR.Count > 0) ? SybaseWithRSHADR : null;
        }

        public IList<SybaseWithRSHADRReplication> GetSybaseRSHADRReplicationByLoginId(string Replicationtype, int id)
        {
            try
            {
                const string sp = "SybaseRSHadr_GetByLoginId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return BuildSybaseWithRSHADREntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithRSHADRDataAccess.GetByLoginId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
    }
}
