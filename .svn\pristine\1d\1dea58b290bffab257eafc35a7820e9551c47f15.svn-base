﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DataSyncProperties", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DataSyncProperties : BaseEntity
    {
        #region Properties

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public string ReplicationType { get; set; }

        [DataMember]
        public int FilterOption { get; set; }

        [DataMember]
        public string FilterExpression { get; set; }

        [DataMember]
        public bool DeleteFilesOption { get; set; }

        [DataMember]
        public string DeleteFilesExpression { get; set; }

        [DataMember]
        public bool EnableSSHPrivateKeyPR { get; set; }

        [DataMember]
        public string SSHPrivateKeyPathPR { get; set; }

        [DataMember]
        public bool EnableSSHPrivateKeyDR { get; set; }

        [DataMember]
        public string SSHPrivateKeyPathDR { get; set; }

        [DataMember]
        public bool RetainFolderPermission { get; set; }

        [DataMember]
        public bool EnableChecksumCompare { get; set; }

        [DataMember]
        public string ShellPromptPR { get; set; }

        [DataMember]
        public string ShellPromptDR { get; set; }

        [DataMember]
        public bool EnableParallelReplication { get; set; }

        [DataMember]
        public int NumberOfThreads { get; set; }

        [DataMember]
        public bool EnableIncrementalReplication { get; set; }

        #endregion Properties
    }
}