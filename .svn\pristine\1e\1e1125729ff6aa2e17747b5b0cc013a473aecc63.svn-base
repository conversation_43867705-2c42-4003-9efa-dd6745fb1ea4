﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class MySqlRepliBuilder : IEntityBuilder<MySqlReplication>
    {
        IList<MySqlReplication> IEntityBuilder<MySqlReplication>.BuildEntities(IDataReader reader)
        {
            var MySqlRepli = new List<MySqlReplication>();

            while (reader.Read())
            {
                MySqlRepli.Add(((IEntityBuilder<MySqlReplication>)this).BuildEntity(reader, new MySqlReplication()));
            }

            return (MySqlRepli.Count > 0) ? MySqlRepli : null;
        }

        MySqlReplication IEntityBuilder<MySqlReplication>.BuildEntity(IDataReader reader, MySqlReplication MySqlRepli)
        {
            MySqlRepli.ReplicationBase.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            MySqlRepli.ReplicationBase.reptype = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]);
            MySqlRepli.ReplicationBase.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
            MySqlRepli.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            MySqlRepli.ReplicationId = Convert.IsDBNull(reader["ReplicationId"]) ? 0 : Convert.ToInt32(reader["ReplicationId"]);

            MySqlRepli.PRConnectState = Convert.IsDBNull(reader["PRConnectState"]) ? string.Empty : Convert.ToString(reader["PRConnectState"]);
            MySqlRepli.DRConnectState = Convert.IsDBNull(reader["DRConnectState"]) ? string.Empty : Convert.ToString(reader["DRConnectState"]);
            return MySqlRepli;
        }
    }
}
