﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using CP.BusinessFacade;

namespace CP.UI.Code.Replication.ReplicationInfo
{
    public class SVCReplication : IReplicationInfo
    {
        private readonly IFacade _facade = new Facade();

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraobjectId)
        {
            var currentEntityType = currentEntity.GetType();

            var svcinfr = _facade.GetAllSVCGMReplicationMvalueformonitoring(infraobjectId);
            if (svcinfr != null)
            {
                currentEntityType.GetProperty("PRConsistencyGroupName").SetValue(currentEntity, svcinfr.PRConsistencyGroupName, null);
                currentEntityType.GetProperty("PRRelationshipPrimaryValue").SetValue(currentEntity, svcinfr.PRRelationshipPrimaryValue, null);
                currentEntityType.GetProperty("PRMasterVolumeName").SetValue(currentEntity, svcinfr.PRMasterVolumeName, null);
                currentEntityType.GetProperty("PRAuxiliaryVolumeName").SetValue(currentEntity, svcinfr.PRAuxiliaryVolumeName, null);
                currentEntityType.GetProperty("PRRCRelationshipState").SetValue(currentEntity, svcinfr.PRRCRelationshipState, null);
                currentEntityType.GetProperty("PRRCRelationshipProgress").SetValue(currentEntity, svcinfr.PRRCRelationshipProgress, null);
                currentEntityType.GetProperty("PRRelationshipName").SetValue(currentEntity, svcinfr.PRRelationshipName, null);


                currentEntityType.GetProperty("DRConsistencyGroupName").SetValue(currentEntity, svcinfr.DRConsistencyGroupName, null);
                currentEntityType.GetProperty("DRRelationshipPrimaryValue").SetValue(currentEntity, svcinfr.DRRelationshipPrimaryValue, null);
                currentEntityType.GetProperty("DRMasterVolumeName").SetValue(currentEntity, svcinfr.DRMasterVolumeName, null);
                currentEntityType.GetProperty("DRAuxiliaryVolumeName").SetValue(currentEntity, svcinfr.DRAuxiliaryVolumeName, null);
                currentEntityType.GetProperty("DRRCRelationshipState").SetValue(currentEntity, svcinfr.DRRCRelationshipState, null);
                currentEntityType.GetProperty("DRRCRelationshipProgress").SetValue(currentEntity, svcinfr.DRRCRelationshipProgress, null);
                currentEntityType.GetProperty("DRRelationshipName").SetValue(currentEntity, svcinfr.DRRelationshipName, null);

            }
            else
            {
                currentEntityType.GetProperty("PRConsistencyGroupName").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRRelationshipPrimaryValue").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRMasterVolumeName").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRAuxiliaryVolumeName").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRRCRelationshipState").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRRCRelationshipProgress").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRRelationshipName").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("DRConsistencyGroupName").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRRelationshipPrimaryValue").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRMasterVolumeName").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRAuxiliaryVolumeName").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRRCRelationshipState").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRRCRelationshipProgress").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRRelationshipName").SetValue(currentEntity, "N/A", null);

            }
            return currentEntity;
        }

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectid, int mailBoxId, string mailboxname)
        {
            return currentEntity;
        }
    }
}


