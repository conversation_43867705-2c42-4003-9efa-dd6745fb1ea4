﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IBusinessUserFunctionDataAccess

    public interface IBusinessUserFunctionDataAccess
    {
        BusinessUserFunction Add(BusinessUserFunction businessUserFunction);

        BusinessUserFunction Update(BusinessUserFunction businessUserFunction);

        BusinessUserFunction GetById(int id);

        IList<BusinessUserFunction> GetByBusinessInfoId(int businessid);

        IList<BusinessUserFunction> GetByBusinessServiceId(int businessServiceId);

        BusinessUserFunction GetByName(string name);

        IList<BusinessUserFunction> GetAll();

        bool DeleteById(int id);

        bool IsExistByName(string name);
    }

    #endregion IBusinessUserFunctionDataAccess
}