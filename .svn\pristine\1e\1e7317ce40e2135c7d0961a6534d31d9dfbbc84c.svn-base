﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using System.Linq;

namespace CP.DataAccess
{
    internal sealed class ZFSReplicationMonitorLogsBuilder : IEntityBuilder<ZFSReplicationMonitorLogs>
    {

      IList<ZFSReplicationMonitorLogs> IEntityBuilder<ZFSReplicationMonitorLogs>.BuildEntities(IDataReader reader)
       {
           var ZFSReplicationMonitorLogs = new List<ZFSReplicationMonitorLogs>();

           while (reader.Read())
           {
               ZFSReplicationMonitorLogs.Add(((IEntityBuilder<ZFSReplicationMonitorLogs>)this).BuildEntity(reader, new ZFSReplicationMonitorLogs()));
           }

           return (ZFSReplicationMonitorLogs.Count > 0) ? ZFSReplicationMonitorLogs : null;
       }

        ZFSReplicationMonitorLogs IEntityBuilder<ZFSReplicationMonitorLogs>.BuildEntity(IDataReader reader, ZFSReplicationMonitorLogs robocopy)
       {

           robocopy.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
           robocopy.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
           robocopy.PRStoragePoolName = Convert.IsDBNull(reader["PRStoragePoolName"])
               ? string.Empty
               : Convert.ToString(reader["PRStoragePoolName"]);
           robocopy.DRStoragePoolName = Convert.IsDBNull(reader["DRStoragePoolName"])
               ? string.Empty
               : Convert.ToString(reader["DRStoragePoolName"]);

           robocopy.PRActionName = Convert.IsDBNull(reader["PRActionName"])
               ? string.Empty
               : Convert.ToString(reader["PRActionName"]);
           robocopy.DRActionName = Convert.IsDBNull(reader["DRActionName"])
               ? string.Empty
               : Convert.ToString(reader["DRActionName"]);

           robocopy.PRProjectName = Convert.IsDBNull(reader["PRProjectName"])
            ? string.Empty
            : Convert.ToString(reader["PRProjectName"]);
           robocopy.DRProjectName = Convert.IsDBNull(reader["DRProjectName"])
               ? string.Empty
               : Convert.ToString(reader["DRProjectName"]);



           robocopy.PRZFSStorageAppliance = Convert.IsDBNull(reader["PRZFSStorageAppliance"])
          ? string.Empty
          : Convert.ToString(reader["PRZFSStorageAppliance"]);
           robocopy.DRZFSStorageAppliance = Convert.IsDBNull(reader["DRZFSStorageAppliance"])
               ? string.Empty
               : Convert.ToString(reader["DRZFSStorageAppliance"]);



           robocopy.PRZFSStorageApplianceIPAddress = Convert.IsDBNull(reader["PRZFSStorageApplianceIPAddress"])
             ? string.Empty
             : Convert.ToString(reader["PRZFSStorageApplianceIPAddress"]);
           robocopy.DRZFSStorageApplianceIPAddress = Convert.IsDBNull(reader["DRZFSStorageApplianceIPAddress"])
               ? string.Empty
               : Convert.ToString(reader["DRZFSStorageApplianceIPAddress"]);

           robocopy.PRReplicationMode = Convert.IsDBNull(reader["PRReplicationMode"])
               ? string.Empty
               : Convert.ToString(reader["PRReplicationMode"]);
           robocopy.DRReplicationMode = Convert.IsDBNull(reader["DRReplicationMode"])
               ? string.Empty
               : Convert.ToString(reader["DRReplicationMode"]);

           robocopy.PREstimatedSize = Convert.IsDBNull(reader["PREstimatedSize"])
          ? string.Empty
          : Convert.ToString(reader["PREstimatedSize"]);
           robocopy.DREstimatedSize = Convert.IsDBNull(reader["DREstimatedSize"])
               ? string.Empty
               : Convert.ToString(reader["DREstimatedSize"]);


           robocopy.PREstimatedTime = Convert.IsDBNull(reader["PREstimatedTime"])
             ? string.Empty
             : Convert.ToString(reader["PREstimatedTime"]);
           robocopy.DREstimatedTime = Convert.IsDBNull(reader["DREstimatedTime"])
               ? string.Empty
               : Convert.ToString(reader["DREstimatedTime"]);

           robocopy.PRReplicationState = Convert.IsDBNull(reader["PRReplicationState"])
               ? string.Empty
               : Convert.ToString(reader["PRReplicationState"]);
           robocopy.DRReplicationState = Convert.IsDBNull(reader["DRReplicationState"])
               ? string.Empty
               : Convert.ToString(reader["DRReplicationState"]);

           robocopy.PRReplicationStateDescription = Convert.IsDBNull(reader["PRReplicationStateDescription"])
          ? string.Empty
          : Convert.ToString(reader["PRReplicationStateDescription"]);
           robocopy.DRReplicationStateDescription = Convert.IsDBNull(reader["DRReplicationStateDescription"])
               ? string.Empty
               : Convert.ToString(reader["DRReplicationStateDescription"]);
           robocopy.PRLastReplicationStatus = Convert.IsDBNull(reader["PRLastReplicationStatus"])
             ? string.Empty
             : Convert.ToString(reader["PRLastReplicationStatus"]);
           robocopy.DRLastReplicationStatus = Convert.IsDBNull(reader["DRLastReplicationStatus"])
               ? string.Empty
               : Convert.ToString(reader["DRLastReplicationStatus"]);

           robocopy.PRLastSyncTime = Convert.IsDBNull(reader["PRLastSyncTime"])
               ? string.Empty
               : Convert.ToString(reader["PRLastSyncTime"]);
           robocopy.DRLastSyncTime = Convert.IsDBNull(reader["DRLastSyncTime"])
               ? string.Empty
               : Convert.ToString(reader["DRLastSyncTime"]);

           robocopy.DataLag = Convert.IsDBNull(reader["DataLag"])
          ? string.Empty
          : Convert.ToString(reader["DataLag"]);
           robocopy.DRZFSStorageAppliance = Convert.IsDBNull(reader["DRZFSStorageAppliance"])
               ? string.Empty
               : Convert.ToString(reader["DRZFSStorageAppliance"]);

           robocopy.CreateDate = Convert.IsDBNull(reader["CreateDate"])
             ? DateTime.MinValue
             : Convert.ToDateTime(reader["CreateDate"].ToString());

           //robocopy.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
           //robocopy.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
          
           //robocopy.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
           //robocopy.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
           //    ? DateTime.MinValue
           //    : Convert.ToDateTime(reader["UpdateDate"].ToString());

           //robocopy.SelectedOptions = Convert.IsDBNull(reader["SelectedOptions"]) ? string.Empty : Convert.ToString(reader["SelectedOptions"]);


           return robocopy;
       }
    }
}
