namespace CP.UI.Report.TelerikReports
{
    using CP.Common.DatabaseEntity;
    using log4net;
    using System;
    using System.Collections.Generic;
    using System.ComponentModel;
    using System.Data;
    using System.Drawing;
    using System.Windows.Forms;
    using Telerik.Reporting;
    using Telerik.Reporting.Drawing;

    /// <summary>
    /// Summary description for MssqlDatalagReport.
    /// </summary>
    public partial class MongoDB_DataLagReport : Telerik.Reporting.Report
    {

        private readonly ILog _logger = LogManager.GetLogger(typeof(MongoDB_DataLagReport));
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();

        public MongoDB_DataLagReport()
        {
            //
            // Required for telerik Reporting designer support
            //
            InitializeComponent();

            //
            // TODO: Add any constructor code after InitializeComponent call
            //
        }


        private void ShowTable()
        {
            try
            {
                string stdate = (this.ReportParameters["iStartDate"].Value).ToString();
                int LoggedInUserId = Convert.ToInt32(this.ReportParameters["iInfraId"].Value);
                string enddate = (this.ReportParameters["iEndDate"].Value).ToString();


                //IList<MSSQLDBMirrorReplicationMonitor> listmssql = new List<MSSQLDBMirrorReplicationMonitor>();

                // lstemail = Facade.GetEmailByDate(stdate, enddate, LoggedInUserId);
                IList<MongoDBDMonitorStatus> listmssql = Facade.GetMongoDBReplicationByDate(LoggedInUserId, stdate, enddate);
                //listmssql = Facade.GetMSSQLDBMirrorGetByDate(LoggedInUserId, stdate, enddate);

                var dataTable = new DataTable();
                dataTable.Columns.Add("SRNO");
                dataTable.Columns.Add("MongodStatusPR");
                dataTable.Columns.Add("MongodStatusDR");
                dataTable.Columns.Add("HostNamePR");
                dataTable.Columns.Add("HostNameDR");
                dataTable.Columns.Add("CreateDate");
                dataTable.Columns.Add("PRDatalag");

                //dataTable.Columns.Add("CreateDate");

                _logger.Info("Data Mapping Start For Report.");

                int i = 1;
                if (listmssql != null)
                {
                    foreach (MongoDBDMonitorStatus grp in listmssql)
                    {


                        DataRow dr = dataTable.NewRow();
                        dr["SRNO"] = i.ToString();

                        dr["MongodStatusPR"] = grp.MongodStatusPR != null ? grp.MongodStatusPR : "NA";
                        dr["MongodStatusDR"] = grp.MongodStatusDR != null ? grp.MongodStatusDR : "NA";
                        dr["HostNamePR"] = grp.HostNamePR != null ? grp.HostNamePR : "NA";
                        dr["HostNameDR"] = grp.HostNameDR != null ? grp.HostNameDR : "NA";
                        dr["CreateDate"] = grp.CreateDate != null ? (Convert.ToDateTime(grp.CreateDate).ToString()) : "NA";
                        dr["PRDatalag"] = grp.PRDatalag != null ? grp.PRDatalag : "NA";


                        i++;

                        dataTable.Rows.Add(dr);
                    }
                }
                this.DataSource = dataTable;
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In ShowTable Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In ShowTable Method, InnerException Message " + ex.InnerException.Message);
            }
        }

        private void MongoDB_DataLagReport_NeedDataSource(object sender, EventArgs e)
        {
            ShowTable();
        }

        
    }
}