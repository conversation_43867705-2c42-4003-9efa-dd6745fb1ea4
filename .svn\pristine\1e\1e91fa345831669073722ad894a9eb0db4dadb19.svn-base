﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class ScheduleDiscoveryProfDetailsBuilder : IEntityBuilder<ScheduleDiscoveryProfDetails>
    {
        IList<ScheduleDiscoveryProfDetails> IEntityBuilder<ScheduleDiscoveryProfDetails>.BuildEntities(IDataReader reader)
        {
            var scheduleDiscoveryProfileDetails = new List<ScheduleDiscoveryProfDetails>();

            while (reader.Read())
            {
                scheduleDiscoveryProfileDetails.Add(((IEntityBuilder<ScheduleDiscoveryProfDetails>)this).BuildEntity(reader,
                    new ScheduleDiscoveryProfDetails()));
            }

            return (scheduleDiscoveryProfileDetails.Count > 0) ? scheduleDiscoveryProfileDetails : null;
        }


        ScheduleDiscoveryProfDetails IEntityBuilder<ScheduleDiscoveryProfDetails>.BuildEntity(IDataReader reader,
            ScheduleDiscoveryProfDetails scheduleDiscoveryProfDetails)
        {

            scheduleDiscoveryProfDetails.ScheDiscProfileName = Convert.IsDBNull(reader["ScheDiscProfileName"]) ? string.Empty : Convert.ToString(reader["ScheDiscProfileName"]);

            scheduleDiscoveryProfDetails.HostFrom = Convert.IsDBNull(reader["HostFrom"]) ? string.Empty : Convert.ToString(reader["HostFrom"]);

            scheduleDiscoveryProfDetails.HostTo = Convert.IsDBNull(reader["HostTo"]) ? string.Empty : Convert.ToString(reader["HostTo"]);

            scheduleDiscoveryProfDetails.OsFilter = Convert.IsDBNull(reader["OsFilter"]) ? "All" : Convert.ToString(reader["OsFilter"]);

            scheduleDiscoveryProfDetails.AppFilter = Convert.IsDBNull(reader["AppFilter"]) ? "All" : Convert.ToString(reader["AppFilter"]);

            scheduleDiscoveryProfDetails.IsScheduled = Convert.IsDBNull(reader["IsScheduled"]) ? 0 : Convert.ToInt32(reader["IsScheduled"]);

            return scheduleDiscoveryProfDetails;
        }

    }
}
