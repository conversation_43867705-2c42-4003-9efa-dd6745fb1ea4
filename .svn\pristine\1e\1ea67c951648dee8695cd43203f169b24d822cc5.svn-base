﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.HanaDBSystemOperationMode
{
    internal sealed class HanaDbSystemOperationModeDataAccess : BaseDataAccess, IHanaDbSystemOperationModeDataAccess
    {
         #region Constructors

       public HanaDbSystemOperationModeDataAccess(Context context)
            : base(context)
        {
        }

       protected override IEntityBuilder<HanaDbSystemOperationMode> CreateEntityBuilder<HanaDbSystemOperationMode>()
        {
            return (new HanaDbSystemOperationModeBuilder()) as IEntityBuilder<HanaDbSystemOperationMode>;
        }

        #endregion Constructors


       #region Methods

       IList<HanaDbSystemOperationMode> IHanaDbSystemOperationModeDataAccess.GetByInfraObjectId(int infraObjectId)
       {
           try
           {
               const string sp = "HanaDBOpeMode_GetByInfraId";

               using (DbCommand cmd = Database.GetStoredProcCommand(sp))
               {
                   Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                   using (IDataReader reader = Database.ExecuteReader(cmd))
                   {
                       return CreateEntityBuilder<HanaDbSystemOperationMode>().BuildEntities(reader);
                       //return reader.Read()
                       //    ? (CreateEntityBuilder<HanaDBDatabaseService>()).BuildEntity(reader, new HanaDB_Database_Service())
                       //    : null;
                   }
               }
           }
           catch (Exception exc)
           {
               throw new CpException(CpExceptionType.DataAccessFetchOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                   "Error In DAL While Executing Function Signature IHanaDbSystemOperationModeDataAccess.GetByInfraObjectId(" +
                   infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
           }
       }

       #endregion
    }
}
