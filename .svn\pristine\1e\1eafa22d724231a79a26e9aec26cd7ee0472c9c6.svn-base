﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;


namespace CP.DataAccess
{
    internal sealed class DropDownDataAccess : BaseDataAccess, IDropDownDataAccess
    {
        #region Constructors

        public DropDownDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DropDown> CreateEntityBuilder<DropDown>()
        {
            return (new DropDownBuilder()) as IEntityBuilder<DropDown>;
        }


        public void updatewfactionname(string WFName, string id)
        { //[WorkflowAction_UPdateEncryption]}

            try
            {
                const string sp = "WorkflowAction_UPdateEncryption";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.String, WFName);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.String, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {



                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetAllWorkflowActionsByType_DropDown" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }


        IList<DropDown> IDropDownDataAccess.GetAllWorkflowActionsByType_DropDown(int TypeId)
        {
            try
            {
                const string sp = "WorkflowAction_GetAllByType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.Int32, TypeId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("WORKFLOWACTION_CREATECur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        IList<DropDown> workflowActionNewList = new List<DropDown>();

                        while (reader.Read())
                        {
                            var workflowAction = new DropDown();
                            workflowAction.Id = Convert.IsDBNull(reader["Id"]) ? "0" : Convert.ToString(reader["Id"]);
                            workflowAction.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : CryptographyHelper.Md5Decrypt(reader["Name"].ToString());
                            //workflowAction.Name = Convert.IsDBNull(reader["ActualName"]) ? string.Empty : (reader["ActualName"].ToString());
                            workflowActionNewList.Add(workflowAction);
                            // updatewfactionname(workflowAction.Name, workflowAction.Id);
                        }

                        return workflowActionNewList;

                        // return CreateEntityBuilder<DropDown>().BuildEntities(reader);

                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowActionDataAccess.GetAllWorkflowActionsByType_DropDown" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }



        IList<DropDown> IDropDownDataAccess.GetWorkflowsByUserrole_DropDown(string userRole, int userId)
        {
            try
            {
                const string sp = "GetWorkflowByuserRole_DropDown";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iuserRole", DbType.AnsiString, userRole);
                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, userId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DropDown>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser_DropDown" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<DropDown> IDropDownDataAccess.GetWorkflowsByUserrole_DropDown_Limit(string userRole, int userId, int limit)
        {
            try
            {
                const string sp = "GetWorkflowByuserRole_DropDown_Limit";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iuserRole", DbType.AnsiString, userRole);
                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, userId);
                    Database.AddInParameter(cmd, Dbstring + "ilimit", DbType.Int32, limit);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DropDown>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser_DropDown" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<DropDown> IDropDownDataAccess.GetWorkflowsByUserrole_DropDown_Limit_new1(string userRole, int userId)
        {
            IList<DropDown> wfdetails_new = new List<DropDown>();
            try
            {
                const string sp = "GetWorkflowByuserRole_DropDown_Limit_new1";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iuserRole", DbType.AnsiString, userRole);
                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, userId);
                 
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //return CreateEntityBuilder<DropDown>().BuildEntities(reader);
                        while(reader.Read())
                        {
                            var wfdetails = new DropDown
                            {
                                workflowId = Convert.IsDBNull(reader["Id"]) ? "0" : Convert.ToString(reader["Id"]),
                                Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                            };
                            wfdetails_new.Add(wfdetails);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser_DropDown" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            return wfdetails_new;
        }

        IList<DropDown> IDropDownDataAccess.GetWorkflowsByUserrole_DropDown_Search(string isSuperAdmin, int LoggedInUserId, string search)
        {
            try
            {
                const string sp = "GetWorkflowByuserRole_DropDown_Search";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iuserRole", DbType.AnsiString, isSuperAdmin);
                    Database.AddInParameter(cmd, Dbstring + "iuserId", DbType.Int32, LoggedInUserId);
                    Database.AddInParameter(cmd, Dbstring + "iSearch", DbType.AnsiString, search);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DropDown>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser_DropDown" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<DropDown> IDropDownDataAccess.GetprofileByUserrole_DropDown_Search(string search)
        {
            IList<DropDown> wfdetails_new = new List<DropDown>();
            try
            {
                const string sp = "GetprofileByuserRole_DropDown_Search";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {                    
                    Database.AddInParameter(cmd, Dbstring + "iSearch", DbType.AnsiString, search);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var wfdetails = new DropDown
                            {
                                workflowId = Convert.IsDBNull(reader["Id"]) ? "0" : Convert.ToString(reader["Id"]),
                                Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                            };
                            wfdetails_new.Add(wfdetails);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser_DropDown" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            return wfdetails_new;
        }

        IList<DropDown> IDropDownDataAccess.GetParallelProfile_NmandId()
        {
            try
            {
                const string sp = "getparallelprofile_NMandID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DropDown>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser_DropDown" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }



        IList<DropDown> IDropDownDataAccess.GetWorkflow_NmandId()
        {
            try
            {
                const string sp = "getWorkflow_NMandID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DropDown>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser_DropDown" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<DropDown> IDropDownDataAccess.GetParallelProfile_NmandId_new()
        {
            try
            {
                const string sp = "GetParallelProfile_NmandId_new";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("Cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<DropDown>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWorkflowDataAccess.GetWorkflowByuser_DropDown" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }




        #endregion Constructors

    }
}