﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class DatabaseNodesBuilder : IEntityBuilder<DatabaseNodes>
    {
        IList<DatabaseNodes> IEntityBuilder<DatabaseNodes>.BuildEntities(IDataReader reader)
        {
            var databasenodess = new List<DatabaseNodes>();

            while (reader.Read())
            {
                databasenodess.Add(((IEntityBuilder<DatabaseNodes>)this).BuildEntity(reader, new DatabaseNodes()));
            }

            return (databasenodess.Count > 0) ? databasenodess : null;
        }

        DatabaseNodes IEntityBuilder<DatabaseNodes>.BuildEntity(IDataReader reader, DatabaseNodes databaseNodes)
        {
            //const int FLD_ID = 0;
            //const int FLD_DATABASEID = 1;
            //const int FLD_NODEID = 2;

            //databaseNodes.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //databaseNodes.DatabaseId = reader.IsDBNull(FLD_DATABASEID) ? 0 : reader.GetInt32(FLD_DATABASEID);
            //databaseNodes.NodeId = reader.IsDBNull(FLD_NODEID) ? 0 : reader.GetInt32(FLD_NODEID);

            //Fields in bcms_database_nodes table on 16/07/2013 : Id, DatabaseID, NodeId

            databaseNodes.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            databaseNodes.DatabaseId = Convert.IsDBNull(reader["DatabaseID"])
                ? 0
                : Convert.ToInt32(reader["DatabaseID"]);
            databaseNodes.NodeId = Convert.IsDBNull(reader["NodeId"]) ? 0 : Convert.ToInt32(reader["NodeId"]);

            return databaseNodes;
        }
    }
}