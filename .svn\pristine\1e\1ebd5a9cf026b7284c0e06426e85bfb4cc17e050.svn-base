﻿using System;

namespace CP.UI.Admin
{
    public partial class ConfigureBusinessService : InfraObjectsBasePage
    {
        private static bool _isUserSuperAdmin;

        public override void PrepareView()
        {
            _isUserSuperAdmin = IsSuperAdmin;
            if (_isUserSuperAdmin)
            {
                dvmessagewithClick.Visible = true;
                dvmessage.Visible = false;
            }
            else
            {
                dvmessagewithClick.Visible =false ;
                dvmessage.Visible = true;
            }
        }
    }
}