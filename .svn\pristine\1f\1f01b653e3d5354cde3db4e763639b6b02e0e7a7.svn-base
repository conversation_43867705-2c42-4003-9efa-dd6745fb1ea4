﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="MySqlReplicationConfig.ascx.cs" Inherits="CP.UI.Controls.MySqlReplicationConfig" %>

<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">MySql Replication Console</h4>
        </div>
        <div class="widget-body">
            <table class="table">
                <thead>
                    <tr>
                        <th style="width: 24%;"></th>
                        <th style="width: 37.5% !important;">Production Server
                        </th>
                        <th style="width: 37.5% !important;">DR Server
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <label>
                                 Connect State
                            </label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                           <asp:TextBox ID="txtPRConnectState" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPRConnectState" runat="server" ControlToValidate="txtPRConnectState" CssClass="error"
                                Display="Dynamic"  ErrorMessage="Enter PR Connect State"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                           <asp:TextBox ID="txtDRConnectState" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRConnectSate" runat="server" ControlToValidate="txtDRConnectState" CssClass="error"
                                Display="Dynamic" ErrorMessage="Enter DR Connect State"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="form-actions row">
    <div class="col-md-3">
        <asp:Label ID="lblMsg" runat="server" Text=""></asp:Label>
    </div>
    <div class="col-md-7 text-right">
        <asp:Button ID="SaveRep" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save"
            OnClick="SaveRep_Click" />
        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server"
            Text="Cancel" CausesValidation="false" OnClick="btnCancel_Click" />
    </div>
</div>