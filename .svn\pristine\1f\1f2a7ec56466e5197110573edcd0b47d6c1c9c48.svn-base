﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="Financial_ImpactRPT.aspx.cs" Inherits="CP.UI.Report.Financial_ImpactRPT" %>
<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<%@ Register Assembly="Telerik.ReportViewer.WebForms, Version=8.1.14.618, Culture=neutral, PublicKeyToken=a9d7983dfcc261be" Namespace="Telerik.ReportViewer.WebForms" TagPrefix="telerik" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
                <style type="text/css">
        .interactive-page .sheet {
            margin-left: 35px !important;
        }
        .InputButtonClass {
            margin-top: 0px !important;
        }
        #ctl00_cphBody_RptVwr_ReportToolbar_ExportGr_FormatList > select {
            width: 100% !important;
        }
                    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">

        <h3>Financial Impact Report</h3>

                        <div class="form-group">
                            <div class="col-md-2"></div>
                            <div class="col-md-8">
                                <telerik:ReportViewer ID="RptVwr" runat="server" Width="100%" Height="900" Skin="Default"></telerik:ReportViewer>
                            </div>
                            <div class="col-md-2"></div>
                        </div>

 
            </div>
</asp:Content>
