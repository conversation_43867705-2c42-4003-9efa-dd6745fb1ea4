﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.BusinessFacade;
using CP.Common.Shared;
using System.Configuration;
using CP.Helper;
namespace CP.UI
{
    public partial class CompanyInfoPage : System.Web.UI.Page
    {
        #region Variables

        CompanyProfile _CompanyProfile = new CompanyProfile();
        User _User = new User();
        private static IFacade _facade = new Facade();
        
        #endregion

        #region Methods
        private void BuildCompanyProfileEntity()
        {

            try
            {
                _CompanyProfile.Name = txtcompanyName.Text;
                _CompanyProfile.DisplayName = txtdisName.Text;
                _CompanyProfile.IsParent = true;
                _CompanyProfile.ParentId = 0;
                _CompanyProfile.CompanyLogoPath = "";
                _CompanyProfile.CreatorId = 46;              
                _CompanyProfile.CompanyId = txtCompanyId.Text;            
                _CompanyProfile.CompanyInformation.Address = "Pune";
                _CompanyProfile.CompanyInformation.Phone = "+91-20-66878300";
                _CompanyProfile.CompanyInformation.Fax = "+91-20-66878300";
                _CompanyProfile.CompanyInformation.WebAddress = "www." + txtdisName.Text.ToLower() + ".com";
                _CompanyProfile.CompanyInformation.Email = "admin@" + txtdisName.Text.ToLower() + ".com";
                _CompanyProfile.CompanyInformation.CreatorId = 46;
            }
            catch (Exception)
            {


            }
        }

        private void BuildUserEntity()
        {
            try
            {
                _User.LoginName = txtLoginName.Text;
                _User.LoginPassword = txtPassword.Text;
                if (Session["CompanyId"] != null && Session["Email"] != null)
                {
                    _User.CompanyId = Convert.ToInt32(Session["CompanyId"]);
                    _User.UserInformation.Email = Session["Email"].ToString();
                }
                else
                {
                    IList<CompanyProfile> companies = _facade.GetAllCompanyProfiles();
                    foreach (var item in companies)
                    {
                        _User.CompanyId = item.Id;
                        _User.UserInformation.Email = item.CompanyInformation.Email;
                    }
                }
                _User.Role = UserRole.SuperAdmin;
                _User.LastLoginDate = DateTime.Now;
                _User.LastLoginIP = "*************";
                _User.LastAlertId = 0;
                _User.LastIncidentId = 0;
                _User.LastPasswordChanged = DateTime.Now;
                _User.IsReset = false;
                _User.IsActive = 1;
                _User.InfraObjectAllFlag = true;
                _User.ApplicationAllFlag = true;
                _User.CreatorId = 1;

                _User.UserInformation.UserName = txtLoginName.Text;
                _User.UserInformation.Address = "Pune";
                _User.UserInformation.Phone = "+91-20-66878300";
                _User.UserInformation.Mobile = "+91-9999999999";
                _User.UserInformation.AlertMode = AlertModeType.EmailandSMS;
                _User.IsActive = 1;
                _User.CreatorId = 1;

                _User.LoginType = LoginType.FormAuthentication;
            }
            catch (Exception)
            {


            }

        }
        #endregion

        #region Events

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                if (!IsPostBack)
                {
                    ViewState["_token"] = UrlHelper.AddTokenToRequest();
                    if (ViewState["_token"] != null)
                    {
                        hdtokenKey.Value = ViewState["_token"].ToString();
                    }
                }
                passExp.Title = "Password must have minimum (8,16) characters. At least 1 upper-case alphabetic, 4 lower-case, 3 numeric character and 1 special characters.";
                var CompanyCount = _facade.GetAllCompanyProfiles();
                var UserCount = _facade.GetAllUsers();
                if (CompanyCount.Count == 0 && UserCount.Count == 0)
                {
                    pnlCompanyInfo.Visible = true;
                    pnlUserInfo.Visible = false;
                }
                else if (CompanyCount.Count != 0 && UserCount.Count == 0)
                {
                    pnlCompanyInfo.Visible = false;
                    pnlUserInfo.Visible = true;

                }
                else if (CompanyCount.Count == 0 && UserCount.Count != 0)
                {
                    pnlCompanyInfo.Visible = true;
                    pnlUserInfo.Visible = false;

                }
            }
            catch (Exception)
            {
                
               
            }
        }
       
        protected void btnSave_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null))
            {
                if (!UrlHelper.IsTokenValidated(ViewState["_token"].ToString()) || Request.HttpMethod != "POST")
                {
                    Response.Redirect("~/Logout.aspx");
                    return;
                }
            }
            else
            {
                Response.Redirect("~/Logout.aspx");
                return;
            }

            try
            {
                pnlCompanyInfo.Visible = false;
                pnlUserInfo.Visible = true;
                BuildCompanyProfileEntity();
                _CompanyProfile = _facade.AddCompanyProfile(_CompanyProfile);
                Session["CompanyId"] = _CompanyProfile.Id;
                Session["Email"] = _CompanyProfile.CompanyInformation.Email;
            }
            catch (Exception)
            {
                
                
            }
           
        }

        protected void btnSave_Click1(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null))
            {
                if (!UrlHelper.IsTokenValidated(ViewState["_token"].ToString()) || Request.HttpMethod != "POST")
                {
                    Response.Redirect("~/Logout.aspx");
                    return;
                }
            }
            else
            {
                Response.Redirect("~/Logout.aspx");
                return;
            }

            try
            {
                BuildUserEntity();
                _User = _facade.AddUser(_User);
                if (_User != null)
                {
                    Response.Redirect("~/Login.aspx");
                }
            }
            catch (Exception)
            {
                
               
            }
        }

        #endregion
    }
}