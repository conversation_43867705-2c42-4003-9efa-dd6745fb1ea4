﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using System.Linq;

namespace CP.DataAccess
{
    internal sealed class HACMPResourceGroupsMonitorBuilder : IEntityBuilder<HACMPResourceGroupsMonitor>
    {
        IList<HACMPResourceGroupsMonitor> IEntityBuilder<HACMPResourceGroupsMonitor>.BuildEntities(IDataReader reader)
        {
            var HACMPResourceGroupsMonitor = new List<HACMPResourceGroupsMonitor>();

            while (reader.Read())
            {
                HACMPResourceGroupsMonitor.Add(((IEntityBuilder<HACMPResourceGroupsMonitor>)this).BuildEntity(reader, new HACMPResourceGroupsMonitor()));
            }

            return (HACMPResourceGroupsMonitor.Count > 0) ? HACMPResourceGroupsMonitor : null;
        }

        HACMPResourceGroupsMonitor IEntityBuilder<HACMPResourceGroupsMonitor>.BuildEntity(IDataReader reader, HACMPResourceGroupsMonitor robocopy)
        {

            robocopy.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            robocopy.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            robocopy.GroupName = Convert.IsDBNull(reader["GroupName"])
                ? string.Empty
                : Convert.ToString(reader["GroupName"]);
            robocopy.GroupState = Convert.IsDBNull(reader["GroupState"])
                ? string.Empty
                : Convert.ToString(reader["GroupState"]);

            robocopy.Node = Convert.IsDBNull(reader["Node"])
                ? string.Empty
                : Convert.ToString(reader["Node"]);

            robocopy.ClusterId = Convert.IsDBNull(reader["ClusterId"]) ? 0 : Convert.ToInt32(reader["ClusterId"]);
          

            //robocopy.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            //robocopy.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);

            //robocopy.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            //robocopy.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
            //    ? DateTime.MinValue
            //    : Convert.ToDateTime(reader["UpdateDate"].ToString());

            //robocopy.SelectedOptions = Convert.IsDBNull(reader["SelectedOptions"]) ? string.Empty : Convert.ToString(reader["SelectedOptions"]);


            return robocopy;
        }
    }
}
