﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Web;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using System.Linq;

namespace CP.UI.Admin
{
    public partial class BusinessProcessAuto : BPAutomationBasePageEditor
    {
        public string BPtype;

        public override string MessageInitials
        {
            get { return "BusinessProcess"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Admin.BusinessProcess;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            //ViewState["_token"] = UrlHelper.AddTokenToRequest();
            //if (ViewState["_token"] != null)
            //{
            //    hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            //}

            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 

            Utility.SelectMenu(Master, "Module6");
            ddlgrouplst.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
            ddlBPType.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            ddlBPhostname.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");
            txtPRIp.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");
            txtsourcedir.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator5.ClientID + ")");
            txtdestdir.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator6.ClientID + ")");
            ddlscripthost.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator7.ClientID + ")");
            txtDRIp.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator8.ClientID + ")");
            txtpath.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator9.ClientID + ")");
            //Existing Code Commented by meenakshi 30April- 12:30
            //Utility.PopulateInfrObjectByParentAndRole(ddlgrouplst, LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            IList<InfraObject> gdata = null;
            gdata = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
            if (gdata != null)
            {
                ddlgrouplst.DataSource = gdata;
                ddlgrouplst.DataTextField = "Name";
                ddlgrouplst.DataValueField = "Id";
                ddlgrouplst.DataBind();
            }
            ddlgrouplst.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));

            ddlBPType.Items.Insert(0, new ListItem("Select Component", "0"));
            ddlBPhostname.Items.Insert(0, new ListItem("Select HostName", "0"));
            ddlscripthost.Items.Insert(0, new ListItem("Select HostName", "0"));
        }

        public void CheckBprocess(string proc)
        {
            var automation = Facade.GetBusinessProcessByName(Session["InfraObjectListText"].ToString(), proc);
            if (automation != null)
            {
                foreach (var auto in automation)
                {
                    ddlBPhostname.SelectedValue = auto.PRHostId.ToString();
                    ddlscripthost.SelectedValue = auto.DRHostId.ToString();
                    txtsourcedir.Text = auto.SourceDir;
                    txtdestdir.Text = auto.DestinationDir;
                    txtpath.Text = auto.ScriptPath;
                    txtPRIp.Text = auto.PRIPAddress;
                    txtDRIp.Text = auto.DRIPAddress;
                    hiddenid.Value = auto.Id.ToString();
                    btnSave.Text = "Update";
                    ddlBPhostname.Enabled = false;
                    ddlscripthost.Enabled = false;
                    btnDelete.Visible = true;
                }
            }
            else
            {
                ddlBPhostname.SelectedValue = "0";
                ddlscripthost.SelectedValue = "0";
                txtsourcedir.Text = string.Empty;
                txtdestdir.Text = string.Empty;
                txtpath.Text = string.Empty;
                btnSave.Text = "Save";
                ddlBPhostname.Enabled = true;
                ddlscripthost.Enabled = true;
                hiddenid.Value = "0";
                btnDelete.Visible = false;
            }
        }

        protected void DdlgrouplstSelectedIndexChanged(object sender, EventArgs e)
        {
            if (Convert.ToInt32(ddlgrouplst.SelectedValue) > 0)
            {
                ddlBPType.Enabled = true;
                ddlBPType.SelectedValue = "0";
                btnDelete.Visible = false;
                try
                {
                    lblInactcompfle.Text = "Compress Files";
                    lblinactUncmpfle.Text = "UnCompress Files";
                    lblInactDeltefle.Text = "Delete Files";
                    lblInactMovefle.Text = "Move Files";
                    lblInactCopyfle.Text = "Copy Files";
                    lnlACmp.Text = string.Empty;
                    lnkAUCmp.Text = string.Empty;
                    lnlADlte.Text = string.Empty;
                    lnkAMve.Text = string.Empty;
                    lnkACpy.Text = string.Empty;
                    ddlBPhostname.Enabled = true;
                    ddlscripthost.Enabled = true;
                    int id = Convert.ToInt32(ddlgrouplst.SelectedValue);
                    if (id == 0)
                    {
                        lblGrpmsg.Text = "Select A GroupName";
                        ddlBPhostname.Items.Clear();
                        ddlscripthost.Items.Clear();
                        ddlBPhostname.Items.Insert(0, new ListItem("Select HostName", "0"));
                        ddlscripthost.Items.Insert(0, new ListItem("Select HostName", "0"));
                    }
                    else
                    {
                        //var grp = Facade.GetInfraObjectById.GetInfraObjectById(id);
                        var grp = Facade.GetInfraObjectById(id);
                        var serverpr = Facade.GetServerById(grp.PRServerId);
                        var serverdr = Facade.GetServerById(grp.DRServerId);
                        lblGrpmsg.Text = string.Empty;

                        if (serverpr != null && serverdr != null)
                        {
                            var dictionary = new Dictionary<int, string>();
                            dictionary.Add(grp.PRServerId, CryptographyHelper.Md5Decrypt(serverpr.IPAddress));
                            dictionary.Add(grp.DRServerId, CryptographyHelper.Md5Decrypt(serverdr.IPAddress));
                            Session["IP"] = dictionary;
                        }

                        if (ddlBPhostname.Items.Count > 0)
                        {
                            ddlBPhostname.Items.Clear();
                            ddlscripthost.Items.Clear();
                            ddlBPhostname.Items.Insert(0, new ListItem("Select HostName", "0"));
                            ddlscripthost.Items.Insert(0, new ListItem("Select HostName", "0"));
                        }

                        if (serverpr != null && serverdr != null)
                        {
                            ddlBPhostname.Items.Clear();
                            ddlscripthost.Items.Clear();
                            ddlBPhostname.Items.Insert(0, new ListItem("Select HostName", "0"));
                            ddlscripthost.Items.Insert(0, new ListItem("Select HostName", "0"));
                            ddlBPhostname.Items.Insert(1, new ListItem(serverpr.Name, serverpr.Id.ToString()));
                            ddlscripthost.Items.Insert(1, new ListItem(serverpr.Name, serverpr.Id.ToString()));
                            ddlBPhostname.Items.Insert(2, new ListItem(serverdr.Name, serverdr.Id.ToString()));
                            ddlscripthost.Items.Insert(2, new ListItem(serverdr.Name, serverdr.Id.ToString()));
                        }
                    }

                    Session["InfraObjectListText"] = ddlgrouplst.SelectedItem.Text;

                    ddlBPhostname.SelectedValue = "0";
                    ddlscripthost.SelectedValue = "0";
                    btnSave.Text = "Save";
                    txtsourcedir.Text = string.Empty;
                    txtdestdir.Text = string.Empty;
                    txtpath.Text = string.Empty;
                    txtPRIp.Text = string.Empty;
                    txtDRIp.Text = string.Empty;

                    var myAl = new ArrayList();
                    myAl.Add("Compress Files");
                    myAl.Add("UnCompress Files");
                    myAl.Add("Delete Files");
                    myAl.Add("Move Files");
                    myAl.Add("Copy Files");
                    var myAl1 = new ArrayList();
                    IList<BPAutomation> automationList = Facade.GetBusinessProcessByGroupName(ddlgrouplst.SelectedItem.Text);
                    if (automationList != null)
                    {
                        foreach (var i in automationList)
                        {
                            myAl1.Add(i.BPComponent);
                        }
                    }
                    else
                    {
                        lblInactcompfle.Text = "Compress Files";
                        lblinactUncmpfle.Text = "UnCompress Files";
                        lblInactDeltefle.Text = "Delete Files";
                        lblInactMovefle.Text = "Move Files";
                        lblInactCopyfle.Text = "Copy Files";
                    }

                    foreach (var s in myAl1)
                    {
                        if (s.ToString() == myAl[0].ToString())
                            lnlACmp.Text = myAl[0].ToString();
                        if (s.ToString() == myAl[1].ToString())
                            lnkAUCmp.Text = myAl[1].ToString();
                        if (s.ToString() == myAl[2].ToString())
                            lnlADlte.Text = myAl[2].ToString();
                        if (s.ToString() == myAl[3].ToString())
                            lnkAMve.Text = myAl[3].ToString();
                        if (s.ToString() == myAl[4].ToString())
                            lnkACpy.Text = myAl[4].ToString();
                    }

                    foreach (var v in myAl1)
                    {
                        if (v.ToString() == myAl[0].ToString())
                            lblInactcompfle.Text = string.Empty;

                        if (v.ToString() == myAl[1].ToString())
                            lblinactUncmpfle.Text = string.Empty;

                        if (v.ToString() == myAl[2].ToString())
                            lblInactDeltefle.Text = string.Empty;

                        if (v.ToString() == myAl[3].ToString())
                            lblInactMovefle.Text = string.Empty;

                        if (v.ToString() == myAl[4].ToString())
                            lblInactCopyfle.Text = string.Empty;
                    }
                }
                catch (CpException bcms)
                {
                    ExceptionManager.Manage(bcms);
                }
            }
            else
            {
                ddlBPType.Enabled = false;
            }
        }

        protected void DdlBPTypeSelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                string process = ddlBPType.SelectedItem.Text;
                int id = Convert.ToInt32(ddlBPType.SelectedValue);
                if (id > 3)
                {
                    txtdestdir.Visible = true;
                    lbldestdir.Visible = true;
                }
                else
                {
                    txtdestdir.Visible = false;
                    lbldestdir.Visible = false;
                }

                Session["BpTypeText"] = ddlBPType.SelectedItem.Text;
                txtPRIp.Text = string.Empty;
                txtDRIp.Text = string.Empty;
                CheckBprocess(process);
            }
            catch (CpException bcms)
            {
                ExceptionManager.Manage(bcms);
            }
        }
        protected void BtnDeleteClick(object sender, EventArgs e)
        {
            var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
            if (returnUrl.IsNullOrEmpty() && !ValidateRequest("BusinessProcessDeleting", UserActionType.CreateBusinessProcess))
            {
                returnUrl = ReturnUrl;
            }
            try
            {
                CurrentEntity.Id = Convert.ToInt32(hiddenid.Value);
                CurrentEntity.InfraObjectName = ddlgrouplst.SelectedItem.Text;

                bool isDelete = Facade.DeleteBusinessProcessAutoById(CurrentEntity.Id);

                ActivityLogger.AddLog(LoggedInUserName, "User", UserActionType.UpdateUserAccount,
                            "User " + CurrentEntity.GroupName + " deleted by  " + LoggedInUserName,
                            LoggedInUserId);

                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials + '"' + CurrentEntity.InfraObjectName + '"', TransactionType.Delete));

            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                returnUrl = Request.RawUrl;

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                returnUrl = Request.RawUrl;

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                    ExceptionManager.Manage(customEx, this);
                }
            }
            if (returnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(returnUrl));
            }
        }
        protected void BtnSaveClick(object sender, EventArgs e)
        {

            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("BusinessProcess", UserActionType.CreateBusinessProcess))
            {

                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {

                    var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }
                    var submitButton = (Button)sender;
                    var buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }

                    try
                    {
                        if (currentTransactionType != TransactionType.Undefined && ValidateRequest("BusinessProcess", UserActionType.CreateBusinessProcess))
                        {
                            BuildEntities();
                            StartTransaction();
                            SaveEditor();
                            EndTransaction();
                        }
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials + " " + '"' + CurrentEntity.InfraObjectName + '"',
                                                                                                    currentTransactionType));
                        btnSave.Enabled = false;
                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, this);
                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, this);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, this);
                        }
                    }
                    if (returnUrl.IsNotNullOrEmpty())
                    {
                        Helper.Url.Redirect(new SecureUrl(returnUrl));
                    }
                }
            }
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.BusinessProcessAuto.BusinessProcessList);
        }

        protected void DdlBPhostnameSelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (ddlBPhostname.SelectedItem.Text == "Select HostName")
                {
                    lblBg.Text = "Select  Host Name";
                    txtPRIp.Text = string.Empty;
                }
                else
                {
                    var dict = (Dictionary<int, string>)HttpContext.Current.Session["IP"];
                    string pvalue = dict[Convert.ToInt32(ddlBPhostname.SelectedValue)];
                    txtPRIp.Text = pvalue;
                    lblBg.Text = string.Empty;
                }
            }
            catch (CpException bcms)
            {
                ExceptionManager.Manage(bcms);
            }
        }

        protected void DdlscripthostTextChanged(object sender, EventArgs e)
        {
            try
            {
                if (ddlscripthost.SelectedItem.Text == "Select HostName")
                {
                    lblSg.Text = "Select Host Name";
                    txtDRIp.Text = string.Empty;
                }
                else
                {
                    var dict = (Dictionary<int, string>)HttpContext.Current.Session["IP"];
                    string pvalue = dict[Convert.ToInt32(ddlscripthost.SelectedValue)];
                    txtDRIp.Text = pvalue;
                    lblSg.Text = string.Empty;
                }
            }
            catch (CpException bcms)
            {
                ExceptionManager.Manage(bcms);
            }
        }

        protected void LnlACmpClick(object sender, EventArgs e)
        {
            string param = lnlACmp.Text;
            if (param == "Compress Files")
            {
                txtsourcedir.Visible = true;
                txtpath.Visible = true;
                txtdestdir.Visible = false;
                lbldestdir.Visible = false;
            }

            CheckBprocess(param);
            ddlBPType.SelectedIndex = 1;
        }

        protected void LnkAuCmpClick(object sender, EventArgs e)
        {
            string param1 = lnkAUCmp.Text;
            if (param1 == "UnCompress Files")
            {
                txtsourcedir.Visible = true;
                txtpath.Visible = true;
                txtdestdir.Visible = false;
                lbldestdir.Visible = false;
            }

            CheckBprocess(param1);
            ddlBPType.SelectedIndex = 2;
        }

        protected void LnlADlteClick(object sender, EventArgs e)
        {
            string param2 = lnlADlte.Text;
            if (param2 == "Delete Files")
            {
                txtsourcedir.Visible = true;
                txtpath.Visible = true;
                txtdestdir.Visible = false;
                lbldestdir.Visible = false;
            }

            CheckBprocess(param2);
            ddlBPType.SelectedIndex = 3;
        }

        protected void LnkAMveClick(object sender, EventArgs e)
        {
            string param3 = lnkAMve.Text;
            if (param3 == "Move Files")
            {
                txtsourcedir.Visible = true;
                txtpath.Visible = true;
                txtdestdir.Visible = true;
                lbldestdir.Visible = true;
            }
            CheckBprocess(param3);
            ddlBPType.SelectedIndex = 4;
        }

        protected void LnkACpyClick(object sender, EventArgs e)
        {
            string param4 = lnkACpy.Text;
            if (param4 == "Copy Files")
            {
                txtsourcedir.Visible = true;
                txtpath.Visible = true;
                txtdestdir.Visible = true;
                lbldestdir.Visible = true;
            }
            CheckBprocess(param4);
            ddlBPType.SelectedIndex = 5;
        }

        public override void BuildEntities()
        {
            CurrentEntity.InfraObjectName = ddlgrouplst.SelectedItem.Text;
            CurrentEntity.BPComponent = ddlBPType.SelectedItem.Text;
            CurrentEntity.PRHostId = Convert.ToInt32(ddlBPhostname.SelectedValue);
            CurrentEntity.DRHostId = Convert.ToInt32(ddlscripthost.SelectedValue);
            CurrentEntity.SourceDir = txtsourcedir.Text;
            CurrentEntity.DestinationDir = txtdestdir.Text;
            CurrentEntity.ScriptPath = txtpath.Text;
            CurrentEntity.PRIPAddress = txtPRIp.Text;
            CurrentEntity.DRIPAddress = txtDRIp.Text;
            CurrentEntity.Id = Convert.ToInt32(hiddenid.Value);
        }

        public override void PrepareEditView()
        {
            throw new NotImplementedException();
        }

        public override void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.CreatorId = LoggedInUserId;
                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddBusinessProcess(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "BusinessProcess", UserActionType.CreateBusinessProcess, "The BusinessProcess '" + CurrentEntity.GroupName + "' type '" + CurrentEntity.BPComponent + "' was Added to the Business Process", LoggedInUserId);
            }
            else
            {
                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateBusinessProcess(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUserName, "BusinessProcess", UserActionType.UpdateBusinessProcess, "The BusinessProcess '" + CurrentEntity.GroupName + "' type '" + CurrentEntity.BPComponent + "' was Updated to the Business Process", LoggedInUserId);
            }
        }

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((ViewState["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request


        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtCountryCode");
                //IgnoreIDs.Add("txtCountryCode2");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

    }
}