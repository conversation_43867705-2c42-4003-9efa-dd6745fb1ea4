﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class BIAAlertCountBusinessServiceWiseDataAccess : BaseDataAccess, IBIAAlertCountBusinessServiceWiseDataAccess
    {
        #region Constructors

        public BIAAlertCountBusinessServiceWiseDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<BIAAlertCountBusinessServiceWise> CreateEntityBuilder<BIAAlertCountBusinessServiceWise>()
        {
            return (new BIAAlertCountBusinessServiceWiseBuilder()) as IEntityBuilder<BIAAlertCountBusinessServiceWise>;
        }

        #endregion

        #region Methods

        IList<BIAAlertCountBusinessServiceWise> IBIAAlertCountBusinessServiceWiseDataAccess.GetAlertCountBusinessServiceWise()
        {
            try
            {
                const string sp = "BIAALERTCOUNTPERBSERVICE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BIAAlertCountBusinessServiceWise>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBIAAlertCountBusinessServiceWiseDataAccess.GetAlertCountBusinessServiceWise()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BIAAlertCountBusinessServiceWise> IBIAAlertCountBusinessServiceWiseDataAccess.GetAlertCountBusinessFunctionWise()
        {
            try
            {
                const string sp = "BIAALERTCOUNTPERBFUNCTION";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BIAAlertCountBusinessServiceWise>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBIAAlertCountBusinessServiceWiseDataAccess.GetAlertCountBusinessFunctionWise()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BIAAlertCountBusinessServiceWise> IBIAAlertCountBusinessServiceWiseDataAccess.GetAlertCountComponentWise()
        {
            try
            {
                const string sp = "BIAALERTCOUNTCOMPWISE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BIAAlertCountBusinessServiceWise>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBIAAlertCountBusinessServiceWiseDataAccess.GetAlertCountComponentWise()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BIAAlertCountBusinessServiceWise> IBIAAlertCountBusinessServiceWiseDataAccess.GetAlertCountCategoryWise()
        {
            try
            {
                const string sp = "BIAALETCOUNTPERCATEGORYWISE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BIAAlertCountBusinessServiceWise>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBIAAlertCountBusinessServiceWiseDataAccess.GetAlertCountCategoryWise()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion
    }
}
