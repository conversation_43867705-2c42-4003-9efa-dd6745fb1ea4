﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class ExchangeDAGServiceMonitoringBuilder : IEntityBuilder<ExchangeDAGServiceMonitoring>
    {
        IList<ExchangeDAGServiceMonitoring> IEntityBuilder<ExchangeDAGServiceMonitoring>.BuildEntities(
            IDataReader reader)
        {
            var exchangeDAGServiceMonitoring = new List<ExchangeDAGServiceMonitoring>();

            while (reader.Read())
            {
                exchangeDAGServiceMonitoring.Add(
                    ((IEntityBuilder<ExchangeDAGServiceMonitoring>)this).BuildEntity(reader,
                        new ExchangeDAGServiceMonitoring()));
            }

            return (exchangeDAGServiceMonitoring.Count > 0) ? exchangeDAGServiceMonitoring : null;
        }

        ExchangeDAGServiceMonitoring IEntityBuilder<ExchangeDAGServiceMonitoring>.BuildEntity(IDataReader reader,
            ExchangeDAGServiceMonitoring exchangeDAGServiceMonitoring)
        {
            exchangeDAGServiceMonitoring.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            exchangeDAGServiceMonitoring.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"])
                ? 0
                : Convert.ToInt32(reader["InfraObjectId"]);

            exchangeDAGServiceMonitoring.ActiveDirectoryTopologyPRMode =
                Convert.IsDBNull(reader["ActiveDirectoryTopology_PR_Mode"])
                    ? string.Empty
                    : reader["ActiveDirectoryTopology_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ActiveDirectoryTopologyPRStatus =
                Convert.IsDBNull(reader["ActiveDirectoryTopology_PR_Status"])
                    ? string.Empty
                    : reader["ActiveDirectoryTopology_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.ActiveDirectoryTopologyDRMode =
                Convert.IsDBNull(reader["ActiveDirectoryTopology_DR_Mode"])
                    ? string.Empty
                    : reader["ActiveDirectoryTopology_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ActiveDirectoryTopologyDRStatus =
                Convert.IsDBNull(reader["ActiveDirectoryTopology_DR_Status"])
                    ? string.Empty
                    : reader["ActiveDirectoryTopology_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.AddressBookPRMode = Convert.IsDBNull(reader["AddressBook_PR_Mode"])
                ? string.Empty
                : reader["AddressBook_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.AddressBookPRStatus = Convert.IsDBNull(reader["AddressBook_PR_Status"])
                ? string.Empty
                : reader["AddressBook_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.AddressBookDRMode = Convert.IsDBNull(reader["AddressBook_DR_Mode"])
                ? string.Empty
                : reader["AddressBook_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.AddressBookDRStatus = Convert.IsDBNull(reader["AddressBook_DR_Status"])
                ? string.Empty
                : reader["AddressBook_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.AntispamUpdatePRMode = Convert.IsDBNull(reader["AntispamUpdate_PR_Mode"])
                ? string.Empty
                : reader["AntispamUpdate_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.AntispamUpdatePRStatus = Convert.IsDBNull(reader["AntispamUpdate_PR_Status"])
                ? string.Empty
                : reader["AntispamUpdate_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.AntispamUpdateDRMode = Convert.IsDBNull(reader["AntispamUpdate_DR_Mode"])
                ? string.Empty
                : reader["AntispamUpdate_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.AntispamUpdateDRStatus = Convert.IsDBNull(reader["AntispamUpdate_DR_Status"])
                ? string.Empty
                : reader["AntispamUpdate_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.EdgeSyncPRMode = Convert.IsDBNull(reader["EdgeSync_PR_Mode"])
                ? string.Empty
                : reader["EdgeSync_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.EdgeSyncPRStatus = Convert.IsDBNull(reader["EdgeSync_PR_Status"])
                ? string.Empty
                : reader["EdgeSync_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.EdgeSyncDRMode = Convert.IsDBNull(reader["EdgeSync_DR_Mode"])
                ? string.Empty
                : reader["EdgeSync_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.EdgeSyncDRStatus = Convert.IsDBNull(reader["EdgeSync_DR_Status"])
                ? string.Empty
                : reader["EdgeSync_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.FileDistributionPRMode = Convert.IsDBNull(reader["FileDistribution_PR_Mode"])
                ? string.Empty
                : reader["FileDistribution_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.FileDistributionPRStatus =
                Convert.IsDBNull(reader["FileDistribution_PR_Status"])
                    ? string.Empty
                    : reader["FileDistribution_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.FileDistributionDRMode = Convert.IsDBNull(reader["FileDistribution_DR_Mode"])
                ? string.Empty
                : reader["FileDistribution_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.FileDistributionDRStatus =
                Convert.IsDBNull(reader["FileDistribution_DR_Status"])
                    ? string.Empty
                    : reader["FileDistribution_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.AuthenticationServicePRMode =
                Convert.IsDBNull(reader["AuthenticationService_PR_Mode"])
                    ? string.Empty
                    : reader["AuthenticationService_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.AuthenticationServicePRStatus =
                Convert.IsDBNull(reader["AuthenticationService_PR_Status"])
                    ? string.Empty
                    : reader["AuthenticationService_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.AuthenticationServiceDRMode =
                Convert.IsDBNull(reader["AuthenticationService_DR_Mode"])
                    ? string.Empty
                    : reader["AuthenticationService_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.AuthenticationServiceDRStatus =
                Convert.IsDBNull(reader["AuthenticationService_DR_Status"])
                    ? string.Empty
                    : reader["AuthenticationService_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.Imap4PRMode = Convert.IsDBNull(reader["IMAP4_PR_Mode"])
                ? string.Empty
                : reader["IMAP4_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.Imap4PRStatus = Convert.IsDBNull(reader["IMAP4_PR_Status"])
                ? string.Empty
                : reader["IMAP4_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.Imap4DRMode = Convert.IsDBNull(reader["IMAP4_DR_Mode"])
                ? string.Empty
                : reader["IMAP4_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.Imap4DRStatus = Convert.IsDBNull(reader["IMAP4_DR_Status"])
                ? string.Empty
                : reader["IMAP4_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.InformationStorePRMode = Convert.IsDBNull(reader["InformationStore_PR_Mode"])
                ? string.Empty
                : reader["InformationStore_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.InformationStorePRStatus =
                Convert.IsDBNull(reader["InformationStore_PR_Status"])
                    ? string.Empty
                    : reader["InformationStore_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.InformationStoreDRMode = Convert.IsDBNull(reader["InformationStore_DR_Mode"])
                ? string.Empty
                : reader["InformationStore_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.InformationStoreDRStatus =
                Convert.IsDBNull(reader["InformationStore_DR_Status"])
                    ? string.Empty
                    : reader["InformationStore_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.MailSubmissionPRMode = Convert.IsDBNull(reader["MailSubmission_PR_Mode"])
                ? string.Empty
                : reader["MailSubmission_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.MailSubmissionPRStatus = Convert.IsDBNull(reader["MailSubmission_PR_Status"])
                ? string.Empty
                : reader["MailSubmission_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.MailSubmissionDRMode = Convert.IsDBNull(reader["MailSubmission_DR_Mode"])
                ? string.Empty
                : reader["MailSubmission_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.MailSubmissionDRStatus = Convert.IsDBNull(reader["MailSubmission_DR_Status"])
                ? string.Empty
                : reader["MailSubmission_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.MailboxAssistantsPRMode = Convert.IsDBNull(reader["MailboxAssistants_PR_Mode"])
                ? string.Empty
                : reader["MailboxAssistants_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.MailboxAssistantsPRStatus =
                Convert.IsDBNull(reader["MailboxAssistants_PR_Status"])
                    ? string.Empty
                    : reader["MailboxAssistants_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.MailboxAssistantsDRMode = Convert.IsDBNull(reader["MailboxAssistants_DR_Mode"])
                ? string.Empty
                : reader["MailboxAssistants_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.MailboxAssistantsDRStatus =
                Convert.IsDBNull(reader["MailboxAssistants_DR_Status"])
                    ? string.Empty
                    : reader["MailboxAssistants_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.MailboxReplicationPRMode =
                Convert.IsDBNull(reader["MailboxReplication_PR_Mode"])
                    ? string.Empty
                    : reader["MailboxReplication_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.MailboxReplicationPRStatus =
                Convert.IsDBNull(reader["MailboxReplication_PR_Status"])
                    ? string.Empty
                    : reader["MailboxReplication_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.MailboxReplicationDRMode =
                Convert.IsDBNull(reader["MailboxReplication_DR_Mode"])
                    ? string.Empty
                    : reader["MailboxReplication_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.MailboxReplicationDRStatus =
                Convert.IsDBNull(reader["MailboxReplication_DR_Status"])
                    ? string.Empty
                    : reader["MailboxReplication_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.MonitoringPRMode = Convert.IsDBNull(reader["Monitoring_PR_Mode"])
                ? string.Empty
                : reader["Monitoring_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.MonitoringPRStatus = Convert.IsDBNull(reader["Monitoring_PR_Status"])
                ? string.Empty
                : reader["Monitoring_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.MonitoringDRMode = Convert.IsDBNull(reader["Monitoring_DR_Mode"])
                ? string.Empty
                : reader["Monitoring_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.MonitoringDRStatus = Convert.IsDBNull(reader["Monitoring_DR_Status"])
                ? string.Empty
                : reader["Monitoring_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.Pop3PRMode = Convert.IsDBNull(reader["POP3_PR_Mode"])
                ? string.Empty
                : reader["POP3_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.Pop3PRStatus = Convert.IsDBNull(reader["POP3_PR_Status"])
                ? string.Empty
                : reader["POP3_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.Pop3DRMode = Convert.IsDBNull(reader["POP3_DR_Mode"])
                ? string.Empty
                : reader["POP3_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.Pop3DRStatus = Convert.IsDBNull(reader["POP3_DR_Status"])
                ? string.Empty
                : reader["POP3_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.ProtectedServiceHostPRMode =
                Convert.IsDBNull(reader["ProtectedServiceHost_PR_Mode"])
                    ? string.Empty
                    : reader["ProtectedServiceHost_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ProtectedServiceHostPRStatus =
                Convert.IsDBNull(reader["ProtectedServiceHost_PR_Status"])
                    ? string.Empty
                    : reader["ProtectedServiceHost_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.ProtectedServiceHostDRMode =
                Convert.IsDBNull(reader["ProtectedServiceHost_DR_Mode"])
                    ? string.Empty
                    : reader["ProtectedServiceHost_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ProtectedServiceHostDRStatus =
                Convert.IsDBNull(reader["ProtectedServiceHost_DR_Status"])
                    ? string.Empty
                    : reader["ProtectedServiceHost_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.ReplicationPRMode = Convert.IsDBNull(reader["Replication_PR_Mode"])
                ? string.Empty
                : reader["Replication_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ReplicationPRStatus = Convert.IsDBNull(reader["Replication_PR_Status"])
                ? string.Empty
                : reader["Replication_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.ReplicationDRMode = Convert.IsDBNull(reader["Replication_DR_Mode"])
                ? string.Empty
                : reader["Replication_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ReplicationDRStatus = Convert.IsDBNull(reader["Replication_DR_Status"])
                ? string.Empty
                : reader["Replication_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.RpcClientAccessPRMode = Convert.IsDBNull(reader["RPCClientAccess_PR_Mode"])
                ? string.Empty
                : reader["RPCClientAccess_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.RpcClientAccessPRStatus = Convert.IsDBNull(reader["RPCClientAccess_PR_Status"])
                ? string.Empty
                : reader["RPCClientAccess_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.RpcClientAccessDRMode = Convert.IsDBNull(reader["RPCClientAccess_DR_Mode"])
                ? string.Empty
                : reader["RPCClientAccess_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.RpcClientAccessDRStatus = Convert.IsDBNull(reader["RPCClientAccess_DR_Status"])
                ? string.Empty
                : reader["RPCClientAccess_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.SearchIndexerPRMode = Convert.IsDBNull(reader["SearchIndexer_PR_Mode"])
                ? string.Empty
                : reader["SearchIndexer_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.SearchIndexerPRStatus = Convert.IsDBNull(reader["SearchIndexer_PR_Status"])
                ? string.Empty
                : reader["SearchIndexer_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.SearchIndexerDRMode = Convert.IsDBNull(reader["SearchIndexer_DR_Mode"])
                ? string.Empty
                : reader["SearchIndexer_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.SearchIndexerDRStatus = Convert.IsDBNull(reader["SearchIndexer_DR_Status"])
                ? string.Empty
                : reader["SearchIndexer_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.WindowsServerBackupPRMode =
                Convert.IsDBNull(reader["WindowsServerBackup_PR_Mode"])
                    ? string.Empty
                    : reader["WindowsServerBackup_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.WindowsServerBackupPRStatus =
                Convert.IsDBNull(reader["WindowsServerBackup_PR_Status"])
                    ? string.Empty
                    : reader["WindowsServerBackup_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.WindowsServerBackupDRMode =
                Convert.IsDBNull(reader["WindowsServerBackup_DR_Mode"])
                    ? string.Empty
                    : reader["WindowsServerBackup_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.WindowsServerBackupDRStatus =
                Convert.IsDBNull(reader["WindowsServerBackup_DR_Status"])
                    ? string.Empty
                    : reader["WindowsServerBackup_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.ServiceHostPRMode = Convert.IsDBNull(reader["ServiceHost_PR_Mode"])
                ? string.Empty
                : reader["ServiceHost_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ServiceHostPRStatus = Convert.IsDBNull(reader["ServiceHost_PR_Status"])
                ? string.Empty
                : reader["ServiceHost_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.ServiceHostDRMode = Convert.IsDBNull(reader["ServiceHost_DR_Mode"])
                ? string.Empty
                : reader["ServiceHost_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ServiceHostDRStatus = Convert.IsDBNull(reader["ServiceHost_DR_Status"])
                ? string.Empty
                : reader["ServiceHost_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.SystemAttendantPRMode = Convert.IsDBNull(reader["SystemAttendant_PR_Mode"])
                ? string.Empty
                : reader["SystemAttendant_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.SystemAttendantPRStatus = Convert.IsDBNull(reader["SystemAttendant_PR_Status"])
                ? string.Empty
                : reader["SystemAttendant_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.SystemAttendantDRMode = Convert.IsDBNull(reader["SystemAttendant_DR_Mode"])
                ? string.Empty
                : reader["SystemAttendant_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.SystemAttendantDRStatus = Convert.IsDBNull(reader["SystemAttendant_DR_Status"])
                ? string.Empty
                : reader["SystemAttendant_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.ThrottlingPRMode = Convert.IsDBNull(reader["Throttling_PR_Mode"])
                ? string.Empty
                : reader["Throttling_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ThrottlingPRStatus = Convert.IsDBNull(reader["Throttling_PR_Status"])
                ? string.Empty
                : reader["Throttling_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.ThrottlingDRMode = Convert.IsDBNull(reader["Throttling_DR_Mode"])
                ? string.Empty
                : reader["Throttling_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ThrottlingDRStatus = Convert.IsDBNull(reader["Throttling_DR_Status"])
                ? string.Empty
                : reader["Throttling_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.TransportPRMode = Convert.IsDBNull(reader["Transport_PR_Mode"])
                ? string.Empty
                : reader["Transport_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.TransportPRStatus = Convert.IsDBNull(reader["Transport_PR_Status"])
                ? string.Empty
                : reader["Transport_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.TransportDRMode = Convert.IsDBNull(reader["Transport_DR_Mode"])
                ? string.Empty
                : reader["Transport_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.TransportDRStatus = Convert.IsDBNull(reader["Transport_DR_Status"])
                ? string.Empty
                : reader["Transport_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.TransportLogSearchPRMode =
                Convert.IsDBNull(reader["TransportLogSearch_PR_Mode"])
                    ? string.Empty
                    : reader["TransportLogSearch_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.TransportLogSearchPRStatus =
                Convert.IsDBNull(reader["TransportLogSearch_PR_Status"])
                    ? string.Empty
                    : reader["TransportLogSearch_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.TransportLogSearchDRMode =
                Convert.IsDBNull(reader["TransportLogSearch_DR_Mode"])
                    ? string.Empty
                    : reader["TransportLogSearch_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.TransportLogSearchDRStatus =
                Convert.IsDBNull(reader["TransportLogSearch_DR_Status"])
                    ? string.Empty
                    : reader["TransportLogSearch_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.ClusterServicePRMode = Convert.IsDBNull(reader["ClusterService_PR_Mode"])
                ? string.Empty
                : reader["ClusterService_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ClusterServicePRStatus = Convert.IsDBNull(reader["ClusterService_PR_Status"])
                ? string.Empty
                : reader["ClusterService_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.ClusterServiceDRMode = Convert.IsDBNull(reader["ClusterService_DR_Mode"])
                ? string.Empty
                : reader["ClusterService_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.ClusterServiceDRStatus = Convert.IsDBNull(reader["ClusterService_DR_Status"])
                ? string.Empty
                : reader["ClusterService_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.WindowsFirewallPRMode = Convert.IsDBNull(reader["WindowsFirewall_PR_Mode"])
                ? string.Empty
                : reader["WindowsFirewall_PR_Mode"].ToString();
            exchangeDAGServiceMonitoring.WindowsFirewallPRStatus = Convert.IsDBNull(reader["WindowsFirewall_PR_Status"])
                ? string.Empty
                : reader["WindowsFirewall_PR_Status"].ToString();
            exchangeDAGServiceMonitoring.WindowsFirewallDRMode = Convert.IsDBNull(reader["WindowsFirewall_DR_Mode"])
                ? string.Empty
                : reader["WindowsFirewall_DR_Mode"].ToString();
            exchangeDAGServiceMonitoring.WindowsFirewallDRStatus = Convert.IsDBNull(reader["WindowsFirewall_DR_Status"])
                ? string.Empty
                : reader["WindowsFirewall_DR_Status"].ToString();

            exchangeDAGServiceMonitoring.IsActive = Convert.IsDBNull(reader["IsActive"])
                ? 0
                : Convert.ToInt32(reader["IsActive"]);
            exchangeDAGServiceMonitoring.CreatorId = Convert.IsDBNull(reader["CreatorId"])
                ? 0
                : Convert.ToInt32(reader["CreatorId"]);
            exchangeDAGServiceMonitoring.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);
            exchangeDAGServiceMonitoring.UpdatorId = Convert.IsDBNull(reader["UpdatorId"])
                ? 0
                : Convert.ToInt32(reader["UpdatorId"]);
            exchangeDAGServiceMonitoring.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);

            return exchangeDAGServiceMonitoring;
        }
    }
}