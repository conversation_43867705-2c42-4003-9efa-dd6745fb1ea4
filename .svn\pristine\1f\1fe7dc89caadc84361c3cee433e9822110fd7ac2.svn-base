﻿using CP.ExceptionHandler;
using CP.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class goldengatedetails : BaseControl //System.Web.UI.UserControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        public override void PrepareView()
        {
            int infraid = Convert.ToInt32(Session["GoldenGateInfraid"]);
            GoldenGateDetails(infraid);
        }
        public void GoldenGateDetails(int id)
        {
            try
            {
                var InfraDet = Facade.GetInfraObjectById(id);
                if (InfraDet != null)
                {
                    var prServerDet = Facade.GetServerById(InfraDet.PRServerId);
                    var drServerDet = Facade.GetServerById(InfraDet.DRServerId);
                    if (prServerDet != null || drServerDet != null)
                    {
                        Label75.Text = prServerDet.Name;
                        Label77.Text = prServerDet.Name;
                        Label79.Text = CryptographyHelper.Md5Decrypt(prServerDet.IPAddress);
                        Label81.Text = CryptographyHelper.Md5Decrypt(drServerDet.IPAddress);
                    }
                    else
                    {
                        Label75.Text = "NA";
                        Label77.Text = "NA";
                        Label79.Text = "NA";
                        Label81.Text = "NA";
                    }
                    switch (prServerDet.OSType)
                    {
                        case "AIX":
                            Label74.Attributes.Add("Class", "icon-aix");
                            break;

                        case "HPUX":
                            Label74.Attributes.Add("Class", "icon-HPUX");
                            break;

                        case "Linux":
                            Label74.Attributes.Add("Class", "icon-linux");
                            break;

                        case "Solaris":
                            Label74.Attributes.Add("Class", "icon-Solaris");
                            break;

                        case "Windows2003":
                            Label74.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2008":
                            Label74.Attributes.Add("Class", "icon-Windows");
                            break;
                        case "Windows2012":
                            Label74.Attributes.Add("Class", "icon-Windows");
                            break;
                    }

                    switch (drServerDet.OSType)
                    {
                        case "AIX":
                            Label76.Attributes.Add("Class", "icon-aix");
                            break;

                        case "HPUX":
                            Label76.Attributes.Add("Class", "icon-HPUX");
                            break;

                        case "Linux":
                            Label76.Attributes.Add("Class", "icon-linux");
                            break;

                        case "Solaris":
                            Label76.Attributes.Add("Class", "icon-Solaris");
                            break;

                        case "Windows2003":
                            Label76.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2008":
                            Label76.Attributes.Add("Class", "icon-Windows");
                            break;
                        case "Windows2012":
                            Label76.Attributes.Add("Class", "icon-Windows");
                            break;
                    }

                    switch (prServerDet.Status.ToString())
                    {
                        case "Pending":
                            Label78.Attributes.Add("class", "fatal");
                            break;
                        case "Down":
                            Label78.Attributes.Add("class", "health-down");
                            break;
                        case "Up":
                            Label78.Attributes.Add("class", "health-up");
                            break;
                    }

                    switch (drServerDet.Status.ToString())
                    {
                        case "Pending":
                            Label80.Attributes.Add("class", "fatal");
                            break;
                        case "Down":
                            Label80.Attributes.Add("class", "health-down");
                            break;
                        case "Up":
                            Label80.Attributes.Add("class", "health-up");
                            break;
                    }

                    var GoldenGateDBDetails = Facade.GetGoldenGateDBMonitorByInfraObjectId(id);
                    if (GoldenGateDBDetails != null)
                    {
                        Label82.Text = GoldenGateDBDetails.PRGGVersion;
                        Label83.Text = GoldenGateDBDetails.DRGGVersion;
                        Label1.Text = GoldenGateDBDetails.PRGGHomePath;
                        Label2.Text = GoldenGateDBDetails.DRGGHomePath;
                        Label84.Text = GoldenGateDBDetails.PRGGMgrStatus;
                        Label85.Text = GoldenGateDBDetails.DRGGMgrStatus;
                        Label3.Text = GoldenGateDBDetails.PRGGSchema;
                        Label4.Text = GoldenGateDBDetails.DRGGSchema;
                        Label5.Text = GoldenGateDBDetails.PRCheckPointTable;
                        Label6.Text = GoldenGateDBDetails.DRCheckPointTable;

                      
                        if (GoldenGateDBDetails.PRGGMgrStatus.IndexOf("Running", StringComparison.OrdinalIgnoreCase)>-1)
                        {
                            spanPrStatus.Attributes.Add("class", "Replicating");
                        }
                        if (GoldenGateDBDetails.PRGGMgrStatus.IndexOf("Abended", StringComparison.OrdinalIgnoreCase)>-1 || GoldenGateDBDetails.PRGGMgrStatus.IndexOf("Down", StringComparison.OrdinalIgnoreCase)>-1 || GoldenGateDBDetails.PRGGMgrStatus.IndexOf("Stop", StringComparison.OrdinalIgnoreCase)>-1)
                        {
                            spanPrStatus.Attributes.Add("class", "stopserviceicon");
                        }

                       if (GoldenGateDBDetails.DRGGMgrStatus.IndexOf("Running", StringComparison.OrdinalIgnoreCase)>-1)
                        {
                            spandrStatus.Attributes.Add("class", "Replicating");
                        }
                         if (GoldenGateDBDetails.DRGGMgrStatus.IndexOf("Abended", StringComparison.OrdinalIgnoreCase)>-1 || GoldenGateDBDetails.DRGGMgrStatus.IndexOf("Down", StringComparison.OrdinalIgnoreCase)>-1 || GoldenGateDBDetails.DRGGMgrStatus.IndexOf("Stop", StringComparison.OrdinalIgnoreCase)>-1)
                        {
                            spandrStatus.Attributes.Add("class", "stopserviceicon");
                        }

                    }
                    else
                    {
                        Label82.Text = "NA";
                        Label83.Text = "NA";
                        Label1.Text = "NA";
                        Label2.Text = "NA";
                        Label84.Text = "NA";
                        Label85.Text = "NA";
                        Label3.Text = "NA";
                        Label4.Text = "NA";
                        Label5.Text = "NA";
                        Label6.Text = "NA";


                    }

                }
            }

            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Golden Gate", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

    }
}
