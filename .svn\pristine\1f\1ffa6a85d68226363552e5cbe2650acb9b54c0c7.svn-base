﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{

    internal sealed class DiscoveryConfigurationDataAccess : BaseDataAccess, IDiscoveryConfigurationDataAccess
    {

        #region Constructors

        public DiscoveryConfigurationDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<DiscoveryConfiguration> CreateEntityBuilder<DiscoveryConfiguration>()
        {
            return (new DiscoveryConfigurationBuilder()) as IEntityBuilder<DiscoveryConfiguration>;
        }

        #endregion

        #region Methods
        /// <summary>
        /// Create <see cref="DiscoveryConfiguration" /> into discovery_configuration table.
        /// </summary>
        /// <param name="objDiscoveryConfiguration"></param>
        /// <returns></returns>
        DiscoveryConfiguration IDiscoveryConfigurationDataAccess.Add(DiscoveryConfiguration objDiscoveryConfiguration)
        {
            try
            {
                const string sp = "DiscoveryConfiguration_Create";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {                    
                    Database.AddInParameter(cmd, Dbstring+"iIPRangeFrom", DbType.String, objDiscoveryConfiguration.IPRangeFrom);
                    Database.AddInParameter(cmd, Dbstring+"iIPRangeTo", DbType.String, objDiscoveryConfiguration.IPRangeTo);
                    Database.AddInParameter(cmd, Dbstring+"iCreatorId", DbType.Int32, objDiscoveryConfiguration.CreatorId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_discovery_configuration"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        objDiscoveryConfiguration = reader.Read()
                            ? CreateEntityBuilder<DiscoveryConfiguration>().BuildEntity(reader, objDiscoveryConfiguration)
                            : null;
                    }

                    if (objDiscoveryConfiguration == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DiscoveryConfiguration already exists. Please specify another application.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this DiscoveryConfiguration.");
                                }
                        }
                    }

                    return objDiscoveryConfiguration;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting DiscoveryConfiguration Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        /// Create <see cref="DiscoveryConfiguration" /> into discovery_configuration table.
        /// </summary>
        /// <param name="objDiscoveryConfiguration"></param>
        /// <returns></returns>
        DiscoveryConfiguration IDiscoveryConfigurationDataAccess.Update(DiscoveryConfiguration objDiscoveryConfiguration)
        {
            try
            {
                const string sp = "DiscoveryConfiguration_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                  //  AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, objDiscoveryConfiguration.Id);
                    Database.AddInParameter(cmd, Dbstring+"iIPRangeFrom", DbType.String, objDiscoveryConfiguration.IPRangeFrom);
                    Database.AddInParameter(cmd, Dbstring+"iIPRangeTo", DbType.String, objDiscoveryConfiguration.IPRangeTo);
                    Database.AddInParameter(cmd, Dbstring+"iUpdatorId", DbType.Int32, objDiscoveryConfiguration.UpdatorId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_discovery_configuration"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        objDiscoveryConfiguration = reader.Read()
                            ? CreateEntityBuilder<DiscoveryConfiguration>().BuildEntity(reader, objDiscoveryConfiguration)
                            : null;
                    }

                    if (objDiscoveryConfiguration == null)
                    {
                       // int returnCode = GetReturnCodeFromParameter(cmd);

                        //switch (returnCode)
                        //{
                        //    case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                        //        {
                        //            throw new ArgumentException(
                        //                "DiscoveryConfiguration already exists. Please specify another DiscoveryConfiguration.");
                        //        }
                        //    default:
                        //        {
                        //            throw new SystemException(
                        //                "An unexpected error has occurred while updating this DiscoveryConfiguration.");
                        //        }
                        //}
                    }

                    return objDiscoveryConfiguration;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating DiscoveryConfiguration Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }


        /// <summary>
        /// Create <see cref="DiscoveryConfiguration" /> into discovery_configuration table.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        DiscoveryConfiguration IDiscoveryConfigurationDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "DiscoveryConfiguration_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_discovery_configuration"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DiscoveryConfiguration>()).BuildEntity(reader, new DiscoveryConfiguration())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDiscoveryConfigurationDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        #endregion
    }

}

