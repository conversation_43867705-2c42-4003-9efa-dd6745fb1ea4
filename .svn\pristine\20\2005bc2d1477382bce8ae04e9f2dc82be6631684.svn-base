﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{

    [Serializable]
    [DataContract(Name = "CPLoadConfiguration", Namespace = "http://www.ContinuityPlatform.com/types")]

  public  class CPLoadMasterLogs : BaseEntity
    {

        #region Properties

        [DataMember]
        public string NodeID { get; set; }

        [DataMember]
        public string BServiceID { get; set; }

        [DataMember]
        public string Opeartion { get; set; }


        [DataMember]
        public string ServiceStatus { get; set; }


        #endregion Properties
    }
}
