﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;

namespace CP.DataAccess
{
    internal sealed class ApprovalBuilder : IEntityBuilder<CP.Common.DatabaseEntity.Approval>
    {
        IList<CP.Common.DatabaseEntity.Approval> IEntityBuilder<CP.Common.DatabaseEntity.Approval>.BuildEntities(IDataReader reader)
        {
            var approvals = new List<CP.Common.DatabaseEntity.Approval>();

            while (reader.Read())
            {
                approvals.Add(((IEntityBuilder<CP.Common.DatabaseEntity.Approval>)this).BuildEntity(reader, new CP.Common.DatabaseEntity.Approval()));
            }

            return (approvals.Count > 0) ? approvals : null;
        }

        CP.Common.DatabaseEntity.Approval IEntityBuilder<CP.Common.DatabaseEntity.Approval>.BuildEntity(IDataReader reader, CP.Common.DatabaseEntity.Approval approvals)
        {
          
            approvals.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            approvals.ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]);
           // approvals.ApprovalRequire = Convert.IsDBNull(reader["ApprovalRequire"]) ? string.Empty : Convert.ToString(reader["ApprovalRequire"]);
            approvals.ApprovalLevel = Convert.IsDBNull(reader["ApprovalLevel"]) ? string.Empty : Convert.ToString(reader["ApprovalLevel"]);
            approvals.LevelNumber = Convert.IsDBNull(reader["LevelNumber"]) ? 0 : Convert.ToInt32(reader["LevelNumber"]);
            approvals.ApproverEmailId = Convert.IsDBNull(reader["ApproverEmailId"]) ? string.Empty : Convert.ToString(reader["ApproverEmailId"]);
            approvals.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            approvals.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            approvals.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue :Convert.ToDateTime(reader["CreateDate"]);
            approvals.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            approvals.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]);
            approvals.ApproverEmailId = Convert.IsDBNull(reader["ApproverEmailId"]) ? string.Empty : Convert.ToString(reader["ApproverEmailId"]);
            return approvals;
        }

    }
}
