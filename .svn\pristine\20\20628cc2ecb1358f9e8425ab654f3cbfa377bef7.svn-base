﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IExchageDagReplicationDataAccess
    {
        ExchangeDAGReplication Add(ExchangeDAGReplication exchangedagReplication);

        ExchangeDAGReplication Update(ExchangeDAGReplication exchangedagReplication);

        ExchangeDAGReplication UpdateByReplicationId(ExchangeDAGReplication scr);

        ExchangeDAGReplication GetByReplicationId(int id);

        ExchangeDAGReplication GetById(int id);

        ExchangeDAGReplication GetByGroupId(int groupId);

        IList<ExchangeDAGReplication> GetAll();

        ExchangeDAGReplication GetByName(string name);

        IList<ExchangeDAGReplication> GetByCompanyId(int companyId, bool isParent);

        bool DeleteById(int id);

        bool IsExistByName(string name);
        IList<ExchangeDAGReplication> GetByLoginId(int id);
    }
}