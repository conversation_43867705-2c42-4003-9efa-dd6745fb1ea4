﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
   public interface IAodgLogRepliDetailsDataAccess
    {
       IList<AodgRepliLogsDetails> GetAodgReplilogetailsByDateNInfraObjectId(int InfraObjectId, string startDate, string endDate);

       IList<AodgRepliLogsDetails> GetAodgReplilogsHourlyByInfraId(int InfraObjectId);
    }
}
