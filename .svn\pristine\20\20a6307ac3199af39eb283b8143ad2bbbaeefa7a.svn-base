﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="SVCGlobalMirrorORMetroMirrorConfiguration.ascx.cs"
    Inherits="CP.UI.Controls.SVCGlobalMirrorORMetroMirrorConfiguration" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>
<div class="widget-body">
    <table class="table">
        <thead>
            <tr>
                <th style="width: 24%;"></th>
                <th style="width: 37.5% !important;">Production Server
                </th>
                <th style="width: 37.5% !important;">DR Server
                </th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>
                    <label>
                        Server<span class="inactive">*</span>
                    </label>
                </td>
                <td>
                    <asp:DropDownList ID="ddlPrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" data-style="btn-default" OnSelectedIndexChanged="ddlPrServer_SelectedIndexChanged">
                    </asp:DropDownList>

                </td>
                <td>
                    <asp:DropDownList ID="ddlDrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" data-style="btn-default" OnSelectedIndexChanged="ddlDrServer_SelectedIndexChanged">
                    </asp:DropDownList>

                </td>
            </tr>
            <tr>
                <td>
                    <label>
                        IP Address<span class="inactive">*</span>
                    </label>
                </td>
                <td>
                    <asp:TextBox ID="txtPRIPAddress" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtPRIPAddress" runat="server" ErrorMessage=" Enter PR IP Address" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtPRIPAddress"></asp:RequiredFieldValidator>
                </td>
                <td>
                    <asp:TextBox ID="txtDRIPAddress" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtDRIPAddress" runat="server" ErrorMessage=" Enter DR IP Address" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtDRIPAddress"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td>
                    <label>
                        Controller Product ID<span class="inactive">*</span></label>
                </td>
                <td>
                    <asp:TextBox ID="txtPRProductID" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtPRProductID" runat="server" ErrorMessage=" Enter Controller PR Product ID" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtPRProductID"></asp:RequiredFieldValidator>
                </td>
                <td>
                    <asp:TextBox ID="txtDRProductID" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtDRProductID" runat="server" ErrorMessage=" Enter Controller DR Product ID" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtDRProductID"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td>
                    <label>
                        Release<span class="inactive">*</span></label>
                </td>
                <td>
                    <asp:TextBox ID="txtPRRelease" CssClass="form-control" Style="Width: 56% !important" runat="server" Text=""></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtPRRelease" runat="server" ErrorMessage=" Enter PR Release" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtPRRelease"></asp:RequiredFieldValidator>
                </td>
                <td>
                    <asp:TextBox ID="txtDRRelease" CssClass="form-control" Style="Width: 56% !important" runat="server" Text=""></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtDRRelease" runat="server" ErrorMessage=" Enter DR Release" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtDRRelease"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td>
                    <label>
                        Login<span class="inactive">*</span></label>
                </td>
                <td>
                    <asp:TextBox ID="txtPRLogin" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtPRLogin" runat="server" ErrorMessage=" Enter PR Login" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtPRLogin"></asp:RequiredFieldValidator>
                </td>
                <td>
                    <asp:TextBox ID="txtDRLogin" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtDRLogin" runat="server" ErrorMessage=" Enter DR Login" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtDRLogin"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td>
                    <label>
                        ConsistentGroupID<span class="inactive">*</span></label>
                </td>
                <td>
                    <asp:TextBox ID="txtPRConsistentGroupID" Enabled="true" CssClass="form-control" Style="Width: 56% !important" MaxLength="5" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtPRConsistentGroupID" runat="server" ErrorMessage=" Enter PR ConsistentGroupID" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtPRConsistentGroupID"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="RegularExpressionValidator4" runat="server" CssClass="error"
                        ControlToValidate="txtPRConsistentGroupID" Display="Dynamic" ErrorMessage=" Number is required"
                        ValidationExpression="[0-9]{0,5}"></asp:RegularExpressionValidator>

                </td>
                <td>
                    <asp:TextBox ID="txtDRConsistentGroupID" Enabled="true" CssClass="form-control" Style="Width: 56% !important" MaxLength="5" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtDRConsistentGroupID" runat="server" ErrorMessage=" Enter DR ConsistentGroupID" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtDRConsistentGroupID"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="RegularExpressionValidator3" runat="server" CssClass="error"
                        ControlToValidate="txtDRConsistentGroupID" Display="Dynamic" ErrorMessage="Number is required"
                        ValidationExpression="[0-9]{0,5}"></asp:RegularExpressionValidator>
                </td>
            </tr>
            <tr>
                <td>
                    <label>
                        RC_rel_name<span class="inactive">*</span></label>
                </td>
                <td>
                    <asp:TextBox ID="txtPRRC_rel_name" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtPRRC_rel_name" runat="server" ErrorMessage=" Enter PR RC_rel_name" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtPRRC_rel_name"></asp:RequiredFieldValidator>
                </td>
                <td>
                    <asp:TextBox ID="txtDRRC_rel_name" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtDRRC_rel_name" runat="server" ErrorMessage=" Enter DR RC_rel_name" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtDRRC_rel_name"></asp:RequiredFieldValidator>
                </td>
            </tr>
            <tr>
                <td>
                    <label>
                        DiskControllerID<span class="inactive">*</span></label>
                </td>
                <td>
                    <asp:TextBox ID="txtPRDiskControllerID" Enabled="true" CssClass="form-control" Style="Width: 56% !important" MaxLength="5" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtPRDiskControllerID" runat="server" ErrorMessage=" Enter PR DiskControllerID" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtPRDiskControllerID"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="RegularExpressionValidator2" runat="server" CssClass="error"
                        ControlToValidate="txtPRDiskControllerID" Display="Dynamic" ErrorMessage=" Number is required"
                        ValidationExpression="[0-9]{0,5}"></asp:RegularExpressionValidator>

                </td>
                <td>
                    <asp:TextBox ID="txtDRDiskControllerID" Enabled="true" CssClass="form-control" Style="Width: 56% !important" MaxLength="5" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtDRDiskControllerID" runat="server" ErrorMessage=" Enter DR DiskControllerID" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtDRDiskControllerID"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" CssClass="error"
                        ControlToValidate="txtDRDiskControllerID" Display="Dynamic" ErrorMessage="Number is required"
                        ValidationExpression="[0-9]{0,5}"></asp:RegularExpressionValidator>

                </td>
            </tr>
            <tr>
                <td>
                    <label>
                         Node Name<span class="inactive">*</span></label>
                </td>
                <td>
                    <asp:TextBox ID="txtPRNodeName" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtPRNodeName" runat="server" ErrorMessage=" Enter PR Node Name" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtPRNodeName"></asp:RequiredFieldValidator>
                </td>
                <td>
                    <asp:TextBox ID="txtDRNodeName" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtDRNodeName" runat="server" ErrorMessage=" Enter DR Node Name" CssClass="error" Display="Dynamic"
                        ControlToValidate="txtDRNodeName"></asp:RequiredFieldValidator>
                </td>
            </tr>

            <tr id="trdatastorename" runat="server">
                <td style="border-bottom: 1px solid #ddd">
                    <label>DataStore Name</label>
                </td>
                <td style="border-bottom: 1px solid #ddd">
                    <asp:TextBox ID="txtPRDataStorename" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                </td>
                <td style="border-bottom: 1px solid #ddd">
                    <asp:TextBox ID="txtDRDataStorename" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                </td>
            </tr>


        </tbody>
    </table>

    <asp:Panel ID="pnlConVmWarepath" runat="server" class="form-group">
        
        <label class="col-md-3 control-label" style="padding-left: 24px">VmPath Details</label>

        <div class="col-md-7" style="padding-left: 6px; padding-right: 10px;">
            <div class="widget margin-bottom-none">
                <asp:ListView ID="lvVmWarepath" runat="server" OnItemDeleting="lvVmWarepathDeleting"
                    OnItemInserting="lvVmWarepathItemInserting" OnItemEditing="lvVmWarepathEditing"
                    OnItemUpdating="lvVmWarepathUpdating" OnItemCanceling="lvVmWarepathCanceling"
                    InsertItemPosition="LastItem" OnItemDataBound="lvVmWarepath_ItemDataBound">
                    <LayoutTemplate>
                        <table class="table table-bordered margin-bottom-none" style="table-layout: fixed">
                            <thead>
                                <tr>

                                    <th style="width: 30%;">VmName
                                    </th>
                                    <th style="width: 30%;">PRVm(vmx) File Path
                                    </th>
                                    <th style="width: 30%;">DRVm(vmx) File Path
                                    </th>
                                    <th style="width: 12%;">Edit/Delete
                                    </th>
                                </tr>
                            </thead>
                        </table>
                        <div id="subanunth-content">
                            <table class="table table-bordered margin-bottom-none">
                                <tbody>
                                    <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                </tbody>
                            </table>
                        </div>
                    </LayoutTemplate>
                    <ItemTemplate>
                        <tr>
                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                            <td style="width: 29%;">
                                <asp:TextBox ID="txtdisvmwarename" Text='<%# Eval("VmwareName") %>' CssClass="form-control" Style="width: 100%" Enabled="False" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtdisPRvmfilepath" Text='<%# Eval("PRVmFilePath") %>' CssClass="form-control" Style="width: 100%" Enabled="False" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtdisDRvmfilepath" Text='<%# Eval("DRVmFilePath") %>' CssClass="form-control" Style="width: 100%" Enabled="False" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 11%; text-align: center; vertical-align: middle">
                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                    ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" CausesValidation="false" />
                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                    ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" CausesValidation="false" />
                                <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                    ConfirmText='<%# "Are you sure want to delete ? " %>' OnClientCancel="CancelClick">
                                </TK1:ConfirmButtonExtender>
                            </td>
                        </tr>
                    </ItemTemplate>
                    <EditItemTemplate>
                        <tr>
                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                            <td style="width: 29%;">
                                <asp:TextBox ID="txtEditvmwarename" Text='<%# Eval("VmwareName") %>' Style="width: 100%" CssClass="form-control" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtEditPRvmfilepath" Text='<%# Eval("PRVmFilePath") %>' Style="width: 100%" CssClass="form-control" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtEditDRvmfilepath" Text='<%# Eval("DRVmFilePath") %>' Style="width: 100%" CssClass="form-control" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 11%; text-align: center; vertical-align: middle">
                                <asp:LinkButton ID="imgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                    ToolTip="Update" CssClass="health-up" CausesValidation="false" />
                                <asp:LinkButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                    ToolTip="Cancel" CssClass="InActive" CausesValidation="false" />

                            </td>
                        </tr>
                    </EditItemTemplate>
                    <InsertItemTemplate>
                        <tr>
                            <td style="width: 29%;">
                                <asp:TextBox ID="txtvmwarename" Text="" CssClass="form-control" Style="width: 100%" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtPRvmfilename" Text="" CssClass="form-control" Style="width: 100%" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 30%;">
                                <asp:TextBox ID="txtDRvmfilename" Text="" CssClass="form-control" Style="width: 100%" runat="server"> </asp:TextBox>
                            </td>
                            <td style="width: 11%; text-align: center; vertical-align: middle">
                                <asp:LinkButton ID="imgInsert" runat="server" CommandName="Insert" AlternateText="Insert"
                                    ToolTip="Insert" CssClass="plus" ValidationGroup="VmWarepath" />

                            </td>
                        </tr>

                    </InsertItemTemplate>
                </asp:ListView>
            </div>
        </div>
      
        
    </asp:Panel>
    <hr style="margin:0 0 8px" />
    <div class="form-group">
        <div class="col-md-6"></div>
        <div class="col-md-6">
            <div class="text-right">
                <asp:Button ID="btnSave" runat="server" Text="Save" Width="20%" CssClass="btn btn-primary" OnClick="btnSave_Click" />
                <asp:Button ID="btnCancel" runat="server" Text="Cancel" Width="20%" CssClass="btn btn-default" OnClick="btnCancel_Click" />
            </div>
        </div>
    </div>
</div>
