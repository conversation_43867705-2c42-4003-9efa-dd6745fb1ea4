﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class NetworkIPBuilder : IEntityBuilder<NetworkIP>
    {
        IList<NetworkIP> IEntityBuilder<NetworkIP>.BuildEntities(IDataReader reader)
        {
            var networks = new List<NetworkIP>();

            while (reader.Read())
            {
                networks.Add(((IEntityBuilder<NetworkIP>)this).BuildEntity(reader, new NetworkIP()));
            }

            return (networks.Count > 0) ? networks : null;
        }

        NetworkIP IEntityBuilder<NetworkIP>.BuildEntity(IDataReader reader, NetworkIP networkIP)
        {
            //const int FLD_ID = 0;
            //const int FLD_CIRCLE = 1;
            //const int FLD_IP = 2;
            //const int FLD_SWITCH = 3;
            //const int FLD_STATUS = 4;

            //networkIP.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //networkIP.Circle = reader.IsDBNull(FLD_CIRCLE) ? string.Empty : reader.GetString(FLD_CIRCLE);
            //networkIP.IP = reader.IsDBNull(FLD_IP) ? string.Empty : reader.GetString(FLD_IP);
            //networkIP.Switch = reader.IsDBNull(FLD_SWITCH) ? string.Empty : reader.GetString(FLD_SWITCH);
            //networkIP.Status = reader.IsDBNull(FLD_STATUS) ? 0 : reader.GetInt32(FLD_STATUS);

            //Fields in bcms_networkip table on 23/07/2013 : Id, Circle, IP, Switch, Status

            networkIP.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            networkIP.Circle = Convert.IsDBNull(reader["Circle"]) ? string.Empty : Convert.ToString(reader["Circle"]);
            networkIP.IP = Convert.IsDBNull(reader["IP"]) ? string.Empty : Convert.ToString(reader["IP"]);
            networkIP.Switch = Convert.IsDBNull(reader["Switch"]) ? string.Empty : Convert.ToString(reader["Switch"]);
            networkIP.Status = Convert.IsDBNull(reader["Status"]) ? 0 : Convert.ToInt32(reader["Status"]);

            return networkIP;
        }
    }
}