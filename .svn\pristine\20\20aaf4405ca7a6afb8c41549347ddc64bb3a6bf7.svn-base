﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Controls;
using CP.ExceptionHandler;
using System.Web;

namespace CP.UI
{
    public partial class NotificationManager : AlertReceiverBasePageEditor
    {
        private bool _smtpvalidator = true;
        private bool _SMSvalidator = true;

        private static IFacade facade = new Facade();

        private readonly IList<ListItem> _previousSelectedItems = new List<ListItem>();

        public IList<InfraObject> infralist { get; set; }

        public override string MessageInitials
        {
            get { return "Notification Manager"; }
        }

        #region Properties

        public override Label MessageViewer
        {
            get { return lblMessage; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Alert.NotificationManager;
                }
                return string.Empty;
            }
        }

        #endregion Properties

        public static string RedirectURL = Constants.UrlConstants.Urls.Alert.NotificationManager;

        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            //ViewState["_token"] = UrlHelper.AddTokenToRequest();
            //if (ViewState["_token"] != null)
            //{
            //    hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            //}
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 

            textName.Attributes.Add("onblur", "ValidatorValidate(" + rfv_name1.ClientID + ")");
            //smtpHost.Attributes.Add("onblur", "ValidatorValidate(" + rfvsmtphost.ClientID + ")");
            txtPort.Attributes.Add("onblur", "ValidatorValidate(" + rfv_txtport.ClientID + ")");
            fromMail.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            //   txtPassword.Attributes.Add("onblur", "ValidatorValidate(" + rfvpassword.ClientID + ")");
            txtemailbox1.Attributes.Add("onblur", "ValidatorValidate(" + rfvEmail.ClientID + ")");

            Utility.SelectMenu(Master, "Module4");
            BindList();
            if (CurrentAlertReceiverId != 0)
            {
                PrepareEditView();
            }

            SmsBindValue();
            SmtpBindValue();

            //ModalPopupExtender2.Hide();
        }

        private void SmtpBindValue()
        {
            smtpHost.Enabled = true;
            txtPort.Enabled = true;
            fromMail.Enabled = true;
            txtPassword.Enabled = true;
            chkboxEnableSSL.Enabled = true;
            chkbxISbodyHTML.Enabled = true;
            txtPassword.Attributes.Add("value", txtPassword.Text);
            //txtPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(txtPassword.Text), hdfStaticGuid.Value);

            //ModalPopupExtender2.Show();
            //btnedit.Visible = false;
            if (smtpHost.Text == string.Empty && txtPort.Text == string.Empty && txtemailbox1.Text == string.Empty && txtPassword.Text == string.Empty)
            {
                //btnsave.Visible = true;
                btnsave.Text = "Save";
            }
            else
            {
                //btnsave.Visible = true;
                btnsave.Text = "Update";
            }
        }

        private void Retrivedatasmtp()
        {

            var smtpDetails = facade.GetSmtpConfigurations();
            if (smtpDetails != null && smtpDetails.Count > 0)
            {
                //Added by Hanumant 05082018 - for getting smtp details companywise 
                var currentuserdetails = Facade.GetUserById(Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]));
                if (currentuserdetails != null)
                {
                    SmtpConfiguration smtpConf = null;
                    foreach (var smtp in smtpDetails)
                    {
                        var smtpuserdetails = Facade.GetUserById(Convert.ToInt32(smtp.CreatorId));
                        if (smtpuserdetails != null && smtpuserdetails.CompanyId == currentuserdetails.CompanyId)
                        {
                            smtpConf = smtp;
                            smtpId.Value = smtpConf.Id.ToString();
                            smtpHost.Text = CryptographyHelper.Md5Decrypt(smtpConf.SmtpHost);
                            txtPort.Text = smtpConf.Port.ToString();
                            fromMail.Text = CryptographyHelper.Md5Decrypt(smtpConf.UserName);
                            //txtPassword.Text = smtpConf.Password;
                            //  txtPassword.Attributes.Add("value", CryptographyHelper.Md5Decrypt(smtpConf.Password));           

                            if (string.IsNullOrEmpty(smtpConf.Password))
                            {
                                ChkPasswordless.Checked = true;

                            }
                            else
                            {
                                // txtPassword.Attributes.Add("value", CryptographyHelper.Md5Decrypt(smtpConf.Password));
                                txtPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(smtpConf.Password), hdfStaticGuid.Value);
                                ChkPasswordless.Checked = false;
                            }


                            if (ChkPasswordless.Checked)
                            {
                                txtPassword.Enabled = false;
                            }
                            else
                            {
                                txtPassword.Enabled = true;
                            }

                            chkboxEnableSSL.Checked = smtpConf.EnableSSL;
                            chkbxISbodyHTML.Checked = smtpConf.IsBodyHTML;
                            ChkPasswordless.Enabled = true;
                            btnsave.Text = "Update";
                            break;
                        }
                        else
                        {
                            smtpId.Value = "0";
                            smtpHost.Text = string.Empty;
                            txtPort.Text = string.Empty;
                            fromMail.Text = string.Empty;
                            txtPassword.Attributes.Add("value", "");
                            chkboxEnableSSL.Checked = false;
                            chkbxISbodyHTML.Checked = false;
                            btnsave.Text = "Save";
                        }
                    }
                }
            }
            else
            {
                smtpId.Value = "0";
                smtpHost.Text = string.Empty;
                txtPort.Text = string.Empty;
                fromMail.Text = string.Empty;
                txtPassword.Attributes.Add("value", "");
                chkboxEnableSSL.Checked = false;
                chkbxISbodyHTML.Checked = false;
                btnsave.Text = "Save";
            }

            //  return str;
        }

        private void SmsBindValue()
        {
            //btnSaveSMS.Visible = false;
            //btnEditSMS.Visible = true;

            IList<SMSConfiguration> smsConfLst = Facade.GetAllSMSConfiguration();
            if (smsConfLst != null && smsConfLst.Count > 0)
            {
                SMSConfiguration smsConf = smsConfLst.FirstOrDefault();

                smsId.Value = smsConf.Id.ToString();
                txtURL.Text = smsConf.URL.ToString().Trim();
                txtSender.Text = smsConf.SenderId.ToString().Trim();
                txtUserName.Text = smsConf.UserName.ToString().Trim();

                // txtPasswordSMS.Attributes.Add("value", Utility.IsMD5EncryptedString(smsConf.Password) ? smsConf.Password : CryptographyHelper.Md5Decrypt(smsConf.Password));

                txtPasswordSMS.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(smsConf.Password), hdfStaticGuid.Value);

                //txtPasswordSMS.Text = CryptographyHelper.Md5Decrypt(smsConf.Password);
                btnSaveSMS.Text = "Update";
            }
            else
            {
                smsId.Value = "0";
                txtURL.Text = string.Empty;
                txtSender.Text = string.Empty;
                txtUserName.Text = string.Empty;
                txtPasswordSMS.Attributes.Add("value", "");
                //txtPasswordSMS.Text = string.Empty;
                btnSaveSMS.Text = "Save";
            }

            //txtURL.Enabled = false;
            //txtSender.Enabled = false;
            //txtUserName.Enabled = false;
            //txtPasswordSMS.Enabled = false;
        }

        public override void PrepareEditView()
        {
            BindControlValues();

            btnsave_newuser.Text = "Update";
        }

        private void BindControlValues()
        {

            textName.Text = CurrentAlertReceiver.Name;
            txtemailbox1.Text = CurrentAlertReceiver.EmailAddress;

            if (CurrentAlertReceiver.MobileNumber != string.Empty)
            {
                divMobile.Visible = true;
                chkMobile.Checked = true;
                // txtMCountryCode.Text = CurrentAlertReceiver.MobileNumber.Substring(0, CurrentAlertReceiver.MobileNumber.Length - 10);
                // txtMobile.Text = CurrentAlertReceiver.MobileNumber.Substring(CurrentAlertReceiver.MobileNumber.Length - 10);           

                string[] countrywithMob = CurrentAlertReceiver.MobileNumber.Split('-');
                string countrycode = countrywithMob[0].ToString();
                string mobileno = countrywithMob[1].ToString();

                txtMCountryCode.Text = countrycode.ToString();
                txtMobile.Text = mobileno.ToString();

                //  txtMCountryCode.Text = CurrentAlertReceiver.MobileNumber;
                //  txtMobile.Text = CurrentAlertReceiver.MobileNumber;           
            }
            else
            {
                divMobile.Visible = false;
                chkMobile.Checked = false;
                txtMCountryCode.Text = "";
                txtMobile.Text = "";
            }

            chkboxActive.Checked = CurrentAlertReceiver.IsActive == 1;
            CheckGroupBoxList();
            // CheckAppBoxList();
        }

        private bool CheckAlertReceiverExist()
        {
            if (CurrentEntity.Id > 0)
            {
                if (txtemailbox1.Text.ToLower().Equals(CurrentEntity.Name))
                {
                    return false;
                }
            }

            return Facade.IsAlertReceiverExistsByNameAndEmail(txtemailbox1.Text);
        }

        protected void TxtNameTextChanged(object sender, EventArgs e)
        {
            if (txtemailbox1.Text != string.Empty)
            {
                lblName1.Text = CheckAlertReceiverExist() ? "Notification Already Exist" : string.Empty;
                if (lblName1.Text != string.Empty)
                {
                    btnsave_newuser.Enabled = false;
                }
                else
                {
                    btnsave_newuser.Enabled = true;
                    btnsave_newuser.CssClass = "btn btn-primary";
                }  
            }
        }

        //private bool CheckAlertReceiverExist(string emailaddress)
        //{
        //    return Facade.IsAlertReceiverExistsByNameAndEmail(emailaddress);
        //}

        private void CheckGroupBoxList()
        {
            var arr1 = new string[25];
            var userGroupList1 = new List<string>();

            Session.Remove("PreviousItem");

            var userGroupList = Facade.GetAlertReceiverByInfraObjectId(CurrentAlertReceiverId);
            if (userGroupList != null)
            {
                foreach (AlertReceiver oReceiver in userGroupList)
                {
                    arr1 = oReceiver.InfraObjectId.Split(',');
                }
                userGroupList1.AddRange(arr1);
            }

            foreach (var listItem in from ListItem listItem in CblstGroup.Items from userGroupItem in userGroupList1 where listItem.Value == userGroupItem select listItem)
            {
                listItem.Selected = true;
                _previousSelectedItems.Add(listItem);
            }
            Session["PreviousItem"] = _previousSelectedItems;
        }

        //private void CheckAppBoxList()
        //{
        //    var arr2 = new string[25];
        //    var userAppGroupList1 = new List<string>();

        //    Session.Remove("PreviousItem");
        //    var userAppGroupList = Facade.GetAllApplicationGroups();
        //    if (userAppGroupList != null)
        //    {
        //        foreach (ApplicationGroup aApplication in userAppGroupList)
        //        {
        //            arr2 = aApplication.Id.ToString().Split(',');
        //        }
        //        userAppGroupList1.AddRange(arr2);
        //    }

        //    foreach (var listItem in from ListItem listItem in cblstApp.Items from userAppItem in userAppGroupList1 where listItem.Value == userAppItem select listItem)
        //    {
        //        listItem.Selected = true;
        //        _previousSelectedItems.Add(listItem);
        //    }
        //    Session["PreviousItem"] = _previousSelectedItems;

        //}

        public override void BuildEntities()
        {
            CurrentEntity.CreatorId = LoggedInUserId;
            CurrentEntity.Name = textName.Text;
            CurrentEntity.EmailAddress = txtemailbox1.Text;

            if (chkMobile.Checked)
            {
                CurrentEntity.MobileNumber = txtMCountryCode.Text.Trim() + "-" + txtMobile.Text.Trim();
            }
            else
            {
                CurrentEntity.MobileNumber = "";
            }

            CurrentEntity.IsActive = chkboxActive.Checked ? 1 : 0;
            CurrentEntity.IsMail = chkboxEmail.Checked ? 1 : 0;

            var selectedItem = Utility.GetSelectedItem(CblstGroup);
            if (selectedItem != null)
            {
                string a = selectedItem.Aggregate("", (current, listItem) => current + (Convert.ToInt32(listItem.Value) + ","));
                a = a.TrimEnd(',');
                CurrentEntity.InfraObjectId = a;
            }
            //var selectedAppItem = Utility.GetSelectedAppItem(cblstApp);
            //{
            //    string a1 = selectedAppItem.Aggregate("", (current, listItem) => current + (Convert.ToInt32(listItem.Value) + ","));
            //    a1 = a1.TrimEnd(',');
            //    CurrentEntity.ApplicationGroupId = a1;
            //}
        }

        private bool ValidateGroup()
        {
            var selectedGroupItem = Utility.GetSelectedItem(CblstGroup);

            if (selectedGroupItem.Count == 0)
            {
                lblGroup.Visible = true;
                return true;
            }
            return false;
        }

        protected void chkMobile_OnCheckedChanged(object sender, EventArgs e)
        {
            if (chkMobile.Checked)
            {
                divMobile.Visible = true;
                rfvmobile.ValidationGroup = "validIsActive";
                revMobile.ValidationGroup = "validIsActive";
            }
            else
            {
                divMobile.Visible = false;
                rfvmobile.ValidationGroup = "";
                revMobile.ValidationGroup = "";
            }
        }

        private void CheckBoxListAllSelected(CheckBoxList grpOrAppList)
        {
            int selectedKwt = 0;

            for (int i = 1; i < grpOrAppList.Items.Count; i++)
            {
                if (grpOrAppList.Items[i].Selected)
                {
                    selectedKwt++;
                }
                grpOrAppList.Items[i].Enabled = true;
            }

            if (selectedKwt == grpOrAppList.Items.Count - 1)
            {
                grpOrAppList.Items[0].Selected = true;
            }
            else
            {
                grpOrAppList.Items[0].Selected = false;
            }
        }


        protected void CblstGroupSelectedIndexChanged(object sender, EventArgs e)
        {
            lblGroup.Visible = false;

            string result = Request.Form["__EVENTTARGET"];
            string[] checkedBox = result.Split('$');
           // int checkedIndex = 0;
           int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);

            if (checkedIndex == 0)
            {
                if (CblstGroup.Items[0].Selected)
                {
                    for (int i = 1; i < CblstGroup.Items.Count; i++)
                    {
                        CblstGroup.Items[i].Selected = true;
                        CblstGroup.Items[i].Enabled = true;
                    }
                }
                else
                {
                    for (int i = 1; i < CblstGroup.Items.Count; i++)
                    {
                        CblstGroup.Items[i].Selected = false;
                        CblstGroup.Items[i].Enabled = true;
                    }
                }
            }
            else
            {
                CheckBoxListAllSelected(CblstGroup);
            }

            //for (int i = 1; i < CblstGroup.Items.Count; i++)
            //{
            //    CblstGroup.Items[i].Enabled = true;
            //}

            //if (CblstGroup.Items[0].Selected)
            //{
            //    if (CblstGroup.SelectedItem.Text == "ALL")
            //    {
            //        for (int i = 1; i < CblstGroup.Items.Count; i++)
            //        {
            //            CblstGroup.Items[i].Enabled = false;
            //            if (CblstGroup.Items[i].Selected)
            //            {
            //                if (i != 0)
            //                {
            //                    CblstGroup.Items[i].Selected = false;
            //                }
            //            }
            //        }
            //    }
            //    else
            //    {
            //        for (int i = 1; i < CblstGroup.Items.Count; i++)
            //        {
            //            CblstGroup.Items[i].Enabled = true;
            //        }
            //    }

            //}
            //else
            //{
            //    for (int i = 1; i < CblstGroup.Items.Count; i++)
            //    {
            //        CblstGroup.Items[i].Enabled = true;
            //    }
            //    CblstGroup.Items[0].Enabled = true;
            //    var userGroup = new UserGroup();
            //    var selectedItem = Utility.GetSelectedItem(CblstGroup);
            //    if (selectedItem != null)
            //    {
            //        foreach (var listItem in selectedItem)
            //        {
            //            userGroup.InfraObjectId = Convert.ToInt32(listItem.Value);

            //        }
            //    }
            //}
        }

        //protected void CblstAppSelectedIndexChanged(object sender, EventArgs e)
        //{
        //    lblApp.Visible = false;
        //    for (int i = 1; i < cblstApp.Items.Count; i++)
        //    {
        //        cblstApp.Items[i].Enabled = true;
        //    }

        //    if (cblstApp.Items[0].Selected)
        //    {
        //        if (cblstApp.SelectedItem.Text == "ALL")
        //        {
        //            for (int i = 1; i < cblstApp.Items.Count; i++)
        //            {
        //                cblstApp.Items[i].Enabled = false;
        //                if (cblstApp.Items[i].Selected)
        //                {
        //                    if (i != 0)
        //                    {
        //                        cblstApp.Items[i].Selected = false;
        //                    }
        //                }
        //            }
        //        }
        //        else
        //        {
        //            for (int i = 1; i < cblstApp.Items.Count; i++)
        //            {
        //                cblstApp.Items[i].Enabled = true;
        //            }
        //        }

        //    }
        //    else
        //    {
        //        for (int i = 1; i < cblstApp.Items.Count; i++)
        //        {
        //            cblstApp.Items[i].Enabled = true;
        //        }
        //        cblstApp.Items[0].Enabled = true;
        //        var userGroup = new UserGroup();
        //        var selectedItem = Utility.GetSelectedItem(cblstApp);
        //        if (selectedItem != null)
        //        {
        //            foreach (var listItem in selectedItem)
        //            {
        //                userGroup.Id = Convert.ToInt32(listItem.Value);

        //            }
        //        }
        //    }

        //}



        public string GetInfraObjectName(object InfraObjectId)
        {
            if (InfraObjectId.ToString().Contains(","))
            {
                string Infranames = string.Empty;
                string[] infraids = InfraObjectId.ToString().Split(',');
                foreach (string id in infraids)
                {
                    if (!string.IsNullOrEmpty(id))
                    {
                        if (Convert.ToInt16(id) > 0)
                        {
                            InfraObject objInfra = facade.GetInfraObjectById(Convert.ToInt32(id));
                            if (objInfra != null)
                                Infranames = Infranames + objInfra.Name + " , ";
                        }
                    }
                }
                if (Infranames.Length != 0)
                    Infranames = Infranames.Remove(Infranames.Length - 2);
                return Infranames;
            }
            else
            {
                if (Convert.ToInt32(InfraObjectId) > 0)
                {
                    //  Group objGroup =   facade.GetGroupById(Convert.ToInt32(GroupId));
                    InfraObject objInfra = facade.GetInfraObjectById(Convert.ToInt32(InfraObjectId));

                    if (objInfra != null)
                        return objInfra.Name;
                    return "";
                }
                else
                    return "All";
            }
        }

        //    public string GetApplicationName(object ApplicationGroupId)
        //    {
        //        if (ApplicationGroupId.ToString().Contains(","))
        //        {
        //            string Appnames = string.Empty;
        //            string[] appsids = ApplicationGroupId.ToString().Split(',');
        //            foreach (string id in appsids)
        //            {
        //                if (Convert.ToInt16(id) > 0)
        //                {
        //                    ApplicationGroup objApplication = facade.GetApplcationGroupById(Convert.ToInt32(id));
        //                    Appnames = Appnames + objApplication.Name + " , ";
        //                }

        //            }
        //            Appnames = Appnames.Remove(Appnames.Length - 2);
        //            return Appnames;
        //        }
        //        else
        //        {
        //        if (Convert.ToInt32(ApplicationGroupId) > 0)
        //        {
        //            ApplicationGroup objApplication = facade.GetApplcationGroupById(Convert.ToInt32(ApplicationGroupId));
        //            return objApplication.Name;
        //        }
        //        else
        //            return "All";
        //    }

        //}

        protected void BtnsaveNewuserClick(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("Notification", UserActionType.CreateNotificationManager))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {

                    if (ValidateGroup()) return;

                    try
                    {
                        BuildEntities();
                        // StartTransaction();
                        SaveEditor();
                        //  EndTransaction();
                        BindList();
                        //Response.Redirect(ReturnUrl);
                        // lbldisplaymsg.Text = "Data Save Succesfully";
                    }
                    catch (CpException ex)
                    {

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, this);
                    }
                    catch (Exception ex)
                    {


                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, this);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, this);
                        }
                    }
                }
            }
        }

        //protected void BtneditClick(object sender, EventArgs e)
        //{
        //    smtpHost.Enabled = true;
        //    txtPort.Enabled = true;
        //    fromMail.Enabled = true;
        //    txtPassword.Enabled = true;
        //    chkboxEnableSSL.Enabled = true;
        //    chkbxISbodyHTML.Enabled = true;
        //    txtPassword.Attributes.Add("value", txtPassword.Text);
        //    ModalPopupExtender2.Show();
        //    btnedit.Visible = false;
        //    if (smtpHost.Text == string.Empty && txtPort.Text == string.Empty && txtemailbox1.Text == string.Empty && txtPassword.Text == string.Empty)
        //    {
        //        btnsave.Visible = true;
        //    }
        //    else
        //    {
        //        btnsave.Visible = true;
        //        btnsave.Text = "Update";
        //    }
        //}

        //protected void EditBtn_Click(object sender, EventArgs e)
        //{
        //    txtURL.Enabled = true;
        //    txtSender.Enabled = true;
        //    txtUserName.Enabled = true;
        //    txtPasswordSMS.Enabled = true;

        //    txtPasswordSMS.Attributes.Add("value", txtPasswordSMS.Text);
        //    mpSMS.Show();

        //    btnEditSMS.Visible = false;
        //    if (txtURL.Text == string.Empty && txtSender.Text == string.Empty && txtUserName.Text == string.Empty && txtPasswordSMS.Text == string.Empty)
        //    {
        //        btnSaveSMS.Text = "Save";
        //        btnSaveSMS.Visible = true;
        //    }
        //    else
        //    {
        //        btnSaveSMS.Visible = true;
        //        btnSaveSMS.Text = "Update";
        //    }
        //}

        protected void ListView1ItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(RedirectURL);
            var lblUserId = (ListView1.Items[e.NewEditIndex].FindControl("ID")) as Label;


            var lblname = (ListView1.Items[e.NewEditIndex].FindControl("lblName")) as Label;

            //ActivityLogger.AddLog(LoggedInUserName, "NotificationManager", UserActionType.UpdateNotificationManager,
            //                          "The NotificationManager '" + lblname.Text +
            //                          "' Opened as Editing Mode ", LoggedInUserId);


            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "NotificationManager", UserActionType.UpdateNotificationManager,
                                      "The NotificationManager '" + lblname.Text +
                                      "' Opened as Editing Mode ", LoggedInUserId);

            if (lblUserId != null && ValidateRequest("NotificationManager Edit", UserActionType.UpdateNotificationManager))
            {
                //secureUrl = UrlHelper.BuildSecureUrl(RedirectURL, string.Empty, Constants.UrlConstants.Params.EmailId,
                //                                     lblUserId.Text);
                secureUrl = UrlHelper.BuildSecureUrl(RedirectURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.EmailId, lblUserId.Text);
                Helper.Url.Redirect(secureUrl);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void ListView1ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ibtnEdit") as ImageButton;
            var delete = e.Item.FindControl("ibtnDelete") as ImageButton;
            var imgAct = e.Item.FindControl("imgActive") as ImageButton;

            HiddenField hdnIsActive = (HiddenField)e.Item.FindControl("hdnIsActive");

            if (hdnIsActive != null)
            {
                string isactive = hdnIsActive.Value.ToString();

                if (isactive == "1")
                {
                    edit.Enabled = true;
                    delete.Enabled = true;
                    imgAct.Enabled = false;

                }
                else
                {
                    edit.Enabled = false;
                    delete.Enabled = false;
                    imgAct.Enabled = true;

                }
            }

            if (imgAct.ToolTip == "Deactivated")
                delete.ToolTip = "Deactive";

            if (IsUserOperator)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }

        }

        protected void ListView1ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            if (e.CommandName == "ChangePassword")
            {
                ListViewDataItem dataItem = (ListViewDataItem)e.Item;
            }
            if (e.CommandName == "ActiveState")
            {
                HiddenField hdnIsActive = (HiddenField)e.Item.FindControl("hdnIsActive");

                if (hdnIsActive != null)
                {
                    var edit = e.Item.FindControl("ibtnEdit") as ImageButton;
                    var delete = e.Item.FindControl("ibtnDelete") as ImageButton;
                    var imageactive = e.Item.FindControl("imgActive") as ImageButton;

                    Label lbText = (Label)e.Item.FindControl("ID");

                    string label = lbText.Text;

                    int id = Convert.ToInt32(label.ToString());

                    int isactive = Convert.ToInt32(hdnIsActive.Value);

                    if (isactive == 0)
                    {
                        //Session["isactive"] = isactive;
                        UnLockNotification(id);
                        edit.Enabled = true;
                        delete.Enabled = true;


                        imageactive.ImageUrl = "../Images/icons/tick-circle.png";
                        imageactive.Enabled = false;

                    }
                    else
                    {
                        edit.Enabled = false;
                        delete.Enabled = false;
                        imageactive.Enabled = true;
                    }
                }
            }
        }

        private bool UnLockNotification(int a)
        {
            return Facade.UnLockNotification(a);
        }

        protected void ListView1ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (ListView1.Items[e.ItemIndex].FindControl("ID")) as Label;
            var lblname = (ListView1.Items[e.ItemIndex].FindControl("lblName")) as Label;
            if (lbl != null && ValidateRequest("NotificationManager Delete", UserActionType.DeleteNotificationManager))
            {
                int id = Convert.ToInt32(lbl.Text);
                bool isDelete = Facade.DeleteAlertReceiverById(id);

                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Notification" + " " + '"' + lblname.Text + '"', TransactionType.InActive));

                //if (isDelete)
                //{
                //    //CurrentCacheManager.DataCache.RemoveItem(CacheKey);
                //    ListView1.EditIndex = -1;
                //    BindList();
                //}
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected string CheckActive(object act)
        {
            int val = Convert.ToInt32(act);
            string strActive = string.Empty;
            switch (val)
            {
                case 0:
                    strActive = "../Images/icons/Lock.png";
                    break;

                case 1:
                    strActive = "../Images/icons/tick-circle.png";
                    break;
            }
            return strActive;
        }

        protected string CheckToolTip(object tool)
        {
            int toolid = Convert.ToInt32(tool);
            string strtool = string.Empty;

            switch (toolid)
            {
                case 0:
                    strtool = "De-Activated, Click to Activate"; //Locked
                    break;

                case 1:
                    strtool = "Active";
                    break;
            }

            return strtool;
        }

        //protected void CblstGroup_TextChanged(object sender, EventArgs e)
        //{
        //    if (CblstGroup.SelectedItem.Text == "ALL")
        //    {
        //        CblstGroup.Enabled = true;
        //    }
        //}
        //protected void CblstApp_TextChanged(object sender, EventArgs e)
        //{
        //    if (cblstApp.SelectedItem.Text == "ALL")
        //    {
        //        cblstApp.Enabled = false;
        //    }
        //}

        protected void Checkpwdless_CheckedChanged(object sender, EventArgs e)
        {
            if (ChkPasswordless.Checked)
            {
                txtPassword.Enabled = false;
                txtPassword.Attributes.Add("value", "");
            }
            else
            {
                txtPassword.Enabled = true;
            }
        }

        protected void btnsave_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("Notification", UserActionType.CreateNotificationManager))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {

                    CheckValidation_smtphost();
                    if (_smtpvalidator == false)
                    {
                        modelbg.Visible = true;
                        Panel_smtphost.Visible = true;
                        //  ModalPopupExtender2.Show();
                        smtpHost.Focus();
                    }
                    else
                    {
                        try
                        {
                            if (smtpId.Value.Trim() != "" && Convert.ToInt32(smtpId.Value) > 0)
                            // if (btnsave.Text == "Update")
                            {
                                var smtpserver = new SmtpConfiguration();
                                smtpserver.Id = Convert.ToInt32(smtpId.Value);
                                smtpserver.SmtpHost = CryptographyHelper.Md5Encrypt(smtpHost.Text);
                                smtpserver.UserName = CryptographyHelper.Md5Encrypt(fromMail.Text);
                                smtpserver.Port = Convert.ToInt32(txtPort.Text);
                                smtpserver.IsBodyHTML = chkbxISbodyHTML.Checked;
                                smtpserver.EnableSSL = chkboxEnableSSL.Checked;
                                //   smtpserver.Password = CryptographyHelper.Md5Encrypt(txtPassword.Text);
                                if (!ChkPasswordless.Checked)
                                {
                                    if (string.IsNullOrEmpty(txtPassword.Text))
                                    {
                                        smtpserver.Password = string.Empty;
                                        //lblPasswordless.Visible = true;
                                        //return;
                                    }
                                    else
                                    {

                                        lblPasswordless.Visible = false;
                                        // smtpserver.Password = CryptographyHelper.Md5Encrypt(txtPassword.Text);
                                        smtpserver.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPassword.Text, hdfStaticGuid.Value));

                                    }

                                }
                                else
                                {
                                    smtpserver.Password = string.Empty;

                                }

                                smtpserver.UpdatorId = LoggedInUserId;
                                smtpserver = Facade.UpdateServer(smtpserver);
                                modelbg.Visible = false;
                                Panel_smtphost.Visible = false;
                                //    ModalPopupExtender2.Hide();
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("SMTPHost", TransactionType.Update));


                            }
                            else
                            {
                                var smtpserver = new SmtpConfiguration();

                                smtpserver.SmtpHost = CryptographyHelper.Md5Encrypt(smtpHost.Text);
                                smtpserver.UserName = CryptographyHelper.Md5Encrypt(fromMail.Text);
                                smtpserver.Port = Convert.ToInt32(txtPort.Text);
                                smtpserver.IsBodyHTML = chkbxISbodyHTML.Checked;
                                smtpserver.EnableSSL = chkboxEnableSSL.Checked;
                                //  smtpserver.Password =CryptographyHelper.Md5Encrypt(txtPassword.Text);
                                if (!ChkPasswordless.Checked)
                                {
                                    if (string.IsNullOrEmpty(txtPassword.Text))
                                    {

                                        smtpserver.Password = string.Empty;
                                        //lblPasswordless.Visible = true;
                                        //return;
                                    }
                                    else
                                    {

                                        lblPasswordless.Visible = false;
                                        //smtpserver.Password = CryptographyHelper.Md5Encrypt(txtPassword.Text);
                                        smtpserver.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPassword.Text, hdfStaticGuid.Value));
                                    }
                                }
                                else
                                {
                                    smtpserver.Password = string.Empty;

                                }
                                smtpserver.CreatorId = LoggedInUserId;
                                smtpserver = Facade.AddServer(smtpserver);
                                modelbg.Visible = false;
                                Panel_smtphost.Visible = false;
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("SMTPHost", TransactionType.Add));

                                //ModalPopupExtender2.Hide();
                            }
                        }
                        catch (Exception)
                        {
                            lblsmtpsavemessage.Text = "Error while Saving";
                            lblsmtpsavemessage.Visible = true;
                        }
                        ClearField();
                        var CurrentURL = Constants.UrlConstants.Urls.Alert.NotificationManager;
                        var secureUrl = new SecureUrl(CurrentURL);
                        if (secureUrl != null)
                        {

                            Helper.Url.Redirect(secureUrl);
                        }

                    }
                }
            }
        }


        protected void SaveBtn_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("Notification", UserActionType.CreateNotificationManager))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {

                    CheckValidation_SMS();
                    if (_SMSvalidator == false)
                    {
                        mpSMS.Show();
                        txtURL.Focus();
                    }
                    else
                    {
                        try
                        {
                            if (btnSaveSMS.Text == "Update")
                            {
                                var smsConf = new SMSConfiguration();
                                smsConf.Id = Convert.ToInt32(smsId.Value);
                                smsConf.URL = txtURL.Text.Trim();
                                smsConf.SenderId = txtSender.Text.Trim();
                                smsConf.UserName = txtUserName.Text.Trim();
                                //smsConf.Password = txtPasswordSMS.Text;
                                //smsConf.Password = Utility.IsMD5EncryptedString(txtPasswordSMS.Text) ? txtPasswordSMS.Text : CryptographyHelper.Md5Encrypt(txtPasswordSMS.Text);
                                smsConf.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPasswordSMS.Text, hdfStaticGuid.Value));

                                smsConf.UpdatorId = LoggedInUserId;
                                smsConf = Facade.UpdateSMSConfiguration(smsConf);
                                mpSMS.Hide();
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("SMS", TransactionType.Update));
                               
                            }
                            else
                            {
                                var smsConf = new SMSConfiguration();
                                if (smsId.Value.Trim() != string.Empty)
                                {
                                    smsConf.Id = Convert.ToInt32(smsId.Value);
                                }
                                else
                                    smsConf.Id = 0;

                                smsConf.URL = txtURL.Text.Trim();
                                smsConf.SenderId = txtSender.Text.Trim();
                                smsConf.UserName = txtUserName.Text.Trim();
                                //smsConf.Password = txtPasswordSMS.Text;
                                //smsConf.Password = Utility.IsMD5EncryptedString(txtPasswordSMS.Text) ? txtPasswordSMS.Text : CryptographyHelper.Md5Encrypt(txtPasswordSMS.Text);
                                smsConf.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPasswordSMS.Text, hdfStaticGuid.Value));

                                smsConf.CreatorId = LoggedInUserId;
                                smsConf = Facade.AddSMSConfiguration(smsConf);
                                mpSMS.Hide();
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("SMS", TransactionType.Add));

                            }
                        }
                        catch (Exception)
                        {
                            lblSMSSaveMessage.Text = "Error while Saving";
                            lblSMSSaveMessage.Visible = true;
                        }
                    }
                    SMSPopupClose();
                    ClearField();
                    var CurrentURL = Constants.UrlConstants.Urls.Alert.NotificationManager;
                    var secureUrl = new SecureUrl(CurrentURL);
                    if (secureUrl != null)
                    {

                        Helper.Url.Redirect(secureUrl);
                    }
                }
            }
        }

        private bool CheckValidation_SMS()
        {
            if (txtURL.Text == string.Empty || txtSender.Text == string.Empty ||
                txtUserName.Text == string.Empty || txtPasswordSMS.Text == string.Empty)
            {
                _SMSvalidator = false;
            }
            return _SMSvalidator;
        }

        private bool CheckValidation_smtphost()
        {
            if (smtpHost.Text == string.Empty || txtPort.Text == string.Empty || fromMail.Text == string.Empty)
            {
                _smtpvalidator = false;
            }
            if (!ChkPasswordless.Checked)
            {
                //if (string.IsNullOrEmpty(txtPassword.Text))
                //{

                //  lblPasswordless.Visible = true;
                _smtpvalidator = true;

                // }

            }
            return _smtpvalidator;
        }

        //[WebMethod]
        //public static string Retrivedatasmtp()
        //{
        //    var str = String.Empty;
        //    List<SmtpConfiguration> smtpdetailsdisplay = new List<SmtpConfiguration>();

        //    var smtpDetails = facade.GetSmtpConfigurations();
        //    if (smtpDetails != null && smtpDetails.Count > 0)
        //    {
        //        SmtpConfiguration smtpConf = smtpDetails.FirstOrDefault();
        //        str = smtpConf.Id + "," + smtpConf.SmtpHost + "," + smtpConf.Port + "," + smtpConf.UserName + "," + smtpConf.Password + "," + smtpConf.EnableSSL + "," + smtpConf.IsBodyHTML;
        //    }
        //    return str;
        //}

        protected void libtnsmtphost_Click(object sender, EventArgs e)
        {
            Retrivedatasmtp();
            modelbg.Visible = true;
            Panel_smtphost.Visible = true;

            //ModalPopupExtender2.Show();
        }

        protected void lnkSMS_Click(object sender, EventArgs e)
        {
            SMSPopupOpen();
        }

        protected void chkboxEnableSSL_CheckedChanged(object sender, EventArgs e)
        {
            modelbg.Visible = true;
            Panel_smtphost.Visible = true;
            // ModalPopupExtender2.Show();
        }

        protected void chkbxISbodyHTML_CheckedChanged(object sender, EventArgs e)
        {
            modelbg.Visible = true;
            Panel_smtphost.Visible = true;
            // ModalPopupExtender2.Show();
        }

        protected void Lkbtnclosesmtphost_Click(object sender, EventArgs e)
        {
            ClearField();
            // Retrivedatasmtp();
        }

        protected void lkbtnCloseSMS_Click(object sender, EventArgs e)
        {
            SmsBindValue();
            SMSPopupClose();
        }

        private void SMSPopupClose()
        {
            //btnSaveSMS.Visible = false;
            //btnEditSMS.Visible = false;
            //btnCloseSMS.Visible = false;

            mpSMS.Hide();
        }

        private void SMSPopupOpen()
        {
            txtURL.Visible = true;
            txtSender.Visible = true;
            txtUserName.Visible = true;
            txtPasswordSMS.Visible = true;

            btnSaveSMS.Visible = true;
            //btnEditSMS.Visible = true;
            btnCloseSMS.Visible = true;

            mpSMS.Show();
        }

        protected void btnclose_Click(object sender, EventArgs e)
        {
            ClearField();

        }

        protected void CloseBtn_Click(object sender, EventArgs e)
        {
            SmsBindValue();
            SMSPopupClose();
        }

        protected void btncancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(ReturnUrl);
        }

        public override void SaveEditor()
        {
            var ar = new AlertReceiver();

            var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
            if (returnUrl.IsNullOrEmpty())
            {
                returnUrl = ReturnUrl;
            }
            var areciverdetail = 0;

            //if (CheckAlertReceiverExist(CurrentEntity.EmailAddress))
            //{
            //    ErrorSuccessNotifier.AddErrorMessage("Email already exists. Please specify another Email.");
            //}
            //else
            //{
            if (CurrentEntity.IsNew)
            {
                //if (CheckAlertReceiverExist(CurrentEntity.EmailAddress))
                //{
                //    ErrorSuccessNotifier.AddErrorMessage("Email already exists. Please specify another Email.");
                //}
                //else
                CurrentEntity = Facade.AddAlertReceiver(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), UserActionType.CreateNotificationManager, "The Notification '" + CurrentEntity.Name + "' was Added to the Notification Manager", CurrentEntityId);

                areciverdetail = ar.Id;

                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Notification" + " " + '"' + CurrentEntity.Name + '"', TransactionType.Add));
            }
            else
            {
                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateAlertReceiver(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), UserActionType.UpdateNotificationManager, "The Notification '" + CurrentEntity.Name + "' was Updated to the Notification Manager", CurrentEntityId);
                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Notification" + " " + '"' + CurrentEntity.Name + '"', TransactionType.Update));
            }
            // }
            var CurrentURL = Constants.UrlConstants.Urls.Alert.NotificationManager;
            var secureUrl = new SecureUrl(CurrentURL);

            if (areciverdetail > 0)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.AlertId, areciverdetail.ToString());
            }
            if (secureUrl != null)
            {

                Helper.Url.Redirect(secureUrl);
            }
        }

        public void ClearField()
        {
            fromMail.Text = string.Empty;
            txtPassword.Text = string.Empty;
            txtPassword.Attributes.Add("value", string.Empty);
            txtPassword.Text = "";
            chkboxEnableSSL.Checked = false;
            chkbxISbodyHTML.Checked = false;
            ChkPasswordless.Checked = false;
            smtpHost.Text = string.Empty;
            txtPort.Text = string.Empty;
            //smtpHost.Enabled = false;
            //txtPort.Enabled = false;
            //fromMail.Enabled = false;
            //txtPassword.Enabled = false;
            //chkboxEnableSSL.Enabled = false;
            //chkbxISbodyHTML.Enabled = false;
            lblsmtpsavemessage.Text = string.Empty;
            //btnsave.Visible = false;
            //btnedit.Visible = true;
            btnsave_newuser.Text = "Save";
            modelbg.Visible = false;
            Panel_smtphost.Visible = false;
            //   ModalPopupExtender2.Hide();
            // UpdatePanel_smtphost.Update();
        }

        public override void BindList()
        {
            //var groupList = Facade.GetAllGroups();
            //var infralist;// = Facade.GetAllInfraObject();
            var infralist = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
            if (infralist != null)
            {
                CblstGroup.DataSource = infralist;
                CblstGroup.DataTextField = "Name";
                CblstGroup.DataValueField = "Id";
                CblstGroup.DataBind();
            }
            CblstGroup.Items.Insert(0, new ListItem("ALL", "0"));

            var EmailList = Facade.GetAllAlertReceiverIsActive();
            if (EmailList != null && EmailList.Count != 0)
            {
                if (IsSuperAdmin)
                {
                    if (EmailList != null)
                    {
                        ListView1.DataSource = EmailList;
                        ListView1.DataBind();
                        ListView1.Visible = true;
                    }
                }
                else
                {
                    var EmailList1 = (from n in EmailList
                                      where n.CreatorId == Convert.ToInt32(LoggedInUserId)
                                      select n).ToList();


                    if (EmailList1 != null)
                    {
                        ListView1.DataSource = EmailList1;
                        ListView1.DataBind();
                        ListView1.Visible = true;
                    }
                }
            }
            //var AppList = Facade.GetAllApplicationGroups();
            //if (AppList != null)
            //{
            //    cblstApp.DataSource = AppList;
            //    cblstApp.DataTextField = "Name";
            //    cblstApp.DataValueField = "Id";
            //    cblstApp.DataBind();
            //}
            //cblstApp.Items.Insert(0, new ListItem("ALL", "0"));

            //var EmailList1 = Facade.GetAllAlertReceivers();
            //ListView1.DataSource = EmailList1;
            //ListView1.DataBind();
            //ListView1.Visible = true;
        }

        protected void btnTestMail_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("NotificationManager TestMail", UserActionType.CreateNotificationManager))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {
                    CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();
                    emailManager.From = fromMail.Text.Trim();
                    emailManager.Body = "This is an e-mail message sent while testing the SMTP configuration.";
                    emailManager.Subject = "Test email";
                    SmtpConfiguration smtpConfig = new SmtpConfiguration();
                    smtpConfig.SmtpHost = smtpHost.Text.Trim();
                    smtpConfig.Port = Convert.ToInt32(txtPort.Text.Trim());
                    smtpConfig.Password = txtPassword.Text.Trim();

                    if (!ChkPasswordless.Checked)
                    {
                        if (string.IsNullOrEmpty(txtPassword.Text))
                        {
                            smtpConfig.Password = string.Empty;
                        }
                        else
                        {

                            lblPasswordless.Visible = false;
                            smtpConfig.Password = Utility.getOriginalEncryData(txtPassword.Text, hdfStaticGuid.Value);
                            //smtpConfig.Password =CryptographyHelper.Md5Decrypt(txtPassword.Text);
                        }

                    }
                    else
                    {
                        smtpConfig.Password = string.Empty;

                    }



                    smtpConfig.EnableSSL = chkboxEnableSSL.Checked;
                    smtpConfig.IsBodyHTML = chkbxISbodyHTML.Checked;
                    smtpConfig.UserName = fromMail.Text.Trim();
                    string output = emailManager.SendTestMail(smtpConfig);
                    if (!string.IsNullOrEmpty(output) && !output.Contains(':'))
                    {
                        lblsmtpsavemessage.Text = output;
                        lblsmtpsavemessage.ForeColor = System.Drawing.Color.Green;
                    }
                    else 
                    {
                        lblsmtpsavemessage.Text = output;
                        lblsmtpsavemessage.ForeColor = System.Drawing.Color.Red;
                    }
                    lblsmtpsavemessage.Visible = true;
                }
            }
        }

        protected void BtnTestSMS_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("NotificationManager TestSMS", UserActionType.CreateNotificationManager))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {

                    CP.Helper.EmailManager Smsvalues = new CP.Helper.EmailManager();
                    SMSConfiguration smsConf = new SMSConfiguration();
                    smsConf.URL = txtURL.Text.Trim();
                    smsConf.SenderId = txtSender.Text.Trim();
                    smsConf.UserName = txtUserName.Text.Trim();

                    smsConf.Password = txtPasswordSMS.Text != string.Empty ? Utility.IsMD5EncryptedString(txtPasswordSMS.Text) ? txtPasswordSMS.Text : CryptographyHelper.Md5Encrypt(txtPasswordSMS.Text) : "";

                    smsConf.Recipient = txtRecipientNumber.Text;

                    string Resultmsg = Smsvalues.SendTestSms(smsConf);
                    lblSMSSaveMessage.Visible = true;
                    lblSMSSaveMessage.Text = Resultmsg;
                }
            }

        }

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((ViewState["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                IgnoreIDs.Add("txtMCountryCode");
                IgnoreIDs.Add("txtemailbox1");
                IgnoreIDs.Add("fromMail");
                //IgnoreIDs.Add("txtCountryCode2");
              //  IgnoreIDs.RemoveAll(x=>System.Text.RegularExpressions.Regex.IsMatch(, "[=+-<>]");)
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            var EmailList = Facade.GetAllAlertReceiverIsActive();
            if (EmailList != null && EmailList.Count != 0)
            {
                if (!String.IsNullOrEmpty(txtsearchvalue.Text))
                {
                    string strName = txtsearchvalue.Text;
                    var EmailList1 = (from n in EmailList where n.Name.ToLower().Contains(strName.ToLower()) select n).ToList();
                    if (EmailList1 != null && EmailList1.Count > 0)
                    {
                        ListView1.DataSource = EmailList1;
                        ListView1.DataBind();
                        ListView1.Visible = true;
                    }
                    else
                    {
                        ListView1.DataSource = null;
                        ListView1.DataBind();
                        ListView1.Visible = true;
                    }
                }
                else
                {
                    var EmailList2 = Facade.GetAllAlertReceiverIsActive();
                    if (EmailList2 != null && EmailList2.Count != 0)
                    {
                        var EmailList1 = from n in EmailList2
                                         where n.CreatorId == Convert.ToInt32(LoggedInUserId)
                                         select n;
                        if (EmailList1 != null)
                        {
                            ListView1.DataSource = EmailList1;
                            ListView1.DataBind();
                            ListView1.Visible = true;
                        }
                    }
                }
                txtsearchvalue.Text = string.Empty;
            }
        }
    }
}