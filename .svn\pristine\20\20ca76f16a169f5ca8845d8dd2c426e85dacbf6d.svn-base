﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class IncidentManagementNewDataAccess : BaseDataAccess, IIncidentManagementNewDataAccess
    {
        #region Constructors

        public IncidentManagementNewDataAccess(Context context)
            : base(context)
        {
        }
        protected override IEntityBuilder<IncidentManagementNew> CreateEntityBuilder<IncidentManagementNew>()
        {
            return (new IncidentManagementNewBuilder()) as IEntityBuilder<IncidentManagementNew>;
        }

        #endregion

        #region Methods

        public IList<IncidentManagementNew> GetAll()
        {

            try
            {
                const string sp = "IncMgntNew_GetAll";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<IncidentManagementNew>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IIncidentManagmentNewDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }

        }

        IList<IncidentManagementNew> IIncidentManagementNewDataAccess.GetByInfraComponentID(int infracompid, string infraCompType)
        {
            try
            {


                if (infracompid < 1)
                {
                    throw new ArgumentNullException("infracompid");
                }
                const string sp = "Incident_GetAllByInfraCompId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraComponentID", DbType.Int32, infracompid);
                    Database.AddInParameter(cmd, Dbstring + "iInfraComponentType", DbType.AnsiString, infraCompType);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<IncidentManagementNew>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAlertDataAccess.GetByInfraComponentID(" + infracompid + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public IList<IncidentManagementNew> GetByIncidentStatus()
        {

            try
            {
                const string sp = "INCMGNTNEW_GETBYSTATUS";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<IncidentManagementNew>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IIncidentManagmentNewDataAccess.GetByIncidentStatus" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }

        }

        #endregion Methods
    }
}
