﻿using System;
using System.Collections.Generic;
using System.Data;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ServiceDRProtectionDataAccess : BaseDataAccess, IServiceDRProtectionDataAccess
    {

         #region Constructors

        public ServiceDRProtectionDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ServiceDRProtection> CreateEntityBuilder<ServiceDRProtection>()
        {
            return (new ServiceDRProtectionBuilder()) as IEntityBuilder<ServiceDRProtection>;
        }
       
         #endregion Constructors


        IList<ServiceDRProtection> IServiceDRProtectionDataAccess.GetAll()
        {
            try
            {
                const string sp = "ServiceDRProtection_GetAll";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ServiceDRProtection>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServiceDRProtectionDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ServiceDRProtection> IServiceDRProtectionDataAccess.GetByUserId(int UserId)
        {
            try
            {
                const string sp = "ServiceDRPro_GetByUserId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, UserId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_alert"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ServiceDRProtection>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServiceDRProtectionDataAccess.GetByUserId(" + UserId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
    }
}
