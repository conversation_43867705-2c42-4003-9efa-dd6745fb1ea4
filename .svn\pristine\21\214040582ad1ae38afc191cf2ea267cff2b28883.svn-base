﻿var oldName = "";
 	$("[id$=ddlLoadProperty]").change(function() {
        var value = $(this).val();
		$("#divActionSet").hide(500);
        hideDivError();
        $("[id$=ddlActionSetList]").val("00").attr('selected', true);
        if (value != "00" && value != "000") {
        	GlobalWorkFlowProperty = value;
            EnableImage();
            $.ajax({
                type: "POST",
                url: "WorkflowConfiguration.aspx/MainFunction",
                data: "{'args':'GetProperty','parameter':'" + value + "'}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function(msg) {
                    AssignValue(msg.d);
                    $(propertyObj).show(500);
                    $("#btnCancelEdit").hide();
                    $("#btnEdit").show();
                    $("#btnPropertySave").hide();
                }
            });
        } else {
        	GlobalWorkFlowProperty ="0";
            DisableImage();
            for (var i = 0; i < txtObj.length; i++) {
                $("#" + txtObj[i] + "").val("");
            }
            if (value== "000") {
                $(propertyObj).show(500);
				EnablePropertyElement();
				$('#btnEdit').trigger('click');
            } else {
                hidePropertyObj();
            }
        }
    });

 	function ChangeWorkflowPropertyName(propertyName){
	var i=1;
	$('.one').each(function(){
		var tempVariable = $(this).text();
		tempVariable = tempVariable.substring(tempVariable.indexOf(".") + 1);
		tempVariable = jQuery.trim(tempVariable);
		if (tempVariable == oldName) {
				name = i + ". &nbsp;&nbsp;" + propertyName + "<img Class='close' src='../../images/icons/cross-circle.png' />";
			$(this).html(name);
		}
		i++;
	});
	$("#ddlSelectedWFProperty option").each(function () {
        var ddlOption = jQuery.trim($(this).text());
        if (ddlOption == oldName) {
                	$(this).text("   " + propertyName);
        }
    });
}

	function hidePropertyObj()
{
        for (var i = 0; i < txtObj.length; i++) {
			if ((i != 2) && (i != 4)){
				document.getElementById(txtObj[i]).value = "";
			}
		}
        $('#divProperties').hide(500);
}

	 $("#btnPropertySave").click(function() {
           var validateValue= validateProperty();
            var flag = validateValue.indexOf(':');
            if($("#errortxtName").is(":visible"))
            {
                return false;
            }
            if (flag > 0)
            {
                PropertyError(validateValue,"divError");
                return false;
            }
            else
            {
                  $("#divError").html("");
                  clearValidation();
            }

        var propertiesValue = '';
        for (var i = 0; i < txtObj.length; i++) {
            if(txtObj[i]== "txtExecMode")
            {
                propertiesValue = propertiesValue + $("#txtExecMode option:selected").val() + ':';
            }

            else
            {
                if(txtObj[i]== "ddlHost")
                {
                    propertiesValue = propertiesValue + $("[id$=ddlHost] option:selected").val() + ':';
                }
                else
                {
                    propertiesValue = propertiesValue + document.getElementById(txtObj[i]).value + ':';
                }
            }
        }

	 	GlobalWorkFlowProperty = $("[id$=ddlLoadProperty]").val();

        propertiesValue = propertiesValue + GlobalWorkFlowProperty;

        $.ajax({
            type: "POST",
            url: "WorkflowConfiguration.aspx/MainFunction",
            data: "{'args':'SaveProperty','parameter':'" + propertiesValue + "'}",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function(msg) {
                                var textValue = $("[id$=ddlLoadProperty] option:selected").val();
                                var propertyName = $("#txtName").val();
            	ChangeWorkflowPropertyName(propertyName);
//                                ChangeName(propertyName);
                                if (textValue != "000")
                                {
                                    $("[id$=ddlLoadProperty] option:selected").text(propertyName);
                                } else
                                {
                                    $("[id$=ddlLoadProperty] option:selected").remove();
                                    $("[id$=ddlLoadProperty]").append("<option value='" + msg.d + "' selected='selected'>" + propertyName + "</option>");
                                    $("[id$=ddlLoadProperty]").append("<option value='000'>Insert new Property</option>");
                //                    $("#ddlActionProperty").append("<option value='" + msg.d + "'>" + propertyName + "</option>");
                                }
                                 EnableImage();
                                DisablePropertyElement();
                                $('#btnPropertySave').hide();
                                $('#btnCancelEdit').hide();
                                $('#btnEdit').show();
                                },
              error : function(msg) {
                                       alert(msg.d);
                                    }
        });
	 	return false;
	 });

	function DisablePropertyElement()
	{
	    for(var i = 0 ; i < txtObj.length ; i++)
	    {
			$('[id$=' + txtObj[i] + ']').attr("disabled", true);
	    }
	}
	function EnablePropertyElement()
	{
	    for(var i = 0 ; i < txtObj.length; i++)
	    {
	       $('[id$=' + txtObj[i] + ']').attr("disabled", false);
	    }
	}
	$('#btnEdit').click(function(){
   oldName = $("#txtName").val();

	$('#btnPropertySave').show();
	$('#btnCancelEdit').show();
	$(this).hide();
	EnablePropertyElement();
	DisableImage();
	});
	function validateProperty()
	{
	   var returnValue="";
	   var flag="";
	  var alphaExp = /^[a-zA-Z0-9''_'\s]{3,35}$/;

	   for(var i = 0 ; i < txtObj.length ; i++)
	   {
	        if(i!=1)
	        {
	           if($("#" + txtObj[i]).val()=="")
	            {
	                       flag = 1;
	               returnValue = returnValue + txtObj[i] + "Require :" ;
	            }
	        }

	        if(i==0)
	        {
	        if (!$("#" + txtObj[i]).val().match(alphaExp))
	            {
	                       flag = 1;
	              returnValue = "only AlphaNumeric value allow ";
	               //returnValue + txtObj[i] +
	            }
	         }

	        if(i==5)
	        {
	           if(isNaN($("#" + txtObj[i]).val()))
	            {
	                    flag = 1;
	                returnValue = returnValue +"Recurrence time should be in  no :" ;
	            }
	        }
	   }
	    if(flag==1)
	     {
	        return returnValue;
	     }
	     else
	     {
	        return "No error";
	     }
	}
   
    $("#btnCancel").click(function() {
        for (var i = 0; i < txtObj.length; i++) {
            document.getElementById(txtObj[i]).value = "";
        }
        propertyObj.style.display = 'none';
    });

	function EnableActionButton()
{
    $("#btnEdit").hide();
    $("#btnCancelEdit").show();
    $("#btnPropertySave").show();
}
function DisableActionButton()
{
    $("#btnEdit").show();
    $("#btnCancelEdit").hide();
    $("#btnPropertySave").hide();
}