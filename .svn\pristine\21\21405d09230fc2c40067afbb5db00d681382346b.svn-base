﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class DatabaseDB2Builder : IEntityBuilder<DatabaseDB2>
    {
        IList<DatabaseDB2> IEntityBuilder<DatabaseDB2>.BuildEntities(IDataReader reader)
        {
            var databaseDb2s = new List<DatabaseDB2>();

            while (reader.Read())
            {
                databaseDb2s.Add(((IEntityBuilder<DatabaseDB2>)this).BuildEntity(reader, new DatabaseDB2()));
            }

            return (databaseDb2s.Count > 0) ? databaseDb2s : null;
        }

        DatabaseDB2 IEntityBuilder<DatabaseDB2>.BuildEntity(IDataReader reader, DatabaseDB2 databaseDb2)
        {
            //const int FLD_ID = 0;
            //const int FLD_BASEDATABASEID = 1;
            //const int FLD_DATABASESID = 2;
            //const int FLD_USERNAME = 3;
            //const int FLD_PASSWORD = 4;
            //const int FLD_PORT = 5;

            //databaseDb2.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //databaseDb2.BaseDatabaseId = reader.IsDBNull(FLD_BASEDATABASEID) ? 0 : reader.GetInt32(FLD_BASEDATABASEID);
            //databaseDb2.DatabaseSID = reader.IsDBNull(FLD_DATABASESID) ? string.Empty : reader.GetString(FLD_DATABASESID);
            //databaseDb2.UserName = reader.IsDBNull(FLD_USERNAME) ? string.Empty : reader.GetString(FLD_USERNAME);
            //databaseDb2.Password = reader.IsDBNull(FLD_PASSWORD) ? string.Empty : reader.GetString(FLD_PASSWORD);
            //databaseDb2.Port = reader.IsDBNull(FLD_PORT) ? 0 : reader.GetInt32(FLD_PORT);

            //Fields in bcms_database_db2 table on 16/07/2013 : Id, BaseDatabaseId, DatabaseSID, UserName, Password, Port

            databaseDb2.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            databaseDb2.BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"])
                ? 0
                : Convert.ToInt32(reader["BaseDatabaseId"]);
            databaseDb2.DatabaseSID = Convert.IsDBNull(reader["DatabaseSID"])
                ? string.Empty
                : Convert.ToString(reader["DatabaseSID"]);
            databaseDb2.UserName = Convert.IsDBNull(reader["UserName"])
                ? string.Empty
                : Convert.ToString(reader["UserName"]);
            databaseDb2.Password = Convert.IsDBNull(reader["Password"])
                ? string.Empty
                : Convert.ToString(reader["Password"]);
            databaseDb2.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);

            return databaseDb2;
        }
    }
}