﻿using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Helper;
using Gios.Pdf;
using log4net;
using SpreadsheetGear;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class ServerDetails : BaseControl
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(ServerDetails));

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        public override void PrepareView()
        {
            IList<BusinessService> businessServiceList = Facade.GetBusinessServicesByCompanyId(LoggedInUserCompany.Id, LoggedInUserCompany.IsParent);

            if (businessServiceList != null)
            {


                var ds = from data in businessServiceList
                         where !String.IsNullOrEmpty(data.CANID)
                         select new
                         {
                             data.Id,
                             DisplayField = data.CANID + " (" + data.Name + ")"
                         };
                ddlCanId.DataSource = ds;
                ddlCanId.DataTextField = "DisplayField";
                ddlCanId.DataValueField = "Id";
                ddlCanId.DataBind();
                ddlCanId.Items.Insert(0, "ALL");
            }
            else
            {
                ddlCanId.Items.Insert(0, new ListItem("No Data Found", "0"));
            }

            Pan1.Visible = false;
            btnview.Visible = true;
            btnExcel.Visible = false;
            btnPdf.Visible = false;

        }


        protected void btnview_Click(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("======Generating User Activity Report HTML View ======");
                _logger.Info(Environment.NewLine);


                Pan1.Visible = true;
                btnview.Visible = false;
                screenreport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }

            btnPdf.Visible = true;
            btnExcel.Visible = true;
        }

        private void ExcelReport()
        {
            _logger.Info("======Generating Server Object Detail Report EXCEL View ======");
            _logger.Info(Environment.NewLine);

            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "Server Details Report By CANID";

            _cells["A1"].ColumnWidth = 7;

            _cells["D3"].Formula = "Server Details Report";
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].Font.Bold = true;
            _cells["B3:F6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:F3"].Font.Size = 11;
            _cells["B5:F8"].Font.Size = 10;
            _cells["B3:F8"].Font.Color = Color.White;
            _cells["B3:F8"].Font.Name = "Cambria";

            IList<InfraObject> infraobj = new List<InfraObject>();
            IList<InfraObject> infraobj2 = new List<InfraObject>();
            IList<Server> _servLST = new List<Server>();
            if (ddlCanId.SelectedItem.Text == "ALL")
            {

                IList<InfraObject> infraobj1 = Facade.GetAllInfraObject();
                foreach (InfraObject inf in infraobj1)
                {
                    BusinessService bs = Facade.GetBusinessServiceById(inf.BusinessServiceId);
                    if (!String.IsNullOrEmpty(bs.CANID))
                    {
                        inf.CANId=bs.CANID;
                        infraobj.Add(inf);
                    }
                }

                foreach (InfraObject _infser in infraobj)
                {
                    Server _ser = Facade.GetServerById(_infser.PRServerId);
                    if (_ser != null)
                    {
                        _ser.CANID=_infser.CANId;
                        _servLST.Add(_ser);
                    }

                    Server _ser1 = Facade.GetServerById(_infser.DRServerId);
                    if (_ser1 != null)
                    {
                        _ser1.CANID=_infser.CANId;
                        _servLST.Add(_ser1);
                    }
                }
            }
            else
            {
                int bsId = Convert.ToInt32(ddlCanId.SelectedItem.Value);
                // infraobj = Facade.GetInfraObjectByBusinessServiceId(bsId);
                var infradetails = Facade.GetInfraObjectByBusinessServiceId(bsId);
                var _busiService = Facade.GetBusinessServiceById(bsId);
                if (infradetails!=null && infradetails.Count()>0)
                {
                    foreach (InfraObject inf in infradetails)
                    {
                        inf.CANId=_busiService.CANID;
                        infraobj2.Add(inf);
                    }

                    foreach (InfraObject _infser in infraobj2)
                    {
                        Server _ser = Facade.GetServerById(_infser.PRServerId);
                        if (_ser != null)
                        {
                            _ser.CANID=_infser.CANId;
                            _servLST.Add(_ser);
                        }

                        Server _ser1 = Facade.GetServerById(_infser.DRServerId);
                        if (_ser1 != null)
                        {
                            _ser1.CANID=_infser.CANId;
                            _servLST.Add(_ser1);
                        }
                    }
                    infraobj = infraobj2;
                }
            }
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 48, 10, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 640, 10, 120, 13);
            string strlogo = LoggedInUserCompany.CompanyLogoPath;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 370, 10, 121, 13);
            }

            reportWorksheet.Cells["A1:F1"].RowHeight = 27;
            reportWorksheet.Cells["A2:F2"].RowHeight = 25;

            _cells["B5"].Formula = "Report Generated Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            _cells["C5"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));// dateTime;
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;

            int row = 8;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "Sr.No.";
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
            _cells["B8:F8"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B8:F8"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "CAN ID.";
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["D" + row.ToString()].Formula = "Server Name";
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["E" + row.ToString()].Formula = "Server IP";
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["F" + row.ToString()].Formula = "Server Type";
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

            row++;
            int dataCount = 0;
            int xlRow = 9;
            if ((_servLST!=null) && (_servLST.Count()>0))
            {
                foreach (var inf in _servLST)
                {
                    dataCount++;
                    int column = 0;
                    string[] xlColumn = { "B", "C", "D", "E", "F" };
                    xlRow++;

                    string ndx = xlColumn[column] + row.ToString();
                    _cells[ndx + ":" + "F" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;



                    _cells[ndx].Formula = i.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    i++;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = inf.CANID.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;


                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = inf.Name.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;


                    string serversr = string.Empty;
                    string serTYPE = string.Empty;
                    string serPR = string.Empty;
                    if (!string.IsNullOrEmpty(inf.IPAddress))
                    {
                        serPR = inf.IPAddress;
                        if (inf.Type.ToLower().ToString().Contains("pr"))
                        {
                            serTYPE="(PR)";
                        }
                        else if (inf.Type.ToLower().ToString().Contains("dr"))
                        {
                            serTYPE="(DR)";
                        }
                        else
                        {
                            serTYPE=inf.Type;
                        }
                        if (!serPR.Equals("-"))
                            serPR= CryptographyHelper.Md5Decrypt(serPR);


                        serversr = serPR+" "+ serTYPE;
                    }
                    else
                    {
                        serPR = inf.HostName;
                        if (inf.Type.ToLower().ToString().Contains("pr"))
                        {
                            serTYPE="(PR)";
                        }
                        else if (inf.Type.ToLower().ToString().Contains("dr"))
                        {
                            serTYPE="(DR)";
                        }
                        else
                        {
                            serTYPE=inf.Type;
                        }
                        serversr = serPR+" "+ serTYPE;
                    }





                    string server = Utility.putnewline(serversr, 20);
                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = server;
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].WrapText = true;
                    column++;

                    string dbPR = inf.Type;

                    if (!dbPR.ToString().ToLower().Equals("neardrdbserver"))
                        dbPR=dbPR.Replace("PR", "").Replace("DR", "").ToString();

                    string database = Utility.putnewline(dbPR, 20);
                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = database;
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].WrapText = true;
                    column++;

                    row++;
                }
            }
            IList<Server> serversExtra = new List<Server>();
            IList<Server> serversExtra1 = Facade.GetAllServers();
            IList<BusinessService> bss = Facade.GetAllBusinessServices();


            if (ddlCanId.SelectedItem.Text == "ALL")
            {
                IList<BusinessService> bs = Facade.GetAllBusinessServices();
                serversExtra1 = (from server in serversExtra1
                                 where !infraobj.Any(io => io.PRServerId == server.Id)
                                       && !infraobj.Any(io => io.DRServerId == server.Id)
                                       && bs.Any(bsi => bsi.Id == server.BusinessServiceId && !string.IsNullOrEmpty(bsi.CANID) && bsi.CANID != "0")
                                 select server).ToList();


                foreach (var serv in serversExtra1)
                {
                    var busiNesss = Facade.GetBusinessServiceById(serv.BusinessServiceId);
                    serv.CANID=busiNesss.CANID;
                    serversExtra.Add(serv);
                }

            }
            else
            {
                BusinessService bs = Facade.GetBusinessServiceById(Convert.ToInt32(ddlCanId.SelectedItem.Value));
                if ((infraobj!=null) && (infraobj.Count()>0))
                {


                    serversExtra1 = (from server in serversExtra1
                                     where !infraobj.Any(io => io.PRServerId == server.Id)
                                           && !infraobj.Any(io => io.DRServerId == server.Id)
                                           && bs.Id == server.BusinessServiceId && !string.IsNullOrEmpty(bs.CANID) && bs.CANID != "0"
                                     select server).ToList();


                    foreach (var serv in serversExtra1)
                    {
                        var busiNesss = Facade.GetBusinessServiceById(serv.BusinessServiceId);
                        serv.CANID=busiNesss.CANID;
                        serversExtra.Add(serv);
                    }
                }
                else
                {
                    IList<InfraObject> infraobj1chk = Facade.GetAllInfraObject();
                    serversExtra1 = (from server in serversExtra1
                                     where !infraobj.Any(io => io.PRServerId == server.Id)
                                           && !infraobj.Any(io => io.DRServerId == server.Id)
                                           && bs.Id == server.BusinessServiceId && !string.IsNullOrEmpty(bs.CANID) && bs.CANID != "0"
                                     select server).ToList();


                    foreach (var serv in serversExtra1)
                    {
                        var busiNesss = Facade.GetBusinessServiceById(serv.BusinessServiceId);
                        serv.CANID=busiNesss.CANID;
                        serversExtra.Add(serv);
                    }

                }


            }



            foreach (Server ser in serversExtra)
            {
                dataCount++;
                int column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F" };
                xlRow++;

                string ndx = xlColumn[column] + row.ToString();
                _cells[ndx + ":" + "F" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;



                _cells[ndx].Formula = i.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                i++;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = ser.CANID.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = ser.Name.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                string serversr = string.Empty;
                string serTYPE = string.Empty;
                string serPR = string.Empty;
                if (!string.IsNullOrEmpty(ser.IPAddress))
                {
                    serPR = ser.IPAddress;
                    if (ser.Type.ToLower().ToString().Contains("pr"))
                    {
                        serTYPE="(PR)";
                    }
                    else if (ser.Type.ToLower().ToString().Contains("dr"))
                    {
                        serTYPE="(DR)";
                    }
                    else
                    {
                        serTYPE=ser.Type;
                    }
                    if (!serPR.Equals("-"))
                        serPR= CryptographyHelper.Md5Decrypt(serPR);


                    serversr = serPR+" "+ serTYPE;
                }
                else
                {
                    serPR = ser.HostName;
                    if (ser.Type.ToLower().ToString().Contains("pr"))
                    {
                        serTYPE="(PR)";
                    }
                    else if (ser.Type.ToLower().ToString().Contains("dr"))
                    {
                        serTYPE="(DR)";
                    }
                    else
                    {
                        serTYPE=ser.Type;
                    }
                    serversr = serPR+" "+ serTYPE;
                }



                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula =serversr;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                _cells[ndx].WrapText = true;
                column++;



                string _typ = string.Empty;
                if (!ser.Type.ToString().ToLower().Equals("neardrdbserver"))
                {
                    _typ= ser.Type.Replace("PR", "").Replace("DR", "").ToString();
                }
                else
                {
                    _typ=ser.Type;
                }

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = _typ;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                _cells[ndx].WrapText = true;
                column++;




                row++;
            }



            _logger.Info("======" + infraobj.Count + " Records Retrieve for " + ddlCanId.SelectedItem.Text + " User======");
            _logger.Info(Environment.NewLine);

            int finalCount = dataCount + 10;
            _cells["B" + finalCount].Formula = "NA : Not Available";
            _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
            _cells["B" + finalCount].Font.Name = "Cambria";
            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

            reportWorksheet.ProtectContents = true;
            OpenExcelFile(reportWorkbook);

            _logger.Info("====== Servers Detail EXCEL Report generated ======");
            _logger.Info(Environment.NewLine);
        }
        private DataTable screenreport()
        {
            bool _chkfisrt = false;
            IList<InfraObject> infraobj = new List<InfraObject>();
            IList<InfraObject> infraobj1 = Facade.GetAllInfraObject();
            IList<InfraObject> infraobj2 = new List<InfraObject>();
            IList<Server> _servLST = new List<Server>();
            IList<Server> ServerDetails = Facade.GetAllServers();
            string _canID = string.Empty;
            if (ddlCanId.SelectedItem.Text == "ALL")
            {
                foreach (InfraObject inf in infraobj1)
                {
                    BusinessService bs = Facade.GetBusinessServiceById(inf.BusinessServiceId);
                    if (!String.IsNullOrEmpty(bs.CANID) && bs.CANID != "0")
                    {
                        inf.CANId=bs.CANID;
                        infraobj.Add(inf);

                    }
                }

                foreach (InfraObject _infser in infraobj)
                {
                    Server _ser = Facade.GetServerById(_infser.PRServerId);
                    if (_ser != null)
                    {
                        _ser.CANID=_infser.CANId;
                        _servLST.Add(_ser);
                    }

                    Server _ser1 = Facade.GetServerById(_infser.DRServerId);
                    if (_ser1 != null)
                    {
                        _ser1.CANID=_infser.CANId;
                        _servLST.Add(_ser1);
                    }
                }

            }
            else
            {
                int bsId = Convert.ToInt32(ddlCanId.SelectedItem.Value);
                var infradetails = Facade.GetInfraObjectByBusinessServiceId(bsId);

                var _busiService = Facade.GetBusinessServiceById(bsId);

                if ((infradetails!=null) && (infradetails.Count>0))
                {
                    foreach (InfraObject inf in infradetails)
                    {
                        inf.CANId=_busiService.CANID;
                        infraobj2.Add(inf);
                    }
                    foreach (InfraObject _infser in infraobj2)
                    {
                        Server _ser = Facade.GetServerById(_infser.PRServerId);
                        if (_ser != null)
                        {
                            _ser.CANID=_infser.CANId;
                            _servLST.Add(_ser);
                        }

                        Server _ser1 = Facade.GetServerById(_infser.DRServerId);
                        if (_ser1 != null)
                        {
                            _ser1.CANID=_infser.CANId;
                            _servLST.Add(_ser1);
                        }
                    }


                }

                infraobj = infraobj2;


            }

            var table = new DataTable();
            table.Columns.Add("Sr.No.");
            table.Columns.Add("CAN ID");
            table.Columns.Add("ServerName");
            table.Columns.Add("ServerIP");
            table.Columns.Add("ServerType");




            int i = 1;

            // btnview.Visible = false;
            var trow = new TableRow();
            tbl.Rows.Add(trow);
            trow.Height = 30;

            var sno = new TableCell { Text = "Sr.No.", CssClass = "RowStyleHeaderNo bold" };
            trow.Cells.Add(sno);
            var canId = new TableCell { Text = "CAN ID", CssClass = "RowStyleHeaderNo bold" };
            trow.Cells.Add(canId);
            var servername = new TableCell { Text = "ServerName", CssClass = "RowStyleHeaderNo bold" };
            trow.Cells.Add(servername);
            var serverlist = new TableCell { Text = "ServerIP", CssClass = "RowStyleHeaderNo bold" };
            trow.Cells.Add(serverlist);
            var dba = new TableCell { Text = "ServerType", CssClass = "rowStyleHeader bold" };
            trow.Cells.Add(dba);


            //////////////////////////////////////////////////////////////////////


            _logger.Info("======" + infraobj.Count + " Records Retrieve for " + ddlCanId.SelectedItem.Text + " User======");
            _logger.Info(Environment.NewLine);
            if (_servLST != null && _servLST.Count != 0)
            {
                foreach (Server inf in _servLST)
                {

                    var tbrow = new TableRow();
                    tbrow.Height = 50;
                    tbrow.BackColor = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                    tbl.Rows.Add(tbrow);
                    DataRow dr = table.NewRow();

                    dr["Sr.No."] = i.ToString();
                    var no = new TableCell { Text = i.ToString(), CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(no);

                    dr["CAN ID"] = inf.CANID.ToString();
                    var canid = new TableCell { Text = inf.CANID, CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(canid);

                    dr["ServerName"] = inf.Name.ToString();
                    var _servername = new TableCell { Text = inf.Name, CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(_servername);


                    string serversr = string.Empty;
                    string serTYPE = string.Empty;
                    string serPR = string.Empty;
                    if (!string.IsNullOrEmpty(inf.IPAddress))
                    {
                        serPR = inf.IPAddress;
                        if (inf.Type.ToLower().ToString().Contains("pr"))
                        {
                            serTYPE="(PR)";
                        }
                        else if (inf.Type.ToLower().ToString().Contains("dr"))
                        {
                            serTYPE="(DR)";
                        }
                        else
                        {
                            serTYPE=inf.Type;
                        }
                        if (!serPR.Equals("-"))
                            serPR= CryptographyHelper.Md5Decrypt(serPR);


                        serversr = serPR+" "+ serTYPE;
                    }
                    else
                    {
                        serPR = inf.HostName;
                        if (inf.Type.ToLower().ToString().Contains("pr"))
                        {
                            serTYPE="(PR)";
                        }
                        else if (inf.Type.ToLower().ToString().Contains("dr"))
                        {
                            serTYPE="(DR)";
                        }
                        else
                        {
                            serTYPE=inf.Type;
                        }
                        serversr = serPR+" "+ serTYPE;
                    }

                    string serverpdf = Utility.putnewline(serversr, 20);
                    dr["ServerIP"] = serverpdf;
                    var spr = new TableCell { Text = serversr, CssClass = "rowStyle1" };
                    tbrow.Cells.Add(spr);



                    string dbPR = inf.Type;
                    // string dbDR = inf.DRServerId != 0 ? Facade.GetServerById(inf.DRServerId).Type : "-";

                    if (!dbPR.ToString().ToLower().Equals("neardrdbserver"))
                        dbPR=dbPR.Replace("PR", "").Replace("DR", "").ToString();

                    //if (!dbDR.ToString().ToLower().Equals("neardrdbserver"))
                    //    dbDR=dbDR.Replace("PR", "").Replace("DR", "").ToString();


                    string databasesr = dbPR;
                    string databasepdf = Utility.putnewline(dbPR, 20);
                    dr["ServerType"] = databasepdf;
                    var dbpr = new TableCell { Text = databasesr, CssClass = "rowStyle1" };
                    tbrow.Cells.Add(dbpr);

                    i++;

                    table.Rows.Add(dr);
                }
            }


            else
            {
                //lblMsg.Visible = true;
                //lblMsg.Text = "No Records Found";
                //_logger.Info("====== Server Detail Report not generated ======");
                //_logger.Info(Environment.NewLine);
                //btnPdf.Visible = false;
                //btnExcel.Visible = false;

                _chkfisrt=true;
            }

            ///////////////////////////////////////////////////////////
            IList<Server> serversExtra = new List<Server>();
            IList<Server> serversExtra1 = Facade.GetAllServers();
            //IList<DatabaseBase> databaseExtra = Facade.GetAllDatabaseBases();
            if (ddlCanId.SelectedItem.Text == "ALL")
            {
                IList<BusinessService> bs = Facade.GetAllBusinessServices();
                serversExtra1 = (from server in serversExtra1
                                 where !infraobj.Any(io => io.PRServerId == server.Id)
                                       && !infraobj.Any(io => io.DRServerId == server.Id)
                                       && bs.Any(bsi => bsi.Id == server.BusinessServiceId && !string.IsNullOrEmpty(bsi.CANID) && bsi.CANID != "0")
                                 select server).ToList();

                foreach (var serv in serversExtra1)
                {
                    var busiNesss = Facade.GetBusinessServiceById(serv.BusinessServiceId);
                    serv.CANID=busiNesss.CANID;
                    serversExtra.Add(serv);
                }



            }
            else
            {
                BusinessService bs = Facade.GetBusinessServiceById(Convert.ToInt32(ddlCanId.SelectedItem.Value));
                IList<InfraObject> infraobj1chk = Facade.GetAllInfraObject();
                if ((infraobj!=null) && (infraobj.Count()>0))
                {
                    serversExtra1 = (from server in serversExtra1
                                     where !infraobj.Any(io => io.PRServerId == server.Id)
                                           && !infraobj.Any(io => io.DRServerId == server.Id)
                                           && bs.Id == server.BusinessServiceId && !string.IsNullOrEmpty(bs.CANID) && bs.CANID != "0"
                                     select server).ToList();
                }
                else
                {
                    serversExtra1 = (from server in serversExtra1
                                     where !infraobj1chk.Any(io => io.PRServerId == server.Id)
                                           && !infraobj1chk.Any(io => io.DRServerId == server.Id)
                                           && bs.Id == server.BusinessServiceId && !string.IsNullOrEmpty(bs.CANID) && bs.CANID != "0"
                                     select server).ToList();
                }

                foreach (var serv in serversExtra1)
                {
                    var busiNesss = Facade.GetBusinessServiceById(serv.BusinessServiceId);
                    serv.CANID=busiNesss.CANID;
                    serversExtra.Add(serv);
                }


            }

            if ((serversExtra!=null) &&(serversExtra.Count()>0))
            {
                foreach (Server ser in serversExtra)
                {
                    var tbrow = new TableRow();
                    tbrow.Height = 50;
                    tbrow.BackColor = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                    tbl.Rows.Add(tbrow);
                    DataRow dr = table.NewRow();

                    dr["Sr.No."] = i.ToString();
                    var no = new TableCell { Text = i.ToString(), CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(no);

                    dr["CAN ID"] = ser.CANID.ToString();
                    var _cnId = new TableCell { Text = ser.CANID.ToString(), CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(_cnId);

                    dr["ServerName"] = ser.Name.ToString();
                    var _servername = new TableCell { Text = ser.Name, CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(_servername);

                    string serversr = string.Empty;
                    string serTYPE = string.Empty;
                    string serPR = string.Empty;
                    if (!string.IsNullOrEmpty(ser.IPAddress))
                    {
                        serPR = ser.IPAddress;
                        if (ser.Type.ToLower().ToString().Contains("pr"))
                        {
                            serTYPE="(PR)";
                        }
                        else if (ser.Type.ToLower().ToString().Contains("dr"))
                        {
                            serTYPE="(DR)";
                        }
                        else
                        {
                            serTYPE=ser.Type;
                        }
                        if (!serPR.Equals("-"))
                            serPR= CryptographyHelper.Md5Decrypt(serPR);


                        serversr = serPR+" "+ serTYPE;
                    }
                    else
                    {
                        serPR = ser.HostName;
                        if (ser.Type.ToLower().ToString().Contains("pr"))
                        {
                            serTYPE="(PR)";
                        }
                        else if (ser.Type.ToLower().ToString().Contains("dr"))
                        {
                            serTYPE="(DR)";
                        }
                        else
                        {
                            serTYPE=ser.Type;
                        }
                        serversr = serPR+" "+ serTYPE;
                    }



                    dr["ServerIp"] = Utility.putnewline(serversr, 20);
                    var spr = new TableCell { Text = serversr, CssClass = "rowStyle1" };
                    tbrow.Cells.Add(spr);



                    string _typ = string.Empty;
                    if (!ser.Type.ToString().ToLower().Equals("neardrdbserver"))
                    {
                        _typ= ser.Type.Replace("PR", "").Replace("DR", "").ToString();
                    }
                    else
                    {
                        _typ=ser.Type;
                    }

                    dr["ServerType"] = Utility.putnewline(_typ, 30); ;
                    var dbpr = new TableCell { Text = _typ, CssClass = "rowStyle1" };
                    tbrow.Cells.Add(dbpr);



                    i++;

                    table.Rows.Add(dr);
                }
            }
            else
            {

                if (_chkfisrt)
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "No Records Found";
                    _logger.Info("====== Server Detail Report not generated ======");
                    _logger.Info(Environment.NewLine);
                    btnPdf.Visible = false;
                    btnExcel.Visible = false;
                }
            }




            return table;
        }
        protected void btnExcel_Click(object sender, EventArgs e)
        {
            try
            {
                screenreport();
                ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }



        protected void btnPdf_Click(object sender, EventArgs e)
        {
            try
            {
                var table = screenreport();
                CreatePdfReport(table);
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        private void CreatePdfReport(DataTable table)
        {
            _logger.Info("======Generating Infra Object Detail Report PDF View ======");
            _logger.Info(Environment.NewLine);
            var myPdfDocument = new PdfDocument(PdfDocumentFormat.InCentimeters(21, 29.7));
            var count = table.Rows.Count;
            PdfTable myPdfTable = myPdfDocument.NewTable(new Font("Verdana", 7), count, 5, 3);
            myPdfTable.ImportDataTable(table);
            myPdfTable.HeadersRow.SetColors(Color.White, Color.FromArgb(79, 129, 189));
            myPdfTable.HeadersRow.SetContentAlignment(ContentAlignment.MiddleLeft);
            myPdfTable.SetBorders(Color.Black, 0.1, BorderType.None);
            myPdfTable.SetColors(Color.Black, Color.FromArgb(219, 229, 241), Color.White);
            myPdfTable.SetColumnsWidth(new[] { 6, 30, 30, 30, 30 });
            myPdfTable.SetContentAlignment(ContentAlignment.MiddleLeft);
            myPdfTable.Columns[1].SetContentAlignment(ContentAlignment.MiddleLeft);

            //var getuseractvdate = Facade.GetUserActivityByStartEndDate(txtstart.Text, txtend.Text);


            PdfImage logoImage = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"));
            PdfImage logoBcms = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"));

            string strlogo = LoggedInUserCompany.CompanyLogoPath.ToString();

            PdfImage complogo = null;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                complogo = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(strlogo));
            }


            var pta = new PdfTextArea(new Font("Verdana", 9, FontStyle.Bold), Color.Black
                    , new PdfArea(myPdfDocument, 0, 20, 595, 80), ContentAlignment.MiddleCenter, "Server Detail Report By CANID");


            var RGT = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 30, 00, 200, 190), ContentAlignment.MiddleRight, "Report Generated Time : " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"))); //+ DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss"));


            //var notavailable = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
            //         , new PdfArea(myPdfDocument, 17, 15, 100, 190), ContentAlignment.MiddleRight, "NA : Not Available");



            int pgNo = 1;
            while (!myPdfTable.AllTablePagesCreated)
            {
                PdfPage newPdfPage = myPdfDocument.NewPage();
                PdfTablePage newPdfTablePage =
                        myPdfTable.CreateTablePage(new PdfArea(myPdfDocument, 48, 115, 500, 670));

                var pageNumber = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                   , new PdfArea(myPdfDocument, 50, 0, 450, 1600), ContentAlignment.MiddleRight, "Page Number :   " + pgNo++.ToString());

                newPdfPage.Add(logoImage, 460, 25, 180);
                newPdfPage.Add(logoBcms, 50, 25, 110);

                if (complogo != null)
                    newPdfPage.Add(complogo, 290, 25, 120);

                newPdfPage.Add(newPdfTablePage);
                newPdfPage.Add(pta);



                newPdfPage.Add(RGT);
                //newPdfPage.Add(notavailable);
                newPdfPage.Add(pageNumber);
                newPdfPage.SaveToDocument();
            }

            string str = DateTime.Now.ToString().Replace("/", "");
            str = str.Replace(":", "");
            str = str.Substring(0, str.Length - 5);
            str = System.Text.RegularExpressions.Regex.Replace(str, @"\s", "");
            str = "Server Detail By CANID" + str + ".pdf";
            string filePath = HttpContext.Current.Server.MapPath(@"~/PdfFiles/" + str);
            //string myUrl = "/PdfFiles/" + str;
            myPdfDocument.SaveToFile(filePath);

            string reportPath = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";

            string myUrl = reportPath + "PdfFiles/" + str;
            // string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            //string myUrl = reportPath + "/PdfFiles/" + str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Server Detail Report By CANID');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
            _logger.Info("====== Server Detail By CANID PDF Report generated ======");
            _logger.Info(Environment.NewLine);
        }

        protected void ddlBussService_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnview.Visible = true;
            Pan1.Visible = false;
        }

        private void OpenExcelFile(IWorkbook workbook)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "ServerDetailsReport" + str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            // string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            // string myUrl = reportPath + "/ExcelFiles/" + str;
            string baseUrl = Request.Url.Scheme + "://" + Request.Url.Authority + Request.ApplicationPath.TrimEnd('/') + "/";

            string myUrl = baseUrl + "ExcelFiles/" + str;

            //var myUrl = "/ExcelFiles/" + str;
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Server Detail Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }


        public string putnewline1(string str)
        {
            string strnew = string.Empty;
            var s = str.Split(new string[] { "&&**&&" }, StringSplitOptions.RemoveEmptyEntries);

            for (int i = 0; i < s.Length; i++)
            {

                strnew = strnew + s[i] + Environment.NewLine;

            }

            return strnew;
        }

    }
}