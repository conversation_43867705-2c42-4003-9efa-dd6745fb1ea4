﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
    internal sealed class ActionAnalyticBuilder : IEntityBuilder<ActionAnalytic>
    {

        IList<ActionAnalytic> IEntityBuilder<ActionAnalytic>.BuildEntities(IDataReader reader)
        {
            var AA = new List<ActionAnalytic>();

            while (reader.Read())
            {
                AA.Add(((IEntityBuilder<ActionAnalytic>)this).BuildEntity(reader, new ActionAnalytic()));
            }
            return (AA.Count > 0) ? AA : null;
        }

        ActionAnalytic IEntityBuilder<ActionAnalytic>.BuildEntity(IDataReader reader, ActionAnalytic aa)
        {
            aa.ActionId = Convert.IsDBNull(reader["ACTIONID"]) ? 0 : Convert.ToInt32(reader["ACTIONID"]);
            aa.ActionName = Convert.IsDBNull(reader["ACTIONNAME"]) ? string.Empty : Convert.ToString(reader["ACTIONNAME"]);
            aa.SolutionType = Convert.IsDBNull(reader["Type"]) ? SoultionType.Undefined : (SoultionType)Enum.Parse(typeof(SoultionType), Convert.ToString(reader["Type"]), true);
            aa.Total = Convert.IsDBNull(reader["TOTAL"]) ? 0 : Convert.ToInt32(reader["TOTAL"]);
            aa.Skip = Convert.IsDBNull(reader["SKIP"]) ? 0 : Convert.ToInt32(reader["SKIP"]);
            aa.Retry = Convert.IsDBNull(reader["RETRY"]) ? 0 : Convert.ToInt32(reader["RETRY"]);
            aa.Abort = Convert.IsDBNull(reader["ABORT"]) ? 0 : Convert.ToInt32(reader["ABORT"]);
            aa.Success = Convert.IsDBNull(reader["SUCCESS"]) ? 0 : Convert.ToInt32(reader["SUCCESS"]);
            aa.ActionConfigRTO = Convert.IsDBNull(reader["ACTIONCONFIGRTO"]) ? 0 : Convert.ToInt32(reader["ACTIONCONFIGRTO"]);
            aa.Completedoutofrto = Convert.IsDBNull(reader["Completedoutofrto"]) ? 0 : Convert.ToInt32(reader["Completedoutofrto"]);
            aa.Completedwithinrto = Convert.IsDBNull(reader["Completedwithinrto"]) ? 0 : Convert.ToInt32(reader["Completedwithinrto"]);
            aa.FailePercentage = Convert.IsDBNull(reader["FailePercentage"]) ? 0.0 : Convert.ToDouble(reader["FailePercentage"]);
            aa.SuccessPercentage = Convert.IsDBNull(reader["SuccessPercentage"]) ? 0.0 : Convert.ToDouble(reader["SuccessPercentage"]);
            aa.Failed = Convert.IsDBNull(reader["Failed"]) ? 0 : Convert.ToInt32(reader["Failed"]);
            aa.CreateDate = Convert.IsDBNull(reader["CREATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CREATEDATE"].ToString());
            aa.UpdateDate = Convert.IsDBNull(reader["UPDATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UPDATEDATE"].ToString());
            return aa;
        }
    }
}
