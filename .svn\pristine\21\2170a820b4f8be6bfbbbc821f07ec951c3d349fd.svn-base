﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="MSSqlDoubleTakeConfig.ascx.cs" Inherits="CP.UI.MSSqlDoubleTakeConfig" %>
<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>

<asp:UpdatePanel ID="upnlDoubleTake" runat="server" UpdateMode="Conditional">
    <ContentTemplate>
        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-head">
                    <h4 class="heading">
                        MSSQL Double Take(Full DB)</h4>
                </div>
                <div class="widget-body">
                    <div class="form-group">
                        <label class="col-replication" for="txtName">
                            Double Take Job Name<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtJobName" CssClass="form-control" style="width:49% !important;" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvtxtJobName" runat="server" CssClass="error" ControlToValidate="txtJobName"
                                Display="Dynamic" ErrorMessage="Please Enter Job Name"></asp:RequiredFieldValidator>
                        </div>
                    </div>   
                    
                     <div class="form-group">
                        <label class="col-replication" for="txtName">
                            Double Take Job Type<span class="inactive">*</span></label>
                        <div class="col-md-9">
                            <asp:TextBox ID="txtJobType" CssClass="form-control" style="width:49% !important;" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvtxtJobType" runat="server" CssClass="error" ControlToValidate="txtJobType"
                                Display="Dynamic" ErrorMessage="Please Enter Job Type"></asp:RequiredFieldValidator>
                        </div>
                    </div>                 
                </div>
                </div>
                </div>
                <div class="form-actions row">
                        <div class="col-lg-3">
                            <asp:Label ID="Label7" runat="server" Text="&nbsp;"></asp:Label>
                        </div>
                        <div class="col-lg-6" style="margin-left: 40.3%;">
                            <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save" OnClick ="btnSave_Click" />
                                
                            <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server"
                                Text="Cancel" CausesValidation="false" OnClick ="btnCancel_Click" />
                        </div>
                    </div>
    </ContentTemplate>
</asp:UpdatePanel>


