﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "SQLNative2008Monitor", Namespace = "http://www.BCMS.com/types")]
  public  class SQLNative2008Monitor :BaseEntity
    {
        #region Properties

        public string InfraObjectName { get; set; }

        public string ApplicationName { get; set; }

        public int InfraObjectId { get; set; }

        public string PRServer { get; set; }

        public string DRServer { get; set; }

        public string DatabaseName { get; set; }

        public string LastGenLog { get; set; }

        public string LastApplyLog { get; set; }

        public string LastCopiedLog { get; set; }

        public string LSNLastBackupLog { get; set; }

        public string LSNLastRestoredLog { get; set; }

        public string LSNLastCopiedLog { get; set; }

        public string LastLogGenTime { get; set; }

        public string LastLogApplTime { get; set; }

        public string LastLogCopyTime { get; set; }

        [DataMember]
        public string DataLag { get; set; }

        #endregion Properties
    }
}
