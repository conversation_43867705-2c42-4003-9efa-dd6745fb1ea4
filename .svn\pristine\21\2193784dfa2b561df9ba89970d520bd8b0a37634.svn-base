﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DROperationResult", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DROperationResult : BaseEntity
    {
        #region Properties

        [DataMember]
        public string ActionName { get; set; }

        [DataMember]
        public DateTime StartTime { get; set; }

        [DataMember]
        public DateTime EndTime { get; set; }

        [DataMember]
        public string ElapsedTime { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public int DROperationId { get; set; }

        [DataMember]
        public int ActionId { get; set; }

        [DataMember]
        public string Message { get; set; }

        #endregion Properties
    }
}