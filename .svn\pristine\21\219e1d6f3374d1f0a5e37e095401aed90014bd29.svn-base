﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.SybaseWithRSHADR_Repli_Monitoring
{
    internal sealed class SybaseWithRSHadrRepliMonitorDataAccess : BaseDataAccess, ISybaseWithRSHADR_RepliMonitorDataAccess
    {
         #region Constructors

        public SybaseWithRSHadrRepliMonitorDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<SybaseWithRSHADR_RepliMonitor> CreateEntityBuilder<SybaseWithRSHADR_RepliMonitor>()
        {
            return (new SybaseWithRSHadrRepliBuilder()) as IEntityBuilder<SybaseWithRSHADR_RepliMonitor>;
        }

        #endregion Constructors

        #region Methods

        SybaseWithRSHADR_RepliMonitor ISybaseWithRSHADR_RepliMonitorDataAccess.GetByInfraObjectId(int infraObjectId)
        {
            try
            {
                const string sp = "SybaseHADRMoniRepli_INFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<SybaseWithRSHADR_RepliMonitor>()).BuildEntity(reader, new SybaseWithRSHADR_RepliMonitor())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISybaseWithRSHADR_RepliMonitorDataAccess.GetByInfraObjectId(" +
                    infraObjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion
    }
}
