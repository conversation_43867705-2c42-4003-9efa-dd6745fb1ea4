﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="MongoDBDetailedMonitoring.ascx.cs" Inherits="CP.UI.Controls.MongoDBDetailedMonitoring" %>
<style>
    hr
    {margin:10px 0;}
    tr.hrtable td {
        padding:8px 0px !important;
    }
        tr.hrtable td > span {margin:0 0 0 8px;
        }
            tr.hrtable td > span.word-wrap {margin:0;
            }
                tr.hrtable td > span.word-wrap span[class$='-icon'] ,tr.hrtable td > span.word-wrap span[class^='icon-'],tr.hrtable td > span.word-wrap span.doc_icon {margin:0 0 0 8px;
               vertical-align: top; }
</style>
<div class="widget">
    <div class="widget-body">
        <div class="widget">
               <div class="widget-head">
                    <span  class="heading">MongoDB Monitor Details </span>
                </div>
            <div class="widget-body">
<table class="table table-bordered table-white" id="tblmonitor" runat="server">
    <thead>
        <tr>
            <th class="col-md-4">Component
            </th>
            <th class="col-md-4">Production Server
            </th>
            <th>DR Server
            </th>
        </tr>
    </thead>
    <tr>
        <td>IP Address
        </td>
        <td>
            <span id="Span1" class="health-up" runat="server">&nbsp;</span>
            <asp:Label ID="lblPrIPAddress" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span2" class="health-up" runat="server">&nbsp;</span>

            <asp:Label ID="lblDrIPAddress" runat="server" class="word-wrap"></asp:Label>
        </td>
    </tr>
    <tr>
        <td>Server Name
        </td>
        <td>
            <span id="Span5" class="icon-storagePR" runat="server">&nbsp;</span>

            <asp:Label ID="lblPrserverName" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span6" class="icon-storageDR" runat="server">&nbsp;</span>

            <asp:Label ID="lblDrserverName" runat="server" class="word-wrap"></asp:Label>
        </td>
    </tr>
    <tr>
        <td>MongoDB status
        </td>
        <td>
            <span id="Span7" class="icon-standby" runat="server">&nbsp;</span>
            <span>
                <asp:Label ID="lblPrMongodbStatus" runat="server"></asp:Label></span>
        </td>
        <td>
            <span id="Span8" class="icon-standby" runat="server">&nbsp;</span>
            <span>
                <asp:Label ID="lblDrMongodbStatus" runat="server"></asp:Label></span>
        </td>
    </tr>
   <%-- <tr>
        <td>Hostname
        </td>
        <td>
            <span id="Span9" class="host-icon" runat="server">&nbsp;</span>
            <span>
                <asp:Label ID="lblPrHostName" runat="server" ></asp:Label></span>
        </td>
        <td>
            <span id="Span10" class="host-icon" runat="server">&nbsp;</span>
            <span>
                <asp:Label ID="lblDrHostName" runat="server"></asp:Label></span>
        </td>
    </tr>
    <tr>
        <td>Version
        </td>
        <td>
            <span id="Span3" class="icon-edition" runat="server">&nbsp;</span>
            <span>
                <asp:Label ID="lblPrVersion" runat="server"></asp:Label></span>
        </td>
        <td>
            <span id="Span4" class="icon-edition" runat="server">&nbsp;</span>
            <span>
                <asp:Label ID="lblDrVersion" runat="server"></asp:Label></span>
        </td>
    </tr>--%>
</table>
                  <div class="widget-head">
                    <span class="heading">Database Level Monitoring </span>
                </div>
<table class="table table-bordered table-white" id="Table1" runat="server">
    <thead>
         <tr>
                             <th class="col-md-4">Component
                            </th>
                            <th class="col-md-4">Production Server
                            </th>
                            <th class="col-md-4">DR Server
                            </th>
                        </tr>
    </thead>
   <%-- <tr>
        <td class="font-bold">DB NAME
        </td>
        <td class="font-bold">Size on Disk
        </td>
        <td class="font-bold">Collections
        </td>
        <td class="font-bold">Documents
        </td>
        <td class="font-bold">Indexes
        </td>
    </tr>
    <tr class="hrtable">
        <td>
            <span id="Span21" class="icon-database" runat="server">&nbsp;</span>
            <asp:Label ID="lblDBNamePR" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span13" class="icon-disks" runat="server">&nbsp;</span>
            <asp:Label ID="lblDisksizePRAdmin" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span19" class="sequence-icon" runat="server">&nbsp;</span>
            <asp:Label ID="lblCollectionPRAdmin" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span29" class="doc_icon" runat="server">&nbsp;</span>
            <asp:Label ID="lblDocumentsPRAdmin" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span35" class="sequence-icon" runat="server">&nbsp;</span>
            <asp:Label ID="lblIndexesPRAdmin" runat="server" class="word-wrap"></asp:Label>
        </td>
    </tr>--%>

     <tr>
                        <td>Hostname
                        </td>
                        <td>
                            <span id="Span9" runat="server" class="host-icon">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPrHostName" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span10" runat="server" class="host-icon">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDrHostName" runat="server"></asp:Label></span>
                        </td>
                    </tr>
                    <tr>
                        <td>Version
                        </td>
                        <td>
                            <span id="Span3" runat="server" class="icon-edition">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPrVersion" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span4" runat="server" class="icon-edition">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDrVersion" runat="server"></asp:Label></span>
                        </td>
                    </tr>

                    <tr>
                        <td>DatabaseName
                        </td>
                        <td>
                            <span id="Span13" runat="server" class="icon-database">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPRDbName" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span17" runat="server" class="icon-database">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDRDbName" runat="server"></asp:Label></span>
                        </td>
                    </tr>

                    <tr>
                        <td>State Description
                        </td>
                        <td>
                            <span id="Span18" runat="server" class="id-icon">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPRDescription" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span19" runat="server" class="id-icon">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDRDescription" runat="server"></asp:Label></span>
                        </td>
                    </tr>

                    <tr>
                        <td>Health
                        </td>
                        <td>
                            <span id="Span20" runat="server" class="health-up">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPRHealth" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span21" runat="server" class="health-up">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDRHealth" runat="server"></asp:Label></span>
                        </td>
                    </tr>

                    <tr>
                        <td>LastHeartbeatMessage
                        </td>
                        <td>
                            <span id="Span22" runat="server" class="archive-log-blue">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPRhertmsg" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span23" runat="server" class="archive-log-blue">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDRhertmsg" runat="server"></asp:Label></span>
                        </td>
                    </tr>

                    <tr>
                        <td>Lag
                        </td>
                        <td>
                            <span id="Span24" runat="server" class="clock-icon-blue">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPRDatalag" runat="server" ></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span25" runat="server" class="clock-icon-blue">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDRDatalag" runat="server"></asp:Label></span>
                        </td>
                    </tr>


</table>

                 <div class="widget-head">
                    <span class="heading">Replication Monitoring </span>
                </div>

<table class="table table-bordered table-white" id="Table2" runat="server">
    <thead>
        <tr>
             <th class="col-md-4">Component
                            </th>
                            <th class="col-md-4">Production Server
                            </th>
                            <th>DR Server
                            </th>
        </tr>
    </thead>
  <%--  <tr>
        <td class="font-bold">DB NAME
        </td>
        <td class="font-bold">Size on Disk
        </td>
        <td class="font-bold">Collections
        </td>
        <td class="font-bold">Documents
        </td>
        <td class="font-bold">Indexes
        </td>
    </tr>
    <tr class="hrtable">
        <td>
            <span id="Span11" class="icon-database" runat="server">&nbsp;</span>
            <asp:Label ID="lblDBNameDR" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span12" class="icon-disks" runat="server">&nbsp;</span>
            <asp:Label ID="lblDisksizeDRAdmin" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span14" class="sequence-icon" runat="server">&nbsp;</span>
            <asp:Label ID="lblCollectionDRAdmin" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span15" class="doc_icon" runat="server">&nbsp;</span>
            <asp:Label ID="lblDocumentsDRAdmin" runat="server" class="word-wrap"></asp:Label>
        </td>
        <td>
            <span id="Span16" class="sequence-icon" runat="server">&nbsp;</span>
            <asp:Label ID="lblIndexesDRAdmin" runat="server" class="word-wrap"></asp:Label>
        </td>
    </tr>
  --%>

    <tr>
                        <td>ReplicaSet Name
                        </td>
                        <td>
                            <span id="Span11" runat="server" class="health-up">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPReplicsetname" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span12" runat="server" class="health-up">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDReplicsetname" runat="server"></asp:Label></span>
                        </td>
                    </tr>
                    <tr>
                        <td>_Id
                        </td>
                        <td>
                            <span id="Span14" runat="server" class="icon-edition">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPRmemberid" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span15" runat="server" class="icon-edition">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDRmemberid" runat="server"></asp:Label></span>
                        </td>
                    </tr>

                    <tr>
                        <td>Current Priority
                        </td>
                        <td>
                            <span id="Span16" runat="server" class="archive-log-blue">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPRPriority" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span26" runat="server" class="archive-log-blue">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDRPriority" runat="server"></asp:Label></span>
                        </td>
                    </tr>

                    <tr>
                        <td>Time Lag
                        </td>
                        <td>
                            <span id="Span27" runat="server" class="clock-icon-blue">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblPRLag" runat="server"></asp:Label></span>
                        </td>
                        <td>
                            <span id="Span28" runat="server" class="clock-icon-blue">&nbsp;</span>
                            <span>
                                <asp:Label ID="lblDRLag" runat="server"></asp:Label></span>
                        </td>
                    </tr>

</table>


</div>
 </div>
</div>
</div>
