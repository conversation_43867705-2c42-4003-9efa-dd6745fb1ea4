﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "MimixAvilability", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class MimixAvilability : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region Properties

        [DataMember]
        public string Activity { get; set; }

        [DataMember]
        public string Status { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }

        #endregion Properties
    }
}
