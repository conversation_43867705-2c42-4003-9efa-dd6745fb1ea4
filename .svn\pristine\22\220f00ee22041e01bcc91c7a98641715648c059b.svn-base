﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.BusinessEntity;

namespace CP.DataAccess
{
    internal sealed class ArchiveBuilder : IEntityBuilder<Archive>
    {
        #region IEntityBuilder<Archive> Members

        IList<Archive> IEntityBuilder<Archive>.BuildEntities(IDataReader reader)
        {
            var archives = new List<Archive>();

            while (reader.Read())
            {
                archives.Add(((IEntityBuilder<Archive>)this).BuildEntity(reader, new Archive()));
            }
            return (archives.Count > 0) ? archives : null;
        }

        Archive IEntityBuilder<Archive>.BuildEntity(IDataReader reader, Archive archive)
        {
            archive.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            archive.TableName = Convert.IsDBNull(reader["TableName"]) ? string.Empty : Convert.ToString(reader["TableName"]);
            archive.Period = Convert.IsDBNull(reader["Period"]) ? 0 : Convert.ToInt32(reader["Period"]);
            archive.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            archive.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            archive.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            archive.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            archive.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());

            return archive;
        }

        #endregion IEntityBuilder<Archive> Members
    }
}