﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class MimixAvilabilityBuilder : IEntityBuilder<MimixAvilability>
    {
        #region IEntityBuilder<MimixAvilability> Members

        IList<MimixAvilability> IEntityBuilder<MimixAvilability>.BuildEntities(IDataReader reader)
        {
            var mimixD = new List<MimixAvilability>();

            while (reader.Read())
            {
                mimixD.Add(((IEntityBuilder<MimixAvilability>)this).BuildEntity(reader, new MimixAvilability()));
            }

            return (mimixD.Count > 0) ? mimixD : null;
        }

        MimixAvilability IEntityBuilder<MimixAvilability>.BuildEntity(IDataReader reader, MimixAvilability mimix)
        {

            mimix.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            mimix.Activity = Convert.IsDBNull(reader["Activity"]) ? string.Empty : Convert.ToString(reader["Activity"]);
            mimix.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
            return mimix;
        }

        #endregion IEntityBuilder<MimixAvilability> Members
    }
}
