﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using System.Collections.Generic;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "vCenterProfile", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class vCenterProfile : BaseEntity
    {
        #region Properties
        [DataMember]
        public string ProfileName { get; set; }

        [DataMember]
        public int IsSelected { get; set; }

        [DataMember]
        public int vCenterServerId { get; set; }

        [DataMember]
        public string OSTYPE { get; set; }

        [DataMember]
        public IList<vCenterProfileDetails> vCenterProfileDetails { get; set; }

        [DataMember]
        public string ProfileType { get; set; }

        #endregion
    }
}
