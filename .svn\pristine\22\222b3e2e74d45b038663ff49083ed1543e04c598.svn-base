﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class SqlNativeMonitorBuilder : IEntityBuilder<SqlNativeMonitor>
    {
        IList<SqlNativeMonitor> IEntityBuilder<SqlNativeMonitor>.BuildEntities(IDataReader reader)
        {
            var sqllognative = new List<SqlNativeMonitor>();

            while (reader.Read())
            {
                sqllognative.Add(((IEntityBuilder<SqlNativeMonitor>)this).BuildEntity(reader, new SqlNativeMonitor()));
            }

            return (sqllognative.Count > 0) ? sqllognative : null;
        }

        SqlNativeMonitor IEntityBuilder<SqlNativeMonitor>.BuildEntity(IDataReader reader, SqlNativeMonitor sqllognative)
        {
            //const int fldId = 0;
            //const int fldGroupid = 1;
            //const int fldPrServer = 2;
            //const int fldDrServer = 3;
            //const int fldDatabaseName = 4;
            //const int fldLastGenLog = 5;
            //const int fldLastApplyLog = 6;
            //const int fldLastCopiedLog = 7;
            //const int fldLsNlastbackupLog = 8;
            //const int fldLsNlastrestoredLog = 9;
            //const int fldLsnLastCopiedLog = 10;
            //const int fldLastLogGenTime = 11;
            //const int fldLastLogApplTime = 12;
            //const int fldLastLogCopyTime = 13;
            //const int fldDatalag = 14;
            //const int fldIsactive = 15;
            //const int fldCreatorid = 16;
            //const int fldCreatedate = 17;
            //const int fldUpdatorid = 18;
            //const int fldUpdatedate = 19;

            //sqllognative.Id = reader.IsDBNull(fldId) ? 0 : reader.GetInt32(fldId);
            //sqllognative.GroupId = reader.IsDBNull(fldGroupid) ? 0 : reader.GetInt32(fldGroupid);
            //sqllognative.PRServer = reader.IsDBNull(fldPrServer) ? string.Empty : reader.GetString(fldPrServer);
            //sqllognative.DRServer = reader.IsDBNull(fldDrServer) ? string.Empty : reader.GetString(fldDrServer);
            //sqllognative.DatabaseName = reader.IsDBNull(fldDatabaseName) ? string.Empty : reader.GetString(fldDatabaseName);
            //sqllognative.LastGenLog = reader.IsDBNull(fldLastGenLog) ? string.Empty : reader.GetString(fldLastGenLog);
            //sqllognative.LastApplyLog = reader.IsDBNull(fldLastApplyLog) ? string.Empty : reader.GetString(fldLastApplyLog);
            //sqllognative.LastCopiedLog = reader.IsDBNull(fldLastCopiedLog) ? string.Empty : reader.GetString(fldLastCopiedLog);
            //sqllognative.LSNLastBackupLog = reader.IsDBNull(fldLsNlastbackupLog) ? string.Empty : reader.GetString(fldLsNlastbackupLog);
            //sqllognative.LSNLastRestoredLog = reader.IsDBNull(fldLsNlastrestoredLog) ? string.Empty : reader.GetString(fldLsNlastrestoredLog);
            //sqllognative.LSNLastCopiedLog = reader.IsDBNull(fldLsnLastCopiedLog) ? string.Empty : reader.GetString(fldLsnLastCopiedLog);
            //sqllognative.LastLogGenTime = reader.IsDBNull(fldLastLogGenTime) ? string.Empty : reader.GetString(fldLastLogGenTime);
            //sqllognative.LastLogApplTime = reader.IsDBNull(fldLastLogApplTime) ? string.Empty : reader.GetString(fldLastLogApplTime);
            //sqllognative.LastLogCopyTime = reader.IsDBNull(fldLastLogCopyTime) ? string.Empty : reader.GetString(fldLastLogCopyTime);
            //sqllognative.DataLag = reader.IsDBNull(fldDatalag) ? string.Empty : reader.GetString(fldDatalag);

            //sqllognative.IsActive = !reader.IsDBNull(fldIsactive) && reader.GetBoolean(fldIsactive);
            //sqllognative.CreatorId = reader.IsDBNull(fldCreatorid) ? 0 : reader.GetInt32(fldCreatorid);
            //sqllognative.CreateDate = reader.IsDBNull(fldCreatedate) ? DateTime.MinValue : reader.GetDateTime(fldCreatedate);
            //sqllognative.UpdatorId = reader.IsDBNull(fldUpdatorid) ? 0 : reader.GetInt32(fldUpdatorid);
            //sqllognative.UpdateDate = reader.IsDBNull(fldUpdatedate) ? DateTime.MinValue : reader.GetDateTime(fldUpdatedate);

            //Fields in bcms_sqlnative_monitor_status table on 23/07/2013 : Id, GroupId, PRServer, DRServer, DatabaseName, Last_GenLog, Last_ApplyLog, Last_CopiedLog, LSN_last_backupLog, LSN_last_restoredLog, LSN_Last_CopiedLog, Last_Log_Gen_Time, Last_Log_Appl_Time, Last_Log_Copy_Time, DataLag, IsActive, CreatorId, CreateDate, UpdatorId, UpdateDate

            sqllognative.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            sqllognative.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            sqllognative.PRServer = Convert.IsDBNull(reader["PRServer"])
                ? string.Empty
                : Convert.ToString(reader["PRServer"]);
            sqllognative.DRServer = Convert.IsDBNull(reader["DRServer"])
                ? string.Empty
                : Convert.ToString(reader["DRServer"]);
            sqllognative.DatabaseName = Convert.IsDBNull(reader["DatabaseName"])
                ? string.Empty
                : Convert.ToString(reader["DatabaseName"]);
            sqllognative.LastGenLog = Convert.IsDBNull(reader["Last_GenLog"])
                ? string.Empty
                : Convert.ToString(reader["Last_GenLog"]);
            sqllognative.LastApplyLog = Convert.IsDBNull(reader["Last_ApplyLog"])
                ? string.Empty
                : Convert.ToString(reader["Last_ApplyLog"]);
            sqllognative.LastCopiedLog = Convert.IsDBNull(reader["Last_CopiedLog"])
                ? string.Empty
                : Convert.ToString(reader["Last_CopiedLog"]);
            sqllognative.LSNLastBackupLog = Convert.IsDBNull(reader["LSN_last_backupLog"])
                ? string.Empty
                : Convert.ToString(reader["LSN_last_backupLog"]);
            sqllognative.LSNLastRestoredLog = Convert.IsDBNull(reader["LSN_last_restoredLog"])
                ? string.Empty
                : Convert.ToString(reader["LSN_last_restoredLog"]);
            sqllognative.LSNLastCopiedLog = Convert.IsDBNull(reader["LSN_Last_CopiedLog"])
                ? string.Empty
                : Convert.ToString(reader["LSN_Last_CopiedLog"]);
            sqllognative.LastLogGenTime = Convert.IsDBNull(reader["Last_Log_Gen_Time"])
                ? string.Empty
                : Convert.ToString(reader["Last_Log_Gen_Time"]);
            sqllognative.LastLogApplTime = Convert.IsDBNull(reader["Last_Log_Appl_Time"])
                ? string.Empty
                : Convert.ToString(reader["Last_Log_Appl_Time"]);
            sqllognative.LastLogCopyTime = Convert.IsDBNull(reader["Last_Log_Copy_Time"])
                ? string.Empty
                : Convert.ToString(reader["Last_Log_Copy_Time"]);
            sqllognative.DataLag = Convert.IsDBNull(reader["DataLag"])
                ? string.Empty
                : Convert.ToString(reader["DataLag"]);

            sqllognative.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            sqllognative.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            sqllognative.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"]);
            sqllognative.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            sqllognative.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);

            return sqllognative;
        }
    }
}