﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;

namespace CP.UI
{
    public partial class InfrastructureMonitorList : InfrastructureMonitorBasePage
    {
        #region variable

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.InfrastructureMonitor;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.InfrastructureMonitorList;
                }
                return string.Empty;
            }
        }

        #endregion variable

        public override void PrepareView()
        {
            //if (IsUserManager)
            //{
            //    Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);
            //}

            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }

            Utility.SelectMenu(Master, "Module3");
            BindList();
        }

        private void BindList()
        {
            var infraMonitor = GetInfrastructureMonitorList();

            var infraObject = Facade.GetAllInfraObject();
            var server = Facade.GetAllServers();

            var infraObjMon = from im in infraMonitor
                              join io in infraObject on im.InfraObjectId equals io.Id
                              where io.IsActive == 1
                              select im;

            var infraObjSvr = (from im in infraObjMon join svr in server on im.ServerId equals svr.Id where svr.IsActive == 1 select im).ToList();

            if (infraObjSvr != null)
            {
                if (infraObjSvr.Count > 0)
                {
                    lvInfrastructureMonitor.DataSource = infraObjSvr;
                    lvInfrastructureMonitor.DataBind();
                }
            }
        }

        private IList<Infrastructure> GetInfrastructureMonitorList()
        {
            return Facade.GetInfrastructureByCompanyIdAndRole(LoggedInUserCompanyId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, true);
        }

        protected void lvInfrastructureMonitorItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                var lbl = (lvInfrastructureMonitor.Items[e.ItemIndex].FindControl("Id")) as Label;
                //var lblinfra = (lvInfrastructureMonitor.Items[e.ItemIndex].FindControl("lblInfraObject")) as Label;

                if (lbl != null && ValidateRequest("Infrastructure Delete", UserActionType.DeleteServerComponent))
                {
                    Facade.DeleteInfrastructureById(Convert.ToInt32(lbl.Text));

                    ActivityLogger.AddLog(LoggedInUserName, "Infrastructure", UserActionType.DeleteServerComponent, "The Infrastructure Id'" + lbl.Text + "' was deleted", LoggedInUserId);

                    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Infrastructure Id" + " " + '"' + lbl.Text + '"', TransactionType.Delete));
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void lvInfrastructureMonitorPreRender(object sender, EventArgs e)
        {
            //if (IsPostBack)
            //{
            //    lvInfrastructureMonitor.DataSource = GetInfrastructureMonitorList();
            //    lvInfrastructureMonitor.DataBind();
            //}
        }

        protected void lvInfrastructureMonitorItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl = (lvInfrastructureMonitor.Items[e.NewEditIndex].FindControl("ID")) as Label;
            var lblApplicationName = (lvInfrastructureMonitor.Items[e.NewEditIndex].FindControl("lblInfraObject")) as Label;
            ActivityLogger.AddLog(LoggedInUserName, "InfrastructureMonitor", UserActionType.UpdateMonitorProfileDetails, "The InfrastructureMonitor '" + lblApplicationName.Text + "' Opened as Editing Mode.", LoggedInUserId);
           
            if (lbl != null  && ValidateRequest("Infrastructure Edit", UserActionType.UpdateMonitorProfileDetails))
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.InfrastructureId,
                                                     lbl.Text);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvInfrastructureMonitor_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                Label lblInfraObject = (Label)e.Item.FindControl("lblInfraObject");
                Label lblServer = (Label)e.Item.FindControl("lblServer");

                ListViewDataItem dataItem = (ListViewDataItem)e.Item;
                Infrastructure rowView = dataItem.DataItem as Infrastructure;

                //System.Data.DataRowView rowView = e.Item as System.Data.DataRowView;

                int _infraObjectId = rowView.InfraObjectId;
                int _serverId = rowView.ServerId;

                //var _infraObject = Facade.GetInfraObjectById(_infraObjectId);
                var _infraObject = Facade.GetInfraObjectById(_infraObjectId);
                if (_infraObject != null)
                lblInfraObject.Text = _infraObject.Name.ToString();

                var _serverName = Facade.GetServerById(_serverId);
                if (_serverName != null)
                lblServer.Text = _serverName.Name.ToString();
            }

            var edit = e.Item.FindControl("ImgEdit") as ImageButton;
            var delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (!IsSuperAdmin && !IsUserAdmin)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
            if (IsUserOperator||IsUserManager)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }

        protected void lvInfrastructureMonitor_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected void BtnSearchClick(object sender, EventArgs e)
        {
            var infraMonitor = GetInfrastructureMonitorList();
            if (infraMonitor != null)
            {
                var infraObject = Facade.GetAllInfraObject();
                var server = Facade.GetAllServers();
                if (!txtsearchvalue.Text.Trim().IsNullOrEmpty())
                {
                    var infraObjMon = from im in infraMonitor
                                      join io in infraObject on im.InfraObjectId equals io.Id
                                      where io.IsActive == 1 && io.Name.ToLower().Contains(txtsearchvalue.Text.Trim())
                                      select im;

                    var infraObjSvr = (from im in infraObjMon join svr in server on im.ServerId equals svr.Id where svr.IsActive == 1 select im).ToList();

                    if (infraObjSvr != null)
                    {
                        if (infraObjSvr.Count > 0)
                        {
                            lvInfrastructureMonitor.DataSource = infraObjSvr;
                            lvInfrastructureMonitor.DataBind();

                        }
                    }
                }
                else
                {
                    var infraObjMon = from im in infraMonitor
                                      join io in infraObject on im.InfraObjectId equals io.Id
                                      where io.IsActive == 1
                                      select im;
                    var infraObjSvr = (from im in infraObjMon join svr in server on im.ServerId equals svr.Id where svr.IsActive == 1 select im).ToList();

                    if (infraObjSvr != null)
                    {
                        if (infraObjSvr.Count > 0)
                        {
                            lvInfrastructureMonitor.DataSource = infraObjSvr;
                            lvInfrastructureMonitor.DataBind();

                        }
                    }
                }
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
    }
}