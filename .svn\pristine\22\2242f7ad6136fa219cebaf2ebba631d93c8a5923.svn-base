﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class AppDepMappingHostsDataAccess : BaseDataAccess, IAppDepMappingHosts
    {
        #region Constructors

        public AppDepMappingHostsDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<AppDepMappingHosts> CreateEntityBuilder<AppDepMappingHosts>()
        {
            return (new AppDepMappingHostsBuilder()) as IEntityBuilder<AppDepMappingHosts>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        /// Inserts or updates application dependency mapping hosts details
        /// </summary>
        /// <param name="AppDepMappingHosts">AppDepMappingHosts</param>
        /// <returns>AppDepMappingHosts</returns>
        /// <author>Ram mahajan-20/12/2014</author>
        AppDepMappingHosts IAppDepMappingHosts.AddOrUpdate(AppDepMappingHosts appDepMappingHosts)
        {
            try
            {
                const string sp = "appdepmappinghosts_InsOrUpd";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iHost", DbType.AnsiString, appDepMappingHosts.Host);
                    Database.AddInParameter(cmd, Dbstring + "iState", DbType.AnsiString, appDepMappingHosts.State);
                    Database.AddInParameter(cmd, Dbstring + "iLastBoot", DbType.AnsiString, appDepMappingHosts.LastBoot);
                    Database.AddInParameter(cmd, Dbstring + "iOperatingSystem", DbType.AnsiString, appDepMappingHosts.OperatingSystem);
                    Database.AddInParameter(cmd, Dbstring + "iAllApps", DbType.AnsiString, appDepMappingHosts.AllApps);
                    Database.AddInParameter(cmd, Dbstring + "iApplicationCount", DbType.Int32, appDepMappingHosts.ApplicationCount);
                    Database.AddInParameter(cmd, Dbstring + "iDependencyProfileName", DbType.AnsiString, appDepMappingHosts.DependencyProfileName);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_appdep"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<AppDepMappingHosts>()).BuildEntity(reader, new AppDepMappingHosts()) : null;

                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting/updating application dependency mapping hosts Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            //return appDepMappingHosts;
        }

        /// <summary>
        /// Gets Application dependency mapping hosts by dependencyProfileName
        /// </summary>
        /// <param name="dependencyProfileName">dependencyProfileName</param>
        /// <returns>List of AppDepMappingHosts</returns>
        /// <author>Ram mahajan-20/12/2014</author>
        IList<AppDepMappingHosts> IAppDepMappingHosts.GetAllAppDepMapHostsByDepProfile(string dependencyProfileName)
        {
            try
            {
                const string sp = "GetAllDepMapHostsByDepProfiles";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDependencyProfileName", DbType.AnsiString, dependencyProfileName);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<AppDepMappingHosts>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAppDepMappingHosts.GetAllAppDepMapHostsByDepProfile(" +
                    dependencyProfileName + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<AppDepMappingHosts> IAppDepMappingHosts.GetAllAppDepMapHostsByDepProfileandbysourcehost(string dependencyProfileName, string sourcehost)
        {
            try
            {
                const string sp = "get_appdepbyprofile";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "idepprofile", DbType.AnsiString, dependencyProfileName);
                    Database.AddInParameter(cmd, Dbstring + "isourcehost", DbType.AnsiString, sourcehost);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    var appdepmaplist = new List<AppDepMappingHosts>();

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            AppDepMappingHosts apphost = new AppDepMappingHosts();
                            apphost.Targethost = Convert.IsDBNull(reader["TargetHost"]) ? string.Empty : Convert.ToString(reader["TargetHost"]);

                            apphost.Host = Convert.IsDBNull(reader["SourceHost"]) ? "NA" : Convert.ToString(reader["SourceHost"]);
                            apphost.OperatingSystem = Convert.IsDBNull(reader["tos"]) ? "NA" : Convert.ToString(reader["tos"]);
                            apphost.AllApps = Convert.IsDBNull(reader["tAllApps"]) ? "NA" : Convert.ToString(reader["tAllApps"]);
                            apphost.Entity = Convert.IsDBNull(reader["TargetEntity"]) ? "NA" : Convert.ToString(reader["TargetEntity"]);
                            apphost.DependencyProfileName = Convert.IsDBNull(reader["DependencyProfileName"]) ? "NA" : Convert.ToString(reader["DependencyProfileName"]);
                            apphost.HostEntity = Convert.IsDBNull(reader["SourceEntity"]) ? "NA" : Convert.ToString(reader["SourceEntity"]);
                            apphost.HostOS = Convert.IsDBNull(reader["OperatingSystem"]) ? "NA" : Convert.ToString(reader["OperatingSystem"]);
                            apphost.HostAllApps = Convert.IsDBNull(reader["AllApps"]) ? "NA" : Convert.ToString(reader["AllApps"]);
                            appdepmaplist.Add(apphost);
                        }
                        return appdepmaplist;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IAppDepMappingHosts.GetAllAppDepMapHostsByDepProfile(" +
                    dependencyProfileName + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #region DDE TagPort
        
        bool IAppDepMappingHosts.UpdateMappingHostByProfile(AppDepMappingHosts appDepMappingHosts)
        {
            try
            {
                const string sp = "appdependoUpdateApps";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iHost", DbType.AnsiString, appDepMappingHosts.Host);
                    Database.AddInParameter(cmd, Dbstring + "iAllApps", DbType.AnsiString, appDepMappingHosts.AllApps);
                    Database.AddInParameter(cmd, Dbstring + "iDependencyProfileName", DbType.AnsiString, appDepMappingHosts.DependencyProfileName);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_appdep"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return true;

                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While UpdateMappingHostByProfile mapping hosts Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return false;
            //return appDepMappingHosts;
        }

        IList<AppDepStandardPorts> IAppDepMappingHosts.GetStandardPorts()
        {
            try
            {
                const string sp = "appdepGetAllStandardPorts";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_appdep"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        var lststandardposts = new List<AppDepStandardPorts>();
                        while (reader.Read())
                        {
                            AppDepStandardPorts port = new AppDepStandardPorts();
                            port.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            port.PortNumber = Convert.IsDBNull(reader["PortNumber"]) ? 0 : Convert.ToInt32(reader["PortNumber"]);
                            port.PortName = Convert.IsDBNull(reader["PortName"]) ? string.Empty : Convert.ToString(reader["PortName"]);
                            lststandardposts.Add(port);
                        }
                        return lststandardposts;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While GetStandardPorts  : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            //return appDepMappingHosts;
        }


        #endregion DDE TagPort

        #endregion Methods
    }
}
