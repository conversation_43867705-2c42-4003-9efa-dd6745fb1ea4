﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class CompanyProfileBuilder : IEntityBuilder<CompanyProfile>
    {
        IList<CompanyProfile> IEntityBuilder<CompanyProfile>.BuildEntities(IDataReader reader)
        {
            var companyProfiles = new List<CompanyProfile>();

            while (reader.Read())
            {
                companyProfiles.Add(((IEntityBuilder<CompanyProfile>)this).BuildEntity(reader, new CompanyProfile()));
            }

            return (companyProfiles.Count > 0) ? companyProfiles : null;
        }

        CompanyProfile IEntityBuilder<CompanyProfile>.BuildEntity(IDataReader reader, CompanyProfile companyProfile)
        {
            //const int FLD_ID = 0;
            //const int FLD_NAME = 1;
            //const int FLD_DISPLAYNAME = 2;
            //const int FLD_ISPARENT = 3;
            //const int FLD_PARENTID = 4;
            //const int FLD_COMPANYLOGOPATH = 5;
            //const int FLD_ISACTIVE = 6;
            //const int FLD_CREATORID = 7;
            //const int FLD_CREATEDATE = 8;
            //const int FLD_UPDATORID = 9;
            //const int FLD_UPDATEDATE = 10;

            //companyProfile.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //companyProfile.Name = reader.IsDBNull(FLD_NAME) ? string.Empty : reader.GetString(FLD_NAME);
            //companyProfile.DisplayName = reader.IsDBNull(FLD_DISPLAYNAME) ? string.Empty : reader.GetString(FLD_DISPLAYNAME);
            //companyProfile.IsParent =!reader.IsDBNull(FLD_ISPARENT) && reader.GetBoolean(FLD_ISPARENT);
            //companyProfile.ParentId = reader.IsDBNull(FLD_PARENTID) ? 0 : reader.GetInt32(FLD_PARENTID);
            //companyProfile.CompanyLogoPath = reader.IsDBNull(FLD_COMPANYLOGOPATH) ? string.Empty : reader.GetString(FLD_COMPANYLOGOPATH);
            //companyProfile.IsActive = !reader.IsDBNull(FLD_ISACTIVE) && reader.GetBoolean(FLD_ISACTIVE);
            //companyProfile.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //companyProfile.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);
            //companyProfile.UpdatorId = reader.IsDBNull(FLD_UPDATORID) ? 0 : reader.GetInt32(FLD_UPDATORID);
            //companyProfile.UpdateDate = reader.IsDBNull(FLD_UPDATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_UPDATEDATE);

            //Fields in bcms_company_profile table on 16/07/2013 : Id, Name, DisplayName, IsParent, ParentId, CompanyLogoPath, IsActive, CreatorId, CreateDate, UpdatorId, UpdateDate

            companyProfile.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            companyProfile.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
            companyProfile.DisplayName = Convert.IsDBNull(reader["DisplayName"])
                ? string.Empty
                : Convert.ToString(reader["DisplayName"]);

            if (!Convert.IsDBNull(reader["IsParent"]))
                companyProfile.IsParent = Convert.ToBoolean(reader["IsParent"]);

            companyProfile.ParentId = Convert.IsDBNull(reader["ParentId"]) ? 0 : Convert.ToInt32(reader["ParentId"]);
            companyProfile.CompanyLogoPath = Convert.IsDBNull(reader["CompanyLogoPath"])
                ? string.Empty
                : Convert.ToString(reader["CompanyLogoPath"]);

            companyProfile.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);

            companyProfile.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            companyProfile.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            companyProfile.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            companyProfile.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());
            companyProfile.CompanyId = Convert.IsDBNull(reader["CompanyId"])
          ? string.Empty
          : Convert.ToString(reader["CompanyId"]);
            return companyProfile;
        }
    }
}