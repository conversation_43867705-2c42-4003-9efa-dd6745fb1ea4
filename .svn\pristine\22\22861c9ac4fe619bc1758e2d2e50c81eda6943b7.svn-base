﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class EMCSRDFStarDataAccess : BaseDataAccess, IEMCSRDFStarDataAccess
    {
        #region Constructors

        public EMCSRDFStarDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<EMCSRDFStar> CreateEntityBuilder<EMCSRDFStar>()
        {
            return (new EMCSRDFStarBuilder()) as IEntityBuilder<EMCSRDFStar>;
        }

        #endregion Constructors

        EMCSRDFStar IEMCSRDFStarDataAccess.AddEMCSRDFStar(EMCSRDFStar emcsrdfrepli)
        {
            try
            {
                const string sp = "EMCSRDFSTAR_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iREPLICATONID", DbType.Int32, emcsrdfrepli.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iSERVERID", DbType.Int32, emcsrdfrepli.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iSymCLIPath", DbType.AnsiString, emcsrdfrepli.SymCLIPath);
                    Database.AddInParameter(cmd, Dbstring + "iCGName", DbType.AnsiString, emcsrdfrepli.CGName);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, emcsrdfrepli.CreatorId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        emcsrdfrepli = reader.Read()
                            ? CreateEntityBuilder<EMCSRDFStar>().BuildEntity(reader, emcsrdfrepli)
                            : null;
                    }

                    return emcsrdfrepli;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting EMCSRDFSG Replication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        EMCSRDFStar IEMCSRDFStarDataAccess.UpdateEMCSRDFStar(EMCSRDFStar emcsrdfrepliupdate)
        {
            try
            {
                const string sp = "EMCSRDFSTAR_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, emcsrdfrepliupdate.Id);
                    Database.AddInParameter(cmd, Dbstring + "iREPLICATONID", DbType.Int32, emcsrdfrepliupdate.ReplicationId);
                    Database.AddInParameter(cmd, Dbstring + "iSERVERID", DbType.Int32, emcsrdfrepliupdate.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iSymCLIPath", DbType.AnsiString, emcsrdfrepliupdate.SymCLIPath);
                    Database.AddInParameter(cmd, Dbstring + "iCGName", DbType.AnsiString, emcsrdfrepliupdate.CGName);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, emcsrdfrepliupdate.UpdatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        emcsrdfrepliupdate = reader.Read()
                            ? CreateEntityBuilder<EMCSRDFStar>().BuildEntity(reader, emcsrdfrepliupdate)
                            : null;
                    }



                    return emcsrdfrepliupdate;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating EMCSRDFSG Storage Replication Entry " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
            throw new NotImplementedException();
        }

        EMCSRDFStar IEMCSRDFStarDataAccess.GetEMCSRDFStarbyReplicationId(int id)
        {
            try
            {
                const string sp = "EmcsrdfStar_GetByReplicationId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<EMCSRDFStar>()).BuildEntity(reader,
                                new EMCSRDFStar());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IEMCSRDFSGDataAccess.GetEMCSRDFSGbyReplicationId(" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public IList<EMCSRDFStar> EMCSRDFStar_GetAllByReplicationType(string replicationtype)
        {
            IList<EMCSRDFStar> lstEMCSRDFStarRepli = new List<EMCSRDFStar>();

            try
            {

                const string sp = "EmcsrdfStar_GetAllReplication";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iTypeId", DbType.String, replicationtype);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            EMCSRDFStar objEMCSRDFStar = new EMCSRDFStar();

                            objEMCSRDFStar.ReplicationId = reader["ReplicationId"] != null ? Convert.ToInt32(reader["ReplicationId"]) : 0;
                            objEMCSRDFStar.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
                            objEMCSRDFStar.SymCLIPath = Convert.IsDBNull(reader["SymCLIPath"]) ? string.Empty : Convert.ToString(reader["SymCLIPath"]);
                            objEMCSRDFStar.CGName = Convert.IsDBNull(reader["CGName"]) ? string.Empty : Convert.ToString(reader["CGName"]);
                            objEMCSRDFStar.ReplicationBase.Id = reader["ReplicationId"] != null ? Convert.ToInt32(reader["ReplicationId"]) : 0;
                            objEMCSRDFStar.ReplicationBase.reptype = reader["ReplicationType"] != null ? Convert.ToString(reader["ReplicationType"]) : string.Empty;
                            objEMCSRDFStar.ReplicationBase.Name = reader["ReplicationName"] != null ? Convert.ToString(reader["ReplicationName"]) : string.Empty;

                            lstEMCSRDFStarRepli.Add(objEMCSRDFStar);
                        }
                    }
                }

            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return lstEMCSRDFStarRepli;
        }

        IList<EMCSRDFStar> IEMCSRDFStarDataAccess.EMCSRDFStarReplication_GetAll()
        {
            const string sp = "EMCSRDFStarRepli_GetAll"; 
            using (DbCommand cmd = Database.GetStoredProcCommand(sp))
            {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                using (IDataReader reader = Database.ExecuteReader(cmd))
                {
                    return CreateEntityBuilder<EMCSRDFStar>().BuildEntities(reader);
                }
            }
        }

    }
}
