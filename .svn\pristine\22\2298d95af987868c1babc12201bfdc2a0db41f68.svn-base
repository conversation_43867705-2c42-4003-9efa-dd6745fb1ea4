﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class MimixDatalagBuilder : IEntityBuilder<MimixDatalag>
    {
        #region IEntityBuilder<MimixDatalag> Members

        IList<MimixDatalag> IEntityBuilder<MimixDatalag>.BuildEntities(IDataReader reader)
        {
            var mimixD = new List<MimixDatalag>();

            while (reader.Read())
            {
                mimixD.Add(((IEntityBuilder<MimixDatalag>)this).BuildEntity(reader, new MimixDatalag()));
            }

            return (mimixD.Count > 0) ? mimixD : null;
        }

        MimixDatalag IEntityBuilder<MimixDatalag>.BuildEntity(IDataReader reader, MimixDatalag mimix)
        {           

            mimix.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            //mimix.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            mimix.DataGroup = Convert.IsDBNull(reader["DataGroup"]) ? string.Empty : Convert.ToString(reader["DataGroup"]);
            mimix.DatabaseErrors = Convert.IsDBNull(reader["DatabaseErrors"]) ? string.Empty : Convert.ToString(reader["DatabaseErrors"]);
            mimix.ElapsedTime = Convert.IsDBNull(reader["ElapsedTime"]) ? string.Empty : Convert.ToString(reader["ElapsedTime"]);
            mimix.ObjectInErrorAndActiv = Convert.IsDBNull(reader["ObjectInErrorAndActiv"]) ? string.Empty : Convert.ToString(reader["ObjectInErrorAndActiv"]);
            mimix.TransferDefinition = Convert.IsDBNull(reader["TransferDefinition"]) ? string.Empty : Convert.ToString(reader["TransferDefinition"]);
            mimix.State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]);
            mimix.DatabaseName = Convert.IsDBNull(reader["DatabaseName"]) ? string.Empty : Convert.ToString(reader["DatabaseName"]);
            mimix.Object = Convert.IsDBNull(reader["Object"]) ? string.Empty : Convert.ToString(reader["Object"]);
            mimix.DBSourceReceiver = Convert.IsDBNull(reader["DBSourceReceiver"]) ? string.Empty : Convert.ToString(reader["DBSourceReceiver"]);
            mimix.DBTargetReceiver = Convert.IsDBNull(reader["DBTargetReceiver"]) ? string.Empty : Convert.ToString(reader["DBTargetReceiver"]);
            mimix.DBLastReadReceiver = Convert.IsDBNull(reader["DBLastReadReceiver"]) ? string.Empty : Convert.ToString(reader["DBLastReadReceiver"]);
            mimix.DBSourceSequence = Convert.IsDBNull(reader["DBSourceSequence"]) ? string.Empty : Convert.ToString(reader["DBSourceSequence"]);
            mimix.DBTargetSequence = Convert.IsDBNull(reader["DBTargetSequence"]) ? string.Empty : Convert.ToString(reader["DBTargetSequence"]);
            mimix.DBLastReadSequence = Convert.IsDBNull(reader["DBLastReadSequence"]) ? string.Empty : Convert.ToString(reader["DBLastReadSequence"]);
            mimix.DBSourceDateTime = Convert.IsDBNull(reader["DBSourceDateTime"]) ? string.Empty : Convert.ToString(reader["DBSourceDateTime"]);
            mimix.DBTargetDateTime = Convert.IsDBNull(reader["DBTargetDateTime"]) ? string.Empty : Convert.ToString(reader["DBTargetDateTime"]);
            mimix.DBLastReadDateTime = Convert.IsDBNull(reader["DBLastReadDateTime"]) ? string.Empty : Convert.ToString(reader["DBLastReadDateTime"]);
            mimix.DBSourceTransPerHour = Convert.IsDBNull(reader["DBSourceTransPerHour"]) ? string.Empty : Convert.ToString(reader["DBSourceTransPerHour"]);
            mimix.DBTargetTransPerHour = Convert.IsDBNull(reader["DBTargetTransPerHour"]) ? string.Empty : Convert.ToString(reader["DBTargetTransPerHour"]);
            mimix.DBLastReadTransPerHour = Convert.IsDBNull(reader["DBLastReadTransPerHour"]) ? string.Empty : Convert.ToString(reader["DBLastReadTransPerHour"]);
            mimix.DBEntrieNotRead = Convert.IsDBNull(reader["DBEntrieNotRead"]) ? string.Empty : Convert.ToString(reader["DBEntrieNotRead"]);
            mimix.ObjCurrentReceiver = Convert.IsDBNull(reader["ObjCurrentReceiver"]) ? string.Empty : Convert.ToString(reader["ObjCurrentReceiver"]);
            mimix.ObjLastReadReceiver = Convert.IsDBNull(reader["ObjLastReadReceiver"]) ? string.Empty : Convert.ToString(reader["ObjLastReadReceiver"]);
            mimix.ObjCurrentSequence = Convert.IsDBNull(reader["ObjCurrentSequence"]) ? string.Empty : Convert.ToString(reader["ObjCurrentSequence"]);
            mimix.ObjLastReadSequence = Convert.IsDBNull(reader["ObjLastReadSequence"]) ? string.Empty : Convert.ToString(reader["ObjLastReadSequence"]);
            mimix.ObjCurrentDateTime = Convert.IsDBNull(reader["ObjCurrentDateTime"]) ? string.Empty : Convert.ToString(reader["ObjCurrentDateTime"]);
            mimix.ObjLastReadDateTime = Convert.IsDBNull(reader["ObjLastReadDateTime"]) ? string.Empty : Convert.ToString(reader["ObjLastReadDateTime"]);
            mimix.ObjCurrentTransPerHour = Convert.IsDBNull(reader["ObjCurrentTransPerHour"]) ? string.Empty : Convert.ToString(reader["ObjCurrentTransPerHour"]);
            mimix.ObjLastReadTransPerHour = Convert.IsDBNull(reader["ObjLastReadTransPerHour"]) ? string.Empty : Convert.ToString(reader["ObjLastReadTransPerHour"]);
            mimix.ObjEntrieNotRead = Convert.IsDBNull(reader["ObjEntrieNotRead"]) ? string.Empty : Convert.ToString(reader["ObjEntrieNotRead"]);
            mimix.CurrentDatalag = Convert.IsDBNull(reader["CurrentDatalag"]) ? string.Empty : Convert.ToString(reader["CurrentDatalag"]);
            return mimix;
        }

        #endregion IEntityBuilder<MimixDatalag> Members
    }
}
