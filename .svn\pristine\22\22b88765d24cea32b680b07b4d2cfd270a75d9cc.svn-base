﻿using CP.BusinessFacade;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CP.UI.Code.Replication.DataLag
{
    public class SybaseWithRSHadrDataLag : IDataLag
    {
        private readonly IFacade _facade = new Facade();

        public InfraObjectInfo GetDataLag(int infraObjectId)
        {
            var infraObjectInfo = new InfraObjectInfo();

            var infraObject = _facade.GetInfraObjectById(infraObjectId);

            var dataLag = _facade.GetCurrentRPOInfraInfobyInfraObjectId(infraObject.Id, infraObject.BusinessFunctionId);

            var businessfunction = _facade.GetBusinessFunctionByInfraObjectId(infraObjectId);

            if (dataLag != null && infraObject != null)
            {
                if (string.IsNullOrEmpty(dataLag.CurrentRPO))
                {
                    infraObjectInfo.DataLag = "N/A";

                    infraObjectInfo.Health = 0;

                    infraObjectInfo.DataLagCreateDate = "N/A";
                }
                else
                {
                    infraObjectInfo.DataLag = dataLag.CurrentRPO;

                    infraObjectInfo.Health = Utility.GetDatlagHealth(dataLag.CurrentRPO, Convert.ToInt32(Utility.ConvertRPOValues(businessfunction.ConfiguredRPO))) ? 1 : 0;

                    infraObjectInfo.DataLagCreateDate = dataLag.CreateDate.ToString();
                }
            }
            else
            {
                infraObjectInfo.DataLag = "N/A";

                infraObjectInfo.Health = 0;

                infraObjectInfo.DataLagCreateDate = "N/A";
            }

            return infraObjectInfo;
        }
    }
}