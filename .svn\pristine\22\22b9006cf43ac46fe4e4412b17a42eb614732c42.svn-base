﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class GroupWorkflowBuilder : IEntityBuilder<GroupWorkflow>
    {
        IList<GroupWorkflow> IEntityBuilder<GroupWorkflow>.BuildEntities(IDataReader reader)
        {
            var groupWorkflows = new List<GroupWorkflow>();

            while (reader.Read())
            {
                groupWorkflows.Add(((IEntityBuilder<GroupWorkflow>)this).BuildEntity(reader, new GroupWorkflow()));
            }

            return (groupWorkflows.Count > 0) ? groupWorkflows : null;
        }

        GroupWorkflow IEntityBuilder<GroupWorkflow>.BuildEntity(IDataReader reader, GroupWorkflow groupWorkflow)
        {
            //const int FLD_ID = 0;
            //const int FLD_GROUPID = 1;
            //const int FLD_WORKFLOWID = 2;
            //const int FLD_ACTIONTYPE = 3;
            //const int FLD_TOTALRTO = 4;
            //const int FLD_CREATORID = 5;
            //const int FLD_CREATEDATE = 6;

            //groupWorkflow.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //groupWorkflow.GroupId = reader.IsDBNull(FLD_GROUPID) ? 0 : reader.GetInt32(FLD_GROUPID);
            //groupWorkflow.WorkflowId = reader.IsDBNull(FLD_WORKFLOWID) ? 0 : reader.GetInt32(FLD_WORKFLOWID);
            //groupWorkflow.ActionType = reader.IsDBNull(FLD_ACTIONTYPE) ? 0 : reader.GetInt32(FLD_ACTIONTYPE);
            //groupWorkflow.TotalRTO = reader.IsDBNull(FLD_TOTALRTO) ? string.Empty : reader.GetString(FLD_TOTALRTO);
            //groupWorkflow.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //groupWorkflow.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);

            //Fields in bcms_group_workflow table on 22/07/2013 : Id, GroupId, WorkflowId, ActionType, TotalRTO, CreatorId, CreateDate

            groupWorkflow.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            //groupWorkflow.GroupId = Convert.IsDBNull(reader["GroupId"]) ? 0 : Convert.ToInt32(reader["GroupId"]);
            groupWorkflow.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            //groupWorkflow.ApplicationId = Convert.IsDBNull(reader["ApplicationId"])
            //    ? 0
            //    : Convert.ToInt32(reader["ApplicationId"]);
            groupWorkflow.WorkflowId = Convert.IsDBNull(reader["WorkflowId"])
                ? 0
                : Convert.ToInt32(reader["WorkflowId"]);
            groupWorkflow.ActionType = Convert.IsDBNull(reader["ActionType"])
                ? 0
                : Convert.ToInt32(reader["ActionType"]);
            groupWorkflow.TotalRTO = Convert.IsDBNull(reader["TotalRTO"])
                ? string.Empty
                : Convert.ToString(reader["TotalRTO"]);
            groupWorkflow.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            groupWorkflow.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            return groupWorkflow;
        }
    }
}