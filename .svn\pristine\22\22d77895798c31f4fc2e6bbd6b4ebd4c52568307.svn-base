﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class MSSqlDBMirrorConfiguration : ReplicationControl
    {
        #region Variable

        private TextBox _txtPRServerNKAddress = new TextBox();
        private TextBox _txtDRServerNKAddress = new TextBox();
        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();

        private MSSQLDBMirrorReplication _mssqldbreplication = null;

        public MSSQLDBMirrorReplication CurrentEntity
        {
            get { return _mssqldbreplication ?? (_mssqldbreplication = new MSSQLDBMirrorReplication()); }
            set
            {
                _mssqldbreplication = value;
            }
        }

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public TextBox PRServerNKAddress
        {
            get
            {
                _txtPRServerNKAddress = Parent.FindControl("txtPRServerNKAddress") as TextBox;
                return _txtPRServerNKAddress;
            }
            set { _txtPRServerNKAddress = value; }
        }

        public TextBox DRServerNKAddress
        {
            get
            {
                _txtDRServerNKAddress = Parent.FindControl("txtDRServerNKAddress") as TextBox;
                return _txtDRServerNKAddress;
            }
            set { _txtDRServerNKAddress = value; }
        }

        public string MessageInitials
        {
            get { return "MSSQLDBMirrorReplication"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion Variable

        public override void PrepareView()
        {
            txtPRServerNKAddress.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtPRServerNKAddress.ClientID + ")");
            txtDRServerNKAddress.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtDRServerNKAddress.ClientID + ")");
            Session.Remove("CurrentMSSQLDBMirror");
            LoadData();
        }

        private void LoadData()
        {
            if (CurrentMSSQLDBMirrorReplication != null)
            {
                CurrentEntity = CurrentMSSQLDBMirrorReplication;

                Session["CurrentMSSQLDBMirror"] = CurrentEntity;
                txtPRServerNKAddress.Text = CurrentMSSQLDBMirrorReplication.PRServerNwkAddress;
                txtDRServerNKAddress.Text = CurrentMSSQLDBMirrorReplication.DRServerNwkAddress;
                btnSave.Text = "Update";
            }
        }



        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            //if ((Session["_token"] != null))
            //{
            if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
            {

                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                //if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                //{

                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                currentTransactionType = TransactionType.Save;

            else if (buttionText.Contains(" update "))
                currentTransactionType = TransactionType.Update;

            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;

            if (Page.IsValid)
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                    returnUrl = ReturnUrl;


                try
                {
                    if (ValidateRequest("MSSqlDBMirrorConfiguration", UserActionType.CreateReplicationComponent))
                    {
                        BuildEntities();
                        StartTransaction();
                        SaveEditor();
                        EndTransaction();

                        string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));

                        btnSave.Enabled = false;
                    }
                }
                catch (CpException ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);

                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, Page);
                    }
                }
                if (returnUrl.IsNotNullOrEmpty())
                {
                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "52");

                    //Helper.Url.Redirect(secureUrl);

                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "52");
                    Helper.Url.Redirect(secureUrl);

                }
            }
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        private void BuildEntities()
        {
            if (Session["CurrentMSSQLDBMirror"] != null)
            {
                CurrentEntity = (MSSQLDBMirrorReplication)Session["CurrentMSSQLDBMirror"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.PRServerNwkAddress = (txtPRServerNKAddress.Text).Trim();
            CurrentEntity.DRServerNwkAddress = (txtDRServerNKAddress.Text).Trim();
            CurrentEntity.CreatorId = LoggedInUserId;
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
                return false;


            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        private void SaveEditor()
        {
            if (Session["CurrentMSSQLDBMirror"] == null)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddMSSqlDBMirrorReplication(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "MSSQLDBMirrorReplication", UserActionType.CreateReplicationComponent, "The MSSQLDBMirrorReplication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
            }
            else if (Session["CurrentMSSQLDBMirror"] != null)
            {
                CurrentEntity.Id = CurrentMSSQLDBMirrorReplication.Id;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateMSSqlDBMirroreplication(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "MSSQLDBMirrorReplication", UserActionType.UpdateReplicationComponent, "The MSSQLDBMirrorReplication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }
    }
}