﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using System.Data.Common;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class PostgreReplicationDataAccess : BaseDataAccess, IPostgreReplicationDataAccess
    {
        #region Constructors

        public PostgreReplicationDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<PostgreReplication> CreateEntityBuilder<PostgreReplication>()
        {
            return (new PostgreReplicationBuilder()) as IEntityBuilder<PostgreReplication>;
        }

        #endregion

        #region  Methods

        PostgreReplication IPostgreReplicationDataAccess.GetByInfraId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "PostgreReplicationGetByInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_post"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<PostgreReplication>()).BuildEntity(reader, new PostgreReplication());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                     ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                     "Error In DAL While Fetching PostgreReplication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                     ex.Message, ex);
            }
        }

        /// <summary>
        /// Below Method is Added By Mahesh Singh Chauhan On Date : 28.07.2014 
        /// </summary>
        /// <param name="id"></param>
        /// <param name="type"></param>
        /// <returns></returns>


        IList<PostgreReplication> IPostgreReplicationDataAccess.GetHourlyByInfraId(int id, string type)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "PostgreHOURLY_GetByInfraId";//PostgreSql_GetByHours_GroupId

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, id);
                    //Database.AddInParameter(cmd, Dbstring+"iType", DbType.AnsiString, type);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_post"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<PostgreReplication>().BuildEntities(reader);
                    }
                }
            }

            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                      ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                      "Error In DAL While Fetching PostgreReplication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                      ex.Message, ex);
            }

        }

        /// <summary>
        /// Below Method is Added By Mahesh Singh Chauhan On Date : 31.07.2014 
        /// </summary>
        /// <param name="groupId"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>

        IList<PostgreReplication> IPostgreReplicationDataAccess.GetByDate(int groupId, string startDate, string endDate)
        {
            try
            {
                const string sp = "POSTGRE_GETBYDATE";//PostgreSqlLogs_GetByDate

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    startDate = Convert.ToDateTime(startDate).ToString("dd-MM-yy");
                    endDate = Convert.ToDateTime(endDate).ToString("dd-MM-yy");
#endif

                    Database.AddInParameter(cmd, Dbstring+"iGroupId", DbType.Int32, groupId);
                    Database.AddInParameter(cmd, Dbstring+"iStartDate", DbType.AnsiString, startDate);
                    Database.AddInParameter(cmd, Dbstring+"iEndDate", DbType.AnsiString, endDate);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_post"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<PostgreReplication>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                      ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                      "Error In DAL While Fetching PostgreReplication Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                      ex.Message, ex);
            }
        }

        #endregion
    }
}
