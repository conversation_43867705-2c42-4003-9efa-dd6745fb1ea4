﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using CP.Helper;

namespace CP.UI.Code.Replication.Component
{
    public class Ec2S3DataSyncComponent : IComponentInfo
    {
        private readonly IFacade _facade = new Facade();

        private ComponentInfo _componentInfo;

        public ComponentInfo CurrentComponent
        {
            get { return _componentInfo ?? (_componentInfo = new ComponentInfo()); }
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId)
        {
            _componentInfo = null;

            GetServerInformation(prServerId, true);

            GetDataLagInformation(infraObjectId);

            GetEc2S3DataSyncInformation(infraObjectId);

            return CurrentComponent;
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId, int mailBoxId, string mailboxname)
        {
            _componentInfo = null;

            //GetServerInformation(prServerId, true);

            //GetServerInformation(drServerId, false);

            //GetDatabaseInformation(prDatabaseId, true);

            //GetDatabaseInformation(drDatabaseId, false);

            //GetDataLagInformation(groupId);

            return CurrentComponent;
        }

        private void GetServerInformation(int serverId, bool isPrimary)
        {
            try
            {
                var server = _facade.GetServerById(serverId);

                if (server != null)
                {
                    BindServerComponents(server, isPrimary);
                }
                else
                {
                    BindNullServerComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullServerComponents(isPrimary);
            }
        }

        private void BindServerComponents(Server server, bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = server.Name;
                CurrentComponent.PRServerIP = server.IPAddress;
                CurrentComponent.PRServerOSType = server.OSType;
                //CurrentComponent.PRServerStatus = server.Status.ToString();
            }
        }

        private void BindNullServerComponents(bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = "N/A";
                CurrentComponent.PRServerIP = "N/A";
                CurrentComponent.PRServerOSType = "N/A";
                //CurrentComponent.PRServerStatus = "Down";
            }
        }

        private void GetDataLagInformation(int infraObjectId)
        {
            try
            {
                var datalag = _facade.GetSQLNative2008MonitorByInfraObjectId(infraObjectId);

                if (datalag != null)
                {
                    CurrentComponent.PRLogSequence = datalag.LastGenLog;
                    CurrentComponent.DRLogSequence = datalag.LastApplyLog;
                }
                else
                {
                    CurrentComponent.PRLogSequence = "Not Available";
                    CurrentComponent.DRLogSequence = "Not Available";
                }
            }
            catch (Exception)
            {
                CurrentComponent.PRLogSequence = "Not Available";
                CurrentComponent.DRLogSequence = "Not Available";
            }
        }

        private void GetEc2S3DataSyncInformation(int infraObjectId)
        {
            try
            {
                var Ec2S3 = _facade.GetEc2S3DataSyncMonitorByInfraId(infraObjectId);

                if (Ec2S3 != null)
                {
                    CurrentComponent.SourceDataPath = Ec2S3.SourceDataPath;
                    //CurrentComponent.S3Bucket = Ec2S3.S3Bucket;
                    CurrentComponent.S3BucketTimeStamp = Ec2S3.S3BucketTimeStamp;
                    CurrentComponent.EC2InstanceId = Ec2S3.EC2InstanceId;
                    CurrentComponent.EC2InstanceStatus = Ec2S3.EC2InstanceStatus;
                    CurrentComponent.Ec2InstanceType = Ec2S3.Ec2InstanceType;
                   CurrentComponent.S3BucketLocation = Ec2S3.S3BucketLocation;
                   CurrentComponent.IPAddress = CryptographyHelper.Md5Decrypt(_componentInfo.PRServerIP);
                   CurrentComponent.OSName = _componentInfo.PRServerOSType;

                }
                else
                {
                    CurrentComponent.SourceDataPath = "Not Available";
                    //CurrentComponent.S3Bucket = "Not Available";
                    CurrentComponent.S3BucketTimeStamp = "Not Available";
                    CurrentComponent.EC2InstanceId = "Not Available";
                    CurrentComponent.EC2InstanceStatus =  "Not Available";
                    CurrentComponent.Ec2InstanceType = "Not Available";
                    CurrentComponent.S3BucketLocation = "Not Available";
                    CurrentComponent.IPAddress = "Not Available";
                    CurrentComponent.OSName = "Not Available";
                }
            }
            catch (Exception)
            {
                CurrentComponent.SourceDataPath = "Not Available";
                CurrentComponent.S3Bucket = "Not Available";
                CurrentComponent.S3BucketTimeStamp = "Not Available";
                CurrentComponent.EC2InstanceId = "Not Available";
                CurrentComponent.EC2InstanceStatus = "Not Available";
                CurrentComponent.Ec2InstanceType = "Not Available";
                CurrentComponent.S3BucketLocation = "Not Available";
                CurrentComponent.IPAddress = "Not Available";
                CurrentComponent.OSName = "Not Available";
            }
        }
    }
}