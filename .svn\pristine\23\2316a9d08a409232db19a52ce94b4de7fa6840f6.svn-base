﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DRReadyRPT.ascx.cs" Inherits="CP.UI.Controls.DRReadyRPT" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<link href="../App_Themes/ReportTheme/Report.css" rel="stylesheet" type="text/css" />
<link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
<script src="../Script/chosen.jquery.js"></script>
<style type="text/css">
    .rowStyleHeader.bold, .rowStyleHeader.bold {
        font-size: 9pt !important;
    }

    .chosen-select + .chosen-container {
        width: 48.5% !important;
        opacity: 1 !important;
    }
</style>
<script>
    $(document).ready(function () {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    });
    function pageLoad() {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    }
</script>

<div class="form-group">
    <label class="col-md-3 control-label">
        Application Name <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlBussService" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlBussService_SelectedIndexChanged"></asp:DropDownList>
    </div>
</div>


<hr />
<div class="form-group">
    <div id="divlable" class="col-xs-6" visible="false">
        <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false"></asp:Label>
        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
    </div>
    <div class="col-xs-6">
        <asp:Button ID="btnview" runat="server" CssClass="btn btn-primary" Style="margin-left: 5px" ValidationGroup="vlGroupSite" Width="20%" OnClick="btnview_Click"
            Text="Export To Excel" />
    </div>
</div>

<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div id="imgLoading" class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>
