﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="POTPValidation.aspx.cs" Inherits="CP.UI.Admin.POTPValidation" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    
                                <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
                                </asp:Panel>
                                <asp:Panel ID="pnlOTP" runat="server" Width="100%" Visible="true">
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 600px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <P class="modal-title">Please Enter the OTP(One Time Password) sent to your Registered Mobile Number</P>
                                                    <asp:LinkButton ID="Lkbtnclosesmtphost" runat="server" ToolTip="Close window" CausesValidation="False" class="close" CommandName="Close"
                                                        OnClick="Lkbtnclosesmtphost_Click" Style="position: absolute; right: 10px;">x</asp:LinkButton>
                                                </div>
                                                <div class="modal-body">
                                                    <asp:Label ID="lblsmtpsavemessage" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>
                                                    <asp:UpdatePanel ID="UpdatePanel_smtphost" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                            <div class="">
                                                                <div class="col-md-12 form-horizontal uniformjs">
                                                                    <div class="form-group">
                                                                     <%--   <label class="col-md-4 control-label ">
                                                                            <asp:Label ID="lblsmtp" runat="server" Text="Please Enter the OTP(One Time Password) sent to your Registered Mobile Number"></asp:Label>
                                                                            <span class="inactive">*</span>
                                                                        </label>--%>
                                                                        <div class="col-md-4">
                                                                            <%--<asp:HiddenField ID="smtpId" runat="server" />--%>
                                                                            <%--<asp:TextBox ID="smtpHost" runat="server" autocomplete="off" CssClass="form-control" placeholder="Enter OTP" Width="72%"></asp:TextBox>--%>
                                                                            <asp:TextBox ID="txtotp" runat="server" CssClass="form-control" placeholder="Enter OTP" Width="100%"></asp:TextBox>
                                                                            <asp:RequiredFieldValidator ID="rfvsmtphost" runat="server" ErrorMessage="Please Enter Correct OTP" ControlToValidate="txtotp"
                                                                                Display="Dynamic" CssClass="error"></asp:RequiredFieldValidator>
                                                                        </div>
                                                                        <div class="col-md-4">
                                                                            <asp:Button ID="btnSave" runat="server" Text="Validate OTP" CssClass="btn btn-primary" OnClick="btnsave_Click" Visible="True" />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                </asp:Panel>

</asp:Content>
