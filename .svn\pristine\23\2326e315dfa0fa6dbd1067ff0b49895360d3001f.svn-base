﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;


namespace CP.DataAccess
{
    internal sealed class DataBaseMSSqlDataAccess : BaseDataAccess, IDataBaseMSSqlDataAccess

    {
        #region Constructors

        public DataBaseMSSqlDataAccess(Context context)
            : base(context)
        {

        }

        protected override IEntityBuilder<SqlNative2008> CreateEntityBuilder<SqlNative2008>()
        {
            return (new DataBaseMSSqlBuilder()) as IEntityBuilder<SqlNative2008>;            
        }

        #endregion Constructors

        #region Methods

        DatabaseSqlNative2008 IDataBaseMSSqlDataAccess.Add(DatabaseSqlNative2008 databasemssql)
        {
            try
            {
                const string sp = "DatabaseMSSql_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iBaseDatabaseId", DbType.Int32, databasemssql.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseName", DbType.String, databasemssql.DatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, databasemssql.InstanceName);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, databasemssql.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, databasemssql.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, databasemssql.Port);
                    Database.AddInParameter(cmd, Dbstring + "iAuthenticationMode", DbType.String, databasemssql.AuthenticationMode);
                    Database.AddInParameter(cmd, Dbstring + "isENABLED", DbType.Int32, databasemssql.SSOEnabled);
                    Database.AddInParameter(cmd, Dbstring + "iSSOProfileId", DbType.Int32, databasemssql.SSOProfileId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databasemssql = reader.Read()
                            ? CreateEntityBuilder<DatabaseSqlNative2008>().BuildEntity(reader, databasemssql)
                            : null;
                    }

                    if (databasemssql == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "DatabaseSql already exists. Please specify another bcms_database_sql.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this bcms_database_sql.");
                                }
                        }
                    }

                    return databasemssql;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting DatabaseSql Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }


        DatabaseSqlNative2008 IDataBaseMSSqlDataAccess.GetByDatabaseBaseId(int databaseBaseId)
        {
            try
            {
                const string sp = "DBMSSql_GetByDBID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, "iDatabaseBaseId", DbType.Int32, databaseBaseId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<DatabaseSqlNative2008>()).BuildEntity(reader, new DatabaseSqlNative2008())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDatabaseSqlDataAccess.GetByDatabaseBaseId(" +
                    databaseBaseId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        DatabaseSqlNative2008 IDataBaseMSSqlDataAccess.UpdateByDatabaseBaseId(DatabaseSqlNative2008 databaseMSSql)
        {
            try
            {
                string sp = DbRoleName + "DatabaseMSSql_UpdateByBaseId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseId", DbType.Int32, databaseMSSql.BaseDatabaseId);
                    Database.AddInParameter(cmd, Dbstring + "iDatabaseName", DbType.String, databaseMSSql.DatabaseName);
                    Database.AddInParameter(cmd, Dbstring + "iInstanceName", DbType.String, databaseMSSql.InstanceName);
                    Database.AddInParameter(cmd, Dbstring + "iUserName", DbType.String, databaseMSSql.UserName);
                    Database.AddInParameter(cmd, Dbstring + "iPassword", DbType.String, databaseMSSql.Password);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, databaseMSSql.Port);
                    Database.AddInParameter(cmd, Dbstring + "iAuthenticationMode", DbType.String, databaseMSSql.AuthenticationMode);
                    Database.AddInParameter(cmd, Dbstring + "isENABLED", DbType.Int32, databaseMSSql.SSOEnabled);
                    Database.AddInParameter(cmd, Dbstring + "iSSOProfileId", DbType.Int32, databaseMSSql.SSOProfileId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    //cmd.Parameters.Add(BuildRefCursorParameter("cur_databasemysql"));
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        databaseMSSql = reader.Read() ? CreateEntityBuilder<DatabaseSqlNative2008>().BuildEntity(reader, databaseMSSql) : null;
                    }

                    if (databaseMSSql == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {                              
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("DatabaseMSSql already exists. Please specify another bcms_database_oracle.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this bcms_database_oracle.");
                                }
                        }
                    }

                    return databaseMSSql;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, ExceptionManager.CommonMessage.UserAlertMessageUpdatedata, "Error In DAL While Updating By DatabaseBaseId DatabaseOracle Entry " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        #endregion Methods
    }
}
