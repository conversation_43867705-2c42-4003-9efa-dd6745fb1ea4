﻿using System;
using System.Data;
using System.Configuration;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;
using BCMS.Common.DatabaseEntity;

namespace BCMS.UI 
{
    public abstract class ExchangeGroupBasePageEditor : ExchangeGroupBasePage, IEditor<ExchangeGroup>
    {
        private string _message = string.Empty;

        #region Properties

        public ExchangeGroup CurrentEntity
        {
            get
            {
                return base.CurrentExchangeGroup;
            }
            set
            {
                base.CurrentExchangeGroup = value;
            }
        }

        public int CurrentEntityId
        {
            get { return base.CurrentExchangeGroupId; }
            set { base.CurrentExchangeGroupId = 0; }
        }


        public abstract Label MessageViewer
        {
            get;
        }

        public abstract string MessageInitials
        {
            get;
        }
        protected new string Message
        {
            get
            {
                return _message;
            }
            set
            {
                _message = value;
            }
        }

        public abstract string ReturnUrl
        {
            get;
        }
        public virtual Label TotalResult
        {
            get
            {
                return null;
            }
        }

           #endregion

        #region Method

        public ExchangeGroup BuildEntity(ExchangeGroup currentEntity)
        {
            return currentEntity;
        }

        public abstract void PrepareEditView();

        public abstract void SaveEditor();

        public virtual void PrepareValidator()
        {

        }

        public abstract void BuildEntities();

        public virtual void BindList() { }

        public virtual void Delete(int entityId)
        {

        }

        public virtual void ClearForm()
        {
            CurrentEntityId = 0;
        }

        public virtual void FinalizeCommand()
        {

        }

        #endregion
    }
}
