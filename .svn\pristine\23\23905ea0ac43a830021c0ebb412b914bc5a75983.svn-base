﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class AlertNotificationBuilder : IEntityBuilder<AlertNotification>
    {
        IList<AlertNotification> IEntityBuilder<AlertNotification>.BuildEntities(IDataReader reader)
        {
            var alertNotifications = new List<AlertNotification>();

            while (reader.Read())
            {
                alertNotifications.Add(((IEntityBuilder<AlertNotification>)this).BuildEntity(reader, new AlertNotification()));
            }

            return (alertNotifications.Count > 0) ? alertNotifications : null;
        }

        AlertNotification IEntityBuilder<AlertNotification>.BuildEntity(IDataReader reader, AlertNotification alertNotification)
        {
            alertNotification.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            alertNotification.AlertCategoryId = Convert.IsDBNull(reader["AlertCategoryId"]) ? 0 : Convert.ToInt32(reader["AlertCategoryId"]);
            alertNotification.AlertType = Convert.IsDBNull(reader["AlertType"]) ? 0 : Convert.ToInt32(reader["AlertType"]);
            alertNotification.AlertSentCount = Convert.IsDBNull(reader["AlertSentCount"]) ? 0 : Convert.ToInt32(reader["AlertSentCount"]);
            alertNotification.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);

            return alertNotification;
        }
    }
}