﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    # region IBusinessFunctionDataAccess

    public interface IBusinessFunctionDataAccess
    {
        BusinessFunction Add(BusinessFunction businessService);

        BusinessFunction Update(BusinessFunction businessService);

        BusinessFunction UpdateBFBIAPRofileByID(BusinessFunction businessService);

        BusinessFunction GetById(int id);

        BusinessFunction GetByInfraObjectId(int infraObjectId);

        BusinessFunction GetByName(string name);

        IList<BusinessFunction> GetByBusinessServiceId(int serviceid);

        IList<BusinessFunction> GetByBusinessServiceIdAndUserId(int businessserviceid, int loggedInUserId);

        IList<BusinessFunction> GetAllBusinessFunctionsByCompanyIdAndRole(int companyId, bool isParent);

        IList<BusinessFunction> GetAll();

        IList<BusinessFunction> GetBusinessFunctionsByCompanyId(int companyId, bool isParent);

        IList<BusinessFunction> GetByLoginId(int loginId);

        BusinessFunction GetByDROperationId(int droperationid);

        IList<BusinessFunction> GetBusinessFunctionUserIdCompId(int userid, int companyid);

        bool DeleteById(int id);

        bool IsExistByName(string name);
        int GetFunctionCount_Recovered();
        int GetFunctionCount_NotRecovered();      
    }

    #endregion
}