﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "Alert", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class BIAFailureActinBySolType : BaseEntity
    {
        [DataMember]
        public SoultionType SoultionType { get; set; }

        [DataMember]
        public int FalureActionCount { get; set; }

        [DataMember]
        public int Total { get; set; }

        [DataMember]
        public double Percentage { get; set; }

        [DataMember]
        public string Averagerto { get; set; }

        [DataMember]
        public string Maxrto { get; set; }

        [DataMember]
        public string MinRTO { get; set; }
    }
}
