﻿<%@ Page Language="C#" AutoEventWireup="true" MasterPageFile="~/Master/BcmsDefault.Master" CodeBehind="WhatIfAnalysis.aspx.cs" Inherits="CP.UI.ImpactAnalysis.WhatIfAnalysis" %>


<%--<!DOCTYPE html>--%>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <style type="text/css">
        .list {
            border-bottom: 0px;
        }

        .padding-noneLRnew {
            padding-left: 5px;
            padding-right: 5px;
        }

        #impacttable.table thead > tr > th, #impacttable.table tbody > tr > th, #impacttable.table tfoot > tr > th, #impacttable.table thead > tr > td, #impacttable.table tbody > tr > td, .table tfoot > tr > td, #impactincidentdetailstable.table thead > tr > th, #impactincidentdetailstable.table tbody > tr > th, #impactincidentdetailstable.table tfoot > tr > th, #impactincidentdetailstable.table thead > tr > td, #impactincidentdetailstable.table tbody > tr > td, .table tfoot > tr > td {
            padding: 5px;
        }

        #impactincidentdetailstable.table thead > tr > th, #impactincidentdetailstable.table tbody > tr > th, #impactincidentdetailstable.table tfoot > tr > th, #impactincidentdetailstable.table thead > tr > td, #impactincidentdetailstable.table tbody > tr > td, .table tfoot > tr > td {
            border-top: 0px;
            border-bottom: 0px;
        }

        #impacttable.text-small, #impactincidentdetailstable.text-small {
            font-size: 11px !important;
        }

        #impactincidentdetailstable .spnBreadCrum {
            text-overflow: ellipsis;
            overflow: hidden;
            display: block;
            width: 90%;
            white-space: nowrap;
        }

        .FinancialImapactTotalCost {
            display: block;
            color: #346d9d;
            width: 100%;
            font-size: 36px !important;
            text-align: center;
            font-weight: bold;
            font-family: segoe_uiregular;
        }

        .impactcostdetails {
            width: 47%;
            display: inline-block;
        }

            .impactcostdetails label {
                font-size: 12px !important;
                display: block;
                margin-bottom: 1px !important;
            }

            .impactcostdetails hr {
                border-top-color: #346d9d;
                margin: 0px 0 0px 0px;
                border-top-width: 1px;
            }

        #ctl00_cphBody_lblIncidentTime2, #ctl00_cphBody_lblIncdowntime2 {
            display: block;
            color: #346d9d;
            font-weight: bold;
        }

        [id$="divLegends"] .d3-legend {
            width: 275px;
        }

        #ctl00_cphBody_RadSplitter2 {
            height: 410px !important;
        }

        .RadSplitter_Default, .RadSplitter_Default .rspPaneTabContainer, .RadSplitter_Default .rspPane, .RadSplitter_Default .rspResizeBar, .RadSplitter_Default .rspSlideContainerResize, .RadSplitter_Default .rspPaneHorizontal, .RadSplitter_Default .rspResizeBarHorizontal, .RadSplitter_Default .rspSlideContainerResizeHorizontal {
            border-color: #ccc !important;
        }
        /*#ctl00_cphBody_pnlBSCostAppend .border-margin-bottom,#ctl00_cphBody_pnlappend .border-margin-bottom {
            margin-bottom: 10px;
        }

        #impacttable.table td {
            padding:5px 5px 5px 0px;
        }*/
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <%--<html>
<head id="Head1" runat="server">--%>
    <%-- <title>What-If Analysis</title>--%>


    <%--</head>
<body>
    <form id="form1" runat="server">--%>
    <%--<asp:ScriptManager ID="sp" runat="server"></asp:ScriptManager>--%>
    <div class="widget widget-heading-simple widget-body-white margin-bottom-none">
        <div class="widget-body">
            <telerik:RadTabStrip ID="RadTabStrip1" runat="server" Skin="Glow" MultiPageID="RadMultiPage1" SelectedIndex="1">
                <Tabs>
                    <telerik:RadTab Text="Impact Summary" runat="server">
                    </telerik:RadTab>
                    <telerik:RadTab Text="Incident What-If Analysis" runat="server" Selected="True">
                    </telerik:RadTab>
                    <telerik:RadTab Text="Analytics" NavigateUrl="../ImpactAnalysis/Analytics.aspx" runat="server" Visible="false">
                    </telerik:RadTab>
                    <telerik:RadTab Text="Rules" NavigateUrl="../ImpactAnalysis/EntityImpactRelationshipForm.aspx" runat="server" Visible="false">
                    </telerik:RadTab>
                </Tabs>
            </telerik:RadTabStrip>
            <telerik:RadMultiPage runat="server" ID="RadMultiPage2" SelectedIndex="1">
                <telerik:RadPageView runat="server" ID="RadPageView5">
                </telerik:RadPageView>
                <telerik:RadPageView ID="RadPageView2" runat="server">

                    <div class="widget margin-top ">
                        <div class="widget-body">
                            <div class="row">
                                <div class="col-md-12 form-horizontal">

                                    <div class="form-group">

                                        <div class="col-md-4">
                                            <label class="col-md-6 npl npr control-label">Incident Id</label>
                                            <div class="col-md-6 npl npr">
                                                <asp:UpdatePanel ID="udpIncID" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="ddlIncId" runat="server" CssClass="col-md-12" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlIncId_SelectedIndexChanged">
                                                        </asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                            <%--  <label class="col-md-5 npl npr control-label">Impacted Entity</label>
                                                <div class="col-md-7 npl npr">
                                                    <asp:Label ID="lblCompnentName" CssClass="text-bold" runat="server" Visible="false" />
                                                    <asp:UpdatePanel ID="udpIPAddress" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                            <asp:DropDownList ID="ddlIpAddress" runat="server" CssClass="col-md-12" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlIpAddress_SelectedIndexChanged">
                                                            </asp:DropDownList>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>--%>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-md-6 npl npr control-label">Impacted InfraObject</label>
                                            <div class="col-md-6 npl npr">
                                                <asp:UpdatePanel ID="updInfraobject" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="dllInfraobject" runat="server" CssClass="col-md-12" data-style="btn-default" OnSelectedIndexChanged="dllInfraobject_SelectedIndexChanged" AutoPostBack="true">
                                                        </asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-md-6 npl npr control-label">Impacted InfraComponent</label>
                                            <div class="col-md-6 npl npr">
                                                <asp:UpdatePanel ID="udpInfraCompo" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="ddlinfraObjectComponent" runat="server" CssClass="col-md-12" data-style="btn-default">
                                                        </asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-md-4">
                                            <label class="col-md-6 npl npr control-label">Impact Time</label>
                                            <div class="col-md-6 npl npr">
                                                <asp:TextBox ID="txtRto" runat="server" Width="46%" CssClass="form-control impactsummary pull-left" Text="1" Style="margin-right: 6px; height: 32px;"></asp:TextBox>
                                                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true" style="display: inline;">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="ddlTimeUnit" runat="server" Width="50%" data-style="btn-default">
                                                            <asp:ListItem Text="Hours" Value="1" Selected="True"></asp:ListItem>
                                                            <asp:ListItem Text="Days" Value="2"></asp:ListItem>
                                                        </asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>

                                        </div>

                                        <div class="col-md-4">
                                            <label class="col-md-6 npl npr control-label">Impact Costing Method</label>
                                            <div class="col-md-6 npl npr">
                                                <asp:DropDownList ID="ddlMethods" runat="server"
                                                    AutoPostBack="false" CssClass="col-md-12" data-style="btn-default">
                                                    <asp:ListItem Text="BIA Data" Value="1"></asp:ListItem>
                                                    <asp:ListItem Text="Exponential" Value="2"></asp:ListItem>
                                                    <asp:ListItem Text="Linear" Value="3"></asp:ListItem>
                                                    <asp:ListItem Text="Common Log" Value="4"></asp:ListItem>
                                                    <asp:ListItem Text="Natural Log" Value="5"></asp:ListItem>
                                                </asp:DropDownList>

                                            </div>
                                        </div>



                                        <div class="col-md-4">
                                            <div class="pull-right margin-right18" style="margin-right: 15px;">
                                                <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="true" style="display: inline;">
                                                    <ContentTemplate>
                                                        <asp:Button ID="btnShowImpact" runat="server" Text="Show Impact" OnClick="btnShowImpact_Click"
                                                            CssClass=" btn btn-primary" />
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>


                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="widget margin-bottom-none">
                        <div class="widget-body">
                            <%--  <telerik:RadMultiPage runat="server" ID="RadMultiPage1" SelectedIndex="0">--%>
                            <%--  <telerik:RadPageView runat="server" ID="RadPageView1">--%>
                            <telerik:RadSplitter ID="RadSplitter2" runat="server" Width="1308" Height="400" Orientation="Horizontal">
                                <telerik:RadPane ID="RadPane3" runat="server" Height="410">

                                    <telerik:RadSplitter ID="RadSplitter1" runat="server" Height="300">
                                        <telerik:RadPane ID="RadPane1" runat="server" MinWidth="80" MaxWidth="300" Width="200" Visible="true">
                                            <div class="widget box-shadow-none border-none margin-bottom-none">
                                                <div class="widget-head">
                                                    <img src="../Images/icons/busfunc_icon.png" style="float: left; margin: 10px 0 0 5px;" />
                                                    <h4 class=" heading text-primary " style="padding: 0 5px;">Incident Tree</h4>

                                                </div>


                                                <div class="widget-body scroll-pane">
                                                    <asp:UpdatePanel ID="UpCurrentIncident" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                            <asp:TreeView ID="tvCurrentIncident" runat="server" ShowLines="true" OnSelectedNodeChanged="tvCurrentIncident_SelectedNodeChanged">
                                                            </asp:TreeView>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>
                                                <asp:UpdatePanel ID="updIcidentDetailsTabButton" runat="server" UpdateMode="Conditional" Visible="false">
                                                    <ContentTemplate>
                                                        <div class="widget widget-tabs widget-tabs-icons-only-1 widget-activity margin-none" style="display: none">
                                                            <div class="widget-head">

                                                                <ul>
                                                                    <li class="active">

                                                                        <asp:LinkButton ID="lnkCurrent" runat="server" OnClick="lnkCurrent_Click">Current</asp:LinkButton>

                                                                    </li>


                                                                    <li class="">

                                                                        <asp:LinkButton ID="lnk2Days" runat="server" OnClick="lnk2Days_Click">2 Days</asp:LinkButton>
                                                                    </li>

                                                                    <li>


                                                                        <asp:LinkButton ID="lnk7Days" runat="server" OnClick="lnk7Days_Click">7 Days</asp:LinkButton>
                                                                    </li>


                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                                <div class="widget-body list" style="display: none">

                                                    <div class="tab-content">
                                                        <div id="tab-CurrentIncident" class="tab-pane active">
                                                        </div>
                                                        <div id="tab-24HrIncident" class="tab-pane">
                                                            <div class="widget-body scroll-pane">
                                                                <asp:UpdatePanel ID="Up24HrIncident" runat="server" UpdateMode="Conditional">
                                                                    <ContentTemplate>
                                                                        <asp:TreeView ID="tv24HrIncident" runat="server" ShowLines="true" OnSelectedNodeChanged="tv24HrIncident_SelectedNodeChanged">
                                                                        </asp:TreeView>
                                                                    </ContentTemplate>
                                                                </asp:UpdatePanel>
                                                            </div>
                                                        </div>
                                                        <div id="tab-AllIncident" class="tab-pane">
                                                            <div class="widget-body scroll-pane">
                                                                <asp:UpdatePanel ID="udpIncidentView" runat="server" UpdateMode="Conditional">
                                                                    <ContentTemplate>
                                                                        <asp:TreeView ID="tvIndicentHierarchy" OnSelectedNodeChanged="tvIndicentHierarchy_SelectedNodeChanged" runat="server" ShowLines="true">
                                                                        </asp:TreeView>
                                                                    </ContentTemplate>
                                                                </asp:UpdatePanel>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                        </telerik:RadPane>
                                        <telerik:RadSplitBar ID="RadSplitbar1" runat="server">
                                        </telerik:RadSplitBar>
                                        <telerik:RadPane ID="RadPane2" runat="server" MinWidth="400" MaxWidth="1100">
                                            <div class="widget box-shadow-none border-none margin-bottom-none">
                                                <div class="widget-head">
                                                    <img src="../Images/icons/icon-impact-diagram-2.png" style="float: left; margin: 10px 0 0 5px;" />
                                                    <h4 class="heading" style="padding: 0 0px;">
                                                        <asp:Label runat="server" ID="Label2" Text="" CssClass="spnBreadCrum"></asp:Label>
                                                        Impacted-Business Services and Applications</h4>
                                                    <div class="pull-right">
                                                        <a id="btnReload" class="back-arrow-icon margin-top" title="Back to full view" style="display: none"></a>
                                                        <a id="btnReset" class="reset-icon" title="Back to initial zoom position">&nbsp;</a>
                                                    </div>
                                                </div>
                                                <div class="widget-body" style="padding: 5px;">
                                                    <asp:UpdatePanel ID="udpgraph" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                            <div class="message warning align-center bold no-bottom-margin">
                                                                <asp:Label ID="lblError" Text="" ForeColor="Red" runat="server" Visible="false"></asp:Label>
                                                            </div>
                                                            <div id="divemptyFunctionPopup"></div>
                                                            <div id="divbsView" style="display: none;">
                                                                <div class="scroll-pane-diagram">
                                                                    <div id="bsbody" runat="server">
                                                                    </div>
                                                                </div>
                                                                <div id="divLegends" runat="server">
                                                                    <div class="col-md-4 d3-legend">
                                                                        <span class="label label-danger">&nbsp;</span>
                                                                        <span class="text-legend">Business Service having more than 30% InfraObjects affected</span>
                                                                    </div>
                                                                    <div class="col-md-4 d3-legend">
                                                                        <span class="label label-warning">&nbsp;</span>
                                                                        <span class="text-legend">Business Service having less than 30% InfraObjects affected</span>
                                                                    </div>
                                                                    <div class="col-md-4 d3-legend">
                                                                        <span class="label label-path-link">&nbsp;</span>
                                                                        <span class="text-legend">Business service having none of InfraObjects affected Or Business Service having InfraObjects with low priorities are affected</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <%--   <div id="divIncView" style="display: none;">
                                                                    <div id="Incbody" runat="server">
                                                                    </div>
                                                                    <div id="incSummary" runat="server">
                                                                        <h4>
                                                                            <asp:Label runat="server" ID="lblIncId" class="spnBreadCrum" Text=""></asp:Label>
                                                                            <b>Financial Impact -<asp:Label runat="server" ID="lblFinancialImpact" class="medium-label" Text=""> </asp:Label></b></h4>

                                                                        <table class="table text-small margin-none">
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td><b>Impacted Infra Component</b></td>
                                                                                    <td><b>Impacted Business Service</b></td>
                                                                                    <td><b>Impacted Business Application</b></td>

                                                                                </tr>
                                                                                <tr>
                                                                                    <td>
                                                                                        <asp:Label runat="server" ID="lblIconComponent" CssClass=""></asp:Label>
                                                                                        <asp:Label Text="" runat="server" ID="lblComponentName"></asp:Label></td>

                                                                                    <td>
                                                                                        <asp:Panel ID="pnlBSCostAppend" runat="server">
                                                                                        </asp:Panel>
                                                                                    </td>
                                                                                    <td>
                                                                                        <asp:Panel ID="pnlappend" runat="server">
                                                                                        </asp:Panel>

                                                                                    </td>

                                                                                </tr>
                                                                            </tbody>

                                                                        </table>
                                                                    </div>
                                                                </div>--%>
                                                            <div id="divIncView" style="display: none;">

                                                                <div class="row" style="margin-left: -5px; margin-right: -5px;">
                                                                    <div id="incSummary" runat="server">

                                                                        <div class="col-md-4 padding-noneLRnew">
                                                                            <img src="../Images/icons/icon-impact-incident-2.png" style="margin: 0px 5px; float: left;" />
                                                                            <h4 class=" heading" style="padding: 0 5px">Incident Details</h4>
                                                                            <%--    <label style="display: inline; vertical-align: middle; margin-left: 2px; width: 100%; text-transform: uppercase; font-size: 12px !important;">
                                                                                    Incident Details
                                                                                </label>--%>

                                                                            <hr style="border-top-color: #346d9d; margin: 5px 0 0px 0px; border-top-width: 2px" />
                                                                            <div class="scroll-pane-new" style="max-height: 200px">
                                                                                <table id="impactincidentdetailstable" class="table text-small margin-none smallimpacttable" style="width: 100%; table-layout: fixed;">
                                                                                    <tbody>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Incident ID :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblIncId" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">InfraObject Name :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblInfraID" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Impact Status :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblImpactStatus" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>

                                                                                        <tr>
                                                                                            <td style="width: 50%;">Impacted Component :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblImpactedComp" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Component Name:</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblDBCompName" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Impacted At :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblIncidentTime" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Impact Down Time :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblIncdowntime" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Impact Resolve Time :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblResolveTime" class="spnBreadCrum" Text=""></asp:Label>
                                                                                            </td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Incident Description :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblIncidentDescription" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Component DR Available :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblDRavailable" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>
                                                                                        <tr>
                                                                                            <td style="width: 50%;">Component DR Activated :</td>

                                                                                            <td style="width: 50%;">
                                                                                                <asp:Label runat="server" ID="lblDRActivated" class="spnBreadCrum" Text=""></asp:Label></td>
                                                                                        </tr>

                                                                                        <%-- <tr>
                                                                                        <td style="width: 48%;">Financial Impact :</td>

                                                                                        <td style="width: 52%;">
                                                                                            <asp:Label runat="server" ID="lblFinancialImpact" class="spnBreadCrum" Text=""> </asp:Label></td>
                                                                                    </tr>--%>
                                                                                    </tbody>
                                                                                </table>
                                                                            </div>
                                                                        </div>

                                                                        <div class="col-md-8 padding-noneLRnew">
                                                                            <img src="../Images/icons/icon-impact-diagram-2.png" style="margin: 0px 5px; float: left;" />
                                                                            <h4 class=" heading" style="padding: 0 5px">Impact Diagram</h4>
                                                                            <%--<label style="display: inline; vertical-align: middle; margin-left: 2px; width: 100%; text-transform: uppercase; font-size: 12px !important;">
                                                                                    Impact Diagram
                                                                                </label>--%>
                                                                            <hr style="border-top-color: #346d9d; margin: 5px 0 0px 0px; border-top-width: 2px" />
                                                                            <div class="scroll-pane-new-graph">

                                                                                <div id="Incbody" runat="server">
                                                                                </div>
                                                                                <div class="col-md-12 text-right graph-legend">
                                                                                    <img src="../Images/icons/yellow-dot.png" />
                                                                                    <label class="margin-right">Partial Impact</label>
                                                                                    <img src="../Images/icons/orange-dot.png" />
                                                                                    <label class="margin-right">Major Impact</label>
                                                                                    <img src="../Images/icons/red-dot.png" />
                                                                                    <label class="margin-right">Total Impact</label>
                                                                                </div>
                                                                            </div>

                                                                        </div>

                                                                    </div>
                                                                </div>

                                                                <div class="row" style="margin-left: -5px; margin-right: -5px;">
                                                                    <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                                                                        <div class="col-md-8 padding-noneLRnew" style="height: 110px; margin-top: 10px;">
                                                                            <img src="../Images/icons/icon-impact-details-1.png" style="margin: 0px 5px; float: left;" />
                                                                            <h4 class=" heading" style="padding: 0 5px">Current Financial Impact Due To Incident</h4>
                                                                            <%--<label style="display: inline; vertical-align: middle; margin-left: 2px; width: 100%; text-transform: uppercase; font-size: 12px !important;">
                                                                                    Current Financial Impact Due To Incident
                                                                                </label>--%>
                                                                            <hr style="border-top-color: #346d9d; margin: 5px 0 0px 0px; border-top-width: 2px" />
                                                                            <div class="scroll-pane-new" style="max-height: 100px">
                                                                                <table id="impacttable" class="table text-small margin-none smallimpacttable">
                                                                                    <tbody>
                                                                                        <tr>
                                                                                            <td style="border-top: 0px solid #ddd; width: 26%;"><b>Impacted Infra Component</b></td>
                                                                                            <td style="border-top: 0px solid #ddd; width: 27%;"><b>Impacted Business Service</b></td>
                                                                                            <td style="border-top: 0px solid #ddd; width: 34%;"><b>Impacted Business Application</b></td>
                                                                                        </tr>
                                                                                        <tr>

                                                                                            <td>
                                                                                                <asp:Label runat="server" ID="lblIconComponent" CssClass=""></asp:Label>
                                                                                                <asp:Label Text="" runat="server" ID="lblComponentName"></asp:Label></td>

                                                                                            <td>
                                                                                                <asp:Panel ID="pnlBSCostAppend" runat="server">
                                                                                                </asp:Panel>
                                                                                            </td>
                                                                                            <td>
                                                                                                <asp:Panel ID="pnlappend" runat="server">
                                                                                                </asp:Panel>

                                                                                            </td>


                                                                                        </tr>
                                                                                    </tbody>

                                                                                </table>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-4 padding-noneLRnew" style="margin-top: 10px;">
                                                                            <img src="../Images/icons/icon-impact-cost-1.png" style="margin: 0px 5px; float: left;" />
                                                                            <h4 class=" heading" style="padding: 0 5px">Impact Cost</h4>
                                                                            <%--<label style="display: inline; vertical-align: middle; margin-left: 2px; width: 100%; text-transform: uppercase; font-size: 12px !important;">
                                                                                    Impact Cost
                                                                                </label>--%>
                                                                            <hr style="border-top-color: #346d9d; margin: 5px 0 0px 0px; border-top-width: 2px" />
                                                                            <%--border-bottom: 1px solid #346d9d;--%>
                                                                            <div>
                                                                                <asp:Label ID="lblFinancialImapactTotalCost" CssClass="FinancialImapactTotalCost" runat="server" Text=""></asp:Label>
                                                                            </div>
                                                                            <div>
                                                                                <div class="impactcostdetails" style="margin-right: 8px;">
                                                                                    <label>Impacted At</label>
                                                                                    <hr />
                                                                                    <asp:Label ID="lblIncidentTime2" runat="server" Text="" CssClass=""></asp:Label>
                                                                                </div>
                                                                                <div class="impactcostdetails">
                                                                                    <label>Impact Down Time</label>
                                                                                    <hr />
                                                                                    <asp:Label ID="lblIncdowntime2" runat="server" Text="" CssClass=""></asp:Label>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>

                                                                </div>



                                                            </div>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>
                                            </div>

                                        </telerik:RadPane>


                                    </telerik:RadSplitter>
                                </telerik:RadPane>

                                <telerik:RadPane ID="RadPane4" runat="server" MaxHeight="250" Height="150" Visible="false" Style="display: none">
                                    <div class="demo-container size-thin">
                                        <telerik:RadSplitter ID="RadSplitter3" runat="server" Height="300" Visible="false">
                                            <telerik:RadPane ID="RadPane6" runat="server" MinWidth="80" MaxWidth="300" Width="270">
                                                <div class="widget box-shadow-none border-none">
                                                    <asp:UpdatePanel ID="udpBF" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                            <div class="widget-head">
                                                                <h4 class=" heading">
                                                                    <asp:Label runat="server" ID="lblName" Text="" CssClass="spnBreadCrum"></asp:Label>
                                                                    Config Details</h4>
                                                            </div>
                                                            <div class="widget-body scroll-pane-bottom col-md-12">
                                                                <asp:Panel runat="server" ID="pnlbusinessFunction">
                                                                    <table class="table" style="width: 230px;">
                                                                        <tbody>
                                                                            <tr>
                                                                                <td class="text-primary">Business Service </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblBSName" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>

                                                                            <tr>
                                                                                <td class="text-primary">Criticality Level  </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblCriticalityLevel" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Static RPO  </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblIsStatic" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Configure RPO </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblConfiguredRPO" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Configure RTO </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblConfiguredRTO" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Configure MAO </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblConfiguredMAO" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>

                                                                        </tbody>
                                                                    </table>
                                                                </asp:Panel>

                                                                <asp:Panel runat="server" ID="pnlbusinessService">
                                                                    <table class="table" style="width: 230px;">
                                                                        <tbody>
                                                                            <tr>
                                                                                <td class="text-primary">Company Name </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblCompanyName" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Site Name </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblSiteName" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Priority </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblPriority" CssClass="pull-right strong" Text=""></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </asp:Panel>

                                                                <asp:Panel runat="server" ID="pnlinfraObject">
                                                                    <table class="table" style="width: 230px;">
                                                                        <tbody>
                                                                            <tr>
                                                                                <td class="text-primary">Business Service </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblIOBSName" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>

                                                                            <tr>
                                                                                <td class="text-primary">Business Function </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblIOBFName" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">DR Ready </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblIsDRReady" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Type </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblType" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Sub Type</td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblSubType" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Recovery Solution Type </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblRecSolType" Text="" CssClass="pull-right strong">Oracle Dataguard</asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Near Site </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblIsNearSite" Text="" CssClass="pull-right strong"> </asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Priority </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblIOPriority" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Primary Server </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblPRServer" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">DR Server </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblDRServer" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Primary Database </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblPRDatabase" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">DR Database </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblDRDatabase" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">Primary Replication </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblPRRep" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                            <tr>
                                                                                <td class="text-primary">DR Replication </td>
                                                                                <td>
                                                                                    <asp:Label runat="server" ID="lblDRRep" Text="" CssClass="pull-right strong"></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                        </tbody>
                                                                    </table>
                                                                </asp:Panel>
                                                            </div>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>
                                            </telerik:RadPane>
                                            <telerik:RadSplitBar ID="RadSplitbar4" runat="server">
                                            </telerik:RadSplitBar>
                                            <telerik:RadPane ID="RadPane7" runat="server" MinWidth="400" MaxWidth="1050" Width="1010" Visible="false">
                                                <div class="widget box-shadow-none border-none">
                                                    <div class="widget-head">
                                                        <h4 class=" heading">Incident Record</h4>
                                                    </div>
                                                    <div class="widget-body scroll-pane-bottom">
                                                        <asp:UpdatePanel ID="updPnlIncidentList" runat="server" UpdateMode="Conditional">
                                                            <ContentTemplate>
                                                                <telerik:RadTreeList ID="RdTreeListIncidentRecord" runat="server" DataKeyNames="Id" ParentDataKeyNames="ParentID"
                                                                    Skin="Default" AutoGenerateColumns="false" AllowPaging="true" AllowSorting="true" PageSize="10"
                                                                    OnNeedDataSource="RdTreeListIncidentRecord_NeedDataSource">
                                                                    <Columns>
                                                                        <telerik:TreeListBoundColumn DataField="Id" UniqueName="Id" HeaderText="" Visible="false">
                                                                        </telerik:TreeListBoundColumn>
                                                                        <telerik:TreeListTemplateColumn DataField="Item" UniqueName="Item"
                                                                            HeaderText="Impact Severity" MaxWidth="26%">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactSeverity" runat="server" Text='<%#Eval("ParentID") %>' Visible="false" />
                                                                                <asp:Label ID="lblID" runat="server" Text='<%#Eval("Id") %>' Visible="false" />
                                                                                <asp:Label ID="lblItemId" runat="server" Text='<%#Eval("ItemId") %>' Visible="false" />
                                                                                <asp:Label ID="lblItemType" runat="server" Text='<%#Eval("ItemType") %>' Visible="false" />
                                                                                <asp:Label ID="Label1" runat="server" Text='<%#Eval("Item") %>' Visible="false" />
                                                                                <a>
                                                                                    <asp:Label ID="lblItem" runat="server" CssClass="text text-info" Visible="true" Text='<%#Eval("Item")%>' /></a>
                                                                                <br />
                                                                                <asp:Label ID="lblImpactType" runat="server" Text='<%#Eval("ImpactType") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ImpactEntity" UniqueName="ImpactEntity"
                                                                            HeaderText="Impact Entity" ItemStyle-VerticalAlign="Middle" MaxWidth="15%">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactEntity" runat="server" Text='<%#Eval("ImpactEntity") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="IncidentName" UniqueName="IncidentName"
                                                                            HeaderText="CP Incident ID" ItemStyle-VerticalAlign="Middle" MaxWidth="10%">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblCpIncidentName" runat="server" Text='<%#Eval("incidentCode") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ClientSystemTicketID" UniqueName="BelongsTo"
                                                                            HeaderText="Client System Ticket ID(HP/BMC)" ItemStyle-VerticalAlign="Middle" MaxWidth="13%">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblClientSystemTicketID" runat="server" Text='<%#Eval("ClientSystemTicketID") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ImpactStartEndDateTime" UniqueName="ImpactStartEndDateTime"
                                                                            HeaderText="Impact Start-End Date Time" ItemStyle-VerticalAlign="Middle" MaxWidth="18%">

                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactStartDate" runat="server" Text='<%#Eval("ImpactStartDateTime") %>' />
                                                                                <asp:Label ID="lblImpactEndDate" runat="server" Text='<%#Eval("ImpactEndDateTime") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ImpactReason" UniqueName="ImpactReason"
                                                                            HeaderText="Impact Reason" ItemStyle-HorizontalAlign="Center" ItemStyle-VerticalAlign="Middle">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactReason" runat="server" Text='<%#Eval("ImpactReason") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ImpactCost" UniqueName="ImpactCost"
                                                                            HeaderText="Impact Cost" ItemStyle-VerticalAlign="Middle" MaxWidth="8%">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblImpactCost" runat="server" Text='<%#Eval("ImpactCost") %>' />

                                                                            </ItemTemplate>
                                                                            <HeaderStyle Width="10px"></HeaderStyle>
                                                                        </telerik:TreeListTemplateColumn>

                                                                        <telerik:TreeListTemplateColumn DataField="ParentID" Visible="false" UniqueName="ParentID"
                                                                            HeaderText="ParentId" ItemStyle-HorizontalAlign="Center" ItemStyle-VerticalAlign="Middle">
                                                                            <ItemTemplate>
                                                                                <asp:Label ID="lblIParent" runat="server" Text='<%#Eval("ParentID") %>' />
                                                                            </ItemTemplate>

                                                                        </telerik:TreeListTemplateColumn>


                                                                    </Columns>
                                                                </telerik:RadTreeList>
                                                            </ContentTemplate>
                                                        </asp:UpdatePanel>
                                                    </div>
                                                </div>

                                            </telerik:RadPane>

                                        </telerik:RadSplitter>
                                    </div>
                                </telerik:RadPane>
                            </telerik:RadSplitter>
                            <%--</telerik:RadPageView>--%>

                            <%-- </telerik:RadMultiPage>--%>
                        </div>
                    </div>


                </telerik:RadPageView>
                <telerik:RadPageView ID="RadPageView3" runat="server">
                </telerik:RadPageView>
                <telerik:RadPageView ID="RadPageView4" runat="server">
                </telerik:RadPageView>
            </telerik:RadMultiPage>
        </div>
    </div>
    <%-- </form>--%>
    <%--<script type="text/javascript">
        if ($('.selectpicker').length)
            $('.selectpicker').selectpicker();
    </script>--%>
    <%--<script src="../Script/jquery.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>--%>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="../Script/d3.min.js"></script>
    <script src="../Script/WhatIfAnalysis.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {
            $(".scroll-pane-new").mCustomScrollbar({
                axis: "y",
                // setHeight: "120px",
            });
            $(".scroll-pane-new-graph").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
            });
            $(".scroll-pane-bottom-new-bottom").mCustomScrollbar({
                axis: "y",
                setHeight: "180px",
            });

        });

        function pageLoad() {

            $(".scroll-pane-new").mCustomScrollbar({
                axis: "y",

            });
            $(".scroll-pane-new-graph").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
            });
            $(".scroll-pane-bottom-new-bottom").mCustomScrollbar({
                axis: "y",
                setHeight: "180px",
            });
            $(".scroll-pane-diagram").mCustomScrollbar({
                axis: "y",
                setHeight: "270px",
            });
            $(".scroll-pane-inc-det").mCustomScrollbar({
                axis: "y",
                setHeight: "320px",
            });
        }
    </script>
</asp:Content>



<%--</body>
</html>--%>



