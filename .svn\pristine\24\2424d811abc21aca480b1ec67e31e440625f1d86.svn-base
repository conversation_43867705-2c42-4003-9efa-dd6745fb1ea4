﻿using CP.BusinessFacade;

namespace CP.UI.Code.Replication.ReplicationInfo
{
    public class SnapMirrorReplicationInfo : IReplicationInfo
    {
        private readonly IFacade _facade = new Facade();

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectid)
        {
            var currentEntityType = currentEntity.GetType();

            var snapMirror = _facade.GetSnapMirrorMonitorByInfraObjectId(infraObjectid);

            if (snapMirror != null)
            {
                currentEntityType.GetProperty("Source").SetValue(currentEntity, snapMirror.Source, null);

                currentEntityType.GetProperty("Destination").SetValue(currentEntity, snapMirror.Destination, null);

                currentEntityType.GetProperty("State").SetValue(currentEntity, snapMirror.State, null);

                currentEntityType.GetProperty("Lag").SetValue(currentEntity, snapMirror.Lag, null);

                currentEntityType.GetProperty("Status").SetValue(currentEntity, snapMirror.Status, null);
            }
            else
            {
                currentEntityType.GetProperty("Source").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("Destination").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("State").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("Lag").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("Status").SetValue(currentEntity, "N/A", null);
            }

            return currentEntity;
        }

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectid,int mailBoxId, string mailboxname)
        {
            return currentEntity;
        }
    }
}