using System;
using CP.Helper;
using System.Drawing;
namespace CP.UI.Report.TelerikReports
{
    partial class SVCGlobalMirrorChartReport
    {
        #region Component Designer generated code
        /// <summary>
        /// Required method for telerik Reporting designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(SVCGlobalMirrorChartReport));
            Telerik.Reporting.TypeReportSource typeReportSource1 = new Telerik.Reporting.TypeReportSource();
            Telerik.Reporting.GraphGroup graphGroup1 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.GraphGroup graphGroup4 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.GraphTitle graphTitle1 = new Telerik.Reporting.GraphTitle();
            Telerik.Reporting.CategoryScale categoryScale1 = new Telerik.Reporting.CategoryScale();
            Telerik.Reporting.NumericalScale numericalScale1 = new Telerik.Reporting.NumericalScale();
            Telerik.Reporting.GraphGroup graphGroup2 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.MonochromaticPalette monochromaticPalette1 = new Telerik.Reporting.Drawing.MonochromaticPalette();
            Telerik.Reporting.GraphGroup graphGroup3 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.MonochromaticPalette monochromaticPalette2 = new Telerik.Reporting.Drawing.MonochromaticPalette();
            Telerik.Reporting.GraphGroup graphGroup5 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.ColorPalette colorPalette1 = new Telerik.Reporting.Drawing.ColorPalette();
            Telerik.Reporting.GraphGroup graphGroup8 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.GraphTitle graphTitle2 = new Telerik.Reporting.GraphTitle();
            Telerik.Reporting.CategoryScale categoryScale2 = new Telerik.Reporting.CategoryScale();
            Telerik.Reporting.NumericalScale numericalScale2 = new Telerik.Reporting.NumericalScale();
            Telerik.Reporting.GraphGroup graphGroup6 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.MonochromaticPalette monochromaticPalette3 = new Telerik.Reporting.Drawing.MonochromaticPalette();
            Telerik.Reporting.GraphGroup graphGroup7 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.MonochromaticPalette monochromaticPalette4 = new Telerik.Reporting.Drawing.MonochromaticPalette();
            Telerik.Reporting.GraphGroup graphGroup9 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.ColorPalette colorPalette2 = new Telerik.Reporting.Drawing.ColorPalette();
            Telerik.Reporting.GraphGroup graphGroup12 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.GraphTitle graphTitle3 = new Telerik.Reporting.GraphTitle();
            Telerik.Reporting.CategoryScale categoryScale3 = new Telerik.Reporting.CategoryScale();
            Telerik.Reporting.NumericalScale numericalScale3 = new Telerik.Reporting.NumericalScale();
            Telerik.Reporting.GraphGroup graphGroup10 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.GraphGroup graphGroup11 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.GraphGroup graphGroup13 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.ColorPalette colorPalette3 = new Telerik.Reporting.Drawing.ColorPalette();
            Telerik.Reporting.GraphGroup graphGroup16 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.GraphTitle graphTitle4 = new Telerik.Reporting.GraphTitle();
            Telerik.Reporting.CategoryScale categoryScale4 = new Telerik.Reporting.CategoryScale();
            Telerik.Reporting.NumericalScale numericalScale4 = new Telerik.Reporting.NumericalScale();
            Telerik.Reporting.GraphGroup graphGroup14 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.MonochromaticPalette monochromaticPalette5 = new Telerik.Reporting.Drawing.MonochromaticPalette();
            Telerik.Reporting.GraphGroup graphGroup15 = new Telerik.Reporting.GraphGroup();
            Telerik.Reporting.Drawing.MonochromaticPalette monochromaticPalette6 = new Telerik.Reporting.Drawing.MonochromaticPalette();
            Telerik.Reporting.NavigateToReportAction navigateToReportAction1 = new Telerik.Reporting.NavigateToReportAction();
            Telerik.Reporting.ReportParameter reportParameter2 = new Telerik.Reporting.ReportParameter();
            Telerik.Reporting.ReportParameter reportParameter3 = new Telerik.Reporting.ReportParameter();
            Telerik.Reporting.ReportParameter reportParameter4 = new Telerik.Reporting.ReportParameter();
            Telerik.Reporting.ReportParameter reportParameter5 = new Telerik.Reporting.ReportParameter();
            Telerik.Reporting.ReportParameter reportParameter6 = new Telerik.Reporting.ReportParameter();
            Telerik.Reporting.ReportParameter reportParameter7 = new Telerik.Reporting.ReportParameter();

            Telerik.Reporting.ReportParameter reportParameter8 = new Telerik.Reporting.ReportParameter();
            Telerik.Reporting.ReportParameter reportParameter9 = new Telerik.Reporting.ReportParameter();
            Telerik.Reporting.ReportParameter reportParameter10 = new Telerik.Reporting.ReportParameter();
            Telerik.Reporting.ReportParameter reportParameter11 = new Telerik.Reporting.ReportParameter();

            Telerik.Reporting.Drawing.StyleRule styleRule1 = new Telerik.Reporting.Drawing.StyleRule();
            this.pageHeaderSection1 = new Telerik.Reporting.PageHeaderSection();
            this.pictureBox1 = new Telerik.Reporting.PictureBox();
            this.pictureBox2 = new Telerik.Reporting.PictureBox();
            this.shape3 = new Telerik.Reporting.Shape();
            this.detail = new Telerik.Reporting.DetailSection();
            this.panel3 = new Telerik.Reporting.Panel();
            this.graph1 = new Telerik.Reporting.Graph();
            this.cartesianCoordinateSystem32 = new Telerik.Reporting.CartesianCoordinateSystem();
            this.graphAxis64 = new Telerik.Reporting.GraphAxis();
            this.graphAxis63 = new Telerik.Reporting.GraphAxis();
            this.sqlDataSource1 = new Telerik.Reporting.SqlDataSource();
            this.lineSeries1 = new Telerik.Reporting.LineSeries();
            this.lineSeries2 = new Telerik.Reporting.LineSeries();
            this.textBox19 = new Telerik.Reporting.TextBox();
            this.textBox20 = new Telerik.Reporting.TextBox();
            this.textBox22 = new Telerik.Reporting.TextBox();
            this.textBox4 = new Telerik.Reporting.TextBox();
            this.shape4 = new Telerik.Reporting.Shape();
            this.shape6 = new Telerik.Reporting.Shape();
            this.panel1 = new Telerik.Reporting.Panel();
            this.graph3 = new Telerik.Reporting.Graph();
            this.cartesianCoordinateSystem36 = new Telerik.Reporting.CartesianCoordinateSystem();
            this.graphAxis72 = new Telerik.Reporting.GraphAxis();
            this.graphAxis71 = new Telerik.Reporting.GraphAxis();
            this.sqlDataSource3 = new Telerik.Reporting.SqlDataSource();
            this.lineSeries3 = new Telerik.Reporting.LineSeries();
            this.lineSeries4 = new Telerik.Reporting.LineSeries();
            this.textBox3 = new Telerik.Reporting.TextBox();
            this.textBox17 = new Telerik.Reporting.TextBox();
            this.textBox24 = new Telerik.Reporting.TextBox();
            this.textBox25 = new Telerik.Reporting.TextBox();
            this.shape8 = new Telerik.Reporting.Shape();
            this.shape11 = new Telerik.Reporting.Shape();
            this.panel2 = new Telerik.Reporting.Panel();
            this.textBox2 = new Telerik.Reporting.TextBox();
            this.textBox26 = new Telerik.Reporting.TextBox();
            this.textBox27 = new Telerik.Reporting.TextBox();
            this.textBox28 = new Telerik.Reporting.TextBox();
            this.graph5 = new Telerik.Reporting.Graph();
            this.cartesianCoordinateSystem1 = new Telerik.Reporting.CartesianCoordinateSystem();
            this.graphAxis1 = new Telerik.Reporting.GraphAxis();
            this.graphAxis2 = new Telerik.Reporting.GraphAxis();
            this.sqlDataSource7 = new Telerik.Reporting.SqlDataSource();
            this.barSeries1 = new Telerik.Reporting.BarSeries();
            this.panel4 = new Telerik.Reporting.Panel();
            this.textBox6 = new Telerik.Reporting.TextBox();
            this.textBox32 = new Telerik.Reporting.TextBox();
            this.textBox34 = new Telerik.Reporting.TextBox();
            this.textBox35 = new Telerik.Reporting.TextBox();
            this.graph4 = new Telerik.Reporting.Graph();
            this.cartesianCoordinateSystem2 = new Telerik.Reporting.CartesianCoordinateSystem();
            this.graphAxis3 = new Telerik.Reporting.GraphAxis();
            this.graphAxis4 = new Telerik.Reporting.GraphAxis();
            this.sqlDataSource8 = new Telerik.Reporting.SqlDataSource();
            this.lineSeries7 = new Telerik.Reporting.LineSeries();
            this.lineSeries8 = new Telerik.Reporting.LineSeries();
            this.shape12 = new Telerik.Reporting.Shape();
            this.shape13 = new Telerik.Reporting.Shape();
            this.pageFooterSection1 = new Telerik.Reporting.PageFooterSection();
            this.pictureBox6 = new Telerik.Reporting.PictureBox();
            this.textBox5 = new Telerik.Reporting.TextBox();
            this.textBox61 = new Telerik.Reporting.TextBox();
            this.shape10 = new Telerik.Reporting.Shape();
            this.reportHeaderSection1 = new Telerik.Reporting.ReportHeaderSection();
            this.panel6 = new Telerik.Reporting.Panel();
            this.textBox9 = new Telerik.Reporting.TextBox();
            this.textBox10 = new Telerik.Reporting.TextBox();
            this.textBox11 = new Telerik.Reporting.TextBox();
            this.textBox12 = new Telerik.Reporting.TextBox();
            this.textBox14 = new Telerik.Reporting.TextBox();
            this.textBox16 = new Telerik.Reporting.TextBox();
            this.pictureBox4 = new Telerik.Reporting.PictureBox();
            this.pictureBox3 = new Telerik.Reporting.PictureBox();
            this.pictureBox7 = new Telerik.Reporting.PictureBox();
            this.pictureBox8 = new Telerik.Reporting.PictureBox();
            this.shape2 = new Telerik.Reporting.Shape();
            this.textBox13 = new Telerik.Reporting.TextBox();
            this.textBox15 = new Telerik.Reporting.TextBox();
            this.shape1 = new Telerik.Reporting.Shape();
            this.shape5 = new Telerik.Reporting.Shape();
            this.textBox1 = new Telerik.Reporting.TextBox();
            this.shape7 = new Telerik.Reporting.Shape();
            this.textBox49 = new Telerik.Reporting.TextBox();
            this.panel5 = new Telerik.Reporting.Panel();
            this.textBox37 = new Telerik.Reporting.TextBox();
            this.textBox7 = new Telerik.Reporting.TextBox();
            this.pictureBox5 = new Telerik.Reporting.PictureBox();
            this.shape9 = new Telerik.Reporting.Shape();
            this.sqlDataSource2 = new Telerik.Reporting.SqlDataSource();
            this.reportFooterSection1 = new Telerik.Reporting.ReportFooterSection();
            this.subReport1 = new Telerik.Reporting.SubReport();
            this.pictureBox17 = new Telerik.Reporting.PictureBox();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // pageHeaderSection1
            // 
            this.pageHeaderSection1.Height = Telerik.Reporting.Drawing.Unit.Inch(0.4122014045715332D);
            this.pageHeaderSection1.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.pictureBox1,
            this.pictureBox2,
            this.shape3,
            this.pictureBox17});
            this.pageHeaderSection1.Name = "pageHeaderSection1";
            // 
            // pictureBox1
            // 
            this.pictureBox1.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0D), Telerik.Reporting.Drawing.Unit.Inch(3.9339065551757812E-05D));
            this.pictureBox1.MimeType = "image/png";
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(2.6000001430511475D), Telerik.Reporting.Drawing.Unit.Inch(0.36000001430511475D));
            this.pictureBox1.Value = ((object)(resources.GetObject("pictureBox1.Value")));
            // 
            // pictureBox2
            // 
            this.pictureBox2.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.9899606704711914D), Telerik.Reporting.Drawing.Unit.Inch(3.9418537198798731E-05D));
            this.pictureBox2.MimeType = "image/jpeg";
            this.pictureBox2.Name = "pictureBox2";
            this.pictureBox2.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(2.2999999523162842D), Telerik.Reporting.Drawing.Unit.Inch(0.36000001430511475D));
            this.pictureBox2.Value = ((object)(resources.GetObject("pictureBox2.Value")));
            // 
            // shape3
            // 
            this.shape3.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0D), Telerik.Reporting.Drawing.Unit.Inch(0.36011806130409241D));
            this.shape3.Name = "shape3";
            this.shape3.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape3.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.283395767211914D), Telerik.Reporting.Drawing.Unit.Inch(0.0520833320915699D));
            this.shape3.Style.Color = System.Drawing.Color.Teal;
            this.shape3.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Point(2D);
            // 
            // detail
            // 
            this.detail.Height = Telerik.Reporting.Drawing.Unit.Inch(12.239584922790527D);
            this.detail.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.panel3,
            this.panel1,
            this.panel2,
            this.panel4});
            this.detail.Name = "detail";
            this.detail.PageBreak = Telerik.Reporting.PageBreak.After;
            // 
            // panel3
            // 
            this.panel3.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.graph1,
            this.textBox19,
            this.textBox20,
            this.textBox22,
            this.textBox4,
            this.shape4,
            this.shape6});
            this.panel3.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.09375D), Telerik.Reporting.Drawing.Unit.Inch(7.8837074397597462E-05D));
            this.panel3.Name = "panel3";
            this.panel3.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.120498657226563D), Telerik.Reporting.Drawing.Unit.Inch(2.585141658782959D));
            this.panel3.Style.BorderColor.Default = System.Drawing.Color.LightGray;
            this.panel3.Style.BorderStyle.Default = Telerik.Reporting.Drawing.BorderType.Solid;
            // 
            // graph1
            // 
            graphGroup1.Groupings.Add(new Telerik.Reporting.Grouping("=Fields.CreateDate"));
            graphGroup1.Name = "createDateGroup";
            graphGroup1.Sortings.Add(new Telerik.Reporting.Sorting("=Fields.CreateDate", Telerik.Reporting.SortDirection.Asc));
            this.graph1.CategoryGroups.Add(graphGroup1);
            this.graph1.CoordinateSystems.Add(this.cartesianCoordinateSystem32);
            this.graph1.DataSource = this.sqlDataSource1;
            this.graph1.Legend.Position = Telerik.Reporting.GraphItemPosition.BottomCenter;
            this.graph1.Legend.Style.LineColor = System.Drawing.Color.LightGray;
            this.graph1.Legend.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.graph1.Legend.Style.Visible = false;
            this.graph1.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(3.9418537198798731E-05D), Telerik.Reporting.Drawing.Unit.Inch(0.2416275292634964D));
            this.graph1.Name = "graph1";
            this.graph1.PlotAreaStyle.LineColor = System.Drawing.Color.LightGray;
            this.graph1.PlotAreaStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.graph1.Series.Add(this.lineSeries1);
            this.graph1.Series.Add(this.lineSeries2);
            graphGroup4.Name = "seriesGroup";
            this.graph1.SeriesGroups.Add(graphGroup4);
            this.graph1.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(7.2078375816345215D), Telerik.Reporting.Drawing.Unit.Inch(2.3434743881225586D));
            this.graph1.Style.BorderColor.Default = System.Drawing.Color.LightGray;
            this.graph1.Style.BorderStyle.Default = Telerik.Reporting.Drawing.BorderType.None;
            this.graph1.Style.Padding.Bottom = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph1.Style.Padding.Left = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph1.Style.Padding.Right = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph1.Style.Padding.Top = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph1.Style.Visible = true;
            graphTitle1.IsInsidePlotArea = false;
            graphTitle1.Position = Telerik.Reporting.GraphItemPosition.TopCenter;
            graphTitle1.Style.Color = System.Drawing.Color.LightSlateGray;
            graphTitle1.Style.Font.Bold = false;
            graphTitle1.Style.Font.Name = "Bell MT";
            graphTitle1.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(13D);
            graphTitle1.Style.LineColor = System.Drawing.Color.LightGray;
            graphTitle1.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            graphTitle1.Style.Visible = false;
            graphTitle1.Text = "Weekly DataLag";
            this.graph1.Titles.Add(graphTitle1);
            // 
            // cartesianCoordinateSystem32
            // 
            this.cartesianCoordinateSystem32.Name = "cartesianCoordinateSystem32";
            this.cartesianCoordinateSystem32.XAxis = this.graphAxis64;
            this.cartesianCoordinateSystem32.YAxis = this.graphAxis63;
            // 
            // graphAxis64
            // 
            this.graphAxis64.MajorGridLineStyle.BorderColor.Default = System.Drawing.Color.Black;
            this.graphAxis64.MajorGridLineStyle.LineColor = System.Drawing.Color.White;
            this.graphAxis64.MajorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis64.MajorGridLineStyle.Visible = true;
            this.graphAxis64.MajorTickMarkDisplayType = Telerik.Reporting.GraphAxisTickMarkDisplayType.None;
            this.graphAxis64.MinorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis64.MinorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis64.MinorGridLineStyle.Visible = false;
            this.graphAxis64.Name = "graphAxis64";
            this.graphAxis64.Scale = categoryScale1;
            this.graphAxis64.Style.BackgroundColor = System.Drawing.Color.White;
            this.graphAxis64.Style.Color = System.Drawing.Color.White;
            this.graphAxis64.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(1D);
            // 
            // graphAxis63
            // 
            this.graphAxis63.MajorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis63.MajorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis63.MajorGridLineStyle.Visible = true;
            this.graphAxis63.MinorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis63.MinorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis63.MinorGridLineStyle.Visible = false;
            this.graphAxis63.Name = "graphAxis63";
            this.graphAxis63.Scale = numericalScale1;
            this.graphAxis63.Style.Color = System.Drawing.Color.Black;
            this.graphAxis63.Style.Font.Name = "Tahoma";
            this.graphAxis63.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(10D);
            this.graphAxis63.Title = "DataLag In Minutes";
            this.graphAxis63.TitleStyle.Font.Name = "Tahoma";
            this.graphAxis63.TitleStyle.Font.Size = Telerik.Reporting.Drawing.Unit.Point(10D);
            // 
            // sqlDataSource1
            // 
            string conString = Utility.TelerikConnection();
            string dataProvider = Utility.TelerikProvider();
            DatalagReport rpt = new DatalagReport();
            string userParameters = rpt.GetNetAppParameter();
            string[] arr = userParameters.Split(' ');
            this.sqlDataSource1.ConnectionString = CryptographyHelper.Md5Decrypt(conString);
            this.sqlDataSource1.ProviderName = CryptographyHelper.Md5Decrypt(dataProvider);
            this.sqlDataSource1.Name = "sqlDataSource1";
            this.sqlDataSource1.CommandTimeout = 5000;
            this.sqlDataSource1.Parameters.AddRange(new Telerik.Reporting.SqlDataSourceParameter[] {
            new Telerik.Reporting.SqlDataSourceParameter(Utility.dbParaPrefix+"IINFRAOBJECTID", System.Data.DbType.Decimal, Convert.ToInt32(arr[0])),
            new Telerik.Reporting.SqlDataSourceParameter(Utility.dbParaPrefix+"IRECOVERYTYPE", System.Data.DbType.Decimal, Convert.ToInt32(arr[3]))});
            this.sqlDataSource1.SelectCommand = "DATALAG_WEEKLYMINUTEFORMAT";
            this.sqlDataSource1.SelectCommandType = Telerik.Reporting.SqlDataSourceCommandType.StoredProcedure;
            // 
            // lineSeries1
            // 
            graphGroup2.Groupings.Add(new Telerik.Reporting.Grouping("=Fields.CreateDate"));
            graphGroup2.Name = "createDateGroup";
            graphGroup2.Sortings.Add(new Telerik.Reporting.Sorting("=Fields.CreateDate", Telerik.Reporting.SortDirection.Asc));
            this.lineSeries1.CategoryGroup = graphGroup2;
            monochromaticPalette1.BaseColor = System.Drawing.Color.Red;
            this.lineSeries1.ColorPalette = monochromaticPalette1;
            this.lineSeries1.CoordinateSystem = this.cartesianCoordinateSystem32;
            this.lineSeries1.DataPointLabel = "=Sum(Fields.CURRENTDATALAG)";
            this.lineSeries1.DataPointLabelStyle.Visible = false;
            this.lineSeries1.DataPointStyle.Visible = false;
            this.lineSeries1.LegendItem.Style.BackgroundColor = System.Drawing.Color.Transparent;
            this.lineSeries1.LegendItem.Style.Font.Name = "Bell MT";
            this.lineSeries1.LegendItem.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.lineSeries1.LegendItem.Style.LineColor = System.Drawing.Color.Transparent;
            this.lineSeries1.LegendItem.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.lineSeries1.LegendItem.Value = "Deviation RPO";
            this.lineSeries1.LineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.lineSeries1.LineStyle.Visible = true;
            this.lineSeries1.LineType = Telerik.Reporting.LineSeries.LineTypes.Smooth;
            this.lineSeries1.MarkerMaxSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries1.MarkerMinSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries1.MarkerSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries1.Name = "lineSeries1";
            graphGroup3.Name = "seriesGroup";
            this.lineSeries1.SeriesGroup = graphGroup3;
            this.lineSeries1.Size = null;
            this.lineSeries1.Y = "=Sum(Fields.CURRENTDATALAG)";
            // 
            // lineSeries2
            // 
            this.lineSeries2.CategoryGroup = graphGroup2;
            monochromaticPalette2.BaseColor = System.Drawing.Color.SteelBlue;
            this.lineSeries2.ColorPalette = monochromaticPalette2;
            this.lineSeries2.CoordinateSystem = this.cartesianCoordinateSystem32;
            this.lineSeries2.DataPointLabel = "=Sum(Fields.CURRENTRPO)";
            this.lineSeries2.DataPointLabelStyle.Visible = false;
            this.lineSeries2.DataPointStyle.Visible = false;
            this.lineSeries2.LegendItem.Style.BackgroundColor = System.Drawing.Color.Transparent;
            this.lineSeries2.LegendItem.Style.Font.Name = "Bell MT";
            this.lineSeries2.LegendItem.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.lineSeries2.LegendItem.Style.LineColor = System.Drawing.Color.Transparent;
            this.lineSeries2.LegendItem.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.lineSeries2.LegendItem.Value = "Configured RPO";
            this.lineSeries2.LineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.lineSeries2.LineStyle.Visible = true;
            this.lineSeries2.LineType = Telerik.Reporting.LineSeries.LineTypes.Smooth;
            this.lineSeries2.MarkerMaxSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries2.MarkerMinSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries2.MarkerSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries2.Name = "lineSeries2";
            this.lineSeries2.SeriesGroup = graphGroup3;
            this.lineSeries2.Size = null;
            this.lineSeries2.Y = "=Sum(Fields.CURRENTRPO)";
            // 
            // textBox19
            // 
            this.textBox19.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(7.3079557418823242D), Telerik.Reporting.Drawing.Unit.Inch(0.33000001311302185D));
            this.textBox19.Name = "textBox19";
            this.textBox19.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(3.8125436305999756D), Telerik.Reporting.Drawing.Unit.Inch(0.38522085547447205D));
            this.textBox19.Value = "This graph shows average daily datalag in minutes over the past 7 days.";
            // 
            // textBox20
            // 
            this.textBox20.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.6231756210327148D), Telerik.Reporting.Drawing.Unit.Inch(0.71529978513717651D));
            this.textBox20.Name = "textBox20";
            this.textBox20.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.1925182342529297D), Telerik.Reporting.Drawing.Unit.Inch(0.23738861083984375D));
            this.textBox20.Value = "Configured RPO";
            // 
            // textBox22
            // 
            this.textBox22.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.6231756210327148D), Telerik.Reporting.Drawing.Unit.Inch(0.95276719331741333D));
            this.textBox22.Name = "textBox22";
            this.textBox22.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.1925182342529297D), Telerik.Reporting.Drawing.Unit.Inch(0.23738861083984375D));
            this.textBox22.Value = "Deviation RPO";
            // 
            // textBox4
            // 
            this.textBox4.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(2.7995665073394775D), Telerik.Reporting.Drawing.Unit.Inch(0D));
            this.textBox4.Name = "textBox4";
            this.textBox4.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.5D), Telerik.Reporting.Drawing.Unit.Inch(0.24154870212078095D));
            this.textBox4.Style.Color = System.Drawing.Color.Teal;
            this.textBox4.Style.Font.Bold = false;
            this.textBox4.Style.Font.Name = "Tahoma";
            this.textBox4.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(16D);
            this.textBox4.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox4.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox4.Value = "Weekly DataLag";
            // 
            // shape4
            // 
            this.shape4.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.2230958938598633D), Telerik.Reporting.Drawing.Unit.Inch(0.95276719331741333D));
            this.shape4.Name = "shape4";
            this.shape4.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape4.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.40000024437904358D), Telerik.Reporting.Drawing.Unit.Inch(0.18000000715255737D));
            this.shape4.Style.Color = System.Drawing.Color.Red;
            // 
            // shape6
            // 
            this.shape6.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.2230958938598633D), Telerik.Reporting.Drawing.Unit.Inch(0.71529978513717651D));
            this.shape6.Name = "shape6";
            this.shape6.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape6.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.40000024437904358D), Telerik.Reporting.Drawing.Unit.Inch(0.18000000715255737D));
            this.shape6.Style.Color = System.Drawing.Color.SteelBlue;
            // 
            // panel1
            // 
            this.panel1.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.graph3,
            this.textBox3,
            this.textBox17,
            this.textBox24,
            this.textBox25,
            this.shape8,
            this.shape11});
            this.panel1.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.0729166641831398D), Telerik.Reporting.Drawing.Unit.Inch(2.7395837306976318D));
            this.panel1.Name = "panel1";
            this.panel1.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.112544059753418D), Telerik.Reporting.Drawing.Unit.Inch(2.7051808834075928D));
            this.panel1.Style.BorderColor.Default = System.Drawing.Color.LightGray;
            this.panel1.Style.BorderStyle.Default = Telerik.Reporting.Drawing.BorderType.Solid;
            // 
            // graph3
            // 
            graphGroup5.Groupings.Add(new Telerik.Reporting.Grouping("=Fields.CreateDate"));
            graphGroup5.Name = "createDateGroup2";
            graphGroup5.Sortings.Add(new Telerik.Reporting.Sorting("=Fields.CreateDate", Telerik.Reporting.SortDirection.Asc));
            this.graph3.CategoryGroups.Add(graphGroup5);
            colorPalette1.Colors.Add(System.Drawing.Color.Green);
            colorPalette1.Colors.Add(System.Drawing.Color.Red);
            this.graph3.ColorPalette = colorPalette1;
            this.graph3.CoordinateSystems.Add(this.cartesianCoordinateSystem36);
            this.graph3.DataSource = this.sqlDataSource3;
            this.graph3.Legend.Position = Telerik.Reporting.GraphItemPosition.BottomCenter;
            this.graph3.Legend.Style.Font.Name = "Bell MT";
            this.graph3.Legend.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.graph3.Legend.Style.LineColor = System.Drawing.Color.LightGray;
            this.graph3.Legend.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.graph3.Legend.Style.Visible = false;
            this.graph3.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(-9.7012814448405038E-12D), Telerik.Reporting.Drawing.Unit.Inch(0.34791532158851624D));
            this.graph3.Name = "graph3";
            this.graph3.PlotAreaStyle.LineColor = System.Drawing.Color.SpringGreen;
            this.graph3.PlotAreaStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.graph3.Series.Add(this.lineSeries3);
            this.graph3.Series.Add(this.lineSeries4);
            graphGroup8.Name = "seriesGroup2";
            this.graph3.SeriesGroups.Add(graphGroup8);
            this.graph3.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(7.228334903717041D), Telerik.Reporting.Drawing.Unit.Inch(2.3572654724121094D));
            this.graph3.Style.BorderColor.Default = System.Drawing.Color.LightGray;
            this.graph3.Style.BorderStyle.Default = Telerik.Reporting.Drawing.BorderType.None;
            this.graph3.Style.Padding.Bottom = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph3.Style.Padding.Left = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph3.Style.Padding.Right = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph3.Style.Padding.Top = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            graphTitle2.Position = Telerik.Reporting.GraphItemPosition.TopCenter;
            graphTitle2.Style.Color = System.Drawing.Color.LightSlateGray;
            graphTitle2.Style.Font.Name = "Bell MT";
            graphTitle2.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(13D);
            graphTitle2.Style.LineColor = System.Drawing.Color.LightGray;
            graphTitle2.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            graphTitle2.Style.Visible = false;
            graphTitle2.Text = "Daily DataLag ";
            this.graph3.Titles.Add(graphTitle2);
            // 
            // cartesianCoordinateSystem36
            // 
            this.cartesianCoordinateSystem36.Name = "cartesianCoordinateSystem36";
            this.cartesianCoordinateSystem36.XAxis = this.graphAxis72;
            this.cartesianCoordinateSystem36.YAxis = this.graphAxis71;
            // 
            // graphAxis72
            // 
            this.graphAxis72.MajorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis72.MajorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis72.MajorGridLineStyle.Visible = false;
            this.graphAxis72.MajorTickMarkDisplayType = Telerik.Reporting.GraphAxisTickMarkDisplayType.None;
            this.graphAxis72.MinorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis72.MinorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis72.MinorGridLineStyle.Visible = false;
            this.graphAxis72.Name = "graphAxis72";
            this.graphAxis72.Scale = categoryScale2;
            this.graphAxis72.Style.Color = System.Drawing.Color.AliceBlue;
            this.graphAxis72.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(1D);
            // 
            // graphAxis71
            // 
            this.graphAxis71.MajorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis71.MajorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis71.MajorGridLineStyle.Visible = true;
            this.graphAxis71.MajorTickMarkDisplayType = Telerik.Reporting.GraphAxisTickMarkDisplayType.Cross;
            this.graphAxis71.MinorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis71.MinorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis71.MinorGridLineStyle.Visible = false;
            this.graphAxis71.Name = "graphAxis71";
            this.graphAxis71.Scale = numericalScale2;
            this.graphAxis71.Style.Font.Name = "Tahoma";
            this.graphAxis71.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(10D);
            this.graphAxis71.Title = "DataLag In Minutes";
            // 
            // sqlDataSource3
            // 
            this.sqlDataSource3.ConnectionString = CryptographyHelper.Md5Decrypt(conString);
            this.sqlDataSource3.ProviderName = CryptographyHelper.Md5Decrypt(dataProvider);
            this.sqlDataSource3.Name = "sqlDataSource3";
            this.sqlDataSource3.CommandTimeout = 5000;
            this.sqlDataSource3.Parameters.AddRange(new Telerik.Reporting.SqlDataSourceParameter[] {
                        new Telerik.Reporting.SqlDataSourceParameter(Utility.dbParaPrefix+"IINFRAOBJECTID", System.Data.DbType.Decimal, Convert.ToInt32(arr[0])),
            new Telerik.Reporting.SqlDataSourceParameter(Utility.dbParaPrefix+"IRECOVERYTYPE", System.Data.DbType.Decimal, Convert.ToInt32(arr[3]))});
            this.sqlDataSource3.SelectCommand = "DATALAG_GETASMINUTEFORMAT";
            this.sqlDataSource3.SelectCommandType = Telerik.Reporting.SqlDataSourceCommandType.StoredProcedure;
            // 
            // lineSeries3
            // 
            graphGroup6.Groupings.Add(new Telerik.Reporting.Grouping("=Fields.CreateDate"));
            graphGroup6.Name = "createDateGroup2";
            graphGroup6.Sortings.Add(new Telerik.Reporting.Sorting("=Fields.CreateDate", Telerik.Reporting.SortDirection.Asc));
            this.lineSeries3.CategoryGroup = graphGroup6;
            monochromaticPalette3.BaseColor = System.Drawing.Color.Red;
            this.lineSeries3.ColorPalette = monochromaticPalette3;
            this.lineSeries3.CoordinateSystem = this.cartesianCoordinateSystem36;
            this.lineSeries3.DataPointLabel = "=Sum(Fields.CURRENTDATALAG)";
            this.lineSeries3.DataPointLabelStyle.Visible = false;
            this.lineSeries3.DataPointStyle.Visible = false;
            this.lineSeries3.LegendItem.Style.BackgroundColor = System.Drawing.Color.Transparent;
            this.lineSeries3.LegendItem.Style.Font.Name = "Bell MT";
            this.lineSeries3.LegendItem.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.lineSeries3.LegendItem.Style.LineColor = System.Drawing.Color.Transparent;
            this.lineSeries3.LegendItem.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.lineSeries3.LegendItem.Value = "Deviation RPO";
            this.lineSeries3.LineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.lineSeries3.LineStyle.Visible = true;
            this.lineSeries3.LineType = Telerik.Reporting.LineSeries.LineTypes.Smooth;
            this.lineSeries3.MarkerMaxSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries3.MarkerMinSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries3.MarkerSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries3.Name = "lineSeries3";
            graphGroup7.Name = "seriesGroup2";
            this.lineSeries3.SeriesGroup = graphGroup7;
            this.lineSeries3.Size = null;
            this.lineSeries3.Y = "=Sum(Fields.CURRENTDATALAG)";
            // 
            // lineSeries4
            // 
            this.lineSeries4.CategoryGroup = graphGroup6;
            monochromaticPalette4.BaseColor = System.Drawing.Color.SteelBlue;
            this.lineSeries4.ColorPalette = monochromaticPalette4;
            this.lineSeries4.CoordinateSystem = this.cartesianCoordinateSystem36;
            this.lineSeries4.DataPointLabel = "=Sum(Fields.CURRENTRPO)";
            this.lineSeries4.DataPointLabelStyle.Visible = false;
            this.lineSeries4.DataPointStyle.Visible = false;
            this.lineSeries4.LegendItem.Style.BackgroundColor = System.Drawing.Color.Transparent;
            this.lineSeries4.LegendItem.Style.Font.Name = "Bell MT";
            this.lineSeries4.LegendItem.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.lineSeries4.LegendItem.Style.LineColor = System.Drawing.Color.Transparent;
            this.lineSeries4.LegendItem.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.lineSeries4.LegendItem.Value = "Configured RPO";
            this.lineSeries4.LineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.lineSeries4.LineStyle.Visible = true;
            this.lineSeries4.LineType = Telerik.Reporting.LineSeries.LineTypes.Smooth;
            this.lineSeries4.MarkerMaxSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries4.MarkerMinSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries4.MarkerSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries4.Name = "lineSeries4";
            this.lineSeries4.SeriesGroup = graphGroup7;
            this.lineSeries4.Size = null;
            this.lineSeries4.Y = "=Sum(Fields.CURRENTRPO)";
            // 
            // textBox3
            // 
            this.textBox3.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(2.7000000476837158D), Telerik.Reporting.Drawing.Unit.Inch(3.9418537198798731E-05D));
            this.textBox3.Name = "textBox3";
            this.textBox3.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.6999608278274536D), Telerik.Reporting.Drawing.Unit.Inch(0.34779691696166992D));
            this.textBox3.Style.Color = System.Drawing.Color.Teal;
            this.textBox3.Style.Font.Bold = false;
            this.textBox3.Style.Font.Name = "Tahoma";
            this.textBox3.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(16D);
            this.textBox3.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox3.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox3.Value = "Daily DataLag ";
            // 
            // textBox17
            // 
            this.textBox17.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.6436328887939453D), Telerik.Reporting.Drawing.Unit.Inch(1.1527729034423828D));
            this.textBox17.Name = "textBox17";
            this.textBox17.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.1925182342529297D), Telerik.Reporting.Drawing.Unit.Inch(0.23738861083984375D));
            this.textBox17.Value = "Deviation RPO";
            // 
            // textBox24
            // 
            this.textBox24.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.6436338424682617D), Telerik.Reporting.Drawing.Unit.Inch(0.91530543565750122D));
            this.textBox24.Name = "textBox24";
            this.textBox24.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.1925182342529297D), Telerik.Reporting.Drawing.Unit.Inch(0.23738861083984375D));
            this.textBox24.Value = "Configured RPO";
            // 
            // textBox25
            // 
            this.textBox25.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(7.3284139633178711D), Telerik.Reporting.Drawing.Unit.Inch(0.43000000715255737D));
            this.textBox25.Name = "textBox25";
            this.textBox25.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(3.7841305732727051D), Telerik.Reporting.Drawing.Unit.Inch(0.48522663116455078D));
            this.textBox25.Value = "This graph shows daily datalag in minutes over the past 30 days.";
            // 
            // shape8
            // 
            this.shape8.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.24355411529541D), Telerik.Reporting.Drawing.Unit.Inch(0.91530543565750122D));
            this.shape8.Name = "shape8";
            this.shape8.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape8.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.40000024437904358D), Telerik.Reporting.Drawing.Unit.Inch(0.18469524383544922D));
            this.shape8.Style.Color = System.Drawing.Color.SteelBlue;
            // 
            // shape11
            // 
            this.shape11.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.24355411529541D), Telerik.Reporting.Drawing.Unit.Inch(1.1527729034423828D));
            this.shape11.Name = "shape11";
            this.shape11.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape11.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.40000024437904358D), Telerik.Reporting.Drawing.Unit.Inch(0.18000000715255737D));
            this.shape11.Style.Color = System.Drawing.Color.Red;
            // 
            // panel2
            // 
            this.panel2.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.textBox2,
            this.textBox26,
            this.textBox27,
            this.textBox28,
            this.graph5});
            this.panel2.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.08124542236328125D), Telerik.Reporting.Drawing.Unit.Inch(5.539583683013916D));
            this.panel2.Name = "panel2";
            this.panel2.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.133003234863281D), Telerik.Reporting.Drawing.Unit.Inch(3.299921989440918D));
            this.panel2.Style.BorderColor.Default = System.Drawing.Color.LightGray;
            this.panel2.Style.BorderStyle.Default = Telerik.Reporting.Drawing.BorderType.Solid;
            // 
            // textBox2
            // 
            this.textBox2.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(2.2995271682739258D), Telerik.Reporting.Drawing.Unit.Inch(3.9418537198798731E-05D));
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(3.4209318161010742D), Telerik.Reporting.Drawing.Unit.Inch(0.28988185524940491D));
            this.textBox2.Style.Color = System.Drawing.Color.Teal;
            this.textBox2.Style.Font.Bold = false;
            this.textBox2.Style.Font.Name = "Tahoma";
            this.textBox2.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(16D);
            this.textBox2.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox2.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox2.Value = "Monthly DataLag Deviation Count";
            // 
            // textBox26
            // 
            this.textBox26.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(7.3204588890075684D), Telerik.Reporting.Drawing.Unit.Inch(0.37000000476837158D));
            this.textBox26.Name = "textBox26";
            this.textBox26.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(3.77158784866333D), Telerik.Reporting.Drawing.Unit.Inch(0.38522624969482422D));
            this.textBox26.Value = "This graph shows  number of datalag deviations per day  over the past 30 days.";
            // 
            // textBox27
            // 
            this.textBox27.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.37639331817627D), Telerik.Reporting.Drawing.Unit.Inch(0.80000048875808716D));
            this.textBox27.Name = "textBox27";
            this.textBox27.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(2.2004730701446533D), Telerik.Reporting.Drawing.Unit.Inch(0.23738861083984375D));
            this.textBox27.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox27.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox27.Value = "DataLag Deviation Count Per Day";
            // 
            // textBox28
            // 
            this.textBox28.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.21923542022705D), Telerik.Reporting.Drawing.Unit.Inch(0.86000019311904907D));
            this.textBox28.Name = "textBox28";
            this.textBox28.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.15000000596046448D), Telerik.Reporting.Drawing.Unit.Inch(0.15000000596046448D));
            this.textBox28.Style.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(160)))), ((int)(((byte)(160)))));
            this.textBox28.Style.Color = System.Drawing.Color.Black;
            this.textBox28.Style.Font.Bold = false;
            this.textBox28.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(5D);
            this.textBox28.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Right;
            this.textBox28.Value = " ";
            // 
            // graph5
            // 
            graphGroup9.Groupings.Add(new Telerik.Reporting.Grouping("=Fields.CreateDate"));
            graphGroup9.Name = "createDateGroup4";
            graphGroup9.Sortings.Add(new Telerik.Reporting.Sorting("=Fields.CreateDate", Telerik.Reporting.SortDirection.Asc));
            this.graph5.CategoryGroups.Add(graphGroup9);
            colorPalette2.Colors.Add(System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(160)))), ((int)(((byte)(160))))));
            this.graph5.ColorPalette = colorPalette2;
            this.graph5.CoordinateSystems.Add(this.cartesianCoordinateSystem1);
            this.graph5.DataSource = this.sqlDataSource7;
            this.graph5.Legend.Position = Telerik.Reporting.GraphItemPosition.BottomCenter;
            this.graph5.Legend.Style.LineColor = System.Drawing.Color.LightGray;
            this.graph5.Legend.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.graph5.Legend.Style.Visible = false;
            this.graph5.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(3.9418537198798731E-05D), Telerik.Reporting.Drawing.Unit.Inch(0.29000025987625122D));
            this.graph5.Name = "graph5";
            this.graph5.PlotAreaStyle.LineColor = System.Drawing.Color.LightGray;
            this.graph5.PlotAreaStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.graph5.Series.Add(this.barSeries1);
            graphGroup12.Name = "seriesGroup4";
            this.graph5.SeriesGroups.Add(graphGroup12);
            this.graph5.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(7.2203407287597656D), Telerik.Reporting.Drawing.Unit.Inch(3.0099217891693115D));
            this.graph5.Style.Padding.Bottom = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph5.Style.Padding.Left = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph5.Style.Padding.Right = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph5.Style.Padding.Top = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            graphTitle3.Position = Telerik.Reporting.GraphItemPosition.TopCenter;
            graphTitle3.Style.Color = System.Drawing.Color.LightSlateGray;
            graphTitle3.Style.Font.Name = "Bell MT";
            graphTitle3.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(13D);
            graphTitle3.Style.LineColor = System.Drawing.Color.LightGray;
            graphTitle3.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            graphTitle3.Style.Visible = false;
            graphTitle3.Text = "Monthly DataLag Deviation Count";
            this.graph5.Titles.Add(graphTitle3);
            // 
            // cartesianCoordinateSystem1
            // 
            this.cartesianCoordinateSystem1.Name = "cartesianCoordinateSystem38";
            this.cartesianCoordinateSystem1.XAxis = this.graphAxis1;
            this.cartesianCoordinateSystem1.YAxis = this.graphAxis2;
            // 
            // graphAxis1
            // 
            this.graphAxis1.MajorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis1.MajorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis1.MajorGridLineStyle.Visible = false;
            this.graphAxis1.MajorTickMarkDisplayType = Telerik.Reporting.GraphAxisTickMarkDisplayType.None;
            this.graphAxis1.MinorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis1.MinorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis1.MinorGridLineStyle.Visible = false;
            this.graphAxis1.Name = "graphAxis76";
            this.graphAxis1.Scale = categoryScale3;
            this.graphAxis1.Style.Color = System.Drawing.Color.Transparent;
            this.graphAxis1.Style.Font.Name = "Angsana New";
            this.graphAxis1.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(1D);
            // 
            // graphAxis2
            // 
            this.graphAxis2.MajorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis2.MajorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis2.MajorGridLineStyle.Visible = true;
            this.graphAxis2.MinorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis2.MinorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis2.MinorGridLineStyle.Visible = false;
            this.graphAxis2.Name = "graphAxis75";
            this.graphAxis2.Scale = numericalScale3;
            this.graphAxis2.Style.Font.Name = "Tahoma";
            this.graphAxis2.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(10D);
            this.graphAxis2.Title = "No. Of Deviations Per Day";
            // 
            // sqlDataSource7
            // 
            this.sqlDataSource7.ConnectionString = CryptographyHelper.Md5Decrypt(conString);
            this.sqlDataSource7.ProviderName = CryptographyHelper.Md5Decrypt(dataProvider);
            this.sqlDataSource7.Name = "sqlDataSource7";
            this.sqlDataSource7.CommandTimeout = 5000;
            this.sqlDataSource7.Parameters.AddRange(new Telerik.Reporting.SqlDataSourceParameter[] {
            new Telerik.Reporting.SqlDataSourceParameter(Utility.dbParaPrefix+"IINFRAOBJECTID", System.Data.DbType.Decimal, Convert.ToInt32(arr[0])),
            new Telerik.Reporting.SqlDataSourceParameter(Utility.dbParaPrefix+"IRECOVERYTYPE", System.Data.DbType.Decimal, Convert.ToInt32(arr[3]))});
            this.sqlDataSource7.SelectCommand = "DATALAG_MNTHLYDEVTIONCNT";
            this.sqlDataSource7.SelectCommandType = Telerik.Reporting.SqlDataSourceCommandType.StoredProcedure;
            // 
            // barSeries1
            // 
            graphGroup10.Groupings.Add(new Telerik.Reporting.Grouping("=Fields.CreateDate"));
            graphGroup10.Name = "createDateGroup4";
            graphGroup10.Sortings.Add(new Telerik.Reporting.Sorting("=Fields.CreateDate", Telerik.Reporting.SortDirection.Asc));
            this.barSeries1.CategoryGroup = graphGroup10;
            this.barSeries1.CoordinateSystem = this.cartesianCoordinateSystem1;
            this.barSeries1.DataPointLabel = "=Sum(Fields.DeviationCount)";
            this.barSeries1.DataPointLabelAlignment = Telerik.Reporting.BarDataPointLabelAlignment.Center;
            this.barSeries1.DataPointLabelStyle.Color = System.Drawing.Color.Black;
            this.barSeries1.DataPointLabelStyle.Font.Name = "Tahoma";
            this.barSeries1.DataPointLabelStyle.Font.Size = Telerik.Reporting.Drawing.Unit.Point(10D);
            this.barSeries1.DataPointLabelStyle.Visible = true;
            this.barSeries1.DataPointStyle.Font.Name = "Tahoma";
            this.barSeries1.DataPointStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.barSeries1.DataPointStyle.Visible = true;
            this.barSeries1.LegendItem.Style.BackgroundColor = System.Drawing.Color.Transparent;
            this.barSeries1.LegendItem.Style.LineColor = System.Drawing.Color.Transparent;
            this.barSeries1.LegendItem.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.barSeries1.LegendItem.Value = "DataLag Deviation Count Per Day";
            this.barSeries1.Name = "barSeries3";
            graphGroup11.Name = "seriesGroup4";
            this.barSeries1.SeriesGroup = graphGroup11;
            this.barSeries1.Y = "=Sum(Fields.DeviationCount)";
            // 
            // panel4
            // 
            this.panel4.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.textBox6,
            this.textBox32,
            this.textBox34,
            this.textBox35,
            this.graph4,
            this.shape12,
            this.shape13});
            this.panel4.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.09375D), Telerik.Reporting.Drawing.Unit.Inch(8.9395837783813477D));
            this.panel4.Name = "panel4";
            this.panel4.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.100001335144043D), Telerik.Reporting.Drawing.Unit.Inch(3.1999218463897705D));
            this.panel4.Style.BorderColor.Default = System.Drawing.Color.LightGray;
            this.panel4.Style.BorderStyle.Default = Telerik.Reporting.Drawing.BorderType.Solid;
            // 
            // textBox6
            // 
            this.textBox6.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(2.5874969959259033D), Telerik.Reporting.Drawing.Unit.Inch(3.9418537198798731E-05D));
            this.textBox6.Name = "textBox6";
            this.textBox6.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.7999212741851807D), Telerik.Reporting.Drawing.Unit.Inch(0.29996031522750854D));
            this.textBox6.Style.Color = System.Drawing.Color.Teal;
            this.textBox6.Style.Font.Bold = false;
            this.textBox6.Style.Font.Name = "Tahoma";
            this.textBox6.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(16D);
            this.textBox6.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox6.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox6.Value = "Monthly DataLag ";
            // 
            // textBox32
            // 
            this.textBox32.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.8000001907348633D), Telerik.Reporting.Drawing.Unit.Inch(1.0999208688735962D));
            this.textBox32.Name = "textBox32";
            this.textBox32.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.1925182342529297D), Telerik.Reporting.Drawing.Unit.Inch(0.23738861083984375D));
            this.textBox32.Value = "Deviation RPO";
            // 
            // textBox34
            // 
            this.textBox34.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.8000001907348633D), Telerik.Reporting.Drawing.Unit.Inch(0.86033755540847778D));
            this.textBox34.Name = "textBox34";
            this.textBox34.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.1925182342529297D), Telerik.Reporting.Drawing.Unit.Inch(0.23738861083984375D));
            this.textBox34.Value = "Configured RPO";
            // 
            // textBox35
            // 
            this.textBox35.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(7.3079161643981934D), Telerik.Reporting.Drawing.Unit.Inch(0.37503179907798767D));
            this.textBox35.Name = "textBox35";
            this.textBox35.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(3.77158784866333D), Telerik.Reporting.Drawing.Unit.Inch(0.48522663116455078D));
            this.textBox35.Value = "This graph shows daily datalag in minutes over the past 30 days.";
            // 
            // graph4
            // 
            graphGroup13.Groupings.Add(new Telerik.Reporting.Grouping("=Fields.CreateDate"));
            graphGroup13.Name = "createDateGroup3";
            graphGroup13.Sortings.Add(new Telerik.Reporting.Sorting("=Fields.CreateDate", Telerik.Reporting.SortDirection.Asc));
            this.graph4.CategoryGroups.Add(graphGroup13);
            colorPalette3.Colors.Add(System.Drawing.Color.Green);
            colorPalette3.Colors.Add(System.Drawing.Color.Red);
            this.graph4.ColorPalette = colorPalette3;
            this.graph4.CoordinateSystems.Add(this.cartesianCoordinateSystem2);
            this.graph4.DataSource = this.sqlDataSource8;
            this.graph4.Legend.Position = Telerik.Reporting.GraphItemPosition.BottomCenter;
            this.graph4.Legend.Style.LineColor = System.Drawing.Color.LightGray;
            this.graph4.Legend.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.graph4.Legend.Style.Visible = false;
            this.graph4.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0D), Telerik.Reporting.Drawing.Unit.Inch(0.30007806420326233D));
            this.graph4.Name = "graph4";
            this.graph4.PlotAreaStyle.LineColor = System.Drawing.Color.LightGray;
            this.graph4.PlotAreaStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.graph4.Series.Add(this.lineSeries7);
            this.graph4.Series.Add(this.lineSeries8);
            graphGroup16.Name = "seriesGroup3";
            this.graph4.SeriesGroups.Add(graphGroup16);
            this.graph4.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(7.2078375816345215D), Telerik.Reporting.Drawing.Unit.Inch(2.7999215126037598D));
            this.graph4.Style.Padding.Bottom = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph4.Style.Padding.Left = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph4.Style.Padding.Right = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.graph4.Style.Padding.Top = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            graphTitle4.Position = Telerik.Reporting.GraphItemPosition.TopCenter;
            graphTitle4.Style.Color = System.Drawing.Color.LightSlateGray;
            graphTitle4.Style.Font.Name = "Bell MT";
            graphTitle4.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(13D);
            graphTitle4.Style.LineColor = System.Drawing.Color.LightGray;
            graphTitle4.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            graphTitle4.Style.Visible = false;
            graphTitle4.Text = "Monthly DataLag";
            this.graph4.Titles.Add(graphTitle4);
            // 
            // cartesianCoordinateSystem2
            // 
            this.cartesianCoordinateSystem2.Name = "cartesianCoordinateSystem37";
            this.cartesianCoordinateSystem2.XAxis = this.graphAxis3;
            this.cartesianCoordinateSystem2.YAxis = this.graphAxis4;
            // 
            // graphAxis3
            // 
            this.graphAxis3.LabelPlacement = Telerik.Reporting.GraphAxisLabelPlacement.None;
            this.graphAxis3.MajorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis3.MajorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis3.MajorGridLineStyle.Visible = false;
            this.graphAxis3.MajorTickMarkDisplayType = Telerik.Reporting.GraphAxisTickMarkDisplayType.None;
            this.graphAxis3.MinorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis3.MinorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis3.MinorGridLineStyle.Visible = false;
            this.graphAxis3.Name = "graphAxis74";
            this.graphAxis3.Scale = categoryScale4;
            this.graphAxis3.Style.Color = System.Drawing.Color.Transparent;
            this.graphAxis3.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(1D);
            // 
            // graphAxis4
            // 
            this.graphAxis4.MajorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis4.MajorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis4.MajorGridLineStyle.Visible = true;
            this.graphAxis4.MinorGridLineStyle.LineColor = System.Drawing.Color.LightGray;
            this.graphAxis4.MinorGridLineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.graphAxis4.MinorGridLineStyle.Visible = false;
            this.graphAxis4.MultiLevelCategoryLabels = true;
            this.graphAxis4.Name = "graphAxis73";
            this.graphAxis4.Scale = numericalScale4;
            this.graphAxis4.Style.Font.Name = "Tahoma";
            this.graphAxis4.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(10D);
            this.graphAxis4.Title = "DataLag In Minutes";
            // 
            // sqlDataSource8
            // 
            this.sqlDataSource8.ConnectionString = CryptographyHelper.Md5Decrypt(conString);
            this.sqlDataSource8.ProviderName = CryptographyHelper.Md5Decrypt(dataProvider);
            this.sqlDataSource8.Name = "sqlDataSource8";
            this.sqlDataSource8.CommandTimeout = 5000;
            this.sqlDataSource8.Parameters.AddRange(new Telerik.Reporting.SqlDataSourceParameter[] {
            new Telerik.Reporting.SqlDataSourceParameter(Utility.dbParaPrefix+"IINFRAOBJECTID", System.Data.DbType.Decimal, Convert.ToInt32(arr[0])),
            new Telerik.Reporting.SqlDataSourceParameter(Utility.dbParaPrefix+"IRECOVERYTYPE", System.Data.DbType.Decimal, Convert.ToInt32(arr[3]))});
            this.sqlDataSource8.SelectCommand = "DATALAG_MONTHLYMIUTEFORMAT";
            this.sqlDataSource8.SelectCommandType = Telerik.Reporting.SqlDataSourceCommandType.StoredProcedure;
            // 
            // lineSeries7
            // 
            graphGroup14.Groupings.Add(new Telerik.Reporting.Grouping("=Fields.CreateDate"));
            graphGroup14.Name = "createDateGroup3";
            graphGroup14.Sortings.Add(new Telerik.Reporting.Sorting("=Fields.CreateDate", Telerik.Reporting.SortDirection.Asc));
            this.lineSeries7.CategoryGroup = graphGroup14;
            monochromaticPalette5.BaseColor = System.Drawing.Color.Red;
            this.lineSeries7.ColorPalette = monochromaticPalette5;
            this.lineSeries7.CoordinateSystem = this.cartesianCoordinateSystem2;
            this.lineSeries7.DataPointLabel = "=Sum(Fields.CURRENTDATALAG)";
            this.lineSeries7.DataPointLabelStyle.Visible = false;
            this.lineSeries7.DataPointStyle.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Top;
            this.lineSeries7.DataPointStyle.Visible = false;
            this.lineSeries7.LegendItem.Style.BackgroundColor = System.Drawing.Color.Transparent;
            this.lineSeries7.LegendItem.Style.Font.Name = "Bell MT";
            this.lineSeries7.LegendItem.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.lineSeries7.LegendItem.Style.LineColor = System.Drawing.Color.Transparent;
            this.lineSeries7.LegendItem.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.lineSeries7.LegendItem.Value = "Deviation RPO";
            this.lineSeries7.LineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.lineSeries7.LineStyle.Visible = true;
            this.lineSeries7.LineType = Telerik.Reporting.LineSeries.LineTypes.Smooth;
            this.lineSeries7.MarkerMaxSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries7.MarkerMinSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries7.MarkerSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries7.Name = "lineSeries5";
            graphGroup15.Name = "seriesGroup3";
            this.lineSeries7.SeriesGroup = graphGroup15;
            this.lineSeries7.Size = null;
            this.lineSeries7.Y = "=Sum(Fields.CURRENTDATALAG)";
            // 
            // lineSeries8
            // 
            this.lineSeries8.CategoryGroup = graphGroup14;
            monochromaticPalette6.BaseColor = System.Drawing.Color.SteelBlue;
            this.lineSeries8.ColorPalette = monochromaticPalette6;
            this.lineSeries8.CoordinateSystem = this.cartesianCoordinateSystem2;
            this.lineSeries8.DataPointLabel = "=Sum(Fields.CURRENTRPO)";
            this.lineSeries8.DataPointLabelStyle.Visible = false;
            this.lineSeries8.DataPointStyle.Visible = false;
            this.lineSeries8.LegendItem.Style.BackgroundColor = System.Drawing.Color.Transparent;
            this.lineSeries8.LegendItem.Style.Font.Name = "Bell MT";
            this.lineSeries8.LegendItem.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.lineSeries8.LegendItem.Style.LineColor = System.Drawing.Color.Transparent;
            this.lineSeries8.LegendItem.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Inch(0D);
            this.lineSeries8.LegendItem.Value = "Configured RPO";
            this.lineSeries8.LineStyle.LineWidth = Telerik.Reporting.Drawing.Unit.Pixel(1D);
            this.lineSeries8.LineStyle.Visible = true;
            this.lineSeries8.LineType = Telerik.Reporting.LineSeries.LineTypes.Smooth;
            this.lineSeries8.MarkerMaxSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries8.MarkerMinSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries8.MarkerSize = Telerik.Reporting.Drawing.Unit.Pixel(0D);
            this.lineSeries8.Name = "lineSeries6";
            this.lineSeries8.SeriesGroup = graphGroup15;
            this.lineSeries8.Size = null;
            this.lineSeries8.Y = "=Sum(Fields.CURRENTRPO)";
            // 
            // shape12
            // 
            this.shape12.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.3999214172363281D), Telerik.Reporting.Drawing.Unit.Inch(0.86033755540847778D));
            this.shape12.Name = "shape12";
            this.shape12.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape12.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.40000024437904358D), Telerik.Reporting.Drawing.Unit.Inch(0.18000000715255737D));
            this.shape12.Style.Color = System.Drawing.Color.SteelBlue;
            // 
            // shape13
            // 
            this.shape13.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.3999214172363281D), Telerik.Reporting.Drawing.Unit.Inch(1.0999208688735962D));
            this.shape13.Name = "shape13";
            this.shape13.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape13.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.40000000596046448D), Telerik.Reporting.Drawing.Unit.Inch(0.18000000715255737D));
            this.shape13.Style.Color = System.Drawing.Color.Red;
            // 
            // pageFooterSection1
            // 
            this.pageFooterSection1.Height = Telerik.Reporting.Drawing.Unit.Inch(0.32220268249511719D);
            this.pageFooterSection1.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.pictureBox6,
            this.textBox5,
            this.textBox61,
            this.shape10});
            this.pageFooterSection1.Name = "pageFooterSection1";
            // 
            // pictureBox6
            // 
            this.pictureBox6.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.023750066757202148D), Telerik.Reporting.Drawing.Unit.Inch(0.052201587706804276D));
            this.pictureBox6.MimeType = "image/png";
            this.pictureBox6.Name = "pictureBox6";
            this.pictureBox6.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.27000001072883606D), Telerik.Reporting.Drawing.Unit.Inch(0.27000001072883606D));
            this.pictureBox6.Value = ((object)(resources.GetObject("pictureBox6.Value")));
            // 
            // textBox5
            // 
            this.textBox5.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.2938288152217865D), Telerik.Reporting.Drawing.Unit.Inch(0.052201587706804276D));
            this.textBox5.Name = "textBox5";
            this.textBox5.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(4.2214312553405762D), Telerik.Reporting.Drawing.Unit.Inch(0.27000012993812561D));
            this.textBox5.Style.Color = System.Drawing.Color.Teal;
            this.textBox5.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(8D);
            this.textBox5.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Left;
            this.textBox5.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox5.Value = "Please consider the environment before printing this report";
            // 
            // textBox61
            // 
            this.textBox61.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.3303756713867188D), Telerik.Reporting.Drawing.Unit.Inch(0.052242279052734375D));
            this.textBox61.Name = "textBox61";
            this.textBox61.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(2.9625387191772461D), Telerik.Reporting.Drawing.Unit.Inch(0.26996040344238281D));
            this.textBox61.Style.Color = System.Drawing.Color.Teal;
            this.textBox61.Style.Font.Bold = false;
            this.textBox61.Style.Font.Name = "Tahoma";
            this.textBox61.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(8D);
            this.textBox61.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Right;
            this.textBox61.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox61.StyleName = "";
            this.textBox61.Value = "Page {PageNumber} of {PageCount}";
            // 
            // shape10
            // 
            this.shape10.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0D), Telerik.Reporting.Drawing.Unit.Inch(3.9418537198798731E-05D));
            this.shape10.Name = "shape10";
            this.shape10.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape10.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.289999961853027D), Telerik.Reporting.Drawing.Unit.Inch(0.0520833395421505D));
            this.shape10.Style.Color = System.Drawing.Color.Teal;
            this.shape10.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Point(2D);
            // 
            // reportHeaderSection1
            // 
            this.reportHeaderSection1.Height = Telerik.Reporting.Drawing.Unit.Inch(1.5482150316238403D);
            this.reportHeaderSection1.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.panel6,
            this.shape1,
            this.shape5,
            this.textBox1,
            this.shape7,
            this.textBox49,
            this.panel5,
            this.shape9});
            this.reportHeaderSection1.Name = "reportHeaderSection1";
            // 
            // panel6
            // 
            this.panel6.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.textBox9,
            this.textBox10,
            this.textBox11,
            this.textBox12,
            this.textBox14,
            this.textBox16,
            this.pictureBox4,
            this.pictureBox3,
            this.pictureBox7,
            this.pictureBox8,
            this.shape2,
            this.textBox13,
            this.textBox15});
            this.panel6.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.0729166641831398D), Telerik.Reporting.Drawing.Unit.Inch(0.78980320692062378D));
            this.panel6.Name = "panel6";
            this.panel6.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.153297424316406D), Telerik.Reporting.Drawing.Unit.Inch(0.55833286046981812D));
            this.panel6.Style.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(247)))), ((int)(((byte)(247)))));
            // 
            // textBox9
            // 
            this.textBox9.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.19061572849750519D), Telerik.Reporting.Drawing.Unit.Inch(3.9498012483818457E-05D));
            this.textBox9.Name = "textBox9";
            this.textBox9.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.4298032522201538D), Telerik.Reporting.Drawing.Unit.Inch(0.25841155648231506D));
            this.textBox9.Style.BorderColor.Bottom = System.Drawing.Color.LightGray;
            this.textBox9.Style.BorderStyle.Bottom = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox9.Style.BorderWidth.Bottom = Telerik.Reporting.Drawing.Unit.Point(0.800000011920929D);
            this.textBox9.Style.Color = System.Drawing.Color.Black;
            this.textBox9.Style.Font.Bold = false;
            this.textBox9.Style.Font.Name = "Tahoma";
            this.textBox9.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(12D);
            this.textBox9.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Left;
            this.textBox9.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox9.Value = "Business Service Name";
            // 
            // textBox10
            // 
            this.textBox10.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(1.6204977035522461D), Telerik.Reporting.Drawing.Unit.Inch(3.9418537198798731E-05D));
            this.textBox10.Name = "textBox10";
            this.textBox10.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(3.099921703338623D), Telerik.Reporting.Drawing.Unit.Inch(0.25841155648231506D));
            this.textBox10.Style.BorderColor.Bottom = System.Drawing.Color.LightGray;
            this.textBox10.Style.BorderStyle.Bottom = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox10.Style.BorderWidth.Bottom = Telerik.Reporting.Drawing.Unit.Point(0.800000011920929D);
            this.textBox10.Style.Color = System.Drawing.Color.Black;
            this.textBox10.Style.Font.Bold = false;
            this.textBox10.Style.Font.Name = "Tahoma";
            this.textBox10.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(12D);
            this.textBox10.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Right;
            this.textBox10.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox10.Value = "= Parameters.iBSName.Value";
            // 
            // textBox11
            // 
            this.textBox11.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(1.6204977035522461D), Telerik.Reporting.Drawing.Unit.Inch(0.25852999091148376D));
            this.textBox11.Name = "textBox11";
            this.textBox11.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(3.1065077781677246D), Telerik.Reporting.Drawing.Unit.Inch(0.29976350069046021D));
            this.textBox11.Style.BorderColor.Bottom = System.Drawing.Color.LightGray;
            this.textBox11.Style.BorderStyle.Bottom = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox11.Style.BorderWidth.Bottom = Telerik.Reporting.Drawing.Unit.Point(0.800000011920929D);
            this.textBox11.Style.Color = System.Drawing.Color.Black;
            this.textBox11.Style.Font.Bold = false;
            this.textBox11.Style.Font.Name = "Tahoma";
            this.textBox11.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(12D);
            this.textBox11.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Right;
            this.textBox11.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox11.Value = "= Parameters.iInfraName.Value";
            // 
            // textBox12
            // 
            this.textBox12.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.19061574339866638D), Telerik.Reporting.Drawing.Unit.Inch(0.25852999091148376D));
            this.textBox12.Name = "textBox12";
            this.textBox12.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.4298030138015747D), Telerik.Reporting.Drawing.Unit.Inch(0.29976350069046021D));
            this.textBox12.Style.BorderColor.Bottom = System.Drawing.Color.LightGray;
            this.textBox12.Style.BorderStyle.Bottom = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox12.Style.BorderWidth.Bottom = Telerik.Reporting.Drawing.Unit.Point(0.800000011920929D);
            this.textBox12.Style.Color = System.Drawing.Color.Black;
            this.textBox12.Style.Font.Bold = false;
            this.textBox12.Style.Font.Name = "Tahoma";
            this.textBox12.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(12D);
            this.textBox12.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Left;
            this.textBox12.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox12.Value = "InfraObject Name";
            // 
            // textBox14
            // 
            this.textBox14.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.2111911773681641D), Telerik.Reporting.Drawing.Unit.Inch(0.29992151260375977D));
            this.textBox14.Name = "textBox14";
            this.textBox14.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(2.9013528823852539D), Telerik.Reporting.Drawing.Unit.Inch(0.25841155648231506D));
            this.textBox14.Style.BorderColor.Bottom = System.Drawing.Color.LightGray;
            this.textBox14.Style.BorderStyle.Bottom = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox14.Style.BorderWidth.Bottom = Telerik.Reporting.Drawing.Unit.Point(0.800000011920929D);
            this.textBox14.Style.Color = System.Drawing.Color.Black;
            this.textBox14.Style.Font.Bold = false;
            this.textBox14.Style.Font.Name = "Tahoma";
            this.textBox14.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(12D);
            this.textBox14.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Right;
            this.textBox14.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox14.Value = "= Parameters.EndDate.Value";
            // 
            // textBox16
            // 
            this.textBox16.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(6.4204978942871094D), Telerik.Reporting.Drawing.Unit.Inch(3.9577484130859375E-05D));
            this.textBox16.Name = "textBox16";
            this.textBox16.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.7811920642852783D), Telerik.Reporting.Drawing.Unit.Inch(0.29976350069046021D));
            this.textBox16.Style.BackgroundImage.Repeat = Telerik.Reporting.Drawing.BackgroundRepeat.NoRepeat;
            this.textBox16.Style.BorderColor.Bottom = System.Drawing.Color.LightGray;
            this.textBox16.Style.BorderStyle.Bottom = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox16.Style.BorderWidth.Bottom = Telerik.Reporting.Drawing.Unit.Point(0.800000011920929D);
            this.textBox16.Style.Color = System.Drawing.Color.Black;
            this.textBox16.Style.Font.Bold = false;
            this.textBox16.Style.Font.Name = "Tahoma";
            this.textBox16.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(12D);
            this.textBox16.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Left;
            this.textBox16.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox16.Value = "Report Start Date";
            // 
            // pictureBox4
            // 
            this.pictureBox4.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(6.2204189300537109D), Telerik.Reporting.Drawing.Unit.Inch(0.10081768035888672D));
            this.pictureBox4.MimeType = "image/png";
            this.pictureBox4.Name = "pictureBox4";
            this.pictureBox4.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.20000000298023224D), Telerik.Reporting.Drawing.Unit.Inch(0.19902488589286804D));
            this.pictureBox4.Value = ((object)(resources.GetObject("pictureBox4.Value")));
            // 
            // pictureBox3
            // 
            this.pictureBox3.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(6.2204189300537109D), Telerik.Reporting.Drawing.Unit.Inch(0.3592686653137207D));
            this.pictureBox3.MimeType = "image/png";
            this.pictureBox3.Name = "pictureBox3";
            this.pictureBox3.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.20000000298023224D), Telerik.Reporting.Drawing.Unit.Inch(0.19902488589286804D));
            this.pictureBox3.Value = ((object)(resources.GetObject("pictureBox3.Value")));
            // 
            // pictureBox7
            // 
            this.pictureBox7.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0D), Telerik.Reporting.Drawing.Unit.Inch(0.059426307678222656D));
            this.pictureBox7.MimeType = "image/png";
            this.pictureBox7.Name = "pictureBox7";
            this.pictureBox7.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.19053702056407929D), Telerik.Reporting.Drawing.Unit.Inch(0.19902488589286804D));
            this.pictureBox7.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.pictureBox7.Value = ((object)(resources.GetObject("pictureBox7.Value")));
            // 
            // pictureBox8
            // 
            this.pictureBox8.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0D), Telerik.Reporting.Drawing.Unit.Inch(0.29992151260375977D));
            this.pictureBox8.MimeType = "image/png";
            this.pictureBox8.Name = "pictureBox8";
            this.pictureBox8.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.19053702056407929D), Telerik.Reporting.Drawing.Unit.Inch(0.19053448736667633D));
            this.pictureBox8.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.pictureBox8.Value = ((object)(resources.GetObject("pictureBox8.Value")));
            // 
            // shape2
            // 
            this.shape2.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(5.5104165077209473D), Telerik.Reporting.Drawing.Unit.Inch(0D));
            this.shape2.Name = "shape2";
            this.shape2.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.NS);
            this.shape2.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.10000000149011612D), Telerik.Reporting.Drawing.Unit.Inch(0.4699999988079071D));
            this.shape2.Style.Color = System.Drawing.Color.SlateGray;
            this.shape2.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Point(1D);
            // 
            // textBox13
            // 
            this.textBox13.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(6.4204978942871094D), Telerik.Reporting.Drawing.Unit.Inch(0.29992151260375977D));
            this.textBox13.Name = "textBox13";
            this.textBox13.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.7811921834945679D), Telerik.Reporting.Drawing.Unit.Inch(0.25841155648231506D));
            this.textBox13.Style.BorderColor.Bottom = System.Drawing.Color.LightGray;
            this.textBox13.Style.BorderStyle.Bottom = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox13.Style.BorderWidth.Bottom = Telerik.Reporting.Drawing.Unit.Point(0.800000011920929D);
            this.textBox13.Style.Color = System.Drawing.Color.Black;
            this.textBox13.Style.Font.Bold = false;
            this.textBox13.Style.Font.Name = "Tahoma";
            this.textBox13.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(12D);
            this.textBox13.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Left;
            this.textBox13.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox13.Value = "Report End Date";
            // 
            // textBox15
            // 
            this.textBox15.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(8.2111911773681641D), Telerik.Reporting.Drawing.Unit.Inch(0D));
            this.textBox15.Name = "textBox15";
            this.textBox15.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(2.9013521671295166D), Telerik.Reporting.Drawing.Unit.Inch(0.29984250664711D));
            this.textBox15.Style.BorderColor.Bottom = System.Drawing.Color.LightGray;
            this.textBox15.Style.BorderStyle.Bottom = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox15.Style.BorderWidth.Bottom = Telerik.Reporting.Drawing.Unit.Point(0.800000011920929D);
            this.textBox15.Style.Color = System.Drawing.Color.Black;
            this.textBox15.Style.Font.Bold = false;
            this.textBox15.Style.Font.Name = "Tahoma";
            this.textBox15.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(12D);
            this.textBox15.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Right;
            this.textBox15.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox15.Value = "= Parameters.StartDate.Value";
            // 
            // shape1
            // 
            this.shape1.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0D), Telerik.Reporting.Drawing.Unit.Inch(1.4043865203857422D));
            this.shape1.Name = "shape1";
            this.shape1.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape1.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(4.79992151260376D), Telerik.Reporting.Drawing.Unit.Inch(0.0520833320915699D));
            this.shape1.Style.Color = System.Drawing.Color.SlateGray;
            this.shape1.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Point(2D);
            // 
            // shape5
            // 
            this.shape5.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(6.300079345703125D), Telerik.Reporting.Drawing.Unit.Inch(1.4043865203857422D));
            this.shape5.Name = "shape5";
            this.shape5.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape5.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(4.9751396179199219D), Telerik.Reporting.Drawing.Unit.Inch(0.0520833320915699D));
            this.shape5.Style.Color = System.Drawing.Color.SlateGray;
            this.shape5.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Point(2D);
            // 
            // textBox1
            // 
            this.textBox1.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(4.8000006675720215D), Telerik.Reporting.Drawing.Unit.Inch(1.3482149839401245D));
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.5000003576278687D), Telerik.Reporting.Drawing.Unit.Inch(0.2000001072883606D));
            this.textBox1.Style.BorderColor.Default = System.Drawing.Color.LightGray;
            this.textBox1.Style.BorderStyle.Default = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox1.Style.Color = System.Drawing.Color.Black;
            this.textBox1.Style.Font.Bold = false;
            this.textBox1.Style.Font.Name = "Tahoma";
            this.textBox1.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.textBox1.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox1.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox1.Value = "DATALAG CHARTS";
            // 
            // shape7
            // 
            this.shape7.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(3.9378803194267675E-05D), Telerik.Reporting.Drawing.Unit.Inch(0.6357153058052063D));
            this.shape7.Name = "shape7";
            this.shape7.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape7.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(4.7915482521057129D), Telerik.Reporting.Drawing.Unit.Inch(0.0520833320915699D));
            this.shape7.Style.Color = System.Drawing.Color.SlateGray;
            this.shape7.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Point(2D);
            // 
            // textBox49
            // 
            this.textBox49.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(4.8000006675720215D), Telerik.Reporting.Drawing.Unit.Inch(0.575023353099823D));
            this.textBox49.Name = "textBox49";
            this.textBox49.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1.5000003576278687D), Telerik.Reporting.Drawing.Unit.Inch(0.21470099687576294D));
            this.textBox49.Style.BorderColor.Default = System.Drawing.Color.LightGray;
            this.textBox49.Style.BorderStyle.Default = Telerik.Reporting.Drawing.BorderType.None;
            this.textBox49.Style.Color = System.Drawing.Color.Black;
            this.textBox49.Style.Font.Bold = false;
            this.textBox49.Style.Font.Name = "Tahoma";
            this.textBox49.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Point(12D);
            this.textBox49.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox49.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Middle;
            this.textBox49.Value = "REPORT DETAILS";
            // 
            // panel5
            // 
            this.panel5.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.textBox37,
            this.pictureBox5,
            this.textBox7});
            this.panel5.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.09375D), Telerik.Reporting.Drawing.Unit.Inch(0.037798721343278885D));
            this.panel5.Name = "panel5";
            this.panel5.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.132464408874512D), Telerik.Reporting.Drawing.Unit.Inch(0.53714579343795776D));
            this.panel5.Style.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(160)))), ((int)(((byte)(160)))));
            // 
            // textBox37
            // 
            this.textBox37.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.20007872581481934D), Telerik.Reporting.Drawing.Unit.Inch(3.9339065551757812E-05D));
            this.textBox37.Name = "textBox37";
            this.textBox37.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(10.806173324584961D), Telerik.Reporting.Drawing.Unit.Inch(0.28999999165534973D));
            this.textBox37.Style.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(160)))), ((int)(((byte)(160)))));
            this.textBox37.Style.Color = System.Drawing.Color.White;
            this.textBox37.Style.Font.Bold = false;
            this.textBox37.Style.Font.Name = "Tahoma";
            this.textBox37.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(22D);
            this.textBox37.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox37.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Bottom;
            this.textBox37.Value = "RPO SLA REPORT";
            // 
            // textBox7
            // 
            this.textBox7.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.27333101630210876D), Telerik.Reporting.Drawing.Unit.Inch(0.29011806845664978D));
            this.textBox7.Name = "textBox7";
            this.textBox7.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(10.806172370910645D), Telerik.Reporting.Drawing.Unit.Inch(0.19988226890563965D));
            this.textBox7.Style.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(160)))), ((int)(((byte)(160)))));
            this.textBox7.Style.Color = System.Drawing.Color.White;
            this.textBox7.Style.Font.Bold = false;
            this.textBox7.Style.Font.Italic = false;
            this.textBox7.Style.Font.Name = "Tahoma";
            this.textBox7.Style.Font.Size = Telerik.Reporting.Drawing.Unit.Pixel(11D);
            this.textBox7.Style.TextAlign = Telerik.Reporting.Drawing.HorizontalAlign.Center;
            this.textBox7.Style.VerticalAlign = Telerik.Reporting.Drawing.VerticalAlign.Top;
            this.textBox7.Value = "created by {Parameters.iCreatedBy.Value} on {Now()}";
            // 
            // pictureBox5
            // 
            this.pictureBox5.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0.019999999552965164D), Telerik.Reporting.Drawing.Unit.Inch(1.862645149230957E-08D));
            this.pictureBox5.MimeType = "image/png";
            this.pictureBox5.Name = "pictureBox5";
            this.pictureBox5.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(0.18000005185604096D), Telerik.Reporting.Drawing.Unit.Inch(0.19555774331092835D));
            this.pictureBox5.Value = ((object)(resources.GetObject("pictureBox5.Value")));
            // 
            // shape9
            // 
            this.shape9.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(6.300079345703125D), Telerik.Reporting.Drawing.Unit.Inch(0.6357153058052063D));
            this.shape9.Name = "shape9";
            this.shape9.ShapeType = new Telerik.Reporting.Drawing.Shapes.LineShape(Telerik.Reporting.Drawing.Shapes.LineDirection.EW);
            this.shape9.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(4.9667272567749023D), Telerik.Reporting.Drawing.Unit.Inch(0.0520833320915699D));
            this.shape9.Style.Color = System.Drawing.Color.SlateGray;
            this.shape9.Style.LineWidth = Telerik.Reporting.Drawing.Unit.Point(2D);
            // 
            // sqlDataSource2
            // 
            this.sqlDataSource2.Name = "sqlDataSource2";
            // 
            // reportFooterSection1
            // 
            this.reportFooterSection1.Height = Telerik.Reporting.Drawing.Unit.Inch(0.40000024437904358D);
            this.reportFooterSection1.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.subReport1});
            this.reportFooterSection1.Name = "reportFooterSection1";
            // 
            // subReport1
            // 
            navigateToReportAction1.ReportSource = null;
            this.subReport1.Action = navigateToReportAction1;
            this.subReport1.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(0D), Telerik.Reporting.Drawing.Unit.Inch(0D));
            this.subReport1.Name = "subReport1";
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("iCreatedBy", "=Parameters.iCreatedBy.Value"));
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("iInfraName", "=Parameters.iInfraName.Value"));
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("StartDate", "=Parameters.StartDate.Value"));
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("EndDate", "=Parameters.EndDate.Value"));
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("iConfigDlag", "=Parameters.iConfigDlag.Value"));
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("iBSName", "=Parameters.iBSName.Value"));

            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("PRServerIP", "=Parameters.PRServerIP.Value"));
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("DRServerIP", "=Parameters.DRServerIP.Value"));
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("PRDBName", "=Parameters.PRDBName.Value"));
            typeReportSource1.Parameters.Add(new Telerik.Reporting.Parameter("DRDBName", "=Parameters.DRDBName.Value"));

            //instanceReportSource1.ReportDocument = this.datalagSubReport;
            typeReportSource1.TypeName = "CP.UI.Report.TelerikReports.SVCGlobalMirrorDatalagReport, CP.UI, Version=*******, Culture = neutral, PublicKeyToken=null";
            this.subReport1.ReportSource = typeReportSource1;
            this.subReport1.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(11.266806602478027D), Telerik.Reporting.Drawing.Unit.Inch(0.40000024437904358D));
            // 
            // pictureBox17
            // 
            this.pictureBox17.Location = new Telerik.Reporting.Drawing.PointU(Telerik.Reporting.Drawing.Unit.Inch(5.2000002861022949D), Telerik.Reporting.Drawing.Unit.Inch(3.9339065551757812E-05D));
            this.pictureBox17.Name = "pictureBox17";
            this.pictureBox17.Size = new Telerik.Reporting.Drawing.SizeU(Telerik.Reporting.Drawing.Unit.Inch(1D), Telerik.Reporting.Drawing.Unit.Inch(0.36000010371208191D));
            this.pictureBox17.Sizing = Telerik.Reporting.Drawing.ImageSizeMode.Stretch;
            string strlogo = rpt.GetLogo();
            if (strlogo != "" && System.IO.File.Exists(strlogo))
            {
                Image image1 = Image.FromFile(strlogo);
                this.pictureBox17.Value = image1;
            }
            else
                this.pictureBox17.Value = ((object)(resources.GetObject("pictureBox17.Value")));
            // 
            // SVCGlobalMirrorChartReport
            // 
            this.Items.AddRange(new Telerik.Reporting.ReportItemBase[] {
            this.pageHeaderSection1,
            this.detail,
            this.pageFooterSection1,
            this.reportHeaderSection1,
            this.reportFooterSection1});
            this.Name = "RPOSLAChartReport";
            this.PageSettings.Landscape = true;
            this.PageSettings.Margins = new Telerik.Reporting.Drawing.MarginsU(Telerik.Reporting.Drawing.Unit.Inch(0.20000000298023224D), Telerik.Reporting.Drawing.Unit.Inch(0.20000000298023224D), Telerik.Reporting.Drawing.Unit.Inch(0.20000000298023224D), Telerik.Reporting.Drawing.Unit.Inch(0.20000000298023224D));
            this.PageSettings.PaperKind = System.Drawing.Printing.PaperKind.A4;

            reportParameter2.AllowNull = true;
            reportParameter2.Name = "iInfraName";
            reportParameter2.Text = "iInfraName";
            reportParameter2.Visible = true;
            reportParameter3.AllowNull = true;
            reportParameter3.Name = "StartDate";
            reportParameter3.Text = "StartDate";
            reportParameter3.Visible = true;
            reportParameter4.AllowNull = true;
            reportParameter4.Name = "EndDate";
            reportParameter4.Text = "EndDate";
            reportParameter4.Visible = true;
            reportParameter5.AllowNull = true;
            reportParameter5.Name = "iCreatedBy";
            reportParameter5.Text = "iCreatedBy";
            reportParameter5.Visible = true;
            reportParameter6.AllowNull = true;
            reportParameter6.Name = "iConfigDlag";
            reportParameter6.Text = "iConfigDlag";
            reportParameter6.Visible = true;
            reportParameter7.AllowNull = true;
            reportParameter7.Name = "iBSName";
            reportParameter7.Text = "iBSName";
            reportParameter7.Visible = true;

            reportParameter8.AllowNull = true;
            reportParameter8.Name = "PRServerIP";
            reportParameter8.Text = "PRServerIP";
            reportParameter8.Visible = true;

            reportParameter9.AllowNull = true;
            reportParameter9.Name = "DRServerIP";
            reportParameter9.Text = "DRServerIP";
            reportParameter9.Visible = true;

            reportParameter10.AllowNull = true;
            reportParameter10.Name = "PRDBName";
            reportParameter10.Text = "PRDBName";
            reportParameter10.Visible = true;

            reportParameter11.AllowNull = true;
            reportParameter11.Name = "DRDBName";
            reportParameter11.Text = "DRDBName";
            reportParameter11.Visible = true;

            this.ReportParameters.Add(reportParameter2);
            this.ReportParameters.Add(reportParameter3);
            this.ReportParameters.Add(reportParameter4);
            this.ReportParameters.Add(reportParameter5);
            this.ReportParameters.Add(reportParameter6);
            this.ReportParameters.Add(reportParameter7);
            this.ReportParameters.Add(reportParameter8);
            this.ReportParameters.Add(reportParameter9);
            this.ReportParameters.Add(reportParameter10);
            this.ReportParameters.Add(reportParameter11);
            this.Style.BackgroundColor = System.Drawing.Color.White;
            styleRule1.Selectors.AddRange(new Telerik.Reporting.Drawing.ISelector[] {
            new Telerik.Reporting.Drawing.TypeSelector(typeof(Telerik.Reporting.TextItemBase)),
            new Telerik.Reporting.Drawing.TypeSelector(typeof(Telerik.Reporting.HtmlTextBox))});
            styleRule1.Style.Padding.Left = Telerik.Reporting.Drawing.Unit.Point(2D);
            styleRule1.Style.Padding.Right = Telerik.Reporting.Drawing.Unit.Point(2D);
            this.StyleSheet.AddRange(new Telerik.Reporting.Drawing.StyleRule[] {
            styleRule1});
            this.Width = Telerik.Reporting.Drawing.Unit.Inch(11.292914390563965D);
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }
        #endregion

        private Telerik.Reporting.PageHeaderSection pageHeaderSection1;
        private Telerik.Reporting.DetailSection detail;
        private Telerik.Reporting.PageFooterSection pageFooterSection1;
        private Telerik.Reporting.PictureBox pictureBox1;
        private Telerik.Reporting.PictureBox pictureBox2;
        private Telerik.Reporting.Shape shape3;
        private Telerik.Reporting.ReportHeaderSection reportHeaderSection1;
        private Telerik.Reporting.Panel panel6;
        private Telerik.Reporting.TextBox textBox9;
        private Telerik.Reporting.TextBox textBox10;
        private Telerik.Reporting.TextBox textBox11;
        private Telerik.Reporting.TextBox textBox12;
        private Telerik.Reporting.TextBox textBox13;
        private Telerik.Reporting.TextBox textBox14;
        private Telerik.Reporting.TextBox textBox15;
        private Telerik.Reporting.TextBox textBox16;
        private Telerik.Reporting.PictureBox pictureBox4;
        private Telerik.Reporting.PictureBox pictureBox3;
        private Telerik.Reporting.PictureBox pictureBox7;
        private Telerik.Reporting.PictureBox pictureBox8;
        private Telerik.Reporting.Shape shape2;
        private Telerik.Reporting.Shape shape1;
        private Telerik.Reporting.Shape shape5;
        private Telerik.Reporting.TextBox textBox1;
        private Telerik.Reporting.Shape shape7;
        private Telerik.Reporting.Shape shape9;
        private Telerik.Reporting.TextBox textBox49;
        private Telerik.Reporting.Panel panel5;
        private Telerik.Reporting.TextBox textBox37;
        private Telerik.Reporting.TextBox textBox7;
        private Telerik.Reporting.PictureBox pictureBox5;
        private Telerik.Reporting.SqlDataSource sqlDataSource1;
        private Telerik.Reporting.Panel panel3;
        private Telerik.Reporting.Graph graph1;
        private Telerik.Reporting.CartesianCoordinateSystem cartesianCoordinateSystem32;
        private Telerik.Reporting.GraphAxis graphAxis64;
        private Telerik.Reporting.GraphAxis graphAxis63;
        private Telerik.Reporting.SqlDataSource sqlDataSource2;
        private Telerik.Reporting.LineSeries lineSeries1;
        private Telerik.Reporting.LineSeries lineSeries2;
        private Telerik.Reporting.TextBox textBox19;
        private Telerik.Reporting.TextBox textBox20;
        private Telerik.Reporting.TextBox textBox22;
        private Telerik.Reporting.TextBox textBox4;
        private Telerik.Reporting.Shape shape4;
        private Telerik.Reporting.Shape shape6;
        private Telerik.Reporting.Panel panel1;
        private Telerik.Reporting.Graph graph3;
        private Telerik.Reporting.CartesianCoordinateSystem cartesianCoordinateSystem36;
        private Telerik.Reporting.GraphAxis graphAxis72;
        private Telerik.Reporting.GraphAxis graphAxis71;
        private Telerik.Reporting.SqlDataSource sqlDataSource3;
        private Telerik.Reporting.LineSeries lineSeries3;
        private Telerik.Reporting.LineSeries lineSeries4;
        private Telerik.Reporting.TextBox textBox3;
        private Telerik.Reporting.TextBox textBox17;
        private Telerik.Reporting.TextBox textBox24;
        private Telerik.Reporting.TextBox textBox25;
        private Telerik.Reporting.Shape shape8;
        private Telerik.Reporting.Shape shape11;
        private Telerik.Reporting.Panel panel2;
        private Telerik.Reporting.TextBox textBox2;
        private Telerik.Reporting.TextBox textBox26;
        private Telerik.Reporting.TextBox textBox27;
        private Telerik.Reporting.TextBox textBox28;
        private Telerik.Reporting.Graph graph5;
        private Telerik.Reporting.CartesianCoordinateSystem cartesianCoordinateSystem1;
        private Telerik.Reporting.GraphAxis graphAxis1;
        private Telerik.Reporting.GraphAxis graphAxis2;
        private Telerik.Reporting.SqlDataSource sqlDataSource7;
        private Telerik.Reporting.BarSeries barSeries1;
        private Telerik.Reporting.Panel panel4;
        private Telerik.Reporting.TextBox textBox6;
        private Telerik.Reporting.TextBox textBox32;
        private Telerik.Reporting.TextBox textBox34;
        private Telerik.Reporting.TextBox textBox35;
        private Telerik.Reporting.Graph graph4;
        private Telerik.Reporting.CartesianCoordinateSystem cartesianCoordinateSystem2;
        private Telerik.Reporting.GraphAxis graphAxis3;
        private Telerik.Reporting.GraphAxis graphAxis4;
        private Telerik.Reporting.SqlDataSource sqlDataSource8;
        private Telerik.Reporting.LineSeries lineSeries7;
        private Telerik.Reporting.LineSeries lineSeries8;
        private Telerik.Reporting.Shape shape12;
        private Telerik.Reporting.Shape shape13;
        private Telerik.Reporting.PictureBox pictureBox6;
        private Telerik.Reporting.TextBox textBox5;
        private Telerik.Reporting.TextBox textBox61;
        private Telerik.Reporting.Shape shape10;
        private Telerik.Reporting.ReportFooterSection reportFooterSection1;
        private Telerik.Reporting.SubReport subReport1;
        private Telerik.Reporting.PictureBox pictureBox17;
    }
}