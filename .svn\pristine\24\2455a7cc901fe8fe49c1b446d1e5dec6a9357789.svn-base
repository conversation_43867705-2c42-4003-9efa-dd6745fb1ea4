﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;

namespace CP.UI.Admin
{
    public class Components
    {
        public string DB_HostName { get; set; }

        public string OS { get; set; }

        public int Active { get; set; }

        public int Cluster { get; set; }

        public string Server_Type { get; set; }

        public string DB_IP_Address { get; set; }

        public int Id { get; set; }
    }

    public partial class BussinessImpactAnalysis : ApplicationGroupBasePage
    {
        #region Variables

        private List<BusinessUserFunction> SavebusFun = new List<BusinessUserFunction>();
        private ApplicationDependency _dependency = new ApplicationDependency();
        private static readonly IList<ApplicationDependency> _finaldependency = new List<ApplicationDependency>();
        private static readonly IList<ApplicationDependency> _foutwarddependency = new List<ApplicationDependency>();
        private static readonly IList<ApplicationDependency> _finfradependency = new List<ApplicationDependency>();
        private static readonly IList<ApplicationDependency> _fExternalInwardsdependency = new List<ApplicationDependency>();
        private static readonly IList<ApplicationDependency> _fExternalOutwardsdependency = new List<ApplicationDependency>();

        private static IList<BusinessUserFunction> _fBusinessFunction = new List<BusinessUserFunction>();
        private static IList<BusinessUserFunction> _editUserFunction = new List<BusinessUserFunction>();
        private static IList<BusinessInfo> _fBusinessInfo = new List<BusinessInfo>();
        private static IList<ApplicationGroupInfo> _fApplicationGroupInfo = new List<ApplicationGroupInfo>();
        private BusinessUserFunction _businessUserFunction = new BusinessUserFunction();
        private readonly IList<ApplicationGroup> _applicationGroups = new List<ApplicationGroup>();
        private readonly IList<ApplicationGroup> _applicationGroups2 = new List<ApplicationGroup>();
        private List<string> appstring = new List<string>();
        public static string CurrentURL = Constants.UrlConstants.Urls.BusinessImpactAnalysis.BIAList;
        private static string IsEdit;
        public string TotalSteps;
        public string CurrentStep;
        public string CompletedSteps;

        #endregion Variables

        #region Properties

        public string MessageInitials
        {
            get { return "BIA"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Admin.BusinessImpactAnalysisList;
                }
                return string.Empty;
            }
        }

        #endregion Properties

        #region Event

        protected void chkNoneChecked(object sender, EventArgs e)
        {
            if (chkNone.Checked)
            {
                txtNone.Enabled = true;
                rfvNone.Enabled = true;
            }
            else
            {
                txtNone.Enabled = false;
                rfvNone.Enabled = false;
                txtNone.Text = "";
            }
        }

        protected void chkG24HoursChecked(object sender, EventArgs e)
        {
            if (chkG24Hours.Checked)
            {
                txtG24Hours.Enabled = true;
                rfvGHours.Enabled = true;
            }
            else
            {
                txtG24Hours.Enabled = false;
                rfvGHours.Enabled = false;
                txtG24Hours.Text = "";
            }
        }

        protected void chkL24HoursChecked(object sender, EventArgs e)
        {
            if (chkL24Hours.Checked)
            {
                txtL24Hours.Enabled = true;
                rfvLHours.Enabled = true;
            }
            else
            {
                txtL24Hours.Enabled = false;
                txtL24Hours.Text = "";
                rfvLHours.Enabled = true;
            }
        }

        protected void chkMinimalChecked(object sender, EventArgs e)
        {
            if (chkMinimal.Checked)
            {
                txtMinimal.Enabled = true;
                RfvMini.Enabled = true;
            }
            else
            {
                txtMinimal.Enabled = false;
                RfvMini.Enabled = false;
                txtMinimal.Text = "";
            }
        }

        protected void chkAllChecked(object sender, EventArgs e)
        {
            if (chkAll.Checked)
            {
                txtAll.Enabled = true;
                rfvAll.Enabled = true;
            }
            else
            {
                txtAll.Enabled = false;
                rfvAll.Enabled = false;
                txtAll.Text = "";
            }
        }

        protected void LvBuFunctionInsertingClick(object sender, ListViewInsertEventArgs e)
        {
            bool isValid = true;
            var getIdBuFunction = new BusinessUserFunction();
            TextBox txtNamedep = (TextBox)e.Item.FindControl("SupportField");
            Label lblerror = (Label)e.Item.FindControl("lblerror");

            if (txtNamedep.Text == "")
            {
                lblerror.Visible = true;
                isValid = false;
            }

            if (isValid)
            {
                getIdBuFunction.BusinessServiceId = Convert.ToInt32(ddlApplicationGroupName.SelectedValue);
                getIdBuFunction.SupportField = txtNamedep.Text;
                getIdBuFunction.Tier1A = 0;
                getIdBuFunction.Tier1B = 0;
                getIdBuFunction.Tier2 = 0;
                getIdBuFunction.Tier3 = 0;
                getIdBuFunction.Tier4 = 0;
                getIdBuFunction.CreatorId = LoggedInUserId;
                _fBusinessFunction.Add(getIdBuFunction);

                if (_fBusinessFunction != null)
                {
                    BusinessFunctionTable();
                }
            }
            else
            {
                return;
            }
        }

        protected void LvBuFunctionDeletingClick(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (lvBUFunction.Items[e.ItemIndex].FindControl("Id")) as Label;

            if (lbl != null)
            {
                var id = Convert.ToInt32(lbl.Text);
                if (id != 0)
                {
                    var inwardId = Facade.GetBusinessUserFunctionById(id);
                    if (inwardId != null)
                    {
                        Facade.DeleteBusinessUserFunctionById(id);
                    }
                }
            }
            _fBusinessFunction.RemoveAt(e.ItemIndex);
            lvBUFunction.EditIndex = -1;
            BusinessFunctionTable();
        }

        protected void LvBuFunctionEditingClick(object sender, ListViewEditEventArgs e)
        {
            var lblId = (lvBUFunction.Items[e.NewEditIndex].FindControl("Id")) as Label;
            var lblSField = (lvBUFunction.Items[e.NewEditIndex].FindControl("SupportField")) as Label;
            var txtFiled = (lvBUFunction.Items[e.NewEditIndex].FindControl("txtSupportField")) as TextBox;
            var chkTier1a = (lvBUFunction.Items[e.NewEditIndex].FindControl("chkTier1avalue")) as CheckBox;
            var chkTier1b = (lvBUFunction.Items[e.NewEditIndex].FindControl("chkTier1bvalue")) as CheckBox;
            var chkTier2 = (lvBUFunction.Items[e.NewEditIndex].FindControl("chkTier2value")) as CheckBox;
            var chkTier3 = (lvBUFunction.Items[e.NewEditIndex].FindControl("chkTier3value")) as CheckBox;
            var chkTier4 = (lvBUFunction.Items[e.NewEditIndex].FindControl("chkTier4value")) as CheckBox;
            var imgedit = (lvBUFunction.Items[e.NewEditIndex].FindControl("ImgEdit")) as Image;
            var imgUp = (lvBUFunction.Items[e.NewEditIndex].FindControl("imgUpdate")) as Image;

            if (lblId != null)
            {
                var appId = Convert.ToInt32(lblId.Text);
                var functioInfo = Facade.GetBusinessUserFunctionById(appId);
                lblSField.Text = functioInfo.SupportField;
                txtFiled.Visible = true;
                txtFiled.Text = lblSField.Text;
                lblSField.Visible = false;
                chkTier1a.Checked = Convert.ToBoolean(functioInfo.Tier1A);
                chkTier1b.Checked = Convert.ToBoolean(functioInfo.Tier1B);
                chkTier2.Checked = Convert.ToBoolean(functioInfo.Tier2);
                chkTier3.Checked = Convert.ToBoolean(functioInfo.Tier3);
                chkTier4.Checked = Convert.ToBoolean(functioInfo.Tier4);
                imgedit.Visible = false;
                imgUp.Visible = true;
            }
        }

        protected void LvBuFunctionUpdatedClick(object sender, ListViewUpdatedEventArgs e)
        {
            BusinessUserFunction updateFunction = new BusinessUserFunction();

            Label lblId1 = lvBUFunction.EditItem.FindControl("Id") as Label;
        }

        protected void LvBuFunctionUpdatingClick(object sender, ListViewUpdateEventArgs e)
        {
            BusinessUserFunction updateFunction = new BusinessUserFunction();
            Label lblId1 = (lvBUFunction.Items[e.ItemIndex].FindControl("Id")) as Label;

            TextBox txtSField = lvBUFunction.Items[e.ItemIndex].FindControl("txtSupportField") as TextBox;
            CheckBox chk1A = lvBUFunction.Items[e.ItemIndex].FindControl("chkTier1avalue") as CheckBox;
            CheckBox chk1B = lvBUFunction.Items[e.ItemIndex].FindControl("chkTier1bvalue") as CheckBox;
            CheckBox chk2 = lvBUFunction.Items[e.ItemIndex].FindControl("chkTier2value") as CheckBox;
            CheckBox chk3 = lvBUFunction.Items[e.ItemIndex].FindControl("chkTier3value") as CheckBox;
            CheckBox chk4 = lvBUFunction.Items[e.ItemIndex].FindControl("chkTier4value") as CheckBox;
            var imgEdit1 = lvBUFunction.Items[e.ItemIndex].FindControl("ImgEdit") as Image;
            var imgUp1 = lvBUFunction.Items[e.ItemIndex].FindControl("imgUpdate") as Image;
            updateFunction.Id = lblId1.Text.ToInteger();
            updateFunction.BusinessServiceId = ddlApplicationGroupName.SelectedValue.ToInteger();
            updateFunction.SupportField = txtSField.Text;
            updateFunction.Tier1A = chk1A.Checked ? 1 : 0;
            updateFunction.Tier1B = chk1B.Checked ? 1 : 0;
            updateFunction.Tier2 = chk2.Checked ? 1 : 0;
            updateFunction.Tier3 = chk3.Checked ? 1 : 0;
            updateFunction.Tier4 = chk4.Checked ? 1 : 0;
            Facade.UpdateBusinessUserFunction(updateFunction);

            Label lblSF = (lvBUFunction.Items[e.ItemIndex].FindControl("SupportField")) as Label;
            lblSF.Visible = true;
            lblSF.Text = txtSField.Text;
            txtSField.Visible = false;
            imgEdit1.Visible = true;
            imgUp1.Visible = false;
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
        }

        protected void BtnImgCloseClick(object sender, EventArgs e)
        {
        }

        protected void WizardStep2_DeActive(object sender, EventArgs e)
        {
            bool btnNext = ValidWizard();
            if (btnNext == true)
            {
                mdlPopup.Show();
                Wizard1.ActiveStepIndex = 1;
            }
        }

        private bool ValidWizard()
        {
            bool testValid = false;
            if (_finaldependency.Count == 0 && _foutwarddependency.Count == 0 && _fExternalOutwardsdependency.Count == 0 && _fExternalInwardsdependency.Count == 0 && _finfradependency.Count == 0)
            {
                testValid = true;
            }
            else
            {
                testValid = false;
            }
            return testValid;
        }

        protected void CloseClick(object sender, EventArgs e)
        {
            Close();
        }

        private void Close()
        {
            mdlPopup.Hide();
        }

        protected void BtnSaveFinish(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }
                var currentTransactionType = TransactionType.Undefined;
                if (IsEdit == "Y")
                {
                    currentTransactionType = TransactionType.Update;
                }
                else
                {
                    currentTransactionType = TransactionType.Save;
                }

                GetAppDesc();
                GetBusinessInfo();
                SaveBusiness();
                var LastId = 0;
                if (currentTransactionType != TransactionType.Undefined)
                {
                    if (_fApplicationGroupInfo.Count != 0 && _fBusinessFunction.Count != 0 && _fBusinessInfo.Count != 0)
                    {
                        try
                        {
                            foreach (ApplicationGroupInfo appGroupInfo in _fApplicationGroupInfo)
                            {
                                ApplicationGroupInfo appGrpInfo = new ApplicationGroupInfo();
                                appGrpInfo.Id = appGroupInfo.Id;
                                appGrpInfo.ApplicationGroupId = appGroupInfo.ApplicationGroupId;
                                appGrpInfo.BusinessOwner = appGroupInfo.BusinessOwner;
                                appGrpInfo.AlternativeNames = appGroupInfo.AlternativeNames;
                                appGrpInfo.SupportTeam = appGroupInfo.SupportTeam;
                                appGrpInfo.ApplicationFunctions = appGroupInfo.ApplicationFunctions;
                                appGrpInfo.HeavyLoadTimes = appGroupInfo.HeavyLoadTimes;
                                appGrpInfo.CriticalTimes = appGroupInfo.CriticalTimes;

                                if (IsEdit == "Y")
                                {
                                    appGrpInfo.UpdatorId = LoggedInUserId;
                                    Facade.UpdateApplicationGroupInfo(appGrpInfo);
                                }
                                else
                                {
                                    appGrpInfo.CreatorId = LoggedInUserId;
                                    Facade.AddApplicationGroupInfo(appGrpInfo);
                                }
                            }
                            foreach (BusinessInfo businessinfo in _fBusinessInfo)
                            {
                                BusinessInfo business = new BusinessInfo();

                                business.Id = businessinfo.Id;
                                business.BusinessServiceId = businessinfo.BusinessServiceId;
                                business.Name = businessinfo.Name;
                                business.Location = businessinfo.Location;
                                business.BriefOverview = businessinfo.BriefOverview;
                                business.Methods = businessinfo.Methods;
                                business.BCPAvailability = businessinfo.BCPAvailability;
                                business.MaximumDowntime = businessinfo.MaximumDowntime;
                                business.DRNone = businessinfo.DRNone;
                                business.DR24Hrs = businessinfo.DR24Hrs;
                                business.DRAll = businessinfo.DRAll;

                                business.DRGreator24 = businessinfo.DRGreator24;
                                business.DRMinimal = businessinfo.DRMinimal;
                                business.FinancialLossProfit = businessinfo.FinancialLossProfit;
                                business.FinancialHMI = businessinfo.FinancialHMI;
                                business.RegulatoryImpact = businessinfo.RegulatoryImpact;
                                business.RegulatoryHMI = businessinfo.RegulatoryHMI;
                                business.CustomerRetension = businessinfo.CustomerRetension;
                                business.CustomerHMI = businessinfo.CustomerHMI;
                                business.BrandNameReputation = businessinfo.BrandNameReputation;
                                business.BrandNameHMI = businessinfo.BrandNameHMI;

                                business.OperationalEffect = businessinfo.OperationalEffect;
                                business.OperationalHMI = businessinfo.OperationalHMI;
                                business.ManagementControl = businessinfo.ManagementControl;
                                business.ManagementHMI = businessinfo.ManagementHMI;
                                business.ComplianceImpact = businessinfo.ComplianceImpact;
                                business.ComplianceHMI = businessinfo.ComplianceHMI;
                                business.Licence = businessinfo.Licence;
                                business.LicenceHMI = businessinfo.LicenceHMI;
                                business.CreditFraud = businessinfo.CreditFraud;
                                business.CreditHMI = businessinfo.CreditHMI;
                                business.CreatorId = LoggedInUserId;
                                if (IsEdit == "Y")
                                {
                                    var updateId = Facade.UpdateBusinessInfo(business);
                                    LastId = updateId.Id;
                                }
                                else
                                {
                                    var insertId = Facade.AddBusinessInfo(business);
                                    LastId = insertId.Id;
                                }
                            }

                            foreach (BusinessUserFunction busfuntion in _editUserFunction)
                            {
                                BusinessUserFunction busfun = new BusinessUserFunction();
                                busfun.Id = busfuntion.Id;
                                busfun.BusinessServiceId = ddlApplicationGroupName.SelectedValue.ToInteger();
                                if (IsEdit == "Y")
                                {
                                    busfun.BusinessInfoId = LastId;
                                }
                                else
                                {
                                    busfun.BusinessInfoId = LastId;
                                }
                                busfun.SupportField = busfuntion.SupportField;
                                busfun.Tier1A = busfuntion.Tier1A;
                                busfun.Tier1B = busfuntion.Tier1B;
                                busfun.Tier2 = busfuntion.Tier2;
                                busfun.Tier3 = busfuntion.Tier3;
                                busfun.Tier4 = busfuntion.Tier4;
                                if (IsEdit == "Y" && busfun.CreatorId != 1 && busfun.Id != 0)
                                {
                                    busfun.UpdatorId = LoggedInUserId;
                                    Facade.UpdateBusinessUserFunction(busfun);
                                }
                                else if (IsEdit == "Y" && busfun.Id == 0)
                                {
                                    busfun.CreatorId = LoggedInUserId;
                                    Facade.AddBusinessUserFunction(busfun);
                                }
                                else
                                {
                                    busfun.CreatorId = LoggedInUserId;
                                    Facade.AddBusinessUserFunction(busfun);
                                }
                            }
                            if (_finaldependency.Count != 0)
                            {
                                foreach (ApplicationDependency inward in _finaldependency)
                                {
                                    ApplicationDependency inw = new ApplicationDependency();
                                    inw.Id = inward.Id;
                                    inw.BusinessServiceId = ddlApplicationGroupName.SelectedValue.ToInteger();
                                    if (IsEdit == "Y")
                                    {
                                        inw.BusinessInfoId = LastId;
                                    }
                                    else
                                    {
                                        inw.BusinessInfoId = LastId;
                                    }
                                    inw.ServerId = inward.ServerId;
                                    inw.DatabaseId = inward.DatabaseId;
                                    inw.DependancyType = inward.DependancyType;
                                    inw.Description = inward.Description;
                                    inw.DRCategory = inward.DRCategory;

                                    if (IsEdit == "Y" && inw.CreatorId != 1 && inw.Id != 0)
                                    {
                                        inw.UpdatorId = LoggedInUserId;
                                        Facade.UpdateApplicationDependency(inw);
                                    }
                                    else if (IsEdit == "Y" && inw.Id == 0)
                                    {
                                        inw.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(inw);
                                    }
                                    else
                                    {
                                        inw.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(inw);
                                    }
                                }
                            }
                            if (_foutwarddependency.Count != 0)
                            {
                                foreach (ApplicationDependency outward in _foutwarddependency)
                                {
                                    ApplicationDependency outw = new ApplicationDependency();
                                    outw.Id = outward.Id;
                                    outw.BusinessServiceId = ddlApplicationGroupName.SelectedValue.ToInteger();
                                    if (IsEdit == "Y")
                                    {
                                        outw.BusinessInfoId = LastId;
                                    }
                                    else
                                    {
                                        outw.BusinessInfoId = LastId;
                                    }

                                    outw.ServerId = outward.ServerId;
                                    outw.DatabaseId = outward.DatabaseId;
                                    outw.DependancyType = outward.DependancyType;
                                    outw.Description = outward.Description;
                                    outw.DRCategory = outward.DRCategory;

                                    if (IsEdit == "Y" && outw.CreatorId != 1 && outw.Id != 0)
                                    {
                                        outw.UpdatorId = LoggedInUserId;
                                        Facade.UpdateApplicationDependency(outw);
                                    }
                                    else if (IsEdit == "Y" && outw.Id == 0)
                                    {
                                        outw.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(outw);
                                    }
                                    else
                                    {
                                        outw.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(outw);
                                    }
                                }
                            }
                            if (_finfradependency.Count != 0)
                            {
                                foreach (ApplicationDependency infra in _finfradependency)
                                {
                                    ApplicationDependency infrastructure = new ApplicationDependency();
                                    infrastructure.Id = infra.Id;
                                    infrastructure.BusinessServiceId = ddlApplicationGroupName.SelectedValue.ToInteger();
                                    if (IsEdit == "Y")
                                    {
                                        infrastructure.BusinessInfoId = LastId;
                                    }
                                    else
                                    {
                                        infrastructure.BusinessInfoId = LastId;
                                    }

                                    infrastructure.ServerId = infra.ServerId;
                                    infrastructure.DatabaseId = infra.DatabaseId;
                                    infrastructure.DependancyType = infra.DependancyType;
                                    infrastructure.Description = infra.Description;
                                    infrastructure.DRCategory = infra.DRCategory;

                                    if (IsEdit == "Y" && infrastructure.CreatorId != 1 && infrastructure.Id != 0)
                                    {
                                        infrastructure.UpdatorId = LoggedInUserId;
                                        Facade.UpdateApplicationDependency(infrastructure);
                                    }
                                    else if (IsEdit == "Y" && infrastructure.Id == 0)
                                    {
                                        infrastructure.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(infrastructure);
                                    }
                                    else
                                    {
                                        infrastructure.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(infrastructure);
                                    }
                                }
                            }
                            if (_fExternalInwardsdependency.Count != 0)
                            {
                                foreach (ApplicationDependency eInward in _fExternalInwardsdependency)
                                {
                                    ApplicationDependency externalIn = new ApplicationDependency();
                                    externalIn.Id = eInward.Id;
                                    externalIn.BusinessServiceId = ddlApplicationGroupName.SelectedValue.ToInteger();
                                    if (IsEdit == "Y")
                                    {
                                        externalIn.BusinessInfoId = LastId;
                                    }
                                    else
                                    {
                                        externalIn.BusinessInfoId = LastId;
                                    }

                                    externalIn.ServerId = eInward.ServerId;
                                    externalIn.DatabaseId = eInward.DatabaseId;
                                    externalIn.DependancyType = eInward.DependancyType;
                                    externalIn.Description = eInward.Description;

                                    if (IsEdit == "Y" && externalIn.CreatorId != 1 && externalIn.Id != 0)
                                    {
                                        externalIn.UpdatorId = LoggedInUserId;
                                        Facade.UpdateApplicationDependency(externalIn);
                                    }
                                    else if (IsEdit == "Y" && externalIn.Id == 0)
                                    {
                                        externalIn.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(externalIn);
                                    }
                                    else
                                    {
                                        externalIn.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(externalIn);
                                    }
                                }
                            }
                            if (_fExternalOutwardsdependency.Count != 0)
                            {
                                foreach (ApplicationDependency eOutward in _fExternalOutwardsdependency)
                                {
                                    ApplicationDependency externalOut = new ApplicationDependency();
                                    externalOut.Id = eOutward.Id;
                                    externalOut.BusinessServiceId = ddlApplicationGroupName.SelectedValue.ToInteger();
                                    if (IsEdit == "Y")
                                    {
                                        externalOut.BusinessInfoId = LastId;
                                    }
                                    else
                                    {
                                        externalOut.BusinessInfoId = LastId;
                                    }

                                    externalOut.ServerId = eOutward.ServerId;
                                    externalOut.DatabaseId = eOutward.DatabaseId;
                                    externalOut.DependancyType = eOutward.DependancyType;
                                    externalOut.Description = eOutward.Description;

                                    if (IsEdit == "Y" && externalOut.CreatorId != 1 && externalOut.Id != 0)
                                    {
                                        externalOut.UpdatorId = LoggedInUserId;
                                        Facade.UpdateApplicationDependency(externalOut);
                                    }
                                    else if (IsEdit == "Y" && externalOut.Id == 0)
                                    {
                                        externalOut.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(externalOut);
                                    }
                                    else
                                    {
                                        externalOut.CreatorId = LoggedInUserId;
                                        Facade.AddApplicationDependency(externalOut);
                                    }
                                }
                            }

                            if (IsEdit == "Y")
                            {
                                Response.Redirect(CurrentURL + "?Bia=UpdateSuccess", false);
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials,
                                                                                               currentTransactionType));
                            }
                            else
                            {
                                Response.Redirect(CurrentURL + "?Bia=Success", false);
                                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials,
                                                                                                currentTransactionType));
                            }
                        }
                        catch (CpException ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            ExceptionManager.Manage(ex, this);
                        }
                        catch (Exception ex)
                        {
                            InvalidateTransaction();

                            returnUrl = Request.RawUrl;

                            ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                            if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                            {
                                ExceptionManager.Manage((CpException)ex.InnerException, this);
                            }
                            else
                            {
                                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                                ExceptionManager.Manage(customEx, this);
                            }
                        }
                        if (returnUrl.IsNotNullOrEmpty())
                        {
                            Helper.Url.Redirect(new SecureUrl(returnUrl));
                        }
                    }
                }
            }
        }

        protected void LvInwardItemInserting(object sender, ListViewInsertEventArgs e)
        {
            bool isValid = true;
            var GetIdInward = new ApplicationDependency();
            DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlInwardServerList");
            DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlInwardDatabaseList");
            TextBox txtDesc = (TextBox)e.Item.FindControl("Description");
            DropDownList ddl1 = (DropDownList)e.Item.FindControl("DRCategory");

            Label lblNa = (Label)e.Item.FindControl("lblApplicationName");
            Label lblDesc = (Label)e.Item.FindControl("lblDescription");
            Label lblDrCate = (Label)e.Item.FindControl("lblDRCategory");
            Label lblInServer1 = (Label)e.Item.FindControl("lblInServer");

            if (ddlserver.SelectedValue == "0")
            {
                lblInServer1.Text = "*";
                lblInServer1.Visible = true;
                isValid = false;
            }
            if (ddldatabase.SelectedValue == "0")
            {
                lblNa.Visible = true;
                lblNa.Text = "*";
                isValid = false;
            }

            if (txtDesc.Text == "")
            {
                lblDesc.Text = "*";
                lblDesc.Visible = true;
                isValid = false;
            }
            if (ddl1.SelectedValue == "0")
            {
                lblDrCate.Text = "Choose One";
                lblDrCate.Visible = true;
                isValid = false;
            }
            if (isValid)
            {
                _dependency.ServerId = ddlserver.SelectedValue.ToInteger();
                _dependency.DatabaseId = ddldatabase.SelectedValue.ToInteger();
                _dependency.BusinessServiceId = Convert.ToInt32(ddlApplicationGroupName.SelectedValue);
                _dependency.DependancyType = (int)AppDependency.InwardDataFlow;
                _dependency.Description = txtDesc.Text;
                _dependency.DRCategory = Convert.ToInt32(ddl1.SelectedValue);
                _dependency.CreatorId = LoggedInUserId;

                _finaldependency.Add(_dependency);
                InwardTable();
            }
            else
            {
                return;
            }
        }

        protected void LvOutwareItemInserting(object sender, ListViewInsertEventArgs e)
        {
            bool isValid = true;
            var GetIdOutward = new ApplicationDependency();
            DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlOutwardServerList");
            DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlOutwardDatabaseList");
            TextBox txtDesc = (TextBox)e.Item.FindControl("Description");
            DropDownList ddl1 = (DropDownList)e.Item.FindControl("DRCategory");
            Label lblNa = (Label)e.Item.FindControl("lblApplicationName");
            Label lblDesc = (Label)e.Item.FindControl("lblDescription");
            Label lblDrCate = (Label)e.Item.FindControl("lblDRCategory");
            Label lblOutServer1 = (Label)e.Item.FindControl("lblOutServer");

            if (ddlserver.SelectedValue == "0")
            {
                lblOutServer1.Text = "*";
                lblOutServer1.Visible = true;
                isValid = false;
            }
            if (ddldatabase.SelectedValue == "0")
            {
                lblNa.Visible = true;
                lblNa.Text = "*";
                isValid = false;
            }

            if (txtDesc.Text == "")
            {
                lblDesc.Text = "*";
                lblDesc.Visible = true;
                isValid = false;
            }
            if (ddl1.SelectedValue == "0")
            {
                lblDrCate.Text = "Choose One";
                lblDrCate.Visible = true;
                isValid = false;
            }
            if (isValid)
            {
                _dependency.ServerId = ddlserver.SelectedValue.ToInteger();
                _dependency.DatabaseId = ddldatabase.SelectedValue.ToInteger();
                _dependency.BusinessServiceId = Convert.ToInt32(ddlApplicationGroupName.SelectedValue);
                _dependency.DependancyType = (int)AppDependency.OutwardDataFlow;
                _dependency.Description = txtDesc.Text;
                _dependency.DRCategory = Convert.ToInt32(ddl1.SelectedValue);
                _dependency.CreatorId = LoggedInUserId;

                _foutwarddependency.Add(_dependency);
                OutwardTable();
            }
            else
            {
                return;
            }
        }

        protected void LvInfrastructureInserting(object sender, ListViewInsertEventArgs e)
        {
            bool isValid = true;
            var GetIdInfra = new ApplicationDependency();
            DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlInfrawardServerList");
            DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlInfrawardDatabaseList");
            TextBox txtDesc = (TextBox)e.Item.FindControl("Description");
            DropDownList ddl1 = (DropDownList)e.Item.FindControl("DRCategory");
            Label lblDbNa = (Label)e.Item.FindControl("lblInfraDBName");
            Label lblDesc = (Label)e.Item.FindControl("lblDescription");
            Label lblDrCate = (Label)e.Item.FindControl("lblDRCategory");
            Label lblInfraServer1 = (Label)e.Item.FindControl("lblInfraaServer");

            if (ddlserver.SelectedValue == "0")
            {
                lblInfraServer1.Text = "*";
                lblInfraServer1.Visible = true;
                isValid = false;
            }
            if (ddldatabase.SelectedValue == "0")
            {
                lblDbNa.Visible = true;
                lblDbNa.Text = "*";
                isValid = false;
            }

            if (txtDesc.Text == "")
            {
                lblDesc.Text = "*";
                lblDesc.Visible = true;
                isValid = false;
            }
            if (ddl1.SelectedValue == "0")
            {
                lblDrCate.Text = "Choose One";
                lblDrCate.Visible = true;
                isValid = false;
            }

            if (isValid)
            {
                _dependency.ServerId = ddlserver.SelectedValue.ToInteger();
                _dependency.DatabaseId = ddldatabase.SelectedValue.ToInteger();
                _dependency.BusinessServiceId = Convert.ToInt32(ddlApplicationGroupName.SelectedValue);
                _dependency.DependancyType = (int)AppDependency.Infrastructure;
                _dependency.Description = txtDesc.Text;
                _dependency.DRCategory = Convert.ToInt32(ddl1.SelectedValue);
                _dependency.CreatorId = LoggedInUserId;
                _finfradependency.Add(_dependency);

                InfraTable();
            }
            else
            {
                return;
            }
        }

        protected void LvExternalInwardInserting(object sender, ListViewInsertEventArgs e)
        {
            bool isValid = true;
            var GetIdExInward = new ApplicationDependency();
            TextBox txtNamedep = (TextBox)e.Item.FindControl("ApplicationName");
            TextBox txtDesc = (TextBox)e.Item.FindControl("Description");
            Label lblNa = (Label)e.Item.FindControl("lblApplicationName");
            Label lblExtServer = (Label)e.Item.FindControl("lblExterServer");
            Label lblDesc = (Label)e.Item.FindControl("lblDescription");
            DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlExternalInwardServerList");
            DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlExternalInwardDatabaseList");

            if (ddlserver.SelectedValue == "0")
            {
                lblExtServer.Text = "*";
                lblExtServer.Visible = true;
                isValid = false;
            }
            if (ddldatabase.SelectedValue == "0")
            {
                lblNa.Visible = true;
                lblNa.Text = "*";
                isValid = false;
            }

            if (txtDesc.Text == "")
            {
                lblDesc.Text = "*";
                lblDesc.Visible = true;
                isValid = false;
            }

            if (isValid)
            {
                _dependency.ServerId = ddlserver.SelectedValue.ToInteger();
                _dependency.DatabaseId = ddldatabase.SelectedValue.ToInteger();
                _dependency.BusinessServiceId = Convert.ToInt32(ddlApplicationGroupName.SelectedValue);
                _dependency.DependancyType = (int)AppDependency.ExternalInwardDataFlow;
                _dependency.Description = txtDesc.Text;
                _dependency.CreatorId = LoggedInUserId;

                _fExternalInwardsdependency.Add(_dependency);
                ExternalInwardTable();
            }
            else
            {
                return;
            }
        }

        protected void LvExternalOutwardInserting(object sender, ListViewInsertEventArgs e)
        {
            bool isValid = true;
            var GetIdExOutward = new ApplicationDependency();
            TextBox txtDesc = (TextBox)e.Item.FindControl("Description");
            Label lblNa = (Label)e.Item.FindControl("lblApplicationName");
            Label lblDesc = (Label)e.Item.FindControl("lblDescription");
            Label lblExtOut = (Label)e.Item.FindControl("lblExtOutServer");
            DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlExternalOutwardServerList");
            DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlExternalOutwardDatabaseList");

            if (ddlserver.SelectedValue == "0")
            {
                lblExtOut.Text = "*";
                lblExtOut.Visible = true;
                isValid = false;
            }
            if (ddldatabase.SelectedValue == "0")
            {
                lblNa.Visible = true;
                lblNa.Text = "*";
                isValid = false;
            }
            if (txtDesc.Text == "")
            {
                lblDesc.Text = "*";
                lblDesc.Visible = true;
                isValid = false;
            }

            if (isValid)
            {
                _dependency.ServerId = ddlserver.SelectedValue.ToInteger();
                _dependency.DatabaseId = ddldatabase.SelectedValue.ToInteger();
                _dependency.BusinessServiceId = Convert.ToInt32(ddlApplicationGroupName.SelectedValue);
                _dependency.DependancyType = (int)AppDependency.ExternalOutwardDataFlow;
                _dependency.Description = txtDesc.Text;
                _dependency.CreatorId = LoggedInUserId;

                _fExternalOutwardsdependency.Add(_dependency);
                ExternalOutwardTable();
            }
            else
            {
                return;
            }
        }

        protected void LvInwardItemEditing(object sender, ListViewEditEventArgs e)
        {
            Label lblId = lvInward.Items[e.NewEditIndex].FindControl("Id") as Label;
            var Id = lblId.Text;
            var getId = Facade.GetApplicationDependencyById(Id.ToInteger());
            var getserverId = Facade.GetServerById(getId.ServerId);

            var getdbId = Facade.GetDatabaseBaseById(getId.DatabaseId);

            EditInward();
            lvInward.EditIndex = e.NewEditIndex;
            lvInward.DataSource = _finaldependency;
            lvInward.DataBind();
            DropDownList ddlServer = lvInward.Items[e.NewEditIndex].FindControl("ddlEditServerList") as DropDownList;
            DropDownList ddldatabase = lvInward.Items[e.NewEditIndex].FindControl("ddlEditDatabaseList") as DropDownList;
            DropDownList ddlcategory = lvInward.Items[e.NewEditIndex].FindControl("DRCategory") as DropDownList;

            Utility.PopulateServer(ddlServer, true);

            Utility.PopulateDatabase(ddldatabase, IsSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany.IsParent, true);
            if (getserverId != null)
            {
                ddlServer.SelectedValue = getserverId.Id.ToString();
            }
            if (getdbId != null)
            {
                ddldatabase.SelectedValue = getdbId.Id.ToString();
            }
            if (ddlcategory != null)
            {
                ddlcategory.SelectedValue = getId.DRCategory.ToString();
            }
        }

        protected void LvInwardItemUpdating(object sender, ListViewUpdateEventArgs e)
        {
            ApplicationDependency update = new ApplicationDependency();
            Label lblId = lvInward.Items[e.ItemIndex].FindControl("Id") as Label;
            TextBox txtDescription = lvInward.Items[e.ItemIndex].FindControl("Description") as TextBox;
            DropDownList ddlServer = lvInward.Items[e.ItemIndex].FindControl("ddlEditServerList") as DropDownList;
            DropDownList ddldatabase = lvInward.Items[e.ItemIndex].FindControl("ddlEditDatabaseList") as DropDownList;
            DropDownList ddlcategory = lvInward.Items[e.ItemIndex].FindControl("DRCategory") as DropDownList;
            update.Id = lblId.Text.ToInteger();
            update.BusinessServiceId = ddlApplicationGroupName.SelectedValue.ToInteger();
            update.DependancyType = (int)AppDependency.InwardDataFlow;
            update.ServerId = ddlServer.SelectedValue.ToInteger();
            update.DatabaseId = ddldatabase.SelectedValue.ToInteger();
            update.Description = txtDescription.Text.ToString();
            update.DRCategory = ddlcategory.SelectedValue.ToInteger();
            Facade.UpdateApplicationDependency(update);
            _finaldependency.Clear();
            EditInward();
            lvInward.EditIndex = -1;
            lvInward.DataSource = _finaldependency;
            lvInward.DataBind();
        }

        protected void LvInwardItemCreated(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlInwardServerList");
                DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlInwardDatabaseList");
                if (ddlserver != null)
                {
                    Utility.PopulateServer(ddlserver, true);
                }
                if (ddldatabase != null)
                {
                    Utility.PopulateDatabase(ddldatabase, IsSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany.IsParent, true);
                }
            }
        }

        protected void LvExternalOutwardItemCreated(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlExternalOutwardServerList");
                DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlExternalOutwardDatabaseList");
                if (ddlserver != null)
                {
                    Utility.PopulateServer(ddlserver, true);
                }
                if (ddldatabase != null)
                {
                    Utility.PopulateDatabase(ddldatabase, IsSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany.IsParent, true);
                }
            }
        }

        protected void LvExternalInwardItemCreated(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlExternalInwardServerList");
                DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlExternalInwardDatabaseList");
                if (ddlserver != null)
                {
                    Utility.PopulateServer(ddlserver, true);
                }
                if (ddldatabase != null)
                {
                    Utility.PopulateDatabase(ddldatabase, IsSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany.IsParent, true);
                }
            }
        }

        protected void LvInfraItemCreated(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlInfrawardServerList");
                DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlInfrawardDatabaseList");
                if (ddlserver != null)
                {
                    Utility.PopulateServer(ddlserver, true);
                }
                if (ddldatabase != null)
                {
                    Utility.PopulateDatabase(ddldatabase, IsSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany.IsParent, true);
                }
            }
        }

        protected void LvOutwardItemCreated(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                DropDownList ddlserver = (DropDownList)e.Item.FindControl("ddlOutwardServerList");
                DropDownList ddldatabase = (DropDownList)e.Item.FindControl("ddlOutwardDatabaseList");
                if (ddlserver != null)
                {
                    Utility.PopulateServer(ddlserver, true);
                }
                if (ddldatabase != null)
                {
                    Utility.PopulateDatabase(ddldatabase, IsSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany.IsParent, true);
                }
            }
        }

        protected void LvOutwardItemEditing(object sender, ListViewEditEventArgs e)
        {
            Label lblId = lvInward.Items[e.NewEditIndex].FindControl("Id") as Label;
            var Id = lblId.Text;
            var getId = Facade.GetApplicationDependencyById(Id.ToInteger());
            var getserverId = Facade.GetServerById(getId.ServerId);

            var getdbId = Facade.GetDatabaseBaseById(getId.DatabaseId);

            EditInward();
            lvOutward.EditIndex = e.NewEditIndex;
            lvOutward.DataSource = _finaldependency;
            lvOutward.DataBind();
            DropDownList ddlServer = lvOutward.Items[e.NewEditIndex].FindControl("ddlEditServerList") as DropDownList;
            DropDownList ddldatabase = lvOutward.Items[e.NewEditIndex].FindControl("ddlEditDatabaseList") as DropDownList;
            DropDownList ddlcategory = lvOutward.Items[e.NewEditIndex].FindControl("DRCategory") as DropDownList;

            Utility.PopulateServer(ddlServer, true);
            Utility.PopulateDatabase(ddldatabase, IsSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany.IsParent, true);
            if (getserverId != null)
            {
                ddlServer.SelectedValue = getserverId.Id.ToString();
            }
            if (getdbId != null)
            {
                ddldatabase.SelectedValue = getdbId.Id.ToString();
            }
            if (ddlcategory != null)
            {
                ddlcategory.SelectedValue = getId.DRCategory.ToString();
            }
        }

        protected void DdlApplicationChanged(object sender, EventArgs e)
        {
            if (ddlApplicationGroupName.SelectedIndex != 0)
            {
                InwardTable();
                OutwardTable();
                InfraTable();
                ExternalInwardTable();
                ExternalOutwardTable();
                BusinessFunctionTable();
            }
        }

        protected void LvInwardItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (lvInward.Items[e.ItemIndex].FindControl("Id")) as Label;

            if (lbl != null)
            {
                var id = Convert.ToInt32(lbl.Text);
                if (id != 0)
                {
                    var inwardId = Facade.GetApplicationDependencyById(id);
                    if (inwardId != null)
                    {
                        Facade.DeleteApplicationDependencyById(id);
                    }
                }
            }
            _finaldependency.RemoveAt(e.ItemIndex);

            lvInward.EditIndex = -1;
            InwardTable();
        }

        protected void LvOutwardItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (lvOutward.Items[e.ItemIndex].FindControl("Id")) as Label;

            if (lbl != null)
            {
                var id = Convert.ToInt32(lbl.Text);
                if (id != 0)
                {
                    var outwardId = Facade.GetApplicationDependencyById(id);
                    if (outwardId != null)
                    {
                        Facade.DeleteApplicationDependencyById(id);
                    }
                }
            }
            _foutwarddependency.RemoveAt(e.ItemIndex);
            lvOutward.EditIndex = -1;
            OutwardTable();
        }

        protected void LvInfrastructureItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (lvInfrastructure.Items[e.ItemIndex].FindControl("Id")) as Label;

            if (lbl != null)
            {
                var id = Convert.ToInt32(lbl.Text);
                if (id != 0)
                {
                    var Id = Facade.GetApplicationDependencyById(id);
                    if (Id != null)
                    {
                        Facade.DeleteApplicationDependencyById(id);
                    }
                }
            }
            _finfradependency.RemoveAt(e.ItemIndex);
            lvInfrastructure.EditIndex = -1;
            InfraTable();
        }

        protected void LvExternalInwardDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (LvExternalInward.Items[e.ItemIndex].FindControl("Id")) as Label;

            if (lbl != null)
            {
                var id = Convert.ToInt32(lbl.Text);
                if (id != 0)
                {
                    var Id = Facade.GetApplicationDependencyById(id);
                    if (Id != null)
                    {
                        Facade.DeleteApplicationDependencyById(id);
                    }
                }
            }
            _fExternalInwardsdependency.RemoveAt(e.ItemIndex);
            LvExternalInward.EditIndex = -1;
            ExternalInwardTable();
        }

        protected void LvExternalOutwardDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lbl = (lvExternalOutward.Items[e.ItemIndex].FindControl("Id")) as Label;

            if (lbl != null)
            {
                var id = Convert.ToInt32(lbl.Text);
                if (id != 0)
                {
                    var Id = Facade.GetApplicationDependencyById(id);
                    if (Id != null)
                    {
                        Facade.DeleteApplicationDependencyById(id);
                    }
                }
            }
            _fExternalOutwardsdependency.RemoveAt(e.ItemIndex);
            lvExternalOutward.EditIndex = -1;
            ExternalOutwardTable();
        }

        #endregion Event

        #region Methods

        public bool SaveBusiness()
        {
            int count = 0;
            bool isValid = true;
            foreach (ListViewDataItem lvi in lvBUFunction.Items)
            {
                Label lblId = (Label)lvi.FindControl("Id");
                Label txt1 = (Label)lvi.FindControl("SupportField");
                CheckBox chk1 = (CheckBox)lvi.FindControl("chkTier1avalue");
                CheckBox chk2 = (CheckBox)lvi.FindControl("chkTier1bvalue");
                CheckBox chk3 = (CheckBox)lvi.FindControl("chkTier2value");
                CheckBox chk4 = (CheckBox)lvi.FindControl("chkTier3value");
                CheckBox chk5 = (CheckBox)lvi.FindControl("chkTier4value");

                if (txt1.Text != "-")
                {
                    var busFun = new BusinessUserFunction();
                    busFun.Id = lblId.Text.ToInteger();
                    busFun.BusinessServiceId = Convert.ToInt32(ddlApplicationGroupName.SelectedValue);
                    busFun.SupportField = txt1.Text;
                    busFun.Tier1A = chk1.Checked ? 1 : 0;
                    busFun.Tier1B = chk2.Checked ? 1 : 0;
                    busFun.Tier2 = chk3.Checked ? 1 : 0;
                    busFun.Tier3 = chk4.Checked ? 1 : 0;
                    busFun.Tier4 = chk5.Checked ? 1 : 0;
                    _editUserFunction.Add(busFun);
                }
                count = _fBusinessFunction.Count;
            }

            return isValid;
        }

        private void EditInward()
        {
            _finaldependency.Clear();
            var getall = Facade.GetAllApplicationDependencies();
            var result = from inward in getall where inward.DependancyType == (int)AppDependency.InwardDataFlow && inward.BusinessServiceId == CurrentApplicationGroupId select inward;
            foreach (ApplicationDependency appInward in result)
            {
                ApplicationDependency subInward = new ApplicationDependency();
                subInward.Id = appInward.Id;
                subInward.BusinessServiceId = appInward.BusinessServiceId;
                subInward.BusinessInfoId = appInward.BusinessInfoId;
                subInward.ServerId = appInward.ServerId;
                subInward.DependancyType = appInward.DependancyType;
                subInward.DatabaseId = appInward.DatabaseId;
                subInward.Description = appInward.Description;
                subInward.DRCategory = appInward.DRCategory;
                _finaldependency.Add(subInward);
            }
        }

        private void InwardTable()
        {
            if (_finaldependency != null)
            {
                if (_finaldependency.Count > 0)
                {
                    lvInward.DataSource = _finaldependency;
                    lvInward.DataBind();
                }
            }
        }

        private void OutwardTable()
        {
            if (_foutwarddependency != null)
            {
                if (_foutwarddependency.Count > 0)
                {
                    lvOutward.DataSource = _foutwarddependency;
                    lvOutward.DataBind();
                }
            }
        }

        private void InfraTable()
        {
            if (_finfradependency != null)
            {
                if (_finfradependency.Count > 0)
                {
                    lvInfrastructure.DataSource = _finfradependency;
                    lvInfrastructure.DataBind();
                }
            }
        }

        private void ExternalInwardTable()
        {
            if (_fExternalInwardsdependency != null)
            {
                if (_fExternalInwardsdependency.Count > 0)
                {
                    LvExternalInward.DataSource = _fExternalInwardsdependency;
                    LvExternalInward.DataBind();
                }
            }
        }

        private void ExternalOutwardTable()
        {
            if (_fExternalOutwardsdependency != null)
            {
                if (_fExternalOutwardsdependency.Count > 0)
                {
                    lvExternalOutward.DataSource = _fExternalOutwardsdependency;
                    lvExternalOutward.DataBind();
                }
            }
        }

        private void BusinessFunctionTable()
        {
            if (_fBusinessFunction != null)
            {
                if (_fBusinessFunction.Count > 0)
                {
                    lvBUFunction.DataSource = _fBusinessFunction;
                    lvBUFunction.DataBind();
                }
            }
        }

        public override void PrepareView()
        {
            Wizard1.PreRender += new EventHandler(Wizard1_PreRender);

            // SideBarList.DataBind();
            ddlApplicationGroupName.Attributes.Add("onblur", "ValidatorValidate(" + rfvAppName.ClientID + ")");
            txtBusinessOwner.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator15.ClientID + ")");
            txtSupportTeam.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator17.ClientID + ")");
            ddlApplicationFunctions.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator13.ClientID + ")");
            txtHeavyLoadTimes.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator18.ClientID + ")");
            txtCriticalTimes.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator19.ClientID + ")");
            txtBusinessRepresentatives.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator20.ClientID + ")");
            txtLocationofBusiness.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator21.ClientID + ")");
            txtMethods.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator22.ClientID + ")");
            txtMaxPeriodofDowntime.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator23.ClientID + ")");
            ddlBriefOverviewofImpact.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator36.ClientID + ")");
            txtNone.Attributes.Add("onblur", "ValidatorValidate(" + rfvNone.ClientID + ")");
            txtG24Hours.Attributes.Add("onblur", "ValidatorValidate(" + rfvGHours.ClientID + ")");
            txtL24Hours.Attributes.Add("onblur", "ValidatorValidate(" + rfvLHours.ClientID + ")");
            txtMinimal.Attributes.Add("onblur", "ValidatorValidate(" + RfvMini.ClientID + ")");
            txtAll.Attributes.Add("onblur", "ValidatorValidate(" + rfvAll.ClientID + ")");
            txtFinancialHours.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator24.ClientID + ")");
            txtRegulatoryHours.Attributes.Add("onblur", "ValidatorValidate(" + txtRegulatoryHours.ClientID + ")");
            ddlCustomerRetention.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator37.ClientID + ")");
            txtCustomerRetentionHours.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator26.ClientID + ")");
            ddlBrandNameReputation.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator26.ClientID + ")");
            txtBrandHMI.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator27.ClientID + ")");
            ddlOperationalEffect.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator39.ClientID + ")");
            txtOperationalEffect.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator28.ClientID + ")");
            ddlManagementControl.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator40.ClientID + ")");
            txtManagementControlHours.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator29.ClientID + ")");
            txtCompliance.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator30.ClientID + ")");
            txtComplianceHours.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator31.ClientID + ")");
            txtLicence.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator32.ClientID + ")");
            txtLicenceHours.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator33.ClientID + ")");
            txtCreditFraud.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator34.ClientID + ")");
            txtCreditFraudHours.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator35.ClientID + ")");

            Utility.SelectMenu(Master, "Module2");

            if (!IsPostBack)
            {
                _finaldependency.Clear();
                _foutwarddependency.Clear();
                _finfradependency.Clear();
                _fExternalInwardsdependency.Clear();
                _fExternalOutwardsdependency.Clear();
                _fBusinessFunction.Clear();
                _editUserFunction.Clear();

                if (CurrentApplicationGroupId == 0)
                {
                    Utility.PopulateApplicationGroup(ddlApplicationGroupName, true);
                    IsEdit = "N";

                    string str = Request.QueryString["C"];
                    if (str == "S")
                    {
                        //   ulMessage.Visible = true;
                        //   lblMessage.Text = "Your Impact Analysis Save Successfully";
                    }
                    _fBusinessFunction = new List<BusinessUserFunction> { new BusinessUserFunction { SupportField = "Marketing", Tier1A = 0, Tier1B = 0, Tier2 = 0, Tier3 = 0, Tier4 = 0 }, new BusinessUserFunction { SupportField = "Customer Service", Tier1A = 0, Tier1B = 0, Tier2 = 0, Tier3 = 0, Tier4 = 0 }, new BusinessUserFunction { SupportField = "Credit and Collection", Tier1A = 0, Tier1B = 0, Tier2 = 0, Tier3 = 0, Tier4 = 0 }, new BusinessUserFunction { SupportField = "Revenue Assurance", Tier1A = 0, Tier1B = 0, Tier2 = 0, Tier3 = 0, Tier4 = 0 }, new BusinessUserFunction { SupportField = "Network", Tier1A = 0, Tier1B = 0, Tier2 = 0, Tier3 = 0, Tier4 = 0 } };
                }
                else
                {
                    EditBindData();
                }
            }
            else
            {
                //    ulMessage.Visible = false;
                //     lblMessage.Text = "";
            }
        }

        protected bool IsCheckedTier1a(object val)
        {
            bool isResult = false;
            bool isValue = Convert.ToBoolean(val);
            foreach (ListViewDataItem lvbufuncation in lvBUFunction.Items)
            {
                CheckBox chk1 = (CheckBox)lvbufuncation.FindControl("chkTier1avalue");

                if (isValue)
                {
                    chk1.Checked = true;
                    return isResult = true;
                }
            }
            return isResult;
        }

        protected bool IsCheckedTier1b(object val)
        {
            bool isResult = false;
            bool isValue = Convert.ToBoolean(val);

            if (isValue)
            {
                return isResult = true;
            }
            return isResult;
        }

        protected bool IsCheckedTier2(object val)
        {
            bool isResult = false;
            bool isValue = Convert.ToBoolean(val);

            if (isValue)
            {
                return isResult = true;
            }

            return isResult;
        }

        protected bool IsCheckedTier3(object val)
        {
            bool isResult = false;
            bool isValue = Convert.ToBoolean(val);

            if (isValue)
            {
                return isResult = true;
            }

            return isResult;
        }

        protected bool IsCheckedTier4(object val)
        {
            bool isResult = false;
            bool isValue = Convert.ToBoolean(val);

            if (isValue)
            {
                return isResult = true;
            }

            return isResult;
        }

        private void EditBindData()
        {
            IsEdit = "Y";
            Utility.PopulateApplicationGroup(ddlApplicationGroupName, true);

            var appGroup = Facade.GetApplicationGroupInfoById(CurrentApplicationGroupId);
            var businessInfo = Facade.GetBusinessInfoByBusinessServiceId(CurrentApplicationGroupId);
            var userFunction = Facade.GetBusinessUserFunctionsByBusinessServiceId(CurrentApplicationGroupId);
            var applicationid = Facade.GetApplicationDependenciesByBusinessServiceId(CurrentApplicationGroupId);

            if (businessInfo != null && userFunction != null && appGroup != null)
            {
                ddlApplicationGroupName.SelectedValue = appGroup.ApplicationGroupId.ToString();

                txtBusinessOwner.Text = appGroup.BusinessOwner;
                txtAlternative.Text = appGroup.AlternativeNames;
                txtSupportTeam.Text = appGroup.SupportTeam;
                ddlApplicationFunctions.SelectedValue = appGroup.ApplicationFunctions.ToString();
                txtHeavyLoadTimes.Text = appGroup.HeavyLoadTimes;
                txtCriticalTimes.Text = appGroup.CriticalTimes;

                txtBusinessRepresentatives.Text = businessInfo.Name;
                txtLocationofBusiness.Text = businessInfo.Location;
                ddlBriefOverviewofImpact.SelectedValue = businessInfo.BriefOverview.ToString();
                txtMethods.Text = businessInfo.Methods;
                rdBCPAvailability.SelectedValue = businessInfo.BCPAvailability.ToString();
                txtMaxPeriodofDowntime.Text = businessInfo.MaximumDowntime;
                txtNone.Text = businessInfo.DRNone;
                if (txtNone.Text != string.Empty)
                {
                    chkNone.Checked = true;
                    txtNone.Enabled = true;
                }
                txtL24Hours.Text = businessInfo.DR24Hrs;
                if (txtL24Hours.Text != string.Empty)
                {
                    chkL24Hours.Checked = true;
                    txtL24Hours.Enabled = true;
                }
                txtG24Hours.Text = businessInfo.DRGreator24;
                if (txtG24Hours.Text != string.Empty)
                {
                    chkG24Hours.Checked = true;
                    chkG24Hours.Enabled = true;
                }
                txtMinimal.Text = businessInfo.DRMinimal;
                if (txtMinimal.Text != string.Empty)
                {
                    chkMinimal.Checked = true;
                    txtMinimal.Enabled = true;
                }
                txtAll.Text = businessInfo.DRAll;
                if (txtAll.Text != string.Empty)
                {
                    chkAll.Checked = true;
                    txtAll.Enabled = true;
                }
                rblFinancialLoss.SelectedValue = businessInfo.FinancialLossProfit.ToString();
                txtFinancialHours.Text = businessInfo.FinancialHMI;
                rblRegulatoryImpact.SelectedValue = businessInfo.RegulatoryImpact.ToString();
                txtRegulatoryHours.Text = businessInfo.RegulatoryHMI;
                ddlCustomerRetention.SelectedValue = businessInfo.CustomerRetension.ToString();
                txtCustomerRetentionHours.Text = businessInfo.CustomerHMI;
                ddlBrandNameReputation.SelectedValue = businessInfo.BrandNameReputation.ToString();
                txtBrandHMI.Text = businessInfo.BrandNameHMI;
                ddlOperationalEffect.SelectedValue = businessInfo.OperationalEffect.ToString();
                txtOperationalEffect.Text = businessInfo.OperationalHMI;
                ddlManagementControl.SelectedValue = businessInfo.ManagementControl.ToString();
                txtManagementControlHours.Text = businessInfo.ManagementHMI;
                txtCompliance.Text = businessInfo.ComplianceImpact;
                txtComplianceHours.Text = businessInfo.ComplianceHMI;
                txtLicence.Text = businessInfo.Licence;
                txtLicenceHours.Text = businessInfo.LicenceHMI;
                txtCreditFraud.Text = businessInfo.CreditFraud;
                txtCreditFraudHours.Text = businessInfo.CreditHMI;

                lvBUFunction.DataSource = userFunction;
                lvBUFunction.DataBind();

                foreach (BusinessUserFunction buf in userFunction)
                {
                    var buftemp = new BusinessUserFunction();
                    buftemp.BusinessServiceId = buf.BusinessServiceId;
                    buftemp.BusinessInfoId = buf.BusinessInfoId;
                    buftemp.Id = buf.Id;
                    buftemp.SupportField = buf.SupportField;
                    buftemp.Tier1A = buf.Tier1A;
                    buftemp.Tier1B = buf.Tier1B;
                    buftemp.Tier2 = buf.Tier2;
                    buftemp.Tier3 = buf.Tier3;
                    buftemp.Tier4 = buf.Tier4;
                    _fBusinessFunction.Add(buftemp);
                }
                if (applicationid != null)
                {
                    foreach (ApplicationDependency appdep in applicationid)
                    {
                        if (appdep.DependancyType == (int)AppDependency.InwardDataFlow)
                        {
                            var ubind = new ApplicationDependency();
                            ubind.Id = appdep.Id;
                            ubind.BusinessInfoId = appdep.BusinessInfoId;
                            ubind.DependancyType = (int)AppDependency.InwardDataFlow;
                            ubind.ServerId = appdep.ServerId;
                            ubind.DatabaseId = appdep.DatabaseId;
                            ubind.Description = appdep.Description;
                            ubind.DRCategory = appdep.DRCategory;
                            _finaldependency.Add(ubind);
                        }
                        if (appdep.DependancyType == (int)AppDependency.OutwardDataFlow)
                        {
                            var ubindout = new ApplicationDependency();
                            ubindout.Id = appdep.Id;
                            ubindout.BusinessInfoId = appdep.BusinessInfoId;
                            ubindout.DependancyType = (int)AppDependency.OutwardDataFlow;
                            ubindout.ServerId = appdep.ServerId;
                            ubindout.DatabaseId = appdep.DatabaseId;
                            ubindout.Description = appdep.Description;
                            ubindout.DRCategory = appdep.DRCategory;
                            _foutwarddependency.Add(ubindout);
                        }
                        if (appdep.DependancyType == (int)AppDependency.Infrastructure)
                        {
                            var ubindInfra = new ApplicationDependency();
                            ubindInfra.Id = appdep.Id;
                            ubindInfra.BusinessInfoId = appdep.BusinessInfoId;
                            ubindInfra.DependancyType = (int)AppDependency.Infrastructure;
                            ubindInfra.ServerId = appdep.ServerId;
                            ubindInfra.DatabaseId = appdep.DatabaseId;
                            ubindInfra.Description = appdep.Description;
                            ubindInfra.DRCategory = appdep.DRCategory;
                            _finfradependency.Add(ubindInfra);
                        }
                        if (appdep.DependancyType == (int)AppDependency.ExternalInwardDataFlow)
                        {
                            var ubindExternalInward = new ApplicationDependency();
                            ubindExternalInward.Id = appdep.Id;
                            ubindExternalInward.BusinessInfoId = appdep.BusinessInfoId;
                            ubindExternalInward.DependancyType = (int)AppDependency.ExternalInwardDataFlow;
                            ubindExternalInward.ServerId = appdep.ServerId;
                            ubindExternalInward.DatabaseId = appdep.DatabaseId;
                            ubindExternalInward.Description = appdep.Description;
                            ubindExternalInward.DRCategory = appdep.DRCategory;
                            _fExternalInwardsdependency.Add(ubindExternalInward);
                        }
                        if (appdep.DependancyType == (int)AppDependency.ExternalOutwardDataFlow)
                        {
                            var ubindExternalOutward = new ApplicationDependency();
                            ubindExternalOutward.Id = appdep.Id;
                            ubindExternalOutward.BusinessInfoId = appdep.BusinessInfoId;
                            ubindExternalOutward.DependancyType = (int)AppDependency.ExternalOutwardDataFlow;
                            ubindExternalOutward.ServerId = appdep.ServerId;
                            ubindExternalOutward.DatabaseId = appdep.DatabaseId;
                            ubindExternalOutward.Description = appdep.Description;
                            ubindExternalOutward.DRCategory = appdep.DRCategory;
                            _fExternalOutwardsdependency.Add(ubindExternalOutward);
                        }
                    }
                }
                lvInward.DataSource = _finaldependency;
                lvInward.DataBind();

                lvOutward.DataSource = _foutwarddependency;
                lvOutward.DataBind();

                lvInfrastructure.DataSource = _finfradependency;
                lvInfrastructure.DataBind();

                LvExternalInward.DataSource = _fExternalInwardsdependency;
                LvExternalInward.DataBind();

                lvExternalOutward.DataSource = _fExternalOutwardsdependency;
                lvExternalOutward.DataBind();
            }
        }

        private void GetAppDesc()
        {
            ApplicationGroupInfo appgrpInfo = new ApplicationGroupInfo();
            appgrpInfo.ApplicationGroupId = ddlApplicationGroupName.SelectedValue.ToInteger();
            appgrpInfo.AlternativeNames = txtAlternative.Text;
            appgrpInfo.BusinessOwner = txtBusinessOwner.Text;
            appgrpInfo.SupportTeam = txtSupportTeam.Text;
            appgrpInfo.ApplicationFunctions = ddlApplicationFunctions.SelectedValue;
            appgrpInfo.HeavyLoadTimes = txtHeavyLoadTimes.Text;
            appgrpInfo.CriticalTimes = txtCriticalTimes.Text;
            appgrpInfo.CreatorId = LoggedInUserId;

            _fApplicationGroupInfo.Add(appgrpInfo);
        }

        private void GetBusinessInfo()
        {
            _fBusinessInfo.Clear();
            BusinessInfo businessInfo = new BusinessInfo();
            BusinessInfo CreateInfo = new BusinessInfo();

            businessInfo.BusinessServiceId = Convert.ToInt32(ddlApplicationGroupName.SelectedValue);

            businessInfo.Name = txtBusinessRepresentatives.Text;
            businessInfo.Location = txtLocationofBusiness.Text;
            businessInfo.BriefOverview = ddlBriefOverviewofImpact.SelectedValue.ToInteger();
            businessInfo.Methods = txtMethods.Text;
            businessInfo.BCPAvailability = Convert.ToInt32(rdBCPAvailability.SelectedValue);
            businessInfo.MaximumDowntime = txtMaxPeriodofDowntime.Text;
            businessInfo.DRNone = txtNone.Text;
            businessInfo.DR24Hrs = txtL24Hours.Text;
            businessInfo.DRGreator24 = txtG24Hours.Text;
            businessInfo.DRMinimal = txtMinimal.Text;
            businessInfo.DRAll = txtAll.Text;
            businessInfo.FinancialLossProfit = Convert.ToInt32(rblFinancialLoss.SelectedValue);
            businessInfo.FinancialHMI = txtFinancialHours.Text;
            businessInfo.RegulatoryImpact = Convert.ToInt32(rblRegulatoryImpact.SelectedValue);
            businessInfo.RegulatoryHMI = txtRegulatoryHours.Text;
            businessInfo.CustomerRetension = Convert.ToInt32(ddlCustomerRetention.SelectedValue);
            businessInfo.CustomerHMI = txtCustomerRetentionHours.Text;
            businessInfo.BrandNameReputation = Convert.ToInt32(ddlBrandNameReputation.SelectedValue);
            businessInfo.BrandNameHMI = txtBrandHMI.Text;
            businessInfo.OperationalEffect = Convert.ToInt32(ddlOperationalEffect.SelectedValue);
            businessInfo.OperationalHMI = txtOperationalEffect.Text;
            businessInfo.ManagementControl = Convert.ToInt32(ddlManagementControl.SelectedValue);
            businessInfo.ManagementHMI = txtManagementControlHours.Text;
            businessInfo.ComplianceImpact = txtCompliance.Text;
            businessInfo.ComplianceHMI = txtComplianceHours.Text;
            businessInfo.Licence = txtLicence.Text;
            businessInfo.LicenceHMI = txtLicenceHours.Text;
            businessInfo.CreditFraud = txtCreditFraud.Text;
            businessInfo.CreditHMI = txtCreditFraudHours.Text;
            businessInfo.CreatorId = LoggedInUserId;

            _fBusinessInfo.Add(businessInfo);
        }

        protected string ConvertServerName(object id)
        {
            int serverid = (int)id;
            var servername = Facade.GetServerById(serverid);
            return servername.Name;
        }

        protected string ConvertDatabaseName(object id)
        {
            int databaseid = (int)id;
            var dbid = Facade.GetDatabaseBaseById(databaseid);
            return dbid.Name;
        }

        #endregion Methods

        protected void OnStepChange(object sender, EventArgs e)
        {
            var currentStep = Wizard1.ActiveStepIndex;
            Repeater SideBarList = Wizard1.FindControl("HeaderContainer").FindControl("SideBarList") as Repeater;
            if (SideBarList != null)
            {
                //var liselected = SideBarList.Items.ToString("li" + Wizard1.ActiveStepIndex);
                //liselected.Attributes.Add("class","active");
            }
        }

        protected void Wizard1_PreRender(object sender, EventArgs e)
        {
            var SideBarList = Wizard1.FindControl("HeaderContainer").FindControl("SideBarList") as Repeater;
            SideBarList.DataSource = Wizard1.WizardSteps;
            SideBarList.DataBind();

            TotalSteps = SideBarList.Items.Count.ToString();
        }

        public string GetItemID(string sItemIDPrefix, object oIndex)
        {
            int iIndex = Convert.ToInt32(oIndex.ToString());
            return sItemIDPrefix + (iIndex);
        }

        public string GetClassForWizardStep(object wizardStep)
        {
            CurrentStep = (Convert.ToInt32(Wizard1.ActiveStepIndex) + 1).ToString();
            CompletedSteps = (Convert.ToInt32(Wizard1.ActiveStepIndex)).ToString();
            WizardStep step = wizardStep as WizardStep;

            if (step == null)
            {
                return "";
            }

            int stepIndex = Wizard1.WizardSteps.IndexOf(step);

            if (stepIndex < Wizard1.ActiveStepIndex)
            {
                return "primary";//stepCompleted
            }
            if (stepIndex > Wizard1.ActiveStepIndex)
            {
                return "";//stepNotCompleted
            }
            return "active";
        }
    }
}