﻿using System.Linq;
using <PERSON><PERSON>Helper;

namespace CP.UI
{
    public partial class SystemError : BasePage
    {
        public override void PrepareView()
        {
            if (WebHelper.CurrentSession.CpExceptionInfo.Message != null)
            {
                string errormsg = WebHelper.CurrentSession.CpExceptionInfo.Message;

                string[] error = errormsg.Split('*');

                if (error.Count() == 2)
                {
                    lblErrorDescription.Text = error[0];

                    lblUserDescription.Text = error[1];
                }

                WebHelper.CurrentSession.CpExceptionInfo.Message = null;
            }
            else if (Message.IsNotNullOrEmpty())
            {
                lblErrorDescription.Text = Message;
            }
        }
    }
}