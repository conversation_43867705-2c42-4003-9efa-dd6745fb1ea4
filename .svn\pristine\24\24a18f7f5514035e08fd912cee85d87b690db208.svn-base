﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="MonitoringSummery.ascx.cs" Inherits="CP.UI.Controls.MonitoringSummery" %>


<table class="table font no-bottom-margin dynamicTable tableTools table table-striped table-bordered table-condensed table-white" width="100%" style="table-layout: fixed;">
    <tbody>
        <tr>
            <th></th>
            <td>PR</td>
            <td>DR</td>
        </tr>
         <tr>
            <th>Compliance Summery</th>
            <td>Installed Patches(
                 <asp:Label ID="ab1" runat="server"></asp:Label>)
            </td>
            <td>Not Installed Patches(
                 <asp:Label ID="Label1" runat="server"></asp:Label>)<br />
                <asp:Label ID="lbl2" runat="server"> name 1 </asp:Label>
            </td>
        </tr>
         <tr>
            <th>Server Utilization</th>
            <td>CPU Load(
                 <asp:Label ID="Label2" runat="server"></asp:Label>) % <br />
               Free Memory(
                 <asp:Label ID="Label3" runat="server"></asp:Label>) MB
            </td>
            <td>CPU Load(
                 <asp:Label ID="Label4" runat="server"></asp:Label>) % <br />
               Free Memory(
                 <asp:Label ID="Label5" runat="server"></asp:Label>) MB
            </td>
        </tr>
         <tr>
            <th>User Level Security Matrix</th>
            <td> User Name  </td>
             <td> User Name  </td>
        </tr>

         <tr>
            <th>SYS Time  </th>
            <td> <asp:Label ID="Label6" runat="server">Time</asp:Label>  </td>
             <td> <asp:Label ID="Label7" runat="server">Time</asp:Label>  </td>
        </tr>

        <tr>
            <th> Firewall  </th>
            <td> <asp:Label ID="Label8" runat="server">Time</asp:Label>  </td>
             <td> <asp:Label ID="Label9" runat="server">Time</asp:Label>  </td>
        </tr>
    </tbody>
</table>
