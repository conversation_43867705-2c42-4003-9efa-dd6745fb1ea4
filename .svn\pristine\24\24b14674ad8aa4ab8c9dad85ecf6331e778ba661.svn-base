﻿using System;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;

namespace CP.UI.Code.Replication.Component
{
    public class Postgre9xComponentMonitor:IComponentInfo
    {
        private readonly IFacade _facade = new Facade();
        private ComponentInfo _componentInfo;

        public ComponentInfo CurrentComponent
        {
            get { return _componentInfo ?? (_componentInfo = new ComponentInfo()); }
        }

        ComponentInfo IComponentInfo.GetComponentInformation(int infraobjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId)
        {
            _componentInfo = null;

            GetServerInformation(prServerId, true);

            GetServerInformation(drServerId, false);

            GetDatabaseInformation(prDatabaseId, true);

            GetDatabaseInformation(drDatabaseId, false);

            GetDataLagInformation(infraobjectId);

            return CurrentComponent;
        }

        ComponentInfo IComponentInfo.GetComponentInformation(int infraobjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId, int mailBoxId, string mailboxname)
        {
            _componentInfo = null;
            return CurrentComponent;
        }


        private void GetDataLagInformation(int infraObjectId)
        {
            try
            {
                var datalag = _facade.Postgre9xComponentMonitorByInfraId(infraObjectId);

                if (datalag != null)
                {
                    CurrentComponent.PRDBClusterState = datalag.DBClusterStatusPR;
                    CurrentComponent.DRDBClusterState = datalag.DBClusterStatusDR;
                    CurrentComponent.PRDBRecoveryState = "N/A";
                    CurrentComponent.DRDBRecoveryState = datalag.DBRecoveryStatusDR;
                }
                else
                {
                    CurrentComponent.PRDBClusterState = "N/A";
                    CurrentComponent.DRDBClusterState = "N/A";
                    CurrentComponent.PRDBRecoveryState = "N/A";
                    CurrentComponent.DRDBRecoveryState = "N/A";
                }
            }
            catch (Exception)
            {
                CurrentComponent.PRDBClusterState = "N/A";
                CurrentComponent.DRDBClusterState = "N/A";
                CurrentComponent.PRDBRecoveryState = "N/A";
                CurrentComponent.DRDBRecoveryState = "N/A";
            }
        }

        private void GetDatabaseInformation(int databaseId, bool isPrimary)
        {
            try
            {
                var database = _facade.GetDatabaseBaseById(databaseId);
                var DatabaseName =_facade.GetDatabasePostgre9xByDatabaseBaseId(databaseId).DatabaseName;

                if (database != null)
                {
                   // BindDatabaseComponents(database, isPrimary);
                    BindDatabaseComponents(database, isPrimary, DatabaseName);
                }
                else
                {
                    //BindNullDatabaseComponents(isPrimary);
                    BindDatabaseComponents(database, isPrimary, DatabaseName);
                }
            }
            catch (Exception)
            {
                BindNullDatabaseComponents(isPrimary);
            }
        }

        private void BindNullDatabaseComponents(bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = "N/A";
                CurrentComponent.PRDatabaseMode = "Down";
            }
            else
            {
                CurrentComponent.DRDatabaseName = "N/A";
                CurrentComponent.DRDatabaseMode = "Down";
            }
        }

        private void BindDatabaseComponents(DatabaseBase database, bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = database.Name;
                CurrentComponent.PRDatabaseMode = database.Mode.ToString();
            }
            else
            {
                CurrentComponent.DRDatabaseName = database.Name;
                CurrentComponent.DRDatabaseMode = database.Mode.ToString();
            }
        }

        private void BindDatabaseComponents(DatabaseBase database, bool isPrimary ,string DatabaseName)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = DatabaseName;
                CurrentComponent.PRDatabaseMode = database.Mode.ToString();
            }
            else
            {
                CurrentComponent.DRDatabaseName = DatabaseName;
                CurrentComponent.DRDatabaseMode = database.Mode.ToString();
            }
        }

        private void BindNullServerComponents(bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = "N/A";
                CurrentComponent.PRServerIP = "N/A";
                CurrentComponent.PRServerOSType = "N/A";
                CurrentComponent.PRServerStatus = "Down";
            }
            else
            {
                CurrentComponent.DRServerName = "N/A";
                CurrentComponent.DRServerIP = "N/A";
                CurrentComponent.DRServerOSType = "N/A";
                CurrentComponent.DRServerStatus = "Down";
            }
        }

        private void BindServerComponents(Server server, bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = server.Name;
                CurrentComponent.PRServerIP = server.IPAddress;
                CurrentComponent.PRServerOSType = server.OSType;
                CurrentComponent.PRServerStatus = server.Status.ToString();
            }
            else
            {
                CurrentComponent.DRServerName = server.Name;
                CurrentComponent.DRServerIP = server.IPAddress;
                CurrentComponent.DRServerOSType = server.OSType;
                CurrentComponent.DRServerStatus = server.Status.ToString();
            }
        }

        private void GetServerInformation(int serverId, bool isPrimary)
        {
            try
            {
                var server = _facade.GetServerById(serverId);

                if (server != null)
                {
                    BindServerComponents(server, isPrimary);
                }
                else
                {
                    BindNullServerComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullServerComponents(isPrimary);
            }
        }
    }
}