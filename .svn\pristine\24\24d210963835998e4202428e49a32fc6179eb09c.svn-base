﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class HACMPClusterDataAccess : BaseDataAccess, IHACMPClusterDataAccess
    {
        #region Constructors

        public HACMPClusterDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<HACMPCluster> CreateEntityBuilder<HACMPCluster>()
        {
            return (new HACMPClusterBuilder()) as IEntityBuilder<HACMPCluster>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="HACMPCluster" /> into HACMPCluster table.
        /// </summary>
        /// <param name="hacmpCluster">Insert HACMPCluster Details</param>
        /// <returns>HACMPCluster</returns>
        /// <author><PERSON><PERSON></author>
        HACMPCluster IHACMPClusterDataAccess.Add(HACMPCluster hacmpCluster)
        {
            try
            {
                const string sp = "HACMPCluster_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, hacmpCluster.Name);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, hacmpCluster.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iLSSRCPath", DbType.AnsiString, hacmpCluster.LSSRCPath);
                    Database.AddInParameter(cmd, Dbstring + "iCLRGInfoPath", DbType.AnsiString, hacmpCluster.CLRGInfoPath);
                    Database.AddInParameter(cmd, Dbstring + "iResourceGroupName", DbType.AnsiString, hacmpCluster.ResourceGroupName);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, hacmpCluster.CreatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        hacmpCluster = reader.Read()
                            ? CreateEntityBuilder<HACMPCluster>().BuildEntity(reader, hacmpCluster)
                            : null;
                    }

                    if (hacmpCluster == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "hacmpCluster already exists. Please specify another hacmpCluster.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this hacmpCluster.");
                                }
                        }
                    }

                    return hacmpCluster;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting hacmpCluster Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="HACMPCluster" /> from HACMPCluster table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>HACMPCluster</returns>
        /// <author>Uma Mehavarnan</author>
        HACMPCluster IHACMPClusterDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "HACMPCluster_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<HACMPCluster>()).BuildEntity(reader, new HACMPCluster())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IHACMPClusterDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
             
        /// <summary>
        ///     Delete <see cref="HACMPCluster" /> from HACMPCluster table by id
        /// </summary>
        /// <returns>bool</returns>
        /// <author>Uma Mehavarnan</author>
        bool IHACMPClusterDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "HACMPCluster_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);
                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    if (returnCode == -1)
                        return true;
#endif
                    //return returnCode > 0;
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting HACMPCluster Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        ///// <summary>
        ///// Update <see cref="HACMPCluster"/> HACMPCluster table.
        ///// </summary>
        ///// <param name="hacmpCluster">Update HACMPCluster Details</param>
        ///// <returns>HACMPCluster</returns>
        ///// <author>Uma Mehavarnan</author>
        HACMPCluster IHACMPClusterDataAccess.Update(HACMPCluster hacmpCluster)
        {
            try
            {
                const string sp = "HACMPCluster_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, hacmpCluster.Id);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, hacmpCluster.Name);
                    Database.AddInParameter(cmd, Dbstring + "iServerId", DbType.Int32, hacmpCluster.ServerId);
                    Database.AddInParameter(cmd, Dbstring + "iLSSRCPath", DbType.AnsiString, hacmpCluster.LSSRCPath);
                    Database.AddInParameter(cmd, Dbstring + "iCLRGInfoPath", DbType.AnsiString, hacmpCluster.CLRGInfoPath);
                    Database.AddInParameter(cmd, Dbstring + "iResourceGroupName", DbType.AnsiString, hacmpCluster.ResourceGroupName);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, hacmpCluster.CreatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        hacmpCluster = reader.Read()
                            ? CreateEntityBuilder<HACMPCluster>().BuildEntity(reader, hacmpCluster)
                            : null;
                    }

                    if (hacmpCluster == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "HACMPCluster already exists. Please specify another hacmpCluster.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this hacmpCluster.");
                                }
                        }
                    }

                    return hacmpCluster;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While updating HACMPCluster Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        /// <summary>
        ///     Get <see cref="HACMPCluster" /> from HACMPCluster table
        /// </summary>
        /// <param></param>
        /// <returns>HACMPCluster List</returns>
        /// <author>Uma Mehavarnan</author>
        IList<HACMPCluster> IHACMPClusterDataAccess.GetAll()
        {
            try
            {
                const string sp = "HACMPCluster_GetALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<HACMPCluster>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IHACMPClusterDataAccess.GetAll()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool IHACMPClusterDataAccess.IsExistHACMPClusterByName(string name)
        {
            try
            {
                const string sp = "HACMPCluster_ISEXISTBYNAME";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while Creating HACMP Cluster.");
                            }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IHACMPClusterDataAccess.IsExistHACMPClusterByName (" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}