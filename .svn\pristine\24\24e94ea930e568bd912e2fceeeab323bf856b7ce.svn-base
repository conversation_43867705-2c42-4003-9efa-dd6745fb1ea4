﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI
{
    public partial class CIODashboard : BasePage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                //DownBusinessServices1.GetDRReadyCount();
            }
        }

        public override void PrepareView()
        { }
    }
}