﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "SiteInchargeInfo", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class SiteInchargeInfo : BaseEntity
    {
        #region Properties

        [DataMember]
        public int SiteId { get; set; }

        [DataMember]
        public string InchargeName { get; set; }

        [DataMember]
        public string Address { get; set; }

        [DataMember]
        public string Mobile { get; set; }

        [DataMember]
        public string Email { get; set; }

        #endregion Properties
    }
}