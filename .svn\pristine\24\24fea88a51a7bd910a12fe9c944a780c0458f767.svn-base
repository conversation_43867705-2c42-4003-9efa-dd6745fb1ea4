﻿using CP.BusinessFacade;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.ImpactAnalysis
{
    public partial class ImpactMasterList : ImpactMasterBasePage
    {

        #region variable

        public static string CurrentURL = Constants.UrlConstants.Urls.ImpactAnalysis.ImpactMasterConfiguration;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.ImpactAnalysis.ImpactMasterList;
                }
                return string.Empty;
            }
        }

        #endregion variable

        public override void PrepareView()
        {
            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }

            Utility.SelectMenu(Master, "Module3");
            BindList();
        }

        private void BindList()
        {
            setListViewPage();
            lvComponent.DataSource = Facade.GetAllImpactMaster();
            lvComponent.DataBind();
        }

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageImpactMasterList"]) != -1) && Session["CurrentPageImpactMasterList"] != null && (Convert.ToInt32(Session["CurrentPageImpactMasterList"]) > 0))
            {
                if (Session["TotalPageRowsCount"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageImpactMasterList"]) == Convert.ToInt32(Session["TotalPageRowsCount"]) - 1)
                    {
                        Session["CurrentPageImpactMasterList"] = Convert.ToInt32(Session["CurrentPageImpactMasterList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCount"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageImpactMasterList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageImpactMasterList"] = -1;

            }
        }

        protected void LvComponentItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageImpactMasterList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCount"] = dataPager1.TotalRowCount;
                var lbl = (lvComponent.Items[e.ItemIndex].FindControl("Id")) as Label;
                var lblName = (lvComponent.Items[e.ItemIndex].FindControl("ipmName")) as Label;
                if (lbl != null)
                {
                        Facade.DeleteImpactMasterById(Convert.ToInt32(lbl.Text));

                        ActivityLogger.AddLog(LoggedInUserName, "Server", UserActionType.DeleteImpactMaster, "The ImpactMaster '" + lblName.Text + "' was deleted", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("ImpactMaster" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(ReturnUrl));
            }
        }

        protected void LvComponentItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageImpactMasterList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvComponent.Items[e.NewEditIndex].FindControl("Id")) as Label;
            if (lbl1 != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ImpactMasterId,
                                                     lbl1.Text);
            }

            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvComponent_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected void LvComponentPreRender(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                lvComponent.DataSource = Facade.GetAllImpactMaster();
                lvComponent.DataBind();
            }
        }


    }
}