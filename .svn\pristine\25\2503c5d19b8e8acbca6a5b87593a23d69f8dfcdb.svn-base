﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "FastCopy", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class FastCopy : BaseEntity
    {
        #region Member Variables

        private ReplicationBase _basereplication = new ReplicationBase();

        #endregion Member Variables

        #region properties

        [DataMember]
        public int ReplicationId { get; set; }

        [DataMember]
        public bool IsCompression { get; set; }

        [DataMember]
        public bool IsFilter { get; set; }

        [DataMember]
        public string Wildcard { get; set; }

        [DataMember]
        public string DataSyncPath { get; set; }

        [DataMember]
        public string ProcessCode { get; set; }

        [DataMember]
        public string LocalDirectory { get; set; }

        [DataMember]
        public string RemoteDirectory { get; set; }

        [DataMember]
        public string OSPlatform { get; set; }

        [DataMember]
        public string DataSyncJREPath { get; set; }

        [DataMember]
        public FastCopyMode Mode { get; set; }

        [DataMember]
        public string ScheduleTime { get; set; }

        [DataMember]
        public int LastReplicationCount { get; set; }

        [DataMember]
        public string Datalag { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _basereplication; }
            set { _basereplication = value; }
        }
        [DataMember]
        public bool IsExcMultiInstance { get; set; }

        #endregion properties
    }
}