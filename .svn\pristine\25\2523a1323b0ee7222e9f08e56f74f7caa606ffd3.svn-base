﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IBusinessServiceDataAccess

    public interface IBusinessServiceRPOInfoDataAccess
    {
        IList<BusinessServiceRPOInfo> GetById(int businessServiceId);

        BusinessServiceRPOInfo GetRPOByInfraObjectId(int InfraObjectId, int iBusinessFunctionId);
        int GetServiceBreachCount(int day,int month,int year);

        int GetServicBreachCountBymonth(int strmonth);
        int GetFunctionBreachCountBymonth(int strmonth);

        IList<BusinessServiceRPOInfo> GetBSRPOLog_CurrentRPOInfo();

        IList<BusinessServiceRPOInfo> GetAllCountByYear(string Type);

        IList<BusinessServiceRPOInfo> GetAllCountByMonthNYear(string Type ,int month);

    }

    #endregion IBusinessServiceDataAccess
}