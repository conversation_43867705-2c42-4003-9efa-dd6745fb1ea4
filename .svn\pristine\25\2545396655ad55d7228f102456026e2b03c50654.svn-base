﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IDatabaseBackupInfoDataAccess
    {
        DatabaseBackupInfo UpdateSchedule(DatabaseBackupInfo db);

        DatabaseBackupInfo Add(DatabaseBackupInfo databaseBackupInfo);

        DatabaseBackupInfo Update(DatabaseBackupInfo databaseBackupInfo);

        DatabaseBackupInfo GetById(int id);

        DatabaseBackupInfo GetByName(string name);

        IList<DatabaseBackupInfo> GetAll();

        bool DeleteById(int id);

        bool IsExistByName(string name);

        DatabaseBackupInfo GetLast();
    }
}