﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace CP.UI.Code.Replication.ReplicationInfo
{
    public class EmcUnityReplicationInfo : IReplicationInfo
    {
        private readonly IFacade _facade = new Facade();

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectid)
        {
            var currentEntityType = currentEntity.GetType();

            EmcUnity_Repli_Monitor EmcUnityRep = _facade.GetEmcUnityRepliMonitorAllByInfraObjectId(infraObjectid);

            if (EmcUnityRep != null)
            {
                currentEntityType.GetProperty("EmcUnityDetails_ID").SetValue(currentEntity, EmcUnityRep.EmcUnityDetails_ID, null);
                currentEntityType.GetProperty("PRStorage").SetValue(currentEntity, EmcUnityRep.PRStorage, null);
                currentEntityType.GetProperty("DRStorage").SetValue(currentEntity, EmcUnityRep.DRStorage, null);
                currentEntityType.GetProperty("PRStorageSoftwareVersion").SetValue(currentEntity, EmcUnityRep.PRStorageSoftwareVersion, null);
                currentEntityType.GetProperty("DRStorageSoftwareVersion").SetValue(currentEntity, EmcUnityRep.DRStorageSoftwareVersion, null);
                currentEntityType.GetProperty("PRRole").SetValue(currentEntity, EmcUnityRep.PRRole, null);
                currentEntityType.GetProperty("DRRole").SetValue(currentEntity, EmcUnityRep.DRRole, null);
                currentEntityType.GetProperty("PRState").SetValue(currentEntity, EmcUnityRep.PRState, null);
                currentEntityType.GetProperty("DRState").SetValue(currentEntity, EmcUnityRep.DRState, null);
                currentEntityType.GetProperty("PRSessionType").SetValue(currentEntity, EmcUnityRep.PRSessionType, null);
                currentEntityType.GetProperty("DRSessionType").SetValue(currentEntity, EmcUnityRep.DRSessionType, null);
                currentEntityType.GetProperty("PRSynchronizationType").SetValue(currentEntity, EmcUnityRep.PRSynchronizationType, null);
                currentEntityType.GetProperty("DRSynchronizationType").SetValue(currentEntity, EmcUnityRep.DRSynchronizationType, null);
                currentEntityType.GetProperty("PRHealthdetails").SetValue(currentEntity, EmcUnityRep.PRHealthdetails, null);
                currentEntityType.GetProperty("DRHealthdetails").SetValue(currentEntity, EmcUnityRep.DRHealthdetails, null);

                currentEntityType.GetProperty("PROperationalStatus").SetValue(currentEntity, EmcUnityRep.PROperationalStatus, null);
                currentEntityType.GetProperty("DROperationalStatus").SetValue(currentEntity, EmcUnityRep.DROperationalStatus, null);
                currentEntityType.GetProperty("PRSourceStatus").SetValue(currentEntity, EmcUnityRep.PRSourceStatus, null);
                currentEntityType.GetProperty("DRSourceStatus").SetValue(currentEntity, EmcUnityRep.DRSourceStatus, null);
                currentEntityType.GetProperty("PRDestinationStatus").SetValue(currentEntity, EmcUnityRep.PRDestinationStatus, null);
                currentEntityType.GetProperty("DRDestinationStatus").SetValue(currentEntity, EmcUnityRep.DRDestinationStatus, null);
                currentEntityType.GetProperty("PRNetworkStatus").SetValue(currentEntity, EmcUnityRep.PRNetworkStatus, null);
                currentEntityType.GetProperty("DRNetworkStatus").SetValue(currentEntity, EmcUnityRep.DRNetworkStatus, null);
                currentEntityType.GetProperty("PRDestinationSystem").SetValue(currentEntity, EmcUnityRep.PRDestinationSystem, null);
                currentEntityType.GetProperty("DRDestinationSystem").SetValue(currentEntity, EmcUnityRep.DRDestinationSystem, null);
                currentEntityType.GetProperty("PR_Resource").SetValue(currentEntity, EmcUnityRep.PR_Resource, null);
                currentEntityType.GetProperty("DR_Resource").SetValue(currentEntity, EmcUnityRep.DR_Resource, null);
                currentEntityType.GetProperty("Time_of_last_sync").SetValue(currentEntity, EmcUnityRep.Time_of_last_sync, null);
                currentEntityType.GetProperty("Sync_transfer_rate").SetValue(currentEntity, EmcUnityRep.Sync_transfer_rate, null);
                currentEntityType.GetProperty("Sync_transfer_size_remaining").SetValue(currentEntity, EmcUnityRep.Sync_transfer_size_remaining, null);

            }
            else
            {

                currentEntityType.GetProperty("EmcUnityDetails_ID").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRStorage").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRStorage").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRStorageSoftwareVersion").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRStorageSoftwareVersion").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRRole").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRRole").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRState").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRState").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRSessionType").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRSessionType").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRSynchronizationType").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRSynchronizationType").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRHealthdetails").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRHealthdetails").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("PROperationalStatus").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DROperationalStatus").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRSourceStatus").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRSourceStatus").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRDestinationStatus").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRDestinationStatus").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRNetworkStatus").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRNetworkStatus").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PRDestinationSystem").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DRDestinationSystem").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("PR_Resource").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DR_Resource").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("Time_of_last_sync").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("Sync_transfer_rate").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("Sync_transfer_size_remaining").SetValue(currentEntity, "N/A", null);


            }

            return currentEntity;
       
        }

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectId, int mailBoxId, string mailboxname)
        {
            return currentEntity;
        }
    }
}