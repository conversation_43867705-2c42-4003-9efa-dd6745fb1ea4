﻿/* ===========================================================
 *             Workflow CSS 
 * ===========================================================
 *              Ram Bhomkar - UI Developer
 * ========================================================== */

#rightColumn #borderhead {
    padding: 6px;
    font-size: 100%;
    line-height: 20px;
    font-weight: bold;
    text-align: center;
}

select[id$=ddlLoadProperty] option[profile="1"], select[id$=ddlSelectedWFProperty] option[profile="1"] {
    background-image: url("../../images/icons/icon-storage.png");
    background-size: 16px 16px;
    background-repeat: no-repeat;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="2"], select[id$=ddlSelectedWFProperty] option[profile="2"] {
    background-image: url("../../images/icons/icon-database.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="3"], select[id$=ddlSelectedWFProperty] option[profile="3"] {
    background-image: url("../../images/icons/icon-os.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="4"], select[id$=ddlSelectedWFProperty] option[profile="4"] {
    background-image: url("../../images/icons/icon-vm.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="5"], select[id$=ddlSelectedWFProperty] option[profile="5"] {
    background-image: url("../../images/network_new.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="6"], select[id$=ddlSelectedWFProperty] option[profile="6"] {
    background-image: url("../../images/worflow_new.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="7"], select[id$=ddlSelectedWFProperty] option[profile="7"] {
    background-image: url("../../images/sync-hyper-v-icon.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}


select[id$=ddlLoadProperty] option[profile="8"], select[id$=ddlSelectedWFProperty] option[profile="8"] {
    background-image: url("../../images/icons/icon-web.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="9"], select[id$=ddlSelectedWFProperty] option[profile="9"] {
    background-image: url("../../images/icons/icon-file-system.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="10"], select[id$=ddlSelectedWFProperty] option[profile="10"] {
    background-image: url("../../images/icons/icon-sunsolris.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="12"], select[id$=ddlSelectedWFProperty] option[profile="12"] {
    background-image: url("../../images/icons/sync-cloud.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="13"], select[id$=ddlSelectedWFProperty] option[profile="13"] {
    background-image: url("../../images/icons/server.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 4px;
    text-indent: 18px;
}

select[id$=ddlLoadProperty] option[profile="14"], select[id$=ddlSelectedWFProperty] option[profile="14"] {
    background-image: url("../../images/replication_cc.png");
    background-repeat: no-repeat;
    background-size: 16px 16px;
    line-height: 16px;
    margin: 0px 5px;
    text-indent: 18px;
}

div.one[profile] {
    background-position: 5px 2px;
    background-size: 16px 18px;
    background-repeat: no-repeat;
}

div.one[profile="1"] {
    background-image: url("../../images/icons/icon-storage.png");
}

div.one[profile="2"] {
    background-image: url("../../images/icons/icon-database.png");
}

div.one[profile="3"] {
    background-image: url("../../images/icons/icon-os.png");
}

div.one[profile="4"] {
    background-image: url("../../images/icons/icon-vm.png");
}

div.one[profile="5"] {
    background-image: url("../../images/network_new.png");
}

div.one[profile="6"] {
    background-image: url("../../images/worflow_new.png");
}

div.one[profile="7"] {
    background-image: url("../../images/sync-hyper-v-icon.png");
    background-size: 14px 14px;
    background-position: 5px 5px;
}

div.one[profile="8"] {
    background-image: url("../../images/icons/icon-web.png");
    background-size: 16px 16px;
}

div.one[profile="9"] {
    background-image: url("../../images/icons/icon-file-system.png");
}

div.one[profile="10"] {
    background-image: url("../../images/icons/icon-sunsolris.png");
    background-size: 16px 16px;
}

div.one[profile="12"] {
    background-image: url("../../images/icons/sync-cloud.png");
    background-size: 16px 16px;
}

div.one[profile="13"] {
    background-image: url("../../images/icons/server.png");
    background-size: 16px 16px;
}

div.one[profile="14"] {
    background-image: url("../../images/replication_cc.png");
    background-size: 16px 16px;
}

.one, .diamond {
    border: 1px solid #CCCCCC;
    border-radius: 0.4em 0.4em 0.4em 0.4em !important;
    color: #333333;
    cursor: move;
    clear: both;
    color: #FFFFFF;
    font-size: 0.833em;
    height: 25px;
    margin: auto;
    overflow: hidden;
    padding: 0.3em 2em;
    /*padding: 5px 31px 5px 10px;*/
    text-align: center;
    text-indent: 12px;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 70%;
    line-height: 15px;
    position: relative;
}

.diamondtext {
    transform: rotate(-45deg) !important;
    margin-left: -15px;
    padding-top: 10px;
    color: black !important;
}

.diamondimg {
    margin-top: -10px;
}

.diamond {
    border-radius: 0 !important;
    height: 30px;
    margin: 8px;
    transform: rotate(45deg);
    width: 30px;
    background-color: #FFFFFF;
    z-index: 1;
    border: 1px solid #707070;
    padding: 0.3em 0em;
}

    .diamond .close {
        color: #000000;
        opacity: 1;
        transform: rotate(45deg);
        float: left;
        margin-left: 16px;
        margin-top: -25px;
    }

.diamond-line {
    background-color: rgb(0, 0, 0);
    height: 50px;
    width: 1px;
    margin-top: -47px;
    z-index: 0;
    position: relative;
    padding-top: 41px;
    text-indent: 6px;
    color: green;
}

.ui-sortable-helper .one {
    border: 1px solid #000 !important;
}

.one .close {
    color: #ff0000;
    margin: 0;
    opacity: 1;
    padding: 0;
    position: absolute;
    right: 3px;
    text-shadow: 0 1px 0 #FFFFFF;
    top: 8px;
    /*background-image: url(../../images/icons/remove-icon-small.png);
    background-repeat: no-repeat;*/
    font-size: 0.8em;
    width: 7px;
    height: 8px;
}

.actionboxline {
    background: url("../../images/line-workflow.png") no-repeat scroll center center rgba(0, 0, 0, 0);
    clear: both;
    height: 7px;
    text-align: center;
    width: 60%;
}

    .actionboxline img {
        margin-top: -12px;
    }

/*.actionboxline:before {
        content: "|";
        display: inline-block;
        top: -5px;
        position: relative;
    }*/

.workflowline {
    clear: both;
    height: 7px;
    text-align: center;
    width: 100%;
}

.smallboxline {
    clear: both;
    height: 7px;
    text-align: center;
    width: 100%;
}

.smallone {
    background: url("../../images/icons/clock.png") no-repeat scroll left center #808080;
    border-radius: 0.4em 0.4em 0.4em 0.4em;
    color: #FFFFFF;
    font-size: 0.833em;
    height: auto;
    margin: auto;
    padding: 0.3em 0.4em;
    text-align: center;
    width: 100%;
}

.Property {
    margin: 0;
    padding: 0;
    width: 100%;
}

.PropertySet {
    margin: 0;
    padding: 0;
    width: 100%;
}

#ulCategory li ul {
    margin: 0;
    padding-left: 16px;
    margin-bottom: 3px;
}

#ulCategory, #uluser, #ulparallelaction {
    background-color: #FFFFFF;
    border: 1px solid #C0C0C0;
    margin-left: 3px;
    padding: 5px;
    width: 90%;
    margin: 0;
}

#uluser, #ulparallelaction {
    height: 150px;
    overflow: auto;
}

ul[id^="innerUl"] {
    height: auto;
    max-height: 150px;
    overflow-x: hidden;
    overflow-y: auto;
}

    ul[id^="innerUl"] li:before {
        content: "├";
        font-size: 19px;
        margin-left: -11px;
        margin-top: -5px;
        vertical-align: middle;
        float: left;
        width: 8px;
        color: #3771a2;
    }

    ul[id^="innerUl"] li:last-child:before {
        content: "└";
    }

li[id^="mainli"] span.minus, li[id^="mainli"] span.plus, #ctl00_cphBody_ddlactionTypeBase, #ctl00_cphBody_ddlLoadProperty, #ddlExist1 {
    cursor: pointer;
}

/*li[id^="mainli"] span.plus:before
{
    color: #000000;
    content: "+";
    font-size: 1.3em;
    color: #666;
    font-weight: bold;
    cursor: pointer;
}

li[id^="mainli"] span.minus:before
{
    color: #000000;
    content: "-";
    font-size: 1.5em;
    font-weight: bold;
    color: #666;
}*/

li[id^="mainli"] ul li {
    margin-left: 7px;
    width: 100%;
}

li[id^="mainli"] > input[type="checkbox"], li[id^="mainli"] > ul > li > input[type="checkbox"] {
    margin: 1px 5px 1px 0;
    vertical-align: baseline;
}

.dragbox {
    font-size: 11px;
    font-weight: normal;
    height: 25px;
    padding-bottom: 1px;
    padding-top: 1px;
}

.workflowmenu {
    border-radius: 0.2em 0.2em 0.2em 0.2em;
    color: #fff;
    margin-top: -2px;
    padding: 5px 10px 5px 10px;
    width: 30%;
}

.menu, .menu-opener .menu-arrow {
    bottom: 0;
    /*font-family: Tahoma,Verdana,Arial,Helvetica,sans-serif;*/
    font-weight: normal;
    line-height: 1.25em;
    z-index: 99;
}

    .menu img {
        margin: 0;
        padding: 0;
    }
    /*.with-menu .menu, .menu-opener .menu-arrow {
    background: url("../../images/menu-border.png") no-repeat scroll left center / 2px 100% rgba(0, 0, 0, 0);
    width: 1.75em;
}*/
    .with-menu .menu > img, .menu-opener .menu-arrow > img {
        margin: 0 0 0 7px;
        position: absolute;
        text-align: right;
        padding-bottom: 6px;
        padding-top: 1px;
    }

    .menu ul {
        /*background: url( "../../Images/menu-bg.png" ) repeat-y scroll 0 0 #fff;*/
        background-color: #fff;
        border: 1px solid #FFFFFF;
        border-radius: 0 0.25em 0.25em 0.25em;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
        display: none;
        margin: 18px 0 0 5px;
        padding: 0.25em 0;
        position: absolute;
        text-align: left;
        width: 15em;
        z-index: 2;
    }

        .menu ul:before {
            content: "";
            background-color: #d9d0d0;
            width: 28px;
            height: 101%;
            position: absolute;
            top: -1px;
            left: -1px;
            bottom: -1px;
            border-radius: 2px 0 0 4px;
        }


.menu-opener .menu > ul {
    left: -1px;
}

.menu ul.reverted {
    border-radius: 0.25em 0 0.25em 0.25em;
}

.menu > ul.reverted {
    left: auto;
    right: 1px;
}

.menu-opener .menu > ul.reverted {
    right: -1px;
}

/*.menu:hover > ul, .menu *:hover > ul {
    display: block;
}*/

.menu:after {
    content: "˅";
    color: #fff;
    vertical-align: middle;
    cursor: pointer;
    font-size: 18px;
    padding-bottom: 5px;
    padding-left: 5px;
    padding-top: 2px;
    border-left: 1px solid #fff;
    text-shadow: 0 1px 0 #000;
}

.menu ul li ul {
    background: url( "../../images/menu-arrow.png" ) no-repeat scroll 0 0 rgba(0, 0, 0, 0);
    border: medium none;
    border-radius: 0 0 0 0;
    box-shadow: none;
    display: block;
    height: 6px;
    left: 94%;
    top: 0.6em;
    width: 4px;
}

    .menu ul li ul li {
        display: none;
    }

.menu ul li:hover > ul {
    background: url( "../../Images/menu-bg.png" ) repeat-y scroll 0 0 #CCCCCC;
    border: 1px solid #FFFFFF;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.6);
    height: auto;
    left: 98%;
    top: -0.167em;
    width: 15em;
}

    .menu ul li:hover > ul.reverted {
        left: auto;
        right: 98%;
    }

    .menu ul li:hover > ul > li {
        display: block;
    }

.menu ul li {
    background-position: 5px 3px;
    background-repeat: no-repeat;
    color: #999999;
    cursor: pointer;
    margin: 0;
    padding: 0.333em 0.833em 0.417em 35px;
    position: relative;
    text-shadow: 1px 1px 0 #FFFFFF;
    z-index: 9999911;
}

.ie7 .menu > ul > li .ie7 .menu ul li:hover > ul > li {
    display: inline-block;
    padding-left: 0;
    padding-right: 0;
    text-indent: 35px;
}

.menu ul li.sep {
    border-bottom: 1px solid #FFFFFF;
    border-top: 1px solid #ADADAD;
    font-size: 0;
    height: 0;
    line-height: 0;
    margin: 2px 0;
    padding: 0;
}

.ie7 .menu ul li.sep {
    z-index: 999910;
}

.menu ul li a {
    color: #1E343F;
    display: block;
    margin: -0.333em -0.833em -0.417em -9px;
    padding: 0.333em 0.833em 0.417em 9px;
    text-shadow: none;
}

.ie7 .menu ul li a {
    margin-left: 26px;
    margin-right: 0;
    text-indent: 0;
}

.menu ul li:hover {
    background-color: #99dcfe;
    z-index: 999912;
}

    .menu ul li:hover > a {
        background: none repeat scroll 0 0 #0081C2;
        color: #FFFFFF;
    }

.menu .icon_address {
    background-image: url( "../../images/icons/address-book.png" );
}

.menu .icon_alarm, .icon_alarm {
    background-image: url( "../../images/icons/alarm-clock-blue.png" );
}

.menu .icon_calendar {
    background-image: url( "../../images/icons/calendar-day.png" );
}

.menu .icon_cards {
    background-image: url( "../../images/icons/cards-address.png" );
}

.menu .icon_chart {
    background-image: url( "../../images/icons/chart.png" );
}

.menu .icon_computer {
    background-image: url( "../../images/icons/computer.png" );
}

.menu .icon_database {
    background-image: url( "../../images/icons/database.png" );
}

.menu .icon_delete {
    background-image: url( "../../images/icons/cross-circle.png" );
}

.menu .icon_doc_excel {
    background-image: url( "../../images/icons/document-excel.png" );
}

.menu .icon_doc_pdf {
    background-image: url( "../../images/icons/document-pdf.png" );
}

.menu .icon_edit {
    background-image: url( "../../images/icons/pencil.png" );
}

.menu .icon_security {
    background-image: url( "../../images/icons/hard-hat.png" );
}

.menu .icon_mail {
    background-image: url( "../../images/icons/mail.png" );
}

.menu .icon_newspaper {
    background-image: url( "../../images/icons/newspaper.png" );
}

.menu .icon_search, .workflowmenus .icon_search {
    background-image: url( "../../images/icons/magnifier.png" );
}

.menu .icon_network, .workflowmenus .icon_network {
    background-image: url( "../../images/icons/globe-network.png" );
}

.menu .icon_server, .workflowmenus .icon_server {
    background-image: url( "../../images/icons/server.png" );
}

.menu .icon_reset, .workflowmenus .icon_reset {
    background-image: url( "../../images/icons/counter-reset.png" );
}

.menu .icon_load, .workflowmenus .icon_load {
    background-image: url( "../../images/icons/Load-icon.png" );
}

.menu .icon_save, .workflowmenus .icon_save {
    background-image: url( "../../images/icons/Save.png" );
}

.menu .icon_attach, .workflowmenus .icon_attach {
    background-image: url( "../../images/icons/Attach-icon.png" );
}

.menu .icon_xportxml, .workflowmenus .icon_xportxml {
    background-image: url( "../../images/icons/file-xml.png" );
}

.menu .icon_new_doc, .workflowmenus .icon_new_doc:before {
    background-image: url( "../../images/icons/new_document.png");
}

.menu .icon_de-attach, .workflowmenus .icon_de-attach {
    background-image: url( "../../images/icons/icon-de-attach.png" );
}

.menu .icon_manageGrwflow, .workflowmenus .icon_manageGrwflow {
    background-image: url( "../../Images/icons/icon-manage-groupwflow.png" );
}

input.select[type="button"] {
    background: url( "../../Images/dd-arrow.png" ) no-repeat scroll 99.9% 50% #FFFFFF !important;
    border: 1px solid #C0C0C0 !important;
    border-radius: 0 0 0 0 !important;
    color: #333 !important;
    overflow: hidden;
    padding-right: 40px;
    text-align: left !important;
    text-indent: 5px;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 45%;
    height: 32px;
}

    input.select[type="button"]:hover {
        background: none repeat scroll 0 0 #FFFFFF;
        border: 1px solid #3399CC;
    }

    input.select[type="button"]:disabled {
        /*background: url( "../../Images/dd-arrow-disable.png" ) no-repeat scroll 99.9% 50% #f0f0f0 !important;*/
        background-color: #f0f0f0 !important;
        border-color: #999999;
        color: #333333 !important;
        cursor: auto;
        opacity: 0.3;
    }

#divProperties table .form-control, #divProperties table select {
    width: 95%;
}

.actioncheckbox {
    margin-top: -23px;
    /*right: 92px;*/
    right: 11%;
}

ul[id^=submainli] {
    margin-left: -6px !important;
    margin-top: -5px !important;
    overflow: hidden !important;
    width: 100%;
}

    ul[id^=submainli] li {
        width: 98% !important;
    }

li[id^="mainli"] ul[id^="submainli"] span.minus:before {
    margin-right: 5px;
}

ul[id^=innerUlsub] {
    margin-left: 5px !important;
}

#divProperties td div.btn-group.bootstrap-select {
    display: inline-block;
    margin: 0;
    padding: 0;
    width: 96%;
}

#divActionName span.caret {
    border-color: #777 rgba(0, 0, 0, 0) -moz-use-text-color;
    display: inline-block;
    margin-top: 15px;
    position: absolute;
    right: 55px;
    vertical-align: middle;
}

#divProperties td .btn-default[disabled="disabled"] {
    border: 1px solid #cccccc;
    background-color: #eeeeee;
    color: #555;
    opacity: 1;
    text-shadow: none;
}

.small img {
    vertical-align: baseline;
    margin-right: -3px;
}

.small .button-checkbox .btn {
    vertical-align: bottom;
    margin-right: 0px;
    width: 18px;
}

    .small .button-checkbox .btn span {
        vertical-align: baseline;
    }


.dragbox.btn.btn-primary.MarkParallel.pull-right, .dragbox.btn.btn-primary.MarkParallel.pull-right:hover, .dragbox.btn.btn-primary.MarkParallel.pull-right:disabled, .dragbox.btn.btn-primary.MarkParallel.pull-right:active {
    background-image: url("../../Images/icons/icon-parallel-action.png");
    background-size: 14px 13px;
    background-repeat: no-repeat;
    background-position: 9px 5px;
}

.dragbox.btn.btn-primary.Delete.pull-right, .dragbox.btn.btn-primary.Delete.pull-right:hover, .dragbox.btn.btn-primary.Delete.pull-right:disabled, .dragbox.btn.btn-primary.Delete.pull-right:active {
    background-image: url("../../Images/icons/icon-test-delete-ew.png");
    background-repeat: no-repeat;
    background-position: 10px 3px;
}

.dragbox.btn.btn-primary.ActionCreate.pull-right, .dragbox.btn.btn-primary.ActionCreate.pull-right:hover, .dragbox.btn.btn-primary.ActionCreate.pull-right:disabled, .dragbox.btn.btn-primary.ActionCreate.pull-right:active {
    background-image: url("../../Images/icons/icon-insertaction1.png");
    background-size: 14px 14px;
    background-repeat: no-repeat;
    background-position: 9px 4px;
}

.dragbox.btn.btn-primary.AddCondition.pull-right, .dragbox.btn.btn-primary.AddCondition.pull-right:hover, .dragbox.btn.btn-primary.AddCondition.pull-right:disabled, .dragbox.btn.btn-primary.AddCondition.pull-right:active {
    background-image: url("../../Images/icons/icon-addcondition.png");
    background-size: 17px 14px;
    background-repeat: no-repeat;
    background-position: 7px 4px;
    margin-left: 5px;
}

#rightDiv .icon.cb-icon-check-empty, #rightDiv .icon.cb-icon-check, #rightDiv .icon.cb-icon-check-indeterminate {
    background-image: url("../../Images/custom-chkbox-rdbtn-new.png") !important;
}

ul#ulCategory li ul li {
    margin-bottom: -2px;
}

input#chkuser.select[type="button"] {
    background: #ffffff url("../../Images/dd-arrow.png") no-repeat scroll 100% 50% !important;
}

.new-select-editable {
    position: relative;
    background-color: white;
    border: 1px solid #ccc;
    width: 427px;
    height: 34px;
}

    .new-select-editable select {
        position: absolute;
        top: 0px;
        left: 0px;
        font-size: 14px;
        border: none;
        width: 425px !important;
        margin: 0;
    }

    .new-select-editable input {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 407px;
        padding: 5px;
        font-size: 12px;
        border: none;
        height: 31px;
        height: 31px;
    }

        .new-select-editable select:focus, .new-select-editable input:focus {
            outline: none;
        }

menu .icon_exportxml, .workflowmenus .icon_exportxml {
    background-image: url( "../../images/icons/xml-export.png" );
}
 