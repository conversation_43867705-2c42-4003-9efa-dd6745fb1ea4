﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ViewApplicationDependencyMapping.aspx.cs" Inherits="CP.UI.SNMP.ViewApplicationDependencyMapping" %>


<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <link href="../App_Themes/CPTheme/MappingAndContextmenu.css" rel="stylesheet" />

    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />

    <style type="text/css">
        .divselect {
            position: absolute;
            top: 90px;
            left: 86px;
            width: 100px !important;
            z-index: 9999;
        }

        .tblchange tr th {
            font-size: 11px !important;
            font-weight: 400;
            padding: 2px 3px !important;
        }

        .poppaneldesign1 .changetblstruc lable, .poppaneldesign1 .changetblstruc span, .poppaneldesign1 .changetblstruc td, .poppaneldesign1 .changetblstruc th {
            font-size: 10px !important;
        }

        #lblError {
            color: red;
            margin: 0px;
        }

        .nodeSpan {
            background-color: #90cb95;
            font-size: 6px;
            font-style: normal;
            border-radius: 4px;
            padding: 1px 4px;
            color: #000;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
     <asp:HiddenField ID="hdtokenKey" runat="server" />
    <telerik:RadWindowManager ID="RadWindowManager1" runat="server" ShowContentDuringLoad="false" ShowOnTopWhenMaximized="true"
        Skin="WebBlue" IconUrl="~/Images/Telerik_CV_Icon.png">
        <Windows>

            <telerik:RadWindow ID="Rdnotification" AutoSize="false" runat="server" VisibleStatusbar="false" Skin="Metro"
                Modal="true" Behaviors="Move, Close, Resize" CenterIfModal="true" Height="540" Title="Business Impact Summary"
                Width="1240" />


        </Windows>
    </telerik:RadWindowManager>

    <asp:UpdatePanel ID="updPnlScan" runat="server" UpdateMode="Conditional">
        <ContentTemplate>
            <div class="innerLR">

                <h3>
                    <img src="../Images/business-process-icon.png" alt="View Application Discovery ">
                    Application Dependency Mapping</h3>


                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="col-md-7 padding-none-LR">
                                    <div class="col-md-12 padding-none-LR">
                                        <label>Host From</label>
                                        <input type="text" id="inIPAddressFrom" class="form-control" placeholder="IP Address" style="width: 18%" runat="server" onkeypress="return isNumberKey(event)" />


                                        <label>To</label>
                                        <input type="text" id="inIPAddressTo" class="form-control appright" placeholder="IP Address" runat="server" style="width: 16%" onkeypress="return isNumberKey(event)" />
                                        <asp:Button ID="btnFindDependency" runat="server" CssClass="btn btn-primary appright" Text="Find Dependencey" OnClick="btnFindDependency_Click" />

                                        <asp:DropDownList ID="ddlAppDepProfiles" CssClass="selectpicker" data-style="btn-default" runat="server" AutoPostBack="true" onchange="javascript:AssignValue();" OnSelectedIndexChanged="ddlAppDepProfiles_SelectedIndexChanged">
                                        </asp:DropDownList>
                                    </div>
                                </div>
                                <div class="col-md-5 margin-top">
                                    <div class="col-md-6 text-right hide">

                                        <span class="count-icon vertical-sub"></span>
                                        Dependent Hosts : 
                                            <asp:Label ID="lblCount" runat="server" CssClass="text-danger margin-right" Text=""></asp:Label>

                                    </div>
                                    <div class=" text-right">
                                        <span class="icon-Time"></span>
                                        Discovery Time :  
                                <asp:Label ID="lblTime" runat="server" Text="" CssClass="text-danger  "></asp:Label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <hr class="" style="margin: 10px 0 5px;" />
                        <div class="text-right">
                            <asp:Label ID="lblNoResultFound" CssClass="text-left" runat="server" Text="" ForeColor="Red" Visible="false"></asp:Label>
                        </div>
                        <div class="row row-merge">
                            <div class="col-md-10">
                                <div class="graph">
                                    <div class="control-zoom" style="display: none;">
                                        <a class="control-zoom-in" href="javascript:void(0);" title="Zoom in"></a>
                                        <a class="control-zoom-out" href="javascript:void(0);" title="Zoom out"></a>
                                    </div>
                                    <div class="tooltip"></div>
                                </div>
                                <div class="margin-right div-discover-btn text-right">
                                    <asp:Label ID="lblSaveMsg" CssClass="text-right padding" runat="server" Text="Application dependency has been saved successfully..." ForeColor="Green" Visible="false"></asp:Label>
                                    <asp:Button ID="btnSaveDiscovery" runat="server" CssClass="btn btn-primary" Text="Save" Visible="false" OnClick="btnSaveDiscovery_Click" />
                                    <asp:Button ID="btnRediscover" runat="server" CssClass="btn btn-primary" Text="Re-Discover" Visible="false" />
                                </div>
                            </div>
                            <div class="col-md-2">

                                <div class="padding" style="height:460px;">
                                    <div class="group-node-details" style="display: none;">
                                        <h3 class='margin-none' style='line-height: 25px;'>
                                            <img alt='Dependencies' src='../Images/icons/Form_Icons/team_config_icon.png' class='vertical-baseline'>
                                            <span class='lblSummary'></span></h3>
                                        <hr class='margin-none' />
                                        <div class="notifyscroll" style="height: 200px;">
                                            <div class="accordionGroup1">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="node-details padding" style="display: none;">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <asp:Panel ID="modelbg" runat="server" class="popup-bg" Style="display: none;">
                        </asp:Panel>

                        <asp:Panel ID="pnlTagEntity" runat="server" Style="display: none;" CssClass="popup-panel">
                            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional" class="popup" ChildrenAsTriggers="false">
                                <ContentTemplate>
                                    <div class="popup-dialog">
                                        <div class="popup-header">
                                            <a class="close">×</a>

                                            <span class="popup-title">
                                                <label class="fa fa-map-marker"></label>
                                                Tag Entity - <span class="text-bold" id="spnIPAddress"></span>
                                            </span>
                                        </div>
                                        <div class="popup-body padding">
                                            <div class="row padding padding-none-TB ">
                                                <div class="col-md-12 form-horizontal uniformjs">
                                                    <div class="form-group">
                                                        <label class="col-md-4 padding-none-LR">
                                                            Entity Name</label>
                                                        <div class="col-md-8 padding-none-LR">
                                                            <asp:TextBox ID="txtEntityName" runat="server" class="form-control"></asp:TextBox>
                                                            <span class="pull-right"></span>

                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 padding-none-LR">
                                                            Description</label>
                                                        <div class="col-md-8 padding-none-LR">
                                                            <asp:TextBox ID="txtDescription" runat="server" TextMode="MultiLine" class="form-control"></asp:TextBox>
                                                            <span class="pull-right"></span>

                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 padding-none-LR">
                                                            Criticality 
                                                        </label>
                                                        <div class="col-md-8 padding-none-LR">
                                                            <asp:DropDownList ID="ddlCriticality" runat="server">
                                                                <asp:ListItem Value="000" Text="Select Criticality"></asp:ListItem>
                                                                <asp:ListItem Value="1" Text="High"></asp:ListItem>
                                                                <asp:ListItem Value="2" Text="Medium"></asp:ListItem>
                                                                <asp:ListItem Value="3" Text="Low"></asp:ListItem>
                                                            </asp:DropDownList>
                                                            <span class="pull-right"></span>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="popup-footer row">


                                                <div class="col-md-8 ">
                                                    <span class="spnmessage text-success hide">saved successfully!</span>

                                                </div>
                                                <div class="col-md-4 padding-none text-right ">
                                                    <input type="button" id="btnSave" class="btn popup-btn" value="Save" runat="server" />
                                                </div>

                                            </div>
                                        </div>

                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                        <asp:Panel ID="pnlMapDependancy" runat="server" Style="display: none;" CssClass="popup-panel">
                            <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional" class="popup" ChildrenAsTriggers="false">
                                <ContentTemplate>
                                    <div class="popup-dialog">
                                        <div class="popup-header">
                                            <a class="close">×</a>
                                            <span class="popup-title">

                                                <label class="fa fa-thumb-tack"></label>
                                                <span id="lbladm"></span>

                                            </span>
                                        </div>
                                        <div class="popup-body padding">
                                            <div class="row padding padding-none-TB ">
                                                <div class="col-md-12 form-horizontal uniformjs">
                                                    <div class="form-group">
                                                        <label class="col-md-4 padding-none-LR">
                                                            Host Name
                                                        </label>
                                                        <label id="spnTagName" class="col-md-8 padding-none-LR">
                                                        </label>
                                                    </div>

                                                    <div class="form-group">
                                                        <label class="col-md-4 padding-none-LR">
                                                            Dependancy 
                                                        </label>
                                                        <div class="col-md-8 padding-none-LR deplist">
                                                            <input id='btnchklist' type='button' value=' - Select IP - '></input>
                                                            <span id="msgspnchklist" class="pull-right error" style="display: none;">*</span>
                                                            <ul id="ulchklist" class="small" style="display: none;">
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="popup-footer row">

                                                <div class="col-md-8 ">
                                                    <span class="spnmessage text-success hide">saved successfully!</span>

                                                </div>
                                                <div class="col-md-4 padding-none text-right ">
                                                    <input type="button" id="btnDepMapSave" class="btn popup-btn" value="OK" runat="server" disabled="disabled" />
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                        <asp:Panel ID="pnlproperties" runat="server" Style="display: none;" CssClass="popup-panel">
                            <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional" class="popup" ChildrenAsTriggers="false">
                                <ContentTemplate>
                                    <div class="popup-dialog">
                                        <div class="popup-header">
                                            <a class="close">×</a>
                                            <span class="popup-title">
                                                <img src="../Images/icons/prop-tab-icn.png" />
                                                Properties - <b id="spnip-prop"></b>

                                            </span>
                                        </div>
                                        <div class="popup-body padding">
                                            <div id="prop-tab" class="row prop-tab-body">
                                                <div class="prop-tab">
                                                    <ul class="testul">
                                                        <li class="active"><a href="#tabs-1" class="cp-prop">&nbsp;</a></li>
                                                        <li><a href="#tabs-2" class="os-prop">&nbsp;</a></li>
                                                        <li><a href="#tabs-3" class="apps-prop">&nbsp;</a></li>
                                                    </ul>
                                                </div>
                                                <div id="tab-content" class="prop-tab-content">
                                                    <div id="tabs-1" class="hide show"></div>
                                                    <div id="tabs-2" class="hide ">TAB2</div>
                                                    <div id="tabs-3" class="hide">TAB3</div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                        <asp:Panel ID="Paneltaggroup" runat="server" Style="display: none;" CssClass="popup-panel">
                            <asp:UpdatePanel ID="UpdatePanel4" runat="server" UpdateMode="Conditional" class="popup" ChildrenAsTriggers="false">
                                <ContentTemplate>
                                    <div class="popup-dialog" style="/*width: 250px*/">
                                        <div class="popup-header">
                                            <a class="close">×</a>
                                            <span class="popup-title">

                                                <label class="fa fa-map-marker"></label>
                                                Group Nodes - <span id="lbladmgrp"></span>

                                            </span>
                                        </div>
                                        <div class="popup-body padding">
                                            <div class="row padding padding-none-TB ">
                                                <div class="col-md-12 form-horizontal uniformjs" style="height: 100px;">

                                                    <div class="form-group">
                                                        <label class="col-md-4 padding-none-LR">
                                                            Nodes 
                                                        </label>
                                                        <div class="col-md-8 padding-none-LR deplist">
                                                            <input id='btngroupchklist' type='button' value=' - Select Nodes - '></input>
                                                            <span id="msgspngroupchklist" class="pull-right error" style="display: none;">*</span>
                                                            <ul id="ulgrpchklist" class="small" style="display: none;">
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 padding-none-LR">
                                                            Group Name 
                                                        </label>
                                                        <div class="col-md-8 padding-none-LR deplist">
                                                            <asp:TextBox ID="txtgrpnode" runat="server" CssClass="form-control" onkeypress="checkForComma(event);" onblur="isGroupnameExist();"></asp:TextBox>
                                                            <span class="pull-right"></span>
                                                            <label id="lblError" style="display: none; color: red">Group name already exists.</label>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-md-4 padding-none-LR">
                                                            Group Description 
                                                        </label>
                                                        <div class="col-md-8 padding-none-LR deplist">
                                                            <asp:TextBox ID="txtgrpDescription" runat="server" CssClass="form-control" TextMode="MultiLine"></asp:TextBox>
                                                            <span class="pull-right"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="popup-footer row">

                                                <div class="col-md-8 ">
                                                    <span class="spnmessage text-success hide">saved successfully!</span>

                                                </div>
                                                <div class="col-md-4 padding-none text-right ">
                                                    <input type="button" id="btnSaveGroupNodes" class="btn popup-btn" value="OK" runat="server" />
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                        <asp:Panel ID="pnlTagPort1" runat="server" Style="display: none;" CssClass="popup-panel poppaneldesign1 ">
                            <asp:UpdatePanel ID="UpdatePanel5" runat="server" UpdateMode="Conditional" class="popup" ChildrenAsTriggers="false">
                                <ContentTemplate>
                                    <div class="popup-dialog" style="width: 290px">
                                        <div class="popup-header">
                                            <a class="close">×</a>

                                            <span class="popup-title">
                                                <img src="../Images/DDE/usb-symbol-white.png" style="width: 13px; vertical-align: middle;" />
                                                Tag Port - <span class="text-bold" id="spnIP"></span>
                                            </span>
                                        </div>
                                        <div class="popup-body padding" style="/*height: 254px*/">

                                            <div class="padding-none-TB " style="margin-bottom: 10px;">
                                                <div class="col-md-12 form-horizontal uniformjs changetblstruc">
                                                    <select class="divselect" id="ddlStandardsPort" runat="server" size="3" style="display: none; height: 60px" onchange="Setselectedport(this)">
                                                    </select>
                                                    <div class="notifyscrolls" style="max-height: 126px; overflow-y: auto">
                                                        <table style="width: 100%; margin-bottom: 0px;" class="dynamicTable tblchange tableTools table table-striped table-bordered table-condensed table-white ">
                                                            <thead>
                                                            </thead>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="popup-footer row">
                                                <div class="col-md-12 text-right padding-none ">
                                                    <input type="button" id="btnaddport" class="btn popup-btn" value="Add Port" runat="server" onclick="addPortRow()" style="width: 60px;" />
                                                    <input type="button" id="btnSaveport" class="btn popup-btn" value="OK" runat="server" onclick="UpdateAllAppData()" style="width: 50px;" />
                                                </div>

                                            </div>
                                        </div>

                                    </div>

                                </ContentTemplate>
                            </asp:UpdatePanel>
                        </asp:Panel>

                    </div>
                </div>
                <asp:UpdateProgress ID="UpdateProgress1" AssociatedUpdatePanelID="updPnlScan" runat="server">
                    <ProgressTemplate>
                        <div id="imgLoading" class="loading-mask">
                            <span>Discovering...</span>
                        </div>
                    </ProgressTemplate>
                </asp:UpdateProgress>
            </div>

            <asp:HiddenField ID="hdnDepProfileName" runat="server" Value=" " />
        </ContentTemplate>
    </asp:UpdatePanel>


    <asp:HiddenField ID="hdnTagInfo" runat="server" Value="start|" />
    <asp:HiddenField ID="hdnLinkInfo" runat="server" Value="start|" />
    <asp:HiddenField ID="hdnDeleteLinkInfo" runat="server" Value="start|" />

    <asp:HiddenField ID="hdnTempLinkInfo" runat="server" Value="" />
    <asp:HiddenField ID="hdnTempDeleteLinkInfo" runat="server" Value="" />

    <asp:HiddenField ID="hdnMapList" runat="server" Value="" />
    <asp:HiddenField ID="hdnUnMapList" runat="server" Value="" />

    <asp:HiddenField ID="hdnIsSaveClicked" runat="server" Value="0" />

    <asp:HiddenField ID="hdnid" runat="server" Value="0" />

    <asp:HiddenField ID="hdnAddedGrpNodesDetails" runat="server" Value="" />

    <asp:HiddenField ID="hdnRemovedGrpNodesDetails" runat="server" Value="" />

    <asp:HiddenField ID="hdnAppdata" runat="server" Value="0" />

    <script src="../Script/d3.v3.min.js"></script>
    <script src="../Script/underscore-min.js"></script>
    <script src="../Script/dependoAppDependency.js"></script>
    <script src="../Script/context.js"></script>
    <script src="../Script/validation.js"></script>
    <script src="../Script/jquery.json.js"></script>
    <script src="../Script/jquery.nicescroll.min.js"></script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>

    <script src="../Script/d3.min.js"></script>

    <script src="../Script/AppDepMapJs.js"></script>


    <script type="text/javascript">

        function OpenWindow(arg) {
            var Url = '../ImpactAnalysis/WhatIfAnalysis.aspx?IPAddress=' + arg;
            window.radopen(Url, 'Rdnotification');

        }


        $(document).ready(function () {
            $(document).ready(function () {
                $('.editbtn').live('click', function () {
                    var currentTD = $(this).parents('tr').find('td');
                    currentRow = currentTD
                    var pos = $(this).position();
                    if ($(this).hasClass('editbtn')) {
                        currentTD = $(this).parents('tr').find('td:nth-child(2)');
                        $.each(currentTD, function () {
                            $(".divselect").css({
                                top: (pos.top - 3) + "px",
                            }).show();
                        });

                    } else {

                    }
                });

                $('.savebtn').live('click', function () {

                    var currentTD = $(this).parents('tr').find('td');
                    if ($(this).hasClass('savebtn')) {
                        currentTD = $(this).parents('tr').find('td:nth-child(2)');
                        $.each(currentTD, function () {
                            $(this).find("span").show();
                            $(".divselect").hide();
                        });
                        $(this).removeClass('savebtn');
                        $(this).addClass('editbtn');
                    } else {

                    }
                });
            });

            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
            });

        });
        $(document).click(function (e) {
            if (!$(e.target).hasClass("divselect")
                && $(e.target).parents(".changetblstruc").length === 0) {
                $(".divselect").hide();
            }
        });
        function pageLoad() {

            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
            });
        }



    </script>

</asp:Content>



