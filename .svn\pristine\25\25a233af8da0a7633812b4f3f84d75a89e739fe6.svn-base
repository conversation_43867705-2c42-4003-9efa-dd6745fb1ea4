﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "OracleLog", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class OracleLog : BaseEntity
    {
        #region Properties

        [DataMember]
        public string LogFileName { get; set; }

        [DataMember]
        public string PRSequenceNo { get; set; }

        [DataMember]
        public string DRSequenceNo { get; set; }

        [DataMember]
        public string PRTransId { get; set; }

        [DataMember]
        public string DRTransId { get; set; }

        [DataMember]
        public string PRLogTime { get; set; }

        [DataMember]
        public string DRLogTime { get; set; }

        [DataMember]
        public string CurrentDataLag { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string InfraObjectName { get; set; }

        [DataMember]
        public int BusinessFunctionId { get; set; }

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public string BusinessServiceName { get; set; }

        //=====================Below Properties Added By Mridul=============//
        [DataMember]
        public int Health { get; set; }

        //[DataMember]
        //public int ApplicationGroupId { get; set; }

        //=================================================//
        [DataMember]
        public string DRLogFileName { get; set; }
        [DataMember]
        public string PRThreadID { get; set; }
        [DataMember]
        public string DRThreadID { get; set; }


        //============Added By Mahesh Singh ============//

        [DataMember]
        public string DailyDate
        {
            get;
            set;
        }

        [DataMember]
        public int DailySlaCount
        {
            get;
            set;
        }

        [DataMember]
        public string MonthlyDate
        {
            get;
            set;
        }

        [DataMember]
        public int MonthlySlaCount
        {
            get;
            set;
        }
        public string Name
        {
            get;
            set;
        }
        public Double ConfigureDataLag
        {
            get;
            set;
        }


        #endregion Properties
    }
}