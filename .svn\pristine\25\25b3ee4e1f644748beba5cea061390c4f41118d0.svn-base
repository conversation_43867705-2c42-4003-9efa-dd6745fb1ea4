﻿using System.IO;
using CP.BusinessFacade;

namespace CP.UI.Code.Replication.ReplicationInfo
{
    public class Postgre9xMonitorStatusInfo : IReplicationInfo
    {
        private readonly IFacade _facade = new Facade();

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectId)
        {
            var currentEntityType = currentEntity.GetType();

            var postgre = _facade.Postgre9xMonitorStatusByInfraId(infraObjectId);
            //var postgremon = _facade.PostgreComponentMonitorByInfraId(infraObjectId);

            if (postgre != null)
            {
                currentEntityType.GetProperty("InfraObjectId").SetValue(currentEntity, postgre.InfraObjectId, null);
                currentEntityType.GetProperty("ReplicationStatusPR").SetValue(currentEntity, postgre.ReplicationStatusPR, null);
                currentEntityType.GetProperty("ReplicationStatusDR").SetValue(currentEntity, postgre.ReplicationStatusDR, null);

                currentEntityType.GetProperty("DataDirectoryPathPR").SetValue(currentEntity, postgre.DataDirectoryPathPR, null);
                currentEntityType.GetProperty("DataDirectoryPathDR").SetValue(currentEntity, postgre.DataDirectoryPathDR, null);
                
                currentEntityType.GetProperty("DataLag_MB").SetValue(currentEntity, postgre.DataLag_MB, null);
                currentEntityType.GetProperty("DataLag_HHMMSS").SetValue(currentEntity, postgre.DataLag_HHMMSS, null);

                currentEntityType.GetProperty("Current_xlog_location").SetValue(currentEntity, postgre.Current_xlog_location, null);
                currentEntityType.GetProperty("Last_xlog_receive_location").SetValue(currentEntity, postgre.Last_xlog_receive_location, null);
                currentEntityType.GetProperty("Last_xlog_replay_location").SetValue(currentEntity, postgre.Last_xlog_replay_location, null);
                currentEntityType.GetProperty("CurrentXlogFileName").SetValue(currentEntity, postgre.CurrentXlogFileName, null);
                currentEntityType.GetProperty("XlogReceiveFileName").SetValue(currentEntity, postgre.XlogReceiveFileName, null);
                currentEntityType.GetProperty("XlogReplayFileName").SetValue(currentEntity, postgre.XlogReplayFileName, null);

            }
            else
            {
                currentEntityType.GetProperty("ReplicationStatusPR").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("ReplicationStatusDR").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("DataDirectoryPathPR").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DataDirectoryPathDR").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("DataLag_MB").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DataLag_HHMMSS").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("Current_xlog_location").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("Last_xlog_receive_location").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("Last_xlog_replay_location").SetValue(currentEntity, "N/A", null);

                currentEntityType.GetProperty("CurrentXlogFileName").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("XlogReceiveFileName").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("XlogReplayFileName").SetValue(currentEntity, "N/A", null);

            }

            return currentEntity;
        }

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectId, int mailBoxId, string mailboxname)
        {
            return currentEntity;
        }
    }
}