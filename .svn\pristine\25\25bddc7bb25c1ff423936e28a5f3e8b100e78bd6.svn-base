﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.CPSLScheduled
{
    internal sealed class CPSLScheduledDataAccess : BaseDataAccess, ICPSLScheduleDataAccess
    {
        #region Constructors

        public CPSLScheduledDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<CPSLSchedule> CreateEntityBuilder<CPSLSchedule>()
        {
            return (new CPSLScheduledBuilder()) as IEntityBuilder<CPSLSchedule>;
        }

        #endregion Constructors

        /// <param name="CPSLSchedule">CPSLSchedule</param>
        /// <returns>CPSLSchedule</returns>
        /// <author>Me<PERSON><PERSON>hi  28July2015</author>
        /// 

        #region Methods

        CPSLSchedule ICPSLScheduleDataAccess.Add(CPSLSchedule infraObject)
        {
            try
            {
                const string sp = "CPSL_SCHEDULER_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, infraObject.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iCPSLId", DbType.Int32, infraObject.CPSLId);
                    Database.AddInParameter(cmd, Dbstring + "iCPSLType", DbType.AnsiString, infraObject.CPSLType);
                    Database.AddInParameter(cmd, Dbstring + "iCPSLTime", DbType.AnsiString, infraObject.CPSLTime);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, infraObject.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iIsSchedule", DbType.Int32, 0);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        infraObject = reader.Read() ? CreateEntityBuilder<CPSLSchedule>().BuildEntity(reader, infraObject) : null;
                    }

                    if (infraObject == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("InfraObject already exists. Please specify another CPSLSchedule.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this CPSLSchedule.");
                                }
                        }
                    }

                    return infraObject;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Executing Function Signature ICPSLScheduleDataAccess.Add"
                     , exc);
            }
        }

        CPSLSchedule ICPSLScheduleDataAccess.Update(CPSLSchedule infraObject)
        {
            try
            {
                const string sp = "CPSL_SCHEDULER_UPDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, infraObject.Id);
                    // Database.AddInParameter(cmd, Dbstring + "iCPSLScript", DbType.AnsiString, infraObject.CPSLScript);
                    Database.AddInParameter(cmd, Dbstring + "iCPSLType", DbType.AnsiString, infraObject.CPSLType);
                    Database.AddInParameter(cmd, Dbstring + "iCPSLTime", DbType.AnsiString, infraObject.CPSLTime);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, infraObject.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iIsSchedule", DbType.Int32,2);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        @infraObject = reader.Read() ? CreateEntityBuilder<CPSLSchedule>().BuildEntity(reader, @infraObject) : null;
                    }
                    if (infraObject == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Group already exists. Please specify another CPSLSchedule.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this CPSLSchedule.");
                                }
                        }
                    }

                    return infraObject;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Executing Function Signature CPSLSchedule.Update"
                     , exc);
            }
        }

        bool ICPSLScheduleDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "CPSL_SCHEDULER_DELBYID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting CPSL_scheduler Entry : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool ICPSLScheduleDataAccess.DeleteByCPSLId(int CPSLid)
        {
            try
            {
                if (CPSLid < 1)
                {
                    throw new ArgumentNullException("CPSLid");
                }
                const string sp = "CPSL_SCHEDULER_DELBYCPSLID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCPSLId", DbType.Int32, CPSLid);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting CPSL_scheduler Entry : " + CPSLid + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<CPSLSchedule> ICPSLScheduleDataAccess.GetAll()
        {
            try
            {
                IList<CPSLSchedule> listCPSL = new List<CPSLSchedule>();

                string sp = DbRoleName + "CPSL_SCHEDULER_ALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var cpslSchedule = new CPSLSchedule
                            {
                                CPSLId = Convert.IsDBNull(reader["CPSLId"]) ? 0 : Convert.ToInt32(reader["CPSLId"]),
                                InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]),
                                CPSLType = Convert.IsDBNull(reader["CPSLType"]) ? 0 : Convert.ToInt32(reader["CPSLType"]),
                                CPSLTime = Convert.IsDBNull(reader["CPSLTime"]) ? string.Empty : Convert.ToString(reader["CPSLTime"]),
                                IsEnable = Convert.IsDBNull(reader["IsEnable"]) ? false : Convert.ToBoolean(reader["IsEnable"]),
                                CPSL_Script =
                                {
                                    CPSLName = Convert.IsDBNull(reader["CPSLName"]) ? string.Empty : Convert.ToString(reader["CPSLName"]),
                                    CPSLScriptText = Convert.IsDBNull(reader["CPSLScript"]) ? string.Empty : Convert.ToString(reader["CPSLScript"]),

                                }
                            };
                            listCPSL.Add(cpslSchedule);

                        }

                    }
                }

                return listCPSL;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPSLScheduleDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<CPSLSchedule> ICPSLScheduleDataAccess.GetByInfraObjectId(int InfraobjectId)
        {
            try
            {
                IList<CPSLSchedule> listCPSL = new List<CPSLSchedule>();

                string sp = DbRoleName + "CPSL_SCHEDULARBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraobjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var cpslSchedule = new CPSLSchedule
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                CPSLId = Convert.IsDBNull(reader["CPSLId"]) ? 0 : Convert.ToInt32(reader["CPSLId"]),
                                InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]),
                                CPSLType = Convert.IsDBNull(reader["CPSLType"]) ? 0 : Convert.ToInt32(reader["CPSLType"]),
                                CPSLTime = Convert.IsDBNull(reader["CPSLTime"]) ? string.Empty : Convert.ToString(reader["CPSLTime"]),
                                IsEnable = Convert.IsDBNull(reader["IsEnable"]) ? false : Convert.ToBoolean(reader["IsEnable"]),
                                CPSL_Script =
                                {
                                    CPSLName = Convert.IsDBNull(reader["CPSLName"]) ? string.Empty : Convert.ToString(reader["CPSLName"]),
                                    CPSLScriptText = Convert.IsDBNull(reader["CPSLScript"]) ? string.Empty : Convert.ToString(reader["CPSLScript"]),

                                }
                            };
                            listCPSL.Add(cpslSchedule);

                        }

                    }
                }

                return listCPSL;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPSLScheduleDataAccess.GetAll" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        CPSLSchedule ICPSLScheduleDataAccess.GetByCPSLIdAndCPSLTime(int InfraobjectId, int CPSLId, string CPSLTime)
        {
            try
            {
                if (CPSLId < 1)
                {
                    throw new ArgumentNullException("CPSLId");
                }

                const string sp = "CPSL_SCHEDULER_ISEXIST";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iCPSLId", DbType.Int32, CPSLId);
                    Database.AddInParameter(cmd, Dbstring + "iCPSLTime", DbType.AnsiString, CPSLTime);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<CPSLSchedule>()).BuildEntity(reader, new CPSLSchedule());
                        }
                        return null;
                    }

                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPSLScheduleDataAccess.GetById(" + CPSLId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }

        }

        CPSLSchedule ICPSLScheduleDataAccess.GetById(int id)
        {
            try
            {
                CPSLSchedule cpslSchedule = new CPSLSchedule();
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "CPSL_SCHEDULER_GETBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {


                            cpslSchedule.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            cpslSchedule.CPSLId = Convert.IsDBNull(reader["CPSLId"]) ? 0 : Convert.ToInt32(reader["CPSLId"]);
                            cpslSchedule.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
                            cpslSchedule.CPSLType = Convert.IsDBNull(reader["CPSLType"]) ? 0 : Convert.ToInt32(reader["CPSLType"]);
                            cpslSchedule.CPSLTime = Convert.IsDBNull(reader["CPSLTime"]) ? string.Empty : Convert.ToString(reader["CPSLTime"]);
                            cpslSchedule.IsEnable = Convert.IsDBNull(reader["IsEnable"]) ? false : Convert.ToBoolean(reader["IsEnable"]);
                            cpslSchedule.CPSL_Script.CPSLName = Convert.IsDBNull(reader["CPSLName"]) ? string.Empty : Convert.ToString(reader["CPSLName"]);
                            cpslSchedule.CPSL_Script.CPSLScriptText = Convert.IsDBNull(reader["CPSLScript"]) ? string.Empty : Convert.ToString(reader["CPSLScript"]);





                        }
                        return cpslSchedule;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ICPSLScheduleDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        bool ICPSLScheduleDataAccess.EnableDisableById(bool IsEnable, int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "CPSLSCRIPT_DISBYID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iIsEnable", DbType.Int32, IsEnable);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Executing Function Signature ICPSLSchedule.Enable true and false : " + id + Environment.NewLine +
                    "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

    }
        #endregion

}
