﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class ParallelProfileBuilder : IEntityBuilder<ParallelProfile>
    {
        IList<ParallelProfile> IEntityBuilder<ParallelProfile>.BuildEntities(IDataReader reader)
        {
            var profile = new List<ParallelProfile>();
            while (reader.Read())
            {
                profile.Add(((IEntityBuilder<ParallelProfile>)this).BuildEntity(reader, new ParallelProfile()));
            }
            return (profile.Count > 0) ? profile : null;
        }

        ParallelProfile IEntityBuilder<ParallelProfile>.BuildEntity(IDataReader reader, ParallelProfile pProfile)
        {
            //const int FLD_ID = 0;
            //const int FLD_PROFILENAME = 1;
            //const int FLD_STATUS = 2;
            //const int FLD_PASSWORD = 3;
            //const int FLD_CREATEORID = 4;
            //const int FLD_CREATEDATE = 5;

            //pProfile.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //pProfile.ProfileName = reader.IsDBNull(FLD_PROFILENAME) ? string.Empty : reader.GetString(FLD_PROFILENAME);
            //pProfile.Status = reader.IsDBNull(FLD_STATUS) ? string.Empty : reader.GetString(FLD_STATUS);
            //pProfile.Password = reader.IsDBNull(FLD_PASSWORD) ? string.Empty : reader.GetString(FLD_PASSWORD);
            //pProfile.CreatorId = reader.IsDBNull(FLD_CREATEORID) ? 0 : reader.GetInt32(FLD_CREATEORID);
            //pProfile.CreateDate = reader.IsDBNull(FLD_CREATEDATE)
            //    ? DateTime.MinValue
            //    : reader.GetDateTime(FLD_CREATEDATE);

            pProfile.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            pProfile.ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? string.Empty : Convert.ToString(reader["ProfileName"]);
            pProfile.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
            pProfile.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
            pProfile.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            pProfile.CreateDate = Convert.IsDBNull(reader["CreateDate"])? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
            return pProfile;
        }
    }
}