﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;

namespace CP.DataAccess.AppDepGroupNode
{
    internal sealed class AppDependencyGroupNodesDataAccess : BaseDataAccess, IApplicationDepGroupNodes
    {

        #region Constructors

        public AppDependencyGroupNodesDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<AppDepGroupNodes> CreateEntityBuilder<AppDepGroupNodes>()
        {
            return (new AppDependencyGroupNodesBuilder()) as IEntityBuilder<AppDepGroupNodes>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        /// Inserts application dependency group nodes details into table "appdep_groupnodes".
        /// </summary>
        /// <param name="AppDepMappingHosts">AppDepGroupNodes</param>
        /// <returns>AppDepGroupNodes</returns>
        /// <author>Ram mahajan-12/01/2017</author>
        AppDepGroupNodes IApplicationDepGroupNodes.AddOrUpdate(AppDepGroupNodes appDepGroupNodes)
        {
            try
            {
                const string sp = "AppDepGroupNodes_InsOrUpd";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iParentGroupNode", DbType.AnsiString, appDepGroupNodes.ParentGroupNode);
                    Database.AddInParameter(cmd, Dbstring + "iDependentGroupNodes", DbType.AnsiString, appDepGroupNodes.DependentGroupNodes);
                    Database.AddInParameter(cmd, Dbstring + "iGroupName", DbType.AnsiString, appDepGroupNodes.GroupName);
                    Database.AddInParameter(cmd, Dbstring + "iGroupDescription", DbType.AnsiString, appDepGroupNodes.GroupDescription);
                    Database.AddInParameter(cmd, Dbstring + "iDepProfileName", DbType.AnsiString, appDepGroupNodes.DepProfileName);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur_appdep"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<AppDepGroupNodes>()).BuildEntity(reader, new AppDepGroupNodes()) : null;

                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting application dependency groupping nodes details : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        /// Get Application dependency group node details by discoveryprofilename
        /// </summary>
        /// <param name="discoveryProfilename">discoveryProfilename</param>
        /// <returns>List of AppDepGroupNodes</returns>
        ///<author>Ram Mahajan -12/01/2017</author>
        IList<AppDepGroupNodes> IApplicationDepGroupNodes.GetGroupNodesDetByProfileName(string discoveryProfilename)
        {
            try
            {
                const string sp = "AppDepGrpNodes_GetByprfName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iAppDepProfileName", DbType.AnsiString, discoveryProfilename);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<AppDepGroupNodes>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationDepGroupNodes.GetGroupNodesDetByProfileName(" +
                    discoveryProfilename + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        /// Get Application dependency group node details by groupName
        /// </summary>
        /// <param name="discoveryProfilename">groupName</param>
        /// <returns>List of AppDepGroupNodes</returns>
        ///<author>Ram Mahajan -12/01/2017</author>
        IList<AppDepGroupNodes> IApplicationDepGroupNodes.GetGroupNodesDetByGroupName(string groupName)
        {
            try
            {
                const string sp = "AppDepGrpNodes_GetByGrpName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iGroupName", DbType.AnsiString, groupName);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<AppDepGroupNodes>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationDepGroupNodes.GetGroupNodesDetByGroupName(" +
                    groupName + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        /// <summary>
        /// Get Application dependency group node details by profileName and groupName
        /// </summary>
        /// <param name="discoveryProfilename">profileName</param>
        /// /// <param name="groupName">groupName</param>
        /// <returns>List of AppDepGroupNodes</returns>
        ///<author>Ram Mahajan -20/01/2017</author>
        IList<AppDepGroupNodes> IApplicationDepGroupNodes.GetGrpNodesDetbyProfileAndGrpName(string profileName, string groupName)
        {
            try
            {
                const string sp = "AppDepGrpNodes_GetByGrpPrfName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iAppDepProfileName", DbType.AnsiString, profileName);
                    Database.AddInParameter(cmd, Dbstring + "iGroupName", DbType.AnsiString, groupName);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<AppDepGroupNodes>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApplicationDepGroupNodes.GetGrpNodesDetbyProfileAndGrpName(" +
                    groupName + "," + profileName + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion Methods
    }
}
