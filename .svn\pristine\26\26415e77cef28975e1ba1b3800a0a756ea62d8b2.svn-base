//
// Progress bars
// --------------------------------------------------

// Progress bar
// ------------------------- //

.progress {
	background: #f0f0f0;
	margin: 0 0 10px;
	.bevelEmboss(0,0);
	.rounded(3px,3px,3px,3px);
	.progress-bar.right { text-align: right; text-indent: 10px; direction: rtl; }
	&.add-outside {
		margin-right: 45px;
		.add { position: absolute; width: 40px; right: -45px; top: 0; text-align: left; padding-left: 5px; }
	}
	&.count-outside {
		margin-left: 40px;
		position: relative;
		overflow: visible;
		.count { position: absolute; width: 40px; left: -40px; top: 0; text-align: left; }
		&:after {
			display: block;
			content: "";
			border: 5px solid transparent;
			border-right-color: #7a7a7a;
			position: absolute;
			left: -10px;
			top: 50%;
			margin-top: -5px;
		}
	}
	.progress-bar {
		background: #7a7a7a;
		.rounded(3px,3px,3px,3px);
	}
	&.progress-success {
		.progress-bar { background: @successColor; }
		&.count-outside:after { border-right-color: @successColor; }
	}
	&.progress-warning {
		.progress-bar { background: @warningColor; }
		&.count-outside:after { border-right-color: @warningColor; }
	}
	&.progress-primary {
		.progress-bar { background: @primaryColor; }
		&.count-outside:after { border-right-color: @primaryColor; }
	}
	&.progress-danger {
		.progress-bar { background: @dangerColor; }
		&.count-outside:after { border-right-color: @dangerColor; }
	}
	&.progress-inverse {
		.progress-bar { background: @inverseColor; }
		&.count-outside:after { border-right-color: @inverseColor; }
	}
	&.progress-small {
		height: 16px;
		.progress-bar { font-size: 8px; line-height: 16px; &.right { text-indent: 2px; } }
		&.count-outside .count { height: 16px; line-height: 16px; }
		&.add-outside .add { height: 16px; line-height: 16px; }
	}
	&.progress-mini {
		height: 10px;
		margin-top:5px !important;
		&.count-outside {
			.rounded(0,3px,3px,0);
			.progress-bar { .rounded(0,3px,3px,0); }
			.count { height: 10px; line-height: 10px; }
		}
		&.add-outside .add { height: 10px; line-height: 10px; }
	}
}