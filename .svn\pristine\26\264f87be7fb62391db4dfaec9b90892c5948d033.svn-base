﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.UI.Controls;
using CP.Helper;
using CP.BusinessFacade;

namespace CP.UI.Admin
{
    public partial class EventManagement : BasePage
    {
        public EventManagements eventmanagement = new EventManagements();
        public IList<InfraObject> InfraList { get; set; }

        public string MessageInitials
        {
            get { return "Event"; }
        }

        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            Utility.SelectMenu(Master, "Module4");
            BindEvent();
            if (LoggedInUserRole == UserRole.Administrator)
            {
                int loginid = LoggedInUser.Id;

                if (loginid != null)
                {
                    PopulateInfraObjectByRole(loginid);
                }
            }
            else
            {
                PopulateinfraobjectName();
            }
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            ddlgroup.SelectedValue = "0";
            ddlevent.SelectedValue = "0";
            ddlworkflow.SelectedValue = "0";
            ddlevent.Items.Insert(0, new ListItem("- Select Event -", "0", true));
            ddlworkflow.Items.Insert(0, new ListItem("- Select Workflow -", "0", true));
        }
        private void PopulateInfraObjectByRole(int id)
        {
            InfraList = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(id, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

            if (InfraList != null)
            {
                ddlgroup.DataSource = InfraList;
                ddlgroup.DataTextField = "Name";
                ddlgroup.DataValueField = "Id";
                ddlgroup.DataBind();
                ddlgroup.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
            }
            else
            {
                ddlgroup.Items.Insert(0, new ListItem("No InfraObject Assigned", "0"));
            }
        }
        private void PopulateinfraobjectName()
        {
            IList<InfraObject> infraobject = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
            if (infraobject != null)
            {
                ddlgroup.DataSource = infraobject;
                ddlgroup.DataTextField = "Name";
                ddlgroup.DataValueField = "Id";
                ddlgroup.DataBind();
                ddlgroup.Items.Insert(0, new ListItem("- Select InfraObject -", "0",true));
              
            }
            else
            {
                ddlgroup.Items.Insert(0, new ListItem("No InfraObject Assigned", "0", true));
            }
        }

        public Dictionary<int, string> GetEnumDictionary<T>()
        {
            try
            {
                return Enum.GetValues(typeof(T)).Cast<T>().ToDictionary(t => (int)(object)t, t => t.ToString());
            }
            catch (Exception ex)
            {
                string msg = ex.Message;
                return null;
            }
        }

        protected void EventSelect(int id)
        {
            var group = Facade.GetInfraObjectById(id);
            if (group != null)
            {
                if (group.RecoveryType == 4)
                {
                    ddlevent.Items.Clear();
                    Dictionary<int, string> dict = GetEnumDictionary<SQL_2K8NativeLShippingEvent>();
                    ddlevent.DataSource = dict;
                    ddlevent.DataTextField = "Value";
                    ddlevent.DataValueField = "Key";
                    ddlevent.DataBind();
                    ddlevent.Items.Insert(0, new ListItem("- Select Event -", "0", true));
                    WorkFlowSelect(id);
                }
            }
        }

        protected void WorkFlowSelect(int Infraid)
        {
            IList<GroupWorkflow> gworkflow = Facade.GetGroupWorkflowsByInfraObjectId(Infraid);
            IList<Workflow> workflow = Facade.GetAllWorkflows();
            var lstworkflow = new List<Workflow>();
            if (gworkflow != null && workflow != null)
            {
                foreach (GroupWorkflow grp in gworkflow)
                {
                    foreach (Workflow workflw in workflow)
                    {
                        if (workflw.Id == grp.WorkflowId)
                            lstworkflow.Add(workflw);
                    }
                }
            }
           
                ddlworkflow.Items.Clear();
                ddlworkflow.DataSource = lstworkflow;
                ddlworkflow.DataTextField = "Name";
                ddlworkflow.DataValueField = "Id";
                ddlworkflow.DataBind();
                ddlworkflow.Items.Insert(0, new ListItem("- Select WorkFlow -", "0", true));
        }

        protected void btnAdd_Click(object sender, EventArgs e)
        {
            if (ValidateRequest("CreateEventManagement", UserActionType.CreateEventManagement))
            {
                BuildEntities();
                SaveEditor();
            }
        }

        public void SaveEditor()
        {
            var currentTransactionType = TransactionType.Undefined;
            if (btnAdd.Text == "Save")
            {
                currentTransactionType = TransactionType.Save;
                eventmanagement.CreatorId = LoggedInUserId;
                var result = Facade.AddEventManagement(eventmanagement);

                string message = MessageInitials + " " + '"' + eventmanagement.Event + '"';
                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                            currentTransactionType));
                Response.Redirect("..//Admin//EventManagement.aspx");
            }
            else
            {
                currentTransactionType = TransactionType.Update;
                eventmanagement.UpdatorId = LoggedInUserId;
                eventmanagement.Id = Convert.ToInt32(hdeventid.Value);
                var res = Facade.UpdateEventManagement(eventmanagement);

                string message = MessageInitials + " " + '"' + eventmanagement.Event + '"';
                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                            currentTransactionType));
                Response.Redirect("..//Admin//EventManagement.aspx");
            }
          
        }

        public void BuildEntities()
        {
            eventmanagement.EventId = Convert.ToInt32(ddlevent.SelectedValue);
            eventmanagement.InfraobjectId = Convert.ToInt32(ddlgroup.SelectedValue);
            eventmanagement.Event = ddlevent.SelectedItem.Text;
            eventmanagement.WorkFlowId = Convert.ToInt32(ddlworkflow.SelectedValue);
        }

        private void BindEvent()
        {
            var events = Facade.GetAllEventManagement();
            if (events != null)
            {
                if (events.Count <= 0) return;
                lstevent.DataSource = events;
                lstevent.DataBind();
            }
        }

        protected string GroupName(object grpid)
        {
            var group = Facade.GetInfraObjectById(Convert.ToInt32(grpid));
            if (group != null)
            {
                return group.Name;
            }
            return "";
        }

        protected string WorkflowName(object workflowid)
        {
            var workflow = Facade.GetWorkflowById(Convert.ToInt32(workflowid));
            if (workflow != null)
            {
                return workflow.Name;
            }
            return "";
        }

        protected void ddlgroup_SelectedIndexChanged1(object sender, EventArgs e)
        {
            if (Convert.ToInt32(ddlgroup.SelectedValue) != 0)
            {
                EventSelect(Convert.ToInt32(ddlgroup.SelectedValue));
            }
        }

        protected void lstevent_ItemEditing1(object sender, ListViewEditEventArgs e)
        {
            btnAdd.Text = "Update";
            var group = (lstevent.Items[e.NewEditIndex].FindControl("lblgroupid")) as Label;
            
            if (@group != null && ValidateRequest("CreateEventManagement", UserActionType.CreateEventManagement))
            {
                EventSelect(Convert.ToInt32(@group.Text));
                var workflow = (lstevent.Items[e.NewEditIndex].FindControl("lblworkflowid")) as Label;
                var events = (lstevent.Items[e.NewEditIndex].FindControl("lblevent")) as Label;
                ddlgroup.SelectedValue = @group.Text;
                if (workflow != null) ddlworkflow.SelectedValue = workflow.Text;
                Dictionary<int, string> dict = GetEnumDictionary<SQL_2K8NativeLShipping>();
                var keys = from entry in dict where events != null && entry.Value == events.Text select entry.Key;
                ddlevent.SelectedValue = keys.FirstOrDefault().ToString();
            }
            var id = (lstevent.Items[e.NewEditIndex].FindControl("lblid")) as Label;
            hdeventid.Value = id.Text;
        }

        protected void lstevent_ItemDeleting1(object sender, ListViewDeleteEventArgs e)
        {
            var lblid = (lstevent.Items[e.ItemIndex].FindControl("lblid")) as Label;
            if (lblid != null && ValidateRequest("CreateEventManagement", UserActionType.CreateEventManagement))
            {
                var id = Convert.ToInt32(lblid.Text);
                Facade.DeleteEventManagementbyId(id);
            }
            Response.Redirect("..//Admin//EventManagement.aspx");
        }
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
    }
}