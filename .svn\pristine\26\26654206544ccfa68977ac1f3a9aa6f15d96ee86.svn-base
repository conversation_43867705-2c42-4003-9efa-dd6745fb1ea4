﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="WorkflowConfiguration.aspx.cs" Inherits="CP.UI.WorkflowConfiguration"
    Title="Continuity Patrol :: Workflow-WorkflowConfiguration" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="../App_Themes/CPTheme/workflow-min.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/jquery-ui.js" type="text/javascript"></script>
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <%--<link href="../App_Themes/CPTheme/select2.css" rel="stylesheet" />--%>
    <%--<script src="../Script/select2.js"></script>
    <script src="../Script/select2.init.js"></script>--%>
    <%--   <link href="../App_Themes/CPTheme/pqselect.min.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/JQuery_NewCss/jquery-ui.css" rel="stylesheet" />--%>

    <style>
        .pq-select-search-div > .ui-icon {
            float: none !important;
            margin-top: 0px !important;
            width: 100% !important;
            height: 26px !important;
        }

        .pq-select-search-div ui-corner-all {
            height: 30px !important;
        }

        .pq-select-search-input {
            height: 26px !important;
        }
    </style>

    <style>
        .chosen-select + .chosen-container {
            width: 95% !important;
            opacity: 1 !important;
        }

        #WorkflowUL .disabled {
            pointer-events: none;
            cursor: default;
        }

        ul#WorkflowUL li a[disabled=disabled] {
            pointer-events: none !important;
            cursor: none !important;
            color: #d2d2d2;
        }

        div#ctl00_cphBody_ddlLoadProperty_chosen {
            width: 360px !important;
        }

        /*div#ctl00_cphBody_ddlWorkflowList_chosen {
            width: 360px !important;
        }*/

        .verify_icon {
            background-image: url('../Images/ApprovalProcessIcon/verified-18.png');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }
    </style>

    <style type="text/css">
        #ctl00_cphBody_pnlDomain #ctl00_cphBody_combobox1 {
            width: 225px;
            padding: 5px 5px 5px 33px;
        }

        .st_div .combobox_button {
            left: 225px !important;
        }

        .st_div .log-ext3 i:before {
            font: 16px 'Glyphicons Regular' !important;
            left: 4px !important;
            top: 11px !important;
        }

        .st_div {
            position: relative;
            margin-top: -15px;
        }

        .combobox_arrow {
            background: transparent url(../App_Themes/CPTheme/jquery.combobox/down-arrow.png) no-repeat center center;
            display: block;
            height: 100%;
            width: 100%;
            background-position: -3px -1px;
        }

        .popover-title {
            padding: 3px 8px;
            font-weight: 700;
            letter-spacing: 1px;
            font-size: 12px;
        }

        .popover-content {
            padding: 4px 16px 4px 4px;
        }

        .popover {
            max-width: 200px;
        }

        .succesrows .tdpop {
            position: relative;
            cursor: pointer !important;
        }

            .succesrows .tdpop .popover {
                top: -37px !important;
                left: -195px !important;
            }

        .whtable .succesrows:last-child .tdpop .popover, .whtable .succesrows:nth-last-child(2) .tdpop .popover {
            top: -107px !important;
            left: -35px !important;
        }

            .whtable .succesrows:last-child .tdpop .popover.left .arrow, .whtable .succesrows:nth-last-child(2) .tdpop .popover.left .arrow {
                top: 106%;
                right: 50%;
                margin-top: -11px;
                border-left-color: #999;
                border-left-color: rgba(0,0,0,0.25);
                border-right-width: 0;
                transform: rotate(90deg);
            }

        .whtable .succesrows:first-child .tdpop .popover, .whtable .succesrows:nth-child(2) .tdpop .popover {
            top: 36px !important;
            left: -35px !important;
        }

            .whtable .succesrows:first-child .tdpop .popover.left .arrow, .whtable .succesrows:nth-child(2) .tdpop .popover.left .arrow {
                top: -6%;
                right: 50%;
                margin-top: -11px;
                border-left-color: #999;
                border-left-color: rgba(0,0,0,0.25);
                border-right-width: 0;
                transform: rotate(270deg);
            }

        a#btnSave[disabled=disabled] {
            cursor: not-allowed;
            pointer-events: none;
        }

        dropup .chosen-container .chosen-drop {
            top: auto !important;
            bottom: 30px !important;
        }

        .whtable .succesrows:nth-child(3) .tdpop .popover {
            top: 36px !important;
            left: -35px !important;
        }

            .whtable .succesrows:nth-child(3) .tdpop .popover.left .arrow {
                top: -6%;
                right: 50%;
                margin-top: -11px;
                border-left-color: #999;
                border-left-color: rgba(0,0,0,0.25);
                border-right-width: 0;
                transform: rotate(270deg);
            }

        div#ctl00_cphBody_UpdateProgress1 {
            width: 100%;
            position: fixed;
            top: 0;
            right: 0px;
            left: 0;
            height: 100%;
            bottom: 0;
            background-color: rgba(0,0,0, 0.5);
            z-index: 10;
        }


        .pq-select-button {
            min-width: 95% !important;
        }

        .pq-select-text {
            height: 30px !important;
            margin-right: 4px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-weight: normal;
            color: #4a4040;
            padding: 4px;
        }

        .pq-select-button .ui-icon {
            float: right;
            margin-top: 0px;
            width: 100%;
            height: 26px;
        }

        .pq-select-button .ui-icon-triangle-1-s {
            background: transparent url(../App_Themes/CPTheme/jquery.combobox/down-arrow.png) no-repeat center center;
            display: block;
            height: 100%;
            width: 100%;
            background-position: -3px -1px;
        }

        .pq-select-button .ui-icon-triangle-1-n {
            background: transparent url(../App_Themes/CPTheme/jquery.combobox/up-arrow.png) no-repeat center center;
            display: block;
            height: 100%;
            width: 100%;
            background-position: -3px -1px;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <asp:HiddenField ID="hdnGuid" runat="server" />
    <asp:HiddenField ID="dominset" runat="server" />
    <asp:HiddenField ID="hdnUserRole" runat="server" />
    <asp:HiddenField ID="HiddenFieldadd" runat="server" />
    <asp:HiddenField ID="HiddenFielddelete" runat="server" />
    <asp:HiddenField ID="HiddenFieldedit" runat="server" />
    <asp:HiddenField ID="HiddenFieldeditt" runat="server" />
    <asp:HiddenField ID="HiddenFieldforloadaction" ClientIDMode="Static" runat="server" />

    <asp:HiddenField ID="hdnWorkflowlock" runat="server" />
    <asp:HiddenField ID="loginuser" runat="server" />

    <asp:HiddenField ID="AllApprovedWorkflowIds" ClientIDMode="Static" runat="server" />
    <asp:HiddenField ID="IsProfileApprovalSetInSettings" ClientIDMode="Static" Value="1" runat="server" />

    <div class="innerLR">
        <asp:Label ID="Label1" runat="server" CssClass="grid-details bold" Text=""></asp:Label>

        <%--  <ul class="breadcrumb show">
            <li>You are here</li>
            <li><a class="glyphicons settings"><i></i>IT Orchestration</a></li>
            <li class="divider"></li>
            <li>Workflow Configuration</li>
        </ul>--%>

        <h3>
            <img src="../Images/worflow_new.png" />
            Workflow Configuration</h3>

        <div class="row">
            <div class="col-md-6">
                <div class="widget">

                    <div class="widget-head">
                        <h4 class="heading" style="position: relative; width: 100%;">
                            <i></i>Configure Workflow
                            <asp:UpdateProgress ID="UpdateProgress1" AssociatedUpdatePanelID="uplworkpage" runat="server">
                                <ProgressTemplate>
                                    <%--<div class="pull-right">
                                        <img src="../Images/icons/loader.gif" />
                                    </div>--%>
                                    <div id="imgLoading" class="loading-mask" style="display: block">
                                        <span>Loading...</span>
                                    </div>
                                </ProgressTemplate>
                            </asp:UpdateProgress>
                        </h4>
                    </div>
                    <div class="widget-body" style="padding: 0px;">
                        <asp:Label ID="lblGroupname" runat="server" Text=""></asp:Label>
                        <asp:UpdatePanel ID="uplworkpage" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <table class="table margin-bottom-none border" style="table-layout: fixed;">
                                    <tbody>

                                        <tr>
                                            <td class="col-md-3">

                                                <asp:DropDownList ID="ddlBaseActionType" runat="server" Width="100%" OnSelectedIndexChanged="ddlBaseActionType_SelectedIndexChanged" AutoPostBack="true">
                                                </asp:DropDownList>



                                            </td>
                                            <td class="col-md-7">
                                                <%--   <asp:DropDownList ID="ddlLoadProperty" runat="server" Width="90%">
                                        </asp:DropDownList>--%>
                                                <asp:DropDownList ID="ddlLoadProperty" runat="server" Style="width: 480px">
                                                </asp:DropDownList>
                                               <%-- <button id="ddlLoadProperty_search" class="btn btn-primary">Search</button>--%>
                                            </td>
                                            <td class="text-right">
                                                <input id="box1" class="dragbox btn btn-primary" type="button" style="display: none;"
                                                    value="Add Activity" />
                                                <input id="disableBox" class="dragbox btn btn-primary" type="button" disabled="disabled"
                                                    value="Add Activity" />
                                                <%--  <input id="btnCondition" class="dragbox btn btn-primary  pull-right" type="button" style="display: block; width:37%"
                                            value="Add Condition" />--%>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </ContentTemplate>
                            <Triggers>
                                <asp:AsyncPostBackTrigger ControlID="ddlBaseActionType" />
                            </Triggers>
                        </asp:UpdatePanel>
                        <table class="table margin-bottom border" style="table-layout: fixed;">
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="text" id="txtRTO" class="form-control" placeholder="Enter RTO" />
                                        <span id="spanRTO"></span>
                                        <span id="SpanRTOmin" style="font-weight: normal;">RTO(min) </span>
                                    </td>
                                    <td class="text-right">
                                        <select id="ddlPlace" cssclass="selectpicker col-md-6" data-style="btn-default" style="width: 50%;">
                                            <option value="1">Insert After </option>
                                            <option value="0">Insert Before </option>
                                        </select>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <div id="divProperties" style="display: none;">
                            <table class="table margin-bottom border" width="100%">
                                <thead>
                                    <tr>
                                        <th colspan="2" class="text-center" style="padding: 4px;">Configure Action
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="col-md-3">Action Name
                                        </td>
                                        <td>
                                            <input id="txtName" type="text" class="form-control" maxlength="150" />
                                            <%-- <asp:TextBox ID="txtName" runat="server" class="form-control" MaxLength="10"></asp:TextBox>
                                            --%>

                                            <span></span>
                                            <span></span>
                                            <input type="hidden" id="hdActivityName" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Description
                                        </td>
                                        <td>
                                            <textarea id="txtDescription" cols="20" rows="2" class="form-control"></textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Action Type
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlactionTypeBase" runat="server" AppendDataBoundItems="true" CssClass="chosen-select">
                                                <asp:ListItem Text="- Please Select -" Value="000" Selected="true" />
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr baseaction="ActionType" id="ActionType">
                                        <td>Action Category
                                        </td>
                                        <td>
                                            <%-- Action <asp:DropDownList ID="ddlActionType" runat="server" Width="50%">
                                </asp:DropDownList>
                                <span></span>--%>
                                            <div id="divActionName">
                                                <input id="Button2" type="button" value="-- Select Action--" class="select align-left "
                                                    style="width: 95%;" /><span></span>
                                            </div>
                                            <%-- <div class="dropdownli" style="display: none;" id="Div2">--%>
                                            <ul id='ulCategory'>
                                            </ul>
                                            <%--   </div>--%>
                                        </td>
                                    </tr>
                                    <tr actiontype="GM" class="trHide">
                                        <td id="idGMReplication">Replication Component
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlReplication" runat="server" CssClass="chosen-select">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ActypeJob" class="trHide">
                                        <td>JobType
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddljobName" runat="server">
                                                <asp:ListItem Value="000">-Select Job Type-</asp:ListItem>
                                                <asp:ListItem Value="1">BackupJob</asp:ListItem>
                                                <asp:ListItem Value="2">CopyJob</asp:ListItem>
                                                <asp:ListItem Value="3">RestoreJob</asp:ListItem>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>

                                    </tr>

                                    <tr actiontype="AlertMechanism" class="trHide">
                                        <td></td>
                                        <td>
                                            <input type="text" id="txtSmsSuccessPage" class="form-control" />
                                            <input type="text" id="txtSmsFailurePage" class="form-control" />
                                            <input type="text" id="txtEmailSuccessPage" class="form-control" />
                                            <input type="text" id="txtEmailFailurePage" class="form-control" />
                                            <input type="text" id="txtAlertMessageType" value="0" class="form-control" />
                                            <input type="text" id="txtUserListId" class="form-control" />
                                        </td>
                                    </tr>
                                    <tr actiontype="SnapMirrorReplication" class="trHide">
                                        <td>Replication Component
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlSnapMirrorReplication" runat="server">
                                                <asp:ListItem Value="000">Select volume</asp:ListItem>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Wait" class="trHide">
                                        <td>Wait (miliseconds)
                                        </td>
                                        <td>
                                            <input type="text" id="txtWait" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="RsyncPath" class="trHide">
                                        <td>Rsync Path
                                        </td>
                                        <td>
                                            <input type="text" id="txtRsyncPath" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="RunTimeCredentials">
                                        <td class="padding-5">
                                            <input id="chkCredential" type="checkbox" title="Credential" />
                                            <span></span>
                                        </td>
                                        <td class="padding-5" id="tdRunTimeCredentials">Accept Credential Runtime
                                        </td>
                                    </tr>

                                    <tr actiontype="ActionNote">
                                        <td class="padding-5">
                                            <input id="chkActionNotes" type="checkbox" title="Note" />
                                            <span></span>
                                        </td>
                                        <td class="padding-5">Accept Note
                                        </td>
                                    </tr>

                                    <tr actiontype="ServerComponent" class="trHide">
                                        <td id="idServerName"></td>
                                        <td class="dropup">
                                            <asp:DropDownList ID="ddlServer" runat="server" CssClass="chosen-select" Style="width: 95%;">
                                                <%-- chosen-select--%>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <%-- <tr actiontype="ServerComponent_srvctl" class="trHide">
                                        <td id="idServerName_srvctl"></td>
                                        <td class="dropup">
                                            <asp:DropDownList ID="ddlServer_srvctl" runat="server" CssClass="chosen-select" Style="width: 95%;">
                                              
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>--%>



                                    <%-- <tr actiontype="MUltipleVM" class="trHide">
                                        <td id="idmultiplevm">Server 
                                        </td>
                                        <td class="dropup">
                                            <asp:DropDownList ID="multiplegrp" name="Multipleselect" runat="server" ClientIDMode="Static" CssClass="" multiple Style="width: 95%; height: 30px;">
                                            </asp:DropDownList>
                                        </td>
                                    </tr>--%>

                                    <tr actiontype="eBDRProfile" class="trHide">
                                        <td>Profile Name</td>

                                        <td>
                                            <asp:DropDownList ID="ddleBDRProfile" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="trVirtualSwitchType" class="trHide">
                                        <td>Virtual Switch Type
                                        </td>
                                        <td>
                                            <select id="ddltrVirtualSwitchType">
                                                <option value="000">Select Virtual Switch Type</option>
                                                <option value="Dis-Connected">Dis-Connected </option>
                                                <option value="Connected">Connected</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="SourceFile" class="trHide">
                                        <td id="tdSourceFile">Source Files
                                        </td>
                                        <td>
                                            <%--<input type="text" id="txtSourceFile" />--%>
                                            <textarea id="txtSourceFile" cols="33" rows="2" class="form-control"></textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ApplyIncrementalLogs" class="trHide">
                                        <td id="trDatabase">Database
                                        </td>
                                        <td class="dropup">
                                            <asp:DropDownList ID="ddlDB" runat="server" Style="width: 95%;" CssClass="chosen-select">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ScpServer" class="trHide">
                                        <td id="idTargetServerName"></td>
                                        <%--<td id="TargetServer">
                                            Target Server
                                        </td>
                                        <td id="idESXITargetServer">
                                            ESXI Target Server
                                        </td>--%>
                                        <td class="dropup">
                                            <asp:DropDownList ID="ddlTargetServer" runat="server" CssClass="chosen-select" Style="width: 95%;">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>



                                    <tr actiontype="TargetDatabase">
                                        <td id="idTargerDatabase">Target Database
                                        </td>
                                        <td class="dropup">
                                            <asp:DropDownList ID="ddlTargetDatabase" runat="server" CssClass="chosen-select" Style="width: 95%;">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="MailBoxDBbyServer" class="trHide">
                                        <td>Mail Database
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlMailDB" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="MailBoxDBbyTargetServer" class="trHide">
                                        <td>Target Mail Database
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlMailTargetDB" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ProcessID" class="trHide">
                                        <td class="padding-5" id="idprocessid">ProcessId
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtprocessId" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DB2SourceDataBase">
                                        <td class="dropup" id="tdDB2Source"></td>
                                        <td>
                                            <asp:DropDownList ID="ddlDB2Source" runat="server">
                                                <asp:ListItem Value="000">Select Database</asp:ListItem>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DNSServer">
                                        <td id="tdDNSServer">DNS Server
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlDNSServer" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="IsReturn" class="trHide">
                                        <td id="tdIsReturn">Return Flag
                                        </td>
                                        <td>
                                            <select id="ddlRetunFlag" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="1">True</option>
                                                <option value="2">False</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="MachineName">
                                        <td>Machine Name
                                        </td>
                                        <%-- <td  id="idMachineName">
                                Machine Name
                            </td>
                            <td  id="idSrcMachineName">
                                Source Machine Name
                            </td>
                            <td  id="idSrcConfigMachineName">
                                Source VM Config File
                            </td>--%>
                                        <td>
                                            <input type="text" id="txtMachineName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="TargetMachine">
                                        <td>Target Machine Name
                                        </td>
                                        <td>
                                            <input type="text" id="txtTargetMachine" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="SnapShotName">
                                        <td>Snap Shot Name
                                        </td>
                                        <td>
                                            <input type="text" id="txtSnapShotName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DSCLIServer" class="trHide">
                                        <td>DSCLI Server
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlDSCLIServer" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ESXIServer">
                                        <td>HMC Server
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlESXIServer" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DSCLIServer1" class="trHide">
                                        <td class="padding-5">DSCLI Server
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlDSCLIServer1" Width="50%" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ESXIServer1" class="trHide">
                                        <td class="padding-5">HMC Server
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlESXIServer1" Width="50%" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="optionsmkflash" class="trHide">
                                        <td>Options
                                        </td>
                                        <td>
                                            <select id="ddlmkflashoptions" style="width: 30%;">
                                                <option value="000">Select One </option>
                                                <option value="1">-tgtinhibit -nocp -record</option>
                                                <option value="2">-tgtse -tgtinhibit -nocp -record</option>
                                                <option value="3">-nocp</option>
                                                <option value="4">-record -persist</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="SourceSID">
                                        <td>Source SID
                                        </td>
                                        <td>
                                            <input type="text" id="txtSourceSID" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="TargetSID" class="trHide">
                                        <td id="idTargetSID"></td>
                                        <td>
                                            <input type="text" id="txtTargetSID" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="LSSID" class="trHide">
                                        <td id="idLSSID">LSS ID
                                        </td>
                                        <td>
                                            <input type="text" id="txtLSSID" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="changeSession" class="trHide">
                                        <td>Session
                                        </td>
                                        <td>
                                            <input type="text" id="txtSession" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DetailsLunsPairs" class="trHide">
                                        <td id="idDetailLuns"></td>
                                        <td>
                                            <textarea name="comments" id="txtLunsPairs" cols="35" rows="5" class="form-control">
                                </textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ExistingHost">
                                        <td>Existing Host
                                        </td>
                                        <td>
                                            <input type="text" id="txtExistingHost" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ExistingIp">
                                        <td>Existing IP
                                        </td>
                                        <td>
                                            <input type="text" id="txtExistingIp" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="NewHost">
                                        <td id="tdnewhost">New Host
                                        </td>
                                        <td>
                                            <input type="text" id="txtNewHost" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <%-- <tr actiontype="NewIp">
                            <td >
                                New Ip
                            </td>
                            <td >
                                <input type="text" id="txtNewIp" />
                            </td>
                        </tr>--%>
                                    <tr actiontype="VG" class="trHide">
                                        <td>InfraObject Name
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlGroupId" CssClass="chosen-select" runat="server" Style="width: 95% !important;">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="OS" class="trHide">
                                        <td>OS Command
                                        </td>
                                        <td>
                                            <input type="text" id="txtOSCommand" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="CustomScript" class="trHide">
                                        <td>Pre Exception Condition
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlPassword" runat="server" Width="50%">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="CellNo">
                                        <td class="padding-5">Cell No </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtCellNo" class="form-control" maxlength="10" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="EmailId">
                                        <td class="padding-5">Email ID</td>
                                        <td class="padding-5">
                                            <input type="text" id="txtEmailId" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="appuser">
                                        <td class="padding-5" id="tdAppUser"></td>
                                        <td class="padding-5">
                                            <input type="text" id="txtappuser" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="AddRemove" class="trHide">
                                        <td>Add/Remove
                                        </td>
                                        <td>
                                            <select id="ddlAddRemove" style="width: 30%;">
                                                <option value="000">Select One </option>
                                                <option value="1">Add </option>
                                                <option value="2">Remove </option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="Filter" class="trHide">
                                        <td>Filter
                                        </td>
                                        <td>
                                            <select id="ddlFilter">
                                                <option value="000">Select Filter</option>
                                                <option value="1">Status</option>
                                                <option value="2">Content Index State</option>
                                                <option value="3">Copy Queue Length</option>
                                                <option value="4">Replay Queue Length</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>


                                    <tr actiontype="Luns" class="trHide">
                                        <td>Luns
                                        </td>
                                        <td>
                                            <textarea name="comments" id="txtLuns" cols="35" rows="5" class="form-control">
                                </textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="MountPoints" class="trHide">
                                        <td>MountPoints
                                        </td>
                                        <td>
                                            <input type="text" id="txtMountPoints" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="LocalMountPoints" class="trHide">
                                        <td>Local MountPoints
                                        </td>
                                        <td>
                                            <input type="text" id="txtLocalMountPoints" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="RemoteMountPoints" class="trHide">
                                        <td>Remote MountPoints
                                        </td>
                                        <td>
                                            <input type="text" id="txtRemoteMountPoints" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="HostName">
                                        <td>Host Name
                                        </td>
                                        <td>
                                            <input type="text" id="txtHostName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="MountVGS" id="TrHideRemoteMountPoints">
                                        <td>VG
                                        </td>
                                        <td>
                                            <input type="text" id="txtMountVGS" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="SwitchOverSession" class="trHide">
                                        <td>New Session
                                        </td>
                                        <td>
                                            <input type="text" id="txtSwitchOverSession" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <%-- <tr actiontype="EMCSYMCLI">
                            <td >
                                EMC Console
                            </td>
                            <td >
                                
                                <span></span>
                            </td>
                        </tr>--%>

                                    <tr actiontype="UseSudo" class="trHide">
                                        <td id="idsubstitute">Substitute Authentication
                                        </td>
                                        <td>
                                            <select id="ddlUseSudo" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="1">Yes </option>
                                                <option value="2">No </option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="DBComponent_srvctl" class="trHide">
                                        <td id="idDB_srvctl"></td>
                                        <td class="dropup">
                                            <asp:DropDownList ID="DB_srvctl" runat="server" CssClass="chosen-select" Style="width: 95%;">
                                                <%--<asp:ListItem Value="000">--Select Database--</asp:ListItem>--%>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="node_srvctl" id="trnode_srvctl" class="trHide">
                                        <td id="idnode_srvctl"></td>
                                        <td class="dropup">
                                            <asp:DropDownList ID="node_srvctl" runat="server" CssClass="chosen-select" Style="width: 95%;">
                                                <%-- chosen-select--%>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>


                                    <tr actiontype="UseSSHProvider" class="trHide">
                                        <td id="idSSHProvider">SSHProvider
                                        </td>
                                        <td>
                                            <select id="ddlSSHProvider" style="width: 30%;">
                                                <%-- <option value="000" selected="selected">Select Option </option>--%>
                                                <option value="SSHProvider1">SSHProvider1 </option>
                                                <option value="SSHProvider2">SSHProvider2</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="PrOrDr" class="trHide">
                                        <td id="idPrDr">PR/DR
                                        </td>
                                        <td id="idScorceStorageID">Source Storage ID
                                        </td>
                                        <td>
                                            <select id="ddlPrDr" style="width: 30%;">
                                                <option selected="selected" value="000">Select Option </option>
                                                <option value="1">PR </option>
                                                <option value="2">DR </option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ControlFile" class="trControlFileHide">
                                        <td id="txtcontrolpath">Control File Path
                                        </td>
                                        <td>
                                            <input type="text" id="txtControlFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="StandByControlFile" class="trHide">
                                        <td>StandBy Control Path
                                        </td>
                                        <td>
                                            <input type="text" id="txtStandByControlFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="file" class="trHide" id="filess">
                                        <td id="fileorname">File
                                        </td>
                                        <td>
                                            <input type="text" id="txtFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="TempFile" class="trHide">
                                        <td>Temp File
                                        </td>
                                        <td>
                                            <input type="text" id="txtTempFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ControlFileScript" class="trHide">
                                        <td>Control File
                                        </td>
                                        <td>
                                            <input type="text" id="txtControlFileScript" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ScriptFile" class="trHide">
                                        <td>Script File
                                        </td>
                                        <td>
                                            <input type="text" id="txtScriptFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ByScriptFile" class="trHide">
                                        <td>Script File
                                        </td>
                                        <td>
                                            <input type="text" id="txtByScriptFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Fastcopy" class="trHide">
                                        <td>Fast Copy
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlFastcopy" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="JobQue" class="trHide">
                                        <td>Job Queue
                                        </td>
                                        <td>
                                            <input type="text" id="txtJobQue" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Listner" class="trHide">
                                        <td>Listener Name
                                        </td>
                                        <td>
                                            <input type="text" id="txtListnerName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Passwordhide" class="trHide">
                                        <td>Is Password 
                                        </td>
                                        <td>
                                            <select id="dllpasswordhide" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="1">Yes </option>
                                                <option value="2">No </option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DomainName" class="trHide">
                                        <td>Domain Name
                                        </td>
                                        <td>
                                            <input type="text" id="txtDomainName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="TargetFile" class="trHide">
                                        <td id="idTargetFile">Target Files
                                        </td>
                                        <td>
                                            <%--<input type="text" id="txtTargetFile" />--%>
                                            <textarea id="txtTargetFile" cols="33" rows="2" class="form-control"></textarea>
                                            <span></span>
                                        </td>
                                    </tr>


                                      <tr actiontype="FastCopyPath" class="trHide">
                                        <td id="idFastCopyPath"></td>
                                        <td>
                                            <input type="text" id="txtFastCopyPath" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>





                                    <tr actiontype="DiskName" class="trHide" id="TrDiskNameiddetails">
                                        <td id="tdDiskName"></td>
                                        <td>
                                            <input type="text" id="txtDiskName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="InterfacePassword" class="trHide">
                                        <td id="idInterfacePassword1"></td>
                                        <td>
                                            <input type="password" id="txtInterfacePassword" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="AccessKey" class="trHide">
                                        <td id="idAccessKey"></td>
                                        <td>
                                            <input type="password" id="txtAccessKey" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="LunStatus" class="trHide">
                                        <td>Lun Status
                                        </td>
                                        <td>
                                            <select id="ddllunstatus" style="width: 30%;">
                                                <option value="000">Select Status</option>
                                                <option value="1">Online</option>
                                                <option value="2">Mapping</option>
                                                <option value="3">Serial No</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>




                                    <tr actiontype="SourceFolder" id="Tr1">
                                        <td id="idsourcefolder">
                                        <td>
                                            <input type="text" id="txtSourceFolder" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="TargetFolder" class="trHide">
                                        <td id="idtargetfolder"></td>
                                        <td>
                                            <input type="text" id="txtTargetFolder" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="TimeOut">
                                        <td>Time Out(Sec)
                                        </td>
                                        <td>
                                            <input type="text" id="txtTimeOut" class="form-control" placeholder="Enter Timeout" />
                                            <span id="spntxtTimeOut"></span>
                                            <%-- <asp:RequiredFieldValidator ID="rfvtimout" CssClass="error" runat="server" ControlToValidate="txtTimeOut"
                                                ErrorMessage="Enter Timout" Display="Dynamic"></asp:RequiredFieldValidator>--%>
                                            <%-- <asp:RegularExpressionValidator ID="timeout" runat="server" CssClass="error" ControlToValidate="txtTimeOut"
                                                ErrorMessage="Enter Valid Timeout" ValidationExpression="^(?![\d\W]+$).+"
                                                Display="Dynamic"></asp:RegularExpressionValidator>--%>
                                        </td>
                                    </tr>
                                    <tr actiontype="VirtualMachine">
                                        <%--<td >
                              Virtual Machine<br />
                                (VMX Path)
                            </td>--%>
                                        <td id="idMachineName">Machine Name
                                        </td>
                                        <td id="idSrcMachineName">Source Machine Name
                                        </td>
                                        <td id="idSrcConfigMachineName">Source VM Config File
                                        </td>
                                        <td>
                                            <input type="text" id="txtVirtualMachine" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DestinationPath">
                                        <td id="idDestinationPath">Destination Path
                                        </td>
                                        <td id="idTargetPath">Target Path
                                        </td>
                                        <td id="IdFullpath">Full File Path
                                        </td>
                                        <td>
                                            <input type="text" id="txtDestinationPath" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="SnapMirrorVolume" class="trHide">
                                        <td>Volume
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlSnapMirrorVolume" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="SnapMirrorStorage" class="trHide">
                                        <td>Source Storage Id
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlSnapMirrorStorage" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="vmPath" class="trHide">
                                        <td id="tdVMPath">VM Path
                                        </td>
                                        <td id="tdDataStore">Data Store
                                        </td>
                                        <td>
                                            <input type="text" id="txtVmPath" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                   
                                    <tr actiontype="NewText" class="trHide">
                                        <td>New Text
                                        </td>
                                        <td>
                                            <input type="text" id="txtNewText" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="RouterConfiguration" class="trHide">
                                        <td>Router Configuration
                                        </td>
                                        <td>
                                            <input type="text" id="txtRouterConfiguration" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Command" class="trHide">
                                        <td id="idcommand"></td>
                                        <td>
                                            <%--<input type="text" id="txtCommand" class="form-control" />
                                            <span></span>--%>
                                            <textarea id="txtCommand" cols="33" rows="2" class="form-control"></textarea>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="EMCSYMCLI">
                                        <td>EMC Console
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlEMCSYMCLIServer" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ZoneName">
                                        <td class="padding-5">Zone Name
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtZoneName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ForestFSMOROle" class="trHide">
                                        <td>Forest FSMO Role</td>
                                        <td>
                                            <select id="ddlForestFSMOROle">
                                                <option selected="selected" value="000">-- Select Option --</option>
                                                <option value="1">SchemaMaster</option>
                                                <option value="2">DomainNamingMaster</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="DomainFSMOROle" class="trHide">
                                        <td>Domain FSMO Role</td>
                                        <td>
                                            <select id="ddlDomainFSMOROle">
                                                <option selected="selected" value="000">-- Select Option --</option>
                                                <option value="1">PDCEmulator</option>
                                                <option value="2">RIDMaster</option>
                                                <option value="3">InfrastructureMaster</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>


                                    <tr actiontype="InstantGeneration" class="trHide" id="TrInstantGeneration">
                                        <td>Instances Generation
                                        </td>
                                        <td>
                                            <select id="ddlInstantGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="Current Generation Instances">Current Generation Instances</option>
                                                <option value="Previous Generation Instances">Previous Generation Instances</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="InstantFamilyCurrentGeneration" class="trHide" id="TrInstantFamilyCurrentGeneration">
                                        <td>Instances Family
                                        </td>
                                        <td>
                                            <select id="ddlInstantFamilyCurrentGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="General purpose">General purpose</option>
                                                <option value="Compute optimized">Compute optimized</option>
                                                <option value="Memory optimized">Memory optimized</option>
                                                <option value="Storage optimized">Storage optimized</option>
                                                <option value="GPU instances">GPU instances</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="InstantFamilyPreviousGeneration" class="trHide" id="TrInstantFamilyPreviousGeneration">
                                        <td>Instances Family
                                        </td>
                                        <td>
                                            <select id="ddlInstantFamilyPreviousGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="General purpose">General purpose</option>
                                                <option value="Compute optimized">Compute optimized</option>
                                                <option value="Memory optimized">Memory optimized</option>
                                                <option value="Storage optimized">Storage optimized</option>
                                                <option value="GPU instances">GPU instances</option>
                                                <option value="Micro instances">Micro instances</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="GeneralpurposeInstanceTypesCurrentGeneration" class="trHide" id="TrGeneralpurposeInstanceTypesCurrentGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlGeneralpurposeInstanceTypesCurrentGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="t2.micro">t2.micro</option>
                                                <option value="t2.small">t2.small</option>
                                                <option value="t2.medium">t2.medium</option>
                                                <option value="m3.medium">m3.medium</option>
                                                <option value="m3.large">m3.large</option>
                                                <option value="m3.xlarge">m3.xlarge</option>
                                                <option value="m3.2xlarge">m3.2xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ComputeoptimizedInstanceTypesCurrentGeneration" class="trHide" id="TrComputeoptimizedInstanceTypesCurrentGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlComputeoptimizedInstanceTypesCurrentGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="c4.large">c4.large</option>
                                                <option value="c4.xlarge">c4.xlarge</option>
                                                <option value="c4.2xlarge">c4.2xlarge</option>
                                                <option value="c4.4xlarge">c4.4xlarge</option>
                                                <option value="c4.8xlarge">c4.8xlarge</option>
                                                <option value="c3.large">c3.large</option>
                                                <option value="c3.xlarge">c3.xlarge</option>
                                                <option value="c3.2xlarge">c3.2xlarge</option>
                                                <option value="c3.4xlarge">c3.4xlarge</option>
                                                <option value="c3.8xlarge">c3.8xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="MemoryoptimizedInstanceTypesCurrentGeneration" class="trHide" id="TrMemoryoptimizedInstanceTypesCurrentGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlMemoryoptimizedInstanceTypesCurrentGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="r3.large">r3.large</option>
                                                <option value="r3.xlarge">r3.xlarge</option>
                                                <option value="r3.2xlarge">r3.2xlarge</option>
                                                <option value="r3.4xlarge">r3.4xlarge</option>
                                                <option value="r3.8xlarge">r3.8xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="StorageoptimizedInstanceTypesCurrentGeneration" class="trHide" id="TrStorageoptimizedInstanceTypesCurrentGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlStorageoptimizedInstanceTypesCurrentGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="i2.xlarge">i2.xlarge</option>
                                                <option value="i2.2xlarge">i2.2xlarge</option>
                                                <option value="i2.4xlarge">i2.4xlarge</option>
                                                <option value="i2.8xlarge">i2.8xlarge</option>
                                                <option value="hs1.8xlarge">hs1.8xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="GPUinstancesInstanceTypesCurrentGeneration" class="trHide" id="TrGPUinstancesInstanceTypesCurrentGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlGPUinstancesInstanceTypesCurrentGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="g2.2xlarge">g2.2xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="GeneralpurposeInstanceTypesPreviousGeneration" class="trHide" id="TrGeneralpurposeInstanceTypesPreviousGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlGeneralpurposeInstanceTypesPreviousGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="m1.small">m1.small</option>
                                                <option value="m1.medium">m1.medium</option>
                                                <option value="m1.large">m1.large</option>
                                                <option value="m1.xlarge">m1.xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ComputeoptimizedInstanceTypesPreviousGeneration" class="trHide" id="TrComputeoptimizedInstanceTypesPreviousGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlComputeoptimizedInstanceTypesPreviousGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="c1.medium">c1.medium</option>
                                                <option value="c1.xlarge">c1.xlarge</option>
                                                <option value="cc2.8xlarge">cc2.8xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="MemoryoptimizedInstanceTypesPreviousGeneration" class="trHide" id="TrMemoryoptimizedInstanceTypesPreviousGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlMemoryoptimizedInstanceTypesPreviousGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="m2.xlarge">m2.xlarge</option>
                                                <option value="m2.2xlarge">m2.2xlarge</option>
                                                <option value="m2.4xlarge">m2.4xlarge</option>
                                                <option value="cr1.8xlarge">cr1.8xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="StorageoptimizedInstanceTypesPreviousGeneration" class="trHide" id="TrStorageoptimizedInstanceTypesPreviousGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlStorageoptimizedInstanceTypesPreviousGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="hi1.4xlarge">hi1.4xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="GPUinstancesInstanceTypesPreviousGeneration" class="trHide" id="TrGPUinstancesInstanceTypesPreviousGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlGPUinstancesInstanceTypesPreviousGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="cg1.4xlarge">cg1.4xlarge</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="MicroinstancesInstanceTypesPreviousGeneration" class="trHide" id="TrMicroinstancesInstanceTypesPreviousGeneration">
                                        <td>Instances Type
                                        </td>
                                        <td>
                                            <select id="ddlMicroinstancesInstanceTypesPreviousGeneration">
                                                <option value="000">Select Option </option>
                                                <option value="t1.micro">t1.micro</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>


                                    <tr actiontype="ObjecttypeforDNSforNetwork" class="trHide">
                                        <td>Object Type
                                        </td>
                                        <td>
                                            <select id="ddlObjecttypeforQueryDNS">
                                                <option value="000">Select Option </option>
                                                <option value="a">a</option>
                                                <option value="a6">a6</option>
                                                <option value="aaaa">aaaa</option>
                                                <option value="afsdb">afsdb</option>
                                                <option value="any">any</option>
                                                <option value="apl">apl</option>
                                                <option value="axfr">axfr</option>
                                                <option value="cert">cert</option>
                                                <option value="cname">cname</option>
                                                <option value="dhcid">dhcid</option>
                                                <option value="div">div</option>
                                                <option value="dname">dname</option>
                                                <option value="dnskey">dnskey</option>
                                                <option value="ds">ds</option>
                                                <option value="gpos">gpos</option>
                                                <option value="hinfo">hinfo</option>
                                                <option value="hip">hip</option>
                                                <option value="ipseckey">ipseckey</option>
                                                <option value="isdn">isdn</option>
                                                <option value="ixfr">ixfr</option>
                                                <option value="key">key</option>
                                                <option value="keydata">keydata</option>
                                                <option value="kx">kx</option>
                                                <option value="loc">loc</option>
                                                <option value="maila">maila</option>
                                                <option value="mailb">mailb</option>
                                                <option value="mb">mb</option>
                                                <option value="md">md</option>
                                                <option value="mf">mf</option>
                                                <option value="mg">mg</option>
                                                <option value="minfo">minfo</option>
                                                <option value="mr">mr</option>
                                                <option value="mx">mx</option>
                                                <option value="naptr">naptr</option>
                                                <option value="none">none</option>
                                                <option value="ns">ns</option>
                                                <option value="nsap">nsap</option>
                                                <option value="nsap_ptr">nsap_ptr</option>
                                                <option value="nsec">nsec</option>
                                                <option value="nsec3">nsec3</option>
                                                <option value="nsec3param">nsec3param</option>
                                                <option value="null">null</option>
                                                <option value="nxt">nxt</option>
                                                <option value="opt">opt</option>
                                                <option value="ptr">ptr</option>
                                                <option value="px">px</option>
                                                <option value="rp">rp</option>
                                                <option value="rrsig">rrsig</option>
                                                <option value="rt">rt</option>
                                                <option value="sig">sig</option>
                                                <option value="soa">soa</option>
                                                <option value="spf">spf</option>
                                                <option value="srv">srv</option>
                                                <option value="sshfp">sshfp</option>
                                                <option value="tkey">tkey</option>
                                                <option value="tsig">tsig</option>
                                                <option value="txt">txt</option>
                                                <option value="unspec">unspec</option>
                                                <option value="wks">wks</option>
                                                <option value="x25">x25</option>

                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ProgramName" class="trHide">
                                        <td>Program Name
                                        </td>
                                        <td>
                                            <select id="dllProgramName" style="width: 95%;">
                                                <option value="000">--Select program--</option>
                                                <option value="Extract">Extract</option>
                                                <option value="Replicat">Replicat</option>

                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="DeviceGroup" class="trHide">
                                        <td id="idDeviceGroup">Device Group</td>
                                        <td>
                                            <input type="text" id="txtDeviceGroup" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="HPUXServer">
                                        <td>Server
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlHPUXServer" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <%--  <tr actiontype="MountPoints" class="trHide">
                            <td >
                                Mount Point
                            </td>
                            <td >
                                <input type="text" id="txtMountPoints" />
                                <span></span>
                            </td>
                        </tr>--%>
                                    <tr actiontype="Secound" class="trHide">
                                        <td id="tdSecond"></td>
                                        <td>
                                            <input type="text" id="txtSecound" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ApplicationName" class="trHide">
                                        <td>Application
                                        </td>
                                        <td>
                                            <input type="text" id="txtApplicationName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="MapFile" class="trHide">
                                        <td>Map File
                                        </td>
                                        <td>
                                            <input type="text" id="txtMapFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Task" class="trHide">
                                        <td>Task
                                        </td>
                                        <td>
                                            <textarea name="Task" id="txtTask" cols="35" rows="5" class="form-control">
                                </textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="FileSysMountPoint" class="trHide">
                                        <td>
                                            <textarea name="Task" id="txtFileSysMountPoint" cols="35" rows="5" class="form-control">
                                </textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <%--<tr actiontype="MountVGS" id="TrHideRemoteMountPoints">
                            <td >
                                VG Name
                            </td>
                            <td >
                                <input type="text" id="txtMountVGS" />
                                <span></span>
                            </td>
                        </tr>--%>
                                    <tr actiontype="WorkflowList" id="TrWorkflowList">
                                        <td>All Work Flow List
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlWorkflowList" runat="server" CssClass="chosen-select">
                                                <asp:ListItem Value="000">--Select Workflow--</asp:ListItem>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="selectedWorkFlowAction" id="TrselectedWorkFlowAction">
                                        <td>WorkFlow Action
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlWFAction" runat="server">
                                                <asp:ListItem Value="000">--Select Action--</asp:ListItem>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="parallalWorkFlowAction" class="trHide" style="display: none">

                                        <td style="display: none" colspan="2">
                                            <input type="text" id="HiddenFieldForParrelAction" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="selectedparallalWorkFlowAction" id="TrselectedWorkFlowAction1">
                                        <td>WorkFlow Action
                                        </td>
                                        <td>
                                            <input id='chkuseroarallelaction' class='select align-left' style='width: 95%' type='button' value=' - Select Workflow Action - ' />
                                            <%--                                            <ul id="ulparallelaction" class="cb-element" style="display: none;">--%>
                                            <ul id="ulparallelaction" class="select align-left" style="width: 95%">
                                                <%--  <li id="mainli4">
                                                    <input type="checkbox" value="">
                                                    <label>kranti</label></li>
                                                <li id="Li1">
                                                    <input type="checkbox" value="">
                                                    <label>Sonali</label></li>
                                                <li id="Li2">
                                                    <input type="checkbox" value="">
                                                    <label>nirja</label></li>--%>
                                            </ul>

                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="dependencyTime" id="TrdependencyTime">
                                        <td>Dependency Time
                                        </td>
                                        <td>
                                            <select id="ddlDependencyTime" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="1">Action Completion </option>
                                                <option value="2">Specify Duration </option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="WFTime" id="TrTime">
                                        <td>Wait Time(Seconds)
                                        </td>
                                        <td>
                                            <input type="text" id="txtTime" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DB2TargetDataBase">
                                        <td id="tdDB2Target">Target Database
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlDB2Target" runat="server">
                                                <asp:ListItem Value="000">Select Database</asp:ListItem>
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>


                                  
                                    <tr actiontype="DiskGroup" class="trHide">
                                        <td id="tdDiskGroup"></td>
                                        <td>
                                            <input type="text" id="txtDiskGroup" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>



                                    <tr actiontype="StartStop" class="trHide" id="Tr2">
                                        <td id="tdStartStop">Start/Stop</td>
                                        <td>
                                            <select id="ddlStartStop">
                                                <option value="000">----Select Option --- </option>
                                                <option id="srvctl_running" value="Start">Start </option>
                                                <option id="srvctl_stopped" value="Stop">Stop</option>
                                            </select>
                                        </td>
                                    </tr>




                                      <tr actiontype="RecordType" class="trHide">
                                        <td>Record Type
                                        </td>
                                        <td>
                                            <select id="ddlrecordtype" style="width: 30%;">
                                                <option value="000">Select Record Type </option>
                                                <option value="Host">Host</option>
                                                <option value="A">A</option>
                                                <option value="AAAA">AAAA</option>
                                                <option value="CNAME">CNAME</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                     <tr actiontype="RecordIPAddress" class="trHide">
                                        <td>Record contains IP Address
                                        </td>
                                        <td>
                                            <select id="ddlRecordIPAddress" style="width: 30%;">
                                                <option value="000">Select IPAddress </option>
                                                <option value="Single IP Address">Single IP Address</option>
                                                <option value="Multiple IP Address">Multiple IP Address</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>




                                    <tr actiontype="CheckOutput" class="trHide">
                                        <%--  <td>Check Output
                                        </td>--%>
                                        <td id="tdCheckOutput"></td>
                                        <td>
                                            <input type="text" id="txtcheckOutput" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>


                                     <tr actiontype="OriginalText" class="trHide">
                                        <td id="idOriginal"></td>
                                        <td>
                                            <input type="text" id="txtOriginalText" class="form-control" />
                                          <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="trJobTypeDoubleTake">
                                        <td>Job Type
                                        </td>
                                        <td>
                                            <select id="ddltdJobTypeDoubleTake">
                                                <option value="000">- Select Job Type -</option>
                                                <option value="ClusterAwareDthv">ClusterAwareDthv</option>
                                                <option value="ClusterAwareFilesAndFolders">ClusterAwareFilesAndFolders</option>
                                                <option value="ClusterAwareHV2V">ClusterAwareHV2V</option>
                                                <option value="ClusterAwareMultiSelectDthv">ClusterAwareMultiSelectDthv</option>
                                                <option value="DataOnlyImageProtection">DataOnlyImageProtection</option>
                                                <option value="DataOnlyImageRecovery">DataOnlyImageRecovery</option>
                                                <option value="Diagnostics">Diagnostics</option>
                                                <option value="Dthv">Dthv</option>
                                                <option value="Exchange">Exchange</option>
                                                <option value="ExchangeClustered">ExchangeClustered</option>
                                                <option value="FilesAndFolders">FilesAndFolders</option>
                                                <option value="FullServerFailover">FullServerFailover</option>
                                                <option value="FullServerImageProtection">FullServerImageProtection</option>
                                                <option value="FullServerImageRecovery">FullServerImageRecovery</option>
                                                <option value="LinuxFullServerFailover">LinuxFullServerFailover</option>
                                                <option value="Lvra">Lvra</option>
                                                <option value="MoveDataOnlyMigration">MoveDataOnlyMigration</option>
                                                <option value="MoveServerMigration">MoveServerMigration</option>
                                                <option value="MultiSelectDthv">MultiSelectDthv</option>
                                                <option value="Sql">Sql</option>
                                                <option value="SqlClustered">SqlClustered</option>
                                                <option value="Uvra">Uvra</option>
                                                <option value="V2V">V2V</option>
                                                <option value="Vra">Vra</option>
                                                <option value="VraMove">VraMove</option>
                                                <option value="ClusterAwareSql">ClusterAwareSql</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="trHighLevalStateDoubleTake">
                                        <td>High Level State
                                        </td>
                                        <td>
                                            <select id="dlltrHighLevalStateDoubleTake">
                                                <option value="000">- Select High Level State -</option>
                                                <option value="Unknown">Unknown</option>
                                                <option value="Created">Created</option>
                                                <option value="Deleting">Deleting</option>
                                                <option value="FailedBack">FailedBack</option>
                                                <option value="FailedOver">FailedOver</option>
                                                <option value="FailingBack">FailingBack</option>
                                                <option value="FailingOver">FailingOver</option>
                                                <option value="FailoverFailed">FailoverFailed</option>
                                                <option value="FailoverPending">FailoverPending</option>
                                                <option value="Mirroring">Mirroring</option>
                                                <option value="MirrorRequired">MirrorRequired</option>
                                                <option value="Paused">Paused</option>
                                                <option value="Pausing">Pausing</option>
                                                <option value="Protecting">Protecting</option>
                                                <option value="Provisioning">Provisioning</option>
                                                <option value="Restored">Restored</option>
                                                <option value="RestoreFailed">RestoreFailed</option>
                                                <option value="RestorePaused">RestorePaused</option>
                                                <option value="RestoreRequired">RestoreRequired</option>
                                                <option value="Restoring">Restoring</option>
                                                <option value="Resuming">Resuming</option>
                                                <option value="Reversing">Reversing</option>
                                                <option value="Reverting">Reverting</option>
                                                <option value="Starting">Starting</option>
                                                <option value="Stopped">Stopped</option>
                                                <option value="Stopping">Stopping</option>
                                                <option value="Undoing">Undoing</option>
                                                <option value="RevertingSnapshot">RevertingSnapshot</option>
                                                <option value="UpdatingTargetImage">UpdatingTargetImage</option>
                                                <option value="CredentialsRequired">CredentialsRequired</option>
                                                <option value="ActivationCodeWarning">ActivationCodeWarning</option>
                                                <option value="ActivationCodeError">ActivationCodeError</option>
                                                <option value="EngineConnectionWarning">EngineConnectionWarning</option>
                                                <option value="EngineConnectionError">EngineConnectionError</option>
                                                <option value="EngineServiceWarning">EngineServiceWarning</option>
                                                <option value="EngineServiceError">EngineServiceError</option>
                                                <option value="ServerCommunicationWarning">ServerCommunicationWarning</option>
                                                <option value="ServerCommunicationError">ServerCommunicationError</option>
                                                <option value="TargetInfoNotAvailable">TargetInfoNotAvailable</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>


                                    <tr actiontype="BackUpFile" id="idBackUpFile">
                                        <td id="backupfile">BackUp File
                                        </td>
                                        <td>
                                            <input type="text" id="txtBackUpFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="TraceFile">
                                        <td id="idTraceFile">Trace File
                                        </td>
                                        <td>
                                            <input type="text" id="txtTraceFile" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Expression" id="idExpression">
                                        <td>Expression
                                        </td>
                                        <td>
                                            <select id="ddlExpression">
                                                <option value="000">- Select Expression -</option>
                                                <option value="1">= </option>
                                                <option value="2">< </option>
                                                <option value="3">> </option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ProcessCount" class="trHide">
                                        <td>Process Count
                                        </td>
                                        <td>
                                            <input type="text" id="txtProcessCount" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="hDisc" class="trHide">
                                        <td>HDisks
                                        </td>
                                        <td>
                                            <input type="text" id="txthDisc" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="horcomInstance" class="trHide">
                                        <td>Horcom Instance
                                        </td>
                                        <td>
                                            <input type="text" id="txtHorcomInstance" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="restorePoint">
                                        <td>Restore Point
                                        </td>
                                        <td>
                                            <input type="text" id="txtRestorePoint" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="AlertText">
                                        <td id="tdAlertText"></td>
                                        <td>
                                            <textarea id="txtAlertText" cols="35" rows="5" class="form-control">
                                </textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="EmailAndSMS">
                                        <td>Alert Mode Type
                                        </td>
                                        <td>
                                            <select id="ddlEmailOrSMS">
                                                <option value="000">- Select Alert Mode Type -</option>
                                                <option value="1">Email</option>
                                                <option value="2">SMS</option>
                                                <option value="3">Email and SMS</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ProcessFlow">
                                        <td>Process Flow
                                        </td>
                                        <td>
                                            <textarea id="txtProcessFlow" cols="35" rows="5" class="form-control">
                                </textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="VolName">
                                        <td>Volume Name
                                        </td>
                                        <td>
                                            <input type="text" id="txtVolName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="FileSystem" class="trHide">
                                        <td id="idFileSystem"></td>
                                        <td>
                                            <input type="text" id="txtFileSystem" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Drive">
                                        <td>Drive
                                        </td>
                                        <td>
                                            <input type="text" id="txtDrive" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="RAM">
                                        <td>RAM
                                        </td>
                                        <td>
                                            <input type="text" id="txtRam" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="CPU">
                                        <td>CPU
                                        </td>
                                        <td>
                                            <input type="text" id="txtCPU" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DiskSize">
                                        <td>Disk Size
                                        </td>
                                        <td>
                                            <input type="text" id="txtDiskSize" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="guestOsServer" class="trHide">
                                        <td>Guest OS Server
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlGuestOsServer" CssClass="chosen-select" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="NewIp">
                                        <td id="tdNewIP">New IP
                                        </td>
                                        <td id="tdIP">IP Address
                                        </td>
                                        <td id="tdZoneIP">Zone IP
                                        </td>
                                        <td id="tdFTPIL">FTP IP
                                        </td>
                                        <td>
                                            <input type="text" id="txtNewIp" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="subnetMask" class="trHide">
                                        <td>Subnet Mask
                                        </td>
                                        <td>
                                            <input type="text" id="txtsubnetMask" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="gateWay" class="trHide">
                                        <td>Gate Way
                                        </td>
                                        <td>
                                            <input type="text" id="txtGateWay" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="primaryDNS" class="trHide">
                                        <td>Primary DNS
                                        </td>
                                        <td>
                                            <input type="text" id="txtPrimary" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="secoundaryDNS" class="trHide">
                                        <td>Secoundary DNS
                                        </td>
                                        <td>
                                            <input type="text" id="txtsecoundaryDNS" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="script" class="trHide">
                                        <td>Script
                                        </td>
                                        <td>
                                            <input type="text" id="txtScript" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ServiceName" class="trHide">
                                        <td id="tdServiceName"></td>
                                        <td>
                                            <input type="text" id="txtServiceName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="Test" class="trHide">
                                        <td class="padding-5">
                                            <input type="text" id="txtVGName" />
                                            <input type="text" id="txtMountPoint" />
                                            <input type="text" id="txtSUDOUser" />
                                            <input type="text" id="txtScriptName" />
                                            <input type="text" id="txtScriptCommand" />
                                            <input type="text" id="txtPassword" />
                                            <input type="text" id="txtChkBxSoftVal" />
                                            <asp:DropDownList ID="ddlActionType" runat="server" Width="50%">
                                                <asp:ListItem Text="- Please Select -" Value="000" Selected="true" />
                                            </asp:DropDownList>
                                        </td>
                                    </tr>

                                    <tr actiontype="OracleSID" class="trHide">
                                        <td>Oracle-Sid
                                        </td>
                                        <td>
                                            <input type="text" id="txtOracleSID" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Owner" class="trHide">
                                        <td>Owner Name
                                        </td>
                                        <td>
                                            <div id="idOwnerHeader">
                                                <input id="Button1" type="button" value="-- Select Owner--" class="select align-left"
                                                    style="width: 95%;" /><span></span>
                                            </div>
                                            <div class="dropdownli" style="display: none;" id="iddropdownli">
                                                <asp:CheckBoxList runat="server" ID="idOwnerName">
                                                    <asp:ListItem Enabled="false">Select Owner </asp:ListItem>
                                                </asp:CheckBoxList>
                                            </div>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ShellPrompt">
                                        <td class="padding-5">ShellPrompt
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtshellprompt" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="RelationshipId">
                                        <td class="padding-5">Relationship ID
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtRelationshipId" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="CheckState">
                                        <td class="padding-5">Check State
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtCheckState" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="RecoveryPlan">
                                        <td class="padding-5">Recovery Plan Name
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtRecoveryPlan" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ClusterName">
                                        <td class="padding-5">Cluster Name
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtClusterName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ClusterGroupResource">
                                        <td class="padding-5">Cluster Group Resource
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtClusterGroupResource" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ScriptBlock" class="trHide">
                                        <td id="idScriptBlock">Script Block</td>
                                        <td>
                                            <textarea id="txtscriptblock" cols="35" rows="5" class="form-control">
                                         </textarea>
                                            <span id="txtscriptblockSpan"></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="URL">
                                        <td class="padding-5">URL </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtURL" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="HTMLContents" class="trHide">
                                        <td>HTML Contents</td>
                                        <td>
                                            <textarea id="txthtmlcontents" cols="35" rows="5" class="form-control">
                                         </textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="WorkflowActionType" class="trHide">
                                        <td>DR Operation Status</td>
                                        <td>
                                            <select id="ddlWorkflowActionType" style="width: 30%;">
                                                <option selected="selected" value="000">-- Select Option --</option>
                                                <option value="2">SwitchOverCompleted</option>
                                                <option value="5">SwitchBackCompleted</option>
                                                <option value="8">FailOverCompleted</option>
                                                <option value="11">FailBackCompleted</option>
                                                <option value="14">CustomCompleted</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="ReplicationMode" class="trHide">
                                        <td>Replication Mode</td>
                                        <td>
                                            <select id="ddlReplicationMode" style="width: 30%;">
                                                <option selected="selected" value="000">-- Select Option --</option>
                                                <option value="1">Recursive</option>
                                                <option value="2">Shallow</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Query" class="trHide">
                                        <td>Query</td>
                                        <td>
                                            <textarea id="txtQuery" cols="35" rows="5" class="form-control">
                                         </textarea>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="StoreProcedure" class="trHide">
                                        <td>Store Procedure</td>
                                        <td>
                                            <textarea id="txtStoreProcedure" cols="35" rows="5" class="form-control">
                                         </textarea>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="MDF" class="trHide">
                                        <td>MDF</td>
                                        <td>
                                            <input type="text" id="txtMdf" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="LDF" class="trHide">
                                        <td>LDF</td>
                                        <td>
                                            <input type="text" id="txtLdf" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="DatabaseNameText" class="trHide">
                                        <td>MSSQL Database Name</td>
                                        <td>
                                            <input type="text" id="txtDatabaseNameText" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="NameSpace" class="trHide">
                                        <td>Name Space</td>
                                        <td>
                                            <input type="text" id="txtNameSpace" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="StarMode" class="trHide">
                                        <td>Start Mode</td>
                                        <td>
                                            <select id="ddlStarMode" style="width: 30%;">
                                                <option selected="selected" value="000">-- Select Option --</option>
                                                <option value="1">Automatic</option>
                                                <option value="2">Manual</option>
                                                <option value="3">Disabled</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="JobName" class="trHide">
                                        <td>Job Name
                                        </td>
                                        <td>
                                            <input type="text" id="txtJobName" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="JobStatus" class="trHide">
                                        <td>Job Status</td>
                                        <td>
                                            <select id="ddlJobStatus" style="width: 30%;">
                                                <option selected="selected" value="000">-- Select Option --</option>
                                                <option value="1">Executing</option>
                                                <option value="2">Waiting for thread</option>
                                                <option value="3">Between retries</option>
                                                <option value="4">Idle</option>
                                                <option value="5">Suspended</option>
                                                <option value="6">Performing completion actions</option>
                                                <option value="7">UNKNOWN</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="PlanState" class="trHide">
                                        <td>Plan State</td>
                                        <td>
                                            <select id="ddlPlanState" style="width: 30%;">
                                                <option selected="selected" value="000">-- Select Option --</option>
                                                <option value="1">cancelling</option>
                                                <option value="2">error</option>
                                                <option value="3">failedOver</option>
                                                <option value="4">needsCleanup</option>
                                                <option value="5">needsFailover</option>
                                                <option value="6">needsReprotect</option>
                                                <option value="7">needsRollback</option>
                                                <option value="8">prompting</option>
                                                <option value="9">protecting</option>
                                                <option value="10">ready</option>
                                                <option value="11">running</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="Resource">
                                        <td class="padding-5">Cluster Resource
                                        </td>
                                        <td class="padding-5">
                                            <input type="text" id="txtresource" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="CredentialsHiddenControl" class="trHide">
                                        <td></td>
                                        <td>
                                            <input type="text" id="txtCredential" class="form-control" />
                                        </td>
                                    </tr>

                                    <tr actiontype="MssqlStatus" class="trHide">
                                        <td>MSSql Status
                                        </td>
                                        <td>
                                            <select id="ddlstatus" style="width: 30%;">
                                                <option value="000">Select Status</option>
                                                <option value="1">Online</option>
                                                <option value="2">Offline</option>
                                                <option value="3">IsExist</option>
                                                <option value="4">NotExist</option>
                                                <option value="5">Restricted</option>
                                                <option value="6">Restoring</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="trddlVerifyAttachStatus" class="trHide">
                                        <td>Status
                                        </td>
                                        <td>
                                            <select id="ddlVerifyAttachStatus" style="width: 95%;">
                                                <option value="000">Select Status</option>
                                                <option value="1">Attach</option>
                                                <option value="2">Detach</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="trddlVerifyVolMountStatus" class="trHide">
                                        <td id="tdVerifyVolMountStatus">Status
                                        </td>
                                        <td>
                                            <select id="ddlVerifyVolMountStatus" style="width: 95%;">
                                                <option value="000">Select Status</option>
                                                <option value="1">Lun Serial No.</option>
                                                <option value="2">NAAID</option>
                                                <option value="3">UUID</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="trcondition" class="trHide">
                                        <td id="tdCondition">Condition
                                        </td>
                                        <td>
                                            <select id="ddlCondition" style="width: 95%;">
                                                <option value="000">Select Condition</option>
                                                <option value="1">Before</option>
                                                <option value="2">After</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="TrVMInstanceStatus" class="trHide">
                                        <td>Check Status
                                        </td>
                                        <td>
                                            <select id="tdVMInstanceStatus" style="width: 95%;">
                                                <option value="000">Select VM Instance Status</option>
                                                <option value="Unknown">Unknown</option>
                                                <option value="CreatingVM">CreatingVM</option>
                                                <option value="StartingVM">StartingVM</option>
                                                <option value="CreatingRole">CreatingRole</option>
                                                <option value="StartingRole">StartingRole</option>
                                                <option value="ReadyRole">ReadyRole</option>
                                                <option value="BusyRole">BusyRole</option>
                                                <option value="StoppingRole">StoppingRole</option>
                                                <option value="StoppingVM">StoppingVM</option>
                                                <option value="DeletingVM">DeletingVM</option>
                                                <option value="StoppedVM">StoppedVM</option>
                                                <option value="RestartingRole">RestartingRole</option>
                                                <option value="CyclingRole">CyclingRole</option>
                                                <option value="FailedStartingRole">FailedStartingRole</option>
                                                <option value="FailedStartingVM">FailedStartingVM</option>
                                                <option value="UnresponsiveRole">UnresponsiveRole</option>
                                                <option value="StoppedDeallocated">StoppedDeallocated</option>
                                                <option value="Preparing">Preparing</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="TrVMpowerstates" class="trHide">
                                        <td>Check Status
                                        </td>
                                        <td>
                                            <select id="tdVMpowerstates" style="width: 95%;">
                                                <option value="000">Select VM Power States</option>
                                                <option value="Starting">Starting</option>
                                                <option value="Started">Started</option>
                                                <option value="Stopping">Stopping</option>
                                                <option value="Stopped">Stopped</option>
                                                <option value="Unknown">Unknown</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="TrVMSizeforStatus" class="trHide">
                                        <td>Check Status
                                        </td>
                                        <td>
                                            <select id="tdVMSizeforStatus" style="width: 95%;">
                                                <option value="000">Select VM Size</option>
                                                <option value="ExtraSmall">ExtraSmall</option>
                                                <option value="Small">Small</option>
                                                <option value="Medium">Medium</option>
                                                <option value="Large">Large</option>
                                                <option value="ExtraLarge">ExtraLarge</option>
                                                <option value="A5">A5</option>
                                                <option value="A6">A6</option>
                                                <option value="A7">A7</option>
                                                <option value="A8">A8</option>
                                                <option value="A9">A9</option>
                                                <option value="A10">A10</option>
                                                <option value="A11">A11</option>
                                                <option value="Basic_A0">Basic_A0</option>
                                                <option value="Basic_A1">Basic_A1</option>
                                                <option value="Basic_A2">Basic_A2</option>
                                                <option value="Basic_A3">Basic_A3</option>
                                                <option value="Basic_A4">Basic_A4</option>
                                                <option value="Standard_D1">Standard_D1</option>
                                                <option value="Standard_D2">Standard_D2</option>
                                                <option value="Standard_D3">Standard_D3</option>
                                                <option value="Standard_D4">Standard_D4</option>
                                                <option value="Standard_D11">Standard_D11</option>
                                                <option value="Standard_D12">Standard_D12</option>
                                                <option value="Standard_D13">Standard_D13</option>
                                                <option value="Standard_D14">Standard_D14</option>
                                                <option value="Standard_G1">Standard_G1</option>
                                                <option value="Standard_G2">Standard_G2</option>
                                                <option value="Standard_G3">Standard_G3</option>
                                                <option value="Standard_G4">Standard_G4</option>
                                                <option value="Standard_G5">Standard_G5</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="DRNetworkPath">
                                        <td>DR Network Path</td>
                                        <td>
                                            <input type="text" id="txtDRNtwkPath" class="form-control" />
                                        </td>
                                    </tr>

                                    <tr actiontype="trReplicationstate" class="trHide">
                                        <td>Replication State
                                        </td>
                                        <td>
                                            <select id="ddltrReplicationstate">
                                                <option value="000">Select Replication state </option>
                                                <option value="Disabled">Disabled</option>
                                                <option value="ReadyForInitialReplication">ReadyForInitialReplication</option>
                                                <option value="InitialReplicationInProgress">InitialReplicationInProgress</option>
                                                <option value="WaitingForInitialReplication">WaitingForInitialReplication</option>
                                                <option value="Replicating">Replicating</option>
                                                <option value="PreparedForFailover">PreparedForFailover</option>
                                                <option value="FailedOverWaitingCompletion">FailedOverWaitingCompletion</option>
                                                <option value="FailedOver">FailedOver</option>
                                                <option value="Suspended">Suspended</option>
                                                <option value="Error">Error</option>
                                                <option value="WaitingForStartResynchronize">WaitingForStartResynchronize</option>
                                                <option value="Resynchronizing">Resynchronizing</option>
                                                <option value="Custom">Custom</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="trHyperVReplicationMode" class="trHide">
                                        <td>Replication Mode
                                        </td>
                                        <td>
                                            <select id="ddltrHyperVReplicationMode">
                                                <option value="000">Select Replication Mode</option>
                                                <option value="None">None</option>
                                                <option value="Primary">Primary</option>
                                                <option value="Replica">Replica</option>
                                                <option value="TestReplica">TestReplica</option>
                                                <option value="Custom">Custom</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="trHyperVVMState" class="trHide">
                                        <td>VM State
                                        </td>
                                        <td>
                                            <select id="ddltrHyperVVMState">
                                                <option value="000">Select VM State</option>
                                                <option value="Other">Other</option>
                                                <option value="Running">Running</option>
                                                <option value="Off">Off</option>
                                                <option value="Stopping">Stopping</option>
                                                <option value="Saved">Saved</option>
                                                <option value="Starting">Starting</option>
                                                <option value="Reset">Reset</option>
                                                <option value="Saving">Saving</option>
                                                <option value="Pausing">Pausing</option>
                                                <option value="Resuming">Resuming</option>
                                                <option value="RunningCritical">RunningCritical</option>
                                                <option value="OffCritical">OffCritical</option>
                                                <option value="StoppingCritical">StoppingCritical</option>
                                                <option value="SavedCritical">SavedCritical</option>
                                                <option value="PausedCritical">PausedCritical</option>
                                                <option value="StartingCritical">StartingCritical</option>
                                                <option value="ResetCritical">ResetCritical</option>
                                                <option value="SavingCritical">SavingCritical</option>
                                                <option value="PausingCritical">PausingCritical</option>
                                                <option value="ResumingCritical">ResumingCritical</option>
                                                <option value="Custom">Custom</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="trhypervFailoverstatus" class="trHide">
                                        <td>Failover Status
                                        </td>
                                        <td>
                                            <select id="ddltrhypervFailoverstatus">
                                                <option value="000">Select Failover Status </option>
                                                <option value="Disabled">Disabled</option>
                                                <option value="ReadyForInitialReplication">ReadyForInitialReplication</option>
                                                <option value="InitialReplicationInProgress">InitialReplicationInProgress</option>
                                                <option value="WaitingForInitialReplication">WaitingForInitialReplication</option>
                                                <option value="Replicating">Replicating</option>
                                                <option value="PreparedForFailover">PreparedForFailover</option>
                                                <option value="FailedOverWaitingCompletion">FailedOverWaitingCompletion</option>
                                                <option value="FailedOver">FailedOver</option>
                                                <option value="Suspended">Suspended</option>
                                                <option value="Error">Error</option>
                                                <option value="WaitingForStartResynchronize">WaitingForStartResynchronize</option>
                                                <option value="Resynchronizing">Resynchronizing</option>
                                                <option value="Custom">Custom</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="trFailedOverWaitingCompletion" class="trHide">
                                        <td>Status
                                        </td>
                                        <td>
                                            <select id="ddltrFailedOverWaitingCompletion">
                                                <option value="000">Select Status </option>
                                                <option value="Disabled">Disabled</option>
                                                <option value="ReadyForInitialReplication">ReadyForInitialReplication</option>
                                                <option value="InitialReplicationInProgress">InitialReplicationInProgress</option>
                                                <option value="WaitingForInitialReplication">WaitingForInitialReplication</option>
                                                <option value="Replicating">Replicating</option>
                                                <option value="PreparedForFailover">PreparedForFailover</option>
                                                <option value="FailedOverWaitingCompletion">FailedOverWaitingCompletion</option>
                                                <option value="FailedOver">FailedOver</option>
                                                <option value="Suspended">Suspended</option>
                                                <option value="Error">Error</option>
                                                <option value="WaitingForStartResynchronize">WaitingForStartResynchronize</option>
                                                <option value="Resynchronizing">Resynchronizing</option>
                                                <option value="Custom">Custom</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="trVMNetworkAdapterStatus" class="trHide">
                                        <td>Switch Type
                                        </td>
                                        <td>
                                            <select id="ddltrVMNetworkAdapterStatus">
                                                <option value="000">Select VMNetwork Adapter Status</option>
                                                <option value="Internal">Internal</option>
                                                <option value="External">External</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="trdatabseMode" class="trHide">
                                        <td>Database Mode
                                        </td>
                                        <td>
                                            <select id="ddltrdatabseMode">
                                                <option value="000">Select Databse Mode </option>
                                                <option value="Read_Write">Read Write </option>
                                                <option value="Read_Only">Read Only</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr actiontype="trdatabseState" class="trHide">
                                        <td>Database State
                                        </td>
                                        <td>
                                            <select id="ddltrdatabseState">
                                                <option value="000">Select Databse State </option>
                                                <option value="Online">Online</option>
                                                <option value="Offline">Offline</option>
                                                <option value="Restoring">Restoring</option>
                                                <option value="Recovering">Recovering</option>
                                                <option value="Recovery Pending">Recovery Pending</option>
                                                <option value="Suspect">Suspect</option>
                                                <option value="Emergency">Emergency</option>
                                                <option value="Normal">Normal</option>
                                                <option value="Shutdown">Shutdown</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>
                                    <%--    <tr>
                                        <td colspan="2">
                                            <div id="divError">
                                            </div>
                                        </td>
                                    </tr>--%>
                                    <tr actiontype="trdbroleandmirrorstate" class="trHide">
                                        <td>Role and Mirror State
                                        </td>
                                        <td>
                                            <select id="ddltrdbroleandmirrorstate">
                                                <option value="000">Select Role Of DB and Mirroring State </option>
                                                <option value="Role Of DB">Role Of DB</option>
                                                <option value="Mirroring State">Mirroring State</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="trdatabaseRole" class="trHide" id="trdatabaseRoleid">
                                        <td>Role Of DB
                                        </td>
                                        <td>
                                            <select id="ddltrdatabaseRole">
                                                <option value="000">Select Role_Of DB </option>
                                                <option value="PRINCIPLE">PRINCIPLE</option>
                                                <option value="MIRROR">MIRROR</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="trMirrorState" class="trHide" id="trMirrorStateid">
                                        <td>Mirroring State
                                        </td>
                                        <td>
                                            <select id="ddltrMirrorState">
                                                <option value="000">Select Mirror State </option>
                                                <option value="SUSPENDED">SUSPENDED</option>
                                                <option value="DISCONNECTED">DISCONNECTED</option>
                                                <option value="SYNCHRONIZING">SYNCHRONIZING</option>
                                                <option value="PENDING_FAILOVER">PENDING FAILOVER</option>
                                                <option value="SYNCHRONIZED">SYNCHRONIZED</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="TargetServerComponent1" class="trHide">
                                        <td id="TdtargetServerComponent1">Target Server1</td>
                                        <td class="dropup">
                                            <asp:DropDownList ID="ddltargetServercomp1" CssClass="chosen-select" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="TargetDatabaseComponent1">
                                        <td id="TdtargetdbComponent1">Target Database1
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddltargetDBComp1" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>


                                    <tr actiontype="TargetServerComponent2" class="trHide">
                                        <td id="TdtargetServerComponent2">Target Server2</td>
                                        <td>
                                            <asp:DropDownList ID="ddltargetServercomp2" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="SoftLayer_Memory" class="trHide">
                                        <td id="idSoftLayer_Memory">Memory
                                        </td>
                                        <td>
                                            <div id="ddlSoftLayerMemory" class="new-select-editable">
                                                <select onchange="this.nextElementSibling.value=this.value">
                                                    <option value="No_Change">No_Change</option>
                                                    <option value="MEMORY_1_GB">MEMORY_1_GB</option>
                                                    <option value="MEMORY_2_GB">MEMORY_2_GB</option>
                                                    <option value="MEMORY_4_GB">MEMORY_4_GB</option>
                                                    <option value="MEMORY_6_GB">MEMORY_6_GB</option>
                                                    <option value="MEMORY_8_GB">MEMORY_8_GB</option>
                                                    <option value="MEMORY_12_GB">MEMORY_12_GB</option>
                                                    <option value="MEMORY_16_GB">MEMORY_16_GB</option>
                                                    <option value="MEMORY_32_GB">MEMORY_32_GB</option>
                                                    <option value="MEMORY_48_GB">MEMORY_48_GB</option>
                                                    <option value="MEMORY_64_GB">MEMORY_64_GB</option>
                                                </select>
                                                <input type="text" name="format" value="" placeholder="-Select Memory-" />
                                            </div>
                                            <%--<span id="errorSoftLayerMemory" class="error"></span>--%>
                                        </td>
                                    </tr>

                                    <tr actiontype="SoftLayer_CPU" class="trHide">
                                        <td id="idSoftLayer_CPU">CPU
                                        </td>
                                        <td>
                                            <div id="ddlSoftLayerCPU" class="new-select-editable">
                                                <select onchange="this.nextElementSibling.value=this.value">
                                                    <option value="No_Change">No_Change</option>
                                                    <option value="CPU_1_x_2GHz_Core">CPU_1_x_2GHz_Core</option>
                                                    <option value="CPU_2_x_2GHz_Cores">CPU_2_x_2GHz_Cores</option>
                                                    <option value="CPU_4_x_2GHz_Cores">CPU_4_x_2GHz_Cores</option>
                                                    <option value="CPU_8_x_2GHz_Cores">CPU_8_x_2GHz_Cores</option>
                                                    <option value="CPU_12_x_2GHz_Cores">CPU_12_x_2GHz_Cores</option>
                                                    <option value="CPU_16_x_2GHz_Cores">CPU_16_x_2GHz_Cores</option>
                                                </select>
                                                <input type="text" name="format" value="" placeholder="-Select CPU-" />
                                            </div>
                                            <%--<span id="errorSoftLayerCPU" class="error"></span>--%>
                                        </td>
                                    </tr>

                                    <tr actiontype="SoftLayerUpgrade" class="trHide">
                                        <td class="padding-5">Wait For Upgrade
                                        </td>
                                        <td class="padding-5">
                                            <input id="chkbxSoftlayerWait" type="checkbox" title="WaitForUpgrade" />
                                            <span></span>
                                        </td>

                                    </tr>

                                    <tr actiontype="TargetDatabaseComponent2">
                                        <td id="TdtargetdbComponent2">Target Database2
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddltargetDBComp2" runat="server">
                                            </asp:DropDownList>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <%--  added new 09-11-2017--%>
                                    <tr actiontype="ControllerType" class="trHide">
                                        <td>Controller Type
                                        </td>
                                        <td>
                                            <select id="dllcontrollertype" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="SCSI">SCSI </option>
                                                <option value="IDE">IDE </option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="ControllerLocation" class="trHide">
                                        <td>Controller Location
                                        </td>
                                        <td>
                                            <select id="dllcontrollerlocation" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="Define Value">Define Value </option>
                                                <option value="Random">Random </option>
                                            </select>
                                            <span></span>
                                        </td>

                                        <%--                                         <td>
                                            <input type="text" id="txtdefinevalue" class="form-control" style="display: none;" />
                                            <span></span>
                                        </td>--%>
                                    </tr>





                                    <tr actiontype="DefineValue" class="trHide" id="DefineVal">
                                        <td>Location Number
                                        </td>
                                        <td id="tdDefineValue">
                                            <input type="text" id="txtdefinevalue" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>


                                    <tr actiontype="VMIsClustered">
                                        <td id="TDVMIsClustered">VM ISClustered
                                        </td>
                                        <td class="removechkdes">
                                            <input id="chkVMCluster" type="checkbox" />
                                            <span></span>
                                        </td>
                                    </tr>


                                    <tr actiontype="SharingOption" class="trHide">
                                        <td id="tdidsharing">Check Sharing Status
                                        </td>
                                        <td>
                                            <select id="dllSharingOption" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="Enable">Enable </option>
                                                <option value="Disable">Disable</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>




                                    




                                    <%-- <tr actiontype="DisableOption" class="trHide">
                                        <td>Disable Option
                                        </td>
                                        <td>
                                            <select id="dlldisableOption" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="Enable">Enable </option>
                                                <option value="Disable">Disable</option>
                                            </select>
                                            <span></span>
                                        </td>
                                    </tr>--%>




                                    <tr actiontype="ClusterSharedVolumeStatus" class="trHide">
                                        <td>Cluster Shared Volume Status
                                        </td>
                                        <td>
                                            <select id="dllClusterSharedVolumeStatus" style="width: 30%;">
                                                <option value="000">Select Option </option>
                                                <option value="Online">Online</option>
                                                <option value="Offline">Offline</option>
                                            </select>
                                            <span></span>
                                        </td>



                                    </tr>

                                    <tr actiontype="eBDRInfraobject" class="trHide">
                                        <td>Infraobject
                                        </td>
                                        <td>
                                            <asp:DropDownList ID="ddlInfraobject" runat="server" CssClass="chosen-select" Style="width: 95%;">
                                            </asp:DropDownList>

                                            <span></span>
                                        </td>
                                    </tr>



                                    <tr actiontype="eBDROperation" class="trHide">
                                        <td>Action Staus
                                        </td>
                                        <td>

                                            <asp:DropDownList ID="ddleBDRStatus" runat="server" CssClass="chosen-select" Style="width: 95%;">
                                                <asp:ListItem Text="- Please Operation -" Value="000" Selected="true" />
                                                <asp:ListItem Text="Start" Value="1" />
                                                <asp:ListItem Text="Stop" Value="2" />
                                                <asp:ListItem Text="Puase" Value="3" />
                                                <asp:ListItem Text="Resume" Value="4" />
                                                <asp:ListItem Text="Reverse" Value="5" />

                                            </asp:DropDownList>
                                            <span></span>
                                        </td>

                                    </tr>
                                    <tr actiontype="inprompt" class="trHide">
                                        <td>ShellPrompt
                                        </td>
                                        <td>
                                            <input type="text" id="txtiprompt" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>

                                    <tr actiontype="intimeout" class="trHide">
                                        <td>TimeOut
                                        </td>
                                        <td>
                                            <input type="text" id="txtitimeout" class="form-control" />
                                            <span></span>
                                        </td>
                                    </tr>
                                    <tr>

                                        <td>

                                            <a id="idAlertMechanism" style="cursor: pointer">Alert Mechanism</a>
                                        </td>
                                        <td>
                                            <div class="col-md-12 padding-none">
                                                <div class="text-left padding-none small pull-left col-md-4">
                                                    <img src="../Images/email-icon.png" alt="Email" style="vertical-align: sub" />
                                                    <input id="ChkEmail" type="checkbox" title="Email" />
                                                    <img src="../Images/sms.png" alt="SMS" style="vertical-align: sub" />
                                                    <input id="ChkSms" type="checkbox" title="SMS" />
                                                </div>
                                                <div class="text-right pull-right col-md-8" style="padding-right: 22px; padding-left: 0px;">
                                                    <img id="btnActionCopy" class="btn btn-primary" style="display: none;" title="Copy Action"
                                                        src="../Images/icons/icon-test-copy.png" />
                                                    <%--<input id="btnActionCopy" class="btn btn-primary" style="display: none;" type="button"
                                                        value="Copy Action" />--%>
                                                    <img id="btnTestCPLScript" class="btn btn-primary" title="Check Script" style="display: none;"
                                                        src="../Images/icons/icon-test-script.png" />
                                                    <%--<input  class="btn btn-primary"  type="button"
                                                        value="Test Script" />--%>
                                                    <img id="btnActionSave" class="btn btn-primary" style="display: none;" title="Save"
                                                        src="../Images/icons/icon-test-save.png" />
                                                    <%--<input id="btnActionSave" class="btn btn-primary" style="display: none;" type="button"
                                                        value="Save" />--%>
                                                    <img id="btnCancelSave" class="btn btn-primary" style="display: none;" title="Cancel"
                                                        src="../Images/icons/icon-test-cancel.png" />
                                                    <%--<input id="btnCancelSave" class="btn btn-primary" style="display: none;" type="button"
                                                        value="Cancel" />--%>
                                                    <img id="btnEditAction" class="btn btn-primary" style="display: none;" title="Edit"
                                                        src="../Images/icons/icon-test-edit.png" />
                                                    <%--<input id="btnEditAction" class="btn btn-primary" style="display: none;" type="button"
                                                        value="Edit" />--%>
                                                    <img id="btnUpdateAction" class="btn btn-primary" style="display: none;" title="Update"
                                                        src="../Images/icons/icon-test-save.png" />
                                                    <%--<input id="btnUpdateAction" class="btn btn-primary" style="display: none;" type="button"
                                                        value="Update" />--%>
                                                    <img id="btnCancelEditAction" class="btn btn-primary" style="display: none;" title="Cancel"
                                                        src="../Images/icons/icon-test-cancel.png" />
                                                    <%--<input id="btnCancelEditAction" class="btn btn-primary" style="display: none;" type="button"
                                                        value="Cancel" />--%>
                                                    <img id="btnDeleteAction" class="btn btn-primary" style="display: none;" title="Delete"
                                                        src="../Images/icons/icon-test-delete.png" />
                                                    <%-- <input id="btnDeleteAction" class="btn btn-primary" style="display: none;" type="button"
                                                        value="Delete" />--%>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>


                                </tbody>
                            </table>
                        </div>


                        <div id="conditionDiv" style="display: none;">
                            <table class="table margin-bottom border" width="100%">
                                <thead>
                                    <tr>
                                        <th class="text-center " style="padding: 4px;" colspan="2">Connect Action
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="col-md-3">Success Action </td>
                                        <td>
                                            <input id="txtSuccessAction" type="text" disabled="disabled" class="form-control " style="width: 83%" />
                                        </td>
                                    </tr>

                                    <tr>
                                        <td class="col-md-3">Select Failure Action </td>
                                        <td>
                                            <select id="ddlExist1" class="col-md-10">
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="col-md-3">Failure Trial Count </td>
                                        <td>

                                            <input id="txtbxFailureCount" type="text" class="form-control " style="width: 83%" />
                                            <span id="txtbxFailureCountSpan"></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" class="text-right">
                                            <input id="btnConnectAction" class="btn btn-primary" type="button"
                                                value="Connect " />

                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                        </div>
                    </div>



                </div>
            </div>
            <div class="col-md-6">
                <div class="widget">
                    <div class="widget-body">
                        <div class="col-md-5 padding-none">
                            <ul class="btn-primary workflowmenu" style="width: 142px !important;">
                                <li class="with-menu"><i></i>Workflow Menu
                                <div class="menu pull-right" id="WorkflowMenu">
                                    <%--  <img src="../images/menu-open-arrow.png" width="16" height="16" />--%>
                                    <ul id="WorkflowUL">
                                        <li class="icon_new_doc" id="idnw" runat="server"><a id="btnNewWorkFlow">New</a></li>
                                        <li class="icon_load"><a id="btnLoadWorkFlow">Load</a></li>
                                        <li class="icon_save"><a id="btnSave">Save</a></li>
                                        <li class="icon_save"><a id="btnSaveAs">SaveAs</a></li>
                                        <li class="icon_save"  style="display: none"  id="save_asdraft"><a id="btnSaveAsdraft">SaveAs Draft</a></li>
                                        <%--  <li class="icon_save"><a id="btnCopyAs">CopyAs</a></li>--%>  <%--its wrking jst uncomment menu is--%>
                                        <li class="icon_attach"><a id="groupAttech">Attach to InfraObject</a></li>
                                        <li class="icon_de-attach"><a id="btnDeAttachGroup">De-Attach InfraObject</a></li>
                                        <%--<li class="icon_manageGrwflow"><a id="btnManageGroupWorkflow" >Manage Group Workflow</a></li>--%>
                                        <%--<li class="icon_alarm"><a id="btnscheduler">Schedule Workflow</a></li>--%>
                                        <%-- <li class="icon_xportxml"><a id="runBook">Run Book </a></li>--%>
                                        <li class="icon_xportxml"><a id="runBook">Workflow Configuration Report</a></li>
                                        <%-- <li class="icon_exportxml"><a id="btnExportXml">Export-XML</a></li> --%>  <%--its wrking jst uncomment menu is--%>
                                    </ul>
                                </div>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-7 padding-none text-right">
                            <div class="col-md-7">
                                <%--<asp:Button ID="btnverify" Text="Verify" runat="server" style="display:none" Class="btn btn-primary"/>--%>
                                <a id="btnverify" style="display: none" class="btn btn-primary">Verify</a>
                                <%--<asp:Button ID="btnreject" Text="Reject" runat="server"  style="display:none" Class="btn btn-danger"/>--%>

                                <a id="btnreject" style="display: none" class="btn btn-danger">Reject</a>
                                <asp:Label ID="lblSaveasdraft" runat="server" Text="This Workflow Is In Draft" style="display:none;color:red;font-size: 13px; margin-right: 5px;margin-top:10px;"></asp:Label> 
                                <span id="ProcessId" style="display: none;"></span>
                            </div>
                            <div  class="col-md-5" id="div_workflowVersion" runat="server">
                                <div id="btnhistory">
                                    <asp:Label ID="lblworkflowVersion" runat="server" Text="" Style="font-size: 13px; margin-right: 5px;"></asp:Label>
                                    <a class="btn btn-primary" style="padding: 2px 5px; text-align: center; border-radius: 3px;">
                                        <img id="btnWorkflowHistory" src="../Images/icons/workflow-restore-icon_white.png" title="History" style="cursor: pointer; vertical-align: text-bottom; padding: 3px 2px; width: 18px;" />
                                    </a>
                                </div>
                            </div>
                        </div>
                        <select id="ddlSelectedWFProperty" style="width: 99%; margin: 0 0.5%;">
                            <option value="00">Select Property</option>
                        </select>
                        <div id="rightColumn" class="widget" style="height: 450px;">
                            <div id="borderhead" class="workflowhead widget-head" style="background-color: #4A8BC2 ! important; background-image: none !important; color: #FFFFFF !important;">
                                <div class="col-lg-5 padding-none-LR">
                                    <img id="imglockWF" src="../Images/icons/Lock.png" title="Workflow in Lock State" class="pull-left" style="margin-top: 4px; display: none;" />
                                    <b id="idWorfkflowName" style="float: left !important; text-align: left; width: 86%; padding-left: 8px;" class="tdword-wrap" title="">Workflow Editor</b>
                                    <span id="workflowHiddenId" style="display: none;"></span>
                                </div>
                                <div id="rightDiv" class="col-lg-7" style="top: -1px !important;">

                                    <%--  <asp:ImageButton ID="verifyworkflow" runat="server" Style="display: none;" ImageUrl="../Images/ApprovalProcessIcon/verified-18.png" />
                                    <asp:ImageButton ID="rejectworkflow" runat="server" Style="display: none;" ImageUrl="../Images/ApprovalProcessIcon/rejected.png" />--%>

                                    <input id="btnCondition" class="dragbox btn btn-primary AddCondition pull-right " type="button" style="display: none; width: 10.5%; height: 25px; padding-bottom: 3px; padding-top: 3px;"
                                        title="Add Condition" />

                                    <input id="btnConditionDisable" class="dragbox btn btn-primary AddCondition pull-right " type="button" disabled="disabled" style="background-color: #70A3CF !important; width: 10.5%; height: 25px; padding-bottom: 3px; padding-top: 3px;"
                                        title="Add Condition" />

                                    <input id="btnActionCreate" class="dragbox btn btn-primary ActionCreate pull-right " type="button" style="width: 10.5%; height: 25px; padding-bottom: 3px; padding-top: 3px;"
                                        title="Insert New" />

                                    <input id="btnActionDelete" class="dragbox btn btn-primary Delete pull-right" type="button" disabled="disabled" style="width: 10.5%; height: 25px; padding-bottom: 3px; padding-top: 3px;"
                                        title="Delete" />

                                    <input id="btnMarkparallel" class="dragbox btn btn-primary MarkParallel pull-right" type="button" disabled="disabled" style="width: 10.5%; height: 25px; padding-bottom: 3px; padding-top: 3px;"
                                        title="Mark Parallel" />

                                    <input id="btnPrallelAction" class="dragbox btn btn-primary pull-right hide" type="button" style="width: 19%;"
                                        value="Mark Parallel" />

                                    <label class="pull-right" style="position: relative">Mark</label>
                                    <input id="chkMarkParallel" type="checkbox" class="pull-right" style="position: relative" value="Mark Parallel" />

                                </div>
                            </div>
                            <div id="dropBox" class="dropBox" align="center" style="height: 400px; overflow: auto; position: absolute; width: 100%;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="pnluserauthon" style="display: none">
        <div class="modal2 bg" style="display: block; z-index: 9999 !important;">
            <div class="modal-dialog" style="width: 500px;">
                <div class="modal-content  widget-body-white">
                    <div class="modal-header">
                        <h3 class="modal-title">User Authentication</h3>
                    </div>
                    <div class="modal-body">
                        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                            <ContentTemplate>
                                <div class="col-md-12 form-horizontal uniformjs">
                                    <div class="col-md-12 form-group">
                                        <div class="col-md-4">
                                            <label>UserName</label>
                                        </div>
                                        <div class="col-md-8">
                                            <input type="text" id="UserName" class="form-control" style="width: 100%" />
                                        </div>
                                    </div>

                                    <div class="col-md-12 form-group">
                                        <div class="col-md-4">
                                            <label>Password</label>
                                        </div>
                                        <div class="col-md-8">
                                            <input type="Password" id="Password" class="form-control" style="width: 100%" />
                                        </div>
                                    </div>

                                    <div class="col-ms-12 form-group">
                                        <div class="col-md-4"></div>
                                        <div class="col-md-8">
                                            <asp:CheckBox ID="chkActiveDirectory" runat="server" AutoPostBack="true" OnCheckedChanged="chkActiveDirectory_CheckedChanged" />

                                            <asp:Label ID="Label2" Text="AD" runat="server" CssClass="padding padding-none-TB"></asp:Label>
                                        </div>
                                    </div>
                                    <div class="clearfix"></div>

                                    <asp:Panel ID="pnlDomain" CssClass="col-md-12 form-group" runat="server" Visible="false">
                                        <div class="col-md-4">
                                            <label>
                                                <asp:Label ID="lbldomainName" runat="server" Text="Domain" Visible="false"></asp:Label></label>
                                        </div>
                                        <div class="col-md-8 st_div">
                                            <div class="input-icon left">
                                                <span class="glyphicons globe log-ext3"><i></i></span>
                                                <div>
                                                    <input id="combobox1" type="text" runat="server" style="display: none" placeholder="Enter Domain" />
                                                </div>
                                            </div>
                                        </div>
                                    </asp:Panel>

                                    <div><span id="labelSDErrormessage" class="error"></span></div>
                                </div>
                            </ContentTemplate>
                        </asp:UpdatePanel>
                    </div>
                    <div class="modal-footer">
                        <div class="col-xs-5 text-left" style="padding-top: 10px;">
                            <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required Fields</span>
                        </div>
                        <div class="col-xs-7">


                            <button id="loguserbtn" type="button" style="display: none" class="btn btn-primary">OK</button>
                            <button id="loguserbtndetach" type="button" style="display: none" class="btn btn-primary">OK</button>
                            <button id="logusercancel" type="button" style="width: 25%; display: inline-block;" class="btn btn-block btn-inverse">Close</button>


                            <%--<asp:Button ID="logusercancel" CssClass="btn btn-block btn-inverse" runat="server" Text="Cancel" CausesValidation="False" TabIndex="19" />--%>
                        </div>

                        <%--<asp:Label ID="labelSDErrormessage" runat="server" CssClass="error" Visible="false"></asp:Label>--%>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <%-- <script src="../Script/pqselect.min.js"></script>--%>
    <script type="text/javascript">
        var xyz = navigator.userAgent;
        if (xyz.indexOf("Chrome") > -1) {
            $("body").addClass("ChromeBrowser");
        }
        $(function () {
            if ($("#ctl00_cphBody_HiddenFieldeditt").val() == "allowedit") {

                $("#dropBox").sortable();
                $("#dropBox").disableSelection();
            }
        });
        $(".innerLR").scroll(function () {
            $("#ulCategory li ul").getNiceScroll().resize();
            //  alert('alert');
        });

        $(document).ready(function () {
            if ($("#ctl00_cphBody_hdnUserRole").val() == "Custom") {
                // $("#btnLoadWorkFlow").attr("disabled", true);
                // debugger


                if ($("#ctl00_cphBody_HiddenFieldadd").val() != "nwlAdd") {
                    $('.icon_new_doc').addClass('disabled');
                    $('.icon_save').addClass('disabled');
                    $('.icon_attach').addClass('disabled');
                    $('.icon_de-attach').addClass('disabled');
                    $('.icon_xportxml').addClass('disabled');
                    $('#btnActionCreate').addClass('disabled');
                    $("#ctl00_cphBody_ddlLoadProperty").prop('disabled', true).trigger("chosen:updated");

                }



            }

            if ($("#ctl00_cphBody_hdnUserRole").val() == "ExecutionAccessUser") {
                // $("#btnLoadWorkFlow").attr("disabled", true);
                // debugger


                if ($("#ctl00_cphBody_HiddenFieldadd").val() != "nwlAdd") {
                    $('.icon_new_doc').addClass('disabled');
                    $('.icon_save').addClass('disabled');
                    $('.icon_attach').addClass('disabled');
                    $('.icon_de-attach').addClass('disabled');
                    $('.icon_xportxml').addClass('disabled');
                    //$('#btnActionCreate').addClass('disabled');
                    $("#ctl00_cphBody_ddlLoadProperty").prop('disabled', true).trigger("chosen:updated");

                    $('#rightDiv').addClass('disabled');
                    $("#btnDeleteAction").attr("disabled", true);
                    $("#btnEditAction").attr("disabled", true);
                    $("#box1").attr("disabled", true);
                    $("#btnActionSave").attr("disabled", true);


                }



            }

            //binddropdown();
            var text = "-Select Database-";
            var value = "000";
            var chkValue = "";
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });

            $("select[id$=ddlMailDB] > option").remove();
            $('[id$=ddlMailDB]').append("<option value='" + value + "'>" + text + "</option>");

            $("select[id$=ddlMailTargetDB] > option").remove();
            $('[id$=ddlMailTargetDB]').append("<option value='" + value + "'>" + text + "</option>");

            $('.myPopover').popover();

            $(".notifycheckscroll").mCustomScrollbar({
                axis: "y",

            });

            $(document).mouseup(function (e) {
                var container = $(".popover");

                // if the target of the click isn't the container nor a descendant of the container
                if (!container.is(e.target) && container.has(e.target).length === 0) {
                    container.hide();
                }

            });

        })

        function pageLoad() {

            $("[id$=ddlLoadProperty]").chosen();
            $("[id$=ddlWorkflowList]").chosen();
            $("#chkMarkParallel").attr("disabled", true);

            //$(".chosen-select").chosen();
            // $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
            //$(".new-select").select2({
            //    //formatResult: format,
            //    //formatSelection: format,
            //});
            $(".notifycheckscroll").mCustomScrollbar({
                axis: "y",

            });

            var field = document.getElementById('txtName');
            field.onkeypress = function (e) {
                //check if the charCode of the symbol inputed is 36 (the character code of $)
                if (e.charCode === 36) {
                    return false; //prevent it from being inputted into the field
                }
            };


            // Getvmname();

        }

        $(document).click(function (e) {
            $('#WorkflowMenu').each(function () {

                if (!$(this).is(e.target)) {
                    $("#WorkflowUL").hide();
                }
            });
        });

        //$("#WorkflowMenu").click(function () {
        $(document).on("click", "#WorkflowMenu", function () {
            //$("#WorkflowUL").show();
            $("#WorkflowUL").toggle();
        });

        //   $(document).on("click", "#TypeAction", function () {
        //    //$("#WorkflowUL").show();
        //       var optionSelected = $("option:selected", this);
        //         alert("hi");
        //   // $("#WorkflowUL").toggle();
        //});

        function FilterWFfunction() {
            if (document.getElementsByClassName('modal-title')[1].innerHTML == "Attach InfraObject") {
                var selectele = document.getElementById("TypeAction");
                if (selectele.options[selectele.selectedIndex].text == "DR Ready") {
                    //alert("DR Ready selected");
                    var ajaxUrl = "WorkflowConfiguration.aspx/GetExistingWorkflow_DRReady";
                    var ajaxData = "{'args':'GetWorkFlow'}";
                    AjaxFunction(ajaxUrl, ajaxData, GetAllWorkFlows, OnError);

                }
                else {
                    var ajaxUrl = "WorkflowConfiguration.aspx/GetExistingWorkflow_WithoutDRReady";
                    var ajaxData = "{'args':'GetWorkFlow'}";
                    AjaxFunction(ajaxUrl, ajaxData, GetAllWorkFlows, OnError);
                }
            }

        }

        //           $('#TypeAction').on('change', function (e) {
        //    var optionSelected = $("option:selected", this);
        //    var valueSelected = this.value;
        //                alert(optionSelected);
        //                alert(valueSelected);
        //});
        function binddropdown() {
            $.ajax({
                type: "POST",
                url: "WorkflowConfiguration.aspx/DiscoverDomains",
                data: "{}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: true,
                success: function Success(data) {
                    getBindValue(data.d);
                    $("#ctl00_cphBody_dominset").val($("#ctl00_cphBody_combobox1").val());
                }
            });
        }
        function getBindValue(value) {
            var data = value.split(":");
            for (var i = 0; i < data.length; i++) {
                var volume = data[i].split(",");
                var text = volume[0];
                //   var html = '<tr><td><input type="text" id="' + text + '" </td></tr>';

                // jQuery('[id$=combobox1]').append("<option value='" + html + "</option>");
                jQuery(function () {
                    jQuery('[id$=combobox1]').combobox([
                        text
                    ]);
                    $('[id$=combobox1]').val(volume[0]);

                });
                //   $("[id$=ctl00_cphBody_combobox1] option:contains(" + text + ")").attr('selected', true);
                //  $("[id$=combobox1]).combobox(text[0]);
            }
        }

        var charcount_daWF;
        //   $(document).on("click", "#ddlLoadProperty_search", function () {
        $(document).on("change", "#ctl00_cphBody_ddlLoadProperty_chosen > .chosen-drop > .chosen-search > input", function () {
            $('#ctl00_cphBody_UpdateProgress1').show();
            //if (event.keyCode == 30 || event.keyCode == 48 || event.keyCode == 13) {
            //    return false;
            //}
            var chtext = $("#ctl00_cphBody_ddlLoadProperty_chosen > .chosen-drop > .chosen-search > input").val();
            //var chtext = $(this).val();
           // alert(chtext);
            //  charcount_daWF = localStorage.getItem('charcount_daWF');
            //  var srchtext = $(this).val();
            // alert(charcount_daWF);
            var baseid = $("#ctl00_cphBody_ddlBaseActionType").val();
            //  if (chtext.length != charcount_daWF) {
            //   localStorage.setItem('charcount_daWF', chtext.length);
            //if ((chtext.length > 2) && (baseid != "000")) {
            if ((baseid != "000")) {
                chtext += "$" + baseid;
                //    alert(chtext);
                $.ajax({
                    type: "POST",
                    url: "WorkflowConfiguration.aspx/ddlloadproperty_textsearch",
                    data: "{'text':'" + chtext + "'}",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    async: true,
                    success: function Success(data) {
                        //  console.log("Success ddlloadproperty_textsearch : " + data);
                        Populateddlloadproperty_1(data);
                        //var ddlloadproperty_1 = $("[id*=ddlloadproperty]");
                        //ddlloadproperty_1.empty().append('<option selected="selected" value="00">Select Activity</option>');
                        //ddlloadproperty_1.empty().append('<option selected="selected" value="000">Add new Activity</option>');
                        //$.each(r.d, function () {
                        //    ddlloadproperty_1.append($("<option></option>").val(this['Value']).html(this['Text']));
                        //});

                        $("#ctl00_cphBody_ddlLoadProperty").trigger("chosen:updated");
                        $('#ctl00_cphBody_UpdateProgress1').hide();
                        //    setTimeout(function () { $("#ctl00_cphBody_ddlLoadProperty_chosen .chosen-drop .chosen-search input").val(srchtext); }(), 500);
                    }
                });
            }
            // }
            //$('#ctl00_cphBody_UpdateProgress1').hide();
            event.stopPropagation();
            // $('#ctl00_cphBody_UpdateProgress1').show();
        })
        $('#TypeAction').on('change', function (e) {
            var optionSelected = $("option:selected", this);
            var valueSelected = this.value;
            alert(optionSelected);
            alert(valueSelected);
        });
        function Populateddlloadproperty_1(msg) {
            var data = msg.d;
            $("#ctl00_cphBody_ddlLoadProperty option").remove();
            var result = data.split(",");
            if (result == "") {
                var text = "-Select Action-";
                var value = "000";
                var chkValue = "";
                $("#ctl00_cphBody_ddlLoadProperty").append("<option value='" + value + "'>" + text + "</option>");
            }
            else {
                var text = "-Select Action-";
                var value = "000";
                var chkValue = "";
                AppendOption('ddlLoadProperty', value, text);
                for (var i = 0; i < result.length; i++) {
                    chkValue = result[i].split(":");
                    text = chkValue[0];
                    value = chkValue[1];
                    $("#ctl00_cphBody_ddlLoadProperty").append("<option value='" + value + "'>" + text + "</option>");
                }
                var options = $("#ctl00_cphBody_ddlLoadProperty option");                    // Collect options         		
                options.detach().sort(function (a, b) {               // Detach from select, then Sort		
                    var at = $(a).text();
                    var bt = $(b).text();
                    return (at > bt) ? 1 : ((at < bt) ? -1 : 0);            // Tell the sort function how to order		
                });
                options.appendTo("#ctl00_cphBody_ddlLoadProperty");
                $("#ctl00_cphBody_ddlLoadProperty option:first").attr('selected', 'selected');
            }
        }

        var charcount_daWF1;
        $(document).on("keyup change", "#ctl00_cphBody_ddlWorkflowList_chosen .chosen-drop .chosen-search input", function () {
            $('#ctl00_cphBody_UpdateProgress1').show();
            if (event.keyCode == 30 || event.keyCode == 48 || event.keyCode == 13) {
                $('#ctl00_cphBody_UpdateProgress1').hide();
                return false;
            }
            var chtext = $(this).val();
            charcount_daWF1 = localStorage.getItem('charcount_daWF1');
            var srchtext = $(this).val();
            //alert(chtext);
            // var baseid = $("#ctl00_cphBody_ddlBaseActionType").val();
            if (chtext.length != charcount_daWF1) {
                localStorage.setItem('charcount_daWF1', chtext.length);
                if ((chtext.length > 2)) {

                    $.ajax({
                        type: "POST",
                        url: "WorkflowConfiguration.aspx/ddlworkflow_textsearch_V2",
                        data: "{'text':'" + chtext + "'}",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        async: true,
                        success: function Success(data) {

                            Populateddlloadworkflow_Search_Action(data.d);

                            $("#ctl00_cphBody_ddlWorkflowList").trigger("chosen:updated");
                            setTimeout(function () { $("#ctl00_cphBody_ddlWorkflowList_chosen .chosen-drop .chosen-search input").val(srchtext); }(), 500);

                        }
                    });

                    $('#ctl00_cphBody_UpdateProgress1').hide();
                }
                else if (chtext.length == 0) {
                    $.ajax({
                        type: "POST",
                        url: "WorkflowConfiguration.aspx/GetExistingWorkflow_New",
                        data: "{'args':'" + chtext + "'}",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        async: true,
                        success: function Success(data) {
                            if (data != null) {
                                Populateddlloadworkflow_Search_Action(data.d);
                                $("#ctl00_cphBody_ddlWorkflowList").trigger("chosen:updated");
                                setTimeout(function () { $("#ctl00_cphBody_ddlWorkflowList .chosen-drop .chosen-search input").val(srchtext); }(), 500);
                            }
                        }
                    })
                    //}
                }
            }

            event.stopPropagation();
            $('#ctl00_cphBody_UpdateProgress1').hide();
        })



        var charcount_da;
        $(document).on("keyup change", "#workFlows_chosen .chosen-drop .chosen-search input", function () {
            if (event.keyCode == 30 || event.keyCode == 48 || event.keyCode == 13) {
                return false;
            }

            var chtext = $(this).val();
            var IsDRReadySelected = "No";
            var selectele = document.getElementById("TypeAction");
            if (selectele.options[selectele.selectedIndex].text == "DR Ready") {
                IsDRReadySelected = "Yes";
            }
            charcount_da = localStorage.getItem('charcount_da');
            if (chtext.length != charcount_da) {
                localStorage.setItem('charcount_da', chtext.length);
                if ((chtext.length > 2) && (document.getElementsByClassName('modal-title')[1].innerHTML == "Attach InfraObject") && ($("#ctl00_cphBody_hdnUserRole").val() != "Custom")) {

                    $.ajax({
                        type: "POST",
                        url: "WorkflowConfiguration.aspx/ddlworkflow_textsearch",
                        data: "{'text':'" + chtext + "','IsDRReadyRequired':'" + IsDRReadySelected + "'}",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        async: true,
                        success: function Success(data) {
                            if (data != null) {
                                Populateddlloadworkflow_1(data.d);
                                $("#workFlows").trigger("chosen:updated");
                                setTimeout(function () { $("#workFlows_chosen .chosen-drop .chosen-search input").val(chtext); }(), 500);
                            }
                        }
                    });
                }
                else if ((chtext.length == 0) && (document.getElementsByClassName('modal-title')[1].innerHTML == "Attach InfraObject") && ($("#ctl00_cphBody_hdnUserRole").val() != "Custom")) {


                    if (IsDRReadySelected == "Yes") {
                        $.ajax({
                            type: "POST",
                            url: "WorkflowConfiguration.aspx/GetExistingWorkflow_DRReady",
                            data: "{'args':'" + chtext + "'}",
                            contentType: "application/json; charset=utf-8",
                            dataType: "json",
                            async: true,
                            success: function Success(data) {
                                if (data != null) {
                                    Populateddlloadworkflow_1(data.d);
                                    $("#workFlows").trigger("chosen:updated");
                                    setTimeout(function () { $("#workFlows_chosen .chosen-drop .chosen-search input").val(chtext); }(), 500);
                                }
                            }
                        });
                    }
                    else {
                        $.ajax({
                            type: "POST",
                            url: "WorkflowConfiguration.aspx/GetExistingWorkflow_WithoutDRReady",
                            data: "{'args':'" + chtext + "'}",
                            contentType: "application/json; charset=utf-8",
                            dataType: "json",
                            async: true,
                            success: function Success(data) {
                                if (data != null) {
                                    Populateddlloadworkflow_1(data.d);
                                    $("#workFlows").trigger("chosen:updated");
                                    setTimeout(function () { $("#workFlows_chosen .chosen-drop .chosen-search input").val(chtext); }(), 500);
                                }
                            }
                        });
                    }




                    //}
                }
            }
            event.stopPropagation();
        })
        function Populateddlloadworkflow_1(msg) {
            var data = msg;
            $("#workFlows option").remove();
            var result = data.split(",");
            if (result == "") {
                var text = "-Select WorkFlow-";
                var value = "000";
                var chkValue = "";
                $("#workFlows").append("<option value='" + value + "'>" + text + "</option>");
            }
            else {
                var text = "-Select WorkFlow-";
                var value = "000";
                var chkValue = "";
                AppendOption('workFlows', value, text);
                //  if (!($("#workFlows option[value='000']").length > 0))	
                for (var i = 0; i < result.length; i++) {
                    chkValue = result[i].split(":");
                    text = chkValue[0];
                    value = chkValue[1];
                    $("#workFlows").append("<option value='" + value + "'>" + text + "</option>");
                }
                var options = $("#workFlows option");                    // Collect options         	
                options.detach().sort(function (a, b) {               // Detach from select, then Sort	
                    var at = $(a).text();
                    var bt = $(b).text();
                    return (at > bt) ? 1 : ((at < bt) ? -1 : 0);            // Tell the sort function how to order	
                });
                options.appendTo("#workFlows");
                $("#workFlows option:first").attr('selected', 'selected');
            }
        }

        $(document).on("keyup change", "#ddlExist_chosen .chosen-drop .chosen-search input", function (event) {
            //console.log(event.keyCode)
            if (event.keyCode == 30 || event.keyCode == 48 || event.keyCode == 13) {
                return false;
            }
            var chtext = $(this).val();
            //alert(document.getElementsByClassName('modal-title')[1].innerHTML);	
            charcount_da = localStorage.getItem('charcount_da');
            if (chtext.length != charcount_da) {
                localStorage.setItem('charcount_da', chtext.length);
                if ((chtext.length > 2) && ($("#ctl00_cphBody_hdnUserRole").val() != "Custom")) {

                    $.ajax({
                        type: "POST",
                        url: "WorkflowConfiguration.aspx/ddlworkflow_textsearch_V2",
                        data: "{'text':'" + chtext + "'}",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        async: true,
                        success: function Success(data) {
                            if (data != null) {
                                Populateddlloadworkflow_Search(data.d);
                                $("#ddlExist").trigger("chosen:updated");
                                setTimeout(function () { $("#ddlExist_chosen .chosen-drop .chosen-search input").val(chtext); }(), 500);
                            }
                        }
                    });
                }
                else if ((chtext.length == 0) && ($("#ctl00_cphBody_hdnUserRole").val() != "Custom")) {
                    $.ajax({
                        type: "POST",
                        url: "WorkflowConfiguration.aspx/GetExistingWorkflow_New",
                        data: "{'args':'" + chtext + "'}",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        async: true,
                        success: function Success(data) {
                            if (data != null) {
                                Populateddlloadworkflow_Search(data.d);
                                $("#ddlExist").trigger("chosen:updated");
                                setTimeout(function () { $("#ddlExist_chosen .chosen-drop .chosen-search input").val(chtext); }(), 500);
                            }
                        }
                    })
                    //}
                }
            }
            event.stopPropagation();
        })

        function Populateddlloadworkflow_Search(msg) {
            var data = msg;
            $("#ddlExist option").remove();
            var result = data.split(",");
            if (result == "") {
                var text = "-Select WorkFlow-";
                var value = "000";
                var chkValue = "";
                $("#ddlExist").append("<option value='" + value + "'>" + text + "</option>");
            }
            else {
                var text = "-Select WorkFlow-";
                var value = "000";
                var chkValue = "";
                AppendOption('ddlExist', value, text);
                //  if (!($("#workFlows option[value='000']").length > 0))	
                for (var i = 0; i < result.length; i++) {
                    chkValue = result[i].split(":");
                    text = chkValue[0];
                    value = chkValue[1];
                    $("#ddlExist").append("<option value='" + value + "'>" + text + "</option>");
                }
                var options = $("#ddlExist option");                    // Collect options         	
                options.detach().sort(function (a, b) {               // Detach from select, then Sort	
                    var at = $(a).text();
                    var bt = $(b).text();
                    return (at > bt) ? 1 : ((at < bt) ? -1 : 0);            // Tell the sort function how to order	
                });
                options.appendTo("#ddlExist");
                $("#ddlExist option:first").attr('selected', 'selected');
            }
        }

        function Populateddlloadworkflow_Search_Action(msg) {
            var data = msg;
            $("#ctl00_cphBody_ddlWorkflowList option").remove();
            var result = data.split(",");
            if (result == "") {
                var text = "-Select WorkFlow-";
                var value = "000";
                var chkValue = "";
                $("#ctl00_cphBody_ddlWorkflowList").append("<option value='" + value + "'>" + text + "</option>");
            }
            else {
                var text = "-Select WorkFlow-";
                var value = "000";
                var chkValue = "";
                AppendOption('ctl00_cphBody_ddlWorkflowList', value, text);
                //  if (!($("#workFlows option[value='000']").length > 0))	
                for (var i = 0; i < result.length; i++) {
                    chkValue = result[i].split(":");
                    text = chkValue[0];
                    value = chkValue[1];
                    $("#ctl00_cphBody_ddlWorkflowList").append("<option value='" + value + "'>" + text + "</option>");
                }
                var options = $("#ctl00_cphBody_ddlWorkflowList option");                    // Collect options         	
                options.detach().sort(function (a, b) {               // Detach from select, then Sort	
                    var at = $(a).text();
                    var bt = $(b).text();
                    return (at > bt) ? 1 : ((at < bt) ? -1 : 0);            // Tell the sort function how to order	
                });
                options.appendTo("#ctl00_cphBody_ddlWorkflowList");
                $("#ctl00_cphBody_ddlWorkflowList option:first").attr('selected', 'selected');
            }
        }
    </script>

    <script src="../Script/Helper.js" type="text/javascript"></script>

    <script src="../Script/WorkflowProperty.js" type="text/javascript"></script>

    <script src="../Script/WorkFlow.js" type="text/javascript"></script>

    <script src="../Script/ActionSet.js" type="text/javascript"></script>

    <script src="../Script/WorkFlowmenu.js" type="text/javascript"></script>

    <script src="../Script/AlertModal.js" type="text/javascript"></script>

    <script src="../Script/validation.js" type="text/javascript"></script>

    <script src="../Script/jquery.nicescroll.min.js"></script>

    <script src="../Script/MaskedPassword.js"></script>

    <script src="../Script/EncryptDecrypt.js"></script>

    <script type="text/javascript" src="../Script/jquery.combobox.js"></script>


</asp:Content>
