﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class GroupDatabaseNodesBuilder : IEntityBuilder<GroupDatabaseNodes>
    {
        IList<GroupDatabaseNodes> IEntityBuilder<GroupDatabaseNodes>.BuildEntities(IDataReader reader)
        {
            var groupDatabaseNodes = new List<GroupDatabaseNodes>();

            while (reader.Read())
            {
                groupDatabaseNodes.Add(((IEntityBuilder<GroupDatabaseNodes>)this).BuildEntity(reader,
                    new GroupDatabaseNodes()));
            }

            return (groupDatabaseNodes.Count > 0) ? groupDatabaseNodes : null;
        }

        GroupDatabaseNodes IEntityBuilder<GroupDatabaseNodes>.BuildEntity(IDataReader reader,
            GroupDatabaseNodes groupDatabaseNodes)
        {
            //ID, GroupId, PrServerId, DrServerId, PrDbseId, DrDbseId, PrNodeId, DrNodeId, IsActive
            groupDatabaseNodes.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            groupDatabaseNodes.InfraObjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
            groupDatabaseNodes.PrServerId = Convert.IsDBNull(reader["PrServerId"])
                ? 0
                : Convert.ToInt32(reader["PrServerId"]);
            groupDatabaseNodes.DrServerId = Convert.IsDBNull(reader["DrServerId"])
                ? 0
                : Convert.ToInt32(reader["DrServerId"]);
            groupDatabaseNodes.PrDbseId = Convert.IsDBNull(reader["PrDbseId"]) ? 0 : Convert.ToInt32(reader["PrDbseId"]);
            groupDatabaseNodes.DrDbseId = Convert.IsDBNull(reader["DrDbseId"]) ? 0 : Convert.ToInt32(reader["DrDbseId"]);
            groupDatabaseNodes.PrNodeId = Convert.IsDBNull(reader["PrNodeId"]) ? 0 : Convert.ToInt32(reader["PrNodeId"]);
            groupDatabaseNodes.DrNodeId = Convert.IsDBNull(reader["DrNodeId"]) ? 0 : Convert.ToInt32(reader["DrNodeId"]);
            //groupDatabaseNodes.IsActive = Convert.ToBoolean(reader["IsActive"].ToString());
            //
            //    groupDatabaseNodes.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            // Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]); ;

            return groupDatabaseNodes;
        }
    }
}