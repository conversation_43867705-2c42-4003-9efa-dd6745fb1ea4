﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.UI.Code.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Helper;
using CP.ExceptionHandler;
using CP.BusinessFacade;
using Telerik.Web.UI;
using System.Text.RegularExpressions;
using log4net;
using System.Net;

namespace CP.UI.Controls
{
    public partial class Rsyncreplicationconf : ReplicationControl
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(Rsyncreplicationconf));
        public static string IPAddress = string.Empty;

        #region Variable

        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        private RSyncReplication _rsync = null;

        private RSyncJob rsyncjob = new RSyncJob();

        private static readonly IList<RSyncReplication> _finalrsync = new List<RSyncReplication>();
        private static readonly IList<RSyncJob> _finalrsyncjob = new List<RSyncJob>();
        private static readonly IList<Site> _sitetype = new List<Site>();
        public string sitetype;

        //  public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;

        public static string RoboCopyEditURL = Constants.UrlConstants.Urls.Component.RSyncOptionsList;

        #endregion Variable

        #region Properties

        public RSyncReplication CurrentEntity
        {
            get { return _rsync ?? (_rsync = new RSyncReplication()); }
            set
            {
                _rsync = value;
            }
        }

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "RSync Replication"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion Properties

        public override void PrepareView()
        {
            string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST 
            IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();

            Session.Remove("RSync");
            _finalrsyncjob.Clear();
            PrepareEditView();

            ShowListview();
        }

        private void PrepareEditView()
        {

            if (CurrentRSync != null)
            {

                CurrentEntity = CurrentRSync;

                Session["RSync"] = CurrentEntity;

                Session["OracleWithRSync"] = CurrentEntity;



                ddlOsPlatform.SelectedValue = CurrentRSync.OSPlatform;
                txtRjdv.Text = CurrentRSync.RSyncPath;
                ddlRType.SelectedValue = CurrentRSync.ExecuteWithSudo;
                if (ddlRType.SelectedItem.Text == "Yes")
                {
                    txtPassword.Attributes["value"] = CurrentRSync.Password;
                }
                else
                {
                    dvPassword.Visible = false;
                }
                string cronexpression = CurrentRSync.ScheduleTime;

                if (ddlOsPlatform.SelectedValue.Trim() == "Windows")
                {
                    //divDataSyncJREPath.Visible = false;
                    //txtWindowpath.Text = CurrentFastCopy.DataSyncPath;
                    //txtDataSyncJREPath.Text = string.Empty;
                    //txtDataSyncPath.Visible = false;
                    //txtWindowpath.Visible = true;
                    //rfvDatasynpath.Enabled = false;
                    //rfvWindowpath.Enabled = true;

                }

                string[] parts = cronexpression.Split('/');

                if (parts[1].Contains(" * * * ?"))
                {
                    RadioButtonList1.SelectedValue = "Minute(s)";
                    Panel_Minuite.Visible = true;
                    var m = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));
                    m = m.Trim();
                    txteveryminuite.Text = m;
                }
                else if (parts[1].Contains("* * ?"))
                {
                    RadioButtonList1.SelectedValue = "Hour(s)";
                    Panel_Hourly.Visible = true;
                    var t = cronexpression.Substring(1, 3);
                    var w = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));
                    t = t.Trim();
                    w = w.Trim();
                    txteveryhour.Text = w;
                    txteveryhourlyminuite.Text = t;
                }
                else
                {
                    RadioButtonList1.SelectedValue = "Day(s)";
                    Panel_Daily.Visible = true;

                    var d = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));
                    txteverydaily.Text = d;
                    var th = cronexpression.Substring(1, 3);
                    var tm = cronexpression.Substring(4, 4);
                    th = th.Trim();
                    tm = tm.Trim();
                    ddlhours.SelectedItem.Text = tm;
                    ddlminutes.SelectedItem.Text = th;
                }
                BindRSyncJobData();
                btnSave.Text = "Update";
            }
            else
            {
                _finalrsync.Clear();
            }


            //if (CurrentFastCopy != null)
            //{
            //}
            //else
            //{
            //    _finalrobocopyjob.Clear();
            //}


        }

        protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (RadioButtonList1.SelectedValue == "Minute(s)")
            {
                Panel_Minuite.Visible = true;
                Panel_Hourly.Visible = false;
                Panel_Daily.Visible = false;
            }
            else if (RadioButtonList1.SelectedValue == "Hour(s)")
            {
                Panel_Hourly.Visible = true;
                Panel_Minuite.Visible = false;
                Panel_Daily.Visible = false;
            }
            else
            {
                Panel_Daily.Visible = true;
                Panel_Hourly.Visible = false;
                Panel_Minuite.Visible = false;
            }
        }

        protected void lvRSyncJobview_ItemCreated(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                var txtSourceDirectory = (TextBox)e.Item.FindControl("txtInsertLocalDirectory");
                var txtDestinationDirectory = (TextBox)e.Item.FindControl("txtInsertRemoteDirectory");

                var ddlInsertRSyncOptionPropertiesList = (DropDownList)e.Item.FindControl("ddlInsertRSyncOptionPropertiesList");

                if (txtSourceDirectory != null)
                {
                    var txts = txtSourceDirectory.Text;
                }

                if (txtDestinationDirectory != null)
                {
                    var txtd = txtDestinationDirectory.Text;
                }

                if (ddlInsertRSyncOptionPropertiesList != null)
                {
                    // Utility.PopulateRSyncOption(ddlInsertRSyncOptionPropertiesList, true);
                    PopulateRSyncOptions(ddlInsertRSyncOptionPropertiesList);
                    PopulateOracleWithRsyncProperties(ddlInsertRSyncOptionPropertiesList);

                }

            }
        }

        private void PopulateRSyncOptions(ListControl lstRSyncOptionProperties)
        {
            lstRSyncOptionProperties.Items.Clear();

            lstRSyncOptionProperties.InsertItem(0, "- Select RSyncOption Properties -", "000");
            var dsPropertiesList = GetRSyncOptionList();

            if (dsPropertiesList != null)
            {
                int i = 1;
                foreach (var prop in dsPropertiesList)
                {

                    lstRSyncOptionProperties.InsertItem(i, prop.Name, prop.Id.ToString());

                    i++;

                }

            }

            //var RSyncOptionsList = GetRSyncOptionList();



            //if (RSyncOptionsList != null)
            //{
            //    lstRSyncOptionProperties.DataSource = RSyncOptionsList;
            //    lstRSyncOptionProperties.DataTextField = "Name";
            //    lstRSyncOptionProperties.DataValueField = "Id";
            //    lstRSyncOptionProperties.DataBind();
            //    lstRSyncOptionProperties.Items.Insert(0, "- Select RSyncOption Properties -");
            //    // lstRSyncOptionProperties.InsertItem(0,"- Select RSyncOption Properties -","000");
            //    //////lstRSyncOptionProperties.Items.Insert(0, new ListItem("- Select RSyncOption Properties -", ""));
            //    //////lstRSyncOptionProperties.SelectedIndex = 0;

            //    //lstRSyncOptionProperties.Items.Insert(0,"- Select RSyncOption Properties -"); 
            //    // int i = 0;
            //    //foreach (var prop in RSyncOptionsList)
            //    //{

            //    //    lstRSyncOptionProperties(prop.Id,prop.Name);
            //    //   // i++;

            //    //}
            //}


        }



        private void PopulateOracleWithRsyncProperties(ListControl lstRSyncOptionProperties)
        {
            lstRSyncOptionProperties.Items.Clear();

            lstRSyncOptionProperties.InsertItem(0, "-Select Application Rsync Properties-", "000");
            var dsPropertiesList = GetRSyncOptionList();

            if (dsPropertiesList != null)
            {
                int i = 1;
                foreach (var prop in dsPropertiesList)
                {

                    lstRSyncOptionProperties.InsertItem(i, prop.Name, prop.Id.ToString());

                    i++;

                }

            }
        }



        private IList<RSyncOptions> GetRSyncOptionList()
        {

            return Facade.GetAlRSyncOptions();
        }

        protected void CustomValidator2_ServerValidate(object source, ServerValidateEventArgs args)
        {

        }

        protected void CustomValidator3_ServerValidate(object source, ServerValidateEventArgs args)
        {

        }

        protected void lvRSyncJobview_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                Label lblDSProperties = (Label)e.Item.FindControl("lblDSProperties");

                //LinkButton lbDSProperties = (LinkButton)e.Item.FindControl("lbDSProperties");

                if (lblDSProperties != null)
                {
                    ListViewDataItem dataItem = (ListViewDataItem)e.Item;
                    RSyncJob rowView = dataItem.DataItem as RSyncJob;

                    //System.Data.DataRowView rowView = e.Item as System.Data.DataRowView;

                    int dsPropertiesId = rowView.RSyncOptionsId;
                    var dsProperties = Facade.GetByRSyncOptionsId(dsPropertiesId);

                    if (dsProperties != null)
                    {
                        lblDSProperties.Text = dsProperties.Name.ToString();
                    }
                }
            }
        }

        private void ShowListview()
        {
            lvRSyncJobview.DataSource = _finalrsyncjob;
            lvRSyncJobview.DataBind();
        }



        protected void lvRSyncJobview_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {

            var lblId = lvRSyncJobview.Items[e.ItemIndex].FindControl("Id") as Label;
            if (lblId != null && lblId.Text != null)
            {
                if (btnSave.Text == "Update")
                {
                    var rsyncJobDetails = Facade.GetRSyncJobById(Convert.ToInt32(lblId.Text));
                    var RSyncDetails = Facade.GetRSyncById(rsyncJobDetails != null ? Convert.ToInt32(rsyncJobDetails.RSyncId) : 0);
                    var replicationBaseDetails = Facade.GetReplicationBaseById(RSyncDetails != null ? Convert.ToInt32(RSyncDetails.ReplicationId) : 0);
                    //var siteDetails = Facade.GetSiteById(replicationBaseDetails != null ? Convert.ToInt32(replicationBaseDetails.SiteId) : 0);

                    //if (siteDetails.IsActive == 1)
                    //{
                    //    lblError.Text = " Replication is in use.";
                    //    lblError.Visible = true;
                    //    return;
                    //}
                    //else
                    //{
                    lblError.Text = "";
                    lblError.Visible = false;
                    Facade.DeleteRSyncJobById(Convert.ToInt32(lblId.Text));
                    // }
                    BindRSyncJobData();
                    ShowListview();
                }
                else
                {
                    _finalrsyncjob.RemoveAt(e.ItemIndex);
                    ShowListview();
                }
            }

        }

        protected void lvRSyncJobview_ItemInserting(object sender, ListViewInsertEventArgs e)
        {
            //if (!IsValid)

            Page.Validate();
            if (!Page.IsValid) { return; }

            if (btnSave.Text == "Save")
            {
                labelSDErrormessage.Visible = false;
                bool isValid = true;

                var GetIdInward = new RSyncJob();

                TextBox txtsource = (TextBox)e.Item.FindControl("txtInsertLocalDirectory");
                TextBox txtdestination = (TextBox)e.Item.FindControl("txtInsertRemoteDirectory");
                DropDownList ddlInsertRSyncPropertiesList = (DropDownList)e.Item.FindControl("ddlInsertRSyncOptionPropertiesList");

                Label lblsource = (Label)e.Item.FindControl("lblInLocalDirectory");
                Label lblDest = (Label)e.Item.FindControl("lblInRemoteDirectory");
                Label lblInsertRSyncOptionProp = (Label)e.Item.FindControl("lblInsertRSyncOptionProp");

                if (txtsource.Text == "")
                {
                    lblsource.Text = "*";
                    lblsource.Visible = true;
                    isValid = false;
                }
                else
                {
                    var getName = Facade.IsExistRSyncJobByName(txtsource.Text.Trim(), txtdestination.Text.Trim(), ReplicationName.Text.Trim());
                    if (getName)
                    {
                        lblError.Visible = true;
                        lblError.Text = "Record already exist";
                        // PopAuthenticationFailed.Visible = true;
                        //     ModalPopupExtenderCustom.Show();

                        return;
                    }
                }

                if (txtdestination.Text == "")
                {
                    lblDest.Text = "*";
                    lblDest.Visible = true;
                    isValid = false;
                }

                if (ddlInsertRSyncPropertiesList.SelectedValue.Trim() == "0")
                {
                    lblInsertRSyncOptionProp.Text = "*";
                    lblInsertRSyncOptionProp.Visible = true;
                    isValid = false;
                }

                //if (!Regex.Match(txtsource.Text, "^((?!.*//.*)(?!.*/ .*)/{1}([^\\(){}:\*\?<>\|'])+.)$|[a-zA-Z]:(\\\w+)*([\\]|[.])?$").Success)
                //{
                //    // first name was incorrect
                //    //MessageBox.Show("Invalid first name", "Message", MessageBoxButton.OK, MessageBoxImage.Error);
                //    //firstNameTextBox.Focus();
                //    lblEditInRemoteDirectory
                //    return;
                //} 

                if (isValid)
                {
                    lblError.Visible = false;
                    rsyncjob.SourceDirectory = txtsource.Text;
                    rsyncjob.DestinationDirectory = txtdestination.Text;
                    rsyncjob.RSyncOptionsId = Convert.ToInt32(ddlInsertRSyncPropertiesList.SelectedValue);

                    rsyncjob.LastSuccessfullReplTime = string.Empty;
                    rsyncjob.CreatorId = 1;

                    if (!CheckValidationWhileUpdate(rsyncjob)) return;

                    //foreach (var fast in _finalfastcopyjob)
                    //{
                    //    if (fast.SourceDirectory.Contains(txtsource.Text.ToString()) || fast.DestinationDirectory.Contains(txtdestination.Text.ToString()))
                    //    {
                    //        lblError.Visible = true;
                    //        lblError.Text = "Record already exist";
                    //        return;
                    //    }
                    //}

                    if (lvRSyncJobview.Items.Count > 0)
                    {
                        foreach (var fast in _finalrsyncjob)
                        {
                            if (fast.SourceDirectory.Contains(txtsource.Text.ToString()))
                            {
                                if (fast.DestinationDirectory.Contains(txtdestination.Text.ToString()))
                                {
                                    lblError.Visible = true;
                                    lblError.Text = "Source and Destination pair path already exist";
                                    upnlTime.Update();
                                    return;
                                }
                            }
                            if (fast.DestinationDirectory.Contains(txtdestination.Text.ToString()))
                            {
                                if (fast.SourceDirectory.Contains(txtsource.Text.ToString()))
                                {
                                    lblError.Visible = true;
                                    lblError.Text = "Source and Destination pair path already exist";
                                    upnlTime.Update();
                                    return;
                                }
                            }
                        }
                    }

                    _finalrsyncjob.Add(rsyncjob);

                    if (_finalrsyncjob != null)
                    {
                        ShowListview();
                    }
                }
                else
                {
                    return;
                }
            }
            else
            {
                _finalrsyncjob.Clear();
                CurrentEntity = (RSyncReplication)Session["RSync"];
                labelSDErrormessage.Visible = false;
                bool isValid = true;
                var GetIdInward = new RSyncJob();
                TextBox txtsource = (TextBox)e.Item.FindControl("txtInsertLocalDirectory");
                TextBox txtdestination = (TextBox)e.Item.FindControl("txtInsertRemoteDirectory");
                DropDownList ddlInsertDSPropertiesList = (DropDownList)e.Item.FindControl("ddlInsertRSyncOptionPropertiesList");

                Label lblsource = (Label)e.Item.FindControl("lblInLocalDirectory");
                Label lblDest = (Label)e.Item.FindControl("lblInRemoteDirectory");
                Label lblInsertDSProp = (Label)e.Item.FindControl("lblInsertRSyncOptionProp");

                if (txtsource.Text == "")
                {
                    lblsource.Text = "*";
                    lblsource.Visible = true;
                    isValid = false;
                }
                else
                {
                    var getName = Facade.IsExistRSyncJobByName(txtsource.Text.Trim(), txtdestination.Text.Trim(), ReplicationName.Text.Trim());
                    if (getName)
                    {
                        lblError.Visible = true;
                        lblError.Text = "Record already exist";
                        // PopAuthenticationFailed.Visible = true;
                        //     ModalPopupExtenderCustom.Show();

                        return;
                    }
                    else
                    {
                        lblError.Text = "";
                        lblError.Visible = true;
                    }
                }
                if (txtdestination.Text == "")
                {
                    lblDest.Text = "*";
                    lblDest.Visible = true;
                    isValid = false;
                }

                if (ddlInsertDSPropertiesList.SelectedValue.Trim() == "0")
                {
                    lblInsertDSProp.Text = "*";
                    lblInsertDSProp.Visible = true;
                    isValid = false;
                }

                if (isValid)
                {
                    rsyncjob.RSyncId = CurrentEntity.Id;
                    rsyncjob.SourceDirectory = txtsource.Text;
                    rsyncjob.DestinationDirectory = txtdestination.Text;
                    rsyncjob.RSyncOptionsId = Convert.ToInt32(ddlInsertDSPropertiesList.SelectedItem.Value);
                    rsyncjob.LastSuccessfullReplTime = string.Empty;
                    rsyncjob.CreatorId = 1;



                    _finalrsyncjob.Add(rsyncjob);

                    if (_finalrsyncjob.Count != 0)
                    {
                        foreach (RSyncJob job in _finalrsyncjob)
                        {
                            RSyncJob inw = new RSyncJob();

                            inw.RSyncId = job.RSyncId;
                            inw.SourceDirectory = job.SourceDirectory;
                            inw.DestinationDirectory = job.DestinationDirectory;
                            inw.RSyncOptionsId = job.RSyncOptionsId;
                            inw.LastSuccessfullReplTime = job.LastSuccessfullReplTime;

                            inw.CreatorId = 1;
                            Facade.AddRSyncjob(inw);
                        }

                        BindRSyncJobData();
                        if (_finalrsyncjob.Count != 0)
                        {
                            ShowListview();
                        }
                    }
                }
                else
                {
                    return;
                }
            }
        }

        private void BindRSyncJobData()
        {
            CurrentEntity = (RSyncReplication)Session["RSync"];

            if (CurrentEntity != null)
            {
                IList<RSyncJob> rsyncjob = Facade.GetRSyncJobByRSyncId(CurrentEntity.Id);
                _finalrsyncjob.Clear();

                if (Session["PreviousItem"] == null)
                {
                    Session["PreviousItem"] = rsyncjob;
                }

                if (rsyncjob != null)
                {
                    _finalrsyncjob.Clear();

                    foreach (RSyncJob job in rsyncjob)
                    {
                        var rsyncjobdetails = new RSyncJob();
                        rsyncjobdetails.Id = job.Id;
                        rsyncjobdetails.RSyncId = job.RSyncId;
                        rsyncjobdetails.SourceDirectory = job.SourceDirectory;
                        rsyncjobdetails.DestinationDirectory = job.DestinationDirectory;
                        rsyncjobdetails.RSyncOptionsId = job.RSyncOptionsId;
                        _finalrsyncjob.Add(rsyncjobdetails);

                    }
                }
            }
        }

        protected void lvRSyncJobview_ItemCanceling(object sender, ListViewCancelEventArgs e)
        {
            lvRSyncJobview.EditIndex = -1;

            if (_finalrsyncjob.Count != 0)
            {
                ShowListview();
            }
        }

        protected void lvRSyncJobview_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Label lblId = lvRSyncJobview.Items[e.NewEditIndex].FindControl("Id") as Label;
            var Id = lblId.Text;

            if (btnSave.Text == "Save")
            {
                lvRSyncJobview.EditIndex = e.NewEditIndex;
                lvRSyncJobview.DataSource = _finalrsyncjob;
                lvRSyncJobview.DataBind();
            }
            else
            {
                lvRSyncJobview.EditIndex = e.NewEditIndex;
                CurrentEntity = (RSyncReplication)Session["RSync"];

                if (CurrentEntity != null)
                {
                    IList<RSyncJob> rsyncjob = Facade.GetRSyncJobByRSyncId(CurrentEntity.Id);

                    if (rsyncjob != null)
                    {
                        lvRSyncJobview.DataSource = rsyncjob;
                        lvRSyncJobview.DataBind();
                    }
                }
            }

            Label lblEditDSPropId = lvRSyncJobview.Items[e.NewEditIndex].FindControl("lblEditDSPropId") as Label;
            DropDownList ddlEditDSPropertiesList = lvRSyncJobview.Items[e.NewEditIndex].FindControl("ddlEditRSyncOptionPropertiesList") as DropDownList;
            if (ddlEditDSPropertiesList != null && lblEditDSPropId != null)
            {
                PopulateRSyncOptions(ddlEditDSPropertiesList);
                ddlEditDSPropertiesList.SelectedValue = lblEditDSPropId.Text.Trim();
            }
        }

        protected void lvRSyncJobview_ItemCommand(object sender, ListViewCommandEventArgs e)
        {

        }

        protected void lvRSyncJobview_ItemUpdating(object sender, ListViewUpdateEventArgs e)
        {
            if (Session["RSync"] != null)
            {
                CurrentEntity = (RSyncReplication)Session["RSync"];
            }

            var lbl = (lvRSyncJobview.Items[e.ItemIndex].FindControl("Id")) as Label;
            var lblId = lbl.Text.ToInteger();

            TextBox txtSourceDirectory = (lvRSyncJobview.Items[e.ItemIndex].FindControl("txtLocalDirectory")) as TextBox;
            TextBox txtDestinationDirectory = (lvRSyncJobview.Items[e.ItemIndex].FindControl("txtRemoteDirectory")) as TextBox;

            DropDownList ddlEditDSPropertiesList = (lvRSyncJobview.Items[e.ItemIndex].FindControl("ddlEditRSyncOptionPropertiesList")) as DropDownList;

            Label lblUpdateId = (lvRSyncJobview.Items[e.ItemIndex].FindControl("Id")) as Label;
            if (lblUpdateId != null)
            {
                rsyncjob.Id = Convert.ToInt32(lblUpdateId.Text);
            }
            var txtsourcedirectory = (lvRSyncJobview.Items[e.ItemIndex].FindControl("txtLocalDirectory")) as TextBox;

            if (txtsourcedirectory != null && txtsourcedirectory.Text != "")
            {
                rsyncjob.SourceDirectory = txtsourcedirectory.Text.Trim();
            }
            var txtdestinationdirectory = (lvRSyncJobview.Items[e.ItemIndex].FindControl("txtRemoteDirectory")) as TextBox;
            if (txtdestinationdirectory != null && txtdestinationdirectory.Text != "")
            {
                rsyncjob.DestinationDirectory = txtdestinationdirectory.Text.Trim();
            }

            if (ddlEditDSPropertiesList != null)
            {
                rsyncjob.RSyncOptionsId = Convert.ToInt32(ddlEditDSPropertiesList.SelectedItem.Value);
            }

            if (btnSave.Text == "Save")
            {

                _finalrsyncjob.Insert(e.ItemIndex, rsyncjob);
                _finalrsyncjob.RemoveAt(e.ItemIndex + 1);
                lvRSyncJobview.EditIndex = -1;
                ShowListview();
            }
            else
            {
                _finalrsyncjob.Insert(e.ItemIndex, rsyncjob);
                _finalrsyncjob.RemoveAt(e.ItemIndex + 1);
                lvRSyncJobview.EditIndex = -1;
                ShowListview();
                // rsyncjob.Id = lblUpdateId.Text.ToInteger();
                // rsyncjob.RSyncId = CurrentEntity.Id;
                // rsyncjob.SourceDirectory = txtsourcedirectory.Text;
                // rsyncjob.DestinationDirectory = txtdestinationdirectory.Text;
                // rsyncjob.RSyncOptionsId = Convert.ToInt32(ddlEditDSPropertiesList.SelectedItem.Value);
                // var BindlastRepTime = Facade.GetRSyncJobById(rsyncjob.Id);
                // rsyncjob.LastSuccessfullReplTime = BindlastRepTime.LastSuccessfullReplTime;

                //// Facade.UpdateRSyncjob(rsyncjob);
                // _finalrsyncjob.Add(rsyncjob);
                // lvRSyncJobview.EditIndex = -1;
                // BindRSyncJobData();
                // if (_finalrsyncjob.Count != 0)
                // {
                //     ShowListview();
                // }
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            // lblErrorMsg.Visible = false;
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }

           

            if (!CheckValidation()) return;

            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            if (ddlRType.SelectedValue == "1" || ddlRType.SelectedValue == "0")
            {
                if (string.IsNullOrEmpty(txtPassword.Text))
                {
                    lblPassErrorMsg.Visible = true;
                    return;
                }
                else
                {
                    lblPassErrorMsg.Visible = false;
                }
            }

            if (Page.IsPostBack)
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }
                try
                {
                    BuildEntities();
                    StartTransaction();
                    SaveEditor();
                    EndTransaction();
                    string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                    //ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(CurrentEntity.ModeType.ToString() + " (" + CurrentEntity.ReplicationBase.Name + ")",
                    //                                                                        currentTransactionType));

                    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                              currentTransactionType));
                    btnSave.Enabled = false;
                }
                catch (CpException ex)
                {
                    InvalidateTransaction();
                    returnUrl = Request.RawUrl;
                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();
                    returnUrl = Request.RawUrl;
                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                    {
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);
                    }
                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                        ExceptionManager.Manage(customEx, Page);
                    }
                }

                if (returnUrl.IsNotNullOrEmpty())
                {
                    if (ReplicationType.SelectedValue == "108")
                    {
                        //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "108");
                        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "108");
                        Helper.Url.Redirect(secureUrl);
                    }
                }

                if (returnUrl.IsNotNullOrEmpty())
                {
                    if (ReplicationType.SelectedValue == "114")
                    {
                        //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "108");
                        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "114");
                        Helper.Url.Redirect(secureUrl);
                    }
                }
            }
            //}
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }
            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        private bool CheckValidation()
        {
            var isValid = true;

            if (RadioButtonList1.SelectedValue == "")
            {
                isValid = false;
                lbltimeintervalErrormessage.Visible = true;
            }

            if (_finalrsyncjob.Count == 0)
            {
                isValid = false;
                labelSDErrormessage.Visible = true;
                labelSDErrormessage.Text = "Select Source, Destination Directories and RSyncOption Properties ";
            }

            return isValid;
        }

        private bool CheckValidationWhileUpdate(RSyncJob _RSyncJob)
        {
            var isValid = true;
            if (string.IsNullOrEmpty(_RSyncJob.DestinationDirectory)
                || string.IsNullOrEmpty(_RSyncJob.SourceDirectory)
                || _RSyncJob.RSyncOptionsId == 0)
            {
                isValid = false;
                labelSDErrormessage.Visible = true;
                labelSDErrormessage.Text = "Select Source, Destination Directories and RSyncOption Properties ";
            }
            else
            {
                isValid = true;
                labelSDErrormessage.Visible = false;
            }

            return isValid;
        }

        private void BuildEntities()
        {
            if (Session["RSync"] != null)
            {
                CurrentEntity = (RSyncReplication)Session["RSync"];
            }

            int repid = CurrentEntity.ReplicationId;
            int repid1 = CurrentReplicationId;

            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Id = repid;
            CurrentEntity.ReplicationBase.Id = repid1;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);

            CurrentEntity.OSPlatform = ddlOsPlatform.SelectedValue;
            CurrentEntity.RSyncPath = txtRjdv.Text;
            CurrentEntity.ExecuteWithSudo = ddlRType.SelectedValue;
            if (ddlRType.SelectedItem.Text == "Yes")
            {
                CurrentEntity.Password = Utility.IsMD5EncryptedString(txtPassword.Text) ? txtPassword.Text : CryptographyHelper.Md5Encrypt(txtPassword.Text);
            }
            else
            {
                dvPassword.Visible = false;
            }
            if (ddlRType.SelectedItem.Text == "No")
            {
                CurrentEntity.Password = Utility.IsMD5EncryptedString(txtPassword.Text) ? txtPassword.Text : CryptographyHelper.Md5Encrypt(txtPassword.Text);
            }
            else
            {
                dvPassword.Visible = false;
            }

            string cronstring = string.Empty;
            if (RadioButtonList1.SelectedValue == "Minute(s)")
            {
                cronstring = string.Format("0 0/{0} * * * ?", txteveryminuite.Text);
            }
            else if (RadioButtonList1.SelectedValue == "Hour(s)")
            {
                cronstring = string.Format("0 {0} 0/{1} * * ?", txteveryhourlyminuite.Text, txteveryhour.Text);
            }
            else
            {
                cronstring = string.Format("0 {0} {1} 1/{2} * ?", ddlminutes.SelectedValue, ddlhours.SelectedValue, txteverydaily.Text);
            }
            CurrentEntity.ScheduleTime = cronstring;

        }

        private void SaveEditor()
        {
            try
            {
                var LastId = 0;
                // if (CurrentEntity.Id == 0)
                if (CurrentEntity.IsNew)
                {
                    CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                    CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                    //  CurrentEntity = Facade.AddFastCopy(CurrentEntity);
                    var insertId = Facade.AddRSync(CurrentEntity);
                    LastId = insertId.Id;
                    ActivityLogger.AddLog1(LoggedInUserName, "RSync", UserActionType.CreateReplicationComponent, "The RSync component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId, IPAddress);
                    _logger.Info("Replication component" + "'" + CurrentEntity.ReplicationBase.Name + "'" + "added  successfully. With User IP Address " + "'" + IPAddress + "'");
                    if (_finalrsyncjob.Count != 0)
                    {
                        foreach (RSyncJob inward in _finalrsyncjob)
                        {
                            RSyncJob fastcopyjob = new RSyncJob();
                            fastcopyjob.RSyncId = LastId;

                            fastcopyjob.SourceDirectory = inward.SourceDirectory;
                            fastcopyjob.DestinationDirectory = inward.DestinationDirectory;
                            fastcopyjob.RSyncOptionsId = inward.RSyncOptionsId;
                            fastcopyjob.LastSuccessfullReplTime = inward.LastSuccessfullReplTime;
                            fastcopyjob.CreatorId = 1;
                            Facade.AddRSyncjob(fastcopyjob);
                            ActivityLogger.AddLog1(LoggedInUserName, "RSyncOptions", UserActionType.CreateFastCopyJob, "The RSync '" + fastcopyjob.RSyncId + "' was added to the RSyncJob component.", LoggedInUserId, IPAddress);
                            _logger.Info("Replication component" + "'" + CurrentEntity.ReplicationBase.Name + "'" + "added  successfully. With User IP Address " + "'" + IPAddress + "'");
                        }
                    }
                }
                else
                {
                    LastId = 0;
                    CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                    // CurrentEntity = Facade.UpdateFastcopy(CurrentEntity);
                    var updateId = Facade.UpdateRSync(CurrentEntity);
                    LastId = updateId.Id;

                    ActivityLogger.AddLog(LoggedInUserName, "RSync", UserActionType.CreateReplicationComponent, "The RSync component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
                    _logger.Info("Replication component" + "'" + CurrentEntity.ReplicationBase.Name + "'" + "updated  successfully. With User IP Address " + "'" + IPAddress + "'");
                    if (_finalrsyncjob.Count != 0)
                    {
                        foreach (RSyncJob inward in _finalrsyncjob)
                        {
                            RSyncJob fastcopyjob = new RSyncJob();
                            fastcopyjob.Id = inward.Id;
                            fastcopyjob.RSyncId = LastId;

                            fastcopyjob.SourceDirectory = inward.SourceDirectory;
                            fastcopyjob.DestinationDirectory = inward.DestinationDirectory;
                            fastcopyjob.RSyncOptionsId = inward.RSyncOptionsId;
                            fastcopyjob.LastSuccessfullReplTime = inward.LastSuccessfullReplTime;
                            fastcopyjob.UpdatorId = 1;
                            Facade.UpdateRSyncjob(fastcopyjob);
                            ActivityLogger.AddLog(LoggedInUserName, "RSyncOptions", UserActionType.CreateFastCopyJob, "The RSync '" + fastcopyjob.RSyncId + "' was added to the RSyncJob component.", LoggedInUserId);
                            _logger.Info("Replication component" + "'" + CurrentEntity.ReplicationBase.Name + "'" + "updated  successfully. With User IP Address " + "'" + IPAddress + "'");
                        }
                    }
                }
            }
            catch (CpException ex)
            {
                if (ex != null)
                {
                    _logger.Error("CP exception while loading Rsyncreplicationconf in  SaveEditor method on Rsyncreplicationconf page load: With User IP Address." + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        _logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        _logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                ExceptionManager.Manage(ex);
            }
        }

        public string RSyncOptionName(object id)
        {
            // id = Session["RsyncPrpId"];
            var dsPropertiesList = GetRSyncOptionList();
            if (dsPropertiesList != null && dsPropertiesList.Count > 0)
            {

                var RSyncOption = Facade.GetByRSyncOptionsId(Convert.ToInt32(id));
                if (RSyncOption != null)
                {
                    return RSyncOption.Name;
                }
            }
            return "";
        }

        protected void Validate_RCJobName(object source, ServerValidateEventArgs args)
        {
            TextBox txtInsertLocalDirectory = (lvRSyncJobview.InsertItem.FindControl("txtInsertLocalDirectory")) as TextBox;

            string textBoxName = ((CustomValidator)source).ControlToValidate;
            var textBox = ((CustomValidator)source).Parent.FindControl(textBoxName) as TextBox;

            string sourcepath = args.Value;

            if (_finalrsyncjob != null && _finalrsyncjob.Count > 0)
            {
                foreach (var rcjob in _finalrsyncjob)
                {
                    if (rcjob.SourceDirectory.Trim().Equals(sourcepath.Trim()))
                    {
                        args.IsValid = false;
                        break;
                    }
                }
            }
        }

        protected void Validate_DestiPath(object source, ServerValidateEventArgs args)
        {
            TextBox txtInsertRemoteDirectory = (lvRSyncJobview.InsertItem.FindControl("txtInsertRemoteDirectory")) as TextBox;

            string destipath = args.Value;

            if (_finalrsyncjob != null && _finalrsyncjob.Count > 0)
            {
                foreach (var rcjob in _finalrsyncjob)
                {
                    if (rcjob.DestinationDirectory.Trim().Equals(destipath.Trim()))
                    {
                        args.IsValid = false;
                        break;
                    }
                }
            }
        }

        protected void Validate_EditRCJobName(object source, ServerValidateEventArgs args)
        {
            TextBox txtLocalDirectory = (lvRSyncJobview.EditItem.FindControl("txtLocalDirectory")) as TextBox;

            string sourcepath = args.Value;

            if (_finalrsyncjob != null && _finalrsyncjob.Count > 0)
            {
                int i = 0;
                foreach (var rcjob in _finalrsyncjob)
                {
                    if (lvRSyncJobview.EditIndex != i)
                    {
                        if (rcjob.SourceDirectory.Trim().Equals(sourcepath.Trim()))
                        {
                            args.IsValid = false;
                            break;
                        }
                    }
                    i++;
                }
            }
        }

        protected void Validate_EditDestiPath(object source, ServerValidateEventArgs args)
        {
            TextBox txtRemoteDirectory = (lvRSyncJobview.EditItem.FindControl("txtRemoteDirectory")) as TextBox;

            string destipath = args.Value;

            if (_finalrsyncjob != null && _finalrsyncjob.Count > 0)
            {
                int i = 0;
                foreach (var rcjob in _finalrsyncjob)
                {
                    if (lvRSyncJobview.EditIndex != i)
                    {
                        if (rcjob.DestinationDirectory.Trim().Equals(destipath.Trim()))
                        {
                            args.IsValid = false;
                            break;
                        }
                    }
                    i++;
                }
            }
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        protected void ddlRType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlRType.SelectedItem.Text == "Yes")
            {
                dvPassword.Visible = true;
            }
            else
            {
                dvPassword.Visible = false;
            }
        }



    }
}