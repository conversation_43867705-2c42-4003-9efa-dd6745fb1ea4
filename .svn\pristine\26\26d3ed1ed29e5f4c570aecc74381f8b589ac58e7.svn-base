﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;


namespace CP.DataAccess
{
    internal sealed class RecoveryPStateMonitorBuilder : IEntityBuilder<RecoverPStateMonitor>
    {
        IList<RecoverPStateMonitor> IEntityBuilder<RecoverPStateMonitor>.BuildEntities(IDataReader reader)
        {
            var objRecoverPStateMonitor = new List<RecoverPStateMonitor>();

            while (reader.Read())
            {
                objRecoverPStateMonitor.Add(((IEntityBuilder<RecoverPStateMonitor>)this).BuildEntity(reader, new RecoverPStateMonitor()));
            }

            return (objRecoverPStateMonitor.Count > 0) ? objRecoverPStateMonitor : null;
        }

        RecoverPStateMonitor IEntityBuilder<RecoverPStateMonitor>.BuildEntity(IDataReader reader, RecoverPStateMonitor recoverPStateMonitor)
        {

            recoverPStateMonitor.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

            recoverPStateMonitor.GGroupName = Convert.IsDBNull(reader["GGroupName"]) ? string.Empty : Convert.ToString(reader["GGroupName"]);

            recoverPStateMonitor.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);

            recoverPStateMonitor.TransferSource = Convert.IsDBNull(reader["TransferSource"]) ? string.Empty : Convert.ToString(reader["TransferSource"]);

            recoverPStateMonitor.Copy = Convert.IsDBNull(reader["Copy"]) ? string.Empty : Convert.ToString(reader["Copy"]);

            recoverPStateMonitor.Journal = Convert.IsDBNull(reader["Journal"]) ? string.Empty : Convert.ToString(reader["Journal"]);

            recoverPStateMonitor.StorageAccess = Convert.IsDBNull(reader["StorageAccess"]) ? string.Empty : Convert.ToString(reader["StorageAccess"]);

            recoverPStateMonitor.Link = Convert.IsDBNull(reader["Link"]) ? string.Empty : Convert.ToString(reader["Link"]);

            recoverPStateMonitor.DataTransferStatus = Convert.IsDBNull(reader["DataTransferStatus"]) ? string.Empty : Convert.ToString(reader["DataTransferStatus"]);

            recoverPStateMonitor.siteName = Convert.IsDBNull(reader["siteName"]) ? string.Empty : Convert.ToString(reader["siteName"]);

            recoverPStateMonitor.ApplianceVersion = Convert.IsDBNull(reader["ApplianceVersion"]) ? string.Empty : Convert.ToString(reader["ApplianceVersion"]);

            recoverPStateMonitor.Enabled = Convert.IsDBNull(reader["Enabled"]) ? string.Empty : Convert.ToString(reader["Enabled"]);


            return recoverPStateMonitor;
        }
    }
}
