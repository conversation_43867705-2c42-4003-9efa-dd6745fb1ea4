﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "bcms_hitachiur_luns", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class HitachiUrLuns : BaseEntity
    {
        #region Member Variable

        private ReplicationBase _replicationBase = new ReplicationBase();

        #endregion Member Variable

        #region Properties

        [DataMember]
        public string ArchVGName { get; set; }

        [DataMember]
        public string ArchMountPoint { get; set; }

        [DataMember]
        public string ArchDevicetype { get; set; }

        [DataMember]
        public string ArchHURTrueCopySource { get; set; }

        [DataMember]
        public string ArchShadowimagePR { get; set; }

        [DataMember]
        public string ArchTarget { get; set; }

        [DataMember]
        public string RedoVGName { get; set; }

        [DataMember]
        public string RedoMountPoint { get; set; }

        [DataMember]
        public string RedoDeviceType { get; set; }

        [DataMember]
        public string RedoHURTrueCopySource { get; set; }

        [DataMember]
        public string RedoShadowimagePR { get; set; }

        [DataMember]
        public string RedoTarget { get; set; }

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public ReplicationBase ReplicationBase
        {
            get { return _replicationBase; }
            set { _replicationBase = value; }
        }

        #endregion Properties
    }
}