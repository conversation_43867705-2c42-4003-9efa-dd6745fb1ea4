﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class IBMXIVMirrorConfiguration : ReplicationControl
    {

        #region Variable
        private static readonly List<InfraobjectCGDetails> SaveLuns = new List<InfraobjectCGDetails>();
        private InfraobjectCGDetails lunsnew = new InfraobjectCGDetails();
        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        private XIVConfiguration _IbmXivMirror = null;
        private InfraobjectCGDetails _CGs;

        #endregion Variable

        #region Properties

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "IBMXIVMirror Information"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public XIVConfiguration CurrentEntity
        {
            get { return _IbmXivMirror ?? (_IbmXivMirror = new XIVConfiguration()); }
            set
            {
                _IbmXivMirror = value;
            }
        }

        #endregion Properties


        public override void PrepareView()
        {
            //ddlServerPR.Attributes.Add("onblur", "ValidatorValidate(" + rfvddlServerPR.ClientID + ")");
            //ddlServerDR.Attributes.Add("onblur", "ValidatorValidate(" + rfvddlServerDR.ClientID + ")");
            //ddlPrServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvPRServer.ClientID + ")");
            //ddlDrServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvDRServer.ClientID + ")");
            //txtPRStorageImgID.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator5.ClientID + ")");
            //txtDRStorageImgID.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator6.ClientID + ")");
            //txtPRWNNNo.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");
            //txtDRWNNNo.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");
            // txtpassword.Attributes.Add("onblur", "getHashData(" + txtpassword.ClientID + ")");



            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            Session.Remove("IBMXIVMirror");

            Utility.PopulateServerByTypeAndRole(ddlServerPR, "XCLIServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            Utility.PopulateServerByTypeAndRole(ddlServerDR, "XCLIServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            Utility.PopulateServerByTypeAndRole(ddlPrServer, "HMCServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            Utility.PopulateServerByTypeAndRole(ddlDrServer, "HMCServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);
            PrepareEditView();
        }


        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((Session["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}

        public void PrepareEditView()
        {
            if (CurrentIBMXIVMirror != null && CurrentIBMXIVMirror.Id > 0)
            {
                CurrentEntity = CurrentIBMXIVMirror;

                Session["IBMXIVMirror"] = CurrentEntity;

                Session.Remove("PreviousItem");

                txtDSCLIHostnamePR.Enabled = true;
                txtDSCLIServerIPPR.Enabled = true;
                txtSSHUserIDPR.Enabled = true;
                txtSSHPasswordPR.Enabled = true;

                txtDSCLIHostnameDR.Enabled = true;
                txtDSCLIServerIPDR.Enabled = true;
                txtSSHUserIDDR.Enabled = true;
                txtSSHPasswordDR.Enabled = true;

                ddlServerPR.SelectedValue = CurrentEntity.PRXCLIServerId.ToString();
                ddlServerDR.SelectedValue = CurrentEntity.DRXCLIServerId.ToString();
                PRXCLIServerDetails();
                DRXCLIServerDetails();

                txtDSCLIPathPR.Text = CurrentEntity.PRXCLIPath.ToString();
                txtDSCLIPathDR.Text = CurrentEntity.DRXCLIPath.ToString();

                ddlPrServer.SelectedValue = CurrentEntity.PRHMCServerId.ToString();
                ddlDrServer.SelectedValue = CurrentEntity.DRHMCServerId.ToString();

                PrserverDetails();
                DrserverDetails();

                txtPRMgtConsoleIP.Visible = true;
                txtDRMgtConsoleIP.Visible = true;

                txtreplicationmode.Text = CurrentEntity.ReplicationMode;

                txtPRStorageImgID.Text = CurrentEntity.PRStorageImageID;
                txtDRStorageImgID.Text = CurrentEntity.DRStorageImageID;

                txtPRWNNNo.Text = CurrentEntity.PRStorageName;
                txtDRWNNNo.Text = CurrentEntity.DRStorageName;

                ShowList123();
                SaveRep.Text = "Update";
            }
            else
            {
                SaveLuns.Clear();
                ShowList();
                // PanelManualtest.Visible = true;
                txtSSHUserIDPR.Text = string.Empty;
                txtSSHPasswordPR.Attributes.Clear();

                txtSSHUserIDDR.Text = string.Empty;
                txtSSHPasswordPR.Attributes.Clear();
            }
        }

        private void PrserverDetails()
        {
            if (ddlPrServer.SelectedValue != "00")
            {
                var server = Facade.GetServerById(CurrentEntity.Id > 0 ? CurrentEntity.PRHMCServerId : ddlPrServer.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtPRMgtConsoleIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtPRUserName.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                   // txtPRPassword.Attributes["value"] = server.SSHPassword;
                    txtPRPassword.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                    txtPRPassword.Attributes["value"] = Utility.getHashKeyByString(txtPRPassword.Text, hdfStaticGuid.Value);
                    

                }

                DisableHMCPRServer();
            }
            else
            {
                txtPRMgtConsoleIP.Text = string.Empty;
                txtPRUserName.Text = string.Empty;
                txtPRPassword.Attributes["value"] = string.Empty;
            }
        }

        private void DrserverDetails()
        {
            if (ddlDrServer.SelectedValue != "00")
            {
                var server = Facade.GetServerById(CurrentEntity.Id > 0 ? CurrentEntity.DRHMCServerId : ddlDrServer.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtDRMgtConsoleIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtDRUserName.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                   // txtDRPassword.Attributes["value"] = server.SSHPassword;
                    txtDRPassword.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                    txtDRPassword.Attributes["value"] = Utility.getHashKeyByString(txtDRPassword.Text, hdfStaticGuid.Value);
                }
                DisableHMCDRServer();
            }
            else
            {
                txtDRMgtConsoleIP.Text = string.Empty;
                txtDRUserName.Text = string.Empty;
                txtDRPassword.Attributes["value"] = string.Empty;
            }
        }

        private void DisableHMCPRServer()
        {
            txtPRMgtConsoleIP.Enabled = false;
            txtPRUserName.Enabled = false;
            txtPRPassword.Enabled = false;
        }

        private void DisableHMCDRServer()
        {
            txtDRMgtConsoleIP.Enabled = false;
            txtDRUserName.Enabled = false;
            txtDRPassword.Enabled = false;
        }

        private void PRXCLIServerDetails()
        {
            if (ddlServerPR.SelectedValue != "0")
            {
                var server = Facade.GetServerById(CurrentEntity.Id > 0 ? CurrentEntity.PRXCLIServerId : ddlServerPR.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtDSCLIHostnamePR.Text = server.Name;
                    txtDSCLIServerIPPR.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtSSHUserIDPR.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                    txtSSHPasswordPR.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                    txtSSHPasswordPR.Attributes["value"] = Utility.getHashKeyByString(txtSSHPasswordPR.Text, hdfStaticGuid.Value);
                   // txtSSHPasswordPR.Attributes.Add("onblur", "getHashData(" + txtSSHPasswordPR.Text + ")");
                   // txtSSHPasswordPR.Attributes["value"] = CryptographyHelper.Md5Encrypt(_txtSSHPasswordPR);
                  // txtSSHPasswordPR.Attributes["value"] = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(_txtSSHPasswordPR, hdfStaticGuid.Value));
                }

                PRDisableXCLI();
            }
            else
            {
                PRDisableXCLI();
                txtDSCLIHostnamePR.Text = string.Empty;
                txtDSCLIServerIPPR.Text = string.Empty;
                txtSSHUserIDPR.Text = string.Empty;
                txtSSHPasswordPR.Attributes["value"] = string.Empty;
            }
        }

        private void DRXCLIServerDetails()
        {
            if (ddlServerDR.SelectedValue != "0")
            {
                var server = Facade.GetServerById(CurrentEntity.Id > 0 ? CurrentEntity.DRXCLIServerId : ddlServerDR.SelectedValue.ToInteger());

                if (server != null)
                {
                    txtDSCLIHostnameDR.Text = server.Name;
                    txtDSCLIServerIPDR.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtSSHUserIDDR.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                    //txtSSHPasswordDR.Attributes["value"] = server.SSHPassword;
                    txtSSHPasswordDR.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                    txtSSHPasswordDR.Attributes["value"] = Utility.getHashKeyByString(txtSSHPasswordDR.Text, hdfStaticGuid.Value);
                }

                DRDisableXCLI();
            }
            else
            {
                DRDisableXCLI();
                txtDSCLIHostnameDR.Text = string.Empty;
                txtDSCLIServerIPDR.Text = string.Empty;
                txtSSHUserIDDR.Text = string.Empty;
                txtSSHPasswordDR.Attributes["value"] = string.Empty;
            }
        }

        private void PRDisableXCLI()
        {
            txtDSCLIHostnamePR.Enabled = false;
            txtDSCLIServerIPPR.Enabled = false;
            txtSSHUserIDPR.Enabled = false;
            txtSSHPasswordPR.Enabled = false;
        }

        private void DRDisableXCLI()
        {
            txtDSCLIHostnameDR.Enabled = false;
            txtDSCLIServerIPDR.Enabled = false;
            txtSSHUserIDDR.Enabled = false;
            txtSSHPasswordDR.Enabled = false;
        }

        protected void DdlServerPRSelectedIndexChanged(object sender, EventArgs e)
        {
            PRXCLIServerDetails();
        }

        protected void DdlServerDRSelectedIndexChanged(object sender, EventArgs e)
        {
            DRXCLIServerDetails();
        }

        protected void DdlPrServerSelectedIndexChanged(object sender, EventArgs e)
        {
            PrserverDetails();
        }

        protected void DdlDrServerSelectedIndexChanged(object sender, EventArgs e)
        {
            DrserverDetails();

        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        private void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddIBMXIVMirror(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "IBMXIVMirror", UserActionType.CreateReplicationComponent, "The IBMXIVMirror Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
            }
            else
            {
                CurrentEntity.Id = CurrentIBMXIVMirror.Id;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateIBMXIVMirror(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "IBMXIVMirror", UserActionType.UpdateReplicationComponent, "The IBMXIVMirror Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        private void BuildEntities()
        {
            if (Session["IBMXIVMirror"] != null)
            {
                CurrentEntity = (XIVConfiguration)Session["IBMXIVMirror"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;

            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;

            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);

            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);

            CurrentEntity.PRXCLIServerId = Convert.ToInt32(ddlServerPR.SelectedValue);

            CurrentEntity.DRXCLIServerId = Convert.ToInt32(ddlServerDR.SelectedValue);

            CurrentEntity.PRHMCServerId = Convert.ToInt32(ddlPrServer.SelectedValue);

            CurrentEntity.DRHMCServerId = Convert.ToInt32(ddlDrServer.SelectedValue);

            CurrentEntity.PRXCLIPath = txtDSCLIPathPR.Text.Trim();

            CurrentEntity.DRXCLIPath = txtDSCLIPathDR.Text.Trim();

            CurrentEntity.ReplicationMode = txtreplicationmode.Text.Trim();

            CurrentEntity.PRStorageImageID = txtPRStorageImgID.Text.Trim();

            CurrentEntity.DRStorageImageID = txtDRStorageImgID.Text.Trim();

            CurrentEntity.PRStorageName = txtPRWNNNo.Text.Trim();

            CurrentEntity.DRStorageName = txtDRWNNNo.Text.Trim();

            BuildGlobalMirrorList();
        }

        private void BuildGlobalMirrorList()
        {
            foreach (var item in lvManual.Items)
            {
                var lbl1 = (Label)item.FindControl("PRName");
                var lbl2 = (Label)item.FindControl("DRName");

                _CGs = new InfraobjectCGDetails
                {
                    PRCGName = lbl1.Text,
                    DRCGName = lbl2.Text,
                };
                CurrentEntity.IBMXIVMirrores.Add(_CGs);
            }
        }

        protected void SaveRepClick(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (Page.IsValid && (Session["ReplicationConfig_token"] != null) && !CheckReplicationNameExist() && ValidateRequest("IBMXIVMirrorConfiguration", UserActionType.CreateReplicationComponent))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {
                    if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
                    {
                        lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
                    }
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }

                    var submitButton = (Button)sender;
                    string buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }
                    try
                    {
                        if (ValidateRequest("IBMXIVMirrorConfiguration", UserActionType.CreateReplicationComponent))
                        {
                            BuildEntities();
                            StartTransaction();
                            SaveEditor();
                            EndTransaction();

                            string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                 currentTransactionType));

                            SaveRep.Enabled = false;
                        }
                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, Page);
                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, Page);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, Page);
                        }
                    }

                    if (returnUrl.IsNotNullOrEmpty())
                    {

                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);

                        //Helper.Url.Redirect(returnUrl);
                        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                        Helper.Url.Redirect(secureUrl);

                    }
                    //if (returnUrl.IsNotNullOrEmpty())
                    //{
                    //    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                    //    Helper.Url.Redirect(secureUrl);
                    //}
                }
            }
        }   

        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }

                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtSSHUser");
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        protected void LvManualItemInserting(object sender, ListViewInsertEventArgs e)
        {
            if (Session["IBMXIVMirror"] != null)
            {
                CurrentEntity = (XIVConfiguration)Session["IBMXIVMirror"];
            }

            bool isValid = true;
            bool isAVolumeDup = false;
            bool isBVolumeDup = false;

            var getIdLuns = new InfraobjectCGDetails();
            var aVolume = (TextBox)e.Item.FindControl("PRName");
            var bVolume = (TextBox)e.Item.FindControl("DRName");

            var lblA = (Label)e.Item.FindControl("lblAVolume");
            var lblB = (Label)e.Item.FindControl("lblBVolume");

            var AVolumeDup = string.Empty;
            var BVolumeDup = string.Empty;

            if (aVolume.Text == "")
            {
                lblA.Text = "*";
                lblA.Visible = true;
                isValid = false;

            }
            if (bVolume.Text == "")
            {
                lblB.Text = "*";
                lblB.Visible = true;
                isValid = false;
            }

            for (int i = 0; i < lvManual.Items.Count(); i++)
            {
                AVolumeDup = ((Label)(lvManual.Items[i].FindControl("PRName"))).Text;
                BVolumeDup = ((Label)(lvManual.Items[i].FindControl("DRName"))).Text;

                if (AVolumeDup.Equals(aVolume.Text) && AVolumeDup != string.Empty)
                {
                    lblA.Text = "Not Available";
                    isValid = false;
                    isAVolumeDup = true;
                    lblA.Visible = true;

                }
                else if (!isAVolumeDup && aVolume.Text != string.Empty)
                {
                    lblA.Text = "";
                    lblA.Visible = false;
                }
                if (BVolumeDup.Equals(bVolume.Text) && BVolumeDup != string.Empty)
                {
                    lblB.Text = "Not Available";
                    isValid = false;
                    isBVolumeDup = true;
                    lblB.Visible = true;

                }
                else if (!isBVolumeDup && bVolume.Text != string.Empty)
                {
                    lblB.Text = "";
                    lblB.Visible = false;
                }
            }

            if (isValid)
            {
                getIdLuns.XIVId = CurrentEntity.Id;
                getIdLuns.PRCGName = aVolume.Text;
                getIdLuns.DRCGName = bVolume.Text;
                getIdLuns.CreatorId = 1;
                // getIdLuns.ReplicationId = CurrentEntity.ReplicationId;
                if (SaveRep.Text == "Update")
                {
                    getIdLuns.ReplicationId = CurrentEntity.ReplicationId;
                    Facade.AddInfraobjectCGName(getIdLuns);
                    testlist();
                }
                if (SaveRep.Text == "Save")
                {
                    SaveLuns.Add(getIdLuns);

                    if (SaveLuns != null)
                    {
                        ShowList();
                    }
                }
            }
        }

        private void ShowList()
        {
            //var SaveLuns = Facade.GetCGDetailsByBaseReplicationId(CurrentEntity.ReplicationId);

            lvManual.DataSource = SaveLuns;
            lvManual.DataBind();
        }

        private void ShowList123()
        {
            var SaveLuns = Facade.GetCGDetailsByBaseReplicationId(CurrentEntity.ReplicationId);

            lvManual.DataSource = SaveLuns;
            lvManual.DataBind();
        }

        protected void LvManualUpdating(object sender, ListViewUpdateEventArgs e)
        {
            if (Session["IBMXIVMirror"] != null)
            {
                CurrentEntity = (XIVConfiguration)Session["IBMXIVMirror"];
            }

            var lblUpdateId = (lvManual.Items[e.ItemIndex].FindControl("Id")) as Label;
            if (lblUpdateId != null)
            {
                lunsnew.Id = Convert.ToInt32(lblUpdateId.Text);
            }
            var txtAvolume = (lvManual.Items[e.ItemIndex].FindControl("PRName")) as TextBox;

            if (txtAvolume != null && txtAvolume.Text != "")
            {
                lunsnew.PRCGName = txtAvolume.Text.Trim();
            }
            var txtBvolume = (lvManual.Items[e.ItemIndex].FindControl("DRName")) as TextBox;
            if (txtBvolume != null && txtBvolume.Text != "")
            {
                lunsnew.DRCGName = txtBvolume.Text.Trim();
            }

            lunsnew.IsActive = 1;
            if (SaveRep.Text == "Save")
            {
                SaveLuns.Insert(e.ItemIndex, lunsnew);
                SaveLuns.RemoveAt(e.ItemIndex + 1);
                lvManual.EditIndex = -1;
                ShowList();
            }
            else
            {
                lunsnew.XIVId = CurrentEntity.Id;
                Facade.UpdateInfraobjectCGName(lunsnew);
                lvManual.EditIndex = -1;
            }
        }

        protected void LvManualDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lblDeleteId = (lvManual.Items[e.ItemIndex].FindControl("Id")) as Label;
            if (lblDeleteId != null)
            {
                int id = lblDeleteId.Text.ToInteger();
                if (SaveRep.Text == "Save")
                {
                    SaveLuns.RemoveAt(e.ItemIndex);
                    ShowList();
                }
                else
                {
                    Facade.DeleteGlobalMirrorLunsById(id);
                }
            }
        }

        protected void LvManualEditing(object sender, ListViewEditEventArgs e)
        {
            if (SaveRep.Text == "Save")
            {
                lvManual.EditIndex = e.NewEditIndex;
                lvManual.DataSource = SaveLuns;
                lvManual.DataBind();
            }
            else
            {
                lvManual.EditIndex = e.NewEditIndex;

                CurrentEntity = (XIVConfiguration)Session["IBMXIVMirror"];

                if (CurrentEntity != null)
                {
                    IList<InfraobjectCGDetails> lunses = Facade.GetCGDetailsByXIVId(CurrentEntity.Id);

                    if (lunses != null)
                    {
                        lvManual.DataSource = lunses;
                        lvManual.DataBind();
                    }
                }
            }
        }

        protected void LvManualCanceling(object sender, ListViewCancelEventArgs e)
        {
            lvManual.EditIndex = -1;
            if (SaveRep.Text == "Save")
            {
                ShowList();
            }
            else
            {
                if (CurrentEntity != null)
                {
                    IList<InfraobjectCGDetails> lunses = Facade.GetCGDetailsByXIVId(CurrentEntity.Id);

                    if (lunses != null)
                    {
                        lvManual.DataSource = lunses;
                        lvManual.DataBind();
                    }
                }
            }
        }

        protected void testlist()
        {
            CurrentEntity = (XIVConfiguration)Session["IBMXIVMirror"];

            if (CurrentEntity != null)
            {
                IList<InfraobjectCGDetails> lunses = Facade.GetCGDetailsByBaseReplicationId(CurrentEntity.ReplicationId);

                if (Session["PreviousItem"] == null)
                {
                    Session["PreviousItem"] = lunses;
                }

                if (lunses != null)
                {
                    lvManual.DataSource = lunses;
                    lvManual.DataBind();
                }
            }
        }

    }
}