﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IUserInfraObjectDataAccess
    {
        UserInfraObject Add(UserInfraObject userInfraObject);

        IList<UserInfraObject> GetByUserId(int id);

        bool DeleteByUserId(UserInfraObject userInfraObject);

        bool DeleteByUserId1(UserInfraObject userInfraObject, int id);

        UserInfraObject _GetWrkInfraAccess(int wrkflwId, int userid);

    }
}