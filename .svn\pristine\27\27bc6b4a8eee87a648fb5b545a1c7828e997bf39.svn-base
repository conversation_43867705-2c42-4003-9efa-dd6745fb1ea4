﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
    internal sealed class InfraScheWFBuilder : IEntityBuilder<InfrascheduleWfdeatils>
    {

        IList<InfrascheduleWfdeatils> IEntityBuilder<InfrascheduleWfdeatils>.BuildEntities(
          IDataReader reader)
        {
            var SchedularLogs = new List<InfrascheduleWfdeatils>();

            while (reader.Read())
            {
                SchedularLogs.Add(((IEntityBuilder<InfrascheduleWfdeatils>)this).BuildEntity(reader,
                    new InfrascheduleWfdeatils()));
            }

            return (SchedularLogs.Count > 0) ? SchedularLogs : null;
        }


        InfrascheduleWfdeatils IEntityBuilder<InfrascheduleWfdeatils>.BuildEntity(IDataReader reader,
     InfrascheduleWfdeatils SchedularLogs)
        {

            SchedularLogs.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);

            SchedularLogs.InfraObjectName = Convert.IsDBNull(reader["InfraObjectName"])
                ? string.Empty
                : Convert.ToString(reader["InfraObjectName"]);
            SchedularLogs.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            SchedularLogs.WorkflowName = Convert.IsDBNull(reader["WorkflowName"])
              ? string.Empty
              : Convert.ToString(reader["WorkflowName"]);
            SchedularLogs.WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]);
            SchedularLogs.CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"])
            ? string.Empty
            : Convert.ToString(reader["CurrentActionName"]);
            SchedularLogs.CurrentActionId = Convert.IsDBNull(reader["CurrentActionId"]) ? 0 : Convert.ToInt32(reader["CurrentActionId"]);
            SchedularLogs.ScheduleId = Convert.IsDBNull(reader["ScheduleId"]) ? 0 : Convert.ToInt32(reader["ScheduleId"]);

            SchedularLogs.ScheduleType = Convert.IsDBNull(reader["ScheduleType"])
          ? string.Empty
          : Convert.ToString(reader["ScheduleType"]);

            SchedularLogs.Status = Convert.IsDBNull(reader["Status"])
       ? string.Empty
       : Convert.ToString(reader["Status"]);

            SchedularLogs.Type = Convert.IsDBNull(reader["Type"])
   ? string.Empty
   : Convert.ToString(reader["Type"]);


            SchedularLogs.StartTime = Convert.IsDBNull(reader["StartTime"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["StartTime"]);
            SchedularLogs.EndTime = Convert.IsDBNull(reader["EndTime"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["EndTime"]);
            if (SchedularLogs.StartTime != null && SchedularLogs.EndTime != null)
            {
                TimeSpan tm = ((SchedularLogs.EndTime) - (SchedularLogs.StartTime));
                //   string ss = Convert.ToString((SchedularLogs.EndTime) - (SchedularLogs.StartTime));
                SchedularLogs.RTO = tm.ToString();
                //  SchedularLogs.RTO = ss;
            }
            else
            {
                SchedularLogs.RTO = null;
            }

            SchedularLogs.Message = Convert.IsDBNull(reader["Message"])
                ? string.Empty
                : Convert.ToString(reader["Message"]);

            return SchedularLogs;
        }

    }
}
