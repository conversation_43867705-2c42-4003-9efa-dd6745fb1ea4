﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IMongoDBDMonitorStatusDataAccess
    {
        IList<MongoDBDMonitorStatus> GetMongoDBReplicationByInfraObjId(int infraId);

        IList<MongoDBDMonitorStatus> GetMongoDBReplicationByDate(int infraObjectId, string startdate, string enddate);

        IList<MongoDBDMonitorStatus> GetMongoDBReplication_Hour_ByInfraId(int infraObjectId);

        MongoDBDMonitorStatus GetMongoDBReplicationBy_InfraObjId(int infraObjectId);
    }
}
