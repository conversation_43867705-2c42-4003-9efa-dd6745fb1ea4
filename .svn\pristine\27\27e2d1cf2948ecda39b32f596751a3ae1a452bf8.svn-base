﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;


namespace CP.DataAccess
{
    internal sealed class AnalyticsBuilder : IEntityBuilder<Analytics>
    {
        IList<Analytics> IEntityBuilder<Analytics>.BuildEntities(IDataReader reader)
        {
            var analytic = new List<Analytics>();

            while (reader.Read())
            {
                analytic.Add(((IEntityBuilder<Analytics>)this).BuildEntity(reader, new Analytics()));
            }

            return (analytic.Count > 0) ? analytic : null;
        }

        Analytics IEntityBuilder<Analytics>.BuildEntity(IDataReader reader, Analytics analytic)
        {
            analytic.ActionName = Convert.IsDBNull(reader["ACTIONNAME"]) ? string.Empty : Convert.ToString(reader["ACTIONNAME"]);
            analytic.TotalActions = Convert.IsDBNull(reader["TotalActions"]) ? 0 : Convert.ToInt32(reader["TotalActions"]);
            analytic.Completedoutofrto = Convert.IsDBNull(reader["Completedoutofrto"]) ? 0 : Convert.ToInt32(reader["Completedoutofrto"]);
            analytic.CompletedWithinRTO = Convert.IsDBNull(reader["CompletedWithinRTO"]) ? 0 : Convert.ToInt32(reader["CompletedWithinRTO"]);
            analytic.CompletedwithinrtoPER = Convert.IsDBNull(reader["CompletedwithinrtoPER"]) ? 0 : Convert.ToInt32(reader["CompletedwithinrtoPER"]);
            analytic.CompletedoutofrtoPER = Convert.IsDBNull(reader["CompletedoutofrtoPER"]) ? 0 : Convert.ToInt32(reader["CompletedoutofrtoPER"]);

            return analytic;
        }
    }
}
