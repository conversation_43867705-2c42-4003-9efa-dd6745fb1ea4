﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess.AppDepGroupNode
{
    internal sealed class AppDependencyGroupNodesBuilder : IEntityBuilder<AppDepGroupNodes>
    {
        IList<AppDepGroupNodes> IEntityBuilder<AppDepGroupNodes>.BuildEntities(IDataReader reader)
        {
            var appDepGroupNodes = new List<AppDepGroupNodes>();

            while (reader.Read())
            {
                appDepGroupNodes.Add(((IEntityBuilder<AppDepGroupNodes>)this).BuildEntity(reader,
                    new AppDepGroupNodes()));
            }

            return (appDepGroupNodes.Count > 0) ? appDepGroupNodes : null;
        }

        AppDepGroupNodes IEntityBuilder<AppDepGroupNodes>.BuildEntity(IDataReader reader,
           AppDepGroupNodes appDepGroupNodes)
        {

            appDepGroupNodes.ParentGroupNode = Convert.IsDBNull(reader["ParentGroupNode"]) ? string.Empty : Convert.ToString(reader["ParentGroupNode"]);

            appDepGroupNodes.DependentGroupNodes = Convert.IsDBNull(reader["DependentGroupNodes"]) ? string.Empty : Convert.ToString(reader["DependentGroupNodes"]);

            appDepGroupNodes.GroupName = Convert.IsDBNull(reader["GroupName"]) ? string.Empty : Convert.ToString(reader["GroupName"]);

            appDepGroupNodes.GroupDescription = Convert.IsDBNull(reader["GroupDescription"]) ? string.Empty : Convert.ToString(reader["GroupDescription"]);

            appDepGroupNodes.DepProfileName = Convert.IsDBNull(reader["DepProfileName"]) ? string.Empty : Convert.ToString(reader["DepProfileName"]);

            return appDepGroupNodes;
        }
    }
}
