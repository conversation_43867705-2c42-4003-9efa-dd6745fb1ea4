﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="InfraObjectDetails.ascx.cs" Inherits="CP.UI.Controls.InfraObjectDetails" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<link href="../App_Themes/ReportTheme/Report.css" rel="stylesheet" type="text/css" />
<link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
<script src="../Script/chosen.jquery.js"></script>
<style type="text/css">
    .rowStyleHeader.bold, .rowStyleHeader.bold {
        font-size: 9pt !important;
    }

    .chosen-select + .chosen-container {
        width: 48.5% !important;
        opacity: 1 !important;
    }
</style>
<script>
    $(document).ready(function () {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    });
    function pageLoad() {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    }
</script>
<%--<asp:UpdatePanel ID="UpdatePanel1" runat="server">
    <ContentTemplate>--%>
<div class="form-group">
    <label class="col-md-3 control-label">
        CAN ID <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlCanId" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlBussService_SelectedIndexChanged"></asp:DropDownList>
    </div>
</div>




<asp:Panel ID="Pan1" CssClass="widget widget-heading-simple widget-body-white" runat="server" Visible="false">
    <hr />
    <div class="widget-head">
        <div class="col-md-4">
            <h4>InfraObject Details Report by CANID</h4>
        </div>
        <div class="col-md-8 text-right">

            <asp:Button ID="btnPdf" ValidationGroup="vlGroupSite" CssClass="btn btn-primary" Width="20%" runat="server" Text="Export To Pdf" OnClick="btnPdf_Click" />
            <asp:Button ID="btnExcel" CssClass="btn btn-primary" Width="20%" runat="server" Text="Export To Excel" OnClick="btnExcel_Click" />
        </div>
    </div>
    <hr />
    <div class="widget-body">
        <div style="overflow: auto; height: 500px;">
            <asp:Table ID="tbl" CssClass="dtable" Style="font-size: 10.5px; width: 100%" runat="server">
            </asp:Table>
        </div>
        <hr />
        <div class="row">
            <%--<div class="col-md-4 ">NA : Not Available </div>--%>
            <div class="col-md-4 text-success">
                <asp:Label ID="lblDatalagG" runat="server"></asp:Label>
            </div>
            <div class="col-md-4 text-danger">
                <asp:Label ID="lblDatalagL" runat="server"></asp:Label>
            </div>
        </div>

        <hr class="separator" />
    </div>
</asp:Panel>

<hr />
<div class="form-group">
    <div id="divlable" class="col-xs-6" visible="false">
        <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false"></asp:Label>
        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
    </div>
    <div class="col-xs-6">
        <asp:Button ID="btnview" runat="server" CssClass="btn btn-primary" Style="margin-left: 5px" ValidationGroup="vlGroupSite" Width="20%" OnClick="btnview_Click"
            Text="View" />
    </div>
</div>
<%--</ContentTemplate>
</asp:UpdatePanel>--%>

<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div id="imgLoading" class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>
