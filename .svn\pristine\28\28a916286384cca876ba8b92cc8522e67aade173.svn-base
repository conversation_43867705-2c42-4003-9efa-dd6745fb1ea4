﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BCMSDefault.Master" AutoEventWireup="true" CodeBehind="WorkflowApprovalProcess.aspx.cs" Inherits="CP.UI.Admin.WorkflowApprovalProcess" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<%--<%@ Register Assembly="Telerik.Web.UI" Namespace="Telerik.Web.UI" TagPrefix="Tk2" %>--%>
<%--<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>--%>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <link href="../App_Themes/CPTheme/jquery.simple-dtpicker.css" type="text/css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.timepicker.css" type="text/css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/radstyle.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <script src="../Script/jquery.min.js"></script>
    <script src="../Script/jquery-migrate.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/jquery.cookie.js"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script src="../Script/jquery.combobox.js" type="text/javascript"></script>
    <%--<script src="../Script/UserConfig.js" type="text/javascript"></script>--%>
    <%--<script src="../Script/MaskedPassword.js"></script>--%>
    <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>


    <style type="text/css">
        .incidentTimeliner {
            margin-top: 10px;
        }

            .incidentTimeliner li {
                margin-bottom: 25px;
                position: relative;
                list-style: none;
            }

                .incidentTimeliner li::before {
                    content: "";
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background-color: #808080;
                    top: 5px;
                    left: -15px;
                    border-radius: 6px;
                }

                .incidentTimeliner li span:first-child {
                    font-size: 12px;
                }

                .incidentTimeliner li span:last-child {
                    opacity: 0.8;
                    font-size: 10px;
                }

                .incidentTimeliner li::after {
                    content: "";
                    position: absolute;
                    width: 2px;
                    height: 65px;
                    background-color: #808080;
                    top: 6px;
                    left: -12px;
                }

                .incidentTimeliner li:last-child::after {
                    height: 0px;
                }

        .lblbx {
            width: 95%;
            display: inline-block;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
        }

        .mailbody {
            width: 99%;
        }



        .approver {
            font-size: 14px;
            vertical-align: sub;
        }

        .SetTime.approver {
            margin-top: 5px;
        }

        .running {
            background-image: url('../Images/ApprovalProcessIcon/running.png');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }

        .Approval-compleated {
            background-image: url('../Images/ApprovalProcessIcon/approved.png');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }


        .pending {
            background-image: url('../Images/icons/alert.gif');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }

        .rejected_icon {
            background-image: url('../Images/ApprovalProcessIcon/rejected.png');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }

        .expired {
            background-image: url('../Images/icons/cross-circle_disable.png');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }

        .verify_icon {
            background-image: url('../Images/ApprovalProcessIcon/verified-18.png');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }

        .progress_icon {
            background-image: url('../Images/ApprovalProcessIcon/Loading_18.gif');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }

        .review_icon {
            background-image: url('../Images/icon-blue/view_blue.png');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }

        .error_icon {
            background-image: url('../Images/ApprovalProcessIcon/error.png');
            background-repeat: no-repeat;
            height: 18px;
            display: inline-block;
            width: 18px;
            background-size: 18px 18px;
        }



        .rcTable.rcSingle {
            width: 100% !important;
        }

        .ajax__calendar {
            z-index: 1234 !important;
        }

        .modal-body .vert-form .ajax__calendar {
            /*z-index: 1234 !important;*/
            top: auto !important;
            bottom: 30px !important;
        }

        .rsAdvDatePicker .RadInput_Metro {
            width: 160px !important;
        }

        .rsHeaderTimeline, .rsHeaderAgenda {
            display: none !important;
        }

        .RadPicker_Default .rcCalPopup, .RadPicker_Default a.rcCalPopup:hover {
            background-image: url(../Images/calendar-gray.png) !important;
            background-position: 5px 2px !important;
        }


        .RadPicker_Default .rcTimePopup {
            background-image: url(../Images/clock_new.png) !important;
            background-position: 0 0px !important;
        }

        a.rsToday {
            text-transform: uppercase !important;
        }

        .rsAllDayHeader {
            text-transform: uppercase !important;
        }

        .classapproversname {
            min-width: 346px;
            display: inline-block;
            min-height: 32px;
        }

        #ctl00_cphBody_txtSearch {
            width: 62%;
        }


        .mCSB_container table:first-child {
            margin-bottom: 0 !important;
        }


        .datetimefield {
            left: 27% !important;
            position: absolute !important;
        }

        .RadTreeView_Outlook .rtChecked, .RadTreeView_Outlook .rtUnchecked, .RadTreeView_Outlook .rtIndeterminate {
            background-image: url(../Images/custom-chkbox-rdbtn-small-new.png) !important;
        }

        .RadTreeView .rtUnchecked {
            background-position: -15px -12px !important;
        }

        .RadTreeView .rtChecked {
            background-position: 1px 0px !important;
        }

        .RadTreeView .rtIndeterminate {
            background-position: -15px 0px !important;
        }

        .RadTreeView_Outlook {
            font: normal 13px/16px Segoe UI !important;
        }

        .combobox {
            display: block !important;
        }

        div#ctl00_cphBody_dvgrp2, div#ctl00_cphBody_domainUserDiv {
            display: inline-block;
        }

        .setwidth .btn-group .dropdown-menu {
            min-width: 95%;
            right: 19px !important;
        }
    </style>


    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/DatetimePickerCss/jquery.datetimepicker.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>



</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
        <asp:HiddenField ID="hdtokenKey" runat="server" />
        <asp:HiddenField ID="hdnopentab" runat="server" />

        <h3>
            <img src="../Images/email-icon-notfication-gray.png" class="vertical-baseline">
            Approval Process</h3>
        <asp:Panel ID="pnlnotifier" runat="server" Visible="false"></asp:Panel>
        <div class="widget widget-heading-simple widget-body-white">
            <div class="widget-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-inline">
                            <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Approval Id / Name"></asp:TextBox>
                            <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" OnClick="BtnApprovalProcessSearchClick" />
                            <span style="margin-left: 15px !important;">
                                <asp:DropDownList runat="server" ID="ddlfilter" CssClass="selectpicker" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlfilter_SelectedIndexChanged">
                                    <asp:ListItem Value="All"></asp:ListItem>
                                    <asp:ListItem Value="Approved"></asp:ListItem>
                                    <asp:ListItem Value="Rejected"></asp:ListItem>
                                    <asp:ListItem Value="Pending"></asp:ListItem>
                                </asp:DropDownList>
                            </span>

                        </div>
                    </div>

                    <div class="col-md-6 text-right">
                        <asp:Button ID="btnNewApproval" class="btn btn-primary plus_icon padding5-10" UseSubmitBehavior="false" runat="server" Text="Create New Approval" CausesValidation="false" OnClick="btnNewApproval_Click" />
                    </div>
                </div>
            </div>
        </div>



        <div class="col-md-12 form-horizontal uniformjs padding-none">
            <asp:UpdatePanel ID="Updatepanel1" runat="server" UpdateMode="Conditional">

                <ContentTemplate>
                    <asp:Panel ID="wfApprovalProcessMsg" class="PanelNotificationBox PanelSuccess AutoHide" runat="server" Visible="false">
                        <asp:Label ID="wfapLabel" Visible="false" runat="server"> </asp:Label>

                        <span class="close">×</span>
                    </asp:Panel>
                </ContentTemplate>
                <Triggers>
                    <asp:AsyncPostBackTrigger ControlID="btnsave" EventName="Click" />
                    <asp:AsyncPostBackTrigger ControlID="btnOKReject" EventName="Click" />
                    <asp:AsyncPostBackTrigger ControlID="btnOK_approval" EventName="Click" />
                </Triggers>
            </asp:UpdatePanel>


            <asp:UpdatePanel ID="Updatepanel2" runat="server" UpdateMode="Always">

                <ContentTemplate>

                    <%--<asp:Timer runat="server" ID="UpdateTimer" Interval="10000" OnTick="UpdateTimer_Tick" Enabled="false" />--%>
                    <asp:HiddenField ID="hdnTimer" runat="server" Value="1" />
                    <asp:HiddenField ID="activepnl" runat="server" Value="1" />
                    <asp:HiddenField ID="hdnTimerbtn" runat="server" Value="1" />

                    <div class="widget widget-heading-simple widget-body-white margin-none">
                        <div class="widget-body" style="height: 500px">

                            <asp:ListView ID="lvWFProfileApprovalProcess" runat="server" OnPreRender="lvWFProfileApprovalProcess_PreRender" OnItemDataBound="lvWFProfileApprovalProcess_ItemDataBound" OnItemCommand="lvWFProfileApprovalProcess_ItemCommand">
                                <LayoutTemplate>
                                    <table class="table tableFixed" style="margin-bottom: 0px; width: 100%;">
                                        <thead>
                                            <tr>
                                                <th style="width: 7%;" class="pad_L10">Approval Id
                                                </th>
                                                <th style="width: 7%;">Profile/ Workflow
                                                </th>
                                                <th style="width: 7%;">Action
                                                </th>
                                                <th style="width: 14%;">Name
                                                </th>
                                                <th style="width: 12%;">Business Service
                                                </th>
                                                <th style="width: 12%;">Business Function
                                                </th>
                                                <th style="width: 13%;">Start Time
                                                </th>
                                                <th style="width: 13%;">End Time
                                                </th>
                                                <th style="width: 0%; display: none;">State
                                                </th>
                                                <th style="width: 8%;">Approval Status
                                                </th>
                                                <th style="width: 5%; text-align: center">Action
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                    <div class="notifyscroll" style="max-height: 352px;">
                                        <table class="table table-hover fixed-table toptabpart margin_b0">
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                            </tbody>
                                        </table>
                                    </div>
                                </LayoutTemplate>
                                <EmptyDataTemplate>
                                    <div class="">
                                        <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true" CssClass="no-data-found"></asp:Label>
                                    </div>
                                </EmptyDataTemplate>

                                <ItemTemplate>
                                    <tr>
                                        <td colspan="9" style="padding: 0px 0; border-top-width: 0px !important;">
                                            <table class="tableinside table tableFixed" style="table-layout: fixed; width: 100%; margin-bottom: 0px;">
                                                <%----%>
                                                <tr>
                                                    <td style="width: 7%;" class="th table-check-cell tdword-wrap">
                                                        <asp:Label ID="lblApprovalProcessNumber" runat="server" Text='<%# Eval("ApprovalProcessNumber") %>' ToolTip='<%# Eval("ApprovalProcessNumber") %>' />
                                                        <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                    </td>
                                                    <td class="truncate th table-check-cell tdword-wrap" style="width: 7%;">
                                                        <asp:Label ID="lbl_PROFILE_OR_WORKFLOW" runat="server" CssClass="pad_L10" Text='<%# Eval("PROFILE_OR_WORKFLOW") %>' ToolTip='<%# Eval("PROFILE_OR_WORKFLOW") %>' />
                                                    </td>
                                                    <td style="width: 7%;" class="truncate th table-check-cell tdword-wrap">
                                                        <asp:Label ID="lbl_EXECUTE_OR_MODIFY" runat="server" Text='<%# Eval("EXECUTE_OR_MODIFY") %>' ToolTip='<%# Eval("EXECUTE_OR_MODIFY") %>' />
                                                    </td>
                                                    <td class="truncate th table-check-cell " style="width: 14%; word-wrap: break-word; !important">
                                                        <asp:Label ID="lblWorkflowNmame" runat="server" CssClass="pad_L10" Text='<%# Eval("WorkflowName") %>' ToolTip='<%# Eval("WorkflowName") %>' />
                                                        <asp:Label ID="lblProfileName" runat="server" CssClass="pad_L10" Text='<%# Eval("ProfileName") %>' ToolTip='<%# Eval("ProfileName") %>' />
                                                        <asp:Label ID="lblProfileId" runat="server" CssClass="pad_L10" Text='<%# Eval("ProfileId") %>' Visible="false" />
                                                    </td>
                                                    <td style="width: 12%;" class="truncate th table-check-cell ">
                                                        <asp:Label ID="lblBuisnessService" runat="server" Text="" ToolTip="" />
                                                    </td>
                                                    <td class="truncate table-check-cell " style="width: 12%;">
                                                        <asp:Label ID="lblBusinessFunction" runat="server" Text="" ToolTip="" />
                                                    </td>
                                                    <td style="width: 12%;" class="truncate  th table-check-cell tdword-wrap">
                                                        <asp:Label ID="lblTIMESTAMP" runat="server" CssClass="pad_L10" Text='<%# Eval("ProfileExecutionStartTime") %>' ToolTip='<%# Eval("ProfileExecutionStartTime") %>' />
                                                    </td>
                                                    <td style="width: 12%;" class="truncate th table-check-cell tdword-wrap">
                                                        <asp:Label ID="lblProfileExecutionEndTime" CssClass="pad_L10" runat="server" Text='<%# Eval("ProfileExecutionEndTime") %>' ToolTip='<%# Eval("ProfileExecutionEndTime") %>' />
                                                    </td>
                                                    <td style="width: 8%;" class="truncate th table-check-cell tdword-wrap">
                                                        <asp:Label ID="lblApprovalStatusIcon" runat="server" CssClass="error_icon" Style="vertical-align: sub;"></asp:Label>
                                                        <asp:Label ID="lblApprovalStatus" runat="server" Text='<%# Eval("ApprovalStatus") %>' ToolTip='<%# Eval("ApprovalStatus") %>' />
                                                    </td>

                                                    <td style="width: 0%; display: none;" class="truncate th table-check-cell tdword-wrap">
                                                        <asp:Label ID="lblState" runat="server" Text='<%# Eval("State") %>' />
                                                    </td>


                                                    <td style="width: 5%; text-align: center" class="tdword-wrap">
                                                        <asp:Label runat="server" ID="btnView" CssClass="imgbtn" Style="vertical-align: baseline;" data-id='<%# "DT" + Eval("ApprovalProcessNumber") %>'></asp:Label>
                                                        <%-- <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Remove" ToolTip="Remove" ImageUrl="../Images/icons/cross-circle.png" /> --%>
                                                        <asp:ImageButton ID="imgbtnCPTracker" runat="server" CommandArgument='<%# Eval("ApprovalProcessNumber") %>' CommandName="Tracker" ImageUrl="~/Images/ApprovalProcessIcon/history.png" ToolTip="Request Overview" />

                                                    </td>

                                                </tr>

                                                <tr class="timelinediv">
                                                    <td colspan="9" style="padding: 5px 0px 0px !important;">
                                                        <asp:ListView ID="lvWFProfileApprovalLevelProcess" OnItemCommand="lvWFProfileApprovalLevelProcess_ItemCommand" OnItemDataBound="lvWFProfileApprovalLevelProcess_ItemDataBound" runat="server" Visible="true">
                                                            <LayoutTemplate>
                                                                <table class="table table-hover fixed-table nestedtable subtable" style="width: 95%; table-layout: fixed;">
                                                                    <thead>
                                                                        <tr>
                                                                            <th style="width: 10%;">Requester
                                                                            </th>
                                                                            <th style="width: 23%;">Approver
                                                                            </th>
                                                                            <th style="width: 17%;">Status
                                                                            </th>
                                                                            <th style="width: 13%;">Request Sent Time
                                                                            </th>
                                                                            <th style="width: 13%;">Approved/Rejected Time
                                                                            </th>
                                                                            <th style="width: 12%;">Approved/ Rejected By
                                                                            </th>
                                                                            <th style="width: 13%; text-align: center;">Action
                                                                            </th>

                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                    </tbody>
                                                                </table>
                                                            </LayoutTemplate>
                                                            <ItemTemplate>
                                                                <tr>
                                                                    <td style="width: 10%; word-wrap: break-word; !important">
                                                                        <asp:Label ID="lblApprovalLevel" runat="server" Text='<%# Eval("ApprovalLevel") %>' Visible="false" />
                                                                        <asp:Label ID="lbl_RequesterName" runat="server" Text='<%# Eval("CreatedByName") %>' />
                                                                        <asp:Label ID="lblRequesterId" runat="server" Text='<%# Eval("CreatorId") %>' Visible="false" />
                                                                        <asp:Label ID="lblId1" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                        <asp:Label ID="lblApprovalProcessId" runat="server" Text='<%# Eval("ApprovalProcessId") %>' Visible="false" />
                                                                    </td>
                                                                    <td style="width: 23%; word-wrap: break-word; !important">
                                                                        <asp:Label ID="Label2" runat="server" ToolTip='<%# Eval("Approver") %>' Text='<%# Eval("Approver") %>' />
                                                                    </td>
                                                                    <td style="width: 17%;">
                                                                        <asp:Label ID="Label3" runat="server" ToolTip='<%# Eval("ApprovalState") %>' Text='<%# Eval("ApprovalState") %>' />
                                                                    </td>
                                                                    <td style="width: 13%;">
                                                                        <asp:Label ID="Label6" runat="server" ToolTip='<%# Eval("CreateDate") %>' Text='<%# Eval("CreateDate") %>' />
                                                                    </td>
                                                                    <td style="width: 13%;">
                                                                        <asp:Label ID="Label9" runat="server" ToolTip='<%# (string)Eval("ApprovalState") == "Pending" ? "--- NA ---" : Eval("UpdateDate") %>' Text='<%# (string)Eval("ApprovalState") == "Pending" ? "--- NA ---" : Eval("UpdateDate") %>' />
                                                                    </td>
                                                                    <td style="width: 12%;">
                                                                        <asp:Label ID="Label8" runat="server" ToolTip='<%# Eval("ApprovalBy") %>' Text='<%# string.IsNullOrEmpty((string)Eval("ApprovalBy")) ? "--- NA ---" : Eval("ApprovalBy") %>' />
                                                                    </td>
                                                                    <td class="tdword-wrap" style="width: 13%; text-align: center">
                                                                        <asp:ImageButton ID="btnReminder" runat="server" ImageUrl="../Images/ApprovalProcessIcon/email-reply.png" ToolTip="Approval Reminder" CommandName="EmailReminder" CommandArgument='<%# Eval("Id") %>' Visible='<%#(string)Eval("ApprovalState") =="Pending" %>' />
                                                                        <%--<asp:ImageButton ID="btnCompleted" runat="server" ImageUrl="../Images/ApprovalProcessIcon/email-blue.png" ToolTip="Approval Mail Details" CommandName="EmailApproval" CommandArgument='<%# Eval("Id") %>' Visible='<%#(string)Eval("ApprovalState") !="Pending" %>' />--%>
                                                                        <asp:ImageButton ID="btnApprove" runat="server" ImageUrl="../Images/ApprovalProcessIcon/thumb-up-20.png" ToolTip="Approve" CommandName="Approve" CommandArgument='<%# Eval("Id") %>' Visible='<%#(string)Eval("ApprovalState") =="Pending" %>' />
                                                                        <asp:ImageButton ID="btnReject" runat="server" ImageUrl="../Images/ApprovalProcessIcon/thumb-down-20.png" ToolTip="Reject" CommandName="Reject" CommandArgument='<%# Eval("Id") %>' Visible='<%#(string)Eval("ApprovalState") =="Pending" %>' />

                                                                        <asp:Label ID="Label15" runat="server" ToolTip='<%# Eval("UpdatorId") %>' Visible="false" Text='<%# Eval("UpdatorId") %>' />
                                                                        <asp:Label ID="lbl_blankVal" runat="server" Text="--"></asp:Label>

                                                                    </td>
                                                                </tr>

                                                            </ItemTemplate>
                                                        </asp:ListView>

                                                    </td>
                                                </tr>

                                                <tr class="timelinediv">
                                                    <td colspan="9">
                                                        <asp:ListView ID="ListView1" OnItemCommand="lvWFProfileApprovalLevelProcess_ItemCommand" OnItemDataBound="ListView1_ItemDataBound" runat="server" Visible="true">
                                                            <LayoutTemplate>
                                                                <table class="table table-hover fixed-table nestedtable subtable" style="width: 95%; table-layout: fixed;">
                                                                    <thead>
                                                                        <tr>
                                                                            <%--<th style="width: 10%;">Requester
                                                                            </th>--%>
                                                                            <%--<th style="width: 23%;">Approver
                                                                            </th>--%>
                                                                            <th style="width: 17%;">Workflow Status
                                                                            </th>
                                                                            <th style="width: 13%;">Request Sent Time
                                                                            </th>
                                                                            <th style="width: 13%;">Verified/Rejected Time
                                                                            </th>
                                                                            <th style="width: 12%;">Verified/Rejected By
                                                                            </th>
                                                                            <th style="width: 13%; text-align: center;">Action
                                                                            </th>

                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                    </tbody>
                                                                </table>
                                                            </LayoutTemplate>
                                                            <ItemTemplate>
                                                                <tr>
                                                                    <%--<td style="width: 10%; word-wrap: break-word; !important">
                                                                        <%--<asp:Label ID="lblApprovalLevel" runat="server" Text='<%# Eval("ApprovalLevel") %>' Visible="false" />--%>
                                                                    <%--<asp:Label ID="lbl_RequesterName" runat="server" Text='<%# Eval("CreatedByName") %>' />--%>
                                                                    <%--<asp:Label ID="lblRequesterId" runat="server" Text='<%# Eval("CreatorId") %>' Visible="false" />--%>
                                                                    <%--<asp:Label ID="lblId1" runat="server" Text='<%# Eval("Id") %>' Visible="false" />--%>
                                                                    <%--<asp:Label ID="lblApprovalProcessId" runat="server" Text='<%# Eval("ApprovalProcessId") %>' Visible="false" />
                                                                    </td>--%>
                                                                    <%--  <td style="width: 23%; word-wrap: break-word; !important">
                                                                        <asp:Label ID="Label2" runat="server" ToolTip='<%# Eval("Approver") %>' Text='<%# Eval("Approver") %>' />
                                                                    </td>--%>
                                                                    <td style="width: 17%;">
                                                                        <asp:Label ID="Label2" runat="server" Visible="false" ToolTip='<%# Eval("Approver") %>' Text='<%# Eval("Approver") %>' />
                                                                        <asp:Label ID="lbl_RequesterName" Visible="false" runat="server" Text='<%# Eval("CreatedByName") %>' />
                                                                        <asp:Label ID="lblApprovalProcessId" runat="server" Text='<%# Eval("ApprovalProcessId") %>' Visible="false" />

                                                                        <asp:Label ID="Label3" runat="server" ToolTip='<%# GetApprovalstate(Eval("UpdatorId")) %>' Text='<%# GetApprovalstate(Eval("UpdatorId")) %>' />
                                                                    </td>
                                                                    <td style="width: 13%;">
                                                                        <asp:Label ID="Label6" runat="server" ToolTip='<%# Eval("UpdateDate") %>' Text='<%# Eval("UpdateDate") %>' />
                                                                    </td>
                                                                    <td style="width: 13%;">
                                                                        <asp:Label ID="Label9" runat="server" ToolTip='<%# (int)Eval("UpdatorId") == 3  ? "--- NA ---" : Eval("Verifieddate") %>' Text='<%# (int)Eval("UpdatorId") ==  3 ? "--- NA ---" : Eval("Verifieddate") %>' />
                                                                    </td>
                                                                    <td style="width: 12%;">
                                                                        <asp:Label ID="Label8" runat="server" ToolTip='<%# Eval("verifiedby") %>' Text='<%# string.IsNullOrEmpty((string)Eval("verifiedby")) ? "--- NA ---" : Eval("verifiedby") %>' />
                                                                    </td>
                                                                    <td class="tdword-wrap" style="width: 13%; text-align: center;">

                                                                        <asp:ImageButton ID="btnverify" runat="server" style="display:none" ImageUrl="../Images/ApprovalProcessIcon/thumb-up-20.png" ToolTip="Verified" CommandName="Verify" CommandArgument='<%# Eval("Id") %>' Visible='<%#(string)Eval("ApprovalState") =="Disapproved" %>' />
                                                                        <asp:ImageButton ID="btnnotverified" runat="server" style="display:none" ImageUrl="../Images/ApprovalProcessIcon/thumb-down-20.png" ToolTip="Disapporved" CommandName="Disapprove" CommandArgument='<%# Eval("Id") %>' Visible='<%#(string)Eval("ApprovalState") =="Disapproved" %>' />
                                                                        <asp:Label ID="Label15" runat="server" ToolTip='<%# Eval("UpdatorId") %>' Visible="false" Text='<%# Eval("UpdatorId") %>' />
                                                                        <asp:Label ID="lbl_blankVal_1" runat="server" Text="--"></asp:Label>

                                                                    </td>
                                                                </tr>

                                                            </ItemTemplate>
                                                        </asp:ListView>

                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </ItemTemplate>
                            </asp:ListView>

                            <div class="form-group footerpager" style="margin: 0px;">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvWFProfileApprovalProcess">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                                <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                Out Of
                                                <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>

                            <div class="col-md-12 text-right">
                                <asp:Button runat="server" ID="btnloadmore" CssClass="btn btn-primary" Text="Load More" OnClick="btnloadmore_Click" />
                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvWFProfileApprovalProcess" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                            NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>

                    </div>


                </ContentTemplate>
                <Triggers>
                    <asp:AsyncPostBackTrigger ControlID="btnsave" EventName="Click" />
                    <asp:AsyncPostBackTrigger ControlID="btnback" EventName="Click" />
                    <asp:AsyncPostBackTrigger ControlID="btnSearch" EventName="Click" />
                    <asp:AsyncPostBackTrigger ControlID="btnOKReject" EventName="Click" />
                    <asp:AsyncPostBackTrigger ControlID="btnCancelReject" EventName="Click" />
                    <asp:AsyncPostBackTrigger ControlID="lvWFProfileApprovalProcess" EventName="ItemDataBound" />
                    <asp:AsyncPostBackTrigger ControlID="btnloadmore" EventName="Click" />
                </Triggers>
            </asp:UpdatePanel>



        </div>


        <asp:UpdatePanel ID="UP_pnlApproval" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <asp:HiddenField ID="hdn_statTime" runat="server" />
                <asp:HiddenField ID="hdn_endTime" runat="server" />
                <asp:Panel ID="pnlApproval" runat="server" Visible="false">
                    <div class="modal_bg"></div>
                    <div class="modal" style="display: block; display: block; background-color: #1e1e1ec4;">
                        <div class="modal-dialog" style="width: 840px;">
                            <div class="modal-content  widget-body-white">
                                <div class="modal-header">
                                    <h3 class="modal-title">
                                        <img src="../Images/icon_white/email-icon-notfication-white.png">
                                        Approval Process</h3>
                                    <asp:LinkButton ID="lnkClose" runat="server" ToolTip="Close window" OnClick="lnkClose_Click"
                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                                </div>
                                <div class="modal-body" style="max-height: 413px !important; overflow-x: scroll;">
                                    <div class="widget widget-heading-simple widget-body-white">

                                        <div class="widget-body">
                                            <div class="col-md-12 padding-none vert-form">

                                                <div class="form-group" style="display: none;">
                                                    <i class="Monitoring_Icon"></i>

                                                    <asp:HiddenField ID="HiddenField4" runat="server" />
                                                    <asp:Label ID="Label4" runat="server" Text="Approval ID"></asp:Label>
                                                    <%-- <span class="inactive">*</span>--%>

                                                    <asp:HiddenField ID="HiddenField5" runat="server" />
                                                    <asp:TextBox ID="txtApprovalId" runat="server" TabIndex="1" CssClass="form-control" CausesValidation="True" ReadOnly="true"></asp:TextBox>
                                                    <asp:Label ID="Label5" runat="server" CssClass="" Visible="False"></asp:Label>

                                                    <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ErrorMessage="Please Enter Profile Name" ControlToValidate="txtApprovalId"
                                                        Display="Dynamic" CssClass="error" SetFocusOnError="true" ValidationGroup="validApp"></asp:RequiredFieldValidator>--%>
                                                </div>

                                                <div class="form-group">
                                                    <i class="Monitoring_Icon"></i>
                                                    <asp:HiddenField ID="HiddenField8" runat="server" />
                                                    <asp:RadioButtonList ID="rbl_ForProfile_Workflow" RepeatDirection="Horizontal" runat="server" AutoPostBack="true" OnSelectedIndexChanged="rbl_ForProfile_Workflow_SelectedIndexChanged">
                                                        <asp:ListItem Text="Profile" Value="Profile"></asp:ListItem>
                                                        <asp:ListItem Text="Workflow" Value="Workflow"></asp:ListItem>
                                                    </asp:RadioButtonList>

                                                    <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" CssClass="error" ControlToValidate="ddl_ApprovalActionType" Display="Dynamic" InitialValue="0" ErrorMessage="Select Approval Action" ValidationGroup="validApp"></asp:RequiredFieldValidator>--%>
                                                </div>



                                                <div class="form-group" style="margin-top: 5px;" runat="server" id="div_profile">
                                                    <div class="col-md-3" style="padding-left: 0px !important;">
                                                        <i class="Monitoring_Icon"></i>
                                                        <asp:HiddenField ID="ApprovalId" runat="server" />
                                                        <asp:Label ID="lblIMAP" runat="server" Text="Profile Name"></asp:Label>
                                                        <%--  <span class="inactive">*</span>--%>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlProfileName" runat="server" TabIndex="6" CssClass="chosen-select" Width="347" AutoPostBack="true" data-style="btn-default" OnSelectedIndexChanged="ddlProfileName_SelectedIndexChanged">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="rfvddlUsecase" runat="server" CssClass="error" ControlToValidate="ddlProfileName" Display="Dynamic" InitialValue="00" ErrorMessage="Select Profile" ValidationGroup="validApp"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>


                                                <div class="form-group" style="margin-top: 5px;" runat="server" id="div_workflow">
                                                    <div class="col-md-3" style="padding-left: 0px !important;">
                                                        <i class="Monitoring_Icon"></i>
                                                        <asp:HiddenField ID="Selected_WorkflowId" runat="server" />
                                                        <asp:Label ID="Label13" runat="server" Text="Workflows"></asp:Label>
                                                        <%--<span class="inactive">*</span>--%>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <asp:TextBox ID="wfID" runat="server" Style="display: none !important;" AutoPostBack="true" OnTextChanged="wfID_TextChanged"></asp:TextBox>
                                                        <asp:DropDownList ID="ddl_WorkflowList" runat="server" Width="347" TabIndex="6" CssClass="chosen-select" AutoPostBack="false" data-style="btn-default">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator5" runat="server" CssClass="error" ControlToValidate="ddl_WorkflowList" Display="Dynamic" InitialValue="0" ErrorMessage="Select Workflow" ValidationGroup="validApp"></asp:RequiredFieldValidator>
                                                        
                                                    </div>
                                                </div>

                                                <div class="form-group" style="margin-top: 57px;">
                                                    <div class="col-md-3" style="padding-left: 0px !important;">
                                                        <i class="Monitoring_Icon"></i>
                                                        <asp:HiddenField ID="HiddenField7" runat="server" />
                                                        <asp:Label ID="Label11" runat="server" Text="Execute / Modify"></asp:Label>
                                                        <%-- <span class="inactive">*</span>--%>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddl_ApprovalActionType" runat="server" TabIndex="6" CssClass="chosen-single" Width="347" data-style="btn-default" AutoPostBack="true" onchange="ChangeActionType(this);" OnSelectedIndexChanged="ddl_ApprovalActionType_SelectedIndexChanged">
                                                            <asp:ListItem Text="-- Select Action --" Value="0" Selected="True"></asp:ListItem>
                                                            <asp:ListItem Text="Execution" Value="Execution"></asp:ListItem>
                                                            <asp:ListItem Text="Modification" Value="Modification"></asp:ListItem>
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" runat="server" CssClass="error" ControlToValidate="ddl_ApprovalActionType" Display="Dynamic" InitialValue="0" ErrorMessage="Select Approval Action" ValidationGroup="validApp"></asp:RequiredFieldValidator>

                                                    </div>
                                                </div>

                                                <div class="form-group" style="margin-top: 99px;">
                                                    <div class="col-md-3" style="padding-left: 0px !important;">
                                                        <asp:Label ID="Label14" runat="server" Text="Approver"></asp:Label>
                                                        <%--<span class="inactive">*</span>--%>
                                                        <asp:HiddenField ID="HDN_ApproversID" runat="server"></asp:HiddenField>
                                                    </div>
                                                    <div class="col-md-9">

                                                        <%--<asp:Label class="form-control classapproversname" ID="lbl_ApproversName" runat="server" Text=""></asp:Label>--%>
                                                        <%--<div id="dvapprovals" Visible="false" runat="server" class="notifycheckscroll" style="padding: 5px; border: 1px solid #ccc; width: 80%;">

                                                            <asp:UpdatePanel ID="UpdatePanel4" ClientIDMode="Static" runat="server" UpdateMode="Conditional">
                                                                <ContentTemplate>
                                                                    <telerik:RadTreeView ClientIDMode="Static" RenderMode="Lightweight" CausesValidation="false" ID="RadTreeView1"  runat="server" CheckBoxes="True" OnNodeCheck="RadTreeView1_NodeCheck"
                                                                        Skin="Outlook" TriStateCheckBoxes="true" CheckChildNodes="true" IsOptionElementsEnabled="True">
                                                                    </telerik:RadTreeView>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>                                                            
                                                        </div>--%>
                                                        <asp:Button ID="btn_selectApprover" ClientIDMode="Predictable" runat="server" CssClass="btn btn-primary" Enabled="false" Text="Select Approver" OnClick="btn_selectApprover_Click" />

                                                        <%-- <asp:Label ID="lbluser_selectmsg" runat="server" CssClass="Rerror" Visible="False" ForeColor="Red">Please Select User</asp:Label>--%>

                                                        <%--<div id="dvapprovals" runat="server" visible="false" style="position: absolute; top: 0px; width: 59%;">
                                                            <asp:TextBox ID="txtSearch" runat="server" autocomplete="off" class="form-control" Width="101%" onkeyup="SearchInfra(this,'#ctl00_cphBody_chkapprovallist');"
                                                                placeholder="Search User">
                                                            </asp:TextBox>
                                                            <asp:Panel ID="Panel_chkapprovallist" runat="server" Width="101%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1" Height="160px" BackColor="Cornsilk" ScrollBars="Vertical" TabIndex="6">
                                                                <div style="padding: 10px;">
                                                                    <span id="spnCount"></span>
                                                                    <asp:CheckBoxList ID="chkapprovallist" runat="server" AutoPostBack="true" OnSelectedIndexChanged="chkapprovallist_SelectedIndexChanged">
                                                                    </asp:CheckBoxList>
                                                                </div>
                                                            </asp:Panel>

                                                        </div>--%>
                                                    </div>
                                                </div>



                                                <div class="form-group" style="padding-top: 55px;">

                                                    <i class="Monitoring_Icon"></i>
                                                    <asp:HiddenField ID="HiddenField2" runat="server" />
                                                    <asp:Label ID="Label1" runat="server" Text="Start Time"></asp:Label>
                                                    <%-- <span class="inactive">*</span>--%>

                                                    <asp:HiddenField ID="HiddenField3" runat="server" />

                                                    <asp:TextBox ID="txtStartDate" runat="server" onkeydown="return false;" autocomplete="off" class="form-control" onchange="txtStartDate_changed()" Style="margin-left: 145px; width: 45% !important;"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="rfvStartDate" CssClass="error" runat="server" ErrorMessage="Select Date" ControlToValidate="txtStartDate"
                                                        ValidationGroup="validApp" Display="Dynamic"></asp:RequiredFieldValidator>

                                                </div>
                                                <div class="form-group" style="padding-top: 10px;">

                                                    <asp:HiddenField ID="HiddenField1" runat="server" />
                                                    <asp:Label ID="Label7" runat="server" Text="End Time"></asp:Label>
                                                    <%--<span class="inactive">*</span>--%>

                                                    <asp:HiddenField ID="HiddenField6" runat="server" />

                                                    <asp:TextBox ID="txtEndDate" runat="server" onkeydown="return false;" autocomplete="off" class="form-control" Style="margin-left: 149px; width: 45% !important;"></asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" CssClass="error" runat="server" ErrorMessage="Select Date" ControlToValidate="txtEndDate"
                                                        ValidationGroup="validApp" Display="Dynamic"></asp:RequiredFieldValidator>
                                                    <asp:Label runat="server" ID="lblduration" Text="Maximum time cannot be more than 6 hours" ForeColor="Red" CssClass="error" Visible="false"></asp:Label>
                                                    <asp:Label runat="server" ID="lblduration_error" Text="" ForeColor="Red" CssClass="error" Visible="true"></asp:Label>

                                                </div>

                                                <div class="form-group" style="padding-top: 10px;">
                                                    <asp:HiddenField ID="HiddenField9" runat="server" />
                                                    <asp:Label ID="Label12" runat="server" Text="Remark"></asp:Label>
                                                    <%-- <span class="inactive">*</span>--%>

                                                    <asp:HiddenField ID="HiddenField10" runat="server" />

                                                    <asp:TextBox ID="txtReasonforRequest" runat="server" autocomplete="off" class="form-control" MaxLength="100" Style="margin-left: 158px; width: 45% !important;">

                                                    </asp:TextBox>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator4" CssClass="error" runat="server" ErrorMessage="Please Enter Remark." ControlToValidate="txtReasonforRequest"
                                                        ValidationGroup="validApp" Display="Dynamic"></asp:RequiredFieldValidator>
                                                </div>

                                                <asp:Label ID="lblMsgStartEndDate" runat="server" Visible="false" CssClass="error"></asp:Label>

                                                <div id="ActiveTimeSlotListD" runat="server" visible="false">
                                                    <asp:Label ID="lblActiveTime" runat="server" Text="Active Time Slot" Visible="false"></asp:Label>
                                                    <asp:ListView ID="listActiveTimeSlot" runat="server">

                                                        <LayoutTemplate>
                                                            <table class="table tableFixed" style="margin-bottom: 0px; width: 100%; margin-top: 10px;">
                                                                <thead>
                                                                    <tr>
                                                                        <th class="pad_L10" style="width: 24%;">APPROVAL NUMBER
                                                                        </th>
                                                                        <th>START TIME
                                                                        </th>
                                                                        <th>END TIME
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                            <div class="notifyscroll" style="max-height: 320px;">
                                                                <table class="table table-hover fixed-table toptabpart margin_b0">
                                                                    <tbody>
                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </LayoutTemplate>
                                                        <ItemTemplate>
                                                            <tr>
                                                                <td colspan="7" style="padding: 0px 0; border-top-width: 0px !important;">
                                                                    <table class="tableinside table tableFixed" style="table-layout: fixed; width: 100%; margin-bottom: 0px;">
                                                                        <tr>

                                                                            <td style="width: 24%;">
                                                                                <asp:Label ID="lblApprovalProcessNumber1" runat="server" Text='<%# Eval("ApprovalProcessNumber") %>' />
                                                                                <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                            </td>
                                                                            <td>
                                                                                <asp:Label ID="lblProfileExeStartTime1" runat="server" Text='<%# Eval("ProfileExecutionStartTime") %>' />

                                                                            </td>
                                                                            <td>
                                                                                <asp:Label ID="lblProfileExeEndTime1" runat="server" Text='<%# Eval("ProfileExecutionEndTime") %>' />
                                                                            </td>

                                                                        </tr>
                                                                    </table>
                                                                </td>
                                                            </tr>
                                                        </ItemTemplate>

                                                    </asp:ListView>
                                                </div>

                                            </div>
                                        </div>


                                    </div>

                                </div>
                                <div class="modal-footer">
                                    <span style="float: left; display: inline-block;">Note : all fields are mandatory.</span>

                                    <div class="form-group" style="margin: 0px;">
                                        <asp:Button ID="btnsave" TabIndex="9" runat="server" Text="Save" CssClass="btn btn-primary btn_save"
                                            ValidationGroup="validApp" OnClick="btnsave_Click" />
                                        <asp:Button ID="btnBack" runat="server" TabIndk2ex="7" Text="Close" CssClass="btn btn-primary btn_back"
                                            CausesValidation="false" OnClick="btnBack_Click" />

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>
            </ContentTemplate>
            <Triggers>
                <asp:AsyncPostBackTrigger ControlID="btnNewApproval" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="btnsave" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="btnback" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="btn_selectApprover" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="txtStartDate" EventName="TextChanged" />
                <asp:AsyncPostBackTrigger ControlID="txtEndDate" EventName="TextChanged" />
                <asp:AsyncPostBackTrigger ControlID="ddl_ApprovalActionType" EventName="SelectedIndexChanged" />
                <asp:AsyncPostBackTrigger ControlID="ddlProfileName" EventName="SelectedIndexChanged" />
                <%--<asp:AsyncPostBackTrigger ControlID="ddl_WorkflowList" EventName="SelectedIndexChanged" />--%>
                <%--<asp:AsyncPostBackTrigger ControlID="chkapprovallist" EventName="SelectedIndexChanged" />--%>
                <asp:AsyncPostBackTrigger ControlID="rbl_ForProfile_Workflow" EventName="SelectedIndexChanged" />
                <%--   <asp:AsyncPostBackTrigger ControlID="RadTreeView1" EventName="NodeCheck" />--%>
                <asp:AsyncPostBackTrigger ControlID="wfID" EventName="TextChanged" />
            </Triggers>
        </asp:UpdatePanel>

        <asp:UpdatePanel ID="UpdatePanel5" ClientIDMode="Static" ChildrenAsTriggers="true" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

                <telerik:RadAjaxManager ID="RadAjaxManager1" runat="server">
                </telerik:RadAjaxManager>

                <asp:Panel ID="pnl_radtreeview" runat="server" Visible="false">

                    <div class="modal_bg"></div>
                    <div class="modal" style="display: block; display: block; background-color: #1e1e1ec4;">
                        <div class="modal-dialog" style="width: 500px;">
                            <div class="modal-content  widget-body-white">
                                <div class="modal-header">
                                    <h3 class="modal-title">Four Eye Approvers</h3>
                                    <asp:LinkButton ID="LinkButton1" runat="server" ToolTip="Close window" OnClick="lnkClose_Click1"
                                        CausesValidation="False" class="close" CommandName="Close"> ×</asp:LinkButton>
                                </div>

                                <div class="col-md-12" style="max-height: 500px !important; overflow: scroll;" id="approvers" runat="server">

                                    <div class="col-md-12" style="padding-left: 0px; font-weight: 900;">
                                        <asp:Label ID="Label2" runat="server" Style="margin-bottom: 10px; !important" Text=""></asp:Label>
                                    </div>
                                    <div class="notifycheckscroll" style="padding: 5px; border: 1px solid #ccc; width: 100%;">

                                        <asp:UpdatePanel ID="updatepanel4" ChildrenAsTriggers="true" ClientIDMode="static" runat="server" UpdateMode="conditional">
                                            <ContentTemplate>
                                                <telerik:RadTreeView ClientIDMode="Static" RenderMode="Lightweight" CausesValidation="false" ID="RadTreeView1" OnNodeCheck="RadTreeView1_NodeCheck" OnNodeClick="RadTreeView1_NodeCheck" runat="server" CheckBoxes="True"
                                                    Skin="Outlook" TriStateCheckBoxes="true" CheckChildNodes="true" IsOptionElementsEnabled="True">
                                                </telerik:RadTreeView>

                                            </ContentTemplate>
                                        </asp:UpdatePanel>
                                    </div>

                                </div>

                                <div class="form-group" style="margin-left: 10px; margin-left: 342px; !important; padding-bottom: 10px !important; padding-top: 10px !important;">
                                    <asp:Button ID="Button7" TabIndex="9" runat="server" Text="Ok" CssClass="btn btn-primary btn_save" OnClick="Button7_Click" />
                                    <asp:Button ID="Button8" runat="server" TabIndk2ex="7" Text="Cancel" CssClass="btn btn-primary btn_back" OnClick="Button8_Click" />

                                </div>
                            </div>
                        </div>
                    </div>

                </asp:Panel>
            </ContentTemplate>
            <Triggers>
                <asp:AsyncPostBackTrigger ControlID="RadTreeView1" EventName="NodeCheck" />
                <asp:AsyncPostBackTrigger ControlID="Button7" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="Button8" EventName="Click" />

            </Triggers>
        </asp:UpdatePanel>


        <asp:UpdatePanel ID="UP_pnlConditionPopUp" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <asp:Panel ID="pnlConditionPopUp" runat="server" Visible="false">
                    <div class="modal bg" style="display: block;">
                        <div class="modal-dialog" style="width: 60%; margin-top: 8%;">
                            <div class="modal-content widget-body-white">
                                <div class="modal-header confirm-modal-header">
                                    <div class="modal-title confirm-modal-title">
                                        <i class="message-icon" style="padding-left: 25px;"></i>Approval Details - 
                                     <asp:Label ID="lblEmailSubject" runat="server" OnInit="lblEmailSubject_Init" CssClass="lblbx" Style="width: 79%; vertical-align: bottom;" />
                                    </div>

                                    <asp:LinkButton ID="lkbtnHistory" runat="server" ToolTip="Close window"
                                        CausesValidation="False" class="close" CommandName="Close" OnClick="lkbtnHistory_Click">×</asp:LinkButton>
                                </div>

                                <div class="modal-body confirm-modal-body">
                                    <div class="form-group">
                                        <div class="notifyscroll" style="max-height: 250px;">
                                            <div class="mailbody">
                                                <asp:UpdatePanel ID="updatepanelIbots" runat="server" UpdateMode="Conditional">
                                                    <ContentTemplate>
                                                        <asp:Repeater ID="rptApprovalMail" runat="server" OnItemDataBound="rptApprovalMail_ItemDataBound">
                                                            <ItemTemplate>
                                                                <h3>
                                                                    <i class="email-user" title="Approver"></i>
                                                                    <asp:Label ID="lblEmailSender" CssClass="approver" Text='<%#Eval("EmailSender") %>' runat="server" />
                                                                    <asp:Label ID="lblDate" Text='<%#Eval("ReceivedTime") %>' CssClass="SetTime pull-right approver" ToolTip="Create Date" Style="background-position: 0px 1px;" runat="server"></asp:Label>
                                                                </h3>

                                                                <div>
                                                                    <asp:Label ID="lblEmailBody" Text='<%#Eval("Description") %>' CssClass="textbody" runat="server" Style="color: #000" />
                                                                </div>
                                                            </ItemTemplate>
                                                            <FooterTemplate>
                                                                <asp:Label ID="lblEmptyData"
                                                                    Text="No Record Found" CssClass="no-data-found" runat="server" Visible="false">
                                                                </asp:Label>
                                                            </FooterTemplate>
                                                        </asp:Repeater>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer confirm-modal-footer">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-9">
                                        <asp:Button ID="Button1" runat="server" class="btn btn-primary" Text="Close" OnClick="lkbtnHistory_Click"></asp:Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:Panel ID="pnlIncidentTracker" runat="server" Visible="false">
                    <div class="modal bg" style="display: block;">
                        <div class="modal-dialog" style="width: 30%; margin-top: 8%;">
                            <div class="modal-content widget-body-white">
                                <div class="modal-header confirm-modal-header">
                                    <div class="modal-title confirm-modal-title">
                                        <i class="incident_id" style="padding-left: 25px;"></i>Request Overview -
                                                    <asp:Label ID="lblIncidentId" runat="server" OnInit="lblIncidentId_Init" />
                                    </div>

                                    <asp:LinkButton ID="lnkbtnCloseIncidentTracker" runat="server" ToolTip="Close window" CausesValidation="False" class="close" CommandName="Close" OnClick="lnkbtnCloseIncidentTracker_Click">×</asp:LinkButton>
                                </div>

                                <div class="modal-body confirm-modal-body">
                                    <div class="widget widget-heading-simple widget-body-white" style="margin-top: 8px !important; box-shadow: none;">
                                        <div class="widget-body">
                                            <div class="notifyscroll" style="max-height: 280px;">
                                                <div class="mailbody">
                                                    <asp:UpdatePanel ID="updatepanel3" runat="server" UpdateMode="Conditional">
                                                        <ContentTemplate>
                                                            <asp:ListView ID="lvIncidentTracker" runat="server">
                                                                <LayoutTemplate>
                                                                    <ul>
                                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                                    </ul>
                                                                </LayoutTemplate>
                                                                <EmptyDataTemplate>
                                                                    <div class="message warning align-center bold no-bottom-margin" style="min-height: 140px;">
                                                                        <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true" Style="margin-top: 20px; left: calc(50% - 80px);" CssClass="no-data-found"></asp:Label>
                                                                    </div>
                                                                </EmptyDataTemplate>
                                                                <ItemTemplate>
                                                                    <li runat="server" class="">
                                                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                                                        <asp:Label ID="lblName" Font-Size="Large" ToolTip='<%#(Eval("Activity")) %>' CssClass="" runat="server" Text='<%# Eval("Activity") %>' /><br />
                                                                        <asp:Label ID="lblDesc" Font-Size="Medium" runat="server" Text='<%# Eval("Createdate") %>' />
                                                                    </li>
                                                                </ItemTemplate>
                                                            </asp:ListView>
                                                        </ContentTemplate>
                                                    </asp:UpdatePanel>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer confirm-modal-footer">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-9">
                                        <asp:Button ID="btnCloseIncidentTracker" runat="server" class="btn btn-primary" Text="Close" OnClick="btnCloseIncidentTracker_Click"></asp:Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:Panel ID="PanlApprovalConfirmation" runat="server" Visible="false">
                    <div class="modal bg" style="display: block;">
                        <div class="modal-dialog" style="width: 38%; margin-top: 8%;">
                            <div class="modal-content widget-body-white">
                                <div class="modal-header confirm-modal-header">
                                    <div class="modal-title confirm-modal-title">Confirmation</div>
                                    <asp:LinkButton ID="lnkbtnCloseApprTop" runat="server" ToolTip="Close window" CausesValidation="False" class="close" CommandName="Close" OnClick="lnkbtnCloseApprTop_Click">×</asp:LinkButton>
                                </div>
                                <div class="clearfix"></div>
                                <div class="modal-body confirm-modal-body">

                                    <div class="form-group">
                                        <div class="col-md-2">
                                            <img src="../Images/ApprovalProcessIcon/thumb-up-50.png" />
                                        </div>
                                        <div class="col-md-10">
                                            <asp:Label ID="lblApprovadMsg" CssClass="confirmtxt padding-none" Style="margin-bottom: 10px;" runat="server"></asp:Label>
                                        </div>
                                    </div>
                                    <div class="clearfix"></div>

                                    <div class="form-group">
                                        <div class="col-md-2">
                                            Reason<label class="col-xs-3 control-label" for="txtReason">
                                                <span class="inactive">*</span></label>
                                        </div>

                                        <div class="col-md-10 formrel">
                                            <asp:TextBox ID="txtReasonApprove" runat="server" class="form-control" Width="380px" TabIndex="1" TextMode="MultiLine" Style="max-width: 380px !important; margin-bottom: 5px" ValidationGroup="UsecaseAct2" />
                                            <asp:Label ID="LbltxtReasonApprove" runat="server" ForeColor="Red" CssClass="error" Text="" Display="Dynamic"></asp:Label>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" CssClass="error" ErrorMessage="Enter Reason"
                                                ControlToValidate="txtReasonApprove" Display="Dynamic"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>

                                </div>


                                <div class="modal-footer confirm-modal-footer">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-9">
                                        <asp:Button ID="btnOK_approval" runat="server" class="btn btn-primary" Text="Yes" Style="width: 30%" OnClick="btnOK_approval_Click" ValidationGroup="UsecaseAct2"></asp:Button>
                                        <asp:Button ID="btnClose_ApprovalDown" runat="server" class="btn btn-primary btncancel" CausesValidation="false" Text="No" Style="width: 30%" OnClick="btnClose_ApprovalDown_Click"></asp:Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:Panel ID="pnlReject" runat="server" Visible="false">
                    <div class="modal bg" style="display: block;">
                        <div class="modal-dialog" style="width: 38%; margin-top: 8%;">
                            <div class="modal-content widget-body-white">
                                <div class="modal-header confirm-modal-header">
                                    <div class="modal-title confirm-modal-title">Confirmation</div>
                                    <asp:LinkButton ID="lnkbtnCloseReject" runat="server" ToolTip="Close window" CausesValidation="False" class="close" OnClick="lnkbtnCloseReject_Click">×</asp:LinkButton>
                                </div>

                                <div class="modal-body confirm-modal-body">
                                    <div class="form-group">
                                        <div class="col-md-2">
                                            <img src="../Images/ApprovalProcessIcon/thumb-down-50.png" />
                                        </div>
                                        <div class="col-md-10">
                                            <asp:Label ID="lblRejectMsg" CssClass="confirmtxt padding-none" Style="margin-bottom: 10px;" runat="server"></asp:Label>
                                        </div>
                                        <div class="clearfix"></div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-md-2">
                                            Reason<label class="col-xs-3 control-label" for="txtReason">
                                                <span class="inactive">*</span></label>
                                        </div>

                                        <div class="col-md-10 formrel">
                                            <asp:TextBox ID="txtReason" runat="server" class="form-control" Width="380px" TabIndex="1" TextMode="MultiLine" Style="max-width: 380px !important; margin-bottom: 5px" ValidationGroup="UsecaseAct" />
                                            <asp:Label ID="lbltxtReason" runat="server" ForeColor="Red" CssClass="error" Text="" Display="Dynamic"></asp:Label>
                                            <asp:RequiredFieldValidator ID="rfvtxtreason" runat="server" CssClass="error" ErrorMessage="Enter Reason"
                                                ControlToValidate="txtReason" Display="Dynamic"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>

                                </div>

                                <div class="modal-footer confirm-modal-footer">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-9">
                                        <asp:Button ID="btnOKReject" runat="server" class="btn btn-primary" Text="Yes" Style="width: 32%" OnClick="btnOKReject_Click" ValidationGroup="UsecaseAct"></asp:Button>
                                        <asp:Button ID="btnCancelReject" runat="server" class="btn btn-primary btncancel" CausesValidation="false" Text="No" Style="width: 32%" OnClick="btnCancelReject_Click"></asp:Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>


                <asp:Panel ID="Panel1" runat="server" Visible="false">
                    <div class="modal bg" style="display: block;">
                        <div class="modal-dialog" style="width: 38%; margin-top: 8%;">
                            <div class="modal-content widget-body-white">
                                <div class="modal-header confirm-modal-header">
                                    <div class="modal-title confirm-modal-title">Confirmation</div>
                                    <asp:LinkButton ID="LinkButton2" runat="server" ToolTip="Close window" CausesValidation="False" class="close" OnClick="LinkButton2_Click">×</asp:LinkButton>
                                </div>

                                <div class="modal-body confirm-modal-body">
                                    <div class="form-group">
                                        <div class="col-md-2">
                                            <img src="../Images/ApprovalProcessIcon/thumb-up-50.png" />
                                        </div>
                                        <div class="col-md-10">
                                            <asp:Label ID="Label16" CssClass="confirmtxt padding-none" Style="margin-bottom: 10px;" runat="server"></asp:Label>
                                        </div>
                                        <div class="clearfix"></div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-md-2">
                                            Remark<label class="col-xs-3 control-label" for="txtReason">
                                                <span class="inactive">*</span></label>
                                        </div>

                                        <div class="col-md-10 formrel">
                                            <asp:TextBox ID="TextBox1" runat="server" class="form-control" Width="380px" TabIndex="1" TextMode="MultiLine" Style="max-width: 380px !important; margin-bottom: 5px" ValidationGroup="UsecaseAct" />
                                            <asp:Label ID="Label17" runat="server" ForeColor="Red" CssClass="error" Text="" Display="Dynamic"></asp:Label>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator6" runat="server" CssClass="error" ErrorMessage="Enter Reason"
                                                ControlToValidate="TextBox1" Display="Dynamic"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>

                                </div>

                                <div class="modal-footer confirm-modal-footer">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-9">
                                        <asp:Button ID="Button2" runat="server" class="btn btn-primary" Text="Yes" Style="width: 32%" OnClick="Button2_Click" ValidationGroup="UsecaseAct"></asp:Button>
                                        <asp:Button ID="Button4" runat="server" class="btn btn-primary btncancel" CausesValidation="false" Text="No" Style="width: 32%" OnClick="Button4_Click"></asp:Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:Panel ID="Panel2" runat="server" Visible="false">
                    <div class="modal bg" style="display: block;">
                        <div class="modal-dialog" style="width: 38%; margin-top: 8%;">
                            <div class="modal-content widget-body-white">
                                <div class="modal-header confirm-modal-header">
                                    <div class="modal-title confirm-modal-title">Confirmation</div>
                                    <asp:LinkButton ID="LinkButton3" runat="server" ToolTip="Close window" CausesValidation="False" class="close" OnClick="LinkButton2_Click">×</asp:LinkButton>
                                </div>

                                <div class="modal-body confirm-modal-body">
                                    <div class="form-group">
                                        <div class="col-md-2">
                                            <img src="../Images/ApprovalProcessIcon/thumb-down-50.png" />
                                        </div>
                                        <div class="col-md-10">
                                            <asp:Label ID="Label18" CssClass="confirmtxt padding-none" Style="margin-bottom: 10px;" runat="server"></asp:Label>
                                        </div>
                                        <div class="clearfix"></div>
                                    </div>
                                    <div class="form-group">
                                        <div class="col-md-2">
                                            Remark<label class="col-xs-3 control-label" for="TextBox2">
                                                <span class="inactive">*</span></label>
                                        </div>

                                        <div class="col-md-10 formrel">
                                            <asp:TextBox ID="TextBox2" runat="server" class="form-control" Width="380px" TabIndex="1" TextMode="MultiLine" Style="max-width: 380px !important; margin-bottom: 5px" ValidationGroup="UsecaseAct" />
                                            <asp:Label ID="Label19" runat="server" ForeColor="Red" CssClass="error" Text="" Display="Dynamic"></asp:Label>
                                            <asp:RequiredFieldValidator ID="RequiredFieldValidator7" runat="server" CssClass="error" ErrorMessage="Enter Reason"
                                                ControlToValidate="TextBox2" Display="Dynamic"></asp:RequiredFieldValidator>
                                        </div>
                                    </div>

                                </div>

                                <div class="modal-footer confirm-modal-footer">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-9">
                                        <asp:Button ID="Button5" runat="server" class="btn btn-primary" Text="Yes" Style="width: 32%" OnClick="Button5_Click" ValidationGroup="UsecaseAct"></asp:Button>
                                        <asp:Button ID="Button6" runat="server" class="btn btn-primary btncancel" CausesValidation="false" Text="No" Style="width: 32%" OnClick="Button6_Click"></asp:Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>
            </ContentTemplate>
            <Triggers>
                <asp:AsyncPostBackTrigger ControlID="lvWFProfileApprovalProcess" EventName="ItemCommand" />
                <asp:AsyncPostBackTrigger ControlID="btnOKReject" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="btnCancelReject" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="Button2" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="Button4" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="Button5" EventName="Click" />
                <asp:AsyncPostBackTrigger ControlID="Button6" EventName="Click" />
            </Triggers>
        </asp:UpdatePanel>


        <asp:UpdatePanel ID="UP_PnlApprovalRemainder" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <asp:Panel ID="PnlApprovalRemainder" runat="server" Visible="false">
                    <div class="modal bg" style="display: block;">
                        <div class="modal-dialog" style="width: 24%; margin-top: 8%;">
                            <div class="modal-content widget-body-white">
                                <div class="modal-header confirm-modal-header">
                                    <div class="modal-title confirm-modal-title">Confirmation</div>
                                    <asp:LinkButton ID="lnkbtncloseremaider" runat="server" ToolTip="Close window" CausesValidation="False" class="close" OnClick="lnkbtncloseremaider_Click">×</asp:LinkButton>
                                </div>

                                <div class="modal-body confirm-modal-body">
                                    <div class="form-group">
                                        <div class="col-md-2">
                                            <img src="../Images/ApprovalProcessIcon/email-50.png" />
                                        </div>
                                        <div class="col-md-10">
                                            <asp:Label ID="Label10" CssClass="confirmtxt padding-none" Style="margin-bottom: 10px;" runat="server" Text="Do you want to Send Reminder for Approval?"></asp:Label>
                                        </div>
                                        <div class="clearfix"></div>
                                    </div>
                                </div>

                                <div class="modal-footer confirm-modal-footer">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-9">
                                        <asp:Button ID="btnOKApprovalRemainder" runat="server" class="btn btn-primary" Text="Yes" Style="width: 32%" OnClick="btnOKApprovalRemainder_Click" ValidationGroup="UsecaseAct"></asp:Button>
                                        <asp:Button ID="btnCloseApprovalRemainder" runat="server" class="btn btn-primary btncancel" Text="No" Style="width: 32%" OnClick="btnCloseApprovalRemainder_Click"></asp:Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>
            </ContentTemplate>
            <Triggers>
                <asp:AsyncPostBackTrigger ControlID="lvWFProfileApprovalProcess" EventName="ItemCommand" />
            </Triggers>
        </asp:UpdatePanel>

        <asp:Panel ID="pnlConditionPopUp123" runat="server" Visible="false">
            <div class="modal bg" style="display: block;">
                <div class="modal-dialog" style="width: 22%; margin-top: 8%;">
                    <div class="modal-content widget-body-white">
                        <div class="modal-header confirm-modal-header">
                            <div class="modal-title confirm-modal-title">Warning</div>
                            <asp:LinkButton ID="lkbtnHistory1" runat="server" ToolTip="Close window" OnClick="lkbtnHistory1_Click"
                                CausesValidation="False" class="close" CommandName="Close">×</asp:LinkButton>
                        </div>
                        <div class="modal-body confirm-modal-body">
                            <div class="form-group">
                                <img src="../Images/Icons/Confirm_Warining_Icon.png" />
                                <asp:Label ID="lblservertest1" CssClass="confirmtxt" runat="server" Text="CPInfraMonitor service is not started"></asp:Label>
                            </div>
                        </div>
                        <div class="modal-footer confirm-modal-footer">
                            <div class="col-md-3"></div>
                            <div class="col-md-9">
                                <asp:Button ID="btnOk1" runat="server" class="btn btn-primary" Text="OK" Style="width: 30%" OnClick="btnOk1_Click" OnClientClick="FireEvent();"></asp:Button>
                                <asp:Button ID="Button3" runat="server" class="btn btn-primary" Text="Cancel" OnClick="Button3_Click"></asp:Button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </asp:Panel>

        <asp:HiddenField ID="hdnvalue" runat="server" Value="Default" />


        <div class="clearfix"></div>



    </div>
    <asp:UpdateProgress ID="UpdateProgress1" runat="server">
        <ProgressTemplate>
            <div id="imgLoading1" class="loading-mask">
                <span>Loading...
                </span>
            </div>
        </ProgressTemplate>
    </asp:UpdateProgress>

    <script src="../Script/DateTimePickerJquery/jquery.datetimepicker.js"></script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <%-- <script src="../Script/jquery.simple-dtpicker.js" type="text/javascript"></script>
    <script src="../Script/jquery.timepicker.min.js" type="text/javascript"></script>--%>
    <script src="../Script/AlertModal.js"></script>
    <script src="../Script/chosen.jquery.js"></script>




    <style>
        #UpdateProgress1 {
            position: initial;
            display: block;
        }
    </style>

    <script>
        $('.modal-body').on('click', function () {
            $('#ctl00_cphBody_dvapprovals').hide();
        });
    </script>

    <script type="text/javascript">
        function page_load() {

            pnl_radtreeview.Visible = false;
        }

    </script>

    <script type="text/javascript">




        function ChangeActionType(val) {
            // $('#ctl00_cphBody_txtStartDate').val() = "";
            document.getElementById("ctl00_cphBody_txtStartDate").value = "";
            document.getElementById("ctl00_cphBody_txtEndDate").value = "";
            $('#ctl00_cphBody_txtEndDate').attr("disabled", true);
            //alert("GT");
        }


        function txtStartDate_changed() {
            $('#ctl00_cphBody_txtEndDate').attr("disabled", false);
            txtEndDate_Click();
        }

        function txtEndDate_Click() {

            //var _typechk = "Execution";
            var value = document.getElementById("<%=ddl_ApprovalActionType.ClientID%>");
            var getvalue = value.options[value.selectedIndex].value;
            var gettext = value.options[value.selectedIndex].text;
            var selectedVal = $("[id$='rbl_ForProfile_Workflow']").find(":checked").val();
            var _maxTime = ($('#ctl00_cphBody_txtStartDate').val());
            var arr = _maxTime.split(" ");
            var date = arr[0].split("-");
            var dd = date[0];
            var mm = date[1] - 1;
            var yy = date[2];

            var time = arr[1].split(":");
            var h = time[0];
            var m = time[1];

            var today = new Date(yy, mm, dd, h, m);
            today.addMonthss(mm);


            //if (_typechk == gettext) {

            //    var _nedt = new Date(today).addHours(6).format("dd-MM-yy HH:mm");
            //}
            //else {
            //    var _nedt = new Date(today).addHours(12).format("dd-MM-yy HH:MM");
            //}

            if (gettext == "Execution") {

                var _nedt = new Date(today).addHours(6).format("dd-MM-yyyy HH:mm");
            }
            else if (gettext == "Modification") {
                // var _nedt = new Date(today).addHours(12).format("dd-MM-yyyy HH:MM");
                if (selectedVal == "Profile")
                    var _nedt = new Date(today).addHours(12).format("dd-MM-yyyy HH:MM");
                else if (selectedVal == "Workflow") {
                    var _nedt = new Date(today).addHours(72).format("dd-MM-yyyy HH:MM");
                }
                else {
                    $('#ctl00_cphBody_lblduration_error').text("Please Select Profile or Workflow...");
                    $('#ctl00_cphBody_lblduration_error').show();

                }
            }
            else {

                $('#ctl00_cphBody_lblduration_error').text("Please Select The Execute or Modify Type...");
                $('#ctl00_cphBody_lblduration_error').show();
            }


            $('#ctl00_cphBody_txtEndDate').val(_nedt);
            // document.getElementbyId('EDIT21').contentEditable = false;
            $('#ctl00_cphBody_txtEndDate').attr("disabled", true);
        }

        Date.prototype.addMonthss = function (h) {
            this.setMonth(h);
            return this;
        }

        Date.prototype.addHours = function (h) {
            this.setHours(this.getHours() + h);
            return this;
        }



    </script>

    <script type="text/javascript">          


        function initializeDateTimePickers() {

            var dt = new Date();
            var time = dt.getHours() + ":" + dt.getMinutes() + ":" + dt.getSeconds();

            var _time = dt.getDate() + "-" + dt.getMonth() + "-" + dt.getYear() + " " + dt.getHours() + ":" + dt.getMinutes();
            var today = new Date(dt.getYear(), dt.getMonth(), dt.getDate(), dt.getHours(), dt.getMinutes(), dt.getSeconds(), dt.getMilliseconds());


            $('#ctl00_cphBody_txtStartDate').datetimepicker({
                ownerDocument: document,
                contentWindow: window,

                value: '',
                rtl: false,

                format: 'd-m-Y H:i',
                formatTime: 'H:i',
                formatDate: 'd-m-Y',

                // new Date(), '1986/12/08', '-1970/01/05','-1970/01/05',
                startDate: false,
                minDate: dt,
                minTime: time,

                step: 10,

                onChangeDateTime: function () {
                    var _maxTime = ($('#ctl00_cphBody_txtStartDate').val()); // Get the current datetime
                    var arr = _maxTime.split(" ");
                    var date = arr[0].split("-");

                    var dd = date[0];
                    var mm = date[1] - 1;

                    if ((dt.getMonth() == mm) && (dt.getDate() == dd)) {
                        $('#ctl00_cphBody_txtStartDate').datetimepicker({

                            minTime: time
                        });

                    } else {
                        $('#ctl00_cphBody_txtStartDate').datetimepicker({

                            minTime: "00:00"
                        });

                    }

                },

            });

            //$('#ctl00_cphBody_txtEndDate').datetimepicker({

            //    ownerDocument: document,
            //    contentWindow: window,

            //    value: '',
            //    rtl: false,

            //    format: 'd-m-y H:i',
            //    formatTime: 'H:i',
            //    formatDate: 'd-m-y',

            //    // new Date(), '1986/12/08', '-1970/01/05','-1970/01/05',
            //    startDate: false,

            //    //  minDateTime: $('#ctl00_cphBody_txtEndDate').val(),

            //    step: 10,

            //    });
        }


        $(document).ready(function () {




            initializeDateTimePickers();
            Search_Workflow();
            GetActiveTimelsot();
            Sys.WebForms.PageRequestManager.getInstance().add_pageLoaded(initializeDateTimePickers, Search_Workflow);
            Sys.WebForms.PageRequestManager.getInstance().add_endRequest(initializeDateTimePickers, Search_Workflow);




        });


        $(window).load(function () {
            initializeDateTimePickers();
        });

        var txtStartDate;
        var txtEndDate;




        function onLoadRadDateTimePicker1(sender, args) {
            txtStartDate = sender;
        }

        function onLoadRadDateTimePicker2(sender, args) {
            txtEndDate = sender;
        }

        function doMyAction() { }
        function pageLoad() {
            activetab();

            $(".notifyscroll").mCustomScrollbar({
                axis: "y"
            });
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
            //$('[id$=txtStartDate]').appendDtpicker();
            //$('[id$=txtEndDate]').appendDtpicker();
            //$('.datepicker').parent().addClass('datepickerpos');
        }

        function activetab() {
            if ($("[id$=activepnl]").val() != " ") {
                $("span[data-id=" + $("[id$=activepnl]").val() + "]").parent().parent().parent().parent().addClass("viewtimeline");
            }
        }






        //function SearchInfra(txtSearch, CblstGroup) {
        //    if ($(txtSearch).val() != "") {
        //        var count = 0;
        //        $(CblstGroup).children('tbody').children('tr').each(function () {
        //            var match = false;
        //            $(this).children('td').children('label').each(function () {
        //                if ($(this).text().toUpperCase().indexOf($(txtSearch).val().toUpperCase()) > -1)
        //                    match = true;
        //            });
        //            if (match) {
        //                $(this).show();
        //                count++;
        //            }
        //            else { $(this).hide(); }
        //        });
        //    }
        //    else {
        //        $(CblstGroup).children('tbody').children('tr').each(function () {
        //            $(this).show();
        //        });
        //        $('#spnCount').html('');
        //    }
        //}


        $(document).on("click", ".imgbtn", function () {
            //$(".imgbtn").click(function () {
            //$(this).parent().parent().next().toggle();          



            if ($(".tableinside").hasClass("viewtimeline")) {
                if ($(this).parent().parent().parent().parent().hasClass("viewtimeline")) {
                    //alert("same");
                    $(this).parent().parent().parent().parent().removeClass("viewtimeline");
                    // timer._startTimer();
                    $("[id$=hdnTimerbtn]").val("1");
                    $("[id$=activepnl]").val(" ");
                }
                else {
                    $(".tableinside").removeClass("viewtimeline");
                    $(this).parent().parent().parent().parent().toggleClass("viewtimeline");
                    //  timer._stopTimer();
                    $("[id$=hdnTimerbtn]").val("0");
                    $("[id$=activepnl]").val(" ");
                }
            }
            else {
                $(this).parent().parent().parent().parent().toggleClass("viewtimeline");
                // timer._stopTimer();
                $("[id$=hdnTimerbtn]").val("0");
                $("[id$=activepnl]").val(" ");
                $("[id$=activepnl]").val($(this).attr("data-id"));
            }


        })



        function handleButtonFocus() {

            Sys.WebForms.PageRequestManager.getInstance().add_initializeRequest(CancelComboBoxPostback);
        }

        function CancelComboBoxPostback(sender, args) {
            var prm = Sys.WebForms.PageRequestManager.getInstance();
            if (prm.get_isInAsyncPostBack() & args.get_postBackElement().id == 'Radtreeview1') {
                args.set_cancel(true);
            }
        }

        function Search_Workflow() {
            var charcount_da;
            $(document).on("keyup", "#ctl00_cphBody_ddl_WorkflowList_chosen .chosen-drop .chosen-search > input", function (event) {

                if (event.keyCode == 30 || event.keyCode == 48 || event.keyCode == 13) {
                    return false;
                }

                var chtext = $(this).val();

                if (chtext.length != charcount_da) {

                    $.ajax({
                        type: "POST",
                        url: "WorkflowApprovalProcess.aspx/workflow_textsearch",
                        data: "{'text':'" + chtext + "'}",
                        contentType: "application/json; charset=utf-8",
                        dataType: "json",
                        async: true,
                        success: function Success(data) {
                            if (data.d != null) {
                                Populateddlloadworkflow(data.d);
                                $("#ctl00_cphBody_ddl_WorkflowList").trigger("chosen:updated");
                                setTimeout(function () { $('#ctl00_cphBody_ddl_WorkflowList_chosen .chosen-drop .chosen-search input').val(chtext); }(), 1000);
                            }
                        }
                    });


                }
            });

        }

        function Populateddlloadworkflow(msg) {
            var data = msg;
            $("#ctl00_cphBody_ddl_WorkflowList option").remove();
            var result = data.split(",");
            if (result == "") {
                var text = "-Select Workflow-";
                var value = "000";
                var chkValue = "";
                $("#ctl00_cphBody_ddl_WorkflowList").append("<option value='" + value + "'>" + text + "</option>");
            }
            else {
                var text = "-Select Workflow-";
                var value = "000";
                var chkValue = "";
                // AppendOption('ctl00_cphBody_ddl_WorkflowList', value, text);
                $("#ctl00_cphBody_ddl_WorkflowList").append("<option value='" + value + "'>" + text + "</option>");
                for (var i = 0; i < result.length; i++) {
                    chkValue = result[i].split(":");
                    text = chkValue[0];
                    value = chkValue[1];
                    $("#ctl00_cphBody_ddl_WorkflowList").append("<option value='" + value + "'>" + text + "</option>");
                }
                var options = $("#ctl00_cphBody_ddl_WorkflowList option");                    // Collect options         		
                options.detach().sort(function (a, b) {               // Detach from select, then Sort		
                    var at = $(a).text();
                    var bt = $(b).text();
                    return (at > bt) ? 1 : ((at < bt) ? -1 : 0);            // Tell the sort function how to order		
                });
                options.appendTo("#ctl00_cphBody_ddl_WorkflowList");
                $("#ctl00_cphBody_ddl_WorkflowList option:first").attr('selected', 'selected');
                //$("#ddl_WorkflowList option:first").attr('selected', 'selected');
            }
        }

        function GetActiveTimelsot() {
            $(document).on("change", '#ctl00_cphBody_ddl_WorkflowList', function () {

                var workflowId = $('#ctl00_cphBody_ddl_WorkflowList').val();

                if (workflowId != "") {
                    $('#ctl00_cphBody_wfID').val(workflowId);
                    $('#ctl00_cphBody_wfID').change();

                    //$.ajax({
                    //    type: "POST",
                    //    url: "WorkflowApprovalProcess.aspx/GetActiveTimeSlot",
                    //    data: "{'workflowId':'" + workflowId + "'}",
                    //    contentType: "application/json; charset=utf-8",
                    //    dataType: "json",
                    //    async: true,
                    //    success: function Success(data) {
                    //        if (data.d != null) {
                    //            // Populateddlloadworkflow(data.d);
                    //            //$("#ctl00_cphBody_ddl_WorkflowList").trigger("chosen:updated");
                    //            //$("#ddl_WorkflowList").trigger("chosen:updated");
                    //            //setTimeout(function () { $('#ctl00_cphBody_ddl_WorkflowList_chosen .chosen-drop .chosen-search input').val(chtext); }(), 1000);
                    //        }
                    //    }
                    //});


                }
            });
        }
    </script>
</asp:Content>
