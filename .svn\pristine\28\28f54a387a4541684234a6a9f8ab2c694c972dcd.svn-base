﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="VVRMssqlFullDBConfiguration.ascx.cs" Inherits="CP.UI.Controls.VVRMssqlFullDBConfiguration" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>


<div class="form-horizontal margin-none">
   <input type="hidden" id="hdfStaticGuid" runat="server" />
     <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">VVR Management Console</h4>
        </div>
        <div class="widget-body">
            <table class="table">
                <thead>
                    <tr>
                        <th style="width: 24%;"></th>
                        <th style="width: 37.5% !important;">Production Server
                        </th>
                        <th style="width: 37.5% !important;">DR Server
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <label>
                                VVR Server
                            </label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                            <asp:DropDownList ID="ddlPrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" data-style="btn-default" OnSelectedIndexChanged="ddlPrServer_SelectedIndexChanged">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvPRServer" runat="server" ControlToValidate="ddlPrServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select Production Server"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:DropDownList ID="ddlDrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" data-style="btn-default" OnSelectedIndexChanged="ddlDrServer_SelectedIndexChanged">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvDRServer" runat="server" ControlToValidate="ddlDrServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select DR Server"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                IP Address
                            </label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRMgtConsoleIP" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPrConsoleIP" runat="server" ErrorMessage="Please Enter IP Address" CssClass="error"
                                ControlToValidate="txtPRMgtConsoleIP"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRMgtConsoleIP" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDrConsoleIP" runat="server" ErrorMessage="Please Enter IP Address" CssClass="error"
                                ControlToValidate="txtDRMgtConsoleIP"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                User Name</label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRUserName" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPRUserName" runat="server" ErrorMessage="Please Enter User Name" CssClass="error"
                                ControlToValidate="txtPRUserName"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRUserName" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRUserName" runat="server" ErrorMessage="Please Enter User Name" CssClass="error"
                                ControlToValidate="txtDRUserName"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                Password</label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRPassword" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>
                           
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRPassword" runat="server" CssClass="form-control" Style="Width: 56% !important" autocomplete="off" TextMode="Password"></asp:TextBox>
                           
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
                    <h4 class="heading">
                        Storage - MSSQL Full DB - VVR</h4>
                </div>
        <div class="widget-body">
            <table class="table">
                <thead>
                    <tr>
                        <th style="width: 24%;"></th>
                        <th style="width: 37.5% !important;">Production Server
                        </th>
                        <th style="width: 37.5% !important;">DR Server
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <label>
                                Disk Group Name
                            </label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRDiskGroup" CssClass="form-control" Style="width: 56% !important" Enabled="true" runat="server"></asp:TextBox>
                              <asp:RequiredFieldValidator ID="rfvPRDiskGroup" runat="server" ErrorMessage="Please Enter PR Disk Group Name" CssClass="error"
                                ControlToValidate="txtPRDiskGroup"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRDiskGroup" CssClass="form-control" Style="width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRDiskGroup" runat="server" ErrorMessage="Please Enter DR Disk Group Name" CssClass="error"
                                ControlToValidate="txtDRDiskGroup"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                               Replicated VG Name
                            </label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRVGName" CssClass="form-control" Style="width: 56% !important" Enabled="true" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPRVgName" runat="server" ErrorMessage="Please Enter PR Replicated VG Name" CssClass="error"
                                ControlToValidate="txtPRVGName"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRVGName" CssClass="form-control" Style="width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRVGName" runat="server" ErrorMessage="Please Enter DR Replicated VG Name" CssClass="error"
                                ControlToValidate="txtDRVGName"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                RLINK Name
                            </label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRRLINKName" CssClass="form-control" Style="width: 56% !important" Enabled="true" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPRRLINKName" runat="server" ErrorMessage="Please Enter PR RLINK Name" CssClass="error"
                                ControlToValidate="txtPRRLINKName"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRRLINKName" CssClass="form-control" Style="width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRRLINKName" runat="server" ErrorMessage="Please Enter DR RLINK Name" CssClass="error"
                                ControlToValidate="txtDRRLINKName"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    
                </tbody>
            </table>

            <div class="form-actions row">
                <div class="col-lg-3">
                    <asp:Label ID="lblMsg" runat="server" Text=""></asp:Label>
                </div>
                <div class="col-lg-7" style="margin-left: 58.4%;">
                    <asp:Button ID="btnSaveRep" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save" OnClick="btnSaveRep_Click"/>
                    <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server" Text="Cancel" CausesValidation="false" OnClick="btnCancel_Click"/>
                </div>
            </div>

        </div>
    </div>
</div>