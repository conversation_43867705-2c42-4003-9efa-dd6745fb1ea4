﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ParallelWorkflowActionResultDataAccess : BaseDataAccess,
        IParallelWorkflowActionResultDataAccess
    {
        #region Constructors

        public ParallelWorkflowActionResultDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ParallelWorkflowActionResult> CreateEntityBuilder
            <ParallelWorkflowActionResult>()
        {
            return (new ParallelWorkflowActionResultBuilder()) as IEntityBuilder<ParallelWorkflowActionResult>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="ParallelWorkflowActionResult" />into bcms_parallel_workflowactionresult table.
        /// </summary>
        /// <param name="parallelresult">parallelresult</param>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author><PERSON><PERSON><PERSON></author>
        ParallelWorkflowActionResult IParallel<PERSON>orkflowActionResultDataAccess.Add(
            ParallelWorkflowActionResult parallelresult)
        {
            try
            {
                parallelresult.CreatorId = 1;
                const string sp = "PARALLELWFACTIONRESULT_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowActionName", DbType.AnsiString, parallelresult.WorkflowActionName);
                    Database.AddInParameter(cmd, Dbstring + "iStartTime", DbType.DateTime, parallelresult.StartTime);
                    Database.AddInParameter(cmd, Dbstring + "iEndTime", DbType.DateTime, parallelresult.EndTime);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, parallelresult.Status);
                    Database.AddInParameter(cmd, Dbstring + "iParallelDROperationId", DbType.Int32, parallelresult.ParallelDROperationId);
                    Database.AddInParameter(cmd, Dbstring + "iParallelGroupWorkflowId", DbType.Int32, parallelresult.ParallelGroupWorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, parallelresult.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, parallelresult.Message);
                    Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.Int32, parallelresult.ActionId);
                    Database.AddInParameter(cmd, Dbstring + "iConditionActionId", DbType.Int32, parallelresult.ConditionActionId);
                    Database.AddInParameter(cmd, Dbstring + "iSkipStep", DbType.Boolean, parallelresult.SkipStep);
                    Database.AddInParameter(cmd, Dbstring + "iDirection", DbType.AnsiString, parallelresult.Direction);
                    //Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, parallelresult.CreatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        parallelresult = reader.Read()
                            ? CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntity(reader, parallelresult)
                            : null;
                    }

                    if (parallelresult == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "GlobalMirror already exists. Please specify another globalMirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this globalMirror.");
                                }
                        }
                    }

                    return parallelresult;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting ParallelWorkflowActionResult Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Update <see cref="ParallelWorkflowActionResult" />into bcms_parallel_workflowactionresult table.
        /// </summary>
        /// <param name="parallelresult">parallelresult</param>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author>Shivraj Mujumale</author>
        ParallelWorkflowActionResult IParallelWorkflowActionResultDataAccess.Update(
            ParallelWorkflowActionResult parallelresult)
        {
            try
            {
                parallelresult.CreatorId = 1;
                const string sp = "PARALLELWFACTIONRESULT_UPDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, parallelresult.Id);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowActionName", DbType.AnsiString,
                       parallelresult.WorkflowActionName);
                    Database.AddInParameter(cmd, Dbstring + "iStartTime", DbType.DateTime, parallelresult.StartTime);
                    Database.AddInParameter(cmd, Dbstring + "iEndTime", DbType.DateTime, parallelresult.EndTime);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, parallelresult.Status);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, parallelresult.Message);
                    Database.AddInParameter(cmd, Dbstring + "iParallelDROperationId", DbType.Int32,
                        parallelresult.ParallelDROperationId);
                    Database.AddInParameter(cmd, Dbstring + "iParallelGroupWorkflowId", DbType.Int32,
                        parallelresult.ParallelGroupWorkflowId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        parallelresult = reader.Read()
                            ? CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntity(reader, parallelresult)
                            : null;
                    }

                    if (parallelresult == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "GlobalMirror already exists. Please specify another globalMirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this globalMirror.");
                                }
                        }
                    }

                    return parallelresult;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating ParallelWorkflowActionResult Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelWorkflowActionResult" />from bcms_parallel_workflowactionresult table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author>Shivraj Mujumale</author>
        ParallelWorkflowActionResult IParallelWorkflowActionResultDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ParalWFActionResult_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelWorkflowActionResult>()).BuildEntity(reader,
                                new ParallelWorkflowActionResult());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetById(" +
                    id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelWorkflowActionResult" />from bcms_parallel_workflowactionresult table.
        /// </summary>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author>Shivraj Mujumale</author>
        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetAll()
        {
            try
            {
                const string sp = "PARALLELWFACTIONRESULT_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelWorkflowActionResult" />from bcms_parallel_workflowactionresult table by id and wid.
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="wid">wid</param>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author>Shivraj Mujumale</author>
        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetParallelDrOperationdata(int id,
            int wid)
        {
            try
            {
                const string sp = "PARALLELDOPRR_GETDATABYID";

                IList<ParallelWorkflowActionResult> objprl = new List<ParallelWorkflowActionResult>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "wid", DbType.Int32, wid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new ParallelWorkflowActionResult
                            {
                                WorkflowActionName = Convert.IsDBNull(reader["WorkflowActionName"])
                ? string.Empty
                : Convert.ToString(reader["WorkflowActionName"]),
                                StartTime = Convert.IsDBNull(reader["StartTime"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["StartTime"]),
                                EndTime = Convert.IsDBNull(reader["EndTime"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["EndTime"]),
                                //Status = (WorkflowActionStatus)Enum.Parse(typeof (WorkflowActionStatus), reader[3].ToString(), true),
                                Status = Convert.IsDBNull(reader["Status"])
                  ? WorkflowActionStatus.Undefined
                  : (WorkflowActionStatus)
                      Enum.Parse(typeof(WorkflowActionStatus), Convert.ToString(reader["Status"]), true),
                                InfraobjectName = Convert.IsDBNull(reader["InfraobjectName"])
                ? string.Empty
                : Convert.ToString(reader["InfraobjectName"]),
                                InfraObjectId = Convert.IsDBNull(reader["InfraobjectId"])
                ? 0
                : Convert.ToInt32(reader["InfraobjectId"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetParallelDrOperationdata(" +
                    id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelWorkflowActionResult" />from bcms_parallel_workflowactionresult table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author>Shivraj Mujumale</author>
        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetParallelDrOperationSummarydata(
            int id)
        {
            try
            {
                const string sp = "PARALLDROPERAT_GETSUMMARYDATA";

                IList<ParallelWorkflowActionResult> objprl = new List<ParallelWorkflowActionResult>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new ParallelWorkflowActionResult
                            {
                                WorkflowActionName = Convert.IsDBNull(reader["WorkflowActionName"]) ? string.Empty : Convert.ToString(reader["WorkflowActionName"]),
                                StartTime = Convert.IsDBNull(reader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["StartTime"]),
                                EndTime = Convert.IsDBNull(reader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["EndTime"]),
                                //Status = (WorkflowActionStatus)Enum.Parse(typeof (WorkflowActionStatus), reader[3].ToString(), true),
                                Status = Convert.IsDBNull(reader["Status"]) ? WorkflowActionStatus.Undefined : (WorkflowActionStatus)Enum.Parse(typeof(WorkflowActionStatus), Convert.ToString(reader["Status"]), true),
                                InfraobjectName = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]),
                                InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]),
                                //Id=Convert.IsDBNull(reader["WorkflowActionResultId"]) ? 0 : Convert.ToInt32(reader["WorkflowActionResultId"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetParallelDrOperationSummarydata(" +
                    id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Delete <see cref="ParallelWorkflowActionResult" />from bcms_parallel_workflowactionresult table by id.
        /// </summary>
        /// <param name="id"> Pass id</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool IParallelWorkflowActionResultDataAccess.DeleteById(int id)
        {
            try
            {
                const string sp = "ParallelWorkflowActionResult_Delete";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ParallelWorkflowActionResult.DeleteById()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelWorkflowActionResult" />from bcms_parallel_workflowactionresult table by groupid.
        /// </summary>
        /// <param name="groupId"> Pass groupid</param>
        /// <returns>ParallelWorkflowActionResult</returns>
        /// <author>Jeyapandi</author>
        ParallelWorkflowActionResult IParallelWorkflowActionResultDataAccess.GetExecutionTimeByInfraObjectId(int infraobjectId)
        {
            try
            {
                if (infraobjectId < 1)
                {
                    throw new ArgumentNullException("infraobjectId");
                }
                const string sp = "ParallelWFAction_GetRunInfra";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, infraobjectId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            //return (CreateEntityBuilder<ParallelWorkflowActionResult>()).BuildEntity(reader, new ParallelWorkflowActionResult());
                            var obj = new ParallelWorkflowActionResult();

                            //Convert.IsDBNull(reader["Message"]) ? string.Empty : Convert.ToString(reader["Message"]);
                            obj.WorkflowExecutionTime = Convert.IsDBNull(reader["EndTime"]) ? string.Empty : Convert.ToString(reader["EndTime"]); ;
                            obj.ParallelDROperationId = Convert.IsDBNull(reader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(reader["ParallelDROperationId"]);
                            obj.ParallelGroupWorkflowId = Convert.IsDBNull(reader["ParallelGroupWorkflowId"]) ? 0 : Convert.ToInt32(reader["ParallelGroupWorkflowId"]);
                            obj.InfraObjectId = Convert.IsDBNull(reader["InfraobjectId"]) ? 0 : Convert.ToInt32(reader["InfraobjectId"]);
                            obj.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);

                            return obj;
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetExecutionTimeByInfraObjectId(" +
                    infraobjectId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        /// <summary>
        /// Update conditional operation in parallel_workflowaActionResult table.
        /// </summary>
        /// <param name="parallelGrpId">parallelGrpId</param>
        /// <param name="InfraobjectId">InfraobjectId</param>
        /// <param name="currentActionId">currentActionId</param>
        /// <param name="conditional">conditional</param>
        /// <returns>True/false</returns>
        /// <author>Ram Mahajan-28/04/2015</author>
        bool IParallelWorkflowActionResultDataAccess.UpdateWFResultConditionalByPGrpIdAndInfraId(int parallelGrpId, int InfraobjectId, int currentActionId, int conditional)
        {
            try
            {
                if (parallelGrpId < 1)
                {
                    throw new ArgumentNullException("id");
                }

                string sp = DbRoleName + "PARALLELWRESULTCONDI_UTBYPIAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, parallelGrpId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iactionid", DbType.Int32, currentActionId);
                    Database.AddInParameter(cmd, Dbstring + "iConditionalAction", DbType.Int32, conditional);
                    //We are just updating record and not selecting/returning any record so commented following lines for oracle                    
                    //#if ORACLE
                    //                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
                    //#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

# if ORACLE
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.UpdateWFResultConditionalByPGrpIdAndInfraId(" +
                    parallelGrpId + " " + InfraobjectId + " " + currentActionId + "" + conditional + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        /// <summary>
        /// Get parallelGroupWorkFlow by ParallelDrOperationId
        /// </summary>
        /// <param name="parallelDrOperationId">parallelDrOperationId</param>
        /// <returns>List of ParallelWorkflowActionResult</returns>
        /// <author>Ram Mahajan-28/04/2015</author>
        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetParalellWFByDrOpId(int parallelDrOperationId)
        {
            try
            {
                string sp = DbRoleName + "GRPWFACTRES_GETBYPARDRID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iparallelDrOperationId", DbType.Int32, parallelDrOperationId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature GetParalellWFResultByDrOpId" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        /// <summary>
        /// Update ConditionalOperation in parallel workflow action result table
        /// </summary>
        /// <param name="id">id</param>
        /// <param name="conditoinalAction">conditoinalAction</param>
        /// <returns>True/False</returns>
        /// <author>Ram Mahajan-28/04/2015</author>
        bool IParallelWorkflowActionResultDataAccess.UpdateWfResultConditionalOperation(int id, int conditoinalAction)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                string sp = DbRoleName + "ParallelWfResultCondi_UpdtById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iConditionalAction", DbType.Int32, conditoinalAction);
                    //We are just updating record and not selecting/returning any record so commented following lines for oracle 
                    //#if ORACLE
                    //                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
                    //#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating UpdateWfResultConditionalOperation Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        /// Get parallelworkflow result
        /// </summary>
        /// <param name="parallelDrOperationId">parallelDrOperationId</param>
        /// <param name="parallelGroupId">parallelGroupId</param>
        /// <param name="infraobjectId">infraobjectId</param>
        /// <returns>List of ParallelWorkflowActionResult</returns>
        /// <author>Ram mahajan-28/04/2015</author>
        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetParalellWFResultByDrOpId(int parallelDrOperationId, int parallelGroupId, int infraobjectId)
        {
            try
            {
                string sp = DbRoleName + "PARALLWFRESULT_GETBYDrOprId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iparallelDrOperationId", DbType.Int32, parallelDrOperationId);
                    Database.AddInParameter(cmd, Dbstring + "iparallelGroupId", DbType.Int32, parallelGroupId);
                    Database.AddInParameter(cmd, Dbstring + "iinfraobjectId", DbType.Int32, infraobjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature GetParalellWFResultByDrOpId" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        //        bool IParallelWorkflowActionResultDataAccess.UpdateWFResultConditionalByPGrpIdAndInfraId(int parallelGrpId, int InfraobjectId, int currentActionId, int conditional)
        //        {
        //            try
        //            {
        //                if (parallelGrpId < 1)
        //                {
        //                    throw new ArgumentNullException("id");
        //                }

        //                string sp = DbRoleName + "PARALLELWRESULTCONDI_UTBYPIAID";

        //                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //                {
        //                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, parallelGrpId);
        //                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraobjectId);
        //                    Database.AddInParameter(cmd, Dbstring + "iactionid", DbType.Int32, currentActionId);
        //                    Database.AddInParameter(cmd, Dbstring + "iConditionalAction", DbType.Int32, conditional);
        //#if ORACLE
        //                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
        //#endif
        //                    int returnCode = Database.ExecuteNonQuery(cmd);

        //# if ORACLE
        //                    return returnCode > 0;
        //#endif
        //                    return returnCode > 0;
        //                }
        //            }
        //            catch (Exception exc)
        //            {
        //                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
        //                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
        //                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.UpdateWFResultConditionalByPGrpIdAndInfraId(" +
        //                    parallelGrpId + " " + InfraobjectId + " " + currentActionId + "" + conditional + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
        //            }
        //        }

        //        bool IParallelWorkflowActionResultDataAccess.UpdateWfResultConditionalOperation(int id, int conditoinalAction)
        //        {
        //            try
        //            {
        //                if (id < 1)
        //                {
        //                    throw new ArgumentNullException("id");
        //                }

        //                string sp = DbRoleName + "ParallelWfResultCondi_UpdtById";

        //                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //                {
        //                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
        //                    Database.AddInParameter(cmd, Dbstring + "iConditionalAction", DbType.Int32, conditoinalAction);
        //#if ORACLE
        //                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
        //#endif
        //                    int returnCode = Database.ExecuteNonQuery(cmd);
        //#if ORACLE
        //                    return returnCode < 0;
        //#endif
        //                    return returnCode > 0;
        //                }
        //            }
        //            catch (Exception exc)
        //            {
        //                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
        //                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
        //                    "Error In DAL While Updating UpdateWfResultConditionalOperation Entry : " + Environment.NewLine +
        //                    "SYSTEM MESSAGE : " + exc.Message, exc);
        //            }
        //        }


        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetDrillCount(int infraId, string ActionIds)
        {
            try
            {
                string sp = DbRoleName + "GETDRILLINFRA";
                int count = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, infraId);
                    Database.AddInParameter(cmd, Dbstring + "iIds", DbType.String, ActionIds);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature GetDrillCount" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        string IParallelWorkflowActionResultDataAccess.GetInfraAction(int infraId)
        {
            string ids = string.Empty;
            try
            {
                string sp = DbRoleName + "GETACTIONIDS_BYINFRAID";
                int count = 0;

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, infraId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    var myScalar = Database.ExecuteScalar(cmd);
                    ids = Convert.ToString(myScalar);
                }

            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature GetDrillCount" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
            return ids;
        }


        bool IParallelWorkflowActionResultDataAccess.UpdateWorkflowActionResultRelodById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                string sp = DbRoleName + "PARALLELWFRELOAD_UPDTBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    // Database.AddInParameter(cmd, Dbstring + "iConditionalAction", DbType.Int32, conditoinalAction);
                    //We are just updating record and not selecting/returning any record so commented following lines for oracle 
                    //#if ORACLE
                    //                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
                    //#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);
#if ORACLE
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Updating UpdateWfResultConditionalOperation Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IParallelWorkflowActionResultDataAccess.UpdateWorkflowActionResultConditionalByPGrpIdAndInfraIdRetryId(int parallelGrpId, int InfraobjectId, int currentActionId)
        {
            try
            {
                if (parallelGrpId < 1)
                {
                    throw new ArgumentNullException("id");
                }

                string sp = DbRoleName + "PARALLELWRESULTCONDI_UTBYRETID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, parallelGrpId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iactionid", DbType.Int32, currentActionId);

                    //  Database.AddInParameter(cmd, Dbstring + "iConditionalAction", DbType.Int32, conditional);
                    //We are just updating record and not selecting/returning any record so commented following lines for oracle                    
                    //#if ORACLE
                    //                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
                    //#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

# if ORACLE
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.UpdateWFResultConditionalByPGrpIdAndInfraId(" +
                    parallelGrpId + " " + InfraobjectId + " " + currentActionId + "" + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        ParallelWorkflowActionResult IParallelWorkflowActionResultDataAccess.GetActionResultByInfra(int infraId, string actionIds)
        {
            ParallelWorkflowActionResult parallelResult = null;
            try
            {
                if (infraId < 1)
                {
                    throw new ArgumentNullException("infraId");
                }

                const string sp = "GETDRILLINFRA";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, infraId);
                    Database.AddInParameter(cmd, Dbstring + "iIds", DbType.String, actionIds);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    //using (IDataReader reader = Database.ExecuteReader(cmd))
                    //{
                    //    if (reader.Read())
                    //    {
                    //        return (CreateEntityBuilder<ParallelWorkflowActionResult>()).BuildEntity(reader,
                    //            new ParallelWorkflowActionResult());
                    //    }
                    //    return null;
                    //}

                    using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                    {

                        while (myParallelWorkflowReader.Read())
                        {
                            parallelResult = new ParallelWorkflowActionResult();

                            parallelResult.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                            parallelResult.WorkflowActionName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowActionName"]);
                            parallelResult.StartTime = Convert.IsDBNull(myParallelWorkflowReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelWorkflowReader["StartTime"]);
                            parallelResult.EndTime = Convert.IsDBNull(myParallelWorkflowReader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelWorkflowReader["EndTime"]);
                            //parallelResult.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);

                            parallelResult.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"])
                ? WorkflowActionStatus.Undefined
                : (WorkflowActionStatus)
                    Enum.Parse(typeof(WorkflowActionStatus), Convert.ToString(myParallelWorkflowReader["Status"]), true);

                            parallelResult.ParallelGroupWorkflowId = Convert.IsDBNull(myParallelWorkflowReader["ParallelGroupWorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelGroupWorkflowId"]);

                            //parallelResult.ParallelGroupWorkflowId = ParallelGroupWorkflowDataAccess.GetById(parallelResult.ParallelGroupWorkflowId).WorkflowId;


                            parallelResult.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                            parallelResult.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraObjectId"]);
                            parallelResult.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                            parallelResult.StartTime = Convert.IsDBNull(myParallelWorkflowReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelWorkflowReader["StartTime"]);
                            parallelResult.ActionId = Convert.IsDBNull(myParallelWorkflowReader["ActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ActionId"]);
                            parallelResult.ConditionActionId = Convert.IsDBNull(myParallelWorkflowReader["ConditionActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionActionId"]);
                            if (!Convert.IsDBNull(myParallelWorkflowReader["SkipStep"]))
                                parallelResult.SkipStep = Convert.ToBoolean(myParallelWorkflowReader["SkipStep"]);
                            parallelResult.Direction = Convert.IsDBNull(myParallelWorkflowReader["Direction"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Direction"]);

                            //parallelResult.ParallelGroupWorkflowId = ParallelGroupWorkflowDataAccess.GetById(parallelResult.ParallelGroupWorkflowId).WorkflowId;
                            parallelResult.StartTime = Convert.IsDBNull(myParallelWorkflowReader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(myParallelWorkflowReader["StartTime"]);

                        }
                        return parallelResult;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetActionResultByInfra(" +
                    infraId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        //neelima

        //        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetParallelDrOperationSummarydataByInfraID(
        //            int id)
        //        {
        //            try
        //            {
        //                const string sp = "PARALLDROPERAT_GETSUMMARYDATA_New";

        //                IList<ParallelWorkflowActionResult> objprl = new List<ParallelWorkflowActionResult>();
        //                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //                {
        //                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, id);

        //#if ORACLE
        //                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
        //#endif

        //                    using (IDataReader reader = Database.ExecuteReader(cmd))
        //                    {
        //                        while (reader.Read())
        //                        {
        //                            var obj = new ParallelWorkflowActionResult
        //                            {
        //                                WorkflowActionName = Convert.IsDBNull(reader["WorkflowActionName"]) ? string.Empty : Convert.ToString(reader["WorkflowActionName"]),
        //                                StartTime = Convert.IsDBNull(reader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["StartTime"]),
        //                                EndTime = Convert.IsDBNull(reader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["EndTime"]),
        //                                //Status = (WorkflowActionStatus)Enum.Parse(typeof (WorkflowActionStatus), reader[3].ToString(), true),
        //                                Status = Convert.IsDBNull(reader["Status"]) ? WorkflowActionStatus.Undefined : (WorkflowActionStatus)Enum.Parse(typeof(WorkflowActionStatus), Convert.ToString(reader["Status"]), true),
        //                                InfraobjectName = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]),
        //                                InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"])
        //                            };
        //                            objprl.Add(obj);
        //                        }
        //                        return objprl;
        //                    }
        //                }
        //            }
        //            catch (Exception exc)
        //            {
        //                throw new CpException(CpExceptionType.DataAccessFetchOperation,
        //                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
        //                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetParallelDrOperationSummarydataByInfraID(" +
        //                    id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
        //            }
        //        }



        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetParallelDrOperationdataBYInfraWid(int id,
           int wid)
        {
            try
            {
                const string sp = "PARALLELDOPRR_GETDATABYID_New";

                IList<ParallelWorkflowActionResult> objprl = new List<ParallelWorkflowActionResult>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "pdrId", DbType.Int32, wid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new ParallelWorkflowActionResult
                            {
                                WorkflowActionName = Convert.IsDBNull(reader["WorkflowActionName"])
                ? string.Empty
                : Convert.ToString(reader["WorkflowActionName"]),
                                StartTime = Convert.IsDBNull(reader["StartTime"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["StartTime"]),
                                EndTime = Convert.IsDBNull(reader["EndTime"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["EndTime"]),
                                //Status = (WorkflowActionStatus)Enum.Parse(typeof (WorkflowActionStatus), reader[3].ToString(), true),
                                Status = Convert.IsDBNull(reader["Status"])
                  ? WorkflowActionStatus.Undefined
                  : (WorkflowActionStatus)
                      Enum.Parse(typeof(WorkflowActionStatus), Convert.ToString(reader["Status"]), true),
                                InfraobjectName = Convert.IsDBNull(reader["InfraobjectName"])
                ? string.Empty
                : Convert.ToString(reader["InfraobjectName"]),
                                InfraObjectId = Convert.IsDBNull(reader["InfraobjectId"])
                ? 0
                : Convert.ToInt32(reader["InfraobjectId"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetParallelDrOperationdataBYInfraWid(" +
                    id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        bool IParallelWorkflowActionResultDataAccess.UpdateWorkflowActionResultStausById(int Id, String Status)
        {
            try
            {
                string sp = DbRoleName + "PARALLELWRESULTSTATUS_UTBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, Id);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, Status);

                    //#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

# if ORACLE
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.UpdateWorkflowActionResultStausById(" +
                    Id + " " + Status + "" + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public bool UpdateWorkflowActionResultStatusById(int id, string Status)
        {
            try
            {
                string sp = DbRoleName + "PARALLELWFRESULT_STATUSBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, Status);

                    //#endif
                    int returnCode = Database.ExecuteNonQuery(cmd);

# if ORACLE
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageUpdatedata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.UpdateWorkflowActionResultStausById(" +
                    id + " " + Status + "" + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetParallelWFResultData()
        {
            try
            {
                const string sp = "GETPARALLELWFRESLISTDATA";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetParallelWFResultData()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetDrillDateSummaryByDate(DateTime dt)
        {
            IList<ParallelWorkflowActionResult> objprl = new List<ParallelWorkflowActionResult>();
            try
            {
                //if (dt < 1)
                //{
                //    throw new ArgumentNullException("bsid");
                //}
                const string sp = "GETWORKFLOWBYDATE";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iDateTime", DbType.Date, dt);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //if (reader.Read())
                        //{
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                        //}
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetDrillDateSummaryByDate(" +
                    dt + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetAllRunning()
        {
            try
            {
                const string sp = "PARALLELWFACTIONRESULT_GETALLRunning";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetAllRunning()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetAllRunning_ParallelWorkflowActionResultByUserId(int UserId)
        {
            try
            {
                const string sp = "PARALLELWFACTIONRESULT_GETALLRunningByUserId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, UserId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetAllRunning()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        IList<ParallelWorkflowActionResult> IParallelWorkflowActionResultDataAccess.GetAllRunning_ParallelWorkflowActionResultByUserId_New(int UserId, int Loggedinuser)
        {
            try
            {
                const string sp = "PARALLELWFACTIONRESULT_GETALLRunningByUserId_New";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, UserId);
                    Database.AddInParameter(cmd, Dbstring + "iLoggedinuser", DbType.Int32, Loggedinuser);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowActionResultDataAccess.GetAllRunning()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        ParallelWorkflowActionResult IParallelWorkflowActionResultDataAccess.ChangeParallelWorkflowActionResult(ParallelWorkflowActionResult parallelresult)
        {
            try
            {
                parallelresult.CreatorId = 1;
                const string sp = "PARALLELWFACTIONRESULT_CHANGE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowActionName", DbType.AnsiString, parallelresult.WorkflowActionName);
                    Database.AddInParameter(cmd, Dbstring + "iStartTime", DbType.DateTime, parallelresult.StartTime);
                    Database.AddInParameter(cmd, Dbstring + "iEndTime", DbType.DateTime, parallelresult.EndTime);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, parallelresult.Status);
                    Database.AddInParameter(cmd, Dbstring + "iParallelDROperationId", DbType.Int32, parallelresult.ParallelDROperationId);
                    Database.AddInParameter(cmd, Dbstring + "iParallelGroupWorkflowId", DbType.Int32, parallelresult.ParallelGroupWorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, parallelresult.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, parallelresult.Message);
                    Database.AddInParameter(cmd, Dbstring + "iActionId", DbType.Int32, parallelresult.ActionId);
                    Database.AddInParameter(cmd, Dbstring + "iConditionActionId", DbType.Int32, parallelresult.ConditionActionId);
                    Database.AddInParameter(cmd, Dbstring + "iSkipStep", DbType.Boolean, parallelresult.SkipStep);
                    Database.AddInParameter(cmd, Dbstring + "iDirection", DbType.AnsiString, parallelresult.Direction);
                    //Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, parallelresult.CreatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        parallelresult = reader.Read()
                            ? CreateEntityBuilder<ParallelWorkflowActionResult>().BuildEntity(reader, parallelresult)
                            : null;
                    }

                    if (parallelresult == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "GlobalMirror already exists. Please specify another globalMirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this globalMirror.");
                                }
                        }
                    }

                    return parallelresult;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting ParallelWorkflowActionResult Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        #endregion Methods
    }
}