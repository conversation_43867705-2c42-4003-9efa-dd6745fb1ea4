﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    public interface IRoboCopyJobDataAccess
    {
        IList<RoboCopyJob> GetAllRoboCopyJob();

        RoboCopyJob AddRoboCopyyjob(RoboCopyJob robocopyjob);

        RoboCopyJob UpdateRobocopyyjob(RoboCopyJob robocopyjob);

        IList<RoboCopyJob> GetRoboCopyJobByRoboCopyyId(int id);

        RoboCopyJob GetRoboCopyJobByyId(int id);

        bool DeleteRoboCopyJobByyId(int id);

        IList<RoboCopyJob> GetRoboCopyJobByRoboCopyOptionsId(int id);
    }
}
