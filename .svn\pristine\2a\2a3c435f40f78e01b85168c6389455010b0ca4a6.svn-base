﻿using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.Shared;
using CP.BusinessFacade;

namespace CP.UI.Component
{
    public partial class Postgre9xMonitoring : GroupBasePage
    {
        private static BusinessFunction _businessFunctionObj = null;
        private static IFacade _facade = new Facade();

        public override void PrepareView()
        {
            var getDbVersion = Facade.GetDatabaseBaseById(CurrentGroup.PRDatabaseId);
            string version = getDbVersion.Version;
            if (version != string.Empty)
            {
                if (version == "10.4")
                {
                    lblCurrentLogFileNm.Text = "Current WAL log file name";
                    lblCurrentLogLocationnm.Text = "Current WAL log location";
                    lblLogReceiverFileNm.Text = "WAL log receive file name";
                    lblLastLogReceiverLoc.Text = "Last WAL log receive location";
                    lblLogReplayFileNm.Text = "WAL log replay file name";
                    lblLastLogReplayLoc.Text = "Last WAL log replay location";
                    reCurrentLogFile.Visible = false;
                    trLogRecieverFile.Visible = false;
                    trLogReplayFileNm.Visible = false;
                }
                else
                {
                    lblCurrentLogFileNm.Text = "Current xlog file name";
                    lblCurrentLogLocationnm.Text = "Current xlog location";
                    lblLogReceiverFileNm.Text = "xlog receive file name";
                    lblLastLogReceiverLoc.Text = "Last xlog receive location";
                    lblLogReplayFileNm.Text = "xlog replay file name";
                    lblLastLogReplayLoc.Text = "Last xlog replay location";
                    reCurrentLogFile.Visible = true;
                    trLogRecieverFile.Visible = true;
                    trLogReplayFileNm.Visible = true;
                }
            }

            Utility.SelectMenu(Master, "Module1");
            try
            {

                if (CurrentGroupId > 0)
                {
                    var PRServer = (CurrentGroup.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted) ? Facade.GetServerById(CurrentGroup.DRServerId) : Facade.GetServerById(CurrentGroup.PRServerId);
                    var DRServer = (CurrentGroup.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted) ? Facade.GetServerById(CurrentGroup.PRServerId) : Facade.GetServerById(CurrentGroup.DRServerId);
                    lblPRIPAddress.Text = CryptographyHelper.Md5Decrypt(PRServer.IPAddress);
                    lblDRIPAddress.Text = CryptographyHelper.Md5Decrypt(DRServer.IPAddress);
                    var postgredbmonitor = Facade.Postgre9xComponentMonitorByInfraId(CurrentGroupId);
                    var postgrerepl = Facade.Postgre9xMonitorStatusByInfraId(CurrentGroupId);

                    if (postgredbmonitor != null)
                        PostgredbMomintor(postgredbmonitor);
                    if (postgrerepl != null)
                        PostgreReplication(postgrerepl);
                }
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.DataAccessFetchOperation, ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                      "Error In DAL While Fetching Postgre Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
                ExceptionManager.Manage(bcms);
            }
        }


        private void PostgredbMomintor(Postgre9xComponentMonitor postgremon)
        {
            lblPRDatabaseVersion.Text = string.IsNullOrEmpty(postgremon.DBVersionPR) ? "N/A" : postgremon.DBVersionPR;
            lblDRDatabaseVersion.Text = string.IsNullOrEmpty(postgremon.DBVersionDR) ? "N/A" : postgremon.DBVersionDR;
            lblPRDatabaseVersionIcon.CssClass = string.IsNullOrEmpty(postgremon.DBVersionPR) ? "icon-NA" : "icon-database";
            lblDRDatabaseVersionIcon.CssClass = string.IsNullOrEmpty(postgremon.DBVersionDR) ? "icon-NA" : "icon-database";

            lblprdatabaseServiceStatus.Text = string.IsNullOrEmpty(postgremon.DBServiceStatusPR) ? "N/A" : postgremon.DBServiceStatusPR;
            lbldrdatabaseServiceStatus.Text = string.IsNullOrEmpty(postgremon.DBServiceStatusDR) ? "N/A" : postgremon.DBServiceStatusDR;

            if (lblprdatabaseServiceStatus.Text != "Running")
            {
                lblprdatabaseServiceStatus.ForeColor = System.Drawing.Color.Red;
            }

            if (lbldrdatabaseServiceStatus.Text != "Running")
            {
                lbldrdatabaseServiceStatus.ForeColor = System.Drawing.Color.Red;
            }

            lblprdatabaseServiceStatusIcon.CssClass = string.IsNullOrEmpty(postgremon.DBServiceStatusPR) || postgremon.DBServiceStatusPR == "N/A" ? "icon-NA" : postgremon.DBServiceStatusPR == "Running" ? "Replicating" : "InActive";
            lbldrdatabaseServiceStatusIcon.CssClass = string.IsNullOrEmpty(postgremon.DBServiceStatusDR) || postgremon.DBServiceStatusDR == "N/A" ? "icon-NA" : postgremon.DBServiceStatusDR == "Running" ? "Replicating" : "InActive";

            lblprDBClusterState.Text = string.IsNullOrEmpty(postgremon.DBClusterStatusPR) ? "N/A" : postgremon.DBClusterStatusPR;
            lbldrDBClusterState.Text = string.IsNullOrEmpty(postgremon.DBClusterStatusDR) ? "N/A" : postgremon.DBClusterStatusDR;
            lblprDBClusterStateIcon.CssClass = string.IsNullOrEmpty(postgremon.DBClusterStatusPR) ? "icon-NA" : "icon-dbupdate";
            lbldrDBClusterStateIcon.CssClass = string.IsNullOrEmpty(postgremon.DBClusterStatusDR) ? "icon-NA" : "icon-dbupdate";

            lblDRDatabaseRecoveryStatus.Text = string.IsNullOrEmpty(postgremon.DBRecoveryStatusDR) ? "N/A" : postgremon.DBRecoveryStatusDR;
            spnPRDatabaseRecoveryStatus.Attributes["class"] = "icon-NA";

            if (lblPRDatabaseRecoveryStatus.Text != "Running")
            {
                lblPRDatabaseRecoveryStatus.ForeColor = System.Drawing.Color.Red;
            }

            if (lblDRDatabaseRecoveryStatus.Text != "Running")
            {
                lblDRDatabaseRecoveryStatus.ForeColor = System.Drawing.Color.Red;
            }

            spnDRDatabaseRecoveryStatus.CssClass = string.IsNullOrEmpty(postgremon.DBRecoveryStatusDR) ? "icon-NA" : postgremon.DBRecoveryStatusDR == "Running" ? "Replicating" : "pause";
            lblDRDatabaseRecoveryStatus.CssClass = "text-success";
        }

        private void PostgreReplication(Postgre9xMonitorStatus postrep)
        {
            lblPRDatabaseRecoveryStatus.Text = "N/A";
            

            lblPRDatabaseRecoverypath.Text = string.IsNullOrEmpty(postrep.DataDirectoryPathPR) ? "N/A" : postrep.DataDirectoryPathPR;
            lblDRDatabaseRecoverypath.Text = string.IsNullOrEmpty(postrep.DataDirectoryPathDR) ? "N/A" : postrep.DataDirectoryPathDR;

            spnPRDatabaseRecoverypath.CssClass = string.IsNullOrEmpty(postrep.DataDirectoryPathPR) ? "icon-NA" : "icon-log";
            spnDRDatabaseRecoverypath.CssClass = string.IsNullOrEmpty(postrep.DataDirectoryPathDR) ? "icon-NA" : "icon-log";
            

            lblCurrentxloglocation.Text = string.IsNullOrEmpty(postrep.Current_xlog_location) ? "N/A" : postrep.Current_xlog_location;
            lblLastxlogreceivelocation.Text = string.IsNullOrEmpty(postrep.Last_xlog_receive_location) ? "N/A" : postrep.Last_xlog_receive_location;
            lblLastxlogreplaylocation.Text = string.IsNullOrEmpty(postrep.Last_xlog_replay_location) ? "N/A" : postrep.Last_xlog_replay_location;
            lblPostgres9xDataLagMB.Text = string.IsNullOrEmpty(postrep.DataLag_MB) ? "N/A" : postrep.DataLag_MB;
            lblPostgres9xDataLagHR.Text = string.IsNullOrEmpty(postrep.DataLag_HHMMSS) ? "N/A" : postrep.DataLag_HHMMSS;
            lblPRReplicationStatus.Text = string.IsNullOrEmpty(postrep.ReplicationStatusPR) ? "N/A" : postrep.ReplicationStatusPR;
            lblDRReplicationStatus.Text = string.IsNullOrEmpty(postrep.ReplicationStatusDR) ? "N/A" : postrep.ReplicationStatusDR;
            lblCurrentXlogFileName.Text = string.IsNullOrEmpty(postrep.CurrentXlogFileName) ? "N/A" : postrep.CurrentXlogFileName;
            lblXlogreceiveFileName.Text = string.IsNullOrEmpty(postrep.XlogReceiveFileName) ? "N/A" : postrep.XlogReceiveFileName;
            lblXlogreplayfilename.Text = string.IsNullOrEmpty(postrep.XlogReplayFileName) ? "N/A" : postrep.XlogReplayFileName;

            spnCurrentxloglocation.CssClass = string.IsNullOrEmpty(postrep.Current_xlog_location) ? "icon-NA" : "icon-numbering";
            spnLastxlogreceivelocation.CssClass = string.IsNullOrEmpty(postrep.Last_xlog_receive_location) ? "icon-NA" : "icon-numbering";
            spnLastxlogreplaylocation.CssClass = string.IsNullOrEmpty(postrep.Last_xlog_replay_location) ? "icon-NA" : "icon-numbering";
            spnPostgres9xDataLagMB.CssClass = string.IsNullOrEmpty(postrep.DataLag_MB) ? "icon-NA" : "icon-disk-symmetrix";
            
            lblPostgres9xDataLagHR.CssClass = "";
            _businessFunctionObj = _facade.GetBusinessFunctionByInfraObjectId(postrep.InfraObjectId);

            if (!postrep.DataLag_HHMMSS.Contains("N/A") && _businessFunctionObj != null)
            {
                //Configure timespan format
                TimeSpan _tsconfigDatalag;
                bool isValidconfigTimeSpanformat = Utility.IsTimeSpanFormat(_businessFunctionObj.ConfiguredRPO);
                if (isValidconfigTimeSpanformat)
                    _tsconfigDatalag = TimeSpan.Parse(_businessFunctionObj.ConfiguredRPO);
                else
                    _tsconfigDatalag = TimeSpan.FromSeconds(Convert.ToDouble(_businessFunctionObj.ConfiguredRPO));

                //Current timespan format
                TimeSpan _tscurrentDatalag;
                bool isValidcurrentTimeSpanformat = Utility.IsTimeSpanFormat(postrep.DataLag_HHMMSS);
                if (isValidcurrentTimeSpanformat)
                    _tscurrentDatalag = TimeSpan.Parse(postrep.DataLag_HHMMSS);
                else
                    _tscurrentDatalag = TimeSpan.FromSeconds(Convert.ToDouble(postrep.DataLag_HHMMSS));

                spnPostgres9xDataLagHR.CssClass = _tscurrentDatalag <= _tsconfigDatalag ? "icon-Time" : "icon-datalag-down";
                lblPostgres9xDataLagHR.CssClass = _tscurrentDatalag <= _tsconfigDatalag ? "" : "inactive";
            }
            
            //spnPostgres9xDataLagHR.CssClass = string.IsNullOrEmpty(postrep.DataLag_HHMMSS) ? "icon-NA" : "icon-Time";
            spnPRReplicationStatus.CssClass = string.IsNullOrEmpty(postrep.ReplicationStatusPR) || postrep.ReplicationStatusPR == "N/A" ? "icon-NA" : postrep.ReplicationStatusPR == "Running" ? "Replicating" : "icon-sync";
            spnDRReplicationStatus.CssClass = string.IsNullOrEmpty(postrep.ReplicationStatusDR) || postrep.ReplicationStatusDR == "N/A"  ? "icon-NA" : postrep.ReplicationStatusDR == "Running" ? "Replicating" : "icon-sync";
            lblCurrentxlogFileNameIcon.CssClass = string.IsNullOrEmpty(postrep.CurrentXlogFileName) ? "icon-NA" : "icon-log";
            lblXlogreceiveFileNameIcon.CssClass = string.IsNullOrEmpty(postrep.XlogReceiveFileName) ? "icon-NA" : "icon-log";
            lblXlogreplayfilenameIcon.CssClass = string.IsNullOrEmpty(postrep.XlogReplayFileName) ? "icon-NA" : "icon-log";
        }
    }
}