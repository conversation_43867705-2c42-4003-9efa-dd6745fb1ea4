﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
    internal sealed class ServerBuilder : IEntityBuilder<Server>
    {
        IList<Server> IEntityBuilder<Server>.BuildEntities(IDataReader reader)
        {
            var servers = new List<Server>();

            while (reader.Read())
            {
                servers.Add(((IEntityBuilder<Server>)this).BuildEntity(reader, new Server()));
            }

            return (servers.Count > 0) ? servers : null;
        }

        Server IEntityBuilder<Server>.BuildEntity(IDataReader reader, Server server)
        {
            //Fields in bcms_server table on 16/07/2013 : Id, Name, SiteId, Type, IPAddress, SSHUserName, SSHPassword, EnableSudoAccess, SudoUser, SudoPassword, DSIPAddress, DSSSHUserName, DSSSHPassword, OSType, IsPartOfCluster, Status, IsActive, CreatorId, CreateDate, UpdatorId, UpdateDate
            server.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"].ToString());
            server.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"].ToString());
            server.SiteId = Convert.IsDBNull(reader["SiteId"]) ? 0 : Convert.ToInt32(reader["SiteId"].ToString());
            server.Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"].ToString().Trim());
            server.IPAddress = Convert.IsDBNull(reader["IPAddress"]) ? string.Empty : Convert.ToString(reader["IPAddress"]);
            server.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"].ToString());

            server.SSHUserName = Convert.IsDBNull(reader["SSHUserName"]) ? string.Empty : Convert.ToString(reader["SSHUserName"]);
            server.SSHPassword = Convert.IsDBNull(reader["SSHPassword"]) ? string.Empty : Convert.ToString(reader["SSHPassword"]);

            if (!Convert.IsDBNull(reader["EnableSudoAccess"]))
                //server.EnableSudoAccess = Convert.ToBoolean(reader["EnableSudoAccess"].ToString());
                server.EnableSudoAccess = Convert.IsDBNull(reader["EnableSudoAccess"].ToString())
                    ? 0
                    : Convert.ToInt32(reader["EnableSudoAccess"]);

            server.SudoUser = Convert.IsDBNull(reader["SudoUser"]) ? string.Empty : reader["SudoUser"].ToString();
            server.SudoPassword = Convert.IsDBNull(reader["SudoPassword"])
                ? string.Empty
                : reader["SudoPassword"].ToString();
            server.DSIPAddress = Convert.IsDBNull(reader["DSIPAddress"])
                ? string.Empty
                : reader["DSIPAddress"].ToString();
            server.DSSSHUserName = Convert.IsDBNull(reader["DSSSHUserName"])
                ? string.Empty
                : reader["DSSSHUserName"].ToString();
            server.DSSSHPassword = Convert.IsDBNull(reader["DSSSHPassword"])
                ? string.Empty
                : reader["DSSSHPassword"].ToString();
            server.OSType = Convert.IsDBNull(reader["OSType"]) ? string.Empty : reader["OSType"].ToString();

            if (!Convert.IsDBNull(reader["IsPartOfCluster"]))
                server.IsPartOfCluster = Convert.ToBoolean(reader["IsPartOfCluster"]);

            server.Status = Convert.IsDBNull(reader["Status"])
                ? ServerStatus.Undefined
                : (ServerStatus)Enum.Parse(typeof(ServerStatus), reader["Status"].ToString(), true);
            server.DataStoreName = Convert.IsDBNull(reader["DataStoreName"])
                ? string.Empty
                : reader["DataStoreName"].ToString();
            server.VmPath = Convert.IsDBNull(reader["VmPath"]) ? string.Empty : reader["VmPath"].ToString();
            server.Disk = Convert.IsDBNull(reader["Disk"]) ? string.Empty : reader["Disk"].ToString();

            server.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            if (!Convert.IsDBNull(reader["IsUseSshKeyAuth"]))
            {
                server.IsUseSshKeyAuthentication = Convert.ToInt32(reader["IsUseSshKeyAuth"]);
                server.SshKeyPath = Convert.IsDBNull(reader["SshKeyPath"])
                    ? string.Empty
                    : reader["SshKeyPath"].ToString();
                server.SshKeyPassword = Convert.IsDBNull(reader["SshKeyPassword"])
                    ? string.Empty
                    : reader["SshKeyPassword"].ToString();
            }
            server.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            server.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            server.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            server.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());
            server.ShellPrompt = Convert.IsDBNull(reader["Shell_Prompt"])
                ? string.Empty
                : reader["Shell_Prompt"].ToString();
            server.LicenseKey = Convert.IsDBNull(reader["LicenceKey"]) ? string.Empty : reader["LicenceKey"].ToString();

            server.IsVerified = Convert.IsDBNull(reader["ISVERIFIED"]) ? 0 : Convert.ToInt32(reader["ISVERIFIED"]);
            server.ErrorMessage = Convert.IsDBNull(reader["ERRORMESSAGE"]) ? string.Empty : reader["ERRORMESSAGE"].ToString();
            server.SSOTypeId = Convert.IsDBNull(reader["SSOTypeId"]) ? 0 : Convert.ToInt32(reader["SSOTypeId"]);
            server.SSOEnabled = Convert.IsDBNull(reader["SSOEnabled"]) ? 0 : Convert.ToInt32(reader["SSOEnabled"]);
            server.HostName = Convert.IsDBNull(reader["HostName"]) ? string.Empty : reader["HostName"].ToString();

            //server.SsoTypeID = Convert.IsDBNull(reader["SSOTYPEID"]) ? 0 : Convert.ToInt32(reader["SSOTYPEID"]);
            //server.IsSsoEnable = Convert.IsDBNull(reader["SSOENABLED"]) ? false : Convert.ToBoolean(reader["SSOENABLED"]);
            server.Safe = Convert.IsDBNull(reader["Safe"]) ? string.Empty : reader["Safe"].ToString();
            server.Object = Convert.IsDBNull(reader["Object"]) ? string.Empty : reader["Object"].ToString();
            server.Folder = Convert.IsDBNull(reader["Folder"]) ? string.Empty : reader["Folder"].ToString();
            server.Reason = Convert.IsDBNull(reader["Reason"]) ? string.Empty : reader["Reason"].ToString();
            server.SSOProfileId = Convert.IsDBNull(reader["SSOProfileId"]) ? 0 : Convert.ToInt32(reader["SSOProfileId"]);
            server.ServerRole = Convert.IsDBNull(reader["ServerRole"]) ? 0 : Convert.ToInt32(reader["ServerRole"]);
            server.IsVirtualGuestOS = Convert.IsDBNull(reader["IsVirtualGuestOS"]) ? 0 : Convert.ToInt32(reader["IsVirtualGuestOS"]);
            server.WinRMPort = Convert.IsDBNull(reader["WinRMPort"]) ? 0 : Convert.ToInt32(reader["WinRMPort"].ToString());
            server.ProxyAccessType = Convert.IsDBNull(reader["ProxyAccessType"]) ? string.Empty : reader["ProxyAccessType"].ToString();
            server.CANID = Convert.IsDBNull(reader["CANID"]) ? string.Empty : reader["CANID"].ToString();
            return server;
        }
    }
}