﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    public interface ILogViewerDataAccess
    {
        bool Add(LogView LogViewer);

        IList<LogDetails> GetAllLogDetailsByUserId(int userId);

        LogView GetLogViewerDetails(int userId);

        //bool UpdateLogProcessingIsCompleted(int userId, int Id);
        bool UpdateLogProcessingIsCompleted(int userId, int Id);

        IList<LogFiles> GetAllLogFiles();

        LogView GetNonProcessedLVDByUserId(int userId);
    }
}

