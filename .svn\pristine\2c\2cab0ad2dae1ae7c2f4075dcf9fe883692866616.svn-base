﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="DatabaseList.aspx.cs" Inherits="CP.UI.DatabaseList" Title="Continuity Patrol :: Database-DatabaseList" %>

<%@ Register Src="../Controls/DatabaseOracleList.ascx" TagName="DatabaseOracleList" TagPrefix="uc1" %>
<%@ Register Src="../Controls/DatabaseSqlList.ascx" TagName="DatabaseSqlList" TagPrefix="uc2" %>
<%@ Register Src="../Controls/DatabaseExchangeList.ascx" TagName="DatabaseExchangeList" TagPrefix="uc3" %>
<%@ Register Src="../Controls/DatabaseOracleRacList.ascx" TagName="DatabaseOracleRacList" TagPrefix="uc4" %>
<%@ Register Src="../Controls/DatabaseDb2List.ascx" TagName="DatabaseDb2List" TagPrefix="uc5" %>
<%@ Register Src="../Controls/DatabaseExchangeDAGList.ascx" TagName="DatabaseExchangeDAGList" TagPrefix="uc6" %>
<%@ Register Src="../Controls/DatabaseMySqlList.ascx" TagName="DatabaseMySqlList" TagPrefix="uc7" %>
<%@ Register Src="../Controls/DatabasePostgreSqlList.ascx" TagPrefix="uc8" TagName="PostgreSql" %>
<%@ Register Src="../Controls/DatabasePostgre9xList.ascx" TagPrefix="uc9" TagName="Postgre9x" %>
<%@ Register Src="~/Controls/DataBaseMSSql.ascx" TagPrefix="uc10" TagName="MSSql" %>
<%@ Register Src="~/Controls/DataBaseSybaseList.ascx" TagPrefix="uc11" TagName="Sybasedadb" %>
<%@ Register Src="~/Controls/MaxDBDatabaseList.ascx" TagPrefix="uc12" TagName="MaxDBdatabase" %>
<%@ Register Src="~/Controls/DataBaseSybaseWithSrsList.ascx" TagPrefix="uc13" TagName="SybaseWithSrsdb" %>
<%@ Register Src="~/Controls/MongoDBList.ascx" TagPrefix="uc1" TagName="MongoDBdatabase" %>
<%@ Register Src="../Controls/DataBaseSybaseWithRsHadrList.ascx" TagPrefix="uc15" TagName="SybaseWithRsHadrdb" %>
<%@ Register Src="~/Controls/DatabaseHanaDBList.ascx" TagPrefix="uc1" TagName="HanaDBdatabaseList" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>


    <script>
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />

    <div class="innerLR">
        <asp:HiddenField ID="hdUserType" runat="server" />
        <asp:UpdatePanel ID="UpdatePanel2" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <h3>
                    <img src="../Images/db-icon-conf.png" />
                    Database List</h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-3">
                                Database Type:
                            </div>
                            <div class="col-md-9">
                                <asp:DropDownList ID="ddldatabaseList" runat="server" AutoPostBack="True"
                                    OnSelectedIndexChanged="DdldatabaseListSelectedIndexChanged" CssClass="chosen-select" TabIndex="4">
                                    <asp:ListItem Value="1" Selected="True">Oracle</asp:ListItem>
                                    <asp:ListItem Value="2">MS-Sql</asp:ListItem>
                                    <asp:ListItem Value="3">MS-Exchange</asp:ListItem>
                                    <asp:ListItem Value="4">OracleRac</asp:ListItem>
                                    <asp:ListItem Value="5">IBM DB2</asp:ListItem>
                                    <asp:ListItem Value="6">MS-ExchangeDAG</asp:ListItem>
                                    <asp:ListItem Value="7">MySQL</asp:ListItem>
                                    <%--  <asp:ListItem Value="8">PostgreSQL</asp:ListItem>--%>
                                    <asp:ListItem Value="9">Postgres9x</asp:ListItem>
                                    <asp:ListItem Value="10">MS-SQL2kX</asp:ListItem>
                                    <asp:ListItem Value="11">SyBase</asp:ListItem>

                                    <asp:ListItem Value="12">MaxDB</asp:ListItem>
                                    <asp:ListItem Value="13">SybaseWithSRS</asp:ListItem>
                                    <asp:ListItem Value="14">MongoDB</asp:ListItem>
                                    <asp:ListItem Value="15">SybaseWithRSHADR</asp:ListItem>
                                    <asp:ListItem Value="16">HANADB</asp:ListItem>
                                </asp:DropDownList>
                            </div>
                        </div>
                        <hr />
                        <uc1:DatabaseOracleList ID="ucDatabaseOracleList" runat="server" Visible="False" />
                        <uc2:DatabaseSqlList ID="DatabaseSqlList1" runat="server" Visible="False" />
                        <uc3:DatabaseExchangeList ID="DatabaseExchangeList1" runat="server" Visible="False" />
                        <uc4:DatabaseOracleRacList ID="DatabaseOracleRacList" runat="server" Visible="False" />
                        <uc5:DatabaseDb2List ID="DatabaseDb2List1" runat="server" Visible="false" />
                        <uc6:DatabaseExchangeDAGList ID="DatabaseExchangeDAGList1" runat="server" Visible="False" />
                        <uc7:DatabaseMySqlList ID="DatabaseMySqlList1" runat="server" Visible="false" />
                        <uc8:PostgreSql ID="PostgreSql1" runat="server" Visible="false" />
                        <uc9:Postgre9x ID="Postgre9x1" runat="server" Visible="false" />
                        <uc10:MSSql ID="MSSql" runat="server" Visible="false" />
                        <uc11:Sybasedadb ID="ucSybasedadb" runat="server" Visible="false" />
                        <uc12:MaxDBdatabase ID="MaxDBdatabase" runat="server" Visible="false" />
                        <uc13:SybaseWithSrsdb ID="SybaseWithSrsdadb" runat="server" Visible="false" />
                        <uc1:MongoDBdatabase ID="MongoDBdatabase1" runat="server" Visible="false" />
                        <uc15:SybaseWithRsHadrdb ID="SybaseWithRsHadrdb1" runat="server" Visible="false" />
                        <uc1:HanaDBdatabaseList ID="ucHanaDBdatabaseList" runat="server" Visible="false" />
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
    <script>
        <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />

    </script>
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>

</asp:Content>
