﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using log4net.Util;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;
using System.Xml.Linq;

namespace CP.DataAccess
{
    internal sealed class ApprovalsProcessDataAccess : BaseDataAccess, IApprovalProcessDataAccess
    {
        #region Constructors

        public ApprovalsProcessDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ApprovalProcesss> CreateEntityBuilder<ApprovalProcesss>()
        {
            return (new ApprovalsProcessBuilder()) as IEntityBuilder<ApprovalProcesss>;
        }

        #endregion Constructors

        #region ApprovalsProcessDataAccess Members

        ApprovalProcess IApprovalProcessDataAccess.Add(ApprovalProcess approvalProcess)
        {
            try
            {
                const string sp = "ApprovalProcessCREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iProfileID", DbType.AnsiString, approvalProcess.ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iAPPROVALPROCESSNUMBER", DbType.AnsiString, approvalProcess.ApprovalProcessNumber);
                    Database.AddInParameter(cmd, Dbstring + "iPROFILEEXECUTIONSTARTTIME", DbType.DateTime, approvalProcess.ProfileExecutionStartTime);
                    Database.AddInParameter(cmd, Dbstring + "iPROFILEEXECUTIONENDTIME", DbType.DateTime, approvalProcess.ProfileExecutionEndTime);
                    Database.AddInParameter(cmd, Dbstring + "iAPPROVALSTATUS", DbType.AnsiString, approvalProcess.ApprovalStatus);
                    Database.AddInParameter(cmd, Dbstring + "iMESSAGE", DbType.AnsiString, approvalProcess.Message);
                    Database.AddInParameter(cmd, Dbstring + "iSTATE", DbType.AnsiString, approvalProcess.State);
                    Database.AddInParameter(cmd, Dbstring + "iPROFILE_OR_WORKFLOW", DbType.AnsiString, approvalProcess.PROFILE_OR_WORKFLOW);
                    Database.AddInParameter(cmd, Dbstring + "iEXECUTE_OR_MODIFY", DbType.AnsiString, approvalProcess.EXECUTE_OR_MODIFY);
                    Database.AddInParameter(cmd, Dbstring + "iISWAITINGFORAPPROVAL", DbType.Int32, approvalProcess.IsWaitingForApproval);
                    Database.AddInParameter(cmd, Dbstring + "iISALLAPPROVALRECEIVED", DbType.Int32, approvalProcess.IsAllApprovalReceived);
                    Database.AddInParameter(cmd, Dbstring + "iISALLAPPROVE", DbType.Int32, approvalProcess.IsVerified);
                    Database.AddInParameter(cmd, Dbstring + "iUserID", DbType.AnsiString, approvalProcess.CreatorId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        approvalProcess = reader.Read() ? CreateEntityBuilder<ApprovalProcess>().BuildEntity(reader, approvalProcess) : null;
                    }

                    if (approvalProcess == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this SCR.");
                                }
                        }
                    }
                    return approvalProcess;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata, "Error In DAL While Inserting approvalProcess Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public ApprovalProcess Update(Common.DatabaseEntity.ApprovalProcess approvalProcess)
        {
            try
            {
                const string sp = "ApproProcesUpdate";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, approvalProcess.Id);
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, approvalProcess.ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalProcessNumber", DbType.String, approvalProcess.ApprovalProcessNumber);
                    Database.AddInParameter(cmd, Dbstring + "iProfileExecutionStartTime", DbType.DateTime, approvalProcess.ProfileExecutionStartTime);
                    Database.AddInParameter(cmd, Dbstring + "iProfileExecutionEndTime", DbType.DateTime, approvalProcess.ProfileExecutionEndTime);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalStatus", DbType.String, approvalProcess.ApprovalStatus);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.String, approvalProcess.Message);
                    Database.AddInParameter(cmd, Dbstring + "iState", DbType.String, approvalProcess.State);
                    Database.AddInParameter(cmd, Dbstring + "iPROFILE_OR_WORKFLOW", DbType.AnsiString, approvalProcess.PROFILE_OR_WORKFLOW);
                    Database.AddInParameter(cmd, Dbstring + "iEXECUTE_OR_MODIFY", DbType.AnsiString, approvalProcess.EXECUTE_OR_MODIFY);
                    Database.AddInParameter(cmd, Dbstring + "iIsWaitingForApproval", DbType.Int32, approvalProcess.IsWaitingForApproval);
                    Database.AddInParameter(cmd, Dbstring + "iIsAllApprovalReceived", DbType.Int32, approvalProcess.IsAllApprovalReceived);
                    Database.AddInParameter(cmd, Dbstring + "iIsAllApprove", DbType.Int32, approvalProcess.IsVerified);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, approvalProcess.UpdatorId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        approvalProcess = reader.Read() ? CreateEntityBuilder<ApprovalProcess>().BuildEntity(reader, approvalProcess) : null;
                    }

                    if (approvalProcess == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Approval Process already exists. Please specify another site.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this site.");
                                }
                        }
                    }

                    return approvalProcess;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating Site Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        public ApprovalProcess GetById(int id)
        {
            try
            {
                const string sp = "ApproProcesGetbyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ApprovalProcess>()).BuildEntity(reader, new ApprovalProcess());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public ApprovalProcess GetApprovalProcessById_Deletion(int id, string profile_workflow)
        {
            try
            {
                const string sp = "GetApprovalProcessById_Deletion";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iprofile_workflow", DbType.String, profile_workflow);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ApprovalProcess>()).BuildEntity(reader, new ApprovalProcess());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public IList<ApprovalProcess> GetByProfileId(int ProfileId)
        {
            IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
            try
            {
                const string sp = "ApproProcesPId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, ProfileId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        //  WfProfileApprovals = (CreateEntityBuilder<ApprovalProcess>()).BuildEntities(reader);
                        while (reader.Read())
                        {
                            var wfprofile = new ApprovalProcess
                            {
                                Id = Convert.IsDBNull(reader["ID"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileId = Convert.IsDBNull(reader["ProfileID"]) ? 0 : Convert.ToInt32(reader["ProfileID"]),
                                ApprovalProcessNumber = Convert.IsDBNull(reader["APPROVALPROCESSNUMBER"]) ? string.Empty : Convert.ToString(reader["APPROVALPROCESSNUMBER"]),
                                ProfileExecutionStartTime = Convert.IsDBNull(reader["PROFILEEXECUTIONSTARTTIME"]) ? DateTime.MinValue : Convert.ToDateTime(reader["PROFILEEXECUTIONSTARTTIME"]),
                                ProfileExecutionEndTime = Convert.IsDBNull(reader["PROFILEEXECUTIONENDTIME"]) ? DateTime.MinValue : Convert.ToDateTime(reader["PROFILEEXECUTIONENDTIME"]),
                                ApprovalStatus = Convert.IsDBNull(reader["APPROVALSTATUS"]) ? string.Empty : Convert.ToString(reader["APPROVALSTATUS"]),
                                Message = Convert.IsDBNull(reader["MESSAGE"]) ? string.Empty : Convert.ToString(reader["MESSAGE"]),
                                State = Convert.IsDBNull(reader["STATE"]) ? string.Empty : Convert.ToString(reader["STATE"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["PROFILE_OR_WORKFLOW"]) ? string.Empty : Convert.ToString(reader["PROFILE_OR_WORKFLOW"]),
                                EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["EXECUTE_OR_MODIFY"]) ? string.Empty : Convert.ToString(reader["EXECUTE_OR_MODIFY"]),
                                IsWaitingForApproval = Convert.IsDBNull(reader["ISWAITINGFORAPPROVAL"]) ? false : Convert.ToBoolean(reader["ISWAITINGFORAPPROVAL"]),
                                IsAllApprovalReceived = Convert.IsDBNull(reader["ISALLAPPROVALRECEIVED"]) ? false : Convert.ToBoolean(reader["ISALLAPPROVALRECEIVED"]),
                                IsAllApprove = Convert.IsDBNull(reader["ISALLAPPROVE"]) ? false : Convert.ToBoolean(reader["ISALLAPPROVE"]),
                                IsActive = Convert.IsDBNull(reader["ISACTIVE"]) ? 0 : Convert.ToInt32(reader["ISACTIVE"]),
                                CreatorId = Convert.IsDBNull(reader["CREATORID"]) ? 0 : Convert.ToInt32(reader["CREATORID"]),
                                CreateDate = Convert.IsDBNull(reader["CREATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CREATEDATE"].ToString()),
                                UpdatorId = Convert.IsDBNull(reader["UPDATORID"]) ? 0 : Convert.ToInt32(reader["UPDATORID"]),
                                UpdateDate = Convert.IsDBNull(reader["UPDATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UPDATEDATE"].ToString()),
                                IsVerified = Convert.IsDBNull(reader["ISALLAPPROVE"]) ? 0 : Convert.ToInt32(reader["ISALLAPPROVE"]),
                            };
                            WfProfileApprovals.Add(wfprofile);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetByProfileId(" + ProfileId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return WfProfileApprovals;
        }

        public IList<ApprovalProcess> GetActByPId(int ProfileId)
        {
            IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
            try
            {
                const string sp = "ApproProcesGetActByPId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, ProfileId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        WfProfileApprovals = (CreateEntityBuilder<ApprovalProcess>()).BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetByProfileId(" + ProfileId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return WfProfileApprovals;
        }

        public IList<ApprovalProcess> GetByApprovalStatus(string approvalStatus)
        {
            IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
            try
            {
                const string sp = "ApproProcesGetbyAS";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iApprovalStatus", DbType.String, approvalStatus);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        WfProfileApprovals = (CreateEntityBuilder<ApprovalProcess>()).BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetByApprovalStatus(" + approvalStatus + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return WfProfileApprovals;
        }

        public IList<ApprovalProcess> GetApprovedDetailsByTypeAndStatus(string profileOrWorkflow, string approvalStatus, DateTime startDate, DateTime endDate)
        {
            IList<ApprovalProcess> WfProfileApprovalsDetails = new List<ApprovalProcess>();
            try
            {
                const string sp = "GetApprovedDetails_byTypeandStatus";

                string start_Date = startDate.ToString("yyyy-MM-dd 00:00:00");
                string End_Date = endDate.ToString("yyyy-MM-dd 23:59:59");

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileOrWorkflow", DbType.String, profileOrWorkflow);
                    Database.AddInParameter(cmd, Dbstring + "iApprovalStatus", DbType.String, approvalStatus);
                    Database.AddInParameter(cmd, Dbstring + "iStartTime", DbType.String, start_Date);
                    Database.AddInParameter(cmd, Dbstring + "iEndTime", DbType.String, End_Date);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            ApprovalProcess _ApprovalProcess = new ApprovalProcess();
                            //WfProfileApprovalsDetails = (CreateEntityBuilder<ApprovalProcess>()).BuildEntities(reader);
                            _ApprovalProcess.ApprovalProcessNumber = Convert.IsDBNull(reader["APPROVALPROCESSNUMBER"]) ? string.Empty : Convert.ToString(reader["APPROVALPROCESSNUMBER"]);
                            _ApprovalProcess.PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["PROFILE_OR_WORKFLOW"]) ? string.Empty : Convert.ToString(reader["PROFILE_OR_WORKFLOW"]);
                            _ApprovalProcess.Approver = Convert.IsDBNull(reader["Approver"]) ? string.Empty : Convert.ToString(reader["Approver"]);

                            _ApprovalProcess.EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["EXECUTE_OR_MODIFY"]) ? string.Empty : Convert.ToString(reader["EXECUTE_OR_MODIFY"]);
                            _ApprovalProcess.ProfileId = Convert.IsDBNull(reader["ProfileID"]) ? 0 : Convert.ToInt32(reader["ProfileID"]);
                            _ApprovalProcess.ProfileExecutionStartTime = Convert.IsDBNull(reader["PROFILEEXECUTIONSTARTTIME"]) ? DateTime.MinValue : Convert.ToDateTime(reader["PROFILEEXECUTIONSTARTTIME"]);
                            _ApprovalProcess.ProfileExecutionEndTime = Convert.IsDBNull(reader["PROFILEEXECUTIONENDTIME"]) ? DateTime.MinValue : Convert.ToDateTime(reader["PROFILEEXECUTIONENDTIME"]);
                            _ApprovalProcess.ApprovalStatus = Convert.IsDBNull(reader["APPROVALSTATE"]) ? string.Empty : Convert.ToString(reader["APPROVALSTATE"]);

                            _ApprovalProcess.CreateDate = Convert.IsDBNull(reader["CREATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CREATEDATE"].ToString());
                            _ApprovalProcess.UpdateDate = Convert.IsDBNull(reader["UPDATEDATE"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UPDATEDATE"].ToString());
                            //here message is used for approvalbyname 
                            _ApprovalProcess.Message = Convert.IsDBNull(reader["APPROVALBY"]) ? string.Empty : Convert.ToString(reader["APPROVALBY"]);
                            //here WorkflowName is used for Note 
                            _ApprovalProcess.WorkflowName = Convert.IsDBNull(reader["NOTE"]) ? string.Empty : Convert.ToString(reader["NOTE"]);
                            //here ProfileName is used for RequesterName(LoginName)  
                            _ApprovalProcess.ProfileName = Convert.IsDBNull(reader["LoginName"]) ? string.Empty : Convert.ToString(reader["LoginName"]);

                            _ApprovalProcess.Creation_Remark = Convert.IsDBNull(reader["Reason"]) ? string.Empty : Convert.ToString(reader["Reason"]);
                            WfProfileApprovalsDetails.Add(_ApprovalProcess);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetByApprovalStatus(" + approvalStatus + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return WfProfileApprovalsDetails;
        }

        public IList<ApprovalProcess> GetByApprovalState(string approvalState)
        {
            IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
            try
            {
                const string sp = "ApproProcesGetbyState";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iState", DbType.String, approvalState);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        WfProfileApprovals = (CreateEntityBuilder<ApprovalProcess>()).BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetByApprovalState(" + approvalState + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return WfProfileApprovals;
        }

        public IList<ApprovalProcess> GetbyIsWAIsAAR(bool IsWaitingForApproval, bool IsAllApprovalReceived)
        {
            IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
            try
            {
                const string sp = "ApproProcesGetbyIsWAIsAAR";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iIsWaitingForApproval", DbType.Boolean, IsWaitingForApproval);

                    Database.AddInParameter(cmd, Dbstring + "iIsAllApprovalReceived", DbType.Boolean, IsAllApprovalReceived);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        WfProfileApprovals = (CreateEntityBuilder<ApprovalProcess>()).BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetbyIsWAIsAAR(" + IsWaitingForApproval + "," + IsAllApprovalReceived + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return WfProfileApprovals;
        }

        public IList<ApprovalProcess> GetAll()
        {

            try
            {
                IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();

                const string sp = "ApproProcesGetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var WfProfileApproval = new ApprovalProcess
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]),
                                ApprovalProcessNumber = Convert.IsDBNull(reader["ApprovalProcessNumber"]) ? string.Empty : Convert.ToString(reader["ApprovalProcessNumber"]),
                                ProfileExecutionStartTime = Convert.IsDBNull(reader["ProfileExecutionStartTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["ProfileExecutionStartTime"]),
                                ProfileExecutionEndTime = Convert.IsDBNull(reader["ProfileExecutionEndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["ProfileExecutionEndTime"]),

                                ApprovalStatus = Convert.IsDBNull(reader["ApprovalStatus"]) ? string.Empty : Convert.ToString(reader["ApprovalStatus"]),
                                Message = Convert.IsDBNull(reader["Message"]) ? string.Empty : Convert.ToString(reader["Message"]),
                                IsWaitingForApproval = Convert.IsDBNull(reader["IsWaitingForApproval"]) ? false : Convert.ToBoolean(reader["IsWaitingForApproval"]),
                                IsAllApprovalReceived = Convert.IsDBNull(reader["IsAllApprovalReceived"]) ? false : Convert.ToBoolean(reader["IsAllApprovalReceived"]),
                                IsAllApprove = Convert.IsDBNull(reader["IsAllApprove"]) ? false : Convert.ToBoolean(reader["IsAllApprove"]),
                                IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]),
                                UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]),
                                UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? "NA" : Convert.ToString(reader["ProfileName"]),
                                WorkflowName = Convert.IsDBNull(reader["WorkflowName"]) ? "NA" : Convert.ToString(reader["WorkflowName"]),
                                State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["PROFILE_OR_WORKFLOW"]) ? string.Empty : Convert.ToString(reader["PROFILE_OR_WORKFLOW"]),
                                EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["EXECUTE_OR_MODIFY"]) ? string.Empty : Convert.ToString(reader["EXECUTE_OR_MODIFY"]),
                            };

                            WfProfileApprovals.Add(WfProfileApproval);
                        }
                    }
                }

                return WfProfileApprovals;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalProcessDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public bool DeleteById(int id)
        {
            try
            {
                const string sp = "ApproProcesDelete";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

#if ORACLE || MSSQL
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalProcessDataAccess.DeleteById()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public string GetCPApprovalCount()
        {
            try
            {
                const string sp = "ApproProcesGetAPCCount";
                string ApprovalId = string.Empty;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            ApprovalId = Convert.IsDBNull(reader["ApprovalProcessNumber"]) ? string.Empty : Convert.ToString(reader["ApprovalProcessNumber"]);
                        }
                    }
                }
                return ApprovalId;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                   "Error In DAL While Executing Function Signature IWFProfileApprovalProcessDataAccess.DeleteById()" + Environment.NewLine +
                   "SYSTEM MESSAGE : " + exc.Message, exc);
                //return null;
            }
        }


        // for Facade.CheckIfProfileIsApprovedfor_Selected_ProfileOrWorkflow()
        public bool CheckIsApprovedOrNot(int UserId, int ProfileId, string Execute_Or_Modify, string Profile_Or_Workflow)
        {
            bool retValue = false;
            try
            {
                const string sp = "CheckIfProfileIsApprovedfor_Selected_ProfileOrWorkflowIDAndUSerId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, UserId);
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.AnsiString, ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iExecute_Or_Modify", DbType.AnsiString, Execute_Or_Modify);
                    Database.AddInParameter(cmd, Dbstring + "iProfile_Or_Workflow", DbType.AnsiString, Profile_Or_Workflow);


                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            string str = Convert.IsDBNull(reader["retvalue"]) ? "0" : reader["retvalue"].ToString();
                            retValue = str == "1" ? true : false;
                        }
                    }
                    return retValue;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ApprovalsProcessDataAccess.CheckIsApprovedOrNot()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
                return false;
            }
        }

        // for Facade.Check_IsProfileMarkedFor_FourEYE_ById()
        public bool CheckIs_ProfileMarked_For_FourEYE_ById(int ProfileId)
        {
            bool retValue = false;
            try
            {
                const string sp = "Approval_Process_IsProfileMarkedFor_FourEYE_ById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.AnsiString, ProfileId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            string str = Convert.IsDBNull(reader["retvalue"]) ? "0" : reader["retvalue"].ToString();
                            retValue = str == "1" ? true : false;
                        }
                    }
                    return retValue;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ApprovalsProcessDataAccess.CheckIs_ProfileMarked_For_FourEYE_ById()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
                return false;
            }
        }

        IList<ApprovalProcess> IApprovalProcessDataAccess.GetLimitedApprovalProcesses()
        {
            try
            {
                IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
                const string sp = "GetLimitedApproLevelProces";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var WfProfileApproval = new ApprovalProcess
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]),
                                ApprovalProcessNumber = Convert.IsDBNull(reader["ApprovalProcessNumber"]) ? string.Empty : Convert.ToString(reader["ApprovalProcessNumber"]),
                                ProfileExecutionStartTime = Convert.IsDBNull(reader["ProfileExecutionStartTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["ProfileExecutionStartTime"]),
                                ProfileExecutionEndTime = Convert.IsDBNull(reader["ProfileExecutionEndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["ProfileExecutionEndTime"]),

                                ApprovalStatus = Convert.IsDBNull(reader["ApprovalStatus"]) ? string.Empty : Convert.ToString(reader["ApprovalStatus"]),
                                Message = Convert.IsDBNull(reader["Message"]) ? string.Empty : Convert.ToString(reader["Message"]),
                                IsWaitingForApproval = Convert.IsDBNull(reader["IsWaitingForApproval"]) ? false : Convert.ToBoolean(reader["IsWaitingForApproval"]),
                                IsAllApprovalReceived = Convert.IsDBNull(reader["IsAllApprovalReceived"]) ? false : Convert.ToBoolean(reader["IsAllApprovalReceived"]),
                                //  IsAllApprove = Convert.IsDBNull(reader["IsAllApprove"]) ? false : Convert.ToBoolean(reader["IsAllApprove"]),
                                IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]),
                                UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]),
                                UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]),
                                ProfileName = Convert.IsDBNull(reader["ProfileName"]) ? "NA" : Convert.ToString(reader["ProfileName"]),
                                WorkflowName = Convert.IsDBNull(reader["WorkflowName"]) ? "NA" : Convert.ToString(reader["WorkflowName"]),
                                State = Convert.IsDBNull(reader["State"]) ? string.Empty : Convert.ToString(reader["State"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["PROFILE_OR_WORKFLOW"]) ? string.Empty : Convert.ToString(reader["PROFILE_OR_WORKFLOW"]),
                                EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["EXECUTE_OR_MODIFY"]) ? string.Empty : Convert.ToString(reader["EXECUTE_OR_MODIFY"]),
                                IsVerified = Convert.IsDBNull(reader["IsAllApprove"]) ? 0 : Convert.ToInt32(reader["IsAllApprove"]),
                            };

                            WfProfileApprovals.Add(WfProfileApproval);
                        }
                    }
                }
                return WfProfileApprovals;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature WFProfileApprovalLevelProcess.GetALL" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }

        }

        public ApprovalProcess GetApprovalprofileById(int id)
        {
            ApprovalProcess _approvalprofile = new ApprovalProcess();
            try
            {
                const string sp = "ApproProfileGetbyId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            ApprovalProcess _appprofile = new ApprovalProcess
                            {
                                ApprovalProcessNumber = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                            };
                            _approvalprofile = _appprofile;
                        }

                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IApprovalProcessDataAccess.GetApprovalprofileById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return _approvalprofile;
        }


        ApprovalProcess IApprovalProcessDataAccess.AddFourEyeDetails(ApprovalProcess approvalProcess)
        {
            try
            {
                const string sp = "Add_FourEyeDetails";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iID", DbType.AnsiString, approvalProcess.Id);
                    Database.AddInParameter(cmd, Dbstring + "iProfileID", DbType.AnsiString, approvalProcess.ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iProfileName", DbType.AnsiString, approvalProcess.ProfileName);
                    Database.AddInParameter(cmd, Dbstring + "iApprovers", DbType.AnsiString, approvalProcess.Approver);
                    Database.AddInParameter(cmd, Dbstring + "iAPPROVALSTATUS", DbType.AnsiString, approvalProcess.ApprovalStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPROFILE_OR_WORKFLOW", DbType.AnsiString, approvalProcess.PROFILE_OR_WORKFLOW);
                    Database.AddInParameter(cmd, Dbstring + "iEXECUTE_OR_MODIFY", DbType.AnsiString, approvalProcess.EXECUTE_OR_MODIFY);
                    Database.AddInParameter(cmd, Dbstring + "iUserID", DbType.AnsiString, approvalProcess.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iState", DbType.AnsiString, approvalProcess.State);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var _approvalprocess = new ApprovalProcess
                            {
                                ProfileId = Convert.IsDBNull(reader["Profile_WorkflowId"]) ? 0 : Convert.ToInt32(reader["Profile_WorkflowId"]),
                                ApprovalStatus = Convert.IsDBNull(reader["Usernames"]) ? string.Empty : Convert.ToString(reader["Usernames"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["Profile_or_Workflow"]) ? string.Empty : Convert.ToString(reader["Profile_or_Workflow"]),
                                EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["ActionType"]) ? string.Empty : Convert.ToString(reader["ActionType"]),
                                ProfileName = Convert.IsDBNull(reader["Profile_workflowName"]) ? string.Empty : Convert.ToString(reader["Profile_workflowName"]),
                                CreatorId = Convert.IsDBNull(reader["creatorId"]) ? 0 : Convert.ToInt32(reader["creatorId"]),
                                CreateDate = Convert.IsDBNull(reader["Createdate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["Createdate"]),
                                State = Convert.IsDBNull(reader["GroupIds"]) ? string.Empty : Convert.ToString(reader["GroupIds"]),
                            };
                            approvalProcess = _approvalprocess;

                        }
                    }

                    if (approvalProcess == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this SCR.");
                                }
                        }
                    }
                    return approvalProcess;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata, "Error In DAL While Inserting approvalProcess Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        ApprovalProcess IApprovalProcessDataAccess.AddAllFourEyeDetails(ApprovalProcess approvalProcess)
        {
            try
            {
                const string sp = "AddAllFourEyeDetails";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iProfileName", DbType.AnsiString, approvalProcess.ProfileName);
                    Database.AddInParameter(cmd, Dbstring + "iApprovers", DbType.AnsiString, approvalProcess.Approver);
                    Database.AddInParameter(cmd, Dbstring + "iAPPROVALSTATUS", DbType.AnsiString, approvalProcess.ApprovalStatus);
                    Database.AddInParameter(cmd, Dbstring + "iPROFILE_OR_WORKFLOW", DbType.AnsiString, approvalProcess.PROFILE_OR_WORKFLOW);
                    Database.AddInParameter(cmd, Dbstring + "iEXECUTE_OR_MODIFY", DbType.AnsiString, approvalProcess.EXECUTE_OR_MODIFY);
                    Database.AddInParameter(cmd, Dbstring + "iUserID", DbType.AnsiString, approvalProcess.CreatorId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var _approvalprocess = new ApprovalProcess
                            {
                                //  ProfileId = Convert.IsDBNull(reader["Profile_workflow"]) ? 0 : Convert.ToInt32(reader["Profile_workflow"]),
                                ApprovalStatus = Convert.IsDBNull(reader["Usernames"]) ? string.Empty : Convert.ToString(reader["Usernames"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["Profile_workflow"]) ? string.Empty : Convert.ToString(reader["Profile_workflow"]),
                                EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["ActionType"]) ? string.Empty : Convert.ToString(reader["ActionType"]),
                                //ProfileName = Convert.IsDBNull(reader["Profile_workflowName"]) ? string.Empty : Convert.ToString(reader["Profile_workflowName"]),
                                CreatorId = Convert.IsDBNull(reader["creatorId"]) ? 0 : Convert.ToInt32(reader["creatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]),

                            };
                            approvalProcess = _approvalprocess;

                        }
                    }

                    if (approvalProcess == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this SCR.");
                                }
                        }
                    }
                    return approvalProcess;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata, "Error In DAL While Inserting approvalProcess Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ApprovalProcess> IApprovalProcessDataAccess.GetAllApproverDetails()
        {
            try
            {
                IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
                const string sp = "GetAllApproverDetails";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    // Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var WfProfileApproval = new ApprovalProcess
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                ProfileId = Convert.IsDBNull(reader["Profile_WorkflowId"]) ? 0 : Convert.ToInt32(reader["Profile_WorkflowId"]),
                                ProfileName = Convert.IsDBNull(reader["profile_workflowName"]) ? string.Empty : Convert.ToString(reader["profile_workflowName"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["Profile_or_Workflow"]) ? string.Empty : Convert.ToString(reader["Profile_or_Workflow"]),
                                Approver = Convert.IsDBNull(reader["Usernames"]) ? string.Empty : Convert.ToString(reader["Usernames"]),
                                EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["ActionType"]) ? string.Empty : Convert.ToString(reader["ActionType"]),
                            };
                            WfProfileApprovals.Add(WfProfileApproval);
                        }
                    }
                }
                return WfProfileApprovals;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature WFProfileApprovalLevelProcess.GetAllApproverDetails" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }

        }

        IList<ApprovalProcess> IApprovalProcessDataAccess.GetAllApproverDetails_ForAll()
        {
            try
            {
                IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
                const string sp = "GetAllApproverDetails_ForAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    // Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var WfProfileApproval = new ApprovalProcess
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["Profile_workflow"]) ? string.Empty : Convert.ToString(reader["Profile_workflow"]),
                                Approver = Convert.IsDBNull(reader["Usernames"]) ? string.Empty : Convert.ToString(reader["Usernames"]),
                                EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["ActionType"]) ? string.Empty : Convert.ToString(reader["ActionType"]),
                            };
                            WfProfileApprovals.Add(WfProfileApproval);
                        }
                    }
                }
                return WfProfileApprovals;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature WFProfileApprovalLevelProcess.GetAllApproverDetails" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }

        }


        public bool Check_IsWorkflowMarkedFor_FourEYE_ById(int workflowId)
        {
            bool retValue = false;
            try
            {
                const string sp = "Approval_Process_IsWorkflowMarkedFor_FourEYE_ById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iwfId", DbType.AnsiString, workflowId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            string str = Convert.IsDBNull(reader["retvalue"]) ? "0" : reader["retvalue"].ToString();
                            retValue = str == "1" ? true : false;
                        }
                    }
                    return retValue;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ApprovalsProcessDataAccess.Check_IsWorkflowMarkedFor_FourEYE_ById()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
                return false;
            }
        }

        public bool DeleteFourEyeDetailsById(int id)
        {
            try
            {
                const string sp = "DeleteFourEyeDetailsById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

#if ORACLE || MSSQL
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalProcessDataAccess.DeleteFourEyeDetailsById()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        public bool DeleteAll_FourEyeDetails(string name, string type)
        {
            try
            {
                const string sp = "DeleteAll_FourEyeDetailsById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iname", DbType.String, name);
                    Database.AddInParameter(cmd, Dbstring + "itype", DbType.String, type);

                    int returnCode = Database.ExecuteNonQuery(cmd);

#if ORACLE || MSSQL
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalProcessDataAccess.DeleteFourEyeDetailsById()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        ApprovalProcess IApprovalProcessDataAccess.GetApprovalstatusbywfId(int id)
        {
            ApprovalProcess approvalprocess = new ApprovalProcess();
            try
            {
                const string sp = "GetApprovalstatusbywfId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iID", DbType.Int32, id);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var _approvalprocess = new ApprovalProcess
                            {
                                IsVerified = Convert.IsDBNull(reader["ISALLAPPROVE"]) ? 0 : Convert.ToInt32(reader["ISALLAPPROVE"]),
                                CreatorId = Convert.IsDBNull(reader["Checkexecutionbypass"]) ? 0 : Convert.ToInt32(reader["Checkexecutionbypass"]),
                            };
                            approvalprocess = _approvalprocess;

                        }
                    }

                    if (approvalprocess == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this SCR.");
                                }
                        }
                    }
                    return approvalprocess;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata, "Error In DAL While Inserting approvalProcess Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public bool Deleteworkflowvalidators(string name)
        {
            try
            {
                const string sp = "Deleteworkflowvalidators";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iname", DbType.String, name);

                    int returnCode = Database.ExecuteNonQuery(cmd);

#if ORACLE || MSSQL
                    return returnCode < 0;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalProcessDataAccess.DeleteFourEyeDetailsById()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ApprovalProcess> IApprovalProcessDataAccess.GetAllValidators()
        {
            try
            {
                IList<ApprovalProcess> WfProfileApprovals = new List<ApprovalProcess>();
                const string sp = "GetAllValidators";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var WfProfileApproval = new ApprovalProcess
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["IsProfile_OR_Workflow"]) ? string.Empty : Convert.ToString(reader["IsProfile_OR_Workflow"]),
                                Approver = Convert.IsDBNull(reader["Usernames"]) ? string.Empty : Convert.ToString(reader["Usernames"]),
                                EXECUTE_OR_MODIFY = "Creation",
                                WorkflowName = Convert.IsDBNull(reader["userids"]) ? string.Empty : Convert.ToString(reader["userids"]),
                            };
                            WfProfileApprovals.Add(WfProfileApproval);
                        }
                    }
                }
                return WfProfileApprovals;
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature WFProfileApprovalLevelProcess.GetAllApproverDetails" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }

        }

        ApprovalProcess IApprovalProcessDataAccess.GetAllValidatorsByType(string IsProfile_OR_Workflow)
        {
            ApprovalProcess approvalprocess = new ApprovalProcess();
            try
            {
                const string sp = "GetAllValidatorsByType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iIsProfile_OR_Workflow", DbType.String, IsProfile_OR_Workflow);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var _approvalprocess = new ApprovalProcess
                            {
                                Approver = Convert.IsDBNull(reader["Usernames"]) ? string.Empty : Convert.ToString(reader["Usernames"]),
                                EXECUTE_OR_MODIFY = "Creation",
                                WorkflowName = Convert.IsDBNull(reader["userids"]) ? string.Empty : Convert.ToString(reader["userids"]),
                            };
                            approvalprocess = _approvalprocess;

                        }
                    }

                    if (approvalprocess == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this SCR.");
                                }
                        }
                    }
                    return approvalprocess;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata, "Error In DAL While Inserting approvalProcess Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        ApprovalProcess IApprovalProcessDataAccess.AddWorkflowValidators(ApprovalProcess approvalProcess)
        {
            try
            {
                const string sp = "AddWorkflowValidators";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iProfileName", DbType.AnsiString, approvalProcess.ProfileName);
                    Database.AddInParameter(cmd, Dbstring + "iApprovers", DbType.AnsiString, approvalProcess.Approver);
                    Database.AddInParameter(cmd, Dbstring + "iAPPROVALSTATUS", DbType.AnsiString, approvalProcess.ApprovalStatus);
                    Database.AddInParameter(cmd, Dbstring + "iEXECUTE_OR_MODIFY", DbType.AnsiString, approvalProcess.EXECUTE_OR_MODIFY);


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var _approvalprocess = new ApprovalProcess
                            {

                                ApprovalStatus = Convert.IsDBNull(reader["Usernames"]) ? string.Empty : Convert.ToString(reader["Usernames"]),
                                PROFILE_OR_WORKFLOW = Convert.IsDBNull(reader["userids"]) ? string.Empty : Convert.ToString(reader["userids"]),
                                EXECUTE_OR_MODIFY = Convert.IsDBNull(reader["userEmails"]) ? string.Empty : Convert.ToString(reader["userEmails"]),


                            };
                            approvalProcess = _approvalprocess;

                        }
                    }

                    if (approvalProcess == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("SCR already exists. Please specify another SCR.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this SCR.");
                                }
                        }
                    }
                    return approvalProcess;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation, ExceptionManager.CommonMessage.UserAlertMessageInsertdata, "Error In DAL While Inserting approvalProcess Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        public string CheckworkflowwatingForReview(int WorkflowId)
        {
            string processID = string.Empty;
            string approvers = string.Empty;
            try
            {
                const string sp = "CheckworkflowwatingForReview";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iworkflowId", DbType.Int32, WorkflowId);

                   // int returnCode = Database.ExecuteNonQuery(cmd);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            processID = Convert.IsDBNull(reader["ID"]) ? string.Empty : Convert.ToString(reader["ID"]);
                            approvers = Convert.IsDBNull(reader["APPROVER"]) ? string.Empty : Convert.ToString(reader["APPROVER"]);

                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IWFProfileApprovalProcessDataAccess.CheckworkflowwatingForReview()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return processID + "&" + approvers;
        }
    }

    #endregion ApprovalsProcessDataAccess

}


