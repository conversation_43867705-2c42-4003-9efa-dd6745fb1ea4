﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="IBMXIVMirrorConfiguration.ascx.cs" Inherits="CP.UI.Controls.IBMXIVMirrorConfiguration" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>

 <script src="../Script/DatabaseConfiguration.js"></script>
    <script src="../Script/ServerConfiguration.js"></script>
    <script src="../Script/EncryptDecrypt.js"></script>
<div class="form-horizontal margin-none">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <div class="widget widget-heading-simple widget-body-gray" style="box-shadow: 0 0 0 0 #f6f6f6;">
        <div class="widget-head">
            <h4 class="heading">DSCLI Server</h4>
        </div>
        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 24%;"></th>
                                <th style="width: 37.5% !important;">Production Server
                                </th>
                                <th style="width: 37.5% !important;">DR Server
                                </th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr>
                                <td>
                                    <label class="">
                                        Select Server <span class="inactive">*</span>
                                    </label>
                                </td>
                                <td>
                                    <asp:DropDownList ID="ddlServerPR" runat="server" AutoPostBack="true" CssClass="selectpicker" OnSelectedIndexChanged="DdlServerPRSelectedIndexChanged" data-style="btn-default">
                                    </asp:DropDownList>
                                    <%-- <asp:RequiredFieldValidator ID="rfvddlServerPR" runat="server" ControlToValidate="ddlServerPR" ValidationGroup="Groupdiscover"
                                                Display="Dynamic" CssClass="error" InitialValue="0" ErrorMessage="Select Server"></asp:RequiredFieldValidator>
                                            <asp:Label ID="lblServerPR" runat="server" ForeColor="Red"></asp:Label>--%>
                                </td>
                                <td>
                                    <asp:DropDownList ID="ddlServerDR" runat="server" AutoPostBack="true" CssClass="selectpicker" OnSelectedIndexChanged="DdlServerDRSelectedIndexChanged" data-style="btn-default">
                                    </asp:DropDownList>
                                    <%-- <asp:RequiredFieldValidator ID="rfvddlServerDR" runat="server" ControlToValidate="ddlServerDR" ValidationGroup="Groupdiscover"
                                                Display="Dynamic" CssClass="error" InitialValue="0" ErrorMessage="Select Server"></asp:RequiredFieldValidator>
                                            <asp:Label ID="lblServerDR" runat="server" ForeColor="Red"></asp:Label>--%>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label class="" for="txtName">
                                        XCLI Server Host Name
                                    </label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDSCLIHostnamePR" CssClass="form-control" Style="width: 56% !important;" runat="server"></asp:TextBox>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDSCLIHostnameDR" CssClass="form-control" Style="width: 56% !important;" runat="server"></asp:TextBox>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label class="" for="txtName">
                                        XCLI Server IP
                                    </label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDSCLIServerIPPR" CssClass="form-control" Style="width: 56% !important;" runat="server"></asp:TextBox>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDSCLIServerIPDR" CssClass="form-control" Style="width: 56% !important;" runat="server"></asp:TextBox>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label class="" for="txtName">
                                        SSH User ID
                                    </label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtSSHUserIDPR" CssClass="form-control" Style="width: 56% !important;" runat="server"></asp:TextBox>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtSSHUserIDDR" CssClass="form-control" Style="width: 56% !important;" runat="server"></asp:TextBox>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label class="" for="txtName">
                                        SSH Password
                                    </label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtSSHPasswordPR" CssClass="form-control" Style="width: 56% !important;" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtSSHPasswordDR" CssClass="form-control" Style="width: 56% !important;" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label class="">
                                        XCLI Path
                                    </label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDSCLIPathPR" CssClass="form-control" Style="width: 56% !important;" runat="server"></asp:TextBox>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDSCLIPathDR" CssClass="form-control" Style="width: 56% !important;" runat="server"></asp:TextBox>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="form-horizontal margin-none">
            <div class="widget widget-heading-simple widget-body-white">
                <div class="widget-body">
                    <table class="table">
                        <thead>
                            <tr>
                                <th style="width: 24%;"></th>
                                <th style="width: 37.5% !important;">Production Server
                                </th>
                                <th style="width: 37.5% !important;">DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <label>
                                        HMC Server <span class="inactive">*</span></label>
                                </td>
                                <td>
                                    <asp:DropDownList ID="ddlPrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" OnSelectedIndexChanged="DdlPrServerSelectedIndexChanged"
                                        data-style="btn-default">
                                    </asp:DropDownList>
                                    <%-- <asp:RequiredFieldValidator ID="rfvPRServer" CssClass="error" runat="server" Display="Dynamic" ControlToValidate="ddlPrServer"
                                                Style="margin-left: 3px;" InitialValue="0" ErrorMessage="Select Production Server" ValidationGroup="Groupdiscover"></asp:RequiredFieldValidator>--%>
                                </td>
                                <td>
                                    <asp:DropDownList ID="ddlDrServer" runat="server" CssClass="selectpicker" OnSelectedIndexChanged="DdlDrServerSelectedIndexChanged"
                                        data-style="btn-default" AutoPostBack="true">
                                    </asp:DropDownList>
                                    <%--  <asp:RequiredFieldValidator ID="rfvDRServer" CssClass="error" runat="server" ErrorMessage="Select DR Server"
                                                Style="margin-left: 3px;" ControlToValidate="ddlDrServer" InitialValue="0" ValidationGroup="Groupdiscover" Display="Dynamic"></asp:RequiredFieldValidator>--%>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label>
                                        Management Console IP
                                    </label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtPRMgtConsoleIP" CssClass="form-control" Style="Width: 56% !important" Enabled="true" runat="server"></asp:TextBox>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDRMgtConsoleIP" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label>
                                        User Name</label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtPRUserName" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDRUserName" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label>
                                        Password</label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtPRPassword" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"
                                        autocomplete="off" TextMode="Password"></asp:TextBox>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtDRPassword" runat="server" CssClass="form-control" Style="Width: 56% !important" autocomplete="off" TextMode="Password"></asp:TextBox>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <label>
                                        Replication Mode</label>
                                </td>
                                <td>
                                    <asp:TextBox ID="txtreplicationmode" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                                </td>
                                <td></td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="form-horizontal margin-none">
                <div class="widget widget-heading-simple widget-body-white">
                    
                    <div class="widget-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th style="width: 24%;">Hardware Details</th>
                                    <th style="width: 37.5% !important;">Production Storage
                                    </th>
                                    <th style="width: 37.5% !important;">DR Storage
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <label>
                                            Storage Name
                                        </label>
                                    </td>
                                    <td>
                                        <asp:TextBox ID="txtPRWNNNo" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                                        <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator3" CssClass="error" ControlToValidate="txtPRWNNNo" Display="Dynamic" runat="server" ErrorMessage="Enter Image ID"></asp:RequiredFieldValidator>--%>
                                    </td>
                                    <td>
                                        <asp:TextBox ID="txtDRWNNNo" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                                        <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator4" CssClass="error" ControlToValidate="txtDRWNNNo" Display="Dynamic" runat="server" ErrorMessage="Enter Image ID"></asp:RequiredFieldValidator>--%>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <label>
                                            Storage Image ID
                                        </label>
                                    </td>
                                    <td>
                                        <asp:TextBox ID="txtPRStorageImgID" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                                        <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator5" CssClass="error" ControlToValidate="txtPRStorageImgID" Display="Dynamic" runat="server" ErrorMessage="Enter WNN"></asp:RequiredFieldValidator>--%>
                                    </td>
                                    <td>
                                        <asp:TextBox ID="txtDRStorageImgID" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                                        <%--<asp:RequiredFieldValidator ID="RequiredFieldValidator6" CssClass="error" ControlToValidate="txtDRStorageImgID" Display="Dynamic" runat="server" ErrorMessage="Enter WNN"></asp:RequiredFieldValidator>--%>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        

                    </div>
                </div>
            </div>
            <div class="form-horizontal margin-none">
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-head">
                        <h4 class="heading">CGName</h4>
                    </div>
                    <div class="widget-body">
                        <asp:Panel ID="PanelManual" runat="server">
                            <asp:ListView ID="lvManual" OnItemDeleting="LvManualDeleting" OnItemInserting="LvManualItemInserting"
                                OnItemEditing="LvManualEditing" OnItemUpdating="LvManualUpdating" OnItemCanceling="LvManualCanceling"
                                InsertItemPosition="LastItem" runat="server">
                                <LayoutTemplate>
                                    <table class="table table-bordered" width="100%" style="table-layout: fixed; margin-bottom: 0;">
                                        <thead>
                                            <tr class="bold" style="background-color: #4A8BC2 !important; color: #FFFFFF; font-weight: bold;">

                                                <td style="width: 45%">PRCG Name <span class="inactive">*</span>
                                                </td>
                                                <td style="width: 45%;">DRCG Name <span class="inactive">*</span>
                                                </td>
                                                <td style="width: 10%">Action</td>
                                            </tr>
                                        </thead>
                                    </table>
                                    <div class="Scrollbar">
                                        <table class="table table-bordered" width="100%" style="border: 1px solid #dddddd; table-layout: fixed;margin-bottom:0px;">
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                            </tbody>
                                        </table>
                                    </div>
                                </LayoutTemplate>
                                <ItemTemplate>
                                    <tr>

                                        <td style="width: 45%;">
                                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                            <asp:Label ID="PRName" Text='<%# Eval("PRCGName") %>' runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 45%;">
                                            <asp:Label ID="DRName" Text='<%# Eval("DRCGName") %>' runat="server"> </asp:Label>
                                        </td>
                                        <td style="width: 10%; vertical-align: middle; text-align: center">
                                            <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                ToolTip="Edit" ImageUrl="../Images/icons/pencil.png" />
                                            <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />
                                        </td>
                                        <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                            ConfirmText='<%# "Are you sure want to delete? " %>' OnClientCancel="CancelClick">
                                        </TK1:ConfirmButtonExtender>
                                    </tr>
                                </ItemTemplate>
                                <EditItemTemplate>
                                    <tr>

                                        <td style="width: 45%;">
                                            <asp:Label ID="Id" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                            <asp:TextBox ID="PRName" MaxLength="4" Text='<%# Eval("PRCGName") %>' Style="width: 95%" CssClass="form-control" runat="server"> </asp:TextBox>

                                            <asp:Label ID="lblAVolume" runat="server" Visible="false" ForeColor="red"> </asp:Label>
                                        </td>
                                        <td style="width: 45%;">
                                            <asp:TextBox ID="DRName" MaxLength="4" Text='<%# Eval("DRCGName") %>' Style="width: 95%" runat="server" CssClass="form-control"> </asp:TextBox>

                                            <asp:Label ID="lblBVolume" runat="server" Visible="false" ForeColor="red"></asp:Label>
                                        </td>
                                        <td style="width: 10%; vertical-align: middle; text-align: center">

                                            <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Update" AlternateText="Update"
                                                ToolTip="Edit" ImageUrl="../Images/icons/navigation-090.png" />
                                            <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />

                                        </td>
                                    </tr>
                                </EditItemTemplate>
                                <InsertItemTemplate>
                                    <tr>

                                        <td style="width: 45%;">
                                            <asp:TextBox ID="PRName" Text='<%# Eval("PRCGName") %>' CssClass="form-control" Style="width: 95%" runat="server"> </asp:TextBox>

                                            <asp:Label ID="lblAVolume" runat="server" Visible="false" ForeColor="red"> </asp:Label>
                                        </td>
                                        <td style="width: 45%;">
                                            <asp:TextBox ID="DRName" Text='<%# Eval("DRCGName") %>' runat="server" Style="width: 95%" CssClass="form-control"> </asp:TextBox>
                                            <asp:Label ID="lblBVolume" runat="server" Visible="false" ForeColor="red"></asp:Label>
                                        </td>
                                        <td style="width: 10%; vertical-align: middle; text-align: center">
                                            <asp:ImageButton ID="imgInsert" runat="server" CommandName="Insert" AlternateText="Insert"
                                                ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />

                                        </td>
                                    </tr>
                                </InsertItemTemplate>

                                <EmptyDataTemplate>
                                    <lable>Empty text that will be displayed.</lable>
                                </EmptyDataTemplate>
                            </asp:ListView>
                            <%--<asp:Label runat="server" Text="abc" ID="lbltext"></asp:Label>--%>
                        </asp:Panel>
                    </div>
                </div>
            </div>
            <div class="form-actions row">
                <div class="col-md-12 text-right">
                    <%-- <div class="col-lg-3">--%>
                    <asp:Label ID="lblMsg" runat="server" Text=""></asp:Label>
                    <%-- </div>
                <div class="col-lg-7" style="margin-left: 57.6%;">--%>
                    <asp:Button ID="SaveRep" CssClass="btn btn-primary" Width="10%" runat="server" Text="Save"
                        OnClick="SaveRepClick" />
                    <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="10%" runat="server"
                        Text="Cancel" OnClick="BtnCancelClick" CausesValidation="false" />
                    <%--</div>--%>
                </div>
            </div>
        </div>
    </div>
</div>
