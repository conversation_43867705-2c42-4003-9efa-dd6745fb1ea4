﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.Shared;
using CP.Common.DatabaseEntity;
using CP.Helper;
using CP.ExceptionHandler;
using System.Collections;
using CP.UI;
using CP.UI.Code.Base;
using CP.UI.Controls;
using System.Text;
using System.Data;
using System.ComponentModel;
using System.Web.Services;
using Telerik.Web.UI;
using System.Drawing;
using Telerik.Web.UI.HtmlChart;
using System.Linq;
using System.Configuration;
using CP.UI.Code.Replication;
using CP.UI.Code.Replication.Clients;
using System.Web.UI.HtmlControls;
using System.Web.UI;
using System.Text.RegularExpressions;
using log4net;

namespace CP.UI.Admin
{
    public partial class BusinessFunctionsBIA : BasePage
    {
        #region Global variables

        #region Current User variables
        private static int _currentLoginUserId;
        private static string _currentLoggedUserName;
        private static int _companyId;
        private static bool _isUserSuperAdmin;
        private static bool _isParent;
        int iMTR = 0;
        int iRTO = 0;
        bool IsCritical = false;
        #endregion

        private readonly ILog _logger = LogManager.GetLogger(typeof(BusinessFunctionsBIA));
        #endregion

        #region PrepareView
        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            // current variables
            _currentLoggedUserName = LoggedInUserName;
            _currentLoginUserId = LoggedInUserId;
            _companyId = LoggedInUserCompanyId;
            _isUserSuperAdmin = IsUserSuperAdmin;
            _isParent = LoggedInUserCompany.IsParent;

            PopulateFunctionsList();
            PopulateImpactTypes();
            PopulateSection();


            //Session["_token"] = UrlHelper.AddTokenToRequest();
            //if (Session["_token"] != null)
            //{
            //    hdtokenKey.Value = Convert.ToString(Session["_token"]);
            //}
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 
        }

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((Session["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request


        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtCountryCode");
                //IgnoreIDs.Add("txtCountryCode2");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        #endregion

        #region Events

        protected void lvFunctionsList_PreRender(object sender, EventArgs e)
        {
            PopulateFunctionsList();
        }

        protected void lvFunctionsList_ItemEditing(object sender, ListViewEditEventArgs e)
        {

            try
            {
                // Make BIA Panel Visible
                divtitle.Visible = true;
                divAccordian.Visible = true;
                lbllvActivitylistInfo.Text = string.Empty;
                lbllvDownStreamDependencylistInfo.Text = string.Empty;
                lbllvUpStreamDependencylistInfo.Text = string.Empty;
                lblInfoQualitativeBIA.Text = string.Empty;
                lblInfoQuantitaiveBIA.Text = string.Empty;
                UdpBIA.Update();

                var lblFunctionID = (lvFunctionsList.Items[e.NewEditIndex].FindControl("lblFunctionID")) as Label;
                if (lblFunctionID != null)
                {
                    if (Convert.ToInt32(lblFunctionID.Text) > 0)
                    {
                        BusinessFunction objBusinessFunction = Facade.GetBusinessFunctionById(Convert.ToInt32(lblFunctionID.Text));
                        lblfunctionheadid.Text = lblFunctionID.Text;
                        lblfunctionhead.Text = objBusinessFunction.Name;
                        SetEditView();
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void lnkBtnViewReport_Click(object sender, EventArgs e)
        {
            ImageButton lnkBtnViewReport = (ImageButton)sender;
            ListViewItem item = (ListViewItem)lnkBtnViewReport.NamingContainer;
            Label lblFunctionID = item.FindControl("lblFunctionID") as Label;

            var secureDbMonitorUrl = UrlHelper.BuildSecureUrl(Constants.UrlConstants.Urls.Admin.BIAFunctionsReport,
                                                                                  string.Empty, Constants.UrlConstants.Params.BusinessfunctionId,
                                                                                  lblFunctionID.Text.ToString());
            if (secureDbMonitorUrl != null)
            {
                Helper.Url.Redirect(secureDbMonitorUrl);
            }
        }

        protected void lnkbtnPerformBIA_Click(object sender, ImageClickEventArgs e)
        {
            string ProfileID = string.Empty;
            ImageButton lnkbtnPerformBIA = (ImageButton)sender;
            ListViewItem item = (ListViewItem)lnkbtnPerformBIA.NamingContainer;
            Label lblFunctionID = item.FindControl("lblFunctionID") as Label;
            Label lblFIATemplateID = item.FindControl("lblFIATemplateID") as Label;

            if (!string.IsNullOrEmpty(lblFIATemplateID.Text) && lblFIATemplateID.Text.Trim() != "0")
            {
                ProfileID = lblFIATemplateID.Text.Trim();
            }

            var secureBFBIAUrl = UrlHelper.BuildSecureUrl(Constants.UrlConstants.Urls.ImpactAnalysis.BusinessFunctionBIA,
                                                                                  string.Empty, Constants.UrlConstants.Params.BusinessfunctionId,
                                                                                  lblFunctionID.Text.ToString(), Constants.UrlConstants.Params.ProfileID, ProfileID);
            if (secureBFBIAUrl != null)
            {
                Helper.Url.Redirect(secureBFBIAUrl);
            }
        }

        protected void btnSavelvActivitylist_Click(object sender, EventArgs e)
        {
            if (String.IsNullOrEmpty(lblfunctionheadid.Text) || Convert.ToInt32(lblfunctionheadid.Text) == 0)
            {
                lbllvActivitylistInfo.Visible = true;
                lbllvActivitylistInfo.ForeColor = Color.Red;
                lbllvActivitylistInfo.Text = "Please select the Business function before continuing!&nbsp;&nbsp;&nbsp;";
                return;
            }
            try
            {

                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA))
                {

                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {

                        //if (!ValidateRequest("BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA))
                        //{
                        //    return;
                        //}

                        foreach (ListViewItem item1 in lvActivitylist.Items)
                        {
                            Label lblActivityID = item1.FindControl("lblActivityId") as Label;
                            TextBox txtActivity = item1.FindControl("txtActivityName") as TextBox;

                            if (!string.IsNullOrEmpty(txtActivity.Text))
                            {
                                BusinessFunctionBIAActivity objApplicationActivity = new BusinessFunctionBIAActivity();
                                objApplicationActivity.Id = Convert.ToInt32(string.IsNullOrEmpty(lblActivityID.Text) ? "0" : lblActivityID.Text);
                                objApplicationActivity.ActivityDescription = txtActivity.Text;
                                objApplicationActivity.BusinessFunctionId = Convert.ToInt32(lblfunctionheadid.Text);
                                objApplicationActivity.CreatorId = LoggedInUserId;
                                objApplicationActivity.UpdatorId = LoggedInUserId;

                                if (Facade.IsExistBusinessFunctionBIAActivityByID(objApplicationActivity.Id))
                                {
                                    Facade.UpdateBusinessFunctionActivityBIA(objApplicationActivity);
                                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIAActivity", UserActionType.UpdateBusinessFunctionBIAActivity, "BusinessFunctionBIAActivity '" + objApplicationActivity.ActivityDescription + "' was added to the BusinessFunctionsBIA table", LoggedInUserId);
                                }
                                else
                                {
                                    Facade.AddBusinessFunctionActivityBIA(objApplicationActivity);
                                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIAActivity", UserActionType.CreateBusinessFunctionBIAActivity, "BusinessFunctionBIAActivity '" + objApplicationActivity.ActivityDescription + "' was added to the BusinessFunctionsBIA table", LoggedInUserId);
                                }

                                lbllvActivitylistInfo.Visible = true;
                                lbllvActivitylistInfo.ForeColor = Color.Green;
                                lbllvActivitylistInfo.Text = "Record Saved Successfully&nbsp;&nbsp;&nbsp;";

                            }
                        }
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }

                lbllvActivitylistInfo.Visible = true;
                lbllvActivitylistInfo.ForeColor = Color.Red;
                lbllvActivitylistInfo.Text = "Error Occured While saving Record: " + ex.Message + "&nbsp;&nbsp;&nbsp;";
            }
        }

        protected void btnSaveUpStream_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {

                    if (String.IsNullOrEmpty(lblfunctionheadid.Text) || Convert.ToInt32(lblfunctionheadid.Text) == 0)
                    {
                        lbllvUpStreamDependencylistInfo.Visible = true;
                        lbllvUpStreamDependencylistInfo.ForeColor = Color.Red;
                        lbllvUpStreamDependencylistInfo.Text = "Please select the Business function before continuing!&nbsp;&nbsp;&nbsp;";
                        return;
                    }
                    foreach (ListViewItem Listitems in lvUpStreamDependencylist.Items)
                    {
                        DropDownList ddlbusinessfunctions = Listitems.FindControl("ddlbusinessfunctions") as DropDownList;
                        if (ddlbusinessfunctions.SelectedIndex > 0)
                        {

                        }
                        else
                        {
                            lbllvUpStreamDependencylistInfo.Visible = true;
                            lbllvUpStreamDependencylistInfo.ForeColor = Color.Red;
                            lbllvUpStreamDependencylistInfo.Text = "Please select the Business function before continuing!&nbsp;&nbsp;&nbsp;";
                            return;
                        }
                    }
                    try
                    {
                        foreach (ListViewItem item in lvUpStreamDependencylist.Items)
                        {
                            BusinessFunctionBIADetails objBusinessFunctionBIADetails = new BusinessFunctionBIADetails();
                            objBusinessFunctionBIADetails = GetApplicationRelationShip(item, true);
                            if (Facade.IsExistBusinessFunctionBIADetailsByID(objBusinessFunctionBIADetails.Id))
                            {
                                Facade.UpdateBusinessFunctionBIADetails(objBusinessFunctionBIADetails);
                                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIADetails", UserActionType.UpdateBusinessFunctionBIADetails, "BusinessFunctionBIADetails was updated to the BusinessFunctionBIAActivity table", LoggedInUserId);
                            }
                            else
                            {
                                Facade.AddBusinessFunctionBIADetails(objBusinessFunctionBIADetails);
                                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIADetails", UserActionType.CreateBusinessFunctionBIADetails, "BusinessFunctionBIADetails was added to the BusinessFunctionBIAActivity table", LoggedInUserId);
                            }

                            PopulateUpstreamApplication();

                            lbllvUpStreamDependencylistInfo.Visible = true;
                            lbllvUpStreamDependencylistInfo.ForeColor = Color.Green;
                            lbllvUpStreamDependencylistInfo.Text = "Record Saved Successfully&nbsp;&nbsp;&nbsp;";
                        }
                    }
                    catch (CpException ex)
                    {
                        ExceptionManager.Manage(ex, this);
                        lbllvUpStreamDependencylistInfo.Visible = true;
                        lbllvUpStreamDependencylistInfo.ForeColor = Color.Red;
                        lbllvUpStreamDependencylistInfo.Text = "Error Occured While saving Record: " + ex.Message + "&nbsp;&nbsp;&nbsp;";
                    }
                    catch (Exception ex)
                    {
                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, this);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                            ExceptionManager.Manage(customEx, this);
                        }
                        lbllvUpStreamDependencylistInfo.Visible = true;
                        lbllvUpStreamDependencylistInfo.ForeColor = Color.Red;
                        lbllvUpStreamDependencylistInfo.Text = "Error Occured While saving Record: " + ex.Message + "&nbsp;&nbsp;&nbsp;";
                    }
                }
            }
        }

        protected void btnSaveDownStreamApp_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {
                    if (String.IsNullOrEmpty(lblfunctionheadid.Text) || Convert.ToInt32(lblfunctionheadid.Text) == 0)
                    {
                        lbllvDownStreamDependencylistInfo.Visible = true;
                        lbllvDownStreamDependencylistInfo.ForeColor = Color.Red;
                        lbllvDownStreamDependencylistInfo.Text = "Please select the Business function before continuing!&nbsp;&nbsp;&nbsp;";
                        return;
                    }
                    foreach (ListViewItem Listitems in lvDownStreamDependencylist.Items)
                    {
                        DropDownList ddlbusinessfunctions = Listitems.FindControl("ddlbusinessfunctions") as DropDownList;
                        if (ddlbusinessfunctions.SelectedIndex > 0)
                        {

                        }
                        else
                        {
                            lbllvDownStreamDependencylistInfo.Visible = true;
                            lbllvDownStreamDependencylistInfo.ForeColor = Color.Red;
                            lbllvDownStreamDependencylistInfo.Text = "Please select the Business function before continuing!&nbsp;&nbsp;&nbsp;";
                            return;
                        }
                    }
                    try
                    {
                        foreach (ListViewItem item in lvDownStreamDependencylist.Items)
                        {
                            BusinessFunctionBIADetails objBusinessFunctionBIADetails = new BusinessFunctionBIADetails();
                            objBusinessFunctionBIADetails = GetApplicationRelationShip(item, false);
                            if (Facade.IsExistBusinessFunctionBIADetailsByID(objBusinessFunctionBIADetails.Id) && ValidateRequest("BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA))
                            {
                                Facade.UpdateBusinessFunctionBIADetails(objBusinessFunctionBIADetails);
                                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIADetails", UserActionType.UpdateBusinessFunctionBIADetails, "BusinessFunctionBIADetails was updated to the BusinessFunctionBIAActivity table", LoggedInUserId);
                            }
                            else
                            {
                                Facade.AddBusinessFunctionBIADetails(objBusinessFunctionBIADetails);
                                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIADetails", UserActionType.CreateBusinessFunctionBIADetails, "BusinessFunctionBIADetails was added to the BusinessFunctionBIAActivity table", LoggedInUserId);
                            }

                            PopulateDownStreamApplication();

                            lbllvDownStreamDependencylistInfo.Visible = true;
                            lbllvDownStreamDependencylistInfo.ForeColor = Color.Green;
                            lbllvDownStreamDependencylistInfo.Text = "Record Saved Successfully&nbsp;&nbsp;&nbsp;";
                        }
                    }
                    catch (CpException ex)
                    {
                        ExceptionManager.Manage(ex, this);
                        lbllvDownStreamDependencylistInfo.Visible = true;
                        lbllvDownStreamDependencylistInfo.ForeColor = Color.Red;
                        lbllvDownStreamDependencylistInfo.Text = "Error Occured While saving Record: " + ex.Message + "&nbsp;&nbsp;&nbsp;";
                    }
                    catch (Exception ex)
                    {
                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, this);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                            ExceptionManager.Manage(customEx, this);
                        }
                        lbllvDownStreamDependencylistInfo.Visible = true;
                        lbllvDownStreamDependencylistInfo.ForeColor = Color.Red;
                        lbllvDownStreamDependencylistInfo.Text = "Error Occured While saving Record: " + ex.Message + "&nbsp;&nbsp;&nbsp;";
                    }
                }
            }
        }

        protected void lnkBtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                ImageButton lnkBtnDelete = (ImageButton)sender;
                ListViewItem item = (ListViewItem)lnkBtnDelete.NamingContainer;
                Label lblID = item.FindControl("lblID") as Label;
                Facade.DeleteBusinessFunctionBIADetailsById(Convert.ToInt32(lblID.Text));
                //{
                if (item.Parent.Parent.ClientID == lvUpStreamDependencylist.ClientID)
                {
                    PopulateUpstreamApplication();
                    lbllvUpStreamDependencylistInfo.Visible = true;
                    lbllvUpStreamDependencylistInfo.ForeColor = Color.Green;
                    lbllvUpStreamDependencylistInfo.Text = "Record Deleted Successfully&nbsp;&nbsp;&nbsp;";

                }
                else
                {
                    PopulateDownStreamApplication();
                    lbllvDownStreamDependencylistInfo.Visible = true;
                    lbllvDownStreamDependencylistInfo.ForeColor = Color.Green;
                    lbllvDownStreamDependencylistInfo.Text = "Record Deleted Successfully&nbsp;&nbsp;&nbsp;";
                }
                //}

            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void SelectedIndexChanged_Rating(object sender, EventArgs e)
        {
            SetResult();
        }

        protected void btnSaveQualitativeBIA_Click(object sender, EventArgs e)
        {
            try
            {
                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {
                        lblInfoQualitativeBIA.Visible = true;
                        string ErrorMsg = string.Empty;
                        if (!AddUpdateBusinessFunctionBIA(ref ErrorMsg, true, false))
                        {
                            lblInfoQualitativeBIA.ForeColor = Color.Red;
                            lblInfoQualitativeBIA.Text = ErrorMsg;
                        }
                        else
                        {
                            lblInfoQualitativeBIA.ForeColor = Color.Green;
                            lblInfoQualitativeBIA.Text = "Record saved Successfully";
                            SetEditView();

                        }
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void btnSaveQuantitativeBIA_Click(object sender, EventArgs e)
        {
            try
            {

                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {
                        lblInfoQuantitaiveBIA.Visible = true;
                        string ErrorMsg = string.Empty;
                        if (!AddUpdateBusinessFunctionBIA(ref ErrorMsg, false, true))
                        {
                            lblInfoQuantitaiveBIA.ForeColor = Color.Red;
                            lblInfoQuantitaiveBIA.Text = ErrorMsg;
                        }
                        else
                        {
                            lblInfoQuantitaiveBIA.ForeColor = Color.Green;
                            lblInfoQuantitaiveBIA.Text = "Record saved Successfully";
                            SetEditView();
                        }
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void btnQualitativeBIAClear_Click(object sender, EventArgs e)
        {
            foreach (GridDataItem item in RgApplicationBIAQualitative.Items)
            {
                DropDownList ddlUpto2Hrs = ((DropDownList)item.FindControl("ddlUpto2Hrs")) as DropDownList;
                DropDownList ddlUpto4Hrs = ((DropDownList)item.FindControl("ddlUpto4Hrs")) as DropDownList;
                DropDownList ddlUpto8Hrs = ((DropDownList)item.FindControl("ddlUpto8Hrs")) as DropDownList;
                DropDownList ddlUpto12Hrs = ((DropDownList)item.FindControl("ddlUpto12Hrs")) as DropDownList;
                DropDownList ddlUpto24Hrs = ((DropDownList)item.FindControl("ddlUpto24Hrs")) as DropDownList;
                DropDownList ddlUpto48Hrs = ((DropDownList)item.FindControl("ddlUpto48Hrs")) as DropDownList;
                DropDownList ddlUpto72Hrs = ((DropDownList)item.FindControl("ddlUpto72Hrs")) as DropDownList;
                DropDownList ddlUpto1Week = ((DropDownList)item.FindControl("ddlUpto1Week")) as DropDownList;
                DropDownList ddlUpto2Weeks = ((DropDownList)item.FindControl("ddlUpto2Weeks")) as DropDownList;
                DropDownList ddlUpto1Month = ((DropDownList)item.FindControl("ddlUpto1Month")) as DropDownList;
                RadTextBox txtComment = ((RadTextBox)item.FindControl("txtComment")) as RadTextBox;
                ddlUpto2Hrs.SelectedIndex = 0;
                ddlUpto4Hrs.SelectedIndex = 0;
                ddlUpto8Hrs.SelectedIndex = 0;
                ddlUpto12Hrs.SelectedIndex = 0;
                ddlUpto24Hrs.SelectedIndex = 0;
                ddlUpto48Hrs.SelectedIndex = 0;
                ddlUpto72Hrs.SelectedIndex = 0;
                ddlUpto1Week.SelectedIndex = 0;
                ddlUpto2Weeks.SelectedIndex = 0;
                ddlUpto1Month.SelectedIndex = 0;
                txtComment.Text = string.Empty;
            }

            txtCalculatedRTO.Text = string.Empty;
            txtCalculatedMTR.Text = string.Empty;

            UpdatePanel4.Update();
        }

        protected void btnCancelQuantitativeBIAClear_Click(object sender, EventArgs e)
        {
            foreach (GridDataItem item in RgApplicationBIAQuantitative.Items)
            {
                RadNumericTextBox txtUpTo2hrs = item.FindControl("txtUpTo2hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpTo4hrs = item.FindControl("txtUpTo4hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpTo8hrs = item.FindControl("txtUpTo8hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpTo12hrs = item.FindControl("txtUpTo12hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpTo24hrs = item.FindControl("txtUpTo24hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpTo48hrs = item.FindControl("txtUpTo48hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpTo72hrs = item.FindControl("txtUpTo72hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpTo1Week = item.FindControl("txtUpTo1Week") as RadNumericTextBox;
                RadNumericTextBox txtUpTo2Week = item.FindControl("txtUpTo2Week") as RadNumericTextBox;
                RadNumericTextBox txtUpTo1month = item.FindControl("txtUpTo1month") as RadNumericTextBox;
                RadTextBox txtComment = item.FindControl("txtComment") as RadTextBox;
                txtUpTo2hrs.Value = 0;
                txtUpTo4hrs.Value = 0;
                txtUpTo8hrs.Value = 0;
                txtUpTo12hrs.Value = 0;
                txtUpTo24hrs.Value = 0;
                txtUpTo48hrs.Value = 0;
                txtUpTo72hrs.Value = 0;
                txtUpTo1Week.Value = 0;
                txtUpTo2Week.Value = 0;
                txtUpTo1month.Value = 0;
                txtComment.Text = string.Empty;
            }

            UpdatePanel5.Update();
        }

        protected void btnCalculateCost_Click(object sender, EventArgs e)
        {
            try
            {
                int iUpto2Hours = 0;
                int iUpto4Hours = 0;
                int iUpto8Hours = 0;
                int iUpto12Hours = 0;
                int iUpto24Hours = 0;
                int iUpto48Hours = 0;
                int iUpto72Hours = 0;
                int iUpto1Week = 0;
                int iUpto2Weeks = 0;
                int iUpto1Month = 0;

                RadNumericTextBox txtUpto2HoursTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo2hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpto4HoursTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo4hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpto8HoursTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo8hrs") as RadNumericTextBox;

                RadNumericTextBox txtUpto12HoursTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo12hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpto24HoursTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo24hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpto48HoursTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo48hrs") as RadNumericTextBox;
                RadNumericTextBox txtUpto72HoursTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo72hrs") as RadNumericTextBox;

                RadNumericTextBox txtUpto1WeekTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo1Week") as RadNumericTextBox;
                RadNumericTextBox txtUpto2WeeksTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo2Week") as RadNumericTextBox;
                RadNumericTextBox txtUpto1MonthTotal = RgApplicationBIAQuantitative.MasterTableView.GetItems(GridItemType.Footer)[0].FindControl("fttxtUpTo1month") as RadNumericTextBox;


                foreach (GridDataItem item in RgApplicationBIAQuantitative.Items)
                {
                    RadNumericTextBox txtUpto2Hours = item.FindControl("txtUpTo2hrs") as RadNumericTextBox;
                    RadNumericTextBox txtUpto4Hours = item.FindControl("txtUpTo4hrs") as RadNumericTextBox;
                    RadNumericTextBox txtUpto8Hours = item.FindControl("txtUpTo8hrs") as RadNumericTextBox;
                    RadNumericTextBox txtUpto12Hours = item.FindControl("txtUpTo12hrs") as RadNumericTextBox;
                    RadNumericTextBox txtUpto24Hours = item.FindControl("txtUpTo24hrs") as RadNumericTextBox;
                    RadNumericTextBox txtUpto48Hours = item.FindControl("txtUpTo48hrs") as RadNumericTextBox;
                    RadNumericTextBox txtUpto72Hours = item.FindControl("txtUpTo72hrs") as RadNumericTextBox;
                    RadNumericTextBox txtUpto1Week = item.FindControl("txtUpTo1Week") as RadNumericTextBox;
                    RadNumericTextBox txtUpto2Weeks = item.FindControl("txtUpTo2Week") as RadNumericTextBox;
                    RadNumericTextBox txtUpto1Month = item.FindControl("txtUpTo1month") as RadNumericTextBox;

                    iUpto2Hours += Convert.ToInt32(string.IsNullOrEmpty(txtUpto2Hours.Text) ? "0" : txtUpto2Hours.Text);
                    iUpto4Hours += Convert.ToInt32(string.IsNullOrEmpty(txtUpto4Hours.Text) ? "0" : txtUpto4Hours.Text);
                    iUpto8Hours += Convert.ToInt32(string.IsNullOrEmpty(txtUpto8Hours.Text) ? "0" : txtUpto8Hours.Text);
                    iUpto12Hours += Convert.ToInt32(string.IsNullOrEmpty(txtUpto12Hours.Text) ? "0" : txtUpto12Hours.Text);
                    iUpto24Hours += Convert.ToInt32(string.IsNullOrEmpty(txtUpto24Hours.Text) ? "0" : txtUpto24Hours.Text);
                    iUpto48Hours += Convert.ToInt32(string.IsNullOrEmpty(txtUpto48Hours.Text) ? "0" : txtUpto48Hours.Text);
                    iUpto72Hours += Convert.ToInt32(string.IsNullOrEmpty(txtUpto72Hours.Text) ? "0" : txtUpto72Hours.Text);
                    iUpto1Week += Convert.ToInt32(string.IsNullOrEmpty(txtUpto1Week.Text) ? "0" : txtUpto1Week.Text);
                    iUpto2Weeks += Convert.ToInt32(string.IsNullOrEmpty(txtUpto2Weeks.Text) ? "0" : txtUpto2Weeks.Text);
                    iUpto1Month += Convert.ToInt32(string.IsNullOrEmpty(txtUpto1Month.Text) ? "0" : txtUpto1Month.Text);
                }

                txtUpto2HoursTotal.Text = iUpto2Hours.ToString();
                txtUpto4HoursTotal.Text = iUpto4Hours.ToString();
                txtUpto8HoursTotal.Text = iUpto8Hours.ToString();
                txtUpto12HoursTotal.Text = iUpto12Hours.ToString();
                txtUpto24HoursTotal.Text = iUpto24Hours.ToString();
                txtUpto48HoursTotal.Text = iUpto48Hours.ToString();
                txtUpto72HoursTotal.Text = iUpto72Hours.ToString();
                txtUpto1WeekTotal.Text = iUpto1Week.ToString();
                txtUpto2WeeksTotal.Text = iUpto2Weeks.ToString();
                txtUpto1MonthTotal.Text = iUpto1Month.ToString();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void ddlImactTypeQA_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            PopulateApplicationBIA(true, false);
        }

        protected void ddlImactTypeQN_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            PopulateApplicationBIA(false, true);
        }

        #region ItemDataBound

        protected void lvUpStreamDependencylist_OnItemDataBound(object sender, ListViewItemEventArgs e)
        {
            try
            {
                if (e.Item.ItemType == ListViewItemType.DataItem)
                {
                    DropDownList ddlbusinessfunctions = e.Item.FindControl("ddlbusinessfunctions") as DropDownList;
                    DropDownList ddlRelationalImpact = e.Item.FindControl("ddlRelationalImpact") as DropDownList;
                    DropDownList ddlRTO = e.Item.FindControl("ddlRTO") as DropDownList;

                    Label lblBusinessFunctionID = e.Item.FindControl("lblBusinessFunctionID") as Label;
                    Label lblRTO = e.Item.FindControl("lblRTO") as Label;
                    Label lblRelID = e.Item.FindControl("lblRelID") as Label;

                    Utility.PopulateImpactRelType(ddlRelationalImpact, true);
                    Utility.PopulateBusinessFunction(ddlbusinessfunctions, true, IsUserSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany);
                    Utility.PopulateRTO(ddlRTO);

                    ddlbusinessfunctions.SelectedValue = lblBusinessFunctionID.Text;
                    ddlRelationalImpact.SelectedValue = lblRelID.Text;
                    ddlRTO.SelectedValue = lblRTO.Text;
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void lvDownStreamDependencylist_OnItemDataBound(object sender, ListViewItemEventArgs e)
        {
            try
            {
                if (e.Item.ItemType == ListViewItemType.DataItem)
                {
                    DropDownList ddlbusinessfunctions = e.Item.FindControl("ddlbusinessfunctions") as DropDownList;
                    DropDownList ddlRelationalImpact = e.Item.FindControl("ddlRelationalImpact") as DropDownList;
                    DropDownList ddlRTO = e.Item.FindControl("ddlRTO") as DropDownList;

                    Label lblApplication = e.Item.FindControl("lblBusinessFunction") as Label;
                    Label lblRTO = e.Item.FindControl("lblRTO") as Label;
                    Label lblRelID = e.Item.FindControl("lblRelID") as Label;

                    Utility.PopulateImpactRelType(ddlRelationalImpact, true);
                    Utility.PopulateBusinessFunction(ddlbusinessfunctions, true, IsUserSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany);
                    Utility.PopulateRTO(ddlRTO);

                    ddlbusinessfunctions.SelectedValue = lblApplication.Text;
                    ddlRelationalImpact.SelectedValue = lblRelID.Text;
                    ddlRTO.SelectedValue = lblRTO.Text;
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void RgApplicationBIAQualitative_ItemDataBound(object sender, Telerik.Web.UI.GridItemEventArgs args)
        {
            try
            {
                ScriptManager smDefault = Master.FindControl("smDefault") as ScriptManager;

                if (smDefault != null)
                {
                    DropDownList ddlUpTO2hrs = args.Item.FindControl("ddlUpTO2hrs") as DropDownList;
                    if (ddlUpTO2hrs != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTO2hrs);
                        Label lblSelectedValueUpTo2hrs = args.Item.FindControl("lblSelectedValueUpTo2hrs") as Label;
                        Utility.PopulateImpactRating(ddlUpTO2hrs);
                        ddlUpTO2hrs.SelectedValue = lblSelectedValueUpTo2hrs.Text;
                    }

                    DropDownList ddlUpTo4hrs = args.Item.FindControl("ddlUpTo4hrs") as DropDownList;
                    if (ddlUpTo4hrs != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo4hrs);
                        Label lblSelectedValueUpTo4hrs = args.Item.FindControl("lblSelectedValueUpTo4hrs") as Label;
                        Utility.PopulateImpactRating(ddlUpTo4hrs);
                        ddlUpTo4hrs.SelectedValue = lblSelectedValueUpTo4hrs.Text;
                    }

                    DropDownList ddlUpTo8hrs = args.Item.FindControl("ddlUpTo8hrs") as DropDownList;
                    if (ddlUpTo8hrs != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo8hrs);
                        Label lblSelectedValueUpTo8hrs = args.Item.FindControl("lblSelectedValueUpTo8hrs") as Label;
                        Utility.PopulateImpactRating(ddlUpTo8hrs);
                        ddlUpTo8hrs.SelectedValue = lblSelectedValueUpTo8hrs.Text;
                    }

                    DropDownList ddlUpTo12hrs = args.Item.FindControl("ddlUpTo12hrs") as DropDownList;
                    if (ddlUpTo12hrs != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo12hrs);
                        Label lblSelectedValueUpTo12hrs = args.Item.FindControl("lblSelectedValueUpTo12hrs") as Label;
                        Utility.PopulateImpactRating(ddlUpTo12hrs);
                        ddlUpTo12hrs.SelectedValue = lblSelectedValueUpTo12hrs.Text;
                    }

                    DropDownList ddlUpTo24hrs = args.Item.FindControl("ddlUpTo24hrs") as DropDownList;
                    if (ddlUpTo24hrs != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo24hrs);
                        Label lblSelectedValueUpTo24hrs = args.Item.FindControl("lblSelectedValueUpTo24hrs") as Label;
                        Utility.PopulateImpactRating(ddlUpTo24hrs);
                        ddlUpTo24hrs.SelectedValue = lblSelectedValueUpTo24hrs.Text;
                    }

                    DropDownList ddlUpTo48hrs = args.Item.FindControl("ddlUpTo48hrs") as DropDownList;
                    if (ddlUpTo48hrs != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo48hrs);
                        Label lblSelectedValueUpTo48hrs = args.Item.FindControl("lblSelectedValueUpTo48hrs") as Label;
                        Utility.PopulateImpactRating(ddlUpTo48hrs);
                        ddlUpTo48hrs.SelectedValue = lblSelectedValueUpTo48hrs.Text;
                    }

                    DropDownList ddlUpTo72hrs = args.Item.FindControl("ddlUpTo72hrs") as DropDownList;
                    if (ddlUpTo72hrs != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo72hrs);
                        Label lblSelectedValueUpTo72hrs = args.Item.FindControl("lblSelectedValueUpTo72hrs") as Label;
                        Utility.PopulateImpactRating(ddlUpTo72hrs);
                        ddlUpTo72hrs.SelectedValue = lblSelectedValueUpTo72hrs.Text;
                    }

                    DropDownList ddlUpTo1Week = args.Item.FindControl("ddlUpTo1Week") as DropDownList;
                    if (ddlUpTo1Week != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo1Week);
                        Label lblSelectedValueUpTo1Week = args.Item.FindControl("lblSelectedValueUpTo1Week") as Label;
                        Utility.PopulateImpactRating(ddlUpTo1Week);
                        ddlUpTo1Week.SelectedValue = lblSelectedValueUpTo1Week.Text;
                    }

                    DropDownList ddlUpTo2Weeks = args.Item.FindControl("ddlUpTo2Weeks") as DropDownList;
                    if (ddlUpTo2Weeks != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo2Weeks);
                        Label lblSelectedValueUpTo2Weeks = args.Item.FindControl("lblSelectedValueUpTo2Weeks") as Label;
                        Utility.PopulateImpactRating(ddlUpTo2Weeks);
                        ddlUpTo2Weeks.SelectedValue = lblSelectedValueUpTo2Weeks.Text;
                    }

                    DropDownList ddlUpTo1Month = args.Item.FindControl("ddlUpTo1Month") as DropDownList;
                    if (ddlUpTo1Month != null)
                    {
                        smDefault.RegisterAsyncPostBackControl(ddlUpTo1Month);
                        Label lblSelectedValueUpTo1Month = args.Item.FindControl("lblSelectedValueUpTo1Month") as Label;
                        Utility.PopulateImpactRating(ddlUpTo1Month);
                        ddlUpTo1Month.SelectedValue = lblSelectedValueUpTo1Month.Text;
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        #endregion

        #region AddRemoveRows

        protected void btnAddNewRowlvActivitylist_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dtActivity = (DataTable)GetActivityData(lvActivitylist);
                dtActivity.Rows.Add(0, "");
                lvActivitylist.DataSource = dtActivity;
                lvActivitylist.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void btnCancelAddNewRowlvActivitylist_Click(object sender, EventArgs e)
        {
            try
            {
                ListView lvActivitylist = ((Button)sender).NamingContainer.FindControl("lvActivitylist") as ListView;
                DataTable dtActivity = GetActivityData(lvActivitylist);
                if (dtActivity.Rows.Count > 0)
                {
                    var objId = dtActivity.Rows[dtActivity.Rows.Count - 1]["ActivityID"];
                    if (Convert.ToInt32(objId) > 0)
                    {
                        var delete = Facade.DeleteBusinessFunctionActivityBIAById(Convert.ToInt32(objId));
                        dtActivity.Rows.RemoveAt(dtActivity.Rows.Count - 1);
                    }
                    else
                    {
                        dtActivity.Rows.RemoveAt(dtActivity.Rows.Count - 1);
                    }
                }
                lvActivitylist.DataSource = dtActivity;
                lvActivitylist.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }

        }

        protected void btnAddNewRowlvUpStreamDependencylist_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dtStreamData = (DataTable)GetApplicationStreamData(lvUpStreamDependencylist, 1);
                dtStreamData.Rows.Add("0", "0", "0", "0", "0", "0", "", "0", "", "0", "0", "0", "0", "0", "0");
                lvUpStreamDependencylist.DataSource = dtStreamData;
                lvUpStreamDependencylist.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void btnCancelAddNewRowlvUpStreamDependencylist_Click(object sender, EventArgs e)
        {
            try
            {
                ListViewItem item = (ListViewItem)((Button)sender).NamingContainer;
                ListView lvUpStreamDependencylist = item.FindControl("lvUpStreamDependencylist") as ListView;
                DataTable dtUpStreamDependencylist = (DataTable)GetUpstreamData(lvUpStreamDependencylist);
                if (dtUpStreamDependencylist.Rows.Count > 0)
                {
                    var objID = dtUpStreamDependencylist.Rows[dtUpStreamDependencylist.Rows.Count - 1]["ID"];
                    string strID = objID.ToString() == null ? "" : objID.ToString();
                    if (strID != string.Empty)
                    {
                        dtUpStreamDependencylist.Rows.RemoveAt(dtUpStreamDependencylist.Rows.Count - 1);
                    }
                    else
                    {
                        dtUpStreamDependencylist.Rows.RemoveAt(dtUpStreamDependencylist.Rows.Count - 1);
                    }
                }
                lvUpStreamDependencylist.DataSource = dtUpStreamDependencylist;
                lvUpStreamDependencylist.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void btnAddNewRowlvDownStreamDependencylist_Click(object sender, EventArgs e)
        {
            try
            {
                DataTable dtDownStreamDependencylist = (DataTable)GetApplicationStreamData(lvDownStreamDependencylist, 0);
                dtDownStreamDependencylist.Rows.Add("0", "0", "0", "0", "0", "0", "", "0", "", "0", "0", "0", "0", "0", "0");
                lvDownStreamDependencylist.DataSource = dtDownStreamDependencylist;
                lvDownStreamDependencylist.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void btnCancelAddNewRowlvDownStreamDependencylist_Click(object sender, EventArgs e)
        {
            try
            {
                ListViewItem item = (ListViewItem)((Button)sender).NamingContainer;
                ListView lvDownStreamDependencylist = item.FindControl("lvDownStreamDependencylist") as ListView;
                DataTable dtDownStreamDependencylist = (DataTable)GetUpstreamData(lvUpStreamDependencylist);
                if (dtDownStreamDependencylist.Rows.Count > 0)
                {
                    var objID = dtDownStreamDependencylist.Rows[dtDownStreamDependencylist.Rows.Count - 1]["ID"];
                    string strID = objID.ToString() == null ? "" : objID.ToString();
                    if (strID != string.Empty)
                    {
                        //var delete = _oProcSrv.BIASurveyQ2Delete(Convert.ToInt32(strID));
                        dtDownStreamDependencylist.Rows.RemoveAt(dtDownStreamDependencylist.Rows.Count - 1);
                    }
                    else
                    {
                        dtDownStreamDependencylist.Rows.RemoveAt(dtDownStreamDependencylist.Rows.Count - 1);
                    }
                }
                lvDownStreamDependencylist.DataSource = dtDownStreamDependencylist;
                lvDownStreamDependencylist.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        protected void lvFunctionsList_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                //LinkButton lnkimgimpact = e.Item.FindControl("lnkimgimpact") as LinkButton;

                HtmlAnchor anchviewdependency = e.Item.FindControl("anchviewdependency") as HtmlAnchor;
                Label lblFunctionID = e.Item.FindControl("lblFunctionID") as Label;
                if (anchviewdependency != null && lblFunctionID != null)
                {

                    string biaurl = "../ImpactAnalysis/ConfigureBIARule.aspx";

                    SecureUrl BIAIncUrl = UrlHelper.BuildSecureUrl(biaurl, string.Empty, Constants.UrlConstants.Params.BusinessfunctionId, "");
                    anchviewdependency.HRef = BIAIncUrl.ToString();

                    //var secureDbMonitorUrl = UrlHelper.BuildSecureUrl(Constants.UrlConstants.Urls.Admin.BIAFunctionDependency,
                    //                                                                      string.Empty, Constants.UrlConstants.Params.BusinessfunctionId,
                    //                                                                      lblFunctionID.Text.ToString());
                    //anchviewdependency.Attributes.Add("onclick", string.Format("openRadWindow('{0}', 'TelRadWindow');", secureDbMonitorUrl.ToString()));
                }
            }
        }

        #endregion

        #endregion

        #region Private Methods

        private void PopulateFunctionsList()
        {
            try
            {
                IList<BusinessFunction> bussinessfunctionlist = new List<BusinessFunction>();
                bussinessfunctionlist = IsUserSuperAdmin ? Facade.GetAllBusinessFunctions() : Facade.GetAllBusinessFunctionByCompanyIdAndRole(LoggedInUserCompanyId, LoggedInUserCompany.IsParent);
                if (bussinessfunctionlist != null)
                {
                    lvFunctionsList.DataSource = bussinessfunctionlist;
                    lvFunctionsList.DataBind();
                }
                else
                {
                    dataPagerFunctionsList1.Visible = false;
                    dataPagerFunctionsList2.Visible = false;
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        private void PopulateSection()
        {
            PopulateActivities();
            PopulateUpstreamApplication();
            PopulateDownStreamApplication();
            PopulateApplicationBIA(true, false);
            PopulateApplicationBIA(false, true);
        }

        private void PopulateActivities()
        {
            try
            {
                DataTable dtActivity = new DataTable();
                dtActivity.Columns.Add("ActivityID");
                dtActivity.Columns.Add("ActivityDetails");

                IList<BusinessFunctionBIAActivity> lstbusinessfunctionbiaActivity = new List<BusinessFunctionBIAActivity>();
                if (!String.IsNullOrEmpty(lblfunctionheadid.Text))
                {
                    lstbusinessfunctionbiaActivity = Facade.GetBusinessFunctionActivityBIAByBusinessFunctionId(Convert.ToInt32(lblfunctionheadid.Text));
                }

                if (lstbusinessfunctionbiaActivity != null)
                {
                    foreach (BusinessFunctionBIAActivity obj in lstbusinessfunctionbiaActivity)
                    {
                        dtActivity.Rows.Add(obj.Id, obj.ActivityDescription);
                    }
                }
                if (dtActivity.Rows.Count == 0)
                {
                    dtActivity.Rows.Add("0", "");
                    dtActivity.Rows.Add("0", "");
                    dtActivity.Rows.Add("0", "");
                    dtActivity.Rows.Add("0", "");
                    dtActivity.Rows.Add("0", "");
                }
                ViewState["dtActivity"] = dtActivity;
                lvActivitylist.DataSource = dtActivity;
                lvActivitylist.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        private void PopulateImpactTypes()
        {
            Utility.PopulateImpactRelType(ddlImactTypeQN, false);
            Utility.PopulateImpactRelType(ddlImactTypeQA, false);
        }

        private void PopulateUpstreamApplication()
        {
            DataTable dtUpstreamApps = GetAppStreamData(1);
            lvUpStreamDependencylist.DataSource = dtUpstreamApps;
            lvUpStreamDependencylist.DataBind();
        }

        private void PopulateDownStreamApplication()
        {
            DataTable dtUpstreamApps = GetAppStreamData(0);
            lvDownStreamDependencylist.DataSource = dtUpstreamApps;
            lvDownStreamDependencylist.DataBind();
        }

        private void PopulateApplicationBIA(bool IsQualitative, bool IsQuantitative)
        {
            try
            {
                DataTable dtBusinessFunctionBIA = new DataTable();
                dtBusinessFunctionBIA.Columns.Add("ID");
                dtBusinessFunctionBIA.Columns.Add("BIAID");
                dtBusinessFunctionBIA.Columns.Add("ImpactTypeID");
                dtBusinessFunctionBIA.Columns.Add("ImpactTypeName");
                dtBusinessFunctionBIA.Columns.Add("ImpactID");
                dtBusinessFunctionBIA.Columns.Add("ImpactName");
                dtBusinessFunctionBIA.Columns.Add("Upto2Hours");
                dtBusinessFunctionBIA.Columns.Add("Upto4Hours");
                dtBusinessFunctionBIA.Columns.Add("Upto8Hours");
                dtBusinessFunctionBIA.Columns.Add("Upto12Hours");
                dtBusinessFunctionBIA.Columns.Add("Upto24Hours");
                dtBusinessFunctionBIA.Columns.Add("Upto48Hours");
                dtBusinessFunctionBIA.Columns.Add("Upto72Hours");
                dtBusinessFunctionBIA.Columns.Add("UpTo1Week");
                dtBusinessFunctionBIA.Columns.Add("UpTo2Weeks");
                dtBusinessFunctionBIA.Columns.Add("UpTo1Month");
                dtBusinessFunctionBIA.Columns.Add("Comment");

                IList<BusinessFunctionBIA> lstBusinessFunctionBIA = null;
                int biaid = 0;

                if (!string.IsNullOrEmpty(lblfunctionheadid.Text) && Convert.ToInt32(lblfunctionheadid.Text) > 0)
                {
                    if (IsQualitative)
                    {
                        biaid = Convert.ToInt32(string.Format("{0}{1}", lblfunctionheadid.Text, ddlImactTypeQA.SelectedValue));
                        lstBusinessFunctionBIA = Facade.GetBusinessFunctionBIAByBussFunctidandBIAID(Convert.ToInt32(lblfunctionheadid.Text), biaid, 1, 0);

                    }
                    else
                    {
                        biaid = Convert.ToInt32(string.Format("{0}{1}", lblfunctionheadid.Text, ddlImactTypeQN.SelectedValue));
                        lstBusinessFunctionBIA = Facade.GetBusinessFunctionBIAByBussFunctidandBIAID(Convert.ToInt32(lblfunctionheadid.Text), biaid, 0, 1);
                    }
                }

                if (lstBusinessFunctionBIA != null)
                {
                    foreach (BusinessFunctionBIA objBusinessFunctionBIA in lstBusinessFunctionBIA)
                    {
                        dtBusinessFunctionBIA.Rows.Add(objBusinessFunctionBIA.Id, objBusinessFunctionBIA.BIAID, objBusinessFunctionBIA.ImpactTypeID, objBusinessFunctionBIA.ImpactTypeMaster.ImpactTypeName, objBusinessFunctionBIA.ImpactID, objBusinessFunctionBIA.ImpactMaster.ImpactName, objBusinessFunctionBIA.Upto2hrs, objBusinessFunctionBIA.Upto4hrs, objBusinessFunctionBIA.Upto8hrs, objBusinessFunctionBIA.Upto12hrs, objBusinessFunctionBIA.Upto24hrs, objBusinessFunctionBIA.Upto48hrs, objBusinessFunctionBIA.Upto72hrs, objBusinessFunctionBIA.Upto1week,
                                objBusinessFunctionBIA.Upto2weeks, objBusinessFunctionBIA.Upto1Month, objBusinessFunctionBIA.Comment);

                    }
                }

                if (dtBusinessFunctionBIA.Rows.Count == 0)
                {
                    IList<ImpactMaster> lstImpactMaster = new List<ImpactMaster>();
                    lstImpactMaster = Facade.GetAllWithImpactType();
                    if (lstImpactMaster != null)
                    {
                        foreach (ImpactMaster objImpactMaster in lstImpactMaster)
                        {
                            dtBusinessFunctionBIA.Rows.Add(0, 0, objImpactMaster.ImpactTypeMaster.Id, objImpactMaster.ImpactTypeMaster.ImpactTypeName, objImpactMaster.Id, objImpactMaster.ImpactName, "", "", "", "", "", "", "", "", "", "", "");
                        }
                    }
                }

                if (IsQualitative)
                {
                    RgApplicationBIAQualitative.DataSource = dtBusinessFunctionBIA;
                    RgApplicationBIAQualitative.DataBind();
                }
                else
                {
                    RgApplicationBIAQuantitative.DataSource = dtBusinessFunctionBIA;
                    RgApplicationBIAQuantitative.DataBind();
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        private DataTable GetAppStreamData(int isInBond)
        {

            DataTable dtStreamData = new DataTable();
            dtStreamData.Columns.Add("ID");
            dtStreamData.Columns.Add("BusinessFunctionID");
            dtStreamData.Columns.Add("InBondAppID");
            dtStreamData.Columns.Add("OutBondAppID");
            dtStreamData.Columns.Add("RelationID");
            dtStreamData.Columns.Add("InBound");
            dtStreamData.Columns.Add("DataSource");
            dtStreamData.Columns.Add("IsRealTime");
            dtStreamData.Columns.Add("AreaofFocus");
            dtStreamData.Columns.Add("CriticalForBCM");
            dtStreamData.Columns.Add("BCMScope");
            dtStreamData.Columns.Add("Supported");
            dtStreamData.Columns.Add("RTO");
            dtStreamData.Columns.Add("IsCritical");
            dtStreamData.Columns.Add("IsDRRequired");
            dtStreamData.Columns.Add("RelTypeID");

            try
            {

                IList<BusinessFunctionBIADetails> lstBusinessFunctionBIADetails = new List<BusinessFunctionBIADetails>();
                if (!String.IsNullOrEmpty(lblfunctionheadid.Text))
                {
                    lstBusinessFunctionBIADetails = Facade.GetBusinessFunctionBIADetailsByBusinessFunctionId(Convert.ToInt32(lblfunctionheadid.Text));
                }

                if (lstBusinessFunctionBIADetails != null)
                {
                    foreach (BusinessFunctionBIADetails obj in lstBusinessFunctionBIADetails)
                    {
                        if (isInBond.ToString().Equals(obj.InBound.ToString()))
                        {
                            dtStreamData.Rows.Add(obj.Id, obj.BusinessFunctionID, obj.InBondAppID, obj.OutBondAppID, obj.RelationID, obj.InBound, obj.DataSource, obj.IsRealTime, obj.AreaofFocus,
                                obj.CriticalForBCM, obj.BCMScope, obj.Supported, obj.RTO, obj.IsCritical, obj.IsDRRequired, obj.RelTypeID);
                        }
                    }

                }
                if (dtStreamData.Rows.Count == 0)
                {
                    dtStreamData.Rows.Add("0", "0", "0", "0", "0", "0", "", "0", "", "0", "0", "0", "0", "0", "0", "0");
                }

                ViewState["AppData"] = dtStreamData;

                return dtStreamData;
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }

            return dtStreamData;
        }

        private DataTable GetApplicationStreamData(ListView lv, int iInbound)
        {
            DataTable dtAppData = ((DataTable)ViewState["AppData"]).Clone();

            try
            {
                foreach (ListViewItem item in lv.Items)
                {
                    string sIsCritical = "-1";
                    string sIsDRRequired = "-1";
                    string sInbondAppID = "0";
                    string sInbondImpactID = "0";
                    string sOutBondAppID = "0";
                    Label lblID = item.FindControl("lblID") as Label;
                    Label lblRelationID = item.FindControl("lblRelationID") as Label;
                    DropDownList ddlApplication = item.FindControl("ddlbusinessfunctions") as DropDownList;
                    DropDownList ddlRelationalImpact = item.FindControl("ddlRelationalImpact") as DropDownList;
                    DropDownList ddlIsRealTimeProcess = item.FindControl("ddlIsRealTimeProcess") as DropDownList;
                    DropDownList ddlCriticalForBCM = item.FindControl("ddlCriticalForBCM") as DropDownList;
                    DropDownList ddlBCMScope = item.FindControl("ddlBCMScope") as DropDownList;
                    DropDownList ddlSupported = item.FindControl("ddlSupported") as DropDownList;
                    DropDownList ddlRTO = item.FindControl("ddlRTO") as DropDownList;

                    if (iInbound == 1)
                    {
                        DropDownList ddlIsCritical = item.FindControl("ddlIsCritical") as DropDownList;
                        DropDownList ddlDRRequired = item.FindControl("ddlDRRequired") as DropDownList;
                        sIsCritical = ddlIsCritical.SelectedValue;
                        sIsDRRequired = ddlIsCritical.SelectedValue;
                        sInbondAppID = lblfunctionheadid.Text;
                        sOutBondAppID = ddlApplication.SelectedValue;
                        sInbondImpactID = ddlRelationalImpact.SelectedValue;
                    }
                    else
                    {
                        sInbondImpactID = ddlRelationalImpact.SelectedValue;
                        sInbondAppID = ddlApplication.SelectedValue;
                        sOutBondAppID = lblfunctionheadid.Text;
                    }

                    TextBox txtInputDataSource = item.FindControl("txtInputDataSource") as TextBox;
                    TextBox txtAreaoffocus = item.FindControl("txtAreaoffocus") as TextBox;

                    dtAppData.Rows.Add(lblID.Text, lblfunctionheadid.Text, sInbondAppID, sOutBondAppID, lblRelationID.Text, iInbound.ToString(), txtInputDataSource.Text, ddlIsRealTimeProcess.SelectedValue, txtAreaoffocus.Text,
                          ddlCriticalForBCM.SelectedValue, ddlBCMScope.SelectedValue, ddlSupported.SelectedValue, ddlRTO.SelectedValue, sIsCritical, sIsDRRequired, sInbondImpactID);
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }

            return dtAppData;
        }

        private BusinessFunctionBIADetails GetApplicationRelationShip(ListViewItem item, bool IsInbond)
        {
            BusinessFunctionBIADetails objAppDependency = null;
            try
            {
                Label lblID = item.FindControl("lblID") as Label;
                Label lblRelationID = item.FindControl("lblRelationID") as Label;
                DropDownList ddlbusinessfunctions = item.FindControl("ddlbusinessfunctions") as DropDownList;
                DropDownList ddlRelationalImpact = item.FindControl("ddlRelationalImpact") as DropDownList;
                DropDownList ddlIsRealTimeProcess = item.FindControl("ddlIsRealTimeProcess") as DropDownList;
                DropDownList ddlCriticalForBCM = item.FindControl("ddlCriticalForBCM") as DropDownList;
                DropDownList ddlBCMScope = item.FindControl("ddlBCMScope") as DropDownList;
                DropDownList ddlSupported = item.FindControl("ddlSupported") as DropDownList;
                DropDownList ddlRTO = item.FindControl("ddlRTO") as DropDownList;
                DropDownList ddlIsCritical = item.FindControl("ddlIsCritical") as DropDownList;
                DropDownList ddlDRRequired = item.FindControl("ddlDRRequired") as DropDownList;
                TextBox txtInputDataSource = item.FindControl("txtInputDataSource") as TextBox;
                TextBox txtAreaoffocus = item.FindControl("txtAreaoffocus") as TextBox;


                if (ddlbusinessfunctions.SelectedIndex > 0)
                {
                    BusinessFunctionBIARelation objBusinessFunctionBIARelation = new BusinessFunctionBIARelation();
                    objAppDependency = new BusinessFunctionBIADetails();

                    objBusinessFunctionBIARelation.Id = Convert.ToInt32(string.IsNullOrEmpty(lblRelationID.Text) ? "0" : lblRelationID.Text);
                    if (IsInbond)
                    {
                        objBusinessFunctionBIARelation.ParentAppID = Convert.ToInt32(ddlbusinessfunctions.SelectedValue);
                        objBusinessFunctionBIARelation.ChildAppID = Convert.ToInt32(lblfunctionheadid.Text);
                    }
                    else
                    {
                        objBusinessFunctionBIARelation.ParentAppID = Convert.ToInt32(lblfunctionheadid.Text);
                        objBusinessFunctionBIARelation.ChildAppID = Convert.ToInt32(ddlbusinessfunctions.SelectedValue);
                    }
                    objBusinessFunctionBIARelation.RelTypeID = Convert.ToInt32(ddlRelationalImpact.SelectedValue);
                    objBusinessFunctionBIARelation.CreatorId = LoggedInUserId;
                    objBusinessFunctionBIARelation.UpdatorId = LoggedInUserId;

                    if (Facade.IsExistBusinessFunctionBIARelationByID(objBusinessFunctionBIARelation.Id))
                    {
                        Facade.UpdateBusinessFunctionBIARelation(objBusinessFunctionBIARelation);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIARelation", UserActionType.UpdateBusinessFunctionBIARelation, "BusinessFunctionBIAActivity was Updated to the BusinessFunctionsBIA table", LoggedInUserId);
                    }
                    else
                    {
                        Facade.AddBusinessFunctionBIARelation(objBusinessFunctionBIARelation);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIARelation", UserActionType.CreateBusinessFunctionBIARelation, "BusinessFunctionBIAActivity was added to the BusinessFunctionsBIA table", LoggedInUserId);
                    }

                    if (objBusinessFunctionBIARelation.Id > 0)
                    {
                        objAppDependency.Id = Convert.ToInt32(string.IsNullOrEmpty(lblID.Text) ? "0" : lblID.Text);
                        objAppDependency.BusinessFunctionID = Convert.ToInt32(lblfunctionheadid.Text);
                        objAppDependency.RelationID = objBusinessFunctionBIARelation.Id;
                        objAppDependency.InBound = Convert.ToInt32(IsInbond ? "1" : "0");
                        objAppDependency.AreaofFocus = txtAreaoffocus.Text;
                        objAppDependency.DataSource = txtInputDataSource.Text;
                        objAppDependency.AreaofFocus = txtAreaoffocus.Text;
                        objAppDependency.IsRealTime = Convert.ToInt32(ddlIsRealTimeProcess.SelectedValue);
                        objAppDependency.CriticalForBCM = Convert.ToInt32(ddlCriticalForBCM.SelectedValue);
                        objAppDependency.BCMScope = Convert.ToInt32(ddlBCMScope.SelectedValue);
                        objAppDependency.Supported = Convert.ToInt32(ddlSupported.SelectedValue);
                        objAppDependency.RTO = Convert.ToInt32(ddlRTO.SelectedValue);
                        objAppDependency.RelTypeID = Convert.ToInt32(ddlRelationalImpact.SelectedValue);

                        if (IsInbond)
                        {
                            objAppDependency.OutBondAppID = Convert.ToInt32(ddlbusinessfunctions.SelectedValue);
                            objAppDependency.InBondAppID = Convert.ToInt32(lblfunctionheadid.Text);
                            objAppDependency.IsCritical = Convert.ToInt32(ddlIsCritical.SelectedValue);
                            objAppDependency.IsDRRequired = Convert.ToInt32(ddlDRRequired.SelectedValue);
                        }
                        else
                        {
                            objAppDependency.OutBondAppID = Convert.ToInt32(lblfunctionheadid.Text);
                            objAppDependency.InBondAppID = Convert.ToInt32(ddlbusinessfunctions.SelectedValue);
                            objAppDependency.IsCritical = -1;
                            objAppDependency.IsDRRequired = -1;
                        }

                        objAppDependency.CreatorId = LoggedInUserId;
                        objAppDependency.UpdatorId = LoggedInUserId;
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }

            return objAppDependency;
        }

        private void SetResult()
        {
            try
            {
                List<int> lstUpTo2Hrs, lstUpTo4Hrs, lstUpTo8Hrs, lstUpTo12Hrs, lstUpTo24Hrs, lstUpTo48Hrs, lstUpTo72Hrs, lstUpTo1Week, lstUpTo2Weeks, lstUpTo1Month;

                List<int> AlllstUpTo2Hrs = new List<int>();
                List<int> AlllstUpTo4Hrs = new List<int>();
                List<int> AlllstUpTo8Hrs = new List<int>();
                List<int> AlllstUpTo12Hrs = new List<int>();
                List<int> AlllstUpTo24Hrs = new List<int>();
                List<int> AlllstUpTo48Hrs = new List<int>();
                List<int> AlllstUpTo72Hrs = new List<int>();
                List<int> AlllstUpTo1Week = new List<int>();
                List<int> AlllstUpTo2Weeks = new List<int>();
                List<int> AlllstUpTo1Month = new List<int>();

                foreach (GridDataItem item in RgApplicationBIAQualitative.Items)
                {
                    lstUpTo2Hrs = new List<int>();
                    lstUpTo4Hrs = new List<int>();
                    lstUpTo8Hrs = new List<int>();
                    lstUpTo12Hrs = new List<int>();
                    lstUpTo24Hrs = new List<int>();
                    lstUpTo48Hrs = new List<int>();
                    lstUpTo72Hrs = new List<int>();
                    lstUpTo1Week = new List<int>();
                    lstUpTo2Weeks = new List<int>();
                    lstUpTo1Month = new List<int>();

                    int i = 0;

                    lstUpTo2Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo2Hrs")).SelectedValue));
                    AlllstUpTo2Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo2Hrs")).SelectedValue));

                    lstUpTo4Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo4Hrs")).SelectedValue));
                    AlllstUpTo4Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo4Hrs")).SelectedValue));

                    lstUpTo8Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo8Hrs")).SelectedValue));
                    AlllstUpTo8Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo8Hrs")).SelectedValue));

                    lstUpTo12Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo12Hrs")).SelectedValue));
                    AlllstUpTo12Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo12Hrs")).SelectedValue));

                    lstUpTo24Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo24Hrs")).SelectedValue));
                    AlllstUpTo24Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo24Hrs")).SelectedValue));

                    lstUpTo48Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo48Hrs")).SelectedValue));
                    AlllstUpTo48Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo48Hrs")).SelectedValue));

                    lstUpTo72Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo72Hrs")).SelectedValue));
                    AlllstUpTo72Hrs.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo72Hrs")).SelectedValue));

                    lstUpTo1Week.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo1Week")).SelectedValue));
                    AlllstUpTo1Week.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo1Week")).SelectedValue));

                    lstUpTo2Weeks.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo2Weeks")).SelectedValue));
                    AlllstUpTo2Weeks.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo2Weeks")).SelectedValue));

                    lstUpTo1Month.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo1Month")).SelectedValue));
                    AlllstUpTo1Month.Add(Convert.ToInt16(((DropDownList)item.FindControl("ddlUpTo1Month")).SelectedValue));

                    #region "Validate Ratings for Impacts"

                    if (lstUpTo2Hrs[i] > lstUpTo4Hrs[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo4Hrs")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo4Hrs")).Visible = true;
                        ViewState["IsRatingValid"] = false;
                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo4Hrs")).Visible = false;
                    }

                    if (lstUpTo4Hrs[i] > lstUpTo8Hrs[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo8Hrs")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo8Hrs")).Visible = true;
                        ViewState["IsRatingValid"] = false;
                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo8Hrs")).Visible = false;
                    }


                    if (lstUpTo8Hrs[i] > lstUpTo12Hrs[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo12Hrs")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo12Hrs")).Visible = true;
                        ViewState["IsRatingValid"] = false;
                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo12Hrs")).Visible = false;
                    }


                    if (lstUpTo12Hrs[i] > lstUpTo24Hrs[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo24Hrs")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo24Hrs")).Visible = true;
                        ViewState["IsRatingValid"] = false;
                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo24Hrs")).Visible = false;
                    }


                    if (lstUpTo24Hrs[i] > lstUpTo48Hrs[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo48Hrs")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo48Hrs")).Visible = true;
                        ViewState["IsRatingValid"] = false;

                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo48Hrs")).Visible = false;
                    }


                    if (lstUpTo48Hrs[i] > lstUpTo72Hrs[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo72Hrs")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo72Hrs")).Visible = true;
                        ViewState["IsRatingValid"] = false;
                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo72Hrs")).Visible = false;
                    }

                    if (lstUpTo72Hrs[i] > lstUpTo1Week[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo1Week")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo1Week")).Visible = true;
                        ViewState["IsRatingValid"] = false;
                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo1Week")).Visible = false;
                    }


                    if (lstUpTo1Week[i] > lstUpTo2Weeks[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo2Weeks")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo2Weeks")).Visible = true;
                        ViewState["IsRatingValid"] = false;
                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo2Weeks")).Visible = false;
                    }

                    if (lstUpTo2Weeks[i] > lstUpTo1Month[i])
                    {
                        ((DropDownList)item.FindControl("ddlUpTo1Month")).SelectedIndex = 0;
                        ((Label)item.FindControl("lblUpTo1Month")).Visible = true;
                        ViewState["IsRatingValid"] = false;
                    }
                    else
                    {
                        ((Label)item.FindControl("lblUpTo1Month")).Visible = false;
                    }

                    i += 1;


                    #endregion
                }

                #region Footer Over all Impact

                GridFooterItem footerItem = (GridFooterItem)RgApplicationBIAQualitative.MasterTableView.GetItems(GridItemType.Footer)[0];

                // Footer Column 1
                TextBox txtUpTo2HrsOI = (TextBox)footerItem.FindControl("txtUpTo2HrsOI");
                txtUpTo2HrsOI.Text = AlllstUpTo2Hrs.Max<int>().ToString();
                Label lblUpTo2HrsOIM = (Label)footerItem.FindControl("lblUpTo2HrsOIM");
                lblUpTo2HrsOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo2HrsOI);

                // Footer Column 2
                TextBox txtUpTo4HrsOI = (TextBox)footerItem.FindControl("txtUpTo4HrsOI");
                txtUpTo4HrsOI.Text = AlllstUpTo4Hrs.Max<int>().ToString();
                Label lblUpTo4HrsOIM = (Label)footerItem.FindControl("lblUpTo4HrsOIM");
                lblUpTo4HrsOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo4HrsOI);

                // Footer Column 3
                TextBox txtUpTo8HrsOI = (TextBox)footerItem.FindControl("txtUpTo8HrsOI");
                txtUpTo8HrsOI.Text = AlllstUpTo8Hrs.Max<int>().ToString();
                Label lblUpTo8HrsOIM = (Label)footerItem.FindControl("lblUpTo8HrsOIM");
                lblUpTo8HrsOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo8HrsOI);

                // Footer Column 4
                TextBox txtUpTo12HrsOI = (TextBox)footerItem.FindControl("txtUpTo12HrsOI");
                txtUpTo12HrsOI.Text = AlllstUpTo12Hrs.Max<int>().ToString();
                Label lblUpTo12HrsOIM = (Label)footerItem.FindControl("lblUpTo12HrsOIM");
                lblUpTo12HrsOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo12HrsOI);

                // Footer Column 3
                TextBox txtUpTo24HrsOI = (TextBox)footerItem.FindControl("txtUpTo24HrsOI");
                txtUpTo24HrsOI.Text = AlllstUpTo24Hrs.Max<int>().ToString();
                Label lblUpTo24HrsOIM = (Label)footerItem.FindControl("lblUpTo24HrsOIM");
                lblUpTo24HrsOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo24HrsOI);

                // Footer Column 4
                TextBox txtUpTo48HrsOI = (TextBox)footerItem.FindControl("txtUpTo48HrsOI");
                txtUpTo48HrsOI.Text = AlllstUpTo48Hrs.Max<int>().ToString();
                Label lblUpTo48HrsOIM = (Label)footerItem.FindControl("lblUpTo48HrsOIM");
                lblUpTo48HrsOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo48HrsOI);

                // Footer Column 5
                TextBox txtUpTo72HrsOI = (TextBox)footerItem.FindControl("txtUpTo72HrsOI");
                txtUpTo72HrsOI.Text = AlllstUpTo72Hrs.Max<int>().ToString();
                Label lblUpTo72HrsOIM = (Label)footerItem.FindControl("lblUpTo72HrsOIM");
                lblUpTo72HrsOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo72HrsOI);

                // Footer Column 6
                TextBox txtUpTo1WeekOI = (TextBox)footerItem.FindControl("txtUpTo1WeekOI");
                txtUpTo1WeekOI.Text = AlllstUpTo1Week.Max<int>().ToString();
                Label lblUpTo1WeekOIM = (Label)footerItem.FindControl("lblUpTo1WeekOIM");
                lblUpTo1WeekOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo1WeekOI);

                // Footer Column 7
                TextBox txtUpTo2WeeksOI = (TextBox)footerItem.FindControl("txtUpTo2WeeksOI");
                txtUpTo2WeeksOI.Text = AlllstUpTo2Weeks.Max<int>().ToString();
                Label lblUpTo2WeeksOIM = (Label)footerItem.FindControl("lblUpTo2WeeksOIM");
                lblUpTo2WeeksOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo2WeeksOI);

                // Footer Column 8
                TextBox txtUpTo1MonthOI = (TextBox)footerItem.FindControl("txtUpTo1MonthOI");
                txtUpTo1MonthOI.Text = AlllstUpTo1Month.Max<int>().ToString();
                Label lblUpTo1MonthOIM = (Label)footerItem.FindControl("lblUpTo1MonthOIM");
                lblUpTo1MonthOIM.Text = Utility.CheckImpactRatingForTextBox(txtUpTo1MonthOI);

                #endregion

                SortedList<int, int> ht = new SortedList<int, int>();
                ht.Add(2, Convert.ToInt16(txtUpTo2HrsOI.Text));
                ht.Add(4, Convert.ToInt16(txtUpTo4HrsOI.Text));
                ht.Add(8, Convert.ToInt16(txtUpTo8HrsOI.Text));
                ht.Add(12, Convert.ToInt16(txtUpTo12HrsOI.Text));
                ht.Add(24, Convert.ToInt16(txtUpTo24HrsOI.Text));
                ht.Add(48, Convert.ToInt16(txtUpTo48HrsOI.Text));
                ht.Add(72, Convert.ToInt16(txtUpTo72HrsOI.Text));
                ht.Add(168, Convert.ToInt16(txtUpTo1WeekOI.Text));
                ht.Add(336, Convert.ToInt16(txtUpTo2WeeksOI.Text));
                ht.Add(720, Convert.ToInt16(txtUpTo1MonthOI.Text));

                iMTR = 0;
                iRTO = 0;
                IsCritical = false;

                CalculateRTOMTR(ht, ref iRTO, ref iMTR, ref IsCritical);

                txtCalculatedRTO.Text = Utility.GetFormattedRTO(iRTO);
                lblCalcualtedRTO.Text = iRTO.ToString();

                txtCalculatedMTR.Text = Utility.GetFormattedRTO(iMTR);
                lblCalcualtedMTR.Text = iMTR.ToString();

            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }

        }

        private void SetEditView()
        {
            PopulateSection();
            SetResult();
            Udptblfunction.Update();
            UpdatePanel1.Update();
            UpdatePanel2.Update();
            UpdatePanel3.Update();
            UpdatePanel4.Update();
            UpdatePanel5.Update();
            Udplvfunction.Update();
        }

        private DataTable GetActivityData(ListView lvActivitylist)
        {
            DataTable dtActivitiess = new DataTable();

            if (ViewState["dtActivity"] != null)
                dtActivitiess = ((DataTable)ViewState["dtActivity"]).Clone();
            dtActivitiess.Rows.Clear();

            try
            {

                foreach (ListViewItem item in lvActivitylist.Items)
                {
                    Label lblActivityID = item.FindControl("lblActivityID") as Label;
                    TextBox txtActivityName = item.FindControl("txtActivityName") as TextBox;
                    dtActivitiess.Rows.Add(lblActivityID.Text, txtActivityName.Text);
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
            return dtActivitiess;
        }

        private DataTable GetUpstreamData(ListView lvUpstreamData)
        {
            DataTable dtUpstreamData = new DataTable();
            if (ViewState["AppData"] != null)
                dtUpstreamData = ((DataTable)ViewState["AppData"]).Clone();
            dtUpstreamData.Rows.Clear();
            foreach (ListViewItem item in lvActivitylist.Items)
            {
                //Label lblActivityID = item.FindControl("lblActivityID") as Label;
                //TextBox txtActivityName = item.FindControl("txtActivityName") as TextBox;
                //dtUpstreamData.Rows.Add(lblActivityID.Text, txtActivityName.Text);
            }
            return dtUpstreamData;
        }

        private void CalculateRTOMTR(SortedList<int, int> ht, ref int iRTO, ref int iMTR, ref bool IsCritical)
        {
            try
            {
                RTOMTRConfiguration objRTOMTRConfigurations = Facade.GetRTOMTRConfiguration();

                if (objRTOMTRConfigurations != null)
                {
                    int iMTRRating = Convert.ToInt16(objRTOMTRConfigurations.MTRRating);

                    int iUserRating = 0;
                    foreach (KeyValuePair<int, int> pair in ht)
                    {
                        iUserRating = pair.Value;
                        if (iUserRating != 0)
                        {
                            if (iMTRRating > iUserRating)
                            {
                                iRTO = Convert.ToInt16(pair.Key);
                            }
                            else
                            {
                                iMTR = Convert.ToInt16(pair.Key);
                                break;
                            }
                        }
                    }

                    if (iRTO == 0 && iMTR == 0) return;

                    if (iRTO == 0) iRTO = iMTR;

                    if (iMTR == 0) iMTR = iRTO;

                    if (iRTO > 0) IsCritical = iRTO <= Convert.ToInt32(objRTOMTRConfigurations.RTO);
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }
        }

        private bool AddUpdateBusinessFunctionBIA(ref string ErrorMsg, bool IsQualitative, bool IsQuantitative)
        {
            bool IsDatasaved = true;
            if (Convert.ToInt32(lblfunctionheadid.Text) > 0 && Convert.ToInt32(ddlImactTypeQA.SelectedValue) > 0 && ValidateRequest("BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA))
            {
                try
                {
                    BusinessFunctionBIASeverity objBusinessFunctionBIASeverity = new BusinessFunctionBIASeverity();
                    objBusinessFunctionBIASeverity.BusinessFunctionID = Convert.ToInt32(lblfunctionheadid.Text);
                    objBusinessFunctionBIASeverity.ImpSeverityID = IsQualitative ? Convert.ToInt32(ddlImactTypeQA.SelectedValue) : Convert.ToInt32(ddlImactTypeQN.SelectedValue);
                    objBusinessFunctionBIASeverity.BIAID = Convert.ToInt32(string.Format("{0}{1}", objBusinessFunctionBIASeverity.BusinessFunctionID, objBusinessFunctionBIASeverity.ImpSeverityID));
                    objBusinessFunctionBIASeverity.CreatorId = LoggedInUserId;
                    objBusinessFunctionBIASeverity.UpdatorId = LoggedInUserId;
                    if (Facade.IsExistBusinessFunctionBIASeverityByfunctionidandBIAID(objBusinessFunctionBIASeverity.BusinessFunctionID, objBusinessFunctionBIASeverity.BIAID))
                    {
                        Facade.UpdateBusinessFunctionBIASeverity(objBusinessFunctionBIASeverity);
                    }
                    else
                    {
                        Facade.AddBusinessFunctionBIASeverity(objBusinessFunctionBIASeverity);
                    }

                    GridDataItemCollection GrdCollection = new GridDataItemCollection();

                    if (IsQualitative)
                        GrdCollection = RgApplicationBIAQualitative.Items;
                    else
                        GrdCollection = RgApplicationBIAQuantitative.Items;

                    foreach (GridDataItem item in GrdCollection)
                    {
                        BusinessFunctionBIA objBusinessFunctionBIA = new BusinessFunctionBIA();
                        objBusinessFunctionBIA.Id = Convert.ToInt32(item.GetDataKeyValue("ID") != null ? item.GetDataKeyValue("ID").ToString() : "0");
                        objBusinessFunctionBIA.BIAID = objBusinessFunctionBIASeverity.BIAID;
                        objBusinessFunctionBIA.BusinessFunctionID = Convert.ToInt32(lblfunctionheadid.Text);
                        objBusinessFunctionBIA.ImpactTypeID = Convert.ToInt32(item.GetDataKeyValue("ImpactTypeID"));
                        objBusinessFunctionBIA.ImpactID = Convert.ToInt32(item.GetDataKeyValue("ImpactID"));
                        objBusinessFunctionBIA.Upto2hrs = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto2Hrs")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo2hrs")).Text);
                        objBusinessFunctionBIA.Upto4hrs = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto4Hrs")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo4hrs")).Text);
                        objBusinessFunctionBIA.Upto8hrs = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto8Hrs")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo8hrs")).Text);
                        objBusinessFunctionBIA.Upto12hrs = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto12Hrs")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo12hrs")).Text);
                        objBusinessFunctionBIA.Upto24hrs = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto24Hrs")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo24hrs")).Text);
                        objBusinessFunctionBIA.Upto48hrs = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto48Hrs")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo48hrs")).Text);
                        objBusinessFunctionBIA.Upto72hrs = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto72Hrs")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo72hrs")).Text);
                        objBusinessFunctionBIA.Upto1week = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto1Week")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo1Week")).Text);
                        objBusinessFunctionBIA.Upto2weeks = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto2Weeks")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo2Week")).Text);
                        objBusinessFunctionBIA.Upto1Month = IsQualitative ? Convert.ToInt32(((DropDownList)item.FindControl("ddlUpto1Month")).Text) : Convert.ToInt32(((RadNumericTextBox)item.FindControl("txtUpTo1month")).Text);
                        objBusinessFunctionBIA.Comment = ((RadTextBox)item.FindControl("txtComment")).Text;
                        objBusinessFunctionBIA.CreatorId = LoggedInUserId;
                        objBusinessFunctionBIA.UpdatorId = LoggedInUserId;
                        objBusinessFunctionBIA.IsQualitative = IsQualitative == true ? 1 : 0;
                        objBusinessFunctionBIA.IsQuantitative = IsQuantitative == true ? 1 : 0;
                        //if (Facade.IsExistBusinessFunctionBIAByID(objBusinessFunctionBIA.Id))
                        if (objBusinessFunctionBIA.Id > 0)
                        {
                            Facade.UpdateBusinessFunctionBIA(objBusinessFunctionBIA);
                            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIA", UserActionType.UpdateBusinessFunctionBIA, "BusinessFunctionBIA was updated to the BusinessFunctionBIA table", LoggedInUserId);
                        }
                        else
                        {
                            Facade.AddBusinessFunctionBIA(objBusinessFunctionBIA);
                            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunctionBIA", UserActionType.CreateBusinessFunctionBIA, "BusinessFunctionBIA was updated to the BusinessFunctionBIA table", LoggedInUserId);
                        }

                    }
                }
                catch (CpException ex)
                {
                    InvalidateTransaction();

                    ExceptionManager.Manage(ex, this);

                    ErrorMsg = "Error Occured While Saving Record: " + ex.Message + "&nbsp;&nbsp;&nbsp;";

                    IsDatasaved = false;
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                    {
                        ExceptionManager.Manage((CpException)ex.InnerException, this);
                    }
                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                        ExceptionManager.Manage(customEx, this);
                    }

                    ErrorMsg = "Error Occured While Saving Record: " + ex.Message + "&nbsp;&nbsp;&nbsp;";
                    IsDatasaved = false;
                }
            }
            else
            {
                ErrorMsg = "Please select the Business function before continuing!&nbsp;&nbsp;&nbsp;";
                IsDatasaved = false;
            }

            return IsDatasaved;
        }

        #endregion

        #region Public Methods

        public string GetBusinessServiceName(object ID)
        {
            string Name = string.Empty;
            BusinessService objBusinessService = new BusinessService();
            if (Convert.ToInt32(ID) > 0)
            {
                objBusinessService = Facade.GetBusinessServiceById(Convert.ToInt32(ID));
                Name = objBusinessService != null ? objBusinessService.Name : string.Empty;

            }
            else
            {
                Name = "NA";
            }
            return Name;
        }
        public string GetFIATemplateName(object ID)
        {
            string Name = string.Empty;
            int ProfileID = 0;

            IList<BFBIAMatrix> objBFBIAMatrix;
            IList<BusinessProfile> objBusinessProfile;
            if (Convert.ToInt32(ID) > 0)
            {
                objBFBIAMatrix = Facade.GetBFBIAMatrixByBFId(Convert.ToInt32(ID));
                if (objBFBIAMatrix != null && objBFBIAMatrix.Count() > 0)
                {
                    ProfileID = objBFBIAMatrix != null ? objBFBIAMatrix.FirstOrDefault().ProfileID : 0;
                }
                else
                { Name = "NA"; }
                if (ProfileID > 0)
                {
                    objBusinessProfile = Facade.GetAllBusinessProfile();
                    if (objBusinessProfile != null && objBusinessProfile.Count() > 0)
                    {

                        var BusinessProfileDetails = from a in objBusinessProfile where a.Id == ProfileID select a;
                        if (BusinessProfileDetails != null && BusinessProfileDetails.Count() > 0)
                        {
                            Name = BusinessProfileDetails.FirstOrDefault().ProfileName;

                        }
                        else
                        {
                            Name = "NA";
                        }

                    }

                }
                else
                { Name = "NA"; }


            }
            else
            {
                Name = "NA";
            }
            return Name;
        }


        public string GetFIATemplateByID(object ID)
        {
            string ProfileID = string.Empty;


            IList<BFBIAMatrix> objBFBIAMatrix;
            if (Convert.ToInt32(ID) > 0)
            {
                objBFBIAMatrix = Facade.GetBFBIAMatrixByBFId(Convert.ToInt32(ID));
                if (objBFBIAMatrix != null && objBFBIAMatrix.Count() > 0)
                {
                    ProfileID = objBFBIAMatrix != null ? objBFBIAMatrix.FirstOrDefault().ProfileID.ToString() : "0";
                }
                else
                { ProfileID = "0"; }


            }
            else
            {
                ProfileID = "0";
            }
            return ProfileID;
        }

        public string GetFormattedRTO(object RTOValue)
        {
            string RTOvalue = Convert.ToString(RTOValue);
            if (!string.IsNullOrEmpty(RTOvalue) && !RTOvalue.Contains("-") && !RTOvalue.Contains("N"))
            {
                TimeSpan _tsconfiguredrto;
                bool IsValidTimeSpanformat = Utility.IsTimeSpanFormat(RTOvalue);
                if (IsValidTimeSpanformat)
                    _tsconfiguredrto = TimeSpan.Parse(RTOvalue);
                else
                    _tsconfiguredrto = TimeSpan.FromSeconds(Convert.ToDouble(RTOvalue));
                RTOvalue = Utility.GetFormattedDateTimeString(_tsconfiguredrto);
            }
            else
            {
                RTOvalue = "N/A";
            }
            return RTOvalue;
        }

        public string GetFormattedRPO(object RPOValue)
        {
            string RPOvalue = Convert.ToString(RPOValue);
            if (!string.IsNullOrEmpty(RPOvalue) && !RPOvalue.Contains("-") && !RPOvalue.Contains("N"))
            {
                TimeSpan _tsconfiguredRpo;
                bool IsValidTimeSpanformat = Utility.IsTimeSpanFormat(RPOvalue);
                if (IsValidTimeSpanformat)
                    _tsconfiguredRpo = TimeSpan.Parse(RPOvalue);
                else
                    _tsconfiguredRpo = TimeSpan.FromSeconds(Convert.ToDouble(RPOvalue));
                RPOvalue = Utility.GetFormattedDateTimeString(_tsconfiguredRpo);
            }
            else
            {
                RPOvalue = "N/A";
            }
            return RPOvalue;
        }

        #endregion




    }
}