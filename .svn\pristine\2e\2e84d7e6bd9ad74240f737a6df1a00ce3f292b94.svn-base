﻿using System;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System.Collections.Generic;

namespace CP.DataAccess
{
    internal sealed class UserActivityDataAccess : BaseDataAccess, IUserActivityDataAccess
    {
        #region Constructors

        public UserActivityDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<UserActivity> CreateEntityBuilder<UserActivity>()
        {
            return (new UserActivityBuilder()) as IEntityBuilder<UserActivity>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="UserActivity" /> UserActivity table.
        /// </summary>
        /// <param name="userActivity">User</param>
        /// <returns>UserActivity</returns>
        /// <author>Kiran Ghadge</author>
        UserActivity IUserActivityDataAccess.Add(UserActivity userActivity)
        {
            try
            {
                const string sp = "UserActivity_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iLoginName", DbType.AnsiString, userActivity.LoginName);
                    Database.AddInParameter(cmd, Dbstring + "iEntity", DbType.AnsiString, userActivity.Entity);
                    Database.AddInParameter(cmd, Dbstring + "iActionType", DbType.AnsiString, userActivity.ActionType.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iPageUrl", DbType.AnsiString, userActivity.PageUrl);
                    Database.AddInParameter(cmd, Dbstring + "iUserHostAddress", DbType.AnsiString, userActivity.UserHostAddress);
                    Database.AddInParameter(cmd, Dbstring + "iActivityDetails", DbType.AnsiString, userActivity.ActivityDetails);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, userActivity.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentLoginIP", DbType.AnsiString, userActivity.CurrentLoginIP);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        userActivity = reader.Read()
                            ? CreateEntityBuilder<UserActivity>().BuildEntity(reader, userActivity)
                            : null;
                    }

                    if (userActivity == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "UserActivity already exists. Please specify another userActivity.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this userActivity.");
                                }
                        }
                    }

                    return userActivity;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While inserting UserActivity Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }

        IList<UserActivity> IUserActivityDataAccess.GetByStartEndDate(string startDate, string endDate)
        {
            try
            {
                const string sp = "UserActivity_GetByStartEndDate";

                IList<UserActivity> objprl = new List<UserActivity>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.String, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.String, endDate);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new UserActivity
                            {
                                LoginName = Convert.IsDBNull(reader["LoginName"]) ? string.Empty : Convert.ToString(reader["LoginName"]),
                                Entity = Convert.IsDBNull(reader["Entity"]) ? string.Empty : Convert.ToString(reader["Entity"]),

                                ActionType = Convert.IsDBNull(reader["ActionType"])
                                                           ? UserActionType.Unknown
                                                           : (UserActionType)Enum.Parse(typeof(UserActionType), Convert.ToString(reader["ActionType"]), true),
                                UserHostAddress = Convert.IsDBNull(reader["UserHostAddress"]) ? string.Empty : Convert.ToString(reader["UserHostAddress"]),
                                ActivityDetails = Convert.IsDBNull(reader["ActivityDetails"]) ? string.Empty : Convert.ToString(reader["ActivityDetails"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserActivityDataAccess.GetByStartEndDate()" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<UserActivity> IUserActivityDataAccess.GetActivityByDate(int loginId, string startDate, string endDate)
        {
            try
            {
                const string sp = "UserActivity_GetByDate";

                IList<UserActivity> objprl = new List<UserActivity>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iloginId", DbType.String, loginId);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.String, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.String, endDate);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new UserActivity
                            {
                                LoginName = Convert.IsDBNull(reader["LoginName"]) ? string.Empty : Convert.ToString(reader["LoginName"]),
                                Entity = Convert.IsDBNull(reader["Entity"]) ? string.Empty : Convert.ToString(reader["Entity"]),

                                ActionType = Convert.IsDBNull(reader["ActionType"])
                                                               ? UserActionType.Unknown
                                                               : (UserActionType)Enum.Parse(typeof(UserActionType), Convert.ToString(reader["ActionType"]), true),
                                UserHostAddress = Convert.IsDBNull(reader["UserHostAddress"]) ? string.Empty : Convert.ToString(reader["UserHostAddress"]),
                                ActivityDetails = Convert.IsDBNull(reader["ActivityDetails"]) ? string.Empty : Convert.ToString(reader["ActivityDetails"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserActivityDataAccess.GetUsrActivityByDate(" +
                    loginId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<UserActivity> IUserActivityDataAccess.GetActivityByDate_New(int loginname, string startDate, string endDate)
        {
            try
            {
                const string sp = "UserActivity_GetByDate_New";

                IList<UserActivity> objprl = new List<UserActivity>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iloginname", DbType.Int32, loginname);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.String, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.String, endDate);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new UserActivity
                            {
                                LoginName = Convert.IsDBNull(reader["LoginName"]) ? string.Empty : Convert.ToString(reader["LoginName"]),
                                Entity = Convert.IsDBNull(reader["Entity"]) ? string.Empty : Convert.ToString(reader["Entity"]),

                                ActionType = Convert.IsDBNull(reader["ActionType"])
                                                               ? UserActionType.Unknown
                                                               : (UserActionType)Enum.Parse(typeof(UserActionType), Convert.ToString(reader["ActionType"]), true),
                                UserHostAddress = Convert.IsDBNull(reader["UserHostAddress"]) ? string.Empty : Convert.ToString(reader["UserHostAddress"]),
                                ActivityDetails = Convert.IsDBNull(reader["ActivityDetails"]) ? string.Empty : Convert.ToString(reader["ActivityDetails"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserActivityDataAccess.GetUsrActivityByDate(" +
                    loginname + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<UserActivity> IUserActivityDataAccess.GetAllUsersWithout_EnterpriseUser(string startDate, string endDate)
        {
            try
            {
                const string sp = "GetUsers_WithoutEnterpriseUser";

                IList<UserActivity> objprl = new List<UserActivity>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.String, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.String, endDate);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new UserActivity
                            {
                                LoginName = Convert.IsDBNull(reader["LoginName"]) ? string.Empty : Convert.ToString(reader["LoginName"]),
                                Entity = Convert.IsDBNull(reader["Entity"]) ? string.Empty : Convert.ToString(reader["Entity"]),

                                ActionType = Convert.IsDBNull(reader["ActionType"])
                                                               ? UserActionType.Unknown
                                                               : (UserActionType)Enum.Parse(typeof(UserActionType), Convert.ToString(reader["ActionType"]), true),
                                PageUrl = Convert.IsDBNull(reader["PageUrl"]) ? string.Empty : Convert.ToString(reader["PageUrl"]),
                                UserHostAddress = Convert.IsDBNull(reader["UserHostAddress"]) ? string.Empty : Convert.ToString(reader["UserHostAddress"]),
                                ActivityDetails = Convert.IsDBNull(reader["ActivityDetails"]) ? string.Empty : Convert.ToString(reader["ActivityDetails"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserActivityDataAccess.GetAllUsersWithout_EnterpriseUser" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<UserActivity> IUserActivityDataAccess.GetAllUsersWithout_EnterpriseUserByDate(string loginname, string startDate, string endDate)
        {
            try
            {
                const string sp = "GetUsers_WithoutEnterpriseUserByDate";

                IList<UserActivity> objprl = new List<UserActivity>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iLoginName", DbType.String, loginname);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.String, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.String, endDate);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new UserActivity
                            {
                                LoginName = Convert.IsDBNull(reader["LoginName"]) ? string.Empty : Convert.ToString(reader["LoginName"]),
                                Entity = Convert.IsDBNull(reader["Entity"]) ? string.Empty : Convert.ToString(reader["Entity"]),

                                ActionType = Convert.IsDBNull(reader["ActionType"])
                                                               ? UserActionType.Unknown
                                                               : (UserActionType)Enum.Parse(typeof(UserActionType), Convert.ToString(reader["ActionType"]), true),
                                PageUrl = Convert.IsDBNull(reader["PageUrl"]) ? string.Empty : Convert.ToString(reader["PageUrl"]),
                                UserHostAddress = Convert.IsDBNull(reader["UserHostAddress"]) ? string.Empty : Convert.ToString(reader["UserHostAddress"]),
                                ActivityDetails = Convert.IsDBNull(reader["ActivityDetails"]) ? string.Empty : Convert.ToString(reader["ActivityDetails"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserActivityDataAccess.GetAllUsersWithout_EnterpriseUser" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<UserActivity> IUserActivityDataAccess.GetAllUsersWithout_EnterpriseUserByDate_new(int loginname, string startDate, string endDate)
        {
            try
            {
                const string sp = "GetAllUsersWithout_EnterpriseUserByDate_new";

                IList<UserActivity> objprl = new List<UserActivity>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iLoginName", DbType.Int32, loginname);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.String, startDate);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.String, endDate);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var obj = new UserActivity
                            {
                                LoginName = Convert.IsDBNull(reader["LoginName"]) ? string.Empty : Convert.ToString(reader["LoginName"]),
                                Entity = Convert.IsDBNull(reader["Entity"]) ? string.Empty : Convert.ToString(reader["Entity"]),

                                ActionType = Convert.IsDBNull(reader["ActionType"])
                                                               ? UserActionType.Unknown
                                                               : (UserActionType)Enum.Parse(typeof(UserActionType), Convert.ToString(reader["ActionType"]), true),
                                PageUrl = Convert.IsDBNull(reader["PageUrl"]) ? string.Empty : Convert.ToString(reader["PageUrl"]),
                                UserHostAddress = Convert.IsDBNull(reader["UserHostAddress"]) ? string.Empty : Convert.ToString(reader["UserHostAddress"]),
                                ActivityDetails = Convert.IsDBNull(reader["ActivityDetails"]) ? string.Empty : Convert.ToString(reader["ActivityDetails"]),
                                CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IUserActivityDataAccess.GetAllUsersWithout_EnterpriseUser" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
       
        IList<DashboardAlert> IUserActivityDataAccess.GetDashboardAlert()
        {
            try
            {
                const string sp = "DASHBOARD_ALERTDETAILS";
                IList<DashboardAlert> alertlist = new List<DashboardAlert>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            DashboardAlert alertdata = new DashboardAlert();
                            alertdata.AlertDate = Convert.IsDBNull(reader[0]) ? string.Empty : Convert.ToString(reader[0]);
                            //  alertdata.Alertcategory = Convert.IsDBNull(reader["UserHostAddress"]) ? string.Empty : Convert.ToString(reader["UserHostAddress"]);
                            //  alertdata.AlertCount = Convert.IsDBNull(reader["UserHostAddress"]) ? string.Empty : Convert.ToString(reader["UserHostAddress"]);
                            alertdata.Critical = Convert.IsDBNull(reader[1]) ? string.Empty : Convert.ToString(reader[2]);
                            alertdata.High = Convert.IsDBNull(reader[2]) ? string.Empty : Convert.ToString(reader[1]);
                            alertdata.Normal = Convert.IsDBNull(reader[3]) ? string.Empty : Convert.ToString(reader[3]);

                            alertlist.Add(alertdata);

                        }

                    }
                }
                return alertlist;
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IDashboardAlert.getalertdetails()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        #endregion Methods
    }
}