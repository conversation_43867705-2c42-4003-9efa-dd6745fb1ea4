﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System.Text;

namespace CP.DataAccess
{
    internal sealed class ParallelWorkflowProfileDataAccess : BaseDataAccess, IParallelWorkflowProfileDataAccess
    {
        #region Constructors

        public ParallelWorkflowProfileDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<PrallelWorkflowProfile> CreateEntityBuilder<PrallelWorkflowProfile>()
        {
            return (new ParallelWorkflowProfileBuilder()) as IEntityBuilder<PrallelWorkflowProfile>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="ParallelGroupWorkflow" />into bcms_parallel_groupworkflow table.
        /// </summary>
        /// <param name="paralleldroperation">paralleldroperation</param>
        /// <returns>ParallelGroupWorkflow</returns>
        /// <author>Jeyapandi</author>
        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.Add(ParallelWorkflowProfile paralleldroperation)
        {
            try
            {

                const string sp = "PARALLELWORKFLOWPROFILE_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, paralleldroperation.ProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, paralleldroperation.InfraobjectId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowType", DbType.Int32, paralleldroperation.WorkflowType);
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectName", DbType.AnsiString, paralleldroperation.InfraobjectName);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowId", DbType.Int32, paralleldroperation.WorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowName", DbType.AnsiString, paralleldroperation.WorkflowName);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionId", DbType.Int32, paralleldroperation.CurrentActionId);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionName", DbType.AnsiString,
                        paralleldroperation.CurrentActionName);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, paralleldroperation.Message);
                    Database.AddInParameter(cmd, Dbstring + "iProgressStatus", DbType.AnsiString,
                        paralleldroperation.ProgressStatus);
                    Database.AddInParameter(cmd, Dbstring + "iConditionalOperation", DbType.Int32,
                        paralleldroperation.ConditionalOperation);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, paralleldroperation.CreatorId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        paralleldroperation = reader.Read()
                            ? CreateEntityBuilder<ParallelWorkflowProfile>().BuildEntity(reader, paralleldroperation)
                            : null;
                    }

                    if (paralleldroperation == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "Parallel Workflow Profile already exists. Please specify another globalMirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this globalMirror.");
                                }
                        }
                    }

                    return paralleldroperation;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table Get by name.
        /// </summary>
        /// <param name="name">name</param>
        /// <returns>ParallelWorkflowProfile</returns>
        /// <author>Jeyapandi</author>
        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.GetByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "ParallelWorkflowProfile_GetByProfileId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.String, name);                                     


                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<ParallelWorkflowProfile>()).BuildEntity(reader,
                                new ParallelWorkflowProfile())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetByName(" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


      

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>ParallelWorkflowProfile</returns>
        /// <author>Jeyapandi</author>
        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "ParallelWFlowProfile_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelWorkflowProfile>()).BuildEntity(reader,
                                new ParallelWorkflowProfile());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetById(" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>ParallelGroupWorkflow</returns>
        /// <author>Shivraj Mujumale</author>
        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.GetByInfraobjectId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ParallelGroupWorkflow_GetByGroupId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelWorkflowProfile>()).BuildEntity(reader,
                                new ParallelWorkflowProfile());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetByGroupId(" +
                    id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>ParallelWorkflowProfile</returns>
        /// <author>Jeyapandi</author>
        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.GetByInfraobjectIdandWorkflowType(int infraId, int workflowType)
        {
            try
            {
                if (infraId < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ParallelWProf_GetByInfraWFType";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, infraId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowType", DbType.Int32, workflowType);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelWorkflowProfile>()).BuildEntity(reader,
                                new ParallelWorkflowProfile());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetByInfraobjectIdandWorkflowType(" +
                    infraId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table.
        /// </summary>
        /// <returns>ParallelGroupWorkflow List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<ParallelWorkflowProfile> IParallelWorkflowProfileDataAccess.GetAll()
        {
            try
            {
                const string sp = "ParallelWorkflowProfile_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowProfile>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ParallelWorkflowProfile> IParallelWorkflowProfileDataAccess.GetAllprofile()
        {
            try
            {
                const string sp = "getparallelprofile";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowProfile>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAllprofile()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ParallelWorkflowProfile> IParallelWorkflowProfileDataAccess.GetByProfileId(int pId)
        {
            try
            {
                const string sp = "ParallelWorkFlowP_GetProfile";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, pId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowProfile>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        string IParallelWorkflowProfileDataAccess.GetworkflowsByProfileId(int pId)
        {

            // string workflows = string.Empty;
            StringBuilder workflows = new StringBuilder();
            string _wfname = string.Empty;
            ParallelWorkflowProfile pw = new ParallelWorkflowProfile(); ;
            try
            {
                const string sp = "workflownames_GetByprofileid";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, pId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            pw.WorkflowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            _wfname = pw.WorkflowName + ",";
                            workflows.Append(_wfname);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return workflows.ToString().TrimEnd(',');
        }

        /// <summary>
        ///     Delete <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id"> Pass id</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool IParallelWorkflowProfileDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ParallelWFProfile_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.DeleteById()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ParallelWorkflowProfile> IParallelWorkflowProfileDataAccess.GetByDRStatus()
        {
            try
            {
                const string sp = "ParallelWFPrfl_GetByDRStatus";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelWorkflowProfile>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from parallel_workflowprofile table by infraobjectId and WorkflowId.
        /// </summary>
        /// <param name="infraId">infraId</param>
        /// <param name="WorkflowId">WorkflowId</param>
        /// <returns>ParallelGroupWorkflow</returns>
        /// <author>Suryaji shinde</author>
        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.GetByWorkflowIdAndInfraobjectId(int infraId, int WorkflowId)
        {
            try
            {
                if (infraId < 1)
                {
                    throw new ArgumentNullException("infraId");
                }
                if (WorkflowId < 1)
                {
                    throw new ArgumentNullException("WorkflowId");
                }

                const string sp = "PW_GetByInfraIdAndWorkflowId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iinfraobjectId", DbType.Int32, infraId);
                    Database.AddInParameter(cmd, Dbstring + "iworkflowId", DbType.Int32, WorkflowId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelWorkflowProfile>()).BuildEntity(reader,
                                new ParallelWorkflowProfile());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelWorkflowProfileDataAccess.GetByWorkflowIdAndInfraobjectId(" +
                    infraId + "," + WorkflowId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.GetByWorkflowId(int workflowId)
        {
            try
            {
                if (workflowId < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ParallelWProf_GetByWFId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iworkflowId", DbType.Int32, workflowId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelWorkflowProfile>()).BuildEntity(reader,
                                new ParallelWorkflowProfile());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetByWorkflowId(" +
                    workflowId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        //-------------------- Approval Changes -----------------------------------
        IList<ParallelWorkflowProfile> IParallelWorkflowProfileDataAccess.GetByProfileIdEmail(int pId)
        {
            IList<ParallelWorkflowProfile> objList = new List<ParallelWorkflowProfile>();
            try
            {
                const string sp = "ParallWFP_GetbyPIdEmail";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, pId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        while (reader.Read())
                        {
                            ParallelWorkflowProfile parallelGroup = new ParallelWorkflowProfile();
                            parallelGroup.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            parallelGroup.ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]);
                            parallelGroup.InfraobjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
                            parallelGroup.WorkflowType = Convert.IsDBNull(reader["WorkflowType"]) ? 0 : Convert.ToInt32(reader["WorkflowType"]);
                            parallelGroup.InfraobjectName = Convert.IsDBNull(reader["InfraObjectName"]) ? string.Empty : Convert.ToString(reader["InfraObjectName"]);
                            parallelGroup.WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]);
                            parallelGroup.WorkflowName = Convert.IsDBNull(reader["WorkflowName"]) ? string.Empty : Convert.ToString(reader["WorkflowName"]);
                            parallelGroup.CurrentActionId = Convert.IsDBNull(reader["CurrentActionId"]) ? 0 : Convert.ToInt32(reader["CurrentActionId"]);
                            parallelGroup.CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"]) ? string.Empty : Convert.ToString(reader["CurrentActionName"]);
                            parallelGroup.Message = Convert.IsDBNull(reader["Message"]) ? string.Empty : Convert.ToString(reader["Message"]);
                            parallelGroup.ConditionalOperation = Convert.IsDBNull(reader["ConditionalOperation"]) ? 0 : Convert.ToInt32(reader["ConditionalOperation"]);
                            parallelGroup.ProgressStatus = Convert.IsDBNull(reader["ProgressStatus"]) ? string.Empty : Convert.ToString(reader["ProgressStatus"]);
                            parallelGroup.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
                            parallelGroup.CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"]);
                            parallelGroup.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
                            parallelGroup.UpdateDate = Convert.IsDBNull(reader["UpdateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["UpdateDate"]);
                            parallelGroup.BusinessServiceName = Convert.IsDBNull(reader["BusinessServiceName"]) ? string.Empty : Convert.ToString(reader["BusinessServiceName"]);
                            parallelGroup.BusinessFunctionName = Convert.IsDBNull(reader["BusinessFunctionName"]) ? string.Empty : Convert.ToString(reader["BusinessFunctionName"]);
                            objList.Add(parallelGroup);
                        }

                        return objList;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        //------------------------------------------------------------------


        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.Get_BserviceAndBfunctionbyWf_Id(int workflowId)
        {
            try
            {
                if (workflowId < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "GetBserviceAndBfunctionbyWf_Id";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iWfId", DbType.Int32, workflowId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            ParallelWorkflowProfile retVal = new ParallelWorkflowProfile();

                            retVal.BusinessFunctionName = Convert.IsDBNull(reader["BusinessFunction"]) ? string.Empty : Convert.ToString(reader["BusinessFunction"]);
                            retVal.BusinessServiceName = Convert.IsDBNull(reader["BusinessServiceName"]) ? string.Empty : Convert.ToString(reader["BusinessServiceName"]);
                            retVal.InfraobjectName = Convert.IsDBNull(reader["InfraObjectName"]) ? string.Empty : Convert.ToString(reader["InfraObjectName"]);

                            return retVal;
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.Get_BserviceAndBfunctionbyWf_Id(" +
                    workflowId + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<ParallelWorkflowProfile> IParallelWorkflowProfileDataAccess.Getparallelworkflow_profile()
        {
            IList<ParallelWorkflowProfile> pwprofile = new List<ParallelWorkflowProfile>();
            ParallelWorkflowProfile pw = new ParallelWorkflowProfile(); ;
            try
            {
                const string sp = "getallparalllelprofile";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            pw.ProfileId = Convert.IsDBNull(reader["ProfileId"]) ? 0 : Convert.ToInt32(reader["ProfileId"]);
                            pw.WorkflowName = Convert.IsDBNull(reader["List_Profile"]) ? string.Empty : Convert.ToString(reader["List_Profile"]);
                            pw.InfraobjectName = Convert.IsDBNull(reader["List_Infra"]) ? string.Empty : Convert.ToString(reader["List_Infra"]);
                            pw.Message = Convert.IsDBNull(reader["List_creatorid"]) ? string.Empty : Convert.ToString(reader["List_creatorid"]);
                            pw.NameofTable = Convert.IsDBNull(reader["List_createDate"]) ? string.Empty : Convert.ToString(reader["List_createDate"]);
                            pw.ProfileName = Convert.IsDBNull(reader["List_updateDate"]) ? string.Empty : Convert.ToString(reader["List_updateDate"]);
                            pwprofile.Add(pw);
                        }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
            return pwprofile;
        }

        bool IParallelWorkflowProfileDataAccess.UpdatebyId(int id, int status)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "Update_FourEye";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iProfileId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iIsfoureye", DbType.Int32, status);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.UpdatebyId()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
                
        
        ParallelWorkflowProfile IParallelWorkflowProfileDataAccess.GetParallelProfileByprofileId(int id)
        {
            try
            {
                const string sp = "ParallelProfile_GetByProfileId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iprofileid", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelWorkflowProfile>()).BuildEntity(reader,
                                new ParallelWorkflowProfile());
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetParallelProfileByprofileId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        string IParallelWorkflowProfileDataAccess.GetBusinessServiceByProfileId(int profileId)
        {
            string bsname = string.Empty;
            try
            {
                const string sp = "GetBusinessServiceByProfileId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iprofileid", DbType.Int32, profileId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {

                        if (reader.Read())
                        {
                            bsname = Convert.IsDBNull(reader["businessServiceName"]) ? string.Empty : Convert.ToString(reader["businessServiceName"]);
                        }
                        //  return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelProfileDataAccess.GetParallelProfileByprofileId(" + profileId + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }

            return bsname;
        }

        #endregion Methods

    }
}