﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IInfraObjectDataAccess

    public interface IInfraObjectDataAccess
    {
        InfraObject Add(InfraObject infraobject);

        InfraObject Update(InfraObject infraobject);

        InfraObject GetById(int id);

        IList<InfraObject> GetByBusinessServiceIdAndBusinessFunctionId(int businessServiceId, int businessFunctionId);

        InfraObject GetByName(string name);

        IList<InfraObject> GetAll();

        IList<InfraObject> GetAllByRole(int id);

        bool DeleteById(int id);

        bool IsExistByName(string name);

        IList<InfraObject> GetByLoginId(int id);

        IList<InfraObject> GetByLoggedInUserId(int userId);

        IList<InfraObject> GetAllByCompanyId(int companyId, bool isParent);

        IList<InfraObject> GetAllInfraObjectIdRPT();

        bool DeleteGlobalMirrorInfraObjectById(int id);

        bool DeleteHitachiInfraObjectById(int id);

        IList<InfraObject> GetByBusinessServiceId(int businessServiceId);

        bool UpdateByState(int infraObjectId, string state, int replStatus);

        bool UpdateAllByState(string state, int repl, int infraapplicationId);

        bool UpdateEnableDataSync(int infraObjectId, bool enableDataSync);

        bool UnlockByTime(int infraObjectId, int minutes, string eventName);

        InfraObject GetByServerId(int id);

        IList<InfraObject> GetByServerId_New(int id);

        IList<InfraObject> GetInfraObjectByDatabaseId(int databaseId);

        IList<InfraObject> GetInfraByStorageImageId(string storageImageId);

        IList<InfraObject> GetInfraobjectByReplicationId(int ReplicationId);
        
        IList<InfraObject> GetIOByServerId(int serverId);


        InfraObject AddInfraId(InfraObject infraObject);

        InfraObject GetInfraObjectByPRReplicationId(int id);

        IList<InfraObject> GetJobByReplicationId(int repId);

        IList<InfraObject> GetReplicationId(int repId);

        IList<InfraObject> GetByBusinessServiceIdAndBusinessFunctionIdAndUserId(int businessServiceId, int businessFunctionId, int LoggedInUserId);

        IList<InfraObject> GetInfraObjectByBusinessServiceIdUserId(int businessServiceId, int LoggedInUserId);

        bool DeleteByIdTemp();


        int GetServiceCountDRReadyCurrentMonth();

        int GetServiceCountNotDRReadyCurrentMonth();


        int GetFunctionCountDRReadyCurrentMonth();

        int GetFunctionCountNotDRReadyCurrentMonth();

        int GetAllInfraObjectCount_NotRecovered();

        InfraObject GetInfraobjectByWorkflowId(int id);

    }

    #endregion IInfraObjectDataAccess
}