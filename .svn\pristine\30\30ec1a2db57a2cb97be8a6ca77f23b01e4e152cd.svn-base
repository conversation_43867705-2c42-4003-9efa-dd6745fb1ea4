﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System.Web;

namespace CP.UI.Controls
{
    public partial class Base24RepliList : BaseControl
    {

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;
        DropDownList _ddlReplicationType = new DropDownList();
        ReplicationType type;
        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }
        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }
        public override void PrepareView()
        {
            //     Binddata();
            
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        private void Binddata()
        {
            setListViewPage();
            //  var result = Facade.GetMSSqlDoubleTakeByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
            type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            var result = GetlvBase24ListByReplicationType(type.ToString());
            lvMSSqlDoubleTake.DataSource = result;
            lvMSSqlDoubleTake.DataBind();

        }
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPagelvMSSqlDoubleTakeList"]) != -1) && Session["CurrentPagelvMSSqlDoubleTakeList"] != null && (Convert.ToInt32(Session["CurrentPagelvMSSqlDoubleTakeList"]) > 0))
            {
                if (Session["TotalPageRowsCountlvMSSqlDoubleTakeList"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPagelvMSSqlDoubleTakeList"]) == Convert.ToInt32(Session["TotalPageRowsCountlvMSSqlDoubleTakeList"]) - 1)
                    {
                        Session["CurrentPagelvMSSqlDoubleTakeList"] = Convert.ToInt32(Session["CurrentPagelvMSSqlDoubleTakeList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCountlvMSSqlDoubleTakeList"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPagelvMSSqlDoubleTakeList"]), dataPager1.MaximumRows, true);
                dataPager1.DataBind();
                Session["CurrentPagelvMSSqlDoubleTakeList"] = -1;
            }
        }
        //private IList<MSSqlDoubletek> GetlvMSSqlDoubleTakeList()
        //{
        //    return Facade.GetMSSqlDoubleTakeByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
        //}

        public IList<Base24Replication> GetlvBase24ListByReplicationType(string iType)
        {
            //var replicationlist = Facade.GetMSSqlDoubleTakeByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
            var replicationlist = Facade.GetBase24ReplicationByCompanyIdAndRole(IsUserSuperAdmin, Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]), IsParentCompnay);

            if (replicationlist != null)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.reptype == iType
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public IList<Base24Replication> GetBase24List(string searchvalue)
        {
            var replicationlist = GetlvBase24ListByReplicationType("DRNET");

            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count> 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().StartsWith(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvMSSqlDoubleTake.Items.Clear();
                lvMSSqlDoubleTake.DataSource = GetBase24List(txtsearchvalue.Text);
                lvMSSqlDoubleTake.DataBind();
            }
        }
        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByReplicationId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Replication " + name + " attaching with group " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void lvMSSqlDoubleTake_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPagelvMSSqlDoubleTakeList"] = dataPager1.StartRowIndex;
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvMSSqlDoubleTake.Items[e.NewEditIndex].FindControl("Id")) as Label;

            var lblName = (lvMSSqlDoubleTake.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Base24", UserActionType.UpdateReplicationComponent,
                                      "The Base24 Replication component '" + lblName.Text +
                                      "' Opened as Editing Mode", LoggedInUserId);

            if (lbl1 != null && lblName != null && ValidateRequest("Base24Replication Edit", UserActionType.ReplicationList))
            {
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                //    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                Helper.Url.Redirect(secureUrl);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}

        }

        protected void lvMSSqlDoubleTake_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPagelvMSSqlDoubleTakeList"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCountlvMSSqlDoubleTakeList"] = dataPager1.TotalRowCount;
                var lblId = lvMSSqlDoubleTake.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvMSSqlDoubleTake.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
                //var ReplicationDetail = Facade.GetReplicationBaseById(Convert.ToInt32(lblId.Text));
                //var SiteDetail = Facade.GetSiteById(ReplicationDetail.SiteId);
                var InfraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));
                if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("Base24Replication Delete", UserActionType.ReplicationList))
                {
                    //var applicationDetailByReplicationId =
                    //    Facade.GetApplicationGroupsByReplicationId(Convert.ToInt32(lblId.Text));
                    //if (applicationDetailByReplicationId != null)
                    //{
                    //    ErrorSuccessNotifier.AddSuccessMessage("The SnapMirror Replication component is in use");
                    //}
                    //else
                    //{
                    if (InfraObjects != null && InfraObjects.Count > 0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The lvBase24 Replication component is in use.");

                    }
                    else
                    {
                        Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "lvBase24", UserActionType.DeleteReplicationComponent,
                                              "The lvBase24 Replication component '" + lblName.Text +
                                              "' was deleted from the replication component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "Base24 Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));

                    }
                }

            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);

            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);

                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "67");

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "67");
                Helper.Url.Redirect(secureUrl);
            }

        }

        protected void lvMSSqlDoubleTake_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator || IsUserManager || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }

        protected void lvMSSqlDoubleTake_PreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }
    }
}