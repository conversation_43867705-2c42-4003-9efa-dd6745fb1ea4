﻿using System;
using System.Diagnostics;
using System.Reflection;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.DataAccess.Configuration;
using Settings = CP.DataAccess.Configuration.Settings;

namespace CP.DataAccess.Base
{
    public abstract class BaseDataAccessFactory
    {
        #region Static Variables

       // private static readonly TypeInfo TypeInfo = Settings.TypeInfo;
        private static readonly CP.DataAccess.Configuration.TypeInfo TypeInfo = Settings.TypeInfo;

        #endregion Static Variables

        #region Instance Variables

        private Context _context;

        #endregion Instance Variables

        #region Property

        protected virtual Context CurrentContext
        {
            [DebuggerStepThrough]
            get { return _context ?? (_context = new Context()); }
        }

        #endregion Property

        #region Constructor

        [DebuggerStepThrough]
        protected BaseDataAccessFactory(Context context)
        {
            _context = context;
        }

        #endregion Constructer

        #region Static Methods

        [DebuggerStepThrough]
        public static BaseDataAccessFactory Create(Context context)
        {
#pragma warning disable 618
            return (BaseDataAccessFactory)AppDomain.CurrentDomain.CreateInstanceAndUnwrap(
#pragma warning restore 618
TypeInfo.AssemblyName,
                TypeInfo.TypeName,
                false,
                BindingFlags.Default,
                null,
                new object[] { context },
                null,
                null,
                null
                );
        }

        #endregion Static Methods

        #region ContinuityPatrol Factory Methods

        public abstract IActionHumanInterventionDataAccess CreateActionHumanInterventionDataAccess();

        public abstract IActionSetDataAccess CreateActionSetDataAccess();

        public abstract IAlertDataAccess CreateAlertDataAccess();

        public abstract IDropDownDataAccess CreateDropDownDataAccess();
        
        public abstract IAlertManagerDataAccess CreateAlertManagerDataAccess();

        public abstract IAlertReceiverDataAccess CreateAlertReceiverDataAccess();

        public abstract IAlertNotificationDataAccess CreateAlertNotificationDataAccess();

        //public abstract IAlertSettingDataAccess CreateAlertSettingDataAccess();

        public abstract IApplicationGroupDataAccess CreateApplicationGroupDataAccess();

        public abstract IApplicationGroupInfoDataAccess CreateApplicationGroupInfoDataAccess();

        public abstract IApplicationDependencyDataAccess CreateApplicationDependencyDataAccess();

        public abstract IAppDepMapSettings CreateApplicationDependencyMappingSettingsDataAccess();

        public abstract IAppDepMappingHosts CreateAppDepMappingHosts();

        public abstract IApplicationDepGroupNodes CreateAppDepGroupNodes();

        public abstract IAppDepMappingLinks CreateAppDepMappingLinks();

        public abstract IAppDepMappingProfileDetails CreateAppDepMappingProfileDetails();

        public abstract ITagEntityDetailsDataAccess CreateTagEntityDetailsDataAccess();

        public abstract IApplicationMonitorDataAccess CreateApplicationDetailDataAccess();

        public abstract IApplicationServiceDataAccess CreateApplicationServiceDataAccess();

        public abstract IApplicationDiscoveryDataAccess CreateApplicationDiscoveryDataAccess();

        public abstract IApplicationDiscoveryProfileDetailsDataAccess CreateApplicationDiscoveryProfileDetailsDataAccess();

        public abstract IArchiveDataAccess CreateArchiveDataAccess();

        public abstract IAuditDataAccess CreateAuditDataAccess();

        public abstract IBPAutomationDataAccess CreateBPAutomationDataAccess();

        public abstract IBusinessInfoDataAccess CreateBusinessInfoDataAccess();

        public abstract IBusinessFunctionDataAccess CreateBusinessFunctionDataAccess();

        public abstract IBusinessFunctionBIAActivityDataAccess CreateBusinessFunctionActivityBIADataAccess();

        public abstract IBusinessFunctionBIADetailsDataAccess CreateBusinessFunctionBIADetailsDataAccess();

        public abstract IBusinessFunctionBIADataAccess CreateBusinessFunctionBIADataAccess();

        public abstract IBusinessFunctionBIASeverityDataAccess CreateBusinessFunctionBIASeverityDataAccess();

        public abstract IBusinessFunctionBIARelationDataAccess CreateBusinessFunctionBIARelationDataAccess();

        public abstract IBusinessServiceDataAccess CreateBusinessServiceDataAccess();

        public abstract IBusinessServiceAvailabilityInfoDataAccess CreateBusinessServiceAvailabilityInfoDataAccess();

        public abstract IBusinessServiceRPOInfoDataAccess CreateBusinessServiceRPOInfoDataAccess();

        public abstract IBusinessServiceRTOInfoDataAccess CreateBusinessServiceRTOInfoDataAccess();

        public abstract IBusinessUserFunctionDataAccess CreateBusinessUserFunctionDataAccess();

        public abstract IBusinessFunctionRPODataAccess CreateBusinessUserFunctionRPODataAccess();

        public abstract IBusinessImpactDataAccess CreateBusinessImpactDataAccess();

        public abstract IBusinessTimeInterval CreateIBusinessTimeIntervalDataAccess();

        public abstract IBusinessProfile CreateBusinessProfileDataAccess();

        public abstract IBIAProfileTimeInterval CreateBIAProfileTimeIntervalDataAccess();

        public abstract IBIAProfileImpactTypes CreateBIAProfileImpactTypesDataAccess();

        public abstract ICompanyInfoDataAccess CreateCompanyInfoDataAccess();

        public abstract ICompanyProfileDataAccess CreateCompanyProfileDataAccess();

        public abstract ICustomExceptionDataAccess CreateCustomExceptionDataAccess();

        public abstract ICustomSubRoleTypeDataAccess CreateCustomSubRoleTypeDataAccess();

        public abstract ICPSLScriptDataAccess CreateCPSLScriptDataAccess();

        public abstract ICPSLScheduleDataAccess CreateCPSLScheduleDataAccess();

        public abstract IDatabaseBackupInfoDataAccess CreateDatabaseBackupInfoDataAccess();

        public abstract IDatabaseBackupOperationDataAccess CreateDatabaseBackupOperationDataAccess();

        public abstract IDatabaseBaseDataAccess CreateDatabaseBaseDataAccess();

        public abstract IDatabaseDb2DataAccess CreateDatabaseDb2DataAccess();

        public abstract IDatabaseSyBaseDataAccess CreateDatabaseSybaseDataAccess();

        public abstract IDatabaseSyBaseWithSrsDataAccess CreateDatabaseSybaseWithSrsDataAccess();

        public abstract IMongoDataBaseDataAccess CreateMongoDatabaseDataAccess();

    
       public abstract IDB2DataSyncMonitorDataAccess CreateDB2DataSyncDataAccess();

        public abstract IDatabaseExchangeDataAccess CreateDatabaseExchangeDataAccess();

        public abstract IDatabaseExchangeDAGDataAccess CreateDataBaseExchangeDAGDataAccess();

        public abstract IDatabaseMaxDBDataAccess CreateDatabaseMaxDBDataAccess();

        public abstract IDatabaseNodesDataAccess CreateDatabaseNodesDataAccess();

        public abstract IDatabaseOracleDataAccess CreateDatabaseOracleDataAccess();

        public abstract IDatabaseOracleRacDataAccess CreateDatabaseOracleRacDataAccess();

        public abstract IDatabaseSqlDataAccess CreateDatabaseSqlDataAccess();

        public abstract IDatabasePostgreSqlDataAccess CreateDatabasePostgreSqlDataAccess();

        public abstract IDatabasePostgre9xDataAccess CreateDatabasePostgre9xDataAccess();

        public abstract IDataBaseMSSqlDataAccess CreateDatabaseMSSqlDataAccess();

        //public abstract IDatabaseTypeDataAccess CreateDatabaseTypeDataAccess();

        public abstract IDatabaseVersionDataAccess CreateDatabaseVersionDataAccess();

        public abstract IDataGuardMonitorDataAccess CreateDataGuardMonitorDataAccess();

        public abstract IDataSyncPropertiesDataAccess CreateDataSyncPropertiesDataAccess();

        public abstract IDataGuardDataAccess CreateDataGuardDataAccess();

        public abstract IDiscoverScanDataAccess CreateDiscoverScanDataAccess();

        public abstract IDiscoveryConfigurationDataAccess CreateDiscoveryConfigurationDataAccess();

        public abstract IDiscoveryHostLogsDataAccess CreateDiscoveryHostLogsDataAccess();

        public abstract IDnsServerDetailsDataAccess CreateDnsServerDetailsDataAccess();

        public abstract IDomainDetailsDataAccess CreateDomainDetailsDataAccess();

        public abstract IDROperationDataAccess CreateDROperationDataAccess();

        public abstract IDROperationResultDataAccess CreateDROperationResultDataAccess();

        public abstract IEventManagementDataAccess CreateEventManagementDataAccess();

        public abstract IEventManagementListDataAccess CreateEventManagementListDataAccess();

        public abstract IEc2S3DataSyncMonitorDetailsDataAccess CreateEc2S3DataSyncMonitorDetailsDataAccess();

        public abstract IEc2S3DataSyncReplicationMonitorDataAccess CreateEc2S3DataSyncRepliMonitorDataAccess();

        public abstract IEMCDeviceDetailsDataAccess CreateEMCDeviceDetailsDataAccess();

        public abstract IEMCSRDFDataAccess CreateEMCSRDFDataAccess();

        public abstract IRecoveryPointDataAccess CreateRecoveryPointDataAccess();

        public abstract IRecoverPStateMonitorDataAccess CreateRecoverPStateMonitorDataAccess();

        public abstract IRecoverPStatisticMonitorDataAccess CreateRecoverPStatisticMonitorDataAccess();

        public abstract IExchangeHealthDataAccess CreateExchangeHealthDataAccess();

        public abstract IExchangeDAGCompMonitorDataAccess CreateExchangeDAGCompMonitorDataAccess();

        public abstract IExchangeDAGMonitoringDataAccess CreateExchangeDAGMonitoringDataAccess();

        public abstract IExchangeDAGReplHealthStatusDataAccess CreateExchangeDAGReplHealthStatusDataAccess();

        public abstract IExchangeDAGServiceMonitoringDataAccess CreateExchangeDAGServiceMonitoringDataAccess();

        public abstract IExchangeSCRStatusDataAccess CreateExchangeSCRStatusDataAccess();

        public abstract IExchangeServiceDataAccess CreateExchangeServiceDataAccess();

        public abstract IFastCopyDataAccess CreateFastCopyDataAccess();

        public abstract IFastCopyJobDataAccess CreateFastCopyJobDataAccess();

        public abstract IFastCopyMonitorDataAccess CreateFastCopyReplicationDataAccess();

        public abstract IGlobalMirrorDataAccess CreateGlobalMirrorDataAccess();

        public abstract IGlobalMirrorLunsDataAccess CreateGlobalMirrorLunsDataAccess();

        public abstract IGlobalMirrorMonitorDataAccess CreateGlobalMirrorReplicationDataAccess();

        public abstract IGroupDataAccess CreateGroupDataAccess();

        public abstract IInfraObjectJobDataAccess CreateInfraObjectJobDataAccess();

        public abstract IHADRReplicationDataAccess CreateHADRReplicationDataAccess();

        public abstract IHADRDataAccess CreateHadrDataAccess();

        //public abstract IInfraObjectsDataAccess CreateInfraObjectsDataAccess();

        public abstract IInfraobjectDiskMonitorDataAccess CreateInfraobjectDiskMonitorDataAccess();

        public abstract IInfraObjectsLunsDataAccess CreateInfraObjectsLunsDataAccess();

        public abstract IHeatmapDataAccess CreateHeatmapDataAccess();

        public abstract IImpactAnalysisDataAccess CreateImpactAnalysisDataAccess();

        public abstract IIncidentManagementSummaryDataAccess CreateIncidentManagementSummaryDataAccess();

        public abstract IIncidentManagementNewDataAccess CreateIncidentManagementNewDataAccess();

        public abstract IImpactTypeMasterDataAccess CreateImpactTypeMasterDataAccess();

        public abstract IImpactMasterDataAccess CreateImpactMasterDataAccess();

        public abstract IImpactRelTypeDataAccess CreateImpactRelTypeDataAccess();

        public abstract IIncidentDataAccess CreateIncidentDataAccess();

        public abstract IInfrastructureDataAccess CreateInfrastructureDataAccess();

        public abstract IIncidentManagementDataAccess CreateIncidentManagementDataAccess();

        public abstract IInfraObjectDataAccess CreateInfraObjectDataAccess();

        public abstract IJobTypeReplicationTypeDataAccess CreateJobTypeReplicationTypeDataAccess();

        public abstract IJobDataAccess CreateJobDataAccess();

        public abstract IGroupLunsDataAccess CreateGroupLunsDataAccess();

        public abstract IGroupWorkflowDataAccess CreateGroupWorkflowDataAccess();

        public abstract IGroupDatabaseNodesDataAccess CreateGroupDatabaseNodesDataAccess();

        public abstract ILicencekeyDataAccess CreateLicencekeyDataAccess();

        public abstract ILogVolumeDataAccess CreateLogVolumeDataAccess();

        public abstract IMaintenanceDataAccess CreateMaintenanceDataAccess();

        public abstract IMaxDBMonitorDataAccess CreateMaxDBMonitorDataAccess();

        public abstract IMaxDBReplicationDataAccess CreateMaxDBReplicationDataAccess();

        public abstract IMountPointDataAccess CreateMountPointDataAccess();

        public abstract IMonitorServicesDataAccess CreateMonitorServicesDataAccess();

        public abstract IMonitorQueueDataAccess CreateMonitorQueueDataAccess();

        public abstract IQueueMoniterDataAccess CreateQueueMoniterDataAccess();

        public abstract IMonitorServiceStatusLogsDataAccess CreateMonitorServiceStatusLogsDataAccess();

        public abstract IMssqlEmcSrdfMonitorDataAccess CreateMssqlEmcSrdfMonitorDataAccess();

        public abstract INetworkIPDataAcess CreateNetworkIPDataAccess();

        public abstract INodesDataAccess CreateNodesDataAccess();

        public abstract IOraleLogDataAccess CreateOracleLogDataAccess();

        public abstract IOracleEmcSrdfVMAXDataAccess CreateOracleEmcSrdfVMAXDataAccess();

        public abstract IOracleEmcSrdfDMXDataAccess CreateOracleEmcSrdfDMXDataAccess();

        public abstract IPostgredbMonitoringDataAccess CreatePostgredbMonitoringDataAccess();

        public abstract IPostgreReplicationDataAccess CreatePostgreReplicationDataAccess();

        public abstract IParallelDROperationDataAccess CreateParallelDROperationDataAccess();

        public abstract IParallDrOpActEnvDataAccess CreateParallDrOpActEnvDataAccess();

        public abstract IParallelGroupWorkflowDataAccess CreateParallelGroupWorkflowDataAccess();

        public abstract IParallelServerDataAccess CreateParallelServerDataAccess();

        public abstract IParallelWorkflowActionResultDataAccess CreateParallelWorkflowActionResultDataAccess();

        public abstract IParallelProfileDataAccess CreateParallelProfileDataAccess();

        public abstract IParallelWorkflowProfileDataAccess CreateParallelWorkflowProfileDataAccess();

        public abstract IReplicationBaseDataAccess CreateReplicationBaseDataAccess();

        public abstract IReportScheduleDataAccess CreateReportScheduleDataAccess();

        public abstract IRTOMTRConfigurationDataAccess CreateRTOMTRConfigurationDataAccess();

        public abstract ISCRDataAccess CreateSCRDataAccess();

        public abstract IExchageDagReplicationDataAccess CreateExchangeReplicationDataAccess();

        public abstract IServerDataAccess CreateServerDataAccess();

        public abstract ISiteTypesDataAccess CreateSiteDataAccess();

        public abstract ISettingDataAccess CreateSettingDataAccess();

        public abstract ISiteInchargeInfoDataAccess CreateSiteInchargeInfoDataAccess();

        public abstract ISMSConfigurationDataAccess CreateSMSconfigurationDataAccess();

        public abstract ISmtpConfigurationDataAccess CreateSmtpconfigurationDataAccess();

        public abstract ISnapMirrorDataAccess CreateSnapMirrorDataAccess();

        public abstract ISnapMirrorMonitorDataAccess CreateSnapMirrorMonitorDataAccess();

        public abstract ISqlNativeDataAccess CreateSqlNativeDataAccess();

        public abstract ISSOConfigurationDataAccess CreateSingleSignOnDataAccess();

        public abstract ISqlNativeMonitorDataAccess CreateSqlNativeMonitorDataAccess();

        public abstract ISQLNative2008MonitorDataAccess CreateSQLNative2008MonitorDataAccess();

        public abstract ISqlNativeHealthDataAccess CreateSqlNativeHealthDataAccess();

        public abstract ISQLNativeHeathParaDataAccess CreateSqlNativeHealthParaDataAccess();

        public abstract ISqlNativeServicesDataAccess CreateSqlNativeServicesDataAccess();

        public abstract ISQLNative2008ReplicationDataAcess CreateSQLNative2008ReplicationDataAcess();

        public abstract IUserActivityDataAccess CreateUserActivityDataAccess();

        public abstract IUserDataAccess CreateUserDataAccess();

        public abstract IUserGroupDataAccess CreateUserGroupDataAccess();

        public abstract IUserInfraObjectDataAccess CreateUserInfraObjectDataAccess();

        public abstract IUserInfoDataAccess CreateUserInfoDataAccess();

        public abstract IVmwareMonitorDataAccess CreateVmwareMonitorDataAccess();

        public abstract IWorkflowActionDataAccess CreateWorkflowActionDataAccess();

        public abstract IWorkflowDataAccess CreateWorkflowDataAccess();

        public abstract ISql2000LogDataAccess CreateSqlLog2000DataAccess();

        public abstract ISql2000HealthDataAccess CreateSql2000HealthDataAccess();

        public abstract ISql2000ServiceDataAccess CreateSqlService2000DataAccess();

        public abstract IHitachiUrReplicationDataAccess CreateHitachiUrReplicationDataAccess();

        public abstract IHitachiURMonitoringDataAccess CreateHitachiUrMonitoringDataAccess();

        public abstract IHitachiURDeviceMonitoringDataAccess CreateHitachiUrDeviceMonitoringDataAccess();

        public abstract IHitachiUrLunsDataAccess CreateHitachiUrLunsDataAccess();

        public abstract IScheduleWorkflowDataAccess CreateWorkflowScheduleDataAccess();

        public abstract IMySQLGlobalMirrorMonitorDataAccess CreateMysqlGlobalMirrorMonitorDataAccess();

        public abstract IMySqlNativeMonitorDataAccess CreateMySqlNativeMonitorDataAccess();

        public abstract IDatabaseMySqlDataAccess CreateDatabaseMySqlDataAccess();

        public abstract IPostgre9xComponentMonitorDataAccess CreatePostgre9xComponentMonitorDataAccess();

        public abstract IPostgre9xMonitorStatusDataAccess CreatePostgre9xMonitorStatusDataAccess();

        public abstract IEC2S3DataSyncDataAccess CreateEC2S3DataSyncReplicationDataAcess();

        public abstract IVMWareNetSnapMirrorDataAccess CreateVMWareNetSnapMirrorMonitorDataAccess();

        public abstract IActiveODGMonitorDataAccess CreateODGMonitorDataAccess();

        public abstract IInfraobjectScheduleworkFlowDataAccess CreateInfraobjectScheduleworkFlowDataAccess();

        public abstract IMSSqlDoubletekRepliMonitorDataAccess CreateMSSqlDoubletekRepliMoniDataAccess();

        public abstract IMSSqlDoubleTakeRepliDataAccess CreateMSSqlDoubleTakeRepliDataAccess();

        public abstract IBFBIAMatrixDataAccess CreateBFBIAMatrixDataAccess();

        public abstract IBFBIAMatrixDetailsDataAccess CreateBFBIAMatrixDetailsDataAccess();

        public abstract IDatabaseMirrorMonitorDataAccess CreateDatabaseMirrorMonitorDataAccess();

        public abstract IMssqlDMXEmcSrdfMonitorDataAccess CreateMssqlDMXEmcSrdfMonitorDataAccess();

        public abstract IMssqlVMAXEmcSrdfMonitorDataAccess CreateMssqlVMAXEmcSrdfMonitorDataAccess();

        public abstract ISybaseDataAccess CreateSybaseMonitorDataAccess();

        public abstract IAccessManagerDataAccess CreateAccessManagerDataAccess();

        public abstract IMimixRepliDataAccess CreateMimixReplicationDataAccess();

        public abstract IMimixDataLagDataAccess CreateMimixDatalgMonitorDataAccess();

        public abstract IMimixHealthDataAccess CreateMimixHealthReplicationDataAccess();

        public abstract IMimixAlertsDataAccess CreateMimixAlertsMonitorDataAccess();

        public abstract IMimixManagerDataAccess CreateMimixManagerMonitorDataAccess();

        public abstract IMimixAvilabilityDataAccess CreateMimixAvilabilityMonitorDataAccess();

        public abstract IHyperVDataAccess CreateHyperVMonitorDataAccess();

        public abstract IHyperVMonitorDataAccess CreateHyperVComponentMonitorDataAccess();

        public abstract IClusterSummaryDataAccess CreateClusterSummaryDataAccess();

        public abstract IClusterNodeDataAccess CreateClusterNodeDataAccess();

        public abstract IMySqlRepliDataAccess CreateMySqlRepliDataAccess();

        public abstract IMySqlMonitorDataAccess CreateMySqlMonitorDataAccess();

        public abstract ISVCGlobalMirrorORMetroConfigDataAccess CreateSVCGlobalMirrorOrMetroDataAccess();

        public abstract ISVCGlobalMirrorMonitorDataAccess CreateSVCGlobalMirrorDataAccess();

        public abstract ISVCcontrollerMonitorDataAccess CreateSVCControllerMonitorDataAccess();

        public abstract ISVCNodeDetailedDataAccess CreateSVCNodeDetailedrMonitorDataAccess();

        public abstract ISVCGMReplicationMDataAccess CreateDatabaseSVCGMReplicationMDataAccess();

        public abstract IInfraobjectGlobalMirrorLunsDataAccess CreateInfraobjectGlobalMirrorlunsDataAccess();

        public abstract IInfraobjectGlobaliMirrorlunsDetailsDataAccess CreateInfraobjectGlobalMirrorlunsDetailsDataAccess();

        public abstract IInfraobjectSchedularStatus CreateInfraobjectSchedularStatusDetailsDataAccess();

        public abstract IInfraobjectSchedularLogs CreateInfraobjectSchedularLogsDetailsDataAccess();

        public abstract IInfrascheduleWfDataAccess CreateInfraobjectSchedularWorkflowDetailsDataAccess();

        public abstract IMSSQLDBMirrorDataAccess CreateMSSQLDBMirrorDataAccess();

        public abstract IMSSQLDBMirrorReplicationDataAccess CreateMSSQLDBMirrorReplicationDataAccess();

        public abstract IInfraobjectCGDetailsDataAccess CreateInfraobjectCGDetailsDataAccess();

        public abstract IVmWarePathDetailDataAccess CreateVmWarePathDetailsDataAccess();

        public abstract IXIVMirrorDataAccess CreateXIVMirrorDataAccess();

        public abstract IXIVReplicationMonitorDataAccess CreateXIVReplicationMonitorDataAccess();

        public abstract IXIVMirrorStatisticsDataAccess CreateXIVMirrorStatisticsDataAccess();

        public abstract IIBMXIVConfigurationDataAccess CreateIBMXIVConfigurationDataAccess();

        public abstract IInfraobjectVolumeDataAccess CreateInfraobjectVolumeDetailsDataAccess();

        public abstract ICGMonitoringDataAccess CreateCGMonitoringDataAccess();

        public abstract ICGVolumeMontoringDataAccess CreateCGVolumeMontoringDataAccess();

        public abstract IClustorMonitoringDataAccess CreateClustorMonitoringDataAccess();

        public abstract ISMSReportDataAccess CreateSMSReportDataAccess();

        public abstract IEmailReportDataAccess CreateEmailReportDataAccess();

        public abstract IScheduleDiscProfDetailsDataAccess CreateScheduleDiscoveryProfileDetailsDataAccess();

        public abstract ISRMVmwareMonitorDataAccess CreateSRMVmwareMonitorDataAccess();

        public abstract IVCenterMonitorProfileDataAccess CreateVCenterMonitorProfileDataAccess();

        public abstract IAnalyticsDataAccess CreateAnalyticsDataAccess();

        public abstract IActionAnalyticDataAccess CreateActionAnalyticDataAccess();

        public abstract IASMGirdDataAccess CreateActionASMGirdDataAccess();

        public abstract IBIAHuminterventionsActionDataAccess CreateBIAHuminterventionsActionDataAccess();

        public abstract IBIAFailureActinBySolTypeDataAccess CreateIBIAFailureActinBySolTypeDataAccess();

        public abstract IBIAFailureActinBySOSBDataAccess CreateBIAFailureActinBySOSBDataAccess();

        public abstract IBIAActionCountCompletedOutOfRTOBySolDataAccess CreateBIAActionCountCompletedOutOfRTOBySolDataAccess();

        public abstract IBIAFailureActionByWorkflowTypeDataAccess CreateBIAFailureActionByWorkflowTypeDataAccess();

        public abstract IBIAActionEfficiencyDataAccess CreateBIAActionEfficiencyDataAccess();

        public abstract IBIAWorkflowAnalyaticDataAccess CreateBIAWorkflowAnalyaticDataAccess();

        public abstract IBIAActionEffiAutoModeDataAccess CreateBIAActionEffiAutoModeDataAccess();

        public abstract IBIAFailedWorkflowByWorkflowType CreateIBIAFailedWorkflowByWorkflowType();

        public abstract IBIAFailedActionDataAccess CreateBIAFailedActionDataAccess();

        public abstract IBIASuccessWFvsHIDataAccess CreateBIASuccessWFvsHIDataAccess();

        public abstract IBIAFailedWorkflowDataAccess CreateIBIAFailedWorkflowDataAccess();

        public abstract IBIAOvervallWorkflowStatisticsDatatAccess CreateBIAOvervallWorkflowStatisticsDataAccess();

        public abstract IBIAActionEffiAutoModeDataAccess CreateIIBIAActionEffiAutoModeDataAccess();

        public abstract IBIAWorkflowfailureprofileTrendDataAccess CreateBIAWorkflowfailureprofileTrendDataAccess();

        public abstract IBIAWorkflowFailureWorkflowTypeTrendDataAccess CreateBIAWorkflowFailureWorkflowTypeTrendDataAccess();

        public abstract IBIAWorkflowTrendsAllDataAccess CreateBIAWorkflowTrendsAllDataAccess();

        public abstract IBIAWorkflowProfilesHumanInterventionsTrendDataAccess CreateBIAWorkflowProfilesHumanInterventionsTrendDataAccess();

        public abstract IBIAWorkflowefficiencyTrendDataAccess CreateBIAWorkflowefficiencyTrendDataAccess();

        public abstract IBIAImpactCountDataAccess CreateBIAImpactCountDataAccess();

        public abstract IBIAActiontrendComponentDataAccess CreateBIAActiontrendComponentDataAccess();

        public abstract IBIAActionFailureHumanInterventionTrendDataAccess CreateIBIAActionFailureHumanInterventionTrendDataAccess();

        public abstract IBIAProfileDetailsActionEfficiencyTrendDataAccess CreateBIAProfileDetailsActionEfficiencyTrendDataAccess();

        public abstract IBIAWorkflowCompletedWithinRTODataAccess CreateBIAWorkflowCompletedWithinRTODataAccess();

        public abstract IBIAAlertCountBusinessServiceWiseDataAccess CreateBIAAlertCountBusinessServiceWiseDataAccess();

        public abstract IBIAGetAlertDetailsDataAccess CreateBIAGetAlertDetailsDataAccess();

        public abstract IBIAAlertsTrendBusinessServiceDataAccess CreateBIAAlertsTrendBusinessServiceDataAccess();

        public abstract IBIAOverallAlertStatisticsDataAccess CreateBIAOverallAlertStatisticsDataAccess();

        public abstract IIncidentManagementBIASummaryDataAccess CreateIncidentManagementBIASummaryDataAccess();

        public abstract IImportCMDBDataAccess CreateImportCMDBDataAccess();

        public abstract IDefaultMonitorServices CreateDefaultMonitorServicesDataAccess();

        public abstract ISybaseWithSRSRepliDataAccess CreateSybaseWithSRSDataAccess();

        public abstract ISybaseWithSRSDataAccess CreateSybaseWithSRSMonitorDataAccess();

        public abstract IMysqlFullDBMonitorDataAccess CreateMyslFullDbDataAccess();

        public abstract IeBDRProfileDataAccess CreateeBDRProfileDataAccess();

        public abstract IeBDRProfileReplication CreateeBDRProfileReplicationDataAccess();

        public abstract IeBDRProfileRepli CreateeBDRProfileRepliDataAccess();

        public abstract IBase24RepliDataAccess CreateBase24ReplicationDataAccess();

        public abstract IBase24RepliMonitorDataAccess CreateBase24RepliMonitorDataAccess();

        public abstract ITPRCReplicationDataAccess CreateTPRCReplicationDataAccess();

        public abstract ITPRCMonitorDataAccess CreateTPRCMonitorDataAccess();

        public abstract IBSDRReadyDaily CreateBSDRReadyDailyDataAccess();

        public abstract IComponentFailureDailyDataAccess CreateComponentFailureDailyDataAccess();

        public abstract IServiceRTODailyDataAccess CreateServiceRTODailyDataAccess();

        public abstract ISubstituteAuthenticateDataAccess CreateSubstituteAuthenticateDataAccess();

        public abstract IMSSQLAlwaysOnReplicationDataAcess CreateMSSQLAlwaysOnReplicationDataAcess();

        public abstract IMSSQL2014ServerDataAccess CreateMSSQL2014ServerMonitorDataAccess();

        public abstract IServiceProfile CreateServiceProfileDataAccess();

        public abstract IUserServiceDataAccess CreateUserServiceProfileDataAccess();

        public abstract IHP3PARStorageDataAccess CreateHP3PARStorageDataAccess();

        public abstract IHP3PAR_MonitorDataAccess CreateHP3PARMonitorDataAccess();

        public abstract IVirtualMonitoringDataAccess CreateVirtualmonitoringDataAccess();

        //public abstract IActiveODGMonitorDataAccess CreateODGMonitorDataAccess();

        public abstract IAodgLogRepliDetailsDataAccess CreateAodgLogRepliDetailsDataAccess();

        public abstract IActiveODGReplicationNonOdgDataAccess CreateAodgNonODGLogRepliDetailsDataAccess();

        public abstract IVVRReplicationDataAccess CreateVVRReplicationDataAccess();

        public abstract IReplicatedGroupNameDataAccess CreateReplicatedGroupNameDataAccess();

        public abstract IRlinkMonitorSecUpdateDataAccess CreateRlinkMonitorSecUpdateDataAccess();

        public abstract IRlinkMonitorRepliPerformDataAccess CreateRlinkMonitorRepliPerformDataAccess();

        public abstract IEmcsrdfSGLogsDataAccess CreateEmcsrdfSGLogsDataAccess();

        public abstract IEMCSRDFSGDataAccess CreateEMCSRDFSGDataAccess();

        public abstract IEMCSRDFStarDataAccess CreateEMCSRDFStarDataAccess();

        public abstract IEMCSRDFStarMonitorDataAccess CreateEMCSRDFStarMonitorDataAccess();

        public abstract IVeritasClusterDataAccess CreateActionVeritasClusterDataAccess();

        public abstract IVeritasClusterMonitoringDataAccess CreateActionVeritasClusterMonitorDataAccess();

        public abstract IZFSReplicationMonitorLogsDataAccess CreateZFSReplicationMonitorLogsDataAccess();

        public abstract IZFSStorageReplicationDataAccess CreateZFSReplicationDataAccess();

        public abstract IMaxFullDBEmcSrdfMonitorDataAccess CreateMaxFullDBEmcSrdfMonitorDataAccess();

        public abstract IEmcISilonReplicationDataAccess CreateEmcISilonReplicationBaseDataAccess();

        public abstract IEmcISilonRepliPolicyDataAccess CreateEmcISilonReplicationPolicyDataAccess();

        public abstract IEmcISilon_MonitorDataAccess CreateEmcISilonMonitorDataAccess();

        public abstract IEmcMirrorViewRepliDataAccess CreateEmcMirrorViewReplicationDataAccess();

        public abstract IEmcMirrorViewRepliMonitorDataAccess CreateEmcMirrorViewRepliMonitorDataAccess();

        public abstract IEmc_MV_Mirror_MonitorDataAccess CreateEmc_MV_Mirror_MonitorDataAccess();

        public abstract IHACMPClusterDataAccess CreateHACMPClusterDataAccess();

        public abstract IHACMPClusterDetailsMonitorDataAccess CreateHACMPClusterDetailsDataAccess();

        public abstract IHACMPResourceGroupsMonitorDataAccess CreateHACMPResourceGroupsMonitorDataAccess();

        public abstract IRoboCopyDataAccess CreateRoboCopyDataAccess();

        public abstract IRoboCopyJobDataAccess CreateRoboCopyJobDataAccess();

        public abstract IRoboCopyOptionsDataAccess CreateRoboCopyOptionsDataAccess();

        public abstract IRoboCopyLogsDataAccess CreateRoboCopyLogsDataAccess();


        public abstract IRecoveryPointMultiDataAccess CreateRecoveryPointMultiDataAccess();


        public abstract IDatabaseSyBaseWithRsHadrDataAccess CreateDatabaseSybaseWithRsHadrDataAccess();

        public abstract ISybaseWithRSHADRDataAccess CreateSybaseWithRSHADRDataAccess();

        public abstract ISybaseWithRSHADR_DBMonitorDataAccess CreateSybaseRsHadrDBMonitorDataAccess();

        public abstract ISybaseWithRSHADR_RepliMonitorDataAccess CreateSybaseRsHadrRepliMonitorDataAccess();

        public abstract ISybaseWithRSHADR_RepliMonitorDataAccessNew CreateSybaseRsHadrRepliMonitorDataAccessNew();

        public abstract IEmcUnityRepliDataAccess CreateEmcUnityReplicationDataAccess();

        public abstract IEmcUnityRepli_MonitorDataAccess CreateEmcUnityRepliMonitorDataAccess();

        public abstract IMongoDBDMonitorStatusDataAccess CreateMongoDBDMonitorStatusDataAccess();

        public abstract IDatabaseHanaDbDataAccess CreateDatabaseHanaDBDataAccess();

        public abstract IHanaDbMonitorDataAccess CreateHanaDbMonitorDataAccess();

        public abstract IHanaDbDatabaseServiceDataAccess CreateHanaDbServiceDataAccess();

        public abstract IHanaDbReplicationModeDataAccess CreateHanaDbRepliModeDataAccess();

        public abstract IHanaDbSystemOperationModeDataAccess CreateHanaDbOperationModeDataAccess();

        public abstract IGoldenGateRepliDataAccess CreateGoldenGateReplicationDataAccess();

        public abstract IGoldenGateDBMonitorDataAccess CreateGoldenGateDBMonitorDataAccess();

        public abstract IGoldenGateRepliMonitorDataAccesscs CreateGoldenGateRepliMonitorDataAccess();

        public abstract IGoldenGateGroupDetailsDataAccesscs CreateGoldenGateGroupDetMonitorDataAccess();

        public abstract IRSyncDataAccess CreateRSyncDataAccess();

        public abstract IRSyncJobDataAccess CreateRSyncJobDataAccess();

        public abstract IRSyncOptionsDataAccess CreateRSyncOptionsDataAccess();

        public abstract IRSyncMonitorDataAccess CreateRSyncMonitorDataAccess();

        public abstract IDatabaseAuthenticateDataAccess CreateDatabaseSubstituteAuthenticateDataAccess();

        public abstract IDatabaseSqlplusDataAccess CreateDatabaseSqlplusDataAccess();

        public abstract IDatabaseRoleDataAccess CreateDatabasRoleDataAccess();

        public abstract INodeAuthenticateDataAccess CreateNodeSubstituteAuthenticateDataAccess();

        public abstract IServiceDRProtectionDataAccess CreateServiceDRProtectionDataAccess();

        public abstract IServiceDiagramDataAccess CreateServiceDiagramDataAccess();

        public abstract IParallelThreadDetailsDataAccess CreateParallelThreadDetailsDataAccess();  

        public abstract ILogViewerDataAccess CreateLogViewerDataAccess();

        public abstract ICPNodeMasterDataCcess CreateCPNodeMasterDataAccess();

        public abstract ILogFileDetailsDataAccess CreateLogFileDetailsDataAccess();

        public abstract IApprovalProcessDataAccess CreateApprovalProcessDataAccess();

        public abstract IApprovalMappingDataAccess CreateApprovalMappingDataAccess();

        public abstract IIMAPConfigrationDataAccess CreateIMAPfigurationDataAccess();

        public abstract IApprovalDataAccess CreateApprovalDataAccess();

        public abstract IApprovalReceivEmail CreateApprovalReceivEmailDataAccess();

        public abstract IApprovalSentMail CreateApprovalSentEmailDataAccess();

        public abstract IApprovalTimelineViwe CreateApprovalTimelineViwe();

        public abstract IApprovalLevelProcessDataAccess CreateApprovalLevelProcessDataAccess();

        public abstract IOTPConfigurationDataAccess CreateOTPConfigDataAccess();

        public abstract ICPLoadMasterDataAccess CreateCPLoadMasterDataAccess();

        public abstract ICPLoadMasterLogs CreateCPLoadMasterLogsDataAccess();


        public abstract ISitetypes CreateSiteTypesDataAccess();



        #endregion ContinuityPatrol Factory Methods


    }
}