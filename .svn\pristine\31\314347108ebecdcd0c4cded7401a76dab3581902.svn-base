﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    public interface IRSyncMonitorDataAccess
    {
        RSyncMonitor GetRSyncMonitorInfrad(int infraId, int jobid);

        IList<RSyncMonitor> RSyncMonitor_GetAllByInfraId(int infraobjectid);

        IList<RSyncMonitor> RSynDetailsGetByDate(int InfraObjectId, string startDate, string endDate);

        IList<RSyncMonitor> GetRsyncMonitorByDate(string startDate, string endDate);

        IList<RSyncMonitor> GetHourlyRsyncByGroupId(int groupid);

        IList<RSyncMonitor> GetAllRecords_forReport(string startDate, string endDate);
    }
}
