﻿using System;
using System.Web.UI;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System.Web.UI.WebControls;
using System.Web;

namespace CP.UI
{
    public partial class ReplicationConfiguration : ReplicationBasePage
    {
        private UserControl _userControl = new UserControl();

        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }


            ddlRepType.Attributes.Add("onblur", "ValidatorValidate(" + rfvRepType.ClientID + ")");
            txtReplName.Attributes.Add("onblur", "ValidatorValidate(" + rfvHostname.ClientID + ")");
            ddlSite.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");


            Utility.SelectMenu(Master, "Module2");
            // EnumHelper.PopulateEnumIntoList(ddlRepType, typeof(ReplicationType), "- Select Replication Type -");
            EnumHelper.PopulateEnumDescriptionIntoList(ddlRepType, typeof(ReplicationType), "- Select Replication Type -");
            ddlRepType.RemoveItem("DB2HADR", "15");
            ddlRepType.RemoveItem("MSSQLServerNative", "4");
            ddlRepType.RemoveItem("HITACHI TrueCopy", "8");
            ddlRepType.RemoveItem("MySQL-GlobalMirrorFullDB", "22");
            ddlRepType.RemoveItem("MySQL-Native", "23");
            ddlRepType.RemoveItem("VMWare with Globalmirror", "11");
            //ddlRepType.RemoveItem("VMWare with Snapmirror", "12");
            ddlRepType.RemoveItem("Sql2000DataSync", "13");
            ddlRepType.RemoveItem("DB2HADR9X", "24");
            ddlRepType.RemoveItem("SAP HANADB Replication", "105");
            ddlRepType.RemoveItem("Postgres 9.X", "27");
            ddlRepType.RemoveItem("Postgres 10.4", "110");
            ddlRepType.RemoveItem("Sybase-SRS", "65");
            Utility.PopulateSiteByCompanyIdAndRole(ddlSite, LoggedInUserCompanyId, IsSuperAdmin, IsParentCompnay, true);

            //if (IsSuperAdmin)
            //{
            //    var SitelistDtails = Facade.GetSitesByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
            //    ddlSite.DataSource = SitelistDtails;
            //    ddlSite.DataTextField = "Name";
            //    ddlSite.DataValueField = "Id";
            //    ddlSite.DataBind();
            //}
            //else
            //{
            //    var SitelistDtails = Facade.GetSiteByUserId(LoggedInUserId);
            //    ddlSite.DataSource = SitelistDtails;
            //    ddlSite.DataTextField = "Name";
            //    ddlSite.DataValueField = "Id";
            //    ddlSite.DataBind();
            //}
            
            LoadData();

            Session["_token"] = UrlHelper.AddTokenToRequest();
            if (Session["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(Session["_token"]);
            }
            if (Session["ReplicationConfig_token"] == null)
            {
                Session["ReplicationConfig_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            }


            if (Session["ReplicationConfig_token"] != null)
            {
                hdtokenKey.Value = Session["ReplicationConfig_token"].ToString();
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
        }

        protected void TxtReplNameTextChanged(object sender, EventArgs e)
        {
            try
            {
                // ulMessage.Visible = false;

                if (ddlRepType.SelectedValue != "0")
                {
                    if (txtReplName.Text != string.Empty)
                    {
                        var linuxpath = ucnFastCopyConfiguration.FindControl("rfvDatasynpath") as RequiredFieldValidator;
                        var windowpath = ucnFastCopyConfiguration.FindControl("rfvWindowpath") as RequiredFieldValidator;
                        lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
                        DropDownList ddlEditDSPropertiesList = ucnRoboCopyRepliConfig.FindControl("ddlEditDSPropertiesList") as DropDownList;
                        DisableUserControl((ReplicationType)Convert.ToInt32(ddlRepType.SelectedValue),
                                           lblPrName.Text == string.Empty);

                        rfvHostname.Validate();
                        revRepName.Validate();
                        linuxpath.Visible = false;
                        windowpath.Visible = false;
                    }
                }
            }
            catch (CpException)
            {
            }
            catch (Exception)
            {
            }
        }

        private void DisableUserControl(ReplicationType replicationType, bool isEnable)
        {
            switch (replicationType)
            {

                case ReplicationType.DB2IBMGLobalmirror:
                    ucnGlobalMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.IBMGlobalMirror:
                    ucnGlobalMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.OracleDataGuard:
                    ucnOracleDataguardConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.DataSync:
                    ucnFastCopyConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.MSSCR:
                    ucnMSSCRConfiguration.Visible = isEnable;

                    break;

                case ReplicationType.NetAppSnapMirror:
                    ucnSnapMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.VMWareDataSync:
                    ucnVmwareFastCopyConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDF:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.OracleWithDataSync:
                    ucnFastCopyConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HITACHIUROracleFullDB:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HITACHIUROracleLogShipping:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFOracleLogShipping:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFOracleFullDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.VMWareHitachiUR:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.MSExchangeDAG:
                    ucnExchangeDAGConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.DB2DataSync:
                    ucnFastCopyConfiguration.Visible = isEnable;
                    break;
                case ReplicationType.EMCSRDFMSSQLFullDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;
                case ReplicationType.SQLNative2008:
                    ucnSQLNative2008ReplicationConfiguration.Visible = isEnable;
                    break;
                case ReplicationType.EC2S3DataSync:
                    ucnEC2DataSyncReplication.Visible = isEnable;
                    break;

                case ReplicationType.MSSqlNetAppSnapMirror:
                    ucnSnapMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.OracleFullDBNetAppSnapMirror:
                    ucnSnapMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.VMWareSnapMirror:
                    ucnSnapMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.MSSQLDoubleTakeFullDB:
                    ucnMSSqlDoubleTakeConfig.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFVMAXOracleFULLDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFDMXOracleFULLDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFVMAXMSSQLFULLDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFDMXMSSQLFULLDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.SyBaseWithDataSync:
                    ucnFastCopyConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.ApplicationDoubleTake:
                    ucnMSSqlDoubleTakeConfig.Visible = isEnable;
                    break;

                case ReplicationType.MIMIX:
                    ucnMimixConfig.Visible = isEnable;
                    break;

                case ReplicationType.HitachiOracleFullDBRac:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFOracleRacFullDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFSyBase:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HitachiSyabse:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HyperV:
                    ucnHypervConfig.Visible = isEnable;
                    break;

                case ReplicationType.AppHitachiUr:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.MySqlNativeLogShipping:
                    ucnMySqlConfig.Visible = isEnable;
                    break;

                case ReplicationType.OracleFullDBSVCGlobalMirror:
                    ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.SVCGlobalMirrorORMetroMirror:
                    ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.MSSQLDBMirroring:
                    ucnMssqlDbMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.SVCMSSQLFullDB:
                    ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.VMWareWithSVC:
                    ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HitachiURMSSQLFullDB:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.GlobalMirrorMSSQLFullDB:
                    ucnGlobalMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.DB2IBMXIVMIRROR:
                    ucnIBMXIVMirrorconfiguration.Visible = isEnable;
                    break;

                case ReplicationType.NetAppSnapMirrorPostgresFullDB:
                    ucnSnapMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.ApplicationXIVMIRROR:
                    ucnIBMXIVMirrorconfiguration.Visible = isEnable;
                    break;

                case ReplicationType.SVCGlobalMirrorOracleFullDBRac:
                    ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = isEnable;
                    break;



                case ReplicationType.EMCSRDFMysqlFullDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;
                case ReplicationType.SybaseWithSRS:
                    ucnSybaseWithSRS.Visible = isEnable;
                    break;

                case ReplicationType.MaxDBWithDataSync:
                    ucnFastCopyConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.ApplicationeBDR:
                case ReplicationType.VirtualeBDR:
                    uceBDRReplicationConfig.Visible = isEnable;
                    break;

                case ReplicationType.DRNET:
                    ucBase24Config.Visible = isEnable;
                    break;
                case ReplicationType.TPCR:
                    ucnTPRCConfigurtion.Visible = isEnable;
                    break;

                case ReplicationType.MSSQLAlwaysOn:
                    ucnMSSQLAlwaysOnReplicationConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.RecoverPoint:
                    ucnRecoveryPointConfig.Visible = isEnable;
                    break;

                case ReplicationType.RecoverPointOracleFULLDB:
                    ucnRecoveryPointConfig.Visible = isEnable;
                    break;

                case ReplicationType.RecoverPointMSSQLFULLDB:
                    ucnRecoveryPointConfig.Visible = isEnable;
                    break;

                case ReplicationType.RecoverPointMYSQLFULLDB:
                    ucnRecoveryPointConfig.Visible = isEnable;
                    break;

                case ReplicationType.HP3PARMSSQLFULLDB:
                    ucHP3PARStorageMSSqlConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HP3PARORACLEFULLDB:
                    ucHP3PARStorageMSSqlConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HP3ParwithPostgressMSSQL:
                    ucHP3PARStorageMSSqlConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HP3ParwithESXI:
                    ucnHP3PARWithESXIConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HP3ParwithApplication:
                    ucHP3PARStorageMSSqlConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.MSSQLFullDBVVRReplication:
                    ucnVVRMssqlFullDBConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFSGORACLEFULLDB:
                    ucnEMCSRDFSGConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFSGAPPLICATION:
                    ucnEMCSRDFSGConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFSTARORACLEFULLDB:
                    ucnEMCSRDFStarConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFSTARAPPLICATION:
                    ucnEMCSRDFStarConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFMSQLFULLDBSG:
                    ucnEMCSRDFSGConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFMSQLFULLDBSTAR:
                    ucnEMCSRDFStarConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.ZFSMaxFULLDB:
                    ucnZFSApplicationConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCISilon:
                    ucnEMCISilonConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EmcMirrorViewOracleFullDB:
                    ucnEmcMirrorViewConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EmcMirrorViewSybaseFullDB:
                    ucnEmcMirrorViewConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EmcMirrorViewApp:
                    ucnEmcMirrorViewConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.RoboCopy:
                    ucnRoboCopyRepliConfig.Visible = isEnable;
                    break;

                case ReplicationType.SybaseWithRSHADR:
                    ucnSybaseWithRSHADR.Visible = isEnable;
                    break;

                case ReplicationType.EmcUnityApp:
                    ucnEmcUnityConfiguration.Visible = isEnable;
                    break;


                case ReplicationType.DB2FullDBSVC:
                    ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = isEnable;
                    break;


                case ReplicationType.PostgressFullDBSVC:
                    ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HP3ParwithMongoFullDB:
                    ucHP3PARStorageMSSqlConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSRDFDB2FullDB:
                    ucnEMCSRDFConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.DB2FullDBEMCRecoveryPoint:
                    ucnRecoveryPointConfig.Visible = isEnable;
                    break;

                case ReplicationType.EMCSTARDB2FullDB:
                    ucnEMCSRDFStarConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.EMCSTARMYSQLFullDB:
                    ucnEMCSRDFStarConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HitachiUrDB2FullDB:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.HitachiUrMySqlFullDB:
                    ucnHitachiConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.GoldenGateRepli:
                    ucnGoldenGateRepliConfiguration.Visible = isEnable;
                    break;

                case ReplicationType.RSync:
                    ucnRSyncConfiguration.Visible = isEnable;
                    break;
            }
            Utility.SelectMenu(Master, "Module2");
        }

        private bool CheckReplicationNameExist()
        {
            if (txtReplName.Text.Equals(CurrentReplicationName))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(txtReplName.Text);
        }

        private void UnvisibleUserControl()
        {
            ucnGlobalMirrorConfiguration.Visible = false;
            ucnSnapMirrorConfiguration.Visible = false;
            ucnVmwareFastCopyConfiguration.Visible = false;
            ucnOracleDataguardConfiguration.Visible = false;
            ucnFastCopyConfiguration.Visible = false;
            ucnEMCSRDFConfiguration.Visible = false;
            ucnMSSCRConfiguration.Visible = false;
            ucnHitachiConfiguration.Visible = false;
            ucnExchangeDAGConfiguration.Visible = false;
            ucnSQLNative2008ReplicationConfiguration.Visible = false;
            ucnEC2DataSyncReplication.Visible = false;
            ucnMSSqlDoubleTakeConfig.Visible = false;
            ucnMimixConfig.Visible = false;
            ucnHypervConfig.Visible = false;
            ucnMySqlConfig.Visible = false;
            ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = false;
            ucnMssqlDbMirrorConfiguration.Visible = false;
            ucnSybaseWithSRS.Visible = false;
            uceBDRReplicationConfig.Visible = false;
            ucnFastCopyConfiguration.FindControl("chkWildCard").Visible = false;
            ucnIBMXIVMirrorconfiguration.Visible = false;
            ucnTPRCConfigurtion.Visible = false;
            ucnMSSQLAlwaysOnReplicationConfiguration.Visible = false;
            ucnRecoveryPointConfig.Visible = false;
            ucHP3PARStorageMSSqlConfiguration.Visible = false;
            ucnEMCSRDFSGConfiguration.Visible = false;
            ucnEMCSRDFStarConfiguration.Visible = false;
            ucnZFSApplicationConfiguration.Visible = false;
            ucnEMCISilonConfiguration.Visible = false;
            ucnEmcMirrorViewConfiguration.Visible = false;
            ucnRoboCopyRepliConfig.Visible = false;
            ucnSybaseWithRSHADR.Visible = false;
            ucnEmcUnityConfiguration.Visible = false;
            ucnGoldenGateRepliConfiguration.Visible = false;
            ucnRSyncConfiguration.Visible = false;
        }

        protected void DdlRepTypeSelectedIndexChanged(object sender, EventArgs e)
        {
            //ulMessage.Visible = false;
            lblPrName.Text = string.Empty;
            UnvisibleUserControl();
            if (ddlRepType.SelectedValue != "0")
            {
                _userControl.Controls.Clear();

                switch ((ReplicationType)Convert.ToInt32(ddlRepType.SelectedValue))
                {
                    case ReplicationType.DB2IBMGLobalmirror:
                        ucnGlobalMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.GlobalmirrorConfiguration);
                        break;

                    case ReplicationType.IBMGlobalMirror:
                    case ReplicationType.OracleFullDBIBMGlobalMirror:
                        ucnGlobalMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.GlobalmirrorConfiguration);
                        break;

                    case ReplicationType.OracleDataGuard:
                        ucnOracleDataguardConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.OracleDataguardConfiguration);
                        break;

                    case ReplicationType.DataSync:
                        ucnFastCopyConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.FastcopyConfiguration);
                        break;

                    case ReplicationType.MSSCR:
                        ucnMSSCRConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.MSSCRConfiguration);
                        break;

                    case ReplicationType.NetAppSnapMirror:
                        ucnSnapMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SnapmirrorConfiguration);
                        break;

                    case ReplicationType.VMWareDataSync:
                        ucnVmwareFastCopyConfiguration.Visible = true;

                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.VmwareFastcopyConfiguration);
                        break;

                    case ReplicationType.EMCSRDF:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;

                    case ReplicationType.EMCSRDFOracleFullDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;
                    case ReplicationType.EMCSRDFMSSQLFullDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;
                    case ReplicationType.EMCSRDFOracleLogShipping:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;

                    case ReplicationType.OracleWithDataSync:
                        ucnFastCopyConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.FastcopyConfiguration);
                        break;

                    case ReplicationType.HITACHIUROracleFullDB:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HITACHIConfiguration);
                        break;

                    case ReplicationType.HITACHIUROracleLogShipping:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HITACHIConfiguration);
                        break;

                    case ReplicationType.VMWareHitachiUR:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HITACHIConfiguration);
                        break;

                    case ReplicationType.MSExchangeDAG:
                        ucnExchangeDAGConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.MSSCRConfiguration);
                        break;

                    case ReplicationType.DB2DataSync:
                        ucnFastCopyConfiguration.Visible = true;
                        ucnFastCopyConfiguration.FindControl("chkWildCard").Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.FastcopyConfiguration);
                        break;
                    case ReplicationType.SQLNative2008:
                        ucnSQLNative2008ReplicationConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.MSSCRConfiguration);
                        break;
                    case ReplicationType.EC2S3DataSync:
                        ucnEC2DataSyncReplication.Visible = true;
                        break;
                    case ReplicationType.VMWareSnapMirror:
                        ucnSnapMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SnapmirrorConfiguration);
                        break;

                    case ReplicationType.MSSqlNetAppSnapMirror:
                        ucnSnapMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SnapmirrorConfiguration);
                        break;

                    case ReplicationType.OracleFullDBNetAppSnapMirror:
                        ucnSnapMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SnapmirrorConfiguration);
                        break;
                    case ReplicationType.MSSQLDoubleTakeFullDB:
                        ucnMSSqlDoubleTakeConfig.Visible = true;
                        break;


                    case ReplicationType.EMCSRDFVMAXOracleFULLDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;

                    case ReplicationType.EMCSRDFDMXOracleFULLDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;

                    case ReplicationType.EMCSRDFVMAXMSSQLFULLDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;


                    case ReplicationType.EMCSRDFDMXMSSQLFULLDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;

                    case ReplicationType.SyBaseWithDataSync:
                        ucnFastCopyConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.FastcopyConfiguration);
                        break;

                    case ReplicationType.ApplicationDoubleTake:
                        ucnMSSqlDoubleTakeConfig.Visible = true;
                        break;

                    case ReplicationType.MIMIX:
                        ucnMimixConfig.Visible = true;
                        break;

                    case ReplicationType.HitachiOracleFullDBRac:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HITACHIConfiguration);
                        break;

                    case ReplicationType.EMCSRDFOracleRacFullDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;

                    case ReplicationType.EMCSRDFSyBase:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;

                    case ReplicationType.HitachiSyabse:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HITACHIConfiguration);
                        break;

                    case ReplicationType.HyperV:
                        ucnHypervConfig.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HyperVConfiguration);
                        break;

                    case ReplicationType.AppHitachiUr:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HITACHIConfiguration);
                        break;

                    case ReplicationType.MySqlNativeLogShipping:
                        ucnMySqlConfig.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.MySqlRepliConfiguration);
                        break;

                    case ReplicationType.SVCGlobalMirrorOracleFullDBRac:
                    case ReplicationType.OracleFullDBSVCGlobalMirror:
                        ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SVCGlobalMirrorConfiguration);
                        break;

                    case ReplicationType.SVCGlobalMirrorORMetroMirror:
                        ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SVCGlobalMirrorConfiguration);
                        break;

                    case ReplicationType.MSSQLDBMirroring:
                        ucnMssqlDbMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.MssqlDbMirrors);
                        break;

                    case ReplicationType.SVCMSSQLFullDB:
                        ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SVCGlobalMirrorConfiguration);
                        break;

                    case ReplicationType.VMWareWithSVC:
                        ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SVCGlobalMirrorConfiguration);
                        break;

                    case ReplicationType.HitachiURMSSQLFullDB:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SVCGlobalMirrorConfiguration);
                        break;

                    case ReplicationType.GlobalMirrorMSSQLFullDB:
                        ucnGlobalMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.GlobalmirrorConfiguration);
                        break;

                    case ReplicationType.DB2IBMXIVMIRROR:
                        ucnIBMXIVMirrorconfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.IBMxivMirrorConfigurations);
                        break;

                    case ReplicationType.NetAppSnapMirrorPostgresFullDB:
                        ucnSnapMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SnapmirrorConfiguration);
                        break;

                    case ReplicationType.ApplicationXIVMIRROR:
                        ucnIBMXIVMirrorconfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.IBMxivMirrorConfigurations);
                        break;

                    case ReplicationType.EMCSRDFMysqlFullDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;


                    case ReplicationType.MaxDBWithDataSync:
                        ucnFastCopyConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.FastcopyConfiguration);
                        break;


                    case ReplicationType.SybaseWithSRS:
                        ucnSybaseWithSRS.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SybaseWithSRS);
                        break;

                    case ReplicationType.TPCR:
                        ucnTPRCConfigurtion.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.TPRCConfigurationt);
                        break;


                    case ReplicationType.DRNET:
                        ucBase24Config.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.Base24Configuration);
                        break;


                    case ReplicationType.MSSQLAlwaysOn:
                        ucnMSSQLAlwaysOnReplicationConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.MSSQLAlwaysOn);
                        break;

                    case ReplicationType.RecoverPoint:
                        ucnRecoveryPointConfig.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RecoverPoint);
                        break;

                    case ReplicationType.RecoverPointOracleFULLDB:
                        ucnRecoveryPointConfig.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RecoverPoint);
                        break;

                    case ReplicationType.RecoverPointMSSQLFULLDB:
                        ucnRecoveryPointConfig.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RecoverPoint);
                        break;

                    case ReplicationType.RecoverPointMYSQLFULLDB:
                        ucnRecoveryPointConfig.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RecoverPoint);
                        break;

                    case ReplicationType.HP3PARMSSQLFULLDB:
                        ucHP3PARStorageMSSqlConfiguration.Visible = true;

                        var txtheadertxt = ucHP3PARStorageMSSqlConfiguration.FindControl("lbltextheader") as Label;
                        txtheadertxt.Text = "Storage - MSSQL Full DB - HP3PAR";
                        break;

                    case ReplicationType.HP3PARORACLEFULLDB:
                        ucHP3PARStorageMSSqlConfiguration.Visible = true;

                        var txtheadertxtHp = ucHP3PARStorageMSSqlConfiguration.FindControl("lbltextheader") as Label;
                        txtheadertxtHp.Text = "Storage - ORACLE Full DB - HP3PAR";
                        break;

                    case ReplicationType.HP3ParwithPostgressMSSQL:
                        ucHP3PARStorageMSSqlConfiguration.Visible = true;
                        var headertxt = ucHP3PARStorageMSSqlConfiguration.FindControl("lbltextheader") as Label;
                        headertxt.Text = "HP3Par with Postgress Full DB";
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HP3PARStorage);
                        break;

                    case ReplicationType.HP3ParwithMongoFullDB:
                        ucHP3PARStorageMSSqlConfiguration.Visible = true;
                        var HP3ParwithMongoFullDBSearch = ucHP3PARStorageMSSqlConfiguration.FindControl("lbltextheader") as Label;
                        HP3ParwithMongoFullDBSearch.Text = "HP3Par with Mongo Full DB";
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HP3PARStorage);
                        break;

                    case ReplicationType.HP3ParwithESXI:
                        ucnHP3PARWithESXIConfiguration.Visible = true;
                        var txtheadertext1 = ucnHP3PARWithESXIConfiguration.FindControl("lbltextheader") as Label;
                        txtheadertext1.Text = "HP3par With ESXI Details";
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HP3PARESXI);
                        break;
                    case ReplicationType.HP3ParwithApplication:
                        ucHP3PARStorageMSSqlConfiguration.Visible = true;
                        var txtheader = ucHP3PARStorageMSSqlConfiguration.FindControl("lbltextheader") as Label;
                        txtheader.Text = "HP3par With Application";
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HP3PARStorage);
                        break;

                    case ReplicationType.MSSQLFullDBVVRReplication:
                        ucnVVRMssqlFullDBConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.VVRReplication);
                        break;
                    case ReplicationType.EMCSRDFSGORACLEFULLDB:
                        ucnEMCSRDFSGConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSG);
                        break;

                    case ReplicationType.EMCSRDFSGAPPLICATION:
                        ucnEMCSRDFSGConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSG);
                        break;

                    case ReplicationType.EMCSRDFSTARORACLEFULLDB:
                        ucnEMCSRDFStarConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSTAR);
                        break;

                    case ReplicationType.EMCSRDFSTARAPPLICATION:
                        ucnEMCSRDFStarConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSTAR);
                        break;

                    case ReplicationType.EMCSRDFMSQLFULLDBSG:
                        ucnEMCSRDFSGConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSG);
                        break;

                    case ReplicationType.EMCSRDFMSQLFULLDBSTAR:
                        ucnEMCSRDFStarConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSTAR);
                        break;
                    default:
                        UnvisibleUserControl();
                        break;

                    case ReplicationType.ApplicationeBDR:
                        uceBDRReplicationConfig.Visible = true;
                        break;

                    case ReplicationType.ZFSOracleFULLDB:
                    case ReplicationType.ZFSWithDB2:
                        ucnZFSApplicationConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.ZFSOracleFullDB);
                        break;

                    case ReplicationType.ZFSApplication:
                        ucnZFSApplicationConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.ZFSOracleFullDB);
                        break;

                    case ReplicationType.ZFSMaxFULLDB:
                        ucnZFSApplicationConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.ZFSOracleFullDB);
                        break;

                    case ReplicationType.EMCISilon:
                        ucnEMCISilonConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcIsilon);
                        break;

                    case ReplicationType.EmcMirrorViewOracleFullDB:
                        ucnEmcMirrorViewConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcMirrorView);
                        break;

                    case ReplicationType.EmcMirrorViewSybaseFullDB:
                        ucnEmcMirrorViewConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcMirrorView);
                        break;

                    case ReplicationType.EmcMirrorViewApp:
                        ucnEmcMirrorViewConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcMirrorView);
                        break;

                    case ReplicationType.RoboCopy:
                        ucnRoboCopyRepliConfig.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RoboCopyReplicationConfig);
                        break;

                    case ReplicationType.SybaseWithRSHADR:
                        ucnSybaseWithRSHADR.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SybaseWithRSHADR);
                        break;

                    case ReplicationType.EmcUnityApp:
                        ucnEmcUnityConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcUnity);
                        break;

                    case ReplicationType.DB2FullDBSVC:
                        ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SVCGlobalMirrorConfiguration);
                        break;

                    case ReplicationType.PostgressFullDBSVC:
                        ucnSVCGlobalMirrorORMetroMirrorConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.SVCGlobalMirrorConfiguration);
                        break;

                    case ReplicationType.EMCSRDFDB2FullDB:
                        ucnEMCSRDFConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsdfConfiguration);
                        break;

                    case ReplicationType.DB2FullDBEMCRecoveryPoint:
                        ucnRecoveryPointConfig.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RecoverPoint);
                        break;

                    case ReplicationType.EMCSTARDB2FullDB:
                        ucnEMCSRDFStarConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSTAR);
                        break;

                    case ReplicationType.EMCSTARMYSQLFullDB:
                        ucnEMCSRDFStarConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSTAR);
                        break;

                    case ReplicationType.HitachiUrDB2FullDB:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HITACHIConfiguration);
                        break;

                    case ReplicationType.HitachiUrMySqlFullDB:
                        ucnHitachiConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HITACHIConfiguration);
                        break;

                    case ReplicationType.VirtualeBDR:
                        uceBDRReplicationConfig.Visible = true;
                        break;

                    case ReplicationType.GoldenGateRepli:
                        ucnGoldenGateRepliConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.GoldenGate);
                        break;

                    case ReplicationType.RSync:
                        ucnRSyncConfiguration.Visible = true;
                        _userControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RSync);
                        break;
                }
            }
            else
            {
                UnvisibleUserControl();
                txtReplName.Text = "";
                ddlSite.ClearSelection();
            }
        }

        private void LoadData()
        {
            if (CurrentReplicationId > 0)
            {
                //ddlSite.ClearItems();
                //ddlSite.ClearSelection();
                txtReplName.Text = CurrentReplicationName;
                ddlRepType.SelectedValue = CurrentReplicationType.ToString();
                ddlSite.SelectedValue = CurrentSiteId.ToString();
                DdlRepTypeSelectedIndexChanged(null, null);
            }
        }
    }
}