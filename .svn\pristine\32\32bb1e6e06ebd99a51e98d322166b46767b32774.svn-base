﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
   public interface INodeAuthenticateDataAccess
    {
       SubstituteNodeAuthentication AddNodeSubstituteAuthn(SubstituteNodeAuthentication objSubsAuth);
       SubstituteNodeAuthentication UpdateNodeAuthn(SubstituteNodeAuthentication objsubsauth);
       IList<SubstituteNodeAuthentication> GetNodeAuthById(int Id);
       IList<SubstituteNodeAuthentication> GetAllNodeAuthn();
        //bool DeleteDatabaseAuthn(int id);
        SubstituteNodeAuthentication GetAllByNodeSubstituteAuthn(int id);

        SubstituteNodeAuthentication deleteSubstituteAuthnbynodeid(int id);
    }
}
