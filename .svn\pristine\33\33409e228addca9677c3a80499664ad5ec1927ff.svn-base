using System;
using System.Runtime.Serialization;
using CP.Common.Base;
using CP.Common.Shared;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "ParallelGroupWorkflow", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class ParallelGroupWorkflow : BaseEntity
    {
        #region Properties

        [DataMember]
        public int InfraObjectId { get; set; }

        [DataMember]
        public string InfraObjectName { get; set; }

        [DataMember]
        public string GroupState { get; set; }

        [DataMember]
        public int WorkflowId { get; set; }

        [DataMember]
        public string WorkflowName { get; set; }

        [DataMember]
        public int CurrentActionId { get; set; }

        [DataMember]
        public string CurrentActionName { get; set; }

        [DataMember]
        public WorkflowActionStatus Status { get; set; }

        [DataMember]
        public string Message { get; set; }

        [DataMember]
        public int ParallelDROperationId { get; set; }

        [DataMember]
        public int ConditionalOperation { get; set; }

        [DataMember]
        public string ProgressStatus { get; set; }

        [DataMember]
        public string NameofTable { get; set; }

        [DataMember]
        public int IsResume { get; set; }

        [DataMember]
        public int IsReExecute { get; set; }


        [DataMember]
        public int IsPause { get; set; }

        [DataMember]
        public string JobName { get; set; }
        [DataMember]
        public int ActionsSuccess { get; set; }

        [DataMember]
        public int ActionsError { get; set; }

        [DataMember]
        public int ActionsSkip { get; set; }

        [DataMember]
        public int ActionsRunning { get; set; }

        [DataMember]
        public int ActionsAbort { get; set; }

        [DataMember]
        public int BusinessServiceId { get; set; }

        [DataMember]
        public DateTime EndTime { get; set; }
        #endregion Properties
    }

    public class workflowactionenviorment
    {
        public int Id { get; set; }
        public string infraobjectid { get; set; }
        public int workflowid { get; set; }
        public int BusinessServiceId { get; set; }
        public int ActionsSuccess { get; set; }
        public int ActionsError { get; set; }
        public int ActionsSkip { get; set; }
        public int ActionsRunning { get; set; }
        public int ActionsAbort { get; set; }
        public DateTime EndTime { get; set; }
        public string Actiontype { get; set; }
        public string EnviornmentType { get; set; }

    }
}