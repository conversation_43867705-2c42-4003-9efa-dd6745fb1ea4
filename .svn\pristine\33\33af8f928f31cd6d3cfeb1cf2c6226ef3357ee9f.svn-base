﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class DatabaseSubstituteAuthenticateBuilder : IEntityBuilder<SubstituteAuthentication>
    {
        IList<SubstituteAuthentication> IEntityBuilder<SubstituteAuthentication>.BuildEntities(IDataReader reader)
        {
            var subsAuthn = new List<SubstituteAuthentication>();

            while (reader.Read())
            {
                subsAuthn.Add(((IEntityBuilder<SubstituteAuthentication>)this).BuildEntity(reader,
                    new SubstituteAuthentication()));
            }

            return (subsAuthn.Count > 0) ? subsAuthn : null;
        }

        SubstituteAuthentication IEntityBuilder<SubstituteAuthentication>.BuildEntity(IDataReader reader,
          SubstituteAuthentication subsAuthn)
        {
            subsAuthn.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);


            subsAuthn.Sqlplus = Convert.IsDBNull(reader["DBConnectionCmd"])
                ? string.Empty
                : Convert.ToString(reader["DBConnectionCmd"]);

            //subsAuthn.Credential = Convert.IsDBNull(reader["Credential"])
            //    ? string.Empty
            //    : Convert.ToString(reader["Credential"]);

            subsAuthn.Credential = Convert.IsDBNull(reader["Credential"]) ? 0 : Convert.ToInt32(reader["Credential"]);

            subsAuthn.Role = Convert.IsDBNull(reader["Role"])
                ? string.Empty
                : Convert.ToString(reader["Role"]);

            subsAuthn.BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"]) ? 0 : Convert.ToInt32(reader["BaseDatabaseId"]);

            subsAuthn.IsSubstituteAuth = Convert.IsDBNull(reader["IsSubstituteAuth"]) ? 0 : Convert.ToInt32(reader["IsSubstituteAuth"]);
            

            return subsAuthn;
        }
    }
}
