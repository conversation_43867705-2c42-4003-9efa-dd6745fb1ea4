﻿

function pageLoad() {
    var shold = localStorage.getItem("pos");
    //var ind = $(".timeline li .fa.fa-spinner.fa-spin,.timeline li .glyphicons.remove, .timeline li .glyphicons.single.ok:last").attr("data-count");
    //var liht = ind * 50;
    //$("#scrolling_div").scrollTop(liht);

    $('dt').click(function () {
        theId = $(this).attr('id').toString();
        //localStorage.dtclickId = localStorage.dtclickId + "^" + theId;
        var IDdd = theId + "" + "EX";
        if ($("#" + IDdd).css('display') == 'none') {
            localStorage.dtclickId = localStorage.dtclickId + "^" + theId + "~" + "expand";
        }
        else {
            localStorage.dtclickId = localStorage.dtclickId + "^" + theId + "~" + "collapse";
        }
        localStorage.ExpandedValue = "";

    });



    $.timeliner({
        startOpen: ['#19550828EX', '#19630828EX']
    });
    $.timeliner({
        timelineContainer: '#timelineContainer_2'
    });

    $(".CBmodal").colorbox({ inline: true, initialWidth: 100, maxWidth: 682, initialHeight: 100, transition: "elastic", speed: 750 });

    //var scrollTo = $(".timeline li .fa.fa-spinner.fa-spin:last").attr("id");
    //if (scrollTo === undefined) {

    //}
    //else {
    //    setTimeout(callscroll(scrollTo), 1000);
    //}

     
    $("#scrolling_div").on("scroll", function () { var sspos = $("#scrolling_div")[0].scrollTop; localStorage.setItem("pos", sspos); })
    localStorage.setItem("pos", shold); 
    setTimeout(
    $("#scrolling_div")[0].scrollTop = shold, 500);
}

function callscroll(scrollTo) {
    var container = $('#scrolling_div');
    var scrollTo = $('#' + scrollTo);


    //var scroll2 = $('#' + scrollTo);
    // Calculating new position of scrollbar
    var position = scrollTo.offset().top
                - container.offset().top
                + container.scrollTop() - 50;

    container.scrollTop(position);

}