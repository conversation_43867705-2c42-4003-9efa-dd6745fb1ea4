﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class MongoDataBaseBuilder : IEntityBuilder<MongoDB>
    {
        IList<MongoDB> IEntityBuilder<MongoDB>.BuildEntities(IDataReader reader)
        {
            var mangodb = new List<MongoDB>();

            while (reader.Read())
            {
                mangodb.Add(((IEntityBuilder<MongoDB>)this).BuildEntity(reader, new MongoDB()));
            }

            return (mangodb.Count > 0) ? mangodb : null;
        }

        MongoDB IEntityBuilder<MongoDB>.BuildEntity(IDataReader reader, MongoDB mongodb)
        {

            mongodb.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            mongodb.BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"])
                ? 0
                : Convert.ToInt32(reader["BaseDatabaseId"]);
            mongodb.InstallationPath = Convert.IsDBNull(reader["InstallationPath"])
                ? string.Empty
                : Convert.ToString(reader["InstallationPath"]);
            mongodb.InstanceName = Convert.IsDBNull(reader["InstanceName"])
          ? string.Empty
          : Convert.ToString(reader["InstanceName"]);
           
            mongodb.UserName = Convert.IsDBNull(reader["UserName"])
                ? string.Empty
                : Convert.ToString(reader["UserName"]);
            mongodb.Password = Convert.IsDBNull(reader["Password"])
                ? string.Empty
                : Convert.ToString(reader["Password"]);
            mongodb.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
            mongodb.BinaryLocation = Convert.IsDBNull(reader["BinaryLocation"])
           ? string.Empty
           : Convert.ToString(reader["BinaryLocation"]);


            return mongodb;
        }

    }
}
