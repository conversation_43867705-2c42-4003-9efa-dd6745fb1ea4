﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;


namespace CP.UI.Controls
{
    public partial class GoldenGateRepliConfiguration : ReplicationControl //: System.Web.UI.UserControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        #region Variable

        TextBox _txtReplicationName = new TextBox();
        DropDownList _ddlReplicationType = new DropDownList();
        DropDownList _ddlSiteId = new DropDownList();
        private GoldenGate _goldengate;

        #endregion

        #region Properties

        public GoldenGate CurrentEntity
        {
            get { return _goldengate ?? (_goldengate = new GoldenGate()); }
            set
            {
                _goldengate = value;
            }
        }


        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "Golden Gate"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion

        public override void PrepareView()
        {          
            PrepareEditView();
        }


        public void PrepareEditView()
        {

            if (CurrentGoldenGateReplication != null)
            {
                CurrentEntity = CurrentGoldenGateReplication;

                Session["GoldenGate"] = CurrentEntity;

                txtGGPath.Text = CurrentEntity.GGPath;
                txtExtractName.Text = CurrentEntity.ExtractName;
             
                btnSave.Text = "Update";
            }
        }

        public void BuildEntities()
        {
            if (Session["GoldenGate"] != null)
            {
                CurrentEntity = (GoldenGate)Session["GoldenGate"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.GGPath = txtGGPath.Text;
            CurrentEntity.ExtractName = txtExtractName.Text;
                 
        }

        public void SaveEditor()
        {
            // if (CurrentEntity.IsNew)
            if (btnSave.Text == "Save")
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;

                CurrentEntity = Facade.AddGoldenGate(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Golden Gate", UserActionType.CreateReplicationComponent, "The Golden Gate Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);

            }
            else
            {
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateGoldenGate(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Golden Gate", UserActionType.UpdateReplicationComponent, "The Golden Gate Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }
            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            if (Page.IsValid)
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }

                try
                {
                    if (currentTransactionType != TransactionType.Undefined)
                    {
                        StartTransaction();
                        BuildEntities();
                        SaveEditor();
                        ReplicationName.Text = string.Empty;
                        EndTransaction();

                        string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                currentTransactionType));
                        btnSave.Enabled = false;
                    }
                }
                catch (CpException ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                    {
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);
                    }
                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, Page);
                    }
                }
                if (returnUrl.IsNotNullOrEmpty())
                {
                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                    //Helper.Url.Redirect(secureUrl);

                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                    Helper.Url.Redirect(secureUrl);
                }
            }
 
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

    }
}