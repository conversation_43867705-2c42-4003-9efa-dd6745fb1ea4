﻿using System;
using System.Collections.Generic;
using System.DirectoryServices;
using System.Net;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.CacheController;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;

namespace CP.UI.Admin
{
    public partial class DNSLookup : DNSLookupBasePageEditor
    {
        private static CacheManager _cacheManager;
        private static string cacheKey = "Component.ServerListIntoCache";
        private DnsServerDetails _buildserver;

        private static CacheManager CurrentCacheManager
        {
            get { return _cacheManager ?? (_cacheManager = new CacheManager()); }
        }

        public override void PrepareView()
        {
            ddlSite.Attributes.Add("onblur", "ValidatorValidate(" + rfvSite.ClientID + ")");
            txtDomainName.Attributes.Add("onblur", "ValidatorValidate(" + rfvDomainName.ClientID + ")");
            if (IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Error.Error403);
            }
            Utility.SelectMenu(Master, "Module2");
            CurrentCacheManager.DataCache.RemoveItem(cacheKey);
            Utility.PopulateServerByTypeAndRole(ddlSite, "DNSServer", LoggedInUserCompanyId, IsSuperAdmin, LoggedInUserCompany.IsParent, true);
            lblNoRecords.Visible = false;
        }

        public IList<DnsServerDetails> AddQuery(int sHostname, string sLookUp, string sDomainText, Label oLabel, HtmlControl oDiv, Button oSave)
        {
            IList<DnsServerDetails> list = new List<DnsServerDetails>();

            var server = Facade.GetServerById(sHostname);
            var deServre = new DirectoryEntry("LDAP://" + CryptographyHelper.Md5Decrypt(server.IPAddress));
            var search = new DirectorySearcher(deServre);
            string query = "(&(objectCategory=Computer))";
            search.Filter = query;
            search.PropertiesToLoad.Add("Name");
            search.PropertiesToLoad.Add("Data");
            SearchResultCollection mySearchResultColl = search.FindAll();
            int iCount = 0;
            foreach (SearchResult sr in mySearchResultColl)
            {
                var results = new DnsServerDetails();
                DirectoryEntry de = sr.GetDirectoryEntry();
                if (sDomainText != "")
                {
                    if (de.Properties["Name"].Value.ToString() == sDomainText)
                    {
                        results.Id = iCount;
                        results.Host = server.Name;
                        results.Name = de.Properties["Name"].Value.ToString();
                        string str1 = de.Properties["Name"].Value.ToString();
                        IPHostEntry ipEntry = Dns.GetHostEntry(str1);
                        IPAddress[] addr = ipEntry.AddressList;
                        foreach (IPAddress t in addr)
                        {
                            results.IPAddress = t.ToString();
                        }
                        results.Zone = ddlLookUp.SelectedItem.Text;
                        results.Domain = txtDomainName.Text;
                        results.UpdateDate = DateTime.UtcNow;
                        results.DNSRefFwd = ddlLookUp.SelectedItem.Text;
                        results.HostId = server.Id;
                        list.Add(results);
                    }
                }
                else
                {
                    results.Id = iCount;
                    results.Host = server.Name;
                    results.Name = de.Properties["Name"].Value.ToString();
                    string str1 = de.Properties["Name"].Value.ToString();
                    IPHostEntry ipEntry = Dns.GetHostEntry(str1);
                    IPAddress[] addr = ipEntry.AddressList;
                    foreach (IPAddress t in addr)
                    {
                        results.IPAddress = t.ToString();
                    }
                    results.Zone = ddlLookUp.SelectedItem.Text;
                    results.Domain = txtDomainName.Text;
                    results.UpdateDate = DateTime.UtcNow;
                    results.DNSRefFwd = ddlLookUp.SelectedItem.Text;
                    results.HostId = server.Id;
                    list.Add(results);
                }
            }
            if (list.Count > 0)
            {
                divListComponants.Visible = true;
                btnSaveDetails.Visible = true;
            }
            else
            {
                btnSaveDetails.Visible = false;
                divListComponants.Visible = false;
                lblNoRecords.Visible = true;
            }
            return list;
        }

        public int GetResult(int a, int b)
        {
            string sA = a.ToString();
            string sB = b.ToString();
            string sResult = "";
            int iALength = sA.Length;
            int iBLength = sB.Length;
            int ilenght = iALength > iBLength ? iALength : iBLength;
            for (int i = 0; i <= ilenght; i++)
            {
                if (i < iALength)
                    sResult += sA[i];
                if (i < iBLength)
                    sResult += sB[i];
            }
            if (sResult != "")
            {
                int iResult = Convert.ToInt32(sResult);
                if (iResult > 10000000)
                {
                    return -1;
                }
                return iResult;
            }
            return -1;
        }

        protected void BtnDiscoverClick(object sender, EventArgs e)
        {
            try
            {
                int iSelectedHostId = Convert.ToInt32(ddlSite.SelectedItem.Value);
                string sSelectdLookUp = ddlLookUp.SelectedItem.Text;
                string sDomainText = txtDomainName.Text;
                Label oLabel = lblNoRecords;
                HtmlControl divList = divListComponants;
                Button oButton = btnSaveDetails;
                IList<DnsServerDetails> dnslist = AddQuery(iSelectedHostId, sSelectdLookUp, sDomainText, oLabel, divList, oButton);
                lvComponent.DataSource = dnslist;
                lvComponent.DataBind();
            }
            catch (Exception ex)
            {
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                Helper.Url.Redirect(new SecureUrl(Request.RawUrl));
            }
        }

        protected void BtnSaveClick(object sender, EventArgs e)
        {
            var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
            if (string.IsNullOrEmpty(returnUrl))
            {
                returnUrl = ReturnUrl;
            }
            int iSelectedHostId = Convert.ToInt32(ddlSite.SelectedItem.Value);
            string sSelectdLookUp = ddlLookUp.SelectedItem.Text;
            string sDomainText = txtDomainName.Text;
            Label oLabel = lblNoRecords;
            HtmlControl divList = divListComponants;
            Button oButton = btnSaveDetails;
            IList<DnsServerDetails> dnserver = AddQuery(iSelectedHostId, sSelectdLookUp, sDomainText, oLabel, divList, oButton);
            foreach (DnsServerDetails dnsbuild in dnserver)
            {
                _buildserver = dnsbuild;
                try
                {
                    BuildEntities();
                    StartTransaction();
                    SaveEditor();
                    EndTransaction();
                }
                catch (Exception ex)
                {
                    string exception = ex.Message;
                }
            }
        }

        protected void LvComponentPreRender(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                int iSelectedHostId = Convert.ToInt32(ddlSite.SelectedItem.Value);
                string sSelectdLookUp = ddlLookUp.SelectedItem.Text;
                string sDomainText = txtDomainName.Text;
                Label oLabel = lblNoRecords;
                HtmlControl divList = divListComponants;
                Button oButton = btnSaveDetails;
                IList<DnsServerDetails> dnsserver = AddQuery(iSelectedHostId, sSelectdLookUp, sDomainText, oLabel, divList, oButton);

                if (dnsserver != null)
                {
                    btnSaveDetails.Visible = true;
                    lvComponent.DataSource = dnsserver;
                    lvComponent.DataBind();
                }
                lblNoRecords.Visible = false;
            }
        }

        public override Label MessageViewer
        {
            get { return lblMessage; }
        }

        public override string MessageInitials
        {
            get { return "BusinessProcess"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Admin.BusinessProcess;
                }
                return string.Empty;
            }
        }

        public override void PrepareEditView()
        {
            throw new NotImplementedException();
        }

        public override void SaveEditor()
        {
            var dnsserver = Facade.GetDnsServerDetailsById(CurrentEntity.HostId, CurrentEntity.Name);

            if (dnsserver == null)
            {
                CurrentEntity.CreatorId = LoggedInUserId;
                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddDnsHostDetails(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUser.LoginName, "DNSLookUp", UserActionType.CreateDNSLookUp, "The CreateDNSLookUp '" + CurrentEntity.Id + "' was Added to the Create DNSLookUp", LoggedInUserId);
            }
            else
            {
                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity.Id = dnsserver.Id;
                CurrentEntity = Facade.UpdateDnsHostDetails(CurrentEntity);
                ActivityLogger.AddLog(LoggedInUser.LoginName, "DNSLookUp", UserActionType.UpdateDNSLookUp, "The CreateDNSLookUp '" + CurrentEntity.Id + "' was Updated to the Create DNSLookUp", LoggedInUserId);
            }
        }

        public override void BuildEntities()
        {
            CurrentEntity.Name = _buildserver.Name;
            CurrentEntity.Zone = _buildserver.Zone;
            CurrentEntity.Domain = _buildserver.Domain;
            CurrentEntity.Host = _buildserver.Host;
            CurrentEntity.IPAddress = _buildserver.IPAddress;
            CurrentEntity.UpdateDate = DateTime.UtcNow;
            CurrentEntity.DNSRefFwd = _buildserver.DNSRefFwd;
            CurrentEntity.HostId = _buildserver.HostId;
        }
    }
}