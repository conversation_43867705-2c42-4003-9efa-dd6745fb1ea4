﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Web.Security;
using System.Web;
using System.Web.UI.HtmlControls;
using log4net;

namespace CP.UI
{
    public partial class DatabaseConfigurationBase : DatabaseBasePageEditor
    {
        private static IFacade _facade = new Facade();
        private UserControl _userControl = new UserControl();
        private readonly IList<ListItem> _SelectedItems = new List<ListItem>();
        private readonly IList<ListItem> _previousSelectedItems = new List<ListItem>();
        private ListView list = new ListView();
        private readonly ILog _logger = LogManager.GetLogger(typeof(DatabaseConfigurationBase));

        public override string MessageInitials
        {
            get
            {
                switch (ddlDBType.SelectedValue)
                {
                    case "Oracle":
                        return "Oracle Database Information ";

                    case "Sql":
                        return "SQL Database Information ";

                    case "Exchange":
                        return "Exchange Database Information ";

                    case "DB2":
                        return "DB2 Database Information";

                    case "ExchageDAG":
                        return "Exchange DAG Database Information";

                    case "MySQL":
                        return "MySQL Database Information";

                    case "PostgreSQL":
                        return "PostgreSQL Database Information";

                    case "Postgres9x":
                        return "Postgres9x Database Information";

                    case "SQLNative2008":
                        return "MSSQL Native-Logshipping Database Information";

                    case "SyBase":
                        return "SyBase Database Information";


                    case "MaxDB":
                        return "MaxDB Database Information";


                    case "SyBaseWithSrs":
                        return "SybaseWithSrs Information";

                    case "MongoDB":
                        return "MongoDB Database Information";

                    case "SyBaseWithRsHADR":
                        return "SyBaseWithRsHADR Database Information";

                    case "HANADB":
                        return "HANADB Database Information";

                }
                return " Database Information";
            }
        }

        public override void PrepareView()
        {
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            txtName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
            ddlDBType.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            // txtAddVersion.Attributes.Add("onblur", "ValidatorValidate(" + rfvVersion.ClientID + ")");
            ddlVersion.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");
            ddlServerId.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");

            // Utility.PopulateDBServerByRoleAndCompanyIdWithType(ddlServerId, IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, true);

            if (LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                Utility.PopulateDBServerByRoleAndCompanyIdWithType(ddlServerId, IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay, true);
            else
            {
                Utility.PopulateDBServerByRoleAndCompanyIdWithUserId(ddlServerId, LoggedInUserId, LoggedInUserCompanyId, Enum.GetName(typeof(UserRole), LoggedInUserRole), true);
            }



            Utility.SelectMenu(Master, "Module2");
            list.Items.Clear();

            var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;
            var nodeList = Facade.GetAllNodes();
            if (nodeList != null)
            {
                cblstGroup.ClearItems();
                cblstGroup.DataSource = nodeList;
                cblstGroup.DataTextField = "Name";
                cblstGroup.DataValueField = "Id";
                cblstGroup.DataBind();
            }

            var ddlAuthType = ExchangeDAGDatabaseConfig1.FindControl("ddlAuthenticationType") as DropDownList;
            var ddlProtocolType = ExchangeDAGDatabaseConfig1.FindControl("ddlProtocolType") as DropDownList;

            EnumHelper.PopulateEnumIntoList(ddlAuthType, typeof(AuthenticationType), " - Select Authentication Type - ");
            EnumHelper.PopulateEnumIntoList(ddlProtocolType, typeof(ProtocolType), " - Select Protocol Type - ");

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 

            PrepareEditView();
        }

        public override void PrepareEditView()
        {
            if (CurrentDatabaseBaseId > 0)
            {
                LoadUserControl(CurrentDatabaseType.ToString());
                //btnVersionAdd.Enabled = false;
                BindControlsValue();

                if (ddlDBType.SelectedValue == "Oracle")
                {
                    ddldbConnectivity.Visible = true;
                    lblDBConnectivity.Visible = true;
                    RequiredFieldValidator5.Visible = true;
                }
                else
                {
                    ddldbConnectivity.Visible = false;
                    lblDBConnectivity.Visible = false;
                    RequiredFieldValidator5.Visible = false;
                    CurrentEntity.DatabaseConnectivity = null;
                }

                btnSave.Text = "Update";
                //  btnAdd.Visible = false;
            }
            else if (CurrentDatabaseBaseId == 0)
            {
                ddlVersion.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectDBVersion, "0"));
            }
        }

        private void BindControlsValue()
        {
            if (CurrentDatabaseType != DatabaseType.Undefined)
            {
                txtName.Text = CurrentDatabase.Name;

                ddlDBType.SelectedValue = CurrentDatabase.DatabaseType.ToString();
                if (CurrentDatabase.DatabaseType.ToString() == DatabaseType.OracleRac.ToString())
                {
                    ddlDBType.SelectedValue = DatabaseType.Oracle.ToString();
                }
                ddlDBType.Enabled = false;


                ddldbConnectivity.SelectItemByText(CurrentDatabase.DatabaseConnectivity);

                string version = CurrentDatabase.Version;
                chkCluster.Checked = CurrentDatabase.IsPartofRac;
                ddlVersion.SelectedValue = version;
                //ddlServerId.SelectedValue = string.Format("{0}~{1}", CurrentDatabase.ServerId, CurrentDatabase.Type == "PRDatabase" ? "PRDBServer" : "DRDBServer");

                if (CurrentDatabase.Type == "PRDatabase")
                    //ddlServerId.SelectedValue = CurrentDatabase.ServerId.ToString() + '~' + CurrentDatabase.Type;
                    ddlServerId.SelectedValue = CurrentDatabase.ServerId.ToString() + '~' + "PRDBServer";
                else if (CurrentDatabase.Type == "DRDatabase")
                    ddlServerId.SelectedValue = CurrentDatabase.ServerId.ToString() + '~' + "DRDBServer";
                else if (CurrentDatabase.Type == "NearDRDBServer")
                    ddlServerId.SelectedValue = CurrentDatabase.ServerId.ToString() + '~' + "NearDRDBServer";

                switch (CurrentDatabaseType)
                {
                    case DatabaseType.Oracle:

                        PrepareOracleEditView();

                        break;

                    case DatabaseType.Sql:

                        PrepareSqlEditView();

                        break;

                    case DatabaseType.Exchange:

                        PrepareExchangeEditView();

                        break;

                    case DatabaseType.OracleRac:
                        PrepareOracleRacEditView();
                        break;

                    case DatabaseType.DB2:

                        PrepareDb2EditView();

                        break;

                    case DatabaseType.ExchangeDAG:

                        PrepareExchage2010EditView();
                        break;

                    case DatabaseType.MySQL:
                        PrepareMySQLEditView();
                        break;

                    case DatabaseType.PostgreSQL:
                        PreparePostgreSQLEditView();
                        break;

                    case DatabaseType.Postgres9x:
                        PreparePostgr9xEditView();
                        break;

                    case DatabaseType.SQLNative2008:
                        PrepareMsSqlEditView();
                        break;

                    case DatabaseType.SyBase:
                        PrepareSybaseEditView();
                        break;


                    case DatabaseType.MaxDB:
                        PrepareMaxDBEditView();
                        break;

                    case DatabaseType.SyBaseWithSrs:
                        PrepareSybaseWithSrsEditView();
                        break;

                    case DatabaseType.MongoDB:
                        PrepareMongoDBEditView();
                        break;

                    case DatabaseType.SyBaseWithRsHADR:
                        PrepareSyBaseWithRsHADREditView();
                        break;

                    case DatabaseType.HANADB:
                        PrepareHANADBEditView();
                        break;
                }
            }
        }

        private void PrepareExchangeEditView()
        {
            var txtDbGroupName = ExchangeDatabaseConfig1.FindControl("txtDbGroupName") as TextBox;
            var txtMailboxDb = ExchangeDatabaseConfig1.FindControl("txtMailboxDb") as TextBox;

            if (txtDbGroupName != null && txtMailboxDb != null)
            {
                txtDbGroupName.Text = CurrentDatabase.DatabaseExchange.StorageGroupName;
                txtMailboxDb.Text = CurrentDatabase.DatabaseExchange.MailBoxDBName;
            }
        }

        private void PrepareSqlEditView()
        {
            var txtdtSid = MSSQLdatabaseconfig1.FindControl("txtdtSID") as TextBox;
            var txtUserName = MSSQLdatabaseconfig1.FindControl("txtUserName") as TextBox;
            var txtPort = MSSQLdatabaseconfig1.FindControl("txtPort") as TextBox;
            var txtPassword = MSSQLdatabaseconfig1.FindControl("txtPassword") as TextBox;
            var rbtnListAuthenticationMode = MSSQLdatabaseconfig1.FindControl("rbtnListAuthenticationMode") as RadioButtonList;
            var txtDataFileLocation = MSSQLdatabaseconfig1.FindControl("txtDataFileLocation") as TextBox;
            var txtTransactionLocation = MSSQLdatabaseconfig1.FindControl("txtTransactionLocation") as TextBox;
            var txtUndofile = MSSQLdatabaseconfig1.FindControl("txtUndofile") as TextBox;
            var txtSqlinstanceName = MSSQLdatabaseconfig1.FindControl("txtinstaname") as TextBox;
            var chkinsatnce = MSSQLdatabaseconfig1.FindControl("chkconfig") as CheckBox;
            var dvinstance = MSSQLdatabaseconfig1.FindControl("divinstance") as Control;
            var txtBackupRestorePath = MSSQLdatabaseconfig1.FindControl("txtBackupRestorePath") as TextBox;
            var txtNetworkSharedPath = MSSQLdatabaseconfig1.FindControl("txtNetworkSharedPath") as TextBox;
            var chkSSOEnable = MSSQLdatabaseconfig1.FindControl("ChkSSOEnable") as CheckBox;
            var ddlSSOProfileName = MSSQLdatabaseconfig1.FindControl("ddlSSOProfile") as DropDownList;
            var pnlsso = MSSQLdatabaseconfig1.FindControl("pnlSSoProfilrDrp") as Panel;
            var spnUserNm = MSSQLdatabaseconfig1.FindControl("spnUserNm") as Control;
            var spnPassword = MSSQLdatabaseconfig1.FindControl("spnPassword") as Control;
            Utility.PopulateSSOProfile(ddlSSOProfileName, true, 3);

            if (txtdtSid != null && txtUserName != null && txtPort != null && txtPassword != null && rbtnListAuthenticationMode != null
                && txtDataFileLocation != null && txtUndofile != null && txtBackupRestorePath != null && txtNetworkSharedPath != null && txtTransactionLocation != null && txtSqlinstanceName != null)
            {
                txtdtSid.Text = CurrentDatabase.DatabaseSql.DatabaseSID;
                txtUserName.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseSql.UserName);
                // txtPassword.Attributes["value"] = CurrentDatabase.DatabaseSql.Password;
                txtPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseSql.Password), hdfStaticGuid.Value);

                txtPort.Text = CurrentDatabase.DatabaseSql.Port.ToString();
                // rbtnListAuthenticationMode.SelectedValue = CurrentDatabase.DatabaseSql.AuthenticationMode.ToString();
                rbtnListAuthenticationMode.SelectedValue = CurrentDatabase.DatabaseSql.AuthenticationMode.ToString() == "Windows" ? SqlAuthenticateType.Windows.ToString() : SqlAuthenticateType.SqlServer.ToString();
                if (CurrentDatabase.DatabaseSql.InstanceName == null || CurrentDatabase.DatabaseSql.InstanceName == string.Empty || CurrentDatabase.DatabaseSql.InstanceName == "")
                {
                    dvinstance.Visible = false;
                    chkinsatnce.Checked = false;
                    CurrentDatabase.DatabaseSql.InstanceName = null;
                }
                else
                {
                    chkinsatnce.Checked = true;
                    dvinstance.Visible = true;
                    txtSqlinstanceName.Text = CurrentDatabase.DatabaseSql.InstanceName;
                }
                if (CurrentDatabase.DatabaseSql.SSOEnabled == 1)
                {
                    chkSSOEnable.Checked = true;
                    var sso = Facade.GetSinglesignOnById(CurrentDatabase.DatabaseSql.SSOEnabled);
                    pnlsso.Visible = true;
                    string ss = sso.ProfileName;

                    ddlSSOProfileName.SelectedValue = CurrentDatabase.DatabaseSql.SSOProfileId.ToString();
                    if (CurrentDatabase.DatabaseSql.InstanceName == "" || CurrentDatabase.DatabaseSql.InstanceName == null)
                    {
                        dvinstance.Visible = false;
                        chkinsatnce.Checked = false;
                    }
                    else
                    {
                        dvinstance.Visible = true;
                        chkinsatnce.Checked = true;
                    }
                    txtSqlinstanceName.Text = CurrentDatabase.DatabaseSql.InstanceName;
                    rbtnListAuthenticationMode.SelectedValue = CurrentDatabase.DatabaseSql.AuthenticationMode.ToString() == "Windows" ? SqlAuthenticateType.Windows.ToString() : SqlAuthenticateType.SqlServer.ToString();
                    spnUserNm.Visible = false;
                    spnPassword.Visible = false;
                }
                else
                {
                    chkSSOEnable.Checked = false;
                    pnlsso.Visible = false;
                    spnUserNm.Visible = true;
                    spnPassword.Visible = true;

                }

                txtDataFileLocation.Text = CurrentDatabase.DatabaseSql.DataFilePath;
                txtTransactionLocation.Text = CurrentDatabase.DatabaseSql.TransLogPath;
                txtUndofile.Text = CurrentDatabase.DatabaseSql.UndoFilePath;
                txtBackupRestorePath.Text = CurrentDatabase.DatabaseSql.BackupRestorePath;
                txtNetworkSharedPath.Text = CurrentDatabase.DatabaseSql.NetworkSharedPath;
            }
        }

        private void PrepareOracleEditView()
        {
            var txtOracleSid = OracleDatabaseconfig1.FindControl("txtOracleSid") as TextBox;
            var txtoracleUserName = OracleDatabaseconfig1.FindControl("txtUserName") as TextBox;
            var txtoraclePort = OracleDatabaseconfig1.FindControl("txtPort") as TextBox;
            var txtoraclePassword = OracleDatabaseconfig1.FindControl("txtPassword") as TextBox;
            // var txtoraclePassword = String.IsNullOrEmpty(_txtoraclePassword.Text) ? null : CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(_txtoraclePassword.Text, hdfStaticGuid.Value));
            var txtOracleAchive = OracleDatabaseconfig1.FindControl("txtAchive") as TextBox;
            var txtoracleHome = OracleDatabaseconfig1.FindControl("txtHome") as TextBox;
            var txtoracleRedo = OracleDatabaseconfig1.FindControl("txtRedo") as TextBox;
            //var txtoracleASMGrid = OracleDatabaseconfig1.FindControl("txtASMGrid") as TextBox;
            var txtoracleInstanceName = OracleDatabaseconfig1.FindControl("txtInstanceName") as TextBox;
            var txtASMInstancename = OracleDatabaseconfig1.FindControl("txtASMInstancename") as TextBox;
            var chkIsASM = OracleDatabaseconfig1.FindControl("chkIsASM") as CheckBox;
            var ChkRACDBnodes = OracleDatabaseconfig1.FindControl("ChkRACDBnodes") as CheckBox;
            var lblInstanceName = OracleDatabaseconfig1.FindControl("lblInstanceName") as Label;
            var chkRac = OracleDatabaseconfig1.FindControl("chkrac") as CheckBox;
            chkRac.Checked = false;
            chkRac.Enabled = false;
            var racconfigpanel = OracleDatabaseconfig1.FindControl("RacPanel1") as Panel;
            var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;

            var txtASMUserName = OracleDatabaseconfig1.FindControl("txtASMUserName") as TextBox;
            var txtASMPassword = OracleDatabaseconfig1.FindControl("txtASMPassword") as TextBox;
            var txtASMPath = OracleDatabaseconfig1.FindControl("txtASMPath") as TextBox;

            var txtPreExecution = OracleDatabaseconfig1.FindControl("txtPreExecution") as TextBox;
            var chkPreExecution = OracleDatabaseconfig1.FindControl("chkPreExecution") as CheckBox;

            var pnlPreExecution = OracleDatabaseconfig1.FindControl("pnlExecution") as Panel;

            var chkAuth = OracleDatabaseconfig1.FindControl("ChkSubstitue") as CheckBox;
            var chkAuth_ReqFieldValidator = OracleDatabaseconfig1.FindControl("rfvtxtdata") as RequiredFieldValidator;
            var pnl = OracleDatabaseconfig1.FindControl("pnlConSubsAuthn") as Panel;
            var lstdetail = OracleDatabaseconfig1.FindControl("lvsubaunth") as ListView;
            var txtDbAuthData2 = OracleDatabaseconfig1.FindControl("txtdata") as TextBox;
            var ddlAuthData2 = OracleDatabaseconfig1.FindControl("ddlAuthsql") as DropDownList;

            var ddlcrendetial2 = OracleDatabaseconfig1.FindControl("ddlcrendetial1") as DropDownList;

            var txtRole2 = OracleDatabaseconfig1.FindControl("txtRole1") as TextBox;
            var ddlRole2 = OracleDatabaseconfig1.FindControl("ddlRole1") as DropDownList;

            racconfigpanel.Visible = false;

            if (txtOracleSid != null && txtoracleUserName != null && txtoraclePassword != null && txtoraclePort != null && txtOracleAchive != null && txtoracleHome != null && txtoracleRedo != null)
            {
                txtOracleSid.Text = CurrentDatabase.DatabaseOracle.OracleSID;
                txtoracleUserName.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseOracle.UserName);
                // txtoraclePassword.Attributes["value"] = CurrentDatabase.DatabaseOracle.Password;
                txtoraclePassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseOracle.Password), hdfStaticGuid.Value);
                txtoraclePort.Text = CurrentDatabase.DatabaseOracle.Port.ToString();
                txtOracleAchive.Text = CurrentDatabase.DatabaseOracle.Archive.ToString();
                txtoracleHome.Text = CurrentDatabase.DatabaseOracle.Home.ToString();
                txtoracleRedo.Text = CurrentDatabase.DatabaseOracle.Redo.ToString();
                //txtoracleASMGrid.Text = CurrentDatabase.DatabaseOracle.ASMGrid.ToString();
                txtoracleInstanceName.Text = CurrentDatabase.DatabaseOracle.InstanceName.ToString();
                txtASMInstancename.Text = CurrentDatabase.DatabaseOracle.AsmInstanceName.ToString();

                txtASMUserName.Text = CurrentDatabase.DatabaseOracle.AsmUserName == "" ? string.Empty : CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseOracle.AsmUserName);
                // txtASMPassword.Attributes["value"] = CurrentDatabase.DatabaseOracle.AsmPassword.ToString();
                //txtASMPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseOracle.AsmPassword.ToString()), hdfStaticGuid.Value);
                txtASMPassword.Attributes["value"] = CurrentDatabase.DatabaseOracle.AsmPassword.ToString();
                txtASMPath.Text = CurrentDatabase.DatabaseOracle.AsmPath.ToString();



                if (CurrentEntity.DatabaseOracle.IsAsm == 1)
                {
                    chkIsASM.Checked = true;
                    txtASMInstancename.Visible = true;
                    lblInstanceName.Visible = true;

                }
                else
                {
                    chkIsASM.Checked = false;
                    txtASMInstancename.Visible = false;
                    lblInstanceName.Visible = false;
                    txtASMInstancename.Text = "";
                }

                if (CurrentEntity.Racdbnode == 1)
                {
                    ChkRACDBnodes.Checked = true;
                    racconfigpanel.Visible = true;
                }
                else
                {
                    ChkRACDBnodes.Checked = false;
                    racconfigpanel.Visible = false;
                    // cblstGroup.ClearSelection();
                }

                if (CurrentDatabase.DatabaseOracle.PreExecutionCommand != string.Empty)
                {
                    txtPreExecution.Text = CurrentDatabase.DatabaseOracle.PreExecutionCommand.ToString();
                    chkPreExecution.Checked = true;
                    pnlPreExecution.Visible = true;

                }
                else
                {
                    chkPreExecution.Checked = false;
                    pnlPreExecution.Visible = false;
                }

                var sqlauth = Facade.GetAllByDatabaseSubstituteAuthn(CurrentDatabase.DatabaseOracle.BaseDatabaseId);

                var databaseSqlplus = Facade.GetAllDatabaseSqlplus();

                if (databaseSqlplus != null)
                {
                    ddlAuthData2.DataSource = databaseSqlplus;
                    ddlAuthData2.DataTextField = "Sqlplus";
                    ddlAuthData2.DataValueField = "Id";
                    ddlAuthData2.DataBind();
                }

                var databaseRole = Facade.GetAllDatabaseRole();

                if (databaseRole != null)
                {
                    ddlRole2.DataSource = databaseRole;
                    ddlRole2.DataTextField = "Role";
                    ddlRole2.DataValueField = "Id";
                    ddlRole2.DataBind();
                }

                if (sqlauth != null)
                {
                    ddlcrendetial2.SelectedValue = sqlauth.Credential.ToString();
                    pnl.Visible = true;
                    chkAuth.Checked = true;
                    if (chkAuth_ReqFieldValidator != null)
                    {
                        chkAuth_ReqFieldValidator.Enabled = true;
                    }
                    var sqlinfo = Facade.GetSqlName(sqlauth.Sqlplus);
                    if (sqlinfo != null)
                    {
                        //dlAuthData2.SelectedValue = sqlinfo.Id.ToString();
                        txtDbAuthData2.Text = sqlinfo.Sqlplus.ToString();
                        if (string.IsNullOrEmpty(txtDbAuthData2.Text))
                        {
                            chkAuth_ReqFieldValidator.IsValid = false;
                        }
                        else
                        {
                            chkAuth_ReqFieldValidator.IsValid = true;
                        }
                    }

                    else
                    {
                        var authdetail = Facade.GetDatabaseAuthById(sqlauth.Id).FirstOrDefault();
                        ddlAuthData2.Items.Add(new ListItem(authdetail.Sqlplus, Convert.ToString(authdetail.Id)));
                        txtDbAuthData2.Text = authdetail.Sqlplus;
                        if (string.IsNullOrEmpty(txtDbAuthData2.Text))
                        {
                            chkAuth_ReqFieldValidator.IsValid = false;
                        }
                        else
                        {
                            chkAuth_ReqFieldValidator.IsValid = true;
                        }
                    }


                    var rolenfo = Facade.GetRoleName(sqlauth.Role);
                    if (rolenfo != null)
                    {
                        //ddlRole2.SelectedValue = rolenfo.Id.ToString();
                        txtRole2.Text = rolenfo.Role.ToString();
                    }
                    else
                    {
                        var authdetail = Facade.GetDatabaseAuthById(sqlauth.Id).FirstOrDefault();
                        ddlRole2.Items.Add(new ListItem(authdetail.Role, Convert.ToString(authdetail.Id)));
                        txtRole2.Text = authdetail.Role;
                    }

                    // ddlRole2.SelectedValue = Convert.ToString(sqlauth.Id);
                }
                else
                {
                    pnl.Visible = false;
                    chkAuth.Checked = false;
                    if (chkAuth_ReqFieldValidator != null)
                    {
                        chkAuth_ReqFieldValidator.Enabled = false;
                    }
                }
            }
        }

        private void PrepareOracleRacEditView()
        {
            var txtOracleSid = OracleDatabaseconfig1.FindControl("txtOracleSid") as TextBox;
            var txtoracleUserName = OracleDatabaseconfig1.FindControl("txtUserName") as TextBox;
            var txtoraclePort = OracleDatabaseconfig1.FindControl("txtPort") as TextBox;
            var txtoraclePassword = OracleDatabaseconfig1.FindControl("txtPassword") as TextBox;
            var chkRac = OracleDatabaseconfig1.FindControl("chkrac") as CheckBox;
            chkRac.Checked = true;
            chkRac.Enabled = false;
            var racconfigpanel = OracleDatabaseconfig1.FindControl("RacPanel1") as Panel;
            var oraclePanel = OracleDatabaseconfig1.FindControl("oraclePanel") as Panel;
            racconfigpanel.Visible = true;
            oraclePanel.Visible = false;

            //  var txtArchiveLogLocation = OracleDatabaseconfig1.FindControl("txtArchiveLogLocation") as TextBox;
            //   var txtSpfileLocation = OracleDatabaseconfig1.FindControl("txtSpfileLocation") as TextBox;
            //   txtOracleSid.Text = CurrentDatabase.DatabaseOracleRac.OracleSID;
            //    txtoracleUserName.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseOracleRac.UserName);
            //    txtoraclePassword.Attributes["value"] = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseOracleRac.Password);
            //     txtoraclePort.Text = CurrentDatabase.DatabaseOracleRac.Port.ToString();
            //     txtArchiveLogLocation.Text = CurrentEntity.DatabaseOracleRac.ArchivePath;
            //  txtSpfileLocation.Text = CurrentEntity.DatabaseOracleRac.InitSPFilePath;
            OracleDatabaseconfig1.SaveNode = Facade.GetAllDataBaseNodesByDatabaseId(CurrentEntity.Id);
            CheckGroupBoxList();
        }

        private void PrepareExchage2010EditView()
        {

            var txtMailboxDb = ExchangeDAGDatabaseConfig1.FindControl("txtMailboxDb") as TextBox;
            var ddlAuthType = ExchangeDAGDatabaseConfig1.FindControl("ddlAuthenticationType") as DropDownList;
            var ddlProtocolType = ExchangeDAGDatabaseConfig1.FindControl("ddlProtocolType") as DropDownList;

            //EnumHelper.PopulateEnumIntoList(ddlAuthType, typeof(AuthenticationType), " - Select Authentication Type - ");
            //EnumHelper.PopulateEnumIntoList(ddlProtocolType, typeof(ProtocolType), " - Select Protocol Type - ");


            if (txtMailboxDb != null)
            {
                txtMailboxDb.Text = CurrentDatabase.DatabaseExcahngeDAG.MailBoxDBName;
                ddlAuthType.SelectedValue = CurrentDatabase.DatabaseExcahngeDAG.AuthenticationType;
                ddlProtocolType.SelectedValue = CurrentDatabase.DatabaseExcahngeDAG.ProtocolType;
            }
        }

        protected void PrepareDb2EditView()
        {
            var txtDatabaseSid = Db2DatabaseConfig1.FindControl("txtDatabaseSid") as TextBox;
            var txtDb2UserName = Db2DatabaseConfig1.FindControl("txtUserName") as TextBox;
            var txtDb2Password = Db2DatabaseConfig1.FindControl("txtPassword") as TextBox;
            var txtDb2Port = Db2DatabaseConfig1.FindControl("txtPort") as TextBox;

            if (txtDatabaseSid != null && txtDb2UserName != null && txtDb2Password != null && txtDb2Port != null)
            {
                txtDatabaseSid.Text = CurrentDatabase.DatabaseDb2.DatabaseSID;
                txtDb2UserName.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseDb2.UserName);
                //txtDb2Password.Attributes["value"] = CurrentDatabase.DatabaseDb2.Password;
                txtDb2Password.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseDb2.Password), hdfStaticGuid.Value);
                txtDb2Port.Text = CurrentDatabase.DatabaseDb2.Port.ToString();
            }
        }

        protected void PrepareMySQLEditView()
        {
            var txtdbName = MysqlDbConfig1.FindControl("txtdbName") as TextBox;
            var txtMysqlusername = MysqlDbConfig1.FindControl("txtUserName") as TextBox;
            var txtMysqlpassword = MysqlDbConfig1.FindControl("txtPassword") as TextBox;
            var txtMysqlport = MysqlDbConfig1.FindControl("txtPort") as TextBox;

            if (txtdbName != null && txtMysqlusername != null && txtMysqlpassword != null && txtMysqlport != null)
            {
                txtdbName.Text = CurrentDatabase.DatabaseMySql.DatabaseID;
                txtMysqlusername.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseMySql.UserName);
                // txtMysqlpassword.Attributes["value"] = CurrentDatabase.DatabaseMySql.Password;
                txtMysqlpassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseMySql.Password), hdfStaticGuid.Value);
                txtMysqlport.Text = CurrentDatabase.DatabaseMySql.Port.ToString();
            }
        }

        protected void PreparePostgreSQLEditView()
        {
            var txtPostgreSqldbName = PostgreSql1.FindControl("txtDatabaseName") as TextBox;
            var txtPostgreSqlusername = PostgreSql1.FindControl("txtUserName") as TextBox;
            var txPostgreSqlpassword = PostgreSql1.FindControl("txtPassword") as TextBox;
            var txPostgreSqlport = PostgreSql1.FindControl("txtPort") as TextBox;
            var txPostgreArchiveLog = PostgreSql1.FindControl("txtArchive") as TextBox;

            if (txtPostgreSqldbName != null && txtPostgreSqlusername != null && txPostgreSqlpassword != null && txPostgreSqlport != null && txPostgreArchiveLog != null)
            {
                txtPostgreSqldbName.Text = CurrentDatabase.PostgreSql.DatabaseName;
                txtPostgreSqlusername.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.PostgreSql.UserName);
                // txPostgreSqlpassword.Attributes["value"] = CurrentDatabase.PostgreSql.Password;
                txPostgreSqlpassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.PostgreSql.Password), hdfStaticGuid.Value);
                txPostgreArchiveLog.Text = CurrentDatabase.PostgreSql.ArchiveLog.ToString();
                txPostgreSqlport.Text = CurrentDatabase.PostgreSql.Port.ToString();
            }
        }

        // Added by Vishal Shinde 01-12-14
        protected void PreparePostgr9xEditView()
        {
            var txtPostgreGRESqldbName = Postgre9x1.FindControl("txtDatabaseName") as TextBox;
            var txtPostgreGRESqlusername = Postgre9x1.FindControl("txtUserName") as TextBox;
            var txPostgreGRESqlpassword = Postgre9x1.FindControl("txtPassword") as TextBox;
            var txPostgreGRESqlport = Postgre9x1.FindControl("txtPort") as TextBox;
            var txDBDataDirectory = Postgre9x1.FindControl("txtDBDataDirectory") as TextBox;
            var txDBbinDirectory = Postgre9x1.FindControl("txtDBbinDirectory") as TextBox;
            var txsulogin = Postgre9x1.FindControl("txtSULogin") as TextBox;
            var txServiceName = Postgre9x1.FindControl("txtServiceName") as TextBox;

            if (txtPostgreGRESqldbName != null && txtPostgreGRESqlusername != null && txPostgreGRESqlpassword != null && txPostgreGRESqlport != null && txDBDataDirectory != null && txDBbinDirectory != null && txsulogin != null && txServiceName != null)
            {
                txtPostgreGRESqldbName.Text = CurrentDatabase.DatabasePostgre9x.DatabaseName;
                txtPostgreGRESqlusername.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabasePostgre9x.UserName);
                // txPostgreGRESqlpassword.Attributes["value"] = CurrentDatabase.DatabasePostgre9x.Password;
                txPostgreGRESqlpassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabasePostgre9x.Password), hdfStaticGuid.Value);
                txPostgreGRESqlport.Text = CurrentDatabase.DatabasePostgre9x.Port.ToString();

                txDBDataDirectory.Text = CurrentDatabase.DatabasePostgre9x.DBDataDirectory.ToString();
                txDBbinDirectory.Text = CurrentDatabase.DatabasePostgre9x.DBbinDirectory.ToString();
                txsulogin.Text = CurrentDatabase.DatabasePostgre9x.SULogin.ToString();
                txServiceName.Text = CurrentDatabase.DatabasePostgre9x.ServiceName.ToString();
            }
        }

        protected void PrepareMsSqlEditView()
        {
            var txtMSSqlDBName = MSSQL2008.FindControl("txtDBName") as TextBox;
            var txtMSSqlinstanceName = MSSQL2008.FindControl("txtinstaname") as TextBox;
            var chkinsatnce = MSSQL2008.FindControl("chkconfig") as CheckBox;
            var dvinstance = MSSQL2008.FindControl("divinstance") as Control;
            var txtMSSqlusername = MSSQL2008.FindControl("txtUserName") as TextBox;
            var txtMSSqlpassword = MSSQL2008.FindControl("txtPassword") as TextBox;
            var txtMSSqlport = MSSQL2008.FindControl("txtPort") as TextBox;
            var rbtnListAuthenticationMode = MSSQL2008.FindControl("rbtnListAuthenticationMode") as RadioButtonList;
            var chkSSOEnable = MSSQL2008.FindControl("ChkSSOEnable") as CheckBox;
            var ddlSSOProfileName = MSSQL2008.FindControl("ddlSSOProfile") as DropDownList;
            var dvusername = MSSQL2008.FindControl("dvusername") as Control;
            var dvpassword = MSSQL2008.FindControl("dvpassword") as Control;
            var pnlsso = MSSQL2008.FindControl("pnlSSoProfilrDrp") as Panel;
            var username = MSSQL2008.FindControl("dvusername") as HtmlGenericControl;
            var password = MSSQL2008.FindControl("dvpassword") as HtmlGenericControl;
            Utility.PopulateSSOProfile(ddlSSOProfileName, true, 3);


            if (txtMSSqlDBName != null && txtMSSqlusername != null && txtMSSqlpassword != null && txtMSSqlport != null && txtMSSqlinstanceName != null)
            {
                if (CurrentDatabase.Databasemssql.InstanceName == null || CurrentDatabase.Databasemssql.InstanceName == string.Empty || CurrentDatabase.Databasemssql.InstanceName == "")
                {
                    dvinstance.Visible = false;
                    chkinsatnce.Checked = false;
                    CurrentDatabase.Databasemssql.InstanceName = null;

                }
                else
                {

                    dvinstance.Visible = true;
                    chkinsatnce.Checked = true;
                    txtMSSqlinstanceName.Text = CurrentDatabase.Databasemssql.InstanceName;
                }
                if (CurrentDatabase.Databasemssql.SSOEnabled == 1)
                {
                    chkSSOEnable.Checked = true;
                    var sso = Facade.GetSinglesignOnById(CurrentDatabase.Databasemssql.SSOProfileId);
                    //   var sso = Facade.GetSignByProfileName(ddlSSOProfileName.SelectedItem.Text);
                    pnlsso.Visible = true;
                    string ss = sso.ProfileName;

                    ddlSSOProfileName.SelectedValue = CurrentDatabase.Databasemssql.SSOProfileId.ToString();

                    //dvinstance.Visible = true;
                    //chkinsatnce.Checked = true;
                    txtMSSqlinstanceName.Text = CurrentDatabase.Databasemssql.InstanceName;
                    rbtnListAuthenticationMode.SelectedValue = CurrentDatabase.Databasemssql.AuthenticationMode.ToString() == "Windows" ? SqlAuthenticateType.Windows.ToString() : SqlAuthenticateType.SqlServer.ToString();
                    username.Visible = true;
                    password.Visible = true;
                }
                else
                {
                    chkSSOEnable.Checked = false;
                    pnlsso.Visible = false;

                }

                txtMSSqlDBName.Text = CurrentDatabase.Databasemssql.DatabaseName.ToString();
                //txtMSSqlusername.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.Databasemssql.UserName);
                //txtMSSqlpassword.Attributes["value"] = CurrentDatabase.Databasemssql.Password;
                txtMSSqlport.Text = CurrentDatabase.Databasemssql.Port.ToString();
                rbtnListAuthenticationMode.SelectedValue = CurrentDatabase.Databasemssql.AuthenticationMode.ToString() == "Windows" ? SqlAuthenticateType.Windows.ToString() : SqlAuthenticateType.SqlServer.ToString();
                if (CurrentDatabase.Databasemssql.AuthenticationMode.ToString() == "Windows")
                {
                    rbtnListAuthenticationMode.SelectedIndex = 0;
                    txtMSSqlusername.Text = "";
                    txtMSSqlpassword.Attributes["value"] = "";
                    dvusername.Visible = false;
                    dvpassword.Visible = false;
                }
                else
                {
                    rbtnListAuthenticationMode.SelectedIndex = 1;
                    dvusername.Visible = true;
                    dvpassword.Visible = true;
                    if (!string.IsNullOrEmpty(CurrentDatabase.Databasemssql.UserName) && !CurrentDatabase.Databasemssql.UserName.Equals("NA"))
                        txtMSSqlusername.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.Databasemssql.UserName);
                    if (!string.IsNullOrEmpty(CurrentDatabase.Databasemssql.Password))

                        txtMSSqlpassword.Attributes["value"] = CurrentDatabase.Databasemssql.Password;

                    //txtMSSqlpassword.Attributes["value"] = CurrentDatabase.Databasemssql.Password;
                    txtMSSqlpassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.Databasemssql.Password), hdfStaticGuid.Value);

                }
            }
        }

        //Added By Meenakshi Patil
        protected void PrepareSybaseEditView()
        {
            var txtname = SybaseDBConfig.FindControl("txtname") as TextBox;
            var txtUserName = SybaseDBConfig.FindControl("txtUserName") as TextBox;
            var txtPort = SybaseDBConfig.FindControl("txtPort") as TextBox;
            var txtPassword = SybaseDBConfig.FindControl("txtPassword") as TextBox;
            var txtPRTransFileLocation = SybaseDBConfig.FindControl("txtPRTransFileLocation") as TextBox;
            var txtdtServername = SybaseDBConfig.FindControl("txtdtServername") as TextBox;
            var txtBackupName = SybaseDBConfig.FindControl("txtBackupName") as TextBox;
            var chkaccesstransactiondump = SybaseDBConfig.FindControl("chkaccesstransactiondump") as CheckBox;
            var txtEnvrnmentPath = SybaseDBConfig.FindControl("txtEnvrnmentPath") as TextBox;

            if (txtname != null && txtUserName != null && txtPort != null && txtPassword != null && txtPRTransFileLocation != null && txtdtServername != null && txtBackupName != null && chkaccesstransactiondump != null && txtEnvrnmentPath != null)
            {
                txtname.Text = CurrentDatabase.Databasesybase.DatabaseSID;
                txtUserName.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.Databasesybase.UserName);
                //txtPassword.Attributes["value"] = CurrentDatabase.Databasesybase.Password;
                txtPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.Databasesybase.Password), hdfStaticGuid.Value);
                txtPort.Text = CurrentDatabase.Databasesybase.Port.ToString();
                txtPRTransFileLocation.Text = CurrentDatabase.Databasesybase.TransactionFileLocation;
                txtdtServername.Text = CurrentDatabase.Databasesybase.SybaseDataServerName;
                txtBackupName.Text = CurrentDatabase.Databasesybase.SybaseBackupServer;
                chkaccesstransactiondump.Checked = Convert.ToBoolean(CurrentDatabase.Databasesybase.IsStandByaccess);
                // txtEnvrnmentPath.Text = CurrentDatabase.Databasesybase.EnvironmentPath;
            }
        }

        //Added By Uma Mehavarnan on 05-May-2016
        protected void PrepareMaxDBEditView()
        {
            var txtMaxDBSId = MaxDBConfig.FindControl("txtMaxDBSId") as TextBox;
            var txtInstanceName = MaxDBConfig.FindControl("txtInstanceName") as TextBox;
            var txtUserName = MaxDBConfig.FindControl("txtUserName") as TextBox;
            var txtPassword = MaxDBConfig.FindControl("txtPassword") as TextBox;
            var txtPort = MaxDBConfig.FindControl("txtPort") as TextBox;
            var txtInstallationPath = MaxDBConfig.FindControl("txtInstallationPath") as TextBox;
            var txtMediumName = MaxDBConfig.FindControl("txtMediumName") as TextBox;
            var txtLogfileName = MaxDBConfig.FindControl("txtLogfileName") as TextBox;
            var txtLogPath = MaxDBConfig.FindControl("txtLogPath") as TextBox;


            if (txtMaxDBSId != null && txtInstanceName != null && txtUserName != null && txtPassword != null && txtPort != null && txtInstallationPath != null && txtMediumName != null && txtLogfileName != null)
            {
                txtMaxDBSId.Text = CurrentDatabase.DatabasemaxDB.DatabaseSID;
                txtInstanceName.Text = CurrentDatabase.DatabasemaxDB.InstanceName.ToString();
                txtUserName.Attributes["value"] = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabasemaxDB.UserName);
                //txtPassword.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabasemaxDB.Password);
                txtPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabasemaxDB.Password), hdfStaticGuid.Value);
                txtPort.Text = CurrentDatabase.DatabasemaxDB.Port.ToString();
                txtInstallationPath.Text = CurrentDatabase.DatabasemaxDB.InstallationPath.ToString();
                txtMediumName.Text = CurrentDatabase.DatabasemaxDB.MediumName.ToString();
                txtLogfileName.Text = CurrentDatabase.DatabasemaxDB.LogfileName.ToString();
                txtLogPath.Text = CurrentDatabase.DatabasemaxDB.LogPath.ToString();
            }
        }

        protected void PrepareMongoDBEditView()
        {
            var txtinstallationpath = MongoDBConfig.FindControl("txtinstallationpath") as TextBox;
            var txtinstancename = MongoDBConfig.FindControl("txtinstancename") as TextBox;
            var txtport = MongoDBConfig.FindControl("txtport") as TextBox;
            var txtusername = MongoDBConfig.FindControl("txtusername") as TextBox;
            var txtpassword = MongoDBConfig.FindControl("txtpassword") as TextBox;
            var txtbinarylocation = MongoDBConfig.FindControl("txtbinarylocation") as TextBox;



            if (txtinstallationpath != null && txtinstancename != null && txtport != null && txtusername != null && txtpassword != null)
            {
                txtinstallationpath.Text = CurrentDatabase.mongodb.InstallationPath;
                txtinstancename.Text = CurrentDatabase.mongodb.InstanceName.ToString();
                txtport.Text = CurrentDatabase.mongodb.Port.ToString();
                txtusername.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.mongodb.UserName);
                //txtpassword.Attributes["value"] = CurrentDatabase.mongodb.Password;
                txtpassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.mongodb.Password), hdfStaticGuid.Value);
                txtbinarylocation.Text = CurrentDatabase.mongodb.BinaryLocation;
            }
        }

        protected void PrepareSyBaseWithRsHADREditView()
        {
            var txtServerPort = SybaseWithRsHadrConfig.FindControl("txtServerPort") as TextBox;
            var txtname = SybaseWithRsHadrConfig.FindControl("txtname") as TextBox;
            var txtUserName = SybaseWithRsHadrConfig.FindControl("txtUserName") as TextBox;
            var txtPassword = SybaseWithRsHadrConfig.FindControl("txtPassword") as TextBox;
            var txtPREnvPath = SybaseWithRsHadrConfig.FindControl("txtEnvPath") as TextBox;
            var txtdtServername = SybaseWithRsHadrConfig.FindControl("txtdtServername") as TextBox;
            var txtBackupName = SybaseWithRsHadrConfig.FindControl("txtBackupName") as TextBox;


            if (txtname != null && txtUserName != null && txtServerPort != null && txtPassword != null && txtPREnvPath != null && txtdtServername != null && txtBackupName != null)
            {
                txtServerPort.Text = CurrentDatabase.DatabaseSybaseWithRSHADR.ServerWithPort.ToString();
                txtname.Text = CurrentDatabase.DatabaseSybaseWithRSHADR.DataBaseSID;
                txtUserName.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseSybaseWithRSHADR.UserName);
                // txtPassword.Attributes["value"] = CurrentDatabase.DatabaseSybaseWithRSHADR.Password;
                txtPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseSybaseWithRSHADR.Password), hdfStaticGuid.Value);
                txtPREnvPath.Text = CurrentDatabase.DatabaseSybaseWithRSHADR.SybaseEnv_Path;
                txtdtServername.Text = CurrentDatabase.DatabaseSybaseWithRSHADR.SybaseDataServerName;
                txtBackupName.Text = CurrentDatabase.DatabaseSybaseWithRSHADR.SybaseBackupServer;
            }
        }

        protected void PrepareHANADBEditView()
        {
            var txtDatabaseSid = HANADatabaseConfig.FindControl("txtDatabaseSid") as TextBox;
            var txtInstanceNo = HANADatabaseConfig.FindControl("txtInstanceNo") as TextBox;
            var txtHostName = HANADatabaseConfig.FindControl("txtHostName") as TextBox;
            var txtHANADBUserName = HANADatabaseConfig.FindControl("txtUserName") as TextBox;
            var txtHANADBPassword = HANADatabaseConfig.FindControl("txtPassword") as TextBox;
            var txtHANADBPort = HANADatabaseConfig.FindControl("txtPort") as TextBox;

            if (txtDatabaseSid != null && txtInstanceNo != null && txtHostName != null)
            {
                txtDatabaseSid.Text = CurrentDatabase.DatabaseHanaDb.DatabaseSID;
                txtInstanceNo.Text = CurrentDatabase.DatabaseHanaDb.InstanceNo;
                txtHostName.Text = CurrentDatabase.DatabaseHanaDb.HostName;
            }
            if (CurrentDatabase.DatabaseHanaDb.UserName != " ")
            {
                txtHANADBUserName.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseHanaDb.UserName);
            }
            else
            {
                txtHANADBUserName.Text = string.Empty;
            }

            if (CurrentDatabase.DatabaseHanaDb.Password != " ")
            {
                //txtHANADBPassword.Attributes["value"] = CurrentDatabase.DatabaseHanaDb.Password;
                txtHANADBPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabaseHanaDb.Password), hdfStaticGuid.Value);
            }
            else
            {
                txtHANADBPassword.Text = string.Empty;
            }
            txtHANADBPort.Text = CurrentDatabase.DatabaseHanaDb.Port == "" ? string.Empty : CurrentDatabase.DatabaseHanaDb.Port;

        }

        //Added By Sumit Wakade
        protected void PrepareSybaseWithSrsEditView()
        {
            var txtname = SybaseWithSrsDBConfig.FindControl("txtname") as TextBox;
            var txtUserName = SybaseWithSrsDBConfig.FindControl("txtUserName") as TextBox;
            var txtPort = SybaseWithSrsDBConfig.FindControl("txtPort") as TextBox;
            var txtPassword = SybaseWithSrsDBConfig.FindControl("txtPassword") as TextBox;
            var txtPREnvPath = SybaseWithSrsDBConfig.FindControl("txtEnvPath") as TextBox;
            var txtdtServername = SybaseWithSrsDBConfig.FindControl("txtdtServername") as TextBox;
            var txtBackupName = SybaseWithSrsDBConfig.FindControl("txtBackupName") as TextBox;


            if (txtname != null && txtUserName != null && txtPort != null && txtPassword != null && txtPREnvPath != null && txtdtServername != null && txtBackupName != null)
            {
                txtname.Text = CurrentDatabase.DatabasesybaseWithSrs.DatabaseSID;
                txtUserName.Text = CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabasesybaseWithSrs.UserName);
                // txtPassword.Attributes["value"] = CurrentDatabase.DatabasesybaseWithSrs.Password;
                txtPassword.Attributes["value"] = Utility.getHashKeyByString(CryptographyHelper.Md5Decrypt(CurrentDatabase.DatabasesybaseWithSrs.Password), hdfStaticGuid.Value);
                txtPort.Text = CurrentDatabase.DatabasesybaseWithSrs.Port.ToString();
                txtPREnvPath.Text = CurrentDatabase.DatabasesybaseWithSrs.SybaseEnv_Path;
                txtdtServername.Text = CurrentDatabase.DatabasesybaseWithSrs.SybaseDataServerName;
                txtBackupName.Text = CurrentDatabase.DatabasesybaseWithSrs.SybaseBackupServer;
            }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.DatabaseList;
                }

                return string.Empty;
            }
        }

        protected void DdlDBTypeSelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlDBType.SelectedValue != Convert.ToString(0))
            {
                lblError.Visible = false;
                lblError.InnerText = string.Empty;
            }
            LoadUserControl(ddlDBType.SelectedValue);

            if (ddlDBType.SelectedValue == "Oracle")
            {
                ddldbConnectivity.Visible = true;
                lblDBConnectivity.Visible = true;
                RequiredFieldValidator5.Visible = true;
            }
            else
            {
                ddldbConnectivity.Visible = false;
                lblDBConnectivity.Visible = false;
                RequiredFieldValidator5.Visible = false;
                CurrentEntity.DatabaseConnectivity = null;
            }

        }

        private bool CheckMailBoxNameExist()
        {
            //if (txtMailboxDb.Text.ToLower().Equals(CurrentDatabase.DatabaseExchange.MailBoxDBName.ToLower()))
            //{
            //    return false;
            //}
            var txtMailboxDb = ExchangeDAGDatabaseConfig1.FindControl("txtMailboxDb") as TextBox;
            var lblmsg = ExchangeDAGDatabaseConfig1.FindControl("lblMailboxName.Text") as Label;
            int serverId = ddlServerId.SelectedValue.Contains("~") ? Convert.ToInt32(ddlServerId.SelectedValue.Split('~').GetValue(0)) : 0;

            return _facade.IsExistDatabaseMailBoxExchangeDAGByName(txtMailboxDb.Text.ToLower(), serverId);
        }

        private void LoadUserControl(string type)
        {

            MSSQLdatabaseconfig1.Visible = false;
            OracleDatabaseconfig1.Visible = false;
            ExchangeDatabaseConfig1.Visible = false;
            ExchangeDAGDatabaseConfig1.Visible = false;
            Db2DatabaseConfig1.Visible = false;
            MysqlDbConfig1.Visible = false;
            PostgreSql1.Visible = false;
            MSSQL2008.Visible = false;
            SybaseDBConfig.Visible = false;
            Postgre9x1.Visible = false;
            MaxDBConfig.Visible = false;
            MongoDBConfig.Visible = false;
            SybaseWithRsHadrConfig.Visible = false;
            switch (type)
            {
                case "OracleRac":
                case "Oracle":
                    OracleDatabaseconfig1.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/OracleDatabaseconfig.ascx");
                    //Utility.PopulateVersionByDBType(ddlVersion, 1, true);
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(OracleDB), "Select Version");

                    ddlVersion.Enabled = true;
                    break;

                case "Sql":
                    MSSQLdatabaseconfig1.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/MSSQLdatabaseconfig.ascx");
                    //Utility.PopulateVersionByDBType(ddlVersion, 2, true);
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(SqlDB), "Select Version");
                    ddlVersion.Enabled = true;
                    break;

                case "Exchange":
                    ExchangeDatabaseConfig1.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/ExchangeDatabaseConfig.ascx");
                    //Utility.PopulateVersionByDBType(ddlVersion, 3, true);
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(ExchangeDB), "Select Version");
                    ddlVersion.Enabled = true;
                    break;

                case "DB2":
                    Db2DatabaseConfig1.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/DB2DatabaseConfig.ascx");
                    //Utility.PopulateVersionByDBType(ddlVersion, 4, true);
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(DB2DB), "Select Version");
                    ddlVersion.Enabled = true;
                    break;

                case "ExchangeDAG":
                    ExchangeDAGDatabaseConfig1.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/MSExchangeDAGDatabaseConfig.ascx");
                    //Utility.PopulateVersionByDBType(ddlVersion, 5, true);                  
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(ExchangeDAG), "Select Version");
                    ddlVersion.Enabled = true;
                    break;

                case "MySQL":
                    MysqlDbConfig1.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/MySqlDbConfig.ascx");
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(MySQLDB), "Select Version");
                    ddlVersion.Enabled = true;
                    break;

                case "PostgreSQL":
                    PostgreSql1.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/PostgreSql.ascx");
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(POSTGRESQLDB), "Select Version");
                    ddlVersion.Enabled = true;
                    break;

                case "Postgres9x":
                    Postgre9x1.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/DatabasePostgre9x.ascx");
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(POSTGRE9XDB), "Select Version");
                    ddlVersion.Enabled = true;
                    break;

                case "SQLNative2008":
                    MSSQL2008.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/DBSqlNative2008.ascx");
                    EnumHelper.PopulateEnumDescriptionIntoList(ddlVersion, typeof(DBSQLNATIVE2008), "Select Version");
                    ddlVersion.Enabled = true;
                    break;

                case "SyBase":
                    SybaseDBConfig.Visible = true;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    _userControl = (UserControl)Page.LoadControl("../Controls/SybaseDatabaseConfig.ascx");
                    EnumHelper.PopulateEnumDescriptionIntoList(ddlVersion, typeof(SybaseDBType), "Select Version");
                    ddlVersion.Enabled = true;
                    RequiredFieldValidator3.Enabled = false;
                    break;


                case "MaxDB":
                    MaxDBConfig.Visible = true;
                    _userControl = (UserControl)Page.LoadControl("../Controls/MaxDBDatabaseConfig.ascx");
                    EnumHelper.PopulateEnumDescriptionIntoList(ddlVersion, typeof(MaxDBType), "Select Version");
                    ddlVersion.Enabled = true;
                    RequiredFieldValidator3.Enabled = false;
                    break;

                case "SyBaseWithSrs":
                    Postgre9x1.Visible = false;
                    PostgreSql1.Visible = false;
                    MysqlDbConfig1.Visible = false;
                    MSSQLdatabaseconfig1.Visible = false;
                    OracleDatabaseconfig1.Visible = false;
                    ExchangeDatabaseConfig1.Visible = false;
                    ExchangeDAGDatabaseConfig1.Visible = false;
                    Db2DatabaseConfig1.Visible = false;
                    MSSQL2008.Visible = false;
                    SybaseDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = false;
                    SybaseWithSrsDBConfig.Visible = true;
                    _userControl = (UserControl)Page.LoadControl("../Controls/SybaseWithSrsConfig.ascx");
                    EnumHelper.PopulateEnumDescriptionIntoList(ddlVersion, typeof(SybaseWithSRSDBType), "Select Version");
                    ddlVersion.Enabled = true;
                    RequiredFieldValidator3.Enabled = false;
                    break;

                case "MongoDB":

                    MongoDBConfig.Visible = true;
                    _userControl = (UserControl)Page.LoadControl("../Controls/MongoDBConfiguration.ascx");
                    EnumHelper.PopulateEnumDescriptionIntoList(ddlVersion, typeof(MongoDBType), "Select Version");
                    ddlVersion.Enabled = true;
                    RequiredFieldValidator3.Enabled = false;
                    break;

                case "SyBaseWithRsHADR":
                    Postgre9x1.Visible = false;
                    PostgreSql1.Visible = false;
                    MysqlDbConfig1.Visible = false;
                    MSSQLdatabaseconfig1.Visible = false;
                    OracleDatabaseconfig1.Visible = false;
                    ExchangeDatabaseConfig1.Visible = false;
                    ExchangeDAGDatabaseConfig1.Visible = false;
                    Db2DatabaseConfig1.Visible = false;
                    MSSQL2008.Visible = false;
                    SybaseDBConfig.Visible = false;
                    SybaseWithSrsDBConfig.Visible = false;
                    SybaseWithRsHadrConfig.Visible = true;
                    _userControl = (UserControl)Page.LoadControl("../Controls/SybaseWithRsHADRConfig.ascx");
                    EnumHelper.PopulateEnumDescriptionIntoList(ddlVersion, typeof(SybaseWithRSHadrDBType), "Select Version");
                    ddlVersion.Enabled = true;
                    RequiredFieldValidator3.Enabled = false;
                    break;

                case "HANADB":
                    HANADatabaseConfig.Visible = true;
                    _userControl = (UserControl)Page.LoadControl("../Controls/HANADatabaseConfig.ascx");
                    EnumHelper.PopulateEnumDbDescriptionIntoList(ddlVersion, typeof(HANADBType), "Select Version");
                    ddlVersion.Enabled = true;
                    RequiredFieldValidator3.Enabled = false;
                    break;

                default:
                    MSSQLdatabaseconfig1.Visible = false;
                    OracleDatabaseconfig1.Visible = false;
                    ExchangeDatabaseConfig1.Visible = false;
                    Db2DatabaseConfig1.Visible = false;
                    ExchangeDAGDatabaseConfig1.Visible = false;
                    PostgreSql1.Visible = false;
                    Postgre9x1.Visible = false;
                    MSSQL2008.Visible = false;
                    ExchangeDAGDatabaseConfig1.Visible = false;
                    SybaseDBConfig.Visible = false;
                    MongoDBConfig.Visible = true;
                    break;


            }
        }

        protected void BtnSaveClick(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("BtnSaveClick Event Execution Start For DataBase Configuration.");

                var chkRac = OracleDatabaseconfig1.FindControl("chkrac") as CheckBox;

                if (chkRac.Checked)
                {
                    if (!CheckValidation_rac()) return;
                }

                var txtMailboxDb = ExchangeDAGDatabaseConfig1.FindControl("txtMailboxDb") as TextBox;
                var lblmsg = ExchangeDAGDatabaseConfig1.FindControl("lblMailboxName") as Label;
                var chkSSOEnabled = MSSQLdatabaseconfig1.FindControl("ChkSSOEnable") as CheckBox;
                var chkSSOEnabled1 = MSSQL2008.FindControl("ChkSSOEnable") as CheckBox;
                if (txtMailboxDb.Text != string.Empty)
                {
                    //  lblmsg.Text = CheckMailBoxNameExist() ? "MailBox Name is Not Avaliable" : string.Empty;
                    if (btnSave.Text != "Update")
                    {
                        if (txtMailboxDb.Text != string.Empty)
                        {
                            if (CheckMailBoxNameExist() == true)
                            {
                                lblmsg.Text = "MailBox Name is Not Avaliable";
                                return;
                            }
                            else
                            {
                                lblmsg.Text = "";
                            }
                        }
                    }
                }

                //if (Page.IsValid && ValidateRequest("Database component",UserActionType.CreateDatabaseComponent))
                if (Page.IsValid && (ViewState["_token"] != null) && !CheckDataBaseNameExist() && ValidateRequest("DatabaseConfigurationBase", UserActionType.CreateDatabaseComponent))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {
                        {
                            _logger.Info("Inputes Are Valid For DataBase Configuration.");

                            //        if (CheckValidDB() && !CheckDataBaseNameExist())
                            if (!CheckDataBaseNameExist())
                            {
                                _logger.Info("DataBase Name Is Valid For DataBase Configuration.");

                                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                                if (returnUrl.IsNullOrEmpty())
                                {
                                    returnUrl = ReturnUrl;
                                }
                                var submitButton = (Button)sender;
                                string buttionText = " " + submitButton.Text.ToLower() + " ";
                                var currentTransactionType = TransactionType.Undefined;
                                if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                                {
                                    currentTransactionType = TransactionType.Save;
                                }
                                else if (buttionText.Contains(" update "))
                                {
                                    currentTransactionType = TransactionType.Update;
                                }

                                if (ddlDBType.SelectedValue == "Sql")// || DatabaseType.SQLNative2008.Equals(ddlDBType.SelectedValue.ToString()))
                                {
                                    if (chkSSOEnabled.Checked == true)
                                    {
                                        var lblssoprofileerror = MSSQLdatabaseconfig1.FindControl("lblssoprofileerror") as Label;
                                        var ddlSSOProfile = MSSQLdatabaseconfig1.FindControl("ddlSSOProfile") as DropDownList;
                                        if (ddlSSOProfile.SelectedValue == 0.ToString())
                                        {
                                            lblssoprofileerror.Visible = true;
                                            return;
                                        }
                                        else
                                        {
                                            lblssoprofileerror.Visible = false;
                                        }
                                    }
                                }

                                if (ddlDBType.SelectedValue == "SQLNative2008")
                                {
                                    if (chkSSOEnabled1.Checked == true)
                                    {
                                        var ddlSSOProfile1 = MSSQL2008.FindControl("ddlSSOProfile") as DropDownList;
                                        var lblSsoProfileerrorMsg = MSSQL2008.FindControl("lblSsoProfileerrorMsg") as Label;
                                        if (ddlSSOProfile1.SelectedValue == 0.ToString())
                                        {
                                            lblSsoProfileerrorMsg.Visible = true;
                                            return;
                                        }
                                        else
                                        {
                                            lblSsoProfileerrorMsg.Visible = false;
                                        }
                                    }
                                }
                                try
                                {
                                    if (currentTransactionType != TransactionType.Undefined)
                                    {
                                        BuildEntities();
                                        StartTransaction();
                                        SaveEditor();
                                        EndTransaction();
                                        string message = MessageInitials + " " + '"' + CurrentEntity.Name + '"';
                                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                           currentTransactionType));
                                        btnSave.Enabled = false;
                                    }
                                }
                                catch (CpException ex)
                                {
                                    _logger.Error("Exception While save database in CPException" + ex.Message);

                                    if (ex.InnerException != null)
                                    {
                                        _logger.Error("Exception While save database in CPException, InnerException  " + ex.InnerException);
                                        _logger.Error("Exception While save database in CPException, InnerException Message " + ex.InnerException.Message);
                                        _logger.Error("Exception While save database in CPException, StackTrace" + ex.InnerException.StackTrace);
                                    }
                                    InvalidateTransaction();

                                    returnUrl = Request.RawUrl;

                                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                                    ExceptionManager.Manage(ex, Page);
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Exception While Save Database, Error Message " + ex.Message);

                                    if (ex.InnerException != null)
                                    {
                                        _logger.Error("Exception While Save Database, InnerException  " + ex.InnerException);
                                        _logger.Error("Exception While Save Database, InnerException Message " + ex.InnerException.Message);
                                        _logger.Error("Exception While Save Database, StackTrace" + ex.InnerException.StackTrace);
                                    }
                                    InvalidateTransaction();

                                    returnUrl = Request.RawUrl;

                                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                                    {
                                        ExceptionManager.Manage((CpException)ex.InnerException, Page);
                                    }
                                    else
                                    {
                                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                                        ExceptionManager.Manage(customEx, Page);
                                    }
                                }

                                if (returnUrl.IsNotNullOrEmpty())
                                {
                                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseId, CurrentEntity.Id);
                                    //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseType, ddlDBType.SelectedValue);

                                    //Helper.Url.Redirect(returnUrl);

                                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseId, CurrentEntity.Id);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseType, ddlDBType.SelectedValue);
                                    //Helper.Url.Redirect(secureUrl);
                                    Response.Redirect(Convert.ToString(secureUrl), false);

                                }

                                //if (returnUrl.IsNotNullOrEmpty())
                                //{
                                //    if (CurrentEntity.DatabaseNode.BaseDatabaseId > 0)
                                //    {
                                //        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.DatabaseType, "OracleRac");
                                //        Helper.Url.Redirect(secureUrl);
                                //    }
                                //    else
                                //    {
                                //        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.DatabaseType, ddlDBType.SelectedValue);
                                //        Helper.Url.Redirect(secureUrl);
                                //    }
                                //}
                            }
                        }
                    }
                }
                _logger.Info("BtnSaveClick Event Execution Completed For DataBase Configuration.");
            }
            catch (Exception exc)
            {
                _logger.Error("Exception Occurred In BtnSaveClick Event Of DataBase Configuration, Error Message " + exc.Message);
                if (exc.InnerException != null)
                {
                    _logger.Error("Exception Occurred In BtnSaveClick Event Of DataBase Configuration, InnerException " + exc.InnerException);
                    _logger.Error("Exception Occurred In BtnSaveClick Event Of DataBase Configuration, InnerException Message " + exc.InnerException.Message);
                    _logger.Error("Exception Occurred In BtnSaveClick Event Of DataBase Configuration, StackTrace" + exc.InnerException.StackTrace);
                }
            }
        }


        public override void BuildEntities()
        {
            try
            {
                _logger.Info("BuildEntities Method Execution Start For DB Configuration.");

                CurrentEntity.Name = txtName.Text;
                var dbType = DatabaseType.Undefined;
                switch (ddlDBType.SelectedValue)
                {
                    case "Oracle":
                        dbType = DatabaseType.Oracle;
                        break;

                    case "Sql":
                        dbType = DatabaseType.Sql;
                        break;

                    case "Exchange":
                        dbType = DatabaseType.Exchange;
                        break;

                    case "DB2":
                        dbType = DatabaseType.DB2;
                        break;

                    case "ExchangeDAG":
                        dbType = DatabaseType.ExchangeDAG;
                        break;

                    case "MySQL":
                        dbType = DatabaseType.MySQL;
                        break;

                    case "PostgreSQL":
                        dbType = DatabaseType.PostgreSQL;
                        break;

                    case "Postgres9x":
                        dbType = DatabaseType.Postgres9x;
                        break;

                    case "SQLNative2008":
                        dbType = DatabaseType.SQLNative2008;
                        break;

                    case "SyBase":
                        dbType = DatabaseType.SyBase;
                        break;

                    case "MaxDB":
                        dbType = DatabaseType.MaxDB;
                        break;


                    case "SyBaseWithSrs":
                        dbType = DatabaseType.SyBaseWithSrs;
                        break;

                    case "MongoDB":
                        dbType = DatabaseType.MongoDB;
                        break;

                    case "SyBaseWithRsHADR":
                        dbType = DatabaseType.SyBaseWithRsHADR;
                        break;

                    case "HANADB":
                        dbType = DatabaseType.HANADB;
                        break;
                }
                CurrentEntity.DatabaseType = (DatabaseType)Enum.Parse(typeof(DatabaseType), dbType.ToString(), true);
                if (CurrentEntity.DatabaseType == DatabaseType.Oracle)
                {
                    CurrentEntity.DatabaseConnectivity = ddldbConnectivity.SelectedItem.Text;
                }
                else
                {
                    CurrentEntity.DatabaseConnectivity = null;
                }

                CurrentEntity.Version = ddlVersion.SelectedValue.ToString();
                CurrentEntity.Type = GetTypeofDatabase();
                CurrentEntity.ServerId = ddlServerId.SelectedValue.Contains("~") ? Convert.ToInt32(ddlServerId.SelectedValue.Split('~').GetValue(0)) : 0;
                //CurrentEntity.IsPartofRac = chkCluster.Checked;
                var chkRac = OracleDatabaseconfig1.FindControl("chkrac") as CheckBox;
                if (chkRac != null) CurrentEntity.IsPartofRac = chkRac.Checked;

                switch (ddlDBType.SelectedValue)
                {
                    case "Oracle":

                        BindOracleEntityValues();
                        break;

                    case "Sql":
                        BindSqlEntityValues();
                        break;

                    case "Exchange":
                        BindExchangeEntityValues();
                        break;

                    case "DB2":
                        BindDB2EntityValues();
                        break;

                    case "ExchangeDAG":
                        BindExchangeDAGEntityValues();
                        break;

                    case "MySQL":
                        BindMySqlEntityValues();
                        break;

                    case "PostgreSQL":
                        BindPostgreSqlEntityValues();
                        break;

                    case "Postgres9x":
                        BindPostgre9xEntityValues();
                        break;

                    case "SQLNative2008":
                        BindMSSQLEntityValues();
                        break;

                    case "SyBase":
                        BindSyBaseEntityValue();
                        break;


                    case "MaxDB":
                        BindMaxDBEntityValue();
                        break;

                    case "MongoDB":
                        BindMongoDBEntityValue();
                        break;


                    case "SyBaseWithSrs":
                        BindSyBaseWithSrsEntityValue();
                        break;

                    case "SyBaseWithRsHADR":
                        BindSyBaseWithRsHADREntityValue();
                        break;

                    case "HANADB":
                        BindHANADBEntityValue();
                        break;

                }
                _logger.Info("BuildEntities Method Execution Completed For DB Configuration.");

            }
            catch (Exception exc)
            {
                _logger.Error("Exception occurred in BuildEntities Method, Error Message " + exc.Message);
                if (exc.InnerException != null)
                {
                    _logger.Error("Exception occurred in BuildEntities Method Of DB Configuration, InnerException  " + exc.InnerException);
                    _logger.Error("Exception occurred in BuildEntities Method Of DB Configuration, InnerException Message " + exc.InnerException.Message);
                    _logger.Error("Exception occurred in BuildEntities Method Of DB Configuration,  StackTrace" + exc.InnerException.StackTrace);
                }
            }
        }


        public bool CheckValidDB()
        {
            try
            {
                _logger.Info("CheckValidDB Method Execution Start.");

                bool _isValid = true;
                var chkRac = OracleDatabaseconfig1.FindControl("chkrac") as CheckBox;

                if (chkRac.Checked)
                {
                    return true; ;
                }
                switch (ddlDBType.SelectedValue)
                {
                    case "Oracle":
                        var txtOracleSid = OracleDatabaseconfig1.FindControl("txtOracleSid") as TextBox;
                        var lblErr = OracleDatabaseconfig1.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(txtOracleSid.Text))
                        {
                            lblErr.Visible = true;
                            lblErr.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            lblErr.Visible = false;
                            lblErr.Text = "";

                        }


                        break;

                    case "Sql":
                        var txtdtSid = MSSQLdatabaseconfig1.FindControl("txtdtSID") as TextBox;
                        var sqllblErr = MSSQLdatabaseconfig1.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(txtdtSid.Text))
                        {
                            sqllblErr.Visible = true;
                            sqllblErr.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            sqllblErr.Visible = false;
                            sqllblErr.Text = "";

                        }
                        break;

                    case "Exchange":
                        var txtDbGroupName = ExchangeDatabaseConfig1.FindControl("txtDbGroupName") as TextBox;
                        var Exchangelbl = ExchangeDatabaseConfig1.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(txtDbGroupName.Text))
                        {
                            Exchangelbl.Visible = true;
                            Exchangelbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            Exchangelbl.Visible = false;
                            Exchangelbl.Text = "";

                        }
                        break;

                    case "DB2":
                        var txtDatabaseSid = Db2DatabaseConfig1.FindControl("txtDatabaseSid") as TextBox;
                        var DB2lbl = Db2DatabaseConfig1.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(txtDatabaseSid.Text))
                        {
                            DB2lbl.Visible = true;
                            DB2lbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            DB2lbl.Visible = false;
                            DB2lbl.Text = "";

                        }
                        break;

                    case "ExchangeDAG":
                        var txtMailboxDB = ExchangeDAGDatabaseConfig1.FindControl("txtMailboxDb") as TextBox;
                        var ExchangeDAGlbl = ExchangeDAGDatabaseConfig1.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(txtMailboxDB.Text))
                        {
                            ExchangeDAGlbl.Visible = true;
                            ExchangeDAGlbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            ExchangeDAGlbl.Visible = false;
                            ExchangeDAGlbl.Text = "";

                        }
                        break;

                    case "MySQL":
                        var txtdbName = MysqlDbConfig1.FindControl("txtdbName") as TextBox;
                        var MySQLlbl = MysqlDbConfig1.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(txtdbName.Text))
                        {
                            MySQLlbl.Visible = true;
                            MySQLlbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            MySQLlbl.Visible = false;
                            MySQLlbl.Text = "";

                        }
                        break;

                    case "PostgreSQL":
                        var txtPostgreSqldbName = PostgreSql1.FindControl("txtDatabaseName") as TextBox;
                        var PostgreSQLlbl = PostgreSql1.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(txtPostgreSqldbName.Text))
                        {
                            PostgreSQLlbl.Visible = true;
                            PostgreSQLlbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            PostgreSQLlbl.Visible = false;
                            PostgreSQLlbl.Text = "";

                        }
                        break;

                    case "Postgres9x":
                        var txtPostgre9xSqldbName = Postgre9x1.FindControl("txtDatabaseName") as TextBox;
                        var Postgres9xlbl = Postgre9x1.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(txtPostgre9xSqldbName.Text))
                        {
                            Postgres9xlbl.Visible = true;
                            Postgres9xlbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            Postgres9xlbl.Visible = false;
                            Postgres9xlbl.Text = "";

                        }
                        break;


                    case "SQLNative2008":
                        var SQLNative2008txtdbName = MSSQL2008.FindControl("txtDBName") as TextBox;
                        var SQLNative2008lbl = MSSQL2008.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(SQLNative2008txtdbName.Text))
                        {
                            SQLNative2008lbl.Visible = true;
                            SQLNative2008lbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            SQLNative2008lbl.Visible = false;
                            SQLNative2008lbl.Text = "";

                        }
                        break;

                    case "SyBase":
                        var SyBasetxtname = SybaseDBConfig.FindControl("txtname") as TextBox;
                        var SyBaselbl = SybaseDBConfig.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(SyBasetxtname.Text))
                        {
                            SyBaselbl.Visible = true;
                            SyBaselbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            SyBaselbl.Visible = false;
                            SyBaselbl.Text = "";

                        }
                        break;
                    case "MaxDB":
                        var MaxDbtxtname = MaxDBConfig.FindControl("txtMaxDBSId") as TextBox;
                        var Maxdblbl = MaxDBConfig.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(MaxDbtxtname.Text))
                        {
                            Maxdblbl.Visible = true;
                            Maxdblbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            Maxdblbl.Visible = false;
                            Maxdblbl.Text = "";

                        }
                        break;

                    //case "MongoDB":
                    //    var InstanceName = MongoDBConfig.FindControl("txtinstancename") as TextBox;
                    //    var lbl1 = MongoDBConfig.FindControl("lblErr") as Label;
                    //    if (!CheckValidation_licenseDB(InstanceName.Text))
                    //    {
                    //        lbl1.Visible = true;
                    //        lbl1.Text = "Invalid DataBase Licensekey";
                    //        _isValid = false;
                    //    }
                    //    else
                    //    {
                    //        lbl1.Visible = false;
                    //        lbl1.Text = "";

                    //    }
                    //    break;
                    case "SyBaseWithSrs":
                        var SyBasewithsrstxtname = SybaseWithSrsDBConfig.FindControl("txtname") as TextBox;
                        var SyBasewithsrslbl = SybaseWithSrsDBConfig.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(SyBasewithsrstxtname.Text))
                        {
                            SyBasewithsrslbl.Visible = true;
                            SyBasewithsrslbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            SyBasewithsrslbl.Visible = false;
                            SyBasewithsrslbl.Text = "";

                        }
                        break;


                    case "SyBaseWithRsHADR":
                        var SyBaseWithRsHADRtxtname = SybaseWithRsHadrConfig.FindControl("txtname") as TextBox;
                        var SyBaseWithRsHADRlbl = SybaseWithRsHadrConfig.FindControl("lblErr") as Label;
                        if (!CheckValidation_licenseDB(SyBaseWithRsHADRtxtname.Text))
                        {
                            SyBaseWithRsHADRlbl.Visible = true;
                            SyBaseWithRsHADRlbl.Text = "Invalid DataBase Licensekey";
                            _isValid = false;
                        }
                        else
                        {
                            SyBaseWithRsHADRlbl.Visible = false;
                            SyBaseWithRsHADRlbl.Text = "";

                        }
                        break;
                }

                _logger.Info("CheckValidDB Method Execution Completed.");

                return _isValid;
            }
            catch (Exception exc)
            {
                _logger.Error("Exception Occurred In CheckValidDB Method, Error Message " + exc.Message);
                if (exc.InnerException != null)
                {
                    _logger.Error("Exception Occurred In CheckValidDB Method, InnerException " + exc.InnerException);
                    _logger.Error("Exception Occurred In CheckValidDB Method, InnerException Message " + exc.InnerException.Message);
                    _logger.Error("Exception Occurred In CheckValidDB Method, StackTrace" + exc.InnerException.StackTrace);
                }
                return false;
            }
        }


        private string GetTypeofDatabase()
        {
            // return (string)ddlServerId.SelectedValue.Split('~').GetValue(1) == "PRDBServer" ? "PRDatabase" : "DRDatabase";
            if (ddlServerId.SelectedValue.Contains("~"))
            {
                if ((string)ddlServerId.SelectedValue.Split('~').GetValue(1) == "PRDBServer")
                    return "PRDatabase";
                else if ((string)ddlServerId.SelectedValue.Split('~').GetValue(1) == "DRDBServer")
                    return "DRDatabase";
                else if ((string)ddlServerId.SelectedValue.Split('~').GetValue(1) == "NearDRDBServer")
                    return "NearDRDBServer";
            }
            return string.Empty;
        }

        private void BindExchangeEntityValues()
        {
            var txtDbGroupName = ExchangeDatabaseConfig1.FindControl("txtDbGroupName") as TextBox;
            var txtMailboxDb = ExchangeDatabaseConfig1.FindControl("txtMailboxDb") as TextBox;

            if (txtDbGroupName != null && txtMailboxDb != null)
            {
                CurrentEntity.DatabaseExchange.StorageGroupName = txtDbGroupName.Text;
                CurrentEntity.DatabaseExchange.MailBoxDBName = txtMailboxDb.Text;
            }
        }

        private void BindExchangeDAGEntityValues()
        {
            var txtMailboxDB = ExchangeDAGDatabaseConfig1.FindControl("txtMailboxDb") as TextBox;
            var lblmsg = ExchangeDAGDatabaseConfig1.FindControl("lblMailboxName.Text") as Label;
            var ddlAuthType = ExchangeDAGDatabaseConfig1.FindControl("ddlAuthenticationType") as DropDownList;
            var ddlProtocolType = ExchangeDAGDatabaseConfig1.FindControl("ddlProtocolType") as DropDownList;

            if (txtMailboxDB != null)
            {
                CurrentEntity.DatabaseExcahngeDAG.MailBoxDBName = txtMailboxDB.Text;
                CurrentEntity.DatabaseExcahngeDAG.AuthenticationType = ddlAuthType.SelectedValue;
                CurrentEntity.DatabaseExcahngeDAG.ProtocolType = ddlProtocolType.SelectedValue;
            }
        }

        //private void BindOracleEntityValues()
        //{
        //    try
        //    {
        //        _logger.Info("BindOracleEntityValues Method Execution Start For Oracle DB.");

        //        var chkRac = OracleDatabaseconfig1.FindControl("chkrac") as CheckBox;


        //        if (chkRac.Checked)
        //        {
        //            var txtArchiveLogLocation = OracleDatabaseconfig1.FindControl("txtArchiveLogLocation") as TextBox;
        //            //var txtSpfileLocation = OracleDatabaseconfig1.FindControl("txtSpfileLocation") as TextBox;
        //            CurrentEntity.DatabaseType = DatabaseType.OracleRac;

        //        list = OracleDatabaseconfig1.FindControl("lvManual") as ListView;
        //        //_SelectedItems.Clear();
        //        //var databasenode = new DatabaseNodes();
        //        //var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;
        //        //var selectedItem = Utility.GetSelectedItem(cblstGroup);
        //        //if (selectedItem.Count != 0)
        //        //{
        //        //    foreach (var listItem in selectedItem)
        //        //    {
        //        //        if (listItem.Text == "ALL")
        //        //        {
        //        //            continue;
        //        //        }
        //        //       CurrentEntity.DatabaseNode.DatabaseId= CurrentEntity.DatabaseOracleRac.Id;
        //        //       CurrentEntity.DatabaseNode.NodeId = Convert.ToInt32(listItem.Value);
        //        //       // Facade.AddDatabaseNodes(databasnode);
        //        //    //   _SelectedItems.Add(listItem);
        //        //       _SelectedItems.Add(listItem);
        //        //    }
        //        //}
        //    }
        //    else
        //    {
        //        var txtOracleSid = OracleDatabaseconfig1.FindControl("txtOracleSid") as TextBox;
        //        var txtoracleUserName = OracleDatabaseconfig1.FindControl("txtUserName") as TextBox;
        //        var txtoraclePort = OracleDatabaseconfig1.FindControl("txtPort") as TextBox;
        //        var txtoraclePassword = OracleDatabaseconfig1.FindControl("txtPassword") as TextBox;
        //        //var _txtoraclePassword = OracleDatabaseconfig1.FindControl("txtPassword") as TextBox;
        //        //var txtoraclePassword = String.IsNullOrEmpty(_txtoraclePassword.Text) ? null : CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(_txtoraclePassword.Text, hdfStaticGuid.Value));
        //        var txtoracleAchive = OracleDatabaseconfig1.FindControl("txtAchive") as TextBox;
        //        var txtoracleHome = OracleDatabaseconfig1.FindControl("txtHome") as TextBox;
        //        var txtoracleRedo = OracleDatabaseconfig1.FindControl("txtRedo") as TextBox;
        //        var txtoracleASMGrid = OracleDatabaseconfig1.FindControl("txtASMGrid") as TextBox;
        //        var txtoracleInstanceName = OracleDatabaseconfig1.FindControl("txtInstanceName") as TextBox;


        //            CurrentEntity.DatabaseOracleRac.OracleHome = "";

        //            list = OracleDatabaseconfig1.FindControl("lvManual") as ListView;
        //            //_SelectedItems.Clear();
        //            //var databasenode = new DatabaseNodes();
        //            //var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;
        //            //var selectedItem = Utility.GetSelectedItem(cblstGroup);
        //            //if (selectedItem.Count != 0)
        //            //{
        //            //    foreach (var listItem in selectedItem)
        //            //    {
        //            //        if (listItem.Text == "ALL")
        //            //        {
        //            //            continue;
        //            //        }
        //            //       CurrentEntity.DatabaseNode.DatabaseId= CurrentEntity.DatabaseOracleRac.Id;
        //            //       CurrentEntity.DatabaseNode.NodeId = Convert.ToInt32(listItem.Value);
        //            //       // Facade.AddDatabaseNodes(databasnode);
        //            //    //   _SelectedItems.Add(listItem);
        //            //       _SelectedItems.Add(listItem);
        //            //    }
        //            //}
        //        }
        //        else
        //        {
        //            var txtOracleSid = OracleDatabaseconfig1.FindControl("txtOracleSid") as TextBox;
        //            var txtoracleUserName = OracleDatabaseconfig1.FindControl("txtUserName") as TextBox;
        //            var txtoraclePort = OracleDatabaseconfig1.FindControl("txtPort") as TextBox;
        //            var txtoraclePassword = OracleDatabaseconfig1.FindControl("txtPassword") as TextBox;
        //            var txtoracleAchive = OracleDatabaseconfig1.FindControl("txtAchive") as TextBox;
        //            var txtoracleHome = OracleDatabaseconfig1.FindControl("txtHome") as TextBox;
        //            var txtoracleRedo = OracleDatabaseconfig1.FindControl("txtRedo") as TextBox;
        //            var txtoracleASMGrid = OracleDatabaseconfig1.FindControl("txtASMGrid") as TextBox;
        //            var txtoracleInstanceName = OracleDatabaseconfig1.FindControl("txtInstanceName") as TextBox;

        //            var txtASMInstancename = OracleDatabaseconfig1.FindControl("txtASMInstancename") as TextBox;
        //            var chkIsASM = OracleDatabaseconfig1.FindControl("chkIsASM") as CheckBox;


        //            var txtASMUserName = OracleDatabaseconfig1.FindControl("txtASMUserName") as TextBox;
        //            var txtASMPassword = OracleDatabaseconfig1.FindControl("txtASMPassword") as TextBox;
        //            var txtASMPath = OracleDatabaseconfig1.FindControl("txtASMPath") as TextBox;

        //        if (txtOracleSid != null && txtoracleUserName != null && txtoraclePassword != null &&
        //            txtoraclePort != null && txtoracleAchive != null && txtoracleHome != null && txtoracleRedo != null)
        //        {
        //            CurrentEntity.DatabaseOracle.OracleSID = txtOracleSid.Text;
        //            CurrentEntity.DatabaseOracle.UserName = CryptographyHelper.Md5Encrypt(txtoracleUserName.Text);
        //           // CurrentEntity.DatabaseOracle.Password = CryptographyHelper.Md5Encrypt(txtoraclePassword.Text);
        //              CurrentEntity.DatabaseOracle.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtoraclePassword.Text, hdfStaticGuid.Value));
        //            // CurrentEntity.DatabaseOracle.Password = Utility.IsMD5EncryptedString(txtoraclePassword) ? txtoraclePassword  : CryptographyHelper.Md5Encrypt(txtoraclePassword);

        //            CurrentEntity.DatabaseOracle.Port = Convert.ToInt32(txtoraclePort.Text);
        //            CurrentEntity.DatabaseOracle.Archive = txtoracleAchive.Text == "" ? string.Empty : txtoracleAchive.Text;
        //            CurrentEntity.DatabaseOracle.Home = txtoracleHome.Text == "" ? string.Empty : txtoracleHome.Text;
        //            CurrentEntity.DatabaseOracle.Redo = txtoracleRedo.Text == "" ? string.Empty : txtoracleRedo.Text;
        //            //CurrentEntity.DatabaseOracle.ASMGrid = txtoracleASMGrid.Text == "" ? string.Empty : txtoracleASMGrid.Text;
        //            CurrentEntity.DatabaseOracle.InstanceName = txtoracleInstanceName.Text == "" ? string.Empty : txtoracleInstanceName.Text;
        //            CurrentEntity.DatabaseOracle.AsmInstanceName = txtASMInstancename.Text == "" ? string.Empty : txtASMInstancename.Text;
        //            CurrentEntity.DatabaseOracle.AsmUserName = CryptographyHelper.Md5Encrypt(txtASMUserName.Text);
        //            //CurrentEntity.DatabaseOracle.AsmPassword = Utility.IsMD5EncryptedString(txtASMPassword.Text) ? txtASMPassword.Text : CryptographyHelper.Md5Encrypt(txtASMPassword.Text);//txtASMPassword.Text == "" ? string.Empty : txtASMPassword.Text;
        //            CurrentEntity.DatabaseOracle.AsmPassword = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtASMPassword.Text, hdfStaticGuid.Value));
        //            CurrentEntity.DatabaseOracle.AsmPath = txtASMPath.Text == "" ? string.Empty : txtASMPath.Text;


        //            var ChkRACDBnodes = OracleDatabaseconfig1.FindControl("ChkRACDBnodes") as CheckBox;

        //            if (txtOracleSid != null && txtoracleUserName != null && txtoraclePassword != null &&
        //                txtoraclePort != null && txtoracleAchive != null && txtoracleHome != null && txtoracleRedo != null)
        //            {
        //                CurrentEntity.DatabaseOracle.OracleSID = txtOracleSid.Text;
        //                CurrentEntity.DatabaseOracle.UserName = CryptographyHelper.Md5Encrypt(txtoracleUserName.Text);
        //                //CurrentEntity.DatabaseOracle.Password = CryptographyHelper.Md5Encrypt(txtoraclePassword.Text);
        //                CurrentEntity.DatabaseOracle.Password = Utility.IsMD5EncryptedString(txtoraclePassword.Text) ? txtoraclePassword.Text : CryptographyHelper.Md5Encrypt(txtoraclePassword.Text);
        //                CurrentEntity.DatabaseOracle.Port = Convert.ToInt32(txtoraclePort.Text);
        //                CurrentEntity.DatabaseOracle.Archive = txtoracleAchive.Text == "" ? string.Empty : txtoracleAchive.Text;
        //                CurrentEntity.DatabaseOracle.Home = txtoracleHome.Text == "" ? string.Empty : txtoracleHome.Text;
        //                CurrentEntity.DatabaseOracle.Redo = txtoracleRedo.Text == "" ? string.Empty : txtoracleRedo.Text;
        //                //CurrentEntity.DatabaseOracle.ASMGrid = txtoracleASMGrid.Text == "" ? string.Empty : txtoracleASMGrid.Text;
        //                CurrentEntity.DatabaseOracle.InstanceName = txtoracleInstanceName.Text == "" ? string.Empty : txtoracleInstanceName.Text;
        //                CurrentEntity.DatabaseOracle.AsmInstanceName = txtASMInstancename.Text == "" ? string.Empty : txtASMInstancename.Text;
        //                CurrentEntity.DatabaseOracle.AsmUserName = CryptographyHelper.Md5Encrypt(txtASMUserName.Text);
        //                CurrentEntity.DatabaseOracle.AsmPassword = Utility.IsMD5EncryptedString(txtASMPassword.Text) ? txtASMPassword.Text : CryptographyHelper.Md5Encrypt(txtASMPassword.Text);//txtASMPassword.Text == "" ? string.Empty : txtASMPassword.Text;
        //                CurrentEntity.DatabaseOracle.AsmPath = txtASMPath.Text == "" ? string.Empty : txtASMPath.Text;

        //                if (chkIsASM.Checked == true)
        //                    CurrentEntity.DatabaseOracle.IsAsm = 1;

        //                else
        //                {
        //                    CurrentEntity.DatabaseOracle.IsAsm = 0;
        //                    CurrentEntity.DatabaseOracle.AsmInstanceName = "";

        //                    CurrentEntity.DatabaseOracle.AsmUserName = "";
        //                    CurrentEntity.DatabaseOracle.AsmPassword = "";
        //                    CurrentEntity.DatabaseOracle.AsmPath = "";

        //                }
        //                if (ChkRACDBnodes.Checked == true)
        //                {
        //                    CurrentEntity.Racdbnode = 1;
        //                }
        //                else
        //                {
        //                    CurrentEntity.Racdbnode = 0;
        //                }
        //            }
        //        }
        //        _logger.Info("BindOracleEntityValues Method Execution Completed For Oracle DB.");
        //    }
        //    }
        //    catch (Exception exc)
        //    {
        //        _logger.Error("Exception Occurred In BindOracleEntityValues Method, Error Message " + exc.Message);
        //        if (exc.InnerException != null)
        //        {
        //            _logger.Error("Exception Occurred In BindOracleEntityValues Method, InnerException " + exc.InnerException);
        //            _logger.Error("Exception Occurred In BindOracleEntityValues Method, InnerException Message " + exc.InnerException.Message);
        //            _logger.Error("Exception Occurred In BindOracleEntityValues Method,  StackTrace" + exc.InnerException.StackTrace);
        //        }
        //    }
        //}

        private void BindOracleEntityValues()
        {
            try
            {
                _logger.Info("BindOracleEntityValues Method Execution Start For Oracle DB.");

                var chkRac = OracleDatabaseconfig1.FindControl("chkrac") as CheckBox;

                if (chkRac.Checked)
                {
                    var txtArchiveLogLocation = OracleDatabaseconfig1.FindControl("txtArchiveLogLocation") as TextBox;
                    //var txtSpfileLocation = OracleDatabaseconfig1.FindControl("txtSpfileLocation") as TextBox;
                    CurrentEntity.DatabaseType = DatabaseType.OracleRac;

                    CurrentEntity.DatabaseOracleRac.OracleHome = "";

                    list = OracleDatabaseconfig1.FindControl("lvManual") as ListView;
                    //_SelectedItems.Clear();
                    //var databasenode = new DatabaseNodes();
                    //var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;
                    //var selectedItem = Utility.GetSelectedItem(cblstGroup);
                    //if (selectedItem.Count != 0)
                    //{
                    //    foreach (var listItem in selectedItem)
                    //    {
                    //        if (listItem.Text == "ALL")
                    //        {
                    //            continue;
                    //        }
                    //       CurrentEntity.DatabaseNode.DatabaseId= CurrentEntity.DatabaseOracleRac.Id;
                    //       CurrentEntity.DatabaseNode.NodeId = Convert.ToInt32(listItem.Value);
                    //       // Facade.AddDatabaseNodes(databasnode);
                    //    //   _SelectedItems.Add(listItem);
                    //       _SelectedItems.Add(listItem);
                    //    }
                    //}
                }
                else
                {
                    var txtOracleSid = OracleDatabaseconfig1.FindControl("txtOracleSid") as TextBox;
                    var txtoracleUserName = OracleDatabaseconfig1.FindControl("txtUserName") as TextBox;
                    var txtoraclePort = OracleDatabaseconfig1.FindControl("txtPort") as TextBox;
                    var txtoraclePassword = OracleDatabaseconfig1.FindControl("txtPassword") as TextBox;
                    //var _txtoraclePassword = OracleDatabaseconfig1.FindControl("txtPassword") as TextBox;
                    //var txtoraclePassword = String.IsNullOrEmpty(_txtoraclePassword.Text) ? null : CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(_txtoraclePassword.Text, hdfStaticGuid.Value));
                    var txtoracleAchive = OracleDatabaseconfig1.FindControl("txtAchive") as TextBox;
                    var txtoracleHome = OracleDatabaseconfig1.FindControl("txtHome") as TextBox;
                    var txtoracleRedo = OracleDatabaseconfig1.FindControl("txtRedo") as TextBox;
                    var txtoracleASMGrid = OracleDatabaseconfig1.FindControl("txtASMGrid") as TextBox;
                    var txtoracleInstanceName = OracleDatabaseconfig1.FindControl("txtInstanceName") as TextBox;

                    var txtASMInstancename = OracleDatabaseconfig1.FindControl("txtASMInstancename") as TextBox;
                    var chkIsASM = OracleDatabaseconfig1.FindControl("chkIsASM") as CheckBox;

                    var txtASMUserName = OracleDatabaseconfig1.FindControl("txtASMUserName") as TextBox;
                    var txtASMPassword = OracleDatabaseconfig1.FindControl("txtASMPassword") as TextBox;
                    var txtASMPath = OracleDatabaseconfig1.FindControl("txtASMPath") as TextBox;


                    var txtPreExecution = OracleDatabaseconfig1.FindControl("txtPreExecution") as TextBox;
                    var chkPreExecution = OracleDatabaseconfig1.FindControl("chkPreExecution") as CheckBox;

                    var txtDbAuthData = OracleDatabaseconfig1.FindControl("txtdata") as TextBox;
                    var ddlAuthData = OracleDatabaseconfig1.FindControl("ddlAuthsql") as DropDownList;

                    var ddlcrendetial1 = OracleDatabaseconfig1.FindControl("ddlcrendetial1") as DropDownList;

                    var txtRole1 = OracleDatabaseconfig1.FindControl("txtRole1") as TextBox;
                    var ddlRole1 = OracleDatabaseconfig1.FindControl("ddlRole1") as DropDownList;
                    SubstituteAuthentication dbauthenticate = new SubstituteAuthentication();

                    var ChkRACDBnodes = OracleDatabaseconfig1.FindControl("ChkRACDBnodes") as CheckBox;

                    if (txtOracleSid != null && txtoracleUserName != null && txtoraclePassword != null &&
                        txtoraclePort != null && txtoracleAchive != null && txtoracleHome != null && txtoracleRedo != null)
                    {
                        CurrentEntity.DatabaseOracle.OracleSID = txtOracleSid.Text;
                        CurrentEntity.DatabaseOracle.UserName = CryptographyHelper.Md5Encrypt(txtoracleUserName.Text);
                        // CurrentEntity.DatabaseOracle.Password = CryptographyHelper.Md5Encrypt(txtoraclePassword.Text);
                        CurrentEntity.DatabaseOracle.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtoraclePassword.Text, hdfStaticGuid.Value));
                        // CurrentEntity.DatabaseOracle.Password = Utility.IsMD5EncryptedString(txtoraclePassword) ? txtoraclePassword  : CryptographyHelper.Md5Encrypt(txtoraclePassword);

                        CurrentEntity.DatabaseOracle.Port = Convert.ToInt32(txtoraclePort.Text);
                        CurrentEntity.DatabaseOracle.Archive = txtoracleAchive.Text == "" ? string.Empty : txtoracleAchive.Text;
                        CurrentEntity.DatabaseOracle.Home = txtoracleHome.Text == "" ? string.Empty : txtoracleHome.Text;
                        CurrentEntity.DatabaseOracle.Redo = txtoracleRedo.Text == "" ? string.Empty : txtoracleRedo.Text;
                        //CurrentEntity.DatabaseOracle.ASMGrid = txtoracleASMGrid.Text == "" ? string.Empty : txtoracleASMGrid.Text;
                        CurrentEntity.DatabaseOracle.InstanceName = txtoracleInstanceName.Text == "" ? string.Empty : txtoracleInstanceName.Text;
                        CurrentEntity.DatabaseOracle.AsmInstanceName = txtASMInstancename.Text == "" ? string.Empty : txtASMInstancename.Text;
                        CurrentEntity.DatabaseOracle.AsmUserName = CryptographyHelper.Md5Encrypt(txtASMUserName.Text);
                        //CurrentEntity.DatabaseOracle.AsmPassword = Utility.IsMD5EncryptedString(txtASMPassword.Text) ? txtASMPassword.Text : CryptographyHelper.Md5Encrypt(txtASMPassword.Text);//txtASMPassword.Text == "" ? string.Empty : txtASMPassword.Text;
                        CurrentEntity.DatabaseOracle.AsmPassword = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtASMPassword.Text, hdfStaticGuid.Value));
                        CurrentEntity.DatabaseOracle.AsmPath = txtASMPath.Text == "" ? string.Empty : txtASMPath.Text;

                        if (chkPreExecution != null)
                        {
                            if (chkPreExecution.Checked)
                            { CurrentEntity.DatabaseOracle.PreExecutionCommand = txtPreExecution.Text == "" ? string.Empty : txtPreExecution.Text; }
                            else
                            { CurrentEntity.DatabaseOracle.PreExecutionCommand = string.Empty; }
                        }

                        if (chkIsASM.Checked == true)
                            CurrentEntity.DatabaseOracle.IsAsm = 1;

                        else
                        {
                            CurrentEntity.DatabaseOracle.IsAsm = 0;
                            CurrentEntity.DatabaseOracle.AsmInstanceName = "";

                            CurrentEntity.DatabaseOracle.AsmUserName = "";
                            CurrentEntity.DatabaseOracle.AsmPassword = "";
                            CurrentEntity.DatabaseOracle.AsmPath = "";

                        }
                        if (ChkRACDBnodes.Checked == true)
                        {
                            CurrentEntity.Racdbnode = 1;
                        }
                        else
                        {
                            CurrentEntity.Racdbnode = 0;
                        }

                        dbauthenticate.Sqlplus = txtDbAuthData.Text;
                        //}

                        dbauthenticate.Credential = Convert.ToInt32(ddlcrendetial1.SelectedValue);

                        //if (ddlRole1.SelectedValue != "00")
                        //{
                        //    dbauthenticate.Role = ddlRole1.SelectedItem.Text;
                        //}
                        //else
                        //{
                        dbauthenticate.Role = txtRole1.Text;
                        CurrentEntity.SubstituteAuthentication1.Id = dbauthenticate.Id;
                        CurrentEntity.SubstituteAuthentication1.Sqlplus = dbauthenticate.Sqlplus;
                        CurrentEntity.SubstituteAuthentication1.Credential = dbauthenticate.Credential;
                        CurrentEntity.SubstituteAuthentication1.Role = dbauthenticate.Role;
                    }
                }
                _logger.Info("BindOracleEntityValues Method Execution Completed For Oracle DB.");
            }
            catch (Exception exc)
            {
                _logger.Error("Exception Occurred In BindOracleEntityValues Method, Error Message " + exc.Message);
                if (exc.InnerException != null)
                {
                    _logger.Error("Exception Occurred In BindOracleEntityValues Method, InnerException " + exc.InnerException);
                    _logger.Error("Exception Occurred In BindOracleEntityValues Method, InnerException Message " + exc.InnerException.Message);
                    _logger.Error("Exception Occurred In BindOracleEntityValues Method,  StackTrace" + exc.InnerException.StackTrace);
                }
            }
        }

        private void BindSqlEntityValues()
        {
            var txtdtSid = MSSQLdatabaseconfig1.FindControl("txtdtSID") as TextBox;
            var txtUserName = MSSQLdatabaseconfig1.FindControl("txtUserName") as TextBox;
            var txtPort = MSSQLdatabaseconfig1.FindControl("txtPort") as TextBox;
            var txtPassword = MSSQLdatabaseconfig1.FindControl("txtPassword") as TextBox;
            var rbtnListAuthenticationMode = MSSQLdatabaseconfig1.FindControl("rbtnListAuthenticationMode") as RadioButtonList;
            var txtinstanceName = MSSQLdatabaseconfig1.FindControl("txtinstaname") as TextBox;
            var chkinsatnce = MSSQLdatabaseconfig1.FindControl("chkconfig") as CheckBox;
            var dvinstance = MSSQLdatabaseconfig1.FindControl("divinstance") as Control;
            var txtDataFileLocation = MSSQLdatabaseconfig1.FindControl("txtDataFileLocation") as TextBox;
            var txtTransactionLocation = MSSQLdatabaseconfig1.FindControl("txtTransactionLocation") as TextBox;
            var txtUndofile = MSSQLdatabaseconfig1.FindControl("txtUndofile") as TextBox;

            var txtBackupRestorePath = MSSQLdatabaseconfig1.FindControl("txtBackupRestorePath") as TextBox;
            var txtNetworkSharedPath = MSSQLdatabaseconfig1.FindControl("txtNetworkSharedPath") as TextBox;
            var chkSSOEnabled = MSSQLdatabaseconfig1.FindControl("ChkSSOEnable") as CheckBox;
            var ddlSSOProfile = MSSQLdatabaseconfig1.FindControl("ddlSSOProfile") as DropDownList;
            var pnlsso = MSSQLdatabaseconfig1.FindControl("pnlSSoProfilrDrp") as Panel;

            if (txtdtSid != null && ddlServerId != null && txtUserName != null && txtPassword != null && txtPort != null
                && rbtnListAuthenticationMode != null && txtDataFileLocation != null && txtTransactionLocation != null &&
                txtUndofile != null && txtBackupRestorePath != null && txtNetworkSharedPath != null)
            {
                CurrentEntity.DatabaseSql.DatabaseSID = txtdtSid.Text;
                CurrentEntity.DatabaseSql.UserName = CryptographyHelper.Md5Encrypt(txtUserName.Text);
                //CurrentEntity.DatabaseSql.Password = CryptographyHelper.Md5Encrypt(txtPassword.Text);
                // CurrentEntity.DatabaseSql.Password = Utility.IsMD5EncryptedString(txtPassword.Text) ? txtPassword.Text : CryptographyHelper.Md5Encrypt(txtPassword.Text);
                CurrentEntity.DatabaseSql.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPassword.Text, hdfStaticGuid.Value));
                CurrentEntity.DatabaseSql.Port = Convert.ToInt32(txtPort.Text);
                CurrentEntity.DatabaseSql.AuthenticationMode = rbtnListAuthenticationMode.SelectedValue == "Windows" ? SqlAuthenticateType.Windows : SqlAuthenticateType.SqlServer;
                if (chkinsatnce.Checked == false)
                {
                    CurrentEntity.DatabaseSql.InstanceName = string.Empty;
                    dvinstance.Visible = false;


                }
                else
                {
                    chkinsatnce.Checked = true;
                    dvinstance.Visible = true;
                    CurrentEntity.DatabaseSql.InstanceName = txtinstanceName.Text;
                }

                if (chkSSOEnabled.Checked == true)
                {
                    CurrentEntity.DatabaseSql.SSOEnabled = 1;
                    pnlsso.Visible = true;
                    CurrentEntity.DatabaseSql.SSOProfileId = Convert.ToInt32(ddlSSOProfile.SelectedValue);

                    //dvinstance.Visible = true;
                    //chkinsatnce.Checked = true;
                    //CurrentEntity.DatabaseSql.InstanceName = txtinstanceName.Text;
                    //CurrentEntity.DatabaseSql.AuthenticationMode = rbtnListAuthenticationMode.SelectedValue == "Windows" ? SqlAuthenticateType.Windows : SqlAuthenticateType.SqlServer;

                }
                else
                {
                    CurrentEntity.DatabaseSql.SSOEnabled = 0;
                    pnlsso.Visible = false;
                    ddlSSOProfile.Visible = false;
                    //  dvinstance.Visible = false;
                    // chkinsatnce.Checked = false;
                    // CurrentEntity.Databasemssql.InstanceName = string.Empty;
                    // CurrentEntity.Databasemssql.AuthenticationMode = rbtnListAuthenticationMode.SelectedValue == "Windows" ? SqlAuthenticateType.Windows : SqlAuthenticateType.SqlServer;
                }

                CurrentEntity.DatabaseSql.DataFilePath = txtDataFileLocation.Text;
                CurrentEntity.DatabaseSql.TransLogPath = txtTransactionLocation.Text;
                CurrentEntity.DatabaseSql.UndoFilePath = txtUndofile.Text;
                CurrentEntity.DatabaseSql.BackupRestorePath = txtBackupRestorePath.Text;
                CurrentEntity.DatabaseSql.NetworkSharedPath = txtNetworkSharedPath.Text;

            }
        }

        protected void BindDB2EntityValues()
        {
            var txtDatabaseSid = Db2DatabaseConfig1.FindControl("txtDatabaseSid") as TextBox;
            var txtDb2UserName = Db2DatabaseConfig1.FindControl("txtUserName") as TextBox;
            var txtDb2Port = Db2DatabaseConfig1.FindControl("txtPort") as TextBox;
            var txtDb2Password = Db2DatabaseConfig1.FindControl("txtPassword") as TextBox;
            if (txtDatabaseSid != null && txtDb2UserName != null && txtDb2Port != null && txtDb2Password != null)
            {
                CurrentEntity.DatabaseDb2.DatabaseSID = txtDatabaseSid.Text;
                CurrentEntity.DatabaseDb2.UserName = CryptographyHelper.Md5Encrypt(txtDb2UserName.Text);
                //CurrentEntity.DatabaseDb2.Password = CryptographyHelper.Md5Encrypt(txtDb2Password.Text);
                //CurrentEntity.DatabaseDb2.Password = Utility.IsMD5EncryptedString(txtDb2Password.Text) ? txtDb2Password.Text : CryptographyHelper.Md5Encrypt(txtDb2Password.Text);
                CurrentEntity.DatabaseDb2.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtDb2Password.Text, hdfStaticGuid.Value));
                CurrentEntity.DatabaseDb2.Port = Convert.ToInt32(txtDb2Port.Text);
            }
        }

        protected void BindMySqlEntityValues()
        {
            var txtdbName = MysqlDbConfig1.FindControl("txtdbName") as TextBox;
            var txtMysqlusername = MysqlDbConfig1.FindControl("txtUserName") as TextBox;
            var txtMysqlpassword = MysqlDbConfig1.FindControl("txtPassword") as TextBox;
            var txtMysqlport = MysqlDbConfig1.FindControl("txtPort") as TextBox;

            if (txtdbName != null && txtMysqlusername != null && txtMysqlpassword != null && txtMysqlport != null)
            {
                CurrentEntity.DatabaseMySql.DatabaseID = txtdbName.Text;
                CurrentEntity.DatabaseMySql.UserName = CryptographyHelper.Md5Encrypt(txtMysqlusername.Text);
                //CurrentEntity.DatabaseMySql.Password = CryptographyHelper.Md5Encrypt(txtMysqlpassword.Text);
                // CurrentEntity.DatabaseMySql.Password = Utility.IsMD5EncryptedString(txtMysqlpassword.Text) ? txtMysqlpassword.Text : CryptographyHelper.Md5Encrypt(txtMysqlpassword.Text);
                CurrentEntity.DatabaseMySql.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtMysqlpassword.Text, hdfStaticGuid.Value));
                CurrentEntity.DatabaseMySql.Port = Convert.ToInt32(txtMysqlport.Text);
            }
        }

        protected void BindPostgreSqlEntityValues()
        {
            var txtPostgreSqldbName = PostgreSql1.FindControl("txtDatabaseName") as TextBox;
            var txtPostgreSqlusername = PostgreSql1.FindControl("txtUserName") as TextBox;
            var txPostgreSqlpassword = PostgreSql1.FindControl("txtPassword") as TextBox;
            var txPostgreSqlport = PostgreSql1.FindControl("txtPort") as TextBox;
            var txPostgreArchiveLog = PostgreSql1.FindControl("txtArchive") as TextBox;

            if (txtPostgreSqldbName != null && txtPostgreSqlusername != null && txPostgreSqlpassword != null && txPostgreSqlport != null && txPostgreArchiveLog != null)
            {
                CurrentEntity.PostgreSql.DatabaseName = txtPostgreSqldbName.Text;
                CurrentEntity.PostgreSql.UserName = CryptographyHelper.Md5Encrypt(txtPostgreSqlusername.Text);
                //CurrentEntity.PostgreSql.Password = CryptographyHelper.Md5Encrypt(txPostgreSqlpassword.Text);
                //CurrentEntity.PostgreSql.Password = Utility.IsMD5EncryptedString(txPostgreSqlpassword.Text) ? txPostgreSqlpassword.Text : CryptographyHelper.Md5Encrypt(txPostgreSqlpassword.Text);
                CurrentEntity.PostgreSql.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txPostgreSqlpassword.Text, hdfStaticGuid.Value));
                CurrentEntity.PostgreSql.Port = Convert.ToInt32(txPostgreSqlport.Text);
                CurrentEntity.PostgreSql.ArchiveLog = txPostgreArchiveLog.Text;
            }
        }

        // added by Vishal Shinde 01-12-2014
        protected void BindPostgre9xEntityValues()
        {
            var txtPostgreSqldbName = Postgre9x1.FindControl("txtDatabaseName") as TextBox;
            var txtPostgreSqlusername = Postgre9x1.FindControl("txtUserName") as TextBox;
            var txPostgreSqlpassword = Postgre9x1.FindControl("txtPassword") as TextBox;
            var txPostgreSqlport = Postgre9x1.FindControl("txtPort") as TextBox;
            var txPostgreDBDataDir = Postgre9x1.FindControl("txtDBDataDirectory") as TextBox;
            var txPostgreDBBinDir = Postgre9x1.FindControl("txtDBBinDirectory") as TextBox;
            var txPostgreSULogin = Postgre9x1.FindControl("txtSULogin") as TextBox;
            var txServiceName = Postgre9x1.FindControl("txtServiceName") as TextBox;

            if (txtPostgreSqldbName != null && txtPostgreSqlusername != null && txPostgreSqlpassword != null && txPostgreSqlport != null && txPostgreDBDataDir != null && txPostgreDBBinDir != null && txPostgreSULogin != null && txServiceName != null)
            {
                CurrentEntity.DatabasePostgre9x.DatabaseName = txtPostgreSqldbName.Text;
                CurrentEntity.DatabasePostgre9x.UserName = CryptographyHelper.Md5Encrypt(txtPostgreSqlusername.Text);
                //CurrentEntity.PostgreSql.Password = CryptographyHelper.Md5Encrypt(txPostgreSqlpassword.Text);
                //CurrentEntity.DatabasePostgre9x.Password = Utility.IsMD5EncryptedString(txPostgreSqlpassword.Text) ? txPostgreSqlpassword.Text : CryptographyHelper.Md5Encrypt(txPostgreSqlpassword.Text);
                CurrentEntity.DatabasePostgre9x.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txPostgreSqlpassword.Text, hdfStaticGuid.Value));
                CurrentEntity.DatabasePostgre9x.Port = Convert.ToInt32(txPostgreSqlport.Text);
                CurrentEntity.DatabasePostgre9x.DBDataDirectory = txPostgreDBDataDir.Text;
                CurrentEntity.DatabasePostgre9x.DBbinDirectory = txPostgreDBBinDir.Text;
                CurrentEntity.DatabasePostgre9x.SULogin = txPostgreSULogin.Text;
                CurrentDatabase.DatabasePostgre9x.ServiceName = txServiceName.Text;
            }
        }

        protected void BindMSSQLEntityValues()
        {
            var txtdbName = MSSQL2008.FindControl("txtDBName") as TextBox;
            var txtinstanceName = MSSQL2008.FindControl("txtinstaname") as TextBox;
            var chkinsatnce = MSSQL2008.FindControl("chkconfig") as CheckBox;
            var dvinstance = MSSQL2008.FindControl("divinstance") as Control;
            var txtUserName = MSSQL2008.FindControl("txtUserName") as TextBox;
            var txtpwd = MSSQL2008.FindControl("txtPassword") as TextBox;
            var txtport = MSSQL2008.FindControl("txtPort") as TextBox;
            var chkSSOEnabled = MSSQL2008.FindControl("ChkSSOEnable") as CheckBox;
            var ddlSSOProfile = MSSQL2008.FindControl("ddlSSOProfile") as DropDownList;
            var rbtnListAuthenticationMode = MSSQL2008.FindControl("rbtnListAuthenticationMode") as RadioButtonList;
            var pnlsso = MSSQL2008.FindControl("pnlSSoProfilrDrp") as Panel;
            var username = MSSQL2008.FindControl("dvusername") as HtmlGenericControl;
            var password = MSSQL2008.FindControl("dvpassword") as HtmlGenericControl;

            if (txtdbName != null && txtUserName != null && txtpwd != null && txtport != null)
            {
                if (chkinsatnce.Checked == false)
                {
                    CurrentEntity.Databasemssql.InstanceName = string.Empty;
                    dvinstance.Visible = false;


                }
                else
                {
                    dvinstance.Visible = true;
                    chkinsatnce.Checked = true;
                    CurrentEntity.Databasemssql.InstanceName = txtinstanceName.Text;
                }
                if (chkSSOEnabled.Checked == true)
                {
                    CurrentEntity.Databasemssql.SSOEnabled = 1;
                    pnlsso.Visible = true;
                    CurrentEntity.Databasemssql.SSOProfileId = Convert.ToInt32(ddlSSOProfile.SelectedValue);

                    //dvinstance.Visible = true;
                    //chkinsatnce.Checked = true;
                    //CurrentEntity.Databasemssql.InstanceName = txtinstanceName.Text;
                    CurrentEntity.Databasemssql.AuthenticationMode = rbtnListAuthenticationMode.SelectedValue == "Windows" ? SqlAuthenticateType.Windows : SqlAuthenticateType.SqlServer;
                    username.Visible = true;
                    password.Visible = true;
                }
                else
                {
                    CurrentEntity.Databasemssql.SSOEnabled = 0;
                    pnlsso.Visible = false;
                    ddlSSOProfile.Visible = false;
                    username.Visible = false;
                    password.Visible = false;
                    //  dvinstance.Visible = false;
                    // chkinsatnce.Checked = false;
                    // CurrentEntity.Databasemssql.InstanceName = string.Empty;
                    // CurrentEntity.Databasemssql.AuthenticationMode = rbtnListAuthenticationMode.SelectedValue == "Windows" ? SqlAuthenticateType.Windows : SqlAuthenticateType.SqlServer;
                }
                if (chkinsatnce.Checked == true)
                {
                    dvinstance.Visible = true;
                    chkinsatnce.Checked = true;
                    CurrentEntity.Databasemssql.InstanceName = txtinstanceName.Text;
                }
                CurrentEntity.Databasemssql.DatabaseName = txtdbName.Text;
                CurrentEntity.Databasemssql.Port = Convert.ToInt32(txtport.Text);
                CurrentEntity.Databasemssql.UserName = txtUserName.Text == "" ? "NA" : CryptographyHelper.Md5Encrypt(txtUserName.Text);
                //CurrentEntity.Databasemssql.Password = txtpwd.Text == "" ? "NA" : CryptographyHelper.Md5Encrypt(txtpwd.Text);
                // CurrentEntity.Databasemssql.Password = Utility.IsMD5EncryptedString(txtpwd.Text) ? txtpwd.Text : CryptographyHelper.Md5Encrypt(txtpwd.Text);
                // CurrentEntity.Databasemssql.Password = txtpwd.Text == string.Empty ? string.Empty : CryptographyHelper.Md5Encrypt(txtpwd.Text);

                CurrentEntity.Databasemssql.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtpwd.Text, hdfStaticGuid.Value));
                CurrentEntity.Databasemssql.AuthenticationMode = rbtnListAuthenticationMode.SelectedValue == "Windows" ? SqlAuthenticateType.Windows : SqlAuthenticateType.SqlServer;
            }
        }

        // Added By Meenakshi Patil
        protected void BindSyBaseEntityValue()
        {
            var txtname = SybaseDBConfig.FindControl("txtname") as TextBox;
            var txtUserName = SybaseDBConfig.FindControl("txtUserName") as TextBox;
            var txtPort = SybaseDBConfig.FindControl("txtPort") as TextBox;
            var txtPassword = SybaseDBConfig.FindControl("txtPassword") as TextBox;
            var txtPRTransFileLocation = SybaseDBConfig.FindControl("txtPRTransFileLocation") as TextBox;
            var txtdtServername = SybaseDBConfig.FindControl("txtdtServername") as TextBox;
            var txtBackupName = SybaseDBConfig.FindControl("txtBackupName") as TextBox;
            var chkaccesstransactiondump = SybaseDBConfig.FindControl("chkaccesstransactiondump") as CheckBox;
            var txtEnvrnmentPath = SybaseDBConfig.FindControl("txtEnvrnmentPath") as TextBox;
            if (chkaccesstransactiondump.Checked)
            {
                CurrentEntity.Databasesybase.IsStandByaccess = 1;

            }
            else
            {
                CurrentEntity.Databasesybase.IsStandByaccess = 0;
            }
            if (txtname != null && txtUserName != null && txtPort != null && txtPassword != null && txtPRTransFileLocation != null && txtdtServername != null && txtBackupName != null && chkaccesstransactiondump != null && txtEnvrnmentPath != null)
            {
                CurrentEntity.Databasesybase.DatabaseSID = txtname.Text;
                CurrentEntity.Databasesybase.UserName = CryptographyHelper.Md5Encrypt(txtUserName.Text);
                // CurrentEntity.Databasesybase.Password = Utility.IsMD5EncryptedString(txtPassword.Text) ? txtPassword.Text : CryptographyHelper.Md5Encrypt(txtPassword.Text);
                CurrentEntity.Databasesybase.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPassword.Text, hdfStaticGuid.Value));
                CurrentEntity.Databasesybase.Port = Convert.ToInt32(txtPort.Text);
                CurrentEntity.Databasesybase.TransactionFileLocation = txtPRTransFileLocation.Text;
                CurrentEntity.Databasesybase.SybaseDataServerName = txtdtServername.Text;

                CurrentEntity.Databasesybase.SybaseBackupServer = txtBackupName.Text;

                CurrentEntity.Databasesybase.SybaseBackupServer = txtBackupName.Text;
                //CurrentEntity.Databasesybase.EnvironmentPath = txtEnvrnmentPath.Text;

            }
        }

        // Added By Uma Mehavarnan on 06-May-2016
        protected void BindMaxDBEntityValue()
        {
            var txtMaxDBSId = MaxDBConfig.FindControl("txtMaxDBSId") as TextBox;
            var txtInstanceName = MaxDBConfig.FindControl("txtInstanceName") as TextBox;
            var txtUserName = MaxDBConfig.FindControl("txtUserName") as TextBox;
            var txtPassword = MaxDBConfig.FindControl("txtPassword") as TextBox;
            var txtPort = MaxDBConfig.FindControl("txtPort") as TextBox;
            var txtInstallationPath = MaxDBConfig.FindControl("txtInstallationPath") as TextBox;
            var txtMediumName = MaxDBConfig.FindControl("txtMediumName") as TextBox;
            var txtLogfileName = MaxDBConfig.FindControl("txtLogfileName") as TextBox;
            var txtLogPath = MaxDBConfig.FindControl("txtLogPath") as TextBox;

            if (txtMaxDBSId != null && txtInstanceName != null && txtUserName != null && txtPassword != null && txtPort != null && txtInstallationPath != null && txtMediumName != null && txtLogfileName != null)
            {
                CurrentEntity.DatabasemaxDB.DatabaseSID = txtMaxDBSId.Text;
                CurrentEntity.DatabasemaxDB.InstanceName = txtInstanceName.Text;
                CurrentEntity.DatabasemaxDB.UserName = CryptographyHelper.Md5Encrypt(txtUserName.Text);
                //CurrentEntity.DatabasemaxDB.Password = Utility.IsMD5EncryptedString(txtPassword.Text) ? txtPassword.Text : CryptographyHelper.Md5Encrypt(txtPassword.Text);

                CurrentEntity.DatabasemaxDB.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPassword.Text, hdfStaticGuid.Value));
                CurrentEntity.DatabasemaxDB.Port = Convert.ToInt32(txtPort.Text);
                CurrentEntity.DatabasemaxDB.InstallationPath = txtInstallationPath.Text;
                CurrentEntity.DatabasemaxDB.MediumName = txtMediumName.Text;
                CurrentEntity.DatabasemaxDB.LogfileName = txtLogfileName.Text;
                CurrentEntity.DatabasemaxDB.LogPath = txtLogPath.Text;
            }
        }

        //Added By Sumit Wakade 26/04/2016
        protected void BindSyBaseWithSrsEntityValue()
        {
            var txtname = SybaseWithSrsDBConfig.FindControl("txtname") as TextBox;
            var txtUserName = SybaseWithSrsDBConfig.FindControl("txtUserName") as TextBox;
            var txtPort = SybaseWithSrsDBConfig.FindControl("txtPort") as TextBox;
            var txtPassword = SybaseWithSrsDBConfig.FindControl("txtPassword") as TextBox;
            var txtdtServername = SybaseWithSrsDBConfig.FindControl("txtdtServername") as TextBox;
            var txtBackupName = SybaseWithSrsDBConfig.FindControl("txtBackupName") as TextBox;
            var txtEnvpath = SybaseWithSrsDBConfig.FindControl("txtEnvPath") as TextBox;
            if (txtname != null && txtUserName != null && txtPort != null && txtPassword != null && txtEnvpath != null && txtdtServername != null && txtBackupName != null)
            {
                CurrentEntity.DatabasesybaseWithSrs.DatabaseSID = txtname.Text;
                CurrentEntity.DatabasesybaseWithSrs.UserName = CryptographyHelper.Md5Encrypt(txtUserName.Text);
                //CurrentEntity.DatabasesybaseWithSrs.Password = Utility.IsMD5EncryptedString(txtPassword.Text) ? txtPassword.Text : CryptographyHelper.Md5Encrypt(txtPassword.Text);
                CurrentEntity.DatabasesybaseWithSrs.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPassword.Text, hdfStaticGuid.Value));
                CurrentEntity.DatabasesybaseWithSrs.Port = Convert.ToInt32(txtPort.Text);
                CurrentEntity.DatabasesybaseWithSrs.SybaseEnv_Path = txtEnvpath.Text;
                CurrentEntity.DatabasesybaseWithSrs.SybaseDataServerName = txtdtServername.Text;
                CurrentEntity.DatabasesybaseWithSrs.SybaseBackupServer = txtBackupName.Text;


            }


        }

        protected void BindMongoDBEntityValue()
        {
            var txtinstallationpath = MongoDBConfig.FindControl("txtinstallationpath") as TextBox;
            var txtinstancename = MongoDBConfig.FindControl("txtinstancename") as TextBox;
            var txtport = MongoDBConfig.FindControl("txtport") as TextBox;
            var txtusername = MongoDBConfig.FindControl("txtusername") as TextBox;
            var txtpassword = MongoDBConfig.FindControl("txtpassword") as TextBox;
            var txtbinarylocation = MongoDBConfig.FindControl("txtbinarylocation") as TextBox;


            if (txtinstallationpath != null && txtinstancename != null && txtport != null && txtusername != null && txtpassword != null)
            {
                CurrentEntity.mongodb.InstallationPath = txtinstallationpath.Text;
                CurrentEntity.mongodb.InstanceName = txtinstancename.Text;
                CurrentEntity.mongodb.Port = Convert.ToInt32(txtport.Text);
                CurrentEntity.mongodb.UserName = CryptographyHelper.Md5Encrypt(txtusername.Text);
                //CurrentEntity.mongodb.Password = Utility.IsMD5EncryptedString(txtpassword.Text) ? txtpassword.Text : CryptographyHelper.Md5Encrypt(txtpassword.Text);
                CurrentEntity.mongodb.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtpassword.Text, hdfStaticGuid.Value));
                CurrentEntity.mongodb.BinaryLocation = txtbinarylocation.Text;

            }
        }

        protected void BindSyBaseWithRsHADREntityValue()
        {
            var txtServerPort = SybaseWithRsHadrConfig.FindControl("txtServerPort") as TextBox;
            var txtname = SybaseWithRsHadrConfig.FindControl("txtname") as TextBox;
            var txtUserName = SybaseWithRsHadrConfig.FindControl("txtUserName") as TextBox;
            var txtPassword = SybaseWithRsHadrConfig.FindControl("txtPassword") as TextBox;
            var txtdtServername = SybaseWithRsHadrConfig.FindControl("txtdtServername") as TextBox;
            var txtBackupName = SybaseWithRsHadrConfig.FindControl("txtBackupName") as TextBox;
            var txtEnvpath = SybaseWithRsHadrConfig.FindControl("txtEnvPath") as TextBox;
            if (txtname != null && txtUserName != null && txtServerPort != null && txtPassword != null && txtEnvpath != null && txtdtServername != null && txtBackupName != null)
            {
                CurrentEntity.DatabaseSybaseWithRSHADR.ServerWithPort = txtServerPort.Text;
                CurrentEntity.DatabaseSybaseWithRSHADR.DataBaseSID = txtname.Text;
                CurrentEntity.DatabaseSybaseWithRSHADR.UserName = CryptographyHelper.Md5Encrypt(txtUserName.Text);
                //CurrentEntity.DatabaseSybaseWithRSHADR.Password = Utility.IsMD5EncryptedString(txtPassword.Text) ? txtPassword.Text : CryptographyHelper.Md5Encrypt(txtPassword.Text);
                CurrentEntity.DatabaseSybaseWithRSHADR.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtPassword.Text, hdfStaticGuid.Value));
                CurrentEntity.DatabaseSybaseWithRSHADR.SybaseEnv_Path = txtEnvpath.Text;
                CurrentEntity.DatabaseSybaseWithRSHADR.SybaseDataServerName = txtdtServername.Text;
                CurrentEntity.DatabaseSybaseWithRSHADR.SybaseBackupServer = txtBackupName.Text;


            }
        }

        protected void BindHANADBEntityValue()
        {
            var txtDatabaseSid = HANADatabaseConfig.FindControl("txtDatabaseSid") as TextBox;
            var txtInstanceNo = HANADatabaseConfig.FindControl("txtInstanceNo") as TextBox;
            var txtHostName = HANADatabaseConfig.FindControl("txtHostName") as TextBox;
            var txtHanaDbUserName = HANADatabaseConfig.FindControl("txtUserName") as TextBox;
            var txtHanaDbPort = HANADatabaseConfig.FindControl("txtPort") as TextBox;
            var txtHanaDbPassword = HANADatabaseConfig.FindControl("txtPassword") as TextBox;
            if (txtDatabaseSid != null && txtInstanceNo != null && txtHostName != null)
            {
                CurrentEntity.DatabaseHanaDb.DatabaseSID = txtDatabaseSid.Text;
                CurrentDatabase.DatabaseHanaDb.InstanceNo = txtInstanceNo.Text;
                CurrentDatabase.DatabaseHanaDb.HostName = txtHostName.Text;

            }

            CurrentEntity.DatabaseHanaDb.UserName = txtHanaDbUserName.Text == "" ? " " : CryptographyHelper.Md5Encrypt(txtHanaDbUserName.Text);           //CryptographyHelper.Md5Encrypt(txtHanaDbUserName.Text);                                    
            //CurrentEntity.DatabaseHanaDb.Password = Utility.IsMD5EncryptedString(txtHanaDbPassword.Text) ? txtHanaDbPassword.Text : CryptographyHelper.Md5Encrypt(txtHanaDbPassword.Text);                                    
            // CurrentEntity.DatabaseHanaDb.Password = txtHanaDbPassword.Text == "" ? " " : CryptographyHelper.Md5Encrypt(txtHanaDbPassword.Text);
            CurrentEntity.DatabaseHanaDb.Password = CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(txtHanaDbPassword.Text, hdfStaticGuid.Value));
            CurrentEntity.DatabaseHanaDb.Port = txtHanaDbPort.Text == "" ? " " : txtHanaDbPort.Text;

        }

        private bool CheckValidation_rac()
        {
            var isValid = true;

            var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;
            var selectedItem = Utility.GetSelectedItem(cblstGroup);
            if (selectedItem.Count == 0)
            {
                isValid = false;
                var lblracnodemsg = OracleDatabaseconfig1.FindControl("labelSDErrormessage") as Label;
                lblracnodemsg.Visible = true;
                lblracnodemsg.Text = "Please Select atleast one Node";
            }
            else
            {
                var lblracnodemsg = OracleDatabaseconfig1.FindControl("labelSDErrormessage") as Label;
                lblracnodemsg.Visible = false;
            }
            return isValid;
        }

        private bool CheckValidation_licenseDB(string _dbname)
        {
            _logger.Info("CheckValidation_licenseDB Method Execution For _dbname " + _dbname);
            //string Type = string.Empty;
            //int _ServerId = ddlServerId.SelectedValue.Contains("~") ? Convert.ToInt32(ddlServerId.SelectedValue.Split('~').GetValue(0)) : 0;
            //var server = Facade.GetServerById(_ServerId);
            //string concot = string.Empty;
            //PTSL.CPEncryptionClass PtsLKey = new PTSL.CPEncryptionClass();
            // return PtsLKey.verifyDBPTSLicenseKey(CryptographyHelper.Md5Decrypt(server.IPAddress), _dbname, server.LicenseKey);
            return true;

        }

        private void CheckGroupBoxList()
        {
            var arr1 = new string[25];
            var userGroupList1 = new List<string>();
            //if(listItem.Value = true)
            // _previousSelectedItems.Add(listItem);
            Session.Remove("PreviousItem");
            var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;
            var NodeList = Facade.GetAllDataBaseNodesByDatabaseId(CurrentEntity.Id);
            if (NodeList != null)
            {
                foreach (ListItem listItem in cblstGroup.Items)
                {
                    foreach (var user in NodeList)
                    {
                        if (listItem.Value == user.NodeId.ToString())
                        {
                            listItem.Selected = true;
                            _previousSelectedItems.Add(listItem);
                        }
                    }
                }
            }
            Session["PreviousItem"] = _previousSelectedItems;
        }

        public override void SaveEditor()
        {
            var ChkSubstitue = OracleDatabaseconfig1.FindControl("ChkSubstitue") as CheckBox;

            try
            {
                _logger.Info("SaveEditor Method Execution Start.");

                if (CurrentEntity.IsNew)
                {
                    var lblmsg = OracleDatabaseconfig1.FindControl("labelSDErrormessage") as Label;
                    lblmsg.Visible = false;
                    CurrentEntity.Mode = DatabaseMode.Down;
                    CurrentEntity.CreatorId = LoggedInUserId;
                    CurrentEntity.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.AddDatabaseBase(CurrentEntity);

                    if (CurrentEntity.DatabaseOracle.BaseDatabaseId > 0)
                    {
                        var item = CurrentEntity.SubstituteAuthentication1;
                        item.BaseDatabaseId = CurrentEntity.DatabaseOracle.BaseDatabaseId;
                        if (ChkSubstitue.Checked)
                        {
                            _facade.AddDatabaeSubstituteAuthn(item);
                        }
                    }

                    if (CurrentEntity.DatabaseNode.BaseDatabaseId > 0)
                    {
                        var databasnode = new DatabaseNodes();
                        var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;
                        var selectedItem = Utility.GetSelectedItem(cblstGroup);

                        if (selectedItem.Count != 0)
                        {
                            foreach (var listItem in selectedItem)
                            {
                                if (listItem.Text == "ALL")
                                {
                                    continue;
                                }
                                CurrentEntity.DatabaseNode.Id = CurrentEntity.DatabaseNode.Id;
                                CurrentEntity.DatabaseNode.DatabaseId = CurrentEntity.DatabaseNode.BaseDatabaseId;
                                CurrentEntity.DatabaseNode.NodeId = Convert.ToInt32(listItem.Value);
                                Facade.AddDatabaseNodes(CurrentEntity.DatabaseNode);
                            }
                        }
                        else
                        {
                        }
                    }

                    // ActivityLogger.AddLog(LoggedInUserName, ddlDBType.SelectedValue, UserActionType.CreateDatabaseComponent, "The Database component '" + CurrentEntity.Name + "' was added to the server component table", LoggedInUserId);
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), ddlDBType.SelectedValue, UserActionType.CreateDatabaseComponent, "The Database component '" + CurrentEntity.Name + "' was added to the server component table", LoggedInUserId);
                }
                else
                {
                    CurrentEntity.UpdatorId = LoggedInUserId;
                    CurrentEntity = Facade.UpdateDatabaseBase(CurrentEntity);

                    var item = CurrentEntity.SubstituteAuthentication1;

                    item.BaseDatabaseId = CurrentEntity.DatabaseOracle.BaseDatabaseId;

                    #region New Code


                    var sqlauth = Facade.GetAllByDatabaseSubstituteAuthn(CurrentDatabase.DatabaseOracle.BaseDatabaseId);

                    if (ChkSubstitue.Checked)
                    {
                        if (sqlauth != null)
                        {
                            item.Id = sqlauth.Id;
                            item.Sqlplus = item.Sqlplus;
                            item.Credential = item.Credential;
                            item.Role = item.Role;

                            Facade.UpdateDatabaseAuthn(item);
                        }
                        else
                        {
                            Facade.AddDatabaeSubstituteAuthn(item);
                        }
                    }
                    else
                    {
                        if (sqlauth != null)
                        {
                            item.Id = sqlauth.Id;
                            item.Sqlplus = item.Sqlplus;
                            item.Credential = item.Credential;
                            item.Role = item.Role;

                            Facade.DeleteDatabaseAuthn(item.Id);
                        }
                        else
                        {

                        }
                    }


                    #endregion New Code

                    //if (CurrentEntity.DatabaseNode.BaseDatabaseId > 0)
                    if (CurrentEntity.Id > 0)
                    {
                        var databasnode = new DatabaseNodes();
                        var cblstGroup = OracleDatabaseconfig1.FindControl("cblstGroup") as CheckBoxList;
                        var selectedItem = Utility.GetSelectedItem(cblstGroup);

                        var previousItems = Session["PreviousItem"] as IList<ListItem>;
                        var currentSelectedItems = Utility.GetSelectedItem(cblstGroup);
                        if (previousItems != null && currentSelectedItems != null)
                        {
                            CompareToCollection(previousItems, currentSelectedItems);
                        }
                    }

                    // ActivityLogger.AddLog(LoggedInUserName, ddlDBType.SelectedValue, UserActionType.UpdateDatabaseComponent, "The Database component'" + CurrentEntity.Name + "' was updated to the server component table", LoggedInUserId);
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), ddlDBType.SelectedValue, UserActionType.UpdateDatabaseComponent, "The Database component'" + CurrentEntity.Name + "' was updated to the server component table", LoggedInUserId);
                }
                _logger.Info("SaveEditor Method Execution Completed.");
            }
            catch (Exception ex)
            {
                //_logger.Error("Exception While insert save database " + ex.Message);
                _logger.Error("Exception Occurred In SaveEditor Method Of DataBase Configuration, Error Message" + ex.Message);
                if (ex.InnerException != null)
                {
                    _logger.Error("Exception Occurred In SaveEditor Method Of DataBase Configuration, InnerException  " + ex.InnerException);
                    _logger.Error("Exception Occurred In SaveEditor Method Of DataBase Configuration, InnerException Message " + ex.InnerException.Message);
                    _logger.Error("Exception Occurred In SaveEditor Method Of DataBase Configuration, StackTrace" + ex.InnerException.StackTrace);
                }
            }
        }


        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Clear();
        }

        private void Clear()
        {
            list.Items.Clear();

            Response.Redirect(Constants.UrlConstants.Urls.Component.DatabaseList);
        }

        [WebMethod]
        public static string SaveDatabaseVersion(string value)
        {
            var data = value.Split(',');
            var version = new DatabaseVersion();
            switch (data[0])
            {
                case "Oracle":
                    version.DataBaseTypeId = 1;
                    break;

                case "Sql":
                    version.DataBaseTypeId = 2;
                    break;

                case "Exchange":
                    version.DataBaseTypeId = 3;
                    break;

                case "DB2":
                    version.DataBaseTypeId = 4;
                    break;

                case "ExchangeDAG":
                    version.DataBaseTypeId = 5;
                    break;

                case "MySQL":
                    version.DataBaseTypeId = 6;
                    break;

                case "MsSql":
                    version.DataBaseTypeId = 7;
                    break;
            }
            version.Name = data[1];
            var result = _facade.AddDatabaseVersion(version);
            if (result != null)
            {
                return "Success" + ":" + result.Name + ":" + result.Name;
            }
            return "Error";
        }

        protected void TxtNameTextChanged(object sender, EventArgs e)
        {
            lblDatabaseName.Text = "";
            btnSave.Enabled = true;
            btnSave.CssClass = "btn btn-primary";
            if (ddlDBType.SelectedValue == "Sql")
                return;
            if (!String.IsNullOrEmpty(txtName.Text))
            {
                lblDatabaseName.Text = CheckDataBaseNameExist() ? "DataBase Name is Not Avaliable" : string.Empty;

                if (lblDatabaseName.Text != string.Empty)
                {
                    btnSave.Enabled = false;
                }
                else
                {
                    ddlDBType.Focus();
                    btnSave.Enabled = true;
                    btnSave.CssClass = "btn btn-primary";
                }
            }
        }

        private bool CheckDataBaseNameExist()
        {
            bool result = false;
            try
            {

                _logger.Info("CheckDataBaseNameExist Method Execution Start.");

                if (CurrentEntity.Id > 0)
                {
                    if (txtName.Text.ToLower().Equals(CurrentEntity.Name.ToLower()))
                    {
                        return false;
                    }
                }
                // txtName.Text.Equals("testdb", StringComparer.OrdinalIgnoreCase);
                // return Facade.IsExistDatabaseBaseByName(txtName.Text);
                result = Facade.IsExistDatabaseBaseByName(txtName.Text);
            }
            catch (Exception exc)
            {
                _logger.Error("Exception occurred in CheckDataBaseNameExist, error message " + exc.Message);
                if (exc.InnerException != null)
                {
                    _logger.Error("Exception occurred in CheckDataBaseNameExist, InnerException " + exc.InnerException.Message);
                    _logger.Error("Exception occurred in CheckDataBaseNameExist,  StackTrace" + exc.InnerException.StackTrace);
                }

            }
            return result;
        }

        private void CompareToCollection(IEnumerable<ListItem> previousItems, IList<ListItem> currentSelectedItems)
        {
            var deleteListGroups = new List<ListItem>();
            var addListGroups = currentSelectedItems;
            foreach (var prItem in previousItems)
            {
                var itemFind = false;

                foreach (var crItem in currentSelectedItems)
                {
                    if (prItem.Text != crItem.Text) continue;
                    if (addListGroups.Contains(crItem))
                    {
                        addListGroups.Remove(crItem);
                    }
                    itemFind = true;
                    break;
                }
                if (!itemFind)
                {
                    deleteListGroups.Add(prItem);
                }
            }

            try
            {
                if (deleteListGroups.Count > 0)
                {
                    foreach (var userGroup in deleteListGroups.Select(deleteItem => new DatabaseNodes
                    {
                        Id = CurrentEntity.DatabaseNode.Id,
                        DatabaseId = Convert.ToInt32(CurrentEntity.Id),
                        NodeId = Convert.ToInt32(deleteItem.Value)
                    }))
                    {
                        Facade.DeleteDatabaseNodesByDatabaseId(userGroup);
                    }
                }

                if (addListGroups.Count > 0)
                {
                    foreach (var userGroup in addListGroups.Select(addItem => new DatabaseNodes
                    {
                        DatabaseId = Convert.ToInt32(CurrentEntity.Id),
                        NodeId = Convert.ToInt32(addItem.Value)
                    }))
                    {
                        Facade.AddDatabaseNodes(userGroup);
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occurred while comparing user group list", ex);
                ExceptionManager.Manage(cpException);
            }
        }

        protected void btnAdd_Click(object sender, EventArgs e)
        {
            if (ddlDBType.SelectedValue == Convert.ToString(0))
            {
                lblError.Visible = true;
                lblError.InnerText = "Select Db Type";
            }
            else if (CurrentDatabaseBaseId == 0)
            {
                lblError.Visible = false;
                //divVersion.Visible = true;
            }
        }

        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                //if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    //ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                //ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        //EOC Validate Request
        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();
                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                IgnoreIDs.Add("txtASMInstancename");
                IgnoreIDs.Add("txtAchive");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        //private void CheckGroupBoxList()
        //{
        //    Session.Remove("PreviousItem");
        //    var userGroupList = Facade.GetUserGroupsByUserId(CurrentUserId);
        //    if (userGroupList != null)
        //    {
        //        foreach (var listItem in from ListItem listItem in cblstGroup.Items from userGroupItem in userGroupList where listItem.Value == userGroupItem.GroupId.ToString() select listItem)
        //        {
        //            listItem.Selected = true;
        //            _previousSelectedItems.Add(listItem);
        //        }
        //    }

        //    Session["PreviousItem"] = _previousSelectedItems;
        //}

        //private bool CheckValidation_licenseDB(string _dbname)
        //{

        //    string Type = string.Empty;
        //    int _ServerId = ddlServerId.SelectedValue.Contains("~") ? Convert.ToInt32(ddlServerId.SelectedValue.Split('~').GetValue(0)) : 0;
        //    var server = Facade.GetServerById(_ServerId);
        //    string concot = string.Empty;
        //    concot = LicensekeyHelper.PickANyNumDB(CryptographyHelper.Md5Decrypt(server.IPAddress) + "DB") + "," + CryptographyHelper.Md5Encrypt(_dbname);

        //    return server.LicenseKey.Equals(concot);
        //}


        //private bool CheckValidation_licenseDB_old(string _dbname)
        //{

        //    string Type = string.Empty;
        //    int _ServerId = ddlServerId.SelectedValue.Contains("~") ? Convert.ToInt32(ddlServerId.SelectedValue.Split('~').GetValue(0)) : 0;
        //    var server = Facade.GetServerById(_ServerId);
        //    string concot = string.Empty;
        //    // concot = LicensekeyHelper.PickANyNumDB(CryptographyHelper.Md5Decrypt(server.IPAddress) + "DB") + CryptographyHelper.Md5Encrypt(_dbname);
        //    concot = LicensekeyHelper.PickANyNumDB(CryptographyHelper.Md5Decrypt(server.IPAddress) + "DB") + LicensekeyHelper.CPLCEncrypt(_dbname);

        //    //  concot = LicensekeyHelper.PickANyNumDB(CryptographyHelper.Md5Decrypt(server.IPAddress) + "DB") + "," + CryptographyHelper.Md5Encrypt(_dbname);

        //    return server.LicenseKey.Equals(concot);
        //}

        //protected void BtnVersionAddClick(object sender, EventArgs e)
        //{
        //    var version = new DatabaseVersion();
        //    if (Convert.ToInt32(ddldbversion.SelectedValue) != 0 && txtVersion.Text != "")
        //    {
        //        version.DataBaseTypeId = Convert.ToInt32(ddldbversion.SelectedValue);
        //        version.Name = txtVersion.Text;
        //        var result = Facade.AddDatabaseVersion(version);
        //        if (result != null)
        //        {
        //            ModalPopupDBVersionAdd.Hide();
        //            txtVersion.Text = string.Empty;

        //            if (!String.Equals("0", ddlDBType.SelectedValue))
        //            {
        //                switch (ddlDBType.SelectedValue)
        //                {
        //                    case "Oracle":
        //                        Utility.PopulateVersionByDBType(ddlVersion, 1, true);
        //                        break;
        //                    case "Sql":
        //                        Utility.PopulateVersionByDBType(ddlVersion, 2, true);
        //                        break;
        //                    case "Exchange":
        //                        Utility.PopulateVersionByDBType(ddlVersion, 3, true);
        //                        break;
        //                }
        //            }
        //        }
        //    }
        // }

        //protected void BtnVersionCancelClick(object sender, EventArgs e)
        //{
        //    txtVersion.Text = string.Empty;
        ////    ddlVersion.SelectedIndex = 0;
        //    ModalPopupDBVersionAdd.Hide();
        //}

        /* protected void btnAddVerion_Click(object sender, EventArgs e)
         {
             var version = new DatabaseVersion();
             //if (txtAddVersion.Text != "")
             //{
                 switch (ddlDBType.SelectedValue)
                 {
                     case "Oracle":
                         version.DataBaseTypeId = 1;
                         break;

                     case "Sql":
                         version.DataBaseTypeId = 2;
                         break;

                     case "Exchange":
                         version.DataBaseTypeId = 3;
                         break;

                     case "DB2":
                         version.DataBaseTypeId = 4;
                         break;

                     case "ExchangeDAG":
                         version.DataBaseTypeId = 5;
                         break;
                 }
                // version.Name = txtAddVersion.Text;
                 var result = Facade.AddDatabaseVersion(version);
                 if (result != null)
                 {
                     //txtAddVersion.Text = "";
                    // txtAddVersion.Visible = false;
                     //divVersion.Visible = false;
                     //txtAddVersion.Enabled = true;
                     //btnAddVerion.Enabled = true;
                     if (!String.Equals("0", ddlDBType.SelectedValue))
                     {
                         switch (ddlDBType.SelectedValue)
                         {
                             case "Oracle":
                                 Utility.PopulateVersionByDBType(ddlVersion, 1, true);
                                 break;

                             case "Sql":
                                 Utility.PopulateVersionByDBType(ddlVersion, 2, true);
                                 break;

                             case "Exchange":
                                 Utility.PopulateVersionByDBType(ddlVersion, 3, true);
                                 break;

                             case "DB2":
                                 Utility.PopulateVersionByDBType(ddlVersion, 4, true);
                                 break;

                             case "ExchangeDAG":
                                 Utility.PopulateVersionByDBType(ddlVersion, 5, true);
                                 break;
                         //}
                     }
                 }
             }
         }

         protected void btnAddCancel_Click(object sender, EventArgs e)
         {
             divVersion.Visible = false;
         }*/

    }
}