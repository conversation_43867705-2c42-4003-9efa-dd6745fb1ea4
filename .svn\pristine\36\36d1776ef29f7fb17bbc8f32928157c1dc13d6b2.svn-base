﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System.Web;
using System.Text;
using System.Data;

namespace CP.UI.Controls
{
    public partial class DatabaseOracleRacList : BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.DatabaseConfiguration;

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.DatabaseList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            BindData();
            //ViewState["_token"] = UrlHelper.AddTokenToRequest();
            //if (ViewState["_token"] != null)
            //{
            //    hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            //}
            //hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());


        }

        private IList<DatabaseBase> GetOracleList()
        {
            //return Facade.GetDatabaseBasesByTypeRoleAndCompanyId(DatabaseType.OracleRac, IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
            //string role = Convert.ToString(LoggedInUserRole);
            //return Facade.GetDatabaseBaseBytypecompanyiduserid(DatabaseType.OracleRac, LoggedInUserCompanyId, LoggedInUserId, role);

            return Facade.GetDatabaseBaseBytypecompanyiduserid(DatabaseType.OracleRac, Convert.ToInt32(HttpContext.Current.Session["_companyId"]), Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]), Convert.ToString(HttpContext.Current.Session["LoggedInUserRole"]));
        }

        private void BindData()
        {
            lvOracledatabase.DataSource = GetOracleList();
            lvOracledatabase.DataBind();
            setListViewPage();
        }

        /// <summary>
        /// if deleting or updating the List of oracle rac database the page will get postback and then Listview is display the same page
        /// where it is deleted or updated. - Goraknath Khule.
        /// </summary>
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageDBOracleRacList"]) != -1) && Session["CurrentPageDBOracleRacList"] != null)
            {
                if (Convert.ToInt32(Session["CurrentPageDBOracleRacList"]) == dataPager1.TotalRowCount)
                {
                    Session["CurrentPageDBOracleRacList"] = Convert.ToInt32(Session["CurrentPageDBOracleRacList"]) - dataPager1.MaximumRows;
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageDBOracleRacList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageDBOracleRacList"] = -1;
            }
        }

        public IList<DatabaseBase> GetOracleList(string searchvalue)
        {
            var Databaselist = GetOracleList();
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && Databaselist != null && Databaselist.Count > 0)
            {
                var result = (from Database in Databaselist
                              where Database.Name.ToLower().Contains(searchvalue.ToLower())
                              select Database).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvOracledatabase.Items.Clear();
                lvOracledatabase.DataSource = GetOracleList(txtsearchvalue.Text);
                lvOracledatabase.DataBind();
            }
        }

        protected void LvdatabasePreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                BindData();
            }
        }

        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByDataBaseId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Database " + name + " attaching with group " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }

        protected void LvdatabaseItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageDBOracleRacList"] = (dataPager1.StartRowIndex);
                var lblDatabaseId = (lvOracledatabase.Items[e.ItemIndex].FindControl("ID")) as Label;
                var lblName = (lvOracledatabase.Items[e.ItemIndex].FindControl("lblName")) as Label;

                //if (lblDatabaseId != null && lblName != null && ValidateRequest("OracleRacList Delete", UserActionType.DatabaseList))
                //{
                // var groupDetailsByDatabaseId = Facade.GetGroupsByDatabaseId(Convert.ToInt32(lblDatabaseId.Text));
                //var workflowDetailsByDatabaseId =
                //    Facade.GetAllWorkflowActionsByDatabaseId(Convert.ToInt32(lblDatabaseId.Text));
                //if (workflowDetailsByDatabaseId != null)
                //{
                //    ErrorSuccessNotifier.AddSuccessMessage("The OracleRac database component is in use");
                //}

                lbl_Server.Text = lblName.Text;
                StringBuilder _builderServer = new StringBuilder();
                StringBuilder _builderInfra = new StringBuilder();
                StringBuilder _builderWFAction = new StringBuilder();
                StringBuilder _builderWorkflow = new StringBuilder();

                if (lblDatabaseId != null && lblName != null && ValidateRequest("OracleRacList Delete", UserActionType.DatabaseList))
                {
                    var infraObjectDetailsByDatabaseId = Facade.GetInfraObjectByDataBaseId(Convert.ToInt32(lblDatabaseId.Text));
                    if (infraObjectDetailsByDatabaseId != null)
                    {
                        foreach (var _infra in infraObjectDetailsByDatabaseId)
                        {
                            _builderInfra.Append(_infra.Name + ",");
                        }
                    }


                    var workflowDetailsByDatabaseId = Facade.GetAllWorkflowActionsByDatabaseId(Convert.ToInt32(lblDatabaseId.Text));

                    if (workflowDetailsByDatabaseId != null)
                    {
                        foreach (var _workflowaction in workflowDetailsByDatabaseId)
                        {
                            _builderWFAction.Append(_workflowaction.Name + ",");
                        }
                    }


                    IList<WorkflowGet> _workflow = Facade.GetAllWorkflows_ByDatabaseId(Convert.ToInt32(lblDatabaseId.Text));

                    if (_workflow != null)
                    {
                        foreach (var item in _workflow)
                        {
                            _builderWorkflow.Append(item.Name + ",");
                        }

                    }


                    //IList<Server> serverlist = Facade.GetServerBy_DBID(Convert.ToInt32(lblDatabaseId.Text));
                    //if (serverlist != null)
                    //{
                    //    foreach (var item in serverlist)
                    //    {
                    //        _builderServer.Append(item.Name + ",");

                    //    }
                    //}


                    if (infraObjectDetailsByDatabaseId != null || workflowDetailsByDatabaseId != null || _workflow.Count > 0)
                    {
                        paneldetails.Visible = true;
                        string infraName = string.Empty;
                        string dbName = string.Empty;

                        DataTable table = new DataTable();
                        table.Columns.Add("Database Attached Infraobject");
                        //table.Columns.Add("Database Attached Server");
                        table.Columns.Add("Database Attached Workflow");
                        table.Columns.Add("Database Attached Workflow Actions");

                        table.Rows.Add(_builderInfra.ToString(),  _builderWorkflow.ToString(), _builderWFAction.ToString());


                        Server_Info_GridView.DataSource = table;
                        Server_Info_GridView.DataBind();

                        return;
                    }

                    else
                    {
                        Facade.DeleteDatabaseBaseById(Convert.ToInt32(lblDatabaseId.Text));

                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "OracleRac", UserActionType.DeleteDatabaseComponent,
                                              "The OracleRac database component '" + lblName.Text +
                                              "' was deleted from the database component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "OracleRac database Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                    }
                }

                

            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.DatabaseType, DatabaseType.OracleRac
                //    .ToString());

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseType, DatabaseType.OracleRac.ToString());
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void LvdatabaseItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageDBOracleRacList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);
            var lblDatabaseId = (lvOracledatabase.Items[e.NewEditIndex].FindControl("ID")) as Label;

            var lblName = (lvOracledatabase.Items[e.NewEditIndex].FindControl("lblName")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "OracleRac", UserActionType.UpdateDatabaseComponent,
                                             "The OracleRac database component '" + lblName.Text +
                                             "' Opened as Editing Mode", LoggedInUserId);

            if (lblDatabaseId != null && ValidateRequest("OracleRacList Edit", UserActionType.DatabaseList))
            {

                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseId, lblDatabaseId.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseType, DatabaseType.OracleRac.ToString());
                Helper.Url.Redirect(secureUrl);

                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.DatabaseId, lblDatabaseId.Text,
                //Constants.UrlConstants.Params.DatabaseType, DatabaseType.OracleRac.ToString());
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}
        }

        protected void LvdatabaseItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ibtnEdit") as ImageButton;
            var delete = e.Item.FindControl("ibtnDelete") as ImageButton;

            if (IsUserOperator || IsUserExecutionUser)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
            if (IsUserManager)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void Server_Info_GridView_RowDataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.Header)
            {
                e.Row.Cells[0].Width = new Unit("25%");
                e.Row.Cells[1].Width = new Unit("25%");
                e.Row.Cells[2].Width = new Unit("25%");
                
            }
        }

        protected void Server_Info_GridView_DataBound(object sender, EventArgs e)
        {

        }
    }
}