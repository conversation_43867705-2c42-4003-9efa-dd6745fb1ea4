﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Xml;

namespace CP.UI
{
    public partial class WorkflowList : BasePage
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(ServerList));
        static IFacade _facade = new Facade();
        private static string _loggedInUserName = string.Empty;
        private static int _currentLoginUserId;

        public override void PrepareView()
        {
            int pagesize_val = 0;
            if (Session["BrowserWidth"] != null)
            {
                //string s = "Width: " + Session["BrowserWidth"] + " Height: " + Session["BrowserHeight"];
                var browser_height = Session["BrowserHeight"];
                var browser_width = Session["BrowserWidth"];
                string _browserheight = browser_height.ToString();
                string _browserwidth = browser_width.ToString();
                pagesize_val = Utility.heightandwidth(Int32.Parse(_browserheight), Int32.Parse(_browserwidth));
            }
            //dataPager1.PageSize = pagesize_val;
            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }
            if (Master != null)
            {
                var hd = Master.FindControl("hdSelectedMenu") as HiddenField;
                if (hd != null) hd.Value = "Module6";
            }
            Utility.SelectMenu(Master, "Module3");
            BindList();
        }


        private void BindList()
        {
            setListViewPage();
            lvComponent.DataSource = GetServerList();
            lvComponent.DataBind();
            // setListViewPage();
        }

        protected void LvComponentPreRender(object sender, EventArgs e)
        {
            //Session["CurrentPageServerList"] = (dataPager1.StartRowIndex);
            if (IsPostBack)
            {
                if (String.IsNullOrEmpty(txtsearchvalue.Text))
                {
                    lvComponent.DataSource = GetServerList();
                    lvComponent.DataBind();


                }
                else
                {
                    lvComponent.DataSource = GetServerListBySearch(txtsearchvalue.Text);
                    lvComponent.DataBind();
                }
            }
        }

        public string Getprofile(object id)
        {
            return "";
        }

        public string Getinfraobject(object id)
        {
            string infraobj = null;

            return infraobj;

        }

        protected void LvComponentItemCommand(object sender, ListViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "Lock")
                {
                    int workflowId = 0;
                    Label lblWorkflowID = e.Item.FindControl("ID") as Label;
                    workflowId = Convert.ToInt32(lblWorkflowID.Text);
                    HiddenField hdnIsActive = (HiddenField)e.Item.FindControl("hdnIsActive");
                    int islock = Convert.ToInt32(hdnIsActive.Value);
                    Label lblWorkflowname = e.Item.FindControl("Workflowname") as Label;
                    string Workflowname = lblWorkflowname.Text.ToString();
                    //get action action ids
                    var workflow = Facade.GetWorkflowById(workflowId);
                    string[] strActionIds = null;
                    if (workflow.ActionIds.Contains(","))
                    {
                        strActionIds = workflow.ActionIds.Split(',');
                    }
                    else
                    {
                        strActionIds[0] = workflow.ActionIds;
                    }


                    if (hdnIsActive != null)
                    {
                        if (islock == 0)
                        {
                            Facade.LockWorkflow(workflowId, 1);
                            //WorkflowList.GetCopyAsWorkflowXML(workflow.Xml, workflow.Name, workflowId);
                            //for (int i = 0; i < strActionIds.Count()-1; i++)
                            //{

                            //    var action = Facade.GetWorkflowActionById(Convert.ToInt32(strActionIds[i]));
                            //    action.Name = action.Name + "_new";
                            //    action.IsLock = 1;
                            //    Facade.AddWorkflowAction(action);

                            //}
                            #region create new work flow code hide
                            //var workflow = Facade.GetWorkflowById(workflowId);
                            //string WFname=  workflow.Name +"new";
                            //WorkflowConfiguration.WorkflowSaveAs(workflow.Xml, WFname);
                            #endregion
                        }
                        else
                        {
                            Facade.LockWorkflow(workflowId, 0);

                            //UpdateWorkflowActionislock

                            for (int i = 0; i < strActionIds.Count() - 1; i++)
                            {
                                Facade.UpdateWorkflowActionislock(Convert.ToInt32(strActionIds[i]));

                            }
                        }

                    }

                    UpdatePanel1.Update();
                }
            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured in LvComponentItemCommand");

                _logger.Info("Exception Occured in LvComponentItemCommand: " + ex.Message.ToString());

                if (ex.InnerException != null)
                    _logger.Info("Exception Occured in LvComponentItemCommand: " + ex.InnerException.Message.ToString());
            }

        }

        //update xml.
        public static string GetCopyAsWorkflowXML(string oldXML, string workflowName, int workflowid)
        {
            string strOldActionIds = string.Empty;
            int arrayCount = 0;

            var reader = XmlReader.Create(new System.IO.StringReader(oldXML));
            var xmldoc = new XmlDocument();
            xmldoc.Load(reader);
            var nodeslist = xmldoc.ChildNodes;
            IDictionary<int, string> actionList = new Dictionary<int, string>();
            IDictionary<string, string> oldactionList = new Dictionary<string, string>();
            // IDictionary<int, string> oldactionList = new Dictionary<int, string>();
            IList<WorkflowAction> workflowActionList = new List<WorkflowAction>();
            workflowActionList = _facade.GetAllNameAndIds();
            try
            {
                foreach (XmlNode nd in nodeslist)
                {
                    if (nd.HasChildNodes)
                    {
                        var chldnd = nd.ChildNodes;
                        foreach (XmlNode chd in chldnd)
                        {
                            if (chd.Attributes["id"].Value.Contains("div"))
                                arrayCount++;
                            else
                            {
                                if (arrayCount < chldnd.Count - 1)
                                {
                                    var nextNd = chldnd[arrayCount + 1];
                                    //  oldactionList.Add(Convert.ToInt32(chd.Attributes["id"].Value), chd.InnerText);
                                    oldactionList.Add(chd.Attributes["id"].Value + "-" + arrayCount, chd.InnerText);
                                }
                                else
                                    oldactionList.Add(chd.Attributes["id"].Value + "-" + arrayCount, chd.InnerText);
                                //oldactionList.Add(Convert.ToInt32(chd.Attributes["id"].Value), chd.InnerText);

                                arrayCount++;
                            }
                        }
                    }
                }

                int j = 1;
                foreach (var item in oldactionList)
                {
                    var val = item.Key.Split('-');

                    WorkflowAction objAction = _facade.GetWorkflowActionById(Convert.ToInt32(val[0]));//
                    if (objAction != null)
                    {
                        if (workflowActionList != null)
                            if (workflowActionList.Count > 0)
                                objAction.Name = CheckWorkflowActionName(objAction.Name, 1, workflowActionList);
                        // objAction.Name = workflowName + "_" + objAction.Name;
                        objAction.IsLock = 1;

                        objAction = _facade.AddWorkflowAction(objAction);
                        workflowActionList.Add(objAction);

                        actionList.Add(objAction.Id, j.ToString() + ". " + objAction.Name);
                        j++;
                    }
                }

                string actionIdstr = string.Empty;
                foreach (var itemList in actionList)
                {
                    actionIdstr += itemList.Key + ",";
                }
                //if (!string.IsNullOrEmpty(actionIdstr))
                //    actionIdstr = actionIdstr.Substring(0, actionIdstr.Length - 1);


                string strWFXml = UpdateWorkflowXML(actionList, oldactionList, oldXML);

                _facade.UpdatexmlById(strWFXml, workflowid, actionIdstr);


                return "Success^";
            }
            catch (Exception exc)
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "GetCopyAsWorkflowXML", UserActionType.CreateAction, "Exception while GetSaveAsWorkflowXML" + exc.Message, _currentLoginUserId);

                return string.Empty;
            }

        }
        public static string UpdateWorkflowXML(IDictionary<int, string> actionList, IDictionary<string, string> oldactionList, string workflowXMl)
        {
            try
            {
                if (actionList != null && actionList.Count > 0)
                {
                    for (int index = 0; index < actionList.Count; index++)
                    {
                        var itemnew = actionList.ElementAt(index);
                        var itemold = oldactionList.ElementAt(index);
                        workflowXMl = workflowXMl.Replace(itemold.Key.ToString().Split('-')[0], itemnew.Key.ToString());
                        workflowXMl = workflowXMl.Replace(itemold.Value, itemnew.Value);
                    }

                }
                return workflowXMl;
            }
            catch (Exception ex)
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "UpdateWorkflowXML", UserActionType.CreateAction, "Exception while UpdateWorkflowXML" + ex.Message, _currentLoginUserId);
            }
            return string.Empty;
        }

        public static string CheckWorkflowActionName(string WFActionName, int j, IList<WorkflowAction> workflowActionList)
        {
            string OldWfActionName = WFActionName;

            var itemaction = workflowActionList.Where(x => x.Name.Equals(WFActionName)).FirstOrDefault();

            if (itemaction == null)
                return WFActionName;
            else
            {
                var x = WFActionName.Split('_').Last();
                var isNumeric = !string.IsNullOrEmpty(x) && x.All(Char.IsDigit);
                if (isNumeric)
                {
                    WFActionName = WFActionName.Remove(WFActionName.Length - 1);
                    int val = Convert.ToInt32(x) + j;
                    WFActionName = WFActionName + val;
                }
                else
                {
                    WFActionName = WFActionName + "_" + j;
                }


                var itemaction1 = (from WFActions in workflowActionList where WFActions.Name == WFActionName select WFActions).FirstOrDefault();
                if (itemaction1 == null)
                    return WFActionName;
            }
            return CheckWorkflowActionName(OldWfActionName, ++j, workflowActionList);

        }

        public void OnConfirm(object sender, EventArgs e)
        {
            string confirmValue = Request.Form["confirm_value"];
            if (confirmValue == "Yes")
            {
                this.Page.ClientScript.RegisterStartupScript(this.GetType(), "alert", "alert('You clicked YES!')", true);
            }
            else
            {
                this.Page.ClientScript.RegisterStartupScript(this.GetType(), "alert", "alert('You clicked NO!')", true);
            }
        }

        private IList<WorkflowManagementDetails> GetServerListBySearch(string value)
        {
            var workflowList = GetServerList();
            value = value.Trim();

            if (!String.IsNullOrEmpty(value) && workflowList != null)
            {
                var result = (from sites in workflowList
                              where sites.WorkflowName.ToLower().Contains(value.ToLower())
                              select sites).ToList();

                return result;
            }
            return null;
        }

        protected string Checklock(object act)
        {
            int val = Convert.ToInt32(act);
            string strActive = string.Empty;
            switch (val)
            {
                case 0:
                    strActive = "../Images/icons/unlock.png";
                    break;

                case 1:
                    strActive = "../Images/icons/Lock.png";
                    break;
            }
            return strActive;
        }

        protected string Status(object act)
        {
            int val = Convert.ToInt32(act);
            string strActive = string.Empty;
            switch (val)
            {
                case 0:
                    strActive = "UnLock";
                    break;

                case 1:
                    strActive = "Lock";
                    break;
            }
            return strActive;
        }

        protected string CheckToolTip(object tool)
        {
            int toolid = Convert.ToInt32(tool);
            string strtool = string.Empty;

            switch (toolid)
            {
                case 0:
                    strtool = "Lock"; //Locked
                    break;

                case 1:
                    strtool = "UnLock";
                    break;
            }

            return strtool;
        }

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageServerList"]) != -1) && Session["CurrentPageServerList"] != null && (Convert.ToInt32(Session["CurrentPageServerList"]) > 0))
            {
                if (Session["TotalPageRowsCount"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageServerList"]) == Convert.ToInt32(Session["TotalPageRowsCount"]) - 1)
                    {
                        Session["CurrentPageServerList"] = Convert.ToInt32(Session["CurrentPageServerList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCount"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageServerList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageServerList"] = -1;

            }

        }

        private IList<WorkflowManagementDetails> GetServerList()
        {
            return Facade.GetWorkflowDetailsbyid();
        }

        protected void lvComponent_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected void LvComponentItemDataBound(object sender, ListViewItemEventArgs e)
        {
            try
            {

                HiddenField hdnIsActive = (HiddenField)e.Item.FindControl("hdnIsActive");
                HiddenField hdnMsg = (HiddenField)e.Item.FindControl("Hdnmsg");
                int islock = Convert.ToInt32(hdnIsActive.Value);
                if (islock == 0)
                {
                    hdnMsg.Value = "Are you sure want to lock ";
                }
                else
                {
                    hdnMsg.Value = "Are you sure want to unlock ";
                }
                //Hdnmsg
                //int workflowId = 0;
                //Label lblWorkflowID = e.Item.FindControl("ID") as Label;
                //workflowId = Convert.ToInt32(lblWorkflowID.Text);

                //var workflowDetails = Facade.GetWorkflowDetailsbyid(workflowId);
                //if (workflowDetails != null)
                //{
                //    Label Username = (Label)e.Item.FindControl("Username");
                //    Username.Text = workflowDetails.Username;
                //    Label InfraobjectName = (Label)e.Item.FindControl("InfraobjectName");
                //    InfraobjectName.Text = workflowDetails.Infraobjectname;

                //}





            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured in LvComponentItemdatabaound");

                _logger.Info("Exception Occured in LvComponentItemdatabaound: " + ex.Message.ToString());

                if (ex.InnerException != null)
                    _logger.Info("Exception Occured in LvComponentItemdatabaound: " + ex.InnerException.Message.ToString());
            }

            //Label city = (Label)e.Item.FindControl("City");
            //city.Text = drv["City"].ToString();
            //Label customerID = (Label)e.Item.FindControl("CustomerID");
            //customerID.Text = drv["CustomerID"].ToString();
        }

        protected void BtnSearchClick(object sender, EventArgs e)
        {
            try
            {

                lvComponent.DataSource = GetServerListBySearch(txtsearchvalue.Text);
                lvComponent.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured while binding data", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

    }
}