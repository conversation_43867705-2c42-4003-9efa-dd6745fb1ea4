﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using Gios.Pdf;
using SpreadsheetGear;
using System.IO;
using log4net;


namespace CP.UI.Controls
{
    public partial class UserActivityDetail : BaseControl
    {

        private readonly ILog _logger = LogManager.GetLogger(typeof(UserActivityDetail));
      
        private static int _companyId;
        private static bool _isUserSuperAdmin;
        private static bool _isParent;
        private static int _currentLoginUserId;
        private static bool _isUserEnterpriseUser;
        public Settings CurrentEntity;
      //  private static bool _isUserEnterpriseUser = chkaproval.Checked;
      
        public override void PrepareView()
        {
            //CalendarExtender1.EndDate = DateTime.Now.Date;
            //CalendarExtender2.EndDate = DateTime.Now.Date;

            //IList<User> userall = Facade.GetAlUsers();
            //userall = userall.OrderBy(a => a.LoginName).ToList();

            //if (userall != null)
            //{
            //    ddlusename.DataSource = userall;
            //    ddlusename.DataTextField = "LoginName";
            //    ddlusename.DataValueField = "Id";
            //    ddlusename.DataBind();
            //    ddlusename.Items.Insert(0, "All");
            //}
            //else
            //{
            //    ddlusename.Items.Insert(0, new ListItem("No User Name Found", "0"));
            //}

            try
            {
                CalendarExtender1.EndDate = DateTime.Now.Date;
                CalendarExtender2.EndDate = DateTime.Now.Date;

                IList<User> userall = new List<User>();
                IList<User> UserAll = new List<User>();
                IList<User> user_all = new List<User>();
                string Value = chkaproval.Checked ? "1" : "0";
                string val = CheckOTP.Checked ? "1" : "0";
                if (Value == "1")
                {
                    CheckOTP.Checked = false;
                }

                if (val == "1")
                {
                    chkaproval.Checked = false;
                }

                _companyId = LoggedInUserCompanyId;
                _isUserSuperAdmin = IsUserSuperAdmin;
                _isUserEnterpriseUser = IsUserEnterpriseUser;
                _isParent = LoggedInUserCompany.IsParent;
                _currentLoginUserId = LoggedInUserId;

                if (_isUserEnterpriseUser)
                {
                    chkaproval.Visible = true;
                    CheckOTP.Visible = true;
                }
                else
                {
                    chkaproval.Visible = false;
                    CheckOTP.Visible = false;
                }

                userall = Facade.GetAllUsers();

                if (_isUserSuperAdmin && _isParent)
                {
                    

                    //userall = Facade.GetAllUsers();
                   
                    if (_isUserEnterpriseUser == false)
                    {
                        UserAll = userall.Where(a => a.Role.ToString() != "EnterpriseUser").ToList();
                        userall = UserAll.OrderBy(a => a.LoginName).ToList();
                        if (userall != null)
                        {
                            ddlusename.DataSource = userall;
                            ddlusename.DataTextField = "LoginName";
                            ddlusename.DataValueField = "Id";
                            ddlusename.DataBind();
                            ddlusename.Items.Insert(0, "All");   
                        }
                        
                    }
                    else if (_isUserEnterpriseUser)
                    {
                       // UserAll = userall.Where(a => a.Role.ToString() == "EnterpriseUser").ToList();
                        user_all = userall.OrderBy(a => a.LoginName).ToList();
                        if (UserAll != null)
                        {

                            ddlusename.DataSource = user_all;
                            ddlusename.DataTextField = "LoginName";
                            ddlusename.DataValueField = "Id";
                            ddlusename.DataBind();
                            ddlusename.Items.Insert(0, "All");
                        }
                        
                    }

                    else
                    {
                        ddlusename.Items.Insert(0, new ListItem("No User Name Found", "0"));
                    }
                }
                else
                {
                   // userall = null;

                    var users = Facade.GetAllUsers();

                    var getusers = from grp in users where grp.CreatorId == _currentLoginUserId || grp.Id == _currentLoginUserId select grp;


                    foreach (var data in getusers)
                    {
                        userall.Add(data);
                       
                    }
                    userall = userall.OrderBy(a => a.LoginName).ToList();
                    if (userall != null)
                    {
                        ddlusename.DataSource = userall;
                        ddlusename.DataTextField = "LoginName";
                        ddlusename.DataValueField = "Id";
                        ddlusename.DataBind();
                        ddlusename.Items.Insert(0, "All");
                    }
                    else
                    {
                        ddlusename.Items.Insert(0, new ListItem("No User Name Found", "0"));
                    }
                }


               
            }
            catch (CpException ex)
            {
                _logger.Info("CpException Occured In User Activity Excel Report Exception: " + ex.Message);
                _logger.Info(Environment.NewLine);
                if (ex.InnerException != null)
                    _logger.Info("CpException Occured In User Activity Excel Report InnerException: " + ex.InnerException);
                _logger.Info(Environment.NewLine);
                ExceptionManager.Manage(ex);

            }
            catch (Exception ex)
            {
                _logger.Info("Exception Occured In User Activity Excel Report Exception: " + ex.Message);
                _logger.Info(Environment.NewLine);
                if (ex.InnerException != null)
                    _logger.Info("Exception Occured In User Activity Excel Report InnerException: " + ex.InnerException);
                _logger.Info(Environment.NewLine);
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled Exception Occured To Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }



            Pan1.Visible = false;
            btnview.Visible = true;
            btnExcel.Visible = false;
            btnPdf.Visible = false;

        }

        private DataTable screenreport()
        {
            _logger.Info("======Generating User Activity Report HTML View ======");
            _logger.Info(Environment.NewLine);

            var table = new DataTable();
            table.Columns.Add("Sr.No.");
            table.Columns.Add("Login Name");
            //table.Columns.Add("Entity");
            table.Columns.Add("ActionType");
            table.Columns.Add("User Host Address");
            table.Columns.Add("Activity Details");
            table.Columns.Add("Activity Date");

            string startdt = Utility.getFormatedDate_New(txtstart.Text);
            string enddt = Utility.getFormatedDate_New(txtend.Text);
            //CurrentEntity.Value = chkaproval.Checked ? "1" : "0";
            //if (CurrentEntity.Value =="1")
            //{
                
            //}
            /*  string startdt = string.Empty;
             string enddt = string.Empty;
 #if ORACLE
             startdt = Convert.ToDateTime(txtstart.Text).ToString("dd-MM-yy");
             enddt = Convert.ToDateTime(txtend.Text).ToString("dd-MM-yy");
 #endif

 #if MSSQL
             startdt = Convert.ToDateTime(txtstart.Text).ToString("yyyy-MM-dd");
             enddt = Convert.ToDateTime(txtend.Text).ToString("yyyy-MM-dd");
 #endif

 #if MYSQL
             startdt = txtstart.Text;
             enddt = txtend.Text;
 #endif
             * */
            //var stdt = Convert.ToDateTime(txtstart.Text);
            //var startdt = stdt.ToString("dd-MMM-yy");
            //var endt = Convert.ToDateTime(txtend.Text);
            //var enddt = endt.ToString("dd-MMM-yy");

            IList<UserActivity> lstUser = new List<UserActivity>();
            IList<UserActivity> data = new List<UserActivity>();
            //var data = ;
            if (ddlusename.SelectedItem.Text == "All")
            {
               // lstUser = Facade.GetUserActivityByStartEndDate(startdt, enddt);
                if (_isUserEnterpriseUser)
                {
                    data = Facade.GetUserActivityByStartEndDate(startdt, enddt);
                }
                else
                {
                     data = Facade.GetAllUsersWithout_EnterpriseUser(startdt, enddt);
                }
                
                string Value = chkaproval.Checked ? "1" : "0";
                string val = CheckOTP.Checked ? "1" : "0";

                if (Value == "1")
                {
                    var Data = data.Where(a => a.ActivityDetails.Contains("CP Profile Approval"));
                    lstUser = (from grp in Data select grp).ToList();
                }
                // lstUser = (from grp in data where grp.CreatorId == LoggedInUserId && grp.LoginName == LoggedInUserName select grp).ToList();         
                else if (val == "1")
                {
                    var Data = data.Where(a => a.ActivityDetails.Contains("CP OTP"));
                    lstUser = (from grp in Data select grp).ToList();
                }

                else
                {
                    lstUser = (from grp in data select grp).ToList();
                }
            }
            else
            {
                //string loginname = ddlusename.SelectedItem.ToString();

                int loginname = Convert.ToInt32(ddlusename.SelectedItem.Value);

                int userid = Convert.ToInt32(ddlusename.SelectedValue);
              //  lstUser = Facade.GetUserActivityByDate(loginname, startdt, enddt);

                if (_isUserEnterpriseUser)
                {
                    //lstUser = Facade.GetUserActivityByDate(loginname, startdt, enddt);

                    lstUser = Facade.GetUserActivityByDate_New(loginname, startdt, enddt);
                }
                else
                {
                    lstUser = Facade.GetAllUsersWithout_EnterpriseUserByDate_new(loginname, startdt, enddt);
                }
                string Value = chkaproval.Checked ? "1" : "0";
                string val = CheckOTP.Checked ? "1" : "0";

                if (Value == "1")
                {
                    var Data = lstUser.Where(a => a.ActivityDetails.Contains("CP Profile Approval"));
                    lstUser = (from grp in Data select grp).ToList();
                }
                // lstUser = (from grp in data where grp.CreatorId == LoggedInUserId && grp.LoginName == LoggedInUserName select grp).ToList();         
                else if (val == "1")
                {
                    var Data = lstUser.Where(a => a.ActivityDetails.Contains("CP OTP"));
                    lstUser = (from grp in Data select grp).ToList();
                }
                else 
                {
                    lstUser = (from grp in lstUser select grp).ToList();
                }
            }


            if (lstUser != null && lstUser.Count != 0)
            {
                // btnview.Visible = false;
                var trow = new TableRow();
                tbl.Rows.Add(trow);
                trow.Height = 30;

                var sno = new TableCell { Text = "Sr.No", CssClass = "RowStyleHeaderNo bold" };
                trow.Cells.Add(sno);
                var lgnm = new TableCell { Text = "Login Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(lgnm);
                //var ent = new TableCell { Text = "Entity", CssClass = "rowStyleHeader bold" };
                //trow.Cells.Add(ent);
                var actiontp = new TableCell { Text = "ActionType", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(actiontp);
                var uhostadd = new TableCell { Text = "User Host Address", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(uhostadd);
                var actdetals = new TableCell { Text = "Activity Details", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(actdetals);
                var createdate = new TableCell { Text = "Activity Date", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(createdate);

                int i = 1;
                _logger.Info("======" + lstUser.Count + " Records Retrieve for " + ddlusename.SelectedItem.Text + " User======");
                _logger.Info(Environment.NewLine);
                foreach (var avtivitydata in lstUser)
                {

                    var tbrow = new TableRow();
                    tbrow.Height = 20;
                    tbrow.BackColor = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                    tbl.Rows.Add(tbrow);
                    DataRow dr = table.NewRow();


                    dr["Sr.No."] = i.ToString();
                    var no = new TableCell { Text = i.ToString(), CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(no);

                    dr["Login Name"] = Utility.putnewline(avtivitydata.LoginName, 25);
                    var lnm = new TableCell { Text = avtivitydata.LoginName != null ? avtivitydata.LoginName : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(lnm);

                    //dr["Entity"] = avtivitydata.Entity.ToString();
                    //var enti = new TableCell { Text = avtivitydata.Entity != null ? avtivitydata.Entity : "NA", CssClass = "rowStyle1" };
                    //tbrow.Cells.Add(enti);

                    dr["ActionType"] = Utility.putnewline(avtivitydata.ActionType.ToString(), 28);
                    var actioty = new TableCell { Text = avtivitydata.ActionType != null ? avtivitydata.ActionType.ToString() : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(actioty);

                    //dr["User Host Address"] = avtivitydata.UserHostAddress;
                    //var hst = new TableCell { Text = avtivitydata.UserHostAddress != null ? avtivitydata.UserHostAddress : "NA", CssClass = "rowStyle1" };
                    //tbrow.Cells.Add(hst);

                    string IP = string.Empty;
                    if (!string.IsNullOrEmpty(avtivitydata.UserHostAddress) && avtivitydata.UserHostAddress != "NA" && avtivitydata.UserHostAddress != "N/A")
                    {
                        if (avtivitydata.UserHostAddress.Contains("::1"))
                        {
                            IP = "127.0. 0.1";
                        }
                        else
                            IP = avtivitydata.UserHostAddress;
                    }

                    dr["User Host Address"] = !string.IsNullOrEmpty(IP) ? IP : "NA";// avtivitydata.UserHostAddress;
                    var hst = new TableCell { Text = !string.IsNullOrEmpty(IP) ? IP : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(hst);

                    //dr["Activity Details"] = avtivitydata.ActivityDetails.Length > 16 ? avtivitydata.ActivityDetails.Insert(16, "\n") : avtivitydata.ActivityDetails;
                    dr["Activity Details"] = Utility.putnewline(avtivitydata.ActivityDetails, 28);
                    var acdetls = new TableCell { Text = avtivitydata.ActivityDetails != null ? avtivitydata.ActivityDetails : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(acdetls);

                    dr["Activity Date"] = avtivitydata.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(avtivitydata.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                    var cdate = new TableCell { Text = avtivitydata.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(avtivitydata.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(cdate);

                    i++;
                    table.Rows.Add(dr);

                }


                _logger.Info("====== User Activity Report generated ======");
                _logger.Info(Environment.NewLine);

            }
            else
            {

                lblMsg.Visible = true;
                lblMsg.Text = "No Records Found";
                _logger.Info("====== User Activity Report not generated ======");
                _logger.Info(Environment.NewLine);
            }

            return table;
        }

        private void CreatePdfReport(DataTable table)
        {

            _logger.Info("======Generating User Activity Report PDF View ======");
            _logger.Info(Environment.NewLine);
            var myPdfDocument = new PdfDocument(PdfDocumentFormat.InCentimeters(21, 29.7));
            var count = table.Rows.Count;
            PdfTable myPdfTable = myPdfDocument.NewTable(new Font("Verdana", 7), count, 6, 3);
            myPdfTable.ImportDataTable(table);
            myPdfTable.HeadersRow.SetColors(Color.White, Color.FromArgb(79, 129, 189));
            myPdfTable.HeadersRow.SetContentAlignment(ContentAlignment.MiddleLeft);
            myPdfTable.SetBorders(Color.Black, 0.1, BorderType.None);
            myPdfTable.SetColors(Color.Black, Color.FromArgb(219, 229, 241), Color.White);
            myPdfTable.SetColumnsWidth(new[] { 5, 25, 28, 15, 28, 20 });
            myPdfTable.SetContentAlignment(ContentAlignment.MiddleLeft);
            myPdfTable.Columns[1].SetContentAlignment(ContentAlignment.MiddleLeft);

            //var getuseractvdate = Facade.GetUserActivityByStartEndDate(txtstart.Text, txtend.Text);


            PdfImage logoImage = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"));
            PdfImage logoBcms = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"));

            string strlogo = LoggedInUserCompany.CompanyLogoPath.ToString();

            PdfImage complogo = null;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                complogo = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(strlogo));
            }


            var pta = new PdfTextArea(new Font("Verdana", 9, FontStyle.Bold), Color.Black
                    , new PdfArea(myPdfDocument, 0, 20, 595, 80), ContentAlignment.MiddleCenter, "User Activity Report");


            var RGT = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 30, 00, 200, 190), ContentAlignment.MiddleRight, "Report Generated Time : " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"))); //+ DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss"));

            var from = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 195, 00, 150, 190), ContentAlignment.MiddleRight, "From Date: " + Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"));

            var to = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 245, 00, 200, 190), ContentAlignment.MiddleRight, "To Date: " + Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"));

            var notavailable = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                     , new PdfArea(myPdfDocument, 17, 15, 100, 190), ContentAlignment.MiddleRight, "NA : Not Available");



            int pgNo = 1;
            while (!myPdfTable.AllTablePagesCreated)
            {
                PdfPage newPdfPage = myPdfDocument.NewPage();
                PdfTablePage newPdfTablePage =
                        myPdfTable.CreateTablePage(new PdfArea(myPdfDocument, 48, 115, 500, 670));

                var pageNumber = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                   , new PdfArea(myPdfDocument, 50, 0, 450, 1600), ContentAlignment.MiddleRight, "Page Number :   " + pgNo++.ToString());

                newPdfPage.Add(logoImage, 460, 25, 180);
                newPdfPage.Add(logoBcms, 50, 25, 110);

                if (complogo != null)
                    newPdfPage.Add(complogo, 290, 25, 120);

                newPdfPage.Add(newPdfTablePage);
                newPdfPage.Add(pta);


                newPdfPage.Add(from);
                newPdfPage.Add(to);
                newPdfPage.Add(RGT);
                newPdfPage.Add(notavailable);
                newPdfPage.Add(pageNumber);
                newPdfPage.SaveToDocument();
            }

            string str = DateTime.Now.ToString().Replace("/", "");
            str = str.Replace(":", "");
            str = str.Substring(0, str.Length - 5);
            str = System.Text.RegularExpressions.Regex.Replace(str, @"\s", "");
            str = "User Activity Report" + str + ".pdf";
            string filePath = HttpContext.Current.Server.MapPath(@"~/PdfFiles/" + str);
            //string myUrl = "/PdfFiles/" + str;
            myPdfDocument.SaveToFile(filePath);
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/PdfFiles/" + str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=User Activity Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
            _logger.Info("====== User Activity PDF Report generated ======");
            _logger.Info(Environment.NewLine);


        }

        private void ExcelReport()
        {
            _logger.Info("======Generating User Activity Report EXCEL View ======");
            _logger.Info(Environment.NewLine);

            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "User Activity Report";

            _cells["A1"].ColumnWidth = 7;

            _cells["E3"].Formula = "User Activity Report";
            _cells["E3"].HorizontalAlignment = HAlign.Center;
            _cells["E3"].Font.Bold = true;
            _cells["B3:G6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:G3"].Font.Size = 11;
            _cells["B5:G8"].Font.Size = 10;
            _cells["B3:G8"].Font.Color = Color.White;
            _cells["B3:G8"].Font.Name = "Cambria";


            IFacade _facade = new Facade();

            string startdt = Utility.getFormatedDate_New(txtstart.Text);//string startdt = Utility.getFormatedDate(txtstart.Text);
            string enddt = Utility.getFormatedDate_New(txtend.Text);//string enddt = Utility.getFormatedDate(txtend.Text);

            //var stdt = Convert.ToDateTime(txtstart.Text);
            //var startdt = stdt.ToString("dd-MMM-yy");
            //var endt = Convert.ToDateTime(txtend.Text);
            //var enddt = endt.ToString("dd-MMM-yy");

            IList<UserActivity> lstUser = new List<UserActivity>();

            if (ddlusename.SelectedItem.Text == "All")
            {
                if (_isUserEnterpriseUser)
                {
                    lstUser = Facade.GetUserActivityByStartEndDate(startdt, enddt);
                }
                else
                {
                    lstUser = Facade.GetAllUsersWithout_EnterpriseUser(startdt, enddt);
                }

                string Value = chkaproval.Checked ? "1" : "0";
                string val = CheckOTP.Checked ? "1" : "0";

                if (Value == "1")
                {
                    var Data = lstUser.Where(a => a.ActivityDetails.Contains("CP Profile Approval"));
                    lstUser = (from grp in Data select grp).ToList();
                }
                // lstUser = (from grp in data where grp.CreatorId == LoggedInUserId && grp.LoginName == LoggedInUserName select grp).ToList();         
                else if (val == "1")
                {
                    var Data = lstUser.Where(a => a.ActivityDetails.Contains("CP OTP"));
                    lstUser = (from grp in Data select grp).ToList();
                }

                else
                {
                    lstUser = (from grp in lstUser select grp).ToList();
                }
            }
            else
            {
                string loginname = ddlusename.SelectedItem.ToString();
              //int userid = Convert.ToInt32(ddlusename.SelectedValue);
                int loginId = Convert.ToInt32(ddlusename.SelectedValue);
          //      lstUser = Facade.GetUserActivityByDate(loginname, startdt, enddt);
              
                if (_isUserEnterpriseUser)
                {
                    lstUser = Facade.GetUserActivityByDate(loginId, startdt, enddt);           
                }
                else
                {
                    lstUser = Facade.GetAllUsersWithout_EnterpriseUserByDate(loginname, startdt, enddt);
                }

                string Value = chkaproval.Checked ? "1" : "0";
                string val = CheckOTP.Checked ? "1" : "0";

                if (Value == "1")
                {
                    var Data = lstUser.Where(a => a.ActivityDetails.Contains("CP Profile Approval"));
                    lstUser = (from grp in Data select grp).ToList();
                }
                // lstUser = (from grp in data where grp.CreatorId == LoggedInUserId && grp.LoginName == LoggedInUserName select grp).ToList();         
                else if (val == "1")
                {
                    var Data = lstUser.Where(a => a.ActivityDetails.Contains("CP OTP"));
                    lstUser = (from grp in Data select grp).ToList();
                }
                else
                {
                    lstUser = (from grp in lstUser select grp).ToList();
                }
            }

            //var getdetail = _facade.GetUserActivityByStartEndDate(startdt, enddt);

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 48, 10, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 640, 10, 120, 13);
            string strlogo = LoggedInUserCompany.CompanyLogoPath;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 370, 10, 121, 13);
            }

            reportWorksheet.Cells["A1:F1"].RowHeight = 27;
            reportWorksheet.Cells["A2:F2"].RowHeight = 25;

            _cells["B6"].Formula = "From Date";
            _cells["B6"].Font.Bold = true;
            _cells["B6"].HorizontalAlignment = HAlign.Left;

            _cells["C6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtstart.Text;
            _cells["C6"].Font.Bold = true;
            _cells["C6"].HorizontalAlignment = HAlign.Left;

            // var dateTime = DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss");
            _cells["B5"].Formula = "Report Generated Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            _cells["C5"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));// dateTime;
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;

            _cells["F6"].Formula = "To Date";
            _cells["F6"].Font.Bold = true;
            _cells["F6"].HorizontalAlignment = HAlign.Right;

            _cells["G6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtend.Text;
            _cells["G6"].Font.Bold = true;
            _cells["G6"].HorizontalAlignment = HAlign.Left;

            int row = 8;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "Sr.No.";
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
            _cells["B8:G8"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B8:G8"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "Login Name";
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
            productionlog.WrapText = true;

            //_cells["D" + row.ToString()].Formula = "Entity";
            //_cells["D" + row.ToString()].Font.Bold = true;
            //_cells["D" + row.ToString()].HorizontalAlignment = HAlign.Center;

            _cells["D" + row.ToString()].Formula = "Action Type";
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["E" + row.ToString()].Formula = "User Host Address";
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["F" + row.ToString()].Formula = "Activity Details";
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["G" + row.ToString()].Formula = "Activity Date";
            _cells["G" + row.ToString()].Font.Bold = true;
            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;


            row++;
            int dataCount = 0;
            int xlRow = 9;

            foreach (var rp in lstUser)
            {
                dataCount++;
                int column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F", "G" };
                xlRow++;

                string ndx = xlColumn[column] + row.ToString();
                _cells[ndx + ":" + "G" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;



                _cells[ndx].Formula = i.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                i++;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.LoginName != null ? rp.LoginName : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                //ndx = xlColumn[column] + row.ToString();
                //_cells[ndx].Formula = rp.Entity != null ? rp.Entity : "NA";
                //_cells[ndx].Font.Size = 10;
                //_cells[ndx].ColumnWidth = 23;
                //_cells[ndx].Font.Color = Color.Black;
                //_cells[ndx].HorizontalAlignment = HAlign.Left;
                //column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.ActionType.ToString(); ;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                _cells[ndx].WrapText = true;
                column++;


                string IP = string.Empty;
                if (!string.IsNullOrEmpty(rp.UserHostAddress) && rp.UserHostAddress != "NA" && rp.UserHostAddress != "N/A")
                {
                    if (rp.UserHostAddress.Contains("::1"))
                    {
                        IP = "127.0. 0.1";
                    }
                    else
                        IP = rp.UserHostAddress;
                }

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(IP) ? IP : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.ActivityDetails != null ? rp.ActivityDetails : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                _cells[ndx].WrapText = true;
                column++;


                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].EntireColumn.NumberFormat = "@";
                //   _cells[ndx].Formula = rp.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(rp.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                _cells[ndx].Formula = rp.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(rp.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                row++;
            }
            _logger.Info("======" + lstUser.Count + " Records Retrieve for " + ddlusename.SelectedItem.Text + " User======");
            _logger.Info(Environment.NewLine);

            int finalCount = dataCount + 10;
            _cells["B" + finalCount].Formula = "NA : Not Available";
            _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
            _cells["B" + finalCount].Font.Name = "Cambria";
            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

            reportWorksheet.ProtectContents = true;
            OpenExcelFile(reportWorkbook);

            _logger.Info("====== User Activity EXCEL Report generated ======");
            _logger.Info(Environment.NewLine);


        }

        private void OpenExcelFile(IWorkbook workbook)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "UserActivityReport" + str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + str;
            //var myUrl = "/ExcelFiles/" + str;
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=User Activity Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }

        protected void btnview_Click(object sender, EventArgs e)
        {
            try
            {
                var stdt = Convert.ToDateTime(txtstart.Text);
                var startdt = stdt.ToString("yyyy-MM-dd");
                var endt = Convert.ToDateTime(txtend.Text);
                var enddt = endt.ToString("yyyy-MM-dd");

                string dat = DateTime.Now.ToString("yyyy-MM-dd");
                if (Convert.ToDateTime(stdt) > Convert.ToDateTime(endt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "Start Date Greater than End Date";
                    Pan1.Visible = false;
                    return;
                }
                else if (Convert.ToDateTime(dat) < Convert.ToDateTime(stdt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "Start Date can't Greater than Today Date";
                    Pan1.Visible = false;
                    return;
                }
                else if (Convert.ToDateTime(dat) < Convert.ToDateTime(endt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "End Date can't Greater than Today Date";
                    Pan1.Visible = false;
                    return;
                }

                Pan1.Visible = true;

                btnview.Visible = false;
                screenreport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }

            btnPdf.Visible = true;
            btnExcel.Visible = true;
        }

        protected void btnExcel_Click(object sender, EventArgs e)
        {
            try
            {
                screenreport();
                ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void btnPdf_Click(object sender, EventArgs e)
        {
            try
            {
                var table = screenreport();
                CreatePdfReport(table);
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void txtstart_TextChanged(object sender, EventArgs e)
        {
            try
            {
                btnview.Visible = true;
                lblMsg.Visible = false;
                Pan1.Visible = false;
                if (!string.IsNullOrEmpty(txtstart.Text))
                {
                    var enddt = Convert.ToDateTime(txtstart.Text);
                    CalendarExtender2.StartDate = enddt;
                }
                txtend.Text = string.Empty;
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured on Start Date Text Changed for User Activity Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void txtend_TextChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
            Pan1.Visible = false;
            btnview.Visible = true;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            //RegisterPostBackControl();ddlusename
        }

        protected void ddlusename_SelectedIndexChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
            btnview.Visible = true;
            Pan1.Visible = false;
        }

        private void RegisterPostBackControl()
        {
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnview);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(ddlusename);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(txtstart);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(txtend);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnPdf);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnExcel);
        }
         
        protected void CheckOTP_CheckedChanged(object sender, EventArgs e)
        {
            if (CheckOTP.Checked)
            {
                chkaproval.Checked = false;

            }
        }

        protected void chkaproval_CheckedChanged(object sender, EventArgs e)
        {
            if (chkaproval.Checked)
            {
                CheckOTP.Checked = false;

            }
        }

   
    }
}