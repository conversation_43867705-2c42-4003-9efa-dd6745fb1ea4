﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess
{
    #region IBaseDatabaseDataAccess

    public interface IDatabaseBaseDataAccess
    {
        DatabaseBase Add(DatabaseBase database);

        DatabaseBase Update(DatabaseBase database);

        DatabaseBase GetById(int id);

        DatabaseBase GetDatabaseNameById(DatabaseType databaseType, int id);

        IList<DatabaseBase> GetAll();

        IList<DatabaseBase> GetAllNotConfigured();

        IList<DatabaseBase> GetByType(DatabaseType type);

        IList<DatabaseBase> GetByTypeAndCompanyId(DatabaseType type, int companyId, bool isParent);

        IList<DatabaseBase> GetAllDatabaseByUserCompanyId(int companyId, bool isParent);

        IList<DatabaseBase> GetByServerId(int serverId);

        IList<DatabaseBase> GetByServerIdAndType(int serverId, string type);

        IList<DatabaseBase> GetByInfraObjectId(int infraObjectId);

        bool DeleteById(int id);

        bool IsExistByName(string name);

        bool UpdateByMode(int id, string mode);

        //DatabaseBase GetByGroupIdPRServerId(int groupId);

        DatabaseBase GetByInfraObjectIdPRServerId(int groupId);

        DatabaseBase GetByGroupIdDrServerId(int groupId);

        IList<DatabaseBase> GetDatabaseBasetypecompanyiduserid(DatabaseType type, int companyid, int userid, string role);

        IList<DatabaseBase> GetDatabaseBaseUserInfraId(int userid);

        IList<DatabaseBase> GetDatabBaseId(int id);
    }

    #endregion IBaseDatabaseDataAccess
}