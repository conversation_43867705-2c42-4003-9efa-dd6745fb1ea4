﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using Gios.Pdf;
using SpreadsheetGear;
using System.IO;
using log4net;


namespace CP.UI.Controls
{
    public partial class SMSReportlist : BaseControl
    {
        public static Int32 tcount;
        private readonly ILog _logger = LogManager.GetLogger(typeof(SMSReportlist));
        public override void PrepareView()
        {
            CalendarExtender1.EndDate = DateTime.Now.Date;
            CalendarExtender2.EndDate = DateTime.Now.Date;
            //IList<User> userall = Facade.GetAlUsers();

            //if (userall != null)
            //{
            //    ddlusename.DataSource = userall;
            //    ddlusename.DataTextField = "LoginName";
            //    ddlusename.DataValueField = "Id";
            //    ddlusename.DataBind();
            //    ddlusename.Items.Insert(0, "All");
            //}
            //else
            //{
            //    ddlusename.Items.Insert(0, new ListItem("No User Name Found", "0"));
            //}

            Pan1.Visible = false;
            btnview.Visible = true;
            btnExcel.Visible = false;
            btnPdf.Visible = false;

        }

        private DataTable screenreport()
        {
            _logger.Info("======Generating SMS Details Report HTML View ======");
            _logger.Info(Environment.NewLine);

            tcount = 0;
            var table = new DataTable();
            table.Columns.Add("Sr.No.");
            table.Columns.Add("Receiver Name");
            table.Columns.Add("SMS Description");
            table.Columns.Add("InfraObject Name");
            table.Columns.Add("SMS Count");
            //table.Columns.Add("Activity Details");
            table.Columns.Add("SMS Date");


            string startdt = Utility.getFormatedDate_New(txtstart.Text);
            string enddt = Utility.getFormatedDate_New(txtend.Text);

            //var stdt = Convert.ToDateTime(txtstart.Text);
            //var startdt = stdt.ToString("dd-MMM-yy");
            //var endt = Convert.ToDateTime(txtend.Text);
            //var enddt = endt.ToString("dd-MMM-yy");

            IList<SMSReport> lstemail = new List<SMSReport>();

            //if (ddlusename.SelectedItem.Text == "All")
            //{
            //    lstUser = Facade.GetUserActivityByStartEndDate(startdt, enddt);
            //}
            //else
            //{
            //    string loginname = ddlusename.SelectedItem.ToString();
            //    lstUser = Facade.GetUserActivityByDate(loginname, startdt, enddt);
            //}

            lstemail = Facade.GetSMSByDate(startdt, enddt);
            Int32 totalemailcount = 0;
            if (lstemail != null && lstemail.Count != 0)
            {

             

                var trow = new TableRow();
                tbl.Rows.Add(trow);
                trow.Height = 30;

                var sno = new TableCell { Text = "Sr.No", CssClass = "RowStyleHeaderNo bold" };
                trow.Cells.Add(sno);
                var lgnm = new TableCell { Text = "Receiver Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(lgnm);
                var ent = new TableCell { Text = "SMS Description", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(ent);

                var actiontp = new TableCell { Text = "InfraObject Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(actiontp);
                var uhostadd = new TableCell { Text = "SMS Count", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(uhostadd);
                //var actdetals = new TableCell { Text = "Activity Details", CssClass = "rowStyleHeader bold" };
                //trow.Cells.Add(actdetals);

                var createdate = new TableCell { Text = "SMS Date", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(createdate);

                int i = 1;
                _logger.Info("======" + lstemail.Count + " Records Retrieve for SMS Details Report ======");
                _logger.Info(Environment.NewLine);
                foreach (var avtivitydata in lstemail)
                {
                    totalemailcount = totalemailcount + Convert.ToInt32(avtivitydata.SMSCount.ToString());

                    var tbrow = new TableRow();
                    tbrow.Height = 20;
                    tbrow.BackColor = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                    tbl.Rows.Add(tbrow);
                    DataRow dr = table.NewRow();


                    dr["Sr.No."] = i.ToString();
                    var no = new TableCell { Text = i.ToString(), CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(no);

                    dr["Receiver Name"] = avtivitydata.UserName;
                    var lnm = new TableCell { Text = avtivitydata.UserName != null ? avtivitydata.UserName : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(lnm);

                    dr["SMS Description"] = avtivitydata.SMSDescription.ToString();
                    var enti = new TableCell { Text = avtivitydata.SMSDescription != null ? avtivitydata.SMSDescription : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(enti);

                    dr["InfraObject Name"] = avtivitydata.InfraobjectName.ToString();
                    var actioty = new TableCell { Text = avtivitydata.InfraobjectName != null ? avtivitydata.InfraobjectName.ToString() : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(actioty);

                    dr["SMS Count"] = avtivitydata.SMSCount;
                    var hst = new TableCell { Text = avtivitydata.SMSCount != null ? avtivitydata.SMSCount.ToString() : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(hst);

                    //dr["Activity Details"] = avtivitydata.ActivityDetails.Length > 16 ? avtivitydata.ActivityDetails.Insert(16, "\n") : avtivitydata.ActivityDetails;
                    //var acdetls = new TableCell { Text = avtivitydata.ActivityDetails != null ? avtivitydata.ActivityDetails : "NA", CssClass = "rowStyle1" };
                    //tbrow.Cells.Add(acdetls);

                    dr["SMS Date"] = avtivitydata.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(avtivitydata.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) :"NA"; //Utility.Formatdate(avtivitydata.CreateDate.ToString()) : "NA";
                    var cdate = new TableCell { Text = avtivitydata.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(avtivitydata.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) :"NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(cdate);

                    i++;
                    table.Rows.Add(dr);
                }
                tcount = totalemailcount;

                rptemail.InnerHtml = "SMS Activity Report     Total Count : " + tcount;
                _logger.Info("====== SMS Details Report HTML generated ======");
                _logger.Info(Environment.NewLine);
            }
            else
            {
                
                lblMsg.Visible = true;
                lblMsg.Text = "No Records Found";
                _logger.Info("====== SMS Details Report HTML not generated ======");
                _logger.Info(Environment.NewLine);
            }

            return table;
        }

        private void CreatePdfReport(DataTable table)
        {
            _logger.Info("======Generating SMS Details Report PDF View ======");
            _logger.Info(Environment.NewLine);
            
            var myPdfDocument = new PdfDocument(PdfDocumentFormat.InCentimeters(21, 29.7));
            var count = table.Rows.Count;
            PdfTable myPdfTable = myPdfDocument.NewTable(new Font("Verdana", 7), count, 7, 3);
            myPdfTable.ImportDataTable(table);
            myPdfTable.HeadersRow.SetColors(Color.White, Color.FromArgb(79, 129, 189));
            myPdfTable.HeadersRow.SetContentAlignment(ContentAlignment.MiddleLeft);
            myPdfTable.SetBorders(Color.Black, 0.1, BorderType.None);
            myPdfTable.SetColors(Color.Black, Color.FromArgb(219, 229, 241), Color.White);
            myPdfTable.SetColumnsWidth(new[] { 20, 30, 60, 35, 30 });
            myPdfTable.SetContentAlignment(ContentAlignment.MiddleLeft);
            myPdfTable.Columns[1].SetContentAlignment(ContentAlignment.MiddleLeft);

            //var getuseractvdate = Facade.GetUserActivityByStartEndDate(txtstart.Text, txtend.Text);


            PdfImage logoImage = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"));
            PdfImage logoBcms = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"));
            string strlogo = LoggedInUserCompany.CompanyLogoPath.ToString();

            PdfImage complogo = null;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                complogo = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(strlogo));
            }

          


            var pta = new PdfTextArea(new Font("Verdana", 9, FontStyle.Bold), Color.Black
                    , new PdfArea(myPdfDocument, 0, 20, 595, 80), ContentAlignment.MiddleCenter, "SMS Details Report");


            var RGT = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                    , new PdfArea(myPdfDocument, 64, 00, 200, 190), ContentAlignment.MiddleLeft, "Report Generated Time : " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")));//+ DateTime.Now.ToString("dd-MMM-yyyy HH:mm"));

            var from = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                   , new PdfArea(myPdfDocument, 280, 00, 150, 190), ContentAlignment.MiddleLeft, "From Date: " + Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"));


            var TC = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                  , new PdfArea(myPdfDocument, 64, 15, 200, 190), ContentAlignment.MiddleLeft, "Total Count : " + tcount);


          
            var to = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 280, 15, 200, 190), ContentAlignment.MiddleLeft, "To Date: " + Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"));

            var notavailable = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 380, 15, 100, 190), ContentAlignment.MiddleRight, "NA : Not Available");


            int pgNo = 1;
            while (!myPdfTable.AllTablePagesCreated)
            {
                PdfPage newPdfPage = myPdfDocument.NewPage();
                PdfTablePage newPdfTablePage =
                        myPdfTable.CreateTablePage(new PdfArea(myPdfDocument, 48, 115, 500, 670));

                var pageNumber = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                   , new PdfArea(myPdfDocument, 50, 0, 450, 1600), ContentAlignment.MiddleRight, "Page Number :   " + pgNo++.ToString());

                newPdfPage.Add(logoImage, 460, 25, 180);
                newPdfPage.Add(logoBcms, 50, 25, 110);
                if (complogo != null)
                    newPdfPage.Add(complogo, 290, 25, 120);
                newPdfPage.Add(newPdfTablePage);
                newPdfPage.Add(pta);


                newPdfPage.Add(from);
                newPdfPage.Add(to);
                newPdfPage.Add(RGT);
                newPdfPage.Add(TC);
                newPdfPage.Add(notavailable);
                newPdfPage.Add(pageNumber);
                newPdfPage.SaveToDocument();
            }

            string str = DateTime.Now.ToString().Replace("/", "");
            str = str.Replace(":", "");
            str = str.Substring(0, str.Length - 5);
            str = System.Text.RegularExpressions.Regex.Replace(str, @"\s", "");
            str = "SMS Details Report" + str + ".pdf";
            string filePath = HttpContext.Current.Server.MapPath(@"~/PdfFiles/" + str);
            //string myUrl = "/PdfFiles/" + str;
            myPdfDocument.SaveToFile(filePath);
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/PdfFiles/" + str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=SMS Details Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
            _logger.Info("====== SMS Details PDF Report generated ======");
            _logger.Info(Environment.NewLine);
        }

        private void ExcelReport()
        {
            _logger.Info("======Generating SMS Details Report EXCEL View ======");
            _logger.Info(Environment.NewLine);
            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "SMS Details Report";

            _cells["A1"].ColumnWidth = 7;

            _cells["D3"].Formula = "SMS Details Report";
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].Font.Bold = true;
            _cells["B3:H6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:H3"].Font.Size = 11;
            _cells["B5:H8"].Font.Size = 10;
            _cells["B3:H8"].Font.Color = Color.White;
            _cells["B3:H8"].Font.Name = "Cambria";


            IFacade _facade = new Facade();

            string startdt = Utility.getFormatedDate(txtstart.Text);//string startdt = Utility.getFormatedDate(txtstart.Text);
            string enddt = Utility.getFormatedDate_New(txtend.Text);//string enddt = Utility.getFormatedDate(txtend.Text);

            //var stdt = Convert.ToDateTime(txtstart.Text);
            //var startdt = stdt.ToString("dd-MMM-yy");
            //var endt = Convert.ToDateTime(txtend.Text);
            //var enddt = endt.ToString("dd-MMM-yy");

            IList<SMSReport> lstemail = new List<SMSReport>();

            //if (ddlusename.SelectedItem.Text == "All")
            //{
            //    lstUser = Facade.GetUserActivityByStartEndDate(startdt, enddt);
            //}
            //else
            //{
            //    string loginname = ddlusename.SelectedItem.ToString();
            //    lstUser = Facade.GetUserActivityByDate(loginname, startdt, enddt);
            //}

            lstemail = Facade.GetSMSByDate(startdt, enddt);

            //var getdetail = _facade.GetUserActivityByStartEndDate(startdt, enddt);

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 48, 10, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 690, 10, 120, 13);
            string strlogo = LoggedInUserCompany.CompanyLogoPath;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 400, 10, 121, 13);
            }

            reportWorksheet.Cells["A1:F1"].RowHeight = 27;
            reportWorksheet.Cells["A2:F2"].RowHeight = 25;

            _cells["E6"].Formula = "Total Count";
            _cells["B6"].Font.Bold = true;
            _cells["B6"].HorizontalAlignment = HAlign.Left;

            Int32 totalcount = 0;

            _cells["C6"].Formula = ":  ";
            _cells["C6"].Font.Bold = true;
            _cells["C6"].HorizontalAlignment = HAlign.Left;

            _cells["E5"].Formula = "From Date";
            _cells["E5"].Font.Bold = true;
            _cells["E5"].HorizontalAlignment = HAlign.Left;

            _cells["F5"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //txtstart.Text;
            _cells["F5"].Font.Bold = true;
            _cells["F5"].HorizontalAlignment = HAlign.Left;

            var dateTime =  Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));//DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
            _cells["B5"].Formula = "Report Generated Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            _cells["C5"].Formula = ":  " + dateTime;
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;

            _cells["E6"].Formula = "To Date";
            _cells["E6"].Font.Bold = true;
            _cells["E6"].HorizontalAlignment = HAlign.Left;

            _cells["F6"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); // txtend.Text;
            _cells["F6"].Font.Bold = true;
            _cells["F6"].HorizontalAlignment = HAlign.Left;

            int row = 8;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "Sr.No.";
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
            _cells["B8:H8"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B8:H8"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "Receiver Name";
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
            productionlog.WrapText = true;

            _cells["D" + row.ToString()].Formula = "SMS Description";
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
            _cells["D" + row.ToString()].ColumnWidth = 100;
            _cells["D" + row.ToString()].WrapText = true;


            _cells["E" + row.ToString()].Formula = "InfraObject Name";
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["F" + row.ToString()].Formula = "SMS Count";
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

            //_cells["G" + row.ToString()].Formula = "Activity Details";
            //_cells["G" + row.ToString()].Font.Bold = true;
            //_cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["G" + row.ToString()].Formula = "SMS Date";
            _cells["G" + row.ToString()].Font.Bold = true;
            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;


            row++;
            int dataCount = 0;
            int xlRow = 9;
            _logger.Info("======" + lstemail.Count + " Records Retrieve for SMS Details Report ======");
            _logger.Info(Environment.NewLine);
            foreach (var rp in lstemail)
            {
                dataCount++;
                int column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H" };
                xlRow++;

                string ndx = xlColumn[column] + row.ToString();
                _cells[ndx + ":" + "H" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;

                totalcount = totalcount + rp.SMSCount;

                _cells[ndx].Formula = i.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                i++;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.UserName != null ? rp.UserName : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.SMSDescription != null ? rp.SMSDescription : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.InfraobjectName != null ? rp.InfraobjectName : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                _cells[ndx].WrapText = true;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = rp.SMSCount.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                //ndx = xlColumn[column] + row.ToString();
                //_cells[ndx].Formula = rp.ActivityDetails != null ? rp.ActivityDetails : "NA";
                //_cells[ndx].Font.Size = 10;
                //_cells[ndx].ColumnWidth = 23;
                //_cells[ndx].Font.Color = Color.Black;
                //_cells[ndx].HorizontalAlignment = HAlign.Left;
                //_cells[ndx].WrapText = true;
                //column++;


                 ndx = xlColumn[column] + row.ToString();
                _cells[ndx].EntireColumn.NumberFormat = "@";
                _cells[ndx].Formula = rp.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(rp.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA"; // Utility.Formatdate(rp.CreateDate.ToString()) : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                row++;
            }

            _cells["B6"].Formula = "Total Count";
            _cells["B6"].Font.Bold = true;
            _cells["B6"].HorizontalAlignment = HAlign.Left;

            tcount = totalcount;


            _cells["C6"].Formula = ":  " + totalcount;
            _cells["C6"].Font.Bold = true;
            _cells["C6"].HorizontalAlignment = HAlign.Left;

            int finalCount = dataCount + 10;
            _cells["B" + finalCount].Formula = "NA : Not Available";
            _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
            _cells["B" + finalCount].Font.Name = "Cambria";
            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 6;

            reportWorksheet.ProtectContents = true;
            OpenExcelFile(reportWorkbook);
            _logger.Info("====== SMS Details EXCEL Report generated ======");
            _logger.Info(Environment.NewLine);

        }

        private void OpenExcelFile(IWorkbook workbook)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "SMSDetailsReport" + str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + str;
            //var myUrl = "/ExcelFiles/" + str;
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=SMS Details Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }

        protected void btnview_Click(object sender, EventArgs e)
        {
            try
            {
                var stdt = Convert.ToDateTime(txtstart.Text);
                var startdt = stdt.ToString("yyyy-MM-dd");
                var endt = Convert.ToDateTime(txtend.Text);
                var enddt = endt.ToString("yyyy-MM-dd");

                string dat = DateTime.Now.ToString("yyyy-MM-dd");
                if (Convert.ToDateTime(stdt) > Convert.ToDateTime(endt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "Start Date Greater than End Date";
                    Pan1.Visible = false;
                    return;
                }
                else if (Convert.ToDateTime(dat) < Convert.ToDateTime(stdt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "Start Date can't Greater than Today Date";
                    Pan1.Visible = false;
                    return;
                }
                else if (Convert.ToDateTime(dat) < Convert.ToDateTime(endt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "End Date can't Greater than Today Date";
                    Pan1.Visible = false;
                    return;
                }

                Pan1.Visible = true;

                screenreport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }

            btnPdf.Visible = true;
            btnExcel.Visible = true;
        }

        protected void btnExcel_Click(object sender, EventArgs e)
        {
            try
            {
                screenreport();
                ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void btnPdf_Click(object sender, EventArgs e)
        {
            try
            {
                var table = screenreport();
                CreatePdfReport(table);
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void txtstart_TextChanged(object sender, EventArgs e)
        {
            try
            {
                lblMsg.Visible = false;
                Pan1.Visible = false;
                if (!string.IsNullOrEmpty(txtstart.Text))
                {
                    var enddt = Convert.ToDateTime(txtstart.Text);
                    CalendarExtender2.StartDate = enddt;
                }
                txtend.Text = string.Empty;
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured on Start Date Text Changed for SMS Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void txtend_TextChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
            Pan1.Visible = false;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            //RegisterPostBackControl();
        }

        protected void ddlusename_SelectedIndexChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
        }

        private void RegisterPostBackControl()
        {
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnview);
            // ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(ddlusename);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(txtstart);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(txtend);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnPdf);
            ScriptManager.GetCurrent(this.Page).RegisterPostBackControl(btnExcel);
        }
    }
}