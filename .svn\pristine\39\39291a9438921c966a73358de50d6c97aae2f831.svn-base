﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RSyncMonitor", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class RSyncMonitor : BaseEntity
    {

        #region Properties

        [DataMember]
        public int RSyncJobId
        {
            get;
            set;
        }

        [DataMember]
        public int InfraobjectId
        {
            get;
            set;
        }

        [DataMember]
        public string SourceIP
        {
            get;
            set;
        }

        [DataMember]
        public string DestinationIP
        {
            get;
            set;
        }

        [DataMember]
        public string SourcePath
        {
            get;
            set;
        }

        [DataMember]
        public string DestinationPath
        {
            get;
            set;
        }

        [DataMember]
        public string RepStartTime
        {
            get;
            set;
        }

        [DataMember]
        public string RepEndTime
        {
            get;
            set;
        }

        [DataMember]
        public string Status
        {
            get;
            set;
        }

        [DataMember]
        public string SelectedOptions
        {
            get;
            set;
        }

        [DataMember]
        public string TotalFilesSize
        {
            get;
            set;
        }

        [DataMember]
        public string Datalag
        {
            get;
            set;
        }

        [DataMember]
        public string TotalNumberoffiles
        {
            get;
            set;
        }

        [DataMember]
        public string TotalTransferfileSize
        {
            get;
            set;
        }

        [DataMember]
        public string NumberOfRegFilesTransfer
        {
            get;
            set;
        }

        #endregion Properties
    }
}
