﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess.DataBaseMySql
{
    internal sealed class DatabaseMySqlBuilder : IEntityBuilder<DatabaseMySql>
    {
        IList<DatabaseMySql> IEntityBuilder<DatabaseMySql>.BuildEntities(IDataReader reader)
        {
            var databasemysqls = new List<DatabaseMySql>();

            while (reader.Read())
            {
                databasemysqls.Add(((IEntityBuilder<DatabaseMySql>)this).BuildEntity(reader, new DatabaseMySql()));
            }

            return (databasemysqls.Count > 0) ? databasemysqls : null;
        }

        DatabaseMySql IEntityBuilder<DatabaseMySql>.BuildEntity(IDataReader reader, DatabaseMySql databasemySql)
        {
            //const int FLD_ID = 0;
            //const int FLD_BASEDATABASEID = 1;
            ////const int FLD_SERVERID = 2;
            //const int FLD_DATABASESID = 2;
            //const int FLD_USERNAME = 3;
            //const int FLD_PASSWORD = 4;
            //const int FLD_PORT = 5;
            //const int FLD_AUTHENTICATIONMODE = 6;
            //const int FLD_DATAFILEPATH = 7;
            //const int FLD_TRANSLOGPATH = 8;
            //const int FLD_UNDOFILEPATH = 9;
            //const int FLD_BACKUPRESTOREPATH = 10;
            //const int FLD_NETWORKSHAREDPATH = 11;

            //databaseSql.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //databaseSql.BaseDatabaseId = reader.IsDBNull(FLD_BASEDATABASEID) ? 0 : reader.GetInt32(FLD_BASEDATABASEID);
            ////databaseSql.ServerId = reader.IsDBNull(FLD_SERVERID) ? 0 : reader.GetInt32(FLD_SERVERID);
            //databaseSql.DatabaseSID = reader.IsDBNull(FLD_DATABASESID) ? string.Empty : reader.GetString(FLD_DATABASESID);
            //databaseSql.UserName = reader.IsDBNull(FLD_USERNAME) ? string.Empty : reader.GetString(FLD_USERNAME);
            //databaseSql.Password = reader.IsDBNull(FLD_PASSWORD) ? string.Empty : reader.GetString(FLD_PASSWORD);
            //databaseSql.Port = reader.IsDBNull(FLD_PORT) ? 0 : reader.GetInt32(FLD_PORT);
            //databaseSql.AuthenticationMode = reader.IsDBNull(FLD_AUTHENTICATIONMODE) ? SqlAuthenticateType.Undefined : (SqlAuthenticateType)Enum.Parse(typeof(SqlAuthenticateType), reader.GetString(FLD_AUTHENTICATIONMODE), true);
            //databaseSql.DataFilePath = reader.IsDBNull(FLD_DATAFILEPATH) ? string.Empty : reader.GetString(FLD_DATAFILEPATH);
            //databaseSql.TransLogPath = reader.IsDBNull(FLD_TRANSLOGPATH) ? string.Empty : reader.GetString(FLD_TRANSLOGPATH);
            //databaseSql.UndoFilePath = reader.IsDBNull(FLD_UNDOFILEPATH) ? string.Empty : reader.GetString(FLD_UNDOFILEPATH);
            //databaseSql.BackupRestorePath = reader.IsDBNull(FLD_BACKUPRESTOREPATH) ? string.Empty : reader.GetString(FLD_BACKUPRESTOREPATH);
            //databaseSql.NetworkSharedPath = reader.IsDBNull(FLD_NETWORKSHAREDPATH) ? string.Empty : reader.GetString(FLD_NETWORKSHAREDPATH);

            //Fields in bcms_database_sql table on 16/07/2013 : Id, BaseDatabaseId, DatabaseSID, UserName, Password, Port, AuthenticationMode, DataFilePath, TransLogPath, UndoFilePath, BackupRestorePath, NetworkSharedPath

            databasemySql.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            databasemySql.BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"]) ? 0 : Convert.ToInt32(reader["BaseDatabaseId"]);
            databasemySql.DatabaseID = Convert.IsDBNull(reader["DatabaseID"]) ? string.Empty : Convert.ToString(reader["DatabaseID"]);
            databasemySql.UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]);
            databasemySql.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
            databasemySql.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);

            return databasemySql;
        }
    }
}