﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="MySqlDbConfig.ascx.cs" Inherits="CP.UI.Controls.MySqlDbConfig" %>
<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">MySql Database </h4>
        </div>
        <div class="widget-body">
            <div class="form-group">

                <label class="col-md-3 control-label">Database Name<span class="inactive">*</span></label>

                <div class="col-md-9">
                    <asp:TextBox ID="txtdbName" runat="server" CssClass="form-control"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="RequiredFieldValidator10" CssClass="error" ControlToValidate="txtdbName" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter Database Name"></asp:RequiredFieldValidator>
                       <asp:Label ID="lblErr" runat="server" ForeColor="Red"
                                            Text="Invalid License Key" Visible="False" Display="dynamic" CssClass="error"></asp:Label>
                </div>
            </div>
            <div class="form-group">

                <label class="col-md-3 control-label">Database Username<span class="inactive">*</span></label>

                <div class="col-md-9">
                    <asp:TextBox ID="txtUserName" runat="server" CssClass="form-control"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtusername" CssClass="error" ControlToValidate="txtUserName" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter UserName"></asp:RequiredFieldValidator>
                    <asp:RegularExpressionValidator ID="rfeUser" runat="server" CssClass="error" ControlToValidate="txtUserName"
                        ErrorMessage="Enter Valid User Name" ValidationGroup="dbConfig" ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$"
                        Display="Dynamic"></asp:RegularExpressionValidator>
                </div>
            </div>
            <div class="form-group">

                <label class="col-md-3 control-label">Database Password<span class="inactive">*</span></label>

                <div class="col-md-9">
                    <asp:TextBox ID="txtPassword" runat="server" autocomplete="off" TextMode="Password" CssClass="form-control"></asp:TextBox>
                    <asp:RequiredFieldValidator ID="rfvtxtpassword" CssClass="error" ControlToValidate="txtPassword" Display="Dynamic" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter Password"></asp:RequiredFieldValidator>
                </div>
            </div>
            <div class="form-group">

                <label class="col-md-3 control-label">Database Port<span class="inactive">*</span></label>

                <div class="col-md-9">
                    <asp:TextBox ID="txtPort" runat="server" MaxLength="6" CssClass="form-control"></asp:TextBox>
                    <asp:RegularExpressionValidator ID="revPhone1" CssClass="error" runat="server" ControlToValidate="txtPort" ValidationGroup="dbConfig" Display="Dynamic" ErrorMessage="Valid Numbers only" ValidationExpression="[0-9]{2,6}"></asp:RegularExpressionValidator>
                    <asp:RequiredFieldValidator ID="rfvtxtport" CssClass="error" ControlToValidate="txtPort" ValidationGroup="dbConfig" runat="server" ErrorMessage="Enter Port"></asp:RequiredFieldValidator>
                </div>
            </div>
        </div>
    </div>
</div>