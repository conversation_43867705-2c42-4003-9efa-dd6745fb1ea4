﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="GridConfiguration.aspx.cs" Inherits="CP.UI.Admin.GridConfiguration" Title="Continuity Patrol :: Events" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
     <asp:HiddenField ID="hdtokenKey" runat="server" />
     <script type="text/javascript">
         function CancelClick() {
             return false;
         }
    </script>

     <script src="../Script/EncryptDecrypt.js"></script>
    <script type="text/javascript">

        function clearText(control) {

            if (control.value != "") {
                control.value = getOrignalData(control, $('#ctl00_cphBody_hdfStaticGuid').val());
            }
        }
        function getHashData(control) {
            if (control.value.length == 0)
                $("#ctl00_cphBody_lblPsswordErrorMsg").css("display", "inline-block");
            else {
                $("#ctl00_cphBody_lblPsswordErrorMsg").css("display", "none");

                control.value = genrateUserNameHash(control, $('#ctl00_cphBody_hdfStaticGuid').val());
            }
            //control.value = getOrignalData(hash, $('#ctl00_cphBody_hdfStaticGuid').val());
        }
         </script>

    <div class="innerLR">
        <input type="hidden" id="hdfStaticGuid" runat="server" />
        <asp:UpdatePanel ID="updtpnl" UpdateMode="Conditional" runat="server">
            <ContentTemplate>

               
                <h3>
                    <img src="../Images/event-icon.png">
                    ASM Grid Configuration</h3>
            

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">

                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        ASM Instance Name
                            <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtInstanceName" runat="server" AutoPostBack="true" CssClass="form-control" OnTextChanged="txtInstanceName_TextChanged"></asp:TextBox>
                                        <asp:Label ID="lblInstanceName" runat="server" ForeColor="Red"></asp:Label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                       ASM User Name
                            <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                       <asp:TextBox ID="txtUserName" runat="server" CssClass="form-control"></asp:TextBox>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtName">
                                        ASM Password<span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtPassword" runat="server" CssClass="form-control" TextMode="Password" autocomplete="OFF" onblur="getHashData(this)" onfocus="clearText(this)"></asp:TextBox>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="txtName">
                                        ASM Grid Path<span class="inactive">*</span>
                                    </label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtGridPath" runat="server" CssClass="form-control"></asp:TextBox>
                                    </div>
                                </div>
                                <hr class="separator" />
                                <div class="form-actions row">

                                    <div class="col-xs-5">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
                                    </div>


                                    <div class="col-xs-7">
                                        <asp:Button CssClass="btn btn-primary" style="width: 15%; margin-left: 8px;" runat="server" Text="Save" TabIndex="15" CausesValidation="True" ID="btnAdd" OnClick="btnSave_Click" />&nbsp;&nbsp;
                     <asp:HiddenField ID="hdeventid" runat="server" />
                                        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server" OnClick="btnCancel_Click"
                                    Text="Cancel" CausesValidation="False" TabIndex="9"/>
                                    </div>
                                </div>
                <hr class="separator" />
                                <asp:ListView ID="ASMGridList" runat="server" Visible="true"  OnItemEditing="ASMGridList_ItemEditing" DataKeyNames="Id" OnItemDeleting="ASMGridList_ItemDeleting">
                                    <LayoutTemplate>
                                        <table class="table table-striped table-bordered table-condensed" width="100%" id="tblevent"
                                            runat="server" style="margin-bottom: 0; margin-top: 10px;">
                                            <thead>
                                                <tr>
                                                
                                                    <th style="width: 5%;" class="text-center">ID
                                                    </th>
                                                    <th style="width: 25%;">Instance Name
                                                    </th>
                                                    <th style="width: 25%;">User Name 
                                                    </th>
                                                    <th style="width: 37%;">Grid-Path
                                                    </th>
                                                    <th style="width: 8%;" class="text-center">Action
                                                    </th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <div class="slim-scroll chat-items" data-scroll-height="210px" data-scroll-size="0">
                                            <table class="table table-striped table-bordered table-condensed" width="100%">
                                                <tbody>
                                                    <asp:PlaceHolder ID="itemplaceholder" runat="server" />
                                                </tbody>
                                            </table>
                                        </div>
                                    </LayoutTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td style="width: 5%;" class="text-center">
                                                <asp:Label ID="lblid" runat="server" Text='<%#Eval("Id") %>' Visible="False"></asp:Label>
                                                <%#Container.DataItemIndex+1 %>
                                            </td>
                                            <td style="width: 25%;">
                                                <asp:Label ID="lblASMInstanceName" runat="server" Text='<%# Eval("ASMInstanceName") %>'></asp:Label>
                                            </td>
                                            <td style="width: 25%;">
                                                <asp:Label ID="lblASMUserName" runat="server" Text='<%# Eval("ASMUserName") %>'></asp:Label>
                                            </td>
                                            <td style="width: 37%;">
                                                <asp:Label ID="lblASMGridPath" runat="server" Text='<%# Eval("ASMGridPath") %>'></asp:Label>
                                                 <asp:Label ID="lblASMPassword" runat="server" Text='<%# Eval("ASMPassword") %>' Visible="false"></asp:Label>
                                            </td>
                                            <td style="width: 8%;" class="text-center">
                                                <asp:ImageButton ID="btnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                    ToolTip="Edit" ImageUrl="../images/icons/pencil.png" CausesValidation="False" />
                                                <asp:ImageButton ID="btnDelete" runat="server" CommandName="Delete" AlternateText="Delete" CausesValidation="False"
                                                    ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                                            </td>
                                             
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>
                               
                                  </div>
                            </div>
                        </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
