﻿using System;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Controls;
using System.Linq;
using System.Collections.Generic;
using System.Web;

namespace CP.UI.Component
{
    public partial class Robocopyoptionsconfig : RoboCopyOptionsBasePageEditor
    {
        public override string MessageInitials
        {
            get { return "RoboCopyOptions"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.RoboCopyOptionsList;
                }
                return string.Empty;
            }
        }

        public override void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.CreatorId = LoggedInUserId;
                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddRoboCopyOptions(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "RoboCopy", UserActionType.CreateDataSyncProperties, "The RoboCopy Properties '" + CurrentEntity.Name + "' was added to the RoboCopyOptions table", LoggedInUserId);
            }
            else
            {
                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateRobocopyoptions(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "RoboCopy Properties", UserActionType.UpdateDataSyncProperties, "The RoboCopy Properties '" + CurrentEntity.Name + "' was updated to the RoboCopyOptions table", LoggedInUserId);
            }
        }

        public override void BuildEntities()
        {
            CurrentEntity.Name = txtReplName.Text.Trim();

            #region CopyOptions
            IList<int> CopyOptionsValue = new List<int>();
            string sortedCopyOptions = "";

            string allitemCopyOptions = "";
            //allitemCopyOptions += "##";
            foreach (ListItem item in CblstGroup.Items)
            {
                allitemCopyOptions += item.Value + ":" + item.Selected;
                allitemCopyOptions += ";";
            }
            allitemCopyOptions += "#";
            allitemCopyOptions += "chkCopyOption:" + chkCopyOption.Checked + ";txtCopyOption:" + txtCopyOption.Text;

            allitemCopyOptions += "#";

            if (chkAminus.Checked)
            {
                allitemCopyOptions += "/A-:$" + chkAminusR.Checked + ";";
                allitemCopyOptions += "/A-:$" + chkAminusH.Checked + ";";
                allitemCopyOptions += "/A-:$" + chkAminusT.Checked + ";";
                allitemCopyOptions += "/A-:$" + chkAminusA.Checked + ";";
                allitemCopyOptions += "/A-:$" + chkAminusN.Checked + ";";

            }

            allitemCopyOptions += "#";

            if (chkAPlus.Checked)
            {
                allitemCopyOptions += "/A+:$" + chkAPlusR.Checked + ";";
                allitemCopyOptions += "/A+:$" + chkAPlusH.Checked + ";";
                allitemCopyOptions += "/A+:$" + chkAPlusT.Checked + ";";
                allitemCopyOptions += "/A+:$" + chkAPlusA.Checked + ";";
                allitemCopyOptions += "/A+:$" + chkAPlusN.Checked + ";";
            }

            allitemCopyOptions += "#";


            if (ChkACopy.Checked)
            {
                allitemCopyOptions += "/Copy:$" + ChkACopyD.Checked + ";";
                allitemCopyOptions += "/Copy:$" + ChkACopyT.Checked + ";";
                allitemCopyOptions += "/Copy:$" + ChkACopyO.Checked + ";";
                allitemCopyOptions += "/Copy:$" + ChkACopyA.Checked + ";";
                allitemCopyOptions += "/Copy:$" + ChkACopyS.Checked + ";";
                allitemCopyOptions += "/Copy:$" + ChkACopyU.Checked + ";";

            }

            string[] sortcopyoptions = allitemCopyOptions.Split('#');

            string[] sortcopyoptionsCblstGroup = sortcopyoptions[0].Split(';');

            string[] sortcopyoptions0 = sortcopyoptionsCblstGroup[0].Split(':');
            string[] sortcopyoptions1 = sortcopyoptionsCblstGroup[1].Split(':');
            string[] sortcopyoptions2 = sortcopyoptionsCblstGroup[2].Split(':');
            string[] sortcopyoptions3 = sortcopyoptionsCblstGroup[3].Split(':');
            string[] sortcopyoptions4 = sortcopyoptionsCblstGroup[4].Split(':');
            string[] sortcopyoptions5 = sortcopyoptionsCblstGroup[5].Split(':');
            string[] sortcopyoptions6 = sortcopyoptionsCblstGroup[6].Split(':');
            string[] sortcopyoptions7 = sortcopyoptionsCblstGroup[7].Split(':');
            string[] sortcopyoptions8 = sortcopyoptionsCblstGroup[8].Split(':');
            string[] sortcopyoptions9 = sortcopyoptionsCblstGroup[9].Split(':');
            string[] sortcopyoptions10 = sortcopyoptionsCblstGroup[10].Split(':');
            string[] sortcopyoptions11 = sortcopyoptionsCblstGroup[11].Split(':');
            string[] sortcopyoptions12 = sortcopyoptionsCblstGroup[12].Split(':');
            string[] sortcopyoptions13 = sortcopyoptionsCblstGroup[13].Split(':');
            string[] sortcopyoptions14 = sortcopyoptionsCblstGroup[14].Split(':');
            string[] sortcopyoptions15 = sortcopyoptionsCblstGroup[15].Split(':');
            string[] sortcopyoptions16 = sortcopyoptionsCblstGroup[15].Split(':');

            if (sortcopyoptions0[1] == "True")
            {
                sortedCopyOptions += "/S%0,";
                //CopyOptionsValue += sortcopyoptions0[0];
            }
            if (sortcopyoptions1[1] == "True")
            {
                sortedCopyOptions += "/E%1,";
            }
            if (sortcopyoptions2[1] == "True")
            {
                sortedCopyOptions += "/M%2,";
            }
            if (sortcopyoptions3[1] == "True")
            {
                sortedCopyOptions += "/Z%3,";
            }
            if (sortcopyoptions4[1] == "True")
            {
                sortedCopyOptions += "/SEC%4,";
            }
            if (sortcopyoptions5[1] == "True")
            {
                sortedCopyOptions += "/MOV%5,";
            }
            if (sortcopyoptions6[1] == "True")
            {
                sortedCopyOptions += "/A%6,";
            }
            if (sortcopyoptions7[1] == "True")
            {
                sortedCopyOptions += "/B%7,";
            }
            if (sortcopyoptions8[1] == "True")
            {
                sortedCopyOptions += "/COPYALL%8,";
            }
            if (sortcopyoptions9[1] == "True")
            {
                sortedCopyOptions += "/NOCOPY%9,";
            }
            if (sortcopyoptions10[1] == "True")
            {
                sortedCopyOptions += "/MIR%10,";
            }
            if (sortcopyoptions11[1] == "True")
            {
                sortedCopyOptions += "/ZB%11,";
            }
            if (sortcopyoptions12[1] == "True")
            {
                sortedCopyOptions += "/CREATE%12,";
            }
            if (sortcopyoptions13[1] == "True")
            {
                sortedCopyOptions += "/PURGE%13,";
            }
            if (sortcopyoptions14[1] == "True")
            {
                sortedCopyOptions += "/MOVE%14,";
            }
            if (sortcopyoptions15[1] == "True")
            {
                sortedCopyOptions += "/FAT%15,";
            }
            if (sortcopyoptions16[1] == "True")
            {
                sortedCopyOptions += "/FFT%16,";
            }



            //  sortedCopyOptions += "#";

            string[] sortcopyoptionschkCopyOption = sortcopyoptions[1].Split(';');
            string[] fdg = sortcopyoptionschkCopyOption[0].Split(':');
            string[] fdgg = sortcopyoptionschkCopyOption[1].Split(':');

            if (fdg[1] == "True")
            {
                sortedCopyOptions += "/LEV:" + fdgg[1];

            }

            sortedCopyOptions += "^";

            if (chkAminus.Checked)
            {

                string[] sortcopyoptionsAminus = sortcopyoptions[2].Split(';');

                string[] hg0 = sortcopyoptionsAminus[0].Split('$');
                string[] hg1 = sortcopyoptionsAminus[1].Split('$');
                string[] hg2 = sortcopyoptionsAminus[2].Split('$');
                string[] hg3 = sortcopyoptionsAminus[3].Split('$');
                string[] hg4 = sortcopyoptionsAminus[4].Split('$');

                if (hg0[1] == "True")
                {
                    sortedCopyOptions += hg0[0] + "R,";
                }
                if (hg1[1] == "True")
                {
                    sortedCopyOptions += hg1[0] + "H,";
                }
                if (hg2[1] == "True")
                {
                    sortedCopyOptions += hg2[0] + "T,";
                }
                if (hg3[1] == "True")
                {
                    sortedCopyOptions += hg3[0] + "A,";
                }
                if (hg4[1] == "True")
                {
                    sortedCopyOptions += hg4[0] + "N";
                }
            }


            sortedCopyOptions += "^";


            if (chkAPlus.Checked)
            {
                string[] sortcopyoptionsAplus = sortcopyoptions[3].Split(';');

                string[] hg0 = sortcopyoptionsAplus[0].Split('$');
                string[] hg1 = sortcopyoptionsAplus[1].Split('$');
                string[] hg2 = sortcopyoptionsAplus[2].Split('$');
                string[] hg3 = sortcopyoptionsAplus[3].Split('$');
                string[] hg4 = sortcopyoptionsAplus[4].Split('$');

                if (hg0[1] == "True")
                {
                    sortedCopyOptions += hg0[0] + "R,";
                }
                if (hg1[1] == "True")
                {
                    sortedCopyOptions += hg1[0] + "H,";
                }
                if (hg2[1] == "True")
                {
                    sortedCopyOptions += hg2[0] + "T,";
                }
                if (hg3[1] == "True")
                {
                    sortedCopyOptions += hg3[0] + "A,";
                }
                if (hg4[1] == "True")
                {
                    sortedCopyOptions += hg4[0] + "N";
                }

            }

            sortedCopyOptions += "^";

            if (ChkACopy.Checked)
            {
                string[] sortcopyoptionsACopy = sortcopyoptions[4].Split(';');

                string[] hg0 = sortcopyoptionsACopy[0].Split('$');
                string[] hg1 = sortcopyoptionsACopy[1].Split('$');
                string[] hg2 = sortcopyoptionsACopy[2].Split('$');
                string[] hg3 = sortcopyoptionsACopy[3].Split('$');
                string[] hg4 = sortcopyoptionsACopy[4].Split('$');
                string[] hg5 = sortcopyoptionsACopy[5].Split('$');

                if (hg0[1] == "True")
                {
                    sortedCopyOptions += hg0[0] + "D,";
                }
                if (hg1[1] == "True")
                {
                    sortedCopyOptions += hg1[0] + "T,";
                }
                if (hg2[1] == "True")
                {
                    sortedCopyOptions += hg2[0] + "O,";
                }
                if (hg3[1] == "True")
                {
                    sortedCopyOptions += hg3[0] + "A,";
                }
                if (hg4[1] == "True")
                {
                    sortedCopyOptions += hg4[0] + "S,";
                }
                if (hg5[1] == "True")
                {
                    sortedCopyOptions += hg5[0] + "U";
                }
            }


            #endregion


            #region RetryOption
            string allitemRetryOption = "";

            string sortedRetyOption = "";
            //allitemRetryOption += "::";

            //allitemRetryOption += "##";
            allitemRetryOption += retryoptionR.Checked + ";txtretryoptionR:" + txtretryoptionR.Text + ";";

            //allitemRetryOption += "#";
            allitemRetryOption += retryoptionW.Checked + ";txtretryoptionW:" + txtretryoptionW.Text + ";";

            string[] gf = allitemRetryOption.Split(';');
            string[] gf1 = gf[1].Split(':');
            string[] gf3 = gf[3].Split(':');



            if (gf[0] == "True")
            {
                sortedRetyOption += "/R:" + gf1[1] + ",";
            }

            if (gf[2] == "True")
            {
                sortedRetyOption += "/W:" + gf3[1] + ",";
            }


            #endregion


            #region Filters


            string allitemFilters = "";
            string sortedFilters = "";
            // allitemFilters += "Cblfilters::";
            foreach (ListItem item in Cblfilters.Items)
            {
                allitemFilters += item.Value + ":" + item.Selected;
                allitemFilters += ";";
            }

            allitemFilters += "#";

            if (chkfiltersIA.Checked)
            {

                //allitemFilters += "CblfiltersIA::";
                foreach (ListItem item in CblfiltersIA.Items)
                {
                    allitemFilters += item.Value + ":" + item.Selected;
                    allitemFilters += ";";
                }


            }

            allitemFilters += "#";


            if (chkfiltersXA.Checked)
            {

                //allitemFilters += "CblfiltersXA::";
                foreach (ListItem item in CblfiltersXA.Items)
                {
                    allitemFilters += item.Value + ":" + item.Selected;
                    allitemFilters += ";";
                }


            }

            allitemFilters += "#";


            allitemFilters += chkFilterTXT1.Checked + "$txtFilterTXT1:" + txtFilterTXT1.Text + ";";
            // allitemFilters += "#";
            allitemFilters += chkFilterTXT2.Checked + "$txtFilterTXT2:" + txtFilterTXT2.Text + ";";
            // allitemFilters += "#";
            allitemFilters += chkFilterTXT3.Checked + "$txtFilterTXT3:" + txtFilterTXT3.Text + ";";
            // allitemFilters += "#";
            allitemFilters += chkFilterTXT4.Checked + "$txtFilterTXT4:" + txtFilterTXT4.Text + ";";


            string[] sortfilteroptions = allitemFilters.Split('#');

            string[] sortcopyoptionsCblfilters = sortfilteroptions[0].Split(';');

            string[] sortcopyoptionss0 = sortcopyoptionsCblfilters[0].Split(':');
            string[] sortcopyoptionss1 = sortcopyoptionsCblfilters[1].Split(':');
            string[] sortcopyoptionss2 = sortcopyoptionsCblfilters[2].Split(':');
            string[] sortcopyoptionss3 = sortcopyoptionsCblfilters[3].Split(':');
            string[] sortcopyoptionss4 = sortcopyoptionsCblfilters[4].Split(':');
            string[] sortcopyoptionss5 = sortcopyoptionsCblfilters[5].Split(':');
            string[] sortcopyoptionss6 = sortcopyoptionsCblfilters[6].Split(':');
            string[] sortcopyoptionss7 = sortcopyoptionsCblfilters[7].Split(':');


            if (sortcopyoptionss0[1] == "True")
            {
                sortedFilters += "/XJ0,";
                //CopyOptionsValue += sortcopyoptions0[0];
            }
            if (sortcopyoptionss1[1] == "True")
            {
                sortedFilters += "/IT1,";
            }
            if (sortcopyoptionss2[1] == "True")
            {
                sortedFilters += "/IS2,";
            }
            if (sortcopyoptionss3[1] == "True")
            {
                sortedFilters += "/XC3,";
            }
            if (sortcopyoptionss4[1] == "True")
            {
                sortedFilters += "/XX4,";
            }
            if (sortcopyoptionss5[1] == "True")
            {
                sortedFilters += "/XO5,";
            }
            if (sortcopyoptionss6[1] == "True")
            {
                sortedFilters += "/XN6,";
            }
            if (sortcopyoptionss7[1] == "True")
            {
                sortedFilters += "/XL7,";
            }


            sortedFilters += "#";

            if (chkfiltersIA.Checked)
            {
                string[] sortcopyoptionsCblfiltersIA = sortfilteroptions[1].Split(';');

                string[] sortcopyoptionsss0 = sortcopyoptionsCblfiltersIA[0].Split(':');
                string[] sortcopyoptionsss1 = sortcopyoptionsCblfiltersIA[1].Split(':');
                string[] sortcopyoptionsss2 = sortcopyoptionsCblfiltersIA[2].Split(':');
                string[] sortcopyoptionsss3 = sortcopyoptionsCblfiltersIA[3].Split(':');
                string[] sortcopyoptionsss4 = sortcopyoptionsCblfiltersIA[4].Split(':');
                string[] sortcopyoptionsss5 = sortcopyoptionsCblfiltersIA[5].Split(':');
                string[] sortcopyoptionsss6 = sortcopyoptionsCblfiltersIA[6].Split(':');
                string[] sortcopyoptionsss7 = sortcopyoptionsCblfiltersIA[7].Split(':');
                string[] sortcopyoptionsss8 = sortcopyoptionsCblfiltersIA[8].Split(':');
                string[] sortcopyoptionsss9 = sortcopyoptionsCblfiltersIA[9].Split(':');
                string[] sortcopyoptionsss10 = sortcopyoptionsCblfiltersIA[10].Split(':');


                if (sortcopyoptionsss0[1] == "True")
                {
                    sortedFilters += "/IA:R0,";
                    //CopyOptionsValue += sortcopyoptions0[0];
                }
                if (sortcopyoptionsss1[1] == "True")
                {
                    sortedFilters += "/IA:S1,";
                }
                if (sortcopyoptionsss2[1] == "True")
                {
                    sortedFilters += "/IA:C2,";
                }
                if (sortcopyoptionsss3[1] == "True")
                {
                    sortedFilters += "/IA:E3,";
                }
                if (sortcopyoptionsss4[1] == "True")
                {
                    sortedFilters += "/IA:D4,";
                }
                if (sortcopyoptionsss5[1] == "True")
                {
                    sortedFilters += "/IA:O5,";
                }
                if (sortcopyoptionsss6[1] == "True")
                {
                    sortedFilters += "/IA:T6,";
                }
                if (sortcopyoptionsss7[1] == "True")
                {
                    sortedFilters += "/IA:N7,";
                }
                if (sortcopyoptionsss8[1] == "True")
                {
                    sortedFilters += "/IA:H8,";
                }
                if (sortcopyoptionsss9[1] == "True")
                {
                    sortedFilters += "/IA:A9,";
                }

            }

            sortedFilters += "#";

            if (chkfiltersXA.Checked)
            {

                string[] sortcopyoptionschkfiltersXA = sortfilteroptions[2].Split(';');

                string[] sortcopyoptionssss0 = sortcopyoptionschkfiltersXA[0].Split(':');
                string[] sortcopyoptionssss1 = sortcopyoptionschkfiltersXA[1].Split(':');
                string[] sortcopyoptionssss2 = sortcopyoptionschkfiltersXA[2].Split(':');
                string[] sortcopyoptionssss3 = sortcopyoptionschkfiltersXA[3].Split(':');
                string[] sortcopyoptionssss4 = sortcopyoptionschkfiltersXA[4].Split(':');
                string[] sortcopyoptionssss5 = sortcopyoptionschkfiltersXA[5].Split(':');
                string[] sortcopyoptionssss6 = sortcopyoptionschkfiltersXA[6].Split(':');
                string[] sortcopyoptionssss7 = sortcopyoptionschkfiltersXA[7].Split(':');
                string[] sortcopyoptionssss8 = sortcopyoptionschkfiltersXA[8].Split(':');
                string[] sortcopyoptionssss9 = sortcopyoptionschkfiltersXA[9].Split(':');
                string[] sortcopyoptionssss10 = sortcopyoptionschkfiltersXA[10].Split(':');


                if (sortcopyoptionssss0[1] == "True")
                {
                    sortedFilters += "/XA:R0,";
                    //CopyOptionsValue += sortcopyoptions0[0];
                }
                if (sortcopyoptionssss1[1] == "True")
                {
                    sortedFilters += "/XA:S1,";
                }
                if (sortcopyoptionssss2[1] == "True")
                {
                    sortedFilters += "/XA:C2,";
                }
                if (sortcopyoptionssss3[1] == "True")
                {
                    sortedFilters += "/XA:E3,";
                }
                if (sortcopyoptionssss4[1] == "True")
                {
                    sortedFilters += "/XA:D4,";
                }
                if (sortcopyoptionssss5[1] == "True")
                {
                    sortedFilters += "/XA:O5,";
                }
                if (sortcopyoptionssss6[1] == "True")
                {
                    sortedFilters += "/XA:T6,";
                }
                if (sortcopyoptionssss7[1] == "True")
                {
                    sortedFilters += "/XA:N7,";
                }
                if (sortcopyoptionssss8[1] == "True")
                {
                    sortedFilters += "/XA:H8,";
                }
                if (sortcopyoptionssss9[1] == "True")
                {
                    sortedFilters += "/XA:A9,";
                }

            }

            sortedFilters += "#";

            string[] sortcopyoptionschkfilterTX = sortfilteroptions[3].Split(';');

            string[] hygh0 = sortcopyoptionschkfilterTX[0].Split('$');
            string[] npo0 = hygh0[1].Split(':');

            string[] hygh1 = sortcopyoptionschkfilterTX[1].Split('$');
            string[] npo1 = hygh1[1].Split(':');

            // string[] hygh2 = sortcopyoptionschkfilterTX[2].Split('$');

            bool hygh2 = false;
            if (sortcopyoptionschkfilterTX[2].StartsWith("True"))
            {
                hygh2 = true;
            }

            string[] npo2 = sortcopyoptionschkfilterTX[2].Split(':');

            //string[] hygh3 = sortcopyoptionschkfilterTX[3].Split('$');
            bool hygh3 = false;
            if (sortcopyoptionschkfilterTX[3].StartsWith("True"))
            {
                hygh3 = true;
            }
            string[] npo3 = sortcopyoptionschkfilterTX[3].Split(':');


            if (hygh0[0] == "True")
            {
                sortedFilters += "/MAX:" + npo0[1] + ",";
            }
            if (hygh1[0] == "True")
            {
                sortedFilters += "/MIN:" + npo1[1] + ",";
            }
            if (hygh2)
            {
                sortedFilters += "/XD:" + npo2[1] + ",";
            }
            if (hygh3)
            {
                sortedFilters += "/XF:" + npo3[1] + ",";
            }

            #endregion

            #region AdvancedFilters

            string sortedAdvancedFilters = "";

            string allitemAdvancedFilters = "";
            //allitemCopyOptions += "##";
            foreach (ListItem item in Cbladvfilters1.Items)
            {
                allitemAdvancedFilters += item.Value + ":" + item.Selected;
                allitemAdvancedFilters += ";";
            }
            allitemAdvancedFilters += "#";
            allitemAdvancedFilters += "chkadvfilter1:" + chkadvfilter1.Checked + ";txtadvfilter1:" + txtadvfilter1.Text;

            allitemAdvancedFilters += "#";

            allitemAdvancedFilters += "chkadvfilter2:" + chkadvfilter2.Checked + ";txtadvfilter2:" + txtadvfilter2.Text;

            allitemAdvancedFilters += "#";

            allitemAdvancedFilters += "chkadvfilter3:" + chkadvfilter3.Checked + ";txtadvfilter3:" + txtadvfilter3.Text;

            allitemAdvancedFilters += "#";

            allitemAdvancedFilters += "chkadvfilter4:" + chkadvfilter4.Checked + ";txtadvfilter4:" + txtadvfilter4.Text;


            string[] sortAdvfilteroptions = allitemAdvancedFilters.Split('#');

            string[] sortAdvCblAdvfilters = sortAdvfilteroptions[0].Split(';');


            string[] sortadvfilter0 = sortAdvCblAdvfilters[0].Split(':');
            string[] sortadvfilter1 = sortAdvCblAdvfilters[1].Split(':');
            string[] sortadvfilter2 = sortAdvCblAdvfilters[2].Split(':');


            if (sortadvfilter0[1] == "True")
            {
                sortedAdvancedFilters += "/XD ($RECYCLE.BIN)0,";
                //CopyOptionsValue += sortcopyoptions0[0];
            }
            if (sortadvfilter1[1] == "True")
            {
                sortedAdvancedFilters += "/XD (System Volume Information)1,";
            }
            if (sortadvfilter2[1] == "True")
            {
                sortedAdvancedFilters += "/XD (RECYCLER)2,";
            }

            sortedAdvancedFilters += "#";


            string[] chkmin = sortAdvfilteroptions[1].Split(';');
            string[] chkmin1 = chkmin[0].Split(':');
            string[] chkmin2 = chkmin[1].Split(':');


            if (chkmin1[1] == "True")
            {
                sortedAdvancedFilters += "/MINAGE:" + chkmin2[1];
            }

            sortedAdvancedFilters += "#";

            string[] chkmax = sortAdvfilteroptions[2].Split(';');
            string[] chkmax1 = chkmax[0].Split(':');
            string[] chkmax2 = chkmax[1].Split(':');


            if (chkmax1[1] == "True")
            {
                sortedAdvancedFilters += "/MAXAGE:" + chkmax2[1];
            }

            sortedAdvancedFilters += "#";


            string[] chkminlad = sortAdvfilteroptions[3].Split(';');
            string[] chkminlad1 = chkminlad[0].Split(':');
            string[] chkminlad2 = chkminlad[1].Split(':');


            if (chkminlad1[1] == "True")
            {
                sortedAdvancedFilters += "/MINLAD:" + chkminlad2[1];
            }


            sortedAdvancedFilters += "#";

            string[] chkmaxlad = sortAdvfilteroptions[4].Split(';');
            string[] chkmaxlad1 = chkmaxlad[0].Split(':');
            string[] chkmaxlad2 = chkmaxlad[1].Split(':');



            if (chkmaxlad1[1] == "True")
            {
                sortedAdvancedFilters += "/MAXLAD:" + chkmaxlad2[1];
            }




            #endregion


            CurrentEntity.RepType = ddlRepType.SelectedItem.Text;
            CurrentEntity.CopyOptions = sortedCopyOptions;
            CurrentEntity.Retry = sortedRetyOption;
            CurrentEntity.Filters = sortedFilters;
            CurrentEntity.AdvancedFilters = sortedAdvancedFilters;

            //#region AdvancedFilter


            //string allitemAdvancedFilters = "";
            //allitemAdvancedFilters += "Cbladvfilters1::";
            //foreach (ListItem item in Cbladvfilters1.Items)
            //{
            //    allitemAdvancedFilters += item.Value + ":" + item.Selected;
            //    allitemAdvancedFilters += ";";
            //}

            //allitemAdvancedFilters += "##";



            //allitemAdvancedFilters += "chkadvfilter1::" + chkadvfilter1.Checked + ";txtadvfilter1::" + txtadvfilter1.Text + ";";
            //allitemAdvancedFilters += "##";
            //allitemAdvancedFilters += "chkadvfilter2::" + chkadvfilter2.Checked + ";txtadvfilter2::" + txtadvfilter2.Text + ";";
            //allitemAdvancedFilters += "##";
            //allitemAdvancedFilters += "chkadvfilter3::" + chkadvfilter3.Checked + ";txtadvfilter3::" + txtadvfilter3.Text + ";";
            //allitemAdvancedFilters += "##";
            //allitemAdvancedFilters += "chkadvfilter4::" + chkadvfilter4.Checked + ";txtadvfilter4::" + txtadvfilter4.Text + ";";


            //#endregion



            //allitemCopyOptions += "##";


            //var selectedItem = Utility.GetSelectedItem(CblstGroup);
            //if (selectedItem != null)
            //{
            //    string a = selectedItem.Aggregate("", (current, listItem) => current + (Convert.ToString(listItem.Text) + ","));
            //    a = a.TrimEnd(',');

            //}

        }

        public override void PrepareEditView()
        {
            BindControlsValue();
            btnSave.Text = "Update";
        }

        public override void PrepareView()
        {
            try
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), UserActionType.CreateHACMPCluster, "Processing HACMPCluster Configuration PrepareView", LoggedInUserId);

                if (IsUserOperator || IsUserManager || IsUserCustom)
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }

                Utility.SelectMenu(Master, "Module2");
                //txtName.Attributes.Add("onblur", "ValidatorValidate(" + rfvDSName.ClientID + ")");
                ////txtName.Attributes.Add("onblur", "ValidatorValidate(" + revDSName.ClientID + ")");
                ViewState["_token"] = UrlHelper.AddTokenToRequest();
                if (ViewState["_token"] != null)
                {
                    hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
                }
                hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
                if (CurrentRoboCopyOptionsId > 0)
                {
                    //txtName.ReadOnly = true;
                    PrepareEditView();
                }
            }
            catch (Exception ex)
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), UserActionType.CreateHACMPCluster, "Error Occured While Processing HACMP Cluster Configuration PrepareView  Error: " + ex.Message, LoggedInUserId);
            }
        }

        private bool CheckRoboCopyExist()
        {
            if (txtReplName.Text.Trim() != string.Empty)
            {
                bool isExists = Facade.IsExistRoboCopyOptionByName(txtReplName.Text);
                if (isExists)
                {
                    lblPrName.Text = "RoboCopyOption Name already exists ";
                    return true;
                }
                else
                {
                    lblPrName.Text = "";
                    return false;
                }
            }
            else
            {
                //lblDSName.Text = "Enter DataSync Properties Name";
                return true;
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("RoboCopyOption", UserActionType.CreateHACMPCluster))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {
                    if (ddlRepType.SelectedItem.Text == "-Select Replication Type-")
                    {
                        return;
                    }

                    if (CurrentRoboCopyOptionsId == 0)
                    {
                        if (!Page.IsValid || CheckRoboCopyExist())
                            return;
                    }
                    else
                    {
                        if (!Page.IsValid)
                            return;
                    }

                    var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }

                    var submitButton = (Button)sender;
                    var buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }

                    try
                    {
                        if (currentTransactionType != TransactionType.Undefined)
                        {
                            BuildEntities();
                            StartTransaction();
                            SaveEditor();
                            EndTransaction();
                        }
                        string message = MessageInitials + " " + '"' + CurrentEntity.Name + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                    currentTransactionType));
                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();
                        returnUrl = Request.RawUrl;
                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                        ExceptionManager.Manage(ex, this);
                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();
                        returnUrl = Request.RawUrl;
                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, this);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                            ExceptionManager.Manage(customEx, this);
                        }
                    }

                    if (returnUrl.IsNotNullOrEmpty())
                    {
                        Helper.Url.Redirect(new SecureUrl(returnUrl));
                    }
                }
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.RoboCopyOptionsList);
        }

        private void BindControlsValue()
        {

            ddlRepType.SelectedItem.Text = CurrentEntity.RepType.ToString();

            txtReplName.Text = CurrentEntity.Name;

            string copyoptions = CurrentEntity.CopyOptions;
            IList<int> lst = new List<int>();


            string[] ccp = copyoptions.Split('^');
            string[] cpo = ccp[0].Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            for (int i = 0; i < cpo.Length; i++)
            {
                if (cpo[i].Contains("/LEV:"))
                {
                    string[] copyOptions = cpo[i].Split(':');
                    chkCopyOption.Checked = true;
                    txtCopyOption.Text = copyOptions[1];
                }
                else
                {
                    string[] copyOptions = cpo[i].Split('%');
                    int p = Convert.ToInt32(copyOptions[1]);
                    CblstGroup.Items[p].Selected = true;
                }
            }

            #region mahesh singh code commented
            //for (int i = 0; i < cpo.Length; i++)
            //{


            //    int lastElementIndex = cpo.Length - 1;
            //    if (!cpo.Last().Contains("/LEV:"))
            //    {
            //        if (i != lastElementIndex)
            //        {

            //            string fddsf = Convert.ToString(cpo[i]);

            //            fddsf = fddsf.Substring(fddsf.Length - 1);

            //            int p = Convert.ToInt32(fddsf);

            //            CblstGroup.Items[p].Selected = true;
            //        }
            //    }
            //    else
            //    {

            //       if (i != lastElementIndex)
            //        {

            //            string fddsf = Convert.ToString(cpo[i]);

            //           if(i>9)
            //           {

            //               fddsf = fddsf.Substring(fddsf.Length - 2);
            //           }
            //           else
            //           {

            //               fddsf = fddsf.Substring(fddsf.Length - 1);

            //           }


            //            int p = Convert.ToInt32(fddsf);

            //            CblstGroup.Items[p].Selected = true;


            //        }
            //       if (i == lastElementIndex)
            //       {

            //           string[] dsdf = cpo.Last().Split(':');

            //           chkCopyOption.Checked = true;
            //           txtCopyOption.Text = dsdf[1];
            //       }



            //    }
            //}
            #endregion

            if (ccp[1].Contains("/A-"))
            {
                chkAminus.Checked = true;
                pnlAminus.Visible = true;

                string[] APlusMinusCopy = ccp[1].Split(',');

                for (int i = 0; i < APlusMinusCopy.Length; i++)
                {
                    string fddsf = Convert.ToString(APlusMinusCopy[i]);

                    if (fddsf.Contains("R") || fddsf.Contains("H") || fddsf.Contains("T") || fddsf.Contains("A") || fddsf.Contains("N"))
                    {

                        fddsf = fddsf.Substring(fddsf.Length - 1);

                        if (fddsf.Contains("R"))
                        {
                            chkAminusR.Checked = true;
                        }
                        if (fddsf.Contains("H"))
                        {
                            chkAminusH.Checked = true;
                        }
                        if (fddsf.Contains("T"))
                        {
                            chkAminusT.Checked = true;
                        }
                        if (fddsf.Contains("A"))
                        {
                            chkAminusA.Checked = true;
                        }
                        if (fddsf.Contains("N"))
                        {
                            chkAminusN.Checked = true;
                        }
                    }

                }


            }
            if (ccp[2].Contains("/A+"))
            {

                chkAPlus.Checked = true;
                pnlAplus.Visible = true;

                string[] APlusMinusCopy = ccp[2].Split(',');

                for (int i = 0; i < APlusMinusCopy.Length; i++)
                {
                    string fddsf = Convert.ToString(APlusMinusCopy[i]);


                    if (fddsf.Contains("R") || fddsf.Contains("H") || fddsf.Contains("T") || fddsf.Contains("A") || fddsf.Contains("N"))
                    {

                        fddsf = fddsf.Substring(fddsf.Length - 1);

                        if (fddsf.Contains("R"))
                        {
                            chkAPlusR.Checked = true;
                        }
                        if (fddsf.Contains("H"))
                        {
                            chkAPlusH.Checked = true;
                        }
                        if (fddsf.Contains("T"))
                        {
                            chkAPlusT.Checked = true;
                        }
                        if (fddsf.Contains("A"))
                        {
                            chkAPlusA.Checked = true;
                        }
                        if (fddsf.Contains("N"))
                        {
                            chkAPlusN.Checked = true;
                        }
                    }

                }
            }
            if (ccp[3].Contains("/Copy"))
            {

                ChkACopy.Checked = true;
                pnlAcopy.Visible = true;

                string[] APlusMinusCopy = ccp[3].Split(',');

                for (int i = 0; i < APlusMinusCopy.Length; i++)
                {
                    string fddsf = Convert.ToString(APlusMinusCopy[i]);


                    if (fddsf.Contains("D") || fddsf.Contains("T") || fddsf.Contains("O") || fddsf.Contains("A") || fddsf.Contains("S") || fddsf.Contains("U"))
                    {

                        fddsf = fddsf.Substring(fddsf.Length - 1);

                        if (fddsf.Contains("D"))
                        {
                            ChkACopyD.Checked = true;
                        }
                        if (fddsf.Contains("T"))
                        {
                            ChkACopyT.Checked = true;
                        }
                        if (fddsf.Contains("O"))
                        {
                            ChkACopyO.Checked = true;
                        }
                        if (fddsf.Contains("A"))
                        {
                            ChkACopyA.Checked = true;
                        }
                        if (fddsf.Contains("S"))
                        {
                            ChkACopyS.Checked = true;
                        }
                        if (fddsf.Contains("U"))
                        {
                            ChkACopyU.Checked = true;
                        }
                    }

                }
            }



            string retryoptions = CurrentEntity.Retry;

            if (retryoptions.Contains("/R:") || retryoptions.Contains("/W:"))
            {
                string[] rety = retryoptions.Split(',');

                if (rety[0].Contains("/R:"))
                {
                    string[] fdgf = rety[0].Split(':');
                    retryoptionR.Checked = true;
                    txtretryoptionR.Text = fdgf[1];


                }
                if (rety[0].Contains("/W:"))
                {
                    string[] fdgf = rety[0].Split(':');
                    retryoptionW.Checked = true;
                    txtretryoptionW.Text = fdgf[1];


                }
                if (rety[1].Contains("/"))
                {
                    if (rety[1].Contains("/W:"))
                    {
                        string[] fdgf = rety[1].Split(':');

                        retryoptionW.Checked = true;
                        txtretryoptionW.Text = fdgf[1];

                    }
                }

            }


            string filters = CurrentEntity.Filters;


            string[] filt = filters.Split('#');


            string[] filte = filt[0].Split(',');

            if (filte[0].Contains("/"))
            {

                //chkfiltersIA.Checked = true;

                for (int i = 0; i < filte.Length; i++)
                {
                    int lastElementIndex = filte.Length - 1;

                    if (i != lastElementIndex)
                    {


                        string fddsf = Convert.ToString(filte[i]);

                        fddsf = fddsf.Substring(fddsf.Length - 1);

                        int p = Convert.ToInt32(fddsf);

                        Cblfilters.Items[p].Selected = true;
                    }

                }


            }



            if (filt[1].Contains("/"))
            {

                string[] filte1 = filt[1].Split(',');

                if (filt[1].Contains("/"))
                {

                    chkfiltersIA.Checked = true;
                    pnlfiltersIA.Visible = true;
                    CblfiltersIA.Visible = true;


                    for (int i = 0; i < filte1.Length; i++)
                    {
                        int lastElementIndex = filte1.Length - 1;

                        if (i != lastElementIndex)
                        {

                            string fddsf = Convert.ToString(filte1[i]);

                            fddsf = fddsf.Substring(fddsf.Length - 1);

                            int p = Convert.ToInt32(fddsf);

                            CblfiltersIA.Items[p].Selected = true;
                        }

                    }
                }

            }


            if (filt[2].Contains("/"))
            {

                string[] filte2 = filt[2].Split(',');

                if (filt[2].Contains("/"))
                {

                    chkfiltersXA.Checked = true;
                    pnlfiltersXA.Visible = true;
                    CblfiltersXA.Visible = true;

                    for (int i = 0; i < filte2.Length; i++)
                    {
                        int lastElementIndex = filte2.Length - 1;

                        if (i != lastElementIndex)
                        {

                            string fddsf = Convert.ToString(filte2[i]);

                            fddsf = fddsf.Substring(fddsf.Length - 1);

                            int p = Convert.ToInt32(fddsf);

                            CblfiltersXA.Items[p].Selected = true;
                        }

                    }
                }
            }

            if (filt[3].Contains("/"))
            {

                string[] filte3 = filt[3].Split(',');

                if (filt[3].Contains("/"))
                {

                    string[] gfg = filt[3].Split(',');

                    if (gfg[0].Contains("/MAX"))
                    {
                        string[] flt = gfg[0].Split(':');
                        chkFilterTXT1.Checked = true;
                        txtFilterTXT1.Text = flt[1];
                    }
                    if (gfg[0].Contains("/MIN"))
                    {
                        string[] flt = gfg[0].Split(':');
                        chkFilterTXT2.Checked = true;
                        txtFilterTXT2.Text = flt[1];
                    }
                    if (gfg[0].Contains("/XD"))
                    {
                        string[] flt = gfg[0].Split(':');
                        chkFilterTXT3.Checked = true;
                        txtFilterTXT3.Enabled = true;
                        txtFilterTXT3.Text = flt[1];
                    }
                    if (gfg[0].Contains("/XF"))
                    {
                        string[] flt = gfg[0].Split(':');
                        txtFilterTXT4.Text = flt[1];
                        chkFilterTXT4.Enabled = true;
                        chkFilterTXT4.Checked = true;
                    }

                    if (gfg[1].Contains("/"))
                    {
                        if (gfg[1].Contains("/MIN"))
                        {
                            string[] flt = gfg[1].Split(':');
                            chkFilterTXT2.Checked = true;
                            txtFilterTXT2.Text = flt[1];
                        }
                        if (gfg[1].Contains("/XF"))
                        {
                            string[] flt = gfg[1].Split(':');
                            txtFilterTXT4.Text = flt[1];
                            chkFilterTXT4.Checked = true;
                            txtFilterTXT4.Enabled = true;
                        }

                        if (gfg[2].Contains("/"))
                        {
                            if (gfg[2].Contains("/XD"))
                            {
                                string[] flt = gfg[2].Split(':');
                                chkFilterTXT3.Checked = true;
                                txtFilterTXT3.Text = flt[1];
                            }
                            if (gfg[3].Contains("/"))
                            {
                                if (gfg[3].Contains("/XF"))
                                {
                                    string[] flt = gfg[3].Split(':');
                                    txtFilterTXT4.Text = flt[1];
                                    chkFilterTXT4.Checked = true;
                                    txtFilterTXT4.Enabled = true;
                                }
                            }
                        }
                    }

                    //chkfiltersIA.Checked = true;


                    //for (int i = 0; i < filte3.Length; i++)
                    //{

                    //    int lastElementIndex = filte3.Length - 1;

                    //    if (i != lastElementIndex)
                    //    {

                    //        string fddsf = Convert.ToString(filte3[i]);

                    //        fddsf = fddsf.Substring(fddsf.Length - 1);

                    //        int p = Convert.ToInt32(fddsf);

                    //        Cblfilters.Items[p].Selected = true;
                    //    }

                    //}
                }
            }

            string AdvFilters = CurrentEntity.AdvancedFilters;

            //string filters = CurrentEntity.Filters;

            string[] Advfilt = AdvFilters.Split('#');


            string[] Advfilte = Advfilt[0].Split(',');

            if (Advfilt[0].Contains("/"))
            {

                //chkfiltersIA.Checked = true;

                for (int i = 0; i < Advfilte.Length; i++)
                {
                    int lastElementIndex = Advfilte.Length - 1;

                    if (i != lastElementIndex)
                    {

                        string fddsf = Convert.ToString(Advfilte[i]);

                        fddsf = fddsf.Substring(fddsf.Length - 1);

                        int p = Convert.ToInt32(fddsf);

                        Cbladvfilters1.Items[p].Selected = true;
                    }

                }
            }

            if (Advfilt[1].Contains("/"))
            {

                string[] Advfilte1 = Advfilt[1].Split(':');
                if (Advfilte1[0].Contains("/MINAGE"))
                {
                    chkadvfilter1.Checked = true;
                    txtadvfilter1.Text = Advfilte1[1];
                }
            }

            if (Advfilt[2].Contains("/"))
            {
                string[] Advfilte2 = Advfilt[2].Split(':');
                if (Advfilte2[0].Contains("/MAXAGE"))
                {
                    chkadvfilter2.Checked = true;
                    txtadvfilter2.Text = Advfilte2[1];
                }

            }

            if (Advfilt[3].Contains("/"))
            {
                string[] Advfilte3 = Advfilt[3].Split(':');
                if (Advfilte3[0].Contains("/MINLAD"))
                {
                    chkadvfilter3.Checked = true;
                    txtadvfilter3.Text = Advfilte3[1];
                }
            }

            if (Advfilt[4].Contains("/"))
            {

                string[] Advfilte4 = Advfilt[4].Split(':');
                if (Advfilte4[0].Contains("/MAXLAD"))
                {
                    chkadvfilter4.Checked = true;
                    txtadvfilter4.Text = Advfilte4[1];
                }
            }


            if (chkCopyOption.Checked)
            {
                txtCopyOption.Enabled = true;
            }
            if (retryoptionR.Checked)
            {
                txtretryoptionR.Enabled = true;
            }
            if (retryoptionW.Checked)
            {
                txtretryoptionW.Enabled = true;
            }


            if (chkFilterTXT1.Checked)
            {
                txtFilterTXT1.Enabled = true;
            }
            if (chkFilterTXT2.Checked)
            {
                txtFilterTXT2.Enabled = true;
            }
            if (chkFilterTXT3.Checked)
            {
                txtFilterTXT3.Enabled = true;
            }
            if (chkFilterTXT4.Checked)
            {
                txtFilterTXT4.Enabled = true;
            }


            if (chkadvfilter1.Checked)
            {
                txtadvfilter1.Enabled = true;
            }
            if (chkadvfilter2.Checked)
            {
                txtadvfilter2.Enabled = true;
            }
            if (chkadvfilter3.Checked)
            {
                txtadvfilter3.Enabled = true;
            }
            if (chkadvfilter4.Checked)
            {
                txtadvfilter4.Enabled = true;
            }



        }

        protected void chkAminus_CheckedChanged(object sender, EventArgs e)
        {
            if (chkAminus.Checked)
            {
                pnlAminus.Visible = true;

                chkAminusR.Checked = false;
                chkAminusH.Checked = false;
                chkAminusT.Checked = false;
                chkAminusA.Checked = false;
                chkAminusN.Checked = false;
            }
            else
            {
                pnlAminus.Visible = false;
            }


        }

        protected void chkAPlus_CheckedChanged(object sender, EventArgs e)
        {
            if (chkAPlus.Checked)
            {
                pnlAplus.Visible = true;

                chkAPlusR.Checked = false;
                chkAPlusH.Checked = false;
                chkAPlusT.Checked = false;
                chkAPlusA.Checked = false;
                chkAPlusN.Checked = false;
            }
            else
            {
                pnlAplus.Visible = false;
            }

        }

        protected void ChkACopy_CheckedChanged(object sender, EventArgs e)
        {
            if (ChkACopy.Checked)
            {
                pnlAcopy.Visible = true;

                ChkACopyD.Checked = false;
                ChkACopyT.Checked = false;
                ChkACopyO.Checked = false;
                ChkACopyA.Checked = false;
                ChkACopyS.Checked = false;
                ChkACopyU.Checked = false;
            }
            else
            {
                pnlAcopy.Visible = false;
            }

        }

        protected void chkfiltersIA_CheckedChanged(object sender, EventArgs e)
        {
            if (chkfiltersIA.Checked)
            {
                CblfiltersIA.Visible = true;
                pnlfiltersIA.Visible = true;

                CblfiltersIA.ClearSelection();

            }
            else
            {
                CblfiltersIA.Visible = false;
            }

        }

        protected void chkfiltersXA_CheckedChanged(object sender, EventArgs e)
        {
            if (chkfiltersXA.Checked)
            {
                CblfiltersXA.Visible = true;
                pnlfiltersXA.Visible = true;

                CblfiltersXA.ClearSelection();

            }
            else
            {
                CblfiltersXA.Visible = false;
            }

        }

        protected void chkCopyOption_CheckedChanged(object sender, EventArgs e)
        {
            if (chkCopyOption.Checked)
            {
                txtCopyOption.Enabled = true;
                txtCopyOption.Text = "";
            }
            else
            {
                txtCopyOption.Enabled = false;
                txtCopyOption.Text = "";
            }


        }

        protected void retryoptionR_CheckedChanged(object sender, EventArgs e)
        {
            if (retryoptionR.Checked)
            {
                txtretryoptionR.Enabled = true;
                txtretryoptionR.Text = "";
            }
            else
            {
                txtretryoptionR.Enabled = false;
                txtretryoptionR.Text = "";
            }

        }

        protected void retryoptionW_CheckedChanged(object sender, EventArgs e)
        {

            if (retryoptionW.Checked)
            {
                txtretryoptionW.Enabled = true;
                txtretryoptionW.Text = "";
            }
            else
            {
                txtretryoptionW.Enabled = false;
                txtretryoptionW.Text = "";
            }

        }




        protected void chkFilterTXT1_CheckedChanged(object sender, EventArgs e)
        {

            if (chkFilterTXT1.Checked)
            {
                txtFilterTXT1.Enabled = true;
                txtFilterTXT1.Text = "";
            }
            else
            {
                txtFilterTXT1.Enabled = false;
                txtFilterTXT1.Text = "";
            }


        }

        protected void chkFilterTXT2_CheckedChanged(object sender, EventArgs e)
        {

            if (chkFilterTXT2.Checked)
            {
                txtFilterTXT2.Enabled = true;
                txtFilterTXT2.Text = "";
            }
            else
            {
                txtFilterTXT2.Enabled = false;
                txtFilterTXT2.Text = "";
            }

        }

        protected void chkFilterTXT3_CheckedChanged(object sender, EventArgs e)
        {

            if (chkFilterTXT3.Checked)
            {
                txtFilterTXT3.Enabled = true;
                txtFilterTXT3.Text = "";
            }
            else
            {
                txtFilterTXT3.Enabled = false;
                txtFilterTXT3.Text = "";
            }

        }

        protected void chkFilterTXT4_CheckedChanged(object sender, EventArgs e)
        {

            if (chkFilterTXT4.Checked)
            {
                txtFilterTXT4.Enabled = true;
                txtFilterTXT4.Text = "";
            }
            else
            {
                txtFilterTXT4.Enabled = false;
                txtFilterTXT4.Text = "";
            }

        }




        protected void chkadvfilter1_CheckedChanged(object sender, EventArgs e)
        {

            if (chkadvfilter1.Checked)
            {
                txtadvfilter1.Enabled = true;
                txtadvfilter1.Text = "";
            }
            else
            {
                txtadvfilter1.Enabled = false;
                txtadvfilter1.Text = "";
            }


        }

        protected void chkadvfilter3_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvfilter3.Checked)
            {
                txtadvfilter3.Enabled = true;
                txtadvfilter3.Text = "";
            }
            else
            {
                txtadvfilter3.Enabled = false;
                txtadvfilter3.Text = "";
            }

        }

        protected void chkadvfilter2_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvfilter2.Checked)
            {
                txtadvfilter2.Enabled = true;
                txtadvfilter2.Text = "";
            }
            else
            {
                txtadvfilter2.Enabled = false;
                txtadvfilter2.Text = "";
            }

        }

        protected void chkadvfilter4_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvfilter4.Checked)
            {
                txtadvfilter4.Enabled = true;
                txtadvfilter4.Text = "";
            }
            else
            {
                txtadvfilter4.Enabled = false;
                txtadvfilter4.Text = "";
            }

        }

        protected void txtReplName_TextChanged(object sender, EventArgs e)
        {
            CheckRoboCopyExist();
        }

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();
                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }


    }
}