﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace CP.DataAccess
{
    public interface IRSyncDataAccess
    {
        RSyncReplication AddRSync(RSyncReplication rsync);

        RSyncReplication UpdateRSync(RSyncReplication rsync);

        IList<RSyncReplication> GetAllRSync();

        RSyncReplication GetRSyncByRepliId(int id);

        RSyncReplication GetRSyncByyId(int id);

        IList<RSyncReplication> GetAllRSyncAndJob();

        IList<RSyncReplication> GetAll_RSyncAndJobByUserId(int User_Id);
    }
}
