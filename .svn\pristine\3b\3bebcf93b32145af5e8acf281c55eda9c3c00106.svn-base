﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "DatabaseOracle", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class DatabaseOracle : BaseEntity
    {
        #region Properties

        [DataMember]
        public int BaseDatabaseId { get; set; }

        [DataMember]
        public int ServerId { get; set; }

        [DataMember]
        public string OracleSID { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public string Password { get; set; }

        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public string Archive { get; set; }

        [DataMember]
        public string Home { get; set; }

        [DataMember]
        public string Redo { get; set; }

        [DataMember]
        public string ASMGrid { get; set; }

        [DataMember]
        public string InstanceName { get; set; }

        [DataMember]
        public string AsmInstanceName { get; set; }

        [DataMember]
        public int IsAsm { get; set; }

        [DataMember]
        public string AsmUserName { get; set; }

        [DataMember]
        public string AsmPassword { get; set; }

        [DataMember]
        public string AsmPath { get; set; }

        [DataMember]
        public string PreExecutionCommand { get; set; }

        [DataMember]
        public int SSOEnabled
        {
            get;
            set;
        }

        [DataMember]
        public int SSOProfileId
        {
            get;
            set;
        }

        #endregion Properties
    }
}