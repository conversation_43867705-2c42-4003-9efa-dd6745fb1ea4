﻿using CP.BusinessFacade;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;

namespace CP.UI.ImpactAnalysis
{
    public partial class ImpactMaster : BasePage
    {
        CP.Common.DatabaseEntity.ImpactMaster impacttypeObj = new CP.Common.DatabaseEntity.ImpactMaster();
        private static readonly IFacade NewFacade = new Facade();

        public static string CurrentURL = Constants.UrlConstants.Urls.ImpactAnalysis.ImpactMasterConfiguration;
        private int ImpactTypeMasterID = 0;
        private static string isImapactActive;
        private Boolean isEdit = false;
        private static bool isNameIExists = false;

        public void saveImpactType()
        {
            try
            {
                if (!CheckImpacNameExist())
                {
                    isNameIExists = false;
                    impacttypeObj.CreatorId = LoggedInUserId;
                    impacttypeObj.UpdatorId = LoggedInUserId;

                    impacttypeObj.ImpactName = txtimpactname.Text;
                    impacttypeObj.ImpactDescription = txtpmpactmasdetail.Text;
                    impacttypeObj.ImpactTypeID = ddlimpacttypemaster.SelectedValue;
                    impacttypeObj.ImpactOrder = ddlsequence.SelectedValue;
                    Facade.AddImpactMaster(impacttypeObj);
                    ActivityLogger.AddLog(LoggedInUserName, "ImpactTypeMaster", UserActionType.CreateServerComponent, "ImpactMaster '" + impacttypeObj.ImpactName + "' was added to the ImpactMaster table", LoggedInUserId);
                    lblMessage.ForeColor = System.Drawing.Color.Green;
                    lblMessage.Text = "Impact Type Saved Successfully";
                }
                else
                {
                    lblMessage.ForeColor = System.Drawing.Color.Red;
                    lblMessage.Text = "Impact Type Name is Already Exist";
                    isNameIExists = true;

                }
            }
            catch
            { }
        }

        public void UpdateImpactType()
        {
            CP.Common.DatabaseEntity.ImpactMaster UpateimpacttypeObj = new CP.Common.DatabaseEntity.ImpactMaster();

            try
            {
                if (!CheckImpacNameExist())
                {
                    isNameIExists = false;
                    UpateimpacttypeObj.UpdatorId = LoggedInUserId;
                    UpateimpacttypeObj.ImpactName = txtimpactname.Text;
                    UpateimpacttypeObj.ImpactDescription = txtpmpactmasdetail.Text;
                    UpateimpacttypeObj.ImpactTypeID = ddlimpacttypemaster.SelectedValue;
                    UpateimpacttypeObj.ImpactOrder = ddlsequence.SelectedValue;
                    if (Session["Id"] != null)
                    {
                        UpateimpacttypeObj.Id = Convert.ToInt32(Session["Id"]);
                    }
                    Facade.UpdateImpactMaster(UpateimpacttypeObj);
                    btnSave.Text = "Save";
                    lblMessage.ForeColor = System.Drawing.Color.Green;
                    lblMessage.Text = "Impact Type Updated Successfully";
                    ActivityLogger.AddLog(LoggedInUserName, "ImpactTypeMaster", UserActionType.UpdateServerComponent, "ImpactMaster '" + UpateimpacttypeObj.ImpactName + "' was updated to the ImpactMaster table", LoggedInUserId);
                }
                else
                {
                    lblMessage.ForeColor = System.Drawing.Color.Red;
                    lblMessage.Text = "Impact Type Name is Already Exist";
                    isNameIExists = true;

                }
            }
            catch
            { }

        }


        public void Clearcontrols()
        {

            txtimpactname.Text = string.Empty;
            txtpmpactmasdetail.Text = string.Empty;
            ddlimpacttypemaster.SelectedIndex = 0;
            ddlsequence.SelectedIndex = 0;

        }

        public override void PrepareView()
        {

            if (IsUserOperator || IsUserManager)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            ImpactTypeMasterID = Convert.ToInt32(Helper.Url.SecureUrl[Constants.UrlConstants.Params.ImpactTypeMasterId].ToString());

           
            Utility.SelectMenu(Master, "Module2");

            if (ImpactTypeMasterID == 0)
            {
                BindImpactTypeMaster(ddlimpacttypemaster, true, ImpactTypeMasterID, isImapactActive);
            }
            else
            { BindImpactTypeMaster(ddlimpacttypemaster, false, ImpactTypeMasterID, isImapactActive); }

            Bindddlsequence();
            BindList();

        }

        public void Bindddlsequence()
        {

            ddlsequence.Items.Clear();
            var ddlcount = NewFacade.GetAllImpactMaster();
            string Listitemvalue = string.Empty;
            ddlsequence.Items.Add(new ListItem("--Select Order--", "0"));

            if (ddlcount != null)
            {
                for (int i = 0; i <= ddlcount.Count; i++)
                {
                    Listitemvalue = (i + 1).ToString();
                     var res = NewFacade.IsImpactMasterExistByImpactOrder(Convert.ToInt32(Listitemvalue));

                     if (!res)
                     {
                         ddlsequence.Items.Add(new ListItem(Listitemvalue, Listitemvalue));
                     }
                }
            }
            else
            {
                Listitemvalue = (1).ToString();
                ddlsequence.Items.Add(new ListItem(Listitemvalue, Listitemvalue));
            }

        }

        public void BindUpdatedddlsequence()
        {

            ddlsequence.Items.Clear();
            var ddlcount = NewFacade.GetAllImpactMaster();
            string Listitemvalue = string.Empty;
            ddlsequence.Items.Add(new ListItem("--Select Order--", "0"));

            if (ddlcount != null)
            {
                for (int i = 0; i <= ddlcount.Count; i++)
                {
                    Listitemvalue = (i + 1).ToString();
                    ddlsequence.Items.Add(new ListItem(Listitemvalue, Listitemvalue));

                }
            }
            else
            {
                Listitemvalue = (1).ToString();
                ddlsequence.Items.Add(new ListItem(Listitemvalue, Listitemvalue));
            }

        }

        private void BindControlsValue(int id)
        {
            try
            {
                CP.Common.DatabaseEntity.ImpactMaster GetimpacttypeObj = new CP.Common.DatabaseEntity.ImpactMaster();
                GetimpacttypeObj = Facade.GetImpactMasterById(id);

                Session["ImpactName"] = GetimpacttypeObj.ImpactName;
                txtimpactname.Text = GetimpacttypeObj.ImpactName;
                txtpmpactmasdetail.Text = GetimpacttypeObj.ImpactDescription;
                ddlsequence.SelectedValue = GetimpacttypeObj.ImpactOrder;
                ddlimpacttypemaster.SelectedValue = GetimpacttypeObj.ImpactTypeID;
                btnSave.Text = "Update";
                UpdatePanel1.Update();
            }
            catch
            { }
        }
        public void BindImpactTypeMaster(ListControl lstGroup, bool addDefaultItem, int impactMasterId, string IsFlag)
        {
            lstGroup.Items.Clear();
            var impacttypemasterList = NewFacade.GetAllImpactTypeMaster();
            if (impacttypemasterList == null) return;
            {
                lstGroup.DataSource = impacttypemasterList;
                lstGroup.DataTextField = "ImpactTypeName";
                lstGroup.DataValueField = "ID";
                lstGroup.DataBind();

            }

            if (impactMasterId == 0)
            {
                lstGroup.AddDefaultItem(Constants.UIConstants.DropDownItemPleaseSelectImpactTypeMaster);
                lstGroup.Enabled = true;
            }
            else
            {
                lstGroup.SelectedValue = impactMasterId.ToString();
                lstGroup.Enabled = false;

            }


        }
        private bool CheckImpacNameExist()
        {
            string Editname = "";
            var res = Facade.ImpactMasterIsExistByName(txtimpactname.Text.Trim());
            if (btnSave.Text == "Update")
            {
                if (Session["ImpactName"] != null)
                {
                    Editname = Session["ImpactName"].ToString();
                }
                if (Editname == txtimpactname.Text)
                    return false;
                else
                    return res;
            }
            else
                return res;
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {

                try
                {
                    if (btnSave.Text == "Update")
                    {
                        UpdateImpactType();
                    }
                    else
                    {
                        saveImpactType();
                    }

                    StartTransaction();

                    EndTransaction();
                    if (!isNameIExists)
                    {
                        BindList();
                        Clearcontrols();
                        Bindddlsequence();
                    }




                }
                catch (CpException ex)
                {
                    InvalidateTransaction();

                    //returnUrl = Request.RawUrl;

                    //ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, this);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    //returnUrl = Request.RawUrl;

                    //ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                    {
                        ExceptionManager.Manage((CpException)ex.InnerException, this);
                    }
                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled,
                            "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, this);
                    }
                }
            }
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {

            string biaurl = "../ImpactAnalysis/ImpactMaster.aspx";

            if (!string.IsNullOrEmpty(ddlimpacttypemaster.SelectedValue))
            {
                SecureUrl BIAUrl = UrlHelper.BuildSecureUrl(biaurl, string.Empty, Constants.UrlConstants.Params.ImpactMasterId, "0", Constants.UrlConstants.Params.ImpactTypeMasterId, ddlimpacttypemaster.SelectedValue);
                Response.Redirect(BIAUrl.ToString());
            }
            else
            {
                SecureUrl BIAUrl = UrlHelper.BuildSecureUrl(biaurl, string.Empty, Constants.UrlConstants.Params.ImpactMasterId, "0", Constants.UrlConstants.Params.ImpactTypeMasterId, "0");
                Response.Redirect(BIAUrl.ToString());

            }



        }
        private void BindList()
        {
            setListViewPage();
            var GetAllImpactMaster = Facade.GetAllImpactMaster();

            if (GetAllImpactMaster != null && GetAllImpactMaster.Count() > 0)
            {

                var ImpactTypeSortByImpactOrder = (from a in GetAllImpactMaster orderby a.ImpactOrder ascending select a).ToList();
                lvComponent.DataSource = ImpactTypeSortByImpactOrder;
                lvComponent.DataBind();

            }
        }
        public string GetCategoryName(object type)
        {
            var categoryId = type.ToString();
            var Categorydetails = Facade.GetImpactTypeMasterById(Convert.ToInt32(categoryId));
            if (Categorydetails!= null &&  Categorydetails.ImpactTypeName!= null)
            {
                return Categorydetails.ImpactTypeName;    
            }
            else
            {
                return null;
            }
		}

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageImpactMasterList"]) != -1) && Session["CurrentPageImpactMasterList"] != null && (Convert.ToInt32(Session["CurrentPageImpactMasterList"]) > 0))
            {
                if (Session["TotalPageRowsCount"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageImpactMasterList"]) == Convert.ToInt32(Session["TotalPageRowsCount"]) - 1)
                    {
                        Session["CurrentPageImpactMasterList"] = Convert.ToInt32(Session["CurrentPageImpactMasterList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCount"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageImpactMasterList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageImpactMasterList"] = -1;

            }
        }

        protected void LvComponentItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                lblMessage.Text = string.Empty;
                Session["CurrentPageImpactMasterList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCount"] = dataPager1.TotalRowCount;
                var lbl = (lvComponent.Items[e.ItemIndex].FindControl("Id")) as Label;
                var lblName = (lvComponent.Items[e.ItemIndex].FindControl("ipmName")) as Label;
                if (lbl != null)
                {
                    Facade.DeleteImpactMasterById(Convert.ToInt32(lbl.Text));
                    BindList();
                    Bindddlsequence();

                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                //ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                //ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

        }

        protected void LvComponentItemEditing(object sender, ListViewEditEventArgs e)
        {
            lblMessage.Text = string.Empty;
            Session["CurrentPageImpactMasterList"] = (dataPager1.StartRowIndex);
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvComponent.Items[e.NewEditIndex].FindControl("Id")) as Label;
            var lblname = (lvComponent.Items[e.NewEditIndex].FindControl("ipmName")) as Label;
            var lblDesc = (lvComponent.Items[e.NewEditIndex].FindControl("ImpDescription")) as Label;
            var Impactorder = (lvComponent.Items[e.NewEditIndex].FindControl("Impactorder")) as Label;

            if (lbl1 != null)
            {
                Session["Id"] = lbl1.Text;
                BindUpdatedddlsequence();
                BindControlsValue(Convert.ToInt32(lbl1.Text));
            }


        }

        protected void lvComponent_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected void LvComponentPreRender(object sender, EventArgs e)
        {
            if (IsPostBack)
            {
                lvComponent.DataSource = Facade.GetAllImpactMaster();
                lvComponent.DataBind();
            }
        }
    }
}