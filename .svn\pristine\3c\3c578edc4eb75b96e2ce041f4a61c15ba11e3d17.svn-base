﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.UI.Component;
using log4net;
using SpreadsheetGear;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;


namespace CP.UI.Controls
{
    public partial class Profile_WorkflowDetails : BaseControl
    {

        private static readonly ILog _logger = LogManager.GetLogger(typeof(WorkflowProfilelist));

        static IFacade _Facade = new Facade();

        IList<Workflow> workflowlist = new List<Workflow>();
        private IWorkbookSet workbookSet = null;
        public String ssFile = string.Empty;
        private IWorkbook templateWorkbook = null;
        private IWorksheet templateWorksheet = null;
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        public override void PrepareView()
        {

        }

        protected void btnPdfSave_Click(object sender, EventArgs e)
        {
            try
            {
                string IsWorkflow_Profile = ddltype.SelectedValue;
                if (IsWorkflow_Profile.Equals("Profile"))
                {
                    Profile_Report();
                }
                else
                {
                    Workflow_Report();
                }




            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred while retriving Workflows excluded from Four Eye functionality " + ex.Message);
            }

        }

        public void Workflow_Report()
        {
            try
            {
                _logger.Info("====retriving Workflows excluded from Four Eye functionality =====");
                var wflist = _Facade.GetLimitedWorkflows_NameAndID().ToList();
                wflist = (from m in workflowlist group m by new { m.Name } into mygroup select mygroup.FirstOrDefault()).Distinct().ToList();

                string sspath = string.Empty;
                IWorkbookSet workbookSet = null;
                _logger.Info(Environment.NewLine);

                workbookSet = Factory.GetWorkbookSet();
                ssFile = Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
                templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                templateWorksheet = templateWorkbook.Worksheets[0];

                IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
                IWorksheet reportWorksheet = null;
                IWorksheet lastWorksheet = null;

                IRange _cells;

                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
                reportWorkbook.Worksheets["Sheet1"].Delete();

                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
                reportWorksheet.Name = "Manage Workflow List Report";

                _cells["A1"].ColumnWidth = 7;
                string strr = "ICICI_Bank Workflow FourEye Status Report";
                _cells["E3"].Formula = strr.ToUpper();
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].Font.Bold = true;
                _cells["B3:G6"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["E3"].Font.Bold = true;
                _cells["E3"].ColumnWidth = 30;
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].VerticalAlignment = VAlign.Top;
                _cells["B3:G3"].Font.Size = 11;
                _cells["B5:F8"].Font.Size = 10;
                _cells["B3:F8"].Font.Color = Color.FromArgb(255, 255, 255);
                _cells["B3:F8"].Font.Name = "Cambria";

                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 50, 15, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 430, 15, 120, 13);

                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 500, 15, 121, 13);
                }

                reportWorksheet.Cells["A1:G1"].RowHeight = 27;

                var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                _cells["B5"].Formula = "Report Generated Time : " + dateTime;
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;
                _cells["B5"].Font.Size = 10;


                int row = 8;
                int i = 1;
                int j = 1;

                _cells["B8:G8"].Interior.Color = Color.FromArgb(79, 129, 189);

                _cells["B" + row.ToString()].Formula = "SR.NO.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:G8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:G8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.FromArgb(0, 0, 0);
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Workflow";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["C" + row.ToString()].ColumnWidth = 30;
                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "InfraObject Name";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["D" + row.ToString()].ColumnWidth = 45;

                _cells["E" + row.ToString()].Formula = "Created By";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["E" + row.ToString()].ColumnWidth = 45;

                _cells["F" + row.ToString()].Formula = "Create Date";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["F" + row.ToString()].ColumnWidth = 45;

                _cells["G" + row.ToString()].Formula = "Four Eye Status";
                _cells["G" + row.ToString()].Font.Bold = true;
                _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["G" + row.ToString()].ColumnWidth = 45;

                _logger.Info("====== Workflow/Profile List Report EXCEL Desgin Completed. ======");

                row++;
                int dataCount = 0;
                int xlRow = 9;

                IList<Workflow> workflows = _Facade.GetAllWorkflows_NameAndID().ToList();

                if ((workflows != null) && (workflows.Count > 0))
                {

                    foreach (var item in workflows)
                    {
                        string IsFourEye = string.Empty;//item.WorkflowId.Equals("0") ? "Disable" : "Enable";
                        if (item.WorkflowId==0)
                        {
                            IsFourEye = "Disable";
                        }
                        else
                        {
                            IsFourEye = "Enable";
                        }

                        var infraname = _Facade.GetInfraobjectByWorkflowId(item.Id);
                        string InfraObject = !string.IsNullOrEmpty(infraname.Name) ? infraname.Name : "NA";

                        dataCount++;

                        int column = 0;
                        xlRow++;
                        _cells["B" + row + ":" + "G" + row].Interior.Color = row % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.FromArgb(255, 255, 255);
                        string ndx = "B" + row.ToString();

                        _cells[ndx].Formula = i.ToString();
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 10;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        i++;
                        column++;

                        ndx = "C" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.Name) ? item.Name : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "D" + row.ToString();
                        _cells[ndx].Formula = InfraObject;
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "E" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.User) ? item.User : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        DateTime CreatDate = item.CreateDate;
                        string CDate = CreatDate.ToString();

                        ndx = "F" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(CDate) ? CDate : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "G" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(IsFourEye) ? IsFourEye : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;


                        row++;
                    }
                    lastWorksheet = null;
                    lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                    reportWorksheet.ProtectContents = true;

                    _logger.Info("============ Workflow/Profile List  report Method Execution Completed. ============");

                    Response.Clear();
                    Response.ContentType = "application/vnd.ms-excel";
                    Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");

                    var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
                    str = "Workflow excluede from Four Eye Report " + "_" + str + ".xls";
                    reportWorkbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

                    string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
                    string myUrl = reportPath + "/ExcelFiles/" + str;
                    var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar= RPO SLA Report');";
                    ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in Workflow excluede from Four Eye Report");
            }
        }

        public void Profile_Report()
        {
            try
            {
                _logger.Info("====retriving Profile excluded from Four Eye functionality =====");
                var wflist = _Facade.GetLimitedWorkflows_NameAndID().ToList();
                wflist = (from m in workflowlist group m by new { m.Name } into mygroup select mygroup.FirstOrDefault()).Distinct().ToList();

                string sspath = string.Empty;
                IWorkbookSet workbookSet = null;
                _logger.Info(Environment.NewLine);

                workbookSet = Factory.GetWorkbookSet();
                ssFile = Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
                templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                templateWorksheet = templateWorkbook.Worksheets[0];

                IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
                IWorksheet reportWorksheet = null;
                IWorksheet lastWorksheet = null;

                IRange _cells;

                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
                reportWorkbook.Worksheets["Sheet1"].Delete();

                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
                reportWorksheet.Name = "Manage Workflow List Report";

                _cells["A1"].ColumnWidth = 7;
                string strr = "ICICI_Bank Profile FourEye Status Report";
                _cells["E3"].Formula = strr.ToUpper();
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].Font.Bold = true;
                _cells["B3:G6"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["E3"].Font.Bold = true;
                _cells["E3"].ColumnWidth = 30;
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].VerticalAlignment = VAlign.Top;
                _cells["B3:G3"].Font.Size = 11;
                _cells["B5:G8"].Font.Size = 10;
                _cells["B3:G8"].Font.Color = Color.FromArgb(255, 255, 255);
                _cells["B3:G8"].Font.Name = "Cambria";

                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 50, 15, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 430, 15, 120, 13);

                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 500, 15, 121, 13);
                }

                reportWorksheet.Cells["A1:G1"].RowHeight = 27;

                var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                _cells["B5"].Formula = "Report Generated Time : " + dateTime;
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;
                _cells["B5"].Font.Size = 10;


                int row = 8;
                int i = 1;
                int j = 1;

                _cells["B8:G8"].Interior.Color = Color.FromArgb(79, 129, 189);

                _cells["B" + row.ToString()].Formula = "SR.NO.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:G8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:G8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.FromArgb(0, 0, 0);
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Workflow";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["C" + row.ToString()].ColumnWidth = 30;
                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "Workflows";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["D" + row.ToString()].ColumnWidth = 45;

                _cells["E" + row.ToString()].Formula = "Created By";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["E" + row.ToString()].ColumnWidth = 45;

                _cells["F" + row.ToString()].Formula = "Create Date";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["F" + row.ToString()].ColumnWidth = 45;

                _cells["G" + row.ToString()].Formula = "Four Eye Status";
                _cells["G" + row.ToString()].Font.Bold = true;
                _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["G" + row.ToString()].ColumnWidth = 45;

                _logger.Info("====== Workflow/Profile List Report EXCEL Desgin Completed. ======");

                row++;
                int dataCount = 0;
                int xlRow = 9;


                var allprofiles = Facade.GetAllParallelProfile_forfoureye();

                if ((allprofiles != null) && (allprofiles.Count > 0))
                {

                    foreach (var item in allprofiles)
                    {
                        string IsFourEye = string.Empty;
                        if (item.IsFourEye==0)
                        {
                            IsFourEye = "Disable";
                        }
                        else
                        {
                            IsFourEye = "Enable";
                        }
                        var wfname = Facade.GetworkflowByprofileId(Convert.ToInt32(item.Id));
                        string InfraObject = !string.IsNullOrEmpty(wfname) ? wfname : "NA";

                        dataCount++;

                        int column = 0;
                        xlRow++;
                        _cells["B" + row + ":" + "G" + row].Interior.Color = row % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.FromArgb(255, 255, 255);
                        string ndx = "B" + row.ToString();

                        _cells[ndx].Formula = i.ToString();
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 10;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        i++;
                        column++;

                        ndx = "C" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.ProfileName) ? item.ProfileName : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "D" + row.ToString();
                        _cells[ndx].Formula = InfraObject;
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "E" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.CreatorId.ToString()) ? item.CreatorId.ToString() : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        DateTime CreatDate = item.CreateDate;
                        string CDate = CreatDate.ToString();

                        ndx = "F" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(CDate) ? CDate : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "G" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(IsFourEye) ? IsFourEye : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;


                        row++;
                    }
                    lastWorksheet = null;
                    lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                    reportWorksheet.ProtectContents = true;

                    _logger.Info("============ Workflow/Profile List  report Method Execution Completed. ============");

                    Response.Clear();
                    Response.ContentType = "application/vnd.ms-excel";
                    Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");

                    var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
                    str = "Profile excluede from Four Eye Report " + "_" + str + ".xls";
                    reportWorkbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

                    string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
                    string myUrl = reportPath + "/ExcelFiles/" + str;
                    var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar= RPO SLA Report');";
                    ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
                }
            }
            catch (Exception)
            {

                throw;
            }
        }
    }
}