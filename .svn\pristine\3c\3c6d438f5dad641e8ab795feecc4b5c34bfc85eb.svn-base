﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI;
using CP.UI.Controls;
using Jscape.Util;
using log4net;
//using Org.BouncyCastle.Asn1.X509;
using SpreadsheetGear;
using SpreadsheetGear.Drawing;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using Telerik.Web.UI;
using System.Text;

namespace CP.UI.Controls
{
    public partial class FourEyeApproverReport : BaseControl
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(FourEyeApproverReport));
        private IWorkbookSet workbookSet = null;
        public String ssFile = string.Empty;
        private IWorkbook templateWorkbook = null;
        private IWorksheet templateWorksheet = null;
        public IFacade facade = new Facade();
        IRange _cells = null;


        protected void Page_Load(object sender, EventArgs e)
        {

        }
        public override void PrepareView()
        {
            //    throw new NotImplementedException();
            //CalendarExtender1.EndDate = DateTime.Now.Date;
            //CalendarExtender2.EndDate = DateTime.Now.Date;
            btnPdfSave.Visible = true;
            Label2.Visible = false;
            // ddltype.Items.Insert(0, new ListItem("- Select Type -", "0"));
            //ddlstatus.Items.Insert(0, new ListItem("- Select Status -", "0"));
            //  BindData();
            //int val = ddltype.SelectedIndex;
            //if (val > 0)
            //{

            //}
        }
        public void GetExcelReport()
        {
            try
            {
                string sspath = string.Empty;
                IWorkbookSet workbookSet = null;

                string AprroverList = null;
                _logger.Info("====== Generating Approver Report ======");
                _logger.Info(Environment.NewLine);

                workbookSet = Factory.GetWorkbookSet();
                ssFile = Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
                templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                templateWorksheet = templateWorkbook.Worksheets[0];

                IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
                IWorksheet reportWorksheet = null;
                IWorksheet lastWorksheet = null;

                IRange _cells;

                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);
                reportWorkbook.Worksheets["Sheet1"].Delete();

                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
                reportWorksheet.Name = "Four Eye profile Approvers";

                _cells["A1"].ColumnWidth = 7;
                string strr = "ICICI_Bank Four Eye Approvers(Profile)";
                _cells["E3"].Formula = strr.ToUpper();
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].Font.Bold = true;
                _cells["B3:F6"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["E3"].Font.Bold = true;
                _cells["E3"].ColumnWidth = 30;
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].VerticalAlignment = VAlign.Top;
                _cells["B3:F3"].Font.Size = 11;
                _cells["B5:F8"].Font.Size = 10;
                _cells["B3:F8"].Font.Color = Color.FromArgb(255, 255, 255);
                _cells["B3:F8"].Font.Name = "Cambria";

                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 50, 15, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 620, 15, 120, 13);

                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 500, 15, 121, 13);
                }

                reportWorksheet.Cells["A1:C1"].RowHeight = 27;

                var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                _cells["B5"].Formula = "Report Generated Time : " + dateTime;
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;
                _cells["B5"].Font.Size = 10;


                int row = 8;
                int i = 1;
                int j = 1;

                _cells["B8:F8"].Interior.Color = Color.FromArgb(79, 129, 189);

                _cells["B" + row.ToString()].Formula = "SR.NO.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:F8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:F8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.FromArgb(0, 0, 0);
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Profile";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["C" + row.ToString()].ColumnWidth = 40;
                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "Action Type";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["D" + row.ToString()].ColumnWidth = 23;

                _cells["E" + row.ToString()].Formula = "Approvers";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["E" + row.ToString()].ColumnWidth = 40;

                _cells["F" + row.ToString()].Formula = "Related Groups";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["F" + row.ToString()].ColumnWidth = 30;
                

                _logger.Info("====== Four Eye Approver Report for Profile EXCEL Desgin Completed. ======");

                row++;
                int dataCount = 0;
                int xlRow = 9;

                IFacade _facade = new Facade();

                IList<ApprovalProcess> approvalProcess = new List<ApprovalProcess>();
                IList<ApprovalProcess> AllapprovalProcess = new List<ApprovalProcess>();
                IList<ApprovalProcess> profile = new List<ApprovalProcess>();
                IList<ApprovalProcess> workflow = new List<ApprovalProcess>();
                IList<ApprovalProcess> ProfileAndWorkflow = new List<ApprovalProcess>();
                IList<string> _nm = new List<string>();
                StringBuilder Grp = new StringBuilder();
                string grpusr = null;
                List<string> userName = new List<string>();
                List<string> grpuser = new List<string>();
               
                approvalProcess = Facade.GetAllApproverDetails();
                AllapprovalProcess = Facade.GetAllApproverDetails_ForAll(); 

                profile = approvalProcess.Where(a => a.PROFILE_OR_WORKFLOW == "Profile").ToList();

                workflow = approvalProcess.Where(a => a.PROFILE_OR_WORKFLOW == "Workflow").ToList();

                ProfileAndWorkflow = approvalProcess.OrderBy(a => a.PROFILE_OR_WORKFLOW).ToList();


                if (profile.Count == 0 && workflow.Count== 0 && ProfileAndWorkflow.Count == 0 )                  
                {
                    Label2.Visible = true;
                    Label2.Text = "No Record Found";
                    
                }
                 //var lst = Facade.GetGroupName_ByUserid(groupId);
                if ((profile != null) && (profile.Count > 0))
                {
                    foreach (var item in profile)
                    {
                      
                        dataCount++;
                        StringBuilder App = new StringBuilder();
                        StringBuilder AppUser = new StringBuilder();
                         string[] Ap_lst = item.Approver.Split(',');
                       // string [] AP_LST = Ap_lst.Count ;
                       
                         foreach (var a in Ap_lst)
                         {
                             if (!string.IsNullOrEmpty(a))
                             {
                                 string[] grp = a.Split('$');
                                 int groupId = grp[1].Trim().ToInteger();                                
                                 if (groupId > 0)
                                 {
                                     var lst = Facade.GetGroupName_ByUserid(groupId);
                                  
                                     if (lst.UserName != null)
                                     {
                                         string Aprover_Group = "" + grp[0] + "-(" + lst.UserName + ")";
                                         AprroverList = App.Append(Aprover_Group + "\n").ToString();
                                         userName.Add(lst.UserName);
                                     }
                                 }
                             }
                         }
                         //userName.Distinct();
                         foreach (var c in userName)
                         {
                             if (!_nm.Contains(c))
                             {
                                 _nm.Add(c);
                                
                             }                             
                         }
                         foreach (var d in _nm)
                         {
                            grpusr = AppUser.Append(d + "\n").ToString();
                         }

                        int column = 0;
                        xlRow++;
                        _cells["B" + row + ":" + "F" + row].Interior.Color = row % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.FromArgb(255, 255, 255);
                        string ndx = "B" + row.ToString();

                        _cells[ndx].Formula = i.ToString();
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 10;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        i++;
                        column++;

                        ndx = "C" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.ProfileName) ? item.ProfileName : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 40;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "D" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.EXECUTE_OR_MODIFY) ? item.EXECUTE_OR_MODIFY : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 23;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                       // row++;

                        ndx = "E" + row.ToString();
                         _cells[ndx].Formula = !string.IsNullOrEmpty(AprroverList) ? AprroverList : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 50;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                       // row++;
                      
                        ndx = "F" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(grpusr) ? grpusr : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                        row++;
                       

                    }

                    lastWorksheet = null;
                    lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                    reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

                    WorkflowApproverReport(reportWorksheet, workflow);

                    lastWorksheet = null;
                    lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                    reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

                    WorkflowAndProfileApproverReport(reportWorksheet, AllapprovalProcess);

                    OpenExcelFile(reportWorkbook, reportWorksheet);


                }


            }
    
            catch (Exception ex)
            {
                _logger.Error("==== Exception in generating approver list report " + ex.InnerException);
            }
        }


        private void WorkflowApproverReport(IWorksheet reportWorksheet, IList<ApprovalProcess> workflow)
        {
            try
            {
                string AprroverList = null;
                IRange _cells;
                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
                reportWorksheet.Name = "Four Eye workflow Approvers";


                _cells["A1"].ColumnWidth = 7;
                string strr = "ICICI_Bank Four Eye Approvers(Workflow)";
                _cells["E3"].Formula = strr.ToUpper();
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].Font.Bold = true;
                _cells["B3:F6"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["E3"].Font.Bold = true;
                _cells["E3"].ColumnWidth = 30;
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].VerticalAlignment = VAlign.Top;
                _cells["B3:F3"].Font.Size = 11;
                _cells["B5:F8"].Font.Size = 10;
                _cells["B3:F8"].Font.Color = Color.FromArgb(255, 255, 255);
                _cells["B3:F8"].Font.Name = "Cambria";

                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 50, 15, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 620, 15, 120, 13);

                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 500, 15, 121, 13);
                }

                reportWorksheet.Cells["A1:C1"].RowHeight = 27;

                var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                _cells["B5"].Formula = "Report Generated Time : " + dateTime;
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;
                _cells["B5"].Font.Size = 10;


                int row = 8;
                int i = 1;
                int j = 1;

                _cells["B8:F8"].Interior.Color = Color.FromArgb(79, 129, 189);

                _cells["B" + row.ToString()].Formula = "SR.NO.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:F8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:F8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.FromArgb(0, 0, 0);
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Workflow";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["C" + row.ToString()].ColumnWidth = 40;
                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "Action Type";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["D" + row.ToString()].ColumnWidth = 23;

                _cells["E" + row.ToString()].Formula = "Approvers";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["E" + row.ToString()].ColumnWidth = 40;

                _cells["F" + row.ToString()].Formula = "Related Groups";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["F" + row.ToString()].ColumnWidth = 30;
                

                _logger.Info("====== Four Eye Approver Report for workflow EXCEL Desgin Completed. ======");

                row++;
                int dataCount = 0;
                int xlRow = 9;

                if ((workflow != null) && (workflow.Count > 0))
                {
                    foreach (var item in workflow)
                    {
                        dataCount++;
                        StringBuilder AppUser = new StringBuilder();
                        StringBuilder App = new StringBuilder();
                        IList<string> _nm = new List<string>();
                        StringBuilder Grp = new StringBuilder();
                        string grpusr = null;
                        List<string> userName = new List<string>();
                        List<string> grpuser = new List<string>();
                        string[] Ap_lst = item.Approver.Split(',');
                        // string [] AP_LST = Ap_lst.Count ;
                        foreach (var a in Ap_lst)
                        {
                            if (!string.IsNullOrEmpty(a))
                            {


                                string[] grp = a.Split('$');
                                int groupId = grp[1].Trim().ToInteger();
                                if (groupId > 0)
                                {
                                    var lst = Facade.GetGroupName_ByUserid(groupId);
                                    if (lst.UserName != null)
                                    {
                                        string Aprover_Group = "" + grp[0] + "-(" + lst.UserName + ")";
                                        AprroverList = App.Append(Aprover_Group + "\n").ToString();
                                        userName.Add(lst.UserName);
                                    }
                                }
                            }
                        }
                        //userName.Distinct();
                        foreach (var c in userName)
                        {
                            if (!_nm.Contains(c))
                            {
                                _nm.Add(c);

                            }
                        }
                        foreach (var d in _nm)
                        {
                            grpusr = AppUser.Append(d + "\n").ToString();
                        }

                        int column = 0;
                        xlRow++;
                        _cells["B" + row + ":" + "F" + row].Interior.Color = row % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.FromArgb(255, 255, 255);
                        string ndx = "B" + row.ToString();

                        _cells[ndx].Formula = i.ToString();
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 10;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        i++;
                        column++;

                        ndx = "C" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.ProfileName) ? item.ProfileName : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 40;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "D" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.EXECUTE_OR_MODIFY) ? item.EXECUTE_OR_MODIFY : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 23;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                       // row++;


                        ndx = "E" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(AprroverList) ? AprroverList : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 50;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                      //  row++;

                        ndx = "F" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(grpusr) ? grpusr : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                        row++;

                    }
                }

            }
            catch (Exception ex)
            {
                _logger.Error("==== Exception occurred in WorkflowApproverReport");
            }
        }

        private void WorkflowAndProfileApproverReport(IWorksheet reportWorksheet, IList<ApprovalProcess> AllapprovalProcess)
        {
            try
            {
                string AprroverList = null;
                IRange _cells;
                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
                reportWorksheet.Name = "All Profile workflow Approvers";


                _cells["A1"].ColumnWidth = 7;
                string strr = "ICICI_Bank Four Eye Approvers(Workflow)";
                _cells["E3"].Formula = strr.ToUpper();
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].Font.Bold = true;
                _cells["B3:F6"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["E3"].Font.Bold = true;
                _cells["E3"].ColumnWidth = 30;
                _cells["E3"].HorizontalAlignment = HAlign.Center;
                _cells["E3"].VerticalAlignment = VAlign.Top;
                _cells["B3:F3"].Font.Size = 11;
                _cells["B5:F8"].Font.Size = 10;
                _cells["B3:F8"].Font.Color = Color.FromArgb(255, 255, 255);
                _cells["B3:F8"].Font.Name = "Cambria";

                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 50, 15, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 620, 15, 120, 13);

                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 500, 15, 121, 13);
                }

                reportWorksheet.Cells["A1:C1"].RowHeight = 27;

                var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                _cells["B5"].Formula = "Report Generated Time : " + dateTime;
                _cells["B5"].Font.Bold = true;
                _cells["B5"].HorizontalAlignment = HAlign.Left;
                _cells["B5"].Font.Size = 10;


                int row = 8;
                int i = 1;
                int j = 1;

                _cells["B8:F8"].Interior.Color = Color.FromArgb(79, 129, 189);

                _cells["B" + row.ToString()].Formula = "SR.NO.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:F8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:F8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.FromArgb(0, 0, 0);
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Profile/Workflow";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["C" + row.ToString()].ColumnWidth = 40;
                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "Action Type";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["D" + row.ToString()].ColumnWidth = 23;

                _cells["E" + row.ToString()].Formula = "Approvers";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["E" + row.ToString()].ColumnWidth = 40;

                _cells["F" + row.ToString()].Formula = "Related Groups";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["F" + row.ToString()].ColumnWidth = 30;

                _logger.Info("====== Four Eye Approver Report for workflow EXCEL Desgin Completed. ======");

                row++;
                int dataCount = 0;
                int xlRow = 9;

                if ((AllapprovalProcess != null) && (AllapprovalProcess.Count > 0))
                {
                    foreach (var item in AllapprovalProcess)
                    {
                        dataCount++;
                        StringBuilder AppUser = new StringBuilder();
                        StringBuilder App = new StringBuilder();
                        IList<string> _nm = new List<string>();
                        StringBuilder Grp = new StringBuilder();
                        string grpusr = null;
                        List<string> userName = new List<string>();
                        List<string> grpuser = new List<string>();
                        string[] Ap_lst = item.Approver.Split(',');
                        // string [] AP_LST = Ap_lst.Count ;
                        foreach (var a in Ap_lst)
                        {
                            if (!string.IsNullOrEmpty(a))
                            {


                                string[] grp = a.Split('$');
                                int groupId = grp[1].Trim().ToInteger();
                                if (groupId > 0)
                                {
                                    var lst = Facade.GetGroupName_ByUserid(groupId);
                                    if (lst.UserName != null)
                                    {
                                        string Aprover_Group = "" + grp[0] + "-(" + lst.UserName + ")";
                                        AprroverList = App.Append(Aprover_Group + "\n").ToString();
                                        userName.Add(lst.UserName);
                                    }
                                }
                            }
                        }
                        //userName.Distinct();
                        foreach (var c in userName)
                        {
                            if (!_nm.Contains(c))
                            {
                                _nm.Add(c);

                            }
                        }
                        foreach (var d in _nm)
                        {
                            grpusr = AppUser.Append(d + "\n").ToString();
                        }

                        int column = 0;
                        xlRow++;
                        _cells["B" + row + ":" + "F" + row].Interior.Color = row % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.FromArgb(255, 255, 255);
                        string ndx = "B" + row.ToString();

                        _cells[ndx].Formula = i.ToString();
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 10;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        i++;
                        column++;

                        ndx = "C" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.PROFILE_OR_WORKFLOW) ? item.PROFILE_OR_WORKFLOW : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 40;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;

                        ndx = "D" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(item.EXECUTE_OR_MODIFY) ? item.EXECUTE_OR_MODIFY : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 23;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                       // row++;


                        ndx = "E" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(AprroverList) ? AprroverList : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 50;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                       // row++;

                        ndx = "F" + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(grpusr) ? grpusr : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 30;
                        _cells[ndx].Font.Color = Color.FromArgb(0, 0, 0);
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        _cells[ndx].WrapText = true;
                        column++;
                        row++;

                    }
                }

            }
            catch (Exception ex)
            {
                _logger.Error("==== Exception occurred in WorkflowApproverReport");
            }
        }

        private void OpenExcelFile(IWorkbook workbook, IWorksheet worksheet)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename= ParallelDROperation.xls");
            string _str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            _str = "Four Eye Approver List" + _str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + _str), FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + _str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=DR Drill Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }

        protected void btnPdfSave_Click(object sender, EventArgs e)
        {
            GetExcelReport();
        }

    }
}