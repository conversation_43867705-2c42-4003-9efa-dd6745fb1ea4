﻿using System;
using System.Web.UI.WebControls;
using System.Linq;
using CP.BusinessFacade;
using CP.ExceptionHandler;
using CP.Helper;
using CP.Common.Shared;
using System.Collections.Generic;
using CP.Common.DatabaseEntity;
using System.Web;

namespace CP.UI.Controls
{
    public partial class SnapMirrorOverview : BaseControl
    {

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;
        ReplicationType type;
        private DropDownList _ddlReplicationType = new DropDownList();
        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlPrRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }
        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            //     Binddata();
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        private void Binddata()
        {
           
            //var result = Facade.GetSnapMirrorsByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId,
             //                                             IsParentCompnay);

            type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue);
            lvSnapMirror.DataSource = GetSnapMirrorListByReplicationType(type.ToString());
            lvSnapMirror.DataBind();
           // setListViewPage();

        }
        /// <summary>
        /// if deleting or updating the List of Buisiness Service exchange the page will get postback and then Listview  swap to the same page
        /// where it is deleted or updated. - Goraknath Khule.
        /// </summary>
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageSnapMirrorOverviewList"]) != -1) && Session["CurrentPageSnapMirrorOverviewList"] != null && (Convert.ToInt32(Session["CurrentPageSnapMirrorOverviewList"]) > 0))
            {
                if (Session["TotalPageRowsCountSnapMirrorList"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageSnapMirrorOverviewList"]) == Convert.ToInt32(Session["TotalPageRowsCountSnapMirrorList"]) - 1)
                    {
                        Session["CurrentPageSnapMirrorOverviewList"] = Convert.ToInt32(Session["CurrentPageSnapMirrorOverviewList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCountSnapMirrorList"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageSnapMirrorOverviewList"]), dataPager1.MaximumRows, true);
                dataPager1.DataBind();
                Session["CurrentPageSnapMirrorOverviewList"] = -1; 
            }
        }
        private IList<SnapMirror> GetSnapMirrorList()
        {
            return Facade.GetSnapMirrorsByCompanyIdAndRole(IsUserSuperAdmin, Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]), IsParentCompnay);
        }

        public IList<SnapMirror> GetSnapMirrorListByReplicationType(string iType)
        {
            var replicationlist = GetSnapMirrorList();

            if (replicationlist != null)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.reptype == iType
                              select replication).ToList();

                return result;
            }
            return null;
        }
        public IList<SnapMirror> GetSnapMirrorList(string searchvalue)
        {
            var replicationlist = GetSnapMirrorList();
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count > 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvSnapMirror.Items.Clear();
                lvSnapMirror.DataSource = GetSnapMirrorList(txtsearchvalue.Text);
                lvSnapMirror.DataBind();
            }
        }

        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByReplicationId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Replication " + name + " attaching with group " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }

        protected void LvReplication_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator||IsUserManager || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }
        protected void LvSnapMirrorItemDeleting(object sender, ListViewDeleteEventArgs e)
        {

            try
            {
                Session["CurrentPageSnapMirrorOverviewList"] = dataPager1.StartRowIndex;
                Session["TotalPageRowsCountSnapMirrorList"] = dataPager1.TotalRowCount;
                var lblId = lvSnapMirror.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvSnapMirror.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;
                //var ReplicationDetail = Facade.GetReplicationBaseById(Convert.ToInt32(lblId.Text));
                //var SiteDetail = Facade.GetSiteById(ReplicationDetail.SiteId);
                var InfraObjects = Facade.GetInfraobjectByReplicationId(Convert.ToInt32(lblId.Text));
                if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("SnapMirrorOverview Delete", UserActionType.ReplicationList))
                {
                    //var applicationDetailByReplicationId =
                    //    Facade.GetApplicationGroupsByReplicationId(Convert.ToInt32(lblId.Text));
                    //if (applicationDetailByReplicationId != null)
                    //{
                    //    ErrorSuccessNotifier.AddSuccessMessage("The SnapMirror Replication component is in use");
                    //}
                    //else
                    //{
                    if (InfraObjects!=null&&InfraObjects.Count>0)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The SnapMirrorOverview Replication component is in use.");
                      
                    }
                    else
                    {
                        Facade.DeleteReplicationBaseById(Convert.ToInt32(lblId.Text));
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "SnapMirror", UserActionType.DeleteReplicationComponent,
                                              "The SnapMirror Replication component '" + lblName.Text +
                                              "' was deleted from the replication component", LoggedInUserId);

                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "SnapMirror Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));

                   }
                }

            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);

            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);

                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                Helper.Url.Redirect(secureUrl);
            }

        }

        protected void LvSnapMirrorItemEditing(object sender, ListViewEditEventArgs e)
        {
            Session["CurrentPageSnapMirrorOverviewList"] = dataPager1.StartRowIndex;
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvSnapMirror.Items[e.NewEditIndex].FindControl("Id")) as Label;

            var lblName = (lvSnapMirror.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;
            ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "SnapMirror", UserActionType.UpdateReplicationComponent,
                                   "The SnapMirror Replication component '" + lblName.Text +
                                   "' Opened as Editing Mode", LoggedInUserId);
            if (lbl1 != null && lblName != null && ValidateRequest("SnapMirrorOverview Edit", UserActionType.ReplicationList))
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                Helper.Url.Redirect(secureUrl);
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                //    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
            }
            //if (secureUrl != null)
            //{
            //    Helper.Url.Redirect(secureUrl);
            //}

        }


        protected void LvSnapMirrorPreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }
    }
}