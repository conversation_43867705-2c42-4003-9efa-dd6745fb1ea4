﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DRReddynesslog.ascx.cs"
    Inherits="CP.UI.Controls.DRReddynesslog" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<link href="../App_Themes/ReportTheme/Report.css" rel="stylesheet" type="text/css" />
<link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
<script src="../Script/chosen.jquery.js"></script>
<style type="text/css">
    .rowStyleHeader.bold, .rowStyleHeader.bold {
        font-size: 9pt !important;
    }

    .chosen-select + .chosen-container {
        width: 48.5% !important;
        opacity: 1 !important;
    }
</style>
<script>
    $(document).ready(function () {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    });
    function pageLoad() {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    }
</script>
<%--<asp:UpdatePanel ID="UpdatePanel1" runat="server">
    <ContentTemplate>--%>

<div class="form-group" runat="server" id="Div1">
    <label class="col-md-3 control-label">
        DR Ready<%-- Readiness--%> <span class="inactive"></span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddldr" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddldr_SelectedIndexChanged"><%--OnSelectedIndexChanged="ddldr_SelectedIndexChanged"--%>
           <%--<asp:ListItem Value="0">All</asp:ListItem>--%>
            <asp:ListItem Value="0">ALL</asp:ListItem>
            <asp:ListItem Value="1">DR Ready</asp:ListItem>
            <asp:ListItem Value="2">DR Not Ready</asp:ListItem>

        </asp:DropDownList>
    </div>
</div>


<div class="form-group">
    <label class="col-md-3 control-label">
        Business Service <span class="inactive"></span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlBussService" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlBussService_SelectedIndexChanged"></asp:DropDownList>
        <asp:Label ID="lblmgs1" runat="server" ForeColor="Red" Visible="false"></asp:Label>
    </div>
</div>

<div class="form-group" visible="false" runat="server" id="dvbf">
    <label class="col-md-3 control-label">
        Business Function <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlfn" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlfn_SelectedIndexChanged"></asp:DropDownList>
    </div>
</div>

<div class="form-group" visible="false" runat="server" id="dvinfra">
    <label class="col-md-3 control-label">
        Infraobject Name <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlinfra" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="true" OnSelectedIndexChanged="ddlinfra_SelectedIndexChanged"></asp:DropDownList>
    </div>
</div>

<%--<div class="form-group" runat="server" id="Div1">
    <label class="col-md-3 control-label">
        DR Ready Readiness <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddldr" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" AutoPostBack="true">
            <asp:ListItem Value="0">All</asp:ListItem>
            <asp:ListItem Value="1">DR Ready</asp:ListItem>
            <asp:ListItem Value="2">DR Not Ready</asp:ListItem>

        </asp:DropDownList>
    </div>
</div>--%>

<asp:UpdatePanel ID="uppnlDate" UpdateMode="Always" runat="server">
    <ContentTemplate>


        <div class="form-group">
            <label class="col-md-3 control-label">
                Start Date <span class="inactive">*</span>
            </label>
            <div class="col-md-9">
                <TK1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtstart" PopupPosition="Right"
                    PopupButtonID="imgFromDate" Format="yyyy-MM-dd">
                </TK1:CalendarExtender>
                <asp:TextBox ID="txtstart" runat="server" CssClass="form-control" AutoPostBack="True" OnTextChanged="txtstart_TextChanged"></asp:TextBox>
                <%--<img src="../images/icons/calendar-month.png" width="16" id="imgFromDate" style="margin-left: 5px;" />--%>
                <asp:ImageButton ID="imgFromDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                <asp:RequiredFieldValidator ID="rfvSic" ForeColor="Red" runat="server" ErrorMessage="Select Date" Display="Dynamic" ControlToValidate="txtstart" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                <%--<asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ForeColor="Red"  ErrorMessage="Select valid date." ValidationGroup="vlGroupSite"
                    ControlToValidate="txtstart" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>--%>
                <asp:Label ID="lblsdateErr" Visible="false" runat="server" Text="Select Valid Date" ForeColor="Red"></asp:Label>
            </div>
        </div>

        <div class="form-group">
            <label class="col-md-3 control-label">
                End Date <span class="inactive">*</span>
            </label>
            <div class="col-md-9">
                <asp:TextBox ID="txtend" runat="server" CssClass="form-control" AutoPostBack="True" OnTextChanged="txtend_TextChanged"></asp:TextBox>
                <%--<img
            src="../images/icons/calendar-month.png" width="16" id="img1" style="margin-left: 5px;" />--%>
                <asp:ImageButton ID="imgBtnEndDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />

                <TK1:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtend" PopupPosition="Right"
                    PopupButtonID="imgBtnEndDate" Format="yyyy-MM-dd">
                </TK1:CalendarExtender>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator3" Display="Dynamic" ForeColor="Red" runat="server" ErrorMessage="Select Date"
                    ControlToValidate="txtend" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                <%--  <asp:RegularExpressionValidator ID="RegularExpressionValidator2" ForeColor="Red" runat="server" ErrorMessage="Select valid date." ValidationGroup="vlGroupSite"
                    ControlToValidate="txtend" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>--%>
                <asp:Label ID="lbledateErr" Visible="false" runat="server" Text="Select Valid Date" ForeColor="Red"></asp:Label>
            </div>
        </div>

    </ContentTemplate>
</asp:UpdatePanel>
<asp:Panel ID="Pan1" CssClass="widget widget-heading-simple widget-body-white" runat="server" Visible="false">
    <hr />
    <div class="widget-head">
        <div class="col-md-4">
            <h4>DR Readyness Execution Log Report</h4>
        </div>
        <div class="col-md-8 text-right">

            <asp:Button ID="btnPdf" ValidationGroup="vlGroupSite" CssClass="btn btn-primary" Width="20%" runat="server" Text="Export To Pdf" OnClick="btnPdf_Click" />
            <asp:Button ID="btnExcel" CssClass="btn btn-primary" Width="20%" runat="server" Text="Export To Excel" OnClick="btnExcel_Click" />
        </div>
    </div>
    <hr />
    <div class="widget-body">
        <div style="overflow: auto; height: 500px;">
            <asp:Table ID="tbl" CssClass="dtable" Style="font-size: 10.5px; width: 100%" runat="server">
            </asp:Table>
        </div>
        <hr />
        <div class="row">
            <div class="col-md-4 ">NA: Not Available </div>
            <div class="col-md-4 text-success">
                <asp:Label ID="lblDatalagG" runat="server"></asp:Label>
            </div>
            <div class="col-md-4 text-danger">
                <asp:Label ID="lblDatalagL" runat="server"></asp:Label>
            </div>
        </div>

        <hr class="separator" />
    </div>
</asp:Panel>

<hr />
<div class="form-group">
    <div id="divlable" class="col-xs-6" visible="false">
        <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false"></asp:Label>
        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
    </div>
    <div class="col-xs-6">
        <asp:Button ID="btnview" runat="server" CssClass="btn btn-primary" Style="margin-left: 5px" ValidationGroup="vlGroupSite" Width="20%" OnClick="btnview_Click"
            Text="Export To Excel" />
    </div>
</div>
<%--</ContentTemplate>
</asp:UpdatePanel>--%>

<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div id="imgLoading" class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>
