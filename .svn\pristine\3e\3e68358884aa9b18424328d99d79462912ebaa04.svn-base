﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Helper;
using SpreadsheetGear;
using System.Web.UI.WebControls;
using System.Data;
using Gios.Pdf;
using System.Drawing;
using System.Collections;
using System.Globalization;
using System.Configuration;
using CP.UI;
using CP.Common.Shared;
using System.IO;
using log4net;

namespace CP.UI.Controls
{
    public partial class ApplicationdependancyReport : BaseControl
    {
        #region Variables

        private string[] xlColumn = { "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "Z" };
        IWorkbookSet workbookSet = null;
        String ssFile = string.Empty;
        IWorkbook templateWorkbook = null;
        IWorksheet templateWorksheet = null;
        IRange cells = null;
        private readonly ILog _logger = LogManager.GetLogger(typeof(ApplicationdependancyReport));
        #endregion Variables

        public override void PrepareView()
        {


            var profilelist = Facade.GetAllAppDepMappingProfiles();

            if (profilelist != null)
            {
                ddldependencyprofile.DataSource = profilelist;
                //ddldiscoveryprofile.DataValueField = profilelist;
                ddldependencyprofile.DataBind();
                ddldependencyprofile.Items.Insert(0, new ListItem("Select Profile Name", "0"));

            }
            else

                ddldependencyprofile.Items.Insert(0, new ListItem("No profile Found", "0"));

        }


        private void ExcelReport()
        {
            _logger.Info("======Generating Application Dependency Report EXCEL View ======");
            _logger.Info(Environment.NewLine);
            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "Application Dependency Details";

            _cells["A1"].ColumnWidth = 7;

            _cells["E3"].Formula = "Application Dependency Report";
            _cells["E3"].Font.Color = Color.White;
            _cells["E3"].Font.Size = 15;
            _cells["E3"].HorizontalAlignment = HAlign.Center;
            _cells["E3"].Font.Bold = true;
            _cells["B3:H6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:H3"].Font.Size = 11;
            _cells["B5:H8"].Font.Size = 10;
            _cells["B3:H8"].Font.Color = Color.White;
            _cells["B3:H8"].Font.Name = "Cambria";

            var dateTime = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")); //DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
            _cells["B5"].Formula = "Report Generation Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Right;

            _cells["C5"].Formula = ":  " + dateTime;
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;



            //var stdt = Convert.ToDateTime(txtstart.Text);
            //var startdt = stdt.ToString("dd-MMM-yy");
            //var endt = Convert.ToDateTime(txtend.Text);
            //var enddt = endt.ToString("dd-MMM-yy");

            _cells["B7"].Formula = "Host Details";
            _cells["B7"].Font.Bold = true;
            _cells["B7"].HorizontalAlignment = HAlign.Left;
            _cells["B7"].Font.Color = Color.Black;

            _cells["B10"].Formula = "Dependent Details";
            _cells["B10"].Font.Bold = true;
            _cells["B10"].HorizontalAlignment = HAlign.Left;
            _cells["B10"].Font.Color = Color.Black;

            IList<AppDepMappingHosts> appdependancy = new List<AppDepMappingHosts>();


            appdependancy = Facade.GetAllApplicationDependencyMappingHostsByDependencyProfileandbysourcehost(ddldependencyprofile.SelectedItem.Text, ddlsourcehost.SelectedItem.Text);

            if (appdependancy != null && appdependancy.Count > 0)
            {
                lblMsg.Text = "";
                //if (ddlusename.SelectedItem.Text == "All")
                //{
                //    lstUser = Facade.GetUserActivityByStartEndDate(startdt, enddt);
                //}
                //else
                //{
                //    string loginname = ddlusename.SelectedItem.ToString();
                //    lstUser = Facade.GetUserActivityByDate(loginname, startdt, enddt);
                //}

                //var getdetail = _facade.GetUserActivityByStartEndDate(startdt, enddt);

               // reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 100, 16, 150, 20);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 690, 10, 120, 13);
                string strlogo = LoggedInUserCompany.CompanyLogoPath;

                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 400, 10, 121, 13);
                }

                reportWorksheet.Cells["A1:F1"].RowHeight = 27;
                reportWorksheet.Cells["A2:F2"].RowHeight = 25;





                _cells["H5"].Formula = "Dependency Profile Name : "+ddldependencyprofile.SelectedItem.Text.ToString();;
                _cells["H5"].Font.Bold = true;
                _cells["H5"].HorizontalAlignment = HAlign.Right;
                _cells["H5"].VerticalAlignment = VAlign.Center;
                _cells["H5"].ColumnWidth = 40;

                //_cells["F5"].Formula = ddldependencyprofile.SelectedItem.Text.ToString();
                //_cells["F5"].Font.Bold = true;
                //_cells["F5"].HorizontalAlignment = HAlign.Left;
                //_cells["F5"].VerticalAlignment = VAlign.Center;
                //_cells["F5"].ColumnWidth = 70;
                //_cells["F5"].WrapText = true;


                //host start

                int row = 8;

                _cells["B" + row.ToString()].Formula = "Sr.No.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:H8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range3 = reportWorksheet.Cells["B8:H8"];
                IBorder border1 = range3.Borders[BordersIndex.EdgeBottom];
                border1.LineStyle = LineStyle.Continous;
                border1.Color = Color.Black;
                border1.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Host IP";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

                var productionlog1 = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog1.WrapText = true;

                _cells["D" + row.ToString()].Formula = "Host Operating System";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["E" + row.ToString()].Formula = "Host Entity";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["F" + row.ToString()].Formula = " Services Runnig On Host";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;


                _cells["B9"].Formula = "1";
                _cells["B9"].Font.Bold = true;
                _cells["B9"].HorizontalAlignment = HAlign.Left;
                _cells["B9"].VerticalAlignment = VAlign.Center;

                _cells["C9"].Formula = ddlsourcehost.SelectedItem.Text;
                _cells["C9"].Font.Bold = true;
                _cells["C9"].HorizontalAlignment = HAlign.Left;
                _cells["C9"].VerticalAlignment = VAlign.Center;


                _cells["D9"].Formula = appdependancy[0].HostOS;
                _cells["D9"].Font.Bold = true;
                _cells["D9"].HorizontalAlignment = HAlign.Left;
                _cells["D9"].VerticalAlignment = VAlign.Center;
                _cells["D9"].WrapText = true;

                _cells["E9"].Formula = appdependancy[0].HostEntity;
                _cells["E9"].Font.Bold = true;
                _cells["E9"].HorizontalAlignment = HAlign.Left;
                _cells["E9"].VerticalAlignment = VAlign.Center;

                _cells["F9"].Formula = appdependancy[0].HostAllApps.Replace(",unknown", "");
                _cells["F9"].Font.Bold = true;
                _cells["F9"].HorizontalAlignment = HAlign.Left;
                _cells["F9"].VerticalAlignment = VAlign.Center;
                _cells["F9"].WrapText = true;
                _cells["F9"].ColumnWidth = 50;





                //end host

                row = row + 3;
                //int row = 8;
                int i = 1;

                _cells["B" + row.ToString()].Formula = "Sr.No.";
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B11:H11"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["B11:H11"].Font.Color = Color.White;

                IRange range = reportWorksheet.Cells["B11:H11"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.Black;
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Dependent IP";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "Operating System";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["E" + row.ToString()].Formula = "Entity";
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["F" + row.ToString()].Formula = "Services Runnig Locally";
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

                //_cells["G" + row.ToString()].Formula = "TimeStamp";
                //_cells["G" + row.ToString()].Font.Bold = true;
                //_cells["G" + row.ToString()].HorizontalAlignment = HAlign.Center;

                //_cells["H" + row.ToString()].Formula = "Discovery ProfileName";
                //_cells["H" + row.ToString()].Font.Bold = true;
                //_cells["H" + row.ToString()].HorizontalAlignment = HAlign.Center;


                row++;
                int dataCount = 0;
                int xlRow = 12;
                _logger.Info("======" + appdependancy.Count + " Records Retrieve for Application Dependency Report ======");
                _logger.Info(Environment.NewLine);
                foreach (var rp in appdependancy)
                {
                    dataCount++;
                    int column = 0;
                    string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H" };
                    xlRow++;

                    string ndx = xlColumn[column] + row.ToString();
                    _cells[ndx + ":" + "H" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;



                    _cells[ndx].Formula = i.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    i++;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.Targethost != null ? rp.Targethost : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.OperatingSystem != null ? rp.OperatingSystem : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;

                    //string stripaddress = rp.Entity != null ? rp.Entity : "NA";

                    //if (stripaddress != null && stripaddress != "NA")
                    //{
                    //    stripaddress = CryptographyHelper.Md5Decrypt(stripaddress);
                    //}

                    //

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.Entity != null ? rp.Entity : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;

                    //string strstatus = rp.Status != null ? rp.Status : "NA";

                    //if (strstatus != null && strstatus != "NA")
                    //{
                    //    strstatus = strstatus == "1" ? "Running" : "Stopped";
                    //}

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = rp.AllApps != null ? rp.AllApps.Replace(",unknown", "") : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 50;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].VerticalAlignment = VAlign.Center;
                    _cells[ndx].WrapText = true;
                    column++;

                    //ndx = xlColumn[column] + row.ToString();
                    //_cells[ndx].Formula = rp.CreateDate != null ? rp.CreateDate.ToString("dd-MMM-yyyy HH:mm:ss") : "NA";
                    //_cells[ndx].Font.Size = 10;
                    //_cells[ndx].ColumnWidth = 23;
                    //_cells[ndx].Font.Color = Color.Black;
                    //_cells[ndx].HorizontalAlignment = HAlign.Center;
                    //_cells[ndx].VerticalAlignment = VAlign.Center;
                    //_cells[ndx].WrapText = true;
                    //column++;


                    //ndx = xlColumn[column] + row.ToString();
                    //_cells[ndx].Formula = rp.DiscoveryProfileName != null ? rp.DiscoveryProfileName.ToShortDateString() : "NA";
                    //_cells[ndx].Font.Size = 10;
                    //_cells[ndx].ColumnWidth = 23;
                    //_cells[ndx].Font.Color = Color.Black;
                    //_cells[ndx].HorizontalAlignment = HAlign.Center;
                    //column++;

                    row++;
                }

                int finalCount = row + 5;
                _cells["B" + finalCount].Formula = "NA : Not Available";
                _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
                _cells["B" + finalCount].Font.Name = "Cambria";
                _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

                reportWorksheet.ProtectContents = true;
                OpenExcelFile(reportWorkbook);
                _logger.Info("====== Application Dependency EXCEL Report generated ======");
                _logger.Info(Environment.NewLine);
            }
            else
            {
                _logger.Info("====== Application Dependency EXCEL Report not generated ======");
                _logger.Info(Environment.NewLine);
                lblMsg.Visible = true;
                lblMsg.Text = "No Records Found";
            }

        }


        private void OpenExcelFile(IWorkbook workbook)
        {
            //txtReportMessage.Text = string.Empty;
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=report.xls");
            string _str = DateTime.Now.ToString("ddMMyyy");
            _str = "ApplicationDependencyReport" + _str + ".xls";

            foreach (IWorksheet ws in workbook.Worksheets)
                ws.ProtectContents = true;

            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + _str), FileFormat.Excel8);
            //string myUrl = "/ExcelFiles/" + _str;
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + _str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Application Dependency Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }


        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void BtnPdfSaveClick(object sender, EventArgs e)
        {
            try
            {
                ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }


        protected void ddldependencyprofile_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {



                IList<AppDependencyLinks> hostlist = new List<AppDependencyLinks>();

                hostlist = Facade.GetAllApplicationDependencyMappingLinksByDependencyProfile(ddldependencyprofile.SelectedItem.Text);

                var hlist = (from p in hostlist select p.SourceHost).Distinct().ToList();

                //var hlist=((from hl in hostlist select hl).Distinct().ToList();
                //    hostlist = hostlist.Distinct().ToList();

                if (hlist != null)
                {
                    ddlsourcehost.DataSource = hlist;
                    //ddlsourcehost.DataTextField = "SourceHost";
                    //ddlsourcehost.DataValueField = "SourceHost";

                    //ddldiscoveryprofile.DataValueField = profilelist;
                    ddlsourcehost.DataBind();
                    ddlsourcehost.Items.Insert(0, new ListItem("Select Source Host", "0"));
                }
                else
                {
                    ddlsourcehost.Items.Insert(0, new ListItem("No profile Found", "0"));
                }
            }
            catch (Exception)
            {


            }

        }
    }
}