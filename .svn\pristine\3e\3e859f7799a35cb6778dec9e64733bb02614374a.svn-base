﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class DatabaseOracleBuilder : IEntityBuilder<DatabaseOracle>
    {
        IList<DatabaseOracle> IEntityBuilder<DatabaseOracle>.BuildEntities(IDataReader reader)
        {
            var databaseOracles = new List<DatabaseOracle>();

            while (reader.Read())
            {
                databaseOracles.Add(((IEntityBuilder<DatabaseOracle>)this).BuildEntity(reader, new DatabaseOracle()));
            }

            return (databaseOracles.Count > 0) ? databaseOracles : null;
        }

        DatabaseOracle IEntityBuilder<DatabaseOracle>.BuildEntity(IDataReader reader, DatabaseOracle databaseOracle)
        {
            //const int FLD_ID = 0;
            //const int FLD_BASEDATABASEID = 1;
            //const int FLD_ORACLESID = 2;
            ////const int FLD_SERVERID = 3;
            //const int FLD_USERNAME = 3;
            //const int FLD_PASSWORD = 4;
            //const int FLD_PORT = 5;

            //databaseOracle.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //databaseOracle.BaseDatabaseId = reader.IsDBNull(FLD_BASEDATABASEID) ? 0 : reader.GetInt32(FLD_BASEDATABASEID);
            //databaseOracle.OracleSID = reader.IsDBNull(FLD_ORACLESID) ? string.Empty : reader.GetString(FLD_ORACLESID);
            ////databaseOracle.ServerId = reader.IsDBNull(FLD_SERVERID) ? 0 : reader.GetInt32(FLD_SERVERID);
            //databaseOracle.UserName = reader.IsDBNull(FLD_USERNAME) ? string.Empty : reader.GetString(FLD_USERNAME);
            //databaseOracle.Password = reader.IsDBNull(FLD_PASSWORD) ? string.Empty : reader.GetString(FLD_PASSWORD);
            //databaseOracle.Port = reader.IsDBNull(FLD_PORT) ? 0 : reader.GetInt32(FLD_PORT);

            //Fields in bcms_database_oracle table on 16/07/2013 :Id, BaseDatabaseId, OracleSID, UserName, Password, Port

            databaseOracle.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            databaseOracle.BaseDatabaseId = Convert.IsDBNull(reader["BaseDatabaseId"])
                ? 0
                : Convert.ToInt32(reader["BaseDatabaseId"]);
            databaseOracle.OracleSID = Convert.IsDBNull(reader["OracleSID"])
                ? string.Empty
                : Convert.ToString(reader["OracleSID"]);
            databaseOracle.UserName = Convert.IsDBNull(reader["UserName"])
                ? string.Empty
                : Convert.ToString(reader["UserName"]);
            databaseOracle.Password = Convert.IsDBNull(reader["Password"])
                ? string.Empty
                : Convert.ToString(reader["Password"]);
            databaseOracle.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
            databaseOracle.Archive = Convert.IsDBNull(reader["Archive"])
                ? string.Empty
                : Convert.ToString(reader["Archive"]);
            databaseOracle.Home = Convert.IsDBNull(reader["Home"]) ? string.Empty : Convert.ToString(reader["Home"]);
            databaseOracle.Redo = Convert.IsDBNull(reader["Redo"]) ? string.Empty : Convert.ToString(reader["Redo"]);
            databaseOracle.ASMGrid = Convert.IsDBNull(reader["ASMGrid"])
                ? string.Empty
                : Convert.ToString(reader["ASMGrid"]);
            databaseOracle.InstanceName = Convert.IsDBNull(reader["InstanceName"])
                ? string.Empty
                : Convert.ToString(reader["InstanceName"]);
            databaseOracle.IsAsm = Convert.IsDBNull(reader["IsASM"]) ? 0 : Convert.ToInt32(reader["IsASM"]);
            databaseOracle.AsmInstanceName = Convert.IsDBNull(reader["ASMInstanceName"]) ? string.Empty : Convert.ToString(reader["ASMInstanceName"]);
            databaseOracle.AsmUserName = Convert.IsDBNull(reader["ASMUserName"]) ? string.Empty : Convert.ToString(reader["ASMUserName"]);
            databaseOracle.AsmPassword = Convert.IsDBNull(reader["ASMPassword"]) ? string.Empty : Convert.ToString(reader["ASMPassword"]);
            databaseOracle.AsmPath = Convert.IsDBNull(reader["ASMPath"]) ? string.Empty : Convert.ToString(reader["ASMPath"]);
            databaseOracle.PreExecutionCommand = Convert.IsDBNull(reader["PreExecutionCommand"]) ? string.Empty : Convert.ToString(reader["PreExecutionCommand"]);
            return databaseOracle;
        }
    }
}