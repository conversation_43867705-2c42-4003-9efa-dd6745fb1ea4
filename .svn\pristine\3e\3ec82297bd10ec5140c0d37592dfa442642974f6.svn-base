﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Controls;
using CP.ExceptionHandler;
using System.Web.Security;
using System.Web;


namespace CP.UI
{
    /// <summary>
    /// This class is used to configure application
    /// </summary>
    public partial class BusinessFunctionConfig : ApplicatinBasePage
    {
        #region Members

        #region Private Members

        private static int _currentLoggedUserId;
        private static int _companyId;
        private static bool _isParent;
        private static IList<RPOTimeSpan> rpolst;

        #endregion Private Members

        #endregion Members

        #region Properties

        #region Public properties

        /// <summary>
        /// This property gets ReturnUrl
        /// </summary>
        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.BusinessFunction.BusinessFunctionList;
                }
                return string.Empty;
            }
        }

        /// <summary>
        /// This property gets initial name for application
        /// </summary>
        public string MessageInitials
        {
            get { return "Business Function"; }
        }

        #endregion Public properties

        #endregion Properties



        #region Methods

        #region Private Methods

        /// <summary>
        /// Checks whether application name is already exist or not
        /// </summary>
        /// <returns>true-if exist otherwise false</returns>
        private bool CheckAppNameExist()
        {
            var res = Facade.IsExistBusinessFunctionByName(txtApplication.Text);
            if (btnSave.Text == "Update")
            {
                if (CurrentApp.Name == txtApplication.Text)
                    return false;
                else
                    return res;
            }
            else
                return res;
        }

        /// <summary>
        /// Binds CurrentApplication values to controls.
        /// </summary>
        private void BindControlValues()
        {
            ddlBusinessService.SelectedValue = CurrentApp.BusinessServiceId.ToString();
            txtApplication.Text = CurrentApp.Name;
            txtdetails.Text = CurrentApp.Description;
            ddlCriticality.SelectItemByText(CurrentApp.CriticalityLevel.ToString());
            txtDataLag.Text = Convert.ToString(CurrentApp.ConfiguredRPO);
            txtDataRecovery.Text = Convert.ToString(CurrentApp.ConfiguredRTO);
            txtMtpod.Text = Convert.ToString(CurrentApp.ConfiguredMAO);
            chkIsStatic.Checked = CurrentApp.IsStatic == 1 ? true : false;
            txtDataLagInByte.Text = Convert.ToString(CurrentApp.DataLagInByte);
            if (CurrentApp.IsStatic == 1)
            {
                btnImgTimeRange.Enabled = false;
                txtDataLag.Enabled = true;
            }
            else
            {
                btnImgTimeRange.Enabled = true;
                txtDataLag.Enabled = false;
                txtDataLag.Text = "";
            }

            if (CurrentApp.DataLagInByte > 0)
                    dvDataLagInByte.Visible = true;
            else
                    dvDataLagInByte.Visible = false;
                
            _currentLoggedUserId = CurrentApp.CreatorId;
        }

        #endregion Private Methods



        #region Protected Methods

        /// <summary>
        /// Checks whether application name is already exist or not
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        protected void TxtNameTextChanged(object sender, EventArgs e)
        {
            if (txtApplication.Text != string.Empty)
            {
                lblName1.Text = CheckAppNameExist() ? "Business Function Already Exist" : "";
            }
        }

        #endregion Protected Methods

        #region Public Methods

        /// <summary>
        /// Builds Application entity
        /// </summary>
        /// <param name="applications"></param>
        public void BuildEntity(BusinessFunction applications)
        {
            applications.BusinessServiceId = Convert.ToInt32(ddlBusinessService.SelectedValue);
            applications.Name = txtApplication.Text;
            applications.Description = txtdetails.Text;
            applications.CriticalityLevel = ddlCriticality.SelectedItem.ToString();
            //applications.ConfiguredRPO = txtDataLag.Text;
            applications.ConfiguredRPO = txtDataLag.Text != Constants.UIConstants.TextBoxDataLagValue ? txtDataLag.Text : string.Empty;
            applications.DataLagInByte = txtDataLagInByte.Text != "" ? Convert.ToInt32(txtDataLagInByte.Text) : 0;
            applications.ConfiguredRTO = txtDataRecovery.Text;
            applications.ConfiguredMAO = txtMtpod.Text;
            applications.IsStatic = chkIsStatic.Checked ? 1 : 0;
            applications.CreatorId = LoggedInUserId;
        }

        /// <summary>
        /// Prepares initial view of page.
        /// </summary>
        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

            rpospan.Visible = false;
            RequiredFieldValidator7.Enabled = chkIsStatic.Checked ? true : false;
            RegularExpressionValidator1.Enabled = chkIsStatic.Checked ? true : false;
            txtDataLag.Enabled = false;
            btnImgTimeRange.Enabled = true;
            dvDataLagInByte.Visible = false;

            if (CurrentApplicationId == 0)
                dvDataLagInByte.Visible = false;
            else
                dvDataLagInByte.Visible = true;

            Utility.SelectMenu(Master, "Module2");
            rpolst = new List<RPOTimeSpan>();
            // NEW CODE START :  This section of code replace design time ddlCriticality hard coded values to values from Enum 'BusinessFunctionCriticalityLevel'

            EnumHelper.PopulateAllEnumDescriptionIntoList(ddlCriticality, typeof(BusinessFunctionCriticalityLevel), "-Select Criticality Level-");

            // NEW CODE END

            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 


            if (!IsPostBack)
            {
                _currentLoggedUserId = LoggedInUserId;
                if (CurrentApplicationId != 0)
                {
                    Populatedropdown();
                    PrepareEditView();
                    rpolst = Facade.GetBusinessFunctionRPOById(CurrentApplicationId);
                    lvtimerange.DataSource = rpolst;
                    lvtimerange.DataBind();

                    if (lvtimerange.Items.Count > 0)
                    {
                        txtDataLag.Text = Constants.UIConstants.TextBoxDataLagValue;
                    }
                }
                else
                {
                    Populatedropdown();
                    lvtimerange.DataSource = rpolst;
                    lvtimerange.DataBind();
                }
            }
        }

        /// <summary>
        /// Populates dropdowns
        /// </summary>
        public void Populatedropdown()
        {
            _currentLoggedUserId = LoggedInUserId;
            _companyId = LoggedInUserCompanyId;
            _isParent = LoggedInUserCompany.IsParent;

            var appGroupList = Facade.GetBusinessServiceByCompanyIdAndRole(_currentLoggedUserId, _companyId, LoggedInUserRole, _isParent, LoggedInUser.InfraObjectAllFlag);
            ddlBusinessService.DataSource = appGroupList;
            ddlBusinessService.DataTextField = "Name";
            ddlBusinessService.DataValueField = "Id";
            ddlBusinessService.DataBind();
            DropDownListExtension.AddDefaultItem(ddlBusinessService, "-Select Business service-");

            //// NEW CODE START :  This section of code replace design time ddlCriticality hard coded values to values from Enum 'BusinessFunctionCriticalityLevel'

            //EnumHelper.PopulateAllEnumDescriptionIntoList(ddlCriticality, typeof(BusinessFunctionCriticalityLevel), "-Select Criticality Level-");

            //// NEW CODE END
        }

        /// <summary>
        /// Prepares edit views
        /// </summary>
        public void PrepareEditView()
        {
            if (CurrentApplicationId > 0)
            {
                BindControlValues();
                btnSave.Text = "Update";
                rpospan.Visible = false;
            }
        }

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                IgnoreIDs.Add("TextBox2");
                IgnoreIDs.Add("TextBox4");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        #endregion Public Methods

        #endregion Methods

        #region Events

        /// <summary>
        /// Save application details into database table "bcms_application".
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// 

        protected void BtnSaveClick(object sender, EventArgs e)
        {
            if (!CheckAppNameExist() && (ViewState["_token"] != null) && Page.IsValid && ValidateRequest("BusinessFunction", UserActionType.CreateBusinessFunction))
            {
                if (chkIsStatic.Checked == false)
                {
                    var result = Validate();
                    if (result == false)
                        return;
                }

                if (lblName1.Text != "")
                    return;


                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }

                else
                {
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }
                    TransactionType currentTransactionType;
                    if (CurrentApplicationId == 0)
                    {
                        currentTransactionType = TransactionType.Save;
                        var applications = new BusinessFunction();
                        BuildEntity(applications);
                        applications.CreatorId = _currentLoggedUserId;
                        var app = Facade.AddBusinessFunction(applications);
                        if (app.IsStatic == 0)
                        {
                            if (rpolst != null)
                            {
                                foreach (var rpoobj in rpolst)
                                {
                                    rpoobj.BusinessFunctionId = app.Id;
                                    var rpo = Facade.AddBusinessFunctionRPO(rpoobj);
                                }
                            }
                        }

                    else
                       // ActivityLogger.AddLog(LoggedInUserName, "BusinessFunction", UserActionType.CreateBusinessFunction, "The Business Function '" + app.Name + "'  was Added to the Business Function table", LoggedInUserId);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "BusinessFunction", UserActionType.CreateBusinessFunction, "The Business Function '" + app.Name + "'  was Added to the Business Function table", LoggedInUserId);
                       
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials + " " + '"' + applications.Name + '"',
                                                                                                     currentTransactionType));
                    }
                    else
                    {
                        currentTransactionType = TransactionType.Update;
                        var applications = new BusinessFunction();
                        BuildEntity(applications);
                        applications.CreatorId = _currentLoggedUserId;
                        applications.Id = CurrentApplicationId;
                        var app = Facade.UpdateBusinessFunction(applications);
                        var res = Facade.DeleteBusinessFunctionRPOById(app.Id);
                        if (app.IsStatic == 0)
                        {
                            if (rpolst != null)
                            {
                                foreach (var rpoobj in rpolst)
                                {
                                    rpoobj.BusinessFunctionId = app.Id;
                                    var rpo = Facade.AddBusinessFunctionRPO(rpoobj);
                                }
                            }
                        }

                        ////Update Service RPO and RTO
                        int serviceId = Convert.ToInt32(ddlBusinessService.SelectedValue);
                        if (serviceId > 0)
                        {
                            int serviceConfgiRPO = 0;
                            int serviceConfgiRTO = 0;
                            IList<BusinessFunction> lstFunction = Facade.GetBusinessFunctionsByBusinessServiceId(serviceId);
                            if (lstFunction != null)
                            {
                                var ItemRPO = lstFunction.FirstOrDefault(x => x.ConfiguredRPO == lstFunction.Max(y => y.ConfiguredRPO));
                                if (ItemRPO != null)
                                    serviceConfgiRPO = Convert.ToInt32(ItemRPO.ConfiguredRPO) / 60;
                                var ItemRTO = lstFunction.FirstOrDefault(x => x.ConfiguredRTO == lstFunction.Max(y => y.ConfiguredRTO));
                                if (ItemRTO != null)
                                    serviceConfgiRTO = Convert.ToInt32(ItemRTO.ConfiguredRTO) / 60;
                                Facade.UpdateServiceRPORTO(serviceId, serviceConfgiRPO, serviceConfgiRTO);
                            }

                        }
                       // ActivityLogger.AddLog(LoggedInUserName, "Applications", UserActionType.UpdateBusinessFunction, "The BusinessFunction '" + app.Name + "'  was updated to th Business Function table", LoggedInUserId);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Applications", UserActionType.UpdateBusinessFunction, "The BusinessFunction '" + app.Name + "'  was updated to th Business Function table", LoggedInUserId);
                      
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials + " " + '"' + applications.Name + '"',
                                                                                                     currentTransactionType));
                    }

                    if (returnUrl.IsNotNullOrEmpty())
                    {

                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.BusinessfunctionId, CurrentApp.Id);

                        Helper.Url.Redirect(returnUrl);
                    }

                    //if (returnUrl.IsNotNullOrEmpty())
                    //{
                    //    var secureUrl = new SecureUrl(returnUrl);
                    //    if (secureUrl.QueryStrings.Count == 0)
                    //    {
                    //        secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Message, Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials, currentTransactionType));
                    //    }
                    //    else
                    //    {
                    //        secureUrl.AddParamater(Constants.UrlConstants.Params.Message, Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials, currentTransactionType));
                    //    }
                    //    Helper.Url.Redirect(secureUrl);

                    //}
                }
            }
        }

        //protected void BtnSaveClick(object sender, EventArgs e)
        //{
        //    if (!CheckAppNameExist() && ValidateRequest("BusinessFunction", UserActionType.CreateBusinessFunction))
        //    {
        //        if (chkIsStatic.Checked == false)
        //        {
        //            var result = Validate();
        //            if (result == false)
        //                return;
        //        }

        //        if (lblName1.Text != "")
        //            return;

        //        string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
        //        if (returnUrl.IsNullOrEmpty())
        //        {
        //            returnUrl = ReturnUrl;
        //        }
        //        TransactionType currentTransactionType;

        //        if (CurrentApplicationId == 0)
        //        {
        //            currentTransactionType = TransactionType.Save;
        //            try
        //            {
        //                var applications = new BusinessFunction();
        //                BuildEntity(applications);
        //                applications.CreatorId = _currentLoggedUserId;
        //                var app = Facade.AddBusinessFunction(applications);
        //                if (app.IsStatic == 0)
        //                {
        //                    if (rpolst != null)
        //                    {
        //                        foreach (var rpoobj in rpolst)
        //                        {
        //                            rpoobj.BusinessFunctionId = app.Id;
        //                            var rpo = Facade.AddBusinessFunctionRPO(rpoobj);
        //                        }
        //                    }
        //                }
        //                ActivityLogger.AddLog(LoggedInUserName, "BusinessFunction", UserActionType.CreateBusinessFunction, "The Business Function '" + app.Name + "'  was Added to the Business Function table", LoggedInUserId);
        //                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials + " " + '"' + applications.Name + '"',
        //                                                                                             currentTransactionType));
        //            }
        //            catch (CpException ex)
        //            {
        //                InvalidateTransaction();

        //                returnUrl = Request.RawUrl;

        //                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

        //                ExceptionManager.Manage(ex, this);
        //            }
        //            catch (Exception ex)
        //            {
        //                InvalidateTransaction();

        //                returnUrl = Request.RawUrl;

        //                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

        //                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
        //                {
        //                    ExceptionManager.Manage((CpException)ex.InnerException, this);
        //                }
        //                else
        //                {
        //                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

        //                    ExceptionManager.Manage(customEx, this);
        //                }
        //            }
        //        }
        //        else
        //        {
        //            currentTransactionType = TransactionType.Update;

        //            try
        //            {
        //                var applications = new BusinessFunction();
        //                BuildEntity(applications);
        //                applications.CreatorId = _currentLoggedUserId;
        //                applications.Id = CurrentApplicationId;
        //                var app = Facade.UpdateBusinessFunction(applications);
        //                var res = Facade.DeleteBusinessFunctionRPOById(app.Id);
        //                if (app.IsStatic == 0)
        //                {
        //                    if (rpolst != null)
        //                    {
        //                        foreach (var rpoobj in rpolst)
        //                        {
        //                            rpoobj.BusinessFunctionId = app.Id;
        //                            var rpo = Facade.AddBusinessFunctionRPO(rpoobj);
        //                        }
        //                    }
        //                }
        //                ActivityLogger.AddLog(LoggedInUserName, "Applications", UserActionType.UpdateBusinessFunction, "The BusinessFunction '" + app.Name + "'  was updated to th Business Function table", LoggedInUserId);
        //                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials + " " + '"' + applications.Name + '"',
        //                                                                                             currentTransactionType));
        //            }
        //            catch (CpException ex)
        //            {
        //                InvalidateTransaction();

        //                returnUrl = Request.RawUrl;

        //                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

        //                ExceptionManager.Manage(ex, this);
        //            }
        //            catch (Exception ex)
        //            {
        //                InvalidateTransaction();

        //                returnUrl = Request.RawUrl;

        //                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

        //                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
        //                {
        //                    ExceptionManager.Manage((CpException)ex.InnerException, this);
        //                }
        //                else
        //                {
        //                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while Updating data", ex);

        //                    ExceptionManager.Manage(customEx, this);
        //                }
        //            }
        //        }
        //        if (returnUrl.IsNotNullOrEmpty())
        //        {
        //            var secureUrl = new SecureUrl(returnUrl);
        //            if (secureUrl.QueryStrings.Count == 0)
        //            {
        //                secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Message, Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials, currentTransactionType));
        //            }
        //            else
        //            {
        //                secureUrl.AddParamater(Constants.UrlConstants.Params.Message, Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(MessageInitials, currentTransactionType));
        //            }
        //            Helper.Url.Redirect(secureUrl);
        //        }
        //    }
        //}

        /// <summary>
        /// Redirect to application list page
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.BusinessFunction.BusinessFunctionList + "?Listitems=" + "Cancel");
        }

        #endregion Events

        protected string CheckType(object act)
        {
            var type = Convert.ToString(act);
            if (type == "Static Days")
                return "icon-calendar-Time";
            if (type == "Weekends")
                return "icon-weekends";
            if (type == "Hours")
                return "icon-hours";
            else
                return "";
        }

        protected void chkIsStatic_CheckedChanged(object sender, EventArgs e)
        {
            txtDataLag.Enabled = chkIsStatic.Checked ? true : false;
            btnImgTimeRange.Enabled = chkIsStatic.Checked ? false : true;
            txtDataLag.Text = "";
            if (chkIsStatic.Checked == false)
            {
                rpospan.Visible = false;
            }

            RequiredFieldValidator7.Enabled = chkIsStatic.Checked ? true : false;
            RegularExpressionValidator1.Enabled = chkIsStatic.Checked ? true : false;
            //rpospan.Visible = chkIsStatic.Checked ? false : true;
            //rpospan.Visible = chkIsStatic.Checked ? true : false;
        }

        protected void lvtimerange_ItemCanceling(object sender, System.Web.UI.WebControls.ListViewCancelEventArgs e)
        {
            lvtimerange.EditIndex = -1;
            lvtimerange.DataSource = rpolst;
            lvtimerange.DataBind();
            ModalPopupExtender1.Show();
        }

        //BOC Validate Request
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    //ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                //ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request

        protected void lvtimerange_ItemDeleting(object sender, System.Web.UI.WebControls.ListViewDeleteEventArgs e)
        {
            var drplbl = (Label)(lvtimerange.Items[e.ItemIndex].FindControl("Label7"));
            string starttxt = "";
            string endtxt = "";
            string val = drplbl.Text;
            bool isStaticDays = val.Contains("Static Days");
            if (isStaticDays && val.Length == 11)
            {
                starttxt = "Label4";
                endtxt = "Label5";
            }
            else
                if (val == "Weekends")
                {
                    starttxt = "Label4";
                    endtxt = "Label5";
                }
                else
                    if (val == "Hours")
                    {
                        starttxt = "Label4";
                        endtxt = "Label5";
                    }
            string tmp = starttxt;
            tmp = endtxt;
            var res = (Label)(lvtimerange.Items[e.ItemIndex].FindControl(starttxt));

            var result = (from rpo in rpolst where rpo.StartTime == res.Text select rpo).ToList();
            var reso = result;
            foreach (var bi in result)
            {
                rpolst.Remove(bi);
            }

            lvtimerange.DataSource = rpolst;
            lvtimerange.DataBind();
            ModalPopupExtender1.Show();
        }

        protected void lvtimerange_ItemEditing(object sender, System.Web.UI.WebControls.ListViewEditEventArgs e)
        {
            lvtimerange.EditIndex = e.NewEditIndex;
            lvtimerange.DataSource = rpolst;
            lvtimerange.DataBind();
            ModalPopupExtender1.Show();
        }

        protected void lvtimerange_ItemInserting(object sender, System.Web.UI.WebControls.ListViewInsertEventArgs e)
        {
            RPOTimeSpan rpotime = new RPOTimeSpan();

            var drplst = (DropDownList)e.Item.FindControl("DropDownList1");
            string starttimetxt = "";
            string endtimetxt = "";
            string selectedval = drplst.SelectedItem.Text;
            if (selectedval == "Static Days")
            {
                starttimetxt = "TextBox2";
                endtimetxt = "TextBox4";
            }
            if (selectedval == "Weekends")
            {
                starttimetxt = "TextBox1";
                endtimetxt = "TextBox3";
            }
            if (selectedval == "Hours")
            {
                starttimetxt = "AVolume";
                endtimetxt = "BVolume";
            }
            var starttime = (TextBox)e.Item.FindControl(starttimetxt);
            var endtime = (TextBox)e.Item.FindControl(endtimetxt);
            var rpo = (TextBox)e.Item.FindControl("CVolume");

            bool res = true;
            if (selectedval == "Hours")
            {
                DateTime start = DateTime.ParseExact(starttime.Text, "HH:mm", CultureInfo.InvariantCulture);
                DateTime end = DateTime.ParseExact(endtime.Text, "HH:mm", CultureInfo.InvariantCulture);
                if (end <= start)
                {
                    res = false;
                    lblerror.Text = "* Enter Larger value";
                }
            }
            if (selectedval == "Weekends")
            {
                if (rpolst != null)
                {
                    foreach (RPOTimeSpan rpoobj in rpolst)
                    {
                        if (rpoobj.TimeSpanType == "Weekends")
                        {
                            res = false;
                            lblerror.Text = "* Weekends Already Exist";
                        }
                    }
                }
            }
            if (res)
            {
                if (drplst.SelectedValue != "000")
                {
                    lblerror.Visible = false;
                    rpotime.TimeSpanType = drplst.SelectedValue;
                    rpotime.StartTime = starttime.Text;
                    rpotime.EndTime = endtime.Text;
                    rpotime.RPO = rpo.Text;
                    if (rpolst == null)
                    {
                        rpolst = new List<RPOTimeSpan>();
                        rpolst.Add(rpotime);
                    }
                    else
                    {
                        rpolst.Add(rpotime);
                    }
                    lvtimerange.DataSource = rpolst;
                    lvtimerange.DataBind();
                }
            }
            else
            {
                lblerror.Visible = true;
            }

            ModalPopupExtender1.Show();
        }

        private bool Validate()
        {

            if (rpolst == null)
            {
                rpospan.Visible = true;
                return false;
            }
            else if (rpolst.Count == 0 && chkIsStatic.Checked == false)
            {
                rpospan.Visible = false;
                return false;
            }
            else
            {
                rpospan.Visible = true;
                return true;
            }

            ////////if (rpolst.Count == 0)
            ////////{
            ////////    rpospan.Visible = true;
            ////////    return false;
            ////////}
            ////////else
            ////////{
            ////////    rpospan.Visible = false;
            ////////    return true;
            ////////}

        }

        protected void tbnDataLgInByte_Click(object sender, System.Web.UI.ImageClickEventArgs e)
        {
            dvDataLagInByte.Visible = (dvDataLagInByte.Visible==true) ? false : true;
        }

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((ViewState["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}
    }
}