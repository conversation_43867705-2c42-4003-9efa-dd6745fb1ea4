﻿using System;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System.Web.UI;
using System.Collections.Generic;
using System.Collections;
using System.Linq;

namespace CP.UI.Controls
{
    public partial class CustomRoleAccessManager : BaseControl
    {
        #region variables

        readonly IList<ListItem> _previousSelectedItems = new List<ListItem>();
        readonly IList<ListItem> _previousApplicationSelectedItems = new List<ListItem>();
        readonly IList<ListItem> _SelectedItems = new List<ListItem>();
        IList<AccessManagerCustom> menuList = new List<AccessManagerCustom>();
        readonly IList<ListItem> _NewlySelected = new List<ListItem>();

        AccessManagerCustom accessmanagercustoms = new AccessManagerCustom();

        private DropDownList _ddlusers = new DropDownList();
        private CheckBoxList _cskgoup;
        private DropDownList _ddlroles;
        private TextBox _txtRolesubtype = new TextBox();
        private Panel _pnlworflowprofiles = new Panel();
        private int chkexceuteval = 0;
        private int chkaddval = 0;
        private int chkeditval = 0;
        private int chkdelval = 0;

        private int chkntexval = 0;

        public DropDownList ddlusers
        {
            get
            {
                _ddlusers = Parent.FindControl("ddlusers") as DropDownList;
                return _ddlusers;
            }
            set
            {
                _ddlusers = value;
            }
        }
        public Panel pnlworflowprofile
        {
            get
            {
                _pnlworflowprofiles = Parent.FindControl("pnlworflowprofiles") as Panel;
                return _pnlworflowprofiles;
            }
            set
            {
                _pnlworflowprofiles = value;
            }
        }
        public TextBox txtRolesubtype
        {
            get
            {
                _txtRolesubtype = Parent.FindControl("txtRolesubtype") as TextBox;
                return _txtRolesubtype;
            }
            set
            {
                _txtRolesubtype = value;
            }
        }

        public DropDownList ddlroles
        {
            get
            {
                _ddlroles = Parent.FindControl("ddlroles") as DropDownList;
                return _ddlroles;
            }
            set
            {
                _ddlroles = value;
            }
        }

        private AccessManagerCustom _AccessmanagerCustom = null;

        public AccessManagerCustom CurrentEntity
        {
            get { return _AccessmanagerCustom ?? (_AccessmanagerCustom = new AccessManagerCustom()); }
            set
            {
                _AccessmanagerCustom = value;
            }
        }

        public string MessageInitials
        {
            get { return "CustomRole Access Manager"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.Custom)
                {
                    return Constants.UrlConstants.Urls.User.UserList;
                }
                return string.Empty;
            }
        }

        #endregion

        #region Method

        public override void PrepareView()
        {
            if (!IsPostBack)
            {
                Utility.PopulateDashboard(chksDashboard);
                Utility.PopulateConfiguration(chkConfiguration);
                Utility.PopulateView(chkView);
                Utility.PopulateManage(chkManage);
                //Utility.PopulateAlerts(chkAlerts);
                // Utility.PopulateReports(chkReports);
                EnumHelper.PopulateEnumDescriptionIntoList(chkAlerts, typeof(SubAlerts), "");
                Utility.PopulateITOrchestration(chkITOrchestration);
                EnumHelper.PopulateEnumDescriptionIntoList(chkReports, typeof(SubReports), "");
                // Utility.PopulateProfiles(chkParallelProfile);
                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = false;
              

            }
        }

        public void BuildEntities()
        {
            if (Session["CurrentUserdata"] != null)
            {
                CurrentEntity = (AccessManagerCustom)Session["CurrentUserdata"];
            }

        }

        private void checklistselected(CheckBoxList chklist, Label lbltest)
        {
            try
            {
                if (Save.Text == "Save")
                {
                    foreach (ListItem listitem in chklist.Items)
                    {
                        if (listitem.Selected)
                        {
                            string inputstring = string.Empty;
                            inputstring = lbltest.Text;
                            accessmanagercustoms.AccessMenuType = LabelString(inputstring); ;
                            accessmanagercustoms.UserId = Convert.ToInt32(ddlusers.SelectedValue);
                            accessmanagercustoms.Role = UserRole.Custom;
                            accessmanagercustoms.AccessSubMenuType = (int)((AccessSubMenuType)Enum.Parse(typeof(AccessSubMenuType), CheckString(listitem.ToString())));
                            accessmanagercustoms.ProfileAttach = "";
                            Facade.AddAccessmanager(accessmanagercustoms);
                        }
                    }
                }
                else
                {
                    foreach (ListItem listitem in chklist.Items)
                    {
                        if (listitem.Selected)
                        {
                            string inputstring = string.Empty;
                            inputstring = lbltest.Text;
                            accessmanagercustoms.AccessMenuType = LabelString(inputstring);
                            accessmanagercustoms.UserId = Convert.ToInt32(ddlusers.SelectedValue);
                            accessmanagercustoms.Role = UserRole.Custom;
                            accessmanagercustoms.AccessSubMenuType = (int)((AccessSubMenuType)Enum.Parse(typeof(AccessSubMenuType), CheckString(listitem.ToString())));
                            accessmanagercustoms.ProfileAttach = "";
                            Facade.AddAccessmanager(accessmanagercustoms);
                        }
                    }
                }
            }
            catch (CpException ex)
            {
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                ExceptionManager.Manage(ex, Page);
            }
        }
        private void checklistselected(CheckBoxList chklist, Label lbltest, string WFProfile)
        {
            try
            {
                if (Save.Text == "Save")
                {
                    int i = 0;
                    foreach (ListItem listitem in chklist.Items)
                    {
                        if (listitem.Selected)
                        {
                            string inputstring = string.Empty;
                            inputstring = lbltest.Text;
                            accessmanagercustoms.AccessMenuType = LabelString(inputstring); ;
                            accessmanagercustoms.UserId = Convert.ToInt32(ddlusers.SelectedValue);
                            accessmanagercustoms.Role = UserRole.Custom;

                            accessmanagercustoms.AccessSubMenuType = (int)((AccessSubMenuType)Enum.Parse(typeof(AccessSubMenuType), CheckString(listitem.ToString())));
                            accessmanagercustoms.ProfileAttach = accessmanagercustoms.AccessSubMenuType == 6 ? WFProfile : string.Empty;




                            if (accessmanagercustoms.AccessSubMenuType != 7)
                            {
                                if (accessmanagercustoms.AccessSubMenuType == 3)
                                {
                                    chkaddval = 1;
                                }
                                if (accessmanagercustoms.AccessSubMenuType == 4)
                                {
                                    chkeditval = 1;
                                }
                                if (accessmanagercustoms.AccessSubMenuType == 5)
                                {
                                    chkdelval = 1;
                                }

                                if (accessmanagercustoms.AccessSubMenuType == 6)
                                {
                                    chkntexval = 1;
                                }


                                Facade.AddAccessmanager(accessmanagercustoms);
                            }

                            if (accessmanagercustoms.AccessSubMenuType == 7 && !string.IsNullOrEmpty(WFProfile) && chkntexval == 1)
                            {
                                accessmanagercustoms.ProfileAttach = WFProfile;
                                Facade.AddAccessmanager(accessmanagercustoms);

                            }

                            if (accessmanagercustoms.AccessSubMenuType == 7 && !string.IsNullOrEmpty(WFProfile) && chkntexval == 0)
                            {
                                accessmanagercustoms.ProfileAttach = "";
                                Facade.AddAccessmanager(accessmanagercustoms);

                            }

                            if (accessmanagercustoms.AccessSubMenuType == 7 && string.IsNullOrEmpty(WFProfile) && chkaddval == 1)
                            {
                                Facade.AddAccessmanager(accessmanagercustoms);
                            }
                            if (accessmanagercustoms.AccessSubMenuType == 7 && string.IsNullOrEmpty(WFProfile) && chkeditval == 1)
                            {
                                Facade.AddAccessmanager(accessmanagercustoms);
                            }
                            if (accessmanagercustoms.AccessSubMenuType == 7 && string.IsNullOrEmpty(WFProfile) && chkdelval == 1)
                            {
                                Facade.AddAccessmanager(accessmanagercustoms);
                            }
                        }
                    }
                }
                else
                {
                    foreach (ListItem listitem in chklist.Items)
                    {
                        if (listitem.Selected)
                        {
                            string inputstring = string.Empty;
                            inputstring = lbltest.Text;
                            accessmanagercustoms.AccessMenuType = LabelString(inputstring);
                            accessmanagercustoms.UserId = Convert.ToInt32(ddlusers.SelectedValue);
                            accessmanagercustoms.Role = UserRole.Custom;

                            accessmanagercustoms.AccessSubMenuType = (int)((AccessSubMenuType)Enum.Parse(typeof(AccessSubMenuType), CheckString(listitem.ToString())));

                            accessmanagercustoms.ProfileAttach = accessmanagercustoms.AccessSubMenuType == 6 ? WFProfile : string.Empty;

                            //Facade.AddAccessmanager(accessmanagercustoms);

                            if (accessmanagercustoms.AccessSubMenuType != 7)
                            {

                                if (accessmanagercustoms.AccessSubMenuType == 3)
                                {
                                    chkaddval = 1;
                                }
                                if (accessmanagercustoms.AccessSubMenuType == 4)
                                {
                                    chkeditval = 1;
                                }
                                if (accessmanagercustoms.AccessSubMenuType == 5)
                                {
                                    chkdelval = 1;
                                }
                                if (accessmanagercustoms.AccessSubMenuType == 6)
                                {
                                    chkntexval = 1;
                                }

                                Facade.AddAccessmanager(accessmanagercustoms);
                            }
                            if (accessmanagercustoms.AccessSubMenuType == 7 && !string.IsNullOrEmpty(WFProfile) && chkntexval == 1)
                            {
                                accessmanagercustoms.ProfileAttach = WFProfile;
                                Facade.AddAccessmanager(accessmanagercustoms);

                            }


                            if (accessmanagercustoms.AccessSubMenuType == 7 && !string.IsNullOrEmpty(WFProfile) && chkntexval == 0)
                            {
                                accessmanagercustoms.ProfileAttach = "";
                                Facade.AddAccessmanager(accessmanagercustoms);

                            }
                            if (accessmanagercustoms.AccessSubMenuType == 7 && string.IsNullOrEmpty(WFProfile) && chkaddval == 1)
                            {
                                Facade.AddAccessmanager(accessmanagercustoms);
                            }
                            if (accessmanagercustoms.AccessSubMenuType == 7 && string.IsNullOrEmpty(WFProfile) && chkeditval == 1)
                            {
                                Facade.AddAccessmanager(accessmanagercustoms);
                            }
                            if (accessmanagercustoms.AccessSubMenuType == 7 && string.IsNullOrEmpty(WFProfile) && chkdelval == 1)
                            {
                                Facade.AddAccessmanager(accessmanagercustoms);
                            }
                        }
                    }
                }
                chkexceuteval = 0;
                chkaddval = 0;
                chkeditval = 0;
                chkdelval = 0;
            }
            catch (CpException ex)
            {
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                ExceptionManager.Manage(ex, Page);
            }
        }
        private string checklistselectedProfile(ListBox chklist, Label lbltest)
        {
            string _profileid = string.Empty;
            try
            {

                foreach (ListItem listitem in chklist.Items)
                {
                    if (listitem.Selected && listitem.ToString() != "ALL")
                    {
                        _profileid = _profileid + "," + listitem.Value.ToString();
                    }
                }

            }
            catch (CpException ex)
            {
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                ExceptionManager.Manage(ex, Page);
            }
            return _profileid.TrimStart(',');
        }
        
        public void SaveEditor()
        {
            if (Session["CurrentUserdata"] == null)
            {
                if (Save.Text == "Save")
                {
                    accessmanagercustoms.users.Id = Convert.ToInt32(ddlusers.SelectedValue);
                    var userdetails = Facade.GetUserById(Convert.ToInt32(ddlusers.SelectedValue));
                    if (userdetails != null)
                    {
                        if (userdetails.Role.ToString() == "Custom")
                        {
                            accessmanagercustoms.users.Role = UserRole.Custom;
                            accessmanagercustoms.CustomSubRolType.RoleSubtype = txtRolesubtype.Text;
                        }
                        if (userdetails.Role.ToString() == "Manager")
                        {
                            accessmanagercustoms.users.Role = UserRole.Manager;
                        }
                        if (userdetails.Role.ToString() == "Administrator")
                        {
                            accessmanagercustoms.users.Role = UserRole.Administrator;
                        }
                        if (userdetails.Role.ToString() == "Operator")
                        {
                            accessmanagercustoms.users.Role = UserRole.Operator;
                        }
                       
                    }
                    accessmanagercustoms.CustomSubRolType.UserId = Convert.ToInt32(ddlusers.SelectedValue);
                    //accessmanagercustoms.CustomSubRolType.RoleSubtype = txtRolesubtype.Text;
                    Facade.UpdateRoleById(accessmanagercustoms.users.Id, (accessmanagercustoms.users.Role).ToString());
                    Facade.AddCustomRoleSubtype(accessmanagercustoms.CustomSubRolType);
                }
                if (Save.Text == "Update")
                {
                    accessmanagercustoms.users.Id = Convert.ToInt32(ddlusers.SelectedValue);
                    //accessmanagercustoms.users.Role = UserRole.Custom;
                    var userdetails = Facade.GetUserById(Convert.ToInt32(ddlusers.SelectedValue));
                    if (userdetails != null)
                    {
                        if (userdetails.Role.ToString() == "Custom")
                        {
                            accessmanagercustoms.users.Role = UserRole.Custom;
                            accessmanagercustoms.CustomSubRolType.RoleSubtype = txtRolesubtype.Text;
                        }
                        if (userdetails.Role.ToString() == "Manager")
                        {
                            accessmanagercustoms.users.Role = UserRole.Manager;
                        }
                        if (userdetails.Role.ToString() == "Administrator")
                        {
                            accessmanagercustoms.users.Role = UserRole.Administrator;
                        }
                        if (userdetails.Role.ToString() == "Operator")
                        {
                            accessmanagercustoms.users.Role = UserRole.Operator;
                        }
                        //else
                        //{

                        //   // accessmanagercustoms.users.Role = UserRole.SubCustom;
                        //    accessmanagercustoms.CustomSubRolType.RoleSubtype = txtRolesubtype.Text;
                        //}
                    }

                    var testvalue = Facade.GetCustomSubRoleTypebyUserId(Convert.ToInt32(ddlusers.SelectedValue));
                    accessmanagercustoms.CustomSubRolType.UserId = Convert.ToInt32(ddlusers.SelectedValue);
                    if(ddlusers.SelectedValue == "Custom")
                    {
                        accessmanagercustoms.CustomSubRolType.RoleSubtype = txtRolesubtype.Text;
                        accessmanagercustoms.CustomSubRolType.Id = testvalue.Id;
                    }
                    Facade.UpdateRoleById(accessmanagercustoms.users.Id, (accessmanagercustoms.users.Role).ToString());
                    Facade.UpdateCustomSubRoleTypebyUserId(accessmanagercustoms.CustomSubRolType);
                    menuList = Facade.GetAccessManagerByUserId(Convert.ToInt32(ddlusers.SelectedValue));
                    Facade.DeleteAccessmanagerbyUserId(Convert.ToInt32(ddlusers.SelectedValue));
                }
                checklistselected(chksDashboard, lblDashboard);
                checklistselected(chkConfiguration, lblConfiguration);
                checklistselected(chkView, lblView);
                checklistselected(chkManage, lblManage);
                checklistselected(chkAlerts, lblAlerts);
                checklistselected(chkITOrchestration, lblITOrchestration, checklistselectedProfile(ddlProfiles, lblWorkFlowProfile));
                checklistselected(chkReports, lblReports);


                accessmanagercustoms.userinfraobject.UserId = Convert.ToInt32(ddlusers.SelectedValue);
                _cskgoup = Parent.FindControl("cskgoup") as CheckBoxList;
                var userInfraList = Facade.GetUserInfraObjectByUserId(accessmanagercustoms.userinfraobject.UserId);
                if (userInfraList != null)
                {
                    foreach (var listItem in from ListItem listItem in _cskgoup.Items from userInfraItem in userInfraList where listItem.Value == userInfraItem.InfraObjectId.ToString() && userInfraItem.IsApplication == 0 select listItem)
                    {
                        listItem.Selected = true;
                        _previousSelectedItems.Add(listItem);
                    }
                }
                var currentSelectedItems = Utility.GetSelectedItem(_cskgoup);
                var previousItems = _previousSelectedItems;
                if (previousItems != null && currentSelectedItems != null)
                {
                    CompareToCollection(previousItems, currentSelectedItems, 0);
                }
                var UserNameDetails = Facade.GetUserInfoByUserId(Convert.ToInt32(ddlusers.SelectedValue));
                ActivityLogger.AddLog(LoggedInUserName, "User", UserActionType.UpdateUserData, "The User '" + UserNameDetails.UserName + "' was updates to the user component", LoggedInUserId);
            }
        }

        private void CompareToCollection(IEnumerable<ListItem> previousItems, IList<ListItem> currentSelectedItems, int isApplication)
        {
            var deleteListInfra = new List<ListItem>();
            var addListGroups = currentSelectedItems;
            foreach (var prItem in previousItems)
            {
                var itemFind = false;

                foreach (var crItem in currentSelectedItems)
                {
                    if (prItem.Text != crItem.Text) continue;
                    if (addListGroups.Contains(crItem))
                    {
                        addListGroups.Remove(crItem);
                    }
                    itemFind = true;
                    break;
                }
                if (!itemFind)
                {
                    deleteListInfra.Add(prItem);
                }
            }

            try
            {
                if (deleteListInfra.Count > 0)
                {
                    foreach (var userInfra in deleteListInfra.Select(deleteItem => new UserInfraObject
                    {
                        UserId = Convert.ToInt32(accessmanagercustoms.UserId),
                        InfraObjectId = Convert.ToInt32(deleteItem.Value),
                    }))
                    {
                        if (userInfra.InfraObjectId != 0 && userInfra.UserId != 0)
                            Facade.DeleteUserInfraObjectByUserId(userInfra);
                    }
                }

                if (addListGroups.Count > 0)
                {

                    foreach (var userInfraObject in addListGroups.Select(addItem => new UserInfraObject
                    {
                        UserId = Convert.ToInt32(accessmanagercustoms.UserId),
                        InfraObjectId = Convert.ToInt32(addItem.Value),
                        IsApplication = Convert.ToInt32(isApplication),
                        CreatorId = Convert.ToInt32(LoggedInUserId)
                    }))
                    {
                        if (userInfraObject.InfraObjectId != 0 && userInfraObject.UserId != 0)
                            Facade.AddUseInfraObject(userInfraObject);
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occurred while comparing user group list", ex);
                ExceptionManager.Manage(cpException);
            }
        }

        protected void Save_Click(object sender, EventArgs e)
        {
            if (Page.IsValid)
            {
                var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }
                var submitButton = (Button)sender;
                var buttionText = " " + submitButton.Text.ToLower() + " ";
                var currentTransactionType = TransactionType.Undefined;
                if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                {
                    currentTransactionType = TransactionType.Save;
                }
                else if (buttionText.Contains(" update "))
                {
                    currentTransactionType = TransactionType.Update;
                }
                try
                {
                    // BuildEntities();
                    StartTransaction();
                    SaveEditor();
                    EndTransaction();
                    var UserNameDetails = Facade.GetUserInfoByUserId(Convert.ToInt32(ddlusers.SelectedValue));
                    string message = MessageInitials + " " + '"' + UserNameDetails.UserName + '"';
                    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));
                    Save.Enabled = false;

                }
                catch (CpException ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    ExceptionManager.Manage(ex, Page);
                }
                catch (Exception ex)
                {
                    InvalidateTransaction();

                    returnUrl = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                    if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        ExceptionManager.Manage((CpException)ex.InnerException, Page);

                    else
                    {
                        var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                        ExceptionManager.Manage(customEx, Page);
                    }
                }

                if (returnUrl.IsNotNullOrEmpty())
                {
                    Helper.Url.Redirect(new SecureUrl(returnUrl));
                }
                else
                {
                    Response.Redirect(Constants.UrlConstants.Urls.User.UserList, false);
                }
            }
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.User.UserList + "?Listitems=" + "Cancel");
        }

        private string LabelString(string labeltext)
        {
            switch (labeltext)
            {
                case "Dashboard":
                    if (labeltext.Equals(AccessManagerType.Dashboard))
                        labeltext = (AccessManagerType.Dashboard).ToString();
                    break;
                case "Configuration":
                    if (labeltext.Equals(AccessManagerType.Configuration))
                        labeltext = (AccessManagerType.Configuration).ToString();
                    break;
                case "View":
                    if (labeltext.Equals(AccessManagerType.View))
                        labeltext = (AccessManagerType.View).ToString();
                    break;
                case "Manage":
                    if (labeltext.Equals(AccessManagerType.Manage))
                        labeltext = (AccessManagerType.Manage).ToString();
                    break;
                case "ITOrchestration":
                    if (labeltext.Equals(AccessManagerType.ITOrchestration))
                        labeltext = (AccessManagerType.ITOrchestration).ToString();
                    break;
                case "Alerts":
                    if (labeltext.Equals(AccessManagerType.Alerts))
                        labeltext = (AccessManagerType.Alerts).ToString();
                    break;
                case "Reports":
                    if (labeltext.Equals(AccessManagerType.Reports))
                        labeltext = (AccessManagerType.Reports).ToString();
                    break;
            }
            return labeltext;

        }

        private string CheckString(string checktext)
        {


            switch (checktext)
            {
                case "Monitor":
                    if (checktext.Equals(AccessSubMenuType.Monitor))
                        checktext = (AccessSubMenuType.Monitor).ToString();
                    break;

                case "Management":
                    if (checktext.Equals(AccessSubMenuType.Management))
                        checktext = (AccessSubMenuType.Management).ToString();
                    break;

                case "Add":
                case "View":
                    if (checktext == "View" && chkexceuteval == 1)
                    {
                        //chkexceuteval = 0;
                        if (checktext.Equals(AccessSubMenuType.View))
                            checktext = (AccessSubMenuType.View).ToString();
                    }
                    else if (checktext == "View" && chkaddval == 1)
                    {

                        if (checktext.Equals(AccessSubMenuType.View))
                            checktext = (AccessSubMenuType.View).ToString();
                    }
                    else if (checktext == "View" && chkeditval == 1)
                    {

                        if (checktext.Equals(AccessSubMenuType.View))
                            checktext = (AccessSubMenuType.View).ToString();

                    }
                    else if (chkdelval == 1 && checktext == "View")
                    {
                        if (checktext.Equals(AccessSubMenuType.View))
                            checktext = (AccessSubMenuType.View).ToString();

                    }

                    else if (checktext == "View")
                    {
                        checktext = "Add";
                        if (checktext.Equals(AccessSubMenuType.Add))
                            checktext = (AccessSubMenuType.Add).ToString();
                    }
                    else
                    {

                        if (checktext.Equals(AccessSubMenuType.Add))
                            checktext = (AccessSubMenuType.Add).ToString();
                    }
                    break;

                case "Edit":
                    if (checktext.Equals(AccessSubMenuType.Edit))
                        checktext = (AccessSubMenuType.Edit).ToString();
                    break;

                case "Delete":
                    if (checktext.Equals(AccessSubMenuType.Delete))
                        checktext = (AccessSubMenuType.Delete).ToString();
                    break;

                case "Execute":
                    //  if (checktext==(AccessSubMenuType.Execute))

                    if (checktext == "Execute")
                    {
                        chkexceuteval = 1;
                        checktext = (AccessSubMenuType.Execute).ToString();
                    }
                    break;

                case "Undefined":
                    if (checktext.Equals(AccessSubMenuType.Undefined))
                        checktext = (AccessSubMenuType.Undefined).ToString();
                    break;
            }

            return checktext;
        }



        private void CheckBoxListAllSelected(CheckBoxList grpOrAppList)
        {
            int selectedKwt = 0;

            for (int i = 1; i < grpOrAppList.Items.Count; i++)
            {
                if (grpOrAppList.Items[i].Selected)
                {
                    selectedKwt++;
                }
                grpOrAppList.Items[i].Enabled = true;
            }

            if (selectedKwt == grpOrAppList.Items.Count - 1)
            {
                grpOrAppList.Items[0].Selected = true;
            }
            else
            {
                grpOrAppList.Items[0].Selected = false;
            }
        }

        private void CheckBoxListAllSelected(ListBox grpOrAppList)
        {
            int selectedKwt = 0;

            for (int i = 1; i < grpOrAppList.Items.Count; i++)
            {
                if (grpOrAppList.Items[i].Selected)
                {
                    selectedKwt++;
                }
                grpOrAppList.Items[i].Enabled = true;
            }

            if (selectedKwt == grpOrAppList.Items.Count - 1)
            {
                grpOrAppList.Items[0].Selected = true;
            }
            else
            {
                grpOrAppList.Items[0].Selected = false;
            }
        }

        protected void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                accessmanagercustoms.users.Id = Convert.ToInt32(ddlusers.SelectedValue);
                accessmanagercustoms.users.Role = UserRole.Custom;

                if (accessmanagercustoms.users.Id != 0)
                {
                    Facade.DeleteAccessmanagerbyUserId(accessmanagercustoms.users.Id);
                    var UserNameDetails = Facade.GetUserInfoByUserId(accessmanagercustoms.users.Id);
                    ActivityLogger.AddLog(LoggedInUserName, "Custom", UserActionType.DeleteUserAccount,
                      "user '" + UserNameDetails.UserName +
                      "' was deleted", LoggedInUserId);

                    ErrorSuccessNotifier.AddSuccessMessage(
                        Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                            "The Access Manager custom Role for " + " " + UserNameDetails.UserName, TransactionType.Delete));

                    Response.Redirect(Constants.UrlConstants.Urls.User.UserList, false);

                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);
                    ExceptionManager.Manage(customEx, Page);
                }
            }
        }

        #endregion

        protected void chkITOrchestration_SelectedIndexChanged(object sender, EventArgs e)
        {

            int changedIndex = chkITOrchestration.SelectedIndex;

            if (changedIndex >= 0)
            {
                bool selectedOpt = chkITOrchestration.Items[changedIndex].Selected;

                string selectedChkValue = chkITOrchestration.Items[changedIndex].Text;

                if (selectedChkValue.ToString().ToUpper().Equals("VIEW") && selectedOpt)
                {

                    if (chkITOrchestration.Items[0].Selected || chkITOrchestration.Items[1].Selected || chkITOrchestration.Items[2].Selected)
                    {
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = true;
                    }
                    else
                    {
                        if (chkITOrchestration.Items[3].Selected)
                        {
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                            pnlworflowprofiles.Visible = true;
                        }
                        else
                        {
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = false;
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = false;
                        }
                    }
                }
                else if (!selectedChkValue.ToString().ToUpper().Equals("VIEW"))
                {
                    if (chkITOrchestration.SelectedItem.ToString().ToUpper().Equals("EXECUTE"))
                    {
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                        pnlworflowprofiles.Visible = true;
                    }

                    if (chkITOrchestration.SelectedItem.ToString().ToUpper().Equals("ADD") && selectedOpt)
                    {
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = true;
                    }
                    else
                    {
                        if (chkITOrchestration.Items[0].Selected || chkITOrchestration.Items[1].Selected || chkITOrchestration.Items[2].Selected)
                        {
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = true;
                        }
                        else
                        {
                            if (chkITOrchestration.Items[3].Selected)
                            {
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                                pnlworflowprofiles.Visible = true;
                            }
                            else
                            {
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = false;
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = false;
                                pnlworflowprofiles.Visible = true;
                            }
                            //}
                        }
                    }

                    if (chkITOrchestration.SelectedItem.ToString().ToUpper().Equals("EDIT") && selectedOpt)
                    {
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = true;

                    }
                    else
                    {
                        if (chkITOrchestration.Items[0].Selected || chkITOrchestration.Items[1].Selected || chkITOrchestration.Items[2].Selected)
                        {
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = true;
                        }
                        else
                        {
                            if (chkITOrchestration.Items[3].Selected)
                            {
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                                pnlworflowprofiles.Visible = true;
                            }
                            else
                            {
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = false;
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = false;
                                pnlworflowprofiles.Visible = false;
                            }
                        }
                    }


                    if (chkITOrchestration.SelectedItem.ToString().ToUpper().Equals("DELETE") && selectedOpt)
                    {
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = true;

                        if (chkITOrchestration.Items[3].Selected)
                        {
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                            pnlworflowprofiles.Visible = true;
                        }
                        else
                        {
                            // chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = false;
                            //chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = false;
                            pnlworflowprofiles.Visible = false;
                        }
                    }
                    else
                    {
                        if (chkITOrchestration.Items[0].Selected || chkITOrchestration.Items[1].Selected || chkITOrchestration.Items[2].Selected)
                        {
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                            chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = true;
                            // pnlworflowprofiles.Visible = true;
                        }
                        else
                        {
                            if (chkITOrchestration.Items[3].Selected)
                            {
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                                pnlworflowprofiles.Visible = true;
                            }
                            else
                            {
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = false;
                                chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = false;
                                pnlworflowprofiles.Visible = true;
                            }
                        }


                        if (chkITOrchestration.Items[3].Selected)
                        {

                            pnlworflowprofiles.Visible = true;

                        }
                        else
                        {

                            pnlworflowprofiles.Visible = false;
                        }



                    }
                }

            }
            else
            {
                if (chkITOrchestration.Items[0].Selected || chkITOrchestration.Items[1].Selected || chkITOrchestration.Items[2].Selected)
                {
                    chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                    chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = true;
                }
                else
                {
                    if (chkITOrchestration.Items[3].Selected)
                    {
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = true;
                        pnlworflowprofiles.Visible = true;
                    }
                    else
                    {
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Selected = false;
                        chkITOrchestration.Items[chkITOrchestration.Items.Count - 1].Enabled = false;
                        pnlworflowprofiles.Visible = false;
                    }
                }
            }

            //foreach (ListItem listitem in chkITOrchestration.Items)
            //{
            //    if (listitem.Selected && listitem.ToString() == "Execute")
            //    {

            //        pnlworflowprofiles.Visible = true;
            //    }
            //    else
            //        pnlworflowprofiles.Visible = false;

            //}
        }

        protected void chkParallelProfile_SelectedIndexChanged(object sender, EventArgs e)
        {
            //ListItem listitem1 = chkParallelProfile.Items.FindByValue("0");
            //if (listitem1.Selected && listitem1.ToString() == "ALL")
            //{
            //    foreach (ListItem listitem in chkParallelProfile.Items)
            //    {
            //        listitem.Selected = true;

            //    }
            //}
            string result = Request.Form["__EVENTTARGET"];
            string[] checkedBox = result.Split('$');
            int checkedIndex = int.Parse(checkedBox[checkedBox.Length - 1]);

            if (checkedIndex == 0)
            {
                if (ddlProfiles.Items[0].Selected)
                {
                    for (int i = 1; i < ddlProfiles.Items.Count; i++)
                    {
                        ddlProfiles.Items[i].Selected = true;
                        ddlProfiles.Items[i].Enabled = true;
                    }
                }
                else
                {
                    for (int i = 1; i < ddlProfiles.Items.Count; i++)
                    {
                        ddlProfiles.Items[i].Selected = false;
                        ddlProfiles.Items[i].Enabled = true;
                    }
                }
            }
            else
            {
                CheckBoxListAllSelected(ddlProfiles);
            }

        }
        
    }
}