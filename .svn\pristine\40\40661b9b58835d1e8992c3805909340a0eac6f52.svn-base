﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="NewDataLag24Hours.ascx.cs"
    Inherits="CP.UI.Controls.NewDataLag24Hours" %>
<link href="../App_Themes/ReportTheme/Report.css" rel="stylesheet" type="text/css" />

            <asp:Panel ID="Pan1" runat="server" CssClass="widget widget-heading-simple widget-body-white">
    <hr />
    <div class="widget-head">
        <div class="col-md-8">
            <h5>Datalag Status Report as on
                <asp:Label ID="lblDate" Font-Names="Lucida Console" Font-Bold="false" Font-Italic="false" runat="server"></asp:Label>
                (From 00:00 Hrs to 23:00 Hrs)</h5>
        </div>
        <div class="col-md-4">
            <asp:Button ID="btnPdf" CssClass="btn btn-primary" Width="45%" runat="server" Text="Export To Pdf"
                OnClick="btnPdf_Click" />
            <asp:Button ID="btnExcel" CssClass="btn btn-primary" Width="45%" runat="server" Text="Export To Excel"
                OnClick="btnExcel_Click" />
        </div>
    </div>
    <hr />
    <div class="widget-body">
        <div style="overflow: auto;">
            <asp:Table ID="tbl" Style="font-size: 10.5px;" runat="server" Width="100%">
            </asp:Table>
        </div>
        <div class="row">
            <div class="col-md-3 text-success">
                <img src="../images/icons/status.png" width="16" height="16" alt="Datalag <= 2Hrs"
                    class="vertical-align" /><asp:Label ID="lblDatalagG" Text="Datalag <= Agreed RPO" runat="server"></asp:Label>
            </div>
            <div class="col-md-3 text-danger">
                <img src="../images/icons/status-busy.png" width="16" height="16" alt="Datalag > 2Hrs" />
                <asp:Label ID="lblDatalagL" Text="Datalag > Agreed RPO" runat="server"></asp:Label>
                
            </div>
            <div class="col-md-3">
                <img src="../images/icons/status-away.jpg" width="16" height="16" alt="Not Available" />Not Available
            </div>
        </div>
    </div>
</asp:Panel>
<hr />
<div class="form-group">
    <div class="col-xs-6">
        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
    </div>
    <div class="col-xs-6">
        <asp:Button ID="btnPdfSave" CssClass="btn btn-primary" Width="20%" runat="server" Style="margin-left:5px"
            Text="View" OnClick="btnPdfSave_Click" />
    </div>
</div>
       

<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div id="imgLoading" class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>