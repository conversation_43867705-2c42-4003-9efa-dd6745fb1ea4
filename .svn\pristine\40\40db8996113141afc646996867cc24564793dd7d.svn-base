﻿using CP.Common.DatabaseEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace CP.DataAccess.InfraobjectScheduledWF
{
    internal sealed class InfraObjectScheduleWFBuilder : IEntityBuilder<InfraObjectscheduleWorkFlows>
    {
        /// <param name="InfraobjectScheduleWorkFlow">InfraobjectScheduleWorkFlow</param>
        /// <returns>InfraobjectScheduleWorkFlow</returns>
        /// <author>Meenakshi  28july2015</author>
        /// 

        IList<InfraObjectscheduleWorkFlows> IEntityBuilder<InfraObjectscheduleWorkFlows>.BuildEntities(IDataReader reader)
        {
            var infraobjects = new List<InfraObjectscheduleWorkFlows>();

            while (reader.Read())
            {
                infraobjects.Add(((IEntityBuilder<InfraObjectscheduleWorkFlows>)this).BuildEntity(reader, new InfraObjectscheduleWorkFlows()));
            }

            return (infraobjects.Count > 0) ? infraobjects : null;
        }

        InfraObjectscheduleWorkFlows IEntityBuilder<InfraObjectscheduleWorkFlows>.BuildEntity(IDataReader reader, InfraObjectscheduleWorkFlows infraobject)
        {
            infraobject.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            infraobject.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            infraobject.WorkFlowId = Convert.IsDBNull(reader["WorkFlowId"]) ? 0 : Convert.ToInt32(reader["WorkFlowId"]);
            infraobject.WorkFlowName = Convert.IsDBNull(reader["WorkFlowName"]) ? string.Empty : Convert.ToString(reader["WorkFlowName"]);
            infraobject.ScheduleType = Convert.IsDBNull(reader["ScheduleType"]) ? 0 : Convert.ToInt32(reader["ScheduleType"]);
            infraobject.ScheduleTime = Convert.IsDBNull(reader["ScheduleTime"]) ? string.Empty : Convert.ToString(reader["ScheduleTime"]);
            infraobject.IsEnable = Convert.IsDBNull(reader["IsEnable"]) ? false : Convert.ToBoolean(reader["IsEnable"]);
            infraobject.ReceiverID = Convert.IsDBNull(reader["ReceiverId"]) ? string.Empty : Convert.ToString(reader["ReceiverId"]);
            infraobject.WORKFLOWTYPE = Convert.IsDBNull(reader["WORKFLOWTYPE"]) ? 0 : Convert.ToInt32(reader["WORKFLOWTYPE"]);
            return infraobject;
        }
    }
}
