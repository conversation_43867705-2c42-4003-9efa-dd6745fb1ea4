﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Helper;
using CPDiscovery;
using log4net;
using CP.Common.Shared;

namespace CP.UI.SNMP
{
    public partial class ViewApplicationDependency : BasePage
    {
        #region Variables
        private readonly ILog _logger = LogManager.GetLogger(typeof(ViewApplicationDependency));
        private static readonly IFacade NewFacade = new Facade();

        private static string hostUserName = string.Empty;
        private static string hostUserPassword = string.Empty;
        private static string osType = string.Empty;

        private static DataTable AppDependencyResultTable = null;
        IList<CPDiscovery.DiscoveryResult> dependencyResult = null;

        private static StringBuilder buildNodeRelSb = null;
        private static StringBuilder jsonNodeRelSb = null;

        public static string inputString = string.Empty;
        public static string jsonString = string.Empty;
        public static string finalJsonString = string.Empty;

        public static int currentNode = 0;
        public static string nameStr = "name", childStr = "children";

        private static bool hasChild = false;
        private static bool first = true;
        private static int firstSplitCount = 0;
        private static string osName, logo = "logo";

        #endregion

        #region Events

        /// <summary>
        /// Page load event
        /// </summary>
        /// <author>Ram Mahajan-25/11/2014</author>
        public override void PrepareView()
        {
            if (IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            //BOC Validate Request
            ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = ViewState["_token"].ToString();
            }
            //EOC 

            if (!IsPostBack)
                System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "GetServerListFromDB();", true);

        }

        /// <summary>
        /// Discover application dependency
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-25/11/2014</author>
        protected void btnScanNetwork_Click(object sender, EventArgs e)
        {
            try
            {
                if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("ViewApplicationDependency", UserActionType.ViewApplicationDependency))
                {
                    if (!ValidateInput())
                    {
                        string returnUrl1 = Request.RawUrl;

                        CP.UI.Controls.ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                        ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                        Helper.Url.Redirect(returnUrl1);

                        //throw new CpException(CpExceptionType.InvalidCharacters);

                    }
                    else
                    {

                        //reload server dropdown on click of scan button
                        System.Web.UI.ScriptManager.RegisterClientScriptBlock(this, this.GetType(), "Script", "GetServerListFromDB();", true);

                        //IList<Server> serverList = new List<Server>();

                        ////check whether user has entered new IP address
                        //if (!string.IsNullOrEmpty(hdnHostUserName.Value) && !string.IsNullOrEmpty(hdnHostUserPwd.Value) &&
                        //    !string.IsNullOrEmpty(hdnOsType.Value) && !string.IsNullOrEmpty(hdnSelectedIndexValue.Value) &&
                        //    !string.IsNullOrEmpty(inIPAddress.Value) && inIPAddress.Value != "-Select Server Name-")
                        //{
                        //    ScanApplications();
                        //}
                        //else
                        //{
                        //    serverList = NewFacade.GetAllServers();
                        //    if (serverList != null && serverList.Count() > 0)
                        //    {
                        //        var matchedServerInfo = serverList.Where(i => i.IPAddress.Equals(inIPAddress.Value, StringComparison.OrdinalIgnoreCase));

                        //    }
                        //}
                        if (!string.IsNullOrEmpty(inIPAddress.Value) && (inIPAddress.Value != "-Select Server Name-"))
                        {
                            ScanApplications();
                        }
                        else
                        {
                            lblCount.Text = string.Empty;
                            lblTime.Text = string.Empty;
                            lblNoResultFound.Visible = true;
                            lblNoResultFound.Text = "Please provide IP Address or select server name";
                        }
                    }
                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
                lblCount.Text = string.Empty;
                lblTime.Text = string.Empty;
                lblNoResultFound.Visible = true;
                lblNoResultFound.Text = "Error while application discovery";
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while discovering application dependency", ex);
                ExceptionManager.Manage(cpException);
                lblCount.Text = string.Empty;
                lblTime.Text = string.Empty;
                lblNoResultFound.Visible = true;
                lblNoResultFound.Text = "Error while application discovery";
            }
        
        }

        /// <summary>
        /// serverlist dropdown event
        /// </summary>
        /// <param name="sender">sender</param>
        /// <param name="e">e</param>
        /// <author>Ram Mahajan-25/11/2014</author>
        protected void DdlServerSelectedIndexChanged(object sender, EventArgs e)
        {
            //if (ddlServerList.SelectedIndex > 0)
            //{
            //    var server = Facade.GetServerById(Convert.ToInt16(ddlServerList.SelectedValue));
            //    if (server != null)
            //    {
            //        if (!string.IsNullOrEmpty(server.IPAddress))
            //            inIPAddress.Value = CryptographyHelper.Md5Decrypt(server.IPAddress);
            //        if (!string.IsNullOrEmpty(server.SSHUserName))
            //            hostUserName = CryptographyHelper.Md5Decrypt(server.SSHUserName);
            //        if (!string.IsNullOrEmpty(server.SSHPassword))
            //            hostUserPassword = CryptographyHelper.Md5Decrypt(server.SSHPassword);
            //        if (!string.IsNullOrEmpty(server.OSType))
            //            osType = server.OSType;
            //    }
            //}
            //else if (ddlServerList.SelectedIndex == 0)
            //    inIPAddress.Value = "";
        }

        #endregion

        #region Methods

        #region Graphical Network Application Discovery tree

        /// <summary>
        /// Gets node relation of Host,OS and List of applications.
        /// </summary>
        /// <param name="appDiscoveryTable">discovery table</param>
        /// <returns>Node relation as string</returns>
        /// <author>Ram mahajan-03/11/2014</author>
        public static string GetNodeRelation(DataTable appDiscoveryTable, string parentNode)
        {
            try
            {
                if (appDiscoveryTable != null && appDiscoveryTable.Rows.Count > 0)
                {
                    if (buildNodeRelSb == null)
                        buildNodeRelSb = new StringBuilder();
                    ConstructNodeRelationForIPAddress(parentNode, appDiscoveryTable);

                    buildNodeRelSb.Append(";");

                    foreach (DataRow row in appDiscoveryTable.Rows)
                    {
                        ConstructNodeRelationForOSAndApp(row);
                    }
                }

            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting GetNodeRelation", ex);
                ExceptionManager.Manage(cpException);
            }
            return Convert.ToString(buildNodeRelSb);
        }

        /// <summary>
        /// Creates node relation for IPAddresses
        /// </summary>
        /// <param name="parentNode">parentNode</param>
        /// <param name="dt">datatable</param>
        /// <author>Ram mahajan-03/11/2014</author>
        public static void ConstructNodeRelationForIPAddress(string parentNode, DataTable dt)
        {
            int count = dt.Rows.Count;
            int i = 0;

            buildNodeRelSb.AppendFormat("{0}:", parentNode);

            foreach (DataRow row in dt.Rows)
            {
                buildNodeRelSb.AppendFormat("{0}", string.Concat(row["Host"].ToString(), "/IP"));

                if (i < count - 1)
                    buildNodeRelSb.Append(",");
                i++;
            }
        }

        /// <summary>
        /// Creates node relation for OS and List of Applications
        /// </summary>
        /// <param name="row">row of data</param>
        /// <author>Ram mahajan-03/11/2014</author>
        public static void ConstructNodeRelationForOSAndApp(DataRow row)
        {
            string operatingSys = string.Empty;

            buildNodeRelSb.AppendFormat("{0}:", string.Concat(row["Host"].ToString(), "/IP"));

            operatingSys = GetOSName(row["OperatingSystem"].ToString());

            buildNodeRelSb.AppendFormat("{0}", !string.IsNullOrEmpty(operatingSys) ? string.Concat(operatingSys, "/opsys") : "OS/opsys");

            buildNodeRelSb.Append(";");

            if (row["AllApps"].ToString().Contains(','))
            {
                string[] splitApplications = row["AllApps"].ToString().Split(',');
                List<string> filterAllApps = null;
                int appLimitCount = 0;

                if (splitApplications.Count() > 0)
                {
                    for (int j = 0; j < splitApplications.Count(); j++)
                    {
                        if (appLimitCount <= 25)
                        {
                            if ((splitApplications[j].ToString().Equals("ssh", StringComparison.OrdinalIgnoreCase)) || (splitApplications[j].ToString().Equals("unknown", StringComparison.OrdinalIgnoreCase)))
                            {
                                //Ignore these apps;
                            }
                            else
                            {
                                if (filterAllApps == null)
                                    filterAllApps = new List<string>();

                                filterAllApps.Add(splitApplications[j].ToString());
                                appLimitCount = appLimitCount + 1;
                            }
                        }
                    }
                    if (filterAllApps != null)
                    {
                        if (filterAllApps.Count() > 0)
                        {
                            int k = 0;
                            buildNodeRelSb.AppendFormat("{0}:", !string.IsNullOrEmpty(operatingSys) ? string.Concat(operatingSys, "/opsys") : "OS/opsys");
                            foreach (var item in filterAllApps)
                            {
                                buildNodeRelSb.AppendFormat("{0}", string.Concat(item, "/app"));
                                if (k < filterAllApps.Count() - 1)
                                    buildNodeRelSb.Append(",");
                                k++;
                            }
                            buildNodeRelSb.Append(";");
                        }

                    }
                }

            }
            else
            {
                if (!string.IsNullOrEmpty(row["AllApps"].ToString()))
                {
                    buildNodeRelSb.AppendFormat("{0}:", !string.IsNullOrEmpty(operatingSys) ? string.Concat(operatingSys, "/opsys") : "OS/opsys");
                    buildNodeRelSb.AppendFormat("{0}", string.Concat(row["AllApps"].ToString(), "/IP"));
                    buildNodeRelSb.Append(";");
                }
            }
        }

        /// <summary>
        /// Get operating system names along with shortform
        /// </summary>
        /// <param name="row">row of data</param>
        /// <returns>Os name</returns>
        /// <author>Ram mahajan-03/11/2014</author>
        public static string GetOSName(string row)
        {
            List<string> osNameList = null;
            osName = string.Empty;

            if (!string.IsNullOrEmpty(row))
            {
                if (row.Contains(','))
                {
                    osNameList = null;
                    if (osNameList == null)
                        osNameList = new List<string>();

                    osNameList = row.Split(',').ToList();
                }
                else if (row.Contains(';'))
                {
                    osNameList = null;
                    if (osNameList == null)
                        osNameList = new List<string>();

                    osNameList = row.Split(';').ToList();
                }
                else
                {
                    osNameList = null;
                    if (osNameList == null)
                        osNameList = new List<string>();
                    osNameList.Add(row);
                }
                if (osNameList != null)
                {
                    if (osNameList.Count() > 0)
                    {
                        if (osNameList[0].Contains(';'))
                            osName = osNameList[0].Split(';')[0].ToString();
                        else
                            osName = osNameList[0];

                        if (!string.IsNullOrEmpty(osName))
                        {
                            if (osName.StartsWith("Microsoft"))
                            {
                                return osName.Replace("Microsoft", "MS");
                            }
                        }
                    }
                }
            }
            return osName;
        }

        /// <summary>
        /// Convert input noderelation string into Json String format
        /// </summary>
        /// <param name="inputStr1">inputStr1</param>
        /// <returns>Json as string</returns>
        /// <author>Ram mahajan-03/11/2014</author>
        public static string GetJsonString(string inputStr1)
        {
            if (jsonNodeRelSb == null)
                jsonNodeRelSb = new StringBuilder();
            Random randomNumber = new Random();
            string[] firstSplitStr = inputStr1.Split(';');
            string[] secSplitStr = firstSplitStr[currentNode].Split(':');
            string[] thirdSplitStr = secSplitStr[1].Split(',');

            string serverImagePath = "../Images/icons/serverIP-icon.png";

            int actualChildCount = thirdSplitStr.Length;
            firstSplitCount = firstSplitStr.Length;

            if (currentNode < firstSplitCount - 1)
            {
                currentNode = currentNode + 1;
            }
            for (int j = 0; j < actualChildCount; j++)
            {
                if (first)
                {
                    jsonNodeRelSb.Append("{");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0] + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    first = false;
                }

                if (HasChild(inputStr1, thirdSplitStr[j]))
                {
                    jsonNodeRelSb.Append("{");
                    if (thirdSplitStr[j].Contains("/opsys"))
                    {
                        //jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByOSType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/app"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByAppType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/IP"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + serverImagePath + "\"" + ",");
                    }
                    else
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"" + ",");

                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    GetJsonString(inputStr1);

                    jsonNodeRelSb.Append("]");
                    jsonNodeRelSb.Append("}" + ",");
                }
                else
                {
                    jsonNodeRelSb.Append("{");
                    if (thirdSplitStr[j].Contains("/opsys"))
                    {
                        //jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"", ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByOSType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/app"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + GetImageByAppType(thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/")))) + "\"" + ",");
                    }
                    else if (thirdSplitStr[j].Contains("/IP"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + serverImagePath + "\"" + ",");
                    }
                    else
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j] + "\"", ",");

                    jsonNodeRelSb.Append("}");
                    if (j != actualChildCount - 1)
                        jsonNodeRelSb.Append(",");
                }
            }

            return Convert.ToString(jsonNodeRelSb);
        }

        /// <summary>
        /// Get imagepath as per operating system name
        /// </summary>
        /// <param name="osName">osName</param>
        /// <returns>string as path</returns>
        /// <author>Ram Mahajan-05/11/2014</author>
        public static string GetImageByOSType(string osName)
        {
            string imagePath = string.Empty;
            if (!string.IsNullOrEmpty(osName))
            {
                if (osName.Contains("Linux"))
                    imagePath = "../Images/icons/linux1.png";
                else if (osName.Contains("Microsoft Windows") || osName.Contains("Windows") || osName.Contains("MS Windows"))
                    imagePath = "../Images/icons/os.png";
                else if (osName.Contains("aix"))
                    imagePath = "../Images/icons/aix.png";
                else
                    imagePath = "../Images/icons/os_2.png";
            }
            return imagePath;
        }

        /// <summary>
        /// Get imagepath as per application
        /// </summary>
        /// <param name="Application">Application</param>
        /// <returns>string as path</returns>
        /// <author>Ram Mahajan-05/11/2014</author>
        public static string GetImageByAppType(string Application)
        {
            string imagePath = string.Empty;
            if (!string.IsNullOrEmpty(Application))
            {
                if (Application.Contains("oracle"))
                    imagePath = "../Images/icons/dbnew.png";
                else if (Application.Contains("ms-sql-s"))
                    imagePath = "../Images/icons/dbnew.png";
                else if (Application.Contains("mysql"))
                    imagePath = "../Images/icons/dbnew.png";
                else if (Application.Contains("postgresql"))
                    imagePath = "../Images/icons/dbnew.png";
                else
                    imagePath = "../Images/icons/app-icon.png";
            }
            return imagePath;
        }

        /// <summary>
        /// Determine whether parent node has any child nodes.
        /// </summary>
        /// <param name="inputString">inputString</param>
        /// <param name="thirdSplitStr1">thirdSplitStr1</param>
        /// <returns>True if haschild- false if not</returns>
        /// <author>Ram mahajan-31/10/2014</author>
        public static bool HasChild(string inputString, string thirdSplitStr1)
        {
            hasChild = false;
            string sixthSplitStr = string.Empty;
            string[] fourthSplitStr = inputString.Split(';');
            int fourthSplitCount = fourthSplitStr.Length;
            for (int p = 0; p < fourthSplitCount; p++)
            {
                string[] fifthSplitStr = fourthSplitStr[p].Split(':');
                sixthSplitStr = fifthSplitStr[0];
                if (sixthSplitStr == thirdSplitStr1)
                {
                    hasChild = true;
                    break;
                }
            }
            return hasChild;
        }

        /// <summary>
        /// Clears all global static variables
        /// </summary>
        /// <author>Ram mahajan-31/10/2014</author>
        public static void ClearAll()
        {
            AppDependencyResultTable = null;
            buildNodeRelSb = null;
            currentNode = 0;
            first = true;
            firstSplitCount = 0;
            hasChild = false;
            jsonNodeRelSb = null;
            inputString = string.Empty;
            jsonString = string.Empty;
            finalJsonString = string.Empty;
        }

        /// <summary>
        /// Get noderelation and creates Json for it.
        /// </summary>
        /// <param name="nodeRelation">nodeRelation</param>
        /// <returns>string</returns>
        /// <author>Ram Mahajan-31/10/2014</author>
        public static string NodeRelationConverter(string nodeRelation)
        {
            ClearAll();
            if (!string.IsNullOrEmpty(nodeRelation))
            {
                jsonString = GetJsonString(nodeRelation);
                jsonString = jsonString + "]" + "}";
                jsonString = jsonString.Replace("],", "]");
            }
            if (!string.IsNullOrEmpty(jsonString))
                return jsonString;
            else
                return string.Empty;
        }

        /// <summary>
        /// Converts IList into Datatable
        /// </summary>
        /// <typeparam name="T">Type</typeparam>
        /// <param name="data">IList Object</param>
        /// <returns>DataTable</returns>
        /// <author>Ram mahajan-31/10/2014</author>
        public static DataTable ConvertToDataTable<T>(IList<T> data)
        {
            PropertyDescriptorCollection properties = TypeDescriptor.GetProperties(typeof(T));
            DataTable table = new DataTable();
            foreach (PropertyDescriptor prop in properties)
                table.Columns.Add(prop.Name, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType);
            foreach (T item in data)
            {
                DataRow row = table.NewRow();
                foreach (PropertyDescriptor prop in properties)
                    row[prop.Name] = prop.GetValue(item) ?? DBNull.Value;
                table.Rows.Add(row);
            }
            return table;
        }

        /// <summary>
        /// Gets application discovery time.
        /// </summary>
        /// <param name="strNotificationDate">strNotificationDate</param>
        /// <param name="strCompletionDate">strCompletionDate</param>
        /// <returns>string as time</returns>
        /// <author>Ram Mahajan-03/11/2014</author>
        public static string GetCompletionTime(string strNotificationDate, string strCompletionDate)
        {
            StringBuilder sbtime = new StringBuilder();
            if (!(string.IsNullOrEmpty(strNotificationDate) || string.IsNullOrEmpty(strCompletionDate)))
            {
                DateTime dtNotificationDate = DateTime.Parse(strNotificationDate);
                DateTime dtCompletionDate = DateTime.Parse(strCompletionDate);

                TimeSpan diffdate = dtCompletionDate - dtNotificationDate;

                if (diffdate.Days > 0)
                {
                    sbtime.Append(diffdate.Days + " Day(s) ");
                }
                if (diffdate.Hours > 0)
                {
                    sbtime.Append(diffdate.Hours + " Hr(s) ");
                }
                if (diffdate.Minutes > 0)
                {
                    sbtime.Append(diffdate.Minutes + " Min(s) ");
                }
                if (diffdate.Seconds > 0)
                {
                    sbtime.Append(diffdate.Seconds + " Sec(s)");
                }
            }
            return sbtime.ToString();
        }

        /// <summary>
        /// Discover applications dependency
        /// </summary>
        /// <author>Ram Mahajan-25/11/2014</author>
        public void ScanApplications()
        {
            _logger.Info(Environment.NewLine);
            _logger.Info("======Start of Application dependency======");
            _logger.Info(Environment.NewLine);

            var ProviderSettings = Facade.GetApplicationDependencyMappingSettings(ConfigurationManager.AppSettings["ProviderName"]);

            if (ProviderSettings != null)
            {
                _logger.Info("=======ProviderSettings Details========");
                _logger.Info(Environment.NewLine);
                _logger.Info("ProviderSettings.ProviderName:--" + ProviderSettings.ProviderName);
                _logger.Info(Environment.NewLine);
                _logger.Info("ProviderSettings.SNMPCommunity:--" + ProviderSettings.SNMPCommunity);
                _logger.Info(Environment.NewLine);
                _logger.Info("ProviderSettings.ThirdPartyPath:--" + ProviderSettings.ThirdPartyPath);
                _logger.Info(Environment.NewLine);

            }
            else
            {
                _logger.Info("ProviderSettings is null");
                _logger.Info(Environment.NewLine);
            }

            DateTime dtStartTime = DateTime.Now;

            dependencyResult = new List<CPDiscovery.DiscoveryResult>();

            if (ProviderSettings != null)
            {
                

                _logger.Info("Calling DiscoverDependency method with following parameters");
                _logger.Info(Environment.NewLine);
                _logger.Info("AppHost:--" + inIPAddress.Value);
                _logger.Info(Environment.NewLine);
                _logger.Info("NmapPath:--" + ConfigurationManager.AppSettings["NmapPath"]);
                _logger.Info(Environment.NewLine);
                _logger.Info("SNMPCommunity:--" + ProviderSettings.SNMPCommunity);
                _logger.Info(Environment.NewLine);

                _logger.Info("Discovering dependency for IP:--" + inIPAddress.Value);
                _logger.Info(Environment.NewLine);

                //dependencyResult = DependencyMapper.DiscoverDependency(inIPAddress.Value, @"C:\nmap-6.47-win32\nmap-6.47", 300000);

                //dependencyResult = DependencyMapper.DiscoverDependency(inIPAddress.Value, @"C:\nmap-6.47-win32\nmap-6.47", ProviderSettings.SNMPCommunity, 300000);
                dependencyResult = DependencyMapper.DiscoverDependency(inIPAddress.Value, ConfigurationManager.AppSettings["NmapPath"], ProviderSettings.SNMPCommunity, 300000);

               

                DateTime dtEndTime = DateTime.Now;
                lblTime.Text = GetCompletionTime(dtStartTime.ToString(), dtEndTime.ToString());

                if (dependencyResult != null && dependencyResult.Count() > 0)
                {
                    _logger.Info("Discovered dependency count for IP:--" + inIPAddress.Value + " is" + " " + dependencyResult.Count());
                    _logger.Info(Environment.NewLine);

                    _logger.Info("List of Discovered dependent IP's for :-- " + inIPAddress.Value);
                    _logger.Info(Environment.NewLine);

                    foreach (var ip in dependencyResult)
                    {
                        _logger.Info("Discovered dependent IP : --" + ip.Host);
                        _logger.Info(Environment.NewLine);
                    }

                    lblNoResultFound.Visible = false;
                    lblCount.Text = dependencyResult.Count.ToString();

                    AppDependencyResultTable = ConvertToDataTable<CPDiscovery.DiscoveryResult>(dependencyResult);

                    if (AppDependencyResultTable != null && AppDependencyResultTable.Rows.Count > 0)
                    {
                        inputString = GetNodeRelation(AppDependencyResultTable, inIPAddress.Value);
                        if (!string.IsNullOrEmpty(inputString))
                        {
                            finalJsonString = NodeRelationConverter(inputString);
                            if (!string.IsNullOrEmpty(finalJsonString))
                            {
                                int defaultHeight = 500;
                                int svgHeight = 0;
                                if (dependencyResult.Count() > 10)
                                {
                                    svgHeight = (defaultHeight + ((dependencyResult.Count() - 10) * 40));
                                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + svgHeight + "');", true);
                                }
                                else
                                {
                                    System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "treeShow('" + finalJsonString + "','" + defaultHeight + "');", true);
                                }
                            }
                        }
                    }
                }
                else
                {
                    //lblCount.Text = dependencyResult.Count.ToString();
                    lblCount.Text = "0";
                    lblNoResultFound.Visible = true;
                    lblNoResultFound.Text = "No application dependency found";
                    hdnHostUserName.Value = string.Empty;
                    hdnHostUserPwd.Value = string.Empty;
                    hdnOsType.Value = string.Empty;
                    hdnSelectedIndexValue.Value = string.Empty;
                    _logger.Info("dependencyResult.Count is zero");
                    _logger.Info(Environment.NewLine);

                }
            }
            _logger.Info("======End of Application dependency======");
            _logger.Info(Environment.NewLine);

        }

        /// <summary>
        /// Get all servers information
        /// </summary>
        /// <returns>List of servers</returns>
        /// <author>Ram Mahajan-25/11/2014</author>
        [WebMethod]
        public static ArrayList GetAllServers()
        {
            ArrayList serverList = new ArrayList();
            var listOfServer = NewFacade.GetAllServers();

            if (listOfServer != null && listOfServer.Count() > 0)
            {
                foreach (var server in listOfServer)
                {
                    serverList.Add(new
                    {
                        Value = (server.Id > 0) ? server.Id : 0,
                        IPAddress = string.IsNullOrEmpty(server.IPAddress) ? string.Empty : CryptographyHelper.Md5Decrypt(server.IPAddress),
                        Display = string.IsNullOrEmpty(server.Name) ? string.Empty : server.Name,
                        hostUserName = string.IsNullOrEmpty((server.SSHUserName)) ? string.Empty : CryptographyHelper.Md5Decrypt(server.SSHUserName),
                       // hostUserName = string.IsNullOrEmpty((server.SSHUserName)) ? string.Empty : CryptographyHelper.Md5Encrypt(server.SSHUserName),
                       // hostUserPassword = string.IsNullOrEmpty(server.SSHPassword) ? string.Empty : CryptographyHelper.Md5Decrypt(server.SSHPassword),
                        hostUserPassword = string.IsNullOrEmpty(server.SSHPassword) ? string.Empty : CryptographyHelper.Md5Encrypt(server.SSHPassword),
                        osType = string.IsNullOrEmpty(server.OSType) ? string.Empty : server.OSType
                    });
                }
            }

            return serverList;
        }

        #endregion


        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request


        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtCountryCode");
                //IgnoreIDs.Add("txtCountryCode2");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        #endregion

    }
}