﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.UI.Controls.ReportClients;
using Gios.Pdf;
using SpreadsheetGear;
using log4net;
using CP.UI.Controls;

namespace CP.UI.Controls
{
    public partial class GGReplicationReportHourly : BaseControl
    {
        private readonly ILog _logger = LogManager.GetLogger(typeof(GGReplicationReportHourly));

        protected void Page_Load(object sender, EventArgs e)
        {
            btnPdfSave.Visible = true;
        }

        protected void btnPdfSave_Click(object sender, EventArgs e)
        {
            PrepareExcelReport();
        }

        public override void PrepareView()
        {
            IList<InfraObject> Infra = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);

            if (Infra != null)
            {
                var InfraDB = (from infra in Infra where infra.RecoveryType == 107 select infra).ToList();

                ddlGroup.DataSource = InfraDB;
                ddlGroup.DataTextField = "Name";
                ddlGroup.DataValueField = "Id";
                ddlGroup.DataBind();
                ddlGroup.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
            }
            else
                ddlGroup.Items.Insert(0, new ListItem("No Infra Object Assigned", "0"));

            DeleteExcelFiles();
            lblMsg.Text = string.Empty;
        }

        private void DeleteExcelFiles()
        {
            if (!Directory.Exists(Server.MapPath(@"~/ExcelFiles")))
            {
                Directory.CreateDirectory(Server.MapPath(@"~/ExcelFiles"));
            }
            var directory = new DirectoryInfo(Server.MapPath(@"~/ExcelFiles"));

            foreach (FileInfo file in directory.GetFiles())
            {
                file.Delete();
            }
        }

        private void PrepareExcelReport()
        {
            var InfraObjId = Convert.ToInt32(ddlGroup.SelectedValue);
            InfraObject InfraObj = Facade.GetInfraObjectById(InfraObjId);
            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(InfraObj != null ? InfraObj.BusinessFunctionId : 0);
            TimeSpan conDatalag = TimeSpan.FromSeconds(businessFtn != null ? Convert.ToDouble(businessFtn.ConfiguredRPO) : Convert.ToDouble(0));
            int replicationId = InfraObj != null ? Convert.ToInt32(InfraObj.PRReplicationId) : 0;
            lblMsg.Text = string.Empty;

            switch (InfraObj.RecoveryType)
            {
                case (int)ReplicationType.GoldenGateRepli:
                    {

                        IList<GoldenGateReplicationMonitor> PR_GGRepliList = null;
                        IList<GoldenGateReplicationMonitor> DR_GGRepliList = null;
                        PR_GGRepliList = Facade.GoldenGateRepliMoniGetLast24Hrs(InfraObjId, "PR");
                        DR_GGRepliList = Facade.GoldenGateRepliMoniGetLast24Hrs(InfraObjId, "DR");
                        if (PR_GGRepliList != null && PR_GGRepliList.Count > 0 && DR_GGRepliList != null && DR_GGRepliList.Count > 0)
                        {
                            GoldenGateReplicationMonitorExcelView(PR_GGRepliList, DR_GGRepliList);
                        }
                        else
                        {
                            lblMsg.Visible = true;
                            lblMsg.Text = "No Records Found.";
                            return;
                        }

                        break;
                    }

                default:
                    {
                        lblMsg.Visible = true;
                        lblMsg.Text = "No Records Found.";
                        return;
                    }
            }

        }

        private void GoldenGateReplicationMonitorExcelView(IList<GoldenGateReplicationMonitor> PRGGRepliLog, IList<GoldenGateReplicationMonitor> DRGGRepliLog)
        {
            _logger.Info("======Generating Datalag (GGReplicationReportHourly) Report EXCEL View ======");
            _logger.Info(Environment.NewLine);

            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);


            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1"].Delete();
            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = ddlGroup.SelectedItem.Text.Length > 30 ? ddlGroup.SelectedItem.Text.Substring(0, 29) : ddlGroup.SelectedItem.Text;

            _cells["A1"].ColumnWidth = 10;

            _cells["D3"].Formula = "GoldenGate Replication Status Report";
            _cells["B3:H6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:H3"].Font.Size = 11;
            _cells["B5:H8"].Font.Size = 10;
            _cells["B3:H8"].Font.Color = Color.White;
            _cells["B3:H8"].Font.Name = "Cambria";


            InfraObject InfraObj = Facade.GetInfraObjectById(Convert.ToInt32(ddlGroup.SelectedItem.Value));
            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(InfraObj != null ? InfraObj.BusinessFunctionId : 0);
            TimeSpan conDatalag = TimeSpan.FromSeconds(Convert.ToDouble(businessFtn.ConfiguredRPO));

            _cells["D4"].Formula = "Infra Object Name : " + InfraObj.Name.ToUpper();
            _cells["D4"].Font.Size = 10;
            _cells["D4"].Font.Bold = true;
            _cells["D4"].ColumnWidth = 30;
            _cells["D4"].HorizontalAlignment = HAlign.Center;
            _cells["D4"].VerticalAlignment = VAlign.Top;

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 63, 10, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 600, 10, 120, 13);
            string strlogo = LoggedInUserCompany.CompanyLogoPath;
            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 450, 10, 121, 13);
            }
            reportWorksheet.Cells["A1:H1"].RowHeight = 27;
            reportWorksheet.Cells["A2:H2"].RowHeight = 25;

            _cells["B6"].Formula = "From Date";
            _cells["B6"].Font.Bold = true;
            _cells["B6"].HorizontalAlignment = HAlign.Left;

            _cells["C6"].Formula = ":  " + DateTime.Today.AddDays(-1).ToString("yyyy-MM-dd");
            _cells["C6"].Font.Bold = true;
            _cells["C6"].HorizontalAlignment = HAlign.Left;

            var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
            _cells["B5"].Formula = "Report Generated Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            _cells["C5"].Formula = ":  " + dateTime;
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;

            _cells["E6"].Formula = "To Date";
            _cells["E6"].Font.Bold = true;
            _cells["E6"].HorizontalAlignment = HAlign.Left;

            _cells["F6"].Formula = ":  " + DateTime.Today.AddDays(-1).ToString("yyyy-MM-dd");
            _cells["F6"].Font.Bold = true;
            _cells["F6"].HorizontalAlignment = HAlign.Left;

            int row = 9;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "Sr.No.";
            _cells["B" + row.ToString()].ColumnWidth = 25;
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
            _cells["B9:H9"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B9:G9"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "Group Name";
            _cells["C" + row.ToString()].ColumnWidth = 25;
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
            productionlog.WrapText = true;

            _cells["D" + row.ToString()].Formula = "Program";
            _cells["D" + row.ToString()].ColumnWidth = 25;
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["E" + row.ToString()].Formula = "Log read checkpoint RBA";
            _cells["E" + row.ToString()].ColumnWidth = 20;
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["F" + row.ToString()].Formula = "Logfile last record read checkpoint RBA";
            _cells["F" + row.ToString()].ColumnWidth = 20;
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["G" + row.ToString()].Formula = "DataLag";
            _cells["G" + row.ToString()].ColumnWidth = 20;
            _cells["G" + row.ToString()].Font.Bold = true;
            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;


            _cells["H" + row.ToString()].Formula = "TimeStamp";
            _cells["H" + row.ToString()].ColumnWidth = 20;
            _cells["H" + row.ToString()].Font.Bold = true;
            _cells["H" + row.ToString()].HorizontalAlignment = HAlign.Left;
            row++;
            int dataCount = 0;
            int xlRow = 10;

            _cells["D8"].Formula = "PRIMARY";
            _cells["D8"].Font.Size = 10;
            _cells["D8"].Font.Bold = true;
            _cells["D8"].ColumnWidth = 30;
            _cells["D8"].HorizontalAlignment = HAlign.Center;
            _cells["D8"].VerticalAlignment = VAlign.Top;
            _cells["D8"].Font.Color = Color.White;
            _cells["B8:H8"].Interior.Color = Color.FromArgb(79, 129, 189);

            foreach (var rp in PRGGRepliLog)
            {
                dataCount++;
                int column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F", "G","H" };
                xlRow++;

                string ndx = xlColumn[column] + row.ToString();
                _cells[ndx + ":" + "H" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;
                _cells[ndx].Formula = i.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                i++;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(rp.Group) ? rp.Group : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(rp.Program) ? rp.Program : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(rp.ChkPtRBALog) ? rp.ChkPtRBALog : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(rp.ChkRBALogFile) ? rp.ChkRBALogFile : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].HorizontalAlignment = HAlign.Left;

                if (rp.TimeAtChkPt != "NA" && !string.IsNullOrEmpty(rp.TimeAtChkPt))
                {
                    string[] split = rp.TimeAtChkPt.Split(':');
                    if (split[0].Contains('.'))
                    {
                        string[] splitFinal = split[0].Split('.');
                        split[0] = splitFinal[1];
                    }

                    bool isHealth = Utility.GetReportDatlagHealth(rp.TimeAtChkPt, businessFtn.ConfiguredRPO);

                    if (isHealth)
                    {
                        _cells[ndx].Formula = rp.TimeAtChkPt;
                        _cells[ndx].Font.Color = Color.Green;
                    }
                    else
                    {
                        _cells[ndx].Formula = rp.TimeAtChkPt;
                        _cells[ndx].Font.Color = Color.Red;
                    }
                }
                else
                {
                    _cells[ndx].Formula = "NA";
                    _cells[ndx].Font.Color = Color.Black;
                }
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(Convert.ToString(rp.CreateDate)) ? Convert.ToString(rp.CreateDate) : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                //column++;

                row++;
            }

            //Secondery


            int finalprCount = dataCount + 11;

            _cells["D" + finalprCount].Formula = "SECONDARY";
            _cells["D" + finalprCount].Font.Color = Color.White;
            _cells["D" + finalprCount].Font.Bold = true;
            _cells["D" + finalprCount].ColumnWidth = 30;
            _cells["D" + finalprCount].HorizontalAlignment = HAlign.Center;
            _cells["D" + finalprCount].VerticalAlignment = VAlign.Top;
            _cells["B" + finalprCount + ":" + "H" + finalprCount].Interior.Color = Color.FromArgb(79, 129, 189);
            i = 1;
            row = row + 3;
            foreach (var rp in DRGGRepliLog)
            {
                dataCount++;
                int column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F", "G","H" };
                xlRow++;

                string ndx = xlColumn[column] + row.ToString();
                _cells[ndx + ":" + "H" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;
                _cells[ndx].Formula = i.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                i++;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(rp.Group) ? rp.Group : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(rp.Program) ? rp.Program : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(rp.ChkPtRBALog) ? rp.ChkPtRBALog : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(rp.ChkRBALogFile) ? rp.ChkRBALogFile : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].HorizontalAlignment = HAlign.Left;

                if (rp.TimeAtChkPt != "NA" && !string.IsNullOrEmpty(rp.TimeAtChkPt))
                {
                    string[] split = rp.TimeAtChkPt.Split(':');
                    if (split[0].Contains('.'))
                    {
                        string[] splitFinal = split[0].Split('.');
                        split[0] = splitFinal[1];
                    }

                    bool isHealth = Utility.GetReportDatlagHealth(rp.TimeAtChkPt, businessFtn.ConfiguredRPO);

                    if (isHealth)
                    {
                        _cells[ndx].Formula = rp.TimeAtChkPt;
                        _cells[ndx].Font.Color = Color.Green;
                    }
                    else
                    {
                        _cells[ndx].Formula = rp.TimeAtChkPt;
                        _cells[ndx].Font.Color = Color.Red;
                    }
                }
                else
                {
                    _cells[ndx].Formula = "NA";
                    _cells[ndx].Font.Color = Color.Black;
                }
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = !string.IsNullOrEmpty(Convert.ToString(rp.CreateDate)) ? Convert.ToString(rp.CreateDate) : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 25;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Left;
                //column++;

                row++;
            }
            //EndPRDR

            int finalCount = dataCount + 15;
            _cells["B" + finalCount].Formula = "NA : Not Available";
            _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
            _cells["B" + finalCount].Font.Name = "Cambria";
            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

            _cells["C" + finalCount].Formula = "DataLag<= " + conDatalag + " Hours";
            _cells["C" + finalCount].Font.Color = Color.Green;
            _cells["C" + finalCount].HorizontalAlignment = HAlign.Left;
            _cells["C" + finalCount].Font.Name = "Cambria";

            _cells["D" + finalCount].Formula = "DataLag> " + conDatalag + "Hours";
            _cells["D" + finalCount].Font.Color = Color.Red;
            _cells["D" + finalCount].HorizontalAlignment = HAlign.Left;
            _cells["D" + finalCount].Font.Name = "Cambria";

            reportWorksheet.ProtectContents = true;


            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");

            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "GGRepliStatus_" + ddlGroup.SelectedItem.Text + "_" + str + ".xls";
            reportWorkbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + str;
            //var myUrl = "/ExcelFiles/" + str;
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar= RPO SLA Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
            _logger.Info("====== Generated Datalag (GGReplicationReportHourly) Report EXCEL View ======");
            _logger.Info(Environment.NewLine);

        }
    }
}