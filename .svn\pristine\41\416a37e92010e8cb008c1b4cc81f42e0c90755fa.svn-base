﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IUserInfoDataAccess

    public interface IUserInfoDataAccess
    {
        UserInfo Add(UserInfo userInfo);

        UserInfo Update(UserInfo userInfo);

        UserInfo GetById(int id);

        UserInfo GetByName(string name);

        UserInfo GetByUserId(int id);

        IList<UserInfo> GetAll();

        bool DeleteById(int id);

        bool IsExistByName(string name);

        UserInfo UpdateOTPCountById(int id, int newOTPCount);
    }

    #endregion IUserInfoDataAccess
}