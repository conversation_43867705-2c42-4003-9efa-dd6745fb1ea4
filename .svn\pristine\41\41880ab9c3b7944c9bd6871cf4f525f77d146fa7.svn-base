﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using SpreadsheetGear;
using System.Data;
using System.IO;
using System.Data.OleDb;
using System.Web.Services;
using CP.Common.DatabaseEntity;
using CP.Helper;
using CP.BusinessFacade;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.UI.Controls;
using log4net;


namespace CP.UI
{
    public partial class ImportCMDB : ImportCSVBasePageEditor
    {
        //Dictionary<string, string> MappingDict = new Dictionary<string, string>();
        static IList<Mapping> lstMapping = new List<Mapping>();
        IFacade _facade = new Facade();
        static string tempFilePath = "";
        DataTable firstTable = new DataTable();
        public static string testStrrr = "";
        string ServerName = "", IPAddress = "";

        private readonly ILog _logger = LogManager.GetLogger(typeof(ImportCMDB));

        public override string MessageInitials
        {
            get { return "ImportCMDB"; }
        }
        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.SereverList;
                }
                return string.Empty;
            }
        }
        protected void Page_Load(object sender, EventArgs e)
        {
            cnfrmExtndrBrowse.Enabled = false;

            if (tempFilePath != "")
            {
                ReadExcelLoadTolist(tempFilePath);
            }

            if (!IsPostBack)
            {
                if (IsUserOperator || IsUserManager)
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }

                lstMapping.Clear();
                lstvw.DataSource = null;
                lstvw.DataBind();
                lvMap.DataSource = null;
                lvMap.DataBind();
            }
            Utility.SelectMenu(Master, "Module2");
        }

        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());

        }
        public override void PrepareEditView()
        {

        }
        public override void BuildEntities()
        {

        }
        public override void SaveEditor()
        {

        }
        protected void BtnImportCSVClick(object sender, EventArgs e)
        {
            //string filPath = Server.MapPath(AsyncUpload.FileName);
            try
            {
                if (ValidateRequest("ImportCMDB", UserActionType.ImportCMDB))
                lstMapping.Clear();
                divMap.Attributes.Add("style", "display:none;");
                lvMap.Visible = false;

                lstvw.PageIndex = 0;

                lstvw.DataSource = null;
                lstvw.DataBind();

                lvMap.DataSource = null;
                lvMap.DataBind();

                if (!AsyncUpload.HasFile)
                {
                    lblErr.Visible = true;
                    lblStatus.Visible = false;
                    lblErr.Text = "Please select xls/xlsx file";
                }
                else
                {
                    lblErr.Text = string.Empty;
                    //check file type
                    StartUpLoad();
                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while file uploading", ex);
                ExceptionManager.Manage(cpException);
            }


        }

        [WebMethod]
        public static void MainFunctionTest(string chkText)
        {
            testStrrr = chkText;
        }

        private void StartUpLoad()
        {


            //First get file type
            //string contentType = AsyncUploadFile.PostedFile.ContentType;

            string contentType = AsyncUpload.PostedFile.FileName.Split('.')[1].ToString();

            //if (contentType.ToLower().Equals("csv", StringComparison.OrdinalIgnoreCase) || contentType.ToLower().Equals("text/plain", StringComparison.OrdinalIgnoreCase)
            //  || contentType.ToLower().Equals("text/x-log", StringComparison.OrdinalIgnoreCase))

            if (contentType.ToLower().Equals("xlsx", StringComparison.OrdinalIgnoreCase) || contentType.ToLower().Equals("xls", StringComparison.OrdinalIgnoreCase))
            {
                string csvFileName = AsyncUpload.FileName;
                ViewState["ExcelName"] = csvFileName;
                //set the csv file path
                string csvFilePath = Server.MapPath("~/ExcelFiles/") + csvFileName;


                //validates the posted file before saving
                if (File.Exists(csvFilePath))
                {
                    //lblStatus.Visible = false;
                    //lblErr.Visible = true;
                    //lblErr.Text = "File already exist...";

                    if ((System.IO.File.Exists(tempFilePath)))
                    {
                        System.IO.File.Delete(tempFilePath);
                    }


                }

                if (AsyncUpload.PostedFile != null && !string.IsNullOrEmpty(AsyncUpload.PostedFile.FileName))
                {


                    AsyncUpload.SaveAs(csvFilePath);
                    //string csvFilePath = Server.MapPath("~/ExcelFiles/") + AsyncUpload.FileName;
                    tempFilePath = csvFilePath;
                    //string filPath = Path.GetFullPath(AsyncUpload.PostedFile.FileName);
                    ReadExcelLoadTolist(csvFilePath);
                    btnImport.Enabled = true;
                    divExcelData.Attributes.Add("style", "display:block");
                    divAuthOptn.Attributes.Add("style", "display:block");
                }

            }
            else
            {
                lblErr.Visible = true;
                lblStatus.Visible = false;
                lblErr.Text = "Only xls/xlsx file format allowed";
            }
        }

        public void ReadExcelLoadTolist(string filepath)
        {
            if (File.Exists(filepath))
            {
                IWorkbookSet workbookSet = null;
                String ssFile = string.Empty;
                IWorkbook templateWorkbook = null;
                IWorksheet templateWorksheet = null;
                IRange _cells = null;

                workbookSet = Factory.GetWorkbookSet();
                //ssFile = @"C:\Users\<USER>\Desktop\Serverlist.xlsx";
                ssFile = filepath;
                templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                templateWorksheet = templateWorkbook.Worksheets[0];
                _cells = templateWorksheet.UsedRange;

                DataSet dataSet = templateWorkbook.GetDataSet(SpreadsheetGear.Data.GetDataFlags.FormattedText);
                firstTable = dataSet.Tables[0];
                firstTable = firstTable.Rows.Cast<DataRow>().Where(row => !row.ItemArray.All(field => field is System.DBNull || string.Compare((field as string).Trim(), string.Empty) == 0)).CopyToDataTable();

                lstvw.DataSource = firstTable;
                lstvw.DataBind();

            }
            else
            {
                lstvw.DataSource = null;
                lstvw.DataBind();
            }
        }

        protected void gvData_DataBound(object sender, GridViewRowEventArgs e)
        {
            if (e.Row.RowType == DataControlRowType.Header)
            {
                for (int i = 0; i < e.Row.Cells.Count; i++)
                {
                    if (e.Row.Cells[i] != null)
                    {
                        CheckBox chk = new CheckBox();
                        chk.Text = e.Row.Cells[i].Text;
                        //chk.CheckedChanged += chkActiveDirectory_CheckedChanged;

                        //chk.AutoPostBack = true;
                        chk.ID = e.Row.Cells[i].Text;
                        //chk.Attributes.Add("valT", e.Row.Cells[i].Text);


                        //chk.Attributes.Add("onclick", "checkCheck(" + this.ClientID + ")");
                        //chk.CssClass = "chkboxwt";
                        e.Row.Cells[i].Controls.Add(chk);
                    }
                }
            }
            //updpnl.Update();
        }

        protected void chkActiveDirectory_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox chk = (CheckBox)sender;
            if (chk.Checked)
            {
                lblHeaderName.Text = chk.Text;
                divMap.Visible = true;
            }
            else
            {
                lblHeaderName.Text = "";
                divMap.Visible = false;
            }

            //updpnl.Update();
        }

        protected void btnSetMappingFileds_Click(object sender, EventArgs e)
        {
            lblError.Text = "";
            lvMap.Visible = true;
            //divAuthOptn.Attributes.Add("style", "display:none");
            ShowAuthenticationOptions(chkAuthenticationOption.Checked, ddlAuthenticationType.SelectedItem.Text);

            if (string.IsNullOrEmpty(testStrrr.Trim()))
            {
                lblError.Text = "Please select the excel column to map";
                return;
            }

            if (lstMapping.Select(x => x.MappedColumnName == ddlServerFiledMap.SelectedItem.Text).FirstOrDefault())
            {
                lblError.Text = ddlServerFiledMap.SelectedItem.Text + " Already Mapped";
                return;
            }

            if (lstMapping.Select(x => x.ExcelColumnName == testStrrr).FirstOrDefault())
            {
                lblError.Text = testStrrr + " Already Mapped";
                return;
            }

            Mapping lpmatm = lstMapping.FirstOrDefault(a => a.ExcelColumnName == testStrrr && a.MappedColumnName == ddlServerFiledMap.SelectedItem.Text);

            if (lpmatm == null)
            {
                lstMapping.Add(new Mapping { ExcelColumnName = testStrrr, MappedColumnName = ddlServerFiledMap.SelectedItem.Text, MappedColumnId = ddlServerFiledMap.SelectedIndex });

                lvMap.DataSource = lstMapping.Where(x => x.ExcelColumnName != null && x.MappedColumnName != null);
                lvMap.DataBind();

                ListItem itemToRemove = ddlServerFiledMap.Items.FindByText(ddlServerFiledMap.SelectedItem.Text);
                if (itemToRemove != null)
                {
                    ddlServerFiledMap.Items.Remove(itemToRemove);
                }

                testStrrr = "";
                //ddlServerFiledMap.RemoveItem(ddlServerFiledMap.SelectedItem.Text, ddlServerFiledMap.SelectedIndex.ToString());

                divMap.Attributes.Add("style", "display: block");

                cnfrmExtndrBrowse.Enabled = true;

            }
            else
            {
                lblError.Text = testStrrr + " Already Mapped";
            }
        }

        protected void OnPaging(object sender, GridViewPageEventArgs e)
        {
            lstvw.PageIndex = e.NewPageIndex;
            lstvw.DataBind();
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            try
            {

                if (lstMapping == null)
                {
                    if (lstMapping.Count == 0)
                    {
                        lblError.Text = "Please Map IP Address";
                        return;
                    }
                }

                var testItem = (from c in lstMapping
                                where c.MappedColumnName == "ServerName"
                                select c).ToList();

                if (testItem == null || testItem.Count == 0)
                {
                    lblError.Text = "Please Map ServerName";
                    return;
                }

                testItem = (from c in lstMapping
                            where c.MappedColumnName == "IPAddress"
                            select c).ToList();

                if (testItem != null)
                {
                    if (testItem.Count != 0)
                    {
                        int AddedCnt = 0, FailedCnt = 0;
                        var _objImportCMDBStatus = new ImportCMDBStatus();
                        _objImportCMDBStatus.ExcelFileName = ViewState["ExcelName"] != null ? ViewState["ExcelName"].ToString() : AsyncUpload.PostedFile.FileName;
                        _objImportCMDBStatus.TotalCount = firstTable.Rows.Count;
                        _objImportCMDBStatus.ConfiguredCount = 0;
                        _objImportCMDBStatus.FailedCount = 0;
                        _objImportCMDBStatus.CreatorId = LoggedInUserId;

                        var ImportCMDBStatus = Facade.AddImportCMDBStatus(_objImportCMDBStatus);

                        foreach (DataRow dr in firstTable.Rows)
                        {
                            try
                            {
                                if (BuildCurrentEntityImportCMDB(dr, ImportCMDBStatus.Id))
                                {
                                    CurrentEntity.CreatorId = LoggedInUserId;
                                    CurrentEntity.UpdatorId = LoggedInUserId;
                                    CurrentEntity = _facade.AddServer(CurrentEntity);
                                    AddedCnt++;
                                }
                                else
                                {
                                    if (!string.IsNullOrEmpty(ServerName) && !string.IsNullOrEmpty(IPAddress))
                                        FailedCnt++;
                                }
                            }
                            catch (Exception ex)
                            {
                                string msg = ex.InnerException != null ? ex.InnerException.ToString() : ex.Message;

                                _logger.Error("Error Occured while Saving Data for Server Name : " + ServerName + " Error : " + msg);

                                Facade.AddImportCMDBLogs(new ImportCMDBLogs()
                                {
                                    IMPStatusId = ImportCMDBStatus.Id,
                                    ServerName = ServerName,
                                    IPAddress = IPAddress,
                                    Status = 0,
                                    FailedReason = "Syntax Error",
                                    CreatorId = LoggedInUserId
                                });

                            }
                        }

                        //string message = "CMDB components";
                        //ErrorSuccessNotifier.AddSuccessMessage(
                        //    Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                        //        TransactionType.Save));

                        ImportCMDBStatus.ConfiguredCount = AddedCnt;
                        ImportCMDBStatus.FailedCount = FailedCnt;

                        Facade.UpdateImportCMDBCount(ImportCMDBStatus);

                        string ExcelName = ViewState["ExcelName"] != null ? ViewState["ExcelName"].ToString() : string.Empty;

                        lblExcelFileName.Text = ExcelName;
                        lblExcelFileName.ToolTip = ExcelName;
                        lblTotalCount.Text = firstTable.Rows.Count.ToString();
                        lblImportedCount.Text = AddedCnt.ToString();
                        lblFailedCount.Text = FailedCnt.ToString();

                        modelbg.Visible = true;
                        pnlImportSuccess.Visible = true;

                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "Import CMDB Data", UserActionType.ImportCMDBData, AddedCnt + " Out of " + firstTable.Rows.Count.ToString() + " CMDB components have been imported successfully from " + ExcelName, LoggedInUserId);

                        //string ExName = ViewState["ExcelName"] != null ? ViewState["ExcelName"].ToString() : string.Empty;
                        //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, "ExcelName", ExName, "TotalCount", firstTable.Rows.Count.ToString(),
                        //                "AddedCount", AddedCnt.ToString());

                        //if (secureUrl != null)
                        //{
                        //    Helper.Url.Redirect(secureUrl);
                        //}
                    }
                    else
                    {
                        lblError.Text = "Please Map IP Address";
                        return;
                    }
                }
                else
                {
                    lblError.Text = "Please Map IP Address";
                    return;
                }

                if ((System.IO.File.Exists(tempFilePath)))
                {
                    System.IO.File.Delete(tempFilePath);
                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while saving server mapping data", ex);
                ExceptionManager.Manage(cpException);
            }

        }

        private bool BuildCurrentEntityImportCMDB(DataRow dRow, int ImportCMDBId)
        {
            try
            {
                string ExclServerName = "", ExclIpadd = "", ExclHostName = "", ExclPort = "", ExclUser = "",
                    ExclShhKeyPath = "", ExclShhKeyPassword = "", ExcelOSType = "", ExcelServerType = "", ExcelServerRole = "";

                int SSOTypeId = 0, SSOProfileId = 0;

                bool SSOEnabled = false;

                foreach (Mapping mp in lstMapping)
                {
                    if (mp.MappedColumnName != null)
                    {
                        switch (mp.MappedColumnName.ToLower())
                        {
                            case "servername":
                                ExclServerName = mp.ExcelColumnName;
                                break;
                            case "ipaddress":
                                ExclIpadd = mp.ExcelColumnName;
                                break;
                            case "hostname":
                                ExclHostName = mp.ExcelColumnName;
                                break;
                            case "port":
                                ExclPort = mp.ExcelColumnName;
                                break;
                            case "user":
                                ExclUser = mp.ExcelColumnName;
                                break;
                            case "password":
                                //ExclPassword = mp.ExcelColumnName;
                                CurrentEntity.SSHPassword = string.IsNullOrEmpty(mp.ExcelColumnName) ? CryptographyHelper.Md5Encrypt("admin@123") : CryptographyHelper.Md5Encrypt(dRow[mp.ExcelColumnName].ToString());
                                break;
                            case "sshkeypath":
                                //ExclShhKeyPath = mp.ExcelColumnName;
                                ExclShhKeyPath = dRow[mp.ExcelColumnName].ToString();
                                break;
                            case "sshkeypassword":
                                //ExclShhKeyPassword = mp.ExcelColumnName;
                                ExclShhKeyPassword = dRow[mp.ExcelColumnName].ToString();
                                break;
                            case "ostype":
                                ExcelOSType = mp.ExcelColumnName;
                                break;
                            case "serverrole":
                                ExcelServerRole = mp.ExcelColumnName;
                                break;
                            case "virtual/physical":
                                ExcelServerType = mp.ExcelColumnName;
                                break;

                        }
                    }

                    if (mp.AuthColumnName != null)
                    {
                        switch (mp.AuthColumnName.ToLower())
                        {
                            case "ssh password":
                                //ExclPassword = mp.AuthValue.Trim();
                                CurrentEntity.SSHPassword = string.IsNullOrEmpty(mp.AuthValue) ? CryptographyHelper.Md5Encrypt("admin@123") : CryptographyHelper.Md5Encrypt(mp.AuthValue);
                                break;
                            case "ssh key":
                                ExclShhKeyPath = mp.AuthValue.Trim();
                                break;
                            case "single sign on":
                                SSOEnabled = true;
                                SSOTypeId = mp.SSOTypeId;
                                SSOProfileId = mp.ProfileId;
                                break;
                        }
                    }
                }

                if (dRow[ExclIpadd] == null)
                {
                    return false;
                }
                else if (string.IsNullOrEmpty(dRow[ExclIpadd].ToString().Trim()))
                {
                    return false;
                }

                if (dRow[ExclServerName] == null)
                {
                    return false;
                }
                else if (string.IsNullOrEmpty(dRow[ExclServerName].ToString().Trim()))
                {
                    return false;
                }

                ServerName = dRow[ExclServerName].ToString();
                IPAddress = dRow[ExclIpadd].ToString();

                var serverdetails = Facade.GetServersByIPAddress(CryptographyHelper.Md5Encrypt(dRow[ExclIpadd].ToString()));

                if (serverdetails != null)
                {
                    var NameFnd = (from c in serverdetails
                                   where c.Name.ToLower().Equals(dRow[ExclServerName].ToString().ToLower())
                                   select c).ToList();

                    if (NameFnd.Count() > 0)
                    {
                        Facade.AddImportCMDBLogs(new ImportCMDBLogs()
                        {
                            IMPStatusId = ImportCMDBId,
                            ServerName = dRow[ExclServerName].ToString(),
                            IPAddress = dRow[ExclIpadd].ToString(),
                            Status = 0,
                            FailedReason = "Duplicate IPAddress",
                            CreatorId = LoggedInUserId
                        });

                        _logger.Info("Server Name " + dRow[ExclServerName].ToString() + " Already Present");
                        return false;
                    }
                }

                CurrentEntity.Name = string.IsNullOrEmpty(ExclServerName) ? "Server" + dRow[ExclIpadd].ToString() : dRow[ExclServerName].ToString();
                CurrentEntity.Name = Facade.IsExistServerByName(CurrentEntity.Name) ? CurrentEntity.Name + "_1" : CurrentEntity.Name;

                if (Facade.IsExistServerByName(CurrentEntity.Name))
                {
                    Facade.AddImportCMDBLogs(new ImportCMDBLogs()
                    {
                        IMPStatusId = ImportCMDBId,
                        ServerName = dRow[ExclServerName].ToString(),
                        IPAddress = dRow[ExclIpadd].ToString(),
                        Status = 0,
                        FailedReason = "Duplicate Server Name",
                        CreatorId = LoggedInUserId
                    });

                    _logger.Info("Server Name " + dRow[ExclServerName].ToString() + " Already Present");
                    return false;
                }

                var stId = (_facade.GetAllSites()).FirstOrDefault();


                CurrentEntity.SiteId = stId.Id;

                CurrentEntity.Type = "PRAppServer";
                CurrentEntity.ServerRole = 1;

                if (!string.IsNullOrEmpty(ExcelServerRole))
                {
                    if (dRow[ExcelServerRole].ToString().ToUpper().Contains("DATABASE"))
                    {
                        CurrentEntity.Type = "PRDBServer";
                        CurrentEntity.ServerRole = 2;
                    }
                    //else if (dRow[ExcelServerType].ToString().ToUpper().Contains("ESXI"))
                    //{
                    //    CurrentEntity.Type = "PRESXIServer";
                    //}
                    //else if (dRow[ExcelServerType].ToString().ToUpper().Contains("DSCLI"))
                    //{
                    //    CurrentEntity.Type = "DSCLIServer";
                    //}
                    //else if (dRow[ExcelServerType].ToString().ToUpper().Contains("HMC"))
                    //{
                    //    CurrentEntity.Type = "HMCServer";
                    //}
                    //else if (dRow[ExcelServerType].ToString().ToUpper().Contains("DNS"))
                    //{
                    //    CurrentEntity.Type = "DNSServer";
                    //}
                    //else if (dRow[ExcelServerType].ToString().ToUpper().Contains("SYMCLI"))
                    //{
                    //    CurrentEntity.Type = "SymCLIServer";
                    //}
                }
                CurrentEntity.IsVirtualGuestOS = 0;

                if (!string.IsNullOrEmpty(ExcelServerType))
                {
                    if (dRow[ExcelServerType].ToString().ToUpper().Contains("VIRTUAL"))
                        CurrentEntity.IsVirtualGuestOS = 1;
                }


                CurrentEntity.IPAddress = CryptographyHelper.Md5Encrypt(dRow[ExclIpadd].ToString());

                try
                {
                    CurrentEntity.Port = string.IsNullOrEmpty(ExclPort) ? 22 : Convert.ToInt32(dRow[ExclPort].ToString());
                }
                catch
                {
                    CurrentEntity.Port = 22;
                }

                if (!string.IsNullOrEmpty(ExcelOSType.Trim()))
                {
                    if (dRow[ExcelOSType].ToString().ToLower().Contains("linux"))
                        CurrentEntity.OSType = "Linux";
                    else if (dRow[ExcelOSType].ToString().ToLower().Contains("windows"))
                    {
                        if (dRow[ExcelOSType].ToString().ToLower().Contains("2008"))
                            CurrentEntity.OSType = "Windows2008";
                        else if (dRow[ExcelOSType].ToString().ToLower().Contains("2012"))
                            CurrentEntity.OSType = "Windows2012";
                        else if (dRow[ExcelOSType].ToString().ToLower().Contains("2003"))
                            CurrentEntity.OSType = "Windows2003";
                        else
                            CurrentEntity.OSType = "Windows2008";
                    }
                    else if (dRow[ExcelOSType].ToString().ToLower().Contains("aix"))
                        CurrentEntity.OSType = "AIX";
                    else if (dRow[ExcelOSType].ToString().ToLower().Contains("hpux"))
                        CurrentEntity.OSType = "HPUX";
                    else if (dRow[ExcelOSType].ToString().ToLower().Contains("solaris"))
                        CurrentEntity.OSType = "Solaris";
                    else
                        CurrentEntity.OSType = "Linux";
                }
                else
                    CurrentEntity.OSType = "Linux";


                //CurrentEntity.SSHUserName = CryptographyHelper.Md5Encrypt("admin");

                CurrentEntity.SSHUserName = string.IsNullOrEmpty(ExclUser) ? CryptographyHelper.Md5Encrypt("admin") : CryptographyHelper.Md5Encrypt(dRow[ExclUser].ToString());

                if (string.IsNullOrEmpty(CurrentEntity.SSHPassword))
                {
                    CurrentEntity.SSHPassword = CryptographyHelper.Md5Encrypt("admin");
                }

                //CurrentEntity.SSHPassword = string.IsNullOrEmpty(ExclPassword) ? CryptographyHelper.Md5Encrypt("admin@123") : CryptographyHelper.Md5Encrypt(dRow[ExclPassword].ToString());

                CurrentEntity.SudoUser = string.Empty;

                CurrentEntity.SudoPassword = string.Empty;

                CurrentEntity.DSIPAddress = CryptoHelperCsJava.Encrypt(dRow[ExclIpadd].ToString());

                CurrentEntity.DSSSHUserName = CryptoHelperCsJava.Encrypt(CurrentEntity.SSHUserName);

                if (SSOEnabled)
                {
                    CurrentEntity.SSOTypeId = SSOTypeId;
                    CurrentEntity.SSOEnabled = 1;
                    CurrentEntity.Safe = string.Empty;
                    CurrentEntity.Object = string.Empty;
                    CurrentEntity.Folder = string.Empty;
                    CurrentEntity.Reason = string.Empty;
                    CurrentEntity.SSOProfileId = SSOProfileId;
                    //CurrentEntity.DSSSHPassword = string.Empty;
                }
                else
                {
                    CurrentEntity.DSSSHPassword = CryptoHelperCsJava.Encrypt(CurrentEntity.SSHPassword);

                    CurrentEntity.SSOTypeId = 0;
                    CurrentEntity.SSOEnabled = 0;

                }

                CurrentEntity.IsPartOfCluster = false;

                CurrentEntity.DataStoreName = "";

                CurrentEntity.VmPath = "";

                CurrentEntity.Disk = "";

                CurrentEntity.EnableSudoAccess = 0;

                //if (ddlAuthenticationType.SelectedValue.Equals("1"))
                //{

                //    CurrentEntity.IsUseSshKeyAuth = Convert.ToInt32(ddlAuthenticationType.SelectedValue);
                //    // if (!ChkSSOEnable.Checked)
                //    // {
                //    CurrentEntity.SshKeyPath = string.IsNullOrEmpty(txtSSHKeyPath.Text) ? string.Empty : CryptographyHelper.Md5Encrypt(txtSSHKeyPath.Text);
                //    CurrentEntity.SshKeyPassword = string.IsNullOrEmpty(txtSSHKeyPassword.Text) ? sshkeypassword : CryptographyHelper.Md5Encrypt(txtSSHKeyPassword.Text);
                //    CurrentEntity.SSHPassword = string.IsNullOrEmpty(txtSSHPassword.Text) ? string.Empty : Utility.IsMD5EncryptedString(txtSSHPassword.Text) ? txtSSHPassword.Text : CryptographyHelper.Md5Encrypt(txtSSHPassword.Text);// string.Empty;

                //    // }

                //}
                ////}
                //else
                //{
                CurrentEntity.IsUseSshKeyAuth = CurrentEntity.OSType.ToLower().Contains("windows") ? 2 : string.IsNullOrEmpty(ExclShhKeyPath) ? 0 : 1;

                if (CurrentEntity.IsUseSshKeyAuth == 1)
                {
                    CurrentEntity.SshKeyPath = string.IsNullOrEmpty(ExclShhKeyPath) ? string.Empty : CryptographyHelper.Md5Encrypt(ExclShhKeyPath);
                    CurrentEntity.SshKeyPassword = string.IsNullOrEmpty(ExclShhKeyPassword) ? string.Empty : CryptographyHelper.Md5Encrypt(ExclShhKeyPassword);
                }

                //}

                //if (ChkSSOEnable.Checked)
                //{
                //    if (ddlSignOnType.SelectedValue == "2")
                //    {
                //        CurrentEntity.SSOEnabled = 1;
                //        CurrentEntity.SSOTypeId = Convert.ToInt32(ddlSignOnType.SelectedValue);
                //        CurrentEntity.Safe = txtSafe.Text;
                //        CurrentEntity.Object = txtObject.Text;
                //        CurrentEntity.Folder = txtFolder.Text;
                //        CurrentEntity.Reason = txtReason.Text;
                //        CurrentEntity.SSOProfileId = Convert.ToInt32(ddlSSOProfile.SelectedValue);
                //    }
                //    else if (ddlSignOnType.SelectedValue == "1")
                //    {
                //        //dsSSHPassword.Visible = true;
                //        CurrentEntity.SSOEnabled = 1;
                //        CurrentEntity.SSOTypeId = Convert.ToInt32(ddlSignOnType.SelectedValue);
                //        CurrentEntity.Safe = string.Empty;
                //        CurrentEntity.Object = string.Empty;
                //        CurrentEntity.Folder = string.Empty;
                //        CurrentEntity.Reason = string.Empty;
                //        CurrentEntity.SSOProfileId = Convert.ToInt32(ddlSSOProfile.SelectedValue);
                //    }
                //}

                CurrentEntity.ShellPrompt = "$";
                //CurrentEntity.LicenseKey = txtLicenceKey.Text;

                //string address1 = dRow[ExclIpadd].ToString();
                //string concot = LicensekeyHelper.PickANyNum(address1);

                //string con1 = LicensekeyHelper.Random1(address1);

                //string con2 = LicensekeyHelper.Random2(address1);

                //string con3 = LicensekeyHelper.Random3(address1);

                //string finalresult1 = concot + "-" + con1 + "-" + con2 + "-" + con3;

                //CurrentEntity.LicenseKey = finalresult1;

                CurrentEntity.HostName = string.IsNullOrEmpty(ExclHostName) ? "testHost" : dRow[ExclHostName].ToString();

                return true;
            }
            catch (Exception ex)
            {
                string msg = ex.InnerException != null ? ex.InnerException.ToString() : ex.Message;

                _logger.Error("Error Occured while Building Data for Server Name : " + ServerName + " Error : " + msg);

                Facade.AddImportCMDBLogs(new ImportCMDBLogs()
                {
                    IMPStatusId = ImportCMDBId,
                    ServerName = ServerName,
                    IPAddress = IPAddress,
                    Status = 0,
                    FailedReason = "Syntax Error",
                    CreatorId = LoggedInUserId
                });

                return false;
            }
        }

        protected void AsyncUpload_UploadedComplete(object sender, AjaxControlToolkit.AsyncFileUploadEventArgs e)
        {
            //updpnl.Update();
        }

        protected override void OnUnload(EventArgs e)
        {
            //tempFilePath = "";
            //if (lstMapping != null)
            //{
            //    lstMapping.Clear();
            //}
            // your code
        }


        protected void lvMap_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            if (lstMapping != null)
            {

                var lblExcelName = (lvMap.Items[e.ItemIndex].FindControl("lblExcelClmnName")) as Label;
                var lblMappedName = (lvMap.Items[e.ItemIndex].FindControl("lblMappedClmnName")) as Label;


                //lstMapping.Remove(new Mapping { ExcelColumnName = lblExcelName.Text, MappedColumnName = lblMappedName.Text });
                Mapping lpmatm = lstMapping.FirstOrDefault(a => a.ExcelColumnName == lblExcelName.Text && a.MappedColumnName == lblMappedName.Text);
                lstMapping.Remove(lpmatm);

                ListItem itemToAdd = ddlServerFiledMap.Items.FindByText(lblMappedName.Text);
                if (itemToAdd == null)
                {
                    ddlServerFiledMap.AddItem(lblMappedName.Text, lpmatm.MappedColumnId.ToString());
                }

                //ddlServerFiledMap.AddItem(lblMappedName.Text, lpmatm.MappedColumnId.ToString());

                lvMap.DataSource = lstMapping.Where(x => x.ExcelColumnName != null && x.MappedColumnName != null);
                lvMap.DataBind();

                if (lstMapping.Count == 0)
                    cnfrmExtndrBrowse.Enabled = false;

                ShowAuthenticationOptions(chkAuthenticationOption.Checked, ddlAuthenticationType.SelectedItem.Text);
                //updpnl.Update();
            }

        }

        protected void BrowseButton_Click(object sender, EventArgs e)
        {
            lstMapping.Clear();
            lvMap.DataSource = null;
            lvMap.DataBind();
            lstvw.DataSource = null;
            lstvw.DataBind();
            //tempFilePath = "";
            divMap.Attributes.Add("style", "display:none;");
            divAuthOptn.Attributes.Add("style", "display:none");
            divExcelData.Attributes.Add("style", "display:none");
        }

        protected void Button3_Click(object sender, EventArgs e)
        {
            lstMapping.Clear();
            lvMap.DataSource = null;
            lvMap.DataBind();
            lstvw.DataSource = null;
            lstvw.DataBind();
            divMap.Attributes.Add("style", "display:none;");
            divAuthOptn.Attributes.Add("style", "display:none");
            divExcelData.Attributes.Add("style", "display:none");
        }

        protected void ddlAuthenticationType_SelectedIndexChanged(object sender, EventArgs e)
        {
            ShowAuthenticationOptions(true, ddlAuthenticationType.SelectedItem.Text);
            txtsshpassword.Text = "";
            txtsshkey.Text = "";
        }

        private void ShowAuthenticationOptions(bool AuthCheck, string AuthType)
        {
            if (AuthCheck)
            {
                switch (AuthType)
                {
                    case "-- Select Type --":
                        divMap.Attributes.Add("style", "display: block");
                        selectauthenticationdrop.Attributes.Add("style", "display:inline-block");
                        divsshpassword.Attributes.Add("style", "display:none");
                        divsshkey.Attributes.Add("style", "display:none");
                        divsinglesignon.Attributes.Add("style", "display:none");
                        break;
                    case "SSH Password":
                        divMap.Attributes.Add("style", "display: block");
                        selectauthenticationdrop.Attributes.Add("style", "display:inline-block");
                        divsshpassword.Attributes.Add("style", "display:block");
                        divsshkey.Attributes.Add("style", "display:none");
                        divsinglesignon.Attributes.Add("style", "display:none");
                        rfvtxtsshkey.Enabled = false;
                        rfvssotype.Enabled = false;
                        rfvssoprofile.Enabled = false;
                        break;
                    case "SSH Key":
                        divMap.Attributes.Add("style", "display: block");
                        selectauthenticationdrop.Attributes.Add("style", "display:inline-block");
                        divsshpassword.Attributes.Add("style", "display:none");
                        divsshkey.Attributes.Add("style", "display:block");
                        divsinglesignon.Attributes.Add("style", "display:none");
                        rfvtxtSSHPassword.Enabled = false;
                        rfvssotype.Enabled = false;
                        rfvssoprofile.Enabled = false;
                        break;
                    case "Single Sign On":
                        divMap.Attributes.Add("style", "display: block");
                        Utility.PopulateSingleSignOnType(ddlSignOnType, true);
                        selectauthenticationdrop.Attributes.Add("style", "display:inline-block");
                        divsshpassword.Attributes.Add("style", "display:none");
                        divsshkey.Attributes.Add("style", "display:none");
                        divsinglesignon.Attributes.Add("style", "display:block");
                        rfvtxtsshkey.Enabled = false;
                        rfvtxtSSHPassword.Enabled = false;
                        break;
                }
            }
            else
            {
                selectauthenticationdrop.Attributes.Add("style", "display:none");
                divsshpassword.Attributes.Add("style", "display:none");
                divsshkey.Attributes.Add("style", "display:none");
                divsinglesignon.Attributes.Add("style", "display:none");

            }
        }

        protected void ddlSignOnType_SelectedIndexChanged(object sender, EventArgs e)
        {
            Utility.PopulateSSOProfile(ddlProfile, true, Convert.ToInt32(ddlSignOnType.SelectedValue));
        }

        protected void btnAuthOptionMap_Click(object sender, EventArgs e)
        {
            lblError.Text = "";
            //divAuthOptn.Attributes.Add("style", "display:none");
            //ShowAuthenticationOptions(chkAuthenticationOption.Checked, ddlAuthenticationType.SelectedIndex);

            string AuthValue = "";
            string SSOType = "";
            int SSOTypeId = 0, profileId = 0;

            switch (ddlAuthenticationType.SelectedItem.Text)
            {
                case "SSH Password":
                    AuthValue = txtsshpassword.Text;
                    break;
                case "SSH Key":
                    AuthValue = txtsshkey.Text;
                    break;
                case "Single Sign On":
                    SSOTypeId = ddlSignOnType.SelectedIndex;
                    SSOType = ddlSignOnType.SelectedItem.Text;
                    profileId = string.IsNullOrEmpty(ddlProfile.SelectedValue) ? 0 : Convert.ToInt32(ddlProfile.SelectedValue);
                    AuthValue = ddlProfile.SelectedItem.Text;
                    break;
            }

            if (lstMapping.Select(x => x.AuthColumnName == ddlAuthenticationType.SelectedItem.Text).FirstOrDefault())
            {
                lblError.Text = ddlAuthenticationType.SelectedItem.Text + " Already Mapped";
                return;
            }

            Mapping lpmatm = lstMapping.FirstOrDefault(a => a.AuthColumnName == ddlAuthenticationType.SelectedItem.Text && a.AuthValue == AuthValue);

            if (lpmatm == null)
            {
                lstMapping.Add(new Mapping
                {
                    AuthTypeId = ddlAuthenticationType.SelectedIndex,
                    AuthColumnName = ddlAuthenticationType.SelectedItem.Text,
                    AuthValue = AuthValue,
                    SSOType = SSOType,
                    SSOTypeId = SSOTypeId,
                    ProfileId = profileId
                });

                lvAuthOptnMap.DataSource = lstMapping.Where(x => x.AuthColumnName != null && x.AuthValue != null);
                lvAuthOptnMap.DataBind();

                DisabledAuthTypeDivs(ddlAuthenticationType.SelectedItem.Text);

                ListItem itemToRemove = ddlAuthenticationType.Items.FindByText(ddlAuthenticationType.SelectedItem.Text);
                if (itemToRemove != null)
                {
                    ddlAuthenticationType.Items.Remove(itemToRemove);
                }

                //ddlAuthenticationType.RemoveItem(ddlAuthenticationType.SelectedItem.Text, ddlAuthenticationType.SelectedIndex.ToString());


                divAuthOptnMap.Attributes.Add("style", "display: block");

            }
            else
            {
                lblError.Text = ddlAuthenticationType.SelectedItem.Text + " Already Mapped";
            }
        }

        private void DisabledAuthTypeDivs(string AuthType)
        {
            switch (AuthType)
            {
                case "SSH Password":
                    divsshpassword.Attributes.Add("style", "display:none");
                    break;
                case "SSH Key":
                    divsshkey.Attributes.Add("style", "display:none");
                    break;
                case "Single Sign On":
                    divsinglesignon.Attributes.Add("style", "display:none");
                    break;
            }
        }

        protected void lvAuthOptnMap_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            if (lstMapping != null)
            {

                var lblAuthType = (lvAuthOptnMap.Items[e.ItemIndex].FindControl("lblAuthenticationType")) as Label;
                var lblAuthValue = (lvAuthOptnMap.Items[e.ItemIndex].FindControl("lblAuthenticationValue")) as Label;
                var lblPassword = (lvAuthOptnMap.Items[e.ItemIndex].FindControl("lblPassword")) as Label;

                string Value = lblAuthValue.Text;

                if (lblAuthType.Text.Trim().Equals("SSH Password"))
                {
                    Value = lblPassword.Text;
                }

                Mapping lpmatm = lstMapping.FirstOrDefault(a => a.AuthColumnName == lblAuthType.Text && a.AuthValue == Value);
                lstMapping.Remove(lpmatm);

                ListItem itemToAdd = ddlAuthenticationType.Items.FindByText(lpmatm.AuthColumnName);
                if (itemToAdd == null)
                {
                    ddlAuthenticationType.AddItem(lpmatm.AuthColumnName, lpmatm.AuthTypeId.ToString());
                }


                //ddlAuthenticationType.AddItem(lpmatm.AuthColumnName, lpmatm.AuthTypeId.ToString());

                lvAuthOptnMap.DataSource = lstMapping.Where(x => x.AuthColumnName != null && x.AuthValue != null);
                lvAuthOptnMap.DataBind();

                //updpnl.Update();
            }
        }

        //protected void lvAuthOptnMap_ItemEditing(object sender, ListViewEditEventArgs e)
        //{
        //    if (lstMapping != null)
        //    {
        //        var lblAuthType = (lvAuthOptnMap.Items[e.NewEditIndex].FindControl("lblAuthenticationType")) as Label;
        //        var lblAuthValue = (lvAuthOptnMap.Items[e.NewEditIndex].FindControl("lblAuthenticationValue")) as Label;
        //        var lblSSOType = (lvAuthOptnMap.Items[e.NewEditIndex].FindControl("lblSSOType")) as Label;

        //        ddlAuthenticationType.SelectedItem.Text = lblAuthType.Text;

        //        ddlSignOnType.SelectedItem.Text = lblAuthValue.Text;
        //    }
        //}

        protected void lvAuthOptnMap_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var lblAuthType = e.Item.FindControl("lblAuthenticationType") as Label;
            var lblAuthValue = e.Item.FindControl("lblAuthenticationValue") as Label;

            if (lblAuthType.Text.Trim().Equals("SSH Password"))
            {
                lblAuthValue.Text = "●●●●●●●●●●●●●●";
            }

        }

        protected void btnOk_Click(object sender, EventArgs e)
        {
            modelbg.Visible = false;
            pnlImportSuccess.Visible = false;

            Response.Redirect("~/Component/ImportCMDB.aspx");
            //testStrrr = "";
            //lstMapping.Clear();
            //lstvw.DataSource = null;
            //lstvw.DataBind();
            //lvMap.DataSource = null;
            //lvMap.DataBind();
            //divMap.Attributes.Add("style", "display:none;");
            //divAuthOptn.Attributes.Add("style", "display:none");
            //divExcelData.Attributes.Add("style", "display:none");
        }

        protected void lkbtnSMS_Click(object sender, EventArgs e)
        {
            modelbg.Visible = false;
            pnlImportSuccess.Visible = false;

            Response.Redirect("~/Component/ImportCMDB.aspx");

            //testStrrr = "";
            //lstMapping.Clear();
            //lstvw.DataSource = null;
            //lstvw.DataBind();
            //lvMap.DataSource = null;
            //lvMap.DataBind();
            //divMap.Attributes.Add("style", "display:none;");
            //divAuthOptn.Attributes.Add("style", "display:none");
            //divExcelData.Attributes.Add("style", "display:none");
        }
        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
    }

    public class Mapping
    {
        public string ExcelColumnName { get; set; }
        public int MappedColumnId { get; set; }
        public string MappedColumnName { get; set; }
        public string AuthColumnName { get; set; }
        public string SSOType { get; set; }
        public string AuthValue { get; set; }
        public int AuthTypeId { get; set; }
        public int SSOTypeId { get; set; }
        public int ProfileId { get; set; }
    }


}