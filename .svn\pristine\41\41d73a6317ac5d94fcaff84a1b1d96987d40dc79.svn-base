﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.Common;
using CP.ExceptionHandler;
using System.Linq;
using System.Text;
using System.Data;

namespace CP.UI.Admin
{
    ///<summary>
    //! This is used for Redirecting Monitor Application Type Infra Object Details
    /// <author> <PERSON><PERSON>h Thakker - 29-04-2014 </author>
    /// </summary>
    public partial class InfraMonitorApplication : InfraObjectsBasePage
    {
        public override void PrepareView()
        {
            Utility.SelectMenu(Master, "Module1");
            if (CurrentInfraObjectId > 0)
            {
                PopulateInfraObjectAppInfo();

                PopulateInfraObjectAppReplicationInfo();

                PopulateServiceInfo();

                PopulateMonitoringService();
            }
        }

        /// <summary>
        ///  Populates Application Services
        /// </summary>
        /// <author> <PERSON><PERSON><PERSON> - 29-04-2014 </author>
        private void PopulateServiceInfo()
        {
            // check if Current infra has Replication DRReady
            if (CurrentInfraObject.DRReady)
            {
                // Get Application Services
                IList<ApplicationService> applicationServices =
                    Facade.GetApplicationServicesByInfraObjectId(CurrentInfraObject.Id);

                if (applicationServices != null && applicationServices.Count > 0)
                {
                    lvAppdetails.DataSource = applicationServices;

                    lvAppdetails.DataBind();
                }
            }
        }

        private void PopulateMonitoringService()
        {
            // check if Current infra has Replication DRReady
            if (CurrentInfraObject != null)
            {
                if (CurrentInfraObject.Id > 0)
                {
                    var monServices = Facade.GetMonitorServiceStatusLogsByInfraObjectId(CurrentInfraObject.Id);

                    if (monServices != null)
                    {
                        if (monServices.Count > 0)
                        {
                            Test.Visible = true;
                            lvMonitoringService.Visible = true;
                            lvMonitoringService.DataSource = monServices;
                            lvMonitoringService.DataBind();
                        }
                    }
                }
            }
        }

        /// <summary>
        ///  It Populates Replication Info for the current infraobject
        /// </summary>
        /// <author> Kuntesh Thakker - 29-04-2014 </author>
        private void PopulateInfraObjectAppReplicationInfo()
        {
            PopulateMonitoringService();
            if (CurrentInfraObject.DRReady)
            {
                // Recovery Type Compared to AppInfra Replication Type
                switch (CurrentInfraObject.RecoveryType)
                {
                    case (int)ReplicationType.IBMGlobalMirror:
                        lblReplicationName.Text = "Replication IBM Global Mirror";
                        //servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "table");
                        divRecoverPointInner.Visible = false;
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        GetGlolbalMirorStatus();
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        break;

                    case (int)ReplicationType.OracleDataGuard:
                        lblReplicationName.Text = "Replication Oracle Data Guard";
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "table");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");

                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        divRecoverPointInner.Visible = false;
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        break;

                    case (int)ReplicationType.OracleWithDataSync:
                    case (int)ReplicationType.DataSync:
                        lblFastCopy.Text = "Replication DataSync";

                        //widget4.Visible = false;

                        //tblCommonComponent.Visible = false;
                        //widget2.Visible = true;
                        //widget2.Style.Add("display", "block");
                        //Test.Visible = true;

                        // widget4.Visible = true;
                        //widget4.Style.Add("display", "block");

                        tblCommonComponent.Visible = true;
                        //widget2.Visible = true;
                        //widget2.Style.Add("display", "block");
                        Test.Visible = true;
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");

                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        //tblFastCopy.Style.Add("display", "table");
                        divFastCopy.Visible = true;
                        divFastCopy.Style.Add("display", "block");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        // tblCommonComponent.Visible = true;
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        GetFastcopyStatus();
                        break;

                    case (int)ReplicationType.EMCSRDF:
                        lblReplicationName.Text = "Replication EMC SRDF";
                        lblReplicationType.Text = "EMC SRDF";
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "table");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        divRecoverPointInner.Visible = false;
                        widget2.Style.Add("display", "block");
                        widget2.Visible = true;
                        GetEmcsrdfStatus();
                        break;

                    case (int)ReplicationType.RecoverPoint:
                        lblReplicationName.Text = "Replication Recover Point";
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                       // divPstatemonitor.Style.Add("display", "block");
                       // divPStatisticMonitor.Style.Add("display", "block");
                        divMimix.Visible = false;
                       // divRecoverPointInner.Visible = true;

                        // RecoveryPointMulti Monitoring 18.05.2018
                        Session["InfraRepliiDRecoveryPointMulti"] = CurrentInfraObject.PRReplicationId;
                        Session["InfraiDRecoveryPointMulti"] = CurrentInfraObject.Id;
                        divRecoveryPointMulti.Visible = true;

                        UserControl RecoveryPointMultiMonitoringDetails = new UserControl();
                        RecoveryPointMultiMonitoringDetails = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RecoveryPointMulti);



                       // GetRecoveryPointStatus();
                        break;

                    case (int)ReplicationType.RecoverPointOracleFULLDB:
                        lblReplicationName.Text = "Replication Recover Point";
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "block");
                        divPStatisticMonitor.Style.Add("display", "block");
                        divMimix.Visible = false;
                      //  divRecoverPointInner.Visible = true;
                        divRecoveryPointMulti.Visible = true;

                        GetRecoveryPointStatus();
                        break;

                    case (int)ReplicationType.RecoverPointMSSQLFULLDB:
                        lblReplicationName.Text = "Replication Recover Point";
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "block");
                        divPStatisticMonitor.Style.Add("display", "block");
                        divMimix.Visible = false;
                       // divRecoverPointInner.Visible = true;
                        divRecoveryPointMulti.Visible = true;

                        GetRecoveryPointStatus();
                        break;

                    case (int)ReplicationType.EC2S3DataSync:
                        lblReplicationName.Text = "Replication EC2S3Datasync";
                        tblCommonComponent.Visible = false;
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "table");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        GetEc2S3datasyncReplicationDetails(CurrentInfraObject.Id);
                        break;

                    case (int)ReplicationType.ApplicationDoubleTake:
                        tblCommonComponent.Visible = false;
                        lblReplicationName.Text = "Replication ApplicationDoubleTake";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        widget4.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        PopulateApplicationDoubletekReplicationDetails(CurrentInfraObject.Id);
                        break;
                    case (int)ReplicationType.MIMIX:
                        tblCommonComponent.Visible = false;
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        //tblMimixDatalagMonitor.Style.Add("display", "table");
                        //tblMimixHealthReplication.Style.Add("display", "table");
                        tblMimixAlertsMonitor.Style.Add("display", "table");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        //tblMimixManager.Style.Add("display", "table");
                        //tblMimixAvilabity.Style.Add("display", "table");
                        widget4.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        widget2.Visible = false;
                        divMimix.Visible = true;
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        PopulateMIMIXManagerDetails(CurrentInfraObject.Id);
                        PopulateMIMIXAvailabilityDetails(CurrentInfraObject.Id);
                        PopulateMIMIXAlertsDetails(CurrentInfraObject.Id);
                        PopulateMIMIXHealthDetails(CurrentInfraObject.Id);
                        PopulateMIMIXDatalagDetails(CurrentInfraObject.Id);
                        break;
                    case (int)ReplicationType.NetAppSnapMirror:
                        tblCommonComponent.Visible = false;
                        lblReplicationName.Text = "Replication SnapMirror";
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        GetSnapMirrorReplicationDetails(CurrentInfraObject.Id);
                     
                        break;

                    case (int)ReplicationType.AppHitachiUr:
                        lblReplicationName.Text = "Replication ApplicationHitachiUR";
                        tblCommonComponent.Visible = true;
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        divHitachiURReplication.Style.Add("display", "block");
                        tblHitachiURReplication.Style.Add("display", "table");
                        lstHitachiURDeviceMonitoring.Style.Add("display", "table");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "block");
                        widget2.Style.Add("display", "block");
                        divPstatemonitorHead.Style.Add("display", "none");
                        tblPstateMoniter.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        PopulateHitachiUROracleDBReplicationDetails(CurrentInfraObject.Id);
                        break;

                    case (int)ReplicationType.ApplicationXIVMIRROR:
                        lblReplicationName.Text = "Application-XIV Mirror";
                        tblxivmirrormonitoring.Style.Add("display", "table");
                        tblSnapComponent.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblGlobalMirror.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasynccompMonitor.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblSnapMirriorReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        lstHitachiURDeviceMonitoring.Style.Add("display", "none");
                        tblCommonComponent.Visible = true;
                        servicestatus.Visible = false;
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        getxivdetails();
                        break;

                    case (int)ReplicationType.SRMVMware:
                        tblCommonComponent.Visible = false;
                        lblReplicationName.Text = "Protection Group & Recovery Plan Monitoring";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        //tblxivmirrormonitoring.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "table");
                        //tblSRMVMwareReplication.Style.Add("display", "div");
                        tblSRMVMwareReplication.Style.Add("display", "table");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "block");
                        divPStatisticMonitor.Style.Add("display", "none");
                        //tblPstateMoniterRpt.Style.Add("display", "none");
                        divPstatemonitorHead.Visible = false;
                        tblPstateMoniter.Visible = false;
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        widget2.Style.Add("display", "block");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        PopulateSRMVMwareDetails(CurrentInfraObject.Id);
                        break;



                    case (int)ReplicationType.ApplicationeBDR:
                        tblCommonComponent.Visible = true;
                        lblReplicationName.Text = "eBDR";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        //tblxivmirrormonitoring.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "div");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "table");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        PopulateeBDRReplicationDetails(CurrentInfraObject.Id);

                        break;

                    case (int)ReplicationType.DRNET:
                        tblCommonComponent.Visible = false;
                        lblReplicationName.Text = "Replication Base24";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        //tblxivmirrormonitoring.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "div");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        //tblBase24Replication.Style.Add("display", "table");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        PopulateApplicationBase24ReplicationDetails(CurrentInfraObject.Id);
                        break;

                    case (int)ReplicationType.TPCR:
                        tblCommonComponent.Visible = false;
                        lblReplicationName.Text = "TPCR";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        tblTPRCRepliMonitoring.Style.Add("display", "table");
                        tblTPRCMonitoring.Style.Add("display", "table");
                        tblSnapComponent.Style.Add("display", "none");
                        tblSnapMirriorReplication.Style.Add("display", "none");
                        tblEc2S3datasynccompMonitor.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        GetTPRCMonitorDetails(CurrentInfraObject.Id);
                        break;

                    case (int)ReplicationType.HP3ParwithApplication:
                        //tblCommonComponent.Visible = false;
                        lblreplicationNm.Text = "Replication HP3Par";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        divMimix.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        tblTPRCRepliMonitoring.Style.Add("display", "none");
                        tblTPRCMonitoring.Style.Add("display", "none");
                        tblSnapComponent.Style.Add("display", "none");
                        tblSnapMirriorReplication.Style.Add("display", "none");
                        tblEc2S3datasynccompMonitor.Style.Add("display", "none");
                        //un hide 
                        divHP3PARStorage.Visible = true;
                        tblHP3PARStorage.Style.Add("display", "table");
                        divHP3PARVirtualMonitoring.Visible = true;
                        GetHP3PARDeatils(CurrentInfraObject.Id);
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        break;

                    case (int)ReplicationType.EMCSRDFSGAPPLICATION:
                        // lblReplicationName.Text = "Replication EMCSRDFSG";
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");

                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;

                        divEmcsrdfSGLogs.Style.Add("display", "block");

                        Session["EmcsrdfSGLogsInfraid"] = CurrentInfraObject.Id; ;

                        UserControl EMCSRDFSGMonitoring = new UserControl();

                        EMCSRDFSGMonitoring = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSGLogs);

                        UserControl VeritasClusterDetail = new UserControl();
                        if (CurrentInfraObject.IsCluster == 1)
                        {
                            Session["VeritasClusterInfraid"] = CurrentInfraObject.Id;

                            VeritasClusterDetail = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.VeritasClusterDetails);
                        }
                        else
                            VeritasClusterDetail.Dispose();
                        break;

                    case (int)ReplicationType.EMCSRDFSTARAPPLICATION:
                        tblCommonComponent.Visible = true;
                        //lblReplicationName.Text = "EMCSRDFSTAR Application";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        tblTPRCRepliMonitoring.Style.Add("display", "none");
                        tblTPRCMonitoring.Style.Add("display", "none");
                        tblSnapComponent.Style.Add("display", "none");
                        tblSnapMirriorReplication.Style.Add("display", "none");
                        tblEc2S3datasynccompMonitor.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = true;
                        Session["GroupEmcStarId"] = CurrentInfraObjectId;
                        UserControl EMCSRDFStarMonitoring = new UserControl();
                        EMCSRDFStarMonitoring = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcsrdfSTARMonitor);

                        UserControl VeritasClusterDetails = new UserControl();
                        if (CurrentInfraObject.IsCluster == 1)
                        {
                            Session["VeritasClusterInfraid"] = CurrentInfraObject.Id;

                            VeritasClusterDetails = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.VeritasClusterDetails);
                        }
                        else
                            VeritasClusterDetails.Dispose();

                        break;

                    case (int)ReplicationType.SVCGlobalMirrorORMetroMirror:
                        tblCommonComponent.Visible = true;
                        lblReplicationName.Text = "SVC - GlobalMirror OR Metro Mirror";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        tblTPRCRepliMonitoring.Style.Add("display", "none");
                        tblTPRCMonitoring.Style.Add("display", "none");
                        tblSnapComponent.Style.Add("display", "none");
                        tblSnapMirriorReplication.Style.Add("display", "none");
                        tblEc2S3datasynccompMonitor.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        tblSVCReplicationMonitoringSummaryLayout.Style.Add("display", "table");
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        PopulateSVCReplicationDetails(CurrentInfraObject.Id);
                        break;

                    case (int)ReplicationType.Undefined:
                        widget2.Visible = false;
                        widget3.Visible = false;
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareComponent.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        tblTPRCRepliMonitoring.Style.Add("display", "none");
                        tblTPRCMonitoring.Style.Add("display", "none");
                        tblSnapComponent.Style.Add("display", "none");
                        tblSnapMirriorReplication.Style.Add("display", "none");
                        tblEc2S3datasynccompMonitor.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        break;

                    case (int)ReplicationType.ZFSApplication:
                        tblCommonComponent.Visible = false;
                        lblReplicationName.Text = "Replication ZFS";
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divMimix.Visible = false;
                        divRecoverPointInner.Visible = false;
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        //ZFSReplication.Style.Add("display", "table");
                        widget5.Visible = true;
                        //GetSnapMirrorReplicationDetails(CurrentInfraObject.Id);

                        // Li2.Visible = false;
                        //Li3.Visible = false;


                        Session["ZFSInfraid"] = CurrentInfraObject.Id;

                        UserControl ZFSReplicationMonitor_UserControl = new UserControl();

                        ZFSReplicationMonitor_UserControl = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.ZFSReplicationMonitor);

                        var infraodg = Facade.GetInfraObjectById(CurrentInfraObject.Id);

                        int isclusterodg = infraodg.IsCluster;

                        if (isclusterodg != 0)
                        {
                            string clustername = infraodg.ClusterName;

                            if (clustername.ToLower().Contains("hacmp"))
                            {

                                Li2.Visible = true;

                                UserControl HACMPPowerHAClusterDetails = new UserControl();

                                HACMPPowerHAClusterDetails = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.HACMPPowerHACluster);

                            }
                        }

                        break;


                    case (int)ReplicationType.EMCISilon:
                        tblCommonComponent.Visible = true;
                        lblServerDetails.Text = "Server Details";
                        lblServerDetails.Visible = true;
                        //lblreplicationNm.Text = "Replication EmcISilon";
                        servicestatus.Visible = false;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        divMimix.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = false;
                        tblTPRCRepliMonitoring.Style.Add("display", "none");
                        tblTPRCMonitoring.Style.Add("display", "none");
                        tblSnapComponent.Style.Add("display", "none");
                        tblSnapMirriorReplication.Style.Add("display", "none");
                        tblEc2S3datasynccompMonitor.Style.Add("display", "none");
                        // tblHP3PARStorage.Style.Add("display", "table");
                        //divHP3PARStorage.Visible = false;
                        // divHP3PARVirtualMonitoring.Visible = false;
                        //tblEmcISilonDetailsMonitoring.Style.Add("display", "table");
                        divEmcISilonMonitoring.Visible = true;
                        lblEmcISilonnm.Text = "Replication Details";
                        lblEmcISilonnm.Visible = true;
                        GetEmcISilonDeatils(CurrentInfraObject.Id);
                        divEmcsrdfStar.Visible = false;
                        break;

                    case (int)ReplicationType.EmcMirrorViewApp:
                        tblCommonComponent.Visible = false;
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        //tblMimixDatalagMonitor.Style.Add("display", "table");
                        //tblMimixHealthReplication.Style.Add("display", "table");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        //tblMimixManager.Style.Add("display", "table");
                        //tblMimixAvilabity.Style.Add("display", "table");
                        widget4.Visible = true;
                        Test.Visible = false;
                        widget3.Visible = false;
                        widget2.Visible = false;
                        divMimix.Visible = false;
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        lblEmcMirrorView.Text = "Group Details Monitoring";
                        lblEmcMirrorView.CssClass = "heading-title";
                        dvEmcMirrorViewReplicationDetails.Visible = true;
                        emcMirrorDiv.Visible = true;
                        widget4body.Visible = false;
                        //emcMirrorDiv.Style.Add("display", "block");
                        SetEmcMirrorViewReplicationInfo(CurrentInfraObject.Id);
                        lblEmcmvmirrornm.Text = "Mirror Details Monitoring";
                        lblEmcmvmirrornm.CssClass = "heading-title";
                        GetEmc_Mv_Mirror_Deatils(CurrentInfraObject.Id);
                        break;

                    case (int)ReplicationType.RoboCopy:
                        tblCommonComponent.Visible = false;
                        //   lblReplicationName.Text = "RoboCopy";
                        servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");

                        widget4body.Attributes.Add("class", "widget-body innerAll inner-2x no-pad");
                        divMimix.Visible = false;
                        Test.Visible = false;
                        widget3.Visible = true;
                        widget2.Visible = false;
                        tblTPRCRepliMonitoring.Style.Add("display", "none");
                        tblTPRCMonitoring.Style.Add("display", "none");
                        tblSnapComponent.Style.Add("display", "none");
                        tblSnapMirriorReplication.Style.Add("display", "none");
                        tblEc2S3datasynccompMonitor.Style.Add("display", "none");
                        divrobocopydeatail.Style.Add("display", "block");   
                        tblrobocpymonitor.Style.Add("display", "table");
                       
                        GetRoboCopyMonitorDetails(CurrentInfraObject.Id);
                        break;

                    case (int)ReplicationType.EmcUnityApp:
                        widget4.Visible = true;
                        widget4.Style.Add("display", "block");
                        tblCommonComponent.Visible = true;
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "div");
                        tblHitachiURReplication.Style.Add("display", "none");
                        //tblMimixDatalagMonitor.Style.Add("display", "table");
                        //tblMimixHealthReplication.Style.Add("display", "table");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        divPstatemonitor.Style.Add("display", "none");
                        divPStatisticMonitor.Style.Add("display", "none");
                        //tblMimixManager.Style.Add("display", "table");
                        //tblMimixAvilabity.Style.Add("display", "table");

                        Test.Visible = false;
                        widget3.Visible = false;
                        widget2.Visible = false;
                        divMimix.Visible = false;
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        //lblEmcMirrorView.Text = "Group Details Monitoring";
                        //lblEmcMirrorView.CssClass = "heading-title";
                        dvEmcMirrorViewReplicationDetails.Visible = false;
                        dvEmcMirrorViewReplicationDetails.Visible = false;
                        divEmcmvmirrorMonitoring_scroll.Visible = false;
                        emcMirrorDiv.Visible = false;
                        //SetEmcMirrorViewReplicationInfo(CurrentInfraObject.Id);
                        //lblEmcmvmirrornm.Text = "Mirror Details Monitoring";
                        //lblEmcmvmirrornm.CssClass = "heading-title";
                        //GetEmc_Mv_Mirror_Deatils(CurrentInfraObject.Id);

                        //divEmcUnity.Visible = true;
                        //tblEmcUnityReplicarion.Style.Add("display", "table");
                        //emcMirrorDiv.Visible = false;
                        //lblddlPolicynm.Visible = true;
                        //ddlEmcUnitySessionName.Visible = true;
                        //EmcUnitySessionNameList();                   
                        //GetEmcUnityDeatilsMonitoring(CurrentInfraObject.Id);
                        dvEmcUnity.Visible = true;
                        Session["EMCUnityInfraId"] = CurrentInfraObjectId;
                        UserControl EMCUnityMonitoring = new UserControl();
                        EMCUnityMonitoring = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.EmcUnityMonitor);

                        break;

                    case (int)ReplicationType.RSync:
                        lblReplicationName.Text = "Rsync Monitoring";
                        //servicestatus.Visible = true;
                        tblGlobalMirror.Style.Add("display", "none");
                        tblFastCopy.Style.Add("display", "none");
                        tblOracleDataGuard.Style.Add("display", "none");
                        tblEMCSRDF.Style.Add("display", "none");
                        tblEc2S3datasyncReplication.Style.Add("display", "none");
                        tblMSSQLDoubletek.Style.Add("display", "none");
                        tblMimixAlertsMonitor.Style.Add("display", "none");
                        divHitachiURReplication.Style.Add("display", "none");
                        tblHitachiURReplication.Style.Add("display", "none");
                        tblxivmirrormonitoring.Style.Add("display", "none");
                        divEmcsrdfStar.Visible = false;
                        divEmcsrdfSGLogs.Style.Add("display", "none");
                        tblSRMVMwareComponent.Style.Add("display", "none");
                        tblSRMVMwareReplication.Style.Add("display", "none");
                        tbleBDRReplicationDetailsMoni.Style.Add("display", "none");
                        // divPstatemonitor.Style.Add("display", "block");
                        // divPStatisticMonitor.Style.Add("display", "block");
                        divMimix.Visible = false;
                        Div7.Visible = false;
                        // divRecoverPointInner.Visible = true;
                        tblCommonComponent.Style.Add("display", "table");
                        //contentmonitor.Visible = false;
                        // RecoveryPointMulti Monitoring 18.05.2018
                        Session["InfraRepliiDRSync"] = CurrentInfraObject.PRReplicationId;
                        Session["InfraiDRSync"] = CurrentInfraObject.Id;
                        divRecoveryPointMulti.Visible = false;
                        dvRSync.Visible = true;
                        //dvContent2.Visible = false;
                        UserControl RRSyncMonitoringDetails = new UserControl();
                        RRSyncMonitoringDetails = (UserControl)Page.LoadControl(Constants.UrlConstants.Urls.Controls.RSyncMonitor);
                        break;
                }

            }
            else
            {
                tblGlobalMirror.Visible = false;
                tblFastCopy.Visible = false;
                tblOracleDataGuard.Visible = false;
                tblEMCSRDF.Visible = false;
                tblEc2S3datasyncReplication.Visible = false;
                tblEc2S3datasynccompMonitor.Visible = false;
                tblMSSQLDoubletek.Visible = false;
                servicestatus.Visible = false;
                tblMimixAlertsMonitor.Visible = false;
                divMimix.Visible = false;
                divRecoverPointInner.Visible = false;
                tblxivmirrormonitoring.Visible = false;
                tblSRMVMwareComponent.Visible = false;
                tblSRMVMwareReplication.Visible = false;
                divHitachiURReplication.Visible = false;
                tblHitachiURReplication.Visible = false;
                tbleBDRReplicationDetailsMoni.Visible = false;
                divPstatemonitor.Visible = false;
                divPStatisticMonitor.Visible = false;
                tblTPRCRepliMonitoring.Visible = false;
                tblTPRCMonitoring.Visible = false;
                tblSnapComponent.Visible = false;
                tblSnapMirriorReplication.Visible = false;
            }
        }


        private void GetHP3PARDeatils(int infraObjectId)
        {

            var infraobjects = Facade.GetInfraObjectById(infraObjectId);
            var MSSqlRepli = Facade.GetHP3PARMonitorByInfraObjectId(infraObjectId);
            //lblPR3PARStorageName.Text = "hp3par details";
            //lblPR3PARStorageName.Visible = true;

            var Repliserver = Facade.GetHP3PARStoragebyReplicationId(CurrentInfraObject.PRReplicationId);
            var serverPR = Facade.GetServerById(Repliserver.PrServerId);
            var serverDR = Facade.GetServerById(Repliserver.DrServerId);

            try
            {
                if (CurrentInfraObject != null)
                {
                    var prserver = Facade.GetServerById(CurrentInfraObject.PRServerId);
                    var drserver = Facade.GetServerById(CurrentInfraObject.DRServerId);
                    if (prserver != null)
                    {

                        if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                        {

                            lblHP3PARPRIP.Text = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                            if (drserver.Status == ServerStatus.Up)
                                prspan.CssClass = "health-up";
                            else if (drserver.Status == ServerStatus.Down)
                            {
                                prspan.CssClass = "health-down";
                            }
                            else
                                prspan.CssClass = "fatal";


                            if (serverPR != null)
                            {

                                lblHP3PARDRIP.Text = CryptographyHelper.Md5Decrypt(serverPR.IPAddress);
                                if (serverPR.Status == ServerStatus.Up)
                                    drspan.CssClass = "health-up";
                                else if (serverPR.Status == ServerStatus.Down)
                                {
                                    drspan.CssClass = "health-down";
                                }
                                else
                                    drspan.CssClass = "fatal";



                            }


                        }
                        else
                        {

                            lblHP3PARPRIP.Text = CryptographyHelper.Md5Decrypt(serverPR.IPAddress);
                            if (serverPR.Status == ServerStatus.Up)
                                prspan.CssClass = "health-up";
                            else if (serverPR.Status == ServerStatus.Down)
                            {
                                prspan.CssClass = "health-down";
                            }
                            else
                                prspan.CssClass = "fatal";


                            lblHP3PARDRIP.Text = CryptographyHelper.Md5Decrypt(serverDR.IPAddress);
                            if (serverDR.Status == ServerStatus.Up)
                                drspan.CssClass = "health-up";
                            else if (serverDR.Status == ServerStatus.Down)
                            {
                                drspan.CssClass = "health-down";
                            }
                            else
                                drspan.CssClass = "fatal";


                        }

                    }


                    var prdataabase = Facade.GetDatabaseSqlByDatabaseBaseId(CurrentInfraObject.PRDatabaseId);
                    if (prdataabase != null)
                    {
                        //CurrentComponentInfo.PRDatabaseName = prdataabase.DatabaseSID;
                    }
                    var drdataabase = Facade.GetDatabaseSqlByDatabaseBaseId(CurrentInfraObject.DRDatabaseId);
                    if (drdataabase != null)
                    {
                        //CurrentComponentInfo.DRDatabaseName = drdataabase.DatabaseSID;
                    }
                }


            }
            catch (Exception)
            {

            }



            if (MSSqlRepli != null)
            {
                lblHP3PARPRIP.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.PRStorageIPAddress)) ? "NA" : MSSqlRepli.PRStorageIPAddress;
                lblHP3PARDRIP.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRStorageIPAddress)) ? "NA" : MSSqlRepli.DRStorageIPAddress;

                lblPR3PARStorageName.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.PRStorageName)) ? "NA" : MSSqlRepli.PRStorageName;
                lblDR3PARStorageName.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRStorageName)) ? "NA" : MSSqlRepli.DRStorageName;
                lblPRRemoteCopyGrpName.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.PRGroupName)) ? "NA" : MSSqlRepli.PRGroupName;
                lblDRRemoteCopyGrpName.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRGroupName)) ? "NA" : MSSqlRepli.DRGroupName;
                lblPR3PARRole.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.PRRole)) ? "NA" : MSSqlRepli.PRRole;
                lblDR3PARRole.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRRole)) ? "NA" : MSSqlRepli.DRRole;

                lblPR3PARRepliMode.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.PRReplicationMode)) ? "NA" : MSSqlRepli.PRReplicationMode;
                lblDR3PARRepliMode.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRReplicationMode)) ? "NA" : MSSqlRepli.DRReplicationMode;

                lblPR3PARState.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.PRState)) ? "NA" : MSSqlRepli.PRState;
                lblDR3PARState.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRState)) ? "NA" : MSSqlRepli.DRState;

                lblPR3PARGrpState.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.PRGroupState)) ? "NA" : MSSqlRepli.PRGroupState;
                lblDR3PARGrpState.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRGroupState)) ? "NA" : MSSqlRepli.DRGroupState;

                lblPR3PARSyncTime.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.PRLastSyncTime)) ? "NA" : MSSqlRepli.PRLastSyncTime;

                if (MSSqlRepli.DRLastSyncTime.Contains("0001"))
                {
                    lblDR3PARSyncTime.Text = "NA";
                }
                else
                {
                    //lblDR3PARSyncTime.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DRLastSyncTime)) ? "NA" : MSSqlRepli.DRLastSyncTime;
                    lblDR3PARSyncTime.Text = MSSqlRepli.DRLastSyncTime;
                }
                lblHP3PARDatalag.Text = string.IsNullOrEmpty(Convert.ToString(MSSqlRepli.DataLag)) ? "NA" : MSSqlRepli.DataLag;

            }
            else
            {
                lblHP3PARPRIP.Text = "NA";
                lblHP3PARDRIP.Text = "NA";

                lblPR3PARStorageName.Text = "NA";
                lblDR3PARStorageName.Text = "NA";
                lblPRRemoteCopyGrpName.Text = "NA";
                lblDRRemoteCopyGrpName.Text = "NA";
                lblPR3PARRole.Text = "NA";
                lblDR3PARRole.Text = "NA";

                lblPR3PARRepliMode.Text = "NA";
                lblDR3PARRepliMode.Text = "NA";

                lblPR3PARState.Text = "NA";
                lblDR3PARState.Text = "NA";

                lblPR3PARGrpState.Text = "NA";
                lblDR3PARGrpState.Text = "NA";

                lblPR3PARSyncTime.Text = "NA";
                lblDR3PARSyncTime.Text = "NA";

                lblHP3PARDatalag.Text = "NA";
            }
            var Virtualdetails = Facade.GetVirtualVolumemonitorbyInfraobjectId(infraObjectId);
            if (Virtualdetails != null)
            {
                lblvvname.Text = Virtualdetails.LocalVVName;
                lblremotevvname.Text = Virtualdetails.RemoteVVName;
                lblsyncstate.Text = Virtualdetails.SyncState;
                lbllstsynstate.Text = Virtualdetails.LastSyncTime;
            }
            else
            {
                lblvvname.Text = "NA";
                lblremotevvname.Text = "NA";
                lblsyncstate.Text = "NA";
                lbllstsynstate.Text = "NA";
            }
        }


        private void GetTPRCMonitorDetails(int infraobjectId)
        {


            var infra = Facade.GetInfraObjectById(CurrentInfraObjectId);
            var Prserver = Facade.GetServerById(infra.PRServerId);
            var Drserver = Facade.GetServerById(infra.DRServerId);
            var TPRCDetails = Facade.GetTPRCMonitorByInfraObjectId(CurrentInfraObjectId);

            if (TPRCDetails != null)
            {
                lblTPIPPR.Text = CryptographyHelper.Md5Decrypt(Prserver.IPAddress);
                lblTPIPDR.Text = CryptographyHelper.Md5Decrypt(Drserver.IPAddress);
                lblPRGroupName.Text = TPRCDetails.GROUPNAME;
                lblDRGroupName.Text = "NA";
                lblPRType.Text = TPRCDetails.TYPE;
                lblDRType.Text = "NA";
                lblPRState.Text = TPRCDetails.STATE;
                lblDRState.Text = "NA";
                lblPRStatus.Text = TPRCDetails.STATUS;
                lblDRStatus.Text = "NA";
                lblPRlocations.Text = TPRCDetails.LOCATIONS;
                lblDRlocations.Text = "NA";
                lblPRCopySts.Text = TPRCDetails.COPYSETS;
                lblDRCopySts.Text = "NA";
                lblPRCopy.Text = TPRCDetails.COPYING;
                lblDRCopy.Text = "NA";
                lblPRRecov.Text = TPRCDetails.RECOVERABLE;
                lblDRRecov.Text = "NA";
                lblPRhost12.Text = TPRCDetails.ACTIVEHOST;
                lblDRhost12.Text = "NA";
                lblPRErrCount.Text = TPRCDetails.ERRORCOUNT;
                lblDRErrCount.Text = "NA";
                lblPRDes.Text = TPRCDetails.DESCRIPTION;
                lblDRDes.Text = "NA";
                lblPRPairName.Text = TPRCDetails.PAIRNAME;
                lblDRPairName.Text = "NA";
                lblPRError.Text = TPRCDetails.ERROR;
                lblDRError.Text = "NA";
                lblPRCopyType.Text = TPRCDetails.COPYTYPE;
                lblDRCopyType.Text = "NA";
                lblPRProgress.Text = TPRCDetails.PROGRESS;
                lblDRProgress.Text = "NA";
                lblPRerrorvol.Text = TPRCDetails.ERRORVOLUMES;
                lblDRerrorvol.Text = "NA";
                lblPRrecoverPair.Text = TPRCDetails.RECVRALEPAIRS;
                lblDRrecoverPair.Text = "NA";
                lblPRcopypairs.Text = TPRCDetails.COPYINGPAIRS;
                lblDRcopypairs.Text = "NA";
                lblPRtotalpairs.Text = TPRCDetails.TOTALPAIRS;
                lblDRtotalpairs.Text = "NA";
                lblPRRecoveryTime.Text = TPRCDetails.RECOVERYTIME;
                lblDRRecoveryTime.Text = "NA";
            }
            else
            {
                lblTPIPPR.Text = "NA";
                lblTPIPDR.Text = "NA";
                lblPRGroupName.Text = "NA";
                lblDRGroupName.Text = "NA";
                lblPRType.Text = "NA";
                lblDRType.Text = "NA";
                lblPRState.Text = "NA";
                lblDRState.Text = "NA";
                lblPRStatus.Text = "NA";
                lblDRStatus.Text = "NA";
                lblPRlocations.Text = "NA";
                lblDRlocations.Text = "NA";
                lblPRCopySts.Text = "NA";
                lblDRCopySts.Text = "NA";
                lblPRCopy.Text = "NA";
                lblDRCopy.Text = "NA";
                lblPRRecov.Text = "NA";
                lblDRRecov.Text = "NA";
                lblPRhost12.Text = "NA";
                lblDRhost12.Text = "NA";
                lblPRErrCount.Text = "NA";
                lblDRErrCount.Text = "NA";
                lblPRDes.Text = "NA";
                lblDRDes.Text = "NA";
                lblPRPairName.Text = "NA";
                lblDRPairName.Text = "NA";
                lblPRError.Text = "NA";
                lblDRError.Text = "NA";
                lblPRCopyType.Text = "NA";
                lblDRCopyType.Text = "NA";
                lblPRProgress.Text = "NA";
                lblDRProgress.Text = "NA";
                lblPRerrorvol.Text = "NA";
                lblDRerrorvol.Text = "NA";
                lblPRrecoverPair.Text = "NA";
                lblDRrecoverPair.Text = "NA";
                lblPRcopypairs.Text = "NA";
                lblDRcopypairs.Text = "NA";
                lblPRtotalpairs.Text = "NA";
                lblDRtotalpairs.Text = "NA";
                lblPRRecoveryTime.Text = "NA";
                lblDRRecoveryTime.Text = "NA";
            }
        }

        private void GetSnapMirrorReplicationDetails(int infraObjectId)
        {
            divPstatemonitor.Style.Add("display", "block");
            widget2.Style.Add("display", "block");
            tblSnapComponent.Style.Add("display", "table");
            tblSnapMirriorReplication.Style.Add("display", "table");
            tblMSSQLDoubletek.Style.Add("display", "none");
            tblPstateMoniter.Style.Add("display", "none");
            divRepeaterPstate.Visible = false;
           // tblRepeaterPstate.Style.Add("display", "none");
            tblGlobalMirror.Visible = false;
            tblEMCSRDF.Visible = false;
            tblOracleDataGuard.Visible = false;
            tblFastCopy.Visible = false;
            servicestatus.Visible = false;
            divPstatemonitorHead.Visible = false;

            var infraObject = Facade.GetSnapMirrorMonitorByInfraObjectId(infraObjectId);
            if (infraObject != null)
            {
                lblsnapmirrorReplicationtype.Text = ReplicationType.NetAppSnapMirror.ToDescription(); //GetExchangeReplicationById(group.PRReplicationId);

                //var snapmirrorreplication = Facade.GetSnapMirrorReplicationById(group.Id);
                lblsource.Text = infraObject.Source;
                lblDestination.Text = infraObject.Destination;
                lblState.Text = infraObject.State;
                lblLag.Text = infraObject.Lag;
                lblStatus.Text = infraObject.Status;
                //Ram Mahajan-GetInfraObjectById method is removed by GetInfraObjectById -08/05/2014
                //var group1 = Facade.GetInfraObjectById(groupId);
                var infraObject1 = Facade.GetInfraObjectById(infraObjectId);
                var lblid = new Label();
                lblid.Text = Convert.ToString(infraObject1.DRReplicationId);
                int id;
                id = Convert.ToInt32(lblid.Text);
                //var volumedata = Facade.GetSnapMirrorById(id);
                //if (volumedata != null)
                //{
                //    lblsourcevolume.Text = volumedata.PRVolume;
                //    lbltargetvolume.Text = volumedata.DRVolume;
                //}
                //else
                //{
                //    lblsourcevolume.Text = "N/A";
                //    lbltargetvolume.Text = "N/A";
                //}
                SetSnapMirrorComponentInfo(infraObjectId);
            }
            else
            {
                lblSnapPRServer.Text = "N/A";
                lblSnapPRIP.Text = "N/A";
                lblSnapDRServer.Text = "N/A";
                lblSnapDRIP.Text = "N/A";
                lblSnapPRServer.Text = "N/A";
                lblSnapPRIP.Text = "N/A";
                lblsource.Text = "N/A";
                lblPRVolume.Text = "N/A";
                lblDRVolume.Text = "N/A";
                lblsnapmirrorReplicationtype.Text = "N/A";
                lblDestination.Text = "N/A";
                lblState.Text = "N/A";
                lblLag.Text = "N/A";
                lblStatus.Text = "N/A";
                lblPRStorageId.Text = "N/A";
                lblDRStorageId.Text = "N/A";
            }
        }

        private IList<EmcISilon_Monitor> BindEmcISilonMonitorList()
        {
            return Facade.GetAllEmcISilonMonitor();
            //EmcISilonReplication emcisilonlist = BindEmcISilonList().FirstOrDefault();
        }

        private IList<EmcISilonReplication> BindEmcISilonList()
        {
            return Facade.GetAllEmcISilonPolicy();
        }

        private void GetEmcISilonDeatils(int infraObjectId)
        {
            var infraobjects = Facade.GetInfraObjectById(infraObjectId);
            IList<EmcISilon_Monitor> emcisilonMonitorlist1 = BindEmcISilonMonitorList();
            var EmcISilonRepli = Facade.GetEmcISilonMonitorByInfraObjectId(infraObjectId);
            var emcrepli = Facade.GetEmcISilonByReplicationId(infraobjects.PRReplicationId);
            var emcPolicy = Facade.GetEmcISilonPolicyByEmcISilonId(emcrepli.Id).FirstOrDefault();
            StringBuilder yyyhy = new StringBuilder();
            string strTable = string.Empty;
            if (EmcISilonRepli != null)
            {
                if (emcPolicy != null)
                {
                    //string[] policynm = EmcISilonRepli.PolicyNamePR.Split(',');
                    string[] policynm = emcPolicy.PolicyName.Split(',');
                    for (int i = 0; i < policynm.Length; i++)
                    {
                        EmcISilon_Monitor emcisilonMonitorlist = emcisilonMonitorlist1.Where(x => x.PolicyNamePR.Equals(policynm[i])).FirstOrDefault();
                        if (emcisilonMonitorlist != null)
                        {
                            strTable = "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white'>";
                            strTable += "<tr><th class='col-md-4'>Parameter Name</th><th col-md-4>PR</th><th col-md-4>DR</th></tr>";

                            strTable += "<tr>";
                            strTable += "<td> Isilon Cluster IP </td>";
                            strTable += "<td><span class='health-up'></span> " + emcisilonMonitorlist.IsilonClusterIPPR + "</td>";
                            strTable += "<td><span class='health-up'></span> " + emcisilonMonitorlist.IsilonClusterIPDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Version</td>";
                            strTable += "<td><span class='icon-edition'></span> " + emcisilonMonitorlist.VersionPR + "</td>";
                            strTable += "<td><span class='icon-edition'></span> " + emcisilonMonitorlist.VersionDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>Health</td>";
                            strTable += "<td><span class='Active'></span> " + emcisilonMonitorlist.HealthPR + "</td>";
                            strTable += "<td><span class='Active'></span> " + emcisilonMonitorlist.HealthDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Cluster Name</td>";
                            strTable += "<td><span class='cluster_icon-blue'></span> " + emcisilonMonitorlist.ClusterNamePR + "</td>";
                            strTable += "<td><span class='cluster_icon-blue'></span> " + emcisilonMonitorlist.ClusterNameDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Policy name</td>";
                            strTable += "<td><span class='id-icon'></span> " + emcisilonMonitorlist.PolicyNamePR + "</td>";
                            strTable += "<td><span class='id-icon'></span> " + emcisilonMonitorlist.PolicyNameDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>Path</td>";
                            strTable += "<td><span class='power-icon'></span> " + emcisilonMonitorlist.PathPR + "</td>";
                            strTable += "<td><span class='power-icon'></span> " + emcisilonMonitorlist.PathDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>State</td>";
                            strTable += "<td><span class='Active'></span> " + emcisilonMonitorlist.StatePR + "</td>";
                            strTable += "<td><span class='Active'></span> " + emcisilonMonitorlist.StateDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Schedule</td>";
                            strTable += "<td><span class='icon-Time'></span> " + emcisilonMonitorlist.SchedulePR + "</td>";
                            strTable += "<td><span class='icon-Time'></span> " + emcisilonMonitorlist.ScheduleDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>FOFB State</td>";
                            strTable += "<td><span class='Active'></span> " + emcisilonMonitorlist.FOFBStatePR + "</td>";
                            strTable += "<td><span class='Active'></span> " + emcisilonMonitorlist.FOFBStateDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Last Job Action</td>";
                            strTable += "<td><span class='icon-sync'></span> " + emcisilonMonitorlist.LastJobActionPR + "</td>";
                            strTable += "<td><span class='icon-sync'></span> " + emcisilonMonitorlist.LastJobStateDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Last Job State</td>";
                            strTable += "<td><span class='icon-Time'></span> " + emcisilonMonitorlist.LastJobStatePR + "</td>";
                            strTable += "<td><span class='icon-Time'></span> " + emcisilonMonitorlist.LastJobStateDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>Last Start Time</td>";
                            strTable += "<td><span class='icon-Time'></span> " + emcisilonMonitorlist.LastStartTimePR + "</td>";
                            strTable += "<td><span class='icon-Time'></span> " + emcisilonMonitorlist.LastStartTimeDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>Last Good-Known state time</td>";
                            strTable += "<td><span class='icon-Time'></span> " + emcisilonMonitorlist.LastGoodKnownStateTimePR + "</td>";
                            strTable += "<td><span class='icon-Time'></span> " + emcisilonMonitorlist.LastGoodKnownStateTimeDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>RPO</td>";
                            strTable += "<td colspan='2'><span class='icon-Time'></span> " + emcisilonMonitorlist.RPO + "</td>";
                            strTable += "</tr>";

                            strTable += "</table>";
                            yyyhy.Append(strTable);
                        }
                        else
                        {
                            strTable = "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white'>";
                            strTable += "<tr><th class='col-md-4'>Parameter Name</th><th col-md-4>PR</th><th col-md-4>DR</th></tr>";

                            strTable += "<tr>";
                            strTable += "<td> Isilon Cluster IP </td>";
                            strTable += "<td><span class='health-up'></span> NA</td>";
                            strTable += "<td><span class='health-up'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Version</td>";
                            strTable += "<td><span class='icon-edition'></span> NA</td>";
                            strTable += "<td><span class='icon-edition'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>Health</td>";
                            strTable += "<td><span class='Active'></span> NA</td>";
                            strTable += "<td><span class='Active'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Cluster Name</td>";
                            strTable += "<td><span class='cluster_icon-blue'></span> NA</td>";
                            strTable += "<td><span class='cluster_icon-blue'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Policy name</td>";
                            strTable += "<td><span class='id-icon'></span>NA</td>";
                            strTable += "<td><span class='id-icon'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>Path</td>";
                            strTable += "<td><span class='power-icon'></span> NA</td>";
                            strTable += "<td><span class='power-icon'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>State</td>";
                            strTable += "<td><span class='Active'></span> NA</td>";
                            strTable += "<td><span class='Active'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Schedule</td>";
                            strTable += "<td><span class='icon-Time'></span> NA</td>";
                            strTable += "<td><span class='icon-Time'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>FOFB State</td>";
                            strTable += "<td><span class='Active'></span> NA</td>";
                            strTable += "<td><span class='Active'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Last Job Action</td>";
                            strTable += "<td><span class='icon-sync'></span> NA</td>";
                            strTable += "<td><span class='icon-sync'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td> Last Job State</td>";
                            strTable += "<td><span class='icon-Time'></span> NA</td>";
                            strTable += "<td><span class='icon-Time'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>Last Start Time</td>";
                            strTable += "<td><span class='icon-Time'></span>NA</td>";
                            strTable += "<td><span class='icon-Time'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>Last Good-Known state time</td>";
                            strTable += "<td><span class='icon-Time'></span>NA</td>";
                            strTable += "<td><span class='icon-Time'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td>RPO</td>";
                            strTable += "<td colspan='2'><span class='icon-Time'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "</table>";
                            yyyhy.Append(strTable);
                        }
                    }

                    divEmcISilonMonitoring.InnerHtml = yyyhy.ToString();
                    divEmcISilonMonitoring.Visible = true;
                    lblEmcISilonnm.Visible = true;
                    divEmcISilonMonitoring_scroll.Visible = true;
                }
            }
            else
            {
                strTable = "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white'>";
                strTable += "<tr><th class='col-md-4'>Parameter Name</th><th col-md-4>PR</th><th col-md-4>DR</th></tr>";

                strTable += "<tr>";
                strTable += "<td> Isilon Cluster IP </td>";
                strTable += "<td><span class='health-up'></span> NA</td>";
                strTable += "<td><span class='health-up'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td> Version</td>";
                strTable += "<td><span class='icon-edition'></span> NA</td>";
                strTable += "<td><span class='icon-edition'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td>Health</td>";
                strTable += "<td><span class='Active'></span> NA</td>";
                strTable += "<td><span class='Active'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td> Cluster Name</td>";
                strTable += "<td><span class='cluster_icon-blue'></span> NA</td>";
                strTable += "<td><span class='cluster_icon-blue'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td> Policy name</td>";
                strTable += "<td><span class='id-icon'></span>NA</td>";
                strTable += "<td><span class='id-icon'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td>Path</td>";
                strTable += "<td><span class='power-icon'></span> NA</td>";
                strTable += "<td><span class='power-icon'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td>State</td>";
                strTable += "<td><span class='Active'></span> NA</td>";
                strTable += "<td><span class='Active'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td> Schedule</td>";
                strTable += "<td><span class='icon-Time'></span> NA</td>";
                strTable += "<td><span class='icon-Time'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td>FOFB State</td>";
                strTable += "<td><span class='Active'></span> NA</td>";
                strTable += "<td><span class='Active'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td> Last Job Action</td>";
                strTable += "<td><span class='icon-sync'></span> NA</td>";
                strTable += "<td><span class='icon-sync'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td> Last Job State</td>";
                strTable += "<td><span class='icon-Time'></span> NA</td>";
                strTable += "<td><span class='icon-Time'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td>Last Start Time</td>";
                strTable += "<td><span class='icon-Time'></span>NA</td>";
                strTable += "<td><span class='icon-Time'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td>Last Good-Known state time</td>";
                strTable += "<td><span class='icon-Time'></span>NA</td>";
                strTable += "<td><span class='icon-Time'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td>RPO</td>";
                strTable += "<td colspan='2'><span class='icon-Time'></span> NA</td>";
                strTable += "</tr>";

                strTable += "</table>";
                yyyhy.Append(strTable);

                divEmcISilonMonitoring.InnerHtml = yyyhy.ToString();
                divEmcISilonMonitoring.Visible = true;
                lblEmcISilonnm.Visible = true;
                divEmcISilonMonitoring_scroll.Visible = true;

            }
            //var infraobjects = Facade.GetInfraObjectById(infraObjectId);
            //var EmcISilonRepli = Facade.GetEmcISilonMonitorByInfraObjectId(infraObjectId);

            //if (EmcISilonRepli != null)
            //{
            //    lblPRISilonClusterIP.Text = EmcISilonRepli.IsilonClusterIPPR;
            //    lblDRISilonClusterIP.Text = EmcISilonRepli.IsilonClusterIPDR;
            //    lblPRISilonVersion.Text = EmcISilonRepli.VersionPR;
            //    lblDRISilonVersion.Text = EmcISilonRepli.VersionDR;
            //    lblPRISilonHealth.Text = EmcISilonRepli.HealthPR;
            //    lblDRISilonHealth.Text = EmcISilonRepli.HealthDR;
            //    lblPRISilonClusterNm.Text = EmcISilonRepli.ClusterNamePR;
            //    lblDRISilonClusterNm.Text = EmcISilonRepli.ClusterNameDR;
            //    lblPRISilonPolicyNm.Text = EmcISilonRepli.PolicyNamePR;
            //    lblDRISilonPolicyNm.Text = EmcISilonRepli.PolicyNameDR;
            //    lblPRISilonPath.Text = EmcISilonRepli.PathPR;
            //    lblDRISilonPath.Text = EmcISilonRepli.PathDR;
            //    lblPRISilonState.Text = EmcISilonRepli.StatePR;
            //    lblDRISilonState.Text = EmcISilonRepli.StateDR;
            //    lblPRISilonSchedule.Text = EmcISilonRepli.SchedulePR;
            //    lblDRISilonSchedule.Text = EmcISilonRepli.ScheduleDR;
            //    lblPRISilonFOFBState.Text = EmcISilonRepli.FOFBStatePR;
            //    lblDRISilonFOFBState.Text = EmcISilonRepli.FOFBStateDR;
            //    lblPRISilonLastJobAction.Text = EmcISilonRepli.LastJobActionPR;
            //    lblDRISilonLastJobAction.Text = EmcISilonRepli.LastJobActionDR;
            //    lblPRIsilonLastJob.Text = EmcISilonRepli.LastJobStatePR;
            //    lblDRIsilonLastJob.Text = EmcISilonRepli.LastJobStateDR;
            //    lblPRISilonLastStartTime.Text = EmcISilonRepli.LastStartTimePR;
            //    lblDRISilonLastStartTime.Text = EmcISilonRepli.LastStartTimeDR;
            //    lblPRISilonGoodState.Text = EmcISilonRepli.LastGoodKnownStateTimePR;
            //    lblDRISilonGoodState.Text = EmcISilonRepli.LastGoodKnownStateTimeDR;
            //    lblISilonRPO.Text = EmcISilonRepli.RPO;
            //}
            //else
            //{
            //    lblPRISilonClusterIP.Text = "NA";
            //    lblDRISilonClusterIP.Text = "NA";
            //    lblPRISilonVersion.Text = "NA";
            //    lblDRISilonVersion.Text = "NA";
            //    lblPRISilonHealth.Text = "NA";
            //    lblDRISilonHealth.Text = "NA";
            //    lblPRISilonClusterNm.Text = "NA";
            //    lblDRISilonClusterNm.Text = "NA";
            //    lblPRISilonPolicyNm.Text = "NA";
            //    lblDRISilonPolicyNm.Text = "NA";
            //    lblPRISilonPath.Text = "NA";
            //    lblDRISilonPath.Text = "NA";
            //    lblPRISilonState.Text = "NA";
            //    lblDRISilonState.Text = "NA";
            //    lblPRISilonSchedule.Text = "NA";
            //    lblDRISilonSchedule.Text = "NA";
            //    lblPRISilonFOFBState.Text = "NA";
            //    lblDRISilonFOFBState.Text = "NA";
            //    lblPRISilonLastJobAction.Text = "NA";
            //    lblDRISilonLastJobAction.Text = "NA";
            //    lblPRIsilonLastJob.Text = "NA";
            //    lblDRIsilonLastJob.Text = "NA";
            //    lblPRISilonLastStartTime.Text = "NA";
            //    lblDRISilonLastStartTime.Text = "NA";
            //    lblPRISilonGoodState.Text = "NA";
            //    lblDRISilonGoodState.Text = "NA";
            //    lblISilonRPO.Text = "NA";
            //}


        }

        private void PopulateHitachiUROracleDBReplicationDetails(int infraObjectId)
        {
            //Ram Mahajan-GetInfraObjectById method is removed by GetInfraObjectById -09/05/2014
            //var group = Facade.GetInfraObjectById(groupId);
            var infraObject = Facade.GetInfraObjectById(infraObjectId);
            if (infraObject != null)
            {
                var hitachiUrPrReplicationDetail = Facade.GetHitachiUrByReplicationId(infraObject.PRReplicationId);
                if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                {
                    if (hitachiUrPrReplicationDetail != null)
                    {
                        lblHitachiURPRStorage.Text = hitachiUrPrReplicationDetail.DrStorageId;
                        lblHitachiURPRHORCOMInstance.Text = hitachiUrPrReplicationDetail.DrHorcomInstance;

                        lblHitachiURDRStorage.Text = hitachiUrPrReplicationDetail.PrStorageId;
                        lblHitachiURDRHORCOMInstance.Text = hitachiUrPrReplicationDetail.PrHorcomInstance;
                    }
                }
                else
                {
                    if (hitachiUrPrReplicationDetail != null)
                    {
                        lblHitachiURPRStorage.Text = hitachiUrPrReplicationDetail.PrStorageId;
                        lblHitachiURPRHORCOMInstance.Text = hitachiUrPrReplicationDetail.PrHorcomInstance;

                        lblHitachiURDRStorage.Text = hitachiUrPrReplicationDetail.DrStorageId;
                        lblHitachiURDRHORCOMInstance.Text = hitachiUrPrReplicationDetail.DrHorcomInstance;
                    }
                }

                var hitachiUrMonitoringDetails = Facade.GetHitachiUrMonitoringByInfraObjectId(infraObjectId);
                if (hitachiUrMonitoringDetails != null)
                {
                    lblPrHORCOMStatus.Text = hitachiUrMonitoringDetails.PRHORCOMStatus;
                    lblDrHORCOMStatus.Text = hitachiUrMonitoringDetails.DRHORCOMStatus;
                    lblAverageCopyPendingTimer.Text = hitachiUrMonitoringDetails.AverageCopyPendingTime;
                }

                switch (lblPrHORCOMStatus.Text.ToLower())
                {
                    case "running":
                        spanPrHORCOMStatus.CssClass = "Replicating";
                        break;

                    case "pausing":
                        spanPrHORCOMStatus.CssClass = "Pausing";
                        break;

                    case "stopped":
                        spanPrHORCOMStatus.CssClass = "InActive";
                        break;
                }

                switch (lblDrHORCOMStatus.Text.ToLower())
                {
                    case "running":
                        spanDrHORCOMStatus.CssClass = "Replicating";
                        break;

                    case "pausing":
                        spanDrHORCOMStatus.CssClass = "pause";
                        break;

                    case "stopped":
                        spanDrHORCOMStatus.CssClass = "InActive";
                        break;
                }

                var hitachiUrDeviceMonitoringDetails = Facade.GetHitachiUrDeviceMonitoringByInfraObjectId(infraObjectId);
                lstHitachiURDeviceMonitoring.DataSource = hitachiUrDeviceMonitoringDetails;
                lstHitachiURDeviceMonitoring.DataBind();
            }
        }

        private void SetSnapMirrorComponentInfo(int infraObjectId)
        {
            var snapMirror = Facade.GetSnapMirrorByInfraObjectId(infraObjectId);
            if (snapMirror != null)
            {
                if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                {
                    var prServer = Facade.GetServerById(CurrentInfraObject.DRServerId);
                    if (prServer != null)
                    {
                        if (prServer.Status == ServerStatus.Up)
                            healthPR.Attributes.Add("class", "health-up");
                        else if (prServer.Status == ServerStatus.Down)
                            healthPR.Attributes.Add("class", "health-down");
                        else
                            healthPR.Attributes.Add("class", "fatal");

                        lblSnapPRServer.Text = prServer.Name;
                        lblSnapPRIP.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                    }
                    else
                    {
                        lblSnapPRServer.Text = "N/A";
                        lblSnapPRIP.Text = "N/A";
                    }
                    var drServer = Facade.GetServerById(CurrentInfraObject.PRServerId);
                    if (drServer != null)
                    {
                        if (drServer.Status == ServerStatus.Up)
                            healthDR.Attributes.Add("class", "health-up");
                        else if (drServer.Status == ServerStatus.Down)
                            healthDR.Attributes.Add("class", "health-down");
                        else
                            healthDR.Attributes.Add("class", "fatal");

                        lblSnapDRServer.Text = drServer.Name;
                        lblSnapDRIP.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);
                    }
                    else
                    {
                        lblSnapDRServer.Text = "N/A";
                        lblSnapDRIP.Text = "N/A";
                    }
                }
                else
                {
                    var prServer = Facade.GetServerById(CurrentInfraObject.PRServerId);
                    if (prServer != null)
                    {
                        if (prServer.Status == ServerStatus.Up)
                            healthPR.Attributes.Add("class", "health-up");
                        else if (prServer.Status == ServerStatus.Down)
                            healthPR.Attributes.Add("class", "health-down");
                        else
                            healthPR.Attributes.Add("class", "fatal");

                        lblSnapPRServer.Text = prServer.Name;
                        lblSnapPRIP.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                    }
                    else
                    {
                        lblSnapPRServer.Text = "N/A";
                        lblSnapPRIP.Text = "N/A";
                    }
                    var drServer = Facade.GetServerById(CurrentInfraObject.DRServerId);
                    if (drServer != null)
                    {
                        if (drServer.Status == ServerStatus.Up)
                            healthDR.Attributes.Add("class", "health-up");
                        else if (drServer.Status == ServerStatus.Down)
                            healthDR.Attributes.Add("class", "health-down");
                        else
                            healthDR.Attributes.Add("class", "fatal");

                        lblSnapDRServer.Text = drServer.Name;
                        lblSnapDRIP.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);
                    }
                    else
                    {
                        lblSnapDRServer.Text = "N/A";
                        lblSnapDRIP.Text = "N/A";
                    }
                }

                lblPRStorageId.Text = snapMirror.PRStorageId;
                lblPRVolume.Text = snapMirror.PRVolume;

                lblDRStorageId.Text = snapMirror.DRStorageId;
                lblDRVolume.Text = snapMirror.DRVolume;
            }
            else
            {
                lblSnapPRServer.Text = "N/A";
                lblSnapPRIP.Text = "N/A";
                lblSnapDRServer.Text = "N/A";
                lblSnapDRIP.Text = "N/A";
                lblSnapPRServer.Text = "N/A";
                lblSnapPRIP.Text = "N/A";
                lblPRVolume.Text = "N/A";
                lblDRVolume.Text = "N/A";
                lblPRStorageId.Text = "N/A";
                lblDRStorageId.Text = "N/A";
            }


        }

        private void PopulateMIMIXDatalagDetails(int infraObjectId)
        {
            var replication = Facade.GetMimixDatalagByInfraObjId(infraObjectId);
            if (replication != null)
            {
                lblDataGroup.Text = replication.DataGroup;
                lblDatabaseErrors.Text = replication.DatabaseErrors;
                lblElapsedTime.Text = replication.ElapsedTime;
                lblObjectInErrorAndActive.Text = replication.ObjectInErrorAndActiv;
                lblTransferDef.Text = replication.TransferDefinition;
                lblDataLagState.Text = replication.State;
                lblDBSourceReceiver.Text = replication.DBSourceReceiver;
                lblDBSourceSequnce.Text = replication.DBSourceSequence;
                lblDBSourceDataTime.Text = replication.DBSourceDateTime;
                lblDBSourceTransPerHour.Text = replication.DBSourceTransPerHour;


                lblDBTargetReceiver.Text = replication.DBTargetReceiver;
                lblDBTargetSequnce.Text = replication.DBTargetSequence;
                lblDBTargetDataTime.Text = replication.DBTargetDateTime;
                lblDBTargetTransPerHour.Text = replication.DBTargetTransPerHour;

                lblDBLastReadReceiver.Text = replication.DBLastReadReceiver;
                lblDBLastReadSequence.Text = replication.DBLastReadSequence;
                lblDBLastReadDataTime.Text = replication.DBLastReadDateTime;
                lblLastReadTrans.Text = replication.DBLastReadTransPerHour;

                lblobjCurrentReceiver.Text = replication.ObjCurrentReceiver;
                lblobjCurrentSeq.Text = replication.ObjCurrentSequence;
                lblobjCurrentDatatime.Text = replication.ObjCurrentDateTime;
                lblobjCurrentTrans.Text = replication.ObjCurrentTransPerHour;

                lblobjLastReceiver.Text = replication.ObjLastReadReceiver;
                lblobjLastSequence.Text = replication.ObjLastReadSequence;
                lblobjLastDateTime.Text = replication.ObjLastReadDateTime;
                lblobjLastTrans.Text = replication.ObjLastReadTransPerHour;
            }
            else
            {
                lblDataGroup.Text = "NA";
                lblDatabaseErrors.Text = "NA";
                lblElapsedTime.Text = "NA";
                lblObjectInErrorAndActive.Text = "NA";
                lblTransferDef.Text = "NA";
                lblDataLagState.Text = "NA";
                lblDBSourceReceiver.Text = "NA";
                lblDBSourceSequnce.Text = "NA";
                lblDBSourceDataTime.Text = "NA";
                lblDBSourceTransPerHour.Text = "NA";


                lblDBTargetReceiver.Text = "NA";
                lblDBTargetSequnce.Text = "NA";
                lblDBTargetDataTime.Text = "NA";
                lblDBTargetTransPerHour.Text = "NA";

                lblDBLastReadReceiver.Text = "NA";
                lblDBLastReadSequence.Text = "NA";
                lblDBLastReadDataTime.Text = "NA";
                lblLastReadTrans.Text = "NA";

                lblobjCurrentReceiver.Text = "NA";
                lblobjCurrentSeq.Text = "NA";
                lblobjCurrentDatatime.Text = "NA";
                lblobjCurrentTrans.Text = "NA";

                lblobjLastReceiver.Text = "NA";
                lblobjLastSequence.Text = "NA";
                lblobjLastDateTime.Text = "NA";
                lblobjLastTrans.Text = "NA";
            }
        }

        private void PopulateMIMIXHealthDetails(int infraObjectId)
        {
            IList<MimixHealth> replication = Facade.GetHealthByInfraObjectId(CurrentInfraObject.Id);
            if (replication != null && replication.Count > 0)
            {
                dvMMXHealth.Visible = true;
                dvMimixHealth.Visible = true;
                lsvMimixHealth.DataSource = replication;
                lsvMimixHealth.DataBind();
            }
            else
            {
                dvMMXHealth.Visible = true;
                dvMimixHealth.Visible = true;
                lsvMimixHealth.DataSource = null;
                lsvMimixHealth.DataBind();
            }
        }

        private void PopulateMIMIXAlertsDetails(int infraObjectId)
        {
            var replication = Facade.GetMimixAlertsByInfraObjId(infraObjectId);
            if (replication != null)
            {
                lblAuditCountPR.Text = replication.AuditCount;
                lblRecoveryCountPR.Text = replication.RecoveryCount;
                lblNotificationCountPR.Text = replication.NotificationCount;
            }
            else
            {
                lblAuditCountPR.Text = "NA";
                lblRecoveryCountPR.Text = "NA";
                lblNotificationCountPR.Text = "NA";
            }
        }

        private void PopulateMIMIXManagerDetails(int infraObjectId)
        {
            IList<MimixManager> replication = Facade.GetManagersByInfraObjectId(CurrentInfraObject.Id);
            if (replication != null && replication.Count > 0)
            {
                dvMMXManager.Visible = true;
                dvMimixManager.Visible = true;
                lsvMimixManager.DataSource = replication;
                lsvMimixManager.DataBind();
            }
            else
            {
                dvMMXManager.Visible = true;
                dvMimixManager.Visible = true;
                lsvMimixManager.DataSource = null;
                lsvMimixManager.DataBind();
            }
        }

        private void PopulateMIMIXAvailabilityDetails(int infraObjectId)
        {
            IList<MimixAvilability> replication = Facade.GetAvilabilitiesByInfraObjectId(CurrentInfraObject.Id);
            if (replication != null && replication.Count > 0)
            {
                dvMMXAvail.Visible = true;
                dvMimixAvail.Visible = true;
                lsvMimixAvail.DataSource = replication;
                lsvMimixAvail.DataBind();
            }
            else
            {
                dvMMXAvail.Visible = true;
                dvMimixAvail.Visible = true;
                lsvMimixAvail.DataSource = null;
                lsvMimixAvail.DataBind();
            }
        }

        private void PopulateApplicationDoubletekReplicationDetails(int infraObjectId)
        {
            tblMSSQLDoubletek.Style.Add("display", "table");
            var replication = Facade.GetMSSQLDoubletekByInfraId(infraObjectId);
            if (replication != null)
            {
                lblJobName.Text = replication.JobName;
                lblReplicationStatus.Text = replication.ReplicationStatus;
                lblSourceServer.Text = replication.SourceServer;
                lblTargetserver.Text = replication.TargetServer;
                lblJobType.Text = replication.JobType;
                lblRepliQueue.Text = replication.ReplicationQueue;
                lblJobActivity.Text = replication.JobActivity;
                lblMirrorStatus.Text = replication.MirrorStatus;
                lblMirrorPerComplete.Text = (replication.MirrorPercentComplete).ToString();
            }
        }
        private void PopulateeBDRReplicationDetails(int infraObjectId)
        {

            var CurrentComponentInfo = new eBDRProfileReplication();


            CurrentComponentInfo = Facade.GeteBDRProfilebyInfraId(infraObjectId);
            if (CurrentComponentInfo.ProfileName != null)
            {

                lblebdrProfileName.Text = CurrentComponentInfo.ProfileName;
                lblebdrsourceHost.Text = CurrentComponentInfo.SourceHost;
                lblebdrTargetHost.Text = CurrentComponentInfo.TargetHost;
                lbleBDROsType.Text = CurrentComponentInfo.OsType;
                lbleBDROsTypeDR.Text = CurrentComponentInfo.OsType;
                lblHealth.Text = (CurrentComponentInfo.Helth).ToString();


                if ((CurrentComponentInfo.OsType).ToLower().Contains("aix"))
                {

                    lblprosicoebdr.CssClass = "icon-aix";
                    lblprosicoebdr.ToolTip = "AIX";

                    lbldrosicoebdr.CssClass = "icon-aix";
                    lbldrosicoebdr.ToolTip = "AIX";
                }
                else if ((CurrentComponentInfo.OsType).ToLower().Contains("hpux"))
                {
                    lblprosicoebdr.CssClass = "icon-HPUX";
                    lblprosicoebdr.ToolTip = "HPUX";

                    lbldrosicoebdr.CssClass = "icon-HPUX";
                    lbldrosicoebdr.ToolTip = "AIX";
                }
                else if ((CurrentComponentInfo.OsType).ToLower().Contains("linux"))
                {
                    lblprosicoebdr.CssClass = "icon-linux";
                    lblprosicoebdr.ToolTip = "Linux";

                    lbldrosicoebdr.CssClass = "icon-linux";
                    lbldrosicoebdr.ToolTip = "Linux";
                }
                else if ((CurrentComponentInfo.OsType).ToLower().Contains("windows"))
                {
                    lblprosicoebdr.CssClass = "icon-Windows";
                    lblprosicoebdr.ToolTip = "Windows";

                    lbldrosicoebdr.CssClass = "icon-Windows";
                    lbldrosicoebdr.ToolTip = "Windows";
                }
                else if ((CurrentComponentInfo.OsType).ToLower().Contains("solaris"))
                {
                    lblprosicoebdr.CssClass = "icon-Solaris";
                    lblprosicoebdr.ToolTip = "Solaris";

                    lbldrosicoebdr.CssClass = "icon-Solaris";
                    lbldrosicoebdr.ToolTip = "Solaris";
                }
                else
                {
                    lblprosicoebdr.CssClass = "vmware-icon";

                    lbldrosicoebdr.CssClass = "vmware-icon";

                }

                //if ((CurrentComponentInfo.OsType).ToLower().Contains("file"))
                //{

                //    lblprosicoebdr.CssClass = "running-icon";
                //    lblprosicoebdr.ToolTip = "FILE";
                //}
                //else if ((CurrentComponentInfo.OsType).ToLower().Contains("esxi"))
                //{
                //    lblprosicoebdr.CssClass = "running-icon";
                //    lblprosicoebdr.ToolTip = "ESXI";
                //}
                //else if ((CurrentComponentInfo.OsType).ToLower().Contains("aws"))
                //{
                //    lblprosicoebdr.CssClass = "running-icon";
                //    lblprosicoebdr.ToolTip = "AWS";
                //}
                //else if ((CurrentComponentInfo.OsType).ToLower().Contains("phy"))
                //{
                //    lblprosicoebdr.CssClass = "running-icon";
                //    lblprosicoebdr.ToolTip = "PHY";
                //}
                //else if ((CurrentComponentInfo.OsType).ToLower().Contains("kum"))
                //{
                //    lblprosicoebdr.CssClass = "running-icon";
                //    lblprosicoebdr.ToolTip = "kum";
                //}
                //else if ((CurrentComponentInfo.OsType).ToLower().Contains("vhm"))
                //{
                //    lblprosicoebdr.CssClass = "running-icon";
                //    lblprosicoebdr.ToolTip = "VHM";
                //}
                //else
                //{
                //    lblprosicoebdr.CssClass = "running-icon";                   
                //}



                lbleBDRMachinName.Text = CurrentComponentInfo.MachineName;
                lblebDRStatus.Text = CurrentComponentInfo.Status;

                if ((CurrentComponentInfo.Status).ToLower().Contains("running") || (CurrentComponentInfo.Status).ToLower().Contains("replication_in_progress"))
                {

                    lblstatusiconforebdr.CssClass = "running-icon";
                    lblstatusiconforebdr.ToolTip = "Running";
                }
                else if ((CurrentComponentInfo.Status).ToLower().Contains("paused"))
                {
                    lblstatusiconforebdr.CssClass = "pause";
                    lblstatusiconforebdr.ToolTip = "Pause";
                }
                else if ((CurrentComponentInfo.Status).ToLower().Contains("resumed_replication"))
                {
                    lblstatusiconforebdr.CssClass = "resumeserviceicon";
                    lblstatusiconforebdr.ToolTip = "Resumed";
                }
                else if ((CurrentComponentInfo.Status).ToLower().Contains("canceled_replication"))
                {
                    lblstatusiconforebdr.CssClass = "InActive";
                    lblstatusiconforebdr.ToolTip = "Cancel";
                }
                else if ((CurrentComponentInfo.Status).ToLower().Contains("replication_completed"))
                {
                    lblstatusiconforebdr.CssClass = "Active";
                    lblstatusiconforebdr.ToolTip = "Completed";
                }
                else if ((CurrentComponentInfo.Status).ToLower().Contains("started"))
                {
                    lblstatusiconforebdr.CssClass = "playserviceicon";
                    lblstatusiconforebdr.ToolTip = "Start";
                }
                else
                {
                    lblstatusiconforebdr.CssClass = "fatal";
                    lblstatusiconforebdr.ToolTip = "Error";
                }

                // lblHealth.Text = Convert.ToString(CurrentComponentInfo.Helth);
                if (CurrentComponentInfo.Helth == 1)
                {
                    lbleBDRHealthicon.CssClass = "health-up";
                    //lblHealth.ToolTip = "Healthy";

                }
                else
                {
                    lbleBDRHealthicon.CssClass = "health-down";
                    //lblHealth.ToolTip = "Down";

                }


            }
            else
            {
                lblebdrProfileName.Text = "N/A";
                lblebdrsourceHost.Text = "N/A";
                lblebdrTargetHost.Text = "N/A";
                lbleBDROsType.Text = "N/A";
                lbleBDROsTypeDR.Text = "N/A";
                lbleBDRMachinName.Text = "N/A";
                lblebDRStatus.Text = "N/A";

            }
        }

        private void SetEmcMirrorViewReplicationInfo(int infraObjectId)
        {
            var prrepdet = Facade.GetEmcMirrorViewbyReplicationId(CurrentInfraObject.PRReplicationId);
            var lstEmcMirrorView = Facade.GetEmcMirrorViewRepliMonitorByCGNm(CurrentInfraObjectId, prrepdet.CGName);
            string strTable = "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white'>";

            //var lstEmcMirrorView = Facade.GetEmcMirrorViewRepliMonitorByInfraObjectId(CurrentInfraObjectId);

            if (lstEmcMirrorView == null)
            {
                //dvCloudantDB.Visible = false;
                // lblEmpty1.Visible = true;
                strTable += "<tr><th class='col-md-4'>Parameter Name</th><th class='col-md-4'>PR</th><th class='col-md-4'>DR</th></th></tr>";
                strTable += "<tr>";
                strTable += "<td style='width:5%;'>CG Name</td>";
                strTable += "<td style='width:5%;' colspan='2'><span class='icon-NA'></span> N/A</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td style='width:5%;'>Storage Processor</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td style='width:5%;'>Agent Rev</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span>N/A</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td style='width:5%;'>NAS version</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td style='width:5%;'>Role</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td style='width:5%;'>State</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td style='width:5%;'>Condition</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td style='width:5%;'>Mirror Names</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                strTable += "</tr>";

                dvEmcMirrorViewReplicationDetails.InnerHtml = strTable;
            }
            else
            {
                strTable += "<tr><th class='col-md-4'>Parameter Name</th><th class='col-md-4'>PR</th><th class='col-md-4'>DR</th></th></tr>";
                //int i = 0;
                if (lstEmcMirrorView != null)
                {

                    strTable += "<tr>";
                    strTable += "<td style='width:5%;'>CG Name</td>";
                    strTable += "<td style='width:5%;' colspan='2'><span class='business-setting-icon'></span> " + lstEmcMirrorView.CGName + "</td>";

                    strTable += "</tr>";
                    strTable += "<tr>";
                    strTable += "<td style='width:5%;'>Storage Processor</td>";
                    strTable += "<td style='width:5%;'><span class='icon-storagePR'></span> " + lstEmcMirrorView.SPNameOrIPPR + "</td>";
                    strTable += "<td style='width:5%;'><span class='icon-storageDR'></span> " + lstEmcMirrorView.SPNameOrIPDR + "</td>";
                    strTable += "</tr>";

                    if (lstEmcMirrorView.AgentRevPR != "" || lstEmcMirrorView.AgentRevDR != "")
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>Agent Rev</td>";
                        strTable += "<td style='width:5%;'><span class='business-setting-icon'></span> " + lstEmcMirrorView.AgentRevPR + "</td>";
                        strTable += "<td style='width:5%;'><span class='business-setting-icon'></span> " + lstEmcMirrorView.AgentRevDR + "</td>";
                        strTable += "</tr>";
                    }
                    else
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>Agent Rev</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span>N/A</td>";
                        strTable += "</tr>";
                    }
                    if (lstEmcMirrorView.NASVersionPR != "" || lstEmcMirrorView.NASVersionDR != "")
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>NAS version</td>";
                        strTable += "<td style='width:5%;'><span class='icon-database'></span> " + lstEmcMirrorView.NASVersionPR + "</td>";
                        strTable += "<td style='width:5%;'><span class='icon-database'></span> " + lstEmcMirrorView.NASVersionDR + "</td>";
                        strTable += "</tr>";
                    }
                    else
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>NAS version</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "</tr>";
                    }
                    if (lstEmcMirrorView.RolePR != "" || lstEmcMirrorView.RoleDR != "")
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>Role</td>";
                        strTable += "<td style='width:5%;'><span class='user-icon'></span> " + lstEmcMirrorView.RolePR + "</td>";
                        strTable += "<td style='width:5%;'><span class='user-icon'></span> " + lstEmcMirrorView.RoleDR + "</td>";
                        strTable += "</tr>";
                    }
                    else
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>Role</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "</tr>";
                    }
                    if (lstEmcMirrorView.StatePR != "" || lstEmcMirrorView.StateDR != "")
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>State</td>";
                        strTable += "<td style='width:5%;'><span class='refresh-icon-blue1'></span> " + lstEmcMirrorView.StatePR + "</td>";
                        strTable += "<td style='width:5%;'><span class='refresh-icon-blue1'></span> " + lstEmcMirrorView.StateDR + "</td>";
                        strTable += "</tr>";
                    }
                    else
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>State</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "</tr>";
                    }
                    if (lstEmcMirrorView.ConditionPR != "" || lstEmcMirrorView.ConditionDR != "")
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>Condition</td>";
                        strTable += "<td style='width:5%;'><span class='refresh-icon1'></span> " + lstEmcMirrorView.ConditionPR + "</td>";
                        strTable += "<td style='width:5%;'><span class='refresh-icon1'></span> " + lstEmcMirrorView.ConditionDR + "</td>";
                        strTable += "</tr>";
                    }
                    else
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>Condition</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "</tr>";
                    }
                    if (lstEmcMirrorView.MirrorNamesPR != "" || lstEmcMirrorView.MirrorNamesDR != "")
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>Mirror Names</td>";
                        string[] TempArrPR = lstEmcMirrorView.MirrorNamesPR.Split(',');
                        string[] TempArrDR = lstEmcMirrorView.MirrorNamesDR.Split(',');
                        string prTable = "";
                        string drTable = "";
                        for (int i = 0; i < TempArrPR.Length; i++)
                        {
                            prTable += "<span class='icon-database'></span> " + TempArrPR[i].ToString() + "<hr style='margin:5px 0;'/>";
                        }
                        for (int i = 0; i < TempArrDR.Length; i++)
                        {
                            drTable += "<span class='icon-database'></span> " + TempArrDR[i].ToString() + "<hr style='margin:5px 0;' />";
                        }
                        strTable += "<td>" + prTable + "</td>";
                        strTable += "<td>" + drTable + "</td></tr></table>";
                    }
                    else
                    {
                        strTable += "<tr>";
                        strTable += "<td style='width:5%;'>Mirror Names</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "<td style='width:5%;'><span class='icon-NA'></span> N/A</td>";
                        strTable += "</tr>";
                    }
                }



            }
            strTable += "</table>";

            dvEmcMirrorViewReplicationDetails.InnerHtml = strTable;
            dvEmcMirrorViewReplicationDetails.Visible = true;
        }

        private IList<Emc_MV_Mirror_Monitor> BindEmc_MVMirror_MonitorList()
        {
            return Facade.GetAll_EmcMirrorMonitor();

        }

        private void GetEmc_Mv_Mirror_Deatils(int infraObjectId)
        {

            var infraobjects = Facade.GetInfraObjectById(infraObjectId);
            IList<Emc_MV_Mirror_Monitor> emcmvmirrorMonitorlist1 = BindEmc_MVMirror_MonitorList();
            var EmcMVMirror_Monitor = Facade.GetEmcMirrorMonitorByInfraObjectId(infraObjectId);
            var lstEmcMirrorView = Facade.GetEmcMirrorViewRepliMonitorByInfraObjectId(CurrentInfraObjectId);
            //var emcrepli = Facade.GetEmcISilonByReplicationId(infraobjects.PRReplicationId);
            //var emcPolicy = Facade.GetEmcISilonPolicyByEmcISilonId(emcrepli.Id).FirstOrDefault();
            StringBuilder AllMVMirrorList = new StringBuilder();
            string strTable = string.Empty;
            if (EmcMVMirror_Monitor != null)
            {
                if (lstEmcMirrorView != null)
                {

                    string[] emcmvmirrornm = lstEmcMirrorView.MirrorNamesPR.Split(',');
                    for (int i = 0; i < emcmvmirrornm.Length; i++)
                    {
                        Emc_MV_Mirror_Monitor emcmvmirrorMonitorlist = emcmvmirrorMonitorlist1.Where(x => x.MirrorName.Equals(emcmvmirrornm[i])).FirstOrDefault();
                        if (emcmvmirrorMonitorlist != null)
                        {
                            strTable = "<div class='col-md-12 padding-none' style='margin-bottom:30px'>";
                            strTable += "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white' style='margin-bottom:0px'>";
                            strTable += "<tr><th class='col-md-4'>Mirror Name</th><th class='col-md-4'>" + emcmvmirrorMonitorlist.MirrorName + "</th><th class='col-md-4'></th></tr>";
                            strTable += "</table>";
                            strTable += "<div class='slim-scroll chat-items' data-scroll-max-height='120px' data-scroll-size='0'>";
                            strTable += "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white'>";
                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> LUN </td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-storageDR'></span> " + emcmvmirrorMonitorlist.LUN + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> Remote Mirror Status</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-storageDR'></span> " + emcmvmirrorMonitorlist.RemoteMirrorStatus + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>MirrorView State</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-storageDR'></span> " + emcmvmirrorMonitorlist.MirrorViewState + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> MirrorView Faulted</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-storageDR'></span> " + emcmvmirrorMonitorlist.MirrorViewFaulted + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> MirrorView Transitioning</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-storageDR'></span> " + emcmvmirrorMonitorlist.MirrorViewTransitioning + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image Size(GBs)</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageSizeInGBs + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image Count</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageCount + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> Image UID</td>";
                            strTable += "<td class='col-md-4'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageUIDPR + "</td>";
                            strTable += "<td class='col-md-4'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageUIDDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image Condition</td>";
                            strTable += "<td class='col-md-4'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageConditionPR + "</td>";
                            strTable += "<td class='col-md-4'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageConditionDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> Is Image Primary</td>";
                            strTable += "<td class='col-md-4'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.IsImagePrimaryPR + "</td>";
                            strTable += "<td class='col-md-4'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.IsImagePrimaryDR + "</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image state</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageState + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Recovery Policy</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-dbrecover'></span> " + emcmvmirrorMonitorlist.RecoveryPolicy + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image Faulted</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageFaulted + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image transitioning</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='business-setting-icon'></span> " + emcmvmirrorMonitorlist.ImageTransitioning + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Synchronizing Progress</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='refresh-icon1'></span> " + emcmvmirrorMonitorlist.SynchronizingProgress + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Time Since previous Update (In Minutes)</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-Time'></span> " + emcmvmirrorMonitorlist.TimeSincePreviousUpdate + "</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Last Image Error</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span> " + emcmvmirrorMonitorlist.LastImageError + "</td>";

                            strTable += "</tr>";



                            strTable += "</table>";
                            strTable += "</div>";
                            strTable += "</div>";
                            AllMVMirrorList.Append(strTable);

                        }

                        else
                        {



                            strTable = "<div class='col-md-12 padding-none' style='margin-bottom:30px'>";
                            strTable += "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white' style='margin-bottom:0px'>";
                            strTable += "<tr><th class='col-md-4'>Mirror Name</th><th class='col-md-4'><span class='icon-NA'>NA</th><th class='col-md-4'></th></tr>";
                            strTable += "</table>";
                            strTable += "<div class='slim-scroll chat-items' data-scroll-max-height='120px' data-scroll-size='0'>";
                            strTable += "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white montr-dbtable montr'>";
                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> LUN </td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> Remote Mirror Status</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>MirrorView State</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> MirrorView Faulted</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> MirrorView Transitioning</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image Size(GBs)</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image Count</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> Image UID</td>";
                            strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                            strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image Condition</td>";
                            strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                            strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'> Is Image Primary</td>";
                            strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                            strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image state</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Recovery Policy</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image Faulted</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Image transitioning</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Synchronizing Progress</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Time Since previous Update (In Minutes)</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span> NA</td>";
                            strTable += "</tr>";

                            strTable += "<tr>";
                            strTable += "<td class='col-md-4'>Last Image Error</td>";
                            strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                            strTable += "</tr>";



                            strTable += "</table>";
                            strTable += "</div>";
                            strTable += "</div>";
                            AllMVMirrorList.Append(strTable);
                        }

                        divEmcmvmirrorMonitoring.InnerHtml = AllMVMirrorList.ToString();
                        divEmcmvmirrorMonitoring.Visible = true;
                        lblEmcmvmirrornm.Visible = true;
                        divEmcmvmirrorMonitoring_scroll.Visible = true;
                    }
                }
                else
                {

                    strTable = "<div class='col-md-12 padding-none' style='margin-bottom:30px'>";
                    strTable += "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white' style='margin-bottom:0px'>";
                    strTable += "<tr><th class='col-md-4'>Mirror Name</th><th class='col-md-4'><span class='icon-NA'>NA</th><th class='col-md-4'></th></tr>";
                    strTable += "</table>";
                    strTable += "<div class='slim-scroll chat-items' data-scroll-max-height='120px' data-scroll-size='0'>";
                    strTable += "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white montr-dbtable montr'>";
                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'> LUN </td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'> Remote Mirror Status</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>MirrorView State</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'> MirrorView Faulted</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'> MirrorView Transitioning</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Image Size(GBs)</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Image Count</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'> Image UID</td>";
                    strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                    strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Image Condition</td>";
                    strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                    strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'> Is Image Primary</td>";
                    strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                    strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Image state</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Recovery Policy</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Image Faulted</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Image transitioning</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Synchronizing Progress</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Time Since previous Update (In Minutes)</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span> NA</td>";
                    strTable += "</tr>";

                    strTable += "<tr>";
                    strTable += "<td class='col-md-4'>Last Image Error</td>";
                    strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                    strTable += "</tr>";



                    strTable += "</table>";
                    strTable += "</div>";
                    strTable += "</div>";
                    AllMVMirrorList.Append(strTable);
                }
                divEmcmvmirrorMonitoring.InnerHtml = AllMVMirrorList.ToString();
                divEmcmvmirrorMonitoring.Visible = true;
                lblEmcmvmirrornm.Visible = true;
                divEmcmvmirrorMonitoring_scroll.Visible = true;
            }
            else
            {

                strTable = "<div class='col-md-12 padding-none' style='margin-bottom:30px'>";
                strTable += "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white' style='margin-bottom:0px'>";
                strTable += "<tr><th class='col-md-4'>Mirror Name</th><th class='col-md-4'><span class='icon-NA'>NA</th><th class='col-md-4'></th></tr>";
                strTable += "</table>";
                strTable += "<div class='slim-scroll chat-items' data-scroll-max-height='120px' data-scroll-size='0'>";
                strTable += "<table class='dynamicTable tableTools table table-striped table-bordered table-condensed table-white montr-dbtable montr'>";
                strTable += "<tr>";
                strTable += "<td class='col-md-4'> LUN </td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'> Remote Mirror Status</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>MirrorView State</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'> MirrorView Faulted</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'> MirrorView Transitioning</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Image Size(GBs)</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Image Count</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'> Image UID</td>";
                strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Image Condition</td>";
                strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'> Is Image Primary</td>";
                strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                strTable += "<td class='col-md-4'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Image state</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Recovery Policy</td>";
                strTable += "<td  class='col-md-4'colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Image Faulted</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";

                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Image transitioning</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Synchronizing Progress</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Time Since previous Update (In Minutes)</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span> NA</td>";
                strTable += "</tr>";

                strTable += "<tr>";
                strTable += "<td class='col-md-4'>Last Image Error</td>";
                strTable += "<td class='col-md-4' colspan='2'><span class='icon-NA'></span>NA</td>";
                strTable += "</tr>";



                strTable += "</table>";
                strTable += "</div>";
                strTable += "</div>";
                AllMVMirrorList.Append(strTable);

                divEmcmvmirrorMonitoring.InnerHtml = AllMVMirrorList.ToString();
                divEmcmvmirrorMonitoring.Visible = true;
                lblEmcmvmirrornm.Visible = true;
                divEmcmvmirrorMonitoring_scroll.Visible = true;

            }

        }

        //private void GetRoboCopyMonitorDetails(int infraobjectId)
        //{
        //    //var robocopy = Facade.GetRoboCopyLogsByInfrad(infraobjectId);

        //    var robocopy = Facade.GetRoboLogsByInfraObjId(CurrentInfraObjectId);

        //    var table1 = new DataTable();
        //    table1.Columns.Add("RowIndex");
        //    table1.Columns.Add("Id");
        //    table1.Columns.Add("RoboCopyJobId");
        //    table1.Columns.Add("SourceDirectory");
        //    table1.Columns.Add("DestinationDirectory");
        //    table1.Columns.Add("SelectedOptions");

        //    table1.Columns.Add("RepStartTime");
        //    table1.Columns.Add("RepEndTime");

        //    table1.Columns.Add("TotalDirCount");
        //    table1.Columns.Add("TotalDirCopiedCount");
        //    table1.Columns.Add("TotalSkippedDirCount");
        //    table1.Columns.Add("TotalMisMatchedDirCount");
        //    table1.Columns.Add("TotalFailedDirCount");
        //    table1.Columns.Add("TotalExtrasDirCount");

        //    table1.Columns.Add("TotalFilesCount");
        //    table1.Columns.Add("TotalFilesCopiedCount");
        //    table1.Columns.Add("TotalSkippedFilesCount");
        //    table1.Columns.Add("TotalMisMatchedFilesCount");
        //    table1.Columns.Add("TotalFailedFilesCount");
        //    table1.Columns.Add("TotalExtrasFilesCount");

        //    table1.Columns.Add("TotalBytesCount");
        //    table1.Columns.Add("TotalBytesCopiedCount");
        //    table1.Columns.Add("TotalSkippedBytesCount");
        //    table1.Columns.Add("TotalMisMatchedBytesCount");
        //    table1.Columns.Add("TotalFailedBytesCount");
        //    table1.Columns.Add("TotalExtrasBytesCount");

        //    table1.Columns.Add("TotalTimesCount");
        //    table1.Columns.Add("TotalTimesCopiedCount");
        //    table1.Columns.Add("TotalSkippedTimesCount");
        //    table1.Columns.Add("TotalMisMatchedTimesCount");
        //    table1.Columns.Add("TotalFailedTimesCount");
        //    table1.Columns.Add("TotalExtrasTimesCount");

        //    table1.Columns.Add("SpeedBytesPerSeconds");
        //    table1.Columns.Add("SpeedMBPerMinute");


        //    if (robocopy != null && robocopy.Count > 0)
        //    {

        //        lblNoRoboJobs.Text = robocopy.Count.ToString();
        //        int i = 0;
        //        foreach (var rcItem in robocopy)
        //        {
        //            i++;
        //            DataRow dr = table1.NewRow();
        //            dr["RowIndex"] = i.ToString();
        //            dr["Id"] = rcItem.Id;
        //            dr["RoboCopyJobId"] = rcItem.RoboCopyJobId;

        //            var robojobb = Facade.GetRoboCopyJobById(rcItem.RoboCopyJobId);
        //            dr["SourceDirectory"] = robojobb.SourceDirectory;
        //            dr["DestinationDirectory"] = robojobb.DestinationDirectory;

        //            dr["SelectedOptions"] = rcItem.SelectedOptions;

        //            dr["RepStartTime"] = rcItem.RepStartTime;
        //            dr["RepEndTime"] = rcItem.RepEndTime;

        //            dr["TotalDirCount"] = rcItem.TotalDirCount;
        //            dr["TotalDirCopiedCount"] = rcItem.TotalDirCopiedCount;
        //            dr["TotalSkippedDirCount"] = rcItem.TotalSkippedDirCount;
        //            dr["TotalMisMatchedDirCount"] = rcItem.TotalMisMatchedDirCount;
        //            dr["TotalFailedDirCount"] = rcItem.TotalFailedDirCount;
        //            dr["TotalExtrasDirCount"] = rcItem.TotalExtrasDirCount;

        //            dr["TotalFilesCount"] = rcItem.TotalFilesCount;
        //            dr["TotalFilesCopiedCount"] = rcItem.TotalFilesCopiedCount;
        //            dr["TotalSkippedFilesCount"] = rcItem.TotalSkippedFilesCount;
        //            dr["TotalMisMatchedFilesCount"] = rcItem.TotalMisMatchedFilesCount;
        //            dr["TotalFailedFilesCount"] = rcItem.TotalFailedFilesCount;
        //            dr["TotalExtrasFilesCount"] = rcItem.TotalExtrasFilesCount;

        //            dr["TotalBytesCount"] = rcItem.TotalBytesCount;
        //            dr["TotalBytesCopiedCount"] = rcItem.TotalBytesCopiedCount;
        //            dr["TotalSkippedBytesCount"] = rcItem.TotalSkippedBytesCount;
        //            dr["TotalMisMatchedBytesCount"] = rcItem.TotalMisMatchedBytesCount;
        //            dr["TotalFailedBytesCount"] = rcItem.TotalFailedBytesCount;
        //            dr["TotalExtrasBytesCount"] = rcItem.TotalExtrasBytesCount;

        //            dr["TotalTimesCount"] = rcItem.TotalTimesCount;
        //            dr["TotalTimesCopiedCount"] = rcItem.TotalTimesCopiedCount;
        //            dr["TotalSkippedTimesCount"] = rcItem.TotalSkippedTimesCount;
        //            dr["TotalMisMatchedTimesCount"] = rcItem.TotalMisMatchedTimesCount;
        //            dr["TotalFailedTimesCount"] = rcItem.TotalFailedTimesCount;
        //            dr["TotalExtrasTimesCount"] = rcItem.TotalExtrasTimesCount;

        //            dr["SpeedBytesPerSeconds"] = rcItem.SpeedBytesPerSeconds;
        //            dr["SpeedMBPerMinute"] = rcItem.SpeedMBPerMinute;

        //            table1.Rows.Add(dr);
        //        }
        //    }

        //    rptRoboMonitorStatus.DataSource = table1;
        //    rptRoboMonitorStatus.DataBind();

        //    #region Commented
        //    //var robocopy = robocopy1.FirstOrDefault();
        //    //if (robocopy != null)
        //    //{

        //    //    lbltotaldir.Text = Convert.ToString(robocopy.TotalDirCount);
        //    //    lblcopieddir.Text = Convert.ToString(robocopy.TotalDirCopiedCount);
        //    //    lblskippeddir.Text = Convert.ToString(robocopy.TotalSkippedDirCount);
        //    //    lblmismatcheddir.Text = Convert.ToString(robocopy.TotalMisMatchedDirCount);
        //    //    lblfaileddir.Text = Convert.ToString(robocopy.TotalFailedDirCount);
        //    //    lblextrasdir.Text = Convert.ToString(robocopy.TotalExtrasDirCount);

        //    //    lbltotalfiles.Text = Convert.ToString(robocopy.TotalFilesCount);
        //    //    lblcopiedfiles.Text = Convert.ToString(robocopy.TotalFilesCopiedCount);
        //    //    lblskippedfiles.Text = Convert.ToString(robocopy.TotalSkippedFilesCount);
        //    //    lblmismatchedfiles.Text = Convert.ToString(robocopy.TotalMisMatchedFilesCount);
        //    //    lblfailedfiles.Text = Convert.ToString(robocopy.TotalFailedFilesCount);
        //    //    lblextrasfiles.Text = Convert.ToString(robocopy.TotalExtrasFilesCount);

        //    //    lbltotalbytes.Text = Convert.ToString(robocopy.TotalBytesCount);
        //    //    lblcopiedbytes.Text = Convert.ToString(robocopy.TotalBytesCopiedCount);
        //    //    lblskippedbytes.Text = Convert.ToString(robocopy.TotalSkippedBytesCount);
        //    //    lblmismatchedbytes.Text = Convert.ToString(robocopy.TotalMisMatchedBytesCount);
        //    //    lblfailedbytes.Text = Convert.ToString(robocopy.TotalFailedBytesCount);
        //    //    lblextrasbytes.Text = Convert.ToString(robocopy.TotalExtrasBytesCount);

        //    //    lbltotaltimes.Text = Convert.ToString(robocopy.TotalTimesCount);
        //    //    lblcopiedtimes.Text = Convert.ToString(robocopy.TotalTimesCopiedCount);
        //    //    lblskippedtimes.Text = Convert.ToString(robocopy.TotalSkippedTimesCount);
        //    //    lblmismatchedtimes.Text = Convert.ToString(robocopy.TotalMisMatchedTimesCount);
        //    //    lblfailedtimes.Text = Convert.ToString(robocopy.TotalFailedTimesCount);
        //    //    lblextrastimes.Text = Convert.ToString(robocopy.TotalExtrasTimesCount);

        //    //    lblrepstarttime.Text = robocopy.RepStartTime;
        //    //    lblrependtime.Text = robocopy.RepEndTime;

        //    //    speedbytepersec.Text = Convert.ToString(robocopy.SpeedBytesPerSeconds);
        //    //    speedmegabytepermin.Text = Convert.ToString(robocopy.SpeedMBPerMinute);

        //    //    lblRoboSelectedOptions.Text = robocopy.SelectedOptions;

        //    //    var robojobb = Facade.GetRoboCopyJobById(robocopy.RoboCopyJobId);

        //    //    lblspath.Text = robojobb.SourceDirectory;
        //    //    lbldpath.Text = robojobb.DestinationDirectory;

        //    //}
        //    #endregion Commented

        //    #region commented
        //    //var infradetails = Facade.GetInfraObjectById(infraobjectId);

        //    ////string selectedoptions = string.Empty;

        //    // var repl = Facade.GetReplicationBaseById(infradetails.PRReplicationId);

        //    // if (repl != null)
        //    // {

        //    //     var robocopyfor = Facade.GetRoboCopyByReplicationId(repl.Id);

        //    //     if (robocopyfor != null)
        //    //     {

        //    //         var robojobb = Facade.GetRoboCopyJobByRoboCopyId(robocopyfor.Id);

        //    //         if (robojobb != null)
        //    //         {

        //    //             //var robojob = robojobb.FirstOrDefault();

        //    //             foreach (var robojob in robojobb)
        //    //             {
        //    //                 if (lblspath.Text.Trim() != "")
        //    //                 {
        //    //                     lblspath.Text += ", ";
        //    //                 }
        //    //                 if (lbldpath.Text.Trim() != "")
        //    //                 {
        //    //                     lbldpath.Text += ", ";
        //    //                 }

        //    //                 lblspath.Text += robojob.SourceDirectory;
        //    //                 lbldpath.Text += robojob.DestinationDirectory;
        //    //             }
        //    //         }
        //    //     }
        //    // }

        //    //var robooption = Facade.GetByRoboCopyOptionsId(robojob.Id);

        //    //string copy = robooption.CopyOptions;

        //    //string fd= copy.Replace("^",",");

        //    //selectedoptions += fd + ",";

        //    //string filter = robooption.Filters;

        //    //string fdfd = filter.Replace("#", "");

        //    //selectedoptions += fdfd + ",";


        //    //string advance = robooption.AdvancedFilters;

        //    //string fr = advance.Replace("#", ",");

        //    //  selectedoptions += fr ;

        //    //  lblRoboSelectedOptions.Text = selectedoptions;

        //    //            select * from infraobject where id=676;
        //    //select * from replication_base where id=801;
        //    //select * from robocopy where replicationid=801;
        //    //SELECT * FROM robocopyjob r where robocopyid=2;
        //    //SELECT * FROM robocopyoptions r where id=2;
        //    #endregion commented

        //    var infradetails = Facade.GetInfraObjectById(infraobjectId);
        //    if (infradetails != null)
        //    {
        //        if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
        //        {
        //            var prserver = Facade.GetServerById(infradetails.DRServerId);
        //            {

        //                Label41.Text = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
        //                Label43.Text = prserver.HostName;

        //                if (prserver.Status == ServerStatus.Up)
        //                {
        //                    spnLabel41.CssClass = "health-up";
        //                }
        //                else if (prserver.Status == ServerStatus.Down)
        //                {
        //                    spnLabel41.CssClass = "health-down";
        //                }
        //                else
        //                {
        //                    spnLabel41.CssClass = "fatal";
        //                }

        //                //lblprip.Text = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
        //                // Label43.Text = prserver.HostName;
        //            }

        //            var drserver = Facade.GetServerById(infradetails.PRServerId);
        //            {
        //                Label42.Text = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
        //                Label44.Text = drserver.HostName;

        //                if (drserver.Status == ServerStatus.Up)
        //                {
        //                    spnLabel42.CssClass = "health-up";
        //                }
        //                else if (drserver.Status == ServerStatus.Down)
        //                {
        //                    spnLabel42.CssClass = "health-down";
        //                }
        //                else
        //                {
        //                    spnLabel42.CssClass = "fatal";
        //                }

        //            }


        //        }
        //        else
        //        {
        //            var prserver = Facade.GetServerById(infradetails.PRServerId);
        //            {
        //                // lblprip.Text = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
        //                Label41.Text = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
        //                Label43.Text = prserver.HostName;

        //                if (prserver.Status == ServerStatus.Up)
        //                {
        //                    spnLabel41.CssClass = "health-up";
        //                }
        //                else if (prserver.Status == ServerStatus.Down)
        //                {
        //                    spnLabel41.CssClass = "health-down";
        //                }
        //                else
        //                {
        //                    spnLabel41.CssClass = "fatal";
        //                }
        //            }

        //            var drserver = Facade.GetServerById(infradetails.DRServerId);
        //            {
        //                Label42.Text = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
        //                Label44.Text = drserver.HostName;

        //                if (drserver.Status == ServerStatus.Up)
        //                {
        //                    spnLabel42.CssClass = "health-up";
        //                }
        //                else if (drserver.Status == ServerStatus.Down)
        //                {
        //                    spnLabel42.CssClass = "health-down";
        //                }
        //                else
        //                {
        //                    spnLabel42.CssClass = "fatal";
        //                }
        //            }

        //        }
        //    }
        //    else
        //    {
        //        // lblprip.Text = "NA";

        //        //lblspath.Text = "NA";
        //        //lbldpath.Text = "NA";
        //        Label42.Text = "NA";
        //    }
        //}

        private void GetRoboCopyMonitorDetails(int infraobjectId)
        {
            //var robocopy = Facade.GetRoboCopyLogsByInfrad(infraobjectId);

            var robocopy = Facade.GetRoboLogsByInfraObjId(CurrentInfraObjectId);
            var infradetails = Facade.GetInfraObjectById(CurrentInfraObjectId);
            var repl = Facade.GetReplicationBaseById(infradetails.PRReplicationId);
            if (repl != null)
            {
                var robocopyfor = Facade.GetRoboCopyByReplicationId(repl.Id);
                if (robocopyfor != null)
                {
                    var robojobb = Facade.GetRoboCopyJobByRoboCopyId(robocopyfor.Id);
                    if (robojobb != null)
                        lblNoRoboJobs.Text = robojobb.Count.ToString();

                }
            }

            var table1 = new DataTable();
            table1.Columns.Add("RowIndex");
            table1.Columns.Add("Id");
            table1.Columns.Add("RoboCopyJobId");
            table1.Columns.Add("SourceDirectory");
            table1.Columns.Add("DestinationDirectory");
            table1.Columns.Add("SelectedOptions");

            table1.Columns.Add("RepStartTime");
            table1.Columns.Add("RepEndTime");

            table1.Columns.Add("TotalDirCount");
            table1.Columns.Add("TotalDirCopiedCount");
            table1.Columns.Add("TotalSkippedDirCount");
            table1.Columns.Add("TotalMisMatchedDirCount");
            table1.Columns.Add("TotalFailedDirCount");
            table1.Columns.Add("TotalExtrasDirCount");

            table1.Columns.Add("TotalFilesCount");
            table1.Columns.Add("TotalFilesCopiedCount");
            table1.Columns.Add("TotalSkippedFilesCount");
            table1.Columns.Add("TotalMisMatchedFilesCount");
            table1.Columns.Add("TotalFailedFilesCount");
            table1.Columns.Add("TotalExtrasFilesCount");

            table1.Columns.Add("TotalBytesCount");
            table1.Columns.Add("TotalBytesCopiedCount");
            table1.Columns.Add("TotalSkippedBytesCount");
            table1.Columns.Add("TotalMisMatchedBytesCount");
            table1.Columns.Add("TotalFailedBytesCount");
            table1.Columns.Add("TotalExtrasBytesCount");

            table1.Columns.Add("TotalTimesCount");
            table1.Columns.Add("TotalTimesCopiedCount");
            table1.Columns.Add("TotalSkippedTimesCount");
            table1.Columns.Add("TotalMisMatchedTimesCount");
            table1.Columns.Add("TotalFailedTimesCount");
            table1.Columns.Add("TotalExtrasTimesCount");

            table1.Columns.Add("SpeedBytesPerSeconds");
            table1.Columns.Add("SpeedMBPerMinute");


            if (robocopy != null && robocopy.Count > 0)
            {

                lblNoRoboJobs.Text = robocopy.Count.ToString();
                int i = 0;
                foreach (var rcItem in robocopy)
                {
                    i++;
                    DataRow dr = table1.NewRow();
                    dr["RowIndex"] = i.ToString();
                    dr["Id"] = rcItem.Id;
                    dr["RoboCopyJobId"] = rcItem.RoboCopyJobId;

                    var robojobb = Facade.GetRoboCopyJobById(rcItem.RoboCopyJobId);
                    dr["SourceDirectory"] = robojobb.SourceDirectory;
                    dr["DestinationDirectory"] = robojobb.DestinationDirectory;

                    dr["SelectedOptions"] = rcItem.SelectedOptions;

                    dr["RepStartTime"] = rcItem.RepStartTime;
                    dr["RepEndTime"] = rcItem.RepEndTime;

                    dr["TotalDirCount"] = rcItem.TotalDirCount;
                    dr["TotalDirCopiedCount"] = rcItem.TotalDirCopiedCount;
                    dr["TotalSkippedDirCount"] = rcItem.TotalSkippedDirCount;
                    dr["TotalMisMatchedDirCount"] = rcItem.TotalMisMatchedDirCount;
                    dr["TotalFailedDirCount"] = rcItem.TotalFailedDirCount;
                    dr["TotalExtrasDirCount"] = rcItem.TotalExtrasDirCount;

                    dr["TotalFilesCount"] = rcItem.TotalFilesCount;
                    dr["TotalFilesCopiedCount"] = rcItem.TotalFilesCopiedCount;
                    dr["TotalSkippedFilesCount"] = rcItem.TotalSkippedFilesCount;
                    dr["TotalMisMatchedFilesCount"] = rcItem.TotalMisMatchedFilesCount;
                    dr["TotalFailedFilesCount"] = rcItem.TotalFailedFilesCount;
                    dr["TotalExtrasFilesCount"] = rcItem.TotalExtrasFilesCount;

                    dr["TotalBytesCount"] = rcItem.TotalBytesCount;
                    dr["TotalBytesCopiedCount"] = rcItem.TotalBytesCopiedCount;
                    dr["TotalSkippedBytesCount"] = rcItem.TotalSkippedBytesCount;
                    dr["TotalMisMatchedBytesCount"] = rcItem.TotalMisMatchedBytesCount;
                    dr["TotalFailedBytesCount"] = rcItem.TotalFailedBytesCount;
                    dr["TotalExtrasBytesCount"] = rcItem.TotalExtrasBytesCount;

                    dr["TotalTimesCount"] = rcItem.TotalTimesCount;
                    dr["TotalTimesCopiedCount"] = rcItem.TotalTimesCopiedCount;
                    dr["TotalSkippedTimesCount"] = rcItem.TotalSkippedTimesCount;
                    dr["TotalMisMatchedTimesCount"] = rcItem.TotalMisMatchedTimesCount;
                    dr["TotalFailedTimesCount"] = rcItem.TotalFailedTimesCount;
                    dr["TotalExtrasTimesCount"] = rcItem.TotalExtrasTimesCount;

                    dr["SpeedBytesPerSeconds"] = rcItem.SpeedBytesPerSeconds;
                    dr["SpeedMBPerMinute"] = rcItem.SpeedMBPerMinute;

                    table1.Rows.Add(dr);
                }
            }


            rptRoboMonitorStatus.DataSource = table1;
            rptRoboMonitorStatus.DataBind();

            #region Commented
            //var robocopy = robocopy1.FirstOrDefault();
            //if (robocopy != null)
            //{

            //    lbltotaldir.Text = Convert.ToString(robocopy.TotalDirCount);
            //    lblcopieddir.Text = Convert.ToString(robocopy.TotalDirCopiedCount);
            //    lblskippeddir.Text = Convert.ToString(robocopy.TotalSkippedDirCount);
            //    lblmismatcheddir.Text = Convert.ToString(robocopy.TotalMisMatchedDirCount);
            //    lblfaileddir.Text = Convert.ToString(robocopy.TotalFailedDirCount);
            //    lblextrasdir.Text = Convert.ToString(robocopy.TotalExtrasDirCount);

            //    lbltotalfiles.Text = Convert.ToString(robocopy.TotalFilesCount);
            //    lblcopiedfiles.Text = Convert.ToString(robocopy.TotalFilesCopiedCount);
            //    lblskippedfiles.Text = Convert.ToString(robocopy.TotalSkippedFilesCount);
            //    lblmismatchedfiles.Text = Convert.ToString(robocopy.TotalMisMatchedFilesCount);
            //    lblfailedfiles.Text = Convert.ToString(robocopy.TotalFailedFilesCount);
            //    lblextrasfiles.Text = Convert.ToString(robocopy.TotalExtrasFilesCount);

            //    lbltotalbytes.Text = Convert.ToString(robocopy.TotalBytesCount);
            //    lblcopiedbytes.Text = Convert.ToString(robocopy.TotalBytesCopiedCount);
            //    lblskippedbytes.Text = Convert.ToString(robocopy.TotalSkippedBytesCount);
            //    lblmismatchedbytes.Text = Convert.ToString(robocopy.TotalMisMatchedBytesCount);
            //    lblfailedbytes.Text = Convert.ToString(robocopy.TotalFailedBytesCount);
            //    lblextrasbytes.Text = Convert.ToString(robocopy.TotalExtrasBytesCount);

            //    lbltotaltimes.Text = Convert.ToString(robocopy.TotalTimesCount);
            //    lblcopiedtimes.Text = Convert.ToString(robocopy.TotalTimesCopiedCount);
            //    lblskippedtimes.Text = Convert.ToString(robocopy.TotalSkippedTimesCount);
            //    lblmismatchedtimes.Text = Convert.ToString(robocopy.TotalMisMatchedTimesCount);
            //    lblfailedtimes.Text = Convert.ToString(robocopy.TotalFailedTimesCount);
            //    lblextrastimes.Text = Convert.ToString(robocopy.TotalExtrasTimesCount);

            //    lblrepstarttime.Text = robocopy.RepStartTime;
            //    lblrependtime.Text = robocopy.RepEndTime;

            //    speedbytepersec.Text = Convert.ToString(robocopy.SpeedBytesPerSeconds);
            //    speedmegabytepermin.Text = Convert.ToString(robocopy.SpeedMBPerMinute);

            //    lblRoboSelectedOptions.Text = robocopy.SelectedOptions;

            //    var robojobb = Facade.GetRoboCopyJobById(robocopy.RoboCopyJobId);

            //    lblspath.Text = robojobb.SourceDirectory;
            //    lbldpath.Text = robojobb.DestinationDirectory;

            //}
            #endregion Commented

            #region commented
            //var infradetails = Facade.GetInfraObjectById(infraobjectId);

            ////string selectedoptions = string.Empty;

            // var repl = Facade.GetReplicationBaseById(infradetails.PRReplicationId);

            // if (repl != null)
            // {

            //     var robocopyfor = Facade.GetRoboCopyByReplicationId(repl.Id);

            //     if (robocopyfor != null)
            //     {

            //         var robojobb = Facade.GetRoboCopyJobByRoboCopyId(robocopyfor.Id);

            //         if (robojobb != null)
            //         {

            //             //var robojob = robojobb.FirstOrDefault();

            //             foreach (var robojob in robojobb)
            //             {
            //                 if (lblspath.Text.Trim() != "")
            //                 {
            //                     lblspath.Text += ", ";
            //                 }
            //                 if (lbldpath.Text.Trim() != "")
            //                 {
            //                     lbldpath.Text += ", ";
            //                 }

            //                 lblspath.Text += robojob.SourceDirectory;
            //                 lbldpath.Text += robojob.DestinationDirectory;
            //             }
            //         }
            //     }
            // }

            //var robooption = Facade.GetByRoboCopyOptionsId(robojob.Id);

            //string copy = robooption.CopyOptions;

            //string fd= copy.Replace("^",",");

            //selectedoptions += fd + ",";

            //string filter = robooption.Filters;

            //string fdfd = filter.Replace("#", "");

            //selectedoptions += fdfd + ",";


            //string advance = robooption.AdvancedFilters;

            //string fr = advance.Replace("#", ",");

            //  selectedoptions += fr ;

            //  lblRoboSelectedOptions.Text = selectedoptions;

            //            select * from infraobject where id=676;
            //select * from replication_base where id=801;
            //select * from robocopy where replicationid=801;
            //SELECT * FROM robocopyjob r where robocopyid=2;
            //SELECT * FROM robocopyoptions r where id=2;
            #endregion commented

            // var infradetails = Facade.GetInfraObjectById(infraobjectId);
            if (infradetails != null)
            {
                if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                {
                    var prserver = Facade.GetServerById(infradetails.DRServerId);
                    {

                        Label41.Text = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                        Label43.Text = prserver.HostName;

                        if (prserver.Status == ServerStatus.Up)
                        {
                            spnLabel41.CssClass = "health-up";
                        }
                        else if (prserver.Status == ServerStatus.Down)
                        {
                            spnLabel41.CssClass = "health-down";
                        }
                        else
                        {
                            spnLabel41.CssClass = "fatal";
                        }

                        //lblprip.Text = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                        // Label43.Text = prserver.HostName;
                    }

                    var drserver = Facade.GetServerById(infradetails.PRServerId);
                    {
                        Label42.Text = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                        Label44.Text = drserver.HostName;

                        if (drserver.Status == ServerStatus.Up)
                        {
                            spnLabel42.CssClass = "health-up";
                        }
                        else if (drserver.Status == ServerStatus.Down)
                        {
                            spnLabel42.CssClass = "health-down";
                        }
                        else
                        {
                            spnLabel42.CssClass = "fatal";
                        }

                    }


                }
                else
                {
                    var prserver = Facade.GetServerById(infradetails.PRServerId);
                    {
                        // lblprip.Text = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                        Label41.Text = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                        Label43.Text = prserver.HostName;

                        if (prserver.Status == ServerStatus.Up)
                        {
                            spnLabel41.CssClass = "health-up";
                        }
                        else if (prserver.Status == ServerStatus.Down)
                        {
                            spnLabel41.CssClass = "health-down";
                        }
                        else
                        {
                            spnLabel41.CssClass = "fatal";
                        }
                    }

                    var drserver = Facade.GetServerById(infradetails.DRServerId);
                    {
                        Label42.Text = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                        Label44.Text = drserver.HostName;

                        if (drserver.Status == ServerStatus.Up)
                        {
                            spnLabel42.CssClass = "health-up";
                        }
                        else if (drserver.Status == ServerStatus.Down)
                        {
                            spnLabel42.CssClass = "health-down";
                        }
                        else
                        {
                            spnLabel42.CssClass = "fatal";
                        }
                    }

                }
            }
            else
            {
                // lblprip.Text = "NA";

                //lblspath.Text = "NA";
                //lbldpath.Text = "NA";
                Label42.Text = "NA";
            }
        }

        private void PopulateApplicationBase24ReplicationDetails(int infraObjectId)
        {
            tblBase24ApplicationDetail.Style.Add("display", "table");
            tblBase24Replication.Style.Add("display", "table");
            widget2.Style.Add("display", "block");
            divPstatemonitor.Style.Add("display", "block");
            tblPstateMoniter.Style.Add("display", "none");

            var replication = Facade.GetBase24ReplicationByInfraId(infraObjectId);
            if (replication != null)
            {
                lblPRBase24RepType.Text = "DR-NET";
                //lblDRBase24RepType.Text = replication.DRReplicationType;
                lblPRBase24RepStatus.Text = replication.PRReplicationStatus;
                lblDRBase24RepStatus.Text = replication.DRReplicationStatus;
                lblPRBase24RepProcess.Text = replication.PRReplicationProcess;
                lblDRBase24RepProcess.Text = replication.DRReplicationProcess;
                lblPRBase24RepQueue.Text = replication.PRReplicationQueue;
                lblDRBase24RepQueue.Text = replication.DRReplicationQueue;
                lblPRBase24RepAccessed.Text = replication.PRReplicationAccessed;
                lblDRBase24RepAccessed.Text = replication.DRReplicationAccessed;
                lblPRBase24RepMode.Text = replication.PRReplicationMode;
                lblDRBase24RepMode.Text = replication.DRReplicationMode;
                lblBase24Datalag.Text = replication.DataLag;

                lblPRBase24AuditFileName.Text = replication.PRAuditFilename;
                lblDRBase24AuditFileName.Text = replication.DRAuditFilename;
            }
            else
            {
                lblPRBase24RepType.Text = "DR-NET";
                //lblDRBase24RepType.Text = replication.DRReplicationType;
                lblPRBase24RepStatus.Text = "N/A";
                lblDRBase24RepStatus.Text = "N/A";
                lblPRBase24RepProcess.Text = "N/A";
                lblDRBase24RepProcess.Text = "N/A";
                lblPRBase24RepQueue.Text = "N/A";
                lblDRBase24RepQueue.Text = "N/A";
                lblPRBase24RepAccessed.Text = "N/A";
                lblDRBase24RepAccessed.Text = "N/A";
                lblPRBase24RepMode.Text = "N/A";
                lblDRBase24RepMode.Text = "N/A";
                lblBase24Datalag.Text = "N/A";

                lblPRBase24AuditFileName.Text = "N/A";
                lblDRBase24AuditFileName.Text = "N/A";
            }



            if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
            {
                GetApplicationServerDetail(CurrentInfraObject.DRServerId, CurrentInfraObject.PRServerId);
            }
            else
            {
                GetApplicationServerDetail(CurrentInfraObject.PRServerId, CurrentInfraObject.DRServerId);
            }
            if (CurrentInfraObject.MonitoringWorkflow > 0)
            {
                var workflow = Facade.GetWorkflowById(CurrentInfraObject.MonitoringWorkflow);
                if (workflow != null)
                {
                    lblWorkflow.Text = workflow.Name; //CurrentInfraObject.Output;
                }
            }
            else
            {
                lblWorkflow.Text = "N/A";
            }
            // added at 03/06/2014
            var AppMonitor = Facade.GetApplicationMonitorByInfraobjectIdAndAppType(CurrentInfraObject.Id, "PR");
            if (AppMonitor == null)
            {
                lblIconPR.CssClass = "";
                lblPRAppStatus.Text = "N/A";
            }
            else if (AppMonitor.Status == "Running")
            {
                lblIconPR.CssClass = "Replicating";
                lblPRAppStatus.CssClass = "active";
                lblPRAppStatus.Text = "Running";

            }
            else if (AppMonitor.Status == "Stopped")
            {
                lblIconPR.CssClass = "InActive";
                lblPRAppStatus.CssClass = "inactive";
                lblPRAppStatus.Text = "Stopped";
            }

            if (CurrentInfraObject.DrMonitorApplicationCheck > 0)
            {
                if (CurrentInfraObject.DrMonitoringWorkflow > 0)
                {
                    var workflow = Facade.GetWorkflowById(CurrentInfraObject.DrMonitoringWorkflow);
                    if (workflow != null)
                    {
                        lblDrWorkflowStatus.Text = workflow.Name; //CurrentInfraObject.Output;
                    }
                }
                else
                {
                    lblDrWorkflowStatus.Text = "N/A";
                }
            }
            else
            {
                lblDrWorkflowStatus.Text = "N/A";
            }

            var AppMonitorDR = Facade.GetApplicationMonitorByInfraobjectIdAndAppType(CurrentInfraObject.Id, "DR");
            if (AppMonitorDR == null)
            {
                lblIconDR.CssClass = "";
                lblDRAppStatus.Text = "N/A";
            }
            else if (AppMonitorDR.Status == "Running")
            {
                lblIconDR.CssClass = "Replicating";
                lblDRAppStatus.CssClass = "active";
                lblDRAppStatus.Text = "Running";
            }
            else if (AppMonitorDR.Status == "Stopped")
            {
                lblIconDR.CssClass = "InActive";
                lblDRAppStatus.CssClass = "inactive";
                lblDRAppStatus.Text = "Stopped";
            }



        }

        public void PopulateSVCReplicationDetails(int infraObjectId)
        {
            var infradeytails = Facade.GetInfraObjectById(infraObjectId);
            var replidetails = Facade.GetReplicationBaseById(infradeytails.PRReplicationId);
            var svcrepli = Facade.GetAllSVCGMReplicationMvalueformonitoring(infradeytails.PRReplicationId);
            if (svcrepli != null)
            {

                lblPRSVCRMRelationshipName.Text = svcrepli.PRRelationshipName;
                lblPRSVCRMGroupName.Text = svcrepli.PRConsistencyGroupName;
                lblPRSVCRMRelationshipPrimaryValue.Text = svcrepli.PRRelationshipPrimaryValue;
                lblPRSVCRMMasterChangeVolumeName.Text = svcrepli.PRMasterVolumeName;
                lblPRSVCRMAuxiliaryChangeVolumeName.Text = svcrepli.PRAuxiliaryVolumeName;
                lblPRSVCRMRelationshipState.Text = svcrepli.PRRCRelationshipState;
                lblPRSVCRMRelationshipProgress.Text = svcrepli.PRRCRelationshipProgress;

                //DR
                lblDRSVCRMRelationshipName.Text = svcrepli.DRRelationshipName;
                lblDRSVCRMGroupName.Text = svcrepli.DRConsistencyGroupName;
                lblDRSVCRMRelationshipPrimaryValue.Text = svcrepli.DRRelationshipPrimaryValue;
                lblDRSVCRMMasterChangeVolumeName.Text = svcrepli.DRMasterVolumeName;
                lblDRSVCRMAuxiliaryChangeVolumeName.Text = svcrepli.DRAuxiliaryVolumeName;
                lblDRSVCRMRelationshipState.Text = svcrepli.DRRCRelationshipState;
                lblDRSVCRMRelationshipProgress.Text = svcrepli.DRRCRelationshipProgress;
            }
            else
            {
                lblPRSVCRMRelationshipName.Text = "NA";
                lblPRSVCRMGroupName.Text = "NA";
                lblPRSVCRMRelationshipPrimaryValue.Text = "NA";
                lblPRSVCRMMasterChangeVolumeName.Text = "NA";
                lblPRSVCRMAuxiliaryChangeVolumeName.Text = "NA";
                lblPRSVCRMRelationshipState.Text = "NA";
                lblPRSVCRMRelationshipProgress.Text = "NA";

                //DR
                lblDRSVCRMRelationshipName.Text = "NA";
                lblDRSVCRMGroupName.Text = "NA";
                lblDRSVCRMRelationshipPrimaryValue.Text = "NA";
                lblDRSVCRMMasterChangeVolumeName.Text = "NA";
                lblDRSVCRMAuxiliaryChangeVolumeName.Text = "NA";
                lblDRSVCRMRelationshipState.Text = "NA";
                lblDRSVCRMRelationshipProgress.Text = "NA";
            }

        }

        public void GetApplicationServerDetail(int prServerid, int drServerid)
        {
            try
            {
                if (prServerid > 0)
                {
                    // get Value for Primary Server component
                    var prServer = Facade.GetServerById(prServerid);

                    if (prServer != null)
                    {
                        lblApplicationPrServer.Text = prServer.Name;
                        lblApplicationPrIpAddress.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                        //SetServerImage(prServer.OSType, "PR", "Application");

                        switch (prServer.OSType)
                        {
                            case "AIX":
                                base24PR.Attributes.Add("Class", "icon-aix");
                                break;

                            case "HPUX":
                                base24PR.Attributes.Add("Class", "icon-HPUX");
                                break;

                            case "Linux":
                                base24PR.Attributes.Add("Class", "icon-linux");
                                break;

                            case "Solaris":
                                base24PR.Attributes.Add("Class", "icon-Solaris");
                                break;

                            case "Windows2003":
                                base24PR.Attributes.Add("Class", "icon-Windows");
                                break;

                            case "Windows2008":
                                base24PR.Attributes.Add("Class", "icon-Windows");
                                break;
                            case "Windows2012":
                                base24PR.Attributes.Add("Class", "icon-Windows");
                                break;

                            case "Windows2016":
                                base24PR.Attributes.Add("Class", "icon-Windows");
                                break;
                        }
                    }
                    else
                    {
                        lblApplicationPrServer.Text = "N/A";
                        lblApplicationPrIpAddress.Text = "N/A";
                    }
                }
                if (drServerid > 0)
                {
                    // get Value for DR Server component
                    var drServer = Facade.GetServerById(drServerid);
                    if (drServer != null)
                    {
                        lblApplicationDrServer.Text = drServer.Name;
                        lblApplicationDrIpAddress.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);
                        //SetServerImage(drServer.OSType, "DR", "Application");

                        switch (drServer.OSType)
                        {
                            case "AIX":
                                base24DR.Attributes.Add("Class", "icon-aix");
                                break;

                            case "HPUX":
                                base24DR.Attributes.Add("Class", "icon-HPUX");
                                break;

                            case "Linux":
                                base24DR.Attributes.Add("Class", "icon-linux");
                                break;

                            case "Solaris":
                                base24DR.Attributes.Add("Class", "icon-Solaris");
                                break;

                            case "Windows2003":
                                base24DR.Attributes.Add("Class", "icon-Windows");
                                break;

                            case "Windows2008":
                                base24DR.Attributes.Add("Class", "icon-Windows");
                                break;
                            case "Windows2012":
                                base24DR.Attributes.Add("Class", "icon-Windows");
                                break;

                            case "Windows2016":
                                base24DR.Attributes.Add("Class", "icon-Windows");
                                break;
                        }
                    }
                    else
                    {
                        lblApplicationDrServer.Text = "N/A";
                        lblApplicationDrIpAddress.Text = "N/A";
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.CommonUnhandled, "Exception occured while fetching server information(" + prServerid + ")", exc);
            }
        }

        /// <summary>
        ///  It Sets Replication Info for the current infraobject
        /// </summary>
        ///  <author> Meenakshi Patil - 10-02-2015 </author>
        private void GetEc2S3datasyncReplicationDetails(int infraObjectId)
        {
            tblEc2S3datasynccompMonitor.Style.Add("display", "table");
            tblEc2S3datasyncReplication.Style.Add("display", "table");

            tblGlobalMirror.Visible = false;
            tblEMCSRDF.Visible = false;
            tblOracleDataGuard.Visible = false;
            tblFastCopy.Visible = false;
            servicestatus.Visible = false;

            var infraObject = Facade.GetEc2S3DataSyncRepliMonitorByInfraId(infraObjectId);
            if (infraObject != null)
            {
                //  lblrepfile.Text = ReplicationType.EC2S3DataSync.ToDescription();

                lblsrcpath.Text = infraObject.SourceServerPath;
                lbldespath.Text = infraObject.DestinationS3Path;
                lblrepstatus.Text = infraObject.ReplicationStatus;
                switch (lblrepstatus.Text.ToLower())
                {
                    case "running":
                        Iconreplistatus.CssClass = "Replicating";
                        lblrepstatus.CssClass = "active";
                        break;

                    case "Maintainance":
                        Iconreplistatus.CssClass = "Maintenance";
                        lblrepstatus.CssClass = "";
                        break;

                    case "stopped":
                        Iconreplistatus.CssClass = "InActive";
                        lblrepstatus.CssClass = "inactive";
                        break;
                }

                var infraObject1 = Facade.GetInfraObjectById(infraObjectId);
                var prServer = Facade.GetServerById(CurrentInfraObject.PRServerId);
                lblserverhostname.Text = prServer.Name;
                lblec2s3ipaddress.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                if (prServer.Status == ServerStatus.Up)
                {
                    lblserverstatus.CssClass = "health-up";
                }
                else if (prServer.Status == ServerStatus.Down)
                {
                    lblserverstatus.CssClass = "health-down";
                }
                else
                {
                    lblserverstatus.CssClass = "fatal";
                }
                lblops.Text = prServer.OSType;
                switch (lblops.Text)
                {
                    case "AIX":
                        OsIcon.Attributes.Add("Class", "icon-aix");
                        break;

                    case "HPUX":
                        OsIcon.Attributes.Add("Class", "icon-HPUX");
                        break;

                    case "Linux":
                        OsIcon.Attributes.Add("Class", "icon-linux");
                        break;

                    case "Solaris":
                        OsIcon.Attributes.Add("Class", "icon-Solaris");
                        break;

                    case "Windows2003":
                        OsIcon.Attributes.Add("Class", "icon-Windows");
                        break;

                    case "Windows2008":
                        OsIcon.Attributes.Add("Class", "icon-Windows");
                        break;
                    case "Windows2012":
                        OsIcon.Attributes.Add("Class", "icon-Windows");
                        break;

                    case "Windows2016":
                        OsIcon.Attributes.Add("Class", "icon-Windows");
                        break;
                }

                SetEc2S3dataSyncComponentInfo(infraObjectId);
            }
        }

        /// <summary>
        ///  It Set Component Info for the current infraobject
        /// </summary>
        /// <author> Meenakshi Patil - 10-02-2015 </author>
        private void SetEc2S3dataSyncComponentInfo(int infraObjectId)
        {
            var Ec2S3datasynch = Facade.GetEc2S3DataSyncMonitorByInfraId(infraObjectId);

            if (Ec2S3datasynch != null)
            {
                lblinstanceId.Text = Ec2S3datasynch.EC2InstanceId;
                lblinstancestatus.Text = Ec2S3datasynch.EC2InstanceStatus;
                switch (lblinstancestatus.Text.ToLower())
                {
                    case "running":
                        statusIcon.CssClass = "Replicating";
                        lblinstancestatus.CssClass = "active";
                        break;

                    case "stopped":
                        statusIcon.CssClass = "InActive";
                        lblinstancestatus.CssClass = "inactive";
                        break;
                }
                lblEc2InstanceType.Text = Ec2S3datasynch.Ec2InstanceType;
                lblsrcdtpath.Text = Ec2S3datasynch.SourceDataPath;
                lblbucketloc.Text = Ec2S3datasynch.S3BucketLocation;
                lbltmestamp.Text = Ec2S3datasynch.S3BucketTimeStamp;

            }

        }

        public void getxivdetails()
        {
            var cgnamelist = Facade.GetCGDetailsByBaseReplicationId(CurrentInfraObject.PRReplicationId);
            if (cgnamelist != null)
            {
                ddlxivcg.DataSource = cgnamelist;
                ddlxivcg.DataTextField = "PRCGName";
                ddlxivcg.DataValueField = "ID";
                ddlxivcg.DataBind();
            }
            //cg monitoring
            var cgmonilist = Facade.GetByCGId(Convert.ToInt32(ddlxivcg.SelectedValue));
            if (cgmonilist != null)
            {
                lblprcgname.Text = cgmonilist.PRCGName;
                lblseccgname.Text = cgmonilist.DRCGName;
                lblcgactive.Text = cgmonilist.ActiveSatus;
                lblcgrpostatus.Text = cgmonilist.RPOStatus;
                lblcglinkup.Text = cgmonilist.LinkUp;
            }
            else
            {
                lblprcgname.Text = "N/A";
                lblseccgname.Text = "N/A";
                lblcgactive.Text = "N/A";
                lblcgrpostatus.Text = "N/A";
                lblcglinkup.Text = "N/A";
            }
            var cgvolmonilist = Facade.GetCGvolById(Convert.ToInt32(ddlxivcg.SelectedValue));

            if (cgvolmonilist != null)
            {
                lvcgvolmoni.DataSource = cgvolmonilist;
                lvcgvolmoni.DataBind();
            }
            else
            {
                lvcgvolmoni.DataSource = null;
                lvcgvolmoni.DataBind();
            }

            var xivmonilist = Facade.GetXIVMonitoringByCgid(Convert.ToInt32(ddlxivcg.SelectedValue));
            if (xivmonilist != null)
            {
                lvxivstat.DataSource = xivmonilist;
                lvxivstat.DataBind();
            }
            else
            {
                lvxivstat.DataSource = null;
                lvxivstat.DataBind();
            }

            var clustmonilist = Facade.GetclustormonitoringByInfraobjectId(CurrentInfraObject.Id);

            if (clustmonilist != null)
            {
                lvclustmoni.DataSource = clustmonilist;
                lvclustmoni.DataBind();
                var stat = clustmonilist[0];
                lblcluststatus.Text = stat.Status.ToString();
            }
            else
            {
                lvclustmoni.DataSource = null;
                lvclustmoni.DataBind();
            }


        }

        /// <summary>
        /// It Populates Infra Object App Type - EMCSRDF
        /// </summary>
        /// <author> Kuntesh Thakker - 29-04-2014 </author>
        private void GetEmcsrdfStatus()
        {
            var replication = Facade.GetEMCSRDFByReplicationId(CurrentInfraObject.PRReplicationId);

            if (replication != null)
            {
                lblDeviceGroupName.Text = replication.DGroupName;
                lblDeviceGroupType.Text = replication.DGType;
                lblDiskGroupsSymmetrixId.Text = replication.DGSummetrixID;
                lblRemoteSymmtrix.Text = replication.RemoteSymmetrixID;
                lblRdfGroupNumber.Text = replication.RdfRaGroupNumber;
                lblDeviceState.Text = replication.State;
                lblPendingTraks.Text = replication.PendingTracks;
                lblAppDataLg.Text = replication.DataLag;
            }
            else
            {
                lblDeviceGroupName.Text = "N/A";
                lblDeviceGroupType.Text = "N/A";
                lblDiskGroupsSymmetrixId.Text = "N/A";
                lblRemoteSymmtrix.Text = "N/A";
                lblRdfGroupNumber.Text = "N/A";
                lblDeviceState.Text = "N/A";
                lblPendingTraks.Text = "N/A";
                lblAppDataLg.Text = "N/A";
            }
        }


        private void GetRecoveryPointStatus()
        {
            string strProductionSiteName = string.Empty;
            string strDRSiteName = string.Empty;
            string strNearDRSiteName = string.Empty;


            var RepliDetails = Facade.RecoveryPoint_GetByReplicationId(CurrentInfraObject.PRReplicationId);

            if (RepliDetails != null)
            {
                strProductionSiteName = RepliDetails.ProductionSiteName;
                strDRSiteName = RepliDetails.DRSiteName;
                strNearDRSiteName = RepliDetails.NearDRSiteName;

                Label119.Text = strProductionSiteName;
                Label120.Text = strDRSiteName;
                Label121.Text = strNearDRSiteName;
            }


            IList<RecoverPStatisticMonitor> listRecoverPStatisticMonitor = Facade.RecoverPStatisticMonitor_GetByInfraId(CurrentInfraObject.Id);
            if (listRecoverPStatisticMonitor != null && listRecoverPStatisticMonitor.Count > 0)
            {
                if (!string.IsNullOrEmpty(strProductionSiteName))
                {
                    //IList<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
                    //objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strProductionSiteName);

                    List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
                    objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strProductionSiteName).ToList();

                    if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
                    {
                        lblRecoverPointHead.Text = objRecoverPStatisticMonitor[0].CGGroupName != null ? objRecoverPStatisticMonitor[0].CGGroupName : "NA";

                        Label119.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();
                        Label87.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
                        Label91.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();

                        // ReplicationLag
                        Label98.Text = listRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : listRecoverPStatisticMonitor[0].CurrentImage.ToString();

                        Label104.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
                        Label101.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
                        Label107.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
                        Label1820.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
                        Label7.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
                    }

                }


                if (!string.IsNullOrEmpty(strDRSiteName))
                {
                    List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
                    objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strDRSiteName).ToList();
                    if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
                    {

                        lblRecoverPointHead.Text = objRecoverPStatisticMonitor[0].CGGroupName != null ? objRecoverPStatisticMonitor[0].CGGroupName : "NA";


                        if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                        {
                            Label87.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
                            Label91.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();
                            Label1820.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
                            Label7.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
                            Label101.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
                            Label104.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
                            Label107.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
                        }
                        else
                        {

                            Label88.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
                            Label94.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();
                            Label105.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
                            Label102.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
                            Label108.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
                            Label117.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();
                            Label120.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

                            Label5.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
                            Label8.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
                        }

                        //Label88.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
                        //Label94.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();
                        //Label105.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
                        //Label102.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
                        //Label108.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
                        //Label117.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();
                        //Label120.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

                        //Label5.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
                        //Label8.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
                    }
                }

                if (!string.IsNullOrEmpty(strNearDRSiteName))
                {
                    List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();

                    objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strNearDRSiteName).ToList();

                    if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
                    {

                        lblRecoverPointHead.Text = objRecoverPStatisticMonitor[0].CGGroupName != null ? objRecoverPStatisticMonitor[0].CGGroupName : "NA";

                        Label90.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
                        Label97.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();


                        Label106.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
                        Label103.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
                        Label109.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
                        Label118.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();

                        Label121.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

                        Label6.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
                        Label9.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();

                    }
                }
                else
                {
                    hideSite3th.Visible = false;
                    hideJournalLag.Visible = false;
                    hideProtectionWindows.Visible = false;
                    hideSite3td.Visible = false;
                    hideVersion3td.Visible = false;
                    hideLatestImage3td.Visible = false;
                    hideCurrentImage3td.Visible = false;
                    hideReplicationLag3td.Visible = false;
                    hideReplicationLagTime3td.Visible = false;
                    hideReplicationLagData3td.Visible = false;
                    hideReplicationLagwrite3td.Visible = false;
                    hideStorageAccessState3td.Visible = false;
                    hideDataTransferStatus3td.Visible = false;
                    hideDataLag3td.Visible = false;
                }
            }

            List<RecoverPStateMonitor> listRecoverPStateMonitor = Facade.RecoverPStateMonitor_GetByInfraId(CurrentInfraObject.Id).ToList();
            if (listRecoverPStateMonitor != null && listRecoverPStateMonitor.Count > 0)
            {
                Label122.Text = listRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : listRecoverPStateMonitor[0].ApplianceVersion.ToString();
                Label123.Text = listRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : listRecoverPStateMonitor[0].ApplianceVersion.ToString();
                Label124.Text = listRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : listRecoverPStateMonitor[0].ApplianceVersion.ToString();

                if (!string.IsNullOrEmpty(strProductionSiteName))
                {
                    List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
                    objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strProductionSiteName).ToList();

                    if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
                    {
                        //  Label119.Text = objRecoverPStateMonitor[0].Copy.ToString();
                        Label98.Text = objRecoverPStateMonitor[0].Link.ToString();
                        //Label1820.Text = objRecoverPStateMonitor[0].Journal.ToString();

                        Label110.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
                        Label113.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
                        Label122.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();

                        string logged = Label110.Text.ToLower();

                        if (string.IsNullOrEmpty(Label110.Text))
                        {
                            iconLabel110.CssClass = "";
                        }
                        else if (Label110.Text.ToLower().Contains(logged))
                        {
                            iconLabel110.CssClass = "asnych_icon-blue vertical-Middle";
                        }
                        else if (Label110.Text.ToLower().Contains("enableing logged access"))
                        {
                            iconLabel110.CssClass = "icon-on";
                        }
                        else if (Label110.Text.ToLower().Contains("direct access"))
                        {
                            iconLabel110.CssClass = "icon-logsent";
                        }
                        else if (Label110.Text.ToLower().Contains("no access"))
                        {
                            iconLabel110.CssClass = "icon-disable";
                        }


                        if (string.IsNullOrEmpty(Label113.Text))
                        {
                            iconLabel113.CssClass = "";
                        }
                        else if (Label113.Text.ToLower() == "active")
                        {
                            iconLabel113.CssClass = "icon-enable";
                        }
                        else if (Label113.Text.ToLower() == "paused")
                        {
                            iconLabel113.CssClass = "pause";
                        }
                        else if (Label113.Text.ToLower().Contains("initializing"))
                        {
                            iconLabel113.CssClass = "initialize-icon";
                        }
                        else if (Label113.Text.ToLower() == "paused by system")
                        {
                            iconLabel113.CssClass = "pause";
                        }

                        else if (Label113.Text.ToLower() == "ready to replicate")
                        {
                            iconLabel113.CssClass = "readytoreplicate-icon";
                        }
                    }
                }
                if (!string.IsNullOrEmpty(strDRSiteName))
                {
                    List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
                    objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strDRSiteName).ToList();

                    if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
                    {
                        //  Label120.Text = objRecoverPStateMonitor[0].Copy.ToString();
                        Label111.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
                        Label114.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();

                        string logged = Label111.Text.ToLower();

                        if (string.IsNullOrEmpty(Label111.Text))
                        {
                            lbliconStorrageAccessState.CssClass = "";
                        }
                        else if (Label111.Text.ToLower().Contains(logged))
                        {
                            lbliconStorrageAccessState.CssClass = "icon-enable";
                        }
                        else if (Label111.Text.ToLower().Contains("enableing logged access"))
                        {
                            lbliconStorrageAccessState.CssClass = "icon-on";
                        }
                        else if (Label111.Text.ToLower().Contains("direct access"))
                        {
                            lbliconStorrageAccessState.CssClass = "icon-logsent";
                        }
                        else if (Label111.Text.ToLower().Contains("no access"))
                        {
                            lbliconStorrageAccessState.CssClass = "icon-disable";
                        }


                        if (string.IsNullOrEmpty(Label114.Text))
                        {
                            lbliconDataTransferStatus.CssClass = "";
                        }
                        else if (Label114.Text.ToLower() == "active")
                        {
                            lbliconDataTransferStatus.CssClass = "icon-enable";
                        }
                        else if (Label114.Text.ToLower() == "paused")
                        {
                            lbliconDataTransferStatus.CssClass = "pause";
                        }
                        else if (Label114.Text.ToLower().Contains("initializing"))
                        {
                            lbliconDataTransferStatus.CssClass = "initialize-icon";
                        }
                        else if (Label114.Text.ToLower() == "paused by system")
                        {
                            lbliconDataTransferStatus.CssClass = "pause";
                        }

                        else if (Label114.Text.ToLower() == "ready to replicate")
                        {
                            lbliconDataTransferStatus.CssClass = "readytoreplicate-icon";
                        }


                        Label123.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
                        Label99.Text = objRecoverPStateMonitor[0].Link == null ? string.Empty : objRecoverPStateMonitor[0].Link.ToString();
                    }
                }

                if (!string.IsNullOrEmpty(strNearDRSiteName))
                {
                    List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
                    objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strNearDRSiteName).ToList();

                    if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
                    {
                        Label112.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
                        Label115.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
                        Label124.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
                        Label100.Text = objRecoverPStateMonitor[0].Link == null ? string.Empty : objRecoverPStateMonitor[0].Link.ToString();

                        string logged = Label112.Text.ToLower();

                        if (string.IsNullOrEmpty(Label112.Text))
                        {
                            iconLabel112.CssClass = "";
                        }
                        else if (Label112.Text.ToLower().Contains(logged))
                        {
                            iconLabel112.CssClass = "icon-enable";
                        }
                        else if (Label112.Text.ToLower().Contains("enableing logged access"))
                        {
                            iconLabel112.CssClass = "icon-on";
                        }
                        else if (Label112.Text.ToLower().Contains("direct access"))
                        {
                            iconLabel112.CssClass = "icon-logsent";
                        }
                        else if (Label112.Text.ToLower().Contains("no access"))
                        {
                            iconLabel112.CssClass = "icon-disable";
                        }



                        if (string.IsNullOrEmpty(Label115.Text))
                        {
                            iconLabel115.CssClass = "";
                        }
                        else if (Label115.Text.ToLower() == "active")
                        {
                            iconLabel115.CssClass = "icon-enable";
                        }
                        else if (Label115.Text.ToLower() == "paused")
                        {
                            iconLabel115.CssClass = "pause";
                        }
                        else if (Label115.Text.ToLower().Contains("initializing"))
                        {
                            iconLabel115.CssClass = "initialize-icon";
                        }
                        else if (Label115.Text.ToLower() == "paused by system")
                        {
                            iconLabel115.CssClass = "pause";
                        }

                        else if (Label115.Text.ToLower() == "ready to replicate")
                        {
                            iconLabel115.CssClass = "readytoreplicate-icon";
                        }

                    }
                }
                else
                {
                    hideSite3th.Visible = false;
                    hideJournalLag.Visible = false;
                    hideProtectionWindows.Visible = false;
                    hideSite3td.Visible = false;
                    hideVersion3td.Visible = false;
                    hideLatestImage3td.Visible = false;
                    hideCurrentImage3td.Visible = false;
                    hideReplicationLag3td.Visible = false;
                    hideReplicationLagTime3td.Visible = false;
                    hideReplicationLagData3td.Visible = false;
                    hideReplicationLagwrite3td.Visible = false;
                    hideStorageAccessState3td.Visible = false;
                    hideDataTransferStatus3td.Visible = false;
                    hideDataLag3td.Visible = false;
                }
            }

        }


        //private void GetRecoveryPointStatus()
        //{
        //    string strProductionSiteName = string.Empty;
        //    string strDRSiteName = string.Empty;
        //    string strNearDRSiteName = string.Empty;


        //    var RepliDetails = Facade.RecoveryPoint_GetByReplicationId(CurrentInfraObject.PRReplicationId);

        //    if (RepliDetails != null)
        //    {
        //        strProductionSiteName = RepliDetails.ProductionSiteName;
        //        strDRSiteName = RepliDetails.DRSiteName;
        //        strNearDRSiteName = RepliDetails.NearDRSiteName;

        //        Label119.Text = strProductionSiteName;
        //        Label120.Text = strDRSiteName;
        //        Label121.Text = strNearDRSiteName;
        //    }


        //    IList<RecoverPStatisticMonitor> listRecoverPStatisticMonitor = Facade.RecoverPStatisticMonitor_GetByInfraId(CurrentInfraObject.Id);
        //    if (listRecoverPStatisticMonitor != null && listRecoverPStatisticMonitor.Count > 0)
        //    {
        //        if (!string.IsNullOrEmpty(strProductionSiteName))
        //        {
        //            //IList<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //            //objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strProductionSiteName);

        //            List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //            objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strProductionSiteName).ToList();

        //            if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //            {
        //                Label119.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();
        //                Label87.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                Label91.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();

        //                // ReplicationLag
        //                Label98.Text = listRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : listRecoverPStatisticMonitor[0].CurrentImage.ToString();

        //                Label104.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                Label101.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                Label107.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                Label1820.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                Label7.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
        //            }

        //        }


        //        if (!string.IsNullOrEmpty(strDRSiteName))
        //        {
        //            List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //            objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strDRSiteName).ToList();
        //            if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //            {
        //                Label88.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                Label94.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();
        //                Label105.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                Label102.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                Label108.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                Label117.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();
        //                Label120.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

        //                Label5.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                Label8.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
        //            }
        //        }

        //        if (!string.IsNullOrEmpty(strNearDRSiteName))
        //        {
        //            List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();

        //            objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strNearDRSiteName).ToList();

        //            if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //            {
        //                Label90.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                Label97.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();


        //                Label106.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                Label103.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                Label109.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                Label118.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();

        //                Label121.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

        //                Label6.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                Label9.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();

        //            }
        //        }
        //        else
        //        {
        //            hideSite3th.Visible = false;
        //            hideJournalLag.Visible = false;
        //            hideProtectionWindows.Visible = false;
        //            hideSite3td.Visible = false;
        //            hideVersion3td.Visible = false;
        //            hideLatestImage3td.Visible = false;
        //            hideCurrentImage3td.Visible = false;
        //            hideReplicationLag3td.Visible = false;
        //            hideReplicationLagTime3td.Visible = false;
        //            hideReplicationLagData3td.Visible = false;
        //            hideReplicationLagwrite3td.Visible = false;
        //            hideStorageAccessState3td.Visible = false;
        //            hideDataTransferStatus3td.Visible = false;
        //            hideDataLag3td.Visible = false;
        //        }
        //    }

        //    List<RecoverPStateMonitor> listRecoverPStateMonitor = Facade.RecoverPStateMonitor_GetByInfraId(CurrentInfraObject.Id).ToList();
        //    if (listRecoverPStateMonitor != null && listRecoverPStateMonitor.Count > 0)
        //    {
        //        if (!string.IsNullOrEmpty(strProductionSiteName))
        //        {
        //            List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //            objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strProductionSiteName).ToList();

        //            if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //            {
        //                //  Label119.Text = objRecoverPStateMonitor[0].Copy.ToString();
        //                Label110.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                Label113.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
        //                Label122.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();

        //                string logged = Label110.Text.ToLower();

        //                if (string.IsNullOrEmpty(Label110.Text))
        //                {
        //                    iconLabel110.CssClass = "";
        //                }
        //                else if (Label110.Text.ToLower().Contains(logged))
        //                {
        //                    iconLabel110.CssClass = "asnych_icon-blue vertical-Middle";
        //                }
        //                else if (Label110.Text.ToLower().Contains("enableing logged access"))
        //                {
        //                    iconLabel110.CssClass = "icon-on";
        //                }
        //                else if (Label110.Text.ToLower().Contains("direct access"))
        //                {
        //                    iconLabel110.CssClass = "icon-logsent";
        //                }
        //                else if (Label110.Text.ToLower().Contains("no access"))
        //                {
        //                    iconLabel110.CssClass = "icon-disable";
        //                }


        //                if (string.IsNullOrEmpty(Label113.Text))
        //                {
        //                    iconLabel113.CssClass = "";
        //                }
        //                else if (Label113.Text.ToLower() == "active")
        //                {
        //                    iconLabel113.CssClass = "icon-enable";
        //                }
        //                else if (Label113.Text.ToLower() == "paused")
        //                {
        //                    iconLabel113.CssClass = "pause";
        //                }
        //                else if (Label113.Text.ToLower().Contains("initializing"))
        //                {
        //                    iconLabel113.CssClass = "initialize-icon";
        //                }
        //                else if (Label113.Text.ToLower() == "paused by system")
        //                {
        //                    iconLabel113.CssClass = "pause";
        //                }

        //                else if (Label113.Text.ToLower() == "ready to replicate")
        //                {
        //                    iconLabel113.CssClass = "readytoreplicate-icon";
        //                }
        //            }
        //        }
        //        if (!string.IsNullOrEmpty(strDRSiteName))
        //        {
        //            List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //            objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strDRSiteName).ToList();

        //            if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //            {
        //                //  Label120.Text = objRecoverPStateMonitor[0].Copy.ToString();
        //                Label111.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                Label114.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();

        //                string logged = Label111.Text.ToLower();

        //                if (string.IsNullOrEmpty(Label111.Text))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "";
        //                }
        //                else if (Label111.Text.ToLower().Contains(logged))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "icon-enable";
        //                }
        //                else if (Label111.Text.ToLower().Contains("enableing logged access"))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "icon-on";
        //                }
        //                else if (Label111.Text.ToLower().Contains("direct access"))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "icon-logsent";
        //                }
        //                else if (Label111.Text.ToLower().Contains("no access"))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "icon-disable";
        //                }


        //                if (string.IsNullOrEmpty(Label114.Text))
        //                {
        //                    lbliconDataTransferStatus.CssClass = "";
        //                }
        //                else if (Label114.Text.ToLower() == "active")
        //                {
        //                    lbliconDataTransferStatus.CssClass = "icon-enable";
        //                }
        //                else if (Label114.Text.ToLower() == "paused")
        //                {
        //                    lbliconDataTransferStatus.CssClass = "pause";
        //                }
        //                else if (Label114.Text.ToLower().Contains("initializing"))
        //                {
        //                    lbliconDataTransferStatus.CssClass = "initialize-icon";
        //                }
        //                else if (Label114.Text.ToLower() == "paused by system")
        //                {
        //                    lbliconDataTransferStatus.CssClass = "pause";
        //                }

        //                else if (Label114.Text.ToLower() == "ready to replicate")
        //                {
        //                    lbliconDataTransferStatus.CssClass = "readytoreplicate-icon";
        //                }


        //                Label123.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
        //                Label99.Text = objRecoverPStateMonitor[0].Link == null ? string.Empty : objRecoverPStateMonitor[0].Link.ToString();
        //            }
        //        }

        //        if (!string.IsNullOrEmpty(strNearDRSiteName))
        //        {
        //            List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //            objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strNearDRSiteName).ToList();

        //            if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //            {
        //                Label112.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                Label115.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
        //                Label124.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
        //                Label100.Text = objRecoverPStateMonitor[0].Link == null ? string.Empty : objRecoverPStateMonitor[0].Link.ToString();

        //                string logged = Label112.Text.ToLower();

        //                if (string.IsNullOrEmpty(Label112.Text))
        //                {
        //                    iconLabel112.CssClass = "";
        //                }
        //                else if (Label112.Text.ToLower().Contains(logged))
        //                {
        //                    iconLabel112.CssClass = "icon-enable";
        //                }
        //                else if (Label112.Text.ToLower().Contains("enableing logged access"))
        //                {
        //                    iconLabel112.CssClass = "icon-on";
        //                }
        //                else if (Label112.Text.ToLower().Contains("direct access"))
        //                {
        //                    iconLabel112.CssClass = "icon-logsent";
        //                }
        //                else if (Label112.Text.ToLower().Contains("no access"))
        //                {
        //                    iconLabel112.CssClass = "icon-disable";
        //                }



        //                if (string.IsNullOrEmpty(Label115.Text))
        //                {
        //                    iconLabel115.CssClass = "";
        //                }
        //                else if (Label115.Text.ToLower() == "active")
        //                {
        //                    iconLabel115.CssClass = "icon-enable";
        //                }
        //                else if (Label115.Text.ToLower() == "paused")
        //                {
        //                    iconLabel115.CssClass = "pause";
        //                }
        //                else if (Label115.Text.ToLower().Contains("initializing"))
        //                {
        //                    iconLabel115.CssClass = "initialize-icon";
        //                }
        //                else if (Label115.Text.ToLower() == "paused by system")
        //                {
        //                    iconLabel115.CssClass = "pause";
        //                }

        //                else if (Label115.Text.ToLower() == "ready to replicate")
        //                {
        //                    iconLabel115.CssClass = "readytoreplicate-icon";
        //                }

        //            }
        //        }
        //        else
        //        {
        //            hideSite3th.Visible = false;
        //            hideJournalLag.Visible = false;
        //            hideProtectionWindows.Visible = false;
        //            hideSite3td.Visible = false;
        //            hideVersion3td.Visible = false;
        //            hideLatestImage3td.Visible = false;
        //            hideCurrentImage3td.Visible = false;
        //            hideReplicationLag3td.Visible = false;
        //            hideReplicationLagTime3td.Visible = false;
        //            hideReplicationLagData3td.Visible = false;
        //            hideReplicationLagwrite3td.Visible = false;
        //            hideStorageAccessState3td.Visible = false;
        //            hideDataTransferStatus3td.Visible = false;
        //            hideDataLag3td.Visible = false;
        //        }
        //    }

        //}


        //private void GetRecoveryPointStatus()
        //{
        //    string strProductionSiteName = string.Empty;
        //    string strDRSiteName = string.Empty;
        //    string strNearDRSiteName = string.Empty;


        //    var RepliDetails = Facade.RecoveryPoint_GetByReplicationId(CurrentInfraObject.PRReplicationId);

        //    if (RepliDetails != null)
        //    {
        //        strProductionSiteName = RepliDetails.ProductionSiteName;
        //        strDRSiteName = RepliDetails.DRSiteName;
        //        strNearDRSiteName = RepliDetails.NearDRSiteName;
        //    }


        //    IList<RecoverPStatisticMonitor> listRecoverPStatisticMonitor = Facade.RecoverPStatisticMonitor_GetByInfraId(CurrentInfraObject.Id);
        //    if (listRecoverPStatisticMonitor != null && listRecoverPStatisticMonitor.Count > 0)
        //    {
        //        if (!string.IsNullOrEmpty(strProductionSiteName))
        //        {
        //            //IList<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //            //objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strProductionSiteName);

        //            List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //            objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strProductionSiteName).ToList();

        //            if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //            {
        //                Label119.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();
        //                //Label87.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                //Label91.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();

        //                //ReplicationLag
        //                //Label98.Text = listRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : listRecoverPStatisticMonitor[0].CurrentImage.ToString();

        //                Label104.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                Label101.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                Label107.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                Label1820.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                Label7.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
        //            }

        //        }


        //        if (!string.IsNullOrEmpty(strDRSiteName))
        //        {
        //            List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //            objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strDRSiteName).ToList();
        //            if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //            {
        //                Label88.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                Label94.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();
        //                Label105.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                Label102.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                Label108.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                Label117.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();
        //                Label120.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

        //                Label5.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                Label8.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
        //            }
        //        }

        //        if (!string.IsNullOrEmpty(strNearDRSiteName))
        //        {
        //            List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();

        //            objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strNearDRSiteName).ToList();

        //            if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //            {
        //                Label90.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                Label97.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();


        //                Label106.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                Label103.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                Label109.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                Label118.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();

        //                Label121.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

        //                Label6.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                Label9.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();

        //            }
        //        }
        //        else
        //        {
        //            hideSite3th.Visible = false;
        //            hideJournalLag.Visible = false;
        //            hideProtectionWindows.Visible = false;
        //            hideSite3td.Visible = false;
        //            hideVersion3td.Visible = false;
        //            hideLatestImage3td.Visible = false;
        //            hideCurrentImage3td.Visible = false;
        //            hideReplicationLag3td.Visible = false;
        //            hideReplicationLagTime3td.Visible = false;
        //            hideReplicationLagData3td.Visible = false;
        //            hideReplicationLagwrite3td.Visible = false;
        //            hideStorageAccessState3td.Visible = false;
        //            hideDataTransferStatus3td.Visible = false;
        //            hideDataLag3td.Visible = false;
        //        }
        //    }

        //    List<RecoverPStateMonitor> listRecoverPStateMonitor = Facade.RecoverPStateMonitor_GetByInfraId(CurrentInfraObject.Id).ToList();
        //    if (listRecoverPStateMonitor != null && listRecoverPStateMonitor.Count > 0)
        //    {
        //        if (!string.IsNullOrEmpty(strProductionSiteName))
        //        {
        //            List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //            objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strProductionSiteName).ToList();

        //            if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //            {
        //                Label119.Text = objRecoverPStateMonitor[0].Copy.ToString();
        //                Label110.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                Label113.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
        //                Label122.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
        //            }
        //        }
        //        if (!string.IsNullOrEmpty(strDRSiteName))
        //        {
        //            List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //            objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strDRSiteName).ToList();

        //            if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //            {
        //                Label120.Text = objRecoverPStateMonitor[0].Copy.ToString();
        //                Label111.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                Label114.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();

        //                string logged = Label111.Text.ToLower();

        //                if (string.IsNullOrEmpty(Label111.Text))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "";
        //                }
        //                else if (Label111.Text.ToLower().Contains(logged))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "icon-enable";
        //                }
        //                else if (Label111.Text.ToLower().Contains("enableing logged access"))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "icon-on";
        //                }
        //                else if (Label111.Text.ToLower().Contains("direct access"))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "icon-logsent";
        //                }
        //                else if (Label111.Text.ToLower().Contains("no access"))
        //                {
        //                    lbliconStorrageAccessState.CssClass = "icon-disable";
        //                }


        //                if (string.IsNullOrEmpty(Label114.Text))
        //                {
        //                    lbliconDataTransferStatus.CssClass = "";
        //                }
        //                else if (Label114.Text.ToLower() == "active")
        //                {
        //                    lbliconDataTransferStatus.CssClass = "icon-enable";
        //                }
        //                else if (Label114.Text.ToLower() == "paused")
        //                {
        //                    lbliconDataTransferStatus.CssClass = "pause";
        //                }
        //                else if (Label114.Text.ToLower().Contains("initializing"))
        //                {
        //                    lbliconDataTransferStatus.CssClass = "initialize-icon";
        //                }
        //                else if (Label114.Text.ToLower() == "paused by system")
        //                {
        //                    lbliconDataTransferStatus.CssClass = "pause";
        //                }


        //                Label123.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
        //                Label99.Text = objRecoverPStateMonitor[0].Link == null ? string.Empty : objRecoverPStateMonitor[0].Link.ToString();
        //            }
        //        }

        //        if (!string.IsNullOrEmpty(strNearDRSiteName))
        //        {
        //            List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //            objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strNearDRSiteName).ToList();

        //            if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //            {
        //                Label112.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                Label115.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
        //                Label124.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
        //                Label100.Text = objRecoverPStateMonitor[0].Link == null ? string.Empty : objRecoverPStateMonitor[0].Link.ToString();
        //            }
        //        }
        //    }

        //}



        //private void GetRecoveryPointStatus()
        // {
        //     string strProductionSiteName = string.Empty;
        //     string strDRSiteName = string.Empty;
        //     string strNearDRSiteName = string.Empty;


        //     var RepliDetails = Facade.RecoveryPoint_GetByReplicationId(CurrentInfraObject.PRReplicationId);

        //     if (RepliDetails!= null)
        //     {
        //         strProductionSiteName = RepliDetails.ProductionSiteName;
        //         strDRSiteName= RepliDetails.DRSiteName;
        //         strNearDRSiteName = RepliDetails.NearDRSiteName;
        //     }


        //     IList<RecoverPStatisticMonitor> listRecoverPStatisticMonitor = Facade.RecoverPStatisticMonitor_GetByInfraId(CurrentInfraObject.Id);
        //     if (listRecoverPStatisticMonitor != null && listRecoverPStatisticMonitor.Count > 0)
        //     {
        //         if (!string.IsNullOrEmpty(strProductionSiteName))
        //         {
        //             //IList<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //             //objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strProductionSiteName);

        //             List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //             objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strProductionSiteName).ToList();

        //             if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //             {
        //                 Label119.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();
        //                 //Label87.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                 //Label91.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();

        //                 //ReplicationLag
        //                 //Label98.Text = listRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : listRecoverPStatisticMonitor[0].CurrentImage.ToString();

        //                 Label104.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                 Label101.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                 Label107.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                 Label1820.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                 Label7.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
        //             }

        //         }


        //         if (!string.IsNullOrEmpty(strDRSiteName))
        //         {
        //             List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();
        //             objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strDRSiteName).ToList();
        //             if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //             {
        //                 Label88.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                 Label94.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();
        //                 Label105.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                 Label102.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                 Label108.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                 Label117.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();
        //                 Label120.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

        //                 Label5.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                 Label8.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();
        //             }
        //         }

        //         if (!string.IsNullOrEmpty(strNearDRSiteName))
        //         {
        //             List<RecoverPStatisticMonitor> objRecoverPStatisticMonitor = new List<RecoverPStatisticMonitor>();

        //             objRecoverPStatisticMonitor = listRecoverPStatisticMonitor.Where(c => c.siteName == strNearDRSiteName).ToList();

        //             if (objRecoverPStatisticMonitor != null && objRecoverPStatisticMonitor.Count > 0)
        //             {
        //                 Label90.Text = objRecoverPStatisticMonitor[0].LastImage == null ? string.Empty : objRecoverPStatisticMonitor[0].LastImage.ToString();
        //                 Label97.Text = objRecoverPStatisticMonitor[0].CurrentImage == null ? string.Empty : objRecoverPStatisticMonitor[0].CurrentImage.ToString();


        //                 Label106.Text = objRecoverPStatisticMonitor[0].ReplicationLagData == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagData.ToString();
        //                 Label103.Text = objRecoverPStatisticMonitor[0].ReplicationLagTime == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagTime.ToString();
        //                 Label109.Text = objRecoverPStatisticMonitor[0].ReplicationLagWriter == null ? string.Empty : objRecoverPStatisticMonitor[0].ReplicationLagWriter.ToString();
        //                 Label118.Text = objRecoverPStatisticMonitor[0].DataLag == null ? string.Empty : objRecoverPStatisticMonitor[0].DataLag.ToString();

        //                 Label121.Text = objRecoverPStatisticMonitor[0].siteName == null ? string.Empty : objRecoverPStatisticMonitor[0].siteName.ToString();

        //                 Label6.Text = objRecoverPStatisticMonitor[0].Journal_Lag == null ? string.Empty : objRecoverPStatisticMonitor[0].Journal_Lag.ToString();
        //                 Label9.Text = objRecoverPStatisticMonitor[0].ProtectionWindow == null ? string.Empty : objRecoverPStatisticMonitor[0].ProtectionWindow.ToString();

        //             }
        //         }
        //         else
        //         {
        //             hideSite3th.Visible = false;
        //             hideJournalLag.Visible = false;
        //             hideProtectionWindows.Visible = false;
        //             hideSite3td.Visible = false;
        //             hideVersion3td.Visible = false;
        //             hideLatestImage3td.Visible = false;
        //             hideCurrentImage3td.Visible = false;
        //             hideReplicationLag3td.Visible = false;
        //             hideReplicationLagTime3td.Visible = false;
        //             hideReplicationLagData3td.Visible = false;
        //             hideReplicationLagwrite3td.Visible = false;
        //             hideStorageAccessState3td.Visible = false;
        //             hideDataTransferStatus3td.Visible = false;
        //             hideDataLag3td.Visible = false;
        //         }
        //     }

        //     List<RecoverPStateMonitor> listRecoverPStateMonitor = Facade.RecoverPStateMonitor_GetByInfraId(CurrentInfraObject.Id).ToList();
        //     if (listRecoverPStateMonitor != null && listRecoverPStateMonitor.Count > 0)
        //     {
        //         if (!string.IsNullOrEmpty(strProductionSiteName))
        //         {
        //             List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //             objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strProductionSiteName).ToList();

        //             if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //             {
        //                 Label119.Text = strProductionSiteName.ToString();
        //                 Label110.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                 Label113.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
        //                 Label122.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
        //             }
        //         }
        //         if (!string.IsNullOrEmpty(strDRSiteName))
        //         {
        //             List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //             objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strDRSiteName).ToList();

        //             if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //             {
        //                 Label111.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                 Label114.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
        //                 Label123.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
        //                 Label99.Text = objRecoverPStateMonitor[0].Link == null ? string.Empty : objRecoverPStateMonitor[0].Link.ToString();
        //             }
        //         }

        //         if (!string.IsNullOrEmpty(strNearDRSiteName))
        //         {
        //             List<RecoverPStateMonitor> objRecoverPStateMonitor = new List<RecoverPStateMonitor>();
        //             objRecoverPStateMonitor = listRecoverPStateMonitor.Where(c => c.siteName == strNearDRSiteName).ToList();

        //             if (objRecoverPStateMonitor != null && objRecoverPStateMonitor.Count > 0)
        //             {
        //                 Label112.Text = objRecoverPStateMonitor[0].StorageAccess.ToString();
        //                 Label115.Text = objRecoverPStateMonitor[0].DataTransferStatus.ToString();
        //                 Label124.Text = objRecoverPStateMonitor[0].ApplianceVersion == null ? string.Empty : objRecoverPStateMonitor[0].ApplianceVersion.ToString();
        //                 Label100.Text = objRecoverPStateMonitor[0].Link == null ? string.Empty : objRecoverPStateMonitor[0].Link.ToString();
        //             }
        //         }
        //     }

        // }
        /// <summary>
        /// It Populates Infra Object App Type - GlobalMirror
        /// </summary>
        /// <author> Kuntesh Thakker - 29-04-2014 </author>
        private void GetGlolbalMirorStatus()
        {
            var globalMirror = Facade.GetGlobalMirrorByReplicationId(CurrentInfraObject.PRReplicationId);

            if (globalMirror != null)
            {
                var globalMirrorReplication = Facade.GetCurrentGlobalMirrorMonitorByStorageImageId(globalMirror.PRStorageImageId);

                if (globalMirrorReplication != null)
                {
                    lblCopyState.Text = globalMirrorReplication.CopyState;

                    switch (lblCopyState.Text.ToLower())
                    {
                        case "running":
                            lblHealthIcon.CssClass = "health-up";
                            lblCopyState.CssClass = "text-success";
                            break;

                        case "paused":
                        case "pausing":
                            lblHealthIcon.CssClass = "health-down";
                            lblCopyState.CssClass = "text-danger";
                            break;

                        case "fatal":
                            lblHealthIcon.CssClass = "heath-down";
                            lblCopyState.CssClass = "text-danger";
                            break;
                    }
                    lblFatal.Text = globalMirrorReplication.FatalReason;
                    lblCurrentTime.Text = globalMirrorReplication.CurrentTime;
                    lblCGTime.Text = globalMirrorReplication.CGTime;
                    lblCGPerc.Text = globalMirrorReplication.SuccessfulCGPercentage;
                    lblseqNo.Text = globalMirrorReplication.FlashCopySequenceNumber;

                    lblGMRID.Text = globalMirrorReplication.GmrResultId;
                    lblMasterCount.Text = globalMirrorReplication.MasterCount;
                    lblMasterSessionID.Text = globalMirrorReplication.MasterSessionId;
                    lblCordTime.Text = globalMirrorReplication.CoordTime;
                    lblMaxDrainTime.Text = globalMirrorReplication.MaxCGDrainTime;
                    lblCGIntervalTime.Text = globalMirrorReplication.CGIntervalTime;
                    lblMasterId.Text = globalMirrorReplication.MasterId;
                }
                else
                {
                    lblFatal.Text = "N/A";
                    lblCurrentTime.Text = "N/A";
                    lblCGTime.Text = "N/A";
                    lblCGPerc.Text = "N/A";
                    lblseqNo.Text = "N/A";

                    lblGMRID.Text = "N/A";
                    lblMasterCount.Text = "N/A";
                    lblMasterSessionID.Text = "N/A";
                    lblCordTime.Text = "N/A";
                    lblMaxDrainTime.Text = "N/A";
                    lblCGIntervalTime.Text = "N/A";
                    lblMasterId.Text = "N/A";
                }
            }
            else
            {
                lblFatal.Text = "N/A";
                lblCurrentTime.Text = "N/A";
                lblCGTime.Text = "N/A";
                lblCGPerc.Text = "N/A";
                lblseqNo.Text = "N/A";

                lblGMRID.Text = "N/A";
                lblMasterCount.Text = "N/A";
                lblMasterSessionID.Text = "N/A";
                lblCordTime.Text = "N/A";
                lblMaxDrainTime.Text = "N/A";
                lblCGIntervalTime.Text = "N/A";
                lblMasterId.Text = "N/A";
            }
        }

        /// <summary>
        /// It Populates Infra Object App Type Info
        /// </summary>
        /// <author> Kuntesh Thakker - 29-04-2014 </author>
        private void PopulateInfraObjectAppInfo()
        {
            tblSnapComponent.Style.Add("display", "none");
            tblEc2S3datasynccompMonitor.Style.Add("display", "none");
            //tblMSSqlNetAppSnapMirrorCompMonitor.Style.Add("display", "none");
            if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
            {
                var prServer = Facade.GetServerById(CurrentInfraObject.DRServerId);
                var drServer = Facade.GetServerById(CurrentInfraObject.PRServerId);
                if (prServer != null)
                {
                    if (prServer.Status == ServerStatus.Up)
                    {
                        spnApphealthPR.Attributes.Add("class", "health-up");
                    }
                    else if (prServer.Status == ServerStatus.Pending)
                    {
                        spnApphealthPR.Attributes.Add("class", "fatal");
                    }
                    else
                    {
                        spnApphealthPR.Attributes.Add("class", "health-down");
                    }
                    lblPRHost.Text = prServer.Name;
                    lblPRIP.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);

                    switch (prServer.OSType)
                    {
                        case "AIX":
                            spnPrHostIcon.Attributes.Add("Class", "icon-aix");
                            break;

                        case "HPUX":
                            spnPrHostIcon.Attributes.Add("Class", "icon-HPUX");
                            break;

                        case "Linux":
                            spnPrHostIcon.Attributes.Add("Class", "icon-linux");
                            break;

                        case "Solaris":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Solaris");
                            break;

                        case "Windows2003":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2008":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;
                        case "Windows2012":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2016":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;
                    }                 
                }
                else
                {
                    spnPrHostIcon.Attributes.Add("Class", "icon-NA");
                    lblPRHost.Text = "N/A";
                    lblPRIP.Text = "N/A";
                }
                if (drServer != null)
                {
                    if (drServer.Status == ServerStatus.Up)
                    {
                        spnApphealthDR.Attributes.Add("class", "health-up");
                    }
                    else if (drServer.Status == ServerStatus.Pending)
                    {
                        spnApphealthDR.Attributes.Add("class", "fatal");
                    }
                    else
                    {
                        spnApphealthDR.Attributes.Add("class", "health-down");
                    }
                    lblDRHost.Text = drServer.Name;
                    lblDRIP.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);

                    switch (drServer.OSType)
                    {
                        case "AIX":
                            spnDrHostIcon.Attributes.Add("Class", "icon-aix");
                            break;

                        case "HPUX":
                            spnDrHostIcon.Attributes.Add("Class", "icon-HPUX");
                            break;

                        case "Linux":
                            spnDrHostIcon.Attributes.Add("Class", "icon-linux");
                            break;

                        case "Solaris":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Solaris");
                            break;

                        case "Windows2003":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2008":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;
                        case "Windows2012":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2016":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;
                    }
                }
                else
                {
                    spnDrHostIcon.Attributes.Add("Class", "icon-NA");
                    lblDRHost.Text = "N/A";
                    lblDRIP.Text = "N/A";
                }
            }
            else
            {
                var prServer = Facade.GetServerById(CurrentInfraObject.PRServerId);
                Server drServer = null;
                if (CurrentInfraObject.DRServerId != 0)
                {
                    drServer = Facade.GetServerById(CurrentInfraObject.DRServerId);
                }
                if (prServer != null)
                {
                    if (prServer.Status == ServerStatus.Up)
                    {
                        spnApphealthPR.Attributes.Add("class", "health-up");
                    }
                    else if (prServer.Status == ServerStatus.Pending)
                    {
                        spnApphealthPR.Attributes.Add("class", "fatal");
                    }
                    else
                    {
                        spnApphealthPR.Attributes.Add("class", "health-down");
                    }
                    lblPRHost.Text = prServer.Name;
                    lblPRIP.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);

                    switch (prServer.OSType)
                    {
                        case "AIX":
                            spnPrHostIcon.Attributes.Add("Class", "icon-aix");
                            break;

                        case "HPUX":
                            spnPrHostIcon.Attributes.Add("Class", "icon-HPUX");
                            break;

                        case "Linux":
                            spnPrHostIcon.Attributes.Add("Class", "icon-linux");
                            break;

                        case "Solaris":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Solaris");
                            break;

                        case "Windows2003":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2008":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;
                        case "Windows2012":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2016":
                            spnPrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;
                    }                 
                }
                else
                {
                    spnPrHostIcon.Attributes.Add("Class", "icon-NA");
                    lblPRHost.Text = "N/A";
                    lblPRIP.Text = "N/A";
                }
                if (drServer != null)
                {

                    if (drServer != null && drServer.Status == ServerStatus.Up)
                    {
                        spnApphealthDR.Attributes.Add("class", "health-up");
                    }
                    else if (drServer != null && drServer.Status == ServerStatus.Pending)
                    {
                        spnApphealthDR.Attributes.Add("class", "fatal");
                    }
                    else
                    {
                        spnApphealthDR.Attributes.Add("class", "health-down");
                    }
                    lblDRHost.Text = drServer.Name;
                    lblDRIP.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);

                    switch (drServer.OSType)
                    {
                        case "AIX":
                            spnDrHostIcon.Attributes.Add("Class", "icon-aix");
                            break;

                        case "HPUX":
                            spnDrHostIcon.Attributes.Add("Class", "icon-HPUX");
                            break;

                        case "Linux":
                            spnDrHostIcon.Attributes.Add("Class", "icon-linux");
                            break;

                        case "Solaris":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Solaris");
                            break;

                        case "Windows2003":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2008":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;
                        case "Windows2012":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;

                        case "Windows2016":
                            spnDrHostIcon.Attributes.Add("Class", "icon-Windows");
                            break;
                    }
                }
                else
                {
                    spnDrHostIcon.Attributes.Add("Class", "icon-NA");
                    lblDRHost.Text = "N/A";
                    lblDRIP.Text = "N/A";

                    //drServer = int.Parse(0); ;
                }

            }

            lblApplicationName.Text = CurrentInfraObject.Name;
        }

        /// <summary>
        /// It Populates Infra Object App Type - DataSync
        /// </summary>
        /// <author> Kuntesh Thakker - 29-04-2014 </author>
        //private void GetFastcopyStatus()
        //{
        //    var fastcopy = Facade.GetFastCopyMonitorByInfraObjectId(CurrentInfraObject.Id);
        //    lblFastcopyRepType.Text = "DataSync";
        //    if (fastcopy != null)
        //    {
        //        lblFastcopyLastFileName.Text = fastcopy.LastFileName;
        //        lblFastcopyLastFileSize.Text = fastcopy.LastFileSize;

        //        var fastcopyId = Facade.GetFastCopyByReplicationId(CurrentInfraObject.PRReplicationId);
        //        IList<FastCopyJob> fastcopyjob = Facade.GetFastCopyJobByFastCopyId(fastcopyId.Id);
        //        if (fastcopyjob != null)
        //        {
        //            var s1 = "";
        //            var s4 = "";
        //            var s2 = "";
        //            var s3 = "";
        //            foreach (FastCopyJob oFastcopy in fastcopyjob)
        //            {
        //                s1 += oFastcopy.SourceDirectory + ",";
        //                s4 += oFastcopy.DestinationDirectory + ",";
        //            }

        //            s2 = s1.Substring(0, s1.LastIndexOf(","));
        //            s3 = s4.Substring(0, s4.LastIndexOf(","));

        //            lblFastcopySourceDirectory.Text = s2;
        //            lblFastcopyTargetDirectory.Text = s3;
        //            // lbltotreplicationJobPairs.Text = fastcopyjob.Count.ToString();
        //            lbltotreplicationJobPairs.Text = fastcopy.IncrementalFilesCount;
        //        }
        //        else
        //        {
        //            lblFastcopySourceDirectory.Text = "N/A";
        //            lblFastcopyTargetDirectory.Text = "N/A";
        //            lblFastcopyLastFileSize.Text = "N/A";
        //        }
        //    }
        //    else
        //    {
        //        lblFastcopySourceDirectory.Text = "N/A";
        //        lblFastcopyTargetDirectory.Text = "N/A";
        //        lbltotreplicationJobPairs.Text = "0";
        //        lblFastcopyLastFileName.Text = "N/A";
        //        lblFastcopyLastFileSize.Text = "N/A";
        //    }
        //}

        private void GetFastcopyStatus()
        {
            //IList<FastCopyMonitor> fastcopy = Facade.GetFastCopyMoniStatusByInfraId(CurrentInfraObject.Id);

            IList<FastCopyMonitor> fastcopyAll = Facade.GetAllFastCopyMonitors();
            var fastcopy = (from fstcpy in fastcopyAll where fstcpy.InfraObjectId == CurrentInfraObject.Id select fstcpy);

            lblFastcopyRepType.Text = "DataSync";
            if (fastcopy != null)
            {
                var _LastFileName = "";
                var _LastFileSize = "";
                int _IncrementalFilesCount = 0;

                foreach (var _fastcopy in fastcopy)
                {
                    if (!string.IsNullOrEmpty(_fastcopy.LastFileName) && !_fastcopy.LastFileName.ToLower().EqualsIgnoreCase("No File Changes"))
                    {
                        _LastFileName = _fastcopy.LastFileName;
                        _LastFileSize = _fastcopy.LastFileSize;
                    }
                    _IncrementalFilesCount = _IncrementalFilesCount + Convert.ToInt32(_fastcopy.IncrementalFilesCount);
                }
                lblFastcopyLastFileName.Text = _LastFileName.Trim(',');
                if (_LastFileName != "")
                {
                    lblFastcopyLastFileSize.Text = _LastFileSize.Trim(',') + " " + "Bytes";
                }
                lbltotreplicationJobPairs.Text = Convert.ToString(_IncrementalFilesCount);

                var fastcopyId = Facade.GetFastCopyByReplicationId(CurrentInfraObject.PRReplicationId);
                IList<FastCopyJob> fastcopyjob = Facade.GetFastCopyJobByFastCopyId(fastcopyId.Id);
                if (fastcopyjob != null)
                {
                    var s1 = "";
                    var s4 = "";
                    var s2 = "";
                    var s3 = "";
                    foreach (FastCopyJob oFastcopy in fastcopyjob)
                    {
                        s1 += oFastcopy.SourceDirectory + ",";
                        s4 += oFastcopy.DestinationDirectory + ",";
                    }

                    s2 = s1.Substring(0, s1.LastIndexOf(","));
                    s3 = s4.Substring(0, s4.LastIndexOf(","));

                    if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                    {
                        lblFastcopySourceDirectory.Text = s3;
                        lblFastcopyTargetDirectory.Text = s2;
                    }
                    else
                    {
                        lblFastcopySourceDirectory.Text = s2;
                        lblFastcopyTargetDirectory.Text = s3;
                    }
                    // lbltotreplicationJobPairs.Text = fastcopyjob.Count.ToString();
                    //lbltotreplicationJobPairs.Text = fastcopy.IncrementalFilesCount;
                }
                else
                {
                    lblFastcopySourceDirectory.Text = "N/A";
                    lblFastcopyTargetDirectory.Text = "N/A";
                    lblFastcopyLastFileSize.Text = "N/A";
                }
            }
            else
            {
                lblFastcopySourceDirectory.Text = "N/A";
                lblFastcopyTargetDirectory.Text = "N/A";
                lbltotreplicationJobPairs.Text = "0";
                lblFastcopyLastFileName.Text = "N/A";
                lblFastcopyLastFileSize.Text = "N/A";
            }
        }

        /// <summary>
        /// Get Status
        /// </summary>
        /// <param name="values"></param>
        /// <returns></returns>
        /// <author> Kuntesh Thakker - 29-04-2014 </author>
        public string GetStatus(object values)
        {
            var status = Convert.ToBoolean(values);

            return status ? "Running" : "Stopped";
        }

        /// <summary>
        /// Gets Status Icons
        /// </summary>
        /// <param name="values"></param>
        /// <returns></returns>
        /// <author> Kuntesh Thakker - 29-04-2014 </author>
        public string GetStatusIcons(object values)
        {
            var status = Convert.ToBoolean(values);

            return status ? "health-up" : "health-down";
        }

        private void PopulateSRMVMwareDetails(int infraObjectId)
        {
            tblSRMVMwareComponent.Visible = true;
            tblSRMVMwareComponent.Style.Add("display", "table");
            tblSRMVMwareReplication.Style.Add("display", "table");
            var CurrentComponentInfo = new SRMVmwareMonitor();

            var prServer = Facade.GetServerById(CurrentInfraObject.PRServerId);
            if (prServer != null)
            {
                lblvCenterServerPR.Text = prServer.IPAddress.Contains("N/A") ? "N/A" : CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                lblvCenterPortPR.Text = Convert.ToString(prServer.Port);

            }
            var drServer = Facade.GetServerById(CurrentInfraObject.DRServerId);
            if (drServer != null)
            {
                lblvCenterServerDR.Text = drServer.IPAddress.Contains("N/A") ? "N/A" : CryptographyHelper.Md5Decrypt(drServer.IPAddress);
                lblvCenterPortDR.Text = Convert.ToString(drServer.Port);

            }

            var prServerSRM = Facade.GetServerById(CurrentInfraObject.PRServerId2);
            if (prServerSRM != null)
            {
                lblSRMServerPR.Text = prServerSRM.IPAddress.Contains("N/A") ? "N/A" : CryptographyHelper.Md5Decrypt(prServerSRM.IPAddress);
                lblSRMServerPortPR.Text = Convert.ToString(prServerSRM.Port);

                var sitedr = Facade.GetSiteById(prServerSRM.SiteId);
                lblSitePR.Text = sitedr.Name;
                lblSiteStatusPR.Text = prServerSRM.Status.ToString() == "Down" ? "Disconnected" : "Connected";
                if (lblSiteStatusPR.Text == "Connected")
                {
                    Span5.Attributes.Add("class", "icon-enable");
                    Span71.Attributes.Add("class", "health-up");
                    Span7.Attributes.Add("class", "health-up");
                }
                else
                {
                    Span5.Attributes.Add("class", "icon-disable");
                    Span71.Attributes.Add("class", "health-down");
                    Span7.Attributes.Add("class", "health-down");
                }

            }

            var drServerSRM = Facade.GetServerById(CurrentInfraObject.DRServerId2);
            if (drServerSRM != null)
            {
                lblSRMServerDR.Text = drServerSRM.IPAddress.Contains("N/A") ? "N/A" : CryptographyHelper.Md5Decrypt(drServerSRM.IPAddress);
                lblSRMServerPortDR.Text = Convert.ToString(drServerSRM.Port);

                var site = Facade.GetSiteById(drServerSRM.SiteId);
                lblSiteDR.Text = site.Name;

                lblSiteStatusDR.Text = drServerSRM.Status.ToString() == "Down" ? "Disconnected" : "Connected";
                if (lblSiteStatusDR.Text == "Connected")
                {
                    Span6.Attributes.Add("class", "icon-enable");
                    Span72.Attributes.Add("class", "health-up");
                    Span8.Attributes.Add("class", "health-up");
                }
                else
                {
                    Span6.Attributes.Add("class", "icon-disable");
                    Span72.Attributes.Add("class", "health-down");
                    Span8.Attributes.Add("class", "health-down");
                }

            }



            CurrentComponentInfo = Facade.GetSRMVmWareMonitorByInfraObjectId(infraObjectId);
            if (CurrentComponentInfo != null)
            {

                lblvCrenterVersionPR.Text = CurrentComponentInfo.VCenterVersionPR;
                lblvCrenterBuildPR.Text = CurrentComponentInfo.VCenterBuildPR;
                lblvCenterVersionDR.Text = CurrentComponentInfo.VCenterVersionDR;
                lblvCrenterBuildDR.Text = CurrentComponentInfo.VCenterBuildDR;

                lblSRMVersionPR.Text = CurrentComponentInfo.SRMVersionPR;
                lblSRMBuildPR.Text = CurrentComponentInfo.SRMBuildPR;
                lblSRMVersionDR.Text = CurrentComponentInfo.SRMVersionDR;
                lblSRMBuildDR.Text = CurrentComponentInfo.SRMBuildDR;

                //Protection Group & Recovery Plan Monitoring Parameters

                lblPRotectionGrpNamePR.Text = CurrentComponentInfo.ProtectionGroupsNamePR;
                lblPGTypePR.Text = CurrentComponentInfo.ProtectionGroupsNameTypePR;
                lblPGStatePR.Text = CurrentComponentInfo.ProtectionGroupsNameStatePR;
                lblVMCountPR.Text = CurrentComponentInfo.ProtectionGroupNameVMCountPR;

                lblPRotectionGrpNameDR.Text = CurrentComponentInfo.ProtectionGroupsNameDR;
                lblPGTypeDR.Text = CurrentComponentInfo.ProtectionGroupsNameTypeDR;
                lblPGStateDR.Text = CurrentComponentInfo.ProtectionGroupsNameStateDR;
                lblVMCountDR.Text = CurrentComponentInfo.ProtectionGroupNameVMCountDR;

                lblRecoveryPlanNamePR.Text = CurrentComponentInfo.RecoveryPlanNamePR;
                lblRecoveryPlanStatePR.Text = CurrentComponentInfo.RecoveryPlanStatePR;
                lblRecoveryPlanNameDR.Text = CurrentComponentInfo.RecoveryPlanNameDR;
                lblRecoveryPlanStateDR.Text = CurrentComponentInfo.RecoveryPlanStateDR;

                lblHPNamePR.Text = CurrentComponentInfo.RecoveryPlanHistoryNamePR;
                lblLastRunDatePR.Text = CurrentComponentInfo.RecoveryPlanHistoryLastRunDatePR;
                lblHistoryStatePR.Text = CurrentComponentInfo.RecoveryPlanHistoryStatePR;
                lblTotalTimePR.Text = CurrentComponentInfo.RecoveryPlanHistoryTotalTimePR;

                lblHPNameDR.Text = CurrentComponentInfo.RecoveryPlanHistoryNameDR;
                lblLastRunDateDR.Text = CurrentComponentInfo.RecoveryPlanHistoryLastRunDateDR;
                lblHistoryStateDR.Text = CurrentComponentInfo.RecoveryPlanHistoryStateDR;
                lblTotalTimeDR.Text = CurrentComponentInfo.RecoveryPlanHistoryTotalTimeDR;
            }
        }

        protected void MonitorService_OnItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                Label ServiceId = (Label)e.Item.FindControl("ServiceId");
                Label WorkflowactionId = (Label)e.Item.FindControl("WorkflowactionId");
                Label lblServiceName = (Label)e.Item.FindControl("lblServiceName");
                Label lblIpAddress = (Label)e.Item.FindControl("lblIpAddress");
                Label lblStatus = (Label)e.Item.FindControl("lblStatus");
                ListViewDataItem dataItem = (ListViewDataItem)e.Item;
                var rowView = dataItem.DataItem as MonitorServiceStatusLogs;

                var serviceid = Convert.ToInt32(ServiceId.Text);
                var monitorServices = Facade.GetMonitorServicesById(serviceid);
                if (monitorServices != null)
                {
                    if (monitorServices.WorkflowId > 0)
                    {
                        if (Convert.ToInt32(WorkflowactionId.Text) > 0)
                        {
                            var workflowdetails = Facade.GetWorkflowActionById(Convert.ToInt32(WorkflowactionId.Text));

                            lblServiceName.Text = workflowdetails.Name;

                            if (workflowdetails.ServerId > 0)
                            {
                                var _server = Facade.GetServerById(workflowdetails.ServerId);
                                if (_server != null)
                                    lblIpAddress.Text = CryptographyHelper.Md5Decrypt(_server.IPAddress);
                            }
                            else
                            {
                                var _server = Facade.GetServerById(monitorServices.ServerId);
                                if (_server != null)
                                    lblIpAddress.Text = CryptographyHelper.Md5Decrypt(_server.IPAddress);
                            }

                        }
                    }
                    else
                    {
                        lblServiceName.Text = monitorServices.ServicePath;
                        var _server = Facade.GetServerById(monitorServices.ServerId);
                        if (_server != null)
                            lblIpAddress.Text = CryptographyHelper.Md5Decrypt(_server.IPAddress);
                    }

                }
            }
        }

        public string GetDeviceClass(object className)
        {
            var classValue = "";
            if (className.ToString().ToLower() == "pair")
            {
                classValue = "icon-state";
            }
            else if (className.ToString().ToLower() == "copy")
            {
                classValue = "Replicating";
            }
            else if (className.ToString().ToUpper() == "PSUS")
            {
                classValue = "InActive";
            }
            else if (className.ToString().ToUpper() == "SSUS")
            {
                classValue = "InActive";
            }

            return classValue;
        }

        protected void LvHitachiDeviceItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType != ListViewItemType.DataItem) return;
            var dataItem = (ListViewDataItem)e.Item;
            var dataKey = lstHitachiURDeviceMonitoring.DataKeys[dataItem.DataItemIndex];
            if (dataKey == null) return;
            var hdfDeviceName = e.Item.FindControl("hdfDeviceName") as HiddenField;
            ((Label)lstHitachiURDeviceMonitoring.FindControl("lblDeviceGroupName")).Text = hdfDeviceName.Value;
        }

        protected void ddlxivcg_SelectedIndexChanged(object sender, EventArgs e)
        {
            var cgmonilist = Facade.GetByCGId(Convert.ToInt32(ddlxivcg.SelectedValue));
            if (cgmonilist != null)
            {
                lblprcgname.Text = cgmonilist.PRCGName;
                lblseccgname.Text = cgmonilist.DRCGName;
                lblcgactive.Text = cgmonilist.ActiveSatus;
                lblcgrpostatus.Text = cgmonilist.RPOStatus;
                lblcglinkup.Text = cgmonilist.LinkUp;


            }
            else
            {
                lblprcgname.Text = "N/A";
                lblseccgname.Text = "N/A";
                lblcgactive.Text = "N/A";
                lblcgrpostatus.Text = "N/A";
                lblcglinkup.Text = "N/A";


            }

            var xivmonilist = Facade.GetXIVMonitoringByCgid(Convert.ToInt32(ddlxivcg.SelectedValue));

            if (xivmonilist != null)
            {
                lvxivstat.DataSource = xivmonilist;
                lvxivstat.DataBind();
            }
            else
            {
                lvxivstat.DataSource = null;
                lvxivstat.DataBind();
            }

            var cgvolmonilist = Facade.GetCGvolById(Convert.ToInt32(ddlxivcg.SelectedValue));

            if (cgvolmonilist != null)
            {
                lvcgvolmoni.DataSource = cgvolmonilist;
                lvcgvolmoni.DataBind();
            }
            else
            {
                lvcgvolmoni.DataSource = null;
                lvcgvolmoni.DataBind();
            }
        }

        protected void lvcgvolmoni_ItemDataBound(object sender, ListViewItemEventArgs e)
        {

        }

        protected void rptRoboMonitorStatus_ItemDataBound(object sender, RepeaterItemEventArgs e)
        {
            Label lblRoboCopyJobId = e.Item.FindControl("lblRoboCopyJobId") as Label;

            if (lblRoboCopyJobId != null)
            {
                var robojob = Facade.GetRoboCopyJobById(Convert.ToInt32(lblRoboCopyJobId.Text));

                Label lblspath = e.Item.FindControl("lblspath") as Label;
                if (lblspath != null)
                {
                    lblspath.Text = robojob.SourceDirectory;
                }
                Label lbldpath = e.Item.FindControl("lbldpath") as Label;
                if (lbldpath != null)
                {
                    lbldpath.Text = robojob.DestinationDirectory;
                }
            }
        }
    }
}