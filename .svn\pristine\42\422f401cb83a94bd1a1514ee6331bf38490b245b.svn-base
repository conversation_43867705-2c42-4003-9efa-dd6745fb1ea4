﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="HP3PARStorageMSSqlConfiguration.ascx.cs" Inherits="CP.UI.Controls.HP3PARStorageMSSqlConfiguration" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>

<div class="form-horizontal margin-none">
     <input type="hidden" id="hdfStaticGuid" runat="server" />
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
            <h4 class="heading">HP3PAR Management Console</h4>
        </div>
        <div class="widget-body">
            <table class="table">
                <thead>
                    <tr>
                        <th style="width: 24%;"></th>
                        <th style="width: 37.5% !important;">Production Server
                        </th>
                        <th style="width: 37.5% !important;">DR Server
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <label>
                                HP3PAR Storage Server
                            </label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                            <asp:DropDownList ID="ddlPrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" data-style="btn-default" OnSelectedIndexChanged="ddlPrServer_SelectedIndexChanged">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvPRServer" runat="server" ControlToValidate="ddlPrServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select Production Server"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:DropDownList ID="ddlDrServer" runat="server" AutoPostBack="true" CssClass="selectpicker" data-style="btn-default" OnSelectedIndexChanged="ddlDrServer_SelectedIndexChanged">
                            </asp:DropDownList>
                            <asp:RequiredFieldValidator ID="rfvDRServer" runat="server" ControlToValidate="ddlDrServer" CssClass="error"
                                Display="Dynamic" InitialValue="0" ErrorMessage="Select DR Server"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                IP Address
                            </label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRMgtConsoleIP" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPrConsoleIP" runat="server" ErrorMessage="Please Enter IP Address" CssClass="error"
                                ControlToValidate="txtPRMgtConsoleIP"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRMgtConsoleIP" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDrConsoleIP" runat="server" ErrorMessage="Please Enter IP Address" CssClass="error"
                                ControlToValidate="txtDRMgtConsoleIP"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                User Name</label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRUserName" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPRUserName" runat="server" ErrorMessage="Please Enter User Name" CssClass="error"
                                ControlToValidate="txtPRUserName"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRUserName" runat="server" CssClass="form-control" Style="Width: 56% !important"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRUserName" runat="server" ErrorMessage="Please Enter User Name" CssClass="error"
                                ControlToValidate="txtDRUserName"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                Password</label>
                            <span class="inactive">*</span>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRPassword" Enabled="true" CssClass="form-control" Style="Width: 56% !important" runat="server" autocomplete="off" TextMode="Password"></asp:TextBox>
                           
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRPassword" runat="server" CssClass="form-control" Style="Width: 56% !important" autocomplete="off" TextMode="Password"></asp:TextBox>
                           
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="form-horizontal margin-none">
    <div class="widget widget-heading-simple widget-body-white">
        <div class="widget-head">
                    <h4 class="heading">
                     <asp:Label runat="server" ID="lbltextheader" Style="font-style:normal;font-weight:700;" Text="Storage - MSSQL Full DB - HP3PAR"></asp:Label>
                    </h4>
                </div>
        <div class="widget-body">
            <table class="table">
                <thead>
                    <tr>
                        <th style="width: 24%;"></th>
                        <th style="width: 37.5% !important;">Production Server
                        </th>
                        <th style="width: 37.5% !important;">DR Server
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <label>
                                3PAR Storage IP Address
                            </label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRStorageIP" CssClass="form-control" Style="width: 56% !important" Enabled="true" runat="server"></asp:TextBox>
                              <asp:RequiredFieldValidator ID="rfvPRStorageIP" runat="server" ErrorMessage="Please Enter PR Storage IP" CssClass="error"
                                ControlToValidate="txtPRStorageIP"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRStorageIP" CssClass="form-control" Style="width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRStorageIP" runat="server" ErrorMessage="Please Enter DR Storage IP" CssClass="error"
                                ControlToValidate="txtDRStorageIP"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                               3PAR Storage Name
                            </label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRStorageName" CssClass="form-control" Style="width: 56% !important" Enabled="true" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPRStorageName" runat="server" ErrorMessage="Please Enter PR Storage Name" CssClass="error"
                                ControlToValidate="txtPRStorageName"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRStorageName" CssClass="form-control" Style="width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRStorageName" runat="server" ErrorMessage="Please Enter DR Storage Name" CssClass="error"
                                ControlToValidate="txtDRStorageName"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                Remote Copy Group Name
                            </label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRRemotecopyName" CssClass="form-control" Style="width: 56% !important" Enabled="true" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPRRemotecopyName" runat="server" ErrorMessage="Please Enter PR Copy Name" CssClass="error"
                                ControlToValidate="txtPRRemotecopyName"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPDRemotecopyName" CssClass="form-control" Style="width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRemotecopyName" runat="server" ErrorMessage="Please Enter DR Copy Name" CssClass="error"
                                ControlToValidate="txtPDRemotecopyName"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label>
                                Volume Name 
                            </label>
                        </td>
                        <td>
                            <asp:TextBox ID="txtPRVolumeName" CssClass="form-control" Style="width: 56% !important" Enabled="true" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvPRVolumeName" runat="server" ErrorMessage="Please Enter PR Volume Name" CssClass="error"
                                ControlToValidate="txtPRVolumeName"></asp:RequiredFieldValidator>
                        </td>
                        <td>
                            <asp:TextBox ID="txtDRVolumeName" CssClass="form-control" Style="width: 56% !important" runat="server"></asp:TextBox>
                            <asp:RequiredFieldValidator ID="rfvDRVolumeName" runat="server" ErrorMessage="Please Enter DR Volume Name" CssClass="error"
                                ControlToValidate="txtDRVolumeName"></asp:RequiredFieldValidator>
                        </td>
                    </tr>
                </tbody>
            </table>

            <div class="form-actions row">
                <div class="col-lg-3">
                    <asp:Label ID="lblMsg" runat="server" Text=""></asp:Label>
                </div>
                <div class="col-lg-7" style="margin-left: 58.4%;">
                    <asp:Button ID="btnSaveRep" CssClass="btn btn-primary" Width="20%" runat="server" Text="Save" OnClick="btnSaveRep_Click"/>
                    <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="20%" runat="server" Text="Cancel" CausesValidation="false" OnClick="btnCancel_Click"/>
                </div>
            </div>

        </div>
    </div>
</div>
