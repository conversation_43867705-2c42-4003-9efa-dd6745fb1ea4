﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ActiveODGConfig.aspx.cs" Inherits="CP.UI.Admin.ActiveODGConfig" %>


<%@ Register Src="../Controls/ZFSStorageReplicationMonitoring.ascx" TagName="ZFSReplicationMonitor_UserControl" TagPrefix="uc1" %>
<%@ Register Src="~/Controls/HACMPPowerHAClusterDetails.ascx" TagPrefix="uc1" TagName="HACMPPowerHAClusterDetails" %>
<%@ Register Src="~/Controls/VeritasClusterDetailedmonitoring.ascx" TagPrefix="uc1" TagName="VeritasClusterDetailedmonitoring" %>
<%@ Register Src="~/Controls/HP3PARmonitorDetails.ascx" TagPrefix="uc1" TagName="HP3PARmonitorDetails" %>

<%@ Register Src="~/Controls/RecoveryPointMultiMonitoringDetails.ascx" TagPrefix="uc1" TagName="RecoveryPointMultiMonitoringDetails" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <%-- <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>--%>
    <%--<script src="../Script/canvasjs.min.js"></script>--%>
    <script src="../Script/jquery.canvasjs.min.js"></script>
    <script src="../Script/ActiveOdgGraph.js"></script>
    <script src="../Script/ActiveOdgHrsSize.js"></script>
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <style type="text/css">
        .widget .widget-head .heading {
            /*color: #555555;
    float: left;
    font-size: 16px;
    height: 35px;
    line-height: 30px;
    margin: 0;*/
            padding: 0 10px;
        }

        .widget.widget-tabs.changetabsstruc > .widget-head {
            background-color: #f9f9f9;
            background-image: linear-gradient(to bottom, #fdfdfd, #f4f4f4);
            background-repeat: repeat-x;
        }

        .changetabsstruc > .widget-head ul li a {
            padding: 0 60px !important;
        }

        .changetabsstruc > .widget-head ul li.active a {
            color: #4a8bc2;
        }

        .table.table-striped.table-bordered.table-condensed.table-white.innertable-change thead tr th, .table.table-striped.table-bordered.table-condensed.table-white.innertable-change tbody tr td {
            font-size: 10px !important;
        }

        .canvasjs-chart-credit {
            display: none !important;
        }

        #ctl00_cphBody_RACNodeId, #ctl00_cphBody_RACClusterNodeId {
            font-weight: 100;
            padding: 3px 12px;
            margin-top: 3px;
            width: 56%;
            margin-left: 142px;
        }

        .widget .widget-head .dropdown-menu li > a:hover, .widget .widget-head .dropdown-menu li > a:focus, .widget .widget-head .dropdown-submenu:hover > a {
            background-image: none;
        }

        .btn-group.bootstrap-select {
            width: 24%;
        }

        #ctl00_cphBody_RACNodeId .caret, #ctl00_cphBody_RACClusterNodeId .caret {
            border-top-color: rgba(0, 0, 0, 0.5);
        }

        #ctl00_cphBody_RACNodeId, #ctl00_cphBody_RACClusterNodeId {
            text-shadow: 0 1px 0 rgba(255, 255, 255, 0.75);
            /* color: rgba(0, 0, 0, 0.6); */
            text-shadow: 0 1px 0 #fff;
            background-color: #efefef;
            background-image: -moz-linear-gradient(top, #f4f4f4, #e7e7e7);
            background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f4f4f4), to(#e7e7e7));
            background-image: -webkit-linear-gradient(top, #f4f4f4, #e7e7e7);
            background-image: -o-linear-gradient(top, #f4f4f4, #e7e7e7);
            background-image: linear-gradient(to bottom, #f4f4f4, #e7e7e7);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#fff4f4f4', endColorstr='#ffe7e7e7', GradientType=0);
            border: 1px solid #cecece;
        }

        .bootstrap-select.btn-group .dropdown-menu {
            min-width: 55%;
        }

        .vertical-Middle {
            vertical-align: middle;
        }

        .icon-word span:first-child {
            height: 19px;
            float: left;
            min-width: 20px;
            margin-top: 2px;
        }

        .icon-word span:last-child {
            display: inline-block;
        }

        .log-heading {
            font-size: 13px;
            color: #222222;
            margin-bottom: 8px;
            font-family: "segoe_uiregular",sans-serif !important;
        }
    </style>
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">

    <div class="innerLR">
        <h3>
            <img src="../Images/monitor.png" style="vertical-align: text-top;">
            Infra Object Monitor </h3>
        <div class="widget  widget-body-white" id="Div1" runat="server">
          <div class="widget-head">
                <span class=" heading"> <asp:Label ID="lblinfraname" runat="server" Text=""></asp:Label></span>
            </div>
              </div>

        <div class="widget  widget-body-white" id="ClusterDetails" runat="server">
            <asp:UpdatePanel ID="UptCulster" runat="server" UpdateMode="Conditional">
                <ContentTemplate>
                    <%--widget-heading-simple--%>
                    <div class="widget-head" style="overflow: visible;">
                        <span class=" heading">Cluster Details</span>
                        <asp:DropDownList ID="RACClusterNodeId" runat="server" CssClass="selectpicker pull-right" btn-style="btn-default" OnSelectedIndexChanged="RACClusterNodeId_SelectedIndexChanged" AutoPostBack="true">
                        </asp:DropDownList>
                    </div>
                    <div class="widget-body">
                        <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                            <thead>
                                <tr>
                                    <th style="width: 26%;">Cluster Details
                                    </th>
                                    <th style="width: 37%;">PR
                                    </th>
                                    <th style="width: 37%;">DR
                                    </th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr>
                                    <td style="width: 26%;">CLUSTER NAME
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label class="cluster_icon-blue vertical-Middle" runat="server" ID="lblSpan108"></asp:Label>
                                        <asp:Label ID="lblPrClusterName" Text="" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label class="cluster_icon-blue vertical-Middle" runat="server" ID="lblSpan109"></asp:Label>
                                        <asp:Label ID="lblDrClusterName" Text="" runat="server"> </asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td style="width: 26%;">CLUSTERWARE ACTIVE VERSION
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label class="cluster_icon-blue vertical-Middle" runat="server" ID="lblSpan110"></asp:Label>
                                        <asp:Label ID="lblPrclusterwareactiveversion" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label class="cluster_icon-blue vertical-Middle" runat="server" ID="lblSpan111"></asp:Label>
                                        <asp:Label ID="lblDrclusterwareactiveversion" runat="server"> </asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td style="width: 26%;">OHAS Status
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan112"></asp:Label>
                                        <asp:Label ID="lblPrOHASStatus" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan113"></asp:Label>
                                        <asp:Label ID="lblDrOHASStatus" runat="server"> </asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td style="width: 26%;">CRS status
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan114"></asp:Label>
                                        <asp:Label ID="lblPRCRSStatus" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan115"></asp:Label>
                                        <asp:Label ID="lblDRCRSStatus" runat="server"> </asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td style="width: 26%;">CSS Status
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan116"></asp:Label>
                                        <asp:Label ID="lblPrCSSStatus" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan117"></asp:Label>
                                        <asp:Label ID="lblDrCSSStatus" runat="server"> </asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td style="width: 26%;">EVM STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan118"></asp:Label>
                                        <asp:Label ID="lblPrEvmstatus" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan119"></asp:Label>
                                        <asp:Label ID="lblDrEvmstatus" runat="server"> </asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td style="width: 26%;">CLUSTER LISTENER
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <%--<asp:Label class="clock-icon-blue vertical-Middle" runat="server" id="lblSpan122"></asp:Label>--%>
                                        <asp:Label ID="lblPrclusterlistener" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <%--<asp:Label class="clock-icon-blue vertical-Middle" runat="server" id="lblSpan123"></asp:Label>--%>
                                        <asp:Label ID="lblDrclusterlistener" runat="server"> </asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td style="width: 26%;">SCAN STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <%-- <asp:Label class="PanelSuccessNew vertical-Middle" runat="server" id="lblSpan120"></asp:Label>--%>
                                        <asp:Label ID="lblPrscanstatus" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <%--<asp:Label class="PanelDangerNew vertical-Middle" runat="server" id="lblSpan121"></asp:Label>--%>
                                        <asp:Label ID="lblDrscanstatus" runat="server"> </asp:Label>
                                    </td>
                                </tr>

                                <tr>
                                    <td style="width: 26%;">SCAN_LISTENER STATUS
                                    </td>
                                    <td style="width: 37%;" class="tdword-wrap">
                                        <%--<asp:Label class="PanelSuccessNew vertical-Middle" runat="server" id="lblSpan125"></asp:Label>--%>
                                        <asp:Label ID="lblPrscanlistenerstatus" runat="server"> </asp:Label>
                                    </td>
                                    <td style="width: 37%;">
                                        <%--<asp:Label class="PanelDangerNew vertical-Middle" runat="server" id="lblSpan124"></asp:Label>--%>
                                        <asp:Label ID="lblDrscanlistenerstatus" runat="server"> </asp:Label>
                                    </td>
                                </tr>



                            </tbody>
                        </table>
                    </div>

                </ContentTemplate>
            </asp:UpdatePanel>
        </div>

        <div class="widget  widget-body-white" id="MultiTenancy" runat="server">
            <%--widget-heading-simple--%>
            <div class="widget-head">
                <span class=" heading">Multi Tenancy </span>
            </div>
            <div class="widget-body">
                <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                    <thead>
                        <tr>
                            <th style="width: 26%;">Multi Tenancy
                            </th>
                            <th style="width: 37%;">PR
                            </th>
                            <th style="width: 37%;">DR
                            </th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td style="width: 26%;">CDB
                            </td>
                            <td style="width: 37%;" class="tdword-wrap">
                                <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan126"></asp:Label>
                                <asp:Label ID="lblPRCDB" runat="server"> </asp:Label>
                            </td>
                            <td style="width: 37%;">
                                <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan127"></asp:Label>
                                <asp:Label ID="lblDRCDB" runat="server"> </asp:Label>
                            </td>
                        </tr>

                        <tr>
                            <td style="width: 26%;">Containers
                            </td>
                            <td style="width: 37%;" class="tdword-wrap icon-word">
                                <asp:Label class="Containers_Icon-blue vertical-Middle" runat="server" ID="lblSpan128"></asp:Label>
                                <asp:Label ID="lblPRContainers" runat="server"> </asp:Label>
                            </td>
                            <td style="width: 37%;" class="icon-word">
                                <asp:Label class="Containers_Icon-blue vertical-Middle" runat="server" ID="lblSpan129"></asp:Label>
                                <asp:Label ID="lblDRContainers" runat="server"> </asp:Label>
                            </td>
                        </tr>

                        <tr>
                            <td style="width: 26%;">PDBs
                            </td>
                            <td style="width: 37%;" class="tdword-wrap icon-word">
                                <asp:Label ID="Label6" runat="server"> </asp:Label>
                                <asp:Label ID="lblPRPDBs" runat="server"> </asp:Label>
                            </td>
                            <td style="width: 37%;" class="icon-word">
                                <asp:Label ID="Label8" runat="server"> </asp:Label>
                                <asp:Label ID="lblDRPDBs" runat="server"> </asp:Label>
                            </td>
                        </tr>


                    </tbody>
                </table>
            </div>
        </div>

        <div class="widget  widget-body-white" id="PluggableDatabases" runat="server">
            <div class="widget-head">
                <span class=" heading">Pluggable Databases </span>
            </div>
            <div class="widget-body">
                <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                    <thead>
                        <tr>
                            <th style="width: 19%;">PDB Name
                            </th>
                            <th style="width: 27%;">Pluggable databases
                            </th>
                            <th style="width: 27%;">PR
                            </th>
                            <th style="width: 27%;">DR
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <asp:UpdatePanel ID="updmain" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>
                                <asp:Repeater runat="server" ID="rptServices">
                                    <HeaderTemplate>
                                    </HeaderTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td style="width: 26%;" rowspan="8" class="text-center vertical-middle"><%#Eval("PRPDBName") %>
                                            </td>
                                            <td style="width: 27%;" class="tdword-wrap">PDB Name
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan131"></asp:Label>
                                                <asp:Label ID="lblPRPDBName" runat="server" Text='<%#Eval("PRPDBName") %>'> </asp:Label>
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan132"></asp:Label>
                                                <asp:Label ID="lblDRPDBName" runat="server" Text='<%#Eval("DRPDBName") %>'> </asp:Label>
                                            </td>
                                        </tr>

                                        <tr>

                                            <td style="width: 27%;" class="tdword-wrap">CONNECTION ID
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="connector_Icon-blue vertical-Middle" runat="server" ID="lblSpan133"></asp:Label>
                                                <asp:Label ID="lblPRconnectionid" runat="server" Text='<%#Eval("PRConnectionId") %>'> </asp:Label>
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="connector_Icon-blue vertical-Middle" runat="server" ID="lblSpan134"></asp:Label>
                                                <asp:Label ID="lblDRconnectionid" runat="server" Text='<%#Eval("DRConnectionId") %>'> </asp:Label>
                                            </td>
                                        </tr>

                                        <tr>

                                            <td style="width: 27%;" class="tdword-wrap">PDB ID
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan135"></asp:Label>
                                                <asp:Label ID="lblPRpdbid" runat="server" Text='<%#Eval("PRPDBId") %>'> </asp:Label>
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan136"></asp:Label>
                                                <asp:Label ID="lblDRpdbid" runat="server" Text='<%#Eval("DRPDBId") %>'> </asp:Label>
                                            </td>
                                        </tr>

                                        <tr>

                                            <td style="width: 27%;" class="tdword-wrap">PDB MODE
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan137"></asp:Label>
                                                <asp:Label ID="lblPRpdbmode" runat="server" Text='<%#Eval("PRPDBMode") %>'> </asp:Label>
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan138"></asp:Label>
                                                <asp:Label ID="lblDRpdbmode" runat="server" Text='<%#Eval("DRPDBMode") %>'> </asp:Label>
                                            </td>
                                        </tr>

                                        <tr>

                                            <td style="width: 27%;" class="tdword-wrap">LOGGING
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="PanelSuccessNew vertical-Middle" runat="server" ID="lblSpan14"></asp:Label>
                                                <asp:Label ID="lblPRlogging" runat="server" Text='<%#Eval("PRLogging") %>'> </asp:Label>
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="PanelDangerNew vertical-Middle" runat="server" ID="lblSpan15"></asp:Label>
                                                <asp:Label ID="lblDRlogging" runat="server" Text='<%#Eval("DRLogging") %>'> </asp:Label>
                                            </td>
                                        </tr>

                                        <tr>

                                            <td style="width: 27%;" class="tdword-wrap">FORCE_LOGGING
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="PanelSuccessNew vertical-Middle" runat="server" ID="lblSpan139"></asp:Label>
                                                <asp:Label ID="lblPRforcelogging" runat="server" Text='<%#Eval("PRForceLogging") %>'> </asp:Label>
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="PanelDangerNew vertical-Middle" runat="server" ID="lblSpan140"></asp:Label>
                                                <asp:Label ID="lblDRforcelogging" runat="server" Text='<%#Eval("DRForceLogging") %>'> </asp:Label>
                                            </td>
                                        </tr>

                                        <tr>

                                            <td style="width: 27%;" class="tdword-wrap">RECOVERY_STATUS
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="PanelSuccessNew vertical-Middle" runat="server" ID="lblSpan144"></asp:Label>
                                                <asp:Label ID="lblPRrecovery_status" runat="server" Text='<%#Eval("PRRecoveryStatus") %>'> </asp:Label>
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="PanelDangerNew vertical-Middle" runat="server" ID="lblSpan141"></asp:Label>
                                                <asp:Label ID="lblDRrecovery_status" runat="server" Text='<%#Eval("DRRecoveryStatus") %>'> </asp:Label>
                                            </td>
                                        </tr>

                                        <tr>

                                            <td style="width: 27%;" class="tdword-wrap">PDB SIZE
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan142"></asp:Label>
                                                <asp:Label ID="lblPRpdbsize" runat="server" Text='<%#Eval("PRPDBSize") %>'> </asp:Label>
                                            </td>
                                            <td style="width: 27%;">
                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan143"></asp:Label>
                                                <asp:Label ID="lblDRpdbsize" runat="server" Text='<%#Eval("DRPDBSize") %>'> </asp:Label>
                                            </td>
                                        </tr>

                                    </ItemTemplate>
                                    <FooterTemplate>
                                    </FooterTemplate>
                                </asp:Repeater>
                            </ContentTemplate>
                        </asp:UpdatePanel>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="widget widget-tabs changetabsstruc">
            <div class="widget-head">
                <ul>
                    <li id="Li1" runat="server" class="active">
                        <a href="#litab1" data-toggle="tab"><i></i>Database Details</a></li>
                    <li id="Li2" runat="server">
                        <a href="#ctl00_cphBody_litab2" data-toggle="tab"><i></i>Replication Details</a></li>
                    <li id="Li4" runat="server" visible="false">
                        <a href="#litab4" data-toggle="tab"><i></i>Global Mirror Details</a></li>
                    <li id="Li3" runat="server" visible="false">
                        <a href="#litab3" data-toggle="tab"><i></i>SVC Details</a></li>
                    <li id="Li5" runat="server" visible="false">
                        <a href="#litab5" data-toggle="tab"><i></i>Recover Point Monitor</a></li>
                    <li id="Li6" runat="server" visible="false">
                        <a href="#litab6" data-toggle="tab"><i></i>Hitachi Details</a></li>
                    <li id="Li7" runat="server" visible="false">
                        <a href="#litab7" data-toggle="tab"><i></i>ZFS Replication Details</a></li>
                     <li id="Li8" runat="server" visible="false">
                        <a href="#litab8" data-toggle="tab"><i></i>Cluster Details</a></li>
                     <li id="Li9" runat="server" visible="false">
                        <a href="#litab9" data-toggle="tab"><i></i>Veritas Cluster Details</a></li>
                     <li id="Li10" runat="server" visible="false">
                        <a href="#litab10" data-toggle="tab"><i></i>Hp3Par Replication Details</a></li>
                </ul>
            </div>
            <asp:UpdatePanel ID="updDatabaseDetails" runat="server" UpdateMode="Conditional">
                <ContentTemplate>
                    <div class="widget-body">
                        <div class="tab-content">
                            <div class="tab-pane" id="litab2" runat="server">
                                <asp:UpdatePanel ID="UdtReplicationDetails" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <div class="widget  widget-body-white" id="divReplicationDetails" runat="server">
                                            <%--widget-heading-simple--%>
                                            <div class="widget-body">
                                                <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                                    <thead>
                                                        <tr>
                                                            <th style="width: 26%;">Parameter Name
                                                            </th>
                                                            <th style="width: 37%;">PR
                                                            </th>
                                                            <th style="width: 37%;">DR
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                </table>


                                                <div class="notifyscroll" style="height: 480px !important">
                                                    <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                                        <tbody>
                                                            <tr>
                                                                <td style="width: 26%;">ACTIVE DG ENABLED
                                                                </td>
                                                                <td style="width: 74%;" class="tdword-wrap" colspan="2">
                                                                    <asp:Label ID="lblPRActiveDGEnabled" runat="server" Visible="false"> </asp:Label>
                                                                    <asp:Label class="PanelSuccessNew vertical-Middle" runat="server" ID="lblSpan59"></asp:Label>
                                                                    <asp:Label ID="lblDRActiveDGEnabled" runat="server"> </asp:Label>
                                                                </td>

                                                            </tr>

                                                            <tr>
                                                                <td style="width: 26%;">DG_BROKER STATUS
                                                                </td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="notification_icon_blue vertical-Middle" runat="server" ID="lblSpan60"></asp:Label>
                                                                    <asp:Label ID="lblPRDGBrokerStatus" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="notification_icon_blue vertical-Middle" runat="server" ID="lblSpan61"></asp:Label>
                                                                    <asp:Label ID="lblDRDGBrokerStatus" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>

                                                            <tr>
                                                                <td style="width: 26%;">DATAGUARD_STATUS
                                                                </td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="refresh-icon-blue1 vertical-Middle" runat="server" ID="lblSpan62"></asp:Label>
                                                                    <asp:Label ID="lblPRDataguardStatus" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 74%;">
                                                                    <asp:Label class="icon-async1 vertical-Middle" runat="server" ID="lblSpan63"></asp:Label>
                                                                    <asp:Label ID="lblDRDataguardStatus" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">RECOVERY_STATUS
                                                                </td>
                                                                <td style="width: 37%; display: none" class="tdword-wrap">
                                                                    <asp:Label class="refresh-icon-blue1 vertical-Middle" runat="server" ID="Label10"></asp:Label>
                                                                    <asp:Label ID="lblPRRecoveryStatus" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 74%;" colspan="2">
                                                                    <asp:Label class="icon-async1 vertical-Middle" runat="server" ID="Label14"></asp:Label>
                                                                    <asp:Label ID="lblDRRecoveryStatus" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">SWITCHOVER STATUS
                                                                </td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="refresh-icon-blue1 vertical-Middle" runat="server" ID="lblSpan64"></asp:Label>
                                                                    <asp:Label ID="lblPRSwitchoverStatus" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="icon-async1 vertical-Middle" runat="server" ID="lblSpan65"></asp:Label>
                                                                    <asp:Label ID="lblDRSwitchoverStatus" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>

                                                            <tr>
                                                                <td style="width: 26%;">LOG_ARCHIVE_CONFIG
                                                                </td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="download-blue-blue vertical-Middle" runat="server" ID="lblSpan66"></asp:Label>
                                                                    <asp:Label ID="lblPRLogArchiveConfig" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="download-blue-blue vertical-Middle" runat="server" ID="lblSpan67"></asp:Label>
                                                                    <asp:Label ID="lblDRLogArchiveConfig" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>

                                                            <tr>
                                                                <td style="width: 26%;">FORCE LOGGING
                                                                </td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="PanelSuccessNew vertical-Middle" runat="server" ID="lblSpan68"></asp:Label>
                                                                    <asp:Label ID="lblPRForceLogging" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="notification_icon_blue vertical-Middle" runat="server" ID="lblSpan69"></asp:Label>
                                                                    <asp:Label ID="lblDRForceLogging" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>

                                                            <tr>
                                                                <td style="width: 26%;">ARCHIVE DEST LOCATION</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="addricon-blue vertical-Middle" runat="server" ID="lblSpan70"></asp:Label>
                                                                    <asp:Label ID="lblPRarchiveDestLocation" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="addricon-blue vertical-Middle" runat="server" ID="lblSpan71"></asp:Label>
                                                                    <asp:Label ID="lblDRarchiveDestLocation" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>

                                                            <tr>
                                                                <td style="width: 26%;">PROTECTION MODE</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan72"></asp:Label>
                                                                    <asp:Label ID="lblPRProtectionMode" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan73"></asp:Label>
                                                                    <asp:Label ID="lblDRProtectionMode" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">TRANSMIT MODE</td>

                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="asnych_icon-blue vertical-Middle" runat="server" ID="lblSpan74"></asp:Label>
                                                                    <asp:Label ID="lblPRTransmitMode" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="asnych_icon-blue vertical-Middle" runat="server" ID="lblSpan75"></asp:Label>
                                                                    <asp:Label ID="lblDRTransmitMode" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">RECOVERY MODE</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan76"></asp:Label>
                                                                    <asp:Label ID="lblPRRecoveryMode" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan77"></asp:Label>
                                                                    <asp:Label ID="lblDRRecoveryMode" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">AFFIRM</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan78"></asp:Label>
                                                                    <asp:Label ID="lblPRAffirm" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan79"></asp:Label>
                                                                    <asp:Label ID="lblDRAffirm" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">ARCHIVER</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="download-blue-blue vertical-Middle" runat="server" ID="lblSpan80"></asp:Label>
                                                                    <asp:Label ID="lblPRArchiver" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="download-blue-blue vertical-Middle" runat="server" ID="lblSpan81"></asp:Label>
                                                                    <asp:Label ID="lblDRArchiver" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">ARCHIVELOG COMPRESSION</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan82"></asp:Label>
                                                                    <asp:Label ID="lblPRArchiveLogCompression" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan83"></asp:Label>
                                                                    <asp:Label ID="lblDRArchiveLogCompression" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">DELAY MINS</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan84"></asp:Label>
                                                                    <asp:Label ID="lblPRDelayMins" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan85"></asp:Label>
                                                                    <asp:Label ID="lblDRDelayMins" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">LOG SEQUENCE</td>
                                                                <td style="width: 37%;" class="tdword-wrap icon-word">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan86"></asp:Label>
                                                                    <asp:Label ID="lblPRLogSequence" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;" class="icon-word">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan87"></asp:Label>
                                                                    <asp:Label ID="lblDRLogSequence" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">REMOTE LOGIN PASSWORDFILE</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="Duplicate_licence_blue vertical-Middle" runat="server" ID="lblSpan88"></asp:Label>
                                                                    <asp:Label ID="lblPRRemoteLoginPasswordFile" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="Duplicate_licence_blue vertical-Middle" runat="server" ID="lblSpan89"></asp:Label>
                                                                    <asp:Label ID="lblDRRemoteLoginPasswordFile" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">STANDBY FILE MANAGEMENT</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan96"></asp:Label>
                                                                    <asp:Label ID="lblPRStandByFileManagement" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan97"></asp:Label>
                                                                    <asp:Label ID="lblDRStandByFileManagement" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">STANDBY REDO LOGS</td>


                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan90"></asp:Label>
                                                                    <asp:Label ID="lblPRStandByRedoLogs" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan91"></asp:Label>
                                                                    <asp:Label ID="lblDRStandByRedoLogs" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">TRANSPORT LAG</td>
                                                                <td style="width: 74%;" class="tdword-wrap" colspan="2">
                                                                    <asp:Label class="lag-blue-icon vertical-Middle" runat="server" ID="Label9"></asp:Label>
                                                                    <asp:Label ID="lblDRTransportLag" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%; display: none" class="tdword-wrap">
                                                                    <asp:Label class="lag-blue-icon vertical-Middle" runat="server" ID="lblSpan102"></asp:Label>
                                                                    <asp:Label ID="lblPRTransportLag" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%; display: none">
                                                                    <asp:Label class="lag-blue-icon vertical-Middle" runat="server" ID="lblSpan103"></asp:Label>

                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">APPLY LAG</td>
                                                                <td style="width: 74%;" class="tdword-wrap" colspan="2">
                                                                    <asp:Label class="lag-blue-icon vertical-Middle" runat="server" ID="Label11"></asp:Label>
                                                                    <asp:Label ID="lblDRApplyLag" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%; display: none" class="tdword-wrap">
                                                                    <asp:Label class="lag-blue-icon vertical-Middle" runat="server" ID="lblSpan104"></asp:Label>
                                                                    <asp:Label ID="lblPRApplyLag" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%; display: none">
                                                                    <asp:Label class="lag-blue-icon vertical-Middle" runat="server" ID="lblSpan105"></asp:Label>

                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">APPLY FINISH TIME</td>
                                                                <td style="width: 74%;" class="tdword-wrap" colspan="2">
                                                                    <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="Label13"></asp:Label>
                                                                    <asp:Label ID="lblDRApplyFinishTime" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%; display: none" class="tdword-wrap">
                                                                    <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan92"></asp:Label>
                                                                    <asp:Label ID="lblPRApplyFinishTime" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%; display: none">
                                                                    <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan93"></asp:Label>

                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">ESTIMATED STARTUP TIME</td>
                                                                <td style="width: 74%;" class="tdword-wrap" colspan="2">
                                                                    <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="Label15"></asp:Label>
                                                                    <asp:Label ID="lblDREstimatedStartupTime" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%; display: none" class="tdword-wrap">
                                                                    <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan94"></asp:Label>
                                                                    <asp:Label ID="lblPREstimatedStartupTime" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%; display: none">
                                                                    <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan95"></asp:Label>

                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">FAL SERVER</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="power-off-blue vertical-Middle" runat="server" ID="lblSpan98"></asp:Label>
                                                                    <asp:Label ID="lblPRFalServer" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="power-off-blue vertical-Middle" runat="server" ID="lblSpan99"></asp:Label>
                                                                    <asp:Label ID="lblDRFalServer" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td style="width: 26%;">FAL CLIENT</td>
                                                                <td style="width: 37%;" class="tdword-wrap">
                                                                    <asp:Label class="power-off-blue vertical-Middle" runat="server" ID="lblSpan100"></asp:Label>
                                                                    <asp:Label ID="lblPRFalClient" runat="server"> </asp:Label>
                                                                </td>
                                                                <td style="width: 37%;">
                                                                    <asp:Label class="power-off-blue vertical-Middle" runat="server" ID="lblSpan101"></asp:Label>
                                                                    <asp:Label ID="lblDRFalClient" runat="server"> </asp:Label>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>



                                                </div>

                                               
                                            </div>
                                        </div>

                                         <div id="dvEmcSrdf" runat="server" visible="false" class="widget  widget-body-white">
                                                    <table id="tblEMCSRDF" class="table table-bordered table-primary" width="100%" runat="server">
                                                        <%--style="display: table;"--%>
                                                        <thead>
                                                            <tr>
                                                                <th colspan="2">Replication Monitor
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td class="col-md-4">Replication Type
                                                                </td>
                                                                <td>
                                                                    <span id="Span26" class="replication-file-icon" runat="server"></span>
                                                                    <span>
                                                                        <asp:Label ID="lblReplicationType" runat="server" Text=""></asp:Label></span>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Device Group Name
                                                                </td>
                                                                <td class="text-indent">
                                                                    <span class="icon-disks">&nbsp;</span>
                                                                    <asp:Label ID="lblDeviceGroupName" runat="server"></asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="col-md-4">Disk Groups Type
                                                                </td>
                                                                <td class="text-indent">
                                                                    <span class="icon-disk-type">&nbsp;</span>
                                                                    <asp:Label ID="lblDeviceGroupType" runat="server"></asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Disk Groups Symmetrix ID
                                                                </td>
                                                                <td class="text-indent">
                                                                    <span class="icon-disk-symmetrix">&nbsp;</span>
                                                                    <asp:Label ID="lblDiskGroupsSymmetrixId" runat="server"></asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Remote Symmetrix ID
                                                                </td>
                                                                <td class="text-indent">
                                                                    <span class="icon-disk-remote">&nbsp;</span>
                                                                    <asp:Label ID="lblRemoteSymmtrix" runat="server"></asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>RDF (RA) Group Number
                                                                </td>
                                                                <td class="text-indent">
                                                                    <span class="icon-numbering">&nbsp;</span>
                                                                    <asp:Label ID="lblRdfGroupNumber" runat="server"></asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Device State
                                                                </td>
                                                                <td>
                                                                    <span class="session-icon">&nbsp;</span>
                                                                    <asp:Label ID="lblDeviceState" runat="server"></asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Pending Tracks
                                                                </td>
                                                                <td class="text-indent">
                                                                    <span class="icon-tracks">&nbsp;</span>
                                                                    <asp:Label ID="lblPendingTraks" runat="server"></asp:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td>Lag (R2 Behind R1)
                                                                </td>
                                                                <td class="text-indent">
                                                                    <span class="icon-Time">&nbsp;</span>
                                                                    <asp:Label ID="lblAppDataLg" runat="server"></asp:Label>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                                <div class="widget  widget-body-white" id="ReplicationDetailsNonODG" runat="server">
                                    <%--widget-heading-simple--%>
                                    <div class="widget-head">
                                        <span class=" heading">Replication Details - Non ODG</span>
                                    </div>
                                    <div class="widget-body">
                                        <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                            <thead>
                                                <tr>
                                                    <th style="width: 26%;">Replication Details - Non ODG
                                                    </th>
                                                    <th style="width: 37%;">PR
                                                    </th>
                                                    <th style="width: 37%;">DR
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                <tr>
                                                    <td style="width: 26%;">ARCHIVE DEST LOCATIONS
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblPRArchive_Dest_Location" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblDRArchive_Dest_Location" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">DATABASE INCARNATION#
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblPRDatabase_Incarnation" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblDRDatabase_Incarnation" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">LOG SEQUENCE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblPRLog_Sequence" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblDRLog_Sequence" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">STANDBY_FILE_MANAGEMENT
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblPRStandby_File_Management" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblDRStandby_File_Management" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">FORCE LOGGING
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblForceLoggingPR" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblForceLoggingDR" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="widget  widget-body-white" id="GraphId" runat="server">
                                    <%--widget-heading-simple--%>
                                    <div class="widget-head">
                                        <span class="heading">ARCHIVE LOG GENERATION </span>
                                    </div>
                                    <div class="widget-body" style="height: 206px">
                                        <div class="col-md-4 data-line" style="border-right: 1px solid #dbdbdb; min-height: 185px;">
                                            <h5 class="log-heading">Archive Log Generation Hourly (Count) </h5>
                                            <div id="ODGchartContainer"></div>
                                        </div>
                                        <div class="col-md-4 data-line" style="border-right: 1px solid #dbdbdb; min-height: 185px;">
                                            <h5 class="log-heading">Archive Log Generation Hourly Last 24 Hrs (Size) </h5>
                                            <%-- <img src="../Images/chart2.png" alt="" />--%>
                                            <div id="ODGSizeHrsChart"></div>
                                        </div>
                                        <div class="col-md-4">
                                            <h5 class="log-heading">Archive Log Generation Past Week (Size) </h5>
                                            <div id="ODGSizeWeeklyChart"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="tab-pane active" id="litab1">

                                <div class="widget  widget-body-white">
                                    <%--widget-heading-simple--%>
                                    <div class="widget-body">
                                        <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                            <thead>
                                                <tr>
                                                    <th style="width: 26%;"></th>
                                                    <th style="width: 37%;">PR
                                                    </th>
                                                    <th style="width: 37%;">DR
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>

                                                <tr>
                                                    <td style="width: 26%;">DATABASE NAME
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpanlbl1"></asp:Label>
                                                        <asp:Label ID="lblPRdatabaseName" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan1"></asp:Label>
                                                        <asp:Label ID="lblDRDatabaseName" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">DATABASE UNIQUE NAME
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan2"></asp:Label>
                                                        <asp:Label ID="lblPRDatabaseUniqueName" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan3"></asp:Label>
                                                        <asp:Label ID="lblDRDatabaseUniqueName" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">DATABASE ROLE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan4"></asp:Label>
                                                        <asp:Label ID="lblPRdatabase_role" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan5"></asp:Label>
                                                        <asp:Label ID="lblDRdatabase_role" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">OPEN MODE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="folder-blue_new vertical-Middle" runat="server" ID="lblSpan6"></asp:Label>
                                                        <asp:Label ID="lblPRopenMode" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="folder-blue_new vertical-Middle" runat="server" ID="lblSpan7"></asp:Label>
                                                        <asp:Label ID="lblDRopenMode" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">DATABASE CREATED TIME
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan8"></asp:Label>
                                                        <asp:Label ID="lblPRdatabaseCreatedTime" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan9"></asp:Label>
                                                        <asp:Label ID="lblDRdatabaseCreatedTime" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">CONTROLFILE TYPE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan10"></asp:Label>
                                                        <asp:Label ID="lblPRcontrolfileType" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan11"></asp:Label>
                                                        <asp:Label ID="lblDRcontrolfileType" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">CURRENT SCN
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan12"></asp:Label>
                                                        <asp:Label ID="lblPRcurrentScn" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan13"></asp:Label>
                                                        <asp:Label ID="lblDRcurrentScn" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr>
                                                    <td style="width: 26%;">FLASHBACK_ON
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="PanelSuccessNew vertical-Middle" runat="server" ID="lblSpan14"></asp:Label>
                                                        <asp:Label ID="lblPRflashback_on" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="PanelDangerNew vertical-Middle" runat="server" ID="lblSpan15"></asp:Label>
                                                        <asp:Label ID="lblDRflashback_on" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <%--new added--%>
                                                <tr style="display: none;">
                                                    <td style="width: 26%;">INSTANCE NAME
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblPRDBIntanceName" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblDRDBIntanceName" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                                <tr style="display: none;">
                                                    <td style="width: 26%;">INSTANCE ID
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblPRDBIntanceID" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblDRDBIntanceID" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr style="display: none;">
                                                    <td style="width: 26%;">INSTANCE STARTUP TIME
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblPRinstanceStartUpTime" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblDRinstanceStartUpTime" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">DATABASE VERSION
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan16"></asp:Label>
                                                        <asp:Label ID="lblPRDatabaseVersion" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan17"></asp:Label>
                                                        <asp:Label ID="lblDRDatabaseVersion" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">DATABASE INCARNATION
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan18"></asp:Label>
                                                        <asp:Label ID="lblPRDatabaseIncarnation" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan19"></asp:Label>
                                                        <asp:Label ID="lblDRDatabaseIncarnation" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">RESETLOGS CHANGE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan20"></asp:Label>
                                                        <asp:Label ID="lblPRResetlogs_Change" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan21"></asp:Label>
                                                        <asp:Label ID="lblDRResetlogs_Change" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">RESETLOGS MODE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan22"></asp:Label>
                                                        <asp:Label ID="lblPRResetlogs_Mode" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan23"></asp:Label>
                                                        <asp:Label ID="lblDRResetlogs_Mode" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <%-- <tr>
                                                    <td style="width: 26%;">CONTROLFILE NAME
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan24"></asp:Label>
                                                        <asp:Label ID="lblPRControlfileName" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan25"></asp:Label>
                                                        <asp:Label ID="lblDRControlfileName" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>--%>
                                                <%-- <tr>
                                                    <td style="width: 26%;">PARAMETER FILE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan26"></asp:Label>
                                                        <asp:Label ID="lblPRParameterfile" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan27"></asp:Label>
                                                        <asp:Label ID="lblDRParameterfile" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>--%>
                                                <tr>
                                                    <td style="width: 26%;">ARCHIVE MODE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="download-blue-blue vertical-Middle" runat="server" ID="lblSpan28"></asp:Label>
                                                        <asp:Label ID="lblPRArchiveMode" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="download-blue-blue vertical-Middle" runat="server" ID="lblSpan29"></asp:Label>
                                                        <asp:Label ID="lblDRArchiveMode" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <%--<tr>
                                                    <td style="width: 26%;">PLATFORM NAME
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan30"></asp:Label>
                                                        <asp:Label ID="lblPRPlatform_Name" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan31"></asp:Label>
                                                        <asp:Label ID="lblDRPlatform_Name" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>--%>
                                                <tr>
                                                    <td style="width: 26%;">DB SIZE (in MB)
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan32"></asp:Label>
                                                        <asp:Label ID="lblPRDBSize" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan33"></asp:Label>
                                                        <asp:Label ID="lblDRDBSize" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">DB CREATE FILE DEST
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan34"></asp:Label>
                                                        <asp:Label ID="lblPRDbCreateFileDest" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan35"></asp:Label>
                                                        <asp:Label ID="lblDRDbCreateFileDest" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">DB FILE NAME CONVERT
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan36"></asp:Label>
                                                        <asp:Label ID="lblPRDbFileNameConvert" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan37"></asp:Label>
                                                        <asp:Label ID="lblDRDbFileNameConvert" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">DB CREATE ONLINE LOG DEST1
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan38"></asp:Label>
                                                        <asp:Label ID="lblPRDbCreateOnlineLogDest1" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan39"></asp:Label>
                                                        <asp:Label ID="lblDRDbCreateOnlineLogDest1" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">LOG FILE NAME CONVERT
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan46"></asp:Label>
                                                        <asp:Label ID="lblPRLogFileNameConvert" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan47"></asp:Label>
                                                        <asp:Label ID="lblDRLogFileNameConvert" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">DB RECOVERY FILE DEST
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan40"></asp:Label>
                                                        <asp:Label ID="lblPRDbRecoveryFileDest" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan41"></asp:Label>
                                                        <asp:Label ID="lblDRDbRecoveryFileDest" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">DB RECOVERY FILE DEST SIZE
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan42"></asp:Label>
                                                        <asp:Label ID="lblPRDbRecoveryFileDestSize" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan43"></asp:Label>
                                                        <asp:Label ID="lblDRDbRecoveryFileDestSize" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="width: 26%;">DB FLASHBACK RETENTION TARGET
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan44"></asp:Label>
                                                        <asp:Label ID="lblPRDbFlashbackRetentionTarget" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan45"></asp:Label>
                                                        <asp:Label ID="lblDRDbFlashbackRetentionTarget" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr id="trlogsequence" runat="server" visible="false">
                                                    <td style="width: 26%;">LOG SEQUENCE</td>
                                                    <td style="width: 37%;" class="tdword-wrap icon-word">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="Label12"></asp:Label>
                                                        <asp:Label ID="lblPRLogSequence1" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;" class="icon-word">
                                                        <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="Label16"></asp:Label>
                                                        <asp:Label ID="lblDRLogSequence1" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>
                                                <tr style="display: none;">
                                                    <td style="width: 26%;">SERVICES
                                                    </td>
                                                    <td style="width: 37%;" class="tdword-wrap">
                                                        <asp:Label ID="lblPRSERVICES" runat="server"> </asp:Label>
                                                    </td>
                                                    <td style="width: 37%;">
                                                        <asp:Label ID="lblDRSERVICES" runat="server"> </asp:Label>
                                                    </td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="widget  widget-body-white">
                                    <%--widget-heading-simple--%>
                                    <asp:UpdatePanel ID="udpInstanceDetails" runat="server" UpdateMode="Conditional">
                                        <ContentTemplate>
                                            <div class="widget-head" style="overflow: visible;">
                                                <span class=" heading">Instance Details </span>
                                                <asp:DropDownList ID="RACNodeId" runat="server" CssClass="selectpicker pull-right" btn-style="btn-default" OnSelectedIndexChanged="RACNodeId_SelectedIndexChanged" AutoPostBack="true">
                                                </asp:DropDownList>
                                            </div>
                                            <div class="widget-body">
                                                <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                                    <thead>
                                                        <tr>
                                                            <th style="width: 26%;">Instance Details
                                                            </th>
                                                            <th style="width: 37%;">PR
                                                            </th>
                                                            <th style="width: 37%;">DR
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>

                                                        <tr>
                                                            <td style="width: 26%;">INSTANCE NAME
                                                            </td>
                                                            <td style="width: 37%;" class="tdword-wrap">
                                                                <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan48"></asp:Label>
                                                                <asp:Label ID="lblInstanceNamePR" runat="server"> </asp:Label>
                                                            </td>
                                                            <td style="width: 37%;">
                                                                <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan49"></asp:Label>
                                                                <asp:Label ID="lblInstanceNameDR" runat="server"> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td style="width: 26%;">INSTANCE ID
                                                            </td>
                                                            <td style="width: 37%;" class="tdword-wrap">
                                                                <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan50"></asp:Label>
                                                                <asp:Label ID="lblInstanceIdPR" runat="server"> </asp:Label>
                                                            </td>
                                                            <td style="width: 37%;">
                                                                <asp:Label class="archive-log-blue vertical-Middle" runat="server" ID="lblSpan51"></asp:Label>
                                                                <asp:Label ID="lblInstanceIdDR" runat="server"> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td style="width: 26%;">INSTANCE STARTUP TIME
                                                            </td>
                                                            <td style="width: 37%;" class="tdword-wrap">
                                                                <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan56"></asp:Label>
                                                                <asp:Label ID="lblInstanceStartupTimePR" runat="server"> </asp:Label>
                                                            </td>
                                                            <td style="width: 37%;">
                                                                <asp:Label class="clock-icon-blue vertical-Middle" runat="server" ID="lblSpan57"></asp:Label>
                                                                <asp:Label ID="lblInstanceStartupTimeDR" runat="server"> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td style="width: 26%;">OPEN MODE
                                                            </td>
                                                            <td style="width: 37%;" class="tdword-wrap">
                                                                <asp:Label class="folder-blue_new vertical-Middle" runat="server" ID="lblSpan54"></asp:Label>
                                                                <asp:Label ID="lblOpenModePR" runat="server"> </asp:Label>
                                                            </td>
                                                            <td style="width: 37%;">
                                                                <asp:Label class="folder-blue_new vertical-Middle" runat="server" ID="lblSpan55"></asp:Label>
                                                                <asp:Label ID="lblOpenModeDR" runat="server"> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 26%;">CLUSTER DATABASE
                                                            </td>
                                                            <td style="width: 37%;" class="tdword-wrap">
                                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan52"></asp:Label>
                                                                <asp:Label ID="lblClusterDatabaseclusPR" runat="server"> </asp:Label>
                                                            </td>
                                                            <td style="width: 37%;">
                                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="lblSpan53"></asp:Label>
                                                                <asp:Label ID="lblClusterDatabaseclusDR" runat="server"> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 26%;">CONTROL FILE NAME
                                                            </td>
                                                            <td style="width: 37%;"><%--class="tdword-wrap"--%>
                                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="Label1"></asp:Label>
                                                                <asp:Label ID="lblPRIntanceControlfileName" runat="server"> </asp:Label>
                                                            </td>
                                                            <td style="width: 37%;">
                                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="Label3"></asp:Label>
                                                                <asp:Label ID="lblDRIntanceControlfileName" runat="server"> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 26%;">PARAMETER FILE
                                                            </td>
                                                            <td style="width: 37%;"><%-- class="tdword-wrap"--%>
                                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="Label2"></asp:Label>
                                                                <asp:Label ID="lblPRInsanceparameterfile" runat="server"> </asp:Label>
                                                            </td>
                                                            <td style="width: 37%;">
                                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="Label5"></asp:Label>
                                                                <asp:Label ID="lblDRInsanceparameterfile" runat="server"> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 26%;">PLATFORM NAME
                                                            </td>
                                                            <td style="width: 37%;" class="tdword-wrap">
                                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="Label4"></asp:Label>
                                                                <asp:Label ID="lblPRInsanceplatformname" runat="server"> </asp:Label>
                                                            </td>
                                                            <td style="width: 37%;">
                                                                <asp:Label class="database_icon_blue vertical-Middle" runat="server" ID="Label7"></asp:Label>
                                                                <asp:Label ID="lblDRInsanceplatformname" runat="server"> </asp:Label>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                        </ContentTemplate>

                                    </asp:UpdatePanel>
                                </div>
                            </div>

                            <div class="widget  widget-body-white">
                                <%--widget-heading-simple--%>
                                <div class="widget-head">
                                    <span class=" heading">ASM Details </span>
                                </div>
                                <div class="widget-body">
                                    <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                        <thead>
                                            <tr>
                                                <th style="width: 26%;"></th>
                                                <th style="width: 37%;">PR
                                                </th>
                                                <th style="width: 37%;">DR
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                            <tr>
                                                <td style="width: 26%;">ASM DG Details
                                                </td>
                                                <td style="width: 37%;">
                                                    <table style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none  innertable-change">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 4%;" class="text-center">#
                                                                </th>
                                                                <th style="width: 16%;">NAME
                                                                </th>
                                                                <th style="width: 16%;">STATE
                                                                </th>
                                                                <th style="width: 16%;">TYPE
                                                                </th>
                                                                <th style="width: 16%;">TOTAL_MB
                                                                </th>
                                                                <th style="width: 16%;">FREE_MB
                                                                </th>
                                                                <th style="width: 16%;">USED(%)
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <asp:UpdatePanel ID="updPRASM" runat="server" UpdateMode="Conditional">
                                                                <ContentTemplate>
                                                                    <asp:Repeater runat="server" ID="rptPRASM">
                                                                        <ItemTemplate>
                                                                            <tr>
                                                                                <td style="width: 4%;" class="text-center">
                                                                                    <asp:Label ID="lblPRSRNO" runat="server" Text='<%#Eval("SRNO") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblPRName" runat="server" Text='<%#Eval("Name") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblPRState" runat="server" Text='<%#Eval("State") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblPRType" runat="server" Text='<%#Eval("Type") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblPRTotalMB" runat="server" Text='<%#Eval("TotalMB") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblPRTotalFreeMB" runat="server" Text='<%#Eval("FreeMB") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblPRUsed" runat="server" Text='<%#Eval("Used") %>'></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                        </ItemTemplate>
                                                                    </asp:Repeater>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>

                                                        </tbody>
                                                    </table>
                                                </td>
                                                <td style="width: 37%;">
                                                    <table style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none  innertable-change">
                                                        <thead>
                                                            <tr>
                                                                <th style="width: 4%;" class="text-center">#
                                                                </th>
                                                                <th style="width: 16%;">NAME
                                                                </th>
                                                                <th style="width: 16%;">STATE
                                                                </th>
                                                                <th style="width: 16%;">TYPE
                                                                </th>
                                                                <th style="width: 16%;">TOTAL_MB
                                                                </th>
                                                                <th style="width: 16%;">FREE_MB
                                                                </th>
                                                                <th style="width: 16%;">USED(%)
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <asp:UpdatePanel ID="updtDRSM" runat="server" UpdateMode="Conditional">
                                                                <ContentTemplate>
                                                                    <asp:Repeater runat="server" ID="rptDRASM">
                                                                        <ItemTemplate>
                                                                            <tr>
                                                                                <td style="width: 4%;" class="text-center">
                                                                                    <asp:Label ID="lblDRSRNo" runat="server" Text='<%#Eval("SRNO") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblDRName" runat="server" Text='<%#Eval("Name") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblDRState" runat="server" Text='<%#Eval("State") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblDRType" runat="server" Text='<%#Eval("Type") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblDRTotalMB" runat="server" Text='<%#Eval("TotalMB") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblDRTotalFreeMB" runat="server" Text='<%#Eval("FreeMB") %>'> </asp:Label>
                                                                                </td>
                                                                                <td style="width: 16%;" class="tdword-wrap">
                                                                                    <asp:Label ID="lblDRUsed" runat="server" Text='<%#Eval("Used") %>'></asp:Label>
                                                                                </td>
                                                                            </tr>
                                                                        </ItemTemplate>
                                                                    </asp:Repeater>
                                                                </ContentTemplate>
                                                            </asp:UpdatePanel>

                                                        </tbody>
                                                    </table>
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="widget  widget-body-white">
                                <%--widget-heading-simple--%>
                                <div class="widget-head">
                                    <span class=" heading">TNS Service Details </span>
                                </div>
                                <div class="widget-body">
                                    <table width="100%" style="table-layout: fixed" class="table table-striped table-bordered table-condensed table-white margin-bottom-none ">
                                        <thead>
                                            <tr>
                                                <th style="width: 26%;">TNS Service Details
                                                </th>
                                                <th style="width: 37%;">PR
                                                </th>
                                                <th style="width: 37%;">DR
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                            <tr>
                                                <td style="width: 26%;">Services
                                                </td>
                                                <td style="width: 37%;" class="tdword-wrap icon-word">
                                                    <asp:Label class="Process_Icon-blue vertical-Middle" runat="server" ID="lblSpan106"></asp:Label>
                                                    <asp:Label ID="lblServicesPR" runat="server"> </asp:Label>
                                                </td>
                                                <td style="width: 37%;" class="icon-word">
                                                    <asp:Label class="Process_Icon-blue vertical-Middle" runat="server" ID="lblSpan107"></asp:Label>
                                                    <asp:Label ID="lblServicesDR" runat="server"> </asp:Label>
                                                </td>
                                            </tr>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>


                        <div class="tab-pane" id="litab4">
                            <table id="tblGlobalMirrorMonitoring" runat="server" class="table table-striped table-condensed table-bordered table-responsive" width="100%">
                                <thead>
                                    <tr>
                                        <th style="width: 26%;">Component Name
                                        </th>
                                        <th style="width: 37%;">PR
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="col-md-4">ID
                                        </td>
                                        <td>
                                            <span id="Span13" class="id-icon" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblGMRID" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Master Count
                                        </td>
                                        <td>
                                            <span id="Span14" class="count-icon" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblMasterCount" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Master Session ID
                                        </td>
                                        <td>
                                            <span id="Span15" class="id-icon" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblMasterSessionID" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Copy State
                                        </td>
                                        <td>
                                            <span id="Span25" class="icon-disk-type" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblCopyState" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Fatal Reason
                                        </td>
                                        <td>
                                            <span id="Span16" class="reason-icon" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblFatal" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>CG Interval Time (seconds)
                                        </td>
                                        <td>
                                            <span id="Span17" class="interval-icon" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblCGIntervalTime" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Coord. Time (milliseconds)
                                        </td>
                                        <td>
                                            <span id="Span18" class="icon-Time" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblCordTime" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Max CG Drain Time (seconds)
                                        </td>
                                        <td>
                                            <span id="Span19" class="icon-Time" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblMaxDrainTime" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Current Time
                                        </td>
                                        <td>
                                            <span id="Span20" class="icon-Time" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblCurrentTime" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>CG Time
                                        </td>
                                        <td>
                                            <span id="Span21" class="icon-Time" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblCGTime" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Successful CG Percentage
                                        </td>
                                        <td>
                                            <span id="Span22" class="percentage-icon" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblCGPerc" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>FlashCopy Sequence Number
                                        </td>
                                        <td>
                                            <span id="Span23" class="sequence-icon" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblseqNo" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Master ID
                                        </td>
                                        <td><span id="Span24" class="id-icon" runat="server"></span>
                                            <span>
                                                <asp:Label ID="lblMasterId" runat="server" Text=""></asp:Label></span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="tab-pane" id="litab3">
                            <table id="tblSvcOracleRelationshipMonitoring" class="table table-striped table-condensed table-bordered table-responsive" width="100%" runat="server">
                                <thead>
                                    <tr>
                                        <th class="col-md-4">Relationship Monitoring
                                        </th>
                                        <th class="col-md-4">Production Server
                                        </th>
                                        <th class="col-md-4">DR Server
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Relationship Name 
                                        </td>
                                        <td><%--<span class="host-icon"></span>--%>
                                            <asp:Label ID="lblPRRelationshipName" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="lblDRRelationshipName" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Group Name
                                        </td>
                                        <td>
                                            <asp:Label ID="lblPRCGroupName" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="lblDRCGroupName" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Relationship Primary Value 
                                        </td>
                                        <td><%--<span class="health-up"></span>--%>
                                            <asp:Label ID="lblPRRelationshipPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="lblDRRelationshipPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Master Change Volume Name 
                                        </td>
                                        <td><%--<span class="health-up"></span>--%>
                                            <asp:Label ID="lblPRMasterChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="lblDRMasterChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Auxiliary Change Volume Name  
                                        </td>
                                        <td><%--<span class="health-up"></span>--%>
                                            <asp:Label ID="lblPRAuxiliaryChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="lblDRAuxiliaryChangeVolumeName" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Relationship State 
                                        </td>
                                        <td><%--<span class="health-up"></span>--%>
                                            <asp:Label ID="lblPRRelationshipState" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="lblDRRelationshipState" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>Relationship Progress 
                                        </td>
                                        <td><%--<span class="health-up"></span>--%>
                                            <asp:Label ID="lblPRRelationshipProgress" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                        <td>
                                            <asp:Label ID="lblDRRelationshipProgress" runat="server" Text="N/A"></asp:Label>
                                        </td>
                                    </tr>

                                </tbody>
                            </table>

                            <div class="widget" id="divSVCGlobalMonitoring" runat="server">

                                <div class="widget-head">
                                    <asp:Label ID="Label34" class="heading" runat="server" Text="SVC-Metro or Global Mirror Consistency Group Monitoring"></asp:Label>
                                </div>

                                <div class="widget-body ">
                                    <div class="tabs-content ">
                                        <table id="tblSVCGlobalMonitor" runat="server" class="table table-bordered table-white" width="100%">
                                            <thead>
                                                <tr>
                                                    <th class="col-md-4">Component
                                                    </th>
                                                    <th class="col-md-4">Production Server
                                                    </th>
                                                    <th class="col-md-4">DR Server
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Group ID 
                                                    </td>
                                                    <td><%--<span class="host-icon"></span>--%>
                                                        <asp:Label ID="lblPRConsistencyGroupID" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="host-icon"></span>--%>
                                                        <asp:Label ID="lblDRConsistencyGroupID" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Group Name
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRConsistencyGroupName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRConsistencyGroupName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Master Cluster Name 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRMasterClusterName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRMasterClusterName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Auxiliary Cluster Name 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRAuxiliaryClusterName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRAuxiliaryClusterName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Group Primary Value 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRGroupPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRGroupPrimaryValue" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Group State 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRGroupState" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRGroupState" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Group Freeze Time 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRGroupfreezetime" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRGroupfreezetime" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Group Status 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRGroupStatus" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRGroupStatus" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Group Sync Status 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRGroupSyncStatus" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRGroupSyncStatus" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Mirror Relationship Name 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRMirrorRelationshipName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRMirrorRelationshipName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Group Relationship Count & Names 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRGroupRelationshipCountNames" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRGroupRelationshipCountNames" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="widget" id="divsvcoraclecontrollermonitor1" runat="server">

                                <div class="widget-head">
                                    <asp:Label ID="Label80" class="heading" runat="server" Text="SVC Storage Controller Monitoring"></asp:Label>
                                </div>

                                <div class="widget-body ">
                                    <div class="tabs-content ">
                                        <table id="tblsvcoraclecontrollermonitor12" class="table table-bordered table-white" width="100%">
                                            <thead>
                                                <tr>
                                                    <th class="col-md-4">Controller Monitoring
                                                    </th>
                                                    <th class="col-md-4">Production Server
                                                    </th>
                                                    <th class="col-md-4">DR Server
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Disk Controller ID 
                                                    </td>
                                                    <td><%--<span class="host-icon"></span>--%>
                                                        <asp:Label ID="lblPRDiskControllerID1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="host-icon"></span>--%>
                                                        <asp:Label ID="lblDRDiskControllerID1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Disk Controller Name
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRDiskControllerName1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRDiskControllerName1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Mdisks Degrade Status 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRMdisksDegradeStatus1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRMdisksDegradeStatus1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Controller Product ID 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRControllerProductID1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRControllerProductID1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>

                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="widget" id="divSVCStoragPoolMonitor1" runat="server">

                                <div class="widget-head">
                                    <asp:Label ID="Label89" class="heading" runat="server" Text="SVC Storage Pool Monitoring"></asp:Label>
                                </div>

                                <div class="widget-body ">
                                    <div class="tabs-content ">
                                        <table id="tblSVCStoragPoolMonitor1" runat="server" class="table table-bordered table-white" width="100%">
                                            <thead>
                                                <tr>
                                                    <th class="col-md-4">Storage Pool Status Monitoring
                                                    </th>
                                                    <th class="col-md-4">Production Server
                                                    </th>
                                                    <th class="col-md-4">DR Server
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Storage Pool Status
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRStoragePoolStatus1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRStoragePoolStatus1" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="widget" id="divSVCSysNodeMonitor" runat="server">

                                <div class="widget-head">
                                    <asp:Label ID="Label45" class="heading" runat="server" Text="SVC Clustered System Node Monitoring"></asp:Label>
                                </div>

                                <div class="widget-body ">
                                    <div class="tabs-content ">
                                        <table id="tblSVCSysNodeMonitor" runat="server" class="table table-bordered table-white" width="100%">
                                            <thead>
                                                <tr>
                                                    <th class="col-md-4">Clustered System Node Monitoring
                                                    </th>
                                                    <th class="col-md-4">Production Server
                                                    </th>
                                                    <th class="col-md-4">DR Server
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Clustered System Node ID 
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblPRClusteredSystemNodeID" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td><%--<span class="health-up"></span>--%>
                                                        <asp:Label ID="lblDRClusteredSystemNodeID" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div class="widget" id="divsvcnodedetailedmonitor" runat="server">

                                <div class="widget-head">
                                    <asp:Label ID="Label49" class="heading" runat="server" Text="SVC Clusered System Node Detailed Monitoring"></asp:Label>
                                </div>

                                <div class="widget-body ">
                                    <div class="tabs-content " width="100%">
                                        <table id="tblsvcnodedetailedmonitor" runat="server" class="table table-bordered table-white" width="100%">
                                            <thead>
                                                <tr>
                                                    <th class="col-md-4">Node Detailed Monitoring
                                                    </th>
                                                    <th class="col-md-4">Production Server
                                                    </th>
                                                    <th class="col-md-4">DR Server
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Node ID  
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblPRNodeID" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblDRNodeID" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Node Name 
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblPRNodeName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblDRNodeName" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Node Status
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblPRNodeStatus" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblDRNodeStatus" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Service IP Address 
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblPRServiceIPAddress" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblDRServiceIPAddress" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Product Machine Type
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblPRProductMachineType" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblDRProductMachineType" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Machine Code Level
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblPRMachineCodeLevel" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblDRMachineCodeLevel" runat="server" Text="N/A"></asp:Label>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <%--RecoverPoint--%>
                         <%--Commented below on 18.06.2018 --%>

                        <%--<div class="tab-pane" id="litab5">
                            <div class="widget" id="divReplicationMonitor" runat="server" style="display: none;">

                                <div class="widget-head">
                                    <asp:Label ID="lblRepliMonitor" CssClass="heading" runat="server" Style="padding-left: 5px !important;"></asp:Label>
                                </div>
                                <div class="widget-body">
                                    <div runat="server" id="divPstatemonitor">
                                        <h5 style="font-size: 16px;">Replication State Monitoring</h5>
                                        <table style="table-layout: fixed; width: 100%" class="table table-bordered table-condensed table-white margin-bottom-none">
                                            <thead>
                                                <tr>
                                                    <th style="width: 33.33%;">Group Name</th>
                                                    <th style="width: 33.33%;">Component</th>
                                                    <th style="width: 33.33%;"></th>

                                                </tr>
                                            </thead>
                                        </table>
                                        <table style="table-layout: fixed; width: 100%" class="table table-bordered table-condensed table-white">
                                            <tbody>
                                                <asp:Repeater runat="server" ID="rptPStateMonitor">
                                                    <HeaderTemplate></HeaderTemplate>
                                                    <ItemTemplate>
                                                        <tr>
                                                            <td style="width: 33.33%; vertical-align: middle;" rowspan="7">
                                                                <asp:Label runat="server" ID="lblComphead" Text='<%#Eval("GGroupName") %>'></asp:Label></td>
                                                            <td style="width: 33.33%;" class="tdword-wrap">
                                                                <asp:Label runat="server" ID="cgGroup" Text="CG GroupName"></asp:Label>
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStateMonitorGroupName" runat="server" Text='<%#Eval("GGroupName") %>'> </asp:Label>
                                                                <asp:Label ID="lblPStateMonitorInfraobject" runat="server" Text='<%#Eval("InfraObjectId") %>' Visible="false"> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Transfer-Source
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStateMonitorTransferSource" runat="server" Text='<%#Eval("TransferSource") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td style="width: 33.33%;" class="tdword-wrap">Copy
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStateMonitorCopy" runat="server" Text='<%#Eval("Copy") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Journal
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStateMonitorJournal" runat="server" Text='<%#Eval("Journal") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 35%;" class="tdword-wrap">Storage-Access
                                                            </td>
                                                            <td style="width: 35%;">
                                                                <asp:Label ID="lblPStateMonitorStorageAccess" runat="server" Text='<%#Eval("StorageAccess") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Link
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStateMonitorLink" runat="server" Text='<%#Eval("Link") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Data-Transfer Status
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStateMonitorDataTransferStatus" runat="server" Text='<%#Eval("DataTransferStatus") %>'> </asp:Label>
                                                            </td>
                                                        </tr>
                                                    </ItemTemplate>
                                                    <FooterTemplate></FooterTemplate>
                                                </asp:Repeater>
                                            </tbody>
                                        </table>

                                    </div>
                                    <div runat="server" id="divPStatisticMonitor">
                                        <h5 style="font-size: 16px;">Replication Statistic Monitoring</h5>
                                        <table style="table-layout: fixed; width: 100%" class="table table-bordered table-condensed table-white margin-bottom-none">
                                            <thead>
                                                <tr>
                                                    <th style="width: 33.33%;">Group Name</th>
                                                    <th style="width: 33.33%;">Component
                                                    </th>
                                                    <th style="width: 33.33%;"></th>
                                                </tr>
                                            </thead>
                                        </table>
                                        <table style="table-layout: fixed; width: 100%" class="table  table-bordered table-condensed table-white">
                                            <tbody>
                                                <asp:Repeater runat="server" ID="rptPStistictMonitor">
                                                    <HeaderTemplate></HeaderTemplate>
                                                    <ItemTemplate>

                                                        <tr>
                                                            <td style="width: 33.33%; vertical-align: middle;" rowspan="12">
                                                                <asp:Label ID="lblpcggroupname" runat="server" Text='<%#Eval("CGGroupName") %>'> </asp:Label></td>
                                                            <td style="width: 33.33%;" class="tdword-wrap">GC Group Name
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticCGGroupName" runat="server" Text='<%#Eval("CGGroupName") %>'> </asp:Label>
                                                                <asp:Label ID="lblPStatisticInfraObjectId" runat="server" Visible="false" Text='<%#Eval("InfraObjectId") %>'> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Usage
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticUsage" runat="server" Text='<%#Eval("Usage") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>
                                                            <td style="width: 33.33%;" class="tdword-wrap">Total
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticTotal" runat="server" Text='<%#Eval("Total") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Last Image
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticLastImage" runat="server" Text='<%#Eval("LastImage") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Current Image
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticCurrentImage" runat="server" Text='<%#Eval("CurrentImage") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Protection Window
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticProtectionWindow" runat="server" Text='<%#Eval("ProtectionWindow") %>'> </asp:Label>
                                                            </td>
                                                        </tr>

                                                        <tr>

                                                            <td style="width: 33.33%;" class="tdword-wrap">Journal Lag
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticJournalLag" runat="server" Text='<%#Eval("Journal_Lag") %>'> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 33.33%;" class="tdword-wrap">Status
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticStatus" runat="server" Text='<%#Eval("Status") %>'> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 33.33%;" class="tdword-wrap">Replication Lag Time 
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticReplcationLagtime" runat="server" Text='<%#Eval("ReplicationLagTime") %>'> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 33.33%;" class="tdword-wrap">Replication Lag Data 
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticReplcationLagData" runat="server" Text='<%#Eval("ReplicationLagData") %>'> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 33.33%;" class="tdword-wrap">Replication Lag Writes 
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticReplcationLagWriters" runat="server" Text='<%#Eval("ReplicationLagWriter") %>'> </asp:Label>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style="width: 33.33%;" class="tdword-wrap">Data Lag
                                                            </td>
                                                            <td style="width: 33.33%;">
                                                                <asp:Label ID="lblPStatisticLag" runat="server" Text='<%#Eval("DataLag") %>'> </asp:Label>
                                                            </td>
                                                        </tr>
                                                    </ItemTemplate>
                                                    <FooterTemplate></FooterTemplate>
                                                </asp:Repeater>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="innerLR" id="divRecoverPointInner" runat="server">
                                <h3>
                                    <img src="../Images/monitor.png" style="vertical-align: text-top">
                                    Recover Point Monitor</h3>
                                <div class="widget" id="Div2" runat="server">

                                    <div class="widget-head" style="display: none;">
                                        <asp:Label runat="server" CssClass="interval-icon" Style="margin-left: 10px;"></asp:Label>
                                        <asp:Label ID="lblRecoverPointHead" CssClass="heading" runat="server" Style="float: none; padding-left: 0;" Text=""></asp:Label>
                                    </div>

                                    <div class="widget-body " id="">
                                        <div id="divRecoverPointReplica" class="tabs-content">
                                            <table id="Table9" class="table table-bordered table-condensed table-striped" runat="server" style="width: 100%; table-layout: fixed">
                                                <thead>
                                                    <tr>
                                                        <th class="col-md-3">Component</th>
                                                        <th class="col-md-3">Site1</th>
                                                        <th class="col-md-3">Site2</th>
                                                        <th class="col-md-3" id="hideSite3th" runat="server">Site3</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="col-md-3" rowspan="2" style="vertical-align: middle;">RP Appliance Version</td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="site-icon"></asp:Label>
                                                            <asp:Label ID="Label119" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="site-icon"></asp:Label>
                                                            <asp:Label ID="Label120" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideSite3td" runat="server">
                                                            <asp:Label runat="server" CssClass="site-icon"></asp:Label>
                                                            <asp:Label ID="Label121" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                                            <asp:Label ID="Label122" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                                            <asp:Label ID="Label123" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideVersion3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                                            <asp:Label ID="Label124" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>

                                            <table id="Table4" class="table table-bordered table-condensed table-striped" runat="server">
                                                <tbody>

                                                    <tr>
                                                        <td class="col-md-3">Latest Image
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                                            <asp:Label ID="Label87" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                                            <asp:Label ID="Label88" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideLatestImage3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                                            <asp:Label ID="Label90" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="col-md-3">Current Image
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                                            <asp:Label ID="Label91" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                                            <asp:Label ID="Label94" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideCurrentImage3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                                            <asp:Label ID="Label97" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>

                                            <table id="Table5" class="table table-bordered table-condensed table-striped" runat="server">

                                                <tbody>
                                                    <tr>
                                                        <td class="col-md-3">Replication Lag</td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                                            <asp:Label ID="Label98" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="lag-blue-icon vertical-Middle"></asp:Label>
                                                            <asp:Label ID="Label99" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideReplicationLag3td" runat="server">
                                                            <asp:Label runat="server" CssClass="lag-blue-icon vertical-Middle"></asp:Label>
                                                            <asp:Label ID="Label100" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="col-md-3">Replication Lag Time
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                                            <asp:Label ID="Label101" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                                            <asp:Label ID="Label102" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideReplicationLagTime3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                                            <asp:Label ID="Label103" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="col-md-3">Replication Lag Data
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                                            <asp:Label ID="Label104" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-disks"></asp:Label>
                                                            <asp:Label ID="Label105" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideReplicationLagData3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-disks"></asp:Label>
                                                            <asp:Label ID="Label106" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="col-md-3">Replication Lag Write
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                                            <asp:Label ID="Label107" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                                            <asp:Label ID="Label108" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideReplicationLagwrite3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-release"></asp:Label>
                                                            <asp:Label ID="Label109" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table id="Table6" class="table table-bordered table-condensed table-striped" runat="server">

                                                <tbody>
                                                    <tr>
                                                        <td class="col-md-3">Storage Access State</td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="asnych_icon-blue vertical-Middle" Style="background-position: 0 4px;"></asp:Label>
                                                            <asp:Label ID="Label110" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-disable"></asp:Label>
                                                            <asp:Label ID="Label111" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideStorageAccessState3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-disable"></asp:Label>
                                                            <asp:Label ID="Label112" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table id="Table7" class="table table-bordered table-condensed table-striped" runat="server">

                                                <tbody>
                                                    <tr>
                                                        <td class="col-md-3">Data Transfer Status</td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                                            <asp:Label ID="Label113" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-enable"></asp:Label>
                                                            <asp:Label ID="Label114" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideDataTransferStatus3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-enable"></asp:Label>
                                                            <asp:Label ID="Label115" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table id="Table8" class="table table-bordered table-condensed table-striped" runat="server">

                                                <tbody>
                                                    <tr>
                                                        <td class="col-md-3">Data Lag</td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-NA"></asp:Label>
                                                            <asp:Label ID="Label116" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3">
                                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                                            <asp:Label ID="Label117" runat="server" Text=""></asp:Label>
                                                        </td>
                                                        <td class="col-md-3" id="hideDataLag3td" runat="server">
                                                            <asp:Label runat="server" CssClass="icon-Time"></asp:Label>
                                                            <asp:Label ID="Label118" runat="server" Text=""></asp:Label>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>--%>

                        <%--EndRecoverPoint--%>



                        <div class="tab-pane" id="litab6">
                            <div id="divHitachiURReplication" runat="server">
                                <div class="widget  widget-body-white">
                                    <div class="widget-body">
                                        <table id="tblHitachiURReplication" class="table table-bordered table-condensed"
                                            width="100%" runat="server" style="display: table;">
                                            <thead>
                                                <tr>
                                                    <th class="col-md-4">Replication Monitor
                                                    </th>
                                                    <th class="col-md-4">Production
                                                    </th>
                                                    <th>DR
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>Storage ID
                                                    </td>
                                                    <td class="text-indent">
                                                        <span id="span3" class="id-icon" runat="server">&nbsp;&nbsp;</span>
                                                        <asp:Label ID="lblHitachiURPRStorage" runat="server"></asp:Label>
                                                    </td>
                                                    <td class="text-indent">
                                                        <span id="span4" class="id-icon" runat="server">&nbsp;&nbsp;</span>
                                                        <asp:Label ID="lblHitachiURDRStorage" runat="server"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>HORCOM Instance
                                                    </td>
                                                    <td class="text-indent">
                                                        <span class="icon-numbering">&nbsp;</span>
                                                        <asp:Label ID="lblHitachiURPRHORCOMInstance" runat="server"></asp:Label>
                                                    </td>
                                                    <td class="text-indent">
                                                        <span class="icon-numbering">&nbsp;</span>
                                                        <asp:Label ID="lblHitachiURDRHORCOMInstance" runat="server"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>HORCOM Status
                                                    </td>
                                                    <td class="text-indent">
                                                        <asp:Label class="icon-async" runat="server" ID="spanPrHORCOMStatus">&nbsp;&nbsp;</asp:Label>
                                                        <asp:Label ID="lblPrHORCOMStatus" runat="server"></asp:Label>
                                                    </td>
                                                    <td class="text-indent">
                                                        <asp:Label class="icon-async" runat="server" ID="spanDrHORCOMStatus">&nbsp;&nbsp;</asp:Label>
                                                        <asp:Label ID="lblDrHORCOMStatus" runat="server"></asp:Label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Average Copy Pending Timer
                                                    </td>
                                                    <td colspan="2" class="text-indent">
                                                        <span class="icon-Time">&nbsp;&nbsp;</span>
                                                        <asp:Label ID="lblAverageCopyPendingTimer" runat="server"></asp:Label>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <asp:ListView ID="ListView1" runat="server" OnItemDataBound="ListView1_ItemDataBound">
                                            <LayoutTemplate>
                                                <table id="tblHitachiURDeviceMonitoring" class="table table-bordered table-condensed" style="display: table;">
                                                    <thead>
                                                        <tr>
                                                            <th colspan="3">Device Group:
                                                <asp:Label runat="server" ID="lblDeviceGroupName"></asp:Label>
                                                            </th>
                                                        </tr>
                                                        <tr>
                                                            <th class="col-md-4">P-VOL/ S-VOL
                                                            </th>
                                                            <th>Lun ID
                                                            </th>
                                                            <th>Status
                                                            </th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder"></asp:PlaceHolder>
                                                    </tbody>
                                                </table>
                                            </LayoutTemplate>
                                            <ItemTemplate>
                                                <tr>
                                                    <td>
                                                        <asp:Label ID="lblType" runat="server" Text='<%# Eval("Type") %>'></asp:Label>
                                                    </td>
                                                    <td>
                                                        <asp:Label ID="lblLunsId" runat="server" Text='<%# Eval("LunsId") %>'></asp:Label>
                                                    </td>
                                                    <td class="text-indent">
                                                        <asp:Label ID="Label38" runat="server" CssClass='<%#GetDeviceClass(Eval("VolumeStatus")) %>'>&nbsp;&nbsp;</asp:Label>
                                                        <asp:Label ID="lblVolumeStatus" runat="server" Text='<%# Eval("VolumeStatus") %>'> </asp:Label>
                                                        <asp:HiddenField ID="hdfDeviceName" runat="server" Value='<%# Eval("DeviceGroupName")%>'></asp:HiddenField>
                                                    </td>
                                                </tr>
                                            </ItemTemplate>
                                        </asp:ListView>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane" id="litab7">

                            <uc1:ZFSReplicationMonitor_UserControl ID="ZFSReplicationMonitor_UserControl" runat="server" Visible="true" />

                        </div>

                          <div id="litab5" class="tab-pane">
                            <div class="innerLR">
                                <uc1:RecoveryPointMultiMonitoringDetails runat="server" ID="RecoveryPointMultiMonitoringDetails" Visible="true" />
                            </div>

                        </div>

                         <div class="tab-pane" id="litab8">
                           <uc1:HACMPPowerHAClusterDetails ID="HACMPPowerHAClusterDetails" runat="server" Visible="true" />
                        </div>
                         <div class="tab-pane" id="litab9">
                           <uc1:VeritasClusterDetailedmonitoring ID="VeritasClusterDetailedmonitoring" runat="server" Visible="true" />
                        </div>
                         <div class="tab-pane" id="litab10">
                           <uc1:HP3PARmonitorDetails ID="HP3PARmonitorDetailsMonitoring" runat="server" Visible="true" />
                        </div>
                          
                       
                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>
    </div>

    <%--   </div>--%>


    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script>
        $(document).ready(function () {
            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
                // setHeight: "200px",
            });
        });

        function pageLoad() {

            $(".notifyscroll").mCustomScrollbar({
                axis: "y",
                // setHeight: "200px",
            });
        }


    </script>

</asp:Content>
