﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess.BusinessServiceRTO
{
    internal sealed class BusinessServiceRTOInfoDataAccess : BaseDataAccess, IBusinessServiceRTOInfoDataAccess
    {
        #region Constructors

        public BusinessServiceRTOInfoDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<BusinessServiceRTOInfo> CreateEntityBuilder<BusinessServiceRTOInfo>()
        {
            return (new BusinessServiceRTOInfoBuilder()) as IEntityBuilder<BusinessServiceRTOInfo>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        /// Gets BusinessFunctionRTO information by businessServiceId
        /// </summary>
        /// <param name="businessServiceId">businessServiceId</param>
        /// <returns>List of BusinessServiceRTOInfo </returns>
        IList<BusinessServiceRTOInfo> IBusinessServiceRTOInfoDataAccess.GetById(int businessServiceId)
        {
            try
            {
                const string sp = "BusiServiceRTOInfo_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.Int32, businessServiceId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<BusinessServiceRTOInfo>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceRTOInfoDataAccess.GetById(" + businessServiceId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        /// <summary>
        /// Get current RTO value
        /// </summary>
        /// <param name="infraObjectId">infraObjectId</param>
        /// <returns>BusinessServiceRTOInfo</returns>
        /// <author>Ram Mahajan-28/08/2014</author>
        BusinessServiceRTOInfo IBusinessServiceRTOInfoDataAccess.GetCurrentRTOByInfraId(int infraObjectId)
        {
            try
            {
                const string sp = "BSRTOINFO_GETBYINFRAID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraObjectId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<BusinessServiceRTOInfo>()).BuildEntity(reader, new BusinessServiceRTOInfo())
                            : null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceRTOInfoDataAccess.GetCurrentRTOByInfraId(" + infraObjectId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        BusinessServiceRTOInfo IBusinessServiceRTOInfoDataAccess.GetMonthlyNotRecoveredServices(string iType, int Month)
        {
            BusinessServiceRTOInfo obj = new BusinessServiceRTOInfo();
            try
            {
                const string sp = "BUSINESSSERVICE_NOTRECOVERED";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, iType);
                    Database.AddInParameter(cmd, Dbstring + "iMonth", DbType.Int32, Month);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            obj.ServiceCount = Convert.IsDBNull(reader["SCount"]) == false ? Convert.ToInt32(reader["SCount"]) : 0;
                            //obj.Months = Convert.IsDBNull(reader["Months"]) == false ? Convert.ToString(reader["Months"]) : null;
                        }
                        //return reader.Read()
                        //    ? (CreateEntityBuilder<BusinessServiceRTOInfo>()).BuildEntity(reader, new BusinessServiceRTOInfo())
                        //    : null;

                        return obj;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceRTOInfoDataAccess.GetMonthlyNotRecoveredServices(" + Month +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        BusinessServiceRTOInfo IBusinessServiceRTOInfoDataAccess.GetMonthlyRecoveredServices(string iType, int Month)
        {
            BusinessServiceRTOInfo obj = new BusinessServiceRTOInfo();
            try
            {
                const string sp = "BUSINESSSERVICE_RECOVERED";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, iType);
                    Database.AddInParameter(cmd, Dbstring + "iMonth", DbType.Int32, Month);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            obj.ServiceCount = Convert.IsDBNull(reader["SCount"]) == false ? Convert.ToInt32(reader["SCount"]) : 0;
                            //obj.Months = Convert.IsDBNull(reader["Months"]) == false ? Convert.ToString(reader["Months"]) : null;
                        }
                        //return reader.Read()
                        //    ? (CreateEntityBuilder<BusinessServiceRTOInfo>()).BuildEntity(reader, new BusinessServiceRTOInfo())
                        //    : null;

                        return obj;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceRTOInfoDataAccess.GetMonthlyRecoveredServices(" + Month +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<BusinessServiceRTOInfo> IBusinessServiceRTOInfoDataAccess.GetDrillExecutionCount()  
        {
            BusinessServiceRTOInfo obj = new BusinessServiceRTOInfo();

            IList<BusinessServiceRTOInfo> _rtoInfo = new List<BusinessServiceRTOInfo>();

            try
            {
                const string sp = "BIADRILLEXECUTIONDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {


#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            obj = new BusinessServiceRTOInfo();
                            obj.ServiceCount = Convert.IsDBNull(reader["ServiceCount"]) == false ? Convert.ToInt32(reader["ServiceCount"]) : 0;
                            obj.Months = Convert.IsDBNull(reader["Months"]) == false ? Convert.ToString(reader["Months"]) : null;
                            //obj.MonthID = Convert.IsDBNull(reader["MonthID"]) ? Convert.ToInt32(reader["MonthID"]) : 0;
                            obj.MonthID = Convert.IsDBNull(reader["MonthID"]) ? 0 : Convert.ToInt32(reader["MonthID"]);
                            _rtoInfo.Add(obj);

                        }
                        //return reader.Read()
                        //    ? (CreateEntityBuilder<BusinessServiceRTOInfo>()).BuildEntity(reader, new BusinessServiceRTOInfo())
                        //    : null;

                        return _rtoInfo;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IBusinessServiceRTOInfoDataAccess.GetMonthlyNotRecoveredServices(" + obj.Months +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        int IBusinessServiceRTOInfoDataAccess.GetRTORecoveredServiceCount()
        {
            try
            {
                const string sp = "RTOSERVICECOUNT_RECOVERED";
                int BreachCount = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            return Convert.IsDBNull(reader["RecBusSer"]) ? 0 : Convert.ToInt32(reader["RecBusSer"]);
                        }
                    }
                    return BreachCount;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function SignatureIBusinessServiceRTOInfoDataAccess.GetRTORecoveredServiceCount()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        int IBusinessServiceRTOInfoDataAccess.GetRTONotRecoveredServiceCount()
        {
            try
            {
                const string sp = "RTOSERVICECOUNT_NOTRECOVERED";
                int BreachCount = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            return Convert.IsDBNull(reader["NotRecBusSer"]) ? 0 : Convert.ToInt32(reader["NotRecBusSer"]);
                        }
                    }
                    return BreachCount;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function SignatureIBusinessServiceRTOInfoDataAccess.GetRTONotRecoveredServiceCount()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        int IBusinessServiceRTOInfoDataAccess.GetRTORecoveredInfraObjCount()
        {
            try
            {
                const string sp = "RTOINFRAOBJCOUNT_RECOVERED";
                int BreachCount = 0;
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            return Convert.IsDBNull(reader["RecInfra"]) ? 0 : Convert.ToInt32(reader["RecInfra"]);
                        }
                    }
                    return BreachCount;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function SignatureIBusinessServiceRTOInfoDataAccess.GetRTORecoveredInfraObjCount()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        void IBusinessServiceRTOInfoDataAccess.UpdateRPORTO(int Id, int ConfigRPO, int ConfgiRTO)
        {
            try
            {
                const string sp = "BusinessService_UpdRPORTO";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, Id);
                    Database.AddInParameter(cmd, Dbstring + "iConfigRPO", DbType.Int32, ConfigRPO);
                    Database.AddInParameter(cmd, Dbstring + "iConfigRTO", DbType.Int32, ConfgiRTO);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    Database.ExecuteNonQuery(cmd);
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function SignatureIBusinessServiceRTOInfoDataAccess.UpdateRPORTO()" + Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }
        #endregion Methods
    }
}