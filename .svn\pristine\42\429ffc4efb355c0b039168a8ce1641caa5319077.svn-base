﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="UserGroup.aspx.cs" Inherits="CP.UI.UserGroup" Title="Continuity Patrol :: User : UserGroup" %>

<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
        $(document).ready(function () {
            //$("#ctl00_Header1_liConfigure").addClass("active");
            //setTimeout(function () {
            //    $("#ctl00_Header1_liInfraObject").removeClass("active");
            //}, 100)
        })
    </script>

    <style type="text/css">
        .tdword-wrap {
            width: 99%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }


    </style>
    <div class="innerLR">
        <asp:UpdatePanel ID="updtpnl" UpdateMode="Conditional" runat="server">
            <ContentTemplate>


                <h3>
                    <img src="../Images/event-icon.png">
                    User Group</h3>


                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <asp:Label ID="lblMessage" runat="server" Visible="False"></asp:Label>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">

                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Group Name
                            <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtInstanceName" runat="server" AutoPostBack="true" CssClass="form-control" OnTextChanged="txtInstanceName_TextChanged"></asp:TextBox>
                                        <asp:Label ID="lblInstanceName" runat="server" CssClass="error" ForeColor="Red"></asp:Label>
                                        <asp:RequiredFieldValidator ID="rqdUsrID" runat="server" ControlToValidate="txtInstanceName" CssClass="error"
                                            ErrorMessage="Enter Group Name" Display="Dynamic"></asp:RequiredFieldValidator>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label" style="width: 25.8% !important;">
                                        User<span class="inactive"> *</span></label>
                                    <div class="col-md-9" style="width: 35.3% !important; border: solid 1px #cccccc;">

                                        <asp:TextBox ID="txtSearch" runat="server" autocomplete="off" class="form-control" Style="display: block; width: 100% !important; margin-top: 10px !important; margin-bottom: 10px !important;" onkeyup="SearchInfra(this,'#ctl00_cphBody_chkapprovers');"
                                            placeholder="Search User">
                                        </asp:TextBox>
                                        <asp:Panel ID="Panel1" runat="server" ScrollBars="Vertical" Height="144px" class="padding pull-left"
                                            Width="100%" BorderColor="#cccccc" BorderStyle="Solid" BorderWidth="1px" Style="margin-bottom: 10px;" TabIndex="7">
                                            <asp:CheckBoxList ID="chkapprovers" runat="server">
                                                <asp:ListItem Text="Select All"></asp:ListItem>
                                            </asp:CheckBoxList>
                                        </asp:Panel>
                                        <%--     <asp:CustomValidator ID="CustomValidator1" ErrorMessage="Please select at least one IPAddress."
                                            ForeColor="Red" ClientValidationFunction="ValidateCheckBoxList" runat="server" />--%>
                                    </div>
                                    <asp:Label ID="Label1" runat="server" CssClass="error" ForeColor="Red"></asp:Label>
                                </div>

                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Remark
                            <span class="inactive">*</span></label>
                                    <div class="col-md-9">
                                        <asp:TextBox ID="txtUserName" runat="server" TextMode="MultiLine" CssClass="form-control"></asp:TextBox>
                                        <asp:Label ID="lblerror" CssClass="error" ForeColor="Red" runat="server" Text=""></asp:Label>
                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ErrorMessage="Please enter reason"
                                            ControlToValidate="txtUserName"></asp:RequiredFieldValidator>
                                    </div>
                                </div>

                                <hr class="separator" />
                                <div class="form-actions row">

                                    <div class="col-xs-5">
                                        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span>
                                    </div>


                                    <div class="col-xs-7">
                                        <asp:Button CssClass="btn btn-primary" Style="width: 15%; margin-left: 8px;" runat="server" Text="Save" TabIndex="15" CausesValidation="True" ID="btnAdd" OnClick="btnAdd_Click" />&nbsp;&nbsp;
                     <asp:HiddenField ID="hdeventid" runat="server" />
                                        <asp:Button ID="btnCancel" CssClass="btn btn-default" Width="15%" runat="server" OnClick="btnCancel_Click"
                                            Text="Cancel" CausesValidation="False" TabIndex="9" />
                                    </div>
                                </div>
                                <hr class="separator" />
                                <div class="row">
                                    <div class="col-xs-5 col-md-push-7 text-right" style="padding : 9px ; !important">

                                        <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Group name"></asp:TextBox>
                                        <asp:Button ID="btnSearch" CausesValidation="false" runat="server" CssClass="btn btn-primary" Width="16%" style="margin-right: 8px; !important" Text="Search" OnClick="btnSearch_Click" />
                                    </div>
                                </div>
                                <asp:ListView ID="grplst" runat="server" OnItemEditing="grplst_ItemEditing" OnItemDeleting="grplst_ItemDeleting" OnPreRender="grplst_PreRender">
                                    <LayoutTemplate>
                                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                                            <thead>
                                                <tr>
                                                    <th style="width: 4%;" class="text-center">
                                                        <span>
                                                            <img src="../Images/icons/server1.png" /></span>
                                                    </th>
                                                    <th style="width: 16%;">Group Name
                                                    </th>
                                                    <th style="width: 20%;">Users
                                                    </th>
                                                    <th style="width: 15%;">Remark
                                                    </th>
                                                    <th style="width: 8%;">Action
                                                    </th>

                                                </tr>
                                            </thead>
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                            </tbody>
                                        </table>
                                    </LayoutTemplate>
                                    <EmptyDataTemplate>
                                        <div class="message warning align-center bold no-bottom-margin">
                                            <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td class="text-center" style="width: 4%;">
                                                <%#Container.DataItemIndex+1 %>
                                            </td>
                                            <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                            <td style="width: 16%;" class="tdword-wrap">
                                                <asp:Label ID="name" runat="server" Text='<%#Eval("LoginName") %>' />
                                            </td>
                                            <td style="width: 20%; word-wrap: break-word; !important">
                                                <asp:Label ID="wfname" runat="server" ToolTip='<%# Eval("LastLoginIP") %>' Text='<%# Eval("LastLoginIP") %>' />
                                            </td>
                                            <td style="width: 15%;" class="tdword-wrap">
                                                <asp:Label ID="Db_IP_Address" runat="server" Text='<%#Eval("UserName") %>' />
                                            </td>
                                            <td style="width: 8%;" class="text-center">
                                                <asp:ImageButton ID="btnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                    ToolTip="Edit" ImageUrl="../images/icons/pencil.png" CausesValidation="False" />
                                                <asp:ImageButton ID="btnDelete" runat="server" CommandName="Delete" AlternateText="Delete" CausesValidation="False"
                                                    ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>

                                <div class="row">
                                    <div class="col-xs-6">
                                        <asp:DataPager ID="dataPager2" runat="server" PagedControlID="grplst">
                                            <Fields>
                                                <asp:TemplatePagerField>
                                                    <PagerTemplate>
                                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                        Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                        <br />
                                                    </PagerTemplate>
                                                </asp:TemplatePagerField>
                                            </Fields>
                                        </asp:DataPager>
                                    </div>
                                    <div class="col-xs-6 text-right">
                                        <asp:DataPager ID="dataPager1" runat="server" PagedControlID="grplst" PageSize="10">
                                            <Fields>
                                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                    ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                                <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                    NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                    NumericButtonCssClass="btn-pagination" />
                                                <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                    ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                            </Fields>
                                        </asp:DataPager>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>



    <script>

        function SearchInfra(txtSearch, chkapprovers) {
            if ($(txtSearch).val() != "") {
                var count = 0;
                $(chkapprovers).children('tbody').children('tr').each(function () {
                    var match = false;
                    $(this).children('td').children('label').each(function () {
                        if ($(this).text().toUpperCase().indexOf($(txtSearch).val().toUpperCase()) > -1)
                            match = true;
                    });
                    if (match) {
                        $(this).show();
                        count++;
                    }
                    else { $(this).hide(); }
                });
            }
            else {
                $(chkapprovers).children('tbody').children('tr').each(function () {
                    $(this).show();
                });
                $('#spnCount').html('');
            }
        }
    </script>
</asp:Content>


