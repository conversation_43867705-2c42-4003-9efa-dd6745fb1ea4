﻿using System;
using System.Runtime.Serialization;
using CP.Common.Base;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "RoboCopyJob", Namespace = "http://www.ContinuityPlatform.com/types")]

    public class RoboCopyJob : BaseEntity
    {
        #region Properties


        [DataMember]
        public int RoboCopyId { get; set; }

        [DataMember]
        public string SourceDirectory { get; set; }

        [DataMember]
        public string DestinationDirectory { get; set; }

        [DataMember]
        public int RoboCopyOptionsId { get; set; }

        [DataMember]
        public string LastSuccessfullReplTime { get; set; }


        [DataMember]
        public string NextScheduleRepTime { get; set; }

        [DataMember]
        public string ModeType { get; set; }

        [DataMember]
        public string OSPlatform { get; set; }


        #endregion Properties
    }
}
