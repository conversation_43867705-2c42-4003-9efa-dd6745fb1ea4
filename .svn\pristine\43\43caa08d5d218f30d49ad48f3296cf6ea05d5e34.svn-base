﻿using CP.BusinessFacade;
using CP.CacheController;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI
{
    public partial class OTP_UserDetailsList : OTPConfigBasePage
    {
        #region variable

        private readonly ILog _logger = LogManager.GetLogger(typeof(OTP_UserDetailsList));
        private static CacheManager _cacheManager;
        private static string cacheKey = "Component.NodesIntoCache";
        public static string CurrentUrl = Constants.UrlConstants.Urls.Admin.otpConfig;

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Admin.Otplist;
                }
                return string.Empty;
            }
        }

        #endregion variable

        private static CacheManager CurrentCacheManager
        {
            get { return _cacheManager ?? (_cacheManager = new CacheManager()); }
        }

        public override void PrepareView()
        {
            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            Utility.SelectMenu(Master, "Module3");
            BindList();
        }

        private void BindList()
        {
            setListViewPage();
            lvotp.DataSource = GetOTPConfigList();
            lvotp.DataBind();
        }

        private IList<OTPConfiguration> GetOTPConfigList()
        {
            IList<OTPConfiguration> test;
            test = Facade.GetAllOTPConfig();
            if (LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.EnterpriseUser)
            {
                return test;
            }
            else
            {
                string role = Convert.ToString(LoggedInUserRole);
                test = (from p in test where (p.CreatorId == LoggedInUserId) select p).ToList();
                return Facade.GetOTPConfigByUserId(LoggedInUserId);

            }
            return test;
        }
       
        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageOTP"]) != -1) && Session["CurrentPageOTP"] != null)
            {
                if (Convert.ToInt32(Session["CurrentPageOTP"]) == dataPager1.TotalRowCount)
                {
                    Session["CurrentPageOTP"] = Convert.ToInt32(Session["CurrentPageOTP"]) - dataPager1.MaximumRows;
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageOTP"]), dataPager1.MaximumRows, true);
                Session["CurrentPageOTP"] = -1;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void lvotp_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentUrl);
            Session["CurrentPageOTP"] = (dataPager1.StartRowIndex);
            var lbl1 = (lvotp.Items[e.NewEditIndex].FindControl("ID")) as Label;
            var lblName = (lvotp.Items[e.NewEditIndex].FindControl("UserId")) as Label;


            ActivityLogger.AddLog(LoggedInUserName, "The User :", UserActionType.UpdateServerComponent, "The User '" + lbl1.Text + "' Opened as Editing Mode.", LoggedInUserId);
            if (lbl1 != null)
            {
                secureUrl = UrlHelper.BuildSecureUrl(CurrentUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.OTPConfigsId, lbl1.Text);
                Helper.Url.Redirect(secureUrl);
            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        public string GetServerName(object serverId)
        {
            if (serverId != null)
            {
                if (Facade.GetServerById(Convert.ToInt32(serverId)) != null)
                {
                    return Facade.GetServerById(Convert.ToInt32(serverId)).Name.ToString();
                }
            }
            return "";
        }

        public string GetUsersName(object userId)
        {
            string usernam = string.Empty;
            try
            {
                if (userId != null)
                {
                    var tp = Facade.GetUserById(Convert.ToInt32(userId));
                    if (tp != null)
                    {
                        return usernam = tp.LoginName;
                    }
                }
            }
            catch (Exception ex)
            {
                throw (ex);
            }
            return usernam;
        }

        protected void lvotp_ItemCanceling(object sender, ListViewCancelEventArgs e)
        {
            lvotp.EditIndex = -1;
            lvotp.DataSource = GetOTPConfigList();
            lvotp.DataBind();
        }

        protected void lvotp_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageOTPList"] = (dataPager1.StartRowIndex);
                Session["TotalPageRowsCount"] = dataPager1.TotalRowCount;
                var lbl = (lvotp.Items[e.ItemIndex].FindControl("Id")) as Label;
                var lblName = (lvotp.Items[e.ItemIndex].FindControl("UserId")) as Label;
                var lbltype = (lvotp.Items[e.ItemIndex].FindControl("DeviceType")) as Label;
                

                //if (lbl != null && lblName != null && ValidateRequest("OTPConfiguration", UserActionType.DeleteServerComponent))
                if (lbl != null)
                {

                    Facade.DeleteOTPConfigById(Convert.ToInt32(lbl.Text));

                    //ActivityLogger.AddLog(LoggedInUserName, "OTP", UserActionType.DeleteServerComponent, "The OTP '" + lbl.Text + "' was deleted", LoggedInUserId);

                    //ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("Server" + " " + '"' + lbl.Text + '"', TransactionType.Delete));

                    ActivityLogger.AddLog(LoggedInUserName, "The User", UserActionType.DeleteServerComponent, "The User was deleted", LoggedInUserId);

                    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage("The Application  :" + " " + '"' + lbltype.Text + '"', TransactionType.Delete));

                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                                     "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }
            if (ReturnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(ReturnUrl);
            }
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void lvotp_PreRender(object sender, EventArgs e)
        {
            if (String.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvotp.DataSource = GetOTPConfigList();
                lvotp.DataBind();
            }
            else
            {
                lvotp.DataSource = GetOTPListBySearch(txtsearchvalue.Text);
                lvotp.DataBind();
            }
        }

        protected void lvotp_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;
            if (IsUserOperator || IsUserManager)
            {
                edit.Enabled = false;
                edit.ImageUrl = "../images/icons/pencil_disable.png";
                delete.Enabled = false;
                delete.ImageUrl = "../images/icons/cross-circle_disable.png";
            }
        }

        protected void lvotp_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            try
            {
                lvotp.DataSource = GetOTPListBySearch(txtsearchvalue.Text);
                lvotp.DataBind();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured while binding data", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        private IList<OTPConfiguration> GetOTPListBySearch(string value)
        {
            var list = GetOTPConfigList();
            value = value.Trim();
            if (!String.IsNullOrEmpty(value) && list != null && list.Count > 0)
            {
                var result = (from nodes in list where nodes.URL.ToLower().Contains(value.ToLower()) select nodes).ToList();
                return result;
            }
            return null;
        }
    }
}