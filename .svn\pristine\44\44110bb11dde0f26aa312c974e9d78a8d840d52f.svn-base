﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class HANADatabaseConfig : System.Web.UI.UserControl
    {
        //protected void Page_Load(object sender, EventArgs e)
        //{

        //}

         public string DatabaseSID
        {
            get
            {
                return txtDatabaseSid.Text;
            }
            set
            {
                txtDatabaseSid.Text = value;
            }
        }

         public string InstanceNumber
         {
             get
             {
                 return txtInstanceNo.Text;
             }
             set
             {
                 txtInstanceNo.Text = value;
             }
         }

         public string HostName
         {
             get
             {
                 return txtHostName.Text;
             }
             set
             {
                 txtHostName.Text = value;
             }
         }

        public string UserName
        {
            get
            {
                return txtUserName.Text;
            }
            set
            {
                txtUserName.Text = value;
            }
        }

        public string Password
        {
            get
            {
                return txtPassword.Text;
            }
            set
            {
                txtPassword.Text = value;
            }
        }

        public string Port
        {
            get
            {
                return txtPort.Text;
            }
            set
            {
                txtPort.Text = value;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            txtDatabaseSid.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");
            txtInstanceNo.Attributes.Add("onblur", "ValidatorValidate(" + rfvInstanceNo.ClientID + ")");
            txtHostName.Attributes.Add("onblur", "ValidatorValidate(" + rfvHostName.ClientID + ")");
            txtUserName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
           //txtPassword.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            txtPassword.Attributes.Add("onblur", "getHashData(" + txtPassword.ClientID + "),ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            txtPort.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");
        }
    
    }
}