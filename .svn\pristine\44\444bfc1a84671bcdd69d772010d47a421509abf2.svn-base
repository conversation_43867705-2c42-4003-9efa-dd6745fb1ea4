using System;
using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IParallelWorkflowactionResultDataAccess

    public interface IParallelWorkflowActionResultDataAccess
    {
        IList<ParallelWorkflowActionResult> GetDrillDateSummaryByDate(DateTime dt);
        IList<ParallelWorkflowActionResult> GetParallelWFResultData();
        ParallelWorkflowActionResult Add(ParallelWorkflowActionResult droperation);

        ParallelWorkflowActionResult ChangeParallelWorkflowActionResult(ParallelWorkflowActionResult droperation);

        ParallelWorkflowActionResult Update(ParallelWorkflowActionResult droperation);

        ParallelWorkflowActionResult GetById(int id);

        IList<ParallelWorkflowActionResult> GetAllRunning();
        IList<ParallelWorkflowActionResult> GetAll();

        IList<ParallelWorkflowActionResult> GetParallelDrOperationdata(int id, int wid);

        IList<ParallelWorkflowActionResult> GetParallelDrOperationSummarydata(int id);

        ParallelWorkflowActionResult GetExecutionTimeByInfraObjectId(int infraobjectId);

        bool UpdateWFResultConditionalByPGrpIdAndInfraId(int parallelGrpId, int infraId, int currentActionId, int conditional);

        bool UpdateWfResultConditionalOperation(int id, int conditoinalAction);

        bool DeleteById(int id);

       // bool UpdateWFResultConditionalByPGrpIdAndInfraId(int parallelGrpId, int infraId, int currentActionId, int conditional);

        IList<ParallelWorkflowActionResult> GetParalellWFByDrOpId(int parallelDrOperationId);

        //bool UpdateWfResultConditionalOperation(int id, int conditoinalAction);

        IList<ParallelWorkflowActionResult> GetParalellWFResultByDrOpId(int parallelDrOperationId, int parallelGroupId, int InfraObjectId);

        IList<ParallelWorkflowActionResult> GetDrillCount(int infraId, string actionIds);

        string GetInfraAction(int infraId);

        bool UpdateWorkflowActionResultConditionalByPGrpIdAndInfraIdRetryId(int parallelGrpId, int infraId, int currentActionId);

        bool UpdateWorkflowActionResultRelodById(int id);

        ParallelWorkflowActionResult GetActionResultByInfra(int infraId, string actionIds);

        //neelima

        //IList<ParallelWorkflowActionResult> GetParallelDrOperationSummarydataByInfraID(int id);
        IList<ParallelWorkflowActionResult> GetParallelDrOperationdataBYInfraWid(int id, int wid);

        bool UpdateWorkflowActionResultStausById(int id, string Status);
        bool UpdateWorkflowActionResultStatusById(int id, string Status);

        IList<ParallelWorkflowActionResult> GetAllRunning_ParallelWorkflowActionResultByUserId(int UserId);
        IList<ParallelWorkflowActionResult> GetAllRunning_ParallelWorkflowActionResultByUserId_New(int UserId,int Loggedinuser);
    }

    #endregion IParallelWorkflowactionResultDataAccess
}