﻿using System;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.UI.Code.Replication.Component
{
    public class OracleComponent : IComponentInfo
    {
        private readonly IFacade _facade = new Facade();

        private ComponentInfo _componentInfo;

        public ComponentInfo CurrentComponent
        {
            get { return _componentInfo ?? (_componentInfo = new ComponentInfo()); }
        }

        private int _prDatabaseId = 0;

        //public DatabaseBase CurrentDatabase
        //{
        //    get
        //    {
        //        if (_prDatabaseId > 0)
        //        {
        //            DatabaseBase database = _facade.GetDatabaseBaseById(_prDatabaseId);

        //            if(database!=null)
        //            {
        //                var oracle = Facade.GetDatabaseOracleByDatabaseBaseId(CurrentDatabaseBaseId);
        //            }
        //        }
        //    }

        //}

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId)
        {
            _componentInfo = null;

            GetServerInformation(prServerId, true);

            GetServerInformation(drServerId, false);

            GetDatabaseInformation(prDatabaseId, true);

            GetDatabaseInformation(drDatabaseId, false);

            GetDataLagInformation(infraObjectId);

            return CurrentComponent;
        }

        public ComponentInfo GetComponentInformation(int infraObjectId, int prServerId, int drServerId, int prDatabaseId, int drDatabaseId, int mailBoxId, string mailboxname)
        {
            _componentInfo = null;

            //GetServerInformation(prServerId, true);

            //GetServerInformation(drServerId, false);

            //GetDatabaseInformation(prDatabaseId, true);

            //GetDatabaseInformation(drDatabaseId, false);

            //GetDataLagInformation(groupId);

            return CurrentComponent;
        }

        private void GetDataLagInformation(int infraObjectId)
        {
            try
            {
                var Infrainfo = _facade.GetInfraObjectById(infraObjectId);
                var databaseinfo = _facade.GetDatabaseBaseById(Infrainfo.PRDatabaseId);

                if (databaseinfo != null && !string.IsNullOrEmpty(databaseinfo.Version) && databaseinfo.Version.ToLower() == "11g" || databaseinfo.Version.ToLower() == "12c")
                {
                    if (databaseinfo.DatabaseType.ToString().Trim().ToLower() == "oracle" && Infrainfo.RecoveryType == (int)ReplicationType.OracleWithDataSync)
                    {
                        var RepliDetails = _facade.GetADOGReplicationNONODGDetailsByInfraObjectId(infraObjectId, DateTime.Now); //odg
                        if (RepliDetails != null)
                        {

                            //if (!string.IsNullOrEmpty(RepliDetails.PRLogSequence) && RepliDetails.Log_sequence.Contains("@"))
                            if (!string.IsNullOrEmpty(RepliDetails.PRLogSequence) && !string.IsNullOrEmpty(RepliDetails.DRLogSequence))
                            {
                                //string[] LogSeq = RepliDetails[i].Log_sequence.Split('@');
                                //CurrentComponent.PRLogSequence = LogSeq[0] + "(#" + LogSeq[1] + ")";

                                //string[] LogSeq = RepliDetails.PRLogSequence.Split('@');
                                //string[] LogThread;
                                //string[] LogSeq1;
                                //string strLength = string.Empty;
                                //LogThread = LogSeq[0].Split(',');
                                //LogSeq1 = LogSeq[1].Split(',');

                                string LogSeq = RepliDetails.PRLogSequence;
                                //string LogThread;
                                //string LogSeq1;
                                //string strLength = string.Empty;
                                //LogThread = LogSeq;
                                //LogSeq1 = LogSeq;

                                //for (int k = 0; k < LogThread.Length; k++)
                                //{
                                //    strLength = strLength + LogThread[k].ToString() + "(Thread #" + LogSeq1[k] + ") ";
                                //}

                                CurrentComponent.PRLogSequence = RepliDetails.PRLogSequence;

                                CurrentComponent.DRLogSequence = RepliDetails.DRLogSequence;



                            }
                            //if (RepliDetails[i].PRDRTYPE == "DR" && !string.IsNullOrEmpty(RepliDetails[i].Log_sequence) && RepliDetails[i].Log_sequence.Contains("@"))
                            //{
                            //    string[] LogSeq = RepliDetails[i].Log_sequence.Split('@');
                            //    string[] LogThread;
                            //    string[] LogSeq1;
                            //    string strLength = string.Empty;
                            //    LogThread = LogSeq[0].Split(',');
                            //    LogSeq1 = LogSeq[1].Split(',');

                            //    for (int k = 0; k < LogThread.Length; k++)
                            //    {
                            //        strLength = strLength + LogThread[k].ToString() + "(Thread #" + LogSeq1[k] + ") ";
                            //    }


                            //    //CurrentComponent.DRLogSequence = LogSeq[0] + "(#" + LogSeq[1] + ")";

                            //    CurrentComponent.DRLogSequence = strLength;
                            //}

                        }
                        else
                        {
                            CurrentComponent.PRLogSequence = "Not Available";
                            CurrentComponent.DRLogSequence = "Not Available";

                        }
                    }
                    else if (databaseinfo.DatabaseType.ToString().Trim().ToLower() == "oracle")
                    {

                        var RepliDetails = _facade.GetRepliDetailsByInfraObjectId(infraObjectId);
                        if (RepliDetails != null && RepliDetails.Count > 0)
                        {
                            for (int i = 0; i < RepliDetails.Count; i++)
                            {
                                if (RepliDetails[i].PRDRTYPE == "PR" && !string.IsNullOrEmpty(RepliDetails[i].Log_sequence) && RepliDetails[i].Log_sequence.Contains("@"))
                                {
                                    //string[] LogSeq = RepliDetails[i].Log_sequence.Split('@');
                                    //CurrentComponent.PRLogSequence = LogSeq[0] + "(#" + LogSeq[1] + ")";

                                    string[] LogSeq = RepliDetails[i].Log_sequence.Split('@');
                                    string[] LogThread;
                                    string[] LogSeq1;
                                    string strLength = string.Empty;
                                    LogThread = LogSeq[0].Split(',');
                                    LogSeq1 = LogSeq[1].Split(',');

                                    for (int k = 0; k < LogThread.Length; k++)
                                    {
                                        strLength = strLength + LogThread[k].ToString() + "(Thread #" + LogSeq1[k] + ") ";
                                    }

                                    CurrentComponent.PRLogSequence = strLength;

                                }
                                if (RepliDetails[i].PRDRTYPE == "DR" && !string.IsNullOrEmpty(RepliDetails[i].Log_sequence) && RepliDetails[i].Log_sequence.Contains("@"))
                                {
                                    string[] LogSeq = RepliDetails[i].Log_sequence.Split('@');
                                    string[] LogThread;
                                    string[] LogSeq1;
                                    string strLength = string.Empty;
                                    LogThread = LogSeq[0].Split(',');
                                    LogSeq1 = LogSeq[1].Split(',');

                                    for (int k = 0; k < LogThread.Length; k++)
                                    {
                                        strLength = strLength + LogThread[k].ToString() + "(Thread #" + LogSeq1[k] + ") ";
                                    }


                                    //CurrentComponent.DRLogSequence = LogSeq[0] + "(#" + LogSeq[1] + ")";

                                    CurrentComponent.DRLogSequence = strLength;
                                }
                            }
                        }
                        else
                        {
                            CurrentComponent.PRLogSequence = "Not Available";
                            CurrentComponent.DRLogSequence = "Not Available";
                        }
                    }
                }
                else
                {
                    var datalag = _facade.GetOracleLogByInfraObjectId(infraObjectId);

                    if (datalag != null)
                    {
                        CurrentComponent.PRLogSequence = datalag.PRSequenceNo;
                        CurrentComponent.DRLogSequence = datalag.DRSequenceNo;
                    }
                    else
                    {
                        CurrentComponent.PRLogSequence = "Not Available";
                        CurrentComponent.DRLogSequence = "Not Available";
                    }
                }
            }
            catch (Exception)
            {
                CurrentComponent.PRLogSequence = "Not Available";
                CurrentComponent.DRLogSequence = "Not Available";
            }
        }

        private void GetDatabaseInformation(int databaseId, bool isPrimary)
        {
            try
            {
                var database = _facade.GetDatabaseOracleByDatabaseBaseId(databaseId);

                if (database != null)
                {
                    BindDatabaseComponents(database, isPrimary);
                }
                else
                {
                    BindNullDatabaseComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullDatabaseComponents(isPrimary);
            }
        }

        private void BindDatabaseComponents(DatabaseOracle database, bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = database.OracleSID;
                CurrentComponent.PRDatabaseMode = "Read write";
            }
            else
            {
                CurrentComponent.DRDatabaseName = database.OracleSID;
                CurrentComponent.DRDatabaseMode = "StandBy";
            }
        }

        private void BindNullDatabaseComponents(bool isPrimary)
        {
            if (isPrimary)
            {
                CurrentComponent.PRDatabaseName = "N/A";
                CurrentComponent.PRDatabaseMode = "Down";
            }
            else
            {
                CurrentComponent.DRDatabaseName = "N/A";
                CurrentComponent.DRDatabaseMode = "Down";
            }
        }

        //private void BindDatabaseComponents(DatabaseBase database, bool isPrimary)
        //{
        //    if (isPrimary)
        //    {
        //        CurrentComponent.PRDatabaseName = database.DatabaseOracle.OracleSID;
        //        CurrentComponent.PRDatabaseMode = database.Mode.ToString();
        //    }
        //    else
        //    {
        //        CurrentComponent.DRDatabaseName = database.Name;
        //        CurrentComponent.DRDatabaseMode = database.Mode.ToString();
        //    }
        //}

        private void BindNullServerComponents(bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = "N/A";
                CurrentComponent.PRServerIP = "N/A";
                CurrentComponent.PRServerOSType = "N/A";
                CurrentComponent.PRServerStatus = "Down";
            }
            else
            {
                CurrentComponent.DRServerName = "N/A";
                CurrentComponent.DRServerIP = "N/A";
                CurrentComponent.DRServerOSType = "N/A";
                CurrentComponent.DRServerStatus = "Down";
            }
        }

        private void BindServerComponents(Server server, bool primary)
        {
            if (primary)
            {
                CurrentComponent.PRServerName = server.Name;
                CurrentComponent.PRServerIP = server.IPAddress;
                CurrentComponent.PRServerOSType = server.OSType;
                CurrentComponent.PRServerStatus = server.Status.ToString();
            }
            else
            {
                CurrentComponent.DRServerName = server.Name;
                CurrentComponent.DRServerIP = server.IPAddress;
                CurrentComponent.DRServerOSType = server.OSType;
                CurrentComponent.DRServerStatus = server.Status.ToString();
            }
        }

        private void GetServerInformation(int serverId, bool isPrimary)
        {
            try
            {
                var server = _facade.GetServerById(serverId);

                if (server != null)
                {
                    BindServerComponents(server, isPrimary);
                }
                else
                {
                    BindNullServerComponents(isPrimary);
                }
            }
            catch (Exception)
            {
                BindNullServerComponents(isPrimary);
            }
        }
    }
}