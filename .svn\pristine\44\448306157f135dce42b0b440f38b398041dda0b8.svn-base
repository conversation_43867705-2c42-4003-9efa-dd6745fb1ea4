﻿

using System;
using System.Collections;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using Gios.Pdf;
using log4net;
using SpreadsheetGear;

using CP.Helper;

namespace CP.UI.Controls
{
    public partial class RsyncReport : BaseControl
    {

        private readonly ILog _logger = LogManager.GetLogger(typeof(RsyncReport));
        private int Checks = 1;
        private string[] xlColumn = { "A", "B", "C", "D", "E", "F", "G", "H" };
        public override void PrepareView()
        {
            DeleteRecords();
        }

        private void DeleteRecords()
        {
            if (!Directory.Exists(Server.MapPath(@"~/PdfFiles")))
            {
                Directory.CreateDirectory(Server.MapPath(@"~/PdfFiles"));
            }
            var directory = new DirectoryInfo(Server.MapPath(@"~/PdfFiles"));

            foreach (FileInfo file in directory.GetFiles())
            {
                file.Delete();
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void btnrsyncrpt_Click(object sender, EventArgs e)
        {
            try
            {
               
                PrepareReportForRsync();

            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                _logger.Error("exception occured to Generate Rsync DataLag Report Message - " + ex.Message);

                if (ex.InnerException != null)
                {
                    _logger.Error("exception occured to Generate Rsync DataLag Report InnerException message -" + ex.InnerException.Message);
                }
            }
        }


        protected void txtstart_TextChanged(object sender, EventArgs e)
        {
            try
            {
                lblMsg.Text = string.Empty;
             
                if (!string.IsNullOrEmpty(txtstart.Text))
                {
                    var enddt = Convert.ToDateTime(txtstart.Text);
                    CalendarExtender2.StartDate = enddt;
                }
                txtend.Text = string.Empty;
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured on Start Date Text Changed for DataSync Monitor Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void txtend_TextChanged(object sender, EventArgs e)
        {
            lblMsg.Text = string.Empty;
            
        }

        #region old

        //private void PrepareReportForRsync()
        //{
        //    try
        //    {

        //        string strdate = Utility.getFormatedDate_New(txtstart.Text);
        //        string enddate = Utility.getFormatedDate_New(txtend.Text);
              
        //        IList<RSyncMonitor> RSyncList = Facade.GetALLRsyncRecords(strdate, enddate);

        //        _logger.Info("RSyncMonitorRPT Method Execution Start.");

        //       // divNetapp.Visible = false;
        //        IWorkbookSet workbookSet = null;
        //        String ssFile = string.Empty;
        //        IWorkbook templateWorkbook = null;
        //        IWorksheet templateWorksheet = null;
        //        IRange _cells = null;

        //        workbookSet = Factory.GetWorkbookSet();
        //        ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
        //        templateWorkbook = workbookSet.Workbooks.Open(ssFile);
        //        templateWorksheet = templateWorkbook.Worksheets[0];

        //        IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
        //        IWorksheet reportWorksheet = null;
        //        IWorksheet lastWorksheet = null;

        //        lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
        //        reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);


        //        lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
        //        reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

        //        reportWorkbook.Worksheets["Sheet1"].Delete();
        //        reportWorkbook.Worksheets["Sheet1 (2)"].Delete();

        //        _cells = reportWorksheet.Cells;
        //        reportWorksheet.WindowInfo.DisplayGridlines = false;
        //       // reportWorksheet.Name = ddlGroup.SelectedItem.Text.Length > 30 ? ddlGroup.SelectedItem.Text.Substring(0, 29) : ddlGroup.SelectedItem.Text;

        //        _cells["A1"].ColumnWidth = 10;

        //        _cells["F3"].Formula = "Rsync Monitor Report";
        //        _cells["B3:O6"].Interior.Color = Color.FromArgb(79, 129, 189);
        //        _cells["F3"].Font.Bold = true;
        //        _cells["F3"].ColumnWidth = 30;
        //        _cells["F3"].HorizontalAlignment = HAlign.Center;
        //        _cells["F3"].VerticalAlignment = VAlign.Top;
        //        _cells["B3:O3"].Font.Size = 11;
        //        _cells["B5:O8"].Font.Size = 10;
        //        _cells["B3:O8"].Font.Color = Color.White;
        //        _cells["B3:O8"].Font.Name = "Cambria";

        //         reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 63, 10, 120, 12);
        //            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 900, 10, 120, 13);
        //            //reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/CompanyLogo/licPdf.jpg"), 63, 10, 120, 30);
        //            //reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/CompanyLogo/HPPdf.jpg"), 600, 10, 120, 30);

        //            string strlogo = LoggedInUserCompany.CompanyLogoPath;
        //            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
        //            {
        //                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 450, 10, 121, 13);
        //            }
        //            //reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/CompanyLogo/EDMS.jpg"), 450, 10, 121, 13);
        //            //reportWorksheet.Cells["A1:K1"].RowHeight = 27;

        //            //var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
        //            //_cells["B5"].Formula = "Duration";
        //            //_cells["B5"].Font.Bold = true;
        //            //_cells["B5"].HorizontalAlignment = HAlign.Left;

        //            //_cells["C5"].Formula = ": 30 Days";
        //            //_cells["C5"].Font.Bold = true;
        //            //_cells["C5"].HorizontalAlignment = HAlign.Left;
        //            _cells["B6:D6"].Merge();
        //            var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
        //            _cells["B6:D6"].Formula = "Report Generated Time" + ":  " + dateTime;
        //            _cells["B6:D6"].Font.Bold = true;
        //            _cells["B6:D6"].HorizontalAlignment = HAlign.Left;

        //            //_cells["C6"].Formula = ":  " + dateTime;
        //            //_cells["C6"].Font.Bold = true;
        //            //_cells["C6"].HorizontalAlignment = HAlign.Left;


        //            _cells["N5"].Formula = "From Date";
        //            _cells["N5"].Font.Bold = true;
        //            _cells["N5"].HorizontalAlignment = HAlign.Left;
        //            _cells["N5"].Font.Name = "Cambria";

        //            string strt = Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); //Convert.ToDateTime(txtstart.Text).ToString("dd-MMM-yyyy");
        //            _cells["O5"].Formula = ": " + strt;
        //            _cells["O5"].Font.Bold = true;
        //            _cells["O5"].HorizontalAlignment = HAlign.Left;

        //            _cells["N6"].Formula = "To Date";
        //            _cells["N6"].Font.Bold = true;
        //            _cells["N6"].HorizontalAlignment = HAlign.Left;
        //            _cells["N6"].Font.Name = "Cambria";

        //            string endt = Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");//Convert.ToDateTime(txtend.Text).ToString("dd-MMM-yyyy");
                    
        //            _cells["O6"].Formula = ": " + endt;
        //            _cells["O6"].Font.Bold = true;
        //            _cells["O6"].ColumnWidth = 20;
        //            _cells["O6"].HorizontalAlignment = HAlign.Left;


        //            int row = 8;
        //            int i = 1;

        //            _cells["B" + row.ToString()].Formula = "Sr.No.";
        //            _cells["B" + row.ToString()].ColumnWidth = 25;
        //            _cells["B" + row.ToString()].Font.Bold = true;
        //            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            _cells["B8:O8"].Interior.Color = Color.FromArgb(79, 129, 189);

        //            IRange range = reportWorksheet.Cells["B8:O8"];
        //            IBorder border = range.Borders[BordersIndex.EdgeBottom];
        //            border.LineStyle = LineStyle.Continous;
        //            border.Color = Color.Black;
        //            border.Weight = BorderWeight.Thin;

        //            _cells["C" + row.ToString()].Formula = "Business Service Name";
        //            _cells["C" + row.ToString()].ColumnWidth = 25;
        //            _cells["C" + row.ToString()].Font.Bold = true;
        //            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
        //            productionlog.WrapText = true;

        //            _cells["D" + row.ToString()].Formula = "Business Function Name";
        //            _cells["D" + row.ToString()].ColumnWidth = 25;
        //            _cells["D" + row.ToString()].Font.Bold = true;
        //            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            _cells["E" + row.ToString()].Formula = "InfraObject Name";
        //            _cells["E" + row.ToString()].ColumnWidth = 25;
        //            _cells["E" + row.ToString()].Font.Bold = true;
        //            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            _cells["F" + row.ToString()].Formula = "Source IP Address/Hostname";
        //            _cells["F" + row.ToString()].ColumnWidth = 25;
        //            _cells["F" + row.ToString()].Font.Bold = true;
        //            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;



        //            _cells["G" + row.ToString()].Formula = "Destination IP Address/Hostname";
        //            _cells["G" + row.ToString()].ColumnWidth = 25;
        //            _cells["G" + row.ToString()].Font.Bold = true;
        //            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            _cells["H" + row.ToString()].Formula = "Source Path";
        //            _cells["H" + row.ToString()].ColumnWidth = 20;
        //            _cells["H" + row.ToString()].Font.Bold = true;
        //            _cells["H" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            _cells["I" + row.ToString()].Formula = "Destination Path";
        //            _cells["I" + row.ToString()].ColumnWidth = 20;
        //            _cells["I" + row.ToString()].Font.Bold = true;
        //            _cells["I" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            //
        //            _cells["J" + row.ToString()].Formula = "Total Files Size(KB/bytes)";
        //            _cells["J" + row.ToString()].ColumnWidth = 20;
        //            _cells["J" + row.ToString()].Font.Bold = true;
        //            _cells["J" + row.ToString()].HorizontalAlignment = HAlign.Left;


        //            _cells["K" + row.ToString()].Formula = "Replicated Files Count"; // "Incremental Files Count";
        //            _cells["K" + row.ToString()].ColumnWidth = 20;
        //            _cells["K" + row.ToString()].Font.Bold = true;
        //            _cells["K" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            _cells["L" + row.ToString()].Formula = "Start Time";  //"Replicated Files Count";
        //            _cells["L" + row.ToString()].ColumnWidth = 20;
        //            _cells["L" + row.ToString()].Font.Bold = true;
        //            _cells["L" + row.ToString()].HorizontalAlignment = HAlign.Left;


        //            _cells["M" + row.ToString()].Formula = "End Time";  //"Start Time";
        //            _cells["M" + row.ToString()].ColumnWidth = 20;
        //            _cells["M" + row.ToString()].Font.Bold = true;
        //            _cells["M" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            _cells["N" + row.ToString()].Formula = "Replication Time Interval(M/H/D)";  //"Start Time";
        //            _cells["N" + row.ToString()].ColumnWidth = 20;
        //            _cells["N" + row.ToString()].Font.Bold = true;
        //            _cells["N" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            _cells["O" + row.ToString()].Formula = "DataLag";  //"End Time";
        //            _cells["O" + row.ToString()].ColumnWidth = 20;
        //            _cells["O" + row.ToString()].Font.Bold = true;
        //            _cells["O" + row.ToString()].HorizontalAlignment = HAlign.Left;

                 
        //            row++;
        //            int dataCount = 0;
        //            int xlRow = 9;



               
        //        if (RSyncList != null && RSyncList.Count() > 0)
        //        {
        //            foreach(var rslist in RSyncList )
        //            {
        //            InfraObject InfraObj = Facade.GetInfraObjectById(rslist.InfraobjectId);//(Convert.ToInt32(ddlGroup.SelectedItem.Value));
        //            BusinessFunction businessFtn = Facade.GetBusinessFunctionById(InfraObj != null ? InfraObj.BusinessFunctionId : 0);
        //            // TimeSpan conDatalag = TimeSpan.Parse(Convert.ToString(businessFtn.ConfiguredRPO));
        //            TimeSpan conDatalag = TimeSpan.FromSeconds(Convert.ToDouble(businessFtn.ConfiguredRPO));
        //            BusinessService bs = Facade.GetBusinessServiceById(InfraObj.BusinessServiceId);      

        //            //_cells["F4"].Formula = "Infra Object Name : " + InfraObj.Name.ToUpper();
        //            //_cells["F4"].Font.Size = 10;
        //            //_cells["F4"].Font.Bold = true;
        //            //_cells["F4"].ColumnWidth = 30;
        //            //_cells["F4"].HorizontalAlignment = HAlign.Center;
        //            //_cells["F4"].VerticalAlignment = VAlign.Top;

        //            #region commnet
        //            //reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 63, 10, 120, 12);
        //            //reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 600, 10, 120, 13);
        //            ////reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/CompanyLogo/licPdf.jpg"), 63, 10, 120, 30);
        //            ////reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/CompanyLogo/HPPdf.jpg"), 600, 10, 120, 30);

        //            //string strlogo = LoggedInUserCompany.CompanyLogoPath;
        //            //if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
        //            //{
        //            //    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 450, 10, 121, 13);
        //            //}
        //            ////reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/CompanyLogo/EDMS.jpg"), 450, 10, 121, 13);
        //            ////reportWorksheet.Cells["A1:K1"].RowHeight = 27;

        //            ////_cells["B6"].Formula = "From Date";
        //            ////_cells["B6"].Font.Bold = true;
        //            ////_cells["B6"].HorizontalAlignment = HAlign.Left;

        //            ////_cells["C6"].Formula = ":  " + txtstart.Text;
        //            ////_cells["C6"].Font.Bold = true;
        //            ////_cells["C6"].HorizontalAlignment = HAlign.Left;

        //            //var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
        //            //_cells["B5"].Formula = "Report Generated Time";
        //            //_cells["B5"].Font.Bold = true;
        //            //_cells["B5"].HorizontalAlignment = HAlign.Left;

        //            //_cells["C5"].Formula = ":  " + dateTime;
        //            //_cells["C5"].Font.Bold = true;
        //            //_cells["C5"].HorizontalAlignment = HAlign.Left;

        //            ////_cells["E6"].Formula = "To Date";
        //            ////_cells["E6"].Font.Bold = true;
        //            ////_cells["E6"].HorizontalAlignment = HAlign.Left;

        //            ////_cells["F6"].Formula = ":  " + txtend.Text;
        //            ////_cells["F6"].Font.Bold = true;
        //            ////_cells["F6"].HorizontalAlignment = HAlign.Left;

        //            //int row = 8;
        //            //int i = 1;

        //            //_cells["B" + row.ToString()].Formula = "Sr.No.";
        //            //_cells["B" + row.ToString()].ColumnWidth = 25;
        //            //_cells["B" + row.ToString()].Font.Bold = true;
        //            //_cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            //_cells["B8:K8"].Interior.Color = Color.FromArgb(79, 129, 189);

        //            //IRange range = reportWorksheet.Cells["B8:K8"];
        //            //IBorder border = range.Borders[BordersIndex.EdgeBottom];
        //            //border.LineStyle = LineStyle.Continous;
        //            //border.Color = Color.Black;
        //            //border.Weight = BorderWeight.Thin;

        //            //_cells["C" + row.ToString()].Formula = "Source IP";
        //            //_cells["C" + row.ToString()].ColumnWidth = 25;
        //            //_cells["C" + row.ToString()].Font.Bold = true;
        //            //_cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            //var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
        //            //productionlog.WrapText = true;

        //            //_cells["D" + row.ToString()].Formula = "Destination IP";
        //            //_cells["D" + row.ToString()].ColumnWidth = 25;
        //            //_cells["D" + row.ToString()].Font.Bold = true;
        //            //_cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            //_cells["E" + row.ToString()].Formula = "Source Path";
        //            //_cells["E" + row.ToString()].ColumnWidth = 20;
        //            //_cells["E" + row.ToString()].Font.Bold = true;
        //            //_cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            //_cells["F" + row.ToString()].Formula = "Destination Path";
        //            //_cells["F" + row.ToString()].ColumnWidth = 20;
        //            //_cells["F" + row.ToString()].Font.Bold = true;
        //            //_cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            ////
        //            //_cells["G" + row.ToString()].Formula = "Total Files Size(KB/bytes)";
        //            //_cells["G" + row.ToString()].ColumnWidth = 20;
        //            //_cells["G" + row.ToString()].Font.Bold = true;
        //            //_cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;


        //            //_cells["H" + row.ToString()].Formula = "Incremental Files Count";
        //            //_cells["H" + row.ToString()].ColumnWidth = 20;
        //            //_cells["H" + row.ToString()].Font.Bold = true;
        //            //_cells["h" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            //_cells["I" + row.ToString()].Formula = "Replicated Files Count";
        //            //_cells["I" + row.ToString()].ColumnWidth = 20;
        //            //_cells["I" + row.ToString()].Font.Bold = true;
        //            //_cells["I" + row.ToString()].HorizontalAlignment = HAlign.Left;


        //            //_cells["J" + row.ToString()].Formula = "Start Time";
        //            //_cells["J" + row.ToString()].ColumnWidth = 20;
        //            //_cells["J" + row.ToString()].Font.Bold = true;
        //            //_cells["J" + row.ToString()].HorizontalAlignment = HAlign.Left;

        //            //_cells["K" + row.ToString()].Formula = "End Time";
        //            //_cells["K" + row.ToString()].ColumnWidth = 20;
        //            //_cells["K" + row.ToString()].Font.Bold = true;
        //            //_cells["K" + row.ToString()].HorizontalAlignment = HAlign.Left;
        //            //row++;
        //            //int dataCount = 0;
        //            //int xlRow = 9;


        //            #endregion 

        //               dataCount++;
        //                int column = 0;
        //                // string[] xlColumn = { "B", "C", "D", "E", "F" };
        //                string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J", "K" ,"L","M","N","O"};
        //                xlRow++;

        //                string ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx + ":" + "O" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;
        //                _cells[ndx].Formula = i.ToString();
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 13;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                i++;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(bs.Name) ? bs.Name : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(businessFtn.Name) ? businessFtn.Name : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(InfraObj.Name) ? InfraObj.Name : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;

                        
        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.SourceIP) ? rslist.SourceIP : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.DestinationIP) ? rslist.DestinationIP : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.SourcePath) ? rslist.SourcePath : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.DestinationPath) ? rslist.DestinationPath : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.TotalFilesSize) ? rslist.TotalFilesSize : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Center;
        //                column++;


        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.TotalNumberoffiles) ? rslist.TotalNumberoffiles : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Center;
        //                column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.RepStartTime) ? rslist.RepStartTime : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                column++;


        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.RepEndTime) ? rslist.RepEndTime : "NA";
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                 column++;

        //                 DateTime strdt = Convert.ToDateTime(rslist.RepStartTime);
        //                DateTime enddt = Convert.ToDateTime(rslist.RepEndTime);

        //                string diffTime = ((enddt - strdt).TotalMinutes).ToString();
        //                 ndx = xlColumn[column] + row.ToString();
        //                 _cells[ndx].Formula = !string.IsNullOrEmpty(diffTime.ToString()) ? diffTime.ToString() : "NA"   + "Min";
        //                 _cells[ndx].Font.Size = 10;
        //                 _cells[ndx].ColumnWidth = 25;
        //                 _cells[ndx].Font.Color = Color.Black;
        //                 _cells[ndx].HorizontalAlignment = HAlign.Left;
        //                 column++;

        //                ndx = xlColumn[column] + row.ToString();
        //                _cells[ndx].Font.Size = 10;
        //                _cells[ndx].ColumnWidth = 25;
        //                _cells[ndx].Font.Color = Color.Black;
        //                _cells[ndx].HorizontalAlignment = HAlign.Left;

        //                if (!string.IsNullOrEmpty(rslist.Datalag) && rslist.Datalag != "NA")
        //                {
        //                    string[] split = rslist.Datalag.Split(':');
        //                    if (split[0].Contains('.'))
        //                    {
        //                        string[] splitFinal = split[0].Split('.');
        //                        split[0] = splitFinal[1];
        //                    }

        //                    bool isHealth = Utility.GetReportDatlagHealth(rslist.Datalag, businessFtn.ConfiguredRPO);

        //                    if (isHealth)
        //                    {
        //                        _cells[ndx].Formula = rslist.Datalag;
        //                        _cells[ndx].Font.Color = Color.Green;
        //                    }
        //                    else
        //                    {
        //                        _cells[ndx].Formula = rslist.Datalag;
        //                        _cells[ndx].Font.Color = Color.Red;
        //                        //_cells["B" + row + ":" + ndx].Interior.Color = Color.FromArgb(242, 221, 220);
        //                    }
        //                }
        //                else
        //                {
        //                    _cells[ndx].Formula = "NA";
        //                    //_cells[ndx].Font.Color = Color.Red;
        //                }

        //                column++;

        //                row++;
        //            }
        //        }

        //        int finalCount = dataCount + 10;
        //        _cells["B" + finalCount].Formula = "NA : Not Available";
        //        _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
        //        _cells["B" + finalCount].Font.Name = "Cambria";
        //        _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

        //        //_cells["C" + finalCount].Formula = "Datalag <= " + conDatalag + "Hours";
        //        //_cells["C" + finalCount].Font.Color = Color.Green;
        //        //_cells["C" + finalCount].HorizontalAlignment = HAlign.Left;
        //        //_cells["C" + finalCount].Font.Name = "Cambria";

        //        //_cells["D" + finalCount].Formula = "Datalag > " + conDatalag + "Hours";
        //        //_cells["D" + finalCount].Font.Color = Color.Red;
        //        //_cells["D" + finalCount].HorizontalAlignment = HAlign.Left;
        //        //_cells["D" + finalCount].Font.Name = "Cambria";

        //        reportWorksheet.ProtectContents = true;

        //        _logger.Info("RSyncMonitorRPT Method Execution Completed.");

        //        Response.Clear();
        //        Response.ContentType = "application/vnd.ms-excel";
        //        Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");

        //        var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
        //        str = "RSyncMonitorReport " + "_" + str + ".xls";
        //        reportWorkbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

        //        string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
        //        string myUrl = reportPath + "/ExcelFiles/" + str;
        //        //var myUrl = "/ExcelFiles/" + str;
        //        var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar= RPO SLA Report');";
        //        ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.Error("Exception Occurred In RSyncMonitorRPT Method, Error Message " + ex.Message);
        //        if (ex.InnerException != null)
        //            _logger.Error("Exception Occurred In RSyncMonitorRPT Method, InnerException Message " + ex.InnerException.Message);
        //    }
        //}
        
        #endregion old


        private void PrepareReportForRsync()
        {
            try
            {

                string strdate = Utility.getFormatedDate_New(txtstart.Text);
                string enddate = Utility.getFormatedDate_New(txtend.Text);
                IList<RSyncMonitor> RSyncList = Facade.GetALLRsyncRecords(strdate, enddate); ;

                _logger.Info("RSyncMonitorRPT Method Execution Start.");
                                
                IWorkbookSet workbookSet = null;
                String ssFile = string.Empty;
                IWorkbook templateWorkbook = null;
                IWorksheet templateWorksheet = null;
                IRange _cells = null;

                workbookSet = Factory.GetWorkbookSet();
                ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
                templateWorkbook = workbookSet.Workbooks.Open(ssFile);
                templateWorksheet = templateWorkbook.Worksheets[0];

                IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
                IWorksheet reportWorksheet = null;
                IWorksheet lastWorksheet = null;

                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);


                lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
                reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

                reportWorkbook.Worksheets["Sheet1"].Delete();
                reportWorkbook.Worksheets["Sheet1 (2)"].Delete();

                _cells = reportWorksheet.Cells;
                reportWorksheet.WindowInfo.DisplayGridlines = false;
               
                _cells["A1"].ColumnWidth = 10;

                _cells["I3"].Formula = "Rsync Monitor Report";
                _cells["B3:O6"].Interior.Color = Color.FromArgb(79, 129, 189);
                _cells["I3"].Font.Bold = true;
                _cells["I3"].ColumnWidth = 30;
                _cells["I3"].HorizontalAlignment = HAlign.Center;
                _cells["I3"].VerticalAlignment = VAlign.Top;
                _cells["B3:O3"].Font.Size = 11;
                _cells["B5:O8"].Font.Size = 10;
                _cells["B3:O8"].Font.Color = Color.White;
                _cells["B3:O8"].Font.Name = "Cambria";

                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 63, 10, 120, 12);
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 1110, 10, 120, 13);

                string strlogo = LoggedInUserCompany.CompanyLogoPath;
                if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
                {
                    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 450, 10, 121, 13);
                }

                var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
                _cells["B6"].Formula = "Report Generated Time";
                _cells["B6"].Font.Bold = true;
                _cells["B6"].HorizontalAlignment = HAlign.Left;

                _cells["C6"].Formula = ":  " + dateTime;
                _cells["C6"].Font.Bold = true;
                _cells["C6"].HorizontalAlignment = HAlign.Left;


                _cells["N5"].Formula = "From Date";
                _cells["N5"].Font.Bold = true;
                _cells["N5"].HorizontalAlignment = HAlign.Right;
                _cells["N5"].Font.Name = "Cambria";

                string strt = Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"); 
                _cells["O5"].Formula = ":  " + strt;
                _cells["O5"].Font.Bold = true;
                _cells["O5"].HorizontalAlignment = HAlign.Left;

                _cells["N6"].Formula = "To Date";
                _cells["N6"].Font.Bold = true;
                _cells["N6"].HorizontalAlignment = HAlign.Right;
                _cells["N6"].Font.Name = "Cambria";

                string endt = Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy");

                _cells["O6"].Formula = ":  " + endt;
                _cells["O6"].Font.Bold = true;
                _cells["O6"].HorizontalAlignment = HAlign.Left;


                int row = 8;
                int i = 1;

                _cells["B" + row.ToString()].Formula = "Sr.No.";
                _cells["B" + row.ToString()].ColumnWidth = 25;
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["B8:O8"].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range = reportWorksheet.Cells["B8:O8"];
                IBorder border = range.Borders[BordersIndex.EdgeBottom];
                border.LineStyle = LineStyle.Continous;
                border.Color = Color.Black;
                border.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "Business Service";
                _cells["C" + row.ToString()].ColumnWidth = 25;
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

                var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                productionlog.WrapText = true;

                _cells["D" + row.ToString()].Formula = "InfraObject Name ";
                _cells["D" + row.ToString()].ColumnWidth = 25;
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["E" + row.ToString()].Formula = "Source IP";
                _cells["E" + row.ToString()].ColumnWidth = 20;
                _cells["E" + row.ToString()].Font.Bold = true;
                _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["F" + row.ToString()].Formula = "Destination IP";
                _cells["F" + row.ToString()].ColumnWidth = 20;
                _cells["F" + row.ToString()].Font.Bold = true;
                _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;

                
                _cells["G" + row.ToString()].Formula = "Source Path";
                _cells["G" + row.ToString()].ColumnWidth = 20;
                _cells["G" + row.ToString()].Font.Bold = true;
                _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Left;


                _cells["H" + row.ToString()].Formula = "Destination Path"; 
                _cells["H" + row.ToString()].ColumnWidth = 20;
                _cells["H" + row.ToString()].Font.Bold = true;
                _cells["H" + row.ToString()].HorizontalAlignment = HAlign.Left;

                _cells["I" + row.ToString()].Formula = "DR Total Mount Point Size (IN MB / GB )";  
                _cells["I" + row.ToString()].ColumnWidth = 20;
                _cells["I" + row.ToString()].Font.Bold = true;
                _cells["I" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["I" + row.ToString()].Style.WrapText = true;

                _cells["J" + row.ToString()].Formula = "Incremental Files Count on PROD"; 
                _cells["J" + row.ToString()].ColumnWidth = 20;
                _cells["J" + row.ToString()].Font.Bold = true;
                _cells["J" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["J" + row.ToString()].Style.WrapText = true;

                _cells["K" + row.ToString()].Formula = " Replicated Files Count on DR ";  
                _cells["K" + row.ToString()].ColumnWidth = 20;
                _cells["K" + row.ToString()].Font.Bold = true;
                _cells["K" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["K" + row.ToString()].Style.WrapText = true;


                _cells["L" + row.ToString()].Formula = "Replication Time Interval (M/H/D)";
                _cells["L" + row.ToString()].ColumnWidth = 20;
                _cells["L" + row.ToString()].Font.Bold = true;
                _cells["L" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["L" + row.ToString()].Style.WrapText = true;

                _cells["M" + row.ToString()].Formula = "Replication Start Time";
                _cells["M" + row.ToString()].ColumnWidth = 20;
                _cells["M" + row.ToString()].Font.Bold = true;
                _cells["M" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["M" + row.ToString()].Style.WrapText = true;

                _cells["N" + row.ToString()].Formula = "Replication End Time";
                _cells["N" + row.ToString()].ColumnWidth = 20;
                _cells["N" + row.ToString()].Font.Bold = true;
                _cells["N" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["N" + row.ToString()].Style.WrapText = true;

                _cells["O" + row.ToString()].Formula = "DataLag";
                _cells["O" + row.ToString()].ColumnWidth = 20;
                _cells["O" + row.ToString()].Font.Bold = true;
                _cells["O" + row.ToString()].HorizontalAlignment = HAlign.Left;

                row++;
                int dataCount = 0;
                int xlRow = 14;


                if (RSyncList != null && RSyncList.Count() > 0)
                {
                    foreach (var rslist in RSyncList)
                    {
                        InfraObject InfraObj = Facade.GetInfraObjectById(rslist.InfraobjectId);
                        BusinessFunction businessFtn = Facade.GetBusinessFunctionById(InfraObj != null ? InfraObj.BusinessFunctionId : 0);                       
                        TimeSpan conDatalag = TimeSpan.FromSeconds(Convert.ToDouble(businessFtn.ConfiguredRPO));

                        dataCount++;
                        int column = 0;                       
                        string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O" };
                        xlRow++;

                        string ndx = xlColumn[column] + row.ToString();
                        _cells[ndx + ":" + "O" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;
                        _cells[ndx].Formula = i.ToString();
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        i++;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        var busservice = Facade.GetBusinessServiceById(Convert.ToInt32(businessFtn.BusinessServiceId));
                        _cells[ndx].Formula = !string.IsNullOrEmpty(busservice.Name) ? busservice.Name : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(InfraObj.Name) ? InfraObj.Name : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.SourceIP) ? rslist.SourceIP : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.DestinationIP) ? rslist.DestinationIP : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.SourcePath) ? rslist.SourcePath : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.DestinationPath) ? rslist.DestinationPath : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        double Total_FileSize = 0.0;
                        if (!string.IsNullOrEmpty(rslist.TotalFilesSize) && !Convert.ToString(rslist.TotalFilesSize).Equals("NA"))
                        {
                            var splitbytes = (rslist.TotalFilesSize).Split()[0];
                            Total_FileSize = Convert.ToDouble(splitbytes) / 1024 / 1024;
                        }
                        _cells[ndx].Formula = Total_FileSize.ToString("0.00") + " " + "MB";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.NumberOfRegFilesTransfer) ? rslist.NumberOfRegFilesTransfer : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.NumberOfRegFilesTransfer) ? rslist.NumberOfRegFilesTransfer : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;


                        ndx = xlColumn[column] + row.ToString();
                        DateTime startTime = Convert.ToDateTime(rslist.RepEndTime);
                        DateTime endtime = Convert.ToDateTime(rslist.RepStartTime);

                        TimeSpan duration = startTime - endtime;
                        _cells[ndx].Formula = (duration.Minutes).ToString() + " " + "min";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;


                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.RepStartTime) ? rslist.RepStartTime : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;


                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Formula = !string.IsNullOrEmpty(rslist.RepEndTime) ? rslist.RepEndTime : "NA";
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;
                        column++;

                        ndx = xlColumn[column] + row.ToString();
                        _cells[ndx].Font.Size = 10;
                        _cells[ndx].ColumnWidth = 25;
                        _cells[ndx].Font.Color = Color.Black;
                        _cells[ndx].HorizontalAlignment = HAlign.Left;

                        if (!string.IsNullOrEmpty(rslist.Datalag) && rslist.Datalag != "NA")
                        {
                            string[] split = rslist.Datalag.Split(':');
                            if (split[0].Contains('.'))
                            {
                                string[] splitFinal = split[0].Split('.');
                                split[0] = splitFinal[1];
                            }

                            bool isHealth = Utility.GetReportDatlagHealth(rslist.Datalag, businessFtn.ConfiguredRPO);

                            if (isHealth)
                            {
                                _cells[ndx].Formula = rslist.Datalag;
                                _cells[ndx].Font.Color = Color.Green;
                            }
                            else
                            {
                                _cells[ndx].Formula = rslist.Datalag;
                                _cells[ndx].Font.Color = Color.Red;
                            }
                        }
                        else
                        {
                            _cells[ndx].Formula = "NA";
                        }

                        column++;

                        row++;
                    }
                }

                int finalCount = dataCount + 10;
                _cells["B" + finalCount].Formula = "NA : Not Available";
                _cells["B" + finalCount].HorizontalAlignment = HAlign.Left;
                _cells["B" + finalCount].Font.Name = "Cambria";
                _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

                reportWorksheet.ProtectContents = true;

                _logger.Info("RSyncMonitorRPT Method Execution Completed.");

                Response.Clear();
                Response.ContentType = "application/vnd.ms-excel";
                Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");

                var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
                str = "RSyncMonitorReport " + "_" + str + ".xls";
                reportWorkbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

                string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
                string myUrl = reportPath + "/ExcelFiles/" + str;
                var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar= RPO SLA Report');";
                ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
            }
            catch (Exception ex)
            {
                _logger.Error("Exception Occurred In RSyncMonitorRPT Method, Error Message " + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Exception Occurred In RSyncMonitorRPT Method, InnerException Message " + ex.InnerException.Message);
            }
        }

    }
}