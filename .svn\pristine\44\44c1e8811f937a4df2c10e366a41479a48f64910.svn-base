﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using log4net;
using SpreadsheetGear;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Helper;

namespace CP.UI
{
    public partial class BSHealthSummary : InfraObjectsBasePage  //System.Web.UI.Page
    {
        private IFacade facade = new Facade();
        private readonly ILog _logger = LogManager.GetLogger(typeof(BSHealthSummary));
        private static readonly ILog logger = LogManager.GetLogger(typeof(BSHealthSummary));
        IList<BusinessService> allBS = null;

        IList<actionenviornment> allactionenvirment = null;
        public static IList<BSHealthSummaryDetails> lstServiceHealth = new List<BSHealthSummaryDetails>();
        public static IList<BSHealthSummaryDetails> lstServiceHealth_graph = new List<BSHealthSummaryDetails>();
        public static IList<BSHealthSummaryDetails> lastestServiceHealth = new List<BSHealthSummaryDetails>();
        public static IList<BSHealthSummaryDetails> lstServiceHealthrpt = new List<BSHealthSummaryDetails>();
        static int total_NA = 0;
        static int totalNA = 0;

        //  IList<business> businesshelth = new List<business>();
        private static int affectedInfraCount = 0;

        protected void Page_Load(object sender, EventArgs e)
        {
        }
        public override void PrepareView()
        {
            try
            {
                if (!IsPostBack)
                {
                    allBS = facade.GetAllBusinessServices();


                    allactionenvirment = Facade.getallactionenviornment();
                    if (LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                        allBS = Facade.GetAllBusinessServices();
                    else
                    {
                        allBS = facade.GetBusinessServiceByCompanyIdAndRole(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
                    }
                    allBS = allBS.Where(X => X.CompanyId == LoggedInUserCompanyId).ToList();
                    if (allBS != null)
                    {

                        _logger.Info("Binding Service Health Summary Count");
                        GetServiceHealthSummaryCount(allBS);

                        _logger.Info("Binding Business Service Health Summary ddl.");
                        ddlbsservices.DataSource = allBS;
                        ddlbsservices.DataTextField = "Name";
                        ddlbsservices.DataValueField = "Id";
                        ddlbsservices.DataBind();
                        ddlbsservices.Items.Insert(0, "- Select Business Service -");

                        IList<actionenviornment> allactionenvi = new List<actionenviornment>();
                        allactionenvi = Facade.getallactionenviornment();
                        _logger.Info("Binding Business Ststus Service Health Summary ddl.");
                        actionenviornment obj = new actionenviornment();
                        IList<actionenviornment> lstexecuteData2 = new List<actionenviornment>();
                        IList<actionenviornment> nelactionenvlist = new List<actionenviornment>();
                        foreach (var objectref in allactionenvi)
                        {
                            actionenviornment obj2 = new actionenviornment();
                            obj = objectref;
                            if (obj.EnviornmentType.Contains(' '))
                            {
                                obj.EnviornmentType = obj.EnviornmentType.Split(' ').Last();
                            }
                            nelactionenvlist.Add(obj);
                        }

                        //nelactionenvlist = nelactionenvlist.GroupBy(x => x.EnviornmentType).Select(y => y.First()).ToList();

                        //var bnm = lstexecuteData2;
                        //ddlServiceStatus.DataSource = nelactionenvlist;
                        //ddlServiceStatus.DataTextField = "EnviornmentType";
                        //ddlServiceStatus.DataValueField = "Id";
                        //ddlServiceStatus.DataBind();
                        //ddlServiceStatus.Items.Insert(0, "- Select Business Status -");

                        EnumHelper.PopulateEnumIntoList(ddlServiceStatus, typeof(SiteType), " - Select Site Type - ");

                        var allSite = facade.GetAllSites();
                        //var location=
                        ddlLocation.DataSource = allSite;
                        ddlLocation.DataTextField = "Location";
                        ddlLocation.DataValueField = "Id";
                        ddlLocation.DataBind();
                        ddlLocation.Items.Insert(0, "- Select Location -");

                        _logger.Info("Binding Business Service Health Summary to Listview.");
                        lvBSList.DataSource = BuildService();
                        lvBSList.DataBind();

                    }

                }
                //lvBSList.DataSource = allBS;
                //lvBSList.DataBind();

            }
            catch (Exception ex)
            {
                _logger.Error("Error while loading  business service health page :" + ex.Message);
                if (ex.InnerException != null)
                    _logger.Error("Inner Exception" + ex.InnerException.ToString());
            }
        }


        public void GetServiceHealthSummaryCount(IList<BusinessService> bsLsit)
        {
            try
            {
                //var PRCount = 0;
                //var DRCount = 0;
                //var NAHealthCnt = 0;
                //int affectedInfraObjCount = 0;
                //int bscnt = 0;
                //ctl00_cphBody_lblTotalHealthSummary.Text = bsLsit.Count().ToString();
                //IList<InfraObject> infraList = null;

                //if (bsLsit != null)
                //{
                //    foreach (var bs in bsLsit)
                //    {
                //        infraList = facade.GetInfraObjectByBusinessServiceId(bs.Id);

                //        if (infraList != null)
                //        {
                //            bscnt = infraList.Count();
                //            var DescDrillData = from s in infraList
                //                                where s.State.ToString() != InfraObjectActivityType.Maintenance.ToString()
                //                                select s;
                //            if (DescDrillData != null)
                //            {
                //                var data = from a in DescDrillData
                //                           where a.DROperationStatus == 2 || a.DROperationStatus == 8
                //                           select a;
                //                if (data != null)
                //                {
                //                    affectedInfraObjCount = data.Count();
                //                }
                //            }
                //            // if (bscnt == affectedInfraObjCount)
                //            if (affectedInfraObjCount > 0)
                //            {
                //                DRCount = DRCount + 1;
                //            }
                //            else
                //            {
                //                PRCount = PRCount + 1;
                //            }
                //        }
                //        else
                //        {
                //            NAHealthCnt = NAHealthCnt + 1;
                //        }
                //    }
                //}
                //if (PRCount >= 0 || DRCount >= 0)
                //{
                //    lblProductionCnt.Text = PRCount.ToString();
                //    lblDRCount.Text = DRCount.ToString();
                //}

                //if (NAHealthCnt >= 0)
                //{
                //    lblunderCount.Text = NAHealthCnt.ToString();
                //}

                lstServiceHealth = BuildService();
                lstServiceHealth_graph = lstServiceHealth;
                //int total = lstServiceHealth.Count;
                //int pr = lstServiceHealth.Where(x => x.EnviornmentType.Equals("PR")).Count();
                //int dr = lstServiceHealth.Where(x => x.EnviornmentType.Equals("DR")).Count();
                //int ndr = lstServiceHealth.Where(x => x.EnviornmentType.Equals("NDR")).Count();
                //int colo = lstServiceHealth.Where(x => x.EnviornmentType.Equals("COLO")).Count();
                //int oci = lstServiceHealth.Where(x => x.EnviornmentType.Equals("OCI")).Count();
                //int fb = lstServiceHealth.Where(x => x.EnviornmentType.Equals("FB")).Count();
                //int drready = lstServiceHealth.Where(x => x.EnviornmentType.Equals("DRReady")).Count();
                //int mocktesting = lstServiceHealth.Where(x => x.EnviornmentType.Equals("MockTesting")).Count();
                //int issuetesting = lstServiceHealth.Where(x => x.EnviornmentType.Equals("IssueTesting")).Count();




                int total = lstServiceHealth.Count;
                int pr = lstServiceHealth.Where(x => x.Type.Equals("PRSite")).Count();
                int dr = lstServiceHealth.Where(x => x.Type.Equals("DRSite")).Count();
                int ndr = lstServiceHealth.Where(x => x.Type.Equals("NearDRSite")).Count();
                int colo = lstServiceHealth.Where(x => x.Type.Equals("COLO")).Count();
                int oci = lstServiceHealth.Where(x => x.Type.Equals("OCI")).Count();
                int fb = lstServiceHealth.Where(x => x.Type.Equals("FB")).Count();
                int drready = lstServiceHealth.Where(x => x.Type.Equals("Bunker")).Count();
                //int mocktesting = lstServiceHealth.Where(x => x.EnviornmentType.Equals("MockTesting")).Count();
                //int issuetesting = lstServiceHealth.Where(x => x.EnviornmentType.Equals("IssueTesting")).Count();






                int na = lstServiceHealth.Where(x => x.Type.Equals("NA")).Count();
                ctl00_cphBody_lblTotalHealthSummary.Text = total.ToString();
                //ctl00_cphBody_lblTotalHealthSummary.Text = totalNA.ToString();
                lblProductionCnt.Text = pr.ToString();
                lblDRCount.Text = dr.ToString();
                lblunderCount.Text = ndr.ToString();
                Labelcolo.Text = colo.ToString();
                Labeloci.Text = oci.ToString();
                Labelfb.Text = fb.ToString();
                Labeldrready.Text = drready.ToString();
                //Labelmocktesting.Text = mocktesting.ToString();
                //LabelIssueTesting.Text = issuetesting.ToString();
                //Lablebunker.Text = 

                int na_Count = (totalNA - total_NA) + Convert.ToInt32(na);
                //Labelna.Text = na_Count.ToString();
                Labelna.Text = na.ToString();
                string Health = ctl00_cphBody_lblTotalHealthSummary.Text + "," + lblProductionCnt.Text + "," + lblDRCount.Text + "," + lblunderCount.Text;
                System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script1", "BSHealth('" + Health + "');", true);



                //string Health = ctl00_cphBody_lblTotalHealthSummary.Text + "," + lblProductionCnt.Text + "," + lblDRCount.Text + "," + lblunderCount.Text;
                //System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script1", "BSHealth('" + Health + "');", true);
            }
            catch (Exception exc)
            {
                _logger.Error("Exception occured while  service health summary " + exc.Message);
                if (exc.InnerException != null)
                    _logger.Error("Inner Exception:" + exc.InnerException.ToString());
            }

        }

        protected void ddlbsservices_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                IList<BSHealthSummaryDetails> lstServiceHealth = (IList<BSHealthSummaryDetails>)Session["Keylisttest"];

                //lstServiceHealth = BuildService();
                int serviceId = Convert.ToInt32(ddlbsservices.SelectedValue);
                if (serviceId > 0)
                {
                    lvBSList.DataSource = lstServiceHealth.Where(x => x.Id == serviceId);
                    lvBSList.DataBind();
                    //ddlServiceStatus.SelectedValue = "0";
                    //ddlLocation.SelectedValue = "0";


                    ddlServiceStatus.SelectedIndex = 0;
                    ddlLocation.SelectedIndex = 0;
                }

            }
            catch (Exception ex)
            {

                _logger.Error("Exception while load service data" + ex.Message);
            }
        }

        protected void ddlServiceStatus_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                IList<BSHealthSummaryDetails> lstServiceHealth = (IList<BSHealthSummaryDetails>)Session["Keylisttest"];
                //lstServiceHealth = BuildService();
                string status = Convert.ToString(ddlServiceStatus.SelectedItem.Text);
                if (!string.IsNullOrEmpty(status))
                {
                    lvBSList.DataSource = lstServiceHealth.Where(x => x.Type.Equals(status));
                    lvBSList.DataBind();
                    //ddlbsservices.SelectedValue = "0";
                    //ddlLocation.SelectedValue = "0";

                    ddlbsservices.SelectedIndex = 0;
                    ddlLocation.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {

                _logger.Error("Exception while load service status" + ex.Message);

            }
        }

        protected void ddlLocation_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                IList<BSHealthSummaryDetails> lstServiceHealth = (IList<BSHealthSummaryDetails>)Session["Keylisttest"];
                //lstServiceHealth = BuildService();
                string location = Convert.ToString(ddlLocation.SelectedItem.Text);
                if (!string.IsNullOrEmpty(location))
                {
                    lvBSList.DataSource = lstServiceHealth.Where(x => x.Location.Equals(location));
                    lvBSList.DataBind();
                    //ddlbsservices.SelectedValue = "0";
                    //ddlLocation.SelectedValue = "0";
                    ddlbsservices.SelectedIndex = 0;
                    ddlServiceStatus.SelectedIndex = 0;
                    //ddlLocation.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                _logger.Error("Exception while load service location " + ex.Message);
            }
        }

        protected void lvBSList_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            try
            {
                Label lblHealthBSStatus = (Label)e.Item.FindControl("lblBSStatus");
                var lblId = (Label)e.Item.FindControl("Id");
                var StatusHealthImgIcon = (Label)e.Item.FindControl("StateusImgIcon");
                if (lblId.Text != null)
                {
                    var infraobjectStateList = facade.GetInfraObjectByBusinessServiceId(Convert.ToInt32(lblId.Text));
                    if (infraobjectStateList != null && infraobjectStateList.Count > 0)
                    {
                        if (lblHealthBSStatus.Text == "DR")
                        {
                            StatusHealthImgIcon.CssClass = "bs_dr";
                        }
                        else if (lblHealthBSStatus.Text == "Production")
                        {
                            StatusHealthImgIcon.CssClass = "bs_prod";
                        }
                    }

                    else
                    {
                        StatusHealthImgIcon.CssClass = "icon-NA";
                        lblHealthBSStatus.Text = "Configuration Inprogress";
                    }
                }
                else
                {
                    StatusHealthImgIcon.CssClass = "icon-NA";
                    lblHealthBSStatus.Text = "Configuration Inprogress";
                }
            }
            catch (Exception exc)
            {
                _logger.Error("Exception occured while bind health summary item " + exc.Message);
                if (exc.InnerException != null)
                    _logger.Error("Inner Exception:" + exc.InnerException.ToString());
            }
        }

        public IList<BSHealthSummaryDetails> BuildService()
        {
            try
            {
                lstServiceHealth.Clear();
                lastestServiceHealth.Clear();
                IList<BusinessService> allBS2 = facade.GetAllBusinessServices();
                //IList<workflowactionenviorment> groupWFactionenv = Facade.GetAllGroupWorkflowSummarybyactionenviorment();



                //IList<workflowactionenviorment> exelist2 = groupWFactionenv;
                //var BSGrp2 = exelist2.GroupBy(a => a.BusinessServiceId).ToList();
                //total_NA = BSGrp2.Count;
                //totalNA = allBS2.Count;

                //  var unionResult = allBS2.ToList().Union() .Union(BSGrp2);  
                //var result = allBS2.Where(p => !exelist2.Any(p2 => p2.BusinessServiceId == p.Id)).ToList();
                // var result = exelist2.Where(p => !allBS2.Any(p2 => p2.Id == p.BusinessServiceId)).ToList();


                //if (result != null)
                //{
                //    int i = 1;
                //    foreach (var item in result)
                //    {
                //        BSHealthSummaryDetails objBS = new BSHealthSummaryDetails();
                //        objBS.Id = item.Id;
                //        objBS.BusinesServiceName = item.Name != null ? item.Name : "NA";
                //        objBS.EnviornmentType = "NA";

                //        var infraobjectStateList = facade.GetInfraObjectByBusinessServiceId(item.Id);

                //        int infradropcount = 0;
                //        if (infraobjectStateList != null)
                //        {
                //            if (infraobjectStateList.Count() > 0)
                //            {
                //                foreach (var p in infraobjectStateList)
                //                {

                //                    if (p.DROperationStatus == 2)
                //                    {
                //                        infradropcount++;

                //                    }


                //                }

                //                if (infraobjectStateList.Count() == infradropcount)
                //                {

                //                    Server server = facade.GetServerById(infraobjectStateList.FirstOrDefault().DRServerId);
                //                    if (server != null)
                //                    {
                //                        var SiteDet = facade.GetSiteById(server.SiteId);

                //                        if (SiteDet != null)
                //                        {
                //                            objBS.Location = SiteDet.Location;
                //                            objBS.Type = Convert.ToString(SiteDet.Type);
                //                        }
                //                        else
                //                        {
                //                            objBS.Location = "NA";
                //                            objBS.Type = "NA";
                //                        }
                //                    }
                //                    else
                //                    {
                //                        objBS.Location = "NA";
                //                        objBS.Type = "NA";
                //                    }
                //                }
                //                else
                //                {
                //                    Server server = facade.GetServerById(infraobjectStateList.FirstOrDefault().PRServerId);
                //                    if (server != null)
                //                    {
                //                        var SiteDet = facade.GetSiteById(server.SiteId);

                //                        if (SiteDet != null)
                //                        {
                //                            objBS.Location = SiteDet.Location;
                //                            objBS.Type = Convert.ToString(SiteDet.Type);
                //                        }
                //                        else
                //                        {
                //                            objBS.Location = "NA";
                //                            objBS.Type = "NA";
                //                        }
                //                    }
                //                    else
                //                    {
                //                        objBS.Location = "NA";
                //                        objBS.Type = "NA";
                //                    }
                //                }
                //            }
                //            else
                //            {
                //                objBS.Location = "NA";
                //                objBS.Type = "NA";
                //            }
                //        }
                //        else
                //        {
                //            objBS.Location = "NA";
                //            objBS.Type = "NA";
                //        }

                //        //var SiteDet = facade.GetSiteById(item.SiteId);

                //        //if (SiteDet != null)
                //        //    objBS.Location = SiteDet.Location;
                //        //else
                //        //    objBS.Location = "NA";

                //        lastestServiceHealth.Add(objBS);
                //        i++;

                //    }

                //}



                //allactionenvirment = Facade.getallactionenviornment();

                if (allBS2 != null)
                {
                    int i = 1;

                    foreach (var item in allBS2)
                    {

                        BSHealthSummaryDetails objBS = new BSHealthSummaryDetails();
                        objBS.Id = item.Id;
                        objBS.BusinesServiceName = item.Name;

                        //objBS.EnviornmentType = allactionenvirment.Where(a => !string.IsNullOrEmpty(a.EnviornmentType) && a.Id.ToString() == item.FirstOrDefault().EnviornmentType).FirstOrDefault(x => x.Id.ToString() == item.FirstOrDefault().EnviornmentType) != null ? allactionenvirment.FirstOrDefault(x => x.Id == Convert.ToInt32(item.FirstOrDefault().EnviornmentType)).EnviornmentType : "NA";

                        //objBS.EnviornmentType = allactionenvirment.Where(a => !string.IsNullOrEmpty(a.EnviornmentType) && a.Id.ToString() == item.FirstOrDefault().EnviornmentType).FirstOrDefault(x => x.Id.ToString() == item.FirstOrDefault().EnviornmentType) != null ? allactionenvirment.FirstOrDefault(x => x.Id == Convert.ToInt32(item.FirstOrDefault().EnviornmentType)).EnviornmentType : "NA";

                        //facade.getactionenviornmentById(Convert.ToInt32(item.OrderByDescending(x => x.Id).FirstOrDefault().EnviornmentType)); 

                        //var envId = Convert.ToInt32(item.FirstOrDefault().EnviornmentType);
                        //var matchedEnv = allactionenvirment
                        //    .Where(a => !string.IsNullOrEmpty(a.EnviornmentType) && a.Id.ToString() == item.FirstOrDefault().EnviornmentType)
                        //    .OrderByDescending(x => x.Id)
                        //    .FirstOrDefault();


                        //if (objBS.EnviornmentType.Contains(' '))
                        //{
                        //    objBS.EnviornmentType = objBS.EnviornmentType.Split(' ').Last();
                        //}

                        var infraobjectStateList = facade.GetInfraObjectByBusinessServiceId(objBS.Id);

                        int infradropcount = 0;
                        if (infraobjectStateList != null)
                        {
                            if (infraobjectStateList.Count() > 0)
                            {
                                if (infraobjectStateList.Count() == infraobjectStateList.Where(x => x.Type == 1).Count())
                                {
                                    Server server = new Server();

                                    if (infraobjectStateList.FirstOrDefault().DROperationStatus == 2 || infraobjectStateList.FirstOrDefault().DROperationStatus == 8)
                                    {
                                        server = facade.GetServerById(infraobjectStateList.FirstOrDefault().DRServerId);
                                    }
                                    else
                                    {
                                        server = facade.GetServerById(infraobjectStateList.FirstOrDefault().PRServerId);
                                    }

                                    if (server != null)
                                    {
                                        var SiteDet = facade.GetSiteById(server.SiteId);

                                        if (SiteDet != null)
                                        {
                                            objBS.Location = SiteDet.Location;
                                            objBS.Type = SiteDet.Type.ToString();
                                        }
                                        else
                                        {
                                            objBS.Location = "NA";
                                            objBS.Type = "NA";
                                        }
                                    }
                                    else
                                    {
                                        objBS.Location = "NA";
                                        objBS.Type = "NA";
                                    }
                                }
                                else
                                {
                                    foreach (var p in infraobjectStateList)
                                    {

                                        if (p.DROperationStatus == 2 || p.DROperationStatus == 8)
                                        {
                                            infradropcount++;

                                        }


                                    }

                                    if (infraobjectStateList.Count() == infradropcount)
                                    {
                                        Server server = new Server();
                                        var infra = infraobjectStateList.Where(x => x.Type == 2)
                                                     .OrderByDescending(x => x.UpdateDate)
                                                     .FirstOrDefault();
                                        if (infra != null)
                                        {

                                            if (infra.DROperationStatus == 2 || infra.DROperationStatus == 8)
                                            {
                                                server = facade.GetServerById(infra.DRServerId);
                                            }
                                            else
                                            {
                                                server = facade.GetServerById(infra.PRServerId);
                                            }

                                            // Server server = facade.GetServerById(infraobjectStateList.FirstOrDefault().DRServerId);
                                            if (server != null)
                                            {
                                                var SiteDet = facade.GetSiteById(server.SiteId);

                                                if (SiteDet != null)
                                                {
                                                    objBS.Location = SiteDet.Location;
                                                    objBS.Type = SiteDet.Type.ToString();
                                                }
                                                else
                                                {
                                                    objBS.Location = "NA";
                                                    objBS.Type = "NA";
                                                }

                                            }

                                            else
                                            {
                                                objBS.Location = "NA";
                                                objBS.Type = "NA";
                                            }
                                        }
                                    }
                                    else
                                    {
                                        var infra = infraobjectStateList.Where(x => x.Type == 2)
                                                     .OrderByDescending(x => x.UpdateDate)
                                                     .FirstOrDefault();
                                        //Server server = facade.GetServerById(infraobjectStateList.FirstOrDefault().PRServerId);
                                        Server server = new Server();


                                        if (infra != null)
                                        {

                                            if (infra.DROperationStatus == 2 || infra.DROperationStatus == 8)
                                            {
                                                server = facade.GetServerById(infra.DRServerId);
                                            }
                                            else
                                            {
                                                server = facade.GetServerById(infra.PRServerId);
                                            }
                                            if (server != null)
                                            {
                                                var SiteDet = facade.GetSiteById(server.SiteId);

                                                if (SiteDet != null)
                                                {
                                                    objBS.Location = SiteDet.Location;
                                                    objBS.Type = SiteDet.Type.ToString();
                                                }
                                                else
                                                {
                                                    objBS.Location = "NA";
                                                    objBS.Type = "NA";
                                                }
                                            }
                                            else
                                            {
                                                objBS.Location = "NA";
                                                objBS.Type = "NA";
                                            }
                                        }
                                        else
                                        {
                                            objBS.Location = "NA";
                                            objBS.Type = "NA";
                                        }
                                    }
                                }



                            }
                            else
                            {
                                objBS.Location = "NA";
                                objBS.Type = "NA";
                            }
                        }
                        else
                        {
                            objBS.Location = "NA";
                            objBS.Type = "NA";
                        }
                        lstServiceHealth.Add(objBS);
                        i++;



                    }
                }














                //                List<BSHealthSummaryDetails> objectList = lstServiceHealth.Cast<BSHealthSummaryDetails>()
                //.Concat(result)
                //.ToList();




                //var result3 = from grp in allBS2
                //              join appname in lstServiceHealth on grp.Id equals appname.Id
                //              orderby grp.Name
                //              select new { Id = grp.Id, Name = grp.Name, appName = appname.EnviornmentType, RecoveryType = grp.SiteId };





















                //if (allBS != null)
                //{
                //    lstServiceHealth = new List<BSHealthSummaryDetails>();
                //    foreach (BusinessService objService in allBS)
                //    {
                //        BSHealthSummaryDetails objBSHealthSummary = new BSHealthSummaryDetails();
                //        objBSHealthSummary.BusinessServiceName = objService.Name;
                //        objBSHealthSummary.serviceId = objService.Id;
                //        IList<string> issuesList = new List<string>();
                //        affectedInfraCount = 0;
                //        int businessServiceAvailCount = 0;
                //        BSHealthSummary bs = new BSHealthSummary();
                //        var infraobjectStateList = facade.GetInfraObjectByBusinessServiceId(objService.Id);
                //        var SiteDet = facade.GetSiteById(objService.SiteId);

                //        if (SiteDet != null)
                //            objBSHealthSummary.Location = SiteDet.Location;
                //        else
                //            objBSHealthSummary.Location = "NA";

                //        if (infraobjectStateList != null && infraobjectStateList.Count > 0)
                //        {
                //            businessServiceAvailCount = infraobjectStateList.Count();
                //            //var DescDrillData = (from s in infraobjectStateList where s.State.ToString() != InfraObjectActivityType.Maintenance.ToString() select s).ToList();
                //            var DescDrillData = from s in infraobjectStateList
                //                                where s.State.ToString() != InfraObjectActivityType.Maintenance.ToString()
                //                                select s;
                //            if (DescDrillData != null)
                //            {
                //                var data = from a in DescDrillData
                //                           where a.DROperationStatus == 2 || a.DROperationStatus == 8
                //                           select a;
                //                if (data != null)
                //                    affectedInfraCount = data.Count();
                //            }
                //            if (affectedInfraCount > 0)
                //            {
                //                objBSHealthSummary.Status = "DR";

                //                var data1 = from a1 in infraobjectStateList
                //                            orderby a1.Type descending
                //                            select a1;

                //                Server server = facade.GetServerById(data1.FirstOrDefault().DRServerId);
                //                if (server != null)
                //                    objBSHealthSummary.Location = facade.GetSiteById(server.SiteId).Location;
                //            }
                //            // StatusHealthImgIcon.CssClass = "bs_dr";
                //            else
                //            {
                //                objBSHealthSummary.Status = "Production";

                //                //var data2 = from a1 in infraobjectStateList
                //                //            orderby a1.Type descending
                //                //            select a1;
                //                Server server = facade.GetServerById(infraobjectStateList.FirstOrDefault().PRServerId);

                //                // Server server = facade.GetServerById(data2.FirstOrDefault().PRServerId);
                //                if (server != null)
                //                    objBSHealthSummary.Location = facade.GetSiteById(server.SiteId).Location;
                //            }
                //            //   StatusHealthImgIcon.CssClass = "bs_prod";
                //        }
                //        else
                //            objBSHealthSummary.Status = "Configuration Inprogress";
                //            lstServiceHealth.Add(objBSHealthSummary);
                //    }

                lstServiceHealth = lstServiceHealth.Concat(lastestServiceHealth).ToList();

                Session["Keylisttest"] = lstServiceHealth;


            }
            catch (Exception exc)
            {
                _logger.Error("Exception occured while buils service list :" + exc.Message);
                if (exc.InnerException != null)
                    _logger.Error("Inner Exception:" + exc.InnerException.ToString());
            }
            // lstServiceHealth = lstServiceHealth.Concat(lastestServiceHealth).ToList();
            return lstServiceHealth;

        }
        [System.Web.Services.WebMethod(EnableSession = true)]
        public static string GetBSgraphData()
        {
            try
            {


                if (lstServiceHealth_graph.Count > 0)
                {
                    int total = lstServiceHealth_graph.Count;
                    //int prod = lstServiceHealth.Where(x => x.EnviornmentType.Equals("Production")).Count();

                    //int pr = lstServiceHealth.Where(x => x.EnviornmentType.Equals("PR")).Count();
                    //int dr = lstServiceHealth.Where(x => x.EnviornmentType.Equals("DR")).Count();
                    //int ndr = lstServiceHealth.Where(x => x.EnviornmentType.Equals("NDR")).Count();
                    //int colo = lstServiceHealth.Where(x => x.EnviornmentType.Equals("COLO")).Count();
                    //int oci = lstServiceHealth.Where(x => x.EnviornmentType.Equals("OCI")).Count();
                    //int fb = lstServiceHealth.Where(x => x.EnviornmentType.Equals("FB")).Count();
                    //int drready = lstServiceHealth.Where(x => x.EnviornmentType.Equals("DRReady")).Count();
                    //int mocktesting = lstServiceHealth.Where(x => x.EnviornmentType.Equals("MockTesting")).Count();
                    //int issuetesting = lstServiceHealth.Where(x => x.EnviornmentType.Equals("IssueTesting")).Count();
                    //int na = lstServiceHealth.Where(x => x.EnviornmentType.Equals("NA")).Count();

                    int pr = lstServiceHealth_graph.Where(x => x.Type.Equals("PRSite")).Count();
                    int dr = lstServiceHealth_graph.Where(x => x.Type.Equals("DRSite")).Count();
                    int ndr = lstServiceHealth_graph.Where(x => x.Type.Equals("NearDRSite")).Count();
                    int colo = lstServiceHealth_graph.Where(x => x.Type.Equals("COLO")).Count();
                    int oci = lstServiceHealth_graph.Where(x => x.Type.Equals("OCI")).Count();
                    int fb = lstServiceHealth_graph.Where(x => x.Type.Equals("FB")).Count();
                    int drready = lstServiceHealth_graph.Where(x => x.Type.Equals("Bunker")).Count();
                    int na = lstServiceHealth_graph.Where(x => x.Type.Equals("NA")).Count();
                    int na_Count = (totalNA - total_NA) + Convert.ToInt32(na);



                    //string data = total + "," + pr + "," + dr + "," + ndr + "," + colo + "," + oci + "," + fb + "," + drready + "," + mocktesting + "," + issuetesting + "," + na;
                    string data = total + "," + pr + "," + dr + "," + ndr + "," + colo + "," + oci + "," + fb + "," + drready + "," + na;
                    lstServiceHealth_graph.Clear();
                    return data;
                }

            }
            catch (Exception ex)
            {
                logger.Error("Error while calculate graph data" + ex.Message);
            }
            return string.Empty;

        }
        protected void btnexport_Click(object sender, EventArgs e)
        {
            ExcelReport();
        }

        //public class BSHealthSummaryDetails
        //{
        //    public int serviceId { get; set; }
        //    public string BusinessServiceName { get; set; }
        //    public string Status { get; set; }
        //    public string Location { get; set; }
        //}
        public class BSHealthSummaryDetails
        {
            public int Id { get; set; }
            public string BusinesServiceName { get; set; }
            public string EnviornmentType { get; set; }
            public string Location { get; set; }
            public string Type { get; set; }
        }


        private void ExcelReport()
        {
            // int serviceId = Convert.ToInt32(ddlbsservices.SelectedValue);
            // string location = Convert.ToString(ddlLocation.SelectedItem.Text);
            //string status = Convert.ToString(ddlServiceStatus.SelectedItem.Text);



            lstServiceHealth = BuildService();
            if (Convert.ToInt32(ddlbsservices.SelectedIndex) > 0)
            {
                lstServiceHealthrpt = lstServiceHealth.Where(x => x.Id == Convert.ToInt32(ddlbsservices.SelectedValue)).ToList();
            }


            else if (Convert.ToInt32(ddlLocation.SelectedIndex) > 0)
            {
                lstServiceHealthrpt = lstServiceHealth.Where(x => x.Location.Equals(Convert.ToString(ddlLocation.SelectedItem.Text))).ToList();
            }

            else if (Convert.ToInt32(ddlServiceStatus.SelectedIndex) > 0)
            {
                lstServiceHealthrpt = lstServiceHealth.Where(x => x.Type.Equals(Convert.ToString(ddlServiceStatus.SelectedItem.Text))).ToList();
            }
            else
            {
                lstServiceHealthrpt = BuildService();
            }
            _logger.Info("======Generating Business Service Health Summary Report EXCEL View ======");
            _logger.Info(Environment.NewLine);
            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "BS Summary Report";

            _cells["A1"].ColumnWidth = 7;

            _cells["D3"].Formula = "Business Service Summary Report";
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].Font.Bold = true;
            _cells["B3:F6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:F3"].Font.Size = 11;
            _cells["B5:F8"].Font.Size = 10;
            _cells["B3:F8"].Font.Color = Color.White;
            _cells["B3:F8"].Font.Name = "Cambria";



            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Cont_Patrol.jpg"), 48, 10, 120, 12);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 580, 10, 120, 13);
            // reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/BSE_LOGO.jpg"), 445, 10, 60, 13);

            //string strlogo = LoggedInUserCompany.CompanyLogoPath;
            //if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            //{
            //    reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 445, 10, 60, 13);
            //}
            reportWorksheet.Cells["A1:F1"].RowHeight = 27;
            reportWorksheet.Cells["A2:F2"].RowHeight = 27;


            var dateTime = DateTime.Now.ToString("dd-MM-yyyy HH:mm:ss tt");
            _cells["B5"].Formula = "Report Generated Time" + ":  " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            //_cells["C5"].Formula = ":  " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"));
            //_cells["C5"].Font.Bold = true;
            //_cells["C5"].HorizontalAlignment = HAlign.Left;

            int row = 8;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "SRNO.";
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
            _cells["B8:F8"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B8:E8"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "BUSINESS SERVICE";
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;

            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
            productionlog.WrapText = true;

            _cells["D" + row.ToString()].Formula = "STATUS";
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["E" + row.ToString()].Formula = "LOCATION";
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;

            _cells["F" + row.ToString()].Formula = "SiteType";
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Left;


            row++;
            int dataCount = 0;
            int xlRow = 9;
            _logger.Info(" Records Retrieve for Business Service Health Summary Report ======");
            _logger.Info(Environment.NewLine);

            if (lstServiceHealthrpt != null && lstServiceHealthrpt.Count > 0)
            {
                foreach (var rp in lstServiceHealthrpt)
                {
                    dataCount++;
                    int column = 0;
                    string[] xlColumn = { "B", "C", "D", "E", "F" };
                    xlRow++;

                    string ndx = xlColumn[column] + row.ToString();
                    _cells[ndx + ":" + "F" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;



                    _cells[ndx].Formula = i.ToString();
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 9;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    i++;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = !string.IsNullOrEmpty(rp.BusinesServiceName) ? rp.BusinesServiceName : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 23;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;



                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = !string.IsNullOrEmpty(rp.EnviornmentType) ? rp.EnviornmentType : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 19;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    _cells[ndx].WrapText = true;
                    column++;

                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = !string.IsNullOrEmpty(rp.Location) ? rp.Location : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 20;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;


                    ndx = xlColumn[column] + row.ToString();
                    _cells[ndx].Formula = !string.IsNullOrEmpty(rp.Type) ? rp.Type : "NA";
                    _cells[ndx].Font.Size = 10;
                    _cells[ndx].ColumnWidth = 20;
                    _cells[ndx].Font.Color = Color.Black;
                    _cells[ndx].HorizontalAlignment = HAlign.Left;
                    column++;

                    row++;
                }
            }
            else
            {
                lblMsg.Visible = true;
                lblMsg.Text = "Records Not Found.";
                _logger.Info("Records Not Found in List lstServiceHealth for BS Health Summary Report");
                return;
            }

            int finalCount = dataCount + 10;
            _cells["B" + finalCount].Formula = "NA : Not Available";
            _cells["B" + finalCount].HorizontalAlignment = HAlign.Center;
            _cells["B" + finalCount].Font.Name = "Cambria";
            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

            reportWorksheet.ProtectContents = true;
            OpenExcelFile(reportWorkbook);
            _logger.Info("====== Business Service Health Summary EXCEL Report generated ======");
            _logger.Info(Environment.NewLine);
            //lstServiceHealth.Clear();

        }


        private void OpenExcelFile(IWorkbook workbook)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "BSSummaryRPT" + str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            //string myUrl = reportPath + "/ExcelFiles/" + str;
            //var myUrl = "/ExcelFiles/" + str;
            string myUrl = reportPath + "/ExcelFiles/" + str;
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar= BS Health Summary Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }


    }

}