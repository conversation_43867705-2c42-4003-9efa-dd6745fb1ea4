﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class WorkflowBuilder : IEntityBuilder<Workflow>
    {
        IList<Workflow> IEntityBuilder<Workflow>.BuildEntities(IDataReader reader)
        {
            var workflows = new List<Workflow>();

            while (reader.Read())
            {
                workflows.Add(((IEntityBuilder<Workflow>)this).BuildEntity(reader, new Workflow()));
            }

            return (workflows.Count > 0) ? workflows : null;
        }

        Workflow IEntityBuilder<Workflow>.BuildEntity(IDataReader reader, Workflow workflow)
        {
            //const int FLD_ID = 0;
            //const int FLD_NAME = 1;
            //const int FLD_XML = 2;
            //const int FLD_CREATORID = 3;
            //const int FLD_CREATEDATE = 4;
            //const int FLD_UPDATORID = 5;
            //const int FLD_UPDATEDATE = 6;

            //workflow.Id = reader.IsDBNull(FLD_ID) ? 0 : reader.GetInt32(FLD_ID);
            //workflow.Name = reader.IsDBNull(FLD_NAME) ? string.Empty : reader.GetString(FLD_NAME);
            //workflow.Xml = reader.IsDBNull(FLD_XML) ? string.Empty : reader.GetString(FLD_XML);
            //workflow.CreatorId = reader.IsDBNull(FLD_CREATORID) ? 0 : reader.GetInt32(FLD_CREATORID);
            //workflow.CreateDate = reader.IsDBNull(FLD_CREATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_CREATEDATE);
            //workflow.UpdatorId = reader.IsDBNull(FLD_UPDATORID) ? 0 : reader.GetInt32(FLD_UPDATORID);
            //workflow.UpdateDate = reader.IsDBNull(FLD_UPDATEDATE) ? DateTime.MinValue : reader.GetDateTime(FLD_UPDATEDATE);

            //Fields in bcms_workflow table on 16/07/2013 : Id, Name, Xml, CreatorId, CreateDate, UpdatorId, UpdateDate

            workflow.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            workflow.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
            workflow.Xml = Convert.IsDBNull(reader["Xml"]) ? string.Empty : Convert.ToString(reader["Xml"]);
            workflow.ActionIds = Convert.IsDBNull(reader["ACTIONIDS"]) ? string.Empty : Convert.ToString(reader["ACTIONIDS"]);
            workflow.IsActive = Convert.IsDBNull(reader["IsActive"]) ? 0 : Convert.ToInt32(reader["IsActive"]);
            workflow.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            workflow.CreateDate = Convert.IsDBNull(reader["CreateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreateDate"].ToString());
            workflow.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            workflow.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"].ToString());
            workflow.Version = Convert.IsDBNull(reader["Version"]) ? 1.0 : Convert.ToDouble(reader["Version"]);
            workflow.IsLock = Convert.IsDBNull(reader["IsLock"]) ? 0 : Convert.ToInt32(reader["IsLock"]);

            workflow.IsFourEye = Convert.IsDBNull(reader["IsFourEye"]) ? 0 : Convert.ToInt32(reader["IsFourEye"]);

            return workflow;
        }
    }
}