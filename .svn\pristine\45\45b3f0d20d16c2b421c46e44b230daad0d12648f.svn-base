﻿var isWorkFlowChange = "UnChange";
var btnNewWorkFLowClick = "false";
var actionforNew = "old";
var globalWorkFlowOperation = "New";
var WorkFlowName = "";
var txtObj = new Array("txtName", "txtDescription", "ddlHost", "txtScript", "txtExecMode", "txtRecurrenceTime");
var propertyObj = document.getElementById('divProperties');
var hdActivityAllObj = document.getElementById('hdActivityAll');
var ddlLoadPropertyObj = document.getElementById('ddlLoadProperty');
var conditionDiv = document.getElementById('conditionDiv');
var dropContentObj = document.getElementById('dropContent');
var GlobalWorkFlowProperty = '';
var GlobalWorkFlowID = '';
var globalId = 0;
var txtName = "";
var workFlowId = 0;
var globalConditionId = 1;
var formClose = "";
var allActionNames = "";
var allActionNamesForAddCondition = "";
par = "";
conditionClick = "";
conditionOptionValue = "";
conditionClickId = "";
loopCnt = 0;
chkReturnOrNot = false;
//deleteConditionDiamond = 0;
idWorkflowloaded = 0;
var divCondiCnt = "divCondiNotPresent";
var ddlActivitySetObj = new Array("txtActionSetName", "txtActionSetDescription", "ddlActionProperty", "ddlActionSetProperty");
var clickCount;
let myWin;


$('#idPrompt1').live("keyup", function () {
    var id = $("#idPrompt1").val();
    var span1 = $("#idPromptSpan").next();
    var NamesWorkflow = $("#idPrompt1").val().length;
    if (NamesWorkflow == 1) {
        //var alphaExp = /^[a-zA-Z\s]+$/; ^ [0 - 9]
        var alphaExp = /^[0-9]/;
        if ($("#idPrompt1").val().match(alphaExp)) {
            $(span1).show();
            $(span1).html("Not Allowed");
            $(span1).attr("class", "error");
            return false;
        }
        else {
            $(span1).hide();
            return true;
        }
    }

});



$('#idSaveAs').live("keyup", function () {
    var id = $("#idSaveAs").val();
    var span1 = $("#idSaveAsSpan");
    var NamesWorkflow = $("#idSaveAs").val().length;
    if (NamesWorkflow == 1) {
        //  var alphaExp = /^[a-zA-Z\s]+$/;
        var alphaExp = /^[0-9]/;
        if ($("#idSaveAs").val().match(alphaExp)) {
            $(span1).show();
            $(span1).html("Not Allowed");
            $(span1).attr("class", "error");
            return false;
        }
        else {
            $(span1).hide();
            return true;
        }
    }

});



$(".diamond > .close").live("click", function (event) {
    // deleteConditionDiamond = 1
    var IdOfWhichConditionDelete = $(this).parent().attr('id');
    $('#' + IdOfWhichConditionDelete).parent().prev().children('.one').removeAttr('targetaction');
    //$(this).parent().parent().remove();
    //allActionNamesForAddCondition = "";
    getActionIdWhenConditionAdd();
    var removeActionList = allActionNamesForAddCondition.split(',');
    for (var a = 0; a < removeActionList.length; a++) {
        var divIndex = 0;
        var targetid = 0;
        var middleId = 0;
        if (removeActionList[a].indexOf("divCondi") != -1) {
            var splitActionId = removeActionList[a].split('^');
            divIndex = a;
            for (var b = 0; b < removeActionList.length; b++) {
                var splitActionIdForTargetId = removeActionList[b].split('^');
                if (splitActionId[1] == splitActionIdForTargetId[0]) {
                    targetid = b;
                }
            }
            for (var c = 0; c < removeActionList.length; c++) {
                var splitActionIdForMiddleId = removeActionList[c].split('^');
                if (IdOfWhichConditionDelete == splitActionIdForMiddleId[0]) {
                    middleId = c;
                }
            }
            if (divIndex <= middleId && middleId <= targetid) {
                calculateHeightForDeleteCondition(splitActionId[0]);
            }
            else if (middleId < divIndex && targetid <= middleId) {
                calculateHeightForDeleteCondition(splitActionId[0]);
            }
        }
    }
    $("#" + IdOfWhichConditionDelete).parent().remove();
});

function calculateHeightForDeleteCondition(conditionDivIdWhenAddCondi) {
    $("#" + conditionDivIdWhenAddCondi).parent().children('.line').remove();
    var parentIdWhenAddCondi = $("#" + conditionDivIdWhenAddCondi).parent();
    var attrTotalheight = $("#" + conditionDivIdWhenAddCondi).attr('totalheight');
    var attrActiondifferenceWhenAddCondi = $("#" + conditionDivIdWhenAddCondi).attr('actiondifference');
    var totalheightWhenAddCondi;
    totalheightWhenAddCondi = parseInt(attrTotalheight) - 56;
    drwaConditionLineInUpAndDownDirection(attrActiondifferenceWhenAddCondi, parentIdWhenAddCondi, totalheightWhenAddCondi, conditionDivIdWhenAddCondi);

    //if (parseInt(attrActiondifferenceWhenAddCondi) < 0)
    //{
    //    $("<div class='line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "269px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "float": "right",
    //        "right": "21px",
    //        "color": "red"
    //    }).text("Failure").appendTo(parentIdWhenAddCondi);
    //    $("<div class='line'>").css({
    //        "height": parseInt(totalheightWhenAddCondi),
    //        "background": "#707070",
    //        "width": "1px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "float": "right",
    //        "right": "20px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("<div class='downthirdline line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "69px",
    //        "position": "absolute",
    //        "margin-top": parseInt(totalheightWhenAddCondi) + (-27),
    //        "right": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    //}
    //else if (parseInt(attrActiondifferenceWhenAddCondi) > 0)
    //{
    //    //totalheightWhenAddCondi = parseInt(attrTotalheight) - 56;
    //    $("<div  class='line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "269px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "left": "21px",
    //        "color": "red"
    //    }).text("Failure").appendTo(parentIdWhenAddCondi);
    //    $("<div class='line'>").css({
    //        "height": totalheightWhenAddCondi,
    //        "background": "#707070",
    //        "width": "1px",
    //        "position": "absolute",
    //        "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
    //        "float": "right",
    //        "left": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("<div class='upthirdline line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "69px",
    //        "position": "absolute",
    //        "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
    //        "left": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    //}
}

$("#btnCondition").click(function () {
    var append = $("[id$=ddlSelectedWFProperty] option:selected").val();
    var divMain = 1;
    var profileId = 1;
    var value = "abc";
    var divCondi = "divCondi" + globalConditionId;
    //$("#" + append).after("<div class='Property' id='myDivCondition'><div class='actionboxline'></div><div class='diamond' id='" + divCondi + "'><span style='visibility:hidden'>" + divCondi + "</span><img Class='close' src='../images/icons/remove-icon-small.png' /></div><div class='diamond-line'>Yes</div></div>");
    //globalConditionId = globalConditionId + 1;
    var diamountCount = 0;
    var biggestNum = 0;
    var i = 0;
    var prevConditionId = 0;
    $('.diamond').each(function () {
        if ($(this).hasClass('diamond')) {
            var currentNum = parseInt($(this).attr('id').replace('divCondi', ''), 10);
            if (currentNum > biggestNum) {
                biggestNum = currentNum;
            }
            diamountCount++;
        }
        var parentIdWhenAddCondi = $(this).parent();
        var conditionDivIdWhenAddCondi = $(this).attr('id');
        //if (i == 0) {
        //checkDivcondiDivAfterActionSelect(conditionDivIdWhenAddCondi);
        //    i++
        //}
        //  if (idWorkflowloaded == 1) {
        //  $(this).parent().children('.line').remove();
        // calculateHeight(conditionDivIdWhenAddCondi, parentIdWhenAddCondi);

        //}
    });
    checkDivcondiDivAfterActionSelect();
    getActionIdAndName();
    var tempActionsForAddCondi = allActionNamesForAddCondition.split(',');
    for (var a = 0; a < tempActionsForAddCondi.length; a++) {
        var selectedActionId = $("#ddlSelectedWFProperty option:selected").attr('actionid');
        var splitActionId = tempActionsForAddCondi[a].split('^');
        if (selectedActionId == splitActionId[0]) {
            var actionIndex = splitActionId[1].split('.');
            prevConditionId = actionIndex[0];
        }
    }
    if (diamountCount > 0 && idWorkflowloaded == 1) {
        globalConditionId = biggestNum + 1;
        divCondi = "divCondi" + globalConditionId;
        // $("#" + append).after("<div class='Property' id='myDivCondition'><div class='actionboxline'></div><div class='diamond' id='" + divCondi + "'><span style='visibility:hidden'>" + divCondi + "</span><img Class='close' src='../images/icons/remove-icon-small.png' /></div><div class='diamond-line'>Success</div></div>");
        $("#" + append).after("<div class='Property' id='myDivCondition'><div class='actionboxline'></div><div class='diamond' id='" + divCondi + "'><div class='diamondtext'>" + prevConditionId + "</div><img Class='close' src='../images/icons/remove-icon-small.png' style=margin-top:-24px/><div style='visibility:hidden'>" + divCondi + "</div></div><div class='diamond-line'>Success</div></div>");
    }
    else {
        //$("#" + append).after("<div class='Property' id='myDivCondition'><div class='actionboxline'></div><div class='diamond' id='" + divCondi + "'><span style='visibility:hidden'>" + divCondi + "</span><img Class='close' src='../images/icons/remove-icon-small.png' /></div><div class='diamond-line'>Success</div></div>");
        $("#" + append).after("<div class='Property' id='myDivCondition'><div class='actionboxline'></div><div class='diamond' id='" + divCondi + "'><div class='diamondtext'>" + prevConditionId + "</div><img Class='close' src='../images/icons/remove-icon-small.png' style=margin-top:-24px /><div style='visibility:hidden'>" + divCondi + "</div></div><div class='diamond-line'>Success</div></div>");
        globalConditionId = globalConditionId + 1;
    }
});


function checkDivcondiDivAfterActionSelect() {
    var selectedActionId = $("#ddlSelectedWFProperty option:selected").attr('actionid');
    var chkDivCondi = 0;
    allActionNamesForAddCondition = "";
    getActionIdWhenConditionAdd();
    //getActionNameWhenConditionAdd();
    var tempActionsForAddCondi = allActionNamesForAddCondition.split(',');
    var removeActionList = allActionNamesForAddCondition.split(',');
    //alert(tempActionsForAddCondi);
    for (var a = 0; a < removeActionList.length; a++) {
        var divIndex = 0;
        var targetid = 0;
        var middleId = 0;
        if (removeActionList[a].indexOf("divCondi") != -1) {
            var splitActionId = removeActionList[a].split('^');
            divIndex = a;
            for (var b = 0; b < removeActionList.length; b++) {
                var splitActionIdForTargetId = removeActionList[b].split('^');
                if (splitActionId[1] == splitActionIdForTargetId[0]) {
                    targetid = b;
                }
            }
            for (var c = 0; c < removeActionList.length; c++) {
                var splitActionIdForMiddleId = removeActionList[c].split('^');
                if (selectedActionId == splitActionIdForMiddleId[0]) {
                    middleId = c;
                }
            }
            if (divIndex < middleId && middleId < targetid) {
                calculateHeight(splitActionId[0]);
            }
            else if (middleId < divIndex && targetid <= middleId) {
                calculateHeight(splitActionId[0]);
            }
        }
    }
}

function getActionNameWhenConditionAdd() {
    $('.one,.diamond').each(function () {
        actionDetail = $(this).text();
        actionId = $(this).attr('id');
        allActionNamesForAddCondition = allActionNamesForAddCondition + actionDetail + ",";
    });
}

function getActionIdWhenConditionAdd() {
    allActionNamesForAddCondition = "";
    $('.one,.diamond').each(function () {
        var actionIdWhenCondiAdd = $(this).attr('id');
        var targetactionIdWhenCondiAdd = $(this).attr('targetaction');
        allActionNamesForAddCondition = allActionNamesForAddCondition + actionIdWhenCondiAdd + "^" + targetactionIdWhenCondiAdd + ",";
    });
}


function getActionIdAndName() {
    allActionNamesForAddCondition = "";
    $('.one,.diamond').each(function () {
        var actionIdWhenCondiAdd = $(this).attr('id');
        var actionName = $(this).text();
        var targetactionIdWhenCondiAdd = $(this).attr('targetaction');
        allActionNamesForAddCondition = allActionNamesForAddCondition + actionIdWhenCondiAdd + "^" + actionName + ",";
    });
}

function calculateHeight(conditionDivIdWhenAddCondi) {
    var parentIdWhenAddCondi = $("#" + conditionDivIdWhenAddCondi).parent();
    var conditionDivIdWhenAddCondi = conditionDivIdWhenAddCondi;
    $(parentIdWhenAddCondi).children('.line').remove();
    var attrTotalheight = $("#" + conditionDivIdWhenAddCondi).attr('totalheight');
    var attrActiondifferenceWhenAddCondi = $("#" + conditionDivIdWhenAddCondi).attr('actiondifference');
    var totalheightWhenAddCondi;
    if (loopCnt == 0) {
        totalheightWhenAddCondi = parseInt(attrTotalheight) + 56;
        loopCnt++;
    }
    else {
        totalheightWhenAddCondi = parseInt(attrTotalheight) + 56;// + (loopCnt * 1);
    }
    drwaConditionLineInUpAndDownDirection(attrActiondifferenceWhenAddCondi, parentIdWhenAddCondi, totalheightWhenAddCondi, conditionDivIdWhenAddCondi);
    //if (parseInt(attrActiondifferenceWhenAddCondi) < 0)
    //{
    //    $("<div class='line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "269px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "float": "right",
    //        "right": "21px",
    //        "color": "red"
    //    }).text("Failure").appendTo(parentIdWhenAddCondi);
    //    $("<div class='line'>").css({
    //        "height": parseInt(totalheightWhenAddCondi),
    //        "background": "#707070",
    //        "width": "1px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "float": "right",
    //        "right": "20px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("<div class='downthirdline line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "69px",
    //        "position": "absolute",
    //        "margin-top": parseInt(totalheightWhenAddCondi) + (-27),
    //        "right": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    //}
    //else if (parseInt(attrActiondifferenceWhenAddCondi) > 0)
    //{
    //    $("<div  class='line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "269px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "left": "21px",
    //        "color": "red"
    //    }).text("Failure").appendTo(parentIdWhenAddCondi);
    //    $("<div class='line'>").css({
    //        "height": totalheightWhenAddCondi,
    //        "background": "#707070",
    //        "width": "1px",
    //        "position": "absolute",
    //        "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
    //        "float": "right",
    //        "left": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("<div class='upthirdline line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "69px",
    //        "position": "absolute",
    //        "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
    //        "left": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    //}
}

$("[id^=divCondi]").live("click", function (e) {
    clickCount = 0;
    par = $(this).parent();
    //conditionClick = $(this).text();
    conditionClick = $(this).children().text();
    conditionClickId = $(this).attr('id');
    //if ($(this).attr('targetaction')) {
    //    $(this).parent().children('.line').remove();
    //}
    // GetActionNames();
    GetActionValue();
    $("#txtbxFailureCount").val($("#" + conditionClickId).attr('failedcount'));
});

function GetActionValue() {
    var value = 1;
    var actionDetail;
    $("#ddlExist1 > option").remove();

    $('.one,.diamond').each(function () {
        actionDetail = $(this).text();
        actionId = $(this).attr('id');
        if (actionId.indexOf("divCondi") != -1) {
            // $("#ddlExist1").append("<option value='" + value + "' actionId='" + actionId + "' class='hide'>" + actionDetail + "</option>");
        }
        else {
            $("#ddlExist1").append("<option value='" + value + "' actionId='" + actionId + "'>" + actionDetail + "</option>");
        }
        allActionNames = allActionNames + actionDetail + ",";
        if (conditionClick == actionDetail) {
            conditionOptionValue = value;
        }
        if ((parseInt(conditionOptionValue) + 1) == value) {
            var actionName = actionDetail.split('.');
            $("#txtSuccessAction").attr('value', actionName[1]);
        }
        value++;
    });
    var targetValue = $("#" + conditionClickId).attr('targetaction');
    //$("[id$=ddlExist1] option:selected").val();
    $('[id$=ddlExist1] option[actionid=' + $("#" + conditionClickId).attr('targetaction') + ']').attr("selected", "selected");
}
function GetActionValueWhenWeClickOnConnectButton() {
    allActionNames = "";
    $('.one,.diamond').each(function () {
        actionDetail = $(this).text();
        allActionNames = allActionNames + actionDetail + ",";
    });
}

$("#btnConnectAction").click(function () {

    var id = $("#txtbxFailureCount").val();
    var span1 = id.length;

    var span10 = $("#txtbxFailureCountSpan").html();

    if (span10.length <= 0 && span1 > 0) {

        var selectedActionName = $("#ddlExist1 option:selected").text();
        var selectedActionVal = $("#ddlExist1 option:selected").val();
        var selectedActionId = $("#ddlExist1 option:selected").attr('actionId');
        var getSelectedActionid = $("[id$=ddlSelectedWFProperty] option:selected").val();
        var failedCount = $("#txtbxFailureCount").val();
        if ($("#" + conditionClickId).attr('targetaction')) {
            $("#" + conditionClickId).parent().children('.line').remove();
            $('#' + conditionClickId).parent().prev().children('.one').attr('targetaction', selectedActionId);
        }
        var actionDifference = conditionOptionValue - selectedActionVal;
        var top;
        GetActionValueWhenWeClickOnConnectButton();
        var root = allActionNames.split(',');
        var tempActions = allActionNames.split(',');
        allActionNames = "";
        var condiId = 0;
        var totalHeight;

        for (var j = 0; j < root.length; j++) {
            if (selectedActionName == root[j]) {
                // if (clickCount == 0) {
                //clickCount++;
                if (actionDifference < 0) {
                    tempActions.splice(0, conditionOptionValue);
                    var splitCount = 0;
                    for (var a = 0; a < tempActions.length; a++) {
                        if (tempActions[a] == selectedActionName) {
                            splitCount = a;
                        }
                    }
                    tempActions.splice(splitCount, tempActions.length);
                    for (var k = 0; k < tempActions.length; k++) {
                        var str = tempActions[k].substring(0, tempActions[k].length - 1);
                        if (str.substring(1, str.length) == "divCondi") {
                            condiId++
                        }
                    }

                    var initialHeight = 16;
                    var actionHeight = parseInt(32 * Math.abs(actionDifference));
                    if (condiId > 0) {
                        var totalHeight = parseInt(initialHeight + actionHeight + (23 * condiId));
                    }
                    else {
                        var totalHeight = parseInt(initialHeight + actionHeight);
                    }
                    $("<div class='line'>").css({
                        "height": "1px",
                        "background": "#707070",
                        "width": "269px",
                        "position": "absolute",
                        "margin-top": "-27px",
                        "float": "right",
                        "right": "21px",
                        "color": "red"
                    }).text("Failure").appendTo(par);
                    $("<div class='line'>").css({
                        "height": totalHeight,
                        "background": "#707070",
                        "width": "1px",
                        "position": "absolute",
                        "margin-top": "-27px",
                        "float": "right",
                        "right": "20px"
                    }).appendTo(par);
                    $("<div class='downthirdline line'>").css({
                        "height": "1px",
                        "background": "#707070",
                        "width": "69px",
                        "position": "absolute",
                        "margin-top": totalHeight + (-27),
                        "right": "21px"
                    }).appendTo(par);
                    $("#" + conditionClickId).attr('actionDifference', actionDifference);
                    $("#" + conditionClickId).attr('totalHeight', totalHeight);
                    $("#" + conditionClickId).attr('targetAction', selectedActionId);
                    $("#" + conditionClickId).attr('failedCount', failedCount);
                    $('#' + conditionClickId).parent().prev().children('.one').attr('actionDifference', actionDifference);
                    $('#' + conditionClickId).parent().prev().children('.one').attr('totalHeight', totalHeight);
                    $('#' + conditionClickId).parent().prev().children('.one').attr('targetAction', selectedActionId);
                    $('#' + conditionClickId).parent().prev().children('.one').attr('failedCount', failedCount);
                }
                else if (actionDifference > 0) {
                    var splitCountDown
                    for (var b = 0; b < tempActions.length; b++) {
                        if (tempActions[b] == conditionClick) {
                            splitCountDown = b;
                        }
                    }
                    tempActions.splice(splitCountDown, tempActions.length);
                    var splitCount = 0;
                    for (var a = 0; a < tempActions.length; a++) {
                        if (tempActions[a] == selectedActionName) {
                            splitCount = a;
                        }
                    }
                    tempActions.splice(0, splitCount);
                    for (var k = 0; k < tempActions.length; k++) {
                        var str = tempActions[k].substring(0, tempActions[k].length - 1);
                        if (str.substring(1, str.length) == "divCondi") {
                            condiId++
                        }
                    }
                    var initialHeight = 16;
                    var actionHeight = parseInt(32 * actionDifference);
                    if (condiId > 0) {
                        var totalHeight = parseInt(initialHeight + actionHeight + (22 * condiId));
                    }
                    else {
                        var totalHeight = parseInt(initialHeight + actionHeight);
                    }
                    $("<div  class='line'>").css({
                        "height": "1px",
                        "background": "#707070",
                        "width": "269px",
                        "position": "absolute",
                        "margin-top": "-27px",
                        "left": "21px",
                        "color": "red"
                    }).text("Failure").appendTo(par);
                    $("<div class='line'>").css({
                        "height": totalHeight,
                        "background": "#707070",
                        "width": "1px",
                        "position": "absolute",
                        "margin-top": "-" + (27 + totalHeight) + "px",
                        "float": "right",
                        "left": "21px"
                    }).appendTo(par);
                    $("<div class='upthirdline line'>").css({
                        "height": "1px",
                        "background": "#707070",
                        "width": "69px",
                        "position": "absolute",
                        "margin-top": "-" + (27 + totalHeight) + "px",
                        "left": "21px"
                    }).appendTo(par);
                    $("#" + conditionClickId).attr('actionDifference', actionDifference);
                    $("#" + conditionClickId).attr('totalHeight', totalHeight);
                    $("#" + conditionClickId).attr('targetAction', selectedActionId);
                    $("#" + conditionClickId).attr('failedCount', failedCount);
                    $('#' + conditionClickId).parent().prev().children('.one').attr('actionDifference', actionDifference);
                    $('#' + conditionClickId).parent().prev().children('.one').attr('totalHeight', totalHeight);
                    $('#' + conditionClickId).parent().prev().children('.one').attr('targetAction', selectedActionId);
                    $('#' + conditionClickId).parent().prev().children('.one').attr('failedCount', failedCount);
                }
                // }
            }
            // i++;
        };
    }
    else {
        //$(span).show();
        $("#txtbxFailureCountSpan").show();
        $("#txtbxFailureCountSpan").html("*");
        $("#txtbxFailureCountSpan").css('color', 'Red');
    }
});


$("[id^=divCondi]").live({
    mouseover: function () {
        $(this).parent().children('.line').css({
            "background-color": "#468847",
            "box-shadow": "2px 1px 2px #468847"
        });
    },
    mouseout: function () {
        $(this).parent().children('.line').css({
            'background': '#707070',
            "box-shadow": "none"
        });
    }
});



$("#btnLoadWorkFlow").click(function () {
    var IsProfileApprovalSetInSettings = $('#IsProfileApprovalSetInSettings').val();
    var AllApprovedWorkflowIds = $('#AllApprovedWorkflowIds').val();
    if (IsProfileApprovalSetInSettings == "1" && (AllApprovedWorkflowIds == "No_Ids" || AllApprovedWorkflowIds != "")) {
        $('#btn_SelectWorkflow').hide();
        $('#btn_DeleteSelectedWorkflow').hide();


        if ($("#ctl00_cphBody_HiddenFielddelete").val() != "allowDelete") {
            OpenAlertModelForLoadWorkflowWitCustom("  <div class='col-md-3' id='dvLoadWorkflow'> Select WorkFlow </div> <div class='col-md-9' style='padding: 0;'><select id='ddlExist' class='col-md-10 chosen-select' style='padding:0px !important'></select><span id='idMsg'></span></div>", closeModelPopep);
        } else {
            //  OpenAlertModelForLoadWorkflow("  <div class='col-md-3' id='dvLoadWorkflow'> Select WorkFlow </div> <div class='col-md-9' style='padding: 0;'><select id='ddlExist' class='col-md-10 chosen-select' style='padding:0px !important'></select><span id='idMsg'></span></div>", closeModelPopep, CheckWorkFlowIsAttached);
            OpenAlertModelForLoadWorkflow("  <div class='col-md-3' id='dvLoadWorkflow'> Select WorkFlow </div> <div class='col-md-9' style='padding: 0;'><select id='ddlExist' class='col-md-10 chosen-select' style='padding:0px !important'></select><span id='idMsg'></span></div>", closeModelPopep, CheckWorkFlowIsAttached);
        }

    }
    else {
        if ($("#ctl00_cphBody_HiddenFielddelete").val() != "allowDelete") {
            OpenAlertModelForLoadWorkflowWitCustom("  <div class='col-md-3' id='dvLoadWorkflow'> Select WorkFlow </div> <div class='col-md-9' style='padding: 0;'><select id='ddlExist' class='col-md-10 chosen-select' style='padding:0px !important'></select><span id='idMsg'></span></div>", closeModelPopep);
        } else {
            //  OpenAlertModelForLoadWorkflow("  <div class='col-md-3' id='dvLoadWorkflow'> Select WorkFlow </div> <div class='col-md-9' style='padding: 0;'><select id='ddlExist' class='col-md-10 chosen-select' style='padding:0px !important'></select><span id='idMsg'></span></div>", closeModelPopep, CheckWorkFlowIsAttached);
            OpenAlertModelForLoadWorkflow("  <div class='col-md-3' id='dvLoadWorkflow'> Select WorkFlow </div> <div class='col-md-9' style='padding: 0;'><select id='ddlExist' class='col-md-10 chosen-select' style='padding:0px !important'></select><span id='idMsg'></span></div>", closeModelPopep, CheckWorkFlowIsAttached);
        }
    }

    $("#btnhistory").show();
    $(".modal-footer").css('padding', '5px');
    $(".modal-header .modal-title").css('line-height', '29px');
    $(".close").css({ "font-size": "28px", "color": "#fff" });
    GetDdlExistValue();
    $("button:eq(1)").attr("disabled", true);
    idWorkflowloaded = 1;
});

function CheckWorkFlowIsAttached(win) {
    formClose = win;
    workFlowId = $("#ddlExist option:selected").val();
    var workFlowName = $("#ddlExist option:selected").text();
    $("#idMsg").html("");
    $("#idMsg").removeClass('error');
    $("#idMsg").attr("disabled", "false");
    if (workFlowId == "000") {
        $("#idMsg").html("*");
        $("#idMsg").attr("class", "error");
        $("#idMsg").attr("disabled", "true");
        return;
    }
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/CheckWorkFlowIsAttached",
        data: "{'args':'" + workFlowId + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (msg) {
            PopulateAttachedWorkflow(msg.d, win, workFlowName);
        },
        error: function (msg) {
            alert(msg.d);
        }
    });
}

function PopulateAttachedWorkflow(msg, win, workFlowName) {
    var message = msg;
    if (message == "Success") {
        OpenAlertModelAlert("<b class='text-success'>" + workFlowName + "</b> workflow is not deleting? Because it is already attached ");
    }
    else {
        deleteWorkflow(win, workFlowName);
    }
}

function deleteWorkflow(win, workFlowName) {
    if (workFlowName == "-Select WorkFlow-") {
        OpenAlertModelAlert("Please Select Workflow .. !!!");
    }
    else {
        var hiddentoken = document.getElementById("ctl00_cphBody_hdtokenKey").value;
        var hdnGuid = document.getElementById("ctl00_cphBody_hdnGuid").value;
        $.ajax({
            type: "POST",
            url: "WorkflowConfiguration.aspx/DeleteExistingWorkFLow",
            data: "{'args':'" + workFlowId + "','workflowName':'" + workFlowName + "','ReqToken':'" + hiddentoken + "','ReqGuid':'" + hdnGuid + "'}",
            contentType: "application/json; charset=utf-8",
            dataType: "json",
            success: function (msg) {
                if (msg.d == "XssScriptAttack") {
                    window.location.href = "/Logout.aspx";
                    return false;
                }
                if (msg.d != "AccessDenied") {
                    PopulateDeleteWorkflow(msg.d, win);
                }
                else {
                    PopulateDeleteWorkflowLock(msg.d, win);
                }
            },
            error: function (msg) {
                alert(msg.d);
            }
        });
    }

}

function PopulateDeleteWorkflowLock(msg, win) {
    // OpenAlertModelAlert("Access Denied to delete Workflow,Due to insufficient privileges");
    CloseModel(formClose);
    CloseModel(win);
    newWorkFlowOperation();
    if (msg == "Deletion request generated sucessfully. Once it gets approved you can delete workflow.") {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Deletion request generated sucessfully. Once it gets approved you can delete workflow.</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }
    else if (msg == "Creation request for workflow is not approved..!") {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/Warning.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Creation request for workflow is not approved..!</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }
    else if (msg == "Please select Approvers for deletion of workflow first..!") {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Please select Approvers for deletion of workflow first..!</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }
    else if (msg == "Pending") {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/error.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>The deletion request is in Pending state , Please do approve from approver.</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }

    else {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Workflow Deleted Successfully.</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }

}

function PopulateDeleteWorkflow1() {

    OpenAlertModelAlert("Please Select Workflow .. !!!");
    CloseModel(formClose);
    CloseModel(win);
    newWorkFlowOperation();
}

function PopulateDeleteWorkflow(msg, win) {
    //OpenAlertModelAlert("Workflow Deleted successfully !!!");

    if (msg == "Deletion request generated sucessfully. Once it gets approved you can delete workflow.") {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Deletion request generated sucessfully. Once it gets approved you can delete workflow.</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }
    else if (msg == "Creation request for workflow is not approved..!") {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/Warning.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Creation request for workflow is not approved..!</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }
    else if (msg == "Please select Approvers for deletion of workflow first..!") {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Please select Approvers for deletion of workflow first..!</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }
    else if (msg == "Pending") {
        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/error.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>The deletion request is in Pending state , Please do approve from approver.</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }
    else {

        OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div>  <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Workflow Deleted Successfully.</div></div>");
        CloseModel(formClose);
        CloseModel(win);
        newWorkFlowOperation();
        //History
        $('[id$=lblworkflowVersion]').val('');
        sessionStorage.setItem("workflowidOld", 0);
        sessionStorage.setItem("workflowhistorynameOld", "");
        $("#workflowHiddenId").html("");
        $("#btnhistory").hide();
    }


}

function GetDdlExistValue() {
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/GetExistingWorkflow_New",
        data: "{'args':'GetWorkFlow'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (msg) {
            PopulateDdlExist(msg.d);
            $(".chosen-select").chosen({ search_contains: true });
        },
        error: function (msg) {
            alert(msg.d);
        }
    });
}

function PopulateDdlExist(msg) {
    var data = msg;


    var result = data.split(",").sort();
    if (result == "") {
        var text = "-Select WorkFlow-";
        var value = "000";
        var chkValue = "";
        $("#ddlExist").append("<option value='" + value + "'>" + text + "</option>");
    }
    else {
        var text = "-Select WorkFlow-";
        var value = "000";
        var chkValue = "";
        AppendOption('ddlExist', value, text);
        for (var i = 0; i < result.length; i++) {
            chkValue = result[i].split(":");
            text = chkValue[0];
            value = chkValue[1];
            $("#ddlExist").append("<option value='" + value + "'>" + text + "</option>");
        }

        $("#ddlExist option:first").attr('selected', 'selected');
    }
}

function verify_ApprovalProcess_First(wf_id) {
    try {
        var ajaxUrl = "WorkflowConfiguration.aspx/CheckIsWorkflowApproved";
        var ajaxData = "{'id':'" + wf_id + "'}";
        AjaxFunction(ajaxUrl, ajaxData, showOrHide_Buttons, OnError);

    } catch (e) {
        console.log("Error Occuerd While Verifying Approval Process for  Workflow ID : " + wf_id);
    }
}

function showOrHide_Buttons(val) {
    var value = val.d;
    if (value == "1") {
        $('#dvLoadWorkflow').parents('.modal-content').find('div.modal-footer.align-right').show();
    } else {
        alert('You have not generated the approval request, or the generated request is not approved or is rejected for workflow.');

    }
}

$("#ddlExist").live("change", function () {



    //RequireDropDown($(this).attr("id"));
    var workFlowId = $("#ddlExist").val();
    var workFlowName = $("#ddlExist_chosen .chosen-single span").text();

    var isApprovalSetInSetting = $('#IsProfileApprovalSetInSettings').val();
    if (isApprovalSetInSetting == "1" && workFlowId != "" && workFlowId != undefined) {
        $('#dvLoadWorkflow').parents('.modal-content').find('div.modal-footer.align-right').hide();
        verify_ApprovalProcess_First(workFlowId);
    }

    //var workFlowId = $("#ddlExist option:selected").val();

    //History
    //var workFlowName = $("#ddlExist option:selected").text();
    sessionStorage.setItem("workflowidOld", workFlowId);
    sessionStorage.setItem("workflowhistorynameOld", workFlowName);

    if (workFlowId != 000) {
        $("button:eq(1)").attr("disabled", false);
    }
    else {
        $("button:eq(1)").attr("disabled", true);
    }
    $(".chosen-select").next("div").show();
});

function closeModelPopep(win) {
    //$("#ddlExist").trigger("change");

    var txt = $("#ddlExist").val();
    var span_err = $("#ddlExist").next().next();

    $("#idMsg").html("");
    $("#idMsg").removeClass('error');
    $("#idMsg").attr("disabled", "false");
    if (txt == "000") {
        $("#idMsg").html("*");
        $("#idMsg").attr("class", "error");
        $("#idMsg").attr("disabled", "true");
        return;
    }

    //if ($("#idMsg").hasClass("error")) {
    //    return false;
    //}

    $("#chkMarkParallel").attr('checked', false);
    $("#chkMarkParallel").next().children().children('span').css('display', 'block');
    $("#chkMarkParallel").next().children().children('span.cb-icon-check').css('display', 'none');

    removeItem();
    var e = document.getElementById('ddlExist');
    var id = e.options[e.selectedIndex].value;
    GlobalWorkFlowID = id;

    var hiddentoken = document.getElementById("ctl00_cphBody_hdtokenKey").value;
    var hdnGuid = document.getElementById("ctl00_cphBody_hdnGuid").value;
    $("#workflowHiddenId").html(id);
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/OpenWorkFlow",
        data: "{'parameter':'" + id + "','ReqToken':'" + hiddentoken + "','ReqGuid':'" + hdnGuid + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        sync: true,
        success: function (msg) {

            if (msg.d != "Not_Approved") {




                if (msg.d == "XssScriptAttack") {
                    window.location.href = "/Logout.aspx";
                    return false;
                }



                var loadworkflowval = msg.d.split("$");
                if (loadworkflowval[1] == "True") {
                    //$("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#box1,#idAlertMechanism,#ChkEmail,#ChkSms,#ctl00_cphBody_ddlLoadProperty").attr("disabled", false);
                    $("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#btnMarkparallel,#btnActionDelete,#btnConnectAction,#box1,#idAlertMechanism,#ChkEmail,#ChkSms,#ctl00_cphBody_ddlLoadProperty,#btnSaveAsdraft").removeClass("disabled");
                    //$("#idAlertMechanism").off('click');
                    //changes done for ITIT-5923
                    //$("#idAlertMechanism").css("pointer-events", "none");
                    $("#idAlertMechanism").css("pointer-events", "all");

                    $("#chkMarkParallel").next().find("button").attr("disabled", false);
                    $("#ctl00_cphBody_ddlLoadProperty").prop('disabled', false).trigger("chosen:updated");

                } else {

                    // $("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#box1,#idAlertMechanism,#ChkEmail,#ChkSms,#ctl00_cphBody_ddlLoadProperty").attr("disabled", true);
                    $("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#btnMarkparallel,#btnActionDelete,#btnConnectAction,#box1,#idAlertMechanism,#ChkEmail,#ChkSms,#ctl00_cphBody_ddlLoadProperty,#btnSaveAsdraft").addClass("disabled");
                    //  $("#idAlertMechanism").On('click');
                    $("#idAlertMechanism").css("pointer-events", "all");
                    $("#chkMarkParallel").next().find("button").attr("disabled", true);
                    //$("#ctl00_cphBody_ddlLoadProperty").prop('disabled', true).trigger("chosen:updated");

                }

                var loadworkflow = msg.d.split("$");
                $("[id$=hdnWorkflowlock]").val(loadworkflow[2]);
                //if ($("[id$=loginuser]").val() != "SuperAdmin" && $("[id$=hdnWorkflowlock]").val() == "1")
                if ($("[id$=hdnWorkflowlock]").val() == "1") {
                    $("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#box1,#idAlertMechanism,#ChkEmail,#ChkSms,#ctl00_cphBody_ddlLoadProperty").attr("disabled", true);
                    //$("#idAlertMechanism").off('click');
                    $("#idAlertMechanism").css("pointer-events", "none");
                    //$("#btnSave").attr("disabled", "disabled");
                    $("#chkMarkParallel").next().find("button").attr("disabled", true);
                    $('#imglockWF').show();
                    $('#btnSaveAsdraft').hide();
                    $('#save_asdraft').hide();
                } else {
                    $("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#box1,#idAlertMechanism,#ChkEmail,#ChkSms,#ctl00_cphBody_ddlLoadProperty").attr("disabled", false);
                    //  $("#idAlertMechanism").On('click');
                    $("#idAlertMechanism").css("pointer-events", "all");
                    $("#chkMarkParallel").next().find("button").attr("disabled", false);
                    $('#imglockWF').hide();
                  //  $('#btnSaveAsdraft').show();
                    //$('#save_asdraft').show();
                    $('#HiddenFieldforloadaction').val('true');
                }

                var loadworkflowval = msg.d.split("$");
                if (loadworkflowval[3] == "Not_Approved") {
                    // $("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#btnMarkparallel,#btnActionDelete,#btnConnectAction,#box1,#idAlertMechanism,#ChkEmail,#ChkSms,#ctl00_cphBody_ddlLoadProperty").addClass("disabled");
                    $("#btnNewWorkFlow,#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#btnMarkparallel,#btnActionDelete,#btnConnectAction,#box1,#idAlertMechanism,#ChkEmail,#ChkSms,#ctl00_cphBody_ddlLoadProperty").addClass("disabled");
                    //  $("#idAlertMechanism").On('click');
                    $("#idAlertMechanism").css("pointer-events", "all");
                    $("#chkMarkParallel").next().find("button").attr("disabled", true);
                    $('#HiddenFieldforloadaction').val('false');
                    $('#btnSaveAsdraft').hide();
                    $('#save_asdraft').hide();
                    //$("#ctl00_cphBody_ddlLoadProperty").prop('disabled', true).trigger("chosen:updated");
                }

                // CreateWorkFlowDiagrame(msg.d);
                //CreateWorkFlowDiagrame(loadworkflowval[0]);
                var wfver = msg.d.split('qpwoei');

                var wfversion;

                if (wfver[0].indexOf('.') == -1) {
                    wfversion = wfver[0] + '.0';
                }
                else {
                    wfversion = wfver[0];
                }



                $("#btnhistory").css({ "display": "inline" });
                $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');
                // CreateWorkFlowDiagrame(msg.d);
                var _loadworkflowforReview = msg.d.split("$");
                if (_loadworkflowforReview[4] != null) {
                    if (_loadworkflowforReview[4].split('#')[0] == "true") {
                        $('#ctl00_cphBody_btnverify').show();
                        $('#ctl00_cphBody_btnreject').show();
                        $('#ctl00_cphBody_btnSaveAsdraft').show();
                        $('#btnverify').show();
                        $('#btnreject').show();
                        var value = _loadworkflowforReview[4].split('#')[1];
                        $("#ProcessId").html(value.split('&')[0]);
                    }
                    else {
                        $('#ctl00_cphBody_btnverify').hide();
                        $('#ctl00_cphBody_btnreject').hide();
                        $('#ctl00_cphBody_btnSaveAsdraft').hide();
                        $('#btnverify').hide();
                        $('#btnreject').hide();
                    }
                }

                var IsFourEyeEnabled  = msg.d.split("*");
                if (IsFourEyeEnabled[1] != null) {
                    if (IsFourEyeEnabled[1] == "false") {
                        $('#btnSaveAsdraft').hide();
                        $('#save_asdraft').hide();
                    }
                }





                CreateWorkFlowDiagrame(wfver[1]);
                WorkFlowUnChanged();
                ClearLoadproperty();

            }
            else {
                alert('You have not generated the approval request, or the generated request is not approved or is rejected for workflow.');

            }
        }
    });

    CloseModel(win);
}

function ModalPopupsPrompt() {
    //OpenAlertModelLoad("<div class='col-md-4'> WorkFlow Name</div><div class='col-md-8'><input id='idPrompt1' class='col-md-9 form-control' type=text  style='width:80%;'  /><span id='idPromptSpan'></span><span></span></div>", ModalPopupsPromptOk);
    OpenAlertModelLoad_SAVE_WF("<div class='col-md-4'> WorkFlow Name</div><div class='col-md-8'><input id='idPrompt1' class='col-md-9 form-control' type=text  style='width:80%;'  /><span id='idPromptSpan'></span><span></span></div>", ModalPopupsPromptOk, ModelPopup_NoAction);
    $(".modal-dialog").css('width', '420px');
    $(".modal-footer").css('padding', '5px');
    $(".modal-header .modal-title").css('line-height', '29px');
    $(".close").css({ "font-size": "28px", "color": "#fff" });
    $("button:last").on("click", function () {
        globalWorkFlowOperation = "New";
    });
}

function ModelPopup_NoAction(win) {
    globalWorkFlowOperation = "New";
    $("#idPrompt1").val('');
    CloseModel(win);
}

$('[id$=idPrompt1]').live("blur", function () {
    var id = $("[id$=idPrompt1]").val();
    var r = new RegExp('\\\\]*$');
    if (id != "") {
        if (id.indexOf(" ") >= 0) {
            $("#idPromptSpan").html("Space is not allow in Workflow Name!!!");
            $("#idPromptSpan").attr("class", "error pull-left");
            $("#idPrompt1").focus();
            $(":button:first-child").attr("disabled", "true");
        } else if (r.test(id)) {
            $("#idPromptSpan").html("Backslash is not allow in Workflow Name!!!");
            $("#idPromptSpan").attr("class", "error pull-left");
            $("#idPrompt1").focus();
            $(":button:first-child").attr("disabled", "true");
        }
        else {
            $("#idPromptSpan").html("");
            $(":button:first-child").attr("disabled", false);
            var ajaxUrl = "WorkflowConfiguration.aspx/IsExistWorkflowByName";
            var ajaxData = "{'id':'" + id + "'}";
            AjaxFunction(ajaxUrl, ajaxData, WorkflowDuplicateMessage, OnError);
        }
    }
    else {
        $("#idPromptSpan").html("");
        $(":button:first-child").attr("disabled", "");
        globalWorkFlowOperation = "New";
    }
});

function WorkflowDuplicateMessage(msg) {
    //$("button:first").attr("disabled", false);
    $(":button:first-child").attr('disabled', false);
    if (msg.d == "duplicate") {
        $("#idPromptSpan").html(" Workflow Name is already present !!!");
        $("#idPromptSpan").attr("class", "error pull-left");
        //$("#idPrompt1").focus();
        $("[id$=idPrompt1]").focus();
        // $("button:first").att("disabled", "true");
        $(":button:first-child").attr('disabled', true);
    }
    else {
        $("#idPromptSpan").html("");
        $(":button:first-child").attr('disabled', false);
        //$("button:first").attr("disabled", false);
        globalWorkFlowOperation = "New";
    }
};
//
function ModalPopupsPromptOk(win) {
    if (document.getElementById("idPrompt1").value == "") {
        document.getElementById("idPrompt1").focus();
        return;
    }

    var span1 = $("#idPromptSpan").next();

    // var alphaExp = /^[a-zA-Z\s]+$/;
    var alphaExp = /^[0-9]/;
    if ($("#idPrompt1").val().match(alphaExp)) {
        $(span1).show();
        $(span1).html("Start With Numeric Workflow Name Not Allowed");
        $(span1).attr("class", "error");
        return false;
    }
    else {
        var nname = document.getElementById("idPrompt1").value;
        win.closeModal();
        SaveWorkflow(nname);
    }


}

function ModalPopupsPromptCancel() {
    ModalPopups.Cancel("idPrompt1");
}

function SavedraftWorkflow(wfName) {
    $("#idPrompt1").trigger("blur");
    WorkFlowUnChanged();
    var valueToPass = "";
    var parentTag = "";
    var type = "";
    //var profileId="";
    if (wfName == "Generic") {
        wfName = WorkFlowName;
    }
    else {
        WorkFlowName = wfName;
    }

    var wfRunningType;
    var wfRunningTypeValidate = "False";
    if ($("#chkMarkParallel").attr("checked") == "checked") {
        wfRunningType = "True";
    }
    else {
        wfRunningType = "False";
        $('.one').attr("runparallel", "undefined");
        $(".Property .small").remove();
        $(".Property .divparallel").remove();
    }

    $('.one,.diamond').each(function () {
        parentTag = $(this).parents().get(0);
        //profileId=$(this).attr("profile");
        type = $(parentTag).attr("Class");
        if ($(this).hasClass('diamond')) {
            //  alert("diamond");
            //valueToPass = valueToPass + type + "," + $(this).attr("actionDifference") + "^" + $(this).attr("totalHeight") + "," + $(this).attr("id") + ':';
            valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetAction") + "^" + $(this).attr("failedCount") + ':';
        }
        else {
            var RunparallelValue = "";
            if ($(this).attr('runParallel') == 'parallelRun') {
                if ($(this).parent().find('.divparallel').length) {
                    RunparallelValue = 'parallelRun';
                    valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + RunparallelValue + "^" + $(this).attr("failedCount") + ':';
                    wfRunningTypeValidate = "True";
                }
                else {
                    RunparallelValue = 'undefined';
                    valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + RunparallelValue + "^" + $(this).attr("failedCount") + ':';

                }
            }
            else { valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + $(this).attr("runParallel") + "^" + $(this).attr("failedCount") + ':'; }

        }
    });

    if (wfRunningType == "True") {
        if (wfRunningTypeValidate != "True") {
            wfRunningType = "False";
        }
    }



    valueToPass = valueToPass.substring(0, valueToPass.length - 1);
    var hiddenWorkflowId = $("#workflowHiddenId").html();
    var hiddentoken = document.getElementById("ctl00_cphBody_hdtokenKey").value;//$("#ctl00_cphBody_hdtokenKey").value;
    var hdnGuid = document.getElementById("ctl00_cphBody_hdnGuid").value;
    $.ajax({

        type: "POST",
        url: "WorkflowConfiguration.aspx/SaveDraftWorkFlow",
        data: "{'para':'" + valueToPass + "','name':'" + wfName + "','hiddenId':'" + hiddenWorkflowId + "','wfRunningType':'" + wfRunningType + "','ReqToken':'" + hiddentoken + "','ReqGuid':'" + hdnGuid + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        sync: true,
        success: function (msg) {
            if (msg.d == "XssScriptAttack") {
                window.location.href = "/Logout.aspx";
                return false;
            }
            if (btnNewWorkFLowClick == "true") {
                newWorkFlowOperation();
                $("#idWorfkflowName").html("Workflow Editor");
            }
            else {
                $("#idWorfkflowName").html(wfName);
                $("#idWorfkflowName").attr('title', wfName);
            }
            //var popUp= document.getElementById("idPrompt1").value;
            //if (popUp != null || typeof(popUp)!='undefined' ||popUp!='') {

            var message = msg.d.split('^');

            if (message[0] == "duplicate") {
                $("#idWorfkflowName").html("Workflow Editor");
                alert("Duplicate Workflow Name");
                //OpenAlertModelAlert("Duplicate Name");

                //History
                var wfversion;
                if (message[1].indexOf('.') == -1)
                    wfversion = message[1] + '.0';
                else
                    wfversion = message[1];

                   $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');

            }
            else if (message[0].indexOf("InvalidActionsId") != -1) {

                var ActionName = message[0].split('$')[1];

                $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');

                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/cancel-btn.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'>  Error Occured While Saving The WorkFlow <b class='text-danger' style='word-wrap: break-word;'>" + wfName + ",</b><br/>Kindly Remove The Workflow Action : " + ActionName + " and Try again.</div></div>");
                $(".modal-dialog").css('width', '400px');
                $(".modal-footer").css('padding', '5px');
                $(".modal-header .modal-title").css('line-height', '29px');
                $(".modal-header .modal-title").text("Error");
                $(".close").css({ "font-size": "28px", "color": "#fff" });
                //$(".col-md-12").css('text-align', 'center');
            }
            else if (message[0] == "Update") {

                var str = message[1].split('$');
                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'> Workflow <b class='text-success' style='word-wrap: break-word;'>" + wfName + "</b> saved as Draft Successfully.</div></div>");
                $(".modal-dialog").css('width', '400px');
                $(".modal-footer").css('padding', '5px');
                $(".modal-header .modal-title").css('line-height', '29px');
                $(".close").css({ "font-size": "28px", "color": "#fff" });


                //$(".col-md-12").css('text-align', 'center');
            }
            else if (message[0] == "Please select Approvers for creation of workflow first..!") {
                //  alert(msg.d);
                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/criticle.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'>  <b class='text-danger' style='word-wrap: break-word;'>" + message[0] + "</b> </div></div>");
            }

            else if (message[0] == "Sucess") {

                var str = message[2].split('$');

                sessionStorage.setItem("workflowidOld", str[0]);
                sessionStorage.setItem("workflowhistorynameOld", wfName);

                if (str[1] == "NotRedirect") {
                    OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'> Workflow <b class='text-success' style='word-wrap: break-word;'>" + wfName + "</b> Saved Successfully.</div></div>");
                    $("#workflowHiddenId").html(str[0]);
                    globalWorkFlowOperation = "Update";
                }
            }
            globalWorkFlowOperation = "Update";
            $("#btnSave").attr("disabled", false);
        }
    });
}

function SaveWorkflow(wfName) {
    $("#idPrompt1").trigger("blur");
    WorkFlowUnChanged();
    var valueToPass = "";
    var parentTag = "";
    var type = "";
    //var profileId="";
    if (wfName == "Generic") {
        wfName = WorkFlowName;
    }
    else {
        WorkFlowName = wfName;
    }

    var wfRunningType;
    var wfRunningTypeValidate = "False";
    if ($("#chkMarkParallel").attr("checked") == "checked") {
        wfRunningType = "True";
    }
    else {
        wfRunningType = "False";
        $('.one').attr("runparallel", "undefined");
        $(".Property .small").remove();
        $(".Property .divparallel").remove();
    }

    $('.one,.diamond').each(function () {
        parentTag = $(this).parents().get(0);
        //profileId=$(this).attr("profile");
        type = $(parentTag).attr("Class");
        if ($(this).hasClass('diamond')) {
            //  alert("diamond");
            //valueToPass = valueToPass + type + "," + $(this).attr("actionDifference") + "^" + $(this).attr("totalHeight") + "," + $(this).attr("id") + ':';
            valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetAction") + "^" + $(this).attr("failedCount") + ':';
        }
        else {
            var RunparallelValue = "";
            if ($(this).attr('runParallel') == 'parallelRun') {
                if ($(this).parent().find('.divparallel').length) {
                    RunparallelValue = 'parallelRun';
                    valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + RunparallelValue + "^" + $(this).attr("failedCount") + ':';
                    wfRunningTypeValidate = "True";
                }
                else {
                    RunparallelValue = 'undefined';
                    valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + RunparallelValue + "^" + $(this).attr("failedCount") + ':';

                }
            }
            else { valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + $(this).attr("runParallel") + "^" + $(this).attr("failedCount") + ':'; }

        }
    });

    if (wfRunningType == "True") {
        if (wfRunningTypeValidate != "True") {
            wfRunningType = "False";
        }
    }



    valueToPass = valueToPass.substring(0, valueToPass.length - 1);
    var hiddenWorkflowId = $("#workflowHiddenId").html();
    var hiddentoken = document.getElementById("ctl00_cphBody_hdtokenKey").value;//$("#ctl00_cphBody_hdtokenKey").value;
    var hdnGuid = document.getElementById("ctl00_cphBody_hdnGuid").value;
    // $("#dvvalidator").show();
    //alert(hiddenWorkflowId);
    $.ajax({

        type: "POST",
        url: "WorkflowConfiguration.aspx/SaveWorkFlow",
        data: "{'para':'" + valueToPass + "','name':'" + wfName + "','hiddenId':'" + hiddenWorkflowId + "','wfRunningType':'" + wfRunningType + "','ReqToken':'" + hiddentoken + "','ReqGuid':'" + hdnGuid + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        sync: true,
        success: function (msg) {
            if (msg.d == "XssScriptAttack") {
                window.location.href = "/Logout.aspx";
                return false;
            }
            if (btnNewWorkFLowClick == "true") {
                newWorkFlowOperation();
                $("#idWorfkflowName").html("Workflow Editor");
            }
            else {
                $("#idWorfkflowName").html(wfName);
                $("#idWorfkflowName").attr('title', wfName);
            }
            //var popUp= document.getElementById("idPrompt1").value;
            //if (popUp != null || typeof(popUp)!='undefined' ||popUp!='') {

            var message = msg.d.split('^');

            if (message[0] == "duplicate") {
                $("#idWorfkflowName").html("Workflow Editor");
                alert("Duplicate Workflow Name");
                //OpenAlertModelAlert("Duplicate Name");

                History
                var wfversion;
                if (message[1].indexOf('.') == -1)
                    wfversion = message[1] + '.0';
                else
                    wfversion = message[1];

                   $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');

            }
            else if (message[0].indexOf("InvalidActionsId") != -1) {

                var ActionName = message[0].split('$')[1];

                $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');

                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/cancel-btn.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'>  Error Occured While Saving The WorkFlow <b class='text-danger' style='word-wrap: break-word;'>" + wfName + ",</b><br/>Kindly Remove The Workflow Action : " + ActionName + " and Try again.</div></div>");
                $(".modal-dialog").css('width', '400px');
                $(".modal-footer").css('padding', '5px');
                $(".modal-header .modal-title").css('line-height', '29px');
                $(".modal-header .modal-title").text("Error");
                $(".close").css({ "font-size": "28px", "color": "#fff" });
                //$(".col-md-12").css('text-align', 'center');
            }
            else if (message[0] == "Update") {
                // alert("Workflow Updated");
                //OpenAlertModelAlert("Workflow Updated");

                var str = message[1].split('$')

                //History
                var wfversion;
                if (message[1].indexOf('.') == -1)
                    wfversion = message[1] + '.0';
                else
                    wfversion = message[1];

                  $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');
                if (str[1] == "NotRedirect") {

                    OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'> Workflow <b class='text-success' style='word-wrap: break-word;'>" + wfName + "</b> Updated Successfully.</div></div>");
                    $(".modal-dialog").css('width', '400px');
                    $(".modal-footer").css('padding', '5px');
                    $(".modal-header .modal-title").css('line-height', '29px');
                    $(".close").css({ "font-size": "28px", "color": "#fff" });
                    //$(".col-md-12").css('text-align', 'center');
                } else {
                    var url = "../Admin/WorkflowApprovalProcess.aspx";
                    window.location.href = url;
                }
                //$(".col-md-12").css('text-align', 'center');
            }
            else if (message[0] == "Please select Approvers for creation of workflow first..!") {
                //  alert(msg.d);
                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/criticle.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'>  <b class='text-danger' style='word-wrap: break-word;'>" + message[0] + "</b> </div></div>");
            }

            else if (message[0] == "Sucess") {
                //alert("Workflow Saved Successfully");


               // Histroy
                var wfversion;
                if (message[2].indexOf('.') == -1)
                    wfversion = message[2] + '.0';
                else
                    wfversion = message[2];

                $("#btnhistory").show();
                $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');

                var str = message[2].split('$');

                sessionStorage.setItem("workflowidOld", str[0]);
                sessionStorage.setItem("workflowhistorynameOld", wfName);

                if (str[1] == "NotRedirect") {
                    OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'> Workflow <b class='text-success' style='word-wrap: break-word;'>" + wfName + "</b> Saved Successfully.</div></div>");
                    $("#workflowHiddenId").html(str[0]);
                    globalWorkFlowOperation = "Update";
                } else {
                    var url = "../Admin/WorkflowApprovalProcess.aspx";
                    window.location.href = url;
                }
            }
            globalWorkFlowOperation = "Update";
            $("#btnSave").attr("disabled", false);
        }
    });
}



$("#Button7").click(function () {
    // $("#dvvalidator").hide;
})

$("#Button8").click(function () {
    //$("#dvvalidator").hide;

})

$('[id$=idSaveAs]').live("blur", function () {
    var id = $("[id$=idSaveAs]").val();
    if (id != "") {
        var r = new RegExp('\\\\]*$');
        if (id.indexOf(" ") >= 0) {
            $("#idSaveAsSpan").html("Space is not allow in Workflow Name!!!");
            $("#idSaveAsSpan").attr("class", "error pull-left");
            $("[id$=idSaveAs]").focus();
            $(":button:first-child").attr("disabled", "true");
        } else if (r.test(id)) {
            $("#idSaveAsSpan").html("Backslash is not allow in Workflow Name!!!");
            $("#idSaveAsSpan").attr("class", "error pull-left");
            $("[id$=idSaveAs]").focus();
            $(":button:first-child").attr("disabled", "true");
        }
        else {
            var ajaxUrl = "WorkflowConfiguration.aspx/IsExistWorkflowByName";
            var ajaxData = "{'id':'" + id + "'}";
            AjaxFunction(ajaxUrl, ajaxData, WorkflowSaveAsDuplicateMessage, OnError);
        }
    }
    else {
        $("#idSaveAsSpan").html("");
        // $("button:first").attr("disabled","");
        $(":button:first-child").attr("disabled", false);
        globalWorkFlowOperation = "New";
    }
});


function WorkflowSaveAsDuplicateMessage(msg) {
    //$("button:first").attr("disabled", "true");
    $(":button:first-child").attr('disabled', false);
    if (msg.d == "duplicate") {
        $("#idSaveAsSpan").html(" Workflow Name is already Present !!!");
        $("#idSaveAsSpan").attr("class", "error");
        $("#idSaveAs").focus();
        //$("button:first").attr("disabled", "true");
        $(":button:first-child").attr('disabled', true);
    }
    else {
        $("#idSaveAsSpan").html("");
        //$("button:first").attr("disabled", false);
        $(":button:first-child").attr('disabled', false);
        globalWorkFlowOperation = "New";
    }
};

//

function SaveAsWorkflow(wfName) {
    $("#idSaveAs").trigger("blur");
    WorkFlowUnChanged();
    var valueToPass = "";
    var parentTag = "";
    var type = "";
    if (wfName == "Generic") {
        wfName = WorkFlowName;
    }
    else {
        WorkFlowName = wfName;
    }

    var wfRunningType;
    if ($("#chkMarkParallel").attr("checked") == "checked") {
        wfRunningType = "True";
    }
    else {
        wfRunningType = "False";
        $('.one').attr("runparallel", "undefined");
        $(".Property .small").remove();
    }

    $('.one,.diamond').each(function () {
        parentTag = $(this).parents().get(0);
        type = $(parentTag).attr("Class");
        // valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + ':';
        if ($(this).hasClass('diamond')) {
            valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetAction") + "^" + $(this).attr("failedCount") + ':';
        }
        else {

            var RunparallelValue = "";
            if ($(this).attr("runParallel") == 'parallelRun') {
                if ($(this).parent().find('.divparallel').length) {
                    //alert("p present");
                    RunparallelValue = 'parallelRun';

                    valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + RunparallelValue + "^" + $(this).attr("failedCount") + ':';
                }
                else {
                    RunparallelValue = 'undefined';
                    valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + RunparallelValue + "^" + $(this).attr("failedCount") + ':';
                }
            }
            else {
                valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + $(this).attr("runParallel") + "^" + $(this).attr("failedCount") + ':';
            }


        }
    });

    valueToPass = valueToPass.substring(0, valueToPass.length - 1);
    var hiddentoken = document.getElementById("ctl00_cphBody_hdtokenKey").value;
    var hdnGuid = document.getElementById("ctl00_cphBody_hdnGuid").value;
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/SaveAsWorkFlow",
        data: "{'para':'" + valueToPass + "','name':'" + wfName + "','wfRunningType':'" + wfRunningType + "','ReqToken':'" + hiddentoken + "','ReqGuid':'" + hdnGuid + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        sync: true,
        success: function (msg) {
            if (msg.d == "XssScriptAttack") {
                window.location.href = "/Logout.aspx";
                return false;
            }
            if (btnNewWorkFLowClick == "true") {
                newWorkFlowOperation();
                $("#idWorfkflowName").html("Workflow Editor");
            }
            else {
                $("#idWorfkflowName").html(wfName);
                $("#idWorfkflowName").attr('title', wfName);
                newWorkFlowOperation();
            }
            var message = msg.d;
            if (message == "duplicate") {
                //OpenAlertModelAlert("Duplicate Name");
                $("#idWorfkflowName").html("Workflow Editor");
                alert("Duplicate Workflow Name");
            }
            else if (message[0].indexOf("InvalidActionsId") != -1) {

                var ActionName = message[0].split('$')[1];

                $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');

                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/cancel-btn.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'>  Error Occured While Saving The WorkFlow <b class='text-danger' style='word-wrap: break-word;'>" + wfName + ",</b><br/>Kindly Remove The Workflow Action : " + ActionName + " and Try again.</div></div>");
                $(".modal-dialog").css('width', '400px');
                $(".modal-footer").css('padding', '5px');
                $(".modal-header .modal-title").css('line-height', '29px');
                $(".modal-header .modal-title").text("Error");
                $(".close").css({ "font-size": "28px", "color": "#fff" });
                //$(".col-md-12").css('text-align', 'center');
            }
            else {
                if (message.split('$')[1] == "NotRedirect") {
                    alert("Workflow SaveAs Successfully");

                    //OpenAlertModelAlert("Workflow Save As");
                    //History
                    $('[id$=lblworkflowVersion]').val('');
                    sessionStorage.setItem("workflowidOld", 0);
                    sessionStorage.setItem("workflowhistorynameOld", "");
                    $("#workflowHiddenId").html("");
                    $("#btnhistory").hide();
                }
                else {
                    var url = "../Admin/WorkflowApprovalProcess.aspx";
                    window.location.href = url;
                }
            }
        }
    });
}


function newWorkFlowOperation() {
    $("#ctl00_cphBody_ddlLoadProperty").attr("disabled", false);
    btnNewWorkFLowClick = "false";
    WorkFlowUnChanged();
    //alert("hi1")
    var LastLockWFActionName = sessionStorage.getItem("LastLockWFActionName");
    if ($("[id$=loginuser]").val() != "SuperAdmin") {
        if (LastLockWFActionName != "" && LastLockWFActionName != null && LastLockWFActionName != undefined) {
            var arrayActionName = LastLockWFActionName.split(',');
            for (var i = 0; i < arrayActionName.length; i++) {
                $("[id$=ddlLoadProperty] option[value='" + arrayActionName[i] + "']").remove();
            }
            sessionStorage.setItem("LastLockWFActionName", "");
        }
        //$("[id$=ddlLoadProperty]").chosen();
        $("[id$=ddlLoadProperty]").trigger("chosen:updated");
    }
    //alert("hi2")
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/CreateNewWorkFlow",
        data: "{}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        sync: true,
        success: function (msg) {
            removeItem();
            GlobalWorkFlowID = '';
            globalWorkFlowOperation = "New";
            GlobalWorkFlowProperty = "";
            WorkFlowName = "";
            globalId = 0;
            //            $("#ddlLoadProperty").val("00").attr('selected', true);
            //            $("#ddlActionSetList").val("00").attr('selected', true);
            //$("[id$=ddlLoadProperty]").val("000");
            //$("[id$=ddlActionSetList]").val("00");
            $(propertyObj).hide(500);
            $(conditionDiv).hide(500);
            $("#idWorfkflowName").html("Workflow Editor");
        }
    });
}

function ElementCount() {
    var thisOne = document.getElementById("dropBox");
    var items = thisOne.getElementsByTagName("div");
    //var hdCountObj = items.length;
    return items.length;
}


//$("#box1").click(function () {
$(document).on("click", "#box1", function () {
    var workflowName = $('#idWorfkflowName').text();
   // var selectedtext = $("[id$=ddlLoadProperty] option[value=" + $("[id$=ddlLoadProperty]").val() + "]").text();
    var selectedtext = $("[id$=ddlLoadProperty] option:selected").textContent;

    //var multiDropdown = document.getElementById("ctl00_cphBody_ddlLoadProperty");
    //var selectedtext = (multiDropdown.options[multiDropdown.selectedIndex]).text;


    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/ActionAddDeleteLog",
        data: "{'actionName':'" + selectedtext + "','workflowName':'" + workflowName + "','addDelete':'Add'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (msg) {
        },
        error: function (msg) {
        }
    });

    $(this).hide();
    $("#disableBox").show();
    var t = setTimeout(ChangeButtonType, 500);
    dropItems();
    if ($("#chkMarkParallel").attr('checked') == "checked") {
        $('.one').each(function () {
            if ($(this).next().hasClass('small')) {
            }
            else {
                $(this).parent().append("<div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox'/></div>");
                $('input[type="checkbox"]').checkbox();
            }

        });
    }
    if ($(".one").length > 0) {
        // $("#btnCondition").attr("disabled", false);
        $("#btnCondition").show();
        $("#btnConditionDisable").hide();
    } else {
        // $("#btnCondition").attr("disabled", true);
        $("#btnCondition").hide();
        $("#btnConditionDisable").show();
    }
    addActionBetweenConditionAndSelectedAction();
});

function addActionBetweenConditionAndSelectedAction() {
    var selectedActionId = $("#ddlSelectedWFProperty option:selected").attr('actionid');
    var chkDivCondi = 0;
    allActionNamesForAddCondition = "";
    getActionIdWhenConditionAdd();
    var tempActionsForAddCondi = allActionNamesForAddCondition.split(',');
    var removeActionList = allActionNamesForAddCondition.split(',');
    for (var a = 0; a < removeActionList.length; a++) {
        var divIndex = 0;
        var targetid = 0;
        var middleId = 0;
        if (removeActionList[a].indexOf("divCondi") != -1) {
            var splitActionId = removeActionList[a].split('^');
            divIndex = a;
            for (var b = 0; b < removeActionList.length; b++) {
                var splitActionIdForTargetId = removeActionList[b].split('^');
                if (splitActionId[1] == splitActionIdForTargetId[0]) {
                    targetid = b;
                }
            }
            for (var c = 0; c < removeActionList.length; c++) {
                var splitActionIdForMiddleId = removeActionList[c].split('^');
                if (selectedActionId == splitActionIdForMiddleId[0]) {
                    middleId = c;
                }
            }
            if (divIndex < middleId && middleId < targetid) {
                calculateActionHeightBetweenConditionAndSelectedAction(splitActionId[0]);
            }
            else if (middleId < divIndex && targetid <= middleId) {
                calculateActionHeightBetweenConditionAndSelectedAction(splitActionId[0]);
            }
        }
    }
}

function calculateActionHeightBetweenConditionAndSelectedAction(conditionDivIdWhenAddCondi) {
    var parentIdWhenAddCondi = $("#" + conditionDivIdWhenAddCondi).parent();
    var conditionDivIdWhenAddCondi = conditionDivIdWhenAddCondi;
    $(parentIdWhenAddCondi).children('.line').remove();
    var attrTotalheight = $("#" + conditionDivIdWhenAddCondi).attr('totalheight');
    var attrActiondifferenceWhenAddCondi = $("#" + conditionDivIdWhenAddCondi).attr('actiondifference');
    var totalheightWhenAddCondi;
    if (loopCnt == 0) {
        totalheightWhenAddCondi = parseInt(attrTotalheight) + 32;
        loopCnt++;
    }
    else {
        totalheightWhenAddCondi = parseInt(attrTotalheight) + 32;
    }
    drwaConditionLineInUpAndDownDirection(attrActiondifferenceWhenAddCondi, parentIdWhenAddCondi, totalheightWhenAddCondi, conditionDivIdWhenAddCondi);
    //if (parseInt(attrActiondifferenceWhenAddCondi) < 0)
    //{
    //    $("<div class='line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "269px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "float": "right",
    //        "right": "21px",
    //        "color": "red"
    //    }).text("Failure").appendTo(parentIdWhenAddCondi);
    //    $("<div class='line'>").css({
    //        "height": parseInt(totalheightWhenAddCondi),
    //        "background": "#707070",
    //        "width": "1px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "float": "right",
    //        "right": "20px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("<div class='downthirdline line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "69px",
    //        "position": "absolute",
    //        "margin-top": parseInt(totalheightWhenAddCondi) + (-27),
    //        "right": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    //}
    //else if (parseInt(attrActiondifferenceWhenAddCondi) > 0)
    //{
    //    $("<div  class='line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "269px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "left": "21px",
    //        "color": "red"
    //    }).text("Failure").appendTo(parentIdWhenAddCondi);
    //    $("<div class='line'>").css({
    //        "height": totalheightWhenAddCondi,
    //        "background": "#707070",
    //        "width": "1px",
    //        "position": "absolute",
    //        "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
    //        "float": "right",
    //        "left": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("<div class='upthirdline line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "69px",
    //        "position": "absolute",
    //        "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
    //        "left": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    //}
}

function ChangeButtonType() {
    $("#box1").show();
    $("#disableBox").hide();
}
var dropboxClick = 0;
//$("#dropBox").mousemove(function (event) {
//    $(this).text("X: " + event.pageX + ", Y: " + event.pageY);
//});
//$("#dropBox").on("click", function (e) {
//    dropboxClick++;
//    if (dropboxClick % 2 != 0) {
//        firstTop = e.pageY - 200;
//        firstLeft = e.pageX - 702;
//    }
//    else {
//        secoundTop = e.pageY - 310;
//        secoundLeft = e.pageX - 702;
//        heightdifference = secoundTop - firstTop;
//        widthdifference = secoundLeft - firstLeft;
//        var width = heightdifference + widthdifference;
//        $("<div>").css({
//            "height": width,
//            "background": "black",
//            "width": "2px",
//            "position": "absolute",
//            "top": firstTop,
//            "right": "30px"

//        }).appendTo("#dropBox")
//    }
//});
function dropItems() {
    var totalElement = ElementCount();
    var value = "";
    var id = "";
    var profileId = "";
    var place = $("#ddlPlace option:selected").val();
    //var ddlLoadPropertyValue = $("[id$=ddlLoadProperty] option:selected").val();
    var ddlLoadPropertyValue = $("[id$=ddlLoadProperty]").val();
    var append = $("[id$=ddlSelectedWFProperty]").val();
    //var profileId = $("[id$=ddlLoadProperty] option:selected").attr("profile");
    //var profileId = $("select[id$=ddlactionTypeBase]option:selected").val();
    var profileId = $("select[id$=ddlactionTypeBase]").val();
    var divMain = "divMain" + globalId;
    if (ddlLoadPropertyValue != "00" && ddlLoadPropertyValue != "000") {
        id = ddlLoadPropertyValue;
        value = $("[id$=ddlLoadProperty] option:selected").text();
        //value = $("[id$=ddlLoadProperty] option[value=" + $("[id$=ddlLoadProperty]").val() + "]").text();
        if (globalId == 0) {
            $("<div class='Property' id='" + divMain + "'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /></div><div class='one' id='" + id + "' profile='" + profileId + "' > <img  src='../images/icons/clock.png' alt='' />" + value + "  </div></div>").appendTo("div#dropBox");
        }
        else {
            if (place == "0") {
                $("#" + append).before("<div class='Property' id='" + divMain + "'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /></div><div class='one' id='" + id + "'  profile='" + profileId + "'><img  src='../images/icons/clock.png' />" + value + " </div></div>");
            }
            else {
                $("#" + append).after("<div class='Property' id='" + divMain + "'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /></div><div class='one' id='" + id + "'  profile='" + profileId + "'><img  src='../images/icons/clock.png' />" + value + " </div></div>");
            }
        }
        $('input[type="checkbox"]').checkbox();
        changeGlobalId(divMain, value);
        WorkFlowChange();
    }
    else {
        //var ddlActionSetValue = $("[id$=ddlActionSetList] option:selected").val();
        var ddlActionSetValue = $("[id$=ddlActionSetList]").val();
        if (ddlActionSetValue != "00" && ddlActionSetValue != "000") {
            id = ddlActionSetValue;
            value = $("[id$=ddlActionSetList] option:selected").text();
            //value = $("[id$=ddlActionSetList] option[value=" + $("[id$=ddlActionSetList]").val() + "]").text();
            if (globalId == 0) {
                $("<div class='PropertySet' id='" + divMain + "'><div class='actionboxline'></div><div class='one' id='" + id + "'  profile='" + profileId + "'>" + value + " <img Class='close' src='../images/icons/remove-icon-small.png' /></div></div>").appendTo("div#dropBox");
            }
            else {
                if (place == "0") {
                    $("#" + append).before("<div class='PropertySet' id='" + divMain + "'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /></div><div class='one' id='" + id + "'  profile='" + profileId + "'>" + value + " <img Class='close' src='../images/icons/remove-icon-small.png' /></div></div>");
                }
                else {
                    $("#" + append).after("<div class='PropertySet' id='" + divMain + "'  ><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /></div><div class='one' id='" + id + "'  profile='" + profileId + "'>" + value + " <img Class='close' src='../images/icons/remove-icon-small.png' /></div></div>");
                }
            }
            changeGlobalId(divMain, value);
            WorkFlowChange();
        }
    }
}

function changeGlobalId(id, value) {
    $("#ddlSelectedWFProperty").append("<option value='" + id + "' selected='selected'>" + value + "</option>");
    globalId = globalId + 1;
    SelectedDiv(id);
    // RearrengeDdl(id);
    //var selectedDivId = id;
}



function removeItem() {
    $('.one,.diamond').each(function () {
        var parentTag = $(this).parents().get(0);
        $(parentTag).remove();
    });
    globalId = "ParentId" + 0;
    $("#ddlSelectedWFProperty option").remove();
    $("#txtRTO").val("");
    $("#txtRTO").attr("disabled", false);
}



function CreateWorkFlowDiagrame(result) {
    let closeImgPath = "";
    let CloseClass = "";
    if ($("#ctl00_cphBody_HiddenFielddelete").val() == "allowDelete") {
        closeImgPath = '../images/icons/remove-icon-small.png';
        CloseClass = 'close';
    }





    //  $("#btnCondition").attr("disabled", false);
    $("#btnCondition").show();
    $("#btnConditionDisable").hide();
    SelectedWorkFlowProperties(result);
    globalWorkFlowOperation = "Update";
    var workflowRunningType = "";
    if (result != undefined) {
        $("#chkMarkParallel").attr("disabled", false);
        var resultArray = result.split(":");
        for (var i = 0; i < resultArray.length; i++) {
            if (i == 0) {
                var workflowNameReturnValue = resultArray[i].split('^');
                // WorkFlowName = resultArray[i].split('^');
                WorkFlowName = workflowNameReturnValue[0];
                $("#idWorfkflowName").html(WorkFlowName);
                $("#idWorfkflowName").attr('title', WorkFlowName);
                workflowRunningType = workflowNameReturnValue[1];
            }
            else {
                var setProperties = resultArray[i].split(",");
                if (setProperties[1].indexOf("divCondi") != -1) {
                    var parId = setProperties[1];
                    $("<div class='Property' id='myDivCondition'><div class='actionboxline'></div><div class='diamond' id='" + setProperties[1] + "'><div class='diamondtext'>" + 1 + "</div><img Class='" + CloseClass + "' src='" + closeImgPath + "' style=margin-top:-24px/><div style='visibility:hidden'>" + setProperties[1] + "</div></div><div class='diamond-line'>Success</div></div>").appendTo("div#dropBox");
                    var par11 = $("#" + parId).parent();
                    if (setProperties[3] < 0) {
                        $("<div class='line'>").css({
                            "height": "1px",
                            "background": "#707070",
                            "width": "269px",
                            "position": "absolute",
                            "margin-top": "-27px",
                            "float": "right",
                            "right": "21px",
                            "color": "red"
                        }).text("Failure").appendTo(par11);
                        $("<div class='line'>").css({
                            "height": parseInt(setProperties[2]),
                            "background": "#707070",
                            "width": "1px",
                            "position": "absolute",
                            "margin-top": "-27px",
                            "float": "right",
                            "right": "20px"
                        }).appendTo(par11);
                        $("<div class='downthirdline line'>").css({
                            "height": "1px",
                            "background": "#707070",
                            "width": "69px",
                            "position": "absolute",
                            "margin-top": parseInt(setProperties[2]) + (-27),
                            "right": "21px"
                        }).appendTo(par11);
                        $("#" + parId).attr('actionDifference', setProperties[3]);
                        $("#" + parId).attr('totalHeight', setProperties[2]);
                        $("#" + parId).attr('targetAction', setProperties[4]);
                        $("#" + parId).attr('failedcount', setProperties[5]);
                    }
                    else if (setProperties[3] > 0) {
                        $("<div  class='line'>").css({
                            "height": "1px",
                            "background": "#707070",
                            "width": "269px",
                            "position": "absolute",
                            "margin-top": "-27px",
                            "left": "21px",
                            "color": "red"
                        }).text("Failure").appendTo(par11);
                        $("<div class='line'>").css({
                            "height": parseInt(setProperties[2]),
                            "background": "#707070",
                            "width": "1px",
                            "position": "absolute",
                            "margin-top": "-" + (27 + parseInt(setProperties[2])) + "px",
                            "float": "right",
                            "left": "21px"
                        }).appendTo(par11);
                        $("<div class='upthirdline line'>").css({
                            "height": "1px",
                            "background": "#707070",
                            "width": "69px",
                            "position": "absolute",
                            "margin-top": "-" + (27 + parseInt(setProperties[2])) + "px",
                            "left": "21px"
                        }).appendTo(par11);
                        $("#" + parId).attr('actionDifference', setProperties[3]);
                        $("#" + parId).attr('totalHeight', setProperties[2]);
                        $("#" + parId).attr('targetAction', setProperties[4]);
                        $("#" + parId).attr('failedcount', setProperties[5]);
                    }
                }
                else {
                    $("<div class='" + setProperties[0] + "' id='" + globalId + "'><div class='actionboxline'><img src='../images/line-workflow.png' alt='' /></div><div class='one' id='" + setProperties[1] + "' profile='" + setProperties[3] + "'>" + setProperties[2] + " </div></div>").appendTo("div#dropBox");  //<img Class='" + CloseClass + "'  src='" + closeImgPath + "' />


                    $("#" + globalId + "").children().next().attr('totalHeight', setProperties[4]);
                    $("#" + globalId + "").children().next().attr('actionDifference', setProperties[5]);
                    $("#" + globalId + "").children().next().attr('targetAction', setProperties[6]);
                    $("#" + globalId + "").children().next().attr('runParallel', setProperties[7]);
                    $("#" + globalId + "").children().next().attr('failedcount', setProperties[8]);

                    if (workflowRunningType == "True") {
                        var cntParallel = 0
                        if (cntParallel == 0) {
                            $("#chkMarkParallel").attr('checked', true);
                            $("#chkMarkParallel").next().children().children('span').css('display', 'none');
                            $("#chkMarkParallel").next().children().children('span.cb-icon-check').css('display', 'block');
                            $("#btnActionDelete").attr('disabled', false);
                            $("#btnMarkparallel").attr('disabled', false);

                            cntParallel++;
                        }
                        //if (setProperties[7].contains("parallelRun")) {
                        //    //$("#dropBox #" + globalId + "").append("<div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox' checked ='checked' /></div>");
                        //    $("#dropBox #" + globalId + "").append("<div class='divparallel'><label id='lblparallel' class='absoluteWrap' style='margin-top: -24px;right: 122px;'>P</label></div><div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox' checked ='checked' /></div>");
                        //}
                        //else {
                        //    $("#dropBox #" + globalId + "").append("<div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox'/></div>");
                        //}

                        if (navigator.userAgent.match(/Trident\/7\./)) {
                            if (setProperties[7].indexOf("parallelRun") != -1) {

                                $("#dropBox #" + globalId + "").append("<div class='divparallel'><label id='lblparallel' class='absoluteWrap' style='margin-top: -24px;right: 122px;'>P</label></div><div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox' checked ='checked' /></div>");
                            }
                            else {
                                $("#dropBox #" + globalId + "").append("<div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox'/></div>");
                            }
                        }
                        else if ($.browser.chrome) {

                            if (setProperties[7].indexOf("parallelRun") != -1) {

                                $("#dropBox #" + globalId + "").append("<div class='divparallel'><label id='lblparallel' class='absoluteWrap' style='margin-top: -24px;right: 122px;'>P</label></div><div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox' checked ='checked' /></div>");
                            }
                            else {
                                $("#dropBox #" + globalId + "").append("<div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox'/></div>");
                            }

                        } else if ($.browser.mozilla) {

                            //if (setProperties[7].contains("parallelRun")) {
                            if (setProperties[7].indexOf("parallelRun") != -1) {

                                $("#dropBox #" + globalId + "").append("<div class='divparallel'><label id='lblparallel' class='absoluteWrap' style='margin-top: -24px;right: 122px;'>P</label></div><div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox' checked ='checked' /></div>");
                            }
                            else {
                                $("#dropBox #" + globalId + "").append("<div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox'/></div>");
                            }

                        }
                        else if ($.browser.msie) {
                            if (setProperties[7].indexOf("parallelRun") != -1) {

                                $("#dropBox #" + globalId + "").append("<div class='divparallel'><label id='lblparallel' class='absoluteWrap' style='margin-top: -24px;right: 122px;'>P</label></div><div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox' checked ='checked' /></div>");
                            }
                            else {
                                $("#dropBox #" + globalId + "").append("<div class='small'><input type='checkbox' class='absoluteWrap z-index actioncheckbox'/></div>");
                            }

                        }
                        $('input[type="checkbox"]').checkbox();
                    }
                    else {
                        $("#btnActionDelete").attr('disabled', true);
                        $("#btnMarkparallel").attr('disabled', true);
                    }
                }
                var increment = globalId.substring(8);
                var c = parseInt(increment);
                var test = c + 1;
                globalId = "ParentId" + test;


                if ($("#ctl00_cphBody_ddlLoadProperty option[value='" + setProperties[1] + "']").length < 1) {

                    $('select[name$=ddlLoadProperty]').append("<option value='" + setProperties[1] + "' profile='" + setProperties[3] + "'>" + setProperties[2] + "</option>");
                }
            }
        }

        var parentTag = $('.one').parents().get(0);
        var id = $(parentTag).attr("id");
        if ((id != null || typeof (id) != 'undefined')) {
            SelectedDiv(id);
            // RearrengeDdl(id);
        }
    }
}


$(".one > .close").live("click", function () {
    var parentTag = $(this).parents().get(1);
    var id = $(parentTag).attr("id");
    OpenAlertModelConfirmationClose(id);
    $(".modal-dialog").css('width', '350px');
    $(".modal .modal-footer").css('padding', '5px');
    $(".modal .modal-header .modal-title").css('line-height', '29px');
    $(".close").css('font-size', '28px');
});

function OpenAlertModelConfirmationClose(id) {

    // $(".modal-dialog").width(200);
    $.modal({
        content: '<div class="row"> ' +
            '<div class="col-md-3 text-right"><img src="../images/icons/Warning.png"/></div> ' +
            '<div class="col-md-9 padding-none-LR" style="padding-top: 5px;"> Are you sure to delete this activity ?  </div>' +
            '</div>',
        title: 'Confirmation',
        width: 300,

        buttons: {
            'Yes': function (win) {
                DeleteActivityConform(win, id);
            },
            'No': function (win) {
                CloseModel(win);
            }
        },

    });

}

function DeleteActivityConform(win, id) {
    WorkFlowChange();
    deleteActionBetweenCondition();

    var actionName = $('#' + id + ' > :nth-child(2)').text();
    var workflowName = $('#idWorfkflowName').text();

    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/ActionAddDeleteLog",
        data: "{'actionName':'" + actionName + "','workflowName':'" + workflowName + "','addDelete':'Delete'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (msg) {
        },
        error: function (msg) {
        }
    });

    $("#" + id).remove();
    var parentTag = $('.one').parents().get(0);
    id = $(parentTag).attr("id");
    $("#btnCancelEdit").hide();
    $("#btnEdit").show();
    $("#btnPropertySave").hide();

    if ((id != null || typeof (id) != 'undefined')) {
        SelectedDiv(id);
        RearrengeDdl(id);
    }
    else {
        $("#ddlSelectedWFProperty option").remove();
        $("#ddlSelectedWFProperty").append("<option value='00' selected='selected'> Select Property </option>");
        globalId = 0;
    }
    CloseModel(win);
    if ($(".one").length > 0) {
        //$("#btnCondition").attr("disabled", false);
        $("#btnCondition").show();
        $("#btnConditionDisable").hide();
    } else {
        //$("#btnCondition").attr("disabled", true);
        $("#btnCondition").hide();
        $("#btnConditionDisable").show();
    }
}

function deleteActionBetweenCondition() {
    var IdOfWhichConditionDelete = $("#ddlSelectedWFProperty option:selected").attr('actionid');
    getActionIdWhenConditionAdd();
    var removeActionList = allActionNamesForAddCondition.split(',');

    for (var a = 0; a < removeActionList.length; a++) {
        var divIndex = 0;
        var targetid = 0;
        var middleId = 0;
        if (removeActionList[a].indexOf("divCondi") != -1) {
            var splitActionId = removeActionList[a].split('^');
            divIndex = a;
            for (var b = 0; b < removeActionList.length; b++) {
                var splitActionIdForTargetId = removeActionList[b].split('^');
                if (splitActionId[1] == splitActionIdForTargetId[0]) {
                    targetid = b;
                }
            }
            for (var c = 0; c < removeActionList.length; c++) {
                var splitActionIdForMiddleId = removeActionList[c].split('^');
                if (IdOfWhichConditionDelete == splitActionIdForMiddleId[0]) {
                    middleId = c;
                }
            }
            if (divIndex < middleId && middleId <= targetid) {
                calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(splitActionId[0]);
            }
            else if (middleId < divIndex && targetid <= middleId) {
                calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(splitActionId[0]);
            }
        }
    }
}

function calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(conditionDivIdWhenAddCondi) {
    if (chkReturnOrNot) {
        return;
    }
    var parentIdWhenAddCondi = $("#" + conditionDivIdWhenAddCondi).parent();
    var conditionDivIdWhenAddCondi = conditionDivIdWhenAddCondi;
    $(parentIdWhenAddCondi).children('.line').remove();
    var attrTotalheight = $("#" + conditionDivIdWhenAddCondi).attr('totalheight');
    var attrActiondifferenceWhenAddCondi = $("#" + conditionDivIdWhenAddCondi).attr('actiondifference');
    var totalheightWhenAddCondi;
    if (loopCnt == 0) {
        totalheightWhenAddCondi = parseInt(attrTotalheight) - 32;
        loopCnt++;
    }
    else {
        totalheightWhenAddCondi = parseInt(attrTotalheight) - 32;
    }
    drwaConditionLineInUpAndDownDirection(attrActiondifferenceWhenAddCondi, parentIdWhenAddCondi, totalheightWhenAddCondi, conditionDivIdWhenAddCondi);
    //if (parseInt(attrActiondifferenceWhenAddCondi) < 0)
    //{
    //    $("<div class='line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "269px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "float": "right",
    //        "right": "21px",
    //        "color": "red"
    //    }).text("Failure").appendTo(parentIdWhenAddCondi);
    //    $("<div class='line'>").css({
    //        "height": parseInt(totalheightWhenAddCondi),
    //        "background": "#707070",
    //        "width": "1px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "float": "right",
    //        "right": "20px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("<div class='downthirdline line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "69px",
    //        "position": "absolute",
    //        "margin-top": parseInt(totalheightWhenAddCondi) + (-27),
    //        "right": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    //}
    //else if (parseInt(attrActiondifferenceWhenAddCondi) > 0)
    //{
    //    $("<div  class='line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "269px",
    //        "position": "absolute",
    //        "margin-top": "-27px",
    //        "left": "21px",
    //        "color": "red"
    //    }).text("Failure").appendTo(parentIdWhenAddCondi);
    //    $("<div class='line'>").css({
    //        "height": totalheightWhenAddCondi,
    //        "background": "#707070",
    //        "width": "1px",
    //        "position": "absolute",
    //        "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
    //        "float": "right",
    //        "left": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("<div class='upthirdline line'>").css({
    //        "height": "1px",
    //        "background": "#707070",
    //        "width": "69px",
    //        "position": "absolute",
    //        "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
    //        "left": "21px"
    //    }).appendTo(parentIdWhenAddCondi);
    //    $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    //}
}

function SelectedWorkFlowProperties(result) {
    var cell = document.getElementById("ddlSelectedWFProperty");
    if (cell.hasChildNodes()) {
        while (cell.childNodes.length >= 1) {
            cell.removeChild(cell.firstChild);
        }
    }
    if (result != undefined) {
        var splitArray = result.split(":");
        opt = document.createElement("option");
        cell.options.add(opt);
        opt.text = "-Select-";
        i++;
        opt.value = "--000--";
        for (var i = 0; i < splitArray.length; i++) {
            if (i != 0) {
                var valueOfCell = splitArray[i].split(',');
                var opt = document.createElement("option");
                cell.options.add(opt);
                var name = valueOfCell[2];
                opt.text = name;
                var workFLowID = i;
                opt.value = workFLowID;
            }
        }
    }
}

$("#btnSave").click(function () {
    //$("#btnSave").attr("disabled", true);
    if ($('#dropBox .Property').length == 0) {
        OpenAlertModelAlert("First add workflow action in Workflow!!!");

        $("#btnhistory").show();
        $(".modal-dialog").css('width', '350px');
        $(".modal-footer").css('padding', '5px');
        $(".modal-header .modal-title").css('line-height', '29px');
        $(".close").css({ "font-size": "28px", "color": "#fff" });
        $(".col-md-12").css('text-align', 'center');
        return false;
    }
    if (globalWorkFlowOperation == "New") {
        ModalPopupsPrompt();
        // globalWorkFlowOperation = "Update";
    }
    else {
        SaveWorkflow("Generic");
    }
});

$("#btnSaveAsdraft").click(function () {
    if ($('#dropBox .Property').length == 0) {
        OpenAlertModelAlert("First add workflow action in Workflow!!!");
        $(".modal-dialog").css('width', '350px');
        $(".modal-footer").css('padding', '5px');
        $(".modal-header .modal-title").css('line-height', '29px');
        $(".close").css({ "font-size": "28px", "color": "#fff" });
        $(".col-md-12").css('text-align', 'center');
        return false;
    }
    if (globalWorkFlowOperation == "New") {
        ModalPopupsPrompt();
        // globalWorkFlowOperation = "Update";
    }
    else {
        SavedraftWorkflow("Generic");
    }
});

$("#btnSaveAs").click(function () {
    if ($('#dropBox .Property').length == 0) {
        OpenAlertModelAlert("First add workflow action in Workflow!!!");
        $(".modal-dialog").css('width', '350px');
        $(".modal-footer").css('padding', '5px');
        $(".modal-header .modal-title").css('line-height', '29px');
        $(".close").css({ "font-size": "28px", "color": "#fff" });
        $(".col-md-12").css('text-align', 'center');
        return false;
    }
    SaveAsModalPopupsPrompt();
});

function SaveAsModalPopupsPrompt() {
    OpenAlertModelLoad("<div class='col-md-4'> WorkFlow Name</div><div class='col-md-8 padding-none-LR'> <input id='idSaveAs' type=text class='form-control' style='width:80%;' /><span id='idSaveAsSpan'></span></div>", SaveAsModalPopupsPromptOk);
    $(".modal-dialog").css('width', '420px');
    $(".modal-footer").css('padding', '5px');
    $(".modal-header .modal-title").css('line-height', '29px');
    $(".close").css({ "font-size": "28px", "color": "#fff" });

}

function SaveAsModalPopupsPromptOk(win) {
    if (document.getElementById("idSaveAs").value == "") {
        document.getElementById("idSaveAs").focus();
        return;
    }
    var nname = document.getElementById("idSaveAs").value;

    // SaveWorkflow(nname);
    var span1 = $("#idSaveAsSpan");
    // var alphaExp = /^[a-zA-Z\s]+$/;
    var alphaExp = /^[0-9]/;
    if ($("#idSaveAs").val().match(alphaExp)) {
        $(span1).show();
        $(span1).html("Start With Numeric Workflow Name Not Allowed");
        $(span1).attr("class", "error");
        return false;
    }
    else { win.closeModal(); SaveAsWorkflow(nname); }


}

function AssignValue(result) {
    var splitArray = result.split(":");
    for (var i = 0; i < txtObj.length; i++) {
        if (txtObj[i] == "txtExecMode") {
            $("#txtExecMode").val(splitArray[i]).attr('selected', true);
        }
        else {
            if (txtObj[i] == "ddlHost") {
                $('[id$=ddlHost]').val(splitArray[i]).attr('selected', true);
            }
            else {
                document.getElementById(txtObj[i]).value = splitArray[i];
            }
        }
    }
    txtName = document.getElementById(txtObj[0]).value;
    $("#hdActivityName").val(txtName);
    DisablePropertyElement();
}
var _actid = "00";
var _actname = "Select Activity";

$(".one").live("click", function () {
    var parentTag = $(this).parents().get(0);
    var id = $(parentTag).attr("id");
    SelectedDiv(id);
    //var selectedDivId = increment[1];
    var getType = $(this).parent().attr("class");
    var parentID = $(this).parent().attr("Id");
    $("#ddlSelectedWFProperty").val(parentID).attr('selected', true);
    GlobalWorkFlowProperty = this.id;
});
$(".diamond").live("click", function () {
    var span1 = $("#txtbxFailureCountSpan");
    span1.html("");


    var selectedDiamondId = $(this).attr('Id');
    SelectedDiv(selectedDiamondId);
    $(conditionDiv).show(500);
    //$("[id$=ddlLoadProperty] option:selected").val(000);
    $("[id$=ddlLoadProperty]").val("000");
    $("#txtRTO").val("");
    $('.one').each(function () {
        $(this).css("border", "1px solid #707070");
        $(this).css("background-color", "#FAFAFA");
        $(this).css("color", "#707070");
        //i++;
    });
    $('.diamond').each(function () {
        var abc = this;
        $(this).css("border", "1px solid #707070");
        $(this).css("background-color", "#FAFAFA");
        $(this).css("color", "#707070");
        //i++;
    });
    $("#" + selectedDiamondId).css("border", "1px solid #0081C2 ");
    $("#" + selectedDiamondId).css("background-color", "#aac8e2");
    $("#" + selectedDiamondId).css("color", "#111");
});

//$(".one").live("click", function(){

//    var parentTag = $(this).parents().get(0);
//    var id = $(parentTag).attr("id");
//    GlobalWorkFlowCurrentID = $(this).find('span').attr('id');
//    //alert(GlobalWorkFlowCurrentID);
//    SelectedDiv(id);
//    var selectedDivId = increment[1];
//    var getType = $(this).parent().attr("class");
//    var parentID = $(this).parent().attr("Id");
//    $("#ddlSelectedWFProperty").val(parentID).attr('selected', true);
//    GlobalWorkFlowProperty = this.id;
//    //alert(GlobalWorkFlowProperty);
//});

function callPropertyfunction(value) {
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/MainFunction",
        cache: true,
        data: "{'args':'GetProperty','parameter':'" + value + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",

        success: function (msg) {
            AssignValue(msg.d);
        }
    });
    $("#divActionSet").hide(1000);
    $(propertyObj).show(1000);
    hideDivError();
}

$("#btnRemoveActivity").click(function () {
    $('#' + GlobalWorkFlowProperty).hide(1000);
    //$('#'+ GlobalWorkFlowProperty ).remove();
});

$(function () {

    //History
    sessionStorage.setItem("workflowidOld", 0);
    sessionStorage.setItem("workflowhistorynameOld", "");

    let editDivClasss = "";
    if ($("#ctl00_cphBody_HiddenFieldeditt").val() == "allowedit") {

        $("#dropBox").sortable({
            stop: function (event, ui) {
                $(ui.item).find('.actionboxline').show();
                SelectedDiv($(ui.item).attr("id"));
                RearrengeDdl($(ui.item).attr("id"));
                WorkFlowChange();
                //alert($(ui.item).children().eq(1).attr("id"));
                var draggedActionId = $(ui.item).children().eq(1).attr("id");
                if (draggedActionId.indexOf("divCondi") != -1) {
                    dropConditionBetweenCondition(draggedActionId);
                    return;
                }
                dropActionBetweenConditionAndSelectedAction($(ui.item).children().eq(1).attr("id"));
            },
            start: function (event, ui) {
                $(ui.item).find('.actionboxline').hide();
                // alert($(ui.item).children().eq(1).attr("id"));
                var draggedActionId = $(ui.item).children().eq(1).attr("id");
                if (draggedActionId.indexOf("divCondi") != -1) {
                    $("#" + draggedActionId).parent().children('.line').remove();
                    $('#' + draggedActionId).parent().prev().children('.one').removeAttr('targetaction');
                    $('#' + draggedActionId).removeAttr('targetaction');
                    $('#' + draggedActionId).removeAttr('actiondifference');
                    $('#' + draggedActionId).removeAttr('totalheight');
                    dragConditionBetweenCondition(draggedActionId);
                    //$("#" + draggedActionId).children('.diamondtext').html("");
                    return;
                }
                dragActionDBetweenCondition($(ui.item).children().eq(1).attr("id"));
            }
        });
        $("#dropBox").disableSelection();
    }
});

function dragConditionBetweenCondition(draggedActionId) {
    var IdOfWhichConditionDelete = draggedActionId;
    getActionIdWhenConditionAdd();
    var removeActionList = allActionNamesForAddCondition.split(',');
    for (var a = 0; a < removeActionList.length; a++) {
        var divIndex = 0;
        var targetid = 0;
        var middleId = 0;
        if (removeActionList[a].indexOf("divCondi") != -1) {
            var splitActionId = removeActionList[a].split('^');
            divIndex = a;
            for (var b = 0; b < removeActionList.length; b++) {
                var splitActionIdForTargetId = removeActionList[b].split('^');
                if (splitActionId[1] == splitActionIdForTargetId[0]) {
                    targetid = b;
                }
            }
            for (var c = 0; c < removeActionList.length; c++) {
                var splitActionIdForMiddleId = removeActionList[c].split('^');
                if (IdOfWhichConditionDelete == splitActionIdForMiddleId[0]) {
                    middleId = c;
                }
            }
            if (divIndex <= middleId && middleId <= targetid) {
                calculateHeightForDeleteCondition(splitActionId[0]);
            }
            else if (middleId < divIndex && targetid <= middleId) {
                calculateHeightForDeleteCondition(splitActionId[0]);
            }
        }
    }
}

function dropConditionBetweenCondition(draggedActionId) {
    var selectedActionId = draggedActionId;
    var chkDivCondi = 0;
    allActionNamesForAddCondition = "";
    getActionIdWhenConditionAdd();
    var tempActionsForAddCondi = allActionNamesForAddCondition.split(',');
    var removeActionList = allActionNamesForAddCondition.split(',');
    for (var a = 0; a < removeActionList.length; a++) {
        var divIndex = 0;
        var targetid = 0;
        var middleId = 0;
        if (removeActionList[a].indexOf("divCondi") != -1) {
            var splitActionId = removeActionList[a].split('^');
            divIndex = a;
            for (var b = 0; b < removeActionList.length; b++) {
                var splitActionIdForTargetId = removeActionList[b].split('^');
                if (splitActionId[1] == splitActionIdForTargetId[0]) {
                    targetid = b;
                }
            }
            for (var c = 0; c < removeActionList.length; c++) {
                var splitActionIdForMiddleId = removeActionList[c].split('^');
                if (selectedActionId == splitActionIdForMiddleId[0]) {
                    middleId = c;
                }
            }
            if (splitActionId[1] != "undefined") {
                if (divIndex < middleId && middleId < targetid) {
                    calculateHeight(splitActionId[0]);
                }
                else if (middleId < divIndex && targetid <= middleId) {
                    calculateHeight(splitActionId[0]);
                }
            }
        }
    }

    //getActionIdWhenConditionAdd();
    ////getActionNameWhenConditionAdd();
    //var tempActionsForAddCondi = allActionNamesForAddCondition.split(',');
    //var removeActionList = allActionNamesForAddCondition.split(',');
    ////alert(tempActionsForAddCondi);
    //for (var a = 0; a < removeActionList.length; a++) {
    //    var divIndex = 0;
    //    var targetid = 0;
    //    var middleId = 0;
    //    if (removeActionList[a].contains("divCondi")) {
    //        var splitActionId = removeActionList[a].split('^');
    //        divIndex = a;
    //        for (var b = 0; b < removeActionList.length; b++) {
    //            var splitActionIdForTargetId = removeActionList[b].split('^');
    //            if (splitActionId[1] == splitActionIdForTargetId[0]) {
    //                targetid = b;
    //            }
    //        }
    //        for (var c = 0; c < removeActionList.length; c++) {
    //            var splitActionIdForMiddleId = removeActionList[c].split('^');
    //            if (selectedActionId == splitActionIdForMiddleId[0]) {
    //                middleId = c;
    //            }
    //        }
    //        if (divIndex < middleId && middleId < targetid) {
    //            calculateHeight(splitActionId[0]);
    //        }
    //        else if (middleId < divIndex && targetid <= middleId) {
    //            calculateHeight(splitActionId[0]);
    //        }
    //    }
    //}
}

function dragActionDBetweenCondition(draggedActionId) {
    var IdOfWhichConditionDelete = draggedActionId;
    getActionIdWhenConditionAdd();
    var removeActionList = allActionNamesForAddCondition.split(',');
    for (var a = 0; a < removeActionList.length; a++) {
        chkReturnOrNot = false;
        var divIndex = 0;
        var targetid = 0;
        var middleId = 0;
        if (removeActionList[a].indexOf("divCondi") != -1) {
            var splitActionId = removeActionList[a].split('^');
            var targetActionIdForChkSorting = $("#" + splitActionId[0]).attr("targetaction");
            if (targetActionIdForChkSorting == draggedActionId) {
                $('#' + splitActionId[0]).parent().prev().children('.one').removeAttr('targetaction');
                $('#' + splitActionId[0]).removeAttr('targetaction');
                $("#" + splitActionId[0]).parent().children('.line').remove();
                chkReturnOrNot = true;
                //  return;
            }
            divIndex = a;
            for (var b = 0; b < removeActionList.length; b++) {
                var splitActionIdForTargetId = removeActionList[b].split('^');
                if (splitActionId[1] == splitActionIdForTargetId[0]) {
                    targetid = b;
                }
            }
            for (var c = 0; c < removeActionList.length; c++) {
                var splitActionIdForMiddleId = removeActionList[c].split('^');
                if (IdOfWhichConditionDelete == splitActionIdForMiddleId[0]) {
                    middleId = c;
                }
            }
            if (splitActionId[1] != "undefined") {
                if (divIndex < middleId && middleId <= targetid) {
                    calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(splitActionId[0]);
                }
                else if (middleId < divIndex && targetid <= middleId) {
                    calculateHeightWhenDeletingActionBetweenConditionAndSelectedAction(splitActionId[0]);
                }
            }
        }
    }
}

function dropActionBetweenConditionAndSelectedAction(draggedActionId) {
    var selectedActionId = draggedActionId;
    var chkDivCondi = 0;
    allActionNamesForAddCondition = "";
    getActionIdWhenConditionAdd();
    var tempActionsForAddCondi = allActionNamesForAddCondition.split(',');
    var removeActionList = allActionNamesForAddCondition.split(',');
    for (var a = 0; a < removeActionList.length; a++) {
        var divIndex = 0;
        var targetid = 0;
        var middleId = 0;
        if (removeActionList[a].indexOf("divCondi") != -1) {
            var splitActionId = removeActionList[a].split('^');
            divIndex = a;
            for (var b = 0; b < removeActionList.length; b++) {
                var splitActionIdForTargetId = removeActionList[b].split('^');
                if (splitActionId[1] == splitActionIdForTargetId[0]) {
                    targetid = b;
                }
            }
            for (var c = 0; c < removeActionList.length; c++) {
                var splitActionIdForMiddleId = removeActionList[c].split('^');
                if (selectedActionId == splitActionIdForMiddleId[0]) {
                    middleId = c;
                }
            }
            if (splitActionId[1] != "undefined") {
                if (divIndex < middleId && middleId < targetid) {
                    calculateActionHeightBetweenConditionAndSelectedAction(splitActionId[0]);
                }
                else if (middleId < divIndex && targetid <= middleId) {
                    calculateActionHeightBetweenConditionAndSelectedAction(splitActionId[0]);
                }
            }
        }
    }
}

function RearrengeDdl(id) {
    var textValue = "";
    $("#ddlSelectedWFProperty option").remove();
    $("#ddlSelectedWFProperty").append("<option value='00' selected='selected'> Select Property </option>");
    $('.one').each(function () {
        var parentTag = $(this).parents().get(0);
        var id = $(parentTag).attr("id");
        textValue = $(this).text();
        var actionId = $(this).attr('id');
        var profileId = $(this).attr("profile");
        textValue = textValue.substring(textValue.indexOf(".") + 1);
        $("#ddlSelectedWFProperty").append("<option value='" + id + "' profile='" + profileId + "' actionId='" + actionId + "'>" + textValue + "</option>");
    });
    $("#ddlSelectedWFProperty").val(id).attr('selected', true);
}

$("#ddlSelectedWFProperty").change(function () {
    var id = $("[id$=ddlSelectedWFProperty]").val();
    SelectedDiv(id);
});

function ChangeName(propertyName) {
    var txtName = "";
    var i = "";
    if ($("#txtName").val() == "") {
        txtName = $('#hdActivitySetName').val();
        i = 0;
    }
    else {
        txtName = $('#hdActivityName').val();
        i = 1;
    }
    var tempVariable = "";

    $("#ddlSelectedWFProperty option").each(function () {
        txtName = jQuery.trim(txtName);
        tempVariable = jQuery.trim($(this).text());
        if (tempVariable == txtName) {
            $(this).text(propertyName);
            if (i == 0) {
                $('#hdActivitySetName').val(propertyName);
            }
            else {
                $('#hdActivityName').val(propertyName);
            }
        }
    });


    var i = 1;
    var name = "";
    $('.one').each(function () {
        $(this).html($(this).html().replace(/&nbsp;/gi, ''));
        tempVariable = $(this).text();
        tempVariable = tempVariable.substring(tempVariable.indexOf(".") + 3);
        tempVariable = jQuery.trim(tempVariable);
        txtName = jQuery.trim(txtName);
        if (tempVariable == txtName) {
            name = i + ". &nbsp;&nbsp;" + propertyName + "<img Class='close' src='../images/icons/remove-icon-small.png' />";
            $(this).html(name);
        }
        i++;
    });
}

$("#btnNewWorkFlow").click(function () {
    $('#imglockWF').hide();
    ClearLoadproperty();

    //History
    $('[id$=lblworkflowVersion]').val('');
    sessionStorage.setItem("workflowidOld", 0);
    sessionStorage.setItem("workflowhistorynameOld", "");

    $("#workflowHiddenId").html("");
    $("#btnhistory").hide();
    $("#chkMarkParallel").attr('checked', false);
    $("#chkMarkParallel").attr("disabled", true);
    // document.getElementById("chkMarkParallel").disabled = true;
    $("#chkMarkParallel").next().children().children('span').css('display', 'block');
    $("#chkMarkParallel").next().children().children('span.cb-icon-check').css('display', 'none');
    //$("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#box1,#ctl00_cphBody_ddlLoadProperty").removeClass("disabled");
    //$("#chkMarkParallel").next().find("button").attr("disabled", false);
    $("#btnSave,#btnSaveAs,#groupAttech,#btnDeAttachGroup,#btnEditAction,#btnDeleteAction,#btnActionCreate,#btnCondition,#box1,#ctl00_cphBody_ddlLoadProperty").attr("disabled", false);
    $("#chkMarkParallel").next().find("button").attr("disabled", false);
    $("[id$=ddlLoadProperty]").val("00");
    $("[id$=ddlLoadProperty]").trigger("chosen:updated");
    if ($("[id$=hdnWorkflowlock]").val() == "1") {
        isWorkFlowChange = "UnChange"
    }
    if (isWorkFlowChange == "Change") {
        if ($(".one").length > 0) {
            OpenAlertModelConform('Workflow not save Do you want to save it?', NewWorkflowConfirm, OldWorkflowNotSave);
            $(".modal-dialog").css('width', '430px');
            $(".modal-footer").css('padding', '5px');
            $(".modal-header .modal-title").css('line-height', '29px');
            $(".close").css({ "font-size": "28px", "color": "#fff" });
            // $("#btnCondition").attr("disabled", true);
            $("#btnCondition").hide();
            $("#btnConditionDisable").show();
        }
    }
    else {
        newWorkFlowOperation();
        WorkFlowUnChanged();
        //  $("#btnCondition").attr("disabled", true);
        $("#btnCondition").hide();
        $("#btnConditionDisable").show();
        $("#workflowHiddenId").html("");
    }
});

function OpenAlertSaveConformation() {
    OpenAlertModelConform('Workflow not save Do you want to save it', NewWorkflowConfirm, OldWorkflowNotSave);
    $(".modal-dialog").css('width', '430px');
    $(".modal-footer").css('padding', '5px');
    $(".modal-header .modal-title").css('line-height', '29px');
    $(".close").css({ "font-size": "28px", "color": "#fff" });
}

function OldWorkflowNotSave(win) {
    $("#txtRTO").val("");
    $("#txtRTO").attr("disabled", false);
    newWorkFlowOperation();
    WorkFlowUnChanged();
    CloseModel(win);
}

function NewWorkflowConfirm(win) {
    btnNewWorkFLowClick = "true";
    $('#btnSave').trigger('click');
    CloseModel(win);
}

function SelectedDiv(id) {
    var textValue = "";
    var i = 1;
    var id1 = "";
    var propertyName = "";


    //Niteen M

    let closeImgPath = "";
    if ($("#ctl00_cphBody_HiddenFielddelete").val() == "allowDelete")
        closeImgPath = "<img Class='close' src='../images/icons/remove-icon-small.png' />";

    var actionDiv = $('.one');
    var id2 = "000";
    for (var i = 0; i < actionDiv.length; i++) {
        $(actionDiv[i]).html($(actionDiv[i]).html().replace(/&nbsp;/gi, ''));
        textValue = $(actionDiv[i]).text();
        textValue = jQuery.trim(textValue);
        textValue = textValue.substring(textValue.indexOf(".") + 1);
        textValue = i + 1 + ".&nbsp;&nbsp;" + textValue + "<img Class='close' src='../images/icons/remove-icon-small.png' />";
        $(actionDiv[i]).html(textValue);
        $(actionDiv[i]).css("border", "1px solid #707070");
        $(actionDiv[i]).css("background-color", "#FAFAFA");
        $(actionDiv[i]).css("color", "#707070");

        //Bind Action Drop Down Left
        var parentTag = $(actionDiv[i]).parents().get(0);
        id2 = $(parentTag).attr("id");
        textValue = $(actionDiv[i]).text();
        var actionId = $(actionDiv[i]).attr('id');
        var profileId = $(actionDiv[i]).attr("profile");
        textValue = textValue.substring(textValue.indexOf(".") + 1);
        $("#ddlSelectedWFProperty").append("<option value='" + id2 + "' profile='" + profileId + "' actionId='" + actionId + "'>" + textValue + "</option>");

    }
    $('.diamond').each(function () {
        $(this).css("border", "1px solid #707070");
        $(this).css("background-color", "#FAFAFA");
        $(this).css("color", "#707070");
        i++;
    });

    getActionIdAndName();
    var tempActionsForAddCondi = allActionNamesForAddCondition.split(',');
    for (var a = 0; a < tempActionsForAddCondi.length - 1; a++) {
        if (tempActionsForAddCondi[a].indexOf("divCondi") != -1) {
            var previousAction = tempActionsForAddCondi[a - 1].split('^');
            var previousActionIndex = previousAction[1].split('.');
            var getConditionId = tempActionsForAddCondi[a].split('^');
            $("#" + getConditionId[0]).children('.diamondtext').html(previousActionIndex[0]);
        }
    }



    $("#" + id).find('.one,.diamond').css("border", "1px solid #0081C2 ");
    $("#" + id).find('.one,.diamond').css("background-color", "#aac8e2");
    $("#" + id).find('.one,.diamond').css("color", "#111");
    var type = $("#" + id).attr("Class");
    var idObj = $("#" + id).find('.one').attr("id");
    if (type == "Property") {
        $("[id$=ddlLoadProperty]").val(idObj);
        //commented by hanumant 23012019//=> use SetWFPropertyValues(idObj) function instead of ddlLoadProperty.trigger change;
        // $("[id$=ddlLoadProperty]").trigger('change');
        SetWFPropertyValues(idObj);

        //for setting ddlLoadProperty value
        setTimeout(function () {
            _actid = idObj;
            _actname = $("[id$=txtName]").val();

            if (!$('#ctl00_cphBody_ddlLoadProperty option[value="' + _actid + '"]').length) {
                $("#ctl00_cphBody_ddlLoadProperty option[value='000']").remove();
                $('#ctl00_cphBody_ddlLoadProperty').trigger('chosen:updated');
                $("select[id$=ddlLoadProperty]").append("<option value='" + _actid + "' selected='selected'>" + _actname + "</option>");
                $("select[id$=ddlLoadProperty]").append("<option value='000'>Add New Activity</option>");
            }

            $("#ddlLoadProperty").val(_actid).attr('selected', true);
           // $('#ctl00_cphBody_ddlLoadProperty').trigger('chosen:updated');
            EnableImage();
        }, 1000);
    }
    else {
        CallPropertySetFunction(idObj);
    }
    RearrengeDdl(id);
    //RearrengeDdl()
}

function SetWFPropertyValues(idObj) {
    $("ul#ulCategory").hide();
    //if ($(".nicescroll-rails").length > 0) {
    //    $(".nicescroll-rails").remove();
    //}
    $("#txtRTO").val("");
    $("#divActionSet").hide(500);
    //var value = $(this).val();
    var value = idObj;
    if (value != "undefined") {
        $(actionSetList).val("00").attr('selected', true);
        $(".error").hide();
        DisableImage();
        if (value == DropDownValue.InsertOption) {
            alertMechanismMessages = "";
            $("#ChkEmail").attr('checked', false);
            $("#ChkSms").attr('checked', false);
            $(propertyObj).show(500);
            $(conditionDiv).hide(500);
            ElementDefaultValue();
            ShowActionbtn("btnActionSave");
            EnableActionElement();
            $("#Button2").val("- Select Action -");
            $("ul#ulCategory input[type=checkbox]").attr("checked", "");
            $("ul#ulCategory").empty();
            var actionBaseId = $("[id$=ddlactionTypeBase]").val();
            SelectType(actionBaseId);
        }
        else if (value == DropDownValue.DefaultOption || value == null) {
            $(propertyObj).hide(500);
        }
        else {
            $(propertyObj).show(500);
            $(conditionDiv).hide(500);
            ShowActionbtn("btnEditAction,btnDeleteAction");
            PopulateProperty(value);
            DisableActionElement();
            //EnableImage();
        }
    }
}

function validateActionset() {
    var alphaExp = /^[a-zA-Z\s]+$/;
    var txtActionSetObj = new Array("txtActionSetName", "txtActionSetDescription", "ddlActionSetProperty");
    var returnValue = "";
    var flag = "";

    for (var i = 0; i < txtActionSetObj.length; i++) {
        if (i != 1) {
            if ($("#" + txtActionSetObj[i]).val() == "") {
                flag = 1;
                returnValue = returnValue + txtActionSetObj[i] + "Require :";
            }
        }

        if (i == 0) {
            if (!$("#" + txtActionSetObj[i]).val().match(alphaExp)) {
                flag = 1;
                returnValue = "Action Name only required Alphabet :";
                //returnValue + txtObj[i] +
            }
        }

        if (i == 2) {
            if ($("#" + txtActionSetObj[i] + " option").size() == 0) {
                flag = 1;
                returnValue = returnValue + txtActionSetObj[i] + "Please Select Activity :";
            }
        }
    }
    if (flag == 1) {
        return returnValue;
    }
    else {
        return "No error";
    }
}

$("#btnCancelEdit").click(function () {
    $('#ddlLoadProperty').trigger('change');
    $('#btnPropertySave').hide();
    $('#btnCancelEdit').hide();
    $('#btnEdit').show();
    EnableImage();
    DisablePropertyElement();
    hideDivError();
});

function EnableImage() {
    $('#box1').show();
    $("#disableBox").hide();
}

function DisableImage() {
    $('#box1').hide();
    $("#disableBox").show();
}

function PropertyError(result, divId) {
    result = result.substring(0, result.length - 1);
    var error = result.split(":");

    $("#" + divId).empty();

    for (var i = 0; i < error.length; i++) {
        $("#" + divId).append("<div class='error'   >" + error[i] + "  </div>");
    }
}

$('.validation').blur(function () {
    var divId = $(this).attr("id");
    var error = "";
    //var reg = new RegExp("/[a-zA-Z\s]/");
    var alphaExp = /^[a-zA-Z0-9''_'\s]{3,35}$/;
    var blkspace = /[\s ]+$/;

    var digitExp = /^[0-9]+$/;
    if ($(this).val() == "") {
        if ($(this).attr("id") == "txtName") {
            error = "Please Enter Action Name";
        }
        if ($(this).attr("id") == "txtScript") {
            error = "Please Enter Script";
        }
        if ($(this).attr("id") == "txtRecurrenceTime") {
            error = "Please Enter Recurrence Time";
        }
        if ($(this).attr("id") == "txtActionSetName") {
            error = "Enter Action Set Name";
        }

        $("#error" + divId).html(error);
        $("#error" + divId).show();
        return false;
    }
    else
        if (($(this).attr("id") != "txtRecurrenceTime") && ($(this).attr("id") != "txtScript")) {
            if (!$(this).val().match(alphaExp)) {
                error = "Only AlphaNumeric value allow";

                $("#error" + divId).html(error);
                $("#error" + divId).show();

                return false;
            }
            else {
                $("#error" + divId).hide();
            }
        }
        else {
            $("#error" + divId).hide();
        }
    if ($(this).attr("id") == "txtName") {
        var ddlLoadPropertyValue = $("[id$=ddlLoadProperty]").val();
        if (ddlLoadPropertyValue == "000") {
            checkExistActivityNameOnAdd("txtName", divId, "ddlLoadProperty", "Activity Name is not Available");
        }
        else {
            checkExistActivityNameOnEdit("txtName", divId, "hdActivityName", "ddlLoadProperty", "Activity Name is not Available");
        }
    }
    if ($(this).attr("id") == "txtActionSetName") {
        if (ddlLoadPropertyValue == "000") {
            checkExistActivityNameOnAdd("txtActionSetName", divId, "ddlActionSetList", "Action Set is Not Available");
        }
        else {
            checkExistActivityNameOnEdit("txtActionSetName", divId, "hdActivitySetName", "ddlActionSetList", "Action Set is Not Available");
        }
    }
});

function checkExistActivityNameOnEdit(id, divId, hdId, ddlId, errormsg) {
    var hdvalue = jQuery.trim($("#" + hdId).val());
    var txtName = jQuery.trim($("#" + id).val());
    if (txtName != hdvalue) {
        var name = txtName;
        $("#" + ddlId + " option").each(function () {
            if (name == jQuery.trim($(this).text())) {
                $("#error" + divId).html(errormsg);
                $("#error" + divId).show();
                return false;
            }
        });
    }
}

function checkExistActivityNameOnAdd(id, divId, ddlId, errormsg) {
    var txtName = jQuery.trim($("#" + id).val());
    var name = txtName;
    $("#" + ddlId + " option").each(function () {
        if (name == jQuery.trim($(this).text())) {
            $("#error" + divId).html(errormsg);
            $("#error" + divId).show();
            return false;
        }
    });
}

$('#btnRunFlow').click(function () {
    var activities = "";
    $('.one').each(function () {
        var parentTag = $(this).parents().get(0);
        var type = $(parentTag).attr("class");
        var id = $(this).attr("id");
        activities = activities + type + id + ":";
    });
    activities = activities.substring(0, activities.length - 1);
});
function clearValidation() {
    $(".error").hide();
}

$("#genrateXml").click(function () {
    jPrompt('Enter Name of the file:', '', 'Confirmation', function (r) {
        if (r) {
            $.ajax({
                type: "POST",
                url: "Workflow.aspx/CreateXmlFile",
                data: "{'name':'" + r + "'}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (msg) {
                    jConfirm('Are u sure to Download?', 'Confirmation Dialog', function (r) {
                        if (r == true) {
                            downloadFile();
                        }
                    });
                },
                error: function (msg) {
                    alert(msg.d);
                }
            });
        }
    });

    function downloadFile() {
        document.getElementById("iframeId").src = "http://localhost:4973/BCMS/WorkFlow/download.aspx";
    }
});

function hideDivError() {
    var diverror = new Array("errortxtName", "errortxtScript", "errortxtRecurrenceTime", "divError", "errortxtActionSetName", "divActionSetError");
    for (var i = 0; i < diverror.length; i++) {
        $('#' + diverror[i]).hide();
    }
}

function WorkFlowChange() {
    isWorkFlowChange = "Change";
}

function WorkFlowUnChanged() {
    isWorkFlowChange = "UnChange";
}



if (navigator.userAgent.indexOf("Chrome") == -1) {
    $("body").addClass("chromebrowser");
}
else {
}


function drwaConditionLineInUpAndDownDirection(attrActiondifferenceWhenAddCondi, parentIdWhenAddCondi, totalheightWhenAddCondi, conditionDivIdWhenAddCondi) {
    if (parseInt(attrActiondifferenceWhenAddCondi) < 0) {
        $("<div class='line'>").css({
            "height": "1px",
            "background": "#707070",
            "width": "269px",
            "position": "absolute",
            "margin-top": "-27px",
            "float": "right",
            "right": "21px",
            "color": "red"
        }).text("Failure").appendTo(parentIdWhenAddCondi);
        $("<div class='line'>").css({
            "height": parseInt(totalheightWhenAddCondi),
            "background": "#707070",
            "width": "1px",
            "position": "absolute",
            "margin-top": "-27px",
            "float": "right",
            "right": "20px"
        }).appendTo(parentIdWhenAddCondi);
        $("<div class='downthirdline line'>").css({
            "height": "1px",
            "background": "#707070",
            "width": "69px",
            "position": "absolute",
            "margin-top": parseInt(totalheightWhenAddCondi) + (-27),
            "right": "21px"
        }).appendTo(parentIdWhenAddCondi);
        $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    }
    else if (parseInt(attrActiondifferenceWhenAddCondi) > 0) {
        $("<div  class='line'>").css({
            "height": "1px",
            "background": "#707070",
            "width": "269px",
            "position": "absolute",
            "margin-top": "-27px",
            "left": "21px",
            "color": "red"
        }).text("Failure").appendTo(parentIdWhenAddCondi);
        $("<div class='line'>").css({
            "height": totalheightWhenAddCondi,
            "background": "#707070",
            "width": "1px",
            "position": "absolute",
            "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
            "float": "right",
            "left": "21px"
        }).appendTo(parentIdWhenAddCondi);
        $("<div class='upthirdline line'>").css({
            "height": "1px",
            "background": "#707070",
            "width": "69px",
            "position": "absolute",
            "margin-top": "-" + (27 + totalheightWhenAddCondi) + "px",
            "left": "21px"
        }).appendTo(parentIdWhenAddCondi);
        $("#" + conditionDivIdWhenAddCondi).attr('totalHeight', totalheightWhenAddCondi);
    }
}


$("#btnCopyAs").click(function () {

    if ($('#dropBox .Property').length == 0) {
        OpenAlertModelAlert("First add workflow action in Workflow!!!");
    }
    else
        OpenAlertModelCopyWF("<div class='col-md-12' style='font-size:14px;margin-bottom:7px;'>Copy  " + WorkFlowName + " as </div> <div class='col-md-4' style='margin-top:5px'>  WorkFlow Name</div><div class='col-md-8 padding-none-LR'> <input id='idSaveAs' type=text class='form-control' style='width:80%;height:29px' /><span id='idSaveAsSpan'></span></div>", CopyAsWorkflow, "Copy As Workflow");

});


function CopyAsWorkflow(win) {
    if (document.getElementById("idSaveAs").value == "") {
        document.getElementById("idSaveAs").focus();
        return;
    }
    $("#idPrompt1").trigger("blur");
    WorkFlowUnChanged();
    var valueToPass = "";
    var parentTag = "";
    var type = "";

    wfName = document.getElementById("idSaveAs").value;
    var wfRunningType;
    if ($("#chkMarkParallel").attr("checked") == "checked") {
        wfRunningType = "True";
    }
    else {
        wfRunningType = "False";
        $('.one').attr("runparallel", "undefined");
        $(".Property .small").remove();
        $(".Property .divparallel").remove();
    }

    $('.one,.diamond').each(function () {
        parentTag = $(this).parents().get(0);
        type = $(parentTag).attr("Class");
        if ($(this).hasClass('diamond')) {
            valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetAction") + "^" + $(this).attr("failedCount") + ':';
        }
        else {
            var RunparallelValue = "";
            if ($(this).attr('runParallel') == 'parallelRun') {
                if ($(this).parent().find('.divparallel').length) {
                    RunparallelValue = 'parallelRun';
                    valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + RunparallelValue + "^" + $(this).attr("failedCount") + ':';

                }
                else {
                    RunparallelValue = 'undefined';
                    valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + RunparallelValue + "^" + $(this).attr("failedCount") + ':';

                }
            }
            else { valueToPass = valueToPass + type + "," + jQuery.trim($(this).text()) + "," + $(this).attr("id") + "^" + $(this).attr("totalHeight") + "^" + $(this).attr("actionDifference") + "^" + $(this).attr("targetaction") + "^" + $(this).attr("runParallel") + "^" + $(this).attr("failedCount") + ':'; }

        }
    });

    valueToPass = valueToPass.substring(0, valueToPass.length - 1);
    var hiddenWorkflowId = $("#workflowHiddenId").html();
    var hiddentoken = document.getElementById("ctl00_cphBody_hdtokenKey").value;//$("#ctl00_cphBody_hdtokenKey").value;
    var hdnGuid = document.getElementById("ctl00_cphBody_hdnGuid").value;
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/CopyAsWorkflow",
        data: "{'para':'" + valueToPass + "','name':'" + wfName + "','hiddenId':'" + hiddenWorkflowId + "','wfRunningType':'" + wfRunningType + "','ReqToken':'" + hiddentoken + "','ReqGuid':'" + hdnGuid + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        sync: true,
        success: function (msg) {
            if (msg.d == "XssScriptAttack") {
                window.location.href = "/Logout.aspx";
                return false;
            }
            if (btnNewWorkFLowClick == "true") {
                newWorkFlowOperation();
                $("#idWorfkflowName").html("Workflow Editor");
            }
            else {
                $("#idWorfkflowName").html(wfName);
                $("#idWorfkflowName").attr('title', wfName);
            }
            var message = msg.d.split('^');
            if (message[0] == "duplicate") {
                $("#idWorfkflowName").html("Workflow Editor");
                alert("Duplicate Workflow Name");
            }
            else if (message[0] == "Update") {

                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'> Workflow <b class='text-success' style='word-wrap: break-word;'>" + wfName + "</b> Copy Successfully.</div></div>");
                $(".modal-dialog").css('width', '400px');
                $(".modal-footer").css('padding', '5px');
                $(".modal-header .modal-title").css('line-height', '29px');
                $(".close").css({ "font-size": "28px", "color": "#fff" });
            }
            else {
                win.closeModal();
                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'> Workflow <b class='text-success' style='word-wrap: break-word;'>" + wfName + "</b> Copy Successfully.</div></div>");
                $("#workflowHiddenId").html(message[1]);
                loadActionName();
                $("#ddlExist").trigger("change");

                if ($("#idMsg").hasClass("error")) {
                    return false;
                }

                $("#chkMarkParallel").attr('checked', false);
                $("#chkMarkParallel").next().children().children('span').css('display', 'block');
                $("#chkMarkParallel").next().children().children('span.cb-icon-check').css('display', 'none');

                removeItem();

                GlobalWorkFlowID = message[1];
                var hiddentoken = document.getElementById("ctl00_cphBody_hdtokenKey").value;
                var hdnGuid = document.getElementById("ctl00_cphBody_hdnGuid").value;
                $("#workflowHiddenId").html(GlobalWorkFlowID);
                $.ajax({
                    type: "POST",
                    url: "WorkflowConfiguration.aspx/OpenWorkFlow",
                    data: "{'parameter':'" + GlobalWorkFlowID + "','ReqToken':'" + hiddentoken + "','ReqGuid':'" + hdnGuid + "'}",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json",
                    sync: true,
                    success: function (msg) {
                        if (msg.d == "XssScriptAttack") {
                            window.location.href = "/Logout.aspx";
                            return false;
                        }
                        CreateWorkFlowDiagrame(msg.d);
                        WorkFlowUnChanged();
                    }
                });
            }
            globalWorkFlowOperation = "Update";
        }
    });


}


$("#btnExportXml").click(function () {
    $(".modal-dialog").hide();
    OpenAlertModelXMLimport("<i class='workflow_icon'></i><label class='col-md-4'> Select WorkFlow </label> <select id='ddlExportXml' class='col-md-8' ></select><span id='idMsg'></span>", ExportWorkflowXML);//  class='form-control', , CheckWorkFlowIsAttached
    $(".modal-footer").css('padding', '5px');
    $(".modal-header .modal-title").css('line-height', '29px');
    $(".close").css({ "font-size": "28px", "color": "#fff" });
    GetExportXmlValue();
    $("button:eq(1)").attr("disabled", true);
});


function ExportWorkflowXML(win) {
    var workFlowId = $("#ddlExportXml option:selected").val();
    if (workFlowId != 000) {
        $(win).closeModal();
        $("button:eq(1)").attr("disabled", false);
        PopulateddlExportXmlWF(workFlowId, win);
    }
    else {
        $("button:eq(1)").attr("disabled", true);
    }

}
function GetExportXmlValue() {
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/GetExistingWorkFLow",
        data: "{'args':'GetWorkFlow'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (msg) {
            PopulateddlExportXml(msg.d);
            //$(".chosen-select").chosen({ search_contains: true });
        },
        error: function (msg) {
            alert(msg.d);
        }
    });
}


function PopulateddlExportXml(msg) {
    var data = msg;

    var result = data.split(",");
    if (result == "") {
        var text = "-Select WorkFlow-";
        var value = "000";
        var chkValue = "";
        $("#ddlExportXml").append("<option value='" + value + "'>" + text + "</option>");
    }
    else {
        var text = "-Select WorkFlow-";
        var value = "000";
        var chkValue = "";
        AppendOption('ddlExportXml', value, text);
        for (var i = 0; i < result.length; i++) {
            chkValue = result[i].split(":");
            text = chkValue[0];
            value = chkValue[1];
            $("#ddlExportXml").append("<option value='" + value + "'>" + text + "</option>");
        }

        var options = $("#ddlExportXml option");                    // Collect options         
        options.detach().sort(function (a, b) {               // Detach from select, then Sort
            var at = $(a).text();
            var bt = $(b).text();
            return (at > bt) ? 1 : ((at < bt) ? -1 : 0);            // Tell the sort function how to order
        });
        options.appendTo("#ddlExportXml");
        $("#ddlExportXml option:first").attr('selected', 'selected');

    }
}
var win1;
function PopulateddlExportXmlWF(workFlowId, win) {
    $.ajax({
        type: "POST",
        url: "WorkflowConfiguration.aspx/GetAllInfoOfSelectedWorkflow",
        data: "{'id':'" + workFlowId + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        sync: true,
        success: function (msg) {
            if (msg.d == "XssScriptAttack") {
                window.location.href = "/Logout.aspx";
                return false;
            }
            else
                ExportXML(msg);
        },
        error: function (err) {
            if (err != "") {

            }
        }
    });

    //var ajaxUrl = "WorkflowConfiguration.aspx/GetAllInfoOfSelectedWorkflow";//GetAllActionsParameterOfSelectedWorkflow
    //var id = workFlowId;
    //var ajaxData = "{'id':'" + id + "'}";
    //if (id != "") {
    //    win1 = win;
    //    AjaxFunction(ajaxUrl, ajaxData, ExportXML, OnError);
    //}
    //else {
    //    alert("First Load Workflow");
    //}
}

var ExportXML = function (data) {
    var resultArr = data.d.split('^');
    if (win1 != undefined)
        win1.closeModal();
    if (resultArr[0] == "Success")
        if (resultArr.length > 1) {
            OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Workflow XML Exported Successfully in  <b class='text-success' style='word-wrap: break-word;'>" + resultArr[1] + "</b></div></div>");
            //OpenAlertModel1("Workflow XML Export", );
        }
        else
            OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'> <div class='col-md-2' style='width: 9%;padding: 0;'> <img src='../Images/icons/checked6.png'/> </div> <div class='col-md-9'  style='width: 90%;padding-right: 0;'>Workflow XML Exported Successfully.</div></div>");
    //            OpenAlertModel1("Workflow XML Export", "Workflow XML imported successfully.");


};

//Workflow History Changes Vivek
$("#btnWorkflowHistory").click(function () {

    var hiddenWorkflowId = sessionStorage.getItem("workflowidOld");
    var hiddneworkflowname = sessionStorage.getItem("workflowhistorynameOld");
    OpenWorkflowHistory("  <div id='workflowHistory' class='vert-form' style='min-height:300px;'> <div id='imgLoadinghistory' class='loading-mask hide'>  <span>Loading...</span> </div>  </div>", hiddneworkflowname);
    GetWorkflowHistorybyId(hiddenWorkflowId);
    $(".modal-dialog").css('width', '810px');
    $(".modal-footer").css('padding', '5px 20px');
    $(".modal-header .modal-title").css({ "line-height": "28px" });
    $(".close").css({ "font-size": "28px", "color": "#fff" });
    $('.modal-header:has(h3:contains("Workflow History"))').addClass('workflowic');
});

function GetWorkflowHistorybyId(hiddenWorkflowId) {
    $("#imgLoadinghistory.loading-mask").removeClass("hide");
    $.ajax({
        type: "POST",
        url: "WorkFlowConfiguration.aspx/GetWorkflowHistorybyId",
        data: "{'id':'" + hiddenWorkflowId + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        success: function (msg) {
            $("#imgLoadinghistory.loading-mask").addClass("hide");
            GetWorkflowHistoryData(msg.d);
            $('.myPopover').popover('hide');
            $('.myPopover').click(function () {
                //$(".notifyscroll").mCustomScrollbar('destroy');
                $(".notifyscroll").mCustomScrollbar("destroy");
                $("div.popover").remove();
                $(this).popover('show');
                $(".popover .popover-content").wrap("<div class='notifyscroll' style='max-height:80px !important;'></div>");
                $(".notifyscroll").mCustomScrollbar({
                    axis: "y",
                });
                $(".scr1").scroll(function () { //.box is the class of the div
                    $("div.popover").remove();
                });
            });

            $(document).mouseup(function (e) {
                var container = $(".popover");

                // if the target of the click isn't the container nor a descendant of the container
                if (!container.is(e.target) && container.has(e.target).length === 0) {
                    container.hide();
                }
            });



            //$('.notifyscroll').each(function () {
            //    var $this = $(this);
            //    if ($this.find('tr').length > 9) {
            //        $this.addClass('notifyscroll');
            //        $($this.find('.succesrows')).last().find('.myPopover').attr("data-placement", "top");
            //        $($this.find('.succesrows')).first().find('.myPopover').attr("data-placement", "bottom");
            //        $('.myPopover').popover();
            //    }
            //    else {
            //        $this.removeClass('notifyscroll');
            //    }
            //});

            //$(".notifyscroll").mCustomScrollbar({
            //    axis: "y",
            //});
        },
        error: function (msg) {
            alert(msg.d);
        }
    });
}

function RestoreWorkflow(id, oldactionId, actionVersion, WFId, WFVersion) {
    localStorage.setItem("idnew_", id);
    localStorage.setItem("oldactionId_", oldactionId);
    localStorage.setItem("actionVersion_", actionVersion);
    localStorage.setItem("RestoreWorkflowID", WFId);
    localStorage.setItem("RestoreWFVersion", WFVersion);

}

function WorkflowRestore(name) {

    $.ajax({
        type: "POST",
        url: "WorkFlowConfiguration.aspx/RestoreWorkflow",
        data: "{'id':'" + localStorage.getItem("idnew_") + "','oldactionId':'" + localStorage.getItem("oldactionId_") + "','actionVersion':'" + localStorage.getItem("actionVersion_") + "','name':'" + name + "','RestoreWorkflowID':'" + localStorage.getItem("RestoreWorkflowID") + "','RestoreWFVersion':'" + localStorage.getItem("RestoreWFVersion") + "'}",
        contentType: "application/json; charset=utf-8",
        async: false,
        dataType: "json",
        success: function (msg) {

            var name = msg.d;
            var idnew = name.split('$');
            if (name.includes("Exists")) {
                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'>WorkFlow already restored.</div>");
                $(".modal-dialog").css('width', '400px');
                $(".modal-footer").css('padding', '5px 20px');
                $(".modal-header .modal-title").css('line-height', '29px');
                $(".close").css({ "font-size": "28px", "color": "#fff" });
            }
            else {
                OpenAlertModelAlert("<div style='line-height: 17px; text-indent:0;'><b class='text-success' style='word-wrap: break-word;'>" + idnew[0] + "</b> workFlow restore successfully.</div>");
                $(".modal-dialog").css('width', '400px');
                $(".modal-footer").css('padding', '5px 20px');
                $(".modal-header .modal-title").css('line-height', '18px');
                $(".close").css({ "font-size": "28px", "color": "#fff" });
            }
            $("#lblWorkflowNameVersion").hide();
            $("#btnWorkflowHistory").attr('disabled', true);

            RestoredNewWorkflow(idnew[1]);

            $('#ctl00_cphBody_ddlLoadProperty').trigger('chosen:updated');

            sessionStorage.setItem("workflowidOld", idnew[1]);
            sessionStorage.setItem("workflowhistorynameOld", idnew[0]);
        },
        error: function (msg) {
            alert(msg.d);
        }
    });
}

function ModalRestorePromptOk(win) {

    if (document.getElementById("idPrompt1").value == "") {
        document.getElementById("idPrompt1").focus();
        return;
    }

    var nname = document.getElementById("idPrompt1").value;

    win.closeModal();
    $(".modal").hide();
    WorkflowRestore(nname);
}

function ModalRestorePrompt() {

    OpenAlertRestoreModelLoadworkflow("<div class='col-md-12 vert-form padding-none'><div class='widget widget-heading-simple widget-body-white'><div class='widget-body'><div class=''><div class='form-group'> <i class='newwf-icon-6'> </i> <span> WorkFlow Name </span>  <div class='form-inline'>  <input id='idPrompt1' class='form-control' type=text  style='width:100%;'/><span id='idPromptSpan'></span><span></span></div></div></div></div></div></div>", ModalRestorePromptOk);
    $(".modal-dialog").css('width', '420px');
    $(".modal-footer").css('padding', '5px 20px');
    $(".modal-header .modal-title").css('line-height', '18px');
    $(".close").css({ "font-size": "28px", "color": "#fff" });
}

$('.closeme').live('click', function () {

    $(".modal-dialog").remove();
    $(".modal.bg").hide();
    ModalRestorePrompt();
});

function GetWorkflowHistoryData(msg) {

    $("#workflowHistory").append(msg);
}

function RestoredNewWorkflow(id) {

    $("#chkMarkParallel").attr('checked', false);
    $("#chkMarkParallel").next().children().children('span').css('display', 'block');
    $("#chkMarkParallel").next().children().children('span.cb-icon-check').css('display', 'none');
    GlobalWorkFlowID = id;
    removeItem();
    var hiddentoken = document.getElementById("ctl00_cphBody_hdtokenKey").value;
    var hdnGuid = document.getElementById("ctl00_cphBody_hdnGuid").value;
    $("#workflowHiddenId").html(id);
    $.ajax({
        type: "POST",
        url: "WorkFlowConfiguration.aspx/OpenWorkFlow",
        data: "{'parameter':'" + id + "','ReqToken':'" + hiddentoken + "','ReqGuid':'" + hdnGuid + "'}",
        contentType: "application/json; charset=utf-8",
        dataType: "json",
        async: false,
        success: function (msg) {
            if (msg.d == "XssScriptAttack") {
                window.location.href = "/Logout.aspx";
                return false;
            }
            var wfver = msg.d.split('qpwoei');
            var wfversion;

            if (wfver[0].indexOf('.') == -1) {
                wfversion = wfver[0] + '.0';
            }
            else {
                wfversion = wfver[0];
            }
            $("#btnhistory").css({ "display": "inline" });
            $('[id$=lblworkflowVersion]').html('Workflow Version : ' + parseFloat(wfversion).toFixed(1) + '');
            CreateWorkFlowDiagrame(wfver[1]);
            WorkFlowUnChanged();
            GetDdlExistValue();
        }
    });
}



$('#btnverify').on('click', function () {
    var _processId = $("#ProcessId").html();
    if (_processId != "") {

        var workflowname = $('#idWorfkflowName').text();

        var content = '<div class="form-group" style="min-height: 70px;">'+
                           ' <div class="col-md-12">'+
                               ' <span id="lblWFName">'+
                                    'Are you sure to verify workflow <b>${workflowname}</b>'+
                                '</span>'+
                           ' </div>'+
                            '<div class="col-md-3">'+
                                '<label for="_txtreason" id="lblReason">Remark</label>'+
                            '</div>'+
                           ' <div class="col-md-9">'+
                               ' <input type="text" id="_txtreason"  class="form-control" style="width: 88%;">'+
                               ' <span id="lblreasonerror"  style="color: red; display: none !important;">'+
                               ' Please Enter Reason'+
                           ' </span>'+
                        '</div>'+
                       ' </div>';
        OpenAlertRestoreModelLoadworkflow(content, verifyworkfow);


    }

});

var verifyworkfow = function (win) {

    var reason_value = $('#_txtreason').val();

    if (reason_value != '') {
        var _processId = $("#ProcessId").html();
        _processId = _processId + "$" + reason_value;
        if (_processId != "") {
            $.ajax({
                type: "POST",
                url: "WorkflowConfiguration.aspx/VerifyWorkflow",
                data: "{'ProcessID':'" + _processId + "'}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                success: function (msg) {
                    $('#btnverify').hide();
                    $('#btnreject').hide();
                    CloseModel(win);
                    alert('Sucessfully verified workflow request!!!');
                }
            });
        }
    }
    else {
        $('#lblreasonerror').show();
        return;
    }


}


$('#btnreject').on('click', function () {

    var _processId = $("#ProcessId").html();
    if (_processId != "") {
        var workflowname = $('#idWorfkflowName').text();

        var content = '<div class="form-group" style="min-height: 70px;">'+
                            '<div class="col-md-12">'+
                                '<span id="_lblWFName">Are you sure to reject workflow <b>${workflowname}</b>?</span>'+
                            '</div>'+
                            '<div class="col-md-3">'+
                                '<label for="_txtreason1" id="_lblReason">Remark</label>'+
                            '</div>'+
                            '<div class="col-md-9">'+
                                '<input type="text"  id="_txtreason1" class="form-control" style="width: 88%;" />'+
                                '<span id="lblreasonrejecterror"  style="color: red; display: none !important;">'+
                                'Please Enter Reason'+
                            '</span>'+
                        '</div>'+
                        '</div>';
        OpenAlertRestoreModelLoadworkflow(content, rejectworkflow);

    }


});


var rejectworkflow = function (win) {

    var reason_value = $('#_txtreason1').val();
    if (reason_value != '') {
        var _processId = $("#ProcessId").html();
        _processId = _processId + "$" + reason_value;
        if (_processId != "") {
            $.ajax({
                type: "POST",
                url: "WorkflowConfiguration.aspx/RejectWorkflow",
                data: "{'ProcessID':'" + _processId + "'}",
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                sync: true,
                success: function (msg) {
                    $('#btnverify').hide();
                    $('#btnreject').hide();
                    CloseModel(win);
                    alert('Sucessfully rejected workflow request!!!');
                },
                error: function (err) {
                    if (err != "") {

                    }
                }
            });
        }
    }
    else {
        $('#lblreasonrejecterror').show();
        return;
    }
}