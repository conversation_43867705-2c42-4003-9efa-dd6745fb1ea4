﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Linq;
using System.ComponentModel;
using System.IO;
using System.Management;
using CP.Common.Shared;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Helper;
using System.Web.Services;
using log4net;
using System.Text;
using System.DirectoryServices;
using System.DirectoryServices.ActiveDirectory;
using System.DirectoryServices.AccountManagement;
using System.Text.RegularExpressions;
using Microsoft.Security.Application;
using CP.UI.Controls;
using System.Collections;
using System.Web.Security;
using System.Web.SessionState;
using System.Reflection;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using System.Web.Configuration;
using PTS.SSO;


namespace CP.UI
{
    public partial class Login : BasePage
    {
        private IFacade _facade = new Facade();

        private string _companyname = string.Empty;
        int OTP_Attempt = 0;
        private const int MaxLoginAttempt = 9999;
        private static readonly ILog _logger = LogManager.GetLogger(typeof(Login));

        public int cId;
        User userDetail = new User();
        string newSessionId = string.Empty;
        public int userId = 0;
        public string LoginName = string.Empty;
        private static DateTime lastSentTime = DateTime.MinValue;
        private static TimeSpan sendInterval = TimeSpan.FromSeconds(60); // Adjust as needed from config file

        private void AddCookie()
        {
            FormsAuthenticationTicket ticket = new FormsAuthenticationTicket(1, "TestCookie", DateTime.Now, DateTime.Now.AddSeconds(5), false, "");
            string encryptedText = FormsAuthentication.Encrypt(ticket);
            HttpCookie sessionId = new HttpCookie("ASP.NET_SessionId", encryptedText)
            {
                Path = ConfigurationManager.AppSettings["SessionCookiesPath"].ToString()
            };
            Response.Cookies.Add(sessionId);
        }

        protected override void OnLoad(EventArgs e)
        {
            Utility.setDBParameterPrefix();

            if (!IsPostBack)
            {
                try
                {
                    if (chkActiveDirectory.Checked == true)
                    {
                        txtDomain.Visible = true;
                        lbldomainName.Visible = true;
                        pnlDomain.Visible = true;
                        //combobox1.Value = "icicibankltd.com";
                        if (ConfigurationManager.AppSettings["IsADUser"].Equals("true", StringComparison.OrdinalIgnoreCase))
                        {
                            txtDomain.Text = "ptechno.com";
                        }
                        else
                        {
                            txtDomain.Text = "icicibankltd.com";
                        }

                    }
                    else
                    {
                        txtDomain.Visible = false;
                        lbldomainName.Visible = false;
                        pnlDomain.Visible = false;
                        txtDomain.Text = string.Empty;
                    }

                    Session["ReplicationConfig_token"] = null;
                    Session["SetionTimeForOTP"] = null;
                    HDF_SessionTime.Value = "";
                    /////////////////////////////////////////////////////////////
                    Session.Abandon();
                    Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", ""));
                    AddCookie();
                    /////////////////////////////////////////////////////////////


                    hdfStaticGuid.Value = Guid.NewGuid().ToString();

                    ViewState["_token"] = UrlHelper.AddTokenToRequest();
                    if (ViewState["_token"] != null)
                    {
                        hdtokenKey.Value = ViewState["_token"].ToString();
                    }

                    //log4net.Config.XmlConfigurator.Configure();             

                    //if (ConfigurationManager.AppSettings["IsCaptchaRequired"].Equals("true", StringComparison.OrdinalIgnoreCase))
                    //{
                    //    pnlCaptcha.Visible = true;
                    //    rfvCaptcha.Enabled = true;
                    //}
                    //else
                    //{
                    //    pnlCaptcha.Visible = false;
                    //    rfvCaptcha.Enabled = false;
                    //}
                    lblVersion.Text = ConfigurationManager.AppSettings["Version"].ToString();
                    if (ConfigurationManager.AppSettings["IsCompanyId"].Equals("false"))
                    {
                        Utility.PopulateCompanyProfile(ddlCompanyId, false);
                        pnlCmpvalue.Visible = true;
                        pnlcompvalue.Visible = false;
                    }
                    else
                    {
                        pnlCmpvalue.Visible = false;
                        pnlcompvalue.Visible = true;
                    }
                    var company = _facade.GetAllCompanyProfiles();
                    var users = _facade.GetAllUsers();
                    if (company.Count == 0 || users.Count == 0)
                    {
                        Response.Redirect("~/CompanyInfoPage.aspx", false);

                    }

                    string LoginMethod = ConfigurationManager.AppSettings["LoginMethod"].ToString();
                    if (string.IsNullOrEmpty(LoginMethod))
                    {
                        Response.Redirect("~/Logout.aspx", false);
                    }
                    else
                    {
                        if (LoginMethod.Equals("CP"))
                        {
                            btnLoginSSO.Enabled=false;
                            LoginButton.Enabled = true;
                        }
                        else if (LoginMethod.Equals("SAML"))
                        {
                            btnLoginSSO.Enabled=true;
                            LoginButton.Enabled = false;
                        }
                    }
                    // VerifyLicenseKey();
                }
                catch (Exception ex)
                {
                    var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                    _logger.Info("Exception Occured while populating company names :" + ex.InnerException.Message);
                    ExceptionManager.Manage(bcms);
                }
            }
        }

        private void VerifyLicenseKey()
        {
            StreamReader sread = null;

            string filePath = Server.MapPath(@"~/Common/Key.inf");

            if (File.Exists(filePath))
            {
                IList<Licencekey> liecenKeyList = Facade.GetAllLicenceKeies();

                if (liecenKeyList != null)
                {
                    using (sread = File.OpenText(filePath))
                    {
                        string read = sread.ReadToEnd();

                        foreach (Licencekey licence in liecenKeyList)
                        {
                            if (licence.Key.EndsWith(read.Replace("\r\n", "")))
                            {
                                DateTime dto = Convert.ToDateTime(licence.ValidTo);
                                int id = licence.Id;

                                if (dto > DateTime.Now)
                                {
                                    licensebg.Visible = false;
                                }
                                else
                                {
                                    Licencekey keyststus = Facade.UpdateLicencekeyByStatus(id, 0);
                                    Expirekey.Visible = true;
                                    licensebg.Visible = true;
                                }
                            }
                        }
                    }
                }
                else
                {
                    licensebg.Visible = true;
                }
            }
        }

        private int GetLoginAttempts()
        {
            #region Commented By priyanka

            //if (ViewState["loginAttempts"] != null)
            //{
            //    ViewState["loginAttempts"] = Convert.ToInt32(ViewState["loginAttempts"].ToString()) + 1;
            //    return Convert.ToInt32(ViewState["loginAttempts"].ToString());
            //}
            //ViewState["loginAttempts"] = 1;
            //return 1;
            #endregion

            if (ViewState["loginAttempts"] != null)
            {
                ViewState["loginAttempts"] = Convert.ToInt32(ViewState["loginAttempts"].ToString()) + 1;
                if (Convert.ToInt32(ViewState["loginAttempts"].ToString()) > 3)
                {
                    pnlCaptcha.Visible = true;
                    rfvCaptcha.Enabled = true;
                }
                else
                {
                    pnlCaptcha.Visible = false;
                    rfvCaptcha.Enabled = false;
                }
                return Convert.ToInt32(ViewState["loginAttempts"].ToString());
            }
            ViewState["loginAttempts"] = 1;
            return 1;
        }

        protected void lnkbtnCaptcha_Click(object sender, EventArgs e)
        {
            rfvCaptcha.Visible = false;

            captcha1.ValidateCaptcha(txtcaptcha.Text.Trim());
        }

        public string GetMacAddress()
        {
            using (var mc = new ManagementClass("Win32_NetworkAdapterConfiguration"))
            {
                ManagementObjectCollection moc = mc.GetInstances();

                string macAddress = String.Empty;

                foreach (ManagementObject mo in moc)
                {
                    if (macAddress == String.Empty) // only return MAC Address from first card
                    {
                        if ((bool)mo["IPEnabled"]) macAddress = mo["MacAddress"].ToString();
                    }
                    mo.Dispose();
                }

                macAddress = macAddress.Replace(":", "-");
                return macAddress;
            }
        }

        private string DecryptString(string encrString)
        {
            string decryptedConnectionString = null;
            try
            {
                byte[] b = Convert.FromBase64String(encrString);
                decryptedConnectionString = System.Text.Encoding.ASCII.GetString(b);
                if (!decryptedConnectionString.Contains("/"))
                    decryptedConnectionString = null;
            }
            catch
            {
                decryptedConnectionString = null;
            }

            return decryptedConnectionString;
        }

        public void CreateLicenceKeyFile(string key)
        {
            string filePath = Server.MapPath(@"~/Common/Key.inf");

            using (var sr = new StreamWriter(filePath))
            {
                try
                {
                    sr.WriteLine(key);
                    sr.Close();
                }
                catch (Exception)
                {
                    sr.Close();
                }
            }
        }

        private bool CheckCompanyNameExist()
        {
            return Facade.IsExistCompanyProfileByName(_companyname.ToLower());
        }

        private static bool ValidateRemoteCertificate(object sender, X509Certificate cert, X509Chain chain, SslPolicyErrors policyErrors)
        {
            return true;
        }

        protected void BtnLoginButtonClick(object sender, EventArgs e)
        {
            // Session["SetionTimeForOTP"] = DateTime.Now.ToString("yyyyMMddHHmmssffff");

            if ((ViewState["_token"] != null))
            {
                if (!UrlHelper.IsTokenValidated(ViewState["_token"].ToString()) || Request.HttpMethod != "POST")
                {
                    Response.Redirect("~/Logout.aspx");
                    return;
                }
            }
            else
            {
                Response.Redirect("~/Logout.aspx");
                return;
            }

            AfterOTPValidate();
        }

        private void AfterOTPValidate()
        {
            try
            {
                ServicePointManager.ServerCertificateValidationCallback = ((object sender1, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors) => true);
                ServicePointManager.ServerCertificateValidationCallback = ((object sender1, X509Certificate cert, X509Chain chain, SslPolicyErrors errors) => cert.Subject.Contains(""));
                ServicePointManager.ServerCertificateValidationCallback = (RemoteCertificateValidationCallback)Delegate.Combine(ServicePointManager.ServerCertificateValidationCallback, new RemoteCertificateValidationCallback(ValidateRemoteCertificate));
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11;

                string orignalStr = string.Empty;
                string userEncryptPass = string.Empty;
                newSessionId = string.Empty;
                IncorrectOTP.Visible = false;
                Blank_otp.Visible = false;
                userDetail = Facade.GetUserByLoginName(Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value));

                int loginAttempts = 0;
                UserInfo get_otp_count_new_user_lock = Facade.GetUserInfoById(userDetail.Id);
                if (get_otp_count_new_user_lock.OTPCount >= 3 && get_otp_count_new_user_lock.IsActive == 1)
                {
                    int set_zero_count = 0;

                    var update_otp_count_zero = Facade.UpdateOtpCount(userDetail.Id, set_zero_count);
                }
                var disname = Facade.GetCompanyDisplayName(txtCompanyCode.Text);
                if (disname != null)
                    cId = disname.Id;

                if (ViewState["loginAttempts"] != null)
                    loginAttempts = Convert.ToInt32((ViewState["loginAttempts"]).ToString());
                else
                    loginAttempts = 0;
                if (chkActiveDirectory.Checked == true)
                {
                    _logger.Info("Active directory user selected");
                    Session["_domainNamee"] = txtDomain.Text;
                    bool isAuth = AuthenticateADUser(txtDomain.Text, Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value), Utility.getOriginalEncryData(PassEncyptHidden.Value, hdfStaticGuid.Value));
                    if (isAuth == true)
                    {
                        _logger.InfoFormat("AD user authenticated " + UserEncrypt.Value + " with domain " + txtDomain.Text);

                        if (ConfigurationManager.AppSettings["IsCaptchaRequired"].Equals("true", StringComparison.OrdinalIgnoreCase))
                        {
                            captcha1.ValidateCaptcha(txtcaptcha.Text.Trim());

                        }

                        if (!captcha1.UserValidated)
                        {
                            divClass.Visible = true;
                            lblerror.Visible = false;
                            FailureText.Text = "please Enter valid Captcha".ToUpper();
                            UserName.Focus();
                            Session.Abandon();
                            Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", ""));
                            AddCookie();
                        }
                        else
                        {

                            var loginType = LoginType.AD;

                            string LoginName = Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value);
                            Session["LoggedIn"] = Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value);
                            string domainValue = txtDomain.Text;
                            string domainName = domainValue + "\\" + LoginName;
                            _logger.InfoFormat("Searching saved AD user " + domainName + " in continuity patrol database");
                            var activeInfo = Facade.GetUserByLoginName(domainName);
                            hfDomainName.Value = domainName;

                            _logger.Info("Value of hidden feild domain : " + hfDomainName.Value);
                            if (activeInfo == null)
                            {
                                _logger.InfoFormat("AD user with name " + domainName + " not found with domain in continuity patrol database", CpExceptionType.ADInvalidADAuthentication);
                                throw new CpException(CpExceptionType.ActiveDrUserNotFound);
                            }
                            var userInfo = Facade.GetUserById(activeInfo.Id);

                            hfUserInfoId.Value = userInfo.Id.ToString();
                            _logger.Info("User Id in hidden feild is : " + hfUserInfoId.Value);

                            if (!string.IsNullOrEmpty(userInfo.SessionId) && ConfigurationManager.AppSettings["IsMultiuser"].Equals("false", StringComparison.OrdinalIgnoreCase))
                            {
                                _logger.Info("User is already Logged in");
                                ShowErrorDiv("User is already Logged in".ToUpper());
                                Session.Abandon();
                                return;
                            }

                            if (ConfigurationManager.AppSettings["IsCompanyId"].Equals("true"))
                            {
                                var companyinfo = Facade.GetCompanyInfoByCompanyProfileId(cId);
                                var superUser = Facade.GetSuperUserId(cId);


                                if (userInfo.LastLoginDate == Convert.ToDateTime("1/1/0001 12:00:00 AM"))
                                {
                                    var smtpInfo = Facade.GetSmtpConfigurations();
                                    if (smtpInfo == null)
                                    {
                                        ReGenerateSessionId();
                                        _facade.ValidateCredentialsByLoginType(domainName, Utility.getOriginalEncryData(PassEncyptHidden.Value, hdfStaticGuid.Value), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                        _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);
                                    }
                                    else
                                    {
                                        foreach (var smtpDetails in smtpInfo)
                                        {
                                            CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();
                                            emailManager.From = CryptographyHelper.Md5Decrypt(smtpDetails.UserName);
                                            emailManager.Body = string.Concat("Domain user: ", userInfo.LoginName, " is logged  in successfully into Continuity Patrol at ", userInfo.CreateDate, "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                            emailManager.Subject = "Active Ditectory Login Info";
                                            SmtpConfiguration smtpConfig = new SmtpConfiguration();
                                            smtpConfig.SmtpHost = CryptographyHelper.Md5Decrypt(smtpDetails.SmtpHost);
                                            smtpConfig.Port = smtpDetails.Port;

                                            if (!string.IsNullOrEmpty(smtpDetails.Password))
                                                smtpConfig.Password = CryptographyHelper.Md5Decrypt(smtpDetails.Password);

                                            smtpConfig.EnableSSL = true;
                                            smtpConfig.IsBodyHTML = true;
                                            foreach (var info in superUser)
                                            {
                                                smtpConfig.UserName = info.UserInformation.Email;
                                                string output = emailManager.SendTestMail(smtpConfig);
                                            }

                                            ReGenerateSessionId();
                                            _facade.ValidateCredentialsByLoginType(domainName, Decodeval(PassEncyptHidden.Value), cId, loginType.ToString());
                                            _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);

                                        }
                                    }
                                }
                                else
                                {
                                    ReGenerateSessionId();
                                    _facade.ValidateCredentialsByLoginType(domainName, Decodeval(PassEncyptHidden.Value), cId, loginType.ToString());
                                    _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);
                                }
                            }
                            else
                            {
                                var companyinfo = Facade.GetCompanyInfoByCompanyProfileId(Convert.ToInt32(ddlCompanyId.SelectedValue));
                                var superUser = Facade.GetSuperUserId(Convert.ToInt32(ddlCompanyId.SelectedValue));
                                if (userInfo.LastLoginDate == Convert.ToDateTime("1/1/0001 12:00:00 AM"))
                                {
                                    var smtpInfo = Facade.GetSmtpConfigurations();
                                    if (smtpInfo == null)
                                    {
                                        ReGenerateSessionId();
                                        _facade.ValidateCredentialsByLoginType(domainName, Utility.getOriginalEncryData(PassEncyptHidden.Value, hdfStaticGuid.Value), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                        _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);
                                    }
                                    else
                                    {
                                        foreach (var smtpDetails in smtpInfo)
                                        {
                                            CP.Helper.EmailManager emailManager = new CP.Helper.EmailManager();
                                            emailManager.From = CryptographyHelper.Md5Decrypt(smtpDetails.UserName);
                                            emailManager.Body = string.Concat("Domain user: ", userInfo.LoginName, " is logged  in successfully into Continuity Patrol at ", userInfo.CreateDate, "<br/>", "\r\n\n\n Regards,", "<br/>", "Perpetuuiti Technosoft Pvt.Ltd");
                                            emailManager.Subject = "Active Ditectory Login Info";
                                            SmtpConfiguration smtpConfig = new SmtpConfiguration();
                                            smtpConfig.SmtpHost = CryptographyHelper.Md5Decrypt(smtpDetails.SmtpHost);
                                            smtpConfig.Port = smtpDetails.Port;

                                            if (!string.IsNullOrEmpty(smtpDetails.Password))
                                                smtpConfig.Password = CryptographyHelper.Md5Decrypt(smtpDetails.Password);

                                            smtpConfig.EnableSSL = true;
                                            smtpConfig.IsBodyHTML = true;
                                            foreach (var info in superUser)
                                            {
                                                smtpConfig.UserName = info.UserInformation.Email;
                                                string output = emailManager.SendTestMail(smtpConfig);
                                            }

                                            ReGenerateSessionId();
                                            _facade.ValidateCredentialsByLoginType(domainName, Utility.getOriginalEncryData(PassEncyptHidden.Value, hdfStaticGuid.Value), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                            _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);

                                        }
                                    }
                                }
                                else
                                {
                                    ReGenerateSessionId();
                                    _facade.ValidateCredentialsByLoginType(domainName, Utility.getOriginalEncryData(PassEncyptHidden.Value, hdfStaticGuid.Value), Convert.ToInt32(ddlCompanyId.SelectedValue), loginType.ToString());
                                    _facade.UpdateUserSessionIdByID(userInfo.Id, newSessionId);
                                }
                            }
                        }

                    }//here isauth is finishing
                    else
                    {
                        _logger.InfoFormat("AD user not authenticated " + UserEncrypt.Value + " " + " with domain " + txtDomain.Text, CpExceptionType.ADInvalidADAuthentication);
                        throw new CpException(CpExceptionType.ADInvalidADAuthentication);
                    }
                }
                else
                {
                    if (userDetail != null)
                    {
                        if (!string.IsNullOrEmpty(userDetail.SessionId) && ConfigurationManager.AppSettings["IsMultiuser"].Equals("false", StringComparison.OrdinalIgnoreCase))
                        {
                            _logger.Info("User is already Logged in");
                            ShowErrorDiv("User is already Logged in".ToUpper());
                            Session.Abandon();
                            return;
                        }

                        orignalStr = CryptographyHelper.Md5Decrypt(userDetail.LoginPassword);
                        userEncryptPass = Utility.getHashKeyByString(orignalStr, hdfStaticGuid.Value);
                        if (userEncryptPass.Equals(PassEncyptHidden.Value))
                        {
                            if (ConfigurationManager.AppSettings["IsCaptchaRequired"].Equals("true", StringComparison.OrdinalIgnoreCase))
                            {
                                captcha1.ValidateCaptcha(txtcaptcha.Text.Trim());
                            }
                            if (!captcha1.UserValidated)
                            {
                                ShowErrorDiv("Please Enter valid Captcha".ToUpper());
                            }
                            else
                            {
                                if (ConfigurationManager.AppSettings["IsCompanyId"].Equals("false"))
                                {
                                    ReGenerateSessionId();
                                    _facade.ValidateCredentials(CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value)), CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(userEncryptPass, hdfStaticGuid.Value)), Convert.ToInt32(ddlCompanyId.SelectedValue));
                                    bool result = _facade.UpdateUserSessionIdByID(userDetail.Id, newSessionId);
                                }
                                else
                                {
                                    ReGenerateSessionId();
                                    _facade.ValidateCredentials(CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value)), CryptographyHelper.Md5Encrypt(Utility.getOriginalEncryData(userEncryptPass, hdfStaticGuid.Value)), cId);
                                    _facade.UpdateUserSessionIdByID(userDetail.Id, newSessionId);
                                }
                                string guid = Guid.NewGuid().ToString();
                                Session["LoggedIn"] = Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value);
                                // Session["AuthToken"] = guid;  
                                HiddenField hdnID = (HiddenField)Page.Master.FindControl("hdAuthToken");
                                hdnID.Value = guid;
                                var cookie = new HttpCookie("AuthToken", guid);
                                cookie.Domain = ConfigurationManager.AppSettings["SessionCookiesDomain"].ToString();
                                cookie.Path = ConfigurationManager.AppSettings["SessionCookiesPath"].ToString();

                                Response.Cookies.Add(new HttpCookie("AuthToken", guid));
                            }
                        }
                        else
                        {
                            ShowErrorDiv("Invalid User Name Or Password".ToUpper());
                            throw new CpException(CpExceptionType.InvalidUserOrPassword);
                        }
                    }
                    else
                    {
                        ShowErrorDiv("Invalid User Name Or Password".ToUpper());

                        /////////////////////////////////////////////////////////////LoggedInUserCompanyId
                        Session.Abandon();
                        Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", ""));
                        AddCookie();
                        /////////////////////////////////////////////////////////////
                    }
                }

                userDetail = Facade.GetUserByLoginName(Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value));
                var get_OTP_count_panel = Facade.GetUserInfoById(userDetail.Id);
                var settings = Facade.GetSettingBytId("OTP");
                if (settings != null && settings.Value == "1" && userDetail.Role != UserRole.EnterpriseUser)
                {
                    try
                    {
                        _logger.Info("|$$$| in Generate OTP.  Logged in UserName : " + Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value));
                        _logger.Info("|$$$| in Generate OTP.  Logged in User Id : " + userDetail.Id);


                        UserInfo uInfo = Facade.GetUserInfoById(userDetail.Id);
                        if (string.IsNullOrEmpty(uInfo.Mobile) || uInfo.Mobile.ToUpper() == "NA")
                        {
                            ShowErrorDiv("User Mobile Number not Configured".ToUpper());
                            return;
                        }

                        // _logger.Info("|###| in Login Page.  Featching OTP Details.  111");
                        POTPOSB.OTPCheck _otpcheck = new POTPOSB.OTPCheck();
                        //POTPApplication.OTPCheck _otpcheck = new POTPApplication.OTPCheck();
                        // _logger.Info("|###| in Login Page.  Featching OTP Details.  222");
                        var test = Facade.GetUserByLoginName(Convert.ToString(UserName.Text));
                        if (test != null)
                        {
                            var potpdetails = Facade.GetAllOTPConfig().FirstOrDefault();
                            //_logger.Info("|###| in Login Page.  Featching OTP Details.  333");
                            if (potpdetails != null)
                            {
                                try
                                {

                                    string sessionTime = DateTime.Now.ToString("yyyyMMddHHmmssffff");
                                    Session["SetionTimeForOTP"] = sessionTime;
                                    HDF_SessionTime.Value = sessionTime;


                                    _logger.Info("|###| in Login Page.  Session_Time [GenerateOTP] : " + sessionTime + ".  HiddenField Value [GenerateOTP] : " + HDF_SessionTime.Value);
                                    if (allowRequest())
                                    {
                                        _otpcheck.GenerateNewOTP(potpdetails.URL, potpdetails.Channel, potpdetails.ApplicationName, potpdetails.TransactionCode, uInfo.Mobile.Replace("+91-", ""), sessionTime, potpdetails.MessageType, potpdetails.ProcCode);
                                        //_otpcheck.GenerateNewOTP(potpdetails.URL, potpdetails.WSURL, potpdetails.Channel, potpdetails.ApplicationName, potpdetails.TransactionCode, uInfo.Mobile.Replace("+91-", ""), sessionTime);
                                        //_logger.Info("|###| in Login Page.  Featching OTP Details.  444");
                                        if (get_OTP_count_panel.OTPCount >= 3)
                                        {
                                            modelbg.Visible = true;
                                            pnlOTP.Visible = true;
                                            // btnSave.Enabled = false;
                                            FailureText.Text = "You exceed the OTP Limit.You Entered Three times wrong OTP".ToUpper();
                                        }
                                        else
                                        {
                                            modelbg.Visible = true;
                                            pnlOTP.Visible = true;
                                        }
                                    }
                                    else
                                    {
                                        _logger.Error("*********---Multiple OTP Request is generated from Single User having UserName:"+UserName.Text+" ---*********");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error("Error In OTP Generation. Must Check OTP Configuration.");
                                    modelbg.Visible = true;
                                    pnlOTP.Visible = true;
                                }
                            }
                            else
                            {
                                ShowErrorDiv("OTP Configuration is Not Configured in OTP Signon Config.".ToUpper());
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.InfoFormat("Error while login With OTP." + ex.Message);
                    }
                }
                else
                {
                    //Response.Redirect("~/Admin/Comamndcenter.aspx");

                    if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.Operator
                        || LoggedInUserRole == UserRole.Manager || LoggedInUserRole == UserRole.Custom || LoggedInUserRole == UserRole.DPCLAMUser || LoggedInUserRole == UserRole.ExecutionAccessUser)
                    {
                        HttpContext.Current.Response.Redirect(Constants.UrlConstants.Urls.Admin.CommandCenter, false);
                    }

                    if (LoggedInUserRole == UserRole.EnterpriseUser)
                    {
                        HttpContext.Current.Response.Redirect(Constants.UrlConstants.Urls.Admin.CommandCenter, false);
                    }
                }
            }
            catch (CpException exc)
            {
                UserName.Focus();
                //  Password.Focus();
                divClass.Visible = true;

                /////////////////////////////////////////////////////////////
                Session.Abandon();
                Response.Cookies.Add(new HttpCookie("ASP.NET_SessionId", ""));
                AddCookie();
                /////////////////////////////////////////////////////////////

                if (chkActiveDirectory.Checked == true)
                {
                    _logger.InfoFormat(" Unable to login with user " + "'" + UserEncrypt.Value + "'" + " and domain " + "'" + txtDomain.Text + "'" + " " + exc.Message);
                }
                else
                {
                    _logger.InfoFormat(" Unable to login with user " + "'" + UserEncrypt.Value + "'" + " " + exc.Message);
                }

                if (exc.ExceptionType == CpExceptionType.URAccountLocked)
                {
                    FailureText.Text = exc.Message.ToUpper();
                    _logger.InfoFormat("Error while login" + FailureText.Text);
                }
                else if (exc.ExceptionType == CpExceptionType.CommonUnhandled)
                {
                    _logger.InfoFormat("Error while login" + exc.Message);
                    ExceptionManager.Manage(exc);
                }
                else
                {
                    int attempt = GetLoginAttempts();
                    if (attempt == MaxLoginAttempt)
                    {
                        if (chkActiveDirectory.Checked == false)
                        {
                            if (_facade.LockUserAccount(UserEncrypt.Value))
                            {
                                FailureText.Text = "Your account is locked.Contact your Administrator.".ToUpper();
                                _logger.ErrorFormat("{0} - Account is locked due to unsuccessful login limit exceeded - {1}", HostAddress, UserEncrypt.Value);
                            }
                        }
                        else
                        {
                            FailureText.Text = exc.Message.ToUpper() + "" + UserEncrypt.Value;
                            _logger.InfoFormat("AD user not authenticated " + UserEncrypt.Value + " with domain" + txtDomain.Text, FailureText.Text);
                            //    FailureText.Text = "Your account is locked.Contact your Administrator.".ToUpper();
                            //    _logger.ErrorFormat("{0} - Account is locked due to unsuccessful login limit exceeded - {1}", HostAddress, UserName.Text);
                        }
                    }
                    else
                    {
                        FailureText.Text = exc.Message.ToUpper();
                        //_logger.ErrorFormat("{0} - {1} - {2}", HostAddress, exc.Message, UserEncrypt.Value);
                        _logger.ErrorFormat("{0} - Please Check Username and Password ", HostAddress);
                    }
                }
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured while login", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }



        public static bool allowRequest()
        {
            try
            {
                DateTime currentTime = DateTime.Now;

                // Check if enough time has passed since the last sent message
                if ((currentTime - lastSentTime) < sendInterval)
                {
                    // Rate limit exceeded, wait before calling generate OTP
                    // You can choose to sleep the thread or perform other actions here
                    // For demonstration, let's just wait for a short duration
                    // System.Threading.Thread.Sleep(1000); // Wait for 1 second
                    currentTime = DateTime.Now; // Update current time
                    return false;

                }

                // Update last sent time
                lastSentTime = currentTime;

                // Rate limit not exceeded, can call OTP generation
                return true;
            }
            catch (Exception ex)
            {
                if (ex.InnerException!=null)
                    _logger.Error("*******------InnerException occurred in allowRequest: " + ex.InnerException);
                else
                    _logger.Error("*******------Exception occurred in allowRequest: " + ex.Message);



                return false; // or throw the exception again if you want to propagate it further
            }
        }



        public void ShowErrorDiv(string msg)
        {
            divClass.Visible = true;
            lblerror.Visible = false;
            FailureText.Visible = true;
            FailureText.Text = msg;
            UserName.Focus();
        }

        protected void BtnContinueClick(object sender, EventArgs e)
        {
            try
            {
                if (txtKey.Text != "" && rbKey.Checked && chkKey.Checked)
                {
                    string activekey = txtKey.Text;

                    string decryptString = DecryptString(activekey);

                    if (string.IsNullOrEmpty(decryptString))
                    {
                        companymac.Visible = true;

                        return;
                    }
                    string[] check = decryptString.Split('/');
                    _companyname = check[1];
                    string ipAddress = check[3];
                    if (CheckCompanyNameExist())
                    {
                        if (ipAddress == GetMacAddress())
                        {
                            CreateLicenceKeyFile(activekey);
                            var licence = new Licencekey();
                            IList<Licencekey> str = Facade.GetAllLicenceKeies();

                            if (str != null)
                            {
                                licence.Id = 1;
                                licence.Key = activekey;
                                licence.ValidFrom = DateTime.Now;
                                licence.ValidTo = DateTime.Now.AddYears(50);
                                licence.Status = 1;
                                licence.UpdatorId = 1;
                                var update = Facade.UpdateLicencekey(licence);
                                licensebg.Visible = false;
                            }
                            else
                            {
                                licence.Key = activekey;
                                licence.ValidFrom = DateTime.Now;
                                licence.ValidTo = DateTime.Now.AddYears(50);
                                licence.Status = 1;
                                licence.CreatorId = 1;
                                var test = Facade.AddLicenceKey(licence);
                                licensebg.Visible = false;
                            }
                        }
                        else
                        {
                            companymac.Visible = true;
                        }
                    }
                    else
                    {
                        companymac.Visible = true;
                    }
                }
                else
                {
                    SelectAll.Visible = true;
                }
            }
            catch (Exception)
            {
            }
        }

        public override void PrepareView()
        {

        }


        [WebMethod]
        public static string DiscoverDomains()
        {
            try
            {
                StringBuilder _domain = new StringBuilder();
                List<Domain> _domains = new List<Domain>();
                var returnValue = "";

                if (ConfigurationManager.AppSettings["IsADUser"].Equals("true", StringComparison.OrdinalIgnoreCase))
                {
                    Forest currentForest = Forest.GetCurrentForest();
                    if (currentForest != null && currentForest.Domains.Count > 0)
                    {
                        DomainCollection domains = currentForest.Domains;
                        //foreach (Domain objDomain in domains)
                        //{
                        //    _domains.Add(objDomain);

                        //    returnValue = string.Format("{0}{1}", returnValue, objDomain.Name);
                        //    _logger.Info("Discovered domains are " + returnValue);
                        //    return returnValue.TrimEnd(',');
                        //}
                        //return returnValue;
                        foreach (Domain objDomain in domains)
                        {
                            _domains.Add(objDomain);
                            _domain.Append(objDomain.Name);
                            _domain.Append(",");
                            // _domains.Add(objDomain);
                            // _domain.Append(",petc,petch12");
                            //returnValue = string.Format("{0}{1}", returnValue, objDomain.Name);
                            _logger.Info("Discovered domains are " + objDomain.Name);
                            //return returnValue.TrimEnd(',');
                            // return returnValue;
                        }

                        string domainname = "icicibankltd.com";
                        _domain.Append(domainname);
                        _domain.Append(",");

                        //return returnValue;
                        returnValue = string.Format("{0}{1}", returnValue, _domain.ToString());
                        returnValue.Remove(returnValue.Length - 1, 1);
                    }
                    else
                    {
                        string domainname = "icicibankltd.com";
                        _domain.Append(domainname);
                        _domain.Append(",");

                        returnValue = string.Format("{0}{1}", returnValue, _domain.ToString());
                        returnValue.Remove(returnValue.Length - 1, 1);
                    }
                }
                else
                {
                    string domainname = "icicibankltd.com";
                    _domain.Append(domainname);
                    _domain.Append(",");

                    returnValue = string.Format("{0}{1}", returnValue, _domain.ToString());
                    returnValue.Remove(returnValue.Length - 1, 1);
                }
                return returnValue;
                // return null;
            }

            catch (Exception ex)
            {
                // _logger.Info("Exception Occured while populating company names :" + ex.InnerException.Message);
                // ActivityLogger.
                _logger.Info("Discover domains return empty domains");
                return null;
            }
        }

        public static bool AuthenticateADUser(string _domainName, string _domainUser, string _domianPassword)
        {
            try
            {
                Forest currentForest = Forest.GetCurrentForest();
                DomainCollection domains = currentForest.Domains;

                using (PrincipalContext pc = new PrincipalContext(ContextType.Domain, _domainName))
                {
                    bool isValid = pc.ValidateCredentials(_domainUser, _domianPassword);

                    if (isValid)
                    {
                        _logger.Info("User " + _domainUser + " authenticated with domain " + _domainName);
                        return true;
                    }
                    else
                    {
                        _logger.Info("User " + _domainUser + " not authenticated with domain " + _domainName);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Info("Authentication failed for: " + _domainUser + " " + " with domain " + _domainName + " with error message " + ex.Message);
                return false;
            }
        }


        protected void chkActiveDirectory_CheckedChanged(object sender, EventArgs e)
        {
            if (chkActiveDirectory.Checked)
            {
                txtDomain.Visible = true;
                lbldomainName.Visible = true;
                pnlDomain.Visible = true;
                //POTPApplication.OTPCheck otpcheck = new POTPApplication.OTPCheck();
                //otpcheck.GenerateNewOTP();
            }
            else
            {
                txtDomain.Visible = false;
                lbldomainName.Visible = false;
                pnlDomain.Visible = false;
            }
        }


        public static bool IsAccountLocked(string _domainName, string _domainUser)
        {
            try
            {
                bool IsLocked = false;
                PrincipalContext ctx = new PrincipalContext(ContextType.Domain);
                UserPrincipal user = UserPrincipal.FindByIdentity(ctx, _domainUser);
                if (user != null)
                {
                    string displayName = user.DisplayName;

                    if (user.IsAccountLockedOut())
                    {
                        IsLocked = true;

                    }
                    else
                        IsLocked = false;
                }
                return IsLocked;
            }
            catch (Exception ex)
            {
                return true;
            }
        }

        protected string Decodeval(string input)
        {
            var decode = "";
            var base64EncodedBytes = System.Convert.FromBase64String(input);
            decode = System.Text.Encoding.UTF8.GetString(base64EncodedBytes);
            return decode;

        }

        protected void ReGenerateSessionId()
        {
            try
            {
                newSessionId = string.Empty;
                SessionIDManager manager = new SessionIDManager();
                string oldId = manager.GetSessionID(Context);
                string newId = manager.CreateSessionID(Context);
                bool isAdd = false, isRedir = false;
                manager.RemoveSessionID(Context);
                manager.SaveSessionID(Context, newId, out isRedir, out isAdd);



                HttpApplication ctx = (HttpApplication)HttpContext.Current.ApplicationInstance;
                HttpModuleCollection mods = ctx.Modules;
                System.Web.SessionState.SessionStateModule ssm = (SessionStateModule)mods.Get("Session");
                System.Reflection.FieldInfo[] fields = ssm.GetType().GetFields(BindingFlags.NonPublic | BindingFlags.Instance);
                SessionStateStoreProviderBase store = null;
                System.Reflection.FieldInfo rqIdField = null, rqLockIdField = null, rqStateNotFoundField = null;



                SessionStateStoreData rqItem = null;
                foreach (System.Reflection.FieldInfo field in fields)
                {
                    if (field.Name.Equals("_store")) store = (SessionStateStoreProviderBase)field.GetValue(ssm);
                    if (field.Name.Equals("_rqId")) rqIdField = field;
                    if (field.Name.Equals("_rqLockId")) rqLockIdField = field;
                    if (field.Name.Equals("_rqSessionStateNotFound")) rqStateNotFoundField = field;

                    if ((field.Name.Equals("_rqItem")))
                    {
                        rqItem = (SessionStateStoreData)field.GetValue(ssm);
                    }
                }
                object lockId = rqLockIdField.GetValue(ssm);

                if ((lockId != null) && (oldId != null))
                {
                    store.RemoveItem(Context, oldId, lockId, rqItem);
                }

                rqStateNotFoundField.SetValue(ssm, true);
                rqIdField.SetValue(ssm, newId);
                newSessionId = newId;

            }
            catch (Exception ex)
            {
                string msg = ex.InnerException != null ? ex.InnerException.Message : string.Empty;
                _logger.Info("Error ouccred while executing ReGenerateSessionId() : " + ex.Message + msg + ex.StackTrace);

            }
        }

        protected void btnsave_Click(object sender, EventArgs e)
        {
            try
            {
                lblsmtpsavemessage.Visible = false;
                //  int OTP_Attempt = 0;
                IncorrectOTP.Visible = false;
                Blank_otp.Visible = false;
                if (string.IsNullOrEmpty(LoginName))
                    userDetail = Facade.GetUserByLoginName(Utility.getOriginalEncryData(UserEncrypt.Value, hdfStaticGuid.Value));
                else
                    userDetail = Facade.GetUserByLoginName(LoginName);
                _logger.Info("|@@@| in btnsave_Click.  Logged in UserName : " + userDetail.UserName);
                _logger.Info("|@@@| in btnsave_Click.  Logged in User Id : " + userDetail.Id);

                UserInfo uInfo = Facade.GetUserInfoById(userDetail.Id);
                //  _logger.Info("|@@@| in btnsave_Click.  Logged in User Mobile Number : " + uInfo.Mobile);
                if (string.IsNullOrEmpty(uInfo.Mobile) || uInfo.Mobile.ToUpper() == "NA")
                {
                    ShowErrorDiv("User Mobile Number not Configured".ToUpper());
                    return;
                }
                //UserInfo get_otp_count_new_user_lock = Facade.GetUserInfoById(userDetail.Id);
                //if (get_otp_count_new_user_lock.OTPCount >= 3 && get_otp_count_new_user_lock.IsActive == 1)
                //{
                //    int set_zero_count = 0;

                //  POTPApplication.OTPCheck _otpcheck = new POTPApplication.OTPCheck();


                //  POTPApplication.OTPCheck _otpcheck = new POTPApplication.OTPCheck();

                //    var update_otp_count_zero = Facade.UpdateOtpCount(userDetail.Id, set_zero_count);
                //}
                //  POTPApplication.OTPCheck _otpcheck = new POTPApplication.OTPCheck();

                POTPOSB.OTPCheck _otpcheck = new POTPOSB.OTPCheck();
                if (!string.IsNullOrEmpty(txtotp.Text))
                {
                    var potpdetails = Facade.GetAllOTPConfig().FirstOrDefault();

                    string sessionTime = "";
                    if (Session["SetionTimeForOTP"] != null)
                    {
                        sessionTime = Session["SetionTimeForOTP"].ToString();
                        _logger.Info("|###- 1| in Login Page.  Session_Time From Session [VerifyOTP] : " + sessionTime);
                    }
                    else if (!string.IsNullOrEmpty(HDF_SessionTime.Value))
                    {
                        sessionTime = HDF_SessionTime.Value;
                        _logger.Info("|###- 2| in Login Page.  Session_Time From Hiddent Field [VerifyOTP] : " + sessionTime);
                    }
                    else
                    {
                        _logger.Info("|-ERROR-| in btnsave_Click.  Session['SetionTimeForOTP'] & Hiddenfield Both is null.");
                        return;
                    }

                    _logger.Info("|###| in btnsave_Click.  Session_Time [Verify OTP] : " + sessionTime + ".  HiddenField Value [Verify OTP] : " + HDF_SessionTime.Value);
                    string success = "";

                    success = _otpcheck.VerifyNewOTP(potpdetails.URL, potpdetails.Channel, potpdetails.ApplicationName, potpdetails.TransactionCode,
                          uInfo.Mobile.Replace("+91-", ""), sessionTime, Convert.ToString(txtotp.Text), potpdetails.VerifyMessageType, potpdetails.VerifyProcCode);


                    //success = _otpcheck.VerifyNewOTP(potpdetails.URL, potpdetails.WSURL, potpdetails.Channel, potpdetails.ApplicationName, potpdetails.TransactionCode, uInfo.Mobile.Replace("+91-", ""), sessionTime, Convert.ToString(txtotp.Text));
                    // success = "success";
                    if (!string.IsNullOrEmpty(success) && success.ToLower() == "success")
                    {
                        _logger.Info("====== Regerating Session ID , validating UserCredentials after sucessfull OTP validation ====== ");
                        //HDF_SessionTime.Value = "";
                        //_logger.Info("calling  ReGenerateSessionId() : ");
                        //OTP_Attempt = 0;
                        //var reset_otp_count = Facade.UpdateOtpCount(userDetail.Id, OTP_Attempt);
                        //IncorrectOTP.Visible = false;
                        //Blank_otp.Visible = false;
                        //_logger.Info("calling  ReGenerateSessionId() : ");

                        //ReGenerateSessionId();                       
                        //if (!string.IsNullOrEmpty(hfUserInfoId.Value))
                        //    _facade.UpdateUserSessionIdByID(hfUserInfoId.Value.ToInteger(), newSessionId);
                        //else
                        //    _facade.UpdateUserSessionIdByID(userId, newSessionId);

                        //Response.Redirect("~/Admin/CommandCenter.aspx");
                        //  UpdatePanel_smtphost.Update();



                        HDF_SessionTime.Value = "";
                        OTP_Attempt = 0;
                        var reset_otp_count = Facade.UpdateOtpCount(userDetail.Id, OTP_Attempt);
                        IncorrectOTP.Visible = false;
                        Blank_otp.Visible = false;
                        _logger.Info("calling  ReGenerateSessionId() : ");
                        ReGenerateSessionId();
                        _logger.Info("===== 1 called  ReGenerateSessionId() : ");

                        _logger.Info("===== 2 calling  ValidateCredentialsByLoginType() : ");
                        _facade.ValidateCredentialsByLoginType(hfDomainName.Value.ToString(), Utility.getOriginalEncryData(PassEncyptHidden.Value, hdfStaticGuid.Value), Convert.ToInt32(ddlCompanyId.SelectedValue), LoginType.AD.ToString());
                        _logger.Info("==== 3 called  ValidateCredentialsByLoginType() : ");

                        _logger.Info("==== 4 calling  UpdateUserSessionIdByID() : ");
                        _facade.UpdateUserSessionIdByID(hfUserInfoId.Value.ToInteger(), newSessionId);
                        _logger.Info("==== 5 called  UpdateUserSessionIdByID() : ");

                        Response.Redirect("~/Admin/CommandCenter.aspx");



                        return;
                    }
                    else
                    {
                        // ShowErrorDiv("OTP is Not Validated.Please put correct OTP!".ToUpper());
                        ShowErrorDiv("OTP is Not Validated.Please put correct OTP!".ToUpper());
                        Blank_otp.Visible = false;
                        IncorrectOTP.Visible = true;
                        IncorrectOTP.Text = "Please Enter Valid OTP";
                        UserInfo get_otp_count = Facade.GetUserInfoById(userDetail.Id);
                        if (get_otp_count.OTPCount >= 1)
                        {
                            int new_count = get_otp_count.OTPCount;
                            new_count++;
                            var update_otp_count_new = Facade.UpdateOtpCount(userDetail.Id, new_count);
                        }
                        else
                        {
                            OTP_Attempt++;
                            var update_otp_count = Facade.UpdateOtpCount(userDetail.Id, OTP_Attempt);

                            // UserInfo get_otp_count = Facade.GetUserInfoById(userDetail.Id);
                        }
                        UserInfo get_otp_count_new = Facade.GetUserInfoById(userDetail.Id);

                        if (get_otp_count_new.OTPCount >= 3)
                        {
                            pnlOTP.Visible = false;
                            if (_facade.LockUserAccount(userDetail.LoginName))
                            //  if (_facade.LockUserAccount(UserEncrypt.Value))
                            {
                                //  divClass.Visible = true;
                                //FailureText.Text = "Your account is locked.Contact your Administrator.".ToUpper();



                                _logger.ErrorFormat("{0} - Account is locked due to Wrong OTP limit exceeded - {1}", HostAddress, UserEncrypt.Value);

                                modelbg.Visible = false;
                                OnLoad(e);
                                //Response.Redirect("~/Login.aspx");

                                ShowErrorDiv("Your account is locked.You exceed your OTP Limit!".ToUpper());


                            }
                        }
                        //    UpdatePanel_smtphost.Update();
                        return;
                    }

                }
                else
                {
                    //  ShowErrorDiv("OTP is Not Validated.Please put correct OTP!".ToUpper());
                    ShowErrorDiv("OTP is Not Validated.Please put correct OTP!".ToUpper());
                    IncorrectOTP.Visible = false;
                    Blank_otp.Visible = true;
                    Blank_otp.Text = "You Entered Blank OTP";
                    UserInfo get_otp_count = Facade.GetUserInfoById(userDetail.Id);
                    if (get_otp_count.OTPCount >= 1)
                    {
                        int new_count = get_otp_count.OTPCount;
                        new_count++;
                        var update_otp_count_new = Facade.UpdateOtpCount(userDetail.Id, new_count);
                    }
                    else
                    {
                        OTP_Attempt++;
                        var update_otp_count = Facade.UpdateOtpCount(userDetail.Id, OTP_Attempt);

                        // UserInfo get_otp_count = Facade.GetUserInfoById(userDetail.Id);
                    }
                    UserInfo get_otp_count_new = Facade.GetUserInfoById(userDetail.Id);

                    if (get_otp_count_new.OTPCount >= 3)
                    {
                        pnlOTP.Visible = false;
                        if (_facade.LockUserAccount(userDetail.LoginName))
                        //  if (_facade.LockUserAccount(UserEncrypt.Value))
                        {
                            //  divClass.Visible = true;
                            //FailureText.Text = "Your account is locked.Contact your Administrator.".ToUpper();



                            _logger.ErrorFormat("{0} - Account is locked due to Wrong OTP limit exceeded - {1}", HostAddress, UserEncrypt.Value);

                            modelbg.Visible = false;
                            OnLoad(e);
                            //Response.Redirect("~/Login.aspx");

                            //  ShowErrorDiv("Your account is locked.You exceed your OTP Limit!".ToUpper());


                        }
                    }
                    //   UpdatePanel_smtphost.Update();
                    return;
                }

            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured while validating OTP", ex, Page);
                ExceptionManager.Manage(customEx);

                ShowErrorDiv("OTP is Not Validated.Please put correct OTP!".ToUpper());
                return;
            }

        }

        protected void Lkbtnclosesmtphost_Click(object sender, EventArgs e)
        {
            pnlOTP.Visible = false;
            modelbg.Visible = false;
            Response.Redirect("~/Login.aspx");

        }

        protected void btnLoginSSO_Click(object sender, EventArgs e)
        {
            try
            {
                _logger.Info("====== Inside method btnLoginSSO_Click ========== ");
                _logger.Info("====== Checking ReturnURL ========== ");
                var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                _logger.Info("====== Initiating single sign-on to the identity provider ========== ");
                string partnerIdP = ConfigurationManager.AppSettings["PartnerIdP"];
                _logger.Info("====== Calling method InitiateSSO ========== ");
                SAMLServiceProvider.InitiateSSO(Response, returnUrl, partnerIdP);
                _logger.Info("====== Called method InitiateSSO ========== ");
            }
            catch (Exception ex)
            {
                _logger.Error("Exception occurred in btnLoginSSO_Click method  : " + ex.Message);
            }
        }

        public void OTP_Validation(IDictionary<string, string> attributes)
        {
            _logger.Info("====== Called method OTP_Validation() ========== ");

            var userEmail = attributes.Values.FirstOrDefault();
            _logger.Info("====== Email  for authentication   " + userEmail);

            var user = Facade.getUserDetailsByEmail(userEmail);
            var _user = Facade.GetUserById(user.UserId);
            var get_OTP_count_panel = Facade.GetUserInfoById(user.Id);
            var settings = Facade.GetSettingBytId("OTP");
            LoggedInUserRole = _user.Role;
            userId = user.UserId;
            LoginName = _user.LoginName;
            if (settings != null && settings.Value == "1" && _user.Role != UserRole.EnterpriseUser)
            {
                try
                {
                    _logger.Info("|$$$| in Generate OTP.  Logged in UserName : " + _user.LoginName);
                    _logger.Info("|$$$| in Generate OTP.  Logged in User Id : " + _user.Id);


                    if (string.IsNullOrEmpty(user.Mobile) || user.Mobile.ToUpper() == "NA")
                    {
                        ShowErrorDiv("User Mobile Number not Configured".ToUpper());
                        return;
                    }
                    POTPOSB.OTPCheck _otpcheck = new POTPOSB.OTPCheck();
                    var test = Facade.GetUserByLoginName(Convert.ToString(_user.LoginName));
                    if (test != null)
                    {
                        var potpdetails = Facade.GetAllOTPConfig().FirstOrDefault();
                        if (potpdetails != null)
                        {
                            try
                            {

                                string sessionTime = DateTime.Now.ToString("yyyyMMddHHmmssffff");
                                Session["SetionTimeForOTP"] = sessionTime;
                                HDF_SessionTime.Value = sessionTime;
                                _logger.Info("|###| in Login Page.  Session_Time [GenerateOTP] : " + sessionTime + ".  HiddenField Value [GenerateOTP] : " + HDF_SessionTime.Value);

                                _otpcheck.GenerateNewOTP(potpdetails.URL, potpdetails.Channel, potpdetails.ApplicationName, potpdetails.TransactionCode, user.Mobile.Replace("+91-", ""), sessionTime, potpdetails.MessageType, potpdetails.ProcCode);
                                if (get_OTP_count_panel.OTPCount >= 3)
                                {
                                    modelbg.Visible = true;
                                    pnlOTP.Visible = true;
                                    FailureText.Text = "You exceed the OTP Limit.You Entered Three times wrong OTP".ToUpper();
                                }
                                else
                                {
                                    modelbg.Visible = true;
                                    pnlOTP.Visible = true;
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.Error("Error In OTP Generation. Must Check OTP Configuration.");
                                modelbg.Visible = true;
                                pnlOTP.Visible = true;
                            }
                        }
                        else
                        {
                            ShowErrorDiv("OTP Configuration is Not Configured in OTP Signon Config.".ToUpper());
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.InfoFormat("Error while login With OTP." + ex.Message);
                }
            }
            else
            {


                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.Operator
                    || LoggedInUserRole == UserRole.Manager || LoggedInUserRole == UserRole.Custom || LoggedInUserRole == UserRole.DPCLAMUser || LoggedInUserRole == UserRole.ExecutionAccessUser)
                {
                    HttpContext.Current.Response.Redirect(Constants.UrlConstants.Urls.Admin.CommandCenter, false);
                }

                if (LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    HttpContext.Current.Response.Redirect(Constants.UrlConstants.Urls.Admin.CommandCenter, false);
                }
            }
        }
    }
}
