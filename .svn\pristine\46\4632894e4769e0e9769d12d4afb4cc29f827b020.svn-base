﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using log4net;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.ExceptionHandler;

namespace CP.UI.Admin
{
    public partial class InfraApplicationManagement : InfraObjectsBasePage
    {
        private static string _infraObjectName;
        private static int _infraObjectId;
        private static int _currentLoginUserId;
        private static string _loggedInUserName;
        private static string _loggedInUserRole;
        private static IFacade _facade = new Facade();
        private static string _hostAddress = string.Empty;
        private static ILog _logger = LogManager.GetLogger(typeof(InfraApplicationManagement));
        private static InfraObject currentinfraObject;
        private static int _CurrentInfraObjectDrOperationStatus;

        public override void PrepareView()
        {
            Utility.SelectMenu(Master, "Module1");

            currentinfraObject = CurrentInfraObject;

            if (LoggedInUserId > 0)
            {
                _currentLoginUserId = LoggedInUserId;
                _loggedInUserName = LoggedInUserName;
                _hostAddress = HostAddress;
            }
            _loggedInUserRole = LoggedInUserRole.ToString();
            lbluserrole.Text = _loggedInUserRole;
            if (CurrentInfraObjectId > 0)
            {
                BindVariable();
                BindInfraObjectInfo();
                Session["infraObjectId"] = _infraObjectId;
            }

            if (IsPostBack)
            {
                //  EnableButton();
            }
            else
            {
                ConfirmButtonExtender1.ConfirmText = "Are you sure want to delete disk monitor details for infraobject (" + CurrentInfraObject.Name + ") ? ";
            }

            hdnUserRole.Value = LoggedInUserRole.ToString();
        
        }


        #region EnableButton on Page load(Jquery)
        [WebMethod]
        public static string EnableButtonControls()
        {
            #region Old Code
            //var maintenance = _facade.GetMaintainenceByInfraObjectId(_infraObjectId);
            //User userinfo = _facade.GetLoginNameByCrtrId(maintenance.CreatorId);
            //var infraobjectStatus = _facade.GetInfraObjectById(_infraObjectId);

            //if (maintenance != null)
            //{
            //   // return "Success / " + _loggedInUserName + "," + maintenance.Reason + "," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;
            //    return "Success / " + userinfo.LoginName + "," + maintenance.Reason + "," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;
            
            
            //}
            //else if (infraobjectStatus != null)
            //{
            //    //return infraobjectStatus.State + "/" + _loggedInUserName + ",maintenance," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;

            //    return infraobjectStatus.State + "/" + userinfo.LoginName + ",maintenance," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;
            //}
            //return "Error";
            #endregion Old Code

            User userinfo = new User();
            var maintenance = _facade.GetMaintainenceByInfraObjectId(_infraObjectId);

            // User userinfo = _facade.GetLoginNameByCrtrId(maintenance.CreatorId);
            // Changes done for CPROOT-24571
            if (maintenance != null && maintenance.CreatorId > 0)
                userinfo = _facade.GetLoginNameByCrtrId(maintenance.CreatorId);

            var infraobjectStatus = _facade.GetInfraObjectById(_infraObjectId);

            if (maintenance != null)
            {
                // return "Success / " + _loggedInUserName + "," + maintenance.Reason + "," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;
                return "Success / " + userinfo.LoginName + "," + maintenance.Reason + "," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;


            }
            else if (infraobjectStatus != null && !string.IsNullOrEmpty(userinfo.LoginName))
            {
                //return infraobjectStatus.State + "/" + _loggedInUserName + ",maintenance," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;

                return infraobjectStatus.State + "/" + userinfo.LoginName + ",maintenance," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;
            }
            else
            {
                return infraobjectStatus.State + "/" + _loggedInUserName + ",maintenance," + infraobjectStatus.State + "," + _CurrentInfraObjectDrOperationStatus + "," + _loggedInUserRole;
            }
            return "Error";

        }
        #endregion

        // private void EnableReason()
        // {
        //    var maintenance = Facade.GetMaintainenceByApplicationId(CurrentApplicationId);
        //    if (maintenance != null)
        //    {
        //        trMaintainence.Visible = true;
        //        if(maintenance.Type==1)
        //        {
        //            lblReasonMaintenance.Text = "User <b> " +
        //                                   LoggedInUserName +
        //                                   " </b>   has put this group in  <b> Locked Mode </b> for reason: " +
        //                                    maintenance.Reason;
        //        }
        //        else
        //        {
        //            lblReasonMaintenance.Text = "User <b> " +
        //                                   LoggedInUserName +
        //                                   " </b>   has put this group in  <b> Maintenance Mode </b> for reason: " +
        //                                    maintenance.Reason;
        //        }
        //    }
        // }

        private void BindVariable()
        {
            _infraObjectName = CurrentInfraObject.Name;
            _infraObjectId = CurrentInfraObjectId;
            _currentLoginUserId = LoggedInUserId;
            _loggedInUserName = LoggedInUser.LoginName;
            _CurrentInfraObjectDrOperationStatus = CurrentInfraObject.DROperationStatus;
        }

        private void BindInfraObjectInfo()
        {
            if (currentinfraObject.RecoveryType == 62)
            {
                if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                {
                    var prServer = Facade.GetServerById(CurrentInfraObject.DRServerId2);
                    if (prServer != null)
                    {
                        lblPRHost.Text = prServer.Name;
                        //  lblPRHost.Text = prServer.HostName;
                        lblPRIP.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                    }
                    else
                    {
                        lblPRHost.Text = "N/A";
                        lblPRIP.Text = "N/A";
                    }
                    switch (prServer.Status.ToString())
                    {
                        case "Pending":
                            Span2.Attributes.Add("class", "fatal");
                            break;
                        case "Down":
                            Span2.Attributes.Add("class", "health-down");
                            break;
                        case "Up":
                            Span2.Attributes.Add("class", "health-up");
                            break;
                    }

                    var drServer = Facade.GetServerById(CurrentInfraObject.PRServerId2);
                    if (drServer != null)
                    {
                        lblDRHost.Text = drServer.Name;
                        //lblDRHost.Text = drServer.HostName;
                        lblDRIP.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);
                    }
                    else
                    {
                        lblDRHost.Text = "N/A";
                        lblDRIP.Text = "N/A";
                    }
                    switch (drServer.Status.ToString())
                    {
                        case "Pending":
                            Span3.Attributes.Add("class", "fatal");
                            break;
                        case "Down":
                            Span3.Attributes.Add("class", "health-down");
                            break;
                        case "Up":
                            Span3.Attributes.Add("class", "health-up");
                            break;
                    }
                }
                else
                {
                    var prServer = Facade.GetServerById(CurrentInfraObject.PRServerId2);
                    if (prServer != null)
                    {
                        lblPRHost.Text = prServer.Name;
                        //  lblPRHost.Text = prServer.HostName;
                        lblPRIP.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                    }
                    else
                    {
                        lblPRHost.Text = "N/A";
                        lblPRIP.Text = "N/A";
                    }
                    if (CurrentInfraObject.DRServerId > 0)
                    {
                        var drServer = Facade.GetServerById(CurrentInfraObject.DRServerId2);
                        if (drServer != null)
                        {
                            lblDRHost.Text = drServer.Name;
                            // lblDRHost.Text = drServer.HostName;
                            lblDRIP.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);
                        }
                        else
                        {
                            lblDRHost.Text = "N/A";
                            lblDRIP.Text = "N/A";
                        }
                        switch (drServer.Status.ToString())
                        {
                            case "Pending":
                                Span3.Attributes.Add("class", "fatal");
                                break;
                            case "Down":
                                Span3.Attributes.Add("class", "health-down");
                                break;
                            case "Up":
                                Span3.Attributes.Add("class", "health-up");
                                break;
                        }

                    }

                    switch (prServer.Status.ToString())
                    {
                        case "Pending":
                            Span2.Attributes.Add("class", "fatal");
                            break;
                        case "Down":
                            Span2.Attributes.Add("class", "health-down");
                            break;
                        case "Up":
                            Span2.Attributes.Add("class", "health-up");
                            break;
                    }
                }



                if (CurrentInfraObject.RecoveryType == (int)ReplicationType.EC2S3DataSync)
                {
                    lblDRHost.Text = "N/A";
                    lblDRIP.Text = "N/A";
                }

                lblInfraObjectApplicationName.Text = CurrentInfraObject.Name;
                if (CurrentInfraObject.MonitoringWorkflow > 0)
                {
                    var workflow = Facade.GetWorkflowById(CurrentInfraObject.MonitoringWorkflow);
                    if (workflow != null)
                    {
                        lblMonitoringWorkflow.Text = workflow.Name; //CurrentInfraObject.Output;
                    }
                }

            }

            else
            {
                if (CurrentInfraObject.DROperationStatus == (int)InfraWorkflowOperation.SwitchOverCompleted)
                {
                    var prServer = Facade.GetServerById(CurrentInfraObject.DRServerId);
                    if (prServer != null)
                    {
                        lblPRHost.Text = prServer.Name;
                        //  lblPRHost.Text = prServer.HostName;
                        lblPRIP.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                    }
                    else
                    {
                        lblPRHost.Text = "N/A";
                        lblPRIP.Text = "N/A";
                    }
                    switch (prServer.Status.ToString())
                    {
                        case "Pending":
                            Span2.Attributes.Add("class", "fatal");
                            break;
                        case "Down":
                            Span2.Attributes.Add("class", "health-down");
                            break;
                        case "Up":
                            Span2.Attributes.Add("class", "health-up");
                            break;
                    }

                    var drServer = Facade.GetServerById(CurrentInfraObject.PRServerId);
                    if (drServer != null)
                    {
                        lblDRHost.Text = drServer.Name;
                        //lblDRHost.Text = drServer.HostName;
                        lblDRIP.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);
                    }
                    else
                    {
                        lblDRHost.Text = "N/A";
                        lblDRIP.Text = "N/A";
                    }
                    switch (drServer.Status.ToString())
                    {
                        case "Pending":
                            Span3.Attributes.Add("class", "fatal");
                            break;
                        case "Down":
                            Span3.Attributes.Add("class", "health-down");
                            break;
                        case "Up":
                            Span3.Attributes.Add("class", "health-up");
                            break;
                    }
                }
                else
                {
                    var prServer = Facade.GetServerById(CurrentInfraObject.PRServerId);
                    if (prServer != null)
                    {
                        lblPRHost.Text = prServer.Name;
                        //  lblPRHost.Text = prServer.HostName;
                        lblPRIP.Text = CryptographyHelper.Md5Decrypt(prServer.IPAddress);
                    }
                    else
                    {
                        lblPRHost.Text = "N/A";
                        lblPRIP.Text = "N/A";
                    }
                    if (CurrentInfraObject.DRServerId > 0)
                    {
                        var drServer = Facade.GetServerById(CurrentInfraObject.DRServerId);
                        if (drServer != null)
                        {
                            lblDRHost.Text = drServer.Name;
                            // lblDRHost.Text = drServer.HostName;
                            lblDRIP.Text = CryptographyHelper.Md5Decrypt(drServer.IPAddress);
                        }
                        else
                        {
                            lblDRHost.Text = "N/A";
                            lblDRIP.Text = "N/A";
                        }
                        switch (drServer.Status.ToString())
                        {
                            case "Pending":
                                Span3.Attributes.Add("class", "fatal");
                                break;
                            case "Down":
                                Span3.Attributes.Add("class", "health-down");
                                break;
                            case "Up":
                                Span3.Attributes.Add("class", "health-up");
                                break;
                        }

                    }

                    switch (prServer.Status.ToString())
                    {
                        case "Pending":
                            Span2.Attributes.Add("class", "fatal");
                            break;
                        case "Down":
                            Span2.Attributes.Add("class", "health-down");
                            break;
                        case "Up":
                            Span2.Attributes.Add("class", "health-up");
                            break;
                    }
                }



                if (CurrentInfraObject.RecoveryType == (int)ReplicationType.EC2S3DataSync)
                {
                    lblDRHost.Text = "N/A";
                    lblDRIP.Text = "N/A";
                }

                lblInfraObjectApplicationName.Text = CurrentInfraObject.Name;
                if (CurrentInfraObject.MonitoringWorkflow > 0)
                {
                    var workflow = Facade.GetWorkflowById(CurrentInfraObject.MonitoringWorkflow);
                    if (workflow != null)
                    {
                        lblMonitoringWorkflow.Text = workflow.Name; //CurrentInfraObject.Output;
                    }
                }

                if (CurrentInfraObject.RecoveryType == (int)ReplicationType.NetAppSnapMirror)
                {
                    Span27.Attributes.Add("class", "netapp-icon");
                    Span1.Attributes.Add("class", "netapp-icon");
                }
            }
        }
        protected void CloseClick(object sender, EventArgs e)
        {
            Close();
        }

        private void Close()
        {
            //ModalPopupExtenderSwitchOver.Hide();

            //lblCreate.Text = "Workflow";

            //ModalPopupExtenderFailOver.Hide();

            //ModalPopupExtenderFailBack.Hide();

            //ModalPopupExtenderSwitchBack.Hide();

            //ModalPopupExtenderCustom.Hide();

            ModalPopupExtenderLock.Hide();

            ModalPopupExtenderMaintenance.Hide();

            ModalPopupExtenderMaintenanceAll.Hide();

            CurrentInfraObject = null;

            Response.Redirect(Request.RawUrl);
        }

        [WebMethod]
        public static string InfraObjectApplicationMaintenance(string values)
        {
            var value = values.Split(',');
            var eventName = string.Empty;
            var unlockTime = string.Empty;
            if (Convert.ToInt32(value[1]) == (int)UnlockMode.Auto)
            {
                unlockTime = value[2];
                eventName = CreateEvent(unlockTime, _infraObjectName, _infraObjectId);
            }
            _facade.AddMaintainence(new Maintenance
            {
                Reason = value[0],
                CreatorId = _currentLoginUserId,
                Mode = Convert.ToInt32(value[1]),
                UnlockTime = unlockTime,
                EventName = eventName,
                InfraObjectId = _infraObjectId,
                IsInfraObject = false,
                Type = Convert.ToInt32(value[3])
            }
            );

            string state = Convert.ToInt32(value[3]) == 2 ? InfraObjectState.Maintenance.ToString() : InfraObjectState.Locked.ToString();

            //bool isSuccess = _facade.UpdateInfraObjectByState(_infraObjectId, state, (int)InfraObjectReplicationStatus.Maintenance);
            bool isSuccess = _facade.UpdateInfraObjectByState(_infraObjectId, Convert.ToInt32(value[3]) == 1 ? InfraObjectState.Locked.ToString() : InfraObjectState.Maintenance.ToString(), Convert.ToInt32(value[3]) == 1 ? (int)InfraObjectReplicationStatus.Locked : (int)InfraObjectReplicationStatus.Maintenance);
            if (isSuccess)
            {
                _logger.DebugFormat("{0} - {2} Infra Object is Maintenance - {1}", _hostAddress, _loggedInUserName, _infraObjectName);

                return "Success / " + _loggedInUserName + "," + _CurrentInfraObjectDrOperationStatus;
            }
            return "Error";
        }

        [WebMethod]
        public static string InfraObjectApplicationMaintenanceAll(string values)
        {
            _facade.DropAllEvent();
            var infraList = _facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(currentinfraObject.BusinessServiceId, currentinfraObject.BusinessFunctionId);

            string returnStatus = string.Empty;
            var eventName = string.Empty;
            var value = values.Split(',');
            var mode = value[1];
            var reason = value[0];
            var unlockTime = value[2];
            try
            {
                foreach (var infraObject in infraList)
                {
                    if (Convert.ToInt32(mode) == (int)UnlockMode.Auto)
                    {
                        eventName = CreateEvent(unlockTime, infraObject.Name, infraObject.Id);
                    }
                    if (infraObject.State == "Maintenance" || infraObject.State == "Replicating")
                    {
                        continue;
                    }
                    _facade.AddMaintainence(new Maintenance
                    {
                        Reason = reason,
                        CreatorId = _currentLoginUserId,
                        Mode = Convert.ToInt32(mode),
                        UnlockTime = unlockTime,
                        InfraObjectId = infraObject.Id,
                        EventName = eventName,
                        Type = Convert.ToInt32(value[3])
                    });

                    try
                    {
                        bool isSuccess = _facade.UpdateAllInfraObjectsByState(InfraObjectState.Maintenance.ToString(), (int)InfraObjectReplicationStatus.Maintenance, infraObject.Id);
                        if (isSuccess)
                        {
                            _logger.DebugFormat("{0} - {2} Infra Object is Maintenance - {1}", _hostAddress, _loggedInUserName, infraObject.Name);

                            returnStatus = "Success / " + _loggedInUserName + "," + _CurrentInfraObjectDrOperationStatus;
                        }
                        else
                        {
                            _logger.DebugFormat("Error while updating state of infraObject {0} by user {1} on {2}", infraObject.Name, _loggedInUserName, _hostAddress);
                            returnStatus = "Error";
                        }
                    }
                    catch (CpException exc)
                    {
                        ExceptionManager.Manage(exc);
                    }

                }
                return returnStatus;

            }
            catch
            {
                return string.Empty;
            }


        }

        //Active Application Infra Object
        [WebMethod]
        public static string InfraObjectApplicationActive()
        {
            var maintainence = _facade.GetMaintainenceByInfraObjectId(_infraObjectId);
            if (maintainence != null)
            {
                bool isEventExist = _facade.GetEventByName(maintainence.EventName);
                if (isEventExist)
                {
                    _facade.DropEventByName(maintainence.EventName);
                }
                _facade.AddMaintainence(new Maintenance
                {
                    Reason = string.Empty,
                    IsInfraObject = false,
                    CreatorId = _currentLoginUserId,
                    Mode = 0,
                    UnlockTime = string.Empty,
                    InfraObjectId = _infraObjectId,
                    EventName = string.Empty,
                    Type = Convert.ToInt32(InfraObjectActivityType.Active)
                });
            }
            bool isSuccess = _facade.UpdateInfraObjectByState(_infraObjectId, InfraObjectState.Active.ToString(), (int)InfraObjectReplicationStatus.Running);
            if (isSuccess)
            {
                _logger.DebugFormat("{0} - {1} Infra Object is Activate - {2}", _hostAddress, _infraObjectName, _loggedInUserName);
                return "Success";
            }
            return "Error";
        }
        //End ActiveAll Application Infra Object

        //ActiveAll Application Infra Object as per selected business function
        [WebMethod]
        public static string InfraObjectApplicationActiveAll()
        {
            //bool eventDelete = _facade.DropAllEvent();
            //if (eventDelete)
            //{
            //    var infraObjects = _facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(currentinfraObject.BusinessServiceId, currentinfraObject.BusinessFunctionId);
            //    bool isSuccess = _facade.UpdateAllInfraObjectsByState(InfraObjectState.Active.ToString(), (int)InfraObjectReplicationStatus.Running, _infraObjectId);
            //    if (isSuccess)
            //    {
            //        _logger.DebugFormat("{0} - All Infra Object Applications are Activate - {1}", _hostAddress, _loggedInUserName);
            //        foreach (var infraObject in infraObjects)
            //        {
            //            if (infraObject.State == "Active" || infraObject.State == "Replicating")
            //            {
            //                continue;
            //            }
            //            _facade.AddMaintainence(new Maintenance
            //            {
            //                Reason = string.Empty,
            //                CreatorId = _currentLoginUserId,
            //                Mode = 0,
            //                UnlockTime = string.Empty,
            //                InfraObjectId = infraObject.Id,
            //                EventName = string.Empty,
            //                Type = Convert.ToInt32(InfraObjectActivityType.Active)
            //            });

            //        }
            //        return "Success";
            //    }
            //}

            //return "Error";
            // _facade.DropAllEvent();
            var infraObjects = _facade.GetInfraObjectByBusinessServiceIdAndBusinessFunctionId(currentinfraObject.BusinessServiceId, currentinfraObject.BusinessFunctionId);

            string returnStatus = string.Empty;
            try
            {
                foreach (var infraObject in infraObjects)
                {
                    if (infraObject.State == "Active" || infraObject.State == "Replicating")
                    {
                        continue;
                    }
                    _facade.AddMaintainence(new Maintenance
                    {
                        Reason = string.Empty,
                        CreatorId = _currentLoginUserId,
                        Mode = 0,
                        UnlockTime = string.Empty,
                        InfraObjectId = infraObject.Id,
                        EventName = string.Empty,
                        Type = Convert.ToInt32(InfraObjectActivityType.Active)
                    });

                    try
                    {
                        bool isSuccess = _facade.UpdateAllInfraObjectsByState(InfraObjectState.Active.ToString(), (int)InfraObjectReplicationStatus.Running, infraObject.Id);
                        if (isSuccess)
                        {
                            _logger.DebugFormat("{0} - {2} Infra Object is Locked - {1}", _hostAddress, _loggedInUserName, infraObject.Name);

                            returnStatus = "Success / " + _loggedInUserName + "," + _CurrentInfraObjectDrOperationStatus;
                        }
                        else
                        {
                            _logger.DebugFormat("Error while updating state of infraObject {0} by user {1} on {2}", infraObject.Name, _loggedInUserName, _hostAddress);
                            returnStatus = "Error";
                        }
                    }
                    catch (CpException exc)
                    {
                        ExceptionManager.Manage(exc);
                    }

                }
                return returnStatus;
            }
            catch
            {
                return string.Empty;
            }


        }
        //End ActiveAll Application Infra Object

        private static bool MaintenanceInfraObjectApplication(string reason, int mode, string unlocktime, int type, string name, int id)
        {
            var eventName = string.Empty;
            var unlockTime = string.Empty;

            //if (mode == (int)UnlockMode.Auto)
            //{
            //    unlockTime = unlocktime;
            //    eventName = CreateEvent(unlockTime, name, id);
            //}
            _facade.AddMaintainence(new Maintenance
            {
                Reason = reason,
                CreatorId = _currentLoginUserId,
                Mode = mode,
                UnlockTime = unlockTime,
                EventName = eventName,
                InfraObjectId = id,
                IsInfraObject = false,
                Type = type
            }
            );
            return _facade.UpdateInfraObjectByState(_infraObjectId, type == 1 ? InfraObjectState.Locked.ToString() : InfraObjectState.Maintenance.ToString(), (int)InfraObjectReplicationStatus.Maintenance);
        }

        private static string CreateEvent(string unlockTime, string infraObjectName, int infraObjectId)
        {
            var eventName = GetDateParameter(infraObjectName);
            //var datediff = Convert.ToDateTime(unlockTime).Subtract(Convert.ToDateTime(DateTime.Now.ToString("dd-MM-yyyy HH:mm")));
            //var minutes = Convert.ToInt32(datediff.TotalMinutes);
            var datediff = Convert.ToDateTime(unlockTime).Subtract(DateTime.Now);
            var minutes = Convert.ToInt32(datediff.TotalMinutes);
            _facade.UnlockInfraObjectByTime(infraObjectId, minutes, eventName);
            return eventName;
        }

        public static string GetDateParameter(string appName)
        {
            var date = DateTime.Now.ToString(CultureInfo.InvariantCulture).Substring(0, DateTime.Now.ToString(CultureInfo.InvariantCulture).Length - 5).
                Replace("/", "").Replace(":", "").Replace(" ", "").Replace("-", "");
            return date + appName;
        }

        protected void BtnSwitchOverClick(object sender, EventArgs e)
        {
            var getProfile = Facade.GetParallelWorkflowProfileByInfraobjectIdAndWorkflowType(CurrentInfraObject.Id, (int)WorkflowManagement.SwitchOver);
            lblErrorMessage.Visible = false;
            lblErrorMessage.Text = string.Empty;
            if (getProfile != null)
            {

                if (getProfile.WorkflowType == (int)WorkflowManagement.SwitchOver)
                {
                    var CurrentURL = Constants.UrlConstants.Urls.Admin.ParallelWorkflow;
                    lblErrorMessage.Visible = false;
                    var sendStr = getProfile.Id + "&" + getProfile.ProfileId;
                    var fsend = CryptographyHelper.Md5Encrypt(sendStr);
                    Helper.Url.Redirect(CurrentURL + "?param=" + fsend);


                }
            }
            else
            {
                lblErrorMessage.Visible = true;
                lblErrorMessage.Text = "SwitchOver Workflow is not attached in " + CurrentInfraObject.Name;
            }

        }

        protected void BtnSwitchBackClick(object sender, EventArgs e)
        {
            var getProfile = Facade.GetParallelWorkflowProfileByInfraobjectIdAndWorkflowType(CurrentInfraObject.Id, (int)WorkflowManagement.SwitchBack);
            lblErrorMessage.Visible = false;
            lblErrorMessage.Text = string.Empty;
            if (getProfile != null)
            {

                if (getProfile.WorkflowType == (int)WorkflowManagement.SwitchBack)
                {
                    var CurrentURL = Constants.UrlConstants.Urls.Admin.ParallelWorkflow;
                    Helper.Url.Redirect(CurrentURL + "?param=" + CryptographyHelper.Md5Encrypt(getProfile.Id + "&" + getProfile.ProfileId));

                }
            }
            else
            {
                lblErrorMessage.Visible = true;
                lblErrorMessage.Text = "SwitchBack Workflow is not attached in " + CurrentInfraObject.Name;
            }


        }

        protected void BtnFailOverClick(object sender, EventArgs e)
        {
            var getProfile = Facade.GetParallelWorkflowProfileByInfraobjectIdAndWorkflowType(CurrentInfraObject.Id, (int)WorkflowManagement.FailOver);
            lblErrorMessage.Visible = false;
            lblErrorMessage.Text = string.Empty;
            if (getProfile != null)
            {

                if (getProfile.WorkflowType == (int)WorkflowManagement.FailOver)
                {
                    var CurrentURL = Constants.UrlConstants.Urls.Admin.ParallelWorkflow;
                    Helper.Url.Redirect(CurrentURL + "?param=" + CryptographyHelper.Md5Encrypt(getProfile.Id + "&" + getProfile.ProfileId));

                }
            }
            else
            {
                lblErrorMessage.Visible = true;
                lblErrorMessage.Text = "FailOver Workflow is not attached in " + CurrentInfraObject.Name;
            }

        }

        protected void BtnFailBackClick(object sender, EventArgs e)
        {
            var getProfile = Facade.GetParallelWorkflowProfileByInfraobjectIdAndWorkflowType(CurrentInfraObject.Id, (int)WorkflowManagement.FailBack);
            lblErrorMessage.Visible = false;
            lblErrorMessage.Text = string.Empty;
            if (getProfile != null)
            {

                if (getProfile.WorkflowType == (int)WorkflowManagement.FailBack)
                {
                    var CurrentURL = Constants.UrlConstants.Urls.Admin.ParallelWorkflow;
                    Helper.Url.Redirect(CurrentURL + "?param=" + CryptographyHelper.Md5Encrypt(getProfile.Id + "&" + getProfile.ProfileId));

                }
            }
            else
            {
                lblErrorMessage.Visible = true;
                lblErrorMessage.Text = "FailBack Workflow is not attached in " + CurrentInfraObject.Name;
            }
        }

        protected void BtnCustomClick(object sender, EventArgs e)
        {
            var getProfile = Facade.GetParallelWorkflowProfileByInfraobjectIdAndWorkflowType(CurrentInfraObject.Id, (int)WorkflowManagement.Custom);
            lblErrorMessage.Visible = false;
            lblErrorMessage.Text = string.Empty;
            if (getProfile != null)
            {

                if (getProfile.WorkflowType == (int)WorkflowManagement.Custom)
                {
                    var CurrentURL = Constants.UrlConstants.Urls.Admin.ParallelWorkflow;
                    Helper.Url.Redirect(CurrentURL + "?param=" + CryptographyHelper.Md5Encrypt(getProfile.Id + "&" + getProfile.ProfileId));

                }
            }
            else
            {
                lblErrorMessage.Visible = true;
                lblErrorMessage.Text = "Custom Workflow is not attached in " + CurrentInfraObject.Name;
            }

        }

        protected void btnLock_Click(object sender, EventArgs e)
        {
            bool isSuccess = Facade.UpdateInfraObjectByState(CurrentInfraObjectId, InfraObjectState.Locked.ToString(), (int)InfraObjectReplicationStatus.Maintenance);
            if (isSuccess)
            {
                PrepareView();
                _logger.DebugFormat("{0} - {1} Infra Object is Locked - {2}", HostAddress, _infraObjectName, LoggedInUserName);
            }

        }

        ///<summary>
        //! This is used for  Infra Object Schedule WorkFlow
        /// <author> Shweta singh - 26-02-2015 </author>
        /// </summary>
        /// 

        protected void btnSchduleWFClick(object sender, EventArgs e)
        {
            Response.Redirect("~/Admin/InfraObjectScheduledWorkflow.aspx");
            //    ModalPopupExtenderInfraobjectScheduleWorkflow.Show();

        }
        protected void lnkBtnViewAlertClick(object sender, EventArgs e)
        {
            Response.Redirect("~/Alert/AlertManagement.aspx");
        }

        #region Enable Diskspace Monitor
        ///<summary>
        /// This region is for Enable Diskspace Monitor
        /// <author>Uma Mehavarnan</author>
        /// <date>16-Mar-2016</date>
        /// </summary>

        protected void lnkBtnEnableDSMonitoring_Click(object sender, EventArgs e)
        {
            modelbg.Visible = true;
            pnlEnableDSMonitoring.Visible = true;
            lblEnableDSMonitoringMsg.Text = "";
            lblEnableDSMonitoringMsg.Visible = false;

            var infraDiskMonitor = _facade.GetInfraobjectDiskMonitorByInfraobjectId(CurrentInfraObjectId);

            if (infraDiskMonitor == null || infraDiskMonitor.Id <= 0)
            {
                rdTimeIntervalPR.SelectedValue = "Minute(s)";
                rdTimeIntervalDR.SelectedValue = "Minute(s)";
                setTimeTR.Visible = true;

                Panel_MinuitePR.Visible = true;
                Panel_HourlyPR.Visible = false;
                Panel_DailyPR.Visible = false;
                txteveryminuitePR.Text = string.Empty;

                Panel_MinuiteDR.Visible = true;
                Panel_HourlyDR.Visible = false;
                Panel_DailyDR.Visible = false;
                txteveryminuiteDR.Text = string.Empty;

                btnSubmit.Text = "Save";
                btnDelete.Visible = false;

            }
            else if (infraDiskMonitor != null && infraDiskMonitor.Id > 0)
            {
                FillInfraobjectDiskMonitor(infraDiskMonitor);
            }
        }

        private void FillInfraobjectDiskMonitor(InfraobjectDiskMonitor infraDiskMonitor)
        {
            txtPRVolumeName.Text = infraDiskMonitor.VolumeNamePR;
            txtPRThreshold.Text = infraDiskMonitor.ThresholdPR.ToString();

            txtDRVolumeName.Text = infraDiskMonitor.VolumeNameDR;
            txtDRThreshold.Text = infraDiskMonitor.ThresholdDR.ToString();

            setTimeTR.Visible = true;

            //PR
            string cronexpressionPR = infraDiskMonitor.ScheduleTimePR;
            string[] partsPR = cronexpressionPR.Split('/');

            if (partsPR[1].Contains(" * * * ?"))
            {
                rdTimeIntervalPR.SelectedValue = "Minute(s)";
                Panel_MinuitePR.Visible = true;
                Panel_HourlyPR.Visible = false;
                Panel_DailyPR.Visible = false;
                var mPR = cronexpressionPR.Substring(cronexpressionPR.LastIndexOf('/') + 1, cronexpressionPR.IndexOf('*') - (cronexpressionPR.LastIndexOf('/') + 1));
                mPR = mPR.Trim();
                txteveryminuitePR.Text = mPR;
            }
            else if (partsPR[1].Contains("* * ?"))
            {
                rdTimeIntervalPR.SelectedValue = "Hour(s)";
                Panel_HourlyPR.Visible = true;
                Panel_MinuitePR.Visible = false;
                Panel_DailyPR.Visible = false;
                var tPR = cronexpressionPR.Substring(1, 3);
                var wPR = cronexpressionPR.Substring(cronexpressionPR.LastIndexOf('/') + 1, cronexpressionPR.IndexOf('*') - (cronexpressionPR.LastIndexOf('/') + 1));
                tPR = tPR.Trim();
                wPR = wPR.Trim();
                txteveryhourPR.Text = wPR;
                txteveryhourlyminuitePR.Text = tPR;
            }
            else
            {
                rdTimeIntervalPR.SelectedValue = "Day(s)";
                Panel_DailyPR.Visible = true;
                Panel_HourlyPR.Visible = false;
                Panel_MinuitePR.Visible = false;
                var dPR = cronexpressionPR.Substring(cronexpressionPR.LastIndexOf('/') + 1, cronexpressionPR.IndexOf('*') - (cronexpressionPR.LastIndexOf('/') + 1));
                txteverydailyPR.Text = dPR;
                var thPR = cronexpressionPR.Substring(1, 3);
                var tmPR = cronexpressionPR.Substring(4, 4);
                thPR = thPR.Trim();
                tmPR = tmPR.Trim();
                ddlhoursPR.SelectedItem.Text = tmPR;
                ddlminutesPR.SelectedItem.Text = thPR;
            }

            //DR 
            string cronexpressionDR = infraDiskMonitor.ScheduleTimeDR;
            string[] partsDR = cronexpressionDR.Split('/');

            if (partsDR[1].Contains(" * * * ?"))
            {
                rdTimeIntervalDR.SelectedValue = "Minute(s)";
                Panel_MinuiteDR.Visible = true;
                Panel_HourlyDR.Visible = false;
                Panel_DailyDR.Visible = false;
                var m = cronexpressionDR.Substring(cronexpressionDR.LastIndexOf('/') + 1, cronexpressionDR.IndexOf('*') - (cronexpressionDR.LastIndexOf('/') + 1));
                m = m.Trim();
                txteveryminuiteDR.Text = m;
            }
            else if (partsDR[1].Contains("* * ?"))
            {
                rdTimeIntervalDR.SelectedValue = "Hour(s)";
                Panel_HourlyDR.Visible = true;
                Panel_MinuiteDR.Visible = false;
                Panel_DailyDR.Visible = false;
                var tDR = cronexpressionDR.Substring(1, 3);
                var wDR = cronexpressionDR.Substring(cronexpressionDR.LastIndexOf('/') + 1, cronexpressionDR.IndexOf('*') - (cronexpressionDR.LastIndexOf('/') + 1));
                tDR = tDR.Trim();
                wDR = wDR.Trim();
                txteveryhourDR.Text = wDR;
                txteveryhourlyminuiteDR.Text = tDR;
            }
            else
            {
                rdTimeIntervalDR.SelectedValue = "Day(s)";
                Panel_DailyDR.Visible = true;
                Panel_HourlyDR.Visible = false;
                Panel_MinuiteDR.Visible = false;
                var d = cronexpressionDR.Substring(cronexpressionDR.LastIndexOf('/') + 1, cronexpressionDR.IndexOf('*') - (cronexpressionDR.LastIndexOf('/') + 1));
                txteverydailyDR.Text = d;
                var thDR = cronexpressionDR.Substring(1, 3);
                var tmDR = cronexpressionDR.Substring(4, 4);
                thDR = thDR.Trim();
                tmDR = tmDR.Trim();
                ddlhoursDR.SelectedItem.Text = tmDR;
                ddlminutesDR.SelectedItem.Text = thDR;
            }

            btnSubmit.Text = "Update";
            btnDelete.Visible = true;
        }

        protected void LnkbtnCloseEnableDSMonitoring_Click(object sender, EventArgs e)
        {
            ClearField();
            modelbg.Visible = false;
            pnlEnableDSMonitoring.Visible = false;
        }

        protected void btnClose_Click(object sender, EventArgs e)
        {
            ClearField();
            modelbg.Visible = false;
            pnlEnableDSMonitoring.Visible = false;
        }

        protected void BtnSubmit_Click(object sender, EventArgs e)
        {
            try
            {
                if (Page.IsValid)
                {
                    SaveInfraobjectDiskMonitor();
                }
            }
            catch (Exception ex)
            {
                lblEnableDSMonitoringMsg.Text = "Error while saving infraobject disk monitor details";
                lblEnableDSMonitoringMsg.ForeColor = System.Drawing.Color.DarkRed;
            }
        }

        private void SaveInfraobjectDiskMonitor()
        {
            var infraDiskMonitor = _facade.GetInfraobjectDiskMonitorByInfraobjectId(CurrentInfraObjectId);

            if (infraDiskMonitor == null || infraDiskMonitor.Id <= 0)
            {
                infraDiskMonitor = new InfraobjectDiskMonitor();
            }

            infraDiskMonitor.InfraobjectId = CurrentInfraObjectId;
            infraDiskMonitor.VolumeNamePR = txtPRVolumeName.Text;
            infraDiskMonitor.ThresholdPR = txtPRThreshold.Text;

            infraDiskMonitor.VolumeNameDR = txtDRVolumeName.Text;
            infraDiskMonitor.ThresholdDR = txtDRThreshold.Text;

            string cronstringPR = string.Empty;
            if (rdTimeIntervalPR.SelectedValue == "Minute(s)")
                cronstringPR = string.Format("0 0/{0} * * * ?", txteveryminuitePR.Text);

            else if (rdTimeIntervalPR.SelectedValue == "Hour(s)")
                cronstringPR = string.Format("0 {0} 0/{1} * * ?", txteveryhourlyminuitePR.Text, txteveryhourPR.Text);
            else
                cronstringPR = string.Format("0 {0} {1} 1/{2} * ?", ddlminutesPR.SelectedValue, ddlhoursPR.SelectedValue, txteverydailyPR.Text);
            infraDiskMonitor.ScheduleTimePR = cronstringPR;

            string cronstringDR = string.Empty;
            if (rdTimeIntervalDR.SelectedValue == "Minute(s)")
                cronstringDR = string.Format("0 0/{0} * * * ?", txteveryminuiteDR.Text);
            else if (rdTimeIntervalDR.SelectedValue == "Hour(s)")
                cronstringDR = string.Format("0 {0} 0/{1} * * ?", txteveryhourlyminuiteDR.Text, txteveryhourDR.Text);
            else
                cronstringDR = string.Format("0 {0} {1} 1/{2} * ?", ddlminutesDR.SelectedValue, ddlhoursDR.SelectedValue, txteverydailyDR.Text);
            infraDiskMonitor.ScheduleTimeDR = cronstringDR;

            try
            {
                string actionMsg = string.Empty;
                if (infraDiskMonitor.Id <= 0)
                {
                    infraDiskMonitor.CreatorId = LoggedInUserId;
                    _facade.AddInfraobjectDiskMonitor(infraDiskMonitor);
                    actionMsg = "saved";
                }
                else
                {
                    infraDiskMonitor.UpdatorId = LoggedInUserId;
                    _facade.UpdateInfraobjectDiskMonitor(infraDiskMonitor);
                    actionMsg = "updated";
                }
                lblEnableDSMonitoringMsg.Visible = true;
                lblEnableDSMonitoringMsg.ForeColor = System.Drawing.Color.Green;
                lblEnableDSMonitoringMsg.Text = "Infraobject Disk Monitor Data " + actionMsg + " successfully.";
                btnSubmit.Text = "Update";
                btnDelete.Visible = true;
            }
            catch (Exception ex)
            {
                lblEnableDSMonitoringMsg.ForeColor = System.Drawing.Color.DarkRed;
                lblEnableDSMonitoringMsg.Visible = true;
                string userMsg = string.Empty;
                if (infraDiskMonitor == null)
                {
                    userMsg = "Unhandled exception occurred while saving Infraobject Disk Monitor Data: " + ex;
                }
                else
                {
                    userMsg = "Unhandled exception occurred while updating Infraobject Disk Monitor Data: " + ex;
                }
                lblEnableDSMonitoringMsg.Text = userMsg;
            }
        }

        protected void cvSetTimePRValidator_ServerValidate(object source, ServerValidateEventArgs args)
        {
            if (rdTimeIntervalPR.SelectedValue == "Minute(s)")
            {
                if (txteveryminuitePR.Text.Trim() == "")
                {
                    cvSetTimePR.ErrorMessage = "Please enter Diskspace Monitor Time Interval In Minutes";
                    args.IsValid = false;
                }
            }
            else if (rdTimeIntervalPR.SelectedValue == "Hour(s)")
            {
                if (txteveryminuitePR.Text.Trim() == "" || txteveryhourPR.Text.Trim() == "")
                {
                    cvSetTimePR.ErrorMessage = "Please enter Diskspace Monitor Time Interval Either in Minutes or Hours";
                    args.IsValid = false;
                }
            }
            else if (rdTimeIntervalPR.SelectedValue == "Day(s)")
            {
                if (ddlminutesPR.SelectedValue.Trim() == "" || ddlhoursPR.SelectedValue.Trim() == "" || txteverydailyPR.Text.Trim() == "")
                {
                    cvSetTimePR.ErrorMessage = "Please enter Diskspace Monitor Time Interval Either in Minutes or Hours or Days";
                    args.IsValid = false;
                }
            }
        }

        protected void BtnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                var infraDiskMonitor = _facade.GetInfraobjectDiskMonitorByInfraobjectId(CurrentInfraObjectId);
                bool deleted = _facade.DeleteInfraobjectDiskMonitorById(infraDiskMonitor.Id);

                if (deleted)
                {
                    ClearField();
                    lblEnableDSMonitoringMsg.ForeColor = System.Drawing.Color.DarkRed;
                    lblEnableDSMonitoringMsg.Visible = true;
                    lblEnableDSMonitoringMsg.Text = "Infraobject Disk Monitor for Infraobject (" + CurrentInfraObject.Name + ") Data deleted successfully.";

                    btnSubmit.Text = "Save";
                    btnDelete.Visible = false;
                }
                else
                {
                    lblEnableDSMonitoringMsg.ForeColor = System.Drawing.Color.DarkRed;
                    lblEnableDSMonitoringMsg.Visible = true;
                    lblEnableDSMonitoringMsg.Text = "Error while deleting infraobject disk monitor details";
                }

            }
            catch (Exception ex)
            {
                lblEnableDSMonitoringMsg.Visible = true;
                lblEnableDSMonitoringMsg.Text = "Error while saving infraobject disk monitor details";
                lblEnableDSMonitoringMsg.ForeColor = System.Drawing.Color.DarkRed;
            }
        }

        public void ClearField()
        {
            txtPRVolumeName.Text = string.Empty;
            txtPRThreshold.Text = string.Empty;

            txtDRVolumeName.Text = string.Empty;
            txtDRThreshold.Text = string.Empty;

            txteveryminuitePR.Text = string.Empty;
            txteveryhourPR.Text = string.Empty;
            txteveryhourlyminuitePR.Text = string.Empty;
            txteverydailyPR.Text = string.Empty;

            txteveryminuiteDR.Text = string.Empty;
            txteveryhourDR.Text = string.Empty;
            txteveryhourlyminuiteDR.Text = string.Empty;
            txteverydailyDR.Text = string.Empty;

            lblEnableDSMonitoringMsg.Text = "";
            lblEnableDSMonitoringMsg.Visible = false;

        }

        protected void rdTimeIntervalPR_SelectedIndexChanged(object sender, EventArgs e)
        {
            setTimeTR.Visible = true;
            TimeIntervalSelectionPR();
        }

        private void TimeIntervalSelectionPR()
        {
            if (rdTimeIntervalPR.SelectedValue == "Minute(s)")
            {
                Panel_MinuitePR.Visible = true;
                Panel_HourlyPR.Visible = false;
                Panel_DailyPR.Visible = false;
                txteveryminuitePR.Text = string.Empty;
            }
            else if (rdTimeIntervalPR.SelectedValue == "Hour(s)")
            {
                Panel_HourlyPR.Visible = true;
                Panel_MinuitePR.Visible = false;
                Panel_DailyPR.Visible = false;
                txteveryhourPR.Text = string.Empty;
                txteveryhourlyminuitePR.Text = string.Empty;
            }
            else if (rdTimeIntervalPR.SelectedValue == "Day(s)")
            {
                Panel_DailyPR.Visible = true;
                Panel_HourlyPR.Visible = false;
                Panel_MinuitePR.Visible = false;
                txteverydailyPR.Text = string.Empty;
                ddlhoursPR.SelectedIndex = 0;
                ddlminutesPR.SelectedIndex = 0;
            }
            else
            {
                Panel_DailyPR.Visible = false;
                Panel_HourlyPR.Visible = false;
                Panel_MinuitePR.Visible = false;
                txteverydailyPR.Text = string.Empty;
                ddlhoursPR.SelectedIndex = 0;
                ddlminutesPR.SelectedIndex = 0;
                txteveryminuitePR.Text = string.Empty;
                txteveryhourPR.Text = string.Empty;
                txteveryhourlyminuitePR.Text = string.Empty;
            }
        }

        protected void rdTimeIntervalDR_SelectedIndexChanged(object sender, EventArgs e)
        {
            setTimeTR.Visible = true;
            TimeIntervalSelectionDR();
        }

        private void TimeIntervalSelectionDR()
        {
            if (rdTimeIntervalDR.SelectedValue == "Minute(s)")
            {
                Panel_MinuiteDR.Visible = true;
                Panel_HourlyDR.Visible = false;
                Panel_DailyDR.Visible = false;
                txteveryminuiteDR.Text = string.Empty;
            }
            else if (rdTimeIntervalDR.SelectedValue == "Hour(s)")
            {
                Panel_HourlyDR.Visible = true;
                Panel_MinuiteDR.Visible = false;
                Panel_DailyDR.Visible = false;
                txteveryhourDR.Text = string.Empty;
                txteveryhourlyminuiteDR.Text = string.Empty;
            }
            else if (rdTimeIntervalDR.SelectedValue == "Day(s)")
            {
                Panel_DailyDR.Visible = true;
                Panel_HourlyDR.Visible = false;
                Panel_MinuiteDR.Visible = false;
                txteverydailyDR.Text = string.Empty;
                ddlhoursDR.SelectedIndex = 0;
                ddlminutesDR.SelectedIndex = 0;
            }
            else
            {
                Panel_DailyDR.Visible = false;
                Panel_HourlyDR.Visible = false;
                Panel_MinuiteDR.Visible = false;
                txteverydailyDR.Text = string.Empty;
                ddlhoursDR.SelectedIndex = 0;
                ddlminutesDR.SelectedIndex = 0;
                txteveryminuiteDR.Text = string.Empty;
                txteveryhourDR.Text = string.Empty;
                txteveryhourlyminuiteDR.Text = string.Empty;
            }
        }

        protected void txteveryhourlyminuitePR_TextChanged(object sender, EventArgs e)
        {
            int num;
            bool isNum = int.TryParse(txteveryhourlyminuitePR.Text.Trim(), out num);

            if (isNum)
            {
                rngPR.Enabled = true;
                regexpfornumericPR.Enabled = false;
            }
            else
            {
                rngPR.Enabled = false;
                regexpfornumericPR.Enabled = true;
            }
        }

        protected void txteveryhourlyminuiteDR_TextChanged(object sender, EventArgs e)
        {
            int num;
            bool isNum = int.TryParse(txteveryhourlyminuiteDR.Text.Trim(), out num);

            if (isNum)
            {
                rngDR.Enabled = true;
                regexpfornumericDR.Enabled = false;
            }
            else
            {
                rngDR.Enabled = false;
                regexpfornumericDR.Enabled = true;
            }
        }

        protected override void OnInit(EventArgs e)
        {
            var scriptManager = ScriptManager.GetCurrent(this);
            scriptManager.RegisterPostBackControl(btnClose);
        }

        #endregion Enable Diskspace Monitor

    }
}