﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using System.Web.Security;
using System.Web;

namespace CP.UI.Controls
{
    public partial class FastCopyConfiguration : ReplicationControl
    {
        #region Variable

        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        private FastCopy _fastCopy = null;

        private FastCopyJob fastcopyjob = new FastCopyJob();

        private static readonly IList<FastCopy> _finalfastcopy = new List<FastCopy>();
        private static readonly IList<FastCopyJob> _finalfastcopyjob = new List<FastCopyJob>();
        private static readonly IList<Site> _sitetype = new List<Site>();
        public string sitetype;

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;

        public static string DataSyncEditURL = Constants.UrlConstants.Urls.Component.Datasyncproperties;

        #endregion Variable

        #region Properties

        public FastCopy CurrentEntity
        {
            get { return _fastCopy ?? (_fastCopy = new FastCopy()); }
            set
            {
                _fastCopy = value;
            }
        }

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "Datasync Replication"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion Properties

        public override void PrepareView()
        {
            txtWildCard.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtWildCard.ClientID + ")");
            txtDataSyncPath.Attributes.Add("onblur", "ValidatorValidate(" + rfvDatasynpath.ClientID + ")");
            txtWindowpath.Attributes.Add("onblur", "ValidatorValidate(" + rfvWindowpath.ClientID + ")");
            ddlProcessCode.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
            ddlOsPlatform.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            RadioButtonList1.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");

            Session.Remove("FastCopy");

            if (chkCompression.Checked == false || chkWildCard.Checked == false)
            {
                lblRepOption.Text = string.Empty;
            }
            PrepareEditView();
            ShowListview();

        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    //ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                //ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();
                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtSSHUser");
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        private void PrepareEditView()
        {
            if (CurrentFastCopy != null)
            {

                CurrentEntity = CurrentFastCopy;

                Session["FastCopy"] = CurrentEntity;

                chkCompression.Checked = CurrentFastCopy.IsCompression;
                //  txtDataSyncPath.Text = CurrentFastCopy.DataSyncPath;
                chkExcMultiDataSyncInstance.Checked = CurrentFastCopy.IsExcMultiInstance;
                if (CurrentFastCopy.IsFilter)
                {
                    divWildCard.Visible = true;
                    chkWildCard.Checked = true;
                    txtWildCard.Text = CurrentFastCopy.Wildcard;
                    //lblWildCard.Text = "WildCard";
                }
                else
                {
                    divWildCard.Visible = false;
                    chkWildCard.Checked = false;
                    txtWildCard.Text = string.Empty;
                    //lblWildCard.Text =string.Empty;
                }

                ddlProcessCode.SelectedValue = CurrentFastCopy.ProcessCode;
                ddlOsPlatform.SelectedValue = CurrentFastCopy.OSPlatform;
                string cronexpression = CurrentFastCopy.ScheduleTime;

                if (ddlOsPlatform.SelectedValue.Trim() == "Linux")
                {
                    divDataSyncJREPath.Visible = true;
                    txtDataSyncPath.Text = CurrentFastCopy.DataSyncPath;
                    txtDataSyncJREPath.Text = CurrentFastCopy.DataSyncJREPath.Trim();
                    txtDataSyncPath.Visible = true;
                    txtWindowpath.Visible = false;
                    rfvDatasynpath.Enabled = true;
                    rfvWindowpath.Enabled = false;

                }
                else if (ddlOsPlatform.SelectedValue.Trim() == "Windows")
                {
                    divDataSyncJREPath.Visible = false;
                    txtWindowpath.Text = CurrentFastCopy.DataSyncPath;
                    txtDataSyncJREPath.Text = string.Empty;
                    txtDataSyncPath.Visible = false;
                    txtWindowpath.Visible = true;
                    rfvDatasynpath.Enabled = false;
                    rfvWindowpath.Enabled = true;

                }

                string[] parts = cronexpression.Split('/');

                if (parts[1].Contains(" * * * ?"))
                {
                    RadioButtonList1.SelectedValue = "Minute(s)";
                    Panel_Minuite.Visible = true;
                    var m = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));
                    m = m.Trim();
                    txteveryminuite.Text = m;
                }
                else if (parts[1].Contains("* * ?"))
                {
                    RadioButtonList1.SelectedValue = "Hour(s)";
                    Panel_Hourly.Visible = true;
                    var t = cronexpression.Substring(1, 3);
                    var w = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));
                    t = t.Trim();
                    w = w.Trim();
                    txteveryhour.Text = w;
                    txteveryhourlyminuite.Text = t;
                }
                else
                {
                    RadioButtonList1.SelectedValue = "Day(s)";
                    Panel_Daily.Visible = true;

                    var d = cronexpression.Substring(cronexpression.LastIndexOf('/') + 1, cronexpression.IndexOf('*') - (cronexpression.LastIndexOf('/') + 1));
                    txteverydaily.Text = d;
                    var th = cronexpression.Substring(1, 3);
                    var tm = cronexpression.Substring(4, 4);
                    th = th.Trim();
                    tm = tm.Trim();
                    ddlhours.SelectedItem.Text = tm;
                    ddlminutes.SelectedItem.Text = th;
                }
                BindFastCopyJobData();
                btnSave.Text = "Update";
            }
            else
            {
                _finalfastcopyjob.Clear();
            }
        }

        private void BindFastCopyJobData()
        {
            CurrentEntity = (FastCopy)Session["FastCopy"];

            if (CurrentEntity != null)
            {
                IList<FastCopyJob> fastcopyjob = Facade.GetFastCopyJobByFastCopyId(CurrentEntity.Id);
                _finalfastcopyjob.Clear();

                if (Session["PreviousItem"] == null)
                {
                    Session["PreviousItem"] = fastcopyjob;
                }

                if (fastcopyjob != null)
                {
                    _finalfastcopyjob.Clear();

                    foreach (FastCopyJob job in fastcopyjob)
                    {
                        var fastcopyjobdetails = new FastCopyJob();
                        fastcopyjobdetails.Id = job.Id;
                        fastcopyjobdetails.FastCopyId = job.FastCopyId;
                        fastcopyjobdetails.SourceDirectory = job.SourceDirectory;
                        fastcopyjobdetails.DestinationDirectory = job.DestinationDirectory;
                        fastcopyjobdetails.DataSyncPropertiesId = job.DataSyncPropertiesId;
                        _finalfastcopyjob.Add(fastcopyjobdetails);
                    }
                }
            }
        }

        private void ShowListview()
        {
            lvSDview.DataSource = _finalfastcopyjob;
            lvSDview.DataBind();
        }

        protected void ChkWildCardCheckedChanged(object sender, EventArgs e)
        {
            if (chkWildCard.Checked)
            {
                rfvtxtWildCard.Enabled = true;
                divWildCard.Visible = true;
            }
            else
            {
                rfvtxtWildCard.Enabled = false;
                divWildCard.Visible = false;
            }
            //lblWildCard.Text = chkWildCard.Checked ? "Wild Card" : string.Empty;

            lblRepOption.Text = string.Empty;
        }

        protected void LvSDviewItemCreated(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                var txtSourceDirectory = (TextBox)e.Item.FindControl("txtInsertLocalDirectory");
                var txtDestinationDirectory = (TextBox)e.Item.FindControl("txtInsertRemoteDirectory");

                var ddlInsertDSPropertiesList = (DropDownList)e.Item.FindControl("ddlInsertDSPropertiesList");

                if (txtSourceDirectory != null)
                {
                    var txts = txtSourceDirectory.Text;
                }

                if (txtDestinationDirectory != null)
                {
                    var txtd = txtDestinationDirectory.Text;
                }

                if (ddlInsertDSPropertiesList != null)
                {
                    PopulateDataSyncProperties(ddlInsertDSPropertiesList);
                }
            }
        }

        private void PopulateDataSyncProperties(ListControl lstDSProperties)
        {
            lstDSProperties.Items.Clear();
            lstDSProperties.InsertItem(0, "- Select DataSync Properties -", "0");

            var dsPropertiesList = GetDataSyncPropertiesList();

            if (dsPropertiesList != null)
            {
                string repType = string.Empty;
                if (ReplicationType.SelectedValue == "14" || ReplicationType.SelectedValue == "21" || ReplicationType.SelectedValue == "41" || ReplicationType.SelectedValue == "64" || ReplicationType.SelectedValue == "13")
                {
                    repType = "Database";
                }
                else if (ReplicationType.SelectedValue == "3")
                {
                    repType = "Application";
                }

                var lstDSProp = (from lstDS in dsPropertiesList where lstDS.ReplicationType == repType select lstDS).ToList();

                if (lstDSProp != null)
                {
                    int i = 1;
                    foreach (var prop in lstDSProp)
                    {
                        lstDSProperties.InsertItem(i, prop.Name, prop.Id.ToString());
                        i++;
                    }
                }
            }
        }

        private IList<DataSyncProperties> GetDataSyncPropertiesList()
        {
            //return Facade.GetServersByUserCompanyIdAndRole(LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay);
            if(Convert.ToBoolean(HttpContext.Current.Session["_isUserSuperAdmin"]))
            return Facade.GetAllDataSyncProperties();

            return Facade.GetDatasyncPropListUserinfraId(Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]), Convert.ToInt32(HttpContext.Current.Session["_companyId"]), Convert.ToString(HttpContext.Current.Session["LoggedInUserRole"]));

        }

        protected void lvSDview_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            //var lblDSPropertiesId = (Label)e.Item.FindControl("lblDSPropertiesId");

            //if (e.CommandName == "DataSyncProperties" && lblDSPropertiesId != null)
            //{
            //    var secureUrl = new SecureUrl(DataSyncEditURL);

            //    if (Convert.ToInt32(lblDSPropertiesId.Text) > 0)
            //    {
            //        //DataSyncProperties dsProperties = Facade.GetDataSyncPropertiesById(Convert.ToInt32(lblDSPropertiesId.Text));

            //        secureUrl = UrlHelper.BuildSecureUrl(DataSyncEditURL, string.Empty, Constants.UrlConstants.Params.DataSyncPropertiesId, lblDSPropertiesId.Text);
            //        if (secureUrl != null)
            //        {
            //            Helper.Url.Redirect(secureUrl);
            //        }
            //    }
            //}
        }

        protected void LvSDviewItemUpdating(object sender, ListViewUpdateEventArgs e)
        {
            if (Session["FastCopy"] != null)
            {
                CurrentEntity = (FastCopy)Session["FastCopy"];
            }

            var lbl = (lvSDview.Items[e.ItemIndex].FindControl("Id")) as Label;
            var lblId = lbl.Text.ToInteger();

            TextBox txtSourceDirectory = (lvSDview.Items[e.ItemIndex].FindControl("txtLocalDirectory")) as TextBox;
            TextBox txtDestinationDirectory = (lvSDview.Items[e.ItemIndex].FindControl("txtRemoteDirectory")) as TextBox;

            DropDownList ddlEditDSPropertiesList = (lvSDview.Items[e.ItemIndex].FindControl("ddlEditDSPropertiesList")) as DropDownList;

            Label lblUpdateId = (lvSDview.Items[e.ItemIndex].FindControl("Id")) as Label;
            if (lblUpdateId != null)
            {
                fastcopyjob.Id = Convert.ToInt32(lblUpdateId.Text);
            }
            var txtsourcedirectory = (lvSDview.Items[e.ItemIndex].FindControl("txtLocalDirectory")) as TextBox;

            if (txtsourcedirectory != null && txtsourcedirectory.Text != "")
            {
                fastcopyjob.SourceDirectory = txtsourcedirectory.Text.Trim();
            }
            var txtdestinationdirectory = (lvSDview.Items[e.ItemIndex].FindControl("txtRemoteDirectory")) as TextBox;
            if (txtdestinationdirectory != null && txtdestinationdirectory.Text != "")
            {
                fastcopyjob.DestinationDirectory = txtdestinationdirectory.Text.Trim();
            }

            if (ddlEditDSPropertiesList != null)
            {
                fastcopyjob.DataSyncPropertiesId = Convert.ToInt32(ddlEditDSPropertiesList.SelectedItem.Value);
            }

            if (btnSave.Text == "Save")
            {
                _finalfastcopyjob.Insert(e.ItemIndex, fastcopyjob);
                _finalfastcopyjob.RemoveAt(e.ItemIndex + 1);
                lvSDview.EditIndex = -1;
                ShowListview();
            }
            else
            {
                _finalfastcopyjob.Clear();
                fastcopyjob.Id = lblUpdateId.Text.ToInteger();
                fastcopyjob.FastCopyId = CurrentEntity.Id;
                fastcopyjob.SourceDirectory = txtsourcedirectory.Text;
                fastcopyjob.DestinationDirectory = txtdestinationdirectory.Text;
                fastcopyjob.DataSyncPropertiesId = Convert.ToInt32(ddlEditDSPropertiesList.SelectedItem.Value);
                var BindlastRepTime = Facade.GetFastCopyJobById(fastcopyjob.Id);
                fastcopyjob.LastSuccessfullReplTime = BindlastRepTime.LastSuccessfullReplTime;

                Facade.UpdateFastcopyJob(fastcopyjob);

                lvSDview.EditIndex = -1;
                BindFastCopyJobData();
                if (_finalfastcopyjob.Count != 0)
                {
                    ShowListview();
                }
            }
        }

        protected void LvSDviewItemCanceling(object sender, ListViewCancelEventArgs e)
        {
            //Label lblId = lvSDview.Items[e.ItemIndex].FindControl("Id") as Label;

            lvSDview.EditIndex = -1;

            if (_finalfastcopyjob.Count != 0)
            {
                ShowListview();
            }
        }

        protected void LvSDviewItemEditing(object sender, ListViewEditEventArgs e)
        {
            Label lblId = lvSDview.Items[e.NewEditIndex].FindControl("Id") as Label;
            var Id = lblId.Text;

            if (btnSave.Text == "Save")
            {
                lvSDview.EditIndex = e.NewEditIndex;
                lvSDview.DataSource = _finalfastcopyjob;
                lvSDview.DataBind();
            }
            else
            {
                lvSDview.EditIndex = e.NewEditIndex;
                CurrentEntity = (FastCopy)Session["FastCopy"];

                if (CurrentEntity != null)
                {
                    IList<FastCopyJob> _fastcopyjob = Facade.GetFastCopyJobByFastCopyId(CurrentEntity.Id);

                    if (_fastcopyjob != null)
                    {
                        lvSDview.DataSource = _fastcopyjob;
                        lvSDview.DataBind();
                    }
                }
            }

            Label lblEditDSPropId = lvSDview.Items[e.NewEditIndex].FindControl("lblEditDSPropId") as Label;
            DropDownList ddlEditDSPropertiesList = lvSDview.Items[e.NewEditIndex].FindControl("ddlEditDSPropertiesList") as DropDownList;
            if (ddlEditDSPropertiesList != null && lblEditDSPropId != null)
            {
                PopulateDataSyncProperties(ddlEditDSPropertiesList);
                ddlEditDSPropertiesList.SelectedValue = lblEditDSPropId.Text.Trim();
            }
        }

        protected void lvSDview_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            if (e.Item.ItemType == ListViewItemType.DataItem)
            {
                Label lblDSProperties = (Label)e.Item.FindControl("lblDSProperties");

                //LinkButton lbDSProperties = (LinkButton)e.Item.FindControl("lbDSProperties");

                if (lblDSProperties != null)
                {
                    ListViewDataItem dataItem = (ListViewDataItem)e.Item;
                    FastCopyJob rowView = dataItem.DataItem as FastCopyJob;

                    //System.Data.DataRowView rowView = e.Item as System.Data.DataRowView;

                    int dsPropertiesId = rowView.DataSyncPropertiesId;
                    var dsProperties = Facade.GetDataSyncPropertiesById(dsPropertiesId);

                    if (dsProperties != null)
                    {
                        lblDSProperties.Text = dsProperties.Name.ToString();
                    }
                }
            }
        }

        protected void LvSDviewItemInserting(object sender, ListViewInsertEventArgs e)
        {
            if (btnSave.Text == "Save")
            {
                labelSDErrormessage.Visible = false;
                bool isValid = true;

                var GetIdInward = new FastCopyJob();

                TextBox txtsource = (TextBox)e.Item.FindControl("txtInsertLocalDirectory");
                TextBox txtdestination = (TextBox)e.Item.FindControl("txtInsertRemoteDirectory");
                DropDownList ddlInsertDSPropertiesList = (DropDownList)e.Item.FindControl("ddlInsertDSPropertiesList");

                Label lblsource = (Label)e.Item.FindControl("lblInLocalDirectory");
                Label lblDest = (Label)e.Item.FindControl("lblInRemoteDirectory");
                Label lblInsertDSProp = (Label)e.Item.FindControl("lblInsertDSProp");

                if (txtsource.Text == "")
                {
                    lblsource.Text = "*";
                    lblsource.Visible = true;
                    isValid = false;
                }
                else
                {
                    var getName = Facade.IsExistFastCopyJobByName(txtsource.Text.Trim(), txtdestination.Text.Trim(), ReplicationName.Text.Trim());
                    if (getName)
                    {
                        lblError.Visible = true;
                        lblError.Text = "Record already exist";
                        // PopAuthenticationFailed.Visible = true;
                        //     ModalPopupExtenderCustom.Show();

                        return;
                    }
                }

                if (txtdestination.Text == "")
                {
                    lblDest.Text = "*";
                    lblDest.Visible = true;
                    isValid = false;
                }

                if (ddlInsertDSPropertiesList.SelectedValue.Trim() == "0")
                {
                    lblInsertDSProp.Text = "*";
                    lblInsertDSProp.Visible = true;
                    isValid = false;
                }

                if (isValid)
                {
                    lblError.Visible = false;
                    fastcopyjob.SourceDirectory = txtsource.Text;
                    fastcopyjob.DestinationDirectory = txtdestination.Text;
                    fastcopyjob.DataSyncPropertiesId = Convert.ToInt32(ddlInsertDSPropertiesList.SelectedItem.Value);
                    fastcopyjob.LastSuccessfullReplTime = string.Empty;
                    fastcopyjob.CreatorId = 1;

                    //foreach (var fast in _finalfastcopyjob)
                    //{
                    //    if (fast.SourceDirectory.Contains(txtsource.Text.ToString()) || fast.DestinationDirectory.Contains(txtdestination.Text.ToString()))
                    //    {
                    //        lblError.Visible = true;
                    //        lblError.Text = "Record already exist";

                    //        return;
                    //    }
                    //}

                    if (lvSDview.Items.Count > 0)
                    {
                        foreach (var fast in _finalfastcopyjob)
                        {
                            if (fast.SourceDirectory.Contains(txtsource.Text.ToString()))
                            {
                                if (fast.DestinationDirectory.Contains(txtdestination.Text.ToString()))
                                {
                                    lblError.Visible = true;
                                    lblError.Text = "Source and Destination pair path already exist";
                                    return;
                                }
                            }
                            if (fast.DestinationDirectory.Contains(txtdestination.Text.ToString()))
                            {
                                if (fast.SourceDirectory.Contains(txtsource.Text.ToString()))
                                {
                                    lblError.Visible = true;
                                    lblError.Text = "Source and Destination pair path already exist";
                                    return;
                                }
                            }
                        }
                    }

                    _finalfastcopyjob.Add(fastcopyjob);

                    if (lvSDview.EditIndex != -1)
                        lvSDview.EditIndex = -1;

                    if (_finalfastcopyjob != null)
                    {
                        ShowListview();
                    }
                }
                else
                {
                    return;
                }
            }
            else
            {
                _finalfastcopyjob.Clear();
                CurrentEntity = (FastCopy)Session["FastCopy"];
                labelSDErrormessage.Visible = false;
                bool isValid = true;
                var GetIdInward = new FastCopyJob();
                TextBox txtsource = (TextBox)e.Item.FindControl("txtInsertLocalDirectory");
                TextBox txtdestination = (TextBox)e.Item.FindControl("txtInsertRemoteDirectory");
                DropDownList ddlInsertDSPropertiesList = (DropDownList)e.Item.FindControl("ddlInsertDSPropertiesList");

                Label lblsource = (Label)e.Item.FindControl("lblInLocalDirectory");
                Label lblDest = (Label)e.Item.FindControl("lblInRemoteDirectory");
                Label lblInsertDSProp = (Label)e.Item.FindControl("lblInsertDSProp");

                if (txtsource.Text == "")
                {
                    lblsource.Text = "*";
                    lblsource.Visible = true;
                    isValid = false;
                }
                else
                {
                    var getName = Facade.IsExistFastCopyJobByName(txtsource.Text.Trim(), txtdestination.Text.Trim(), ReplicationName.Text.Trim());
                    if (getName)
                    {
                        lblError.Visible = true;
                        lblError.Text = "Record already exist";
                        // PopAuthenticationFailed.Visible = true;
                        //     ModalPopupExtenderCustom.Show();

                        return;
                    }
                    else
                    {
                        lblError.Text = "";
                        lblError.Visible = false;
                    }
                }
                if (txtdestination.Text == "")
                {
                    lblDest.Text = "*";
                    lblDest.Visible = true;
                    isValid = false;
                }

                if (ddlInsertDSPropertiesList.SelectedValue.Trim() == "0")
                {
                    lblInsertDSProp.Text = "*";
                    lblInsertDSProp.Visible = true;
                    isValid = false;
                }

                if (isValid)
                {
                    fastcopyjob.FastCopyId = CurrentEntity.Id;
                    fastcopyjob.SourceDirectory = txtsource.Text;
                    fastcopyjob.DestinationDirectory = txtdestination.Text;
                    fastcopyjob.DataSyncPropertiesId = Convert.ToInt32(ddlInsertDSPropertiesList.SelectedItem.Value);
                    fastcopyjob.LastSuccessfullReplTime = string.Empty;
                    fastcopyjob.CreatorId = 1;

                    _finalfastcopyjob.Add(fastcopyjob);

                    if (_finalfastcopyjob.Count != 0)
                    {
                        foreach (FastCopyJob job in _finalfastcopyjob)
                        {
                            FastCopyJob inw = new FastCopyJob();

                            inw.FastCopyId = job.FastCopyId;
                            inw.SourceDirectory = job.SourceDirectory;
                            inw.DestinationDirectory = job.DestinationDirectory;
                            inw.DataSyncPropertiesId = job.DataSyncPropertiesId;
                            inw.LastSuccessfullReplTime = job.LastSuccessfullReplTime;

                            inw.CreatorId = 1;
                            Facade.AddFastCopyJob(inw);
                        }

                        BindFastCopyJobData();

                        if (lvSDview.EditIndex != -1)
                            lvSDview.EditIndex = -1;

                        if (_finalfastcopyjob.Count != 0)
                        {
                            ShowListview();
                        }
                    }
                }
                else
                {
                    return;
                }
            }
        }

        protected void LvSDviewItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            var lblId = lvSDview.Items[e.ItemIndex].FindControl("Id") as Label;
            if (lblId != null && lblId.Text != null)
            {
                if (btnSave.Text == "Update")
                {
                    var fastcopyJobDetails = Facade.GetFastCopyJobById(Convert.ToInt32(lblId.Text));
                    var FastCopyDetails = Facade.GetFastCopyById(fastcopyJobDetails != null ? Convert.ToInt32(fastcopyJobDetails.FastCopyId) : 0);
                    var replicationBaseDetails = Facade.GetReplicationBaseById(FastCopyDetails != null ? Convert.ToInt32(FastCopyDetails.ReplicationId) : 0);
                    //var siteDetails = Facade.GetSiteById(replicationBaseDetails != null ? Convert.ToInt32(replicationBaseDetails.SiteId) : 0);

                    //if (siteDetails.IsActive == 1)
                    //{
                    //    lblError.Text = " Replication is in use.";
                    //    lblError.Visible = true;
                    //    return;
                    //}
                    //else
                    //{
                    lblError.Text = "";
                    lblError.Visible = false;
                    Facade.DeleteFastCopyJobById(Convert.ToInt32(lblId.Text));
                    // }
                    BindFastCopyJobData();
                    ShowListview();
                }
                else
                {
                    _finalfastcopyjob.RemoveAt(e.ItemIndex);
                    ShowListview();
                }
            }
        }

        private bool CheckValidation()
        {
            var isValid = true;

            if (RadioButtonList1.SelectedValue == "")
            {
                isValid = false;
                lbltimeintervalErrormessage.Visible = true;
            }

            if (_finalfastcopyjob.Count == 0)
            {
                isValid = false;
                labelSDErrormessage.Visible = true;
                labelSDErrormessage.Text = "Select Source, Destination Directories and DataSync Properties ";
            }

            return isValid;
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }
            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void TxtNameTextChanged(object sender, EventArgs e)
        {
            if (ddlOsPlatform.SelectedItem.Value == "Linux")
            {
                rfvDatasynpath.Visible = true;
                rfvWindowpath.Visible = false;
                txtDataSyncPath.Visible = true;
                txtWindowpath.Visible = false;
            }
        }

        protected void TxtWinNameTextChanged(object sender, EventArgs e)
        {
            if (ddlOsPlatform.SelectedItem.Value == "Windows")
            {
                rfvDatasynpath.Visible = false;
                rfvWindowpath.Visible = true;
                txtDataSyncPath.Visible = false;
                txtWindowpath.Visible = true;
            }
        }

        protected void BtnSaveClick(object sender, EventArgs e)
        {
            lblErrorMsg.Visible = false;
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }

            if (!CheckValidation()) return;

            if ((Session["ReplicationConfig_token"] != null) && !CheckReplicationNameExist() && Page.IsValid && ValidateRequest("FastCopyConfiguration", UserActionType.CreateReplicationComponent))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);


                    //throw new CpException(CpExceptionType.InvalidCharacters);
                }
                else
                {

                    var submitButton = (Button)sender;
                    string buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;

                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }
                    if (txtDataSyncPath.Text == "" && txtWindowpath.Text == "")
                    {
                        lblErrorMsg.Visible = true;
                        lblErrorMsg.Text = "please Enter DataSync path";
                    }
                    else
                    {
                        if (Page.IsPostBack)
                        {
                            string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                            if (returnUrl.IsNullOrEmpty())
                            {
                                returnUrl = ReturnUrl;
                            }
                            try
                            {
                                if (ValidateRequest("FastCopyConfiguration", UserActionType.CreateReplicationComponent))
                                {
                                    BuildEntities();
                                    StartTransaction();
                                    SaveEditor();
                                    EndTransaction();
                                    string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                                    ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(CurrentEntity.Mode.ToString() + " (" + CurrentEntity.ReplicationBase.Name + ")",
                                                                                                            currentTransactionType));
                                    btnSave.Enabled = false;
                                }
                            }
                            catch (CpException ex)
                            {
                                InvalidateTransaction();
                                returnUrl = Request.RawUrl;
                                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                                ExceptionManager.Manage(ex, Page);
                            }
                            catch (Exception ex)
                            {
                                InvalidateTransaction();
                                returnUrl = Request.RawUrl;
                                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                                {
                                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                                }
                                else
                                {
                                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                                    ExceptionManager.Manage(customEx, Page);
                                }
                            }


                            if (returnUrl.IsNotNullOrEmpty())
                            {
                                if (ReplicationType.SelectedValue == "3")
                                {
                                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                    //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "3");

                                    //Helper.Url.Redirect(returnUrl);


                                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "3");
                                    Helper.Url.Redirect(secureUrl);

                                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "3");
                                    //Helper.Url.Redirect(secureUrl);
                                }
                                else if (ReplicationType.SelectedValue == "13")
                                {
                                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "13");
                                    Helper.Url.Redirect(secureUrl);
                                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "14");
                                    //Helper.Url.Redirect(secureUrl);
                                }
                                else if (ReplicationType.SelectedValue == "14")
                                {
                                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "14");
                                    Helper.Url.Redirect(secureUrl);
                                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "14");
                                    //Helper.Url.Redirect(secureUrl);
                                }
                                else if (ReplicationType.SelectedValue == "41")
                                {
                                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "41");
                                    Helper.Url.Redirect(secureUrl);
                                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "41");
                                    //Helper.Url.Redirect(secureUrl);
                                }
                                else if (ReplicationType.SelectedValue == "64")
                                {
                                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "64");
                                    Helper.Url.Redirect(secureUrl);
                                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "64");
                                    //Helper.Url.Redirect(secureUrl);
                                }
                                else
                                {
                                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "21");
                                    Helper.Url.Redirect(secureUrl);
                                    //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "21");
                                    //Helper.Url.Redirect(secureUrl);
                                }

                            }
                        }
                    }
                }
            }
        }



        private void SaveEditor()
        {
            var LastId = 0;
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                //  CurrentEntity = Facade.AddFastCopy(CurrentEntity);
                var insertId = Facade.AddFastCopy(CurrentEntity);
                LastId = insertId.Id;
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "DataSync", UserActionType.CreateReplicationComponent, "The DataSync Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
                if (_finalfastcopyjob.Count != 0)
                {
                    foreach (FastCopyJob inward in _finalfastcopyjob)
                    {
                        FastCopyJob fastcopyjob = new FastCopyJob();
                        fastcopyjob.FastCopyId = LastId;

                        fastcopyjob.SourceDirectory = inward.SourceDirectory;
                        fastcopyjob.DestinationDirectory = inward.DestinationDirectory;
                        fastcopyjob.DataSyncPropertiesId = inward.DataSyncPropertiesId;
                        fastcopyjob.LastSuccessfullReplTime = inward.LastSuccessfullReplTime;
                        fastcopyjob.CreatorId = 1;
                        Facade.AddFastCopyJob(fastcopyjob);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "DataSyncJob", UserActionType.CreateFastCopyJob, "The DataSyncJob '" + fastcopyjob.FastCopyId + "' was added to the DataSyncJob component.", LoggedInUserId);
                    }
                }
            }
            else
            {
                LastId = 0;
                CurrentEntity.Id = CurrentFastCopy.Id;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                // CurrentEntity = Facade.UpdateFastcopy(CurrentEntity);
                var updateId = Facade.UpdateFastcopy(CurrentEntity);
                LastId = updateId.Id;
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "DataSync", UserActionType.UpdateReplicationComponent, "The DataSync Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        private void BuildEntities()
        {
            if (Session["FastCopy"] != null)
            {
                CurrentEntity = (FastCopy)Session["FastCopy"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.IsCompression = chkCompression.Checked;

            CurrentEntity.IsFilter = chkWildCard.Checked;

            //if (equalsOrLike.SelectedValue == "0")
            //{
            //    CurrentEntity.IsFilter = 0;
            //}
            //else if (equalsOrLike.SelectedValue == "1")
            //{
            //    CurrentEntity.IsFilter = 1;
            //}
            //else if (equalsOrLike.SelectedValue == "2")
            //{
            //    CurrentEntity.IsFilter = 2;
            //}

            //CurrentEntity.IsFilter = Convert.ToInt32(equalsOrLike.SelectedValue.ToString());

            CurrentEntity.Wildcard = txtWildCard.Text;
            if (ddlOsPlatform.SelectedValue == "Linux")
            {
                CurrentEntity.DataSyncPath = txtDataSyncPath.Text;
            }
            else
            {
                CurrentEntity.DataSyncPath = txtWindowpath.Text;
            }

            CurrentEntity.ProcessCode = ddlProcessCode.SelectedValue;
            //CurrentEntity.LocalDirectory = txtLocalDirectory.Text;
            //CurrentEntity.RemoteDirectory = txtRemoteDirectory.Text;
            CurrentEntity.LocalDirectory = "-";
            CurrentEntity.RemoteDirectory = "-";

            CurrentEntity.OSPlatform = ddlOsPlatform.SelectedValue;

            if (ddlOsPlatform.SelectedValue.Trim() == "Linux")
            {
                CurrentEntity.DataSyncJREPath = txtDataSyncJREPath.Text.Trim();
            }
            else
            {
                CurrentEntity.DataSyncJREPath = string.Empty;
            }

            string cronstring = string.Empty;
            if (RadioButtonList1.SelectedValue == "Minute(s)")
            {
                cronstring = string.Format("0 0/{0} * * * ?", txteveryminuite.Text);
            }
            else if (RadioButtonList1.SelectedValue == "Hour(s)")
            {
                cronstring = string.Format("0 {0} 0/{1} * * ?", txteveryhourlyminuite.Text, txteveryhour.Text);
            }
            else
            {
                cronstring = string.Format("0 {0} {1} 1/{2} * ?", ddlminutes.SelectedValue, ddlhours.SelectedValue, txteverydaily.Text);
            }
            CurrentEntity.ScheduleTime = cronstring;
            if (ReplicationType.SelectedValue == "3")
            {
                CurrentEntity.Mode = FastCopyMode.DataSync;
            }
            else if (ReplicationType.SelectedValue == "13")
            {
                CurrentEntity.Mode = FastCopyMode.MSSQLWithDataSync;
            }
            else if (ReplicationType.SelectedValue == "14")
            {
                CurrentEntity.Mode = FastCopyMode.OracleWithDataSync;
            }
            else if (ReplicationType.SelectedValue == "41")
            {
                CurrentEntity.Mode = FastCopyMode.SyBaseWithDataSync;
            }
            else if (ReplicationType.SelectedValue == "64")
            {
                CurrentEntity.Mode = FastCopyMode.MaxDBWithDataSync;
            }
            else
            {
                CurrentEntity.Mode = FastCopyMode.DB2DataSync;
            }

            CurrentEntity.IsExcMultiInstance = chkExcMultiDataSyncInstance.Checked;
            //if (ReplicationType.SelectedValue == "21")
            //{
            //    CurrentEntity.Mode = FastCopyMode.DB2DataSync;
            //}
        }

        protected void ddlOsPlatform_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlOsPlatform.SelectedItem.Value == "Linux")
            {
                divDataSyncJREPath.Visible = true;
                txtDataSyncPath.Visible = true;
                txtWindowpath.Text = "";
                txtWindowpath.Visible = false;
                regexpWin.Visible = false;
                regexp.Visible = true;
                rfejrePath.Visible = true;
                //rfvDatasynpath.Visible = true;
                //rfvWindowpath.Visible = false;
                rfvDatasynpath.Enabled = true;
                rfvWindowpath.Enabled = false;
            }
            else if (ddlOsPlatform.SelectedItem.Value == "Windows")
            {
                divDataSyncJREPath.Visible = false;
                txtWindowpath.Visible = true;
                txtDataSyncJREPath.Text = string.Empty;
                txtDataSyncPath.Text = string.Empty;
                txtDataSyncPath.Visible = false;
                regexp.Visible = false;
                regexpWin.Visible = true;
                rfejrePath.Visible = false;
                //rfvDatasynpath.Visible = false;
                //rfvWindowpath.Visible = true;
                rfvDatasynpath.Enabled = false;
                rfvWindowpath.Enabled = true;
            }
            else
            {
                divDataSyncJREPath.Visible = false;
            }
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        protected void RadioButtonList1_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (RadioButtonList1.SelectedValue == "Minute(s)")
            {
                Panel_Minuite.Visible = true;
                Panel_Hourly.Visible = false;
                Panel_Daily.Visible = false;
            }
            else if (RadioButtonList1.SelectedValue == "Hour(s)")
            {
                Panel_Hourly.Visible = true;
                Panel_Minuite.Visible = false;
                Panel_Daily.Visible = false;
            }
            else
            {
                Panel_Daily.Visible = true;
                Panel_Hourly.Visible = false;
                Panel_Minuite.Visible = false;
            }
        }

        protected void txteveryhourlyminuite_TextChanged(object sender, EventArgs e)
        {
            int num;
            bool isNum = int.TryParse(txteveryhourlyminuite.Text.Trim(), out num);

            if (isNum)
            {
                rng.Enabled = true;
                regexpfornumeric.Enabled = false;
            }
            else
            {
                rng.Enabled = false;
                regexpfornumeric.Enabled = true;
            }
            //if (!System.Text.RegularExpressions.Regex.IsMatch("^[0-9]", txteveryhourlyminuite.Text))
            //{
            //    rng.Enabled = true;
            //    regexpfornumeric.Enabled = false;
            //}
            //else
            //{
            //    rng.Enabled = false;
            //    regexpfornumeric.Enabled = true;
            //}
        }

        //protected void BtnAuthenticationFailClick(object sender, EventArgs e)
        //{
        //    PopAuthenticationFailed.Visible = false;
        //    ModalPopupExtenderCustom.Hide();
        //}
    }
}