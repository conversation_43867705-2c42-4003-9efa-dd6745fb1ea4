﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;

namespace CP.UI.Controls
{
    public partial class TPRCConfiguration : ReplicationControl
    {
        #region Variable

        TextBox _txtReplicationName = new TextBox();
        DropDownList _ddlReplicationType = new DropDownList();
        DropDownList _ddlSiteId = new DropDownList();

        private TPRCReplication _tPRCReplication;

        #endregion


        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public TPRCReplication CurrentEntity
        {
            get { return _tPRCReplication ?? (_tPRCReplication = new TPRCReplication()); }
            set
            {
                _tPRCReplication = value;
            }
        }

        public string MessageInitials
        {
            get { return "TPRC Replication Information "; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            txtgroup.Attributes.Add("onblur", "ValidatorValidate(" + rfvtxtgroup.ClientID + ")");
            PrepareEditView();
        }

        private void PrepareEditView()
        {
            if (CurrentTPRCReplication != null && CurrentTPRCReplication.Id > 0)
            {
                btnSave.Text = "Update";
                CurrentEntity = CurrentTPRCReplication;
                Session["TPRCRepli"] = CurrentEntity;
                Session.Remove("PreviousItem");
                txtgroup.Text = CurrentEntity.GroupName;
                txtfilename.Text = CurrentEntity.BatchfileName;
                txtfilepath.Text = CurrentEntity.BatchfilePath;
            }
        }

        //BOC Validate Request
        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((Session["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();
                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }

                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtSSHUser");
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        private void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddTPRCReplication(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "TPRC", UserActionType.CreateReplicationComponent, "The TPRC Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
            }
            else
            {
                CurrentEntity.Id = CurrentTPRCReplication.Id;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateTPRCReplication(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "TPRC", UserActionType.UpdateReplicationComponent, "The TPRC Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void SaveRepClick(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (Page.IsValid && (Session["ReplicationConfig_token"] != null) && !CheckReplicationNameExist() && ValidateRequest("SnapMirrorConfiguration", UserActionType.CreateReplicationComponent))
            {
                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {
                    if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
                    {

                        lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
                    }
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }
                    var submitButton = (Button)sender;
                    string buttionText = " " + submitButton.Text.ToLower() + " ";
                    var currentTransactionType = TransactionType.Undefined;
                    if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
                    {
                        currentTransactionType = TransactionType.Save;
                    }
                    else if (buttionText.Contains(" update "))
                    {
                        currentTransactionType = TransactionType.Update;
                    }

                    try
                    {
                        if (currentTransactionType != TransactionType.Undefined && ValidateRequest("TPRCCConfiguration", UserActionType.CreateReplicationComponent))
                        {
                            BuildEntities();
                            StartTransaction();
                            SaveEditor();
                            EndTransaction();
                            string message = MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                               currentTransactionType));
                            btnSave.Enabled = false;
                        }

                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, Page);

                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, Page);

                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, Page);
                        }
                    }
                    if (returnUrl.IsNotNullOrEmpty())
                    {

                        //WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);

                        //Helper.Url.Redirect(returnUrl);

                        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "68");
                        Helper.Url.Redirect(secureUrl);
                    }
                    //if (returnUrl.IsNotNullOrEmpty())
                    //{
                    //    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "68");

                    //    Helper.Url.Redirect(secureUrl);
                    //}
                }
            }
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        private void BuildEntities()
        {
            if (Session["TPRCRepli"] != null)
            {
                CurrentEntity = (TPRCReplication)Session["TPRCRepli"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;

            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;

            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);

            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);

            CurrentEntity.GroupName = txtgroup.Text;

            CurrentEntity.BatchfileName = txtfilename.Text;

            CurrentEntity.BatchfilePath = txtfilepath.Text;

        }
    }
}