﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ServerDataAccess : BaseDataAccess, IServerDataAccess
    {
        #region Constructors

        public ServerDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<Server> CreateEntityBuilder<Server>()
        {
            return (new ServerBuilder()) as IEntityBuilder<Server>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="Server" />into bcms_server table.
        /// </summary>
        /// <param name="server">Insert server Details</param>
        /// <returns>Server</returns>
        /// <author><PERSON><PERSON><PERSON></author>
        Server IServerDataAccess.Add(Server server)
        {
            try
            {
                const string sp = "SERVER_CREATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, server.Name);
                    Database.AddInParameter(cmd, Dbstring + "iSiteId", DbType.Int32, server.SiteId);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, server.Type);
                    Database.AddInParameter(cmd, Dbstring + "iIPAddress", DbType.AnsiString, server.IPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, server.Port);
                    Database.AddInParameter(cmd, Dbstring + "iSSHUserName", DbType.AnsiString, server.SSHUserName);
                    Database.AddInParameter(cmd, Dbstring + "iSSHPassword", DbType.AnsiString, server.SSHPassword);
                    Database.AddInParameter(cmd, Dbstring + "iEnableSudoAccess", DbType.Int32, server.EnableSudoAccess);
                    Database.AddInParameter(cmd, Dbstring + "iSudoUser", DbType.AnsiString, server.SudoUser);
                    Database.AddInParameter(cmd, Dbstring + "iSudoPassword", DbType.AnsiString, server.SudoPassword);
                    Database.AddInParameter(cmd, Dbstring + "iDSIPAddress", DbType.AnsiString, server.DSIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iDSSSHUserName", DbType.AnsiString, server.DSSSHUserName);
                    Database.AddInParameter(cmd, Dbstring + "iDSSSHPassword", DbType.AnsiString, server.DSSSHPassword);
                    Database.AddInParameter(cmd, Dbstring + "iOSType", DbType.AnsiString, server.OSType);
                    Database.AddInParameter(cmd, Dbstring + "iIsPartOfCluster", DbType.Int32, server.IsPartOfCluster);
                    Database.AddInParameter(cmd, Dbstring + "iDataStoreName", DbType.AnsiString, server.DataStoreName);
                    Database.AddInParameter(cmd, Dbstring + "iVmPath", DbType.AnsiString, server.VmPath);
                    Database.AddInParameter(cmd, Dbstring + "iDisk", DbType.AnsiString, server.Disk);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, server.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iSshKey", DbType.Int32, server.IsUseSshKeyAuth);
                    Database.AddInParameter(cmd, Dbstring + "iIsAuthSshKeyPath", DbType.AnsiString, server.SshKeyPath);
                    Database.AddInParameter(cmd, Dbstring + "iSshKeyPassword", DbType.AnsiString, server.SshKeyPassword);
                    Database.AddInParameter(cmd, Dbstring + "iShellPromt", DbType.AnsiString, server.ShellPrompt);
                    Database.AddInParameter(cmd, Dbstring + "iLicenceKey", DbType.AnsiString, server.LicenseKey);
                    Database.AddInParameter(cmd, Dbstring + "iHostName", DbType.AnsiString, server.HostName);
                    Database.AddInParameter(cmd, Dbstring + "iSSOTypeId", DbType.Int32, server.SSOTypeId);
                    Database.AddInParameter(cmd, Dbstring + "isENABLED", DbType.Int32, server.SSOEnabled);

                    //Database.AddInParameter(cmd, Dbstring + "iSsoTypeID", DbType.AnsiString, server.SsoTypeID);
                    //Database.AddInParameter(cmd, Dbstring + "iSsoEnabled", DbType.Boolean, server.IsSsoEnable);
                    Database.AddInParameter(cmd, Dbstring + "iSafe", DbType.AnsiString, server.Safe);
                    Database.AddInParameter(cmd, Dbstring + "iObject", DbType.AnsiString, server.Object);
                    Database.AddInParameter(cmd, Dbstring + "iFolder", DbType.AnsiString, server.Folder);
                    Database.AddInParameter(cmd, Dbstring + "iReason", DbType.AnsiString, server.Reason);
                    Database.AddInParameter(cmd, Dbstring + "iSSOProfileId", DbType.Int32, server.SSOProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iServerRole", DbType.Int32, server.ServerRole);
                    Database.AddInParameter(cmd, Dbstring + "iIsVirtualGuestOS", DbType.Int32, server.IsVirtualGuestOS);
                    Database.AddInParameter(cmd, Dbstring + "iIsASMGrid", DbType.Int32, server.IsASMGrid);
                    Database.AddInParameter(cmd, Dbstring + "iAsmInstanceId", DbType.Int32, server.ASMInstanceId);
                    Database.AddInParameter(cmd, Dbstring + "iWinRMPort", DbType.Int32, server.WinRMPort);
                    Database.AddInParameter(cmd, Dbstring + "iProxyAccessType", DbType.AnsiString, server.ProxyAccessType);
                    Database.AddInParameter(cmd, Dbstring + "iCANID", DbType.AnsiString, server.CANID);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.AnsiString, server.BusinessServiceId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        server = reader.Read() ? CreateEntityBuilder<Server>().BuildEntity(reader, server) : null;
                    }

                    if (server == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Server already exists. Please specify another server.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while creating this server.");
                                }
                        }
                    }

                    return server;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting Server Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        /// <summary>
        ///     Update <see cref="Server" />into bcms_server table.
        /// </summary>
        /// <param name="server">update server Details</param>
        /// <returns>Server</returns>
        /// <author>Shivraj Mujumale</author>
        Server IServerDataAccess.Update(Server server)
        {
            try
            {
                const string sp = "SERVER_UPDATE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, server.Id);
                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, server.Name);
                    Database.AddInParameter(cmd, Dbstring + "iSiteId", DbType.Int32, server.SiteId);
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, server.Type);
                    Database.AddInParameter(cmd, Dbstring + "iIPAddress", DbType.AnsiString, server.IPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iPort", DbType.Int32, server.Port);
                    Database.AddInParameter(cmd, Dbstring + "iSSHUserName", DbType.AnsiString, server.SSHUserName);
                    Database.AddInParameter(cmd, Dbstring + "iSSHPassword", DbType.AnsiString, server.SSHPassword);
                    Database.AddInParameter(cmd, Dbstring + "iEnableSudoAccess", DbType.Int32, server.EnableSudoAccess);
                    Database.AddInParameter(cmd, Dbstring + "iSudoUser", DbType.AnsiString, server.SudoUser);
                    Database.AddInParameter(cmd, Dbstring + "iSudoPassword", DbType.AnsiString, server.SudoPassword);
                    Database.AddInParameter(cmd, Dbstring + "iDSIPAddress", DbType.AnsiString, server.DSIPAddress);
                    Database.AddInParameter(cmd, Dbstring + "iDSSSHUserName", DbType.AnsiString, server.DSSSHUserName);
                    Database.AddInParameter(cmd, Dbstring + "iDSSSHPassword", DbType.AnsiString, server.DSSSHPassword);
                    Database.AddInParameter(cmd, Dbstring + "iOSType", DbType.AnsiString, server.OSType);
                    Database.AddInParameter(cmd, Dbstring + "iIsPartOfCluster", DbType.Int32, server.IsPartOfCluster);
                    Database.AddInParameter(cmd, Dbstring + "iDataStoreName", DbType.AnsiString, server.DataStoreName);
                    Database.AddInParameter(cmd, Dbstring + "iVmPath", DbType.AnsiString, server.VmPath);
                    Database.AddInParameter(cmd, Dbstring + "iDisk", DbType.AnsiString, server.Disk);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, server.UpdatorId);
                    Database.AddInParameter(cmd, Dbstring + "iSshKey", DbType.Int32, server.IsUseSshKeyAuth);
                    Database.AddInParameter(cmd, Dbstring + "iIsAuthSshKeyPath", DbType.AnsiString, server.SshKeyPath);
                    Database.AddInParameter(cmd, Dbstring + "iSshKeyPassword", DbType.AnsiString, server.SshKeyPassword);
                    Database.AddInParameter(cmd, Dbstring + "iShellPrompt", DbType.AnsiString, server.ShellPrompt);
                    Database.AddInParameter(cmd, Dbstring + "iLicenceKey", DbType.AnsiString, server.LicenseKey);
                    Database.AddInParameter(cmd, Dbstring + "iHostName", DbType.AnsiString, server.HostName);
                    Database.AddInParameter(cmd, Dbstring + "iSSOTypeId", DbType.Int32, server.SSOTypeId);
                    Database.AddInParameter(cmd, Dbstring + "isENABLED", DbType.Int32, server.SSOEnabled);

                    //Database.AddInParameter(cmd, Dbstring + "iSsoTypeID", DbType.AnsiString, server.SsoTypeID);
                    //Database.AddInParameter(cmd, Dbstring + "iSsoEnabled", DbType.Boolean, server.IsSsoEnable);
                    Database.AddInParameter(cmd, Dbstring + "iSafe", DbType.AnsiString, server.Safe);
                    Database.AddInParameter(cmd, Dbstring + "iObject", DbType.AnsiString, server.Object);
                    Database.AddInParameter(cmd, Dbstring + "iFolder", DbType.AnsiString, server.Folder);
                    Database.AddInParameter(cmd, Dbstring + "iReason", DbType.AnsiString, server.Reason);
                    Database.AddInParameter(cmd, Dbstring + "iSSOProfileId", DbType.Int32, server.SSOProfileId);
                    Database.AddInParameter(cmd, Dbstring + "iServerRole", DbType.Int32, server.ServerRole);
                    Database.AddInParameter(cmd, Dbstring + "iIsVirtualGuestOS", DbType.Int32, server.IsVirtualGuestOS);
                    Database.AddInParameter(cmd, Dbstring + "iIsASMGrid", DbType.Int32, server.IsASMGrid);
                    Database.AddInParameter(cmd, Dbstring + "iAsmInstanceId", DbType.Int32, server.ASMInstanceId);
                    Database.AddInParameter(cmd, Dbstring + "iWinRMPort", DbType.Int32, server.WinRMPort);
                    Database.AddInParameter(cmd, Dbstring + "iProxyAccessType", DbType.AnsiString, server.ProxyAccessType);
                    Database.AddInParameter(cmd, Dbstring + "iCANID", DbType.AnsiString, server.CANID);
                    Database.AddInParameter(cmd, Dbstring + "iBusinessServiceId", DbType.AnsiString, server.BusinessServiceId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        server = reader.Read() ? CreateEntityBuilder<Server>().BuildEntity(reader, server) : null;
                    }

                    if (server == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException("Server already exists. Please specify another server.");
                                }
                            default:
                                {
                                    throw new SystemException("An unexpected error has occurred while updating this server.");
                                }
                        }
                    }

                    return server;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating Server Entry : " + Environment.NewLine + "SYSTEM MESSAGE : " +
                    exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Server" />from bcms_server table by id.
        /// </summary>
        /// <param name="id">Pass id</param>
        /// <returns>Server</returns>
        /// <author>Shivraj Mujumale</author>
        Server IServerDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "SERVER_GETBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<Server>()).BuildEntity(reader, new Server());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Server" />from bcms_server table by id.
        /// </summary>
        /// <param name="groupId">Pass groupId</param>
        /// <returns>Server List</returns>
        /// <author>Shivraj Mujumale</author>
        //IList<Server> IServerDataAccess.GetServerByInfraObjectId(int infraObjectId)
        //{
        //    try
        //    {
        //        if (infraObjectId < 1)
        //        {
        //            throw new ArgumentNullException("infraObjectId");
        //        }
        //        const string sp = "Server_GetByInfraObjectId";

        //        using (DbCommand cmd = Database.GetStoredProcCommand(sp))
        //        {
        //            Database.AddInParameter(cmd, Dbstring+"iInfraObjectId ", DbType.Int32, infraObjectId);

        //            using (IDataReader reader = Database.ExecuteReader(cmd))
        //            {
        //                return CreateEntityBuilder<Server>().BuildEntities(reader);
        //            }
        //        }
        //    }
        //    catch (Exception exc)
        //    {
        //        throw new CpException(CpExceptionType.DataAccessFetchOperation,
        //            ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
        //            "Error In DAL While Executing Function Signature IServerDataAccess.GetServerByInfraObjectId(" + infraObjectId +
        //            ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
        //    }
        //}

        IList<Server> IServerDataAccess.GetServerByInfraObjectId(int infraobjectId)
        {
            try
            {
                const string sp = "SERVER_GETBYINFRAOBJECTID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraObjectId", DbType.Int32, infraobjectId);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetServerByInfraObjectId(" + infraobjectId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Server" />from bcms_server table by type.
        /// </summary>
        /// <param name="type">Pass type</param>
        /// <returns>Server List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<Server> IServerDataAccess.GetAllByType(string type, int userid, string role)
        {
            try
            {
                string[] serverValue = type.Split(',');
                string drvalue = serverValue.Length <= 1 ? string.Empty : serverValue[1];
                const string sp = "SERVER_GETALLBYTYPE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iPresxiType", DbType.String, serverValue[0]);
                    Database.AddInParameter(cmd, Dbstring + "iDresxiType", DbType.String, drvalue);
                    //Database.AddInParameter(cmd, Dbstring + "iType", DbType.String, type);
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.AnsiString, userid);
                    Database.AddInParameter(cmd, Dbstring + "iRole", DbType.String, role);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetAllByType(" + type + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Server" />from bcms_server table by osType.
        /// </summary>
        /// <param name="osType">Pass osType</param>
        /// <returns>Server List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<Server> IServerDataAccess.GetAllByOsType(string osType)
        {
            try
            {
                const string sp = "SERVER_GETALLBYOSTYPE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iOSType", DbType.String, osType);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetAllByOsType(" + osType + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Server" />from bcms_server table by osType.
        /// </summary>
        /// <param name="companyId">Pass companyId</param>
        /// <param name="isParent">Pass isParent</param>
        /// <returns>Server List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<Server> IServerDataAccess.GetByCompanyId(int companyId, bool isParent)
        {
            try
            {
                const string sp = "SERVER_GETBYCOMPANYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.AnsiString, companyId);
                    Database.AddInParameter(cmd, Dbstring + "iIsParent", DbType.Int32, isParent);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetByCompanyId(" + companyId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="Server" />from bcms_server table by osType.
        /// </summary>
        /// <returns>Server List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<Server> IServerDataAccess.GetAll()
        {
            try
            {
                const string sp = "SERVER_GETALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Server> IServerDataAccess.GetServerType(string type)
        {
            try
            {
                const string sp = "SERVER_GETBYSERVERTYPE";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iServerType", DbType.AnsiString, type);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Server> IServerDataAccess.GetServerBySiteId(int siteId)
        {
            try
            {
                if (siteId < 1)
                {
                    throw new ArgumentNullException("siteId");
                }
                const string sp = "SERVER_GETBYSITEID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddInParameter(cmd, Dbstring+"iModules", DbType.AnsiString, modules.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iSiteId", DbType.Int32, siteId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetServerBySiteId(" + siteId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Server> IServerDataAccess.GetServerByIP(string ipAddress)
        {
            try
            {
                const string sp = "SERVER_GETBYIPADDRESS";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iIPAddress", DbType.AnsiString, ipAddress);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetServerByIP(" + ipAddress +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Delete <see cref="Server" />from bcms_server table by id.
        /// </summary>
        /// <param name="id">Pass id</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool IServerDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "SERVER_DELETEBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.ExecuteNonQuery(cmd);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.DeleteById()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Check <see cref="Server" />Name into  bcms_server table by name.
        /// </summary>
        /// <param name="name">Pass name</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool IServerDataAccess.IsExistByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "SERVER_ISEXISTBYNAME";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    AddOutputParameter(cmd);

                    Database.AddInParameter(cmd, Dbstring + "iName", DbType.AnsiString, name);

                    //#if ORACLE
                    //                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
                    //#endif

                    Database.ExecuteNonQuery(cmd);

                    int returnCode = GetReturnCodeFromParameter(cmd);

                    switch (returnCode)
                    {
                        case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                            {
                                return true;
                            }
                        case Constants.MySqlConstants.DBStatusCodeReturnEmpty:
                            {
                                return false;
                            }
                        default:
                            {
                                throw new SystemException(
                                    "An unexpected error has occurred while deleting this server.");
                            }
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.IsExistByName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IServerDataAccess.UpdateByStatusAndId(int id, int status)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("SERVER_UPDATEBYSTATUS"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);

                    Database.AddInParameter(dbCommand, Dbstring + "iStatus", DbType.AnsiString, status.ToString());

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, "Exception occurred while update server Details by Type ", exc);
            }
        }

        bool IServerDataAccess.UpdateIsVerified(int id, int isVerified)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_UpdateByIsVerified"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(dbCommand, Dbstring + "iStatus", DbType.AnsiString, "Pending");
                    Database.AddInParameter(dbCommand, Dbstring + "iIsVerified", DbType.Int32, isVerified);
                    Database.AddInParameter(dbCommand, Dbstring + "iErrorMessage", DbType.AnsiString, "");

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, "Exception occurred while update server Details by Type ", exc);
            }
        }

        IList<Server> IServerDataAccess.GetByUserinfraId(int userid)
        {
            try
            {
                if (userid < 1)
                {
                    throw new ArgumentNullException("userid");
                }
                const string sp = "SERVER_USERINFRAID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddInParameter(cmd, Dbstring+"iModules", DbType.AnsiString, modules.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iuserid", DbType.Int32, userid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        var serlogs = new List<Server>();

                        while (reader.Read())
                        {
                            var Logs = new Server
                            {
                                ServerOSTypeCount = Convert.IsDBNull(reader["ServerOSTypeCount"]) ? 0 : Convert.ToInt32(reader["ServerOSTypeCount"]),
                                OSType = Convert.IsDBNull(reader["OSType"]) ? string.Empty : Convert.ToString(reader["OSType"])
                            };

                            serlogs.Add(Logs);

                        }
                        return serlogs;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetByUserinfraId(" + userid +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<Server> IServerDataAccess.GetServersListUserinfraId(int userid, int companyid, string role, string ostype)
        {
            try
            {
                if (userid < 1)
                {
                    throw new ArgumentNullException("userid");
                }
                const string sp = "SERVER_LISTUSERINFRAID";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddInParameter(cmd, Dbstring+"iModules", DbType.AnsiString, modules.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iuserid", DbType.Int32, userid);
                    // Database.AddInParameter(cmd, Dbstring + "iostype", DbType.AnsiString, ostype);
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, companyid);
                    Database.AddInParameter(cmd, Dbstring + "irole", DbType.AnsiString, role);
                    Database.AddInParameter(cmd, Dbstring + "iostype", DbType.AnsiString, ostype);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        var serlogs = new List<Server>();

                        while (reader.Read())
                        {
                            var Logs = new Server
                            {
                                Id = Convert.IsDBNull(reader["id"]) ? 0 : Convert.ToInt32(reader["id"].ToString()),
                                OSType = Convert.IsDBNull(reader["ostype"]) ? string.Empty : Convert.ToString(reader["ostype"]),
                                Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                                Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                                IPAddress = Convert.IsDBNull(reader["IPAddress"]) ? string.Empty : Convert.ToString(reader["IPAddress"]),
                                Status = Convert.IsDBNull(reader["Status"])
                                 ? ServerStatus.Undefined
                                 : (ServerStatus)Enum.Parse(typeof(ServerStatus), reader["Status"].ToString(), true),
                                SSHUserName = Convert.IsDBNull(reader["SSHUserName"]) ? string.Empty : Convert.ToString(reader["SSHUserName"]),
                                SSHPassword = Convert.IsDBNull(reader["SSHPassword"]) ? string.Empty : Convert.ToString(reader["SSHPassword"])
                            };

                            serlogs.Add(Logs);

                        }
                        return serlogs;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetByUserinfraId(" + userid +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<Server> IServerDataAccess.GetServersListUserinfraId(int userid, int companyid, string role)
        {
            try
            {
                if (userid < 1)
                {
                    throw new ArgumentNullException("userid");
                }
                const string sp = "SERVER_LISTUSERINFRAIDList";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddInParameter(cmd, Dbstring+"iModules", DbType.AnsiString, modules.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iuserid", DbType.Int32, userid);
                    // Database.AddInParameter(cmd, Dbstring + "iostype", DbType.AnsiString, ostype);
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, companyid);
                    Database.AddInParameter(cmd, Dbstring + "irole", DbType.AnsiString, role);
                    //Database.AddInParameter(cmd, Dbstring + "iostype", DbType.AnsiString, ostype);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        var serlogs = new List<Server>();

                        while (reader.Read())
                        {
                            var Logs = new Server
                            {
                                Id = Convert.IsDBNull(reader["id"]) ? 0 : Convert.ToInt32(reader["id"].ToString()),
                                OSType = Convert.IsDBNull(reader["ostype"]) ? string.Empty : Convert.ToString(reader["ostype"]),
                                Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]),
                                Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : Convert.ToString(reader["Type"]),
                                IPAddress = Convert.IsDBNull(reader["IPAddress"]) ? string.Empty : Convert.ToString(reader["IPAddress"]),
                                Status = Convert.IsDBNull(reader["Status"])
                                 ? ServerStatus.Undefined
                                 : (ServerStatus)Enum.Parse(typeof(ServerStatus), reader["Status"].ToString(), true),
                                SSHUserName = Convert.IsDBNull(reader["SSHUserName"]) ? string.Empty : Convert.ToString(reader["SSHUserName"]),
                                SSHPassword = Convert.IsDBNull(reader["SSHPassword"]) ? string.Empty : Convert.ToString(reader["SSHPassword"])
                            };

                            serlogs.Add(Logs);

                        }
                        return serlogs;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetByUserinfraId(" + userid +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IServerDataAccess.UpdateServerIPAddress(string IpAddress, string userName, string pwd)
        {
            try
            {
                using (DbCommand dbCommand = Database.GetStoredProcCommand("Server_UpdateByIPAddress"))
                {
                    Database.AddInParameter(dbCommand, Dbstring + "iIPAddress", DbType.AnsiString, IpAddress);
                    Database.AddInParameter(dbCommand, Dbstring + "iSSHUserName", DbType.AnsiString, userName);
                    Database.AddInParameter(dbCommand, Dbstring + "iSudoPassword", DbType.AnsiString, pwd);

                    int isuccess = Database.ExecuteNonQuery(dbCommand);
                    return isuccess > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation, "Exception occurred while update server Details by Type ", exc);
            }
        }


        IList<Server> IServerDataAccess.GetAllServersNameAndId()
        {
            try
            {
                const string sp = "SERVERS_GETALL";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("SERVERS_GETALLCur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        IList<Server> serverList = new List<Server>();
                        while (reader.Read())
                        {
                            var _server = new Server();

                            _server.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
                            _server.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : reader["Name"].ToString();
                            _server.Type = Convert.IsDBNull(reader["Type"]) ? string.Empty : reader["Type"].ToString();

                            serverList.Add(_server);
                        }

                        return serverList;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetAllServersNameAndId()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<Server> IServerDataAccess.GetByAll()
        {
            try
            {
                const string sp = "SERVER_GETByALL";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetByAll()" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }


        IList<Server> IServerDataAccess.GetServerByDatabaseID(int DatabseId)
        {
            try
            {
                if (DatabseId < 1)
                {
                    throw new ArgumentNullException("DatabseId");
                }
                const string sp = "SERVER_GETBYDatabseId";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //Database.AddInParameter(cmd, Dbstring+"iModules", DbType.AnsiString, modules.ToString());
                    Database.AddInParameter(cmd, Dbstring + "iDatabseId", DbType.Int32, DatabseId);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<Server>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IServerDataAccess.GetServerByDatabaseID(" + DatabseId +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }



        #endregion Methods
    }
}