﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI
{
    public partial class ApprovalPortal : BasePage
    {
        public ILog Logger;

        public override void PrepareView()
        {
            if (Request.QueryString["Listitems"] != null)
            {
                string btnText = Request.QueryString["Listitems"];

                if (btnText == "Cancel")
                {
                    dataPager1.SetPageProperties(Convert.ToInt32(Session["PageIndex"] + "0"), 10, true);
                }
            }
            ViewState["_token"] = UrlHelper.AddTokenToRequest();
            if (ViewState["_token"] != null)
            {
                hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
            }
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            Utility.SelectMenu(Master, "Module3");
        }

        private void setListViewPage()
        {
            if ((Convert.ToInt32(Session["CurrentPageApprovalList"]) != -1) && Session["CurrentPageApprovalList"] != null && (Convert.ToInt32(Session["CurrentPageApprovalList"]) > 0))
            {
                if (Session["TotalPageRowsCount"] != null)
                {
                    if (Convert.ToInt32(Session["CurrentPageApprovalList"]) == Convert.ToInt32(Session["TotalPageRowsCount"]) - 1)
                    {
                        Session["CurrentPageApprovalList"] = Convert.ToInt32(Session["CurrentPageApprovalList"]) - dataPager1.MaximumRows;
                        Session["TotalPageRowsCount"] = null;
                    }
                }
                dataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageApprovalList"]), dataPager1.MaximumRows, true);
                Session["CurrentPageApprovalList"] = -1;

            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void lvComponent_ItemEditing(object sender, ListViewEditEventArgs e)
        {

        }

        protected void lvComponent_PreRender(object sender, EventArgs e)
        {
            try
            {
                var AprrovalProcessDetails = Facade.GetAllLevelProcessApprovals();
                if (AprrovalProcessDetails != null)
                {
                    var Approvalpendinglist = AprrovalProcessDetails.Where(X => X.ApprovalState == "Pending").ToList();
                    if (Approvalpendinglist != null)
                    {
                        lvComponent.DataSource = Approvalpendinglist;
                        lvComponent.DataBind();
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error occur while checking Prerender : " + ex.Message);
            }
        }

        protected void lvComponent_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            try
            {
                var edit = e.Item.FindControl("ProocessNoID") as Label;
                if (edit!= null)
                {
                    var appdetails = Facade.GetApprovalProcessById(Convert.ToInt32(edit.Text));
                    if(appdetails!= null)
                    {
                        lblExecutiontimeSlot.Text = (appdetails.ProfileExecutionStartTime).ToString() + "<b> TO </b>" + (appdetails.ProfileExecutionEndTime).ToString();
                        approvalproces.Text = appdetails.ApprovalProcessNumber;
                    }
                }
            }
            catch(Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Info("Exception Occured in lvComponent_ItemDataBound: " + ex.InnerException.Message.ToString());
            }
        }

        protected void lvComponent_ItemCommand(object sender, ListViewCommandEventArgs e)
        {
            try
            {
                if (e.CommandName == "Portal")
                {
                    modelbg.Visible = true;
                    Panel_smtphost.Visible = true;
                }
            }
            catch(Exception ex)
            {
                if (ex.InnerException != null)
                    Logger.Info("Exception Occured in lvComponent_ItemCommand: " + ex.InnerException.Message.ToString());
            }
        }

        protected void lvComponent_PagePropertiesChanging(object sender, PagePropertiesChangingEventArgs e)
        {
            Session["PageIndex"] = e.StartRowIndex / e.MaximumRows;
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        public string GetApprovalProcessNumber(object type)
        {
            string ApprovalProcessNumber = string.Empty;
            try
            {
                if ((Convert.ToInt32(type)) > 0)
                {
                    var Appleveldetails = Facade.GetApprovalProcessById(Convert.ToInt32(type));
                    if (Appleveldetails != null)
                    {
                        ApprovalProcessNumber = Appleveldetails.ApprovalProcessNumber;
                    }
                }
            }
            catch (Exception ex)
            {
                throw (ex);
            }
            return ApprovalProcessNumber;
        }

        protected void Lkbtnclosesmtphost_Click(object sender, EventArgs e)
        {
            modelbg.Visible = false;
            Panel_smtphost.Visible = false;
        }

        protected void btnsave_Click(object sender, EventArgs e)
        {

        }

        protected void btnclose_Click(object sender, EventArgs e)
        {

        }


    }
}