﻿using System;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.Helper;
using CP.UI.Controls;
using CP.ExceptionHandler;
using System.Collections.Generic;
using CP.BusinessFacade;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.Services;
using System.Text;
using System.DirectoryServices.ActiveDirectory;
using log4net;
using System.Net;
using System.DirectoryServices.AccountManagement;


namespace CP.UI.Admin
{
    public partial class MonitoringServices : BasePage
    {
        #region Variable

        private static int _currentLoginUserId;
        private static bool _isUserSuperAdmin;
        HttpContext Current = HttpContext.Current;

        public static string IPAddress = string.Empty;

        private static readonly ILog logger = LogManager.GetLogger(typeof(MonitoringServices));
        private static int _chkval = 0;

        public string MessageInitials
        {
            get { return "Monitoring Services"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Admin.MonitorServces;
                }
                return string.Empty;
            }
        }

        #endregion Variable

        #region Method

        public override void PrepareView()
        {
            try
            {
                logger.Info("In Prepareview Method and Logged In User is:" + LoggedInUserName);
                string hostName1 = Dns.GetHostName(); // Retrive the Name of HOST   
                IPAddress = Dns.GetHostByName(hostName1).AddressList[0].ToString();
                if (IsUserOperator || IsUserManager || IsUserCustom)
                {
                    Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                    return;
                }
                //ViewState["_token"] = UrlHelper.AddTokenToRequest();
                //if (ViewState["_token"] != null)
                //{
                //    hdtokenKey.Value = Convert.ToString(ViewState["_token"]);
                //}
                hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
                _isUserSuperAdmin = IsUserSuperAdmin;
                Current.Session["_isUserSuperAdmin"] = IsUserSuperAdmin;
                Current.Session["LoggedInUserId"] = LoggedInUserId;

                //BOC Validate Request
                ViewState["_token"] = UrlHelper.AddTokenToRequest() + ":" + CryptographyHelper.Md5Encrypt(HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString());
                if (ViewState["_token"] != null)
                {
                    hdtokenKey.Value = ViewState["_token"].ToString();
                }
                //EOC 


                Session["group"] = null;
                Utility.SelectMenu(Master, "Module4");

                if (LoggedInUserId > 0)
                {
                    _currentLoginUserId = LoggedInUserId;
                }
                if (!IsPostBack)
                {
                    IList<Workflow> workflowAll = null;
                    //Utility.PopulateBusinessService(DdlApplication, true);
                    //Utility.PopulateBusinessServiceByCompanyIdAndRole(DdlApplication, true, IsUserSuperAdmin, LoggedInUserCompanyId, LoggedInUserCompany, LoggedInUserRole);
                    IList<BusinessService> BusinessServiceList = Facade.GetBusinessServiceByCompanyIdAndRole(_currentLoginUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
                    if (BusinessServiceList != null)
                    {
                        DdlApplication.DataSource = BusinessServiceList;
                        DdlApplication.DataTextField = "Name";
                        DdlApplication.DataValueField = "Id";
                        DdlApplication.DataBind();

                        DdlApplication.AddDefaultItem(Constants.UIConstants.DropDownlItemPleaseSelectBusinessService);
                    }
                    if (Convert.ToBoolean(Current.Session["_isUserSuperAdmin"]))
                    {
                        Utility.PopulateWorkflows(ddlworkflow, true);
                    }
                    else
                    {
                        workflowAll = Facade.GetWorkflowsByUserrole(Convert.ToBoolean(HttpContext.Current.Session["_isUserSuperAdmin"]) == true ? UserRole.SuperAdmin.ToString() : UserRole.Administrator.ToString(), Convert.ToInt32(HttpContext.Current.Session["LoggedInUserId"]));
                        ddlworkflow.DataSource = workflowAll;
                        ddlworkflow.DataTextField = "Name";
                        ddlworkflow.DataValueField = "Id";
                        ddlworkflow.DataBind();
                        ddlworkflow.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownItemPleaseSelectWorkflowName, "000"));
                    }
                    ddlType.Enabled = false;
                    ddlworkflow.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in Prepareview Method and exception is:" + ex.Message);
            }
        }

        protected string GetApplicationName(object obj)
        {
            logger.Info("In GetApplicationName Method");
            var str = "";
            try
            {
                if (obj != null)
                {
                    var gid = Convert.ToInt32(obj);
                    if (gid > 0)
                    {
                        var getApp = Facade.GetBusinessServicesDetailByInfraObjectId(gid);
                        if (getApp != null)
                        {
                            if (getApp.Count > 0)
                            {
                                foreach (var item in getApp)
                                {
                                    str = item.Name;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  GetApplicationName Method and Exception is:" + ex.Message);
                return "";
            }
            return str;
        }

        protected string GetGroupName(object obj)
        {
            logger.Info("In GetGroupName Method");
            var str = "";
            try
            {
                if (obj != null)
                {
                    var gid = Convert.ToInt32(obj);
                    if (gid > 0)
                    {
                        var getApp = Facade.GetInfraObjectById(gid);

                        if (getApp != null)
                            str = getApp.Name;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  GetGroupName Method and Exception is:" + ex.Message);
                return "";
            }
            return str;
        }

        protected string GetServerName(object obj)
        {
            logger.Info("In GetServerName Method");
            var str = "";
            try
            {
                if (obj != null)
                {
                    var gid = Convert.ToInt32(obj);
                    if (gid > 0)
                    {
                        var getApp = Facade.GetServerById(gid);

                        if (getApp != null)
                            str = getApp.Name;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  GetServerName Method and Exception is:" + ex.Message);
                return "";
            }
            return str;
        }

        protected void BindList(int groupId)
        {
            logger.Info("In BindList Method");
            try
            {
                var getData = Facade.GetMonitoringServicesByInfraObjectId(groupId);
                if (getData != null)
                {
                    if (getData.Count > 0)
                    {
                        lvGroup.DataSource = getData;
                        lvGroup.DataBind();
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  BindList Method and Exception is:" + ex.Message);
            }
        }

        protected string GetServiceOrWorkflow(object objsvcId)
        {
            logger.Info("In GetServiceOrWorkflow Method");
            var str = "";
            try
            {
                if (objsvcId != null)
                {
                    var svcId = Convert.ToInt32(objsvcId);
                    if (svcId > 0)
                    {
                        var getSvc = Facade.GetMonitorServicesById(svcId);
                        if (getSvc != null)
                        {
                            if (getSvc.ServicePath == "" && getSvc.WorkflowId > 0)
                            {
                                var wf = Facade.GetWorkflowById(getSvc.WorkflowId);
                                if (wf != null)
                                {
                                    str = wf.Name;
                                }
                            }
                            else
                            {
                                str = getSvc.ServicePath;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  GetServiceOrWorkflow Method and Exception is:" + ex.Message);
                return "";
            }
            return str;
        }

        #endregion Method

        protected void DdlApplication_SelectedIndexChanged(object sender, EventArgs e)
        {
            logger.Info("In DdlApplication_SelectedIndexChanged Method");
            try
            {
                if (Convert.ToInt32(DdlApplication.SelectedValue) > 0)
                {
                    // lblexist.Visible = false;
                    DdlGroup.Enabled = true;
                    IList<InfraObject> gdata = null;
                    //  var infList = Facade.GetInfraObjectByBusinessServiceIdUserId(Convert.ToInt32(DdlApplication.SelectedValue),LoggedInUserId);
                    //var infList = Facade.GetInfraObjectByBusinessServiceId(Convert.ToInt32(DdlApplication.SelectedValue));
                    gdata = Facade.GetInfraObjectByUserIdCompanyIdRoleAndInfraObjectFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);


                    if (gdata != null)
                    {
                        var gdata1 = from n in gdata
                                     where n.BusinessServiceId == Convert.ToInt32(DdlApplication.SelectedValue)
                                     select n;
                        if (gdata1 != null)
                        {
                            DdlGroup.DataSource = gdata1;
                            DdlGroup.DataTextField = "Name";
                            DdlGroup.DataValueField = "Id";
                            DdlGroup.DataBind();
                        }
                        DdlGroup.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectInfraName, "0"));
                    }
                    //Utility.PopulateIn(DdlGroup, Convert.ToInt32(DdlApplication.SelectedValue), true);
                }

                ddlType.Enabled = false;
                ddlworkflow.Enabled = false;
                txtServicePath.Enabled = false;
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  DdlApplication_SelectedIndexChanged Method and Exception is:" + ex.Message);
            }
        }

        protected void DdlGroup_SelectedIndexChanged(object sender, EventArgs e)
        {
            logger.Info("In DdlGroup_SelectedIndexChanged Method");
            try
            {
                if (Convert.ToInt32(DdlGroup.SelectedValue) > 0)
                {
                    //lblexist.Visible = false;
                    var serverlist = Facade.GetServersByInfraObjectId(Convert.ToInt32(DdlGroup.SelectedValue));
                    if (serverlist != null)
                    {
                        if (serverlist.Count > 0)
                        {
                            DdlServer.DataSource = serverlist;
                            DdlServer.DataTextField = "Name";
                            DdlServer.DataValueField = "Id";
                            DdlServer.DataBind();
                        }
                        DdlServer.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "0"));
                    }

                    DdlServer.Enabled = true;
                    BindList(Convert.ToInt32(DdlGroup.SelectedValue));
                    txtServicePath.Text = string.Empty;
                    btnAdd.Text = "Save";
                    btnAdd.Enabled = false;
                    Session.Remove("ManageId");
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  DdlGroup_SelectedIndexChanged Method and Exception is:" + ex.Message);
            }
        }

        protected void btnValidate2Factor_Click(object sender, EventArgs e)
        {
            pnlPrivType.Visible = true;
            btnsavdel.Visible = false;
            btnSave.Visible = true;
            //  CheckLoginCredentail("Login");
        }

        protected void btnAdd_Click(object sender, EventArgs e)
        {

            if ((ViewState["_token"] != null) && Page.IsValid && ValidateRequest("MonitoringServices", UserActionType.CreateMonitoringServices))
            {

                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);

                }
                else
                {
                    bool chkcredential = false;
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    string domainname = "";
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }
                    try
                    {
                        Button btn = (Button)sender;
                        string args = btn.CommandArgument;

                        if (btnAdd.Text == "Save")
                        {
                            try
                            {

                                chkcredential = CheckLoginCredentail(args);
                                UserName.Text = "";
                                if (chkcredential)
                                {
                                    var manageService = new MonitorServices();
                                    manageService.BusinessFunctionId = Convert.ToInt32(DdlApplication.SelectedValue);
                                    manageService.InfraobjectId = Convert.ToInt32(DdlGroup.SelectedValue);
                                    manageService.ServerId = Convert.ToInt32(DdlServer.SelectedValue);
                                    manageService.Type = Convert.ToInt32(ddlType.SelectedValue);
                                    if (!string.IsNullOrEmpty(ddlworkflow.SelectedValue))
                                        manageService.WorkflowId = Convert.ToInt32(ddlworkflow.SelectedValue);
                                    manageService.ServicePath = (txtServicePath.Text.ToString()).Trim();
                                    manageService.CreatorId = _currentLoginUserId;
                                    manageService.WORKFLOWTYPE = Convert.ToInt32(wrkflwtype.SelectedValue);
                                    //if (!Facade.IsExistMONITORSERVICE(manageService))
                                    //{
                                    bool _IsExists = true;
                                    if (ddlworkflow.SelectedValue == "0")
                                        _IsExists = Facade.ISServiceRecordExists(manageService.BusinessFunctionId, manageService.InfraobjectId, manageService.ServerId, manageService.ServicePath);
                                    else
                                        _IsExists = Facade.ISRecordExists(manageService.BusinessFunctionId, manageService.InfraobjectId, manageService.ServerId, manageService.WorkflowId, manageService.WORKFLOWTYPE);


                                    if (!_IsExists)
                                    {

                                        var save = Facade.AddMonitorServices(manageService);
                                        if (!string.IsNullOrEmpty(combobox1.Value))
                                        {
                                            // domainname = combobox1.Value + "\\" + LoggedInUserName;
                                            domainname = HttpContext.Current.Session["loginUserName"].ToString();
                                        }
                                        else
                                        {
                                            // domainname = LoggedInUserName;
                                            domainname = HttpContext.Current.Session["loginUserName"].ToString(); ;
                                        }
                                        if (ddlworkflow.SelectedValue != "0")
                                        {
                                            var _wrkflwinfo = Facade.GetWorkflowById(manageService.WorkflowId);
                                            ActivityLogger.AddLog1(domainname, "MonitoringService", UserActionType.MonitorServiceAdd, "Workflow '" + _wrkflwinfo.Name + "' is added in MonitorService table", LoggedInUserId, IPAddress);
                                            logger.Info("Workflow ID '" + manageService.WorkflowId + "' is added in MonitorService table");
                                        }
                                        else
                                        {
                                            ActivityLogger.AddLog1(domainname, "MonitoringService", UserActionType.MonitorServiceAdd, "Service '" + manageService.ServicePath + "' is added in MonitorService table", LoggedInUserId, IPAddress);
                                            logger.Info("Service  '" + manageService.ServicePath + "' is added in MonitorService table");
                                        }
                                        if (save.Id > 0)
                                        {
                                            BindList(Convert.ToInt32(DdlGroup.SelectedValue));
                                            btnAdd.Enabled = false;
                                            btnAdd.Text = "Save";
                                            //Utility.PopulateServerByInfraId(DdlServer, Convert.ToInt32(DdlGroup.SelectedValue), true);
                                            txtServicePath.Text = string.Empty;
                                            DdlServer.SelectedValue = "0";

                                            var currentTransactionType = TransactionType.Save;
                                            string message = MessageInitials + " " + '"' + DdlApplication.SelectedItem.Text + '"';
                                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));
                                        }
                                    }
                                    else
                                    {
                                        var currentTransactionType = TransactionType.Exists;
                                        string message = MessageInitials + " " + '"' + DdlApplication.SelectedItem.Text + '"' + " is already Exists";
                                        ErrorSuccessNotifier.AddErrorMessage(message);
                                    }
                                }
                                else
                                {

                                    returnUrl = string.Empty;
                                }
                            }
                            catch (Exception ex)
                            {
                                if (ex != null)
                                {

                                    logger.Error("CP Exception while Adding Record in MonitorService,LoggedInUserID is: " + LoggedInUserId + " With User IP Address:" + IPAddress + " and Exception is:" + ex.Message);
                                    if (ex.InnerException != null)
                                        logger.Error("Inner Exception : " + ex.InnerException.ToString());
                                    if (ex.StackTrace != null)
                                        logger.Error("Exception details : " + ex.StackTrace.ToString());
                                }
                            }
                        }
                        else
                        {

                            pnlPrivType.Visible = true;
                            btnSave.Visible = true;
                            btnsavdel.Visible = false;
                            chkcredential = CheckLoginCredentail(args);
                            UserName.Text = "";
                            if (chkcredential)
                            {
                                var manageService = new MonitorServices();
                                manageService.Id = Convert.ToInt32(Session["ManageId"].ToString());
                                manageService.BusinessFunctionId = Convert.ToInt32(DdlApplication.SelectedValue);
                                manageService.InfraobjectId = Convert.ToInt32(DdlGroup.SelectedValue);
                                manageService.ServerId = Convert.ToInt32(DdlServer.SelectedValue);
                                manageService.Type = Convert.ToInt32(ddlType.SelectedValue);
                                // manageService.WorkflowId = Convert.ToInt32(ddlworkflow.SelectedValue);
                                string _wrkflwname = "";
                                if (!string.IsNullOrEmpty(ddlworkflow.SelectedValue))
                                    _wrkflwname = ddlworkflow.SelectedItem.ToString();

                                if (!string.IsNullOrEmpty(_wrkflwname))
                                {
                                    var _wrkflwlst = Facade.GetWorkflowByName(_wrkflwname);
                                    if (_wrkflwlst != null)
                                    {
                                        manageService.WorkflowId = Convert.ToInt32(_wrkflwlst.Id);
                                    }
                                    else
                                    {
                                        manageService.WorkflowId = Convert.ToInt32(0);
                                    }

                                }
                                else
                                {
                                    manageService.WorkflowId = Convert.ToInt32(0);
                                }
                                manageService.ServicePath = (txtServicePath.Text.ToString()).Trim();
                                manageService.UpdatorId = _currentLoginUserId;
                                manageService.WORKFLOWTYPE = Convert.ToInt32(wrkflwtype.SelectedValue);
                                var existingBS = Facade.GetMonitorServicesById(Convert.ToInt32(Session["ManageId"].ToString()));
                                //if (!Facade.IsExistMONITORSERVICE(manageService))
                                //{
                                bool _IsExists = true;
                                bool _IsExistt = false;

                                if (ddlworkflow.SelectedValue != "0")
                                {
                                    if ((existingBS.BusinessFunctionId != manageService.BusinessFunctionId) || (existingBS.InfraobjectId != manageService.InfraobjectId) || (existingBS.ServerId != manageService.ServerId) || (existingBS.WorkflowId != manageService.WorkflowId))
                                    {
                                        _IsExists = Facade.ISRecordExists(manageService.BusinessFunctionId, manageService.InfraobjectId, manageService.ServerId, manageService.WorkflowId, manageService.WORKFLOWTYPE);
                                        _IsExistt = true;
                                    }
                                    else
                                    {
                                        _IsExists = false;
                                        _IsExistt = true;
                                    }
                                }
                                else
                                {

                                    if ((existingBS.BusinessFunctionId != manageService.BusinessFunctionId) || (existingBS.InfraobjectId != manageService.InfraobjectId) || (existingBS.ServerId != manageService.ServerId) || (existingBS.ServicePath != manageService.ServicePath))
                                    {
                                        _IsExists = Facade.ISServiceRecordExists(manageService.BusinessFunctionId, manageService.InfraobjectId, manageService.ServerId, manageService.ServicePath);
                                        _IsExistt = true;
                                    }
                                    else
                                    {
                                        _IsExists = false;
                                        _IsExistt = true;
                                    }
                                }

                                if (!_IsExists && _IsExistt)
                                {
                                    var update = Facade.UpdateMonitorServices(manageService);
                                    if (!string.IsNullOrEmpty(combobox1.Value))
                                    {
                                        domainname = HttpContext.Current.Session["loginUserName"].ToString(); //combobox1.Value + "\\" + LoggedInUserName;
                                    }
                                    else
                                    {
                                        domainname = HttpContext.Current.Session["loginUserName"].ToString(); //LoggedInUserName;
                                    }
                                    if (ddlworkflow.SelectedValue != "0")
                                    {
                                        var _wrkflwinfo = Facade.GetWorkflowById(manageService.WorkflowId);
                                        ActivityLogger.AddLog1(domainname, "MonitoringService", UserActionType.MonitorServiceUpdate, "Workflow '" + _wrkflwinfo.Name + "' is Updated in MonitorService table", LoggedInUserId, IPAddress);
                                        logger.Info("Workflow ID '" + manageService.WorkflowId + "' is Updated in MonitorService table");

                                    }
                                    else
                                    {
                                        ActivityLogger.AddLog1(domainname, "MonitoringService", UserActionType.MonitorServiceUpdate, "Service '" + manageService.ServicePath + "' is Updated in MonitorService table", LoggedInUserId, IPAddress);
                                        logger.Info("Service '" + manageService.ServicePath + "' is Updated in MonitorService table");


                                    }
                                    if (update.Id > 0)
                                    {
                                        BindList(Convert.ToInt32(DdlGroup.SelectedValue));
                                        btnAdd.Enabled = false;
                                        btnAdd.Text = "Save";
                                        //Utility.PopulateServerByGroupId(DdlServer, Convert.ToInt32(DdlGroup.SelectedValue), true);
                                        txtServicePath.Text = string.Empty;
                                        DdlServer.SelectedValue = "0";

                                        var currentTransactionType = TransactionType.Update;
                                        string message = MessageInitials + " " + '"' + DdlApplication.SelectedItem.Text + '"';
                                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));
                                    }
                                }
                                else
                                {
                                    //lblexist.Visible = true;
                                    //returnUrl = string.Empty;
                                    var currentTransactionType = TransactionType.Exists;
                                    string message = MessageInitials + " " + '"' + DdlApplication.SelectedItem.Text + '"' + " is already Exists";
                                    ErrorSuccessNotifier.AddErrorMessage(message);

                                }
                            }
                            else
                            {

                                returnUrl = string.Empty;
                            }
                        }
                    }
                    catch (CpException ex)
                    {

                        returnUrl = Request.RawUrl;
                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                        ExceptionManager.Manage(ex, this);
                    }
                    catch (Exception ex)
                    {

                        returnUrl = Request.RawUrl;
                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, this);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled,
                                "Unhandled exception occurred while saving data", ex);
                            ExceptionManager.Manage(customEx, this);
                        }
                    }
                    if (returnUrl.IsNotNullOrEmpty())
                    {
                        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Urls.Admin.MonitorServces);

                        Helper.Url.Redirect(secureUrl);
                    }
                }
            }
        }

        protected void lvGroup_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            logger.Info("In lvGroup_ItemDeleting Method");
            try
            {
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                bool chkcredential = false;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }
                var lblDeleteId = (lvGroup.Items[e.ItemIndex].FindControl("lblId")) as Label;
                if (lblDeleteId != null)
                {
                    hdndelid.Value = "";
                    hdndelid.Value = lblDeleteId.Text;
                    Session["DeleteID"] = lblDeleteId.Text;
                    pnlPrivType.Visible = true;
                    btnSave.Visible = false;
                    btnsavdel.Visible = true;
                }
                //if (lblDeleteId != null && ValidateRequest("MonitoringServices Delete", UserActionType.DeleteMonitorProfile))
                //{
                //    int dId = Convert.ToInt32(lblDeleteId.Text);
                //    if (dId > 0)
                //    {
                //        Facade.DeleteMonitorServices(dId);
                //        BindList(Convert.ToInt32(DdlGroup.SelectedValue));

                //        var currentTransactionType = TransactionType.Delete;
                //        string message = MessageInitials + " " + '"' + DdlApplication.SelectedItem.Text + '"';
                //        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));
                //        if (returnUrl.IsNotNullOrEmpty())
                //        {
                //            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Urls.Admin.MonitorServces);

                //            Helper.Url.Redirect(secureUrl);
                //        }
                //    }
                //}
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  lvGroup_ItemDeleting Method and Exception is:" + ex.Message);
            }
        }

        protected void lvGroup_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            logger.Info("In lvGroup_ItemEditing Method");
            try
            {
                UpdatepanelAdd.Update();
                ddlType.Enabled = true;
                var lblId = (lvGroup.Items[e.NewEditIndex].FindControl("lblId")) as Label;
                var lblApplicationName = (lvGroup.Items[e.NewEditIndex].FindControl("lblapplicationId")) as Label;
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "MonitoringService Edit", UserActionType.UpdateMonitorProfile, "The MonitoringService '" + lblApplicationName.Text + "' Opened as Editing Mode.", LoggedInUserId);

                if (lblId != null && ValidateRequest("MonitoringServices Edit", UserActionType.UpdateMonitorProfile))
                {



                    btnAdd.Text = "Update";
                    btnAdd.Enabled = true;
                    txtServicePath.Enabled = true;
                    Session["ManageId"] = lblId.Text;
                    var getValue = Facade.GetMonitorServicesById(Convert.ToInt32(lblId.Text));

                    var serverlist = Facade.GetServersByInfraObjectId(Convert.ToInt32(DdlGroup.SelectedValue));
                    if (serverlist != null)
                    {
                        if (serverlist.Count > 0)
                        {
                            DdlServer.DataSource = serverlist;
                            DdlServer.DataTextField = "Name";
                            DdlServer.DataValueField = "Id";
                            DdlServer.DataBind();
                        }
                        DdlServer.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectServerName, "0"));
                    }
                    if (getValue != null)
                    {
                        DdlServer.SelectedValue = getValue.ServerId.ToString();
                        txtServicePath.Text = (getValue.ServicePath.ToString()).Trim();


                        ddlType.ClearItems();

                        if (getValue.Type.ToString() == "1")
                        {
                            ddlType.AddItem("Select Type", "0");
                            ddlType.AddItem("Use ps -ef", "1");
                            ddlType.AddItem("Use Workflow", "3");
                        }
                        else if (getValue.Type.ToString() == "2")
                        {
                            ddlType.AddItem("Select Type", "0");
                            ddlType.AddItem("Use WMI", "2");
                            ddlType.AddItem("Use Workflow", "3");
                            ddlType.AddItem("Use ssh", "4");
                        }
                        else if (getValue.Type.ToString() == "4")
                        {
                            ddlType.AddItem("Select Type", "0");
                            ddlType.AddItem("Use WMI", "2");
                            ddlType.AddItem("Use Workflow", "3");
                            ddlType.AddItem("Use ssh", "4");
                        }
                        else if (getValue.Type.ToString() == "5")
                        {
                            ddlType.AddItem("Select Type", "0");
                            ddlType.AddItem("Use WMI", "2");
                            ddlType.AddItem("Use Workflow", "3");
                            ddlType.AddItem("Use ssh", "4");
                            ddlType.AddItem("Use PowerShell", "5");
                        }
                        else
                        {
                            ddlType.AddItem("Select Type", "0");
                            ddlType.AddItem("Use ps -ef", "1");
                            ddlType.AddItem("Use Workflow", "3");
                            //ddlType.AddItem("Select Type", "0");
                            //ddlType.AddItem("Use Workflow", "3");
                        }

                        ddlType.SelectedValue = getValue.Type.ToString();

                        if (getValue.Type.ToString() == "3")
                        {
                            ServicePathdiv.Visible = false;
                            workflowdiv.Visible = true;
                            ddlworkflow.Enabled = true;
                            txtServicePath.Enabled = false;
                            //ddlworkflow.SelectedValue = getValue.WorkflowId.ToString();
                            int infraid = (Convert.ToInt32(DdlGroup.SelectedValue));
                            if (getValue.WORKFLOWTYPE == 1)
                            {
                                //Utility.PopulateWorkflows(ddlworkflow, true, 1,infraid);

                                var _workflowList = Facade.GetExistingWorkflowbyInfraobject(infraid);
                                var grpwrkflwlist = Facade.GetAllGroupWorkflows();
                                if (_workflowList != null && grpwrkflwlist != null)
                                {
                                    var _workflowListByOrder = from a in _workflowList
                                                               join b in grpwrkflwlist on a.Id equals b.WorkflowId
                                                               where b.ActionType == 7
                                                               select a;

                                    ddlworkflow.DataSource = _workflowListByOrder;
                                    ddlworkflow.DataTextField = "Name";
                                    ddlworkflow.DataValueField = "Id";
                                    ddlworkflow.DataBind();
                                }
                                ddlworkflow.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName, "0"));
                                wrkflwtype.SelectedValue = "1";
                            }
                            else if (getValue.WORKFLOWTYPE == 2)
                            {
                                //Utility.PopulateWorkflows(ddlworkflow, true, 2,infraid);
                                var _workflowList = Facade.GetExistingWorkflowbyInfraobject(infraid);
                                var grpwrkflwlist = Facade.GetAllGroupWorkflows();
                                if (_workflowList != null && grpwrkflwlist != null)
                                {
                                    var _workflowListByOrder = from a in _workflowList
                                                               join b in grpwrkflwlist on a.Id equals b.WorkflowId
                                                               where b.ActionType == 8
                                                               select a;

                                    ddlworkflow.DataSource = _workflowListByOrder;
                                    ddlworkflow.DataTextField = "Name";
                                    ddlworkflow.DataValueField = "Id";
                                    ddlworkflow.DataBind();
                                }
                                ddlworkflow.Items.Insert(0, new ListItem(Constants.UIConstants.DropDownlItemPleaseSelectWorkflowName, "0"));
                                wrkflwtype.SelectedValue = "2";
                            }
                            // }
                            var wrkflwname = Facade.GetWorkflowById(Convert.ToInt32(getValue.WorkflowId.ToString()));
                            if (wrkflwname != null)
                            {
                                if (string.IsNullOrEmpty(getValue.WORKFLOWTYPE.ToString()))
                                {
                                    Utility.PopulateWorkflows(ddlworkflow, true);
                                }
                                // ddlworkflow.SelectedItem.Text = wrkflwname.Name.ToString();
                                ddlworkflow.SelectedValue = getValue.WorkflowId.ToString();
                            }
                        }
                        else
                        {
                            ddlworkflow.Enabled = false;
                            ServicePathdiv.Visible = true;
                            workflowdiv.Visible = false;
                            txtServicePath.Enabled = true;
                            txtServicePath.Text = (getValue.ServicePath).Trim();
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  lvGroup_ItemEditing Method and Exception is:" + ex.Message);
            }
        }

        protected void DdlServer_SelectedIndexChanged(object sender, EventArgs e)
        {
            logger.Info("In DdlServer_SelectedIndexChanged Method");
            try
            {
                ddlType.Enabled = true;
                ddlType.Enabled = true;
                //lblexist.Visible = false;
                var uId = 0;
                if (Session["ManageId"] != null)
                {
                    uId = Convert.ToInt32(Session["ManageId"].ToString());
                }

                if (Convert.ToInt32(DdlServer.SelectedValue) > 0)
                {
                    btnAdd.Enabled = true;
                    txtServicePath.Enabled = true;
                    if (uId == 0)
                    {
                        btnAdd.Text = "Save";
                    }
                    else
                    {
                        btnAdd.Text = "Update";
                    }
                }
                else
                {
                    btnAdd.Enabled = false;
                    txtServicePath.Enabled = false;
                    ddlType.Enabled = true;
                    ddlType.SelectedIndex = 0;
                    ddlworkflow.SelectedIndex = 0;
                    return;
                }


                int id = Convert.ToInt32(DdlServer.SelectedValue);

                var server = Facade.GetServerById(id);

                ddlType.ClearItems();

                if (server.OSType.Contains("Linux"))
                {
                    ddlType.AddItem("Select Type", "0");
                    ddlType.AddItem("Use ps -ef", "1");
                    ddlType.AddItem("Use Workflow", "3");
                }
                else if (server.OSType.Contains("Windows"))
                {
                    ddlType.AddItem("Select Type", "0");
                    ddlType.AddItem("Use WMI", "2");
                    ddlType.AddItem("Use Workflow", "3");
                    ddlType.AddItem("Use ssh", "4");
                    ddlType.AddItem("Use PowerShell", "5");
                }
                else
                {
                    ddlType.AddItem("Select Type", "0");
                    ddlType.AddItem("Use ps -ef", "1");
                    ddlType.AddItem("Use Workflow", "3");
                    //ddlType.AddItem("Select Type", "0");
                    //ddlType.AddItem("Use Workflow", "3");
                }
                ddlType.Enabled = true;
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  DdlServer_SelectedIndexChanged Method and Exception is:" + ex.Message);
            }
        }

        protected void ddlType_SelectedIndexChanged(object sender, EventArgs e)
        {
            logger.Info("In ddlType_SelectedIndexChanged Method");
            try
            {
                ddlworkflow.Enabled = true;
                txtServicePath.Enabled = true;
                // lblexist.Visible = false;
                var uId = 0;
                if (Session["ManageId"] != null)
                {
                    uId = Convert.ToInt32(Session["ManageId"].ToString());
                }

                if (Convert.ToInt32(ddlType.SelectedValue) == 3)
                {
                    //workflowdiv.Visible = true;
                    workflowtypediv.Visible = true;
                    txtServicePath.Enabled = false;
                    ServicePathdiv.Visible = false;
                }
                else
                {
                    ServicePathdiv.Visible = true;
                    // workflowdiv.Visible = false;
                    workflowtypediv.Visible = false;

                    if (Convert.ToInt32(ddlType.SelectedValue) > 0)
                    {
                        txtServicePath.Enabled = true;
                        btnAdd.Enabled = true;
                        txtServicePath.Enabled = true;
                        if (uId == 0)
                        {
                            btnAdd.Text = "Save";
                        }
                        else
                        {
                            btnAdd.Text = "Update";
                        }
                    }
                    else
                    {
                        btnAdd.Enabled = false;
                        txtServicePath.Enabled = false;
                    }

                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  ddlType_SelectedIndexChanged Method and Exception is:" + ex.Message);
            }
        }

        protected void ddlworkflow_SelectedIndexChanged(object sender, EventArgs e)
        {
            logger.Info("In ddlworkflow_SelectedIndexChanged Method");
            try
            {
                var uId = 0;
                // lblexist.Visible = false;

                if (Session["ManageId"] != null)
                {
                    uId = Convert.ToInt32(Session["ManageId"].ToString());
                }

                if (Convert.ToInt32(ddlworkflow.SelectedValue) > 0)
                {
                    btnAdd.Enabled = true;
                    txtServicePath.Enabled = false;
                    if (uId == 0)
                    {
                        btnAdd.Text = "Save";
                    }
                    else
                    {
                        btnAdd.Text = "Update";
                    }
                }
                else
                {
                    btnAdd.Enabled = false;
                    txtServicePath.Enabled = false;
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  ddlworkflow_SelectedIndexChanged Method and Exception is:" + ex.Message);
            }
        }

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((ViewState["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}


        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((ViewState["_token"] != null) && (ViewState["_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(ViewState["_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(ViewState["_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }
        //EOC Validate Request


        protected bool ValidateInput()
        {
            try
            {
                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtCountryCode");
                //IgnoreIDs.Add("txtCountryCode2");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        protected void wrkflwtype_SelectedIndexChanged(object sender, EventArgs e)
        {
            logger.Info("In wrkflwtype_SelectedIndexChanged Method");
            try
            {
                workflowdiv.Visible = true;
                ddlworkflow.Enabled = true;
                int infraID = Convert.ToInt32(DdlGroup.SelectedValue);
                //Utility.PopulateWorkflows(ddlworkflow, true);
                if (Convert.ToInt32(wrkflwtype.SelectedValue) == 1)
                {
                    Utility.PopulateWorkflows(ddlworkflow, true, 1, infraID);
                }
                else if (Convert.ToInt32(wrkflwtype.SelectedValue) == 2)
                {

                    Utility.PopulateWorkflows(ddlworkflow, true, 2, infraID);
                    // workflowdiv.Visible = true;
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in wrkflwtype_SelectedIndexChanged method :" + ex.Message);
            }
        }

        protected void lnkbtnClose_Click(object sender, EventArgs e)
        {
            logger.Info("In lnkbtnClose_Click Method");
            try
            {
                _chkval = 0;
                pnlPrivType.Visible = false;
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }

                if (returnUrl.IsNotNullOrEmpty())
                {
                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Urls.Admin.MonitorServces);

                    Helper.Url.Redirect(secureUrl);
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  lnkbtnClose_Click Method and Exception is:" + ex.Message);
            }
        }

        protected void chkActiveDirectory_CheckedChanged(object sender, EventArgs e)
        {
            logger.Info("In chkActiveDirectory_CheckedChanged()");
            try
            {
                if (chkActiveDirectory.Checked)
                {
                    combobox1.Visible = true;
                    lbldomainName.Visible = true;
                    //combobox1.Visible = true;
                    pnlDomain.Visible = true;
                    ScriptManager.RegisterStartupScript(UpdatePanel1, this.GetType(), "MyAction", "binddropdown();", true);

                }
                else
                {
                    combobox1.Visible = false;
                    // combobox1.Visible = false;
                    lbldomainName.Visible = false;
                    pnlDomain.Visible = false;
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in chkActiveDirectory_CheckedChanged() and the exception is:" + ex.Message);
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            //try
            //{
            //    User us = new User();
            //    string username = UserName.Text;
            //    string orignalStr = CryptographyHelper.Md5Encrypt(Password.Text);
            //    if (username != null && orignalStr != null)
            //    {

            //        us.UserName = username;
            //        us.LoginName = username;
            //        us.LoginPassword = orignalStr;
            //        var _chklogin = Facade.LoginUser(us);
            //        if (_chklogin != null)
            //        {

            //            _chkval = 1;
            //            pnlPrivType.Visible = false;
            //        }
            //        else
            //        {
            //            labelSDErrormessage.Visible = true;
            //            labelSDErrormessage.Text = "Authentication Failed";
            //        }

            //    }

            //}
            //catch (Exception ex)
            //{
            //    throw ex;
            //}
        }

        protected void btnsavdel_Click(object sender, EventArgs e)
        {
            logger.Info("In btnsavdel_Click Method");
            string domainname = "";
            string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
            bool chkcredential = false;
            int dId = 0;
            if (returnUrl.IsNullOrEmpty())
            {
                returnUrl = ReturnUrl;
            }
            try
            {
                Button btn = (Button)sender;
                string args = btn.CommandArgument;
                chkcredential = CheckLoginCredentail(args);
                UserName.Text = "";

                if (chkcredential)
                {
                    //int dId = Convert.ToInt32(lblDeleteId.Text);
                    //int dId = Convert.ToInt32(hdndelid.Value);
                    dId = Convert.ToInt32(Session["DeleteID"].ToString());
                    if (dId > 0)
                    {

                        Facade.DeleteMonitorServices(dId);
                        if (!string.IsNullOrEmpty(combobox1.Value))
                        {
                            domainname = HttpContext.Current.Session["loginUserName"].ToString(); //combobox1.Value + "\\" + LoggedInUserName;
                        }
                        else
                        {
                            domainname = HttpContext.Current.Session["loginUserName"].ToString();// LoggedInUserName;
                        }
                        ActivityLogger.AddLog1(domainname, "MonitoringService", UserActionType.MonitorServiceDelete, " ID:- '" + dId + "' is InActive from MonitorService table", LoggedInUserId, IPAddress);
                        logger.Info(" ID:- '" + dId + "' is InActive from MonitorService table");
                        BindList(Convert.ToInt32(DdlGroup.SelectedValue));

                        var currentTransactionType = TransactionType.Delete;
                        string message = MessageInitials + " " + '"' + DdlApplication.SelectedItem.Text + '"';
                        ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message, currentTransactionType));
                        if (returnUrl.IsNotNullOrEmpty())
                        {
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Urls.Admin.MonitorServces);

                            Helper.Url.Redirect(secureUrl);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (ex != null)
                {
                    logger.Error("CP Exception while Deleting  Record From MonitorService,LoggedInUserID is: " + LoggedInUserId + " With User IP Address:" + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
            }
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            logger.Info("In Button1_Click Method");
            try
            {
                _chkval = 0;
                pnlPrivType.Visible = false;
                string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                if (returnUrl.IsNullOrEmpty())
                {
                    returnUrl = ReturnUrl;
                }

                if (returnUrl.IsNotNullOrEmpty())
                {
                    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Urls.Admin.MonitorServces);

                    Helper.Url.Redirect(secureUrl);
                }
            }
            catch (Exception ex)
            {
                logger.Error("Exception in  Button1_Click Method and Exception is:" + ex.Message);
            }
        }

        [WebMethod]
        public static string DiscoverDomains()
        {
            logger.Info("In DiscoverDomains()");
            try
            {
                StringBuilder _domain = new StringBuilder();
                var returnValue = "";
                List<Domain> _domains = new List<Domain>();

                Forest currentForest = Forest.GetCurrentForest();
                DomainCollection domains = currentForest.Domains;
                //foreach (Domain objDomain in domains)
                //{
                //    _domains.Add(objDomain);

                //    returnValue = string.Format("{0}{1}", returnValue, objDomain.Name);
                //    _logger.Info("Discovered domains are " + returnValue);
                //    return returnValue.TrimEnd(',');
                //}
                //return returnValue;
                foreach (Domain objDomain in domains)
                {
                    _domains.Add(objDomain);
                    _domain.Append(objDomain.Name);
                    _domain.Append(",");
                    // _domains.Add(objDomain);
                    // _domain.Append(",petc,petch12");
                    //returnValue = string.Format("{0}{1}", returnValue, objDomain.Name);
                    logger.Info("Discovered domains are " + objDomain.Name);
                    //return returnValue.TrimEnd(',');
                    // return returnValue;
                }
                //return returnValue;
                returnValue = string.Format("{0}{1}", returnValue, _domain.ToString());
                returnValue.Remove(returnValue.Length - 1, 1);
                return returnValue;
                // return null;

            }
            catch (Exception ex)
            {
                // _logger.Info("Exception Occured while populating company names :" + ex.InnerException.Message);
                // ActivityLogger.
                logger.Error("Discover domains return empty domains and the exception is:" + ex.Message);
                return null;
            }
        }

        public bool CheckLoginCredentail(string chkname)
        {
            logger.Info("In CheckLoginCredentail Method");
            bool result = false;
            try
            {
                if (chkname == "Save" || chkname == "Delete")
                {
                }
                else
                {
                    try
                    {
                        if (chkActiveDirectory.Checked)
                        {
                            User us = new User();
                            string username = UserName.Text;
                            //string orignalStr = CryptographyHelper.Md5Encrypt(Password.Text);
                            string orignalStr = Password.Text;
                            if (username.Equals(LoggedInUserName))
                            {
                                if (!string.IsNullOrEmpty(Session["_domainNamee"].ToString()) && combobox1.Value.ToString().Equals(Session["_domainNamee"].ToString()))
                                {

                                    if (AuthenticateADUser(combobox1.Value, username, orignalStr))
                                    {
                                        result = true;
                                        pnlPrivType.Visible = false;
                                        //_chkval = 0;
                                        logger.Info("Authenticate Successfully with LoogedInUser:" + LoggedInUserName);
                                    }
                                    else
                                    {
                                        logger.Info("Authenticate Failed with LoogedInUser:" + LoggedInUserName);
                                        result = false;
                                        labelSDErrormessage.Visible = true;
                                        labelSDErrormessage.Text = "Authentication Failed";
                                    }
                                }
                                else
                                {
                                    logger.Info("Authenticate Failed with LoogedInUser:" + LoggedInUserName);
                                    result = false;
                                    labelSDErrormessage.Visible = true;
                                    labelSDErrormessage.Text = "Authentication Failed";
                                }
                            }
                            else
                            {
                                logger.Info("Authenticate Failed , LoggedInUser and UserName is Incorrect:" + LoggedInUserName);
                                result = false;
                                labelSDErrormessage.Visible = true;
                                labelSDErrormessage.Text = "Authentication Failed";
                            }
                        }
                        else
                        {
                            User us = new User();
                            string username = UserName.Text;
                            string orignalStr = CryptographyHelper.Md5Encrypt(Password.Text);
                            if (username != null && orignalStr != null)
                            {
                                us.UserName = username;
                                us.LoginName = username;
                                us.LoginPassword = orignalStr;
                                logger.Info("Login UserName is:" + username + " and LoggedInUserName is:" + LoggedInUserName);
                                if (username.Equals(LoggedInUserName))
                                {
                                    var _chklogin = Facade.LoginUser(us);
                                    if (_chklogin != null)
                                    {
                                        //chkval = 1;
                                        result = true;
                                        pnlPrivType.Visible = false;
                                        _chkval = 0;
                                        logger.Info("Authenticate Successfully with LoogedInUser:" + LoggedInUserName);
                                    }
                                    else
                                    {
                                        logger.Info("Authenticate Failed with LoogedInUser:" + LoggedInUserName);
                                        result = false;
                                        labelSDErrormessage.Visible = true;
                                        labelSDErrormessage.Text = "Authentication Failed";
                                    }
                                }
                                else
                                {
                                    logger.Info("Authenticate Failed with LoogedInUser:" + LoggedInUserName);
                                    result = false;
                                    labelSDErrormessage.Visible = true;
                                    labelSDErrormessage.Text = "Authentication Failed";
                                }
                            }
                            else
                            {
                                logger.Info("Authenticate Failed with LoogedInUser:" + LoggedInUserName);
                                result = false;
                                labelSDErrormessage.Visible = true;
                                labelSDErrormessage.Text = "Authentication Failed";
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                if (ex != null)
                {

                    logger.Error("CP Exception while Checking LoginCredentail ,LoggedInUserID is: " + LoggedInUserId + " With User IP Address:" + IPAddress + " and Exception is:" + ex.Message);
                    if (ex.InnerException != null)
                        logger.Error("Inner Exception : " + ex.InnerException.ToString());
                    if (ex.StackTrace != null)
                        logger.Error("Exception details : " + ex.StackTrace.ToString());
                }
                return false;
            }

            return result;
        }

        public static bool AuthenticateADUser(string _domainName, string _domainUser, string _domianPassword)
        {
            logger.Info("In AuthenticateADUser()");
            try
            {
                Forest currentForest = Forest.GetCurrentForest();
                DomainCollection domains = currentForest.Domains;

                using (PrincipalContext pc = new PrincipalContext(ContextType.Domain, _domainName))
                {
                    bool isValid = pc.ValidateCredentials(_domainUser, _domianPassword);

                    if (isValid)
                    {
                        logger.Info("User " + _domainUser + " authenticated with domain " + _domainName);
                        return true;
                    }
                    else
                    {
                        logger.Info("User " + _domainUser + " not authenticated with domain " + _domainName);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                logger.Error("Authentication failed for: " + _domainUser + " " + " with domain " + _domainName + " with error message " + ex.Message);
                return false;
            }
        }


    }
}