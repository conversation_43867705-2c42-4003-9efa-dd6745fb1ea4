﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;


namespace CP.UI.Controls
{
    public partial class SQLNative2008Overview :BaseControl
    {
        public static string CurrentURL = Constants.UrlConstants.Urls.Component.ReplicationConfiguration;

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        private DropDownList _ddlReplicationType = new DropDownList();

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        private void Binddata()
        {
            //var result = Facade.GetSQLNative2008ReplicationByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId,
            //                                              IsParentCompnay);
            var result = Facade.GetSN2008ReplicationByUserIdCompanyIdRoleAndReplicationFlag(LoggedInUserId, LoggedInUserCompanyId, LoggedInUserRole, IsParentCompnay, LoggedInUser.InfraObjectAllFlag);
            lvSQLNative2008Overview.DataSource = result;
            lvSQLNative2008Overview.DataBind();
            lvSQLNative2008Overview.Visible = true;
        }

        private IList<SQLNative2008Replication> GetSQLNative2008List()
        {
            return Facade.GetSQLNative2008ReplicationByCompanyIdAndRole(IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
        }

        public IList<SQLNative2008Replication> GetSQLNative2008List(string searchvalue)
        {
            var replicationlist = GetSQLNative2008List();
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && replicationlist != null && replicationlist.Count> 0)
            {
                var result = (from replication in replicationlist
                              where replication.ReplicationBase.Name.ToLower().Contains(searchvalue.ToLower())
                              select replication).ToList();

                return result;
            }
            return null;
        }

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvSQLNative2008Overview.Items.Clear();
                lvSQLNative2008Overview.DataSource = GetSQLNative2008List(txtsearchvalue.Text);
                lvSQLNative2008Overview.DataBind();
            }
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void LvSQLNative2008OverviewItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                var lblId = lvSQLNative2008Overview.Items[e.ItemIndex].FindControl("ID") as Label;
                var lblName = (lvSQLNative2008Overview.Items[e.ItemIndex].FindControl("Rep_NAME")) as Label;

                if (lblId != null && lblId.Text != null && lblName != null && ValidateRequest("SQLNative2008Overview Delete", UserActionType.ReplicationList))
                {
                    Facade.DeleteSQLNative2008Id(Convert.ToInt32(lblId.Text));
                    ActivityLogger.AddLog(LoggedInUserName, "SQLNative2008UrReplication",
                                          UserActionType.DeleteReplicationComponent,
                                          "TheSQLNative2008Ur Replication component '" + lblName.Text +
                                          "' was deleted from the replication component", LoggedInUserId);

                    ErrorSuccessNotifier.AddSuccessMessage(
                        Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                            "SQLNative2008 Replication Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));


                }
                
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "28");

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "28");
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected string CheckWithGroup(object name, object id)
        {
            string stringtype = string.Empty;
            Group group = Facade.GetGroupByReplicationId(Convert.ToInt32(id));

            if (group != null)
                return stringtype = "Replication " + name + " attaching with Site " + group.Name + " are you sure want to delete ?";
            else
                return stringtype = "Are you sure want to delete " + name + " ?";
        }

        protected void LvSQLNative2008OverviewItemEditing(object sender, ListViewEditEventArgs e)
        {
            var secureUrl = new SecureUrl(CurrentURL);

            var lbl1 = (lvSQLNative2008Overview.Items[e.NewEditIndex].FindControl("Id")) as Label;

            var lblName = (lvSQLNative2008Overview.Items[e.NewEditIndex].FindControl("Rep_NAME")) as Label;

            ActivityLogger.AddLog(LoggedInUserName, "SQLNAtive2008", UserActionType.UpdateReplicationComponent,
                                  "The SQLNAtive2008 Replication component '" + lblName.Text +
                                  "' Opened as Editing Mode", LoggedInUserId);

            if (lbl1 != null && lblName != null && ValidateRequest("SQLNative2008Overview Edit", UserActionType.ReplicationList))
            {
                //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.ReplicationId, lbl1.Text,
                //    Constants.UrlConstants.Params.ReplicationName, lblName.Text, Constants.UrlConstants.Params.ReplicationType, "28");

                secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, lbl1.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, lblName.Text);
                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "28");
                Helper.Url.Redirect(secureUrl);

            }
            if (secureUrl != null)
            {
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void LvSQLNative2008OverviewPreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                Binddata();
            }
        }


        public override void PrepareView()
        {
           
        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["_token"] != null))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
                {
                    ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        protected void LvSQLNative2008Overview_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            ImageButton edit = e.Item.FindControl("ImgEdit") as ImageButton;
            ImageButton delete = e.Item.FindControl("ImgDelete") as ImageButton;

            if (IsUserOperator || IsUserManager || IsUserExecutionUser)
            {
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
            }
        }
    }
}