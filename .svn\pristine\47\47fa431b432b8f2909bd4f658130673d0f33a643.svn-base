﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.UI.Controls
{
    public partial class Sitemenu : BaseControl
    {

        #region Variable


        #endregion

        public override void PrepareView()
        {
            string logoPath = LoggedInUserCompany.CompanyLogoPath != null ? LoggedInUserCompany.CompanyLogoPath.Replace("../", "~/") : LoggedInUserCompany.CompanyLogoPath;
            imgcompanylogo.ImageUrl = logoPath != null ? logoPath : "";
            //imgcompanylogo.ImageUrl = LoggedInUserCompany!=null ? LoggedInUserCompany.LogofilePath : "../Images/bcms-logo.jpg";
            var sites = Facade.GetSitesByCompanyId(LoggedInUserCompanyId, LoggedInUserCompany.IsParent);
            if (sites != null)
            {
                foreach (var site in sites)
                    {
                    //    if (site.Type == SiteType.PRSite)
                    //    {
                    //        lblPRSite.Text = site.Location;
                    //    }
                    //    if (site.Type == SiteType.DRSite)
                    //    {
                    //        lblDRSite.Text = site.Location;
                    //    }

                    if (site.Type == "PRSite")
                    {
                        lblPRSite.Text = site.Location;
                    }
                    if (site.Type == "DRSite")
                     {
                     lblDRSite.Text = site.Location;
                          }


            }
            }
            else
            {
                lblPRSite.Text = "NA";
                lblDRSite.Text = "NA";
            }



        }
    }
}