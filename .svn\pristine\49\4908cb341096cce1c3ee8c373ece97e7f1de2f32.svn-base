﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true"
    CodeBehind="JobManagement.aspx.cs" Inherits="CP.UI.Admin.JobManagement" Title="Continuity Patrol :: Job Management" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script src="../Script/validation.js" type="text/javascript"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $('[id$=btnCreateJobType]').click(function () {

                $('[id$=txtJobTypeName]').val("");
                $('[id$=txtBcmsClassName]').val("");
            });

            $('[id$=ddlGroup]').live("change", function () {

                $('[id$=txtJobTypeName]').val("");
                $('[id$=txtBcmsClassName]').val("");
            });
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });

        function clrMsg() {
            $('[id$=lblJobTypeError]').hide();
        }

        function pageLoad() {

            $('[id$=btnCreateJobType]').click(function () {
                $('[id$=txtJobTypeName]').val("");
                $('[id$=txtBcmsClassName]').val("");
            });

            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
    <style>
        .chosen-select + .chosen-container {
            width: 48% !important;
            opacity: 1 !important;
        }
    </style>
    <script src="../Script/EncryptDecrypt.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel_ProfileOverview" runat="server" UpdateMode="Conditional">
            <ContentTemplate>

                <h3>
                    <img src="../Images/infra-icon.png">
                    Create InfraObject Job</h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">

                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Select Job Category</label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddljobcategory" runat="server" CssClass="selectpicker col-md-6"
                                            data-style="btn-default" AutoPostBack="True" OnSelectedIndexChanged="ddlJob_SelectedIndexChanged">
                                            <asp:ListItem Value="0">- Select Job Category -</asp:ListItem>
                                            <asp:ListItem Value="Infraobject Db">Solution type DB/App</asp:ListItem>
                                            <%-- <asp:ListItem Value="InfraObject Application">InfraObject Application</asp:ListItem>
                                            <asp:ListItem Value="StorageImageID">StorageImageID</asp:ListItem>
                                            <asp:ListItem Value="InfraobjectWithStorageImageID" Enabled="false">InfraObjectWithStorageImageID</asp:ListItem>
                                            <asp:ListItem Value="BusinessServiceId">Business Service Id</asp:ListItem>--%>
                                        </asp:DropDownList>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <asp:Label ID="lblCategoryName" runat="server" Text="Select InfraObject Name" Visible="true" class="col-md-3 control-label "></asp:Label>

                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlGroup" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default"
                                            AutoPostBack="True" OnSelectedIndexChanged="ddlGroup_SelectedIndexChanged">
                                        </asp:DropDownList>
                                        <asp:Button runat="server" ID="HiddenForModal" CssClass="btn btn-primary" Width="20%"
                                            Style="display: none" />
                                        <TK1:ModalPopupExtender ID="ModalPopupExtenderJobType" runat="server" TargetControlID="btnCreateJobType"
                                            RepositionMode="RepositionOnWindowResizeAndScroll" PopupControlID="pnlJobType"
                                            PopupDragHandleControlID="pnlJobType" Drag="false" BackgroundCssClass="bg">
                                        </TK1:ModalPopupExtender>
                                        <asp:Button ID="btnCreateJobType" runat="server" CssClass="btn btn-primary" Width="15%"
                                            Text="Create Job" OnClick="btnCreateJobType_Click" />
                                        <asp:Label ID="lblGroupError" runat="server" Text="Select Group Name" Visible="false"></asp:Label>
                                    </div>
                                </div>
                                <div class="scroll" style="height: auto; max-height: 300px;">

                                    <asp:ListView ID="lvCJ" runat="server" OnSelectedIndexChanged="lvCJ_SelectedIndexChanged"
                                        OnItemEditing="lvCJ_ItemEditing" OnItemCreated="lvCJ_ItemCreated" OnItemDeleting="lvCJ_ItemDeleting"
                                        OnItemUpdating="lvCJ_ItemUpdating" OnItemDataBound="lvCJ_ItemDataBound" OnItemCommand="lvCJ_ItemCommand"
                                        OnPreRender="lvCJ_PreRender">
                                        <LayoutTemplate>
                                            <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white"
                                                style="margin-bottom: 0;">
                                                <thead>
                                                    <tr>
                                                        <th>No.
                                                        </th>
                                                        <th>Job Name
                                                        </th>
                                                        <th>InfraObject Name
                                                        </th>
                                                        <th>Trigger Name
                                                        </th>
                                                        <th>Time Interval
                                                        </th>
                                                        <th>Status
                                                        </th>
                                                        <th>Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td class="th table-check-cell sorting_1">
                                                    <%#Container.DataItemIndex+1 %>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />

                                                    <asp:Label ID="lblJobId" runat="server" Text='<%# Eval("JobId") %>' Visible="false" />
                                                    <asp:Label ID="lblInfraObjectId" runat="server" Text='<%# Eval("InfraObjectId") %>' Visible="false" />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblJobName" runat="server" Visible="false" Text='<%# GetJobName(Eval("JobId")) %>' />


                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>

                                                </td>
                                                <td>
                                                    <asp:Label ID="lblGroupName" runat="server" Text='<%# GetInfraObjectName(Eval("InfraObjectId")) %>' />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />


                                                    <asp:LinkButton ID="lnkBtnStatus" runat="server" CommandName="ChangeStatus" AlternateText="Status"
                                                        ToolTip="Status" CssClass="icon-sync" Visible="true" OnClick="lnkBtnStatus_Click" />

                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                        ConfirmText='<%# "Are you sure want to delete " + GetJobName(Eval("JobId")) + " ? " %>'>
                                                    </TK1:ConfirmButtonExtender>


                                                    <TK1:ConfirmButtonExtender ID="ConfirmJob" runat="server" TargetControlID="lnkBtnStatus"
                                                        ConfirmText='<%# "Are you sure want to Start/Stop Active Job ? " %>'>
                                                    </TK1:ConfirmButtonExtender>
                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <EditItemTemplate>
                                            <tr>
                                                <td>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>
                                                </td>
                                                <td>


                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblGroupName" runat="server" Text='<%# GetInfraObjectName(Eval("InfraObjectId")) %>' />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>

                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                        ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                                </td>
                                            </tr>
                                        </EditItemTemplate>
                                        <InsertItemTemplate>
                                            <tr>
                                                <td></td>
                                                <td>
                                                    <asp:TextBox ID="txtJobName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtGroupName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtTriggerName" runat="server" class="form-control" Width="60px"
                                                        Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Width="60px" Text='<%# Eval("CronTime") %>' />
                                                    <asp:TextBox ID="txtIsEnabled" runat="server" class="form-control" Width="60px" Enabled="false"
                                                        Visible="false"></asp:TextBox>
                                                </td>

                                                <td>
                                                    <asp:TextBox ID="txtStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>


                                                <td>
                                                    <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                        CausesValidation="false" ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                </td>
                                            </tr>
                                        </InsertItemTemplate>
                                    </asp:ListView>


                                    <asp:ListView ID="lstapplicationJob" runat="server" OnItemUpdating="lstapplicationJob_ItemUpdating"
                                        OnItemDataBound="lstapplicationJob_ItemDataBound" OnItemCommand="lstapplicationJob_ItemCommand"
                                        OnPreRender="lstapplicationJob_PreRender">

                                        <LayoutTemplate>
                                            <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                                                <thead>
                                                    <tr>
                                                        <th>No.
                                                        </th>
                                                        <th>
                                                        Job Name
                                                        
                                                        <th>Trigger Name
                                                        </th>
                                                        <th>Time Interval
                                                        </th>
                                                        <th>Status
                                                        </th>

                                                        <th>Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td>
                                                    <%#Container.DataItemIndex+1 %>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />

                                                    <asp:Label ID="lblJobId" runat="server" Text='<%# Eval("JobId") %>' Visible="false" />
                                                    <asp:Label ID="lblInfraObjectId" runat="server" Text='<%# Eval("InfraObjectId") %>' Visible="false" />


                                                </td>
                                                <td>
                                                    <asp:Label ID="lblJobName" runat="server" Visible="false" Text='<%# GetJobName(Eval("JobId")) %>' />

                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>

                                                </td>

                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />

                                                </td>


                                                <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />



                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                        ConfirmText='<%# "Are you sure want to delete " + GetJobName(Eval("JobId")) + " ? " %>'>
                                                    </TK1:ConfirmButtonExtender>


                                                    <asp:LinkButton ID="lnkBtnStatus" runat="server" CommandName="ChangeStatus" AlternateText="Status"
                                                        ToolTip="Status" CssClass="icon-sync" Visible="true" OnClick="lnkBtnStatus_Click" />


                                                    <TK1:ConfirmButtonExtender ID="ConfirmJob" runat="server" TargetControlID="lnkBtnStatus"
                                                        ConfirmText='<%# "Are you sure want to Start/Stop Active Job ? " %>'>
                                                    </TK1:ConfirmButtonExtender>

                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <EditItemTemplate>
                                            <tr>
                                                <td>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>

                                                </td>

                                                <td>


                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>

                                                </td>

                                                </td>
                                                <td>

                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />

                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />

                                                </td>
                                                <td>
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>

                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>

                                                    <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                        ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                                </td>
                                            </tr>
                                        </EditItemTemplate>
                                        <InsertItemTemplate>
                                            <tr>
                                                <td></td>

                                                <td>
                                                    <asp:TextBox ID="txtJobName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>

                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtGroupName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>

                                                </td>
                                                <td></td>
                                                <td>
                                                    <asp:TextBox ID="txtTriggerName" runat="server" class="form-control" Width="60px"
                                                        Enabled="false"></asp:TextBox>

                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Width="60px" Text='<%# Eval("CronTime") %>' />

                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtIsEnabled" runat="server" class="form-control" Width="60px" Enabled="false"
                                                        Visible="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                        ConfirmText='<%# "Are you sure want to delete "  + Eval("JobId") + " ? "  %>'>
                                                    </TK1:ConfirmButtonExtender>
                                                </td>
                                            </tr>
                                        </InsertItemTemplate>
                                    </asp:ListView>


                                    <asp:ListView ID="lstdynamicjob" runat="server"
                                        OnItemDataBound="lstdynamicjob_ItemDataBound" OnItemCommand="lstdynamicjob_ItemCommand"
                                        OnPreRender="lstdynamicjob_PreRender">

                                        <LayoutTemplate>
                                            <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                                                <thead>
                                                    <tr>
                                                        <th>No.
                                                        </th>
                                                        <th>
                                                        Job Name
                                                        
                                                        <th>Trigger Name
                                                        </th>
                                                        <th>Time Interval
                                                        </th>
                                                        <th>Status
                                                        </th>

                                                        <th>Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td>
                                                    <%#Container.DataItemIndex+1 %>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />

                                                    <asp:Label ID="lblJobId" runat="server" Text='<%# Eval("JobId") %>' Visible="false" />



                                                </td>
                                                <td>
                                                    <asp:Label ID="lblJobName" runat="server" Visible="false" Text='<%# GetJobName(Eval("JobId")) %>' />

                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>

                                                </td>

                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />

                                                </td>


                                                <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />



                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                        ConfirmText='<%# "Are you sure want to delete " + GetJobName(Eval("JobId")) + " ? " %>'>
                                                    </TK1:ConfirmButtonExtender>


                                                    <asp:LinkButton ID="lnkBtnStatus" runat="server" CommandName="ChangeStatus" AlternateText="Status"
                                                        ToolTip="Status" CssClass="icon-sync" Visible="false" OnClick="lnkBtnStatus_Click" />


                                                    <TK1:ConfirmButtonExtender ID="ConfirmJob" runat="server" TargetControlID="lnkBtnStatus"
                                                        ConfirmText='<%# "Are you sure want to Start/Stop Active Job ? " %>'>
                                                    </TK1:ConfirmButtonExtender>

                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <EditItemTemplate>
                                            <tr>
                                                <td>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>

                                                </td>

                                                <td>


                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>

                                                </td>

                                                </td>
                                                <td>

                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />

                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />

                                                </td>
                                                <td>
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>

                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>

                                                    <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                        ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                                </td>
                                            </tr>
                                        </EditItemTemplate>
                                        <InsertItemTemplate>
                                            <tr>
                                                <td></td>

                                                <td>
                                                    <asp:TextBox ID="txtJobName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>

                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtGroupName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>

                                                </td>
                                                <td></td>
                                                <td>
                                                    <asp:TextBox ID="txtTriggerName" runat="server" class="form-control" Width="60px"
                                                        Enabled="false"></asp:TextBox>

                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Width="60px" Text='<%# Eval("CronTime") %>' />

                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtIsEnabled" runat="server" class="form-control" Width="60px" Enabled="false"
                                                        Visible="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />
                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                        ConfirmText='<%# "Are you sure want to delete "  + Eval("JobId") + " ? "  %>'>
                                                    </TK1:ConfirmButtonExtender>
                                                </td>
                                            </tr>
                                        </InsertItemTemplate>
                                    </asp:ListView>

                                    <asp:ListView ID="lstStorage" runat="server" OnItemUpdating="lstStorage_ItemUpdating"
                                        OnItemDataBound="lstStorage_ItemDataBound" OnItemCommand="lstStorage_ItemCommand"
                                        OnPreRender="lstStorage_PreRender">

                                        <LayoutTemplate>
                                            <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                                                <thead>
                                                    <tr>
                                                        <th>No.
                                                        </th>

                                                        <th>Job Name
                                                        </th>
                                                        <th>StorageImageId
                                                        </th>
                                                        <th>Trigger Name
                                                        </th>
                                                        <th>Time Interval
                                                        </th>
                                                        <th>Status
                                                        </th>
                                                        <th>Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td class="th table-check-cell sorting_1">
                                                    <%#Container.DataItemIndex+1 %>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />

                                                    <asp:Label ID="lblJobId" runat="server" Text='<%# Eval("JobId") %>' Visible="false" />
                                                    <asp:Label ID="lblInfraObjectId" runat="server" Text='<%# Eval("InfraObjectId") %>' Visible="false" />

                                                </td>
                                                <td>

                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>

                                                </td>
                                                <td>
                                                    <asp:Label ID="lblStorageImageName" runat="server" Text='<%#Eval("StorageImageId")%>' />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>

                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>


                                                <td>
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" />


                                                    <asp:LinkButton ID="lnkBtnStatus" runat="server" CommandName="ChangeStatus" AlternateText="Status"
                                                        ToolTip="Status" CssClass="icon-sync" Visible="true" OnClick="lnkBtnStatus_Click" />


                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                        ConfirmText='<%# "Are you sure want to delete"  + Eval("JobId") + " ? " %>'>
                                                    </TK1:ConfirmButtonExtender>



                                                    <TK1:ConfirmButtonExtender ID="ConfirmJob" runat="server" TargetControlID="lnkBtnStatus"
                                                        ConfirmText='<%# "Are you sure want to Start/Stop Active Job ? " %>'>
                                                    </TK1:ConfirmButtonExtender>


                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <EditItemTemplate>
                                            <tr>
                                                <td>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>

                                                </td>

                                                <td>

                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>



                                                </td>

                                                <td>

                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>
                                                <td>

                                                    <td>
                                                        <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                    </td>


                                                    <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                        ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                                </td>
                                            </tr>
                                        </EditItemTemplate>
                                        <InsertItemTemplate>
                                            <tr>
                                                <td></td>

                                                <td>
                                                    <asp:TextBox ID="txtJobName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>

                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtGroupName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>

                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtTriggerName" runat="server" class="form-control" Width="60px"
                                                        Enabled="false"></asp:TextBox>

                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Width="60px" Text='<%# Eval("CronTime") %>' />

                                                    <asp:TextBox ID="txtIsEnabled" runat="server" class="form-control" Width="60px" Enabled="false"
                                                        Visible="false"></asp:TextBox>
                                                </td>

                                                <td>
                                                    <asp:TextBox ID="txtStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>


                                                <td>

                                                    <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                        CausesValidation="false" ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                </td>
                                            </tr>
                                        </InsertItemTemplate>
                                    </asp:ListView>



                                    <asp:ListView ID="lvInfraWithStorageImage" runat="server" OnSelectedIndexChanged="lvInfraWithStorageImage_SelectedIndexChanged"
                                        OnItemEditing="lvInfraWithStorageImage_ItemEditing" OnItemCreated="lvInfraWithStorageImage_ItemCreated" OnItemDeleting="lvInfraWithStorageImage_ItemDeleting"
                                        OnItemUpdating="lvInfraWithStorageImage_ItemUpdating" OnItemDataBound="lvInfraWithStorageImage_ItemDataBound" OnItemCommand="lvInfraWithStorageImage_ItemCommand"
                                        OnPreRender="lvInfraWithStorageImage_PreRender">
                                        <LayoutTemplate>
                                            <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white"
                                                style="margin-bottom: 0;">
                                                <thead>
                                                    <tr>
                                                        <th>No.
                                                        </th>
                                                        <th>Job Name
                                                        </th>
                                                        <th>StorageImage Name
                                                        </th>
                                                        <th>Trigger Name
                                                        </th>
                                                        <th>Time Interval
                                                        </th>
                                                        <th>Status
                                                        </th>
                                                        <th>Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td class="th table-check-cell sorting_1">
                                                    <%#Container.DataItemIndex+1 %>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />

                                                    <asp:Label ID="lblJobId" runat="server" Text='<%# Eval("JobId") %>' Visible="false" />
                                                    <asp:Label ID="lblInfraObjectId" runat="server" Text='<%# Eval("InfraObjectId") %>' Visible="false" />
                                                </td>
                                                <td>

                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>

                                                </td>
                                                <td>

                                                    <asp:Label ID="lblInfraWithStorageImageName" runat="server" Text='<%# GetInfraWithStorageImageName(Eval("InfraWithStorageImageId")) %>' />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />


                                                    <asp:LinkButton ID="lnkBtnStatus" runat="server" CommandName="ChangeStatus" AlternateText="Status"
                                                        ToolTip="Status" CssClass="icon-sync" Visible="true" OnClick="lnkBtnStatus_Click" />

                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                        ConfirmText='<%# "Are you sure want to delete " + Eval("JobId") + " ? " %>'>
                                                    </TK1:ConfirmButtonExtender>


                                                    <TK1:ConfirmButtonExtender ID="ConfirmJob" runat="server" TargetControlID="lnkBtnStatus"
                                                        ConfirmText='<%# "Are you sure want to Start/Stop Active Job ? " %>'>
                                                    </TK1:ConfirmButtonExtender>
                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <EditItemTemplate>
                                            <tr>
                                                <td>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>
                                                </td>
                                                <td>


                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>
                                                </td>
                                                <td>

                                                    <asp:Label ID="lblInfraWithStorageImageName" runat="server" Text='<%# GetInfraWithStorageImageName(Eval("InfraWithStorageImageId")) %>' />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>

                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                        ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                                </td>
                                            </tr>
                                        </EditItemTemplate>
                                        <InsertItemTemplate>
                                            <tr>
                                                <td></td>
                                                <td>
                                                    <asp:TextBox ID="txtJobName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>

                                                    <asp:TextBox ID="txtInfraWithStorageImageName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtTriggerName" runat="server" class="form-control" Width="60px"
                                                        Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Width="60px" Text='<%# Eval("CronTime") %>' />
                                                    <asp:TextBox ID="txtIsEnabled" runat="server" class="form-control" Width="60px" Enabled="false"
                                                        Visible="false"></asp:TextBox>
                                                </td>

                                                <td>
                                                    <asp:TextBox ID="txtStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>


                                                <td>
                                                    <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                        CausesValidation="false" ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                </td>
                                            </tr>
                                        </InsertItemTemplate>
                                    </asp:ListView>



                                    <asp:ListView ID="lvBs" runat="server" OnSelectedIndexChanged="lvBs_SelectedIndexChanged"
                                        OnItemEditing="lvBs_ItemEditing" OnItemCreated="lvBs_ItemCreated" OnItemDeleting="lvBs_ItemDeleting"
                                        OnItemUpdating="lvBs_ItemUpdating" OnItemDataBound="lvBs_ItemDataBound" OnItemCommand="lvBs_ItemCommand"
                                        OnPreRender="lvBs_PreRender">
                                        <LayoutTemplate>
                                            <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white"
                                                style="margin-bottom: 0;">
                                                <thead>
                                                    <tr>
                                                        <th>No.
                                                        </th>
                                                        <th>Job Name
                                                        </th>
                                                        <th>Business Service Name
                                                        </th>
                                                        <th>Trigger Name
                                                        </th>
                                                        <th>Time Interval
                                                        </th>
                                                        <th>Status
                                                        </th>
                                                        <th>Action
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </LayoutTemplate>
                                        <ItemTemplate>
                                            <tr>
                                                <td class="th table-check-cell sorting_1">
                                                    <%#Container.DataItemIndex+1 %>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" />

                                                    <asp:Label ID="lblJobId" runat="server" Text='<%# Eval("JobId") %>' Visible="false" />
                                                    <asp:Label ID="lblInfraObjectId" runat="server" Text='<%# Eval("InfraObjectId") %>' Visible="false" />
                                                </td>
                                                <td>

                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>

                                                </td>
                                                <td>

                                                    <asp:Label ID="lblBusinessService" runat="server" Text='<%# GetBusinessServiceName(Eval("BusinessServiceId")) %>' />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>
                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                                                        ToolTip="Delete" ImageUrl="../Images/icons/cross-circle.png" Visible="true" />


                                                    <asp:LinkButton ID="lnkBtnStatus" runat="server" CommandName="ChangeStatus" AlternateText="Status"
                                                        ToolTip="Status" CssClass="icon-sync" Visible="true" OnClick="lnkBtnStatus_Click" />

                                                    <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                        ConfirmText='<%# "Are you sure want to delete " + Eval("JobId") + " ? " %>'>
                                                    </TK1:ConfirmButtonExtender>


                                                    <TK1:ConfirmButtonExtender ID="ConfirmJob" runat="server" TargetControlID="lnkBtnStatus"
                                                        ConfirmText='<%# "Are you sure want to Start/Stop Active Job ? " %>'>
                                                    </TK1:ConfirmButtonExtender>
                                                </td>
                                            </tr>
                                        </ItemTemplate>
                                        <EditItemTemplate>
                                            <tr>
                                                <td>
                                                    <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false"></asp:Label>
                                                </td>
                                                <td>


                                                    <asp:LinkButton ID="lnkBtnJobName" runat="server" CommandName="JobDetails" OnClick="lnkBtnJobName_Click" Text='<%# GetJobName(Eval("JobId")) %>'>LinkButton</asp:LinkButton>
                                                </td>
                                                <td>

                                                    <asp:Label ID="lblBusinessService" runat="server" Text='<%# GetBusinessServiceName(Eval("BusinessServiceId")) %>' />

                                                </td>
                                                <td>
                                                    <asp:Label ID="lblTriggerName" runat="server" Text='<%# Eval("TriggerName") %>' />
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Text='<%# Eval("CronTime") %>' />
                                                    <asp:Label ID="lblIsEnabled" runat="server" Text='<%# Eval("IsEnabled") %>' Visible="false" />
                                                </td>

                                                <td>
                                                    <asp:Label ID="lblStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>

                                                <td>
                                                    <asp:ImageButton ID="ImgCancel" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                        ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                                </td>
                                            </tr>
                                        </EditItemTemplate>
                                        <InsertItemTemplate>
                                            <tr>
                                                <td></td>
                                                <td>
                                                    <asp:TextBox ID="txtJobName" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>


                                                    <asp:TextBox ID="txtBusinessService" runat="server" class="form-control" Width="60px" Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:TextBox ID="txtTriggerName" runat="server" class="form-control" Width="60px"
                                                        Enabled="false"></asp:TextBox>
                                                </td>
                                                <td>
                                                    <asp:ImageButton ID="imgCronPopUp" runat="server" CommandName="CronPopUp" AlternateText="Create Cron"
                                                        ToolTip="Configure Time Interval" ImageUrl="~/Images/icons/clock.png" />
                                                    <asp:Label ID="lblCronExp" runat="server" Width="60px" Text='<%# Eval("CronTime") %>' />
                                                    <asp:TextBox ID="txtIsEnabled" runat="server" class="form-control" Width="60px" Enabled="false"
                                                        Visible="false"></asp:TextBox>
                                                </td>

                                                <td>
                                                    <asp:TextBox ID="txtStatus" runat="server" CssClass="text-success" Text='<%# Eval("JobStatus") %>' />
                                                </td>


                                                <td>
                                                    <asp:ImageButton ID="imgUpdate" runat="server" CommandName="Insert" AlternateText="Insert"
                                                        CausesValidation="false" ToolTip="Insert" ImageUrl="../images/icons/plus-circle.png" />
                                                </td>
                                            </tr>
                                        </InsertItemTemplate>
                                    </asp:ListView>




                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <asp:Panel ID="modelbg" runat="server" Visible="false" class="bg" Style="position: fixed; left: 0px; top: 0px; z-index: 1050; width: 1349px; height: 100%;">
                </asp:Panel>




                <asp:Panel ID="pnlCronPopUp" Visible="false" runat="server" Style="margin-top: 100px;">
                    <div id="Cronmodal" class="modal " style="display: block;">
                        <div class="modal-dialog" style="width: 750px">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <asp:LinkButton ID="LinkButton1" runat="server" CommandName="Close" class="close"
                                        OnClick="LinkButton1_Click">×</asp:LinkButton>
                                    <h3 class="modal-title">Configure Time Interval</h3>
                                </div>

                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <%--<asp:Label ID="lblSuccecMessage" runat="server" CssClass="text-success" Text="" Visible="false"></asp:Label>--%>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Time Interval</label>
                                                <div class="col-md-9">
                                                    <asp:RadioButtonList ID="rblTimeInterval" runat="server" RepeatDirection="Horizontal"
                                                        OnSelectedIndexChanged="rblTimeInterval_SelectedIndexChanged" AutoPostBack="true">
                                                        <asp:ListItem>Minutes</asp:ListItem>
                                                        <asp:ListItem>Hourly</asp:ListItem>
                                                        <asp:ListItem>Daily</asp:ListItem>

                                                    </asp:RadioButtonList>
                                                    <asp:TextBox ID="txtCE" runat="server" class="form-control" Width="0px" Visible="false"></asp:TextBox>
                                                    <asp:TextBox ID="txtCETime" runat="server" class="form-control" Width="0px" Visible="false"></asp:TextBox>
                                                </div>
                                            </div>
                                            <asp:Panel ID="Panel_Minuite" runat="server" CssClass="form-group" Visible="false">
                                                <label class="col-md-3 control-label">
                                                    Set Time
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:Label ID="lblEveryMinuite" runat="server" Text="Every"></asp:Label>
                                                    <asp:TextBox ID="txteveryminuite" runat="server" class="form-control" MaxLength="4"
                                                        CausesValidation="True" Text="0" Width="15%"></asp:TextBox>
                                                    <asp:Label ID="lblminuites" runat="server" Text="Minute(s)"></asp:Label>

                                                    <div class="text-left">
                                                        <asp:RegularExpressionValidator ID="REVEveryminuite" runat="server" CssClass="error"
                                                            ControlToValidate="txteveryminuite" ErrorMessage="Please Enter only Numbers"
                                                            Display="Dynamic" ValidationExpression="\d+" ValidationGroup="ValidationTimeInterval"></asp:RegularExpressionValidator>
                                                        <asp:RequiredFieldValidator ID="rfvtxteveryminuite" runat="server" Enabled="true"
                                                            ControlToValidate="txteveryminuite" Display="Dynamic" CssClass="error"
                                                            ErrorMessage="Field cant be empty,Please enter minutes" ValidationGroup="ValidationTimeInterval"></asp:RequiredFieldValidator>
                                                        <asp:CompareValidator ID="CompareValidator1" runat="server" ControlToValidate="txteveryminuite"
                                                            CssClass="error" ErrorMessage="Minutes Must be &gt; 0" Operator="GreaterThan"
                                                            Display="Dynamic" Type="Integer" ValueToCompare="0" ValidationGroup="ValidationTimeInterval" />
                                                        &nbsp;
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <asp:Panel ID="Panel_Hourly" runat="server" CssClass="form-group" Visible="false">
                                                <label class="col-md-3 control-label">
                                                    Set Time
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:Label ID="lblEveryHourly" runat="server" Text="Every"></asp:Label>
                                                    <asp:TextBox ID="txteveryhour" runat="server" Width="15%" CssClass="form-control"
                                                        MaxLength="4" Text="0"></asp:TextBox>
                                                    <asp:Label ID="lblhours" runat="server" Text="Hour(s)" CssClass="margin-right"></asp:Label>
                                                    <asp:TextBox ID="txteveryhourlyminuite" runat="server" Width="15%" CssClass="form-control"
                                                        MaxLength="4" Text="0"></asp:TextBox>
                                                    <asp:Label ID="lblhourlyminuites" runat="server" Text="Minute(s)" OnLoad="lblhourlyminuites_Load"></asp:Label>

                                                    <div class="text-left">
                                                        <asp:CompareValidator ID="CompareValidator2" runat="server" ControlToValidate="txteveryhour"
                                                            CssClass="error" ErrorMessage="Hours Must be &gt; 0" Operator="GreaterThan" Type="Integer" Display="Dynamic"
                                                            ValueToCompare="0" ValidationGroup="ValidationTimeInterval" />

                                                        <asp:RangeValidator ID="rveveryHr" runat="server" ControlToValidate="txteveryhour" Display="Dynamic"
                                                            MaximumValue="24" MinimumValue="0" Type="Integer" CssClass="error" ErrorMessage="Hours should not be greater than 24"
                                                            ValidationGroup="ValidationTimeInterval" />


                                                        <asp:RangeValidator ID="rvHrlyminutes" runat="server" ControlToValidate="txteveryhourlyminuite" Display="Dynamic"
                                                            MaximumValue="59" MinimumValue="0" Type="Integer" CssClass="error" ErrorMessage="Minutes should not be greater than 59"
                                                            ValidationGroup="ValidationTimeInterval" />

                                                        <asp:RequiredFieldValidator ID="rfvtxteveryhourlyminuite" runat="server" Enabled="true"
                                                            ControlToValidate="txteveryhourlyminuite" Display="Dynamic" ForeColor="Red"
                                                            ValidationGroup="ValidationTimeInterval" CssClass="error" ErrorMessage="Field cant be empty,Must be between 1 to 60"></asp:RequiredFieldValidator>

                                                        <asp:RegularExpressionValidator ID="revtxteveryhourlyminuite" runat="server" CssClass="error"
                                                            ControlToValidate="txteveryhourlyminuite" ErrorMessage="Please Enter only Numbers" Display="Dynamic"
                                                            ValidationExpression="\d+" SetFocusOnError="True" ValidationGroup="ValidationTimeInterval"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <asp:Panel ID="Panel_Daily" runat="server" CssClass="form-group" Visible="false">
                                                <label class="col-md-3 control-label">
                                                    Set Time
                                                </label>
                                                <div class="col-md-9">

                                                    <asp:Label ID="lblEverydaily" runat="server" class="pull-left form-control-static">Every &nbsp; </asp:Label>
                                                    <asp:TextBox ID="txteverydaily" runat="server" Width="15%" class="form-control pull-left"
                                                        Text="0"></asp:TextBox>
                                                    <asp:Label ID="lbldays" runat="server" Text="Day(s) " CssClass="padding pull-left"></asp:Label>

                                                    <asp:Label ID="lblstartTime" runat="server" Text="StartTime " class="pull-left padding"></asp:Label>
                                                    <asp:DropDownList ID="ddlhours" runat="server" CssClass="selectpicker col-md-2" data-style="btn-default">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:DropDownList ID="ddlminuites" runat="server" CssClass="selectpicker col-md-2"
                                                        data-style="btn-default">
                                                        <asp:ListItem>00</asp:ListItem>
                                                        <asp:ListItem>01</asp:ListItem>
                                                        <asp:ListItem>02</asp:ListItem>
                                                        <asp:ListItem>03</asp:ListItem>
                                                        <asp:ListItem>04</asp:ListItem>
                                                        <asp:ListItem>05</asp:ListItem>
                                                        <asp:ListItem>06</asp:ListItem>
                                                        <asp:ListItem>07</asp:ListItem>
                                                        <asp:ListItem>08</asp:ListItem>
                                                        <asp:ListItem>09</asp:ListItem>
                                                        <asp:ListItem>10</asp:ListItem>
                                                        <asp:ListItem>11</asp:ListItem>
                                                        <asp:ListItem>12</asp:ListItem>
                                                        <asp:ListItem>13</asp:ListItem>
                                                        <asp:ListItem>14</asp:ListItem>
                                                        <asp:ListItem>15</asp:ListItem>
                                                        <asp:ListItem>16</asp:ListItem>
                                                        <asp:ListItem>17</asp:ListItem>
                                                        <asp:ListItem>18</asp:ListItem>
                                                        <asp:ListItem>19</asp:ListItem>
                                                        <asp:ListItem>20</asp:ListItem>
                                                        <asp:ListItem>21</asp:ListItem>
                                                        <asp:ListItem>22</asp:ListItem>
                                                        <asp:ListItem>23</asp:ListItem>
                                                        <asp:ListItem>24</asp:ListItem>
                                                        <asp:ListItem>25</asp:ListItem>
                                                        <asp:ListItem>26</asp:ListItem>
                                                        <asp:ListItem>27</asp:ListItem>
                                                        <asp:ListItem>28</asp:ListItem>
                                                        <asp:ListItem>29</asp:ListItem>
                                                        <asp:ListItem>30</asp:ListItem>
                                                        <asp:ListItem>31</asp:ListItem>
                                                        <asp:ListItem>32</asp:ListItem>
                                                        <asp:ListItem>33</asp:ListItem>
                                                        <asp:ListItem>34</asp:ListItem>
                                                        <asp:ListItem>35</asp:ListItem>
                                                        <asp:ListItem>36</asp:ListItem>
                                                        <asp:ListItem>37</asp:ListItem>
                                                        <asp:ListItem>38</asp:ListItem>
                                                        <asp:ListItem>39</asp:ListItem>
                                                        <asp:ListItem>40</asp:ListItem>
                                                        <asp:ListItem>41</asp:ListItem>
                                                        <asp:ListItem>42</asp:ListItem>
                                                        <asp:ListItem>43</asp:ListItem>
                                                        <asp:ListItem>44</asp:ListItem>
                                                        <asp:ListItem>45</asp:ListItem>
                                                        <asp:ListItem>46</asp:ListItem>
                                                        <asp:ListItem>47</asp:ListItem>
                                                        <asp:ListItem>48</asp:ListItem>
                                                        <asp:ListItem>49</asp:ListItem>
                                                        <asp:ListItem>50</asp:ListItem>
                                                        <asp:ListItem>51</asp:ListItem>
                                                        <asp:ListItem>52</asp:ListItem>
                                                        <asp:ListItem>53</asp:ListItem>
                                                        <asp:ListItem>54</asp:ListItem>
                                                        <asp:ListItem>55</asp:ListItem>
                                                        <asp:ListItem>56</asp:ListItem>
                                                        <asp:ListItem>57</asp:ListItem>
                                                        <asp:ListItem>58</asp:ListItem>
                                                        <asp:ListItem>59</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <div class="clearfix"></div>

                                                    <div class="text-left ">
                                                        <asp:RegularExpressionValidator ID="revdays" runat="server" CssClass="error" ControlToValidate="txteverydaily"
                                                            ErrorMessage="Please Enter only Numbers" ValidationExpression="\d+" Display="Dynamic" ValidationGroup="ValidationTimeInterval"></asp:RegularExpressionValidator>
                                                        <asp:RequiredFieldValidator ID="rfveverydaily" runat="server" ControlToValidate="txteverydaily"
                                                            CssClass="error" Display="Dynamic" Enabled="true" ErrorMessage="Please Enter Days"
                                                            ForeColor="Red" ValidationGroup="ValidationTimeInterval"></asp:RequiredFieldValidator>
                                                        <asp:CompareValidator ID="CompareValidator4" runat="server" ControlToValidate="txteverydaily"
                                                            CssClass="error" ErrorMessage="Days should be greater than 0(Zero)" Operator="GreaterThan"
                                                            Display="Dynamic" Type="Integer" ValueToCompare="0" ValidationGroup="ValidationTimeInterval" />
                                                        &nbsp;
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <asp:Button ID="btnCreateCron" runat="server" CssClass="btn btn-primary" Width="20%"
                                        OnClick="btnCreateCron_Click" Text="Create" ValidationGroup="ValidationTimeInterval"
                                        Visible="true" />
                                    <asp:Button ID="btnCancelCron" CssClass="btn btn-default" Width="20%" runat="server"
                                        Text="Cancel" OnClick="btnCancelCron_Click" />
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:Panel ID="pnlJobType" runat="server" Style="margin: auto; display: none">
                    <div class="modal" style="display: block;">
                        <div class="modal-dialog" style="width: 700px;">
                            <div class="modal-content  widget-body-white">
                                <div class="modal-header">
                                    <asp:LinkButton ID="lnkbtnClose" class="close" runat="server" CommandName="Close"> ×</asp:LinkButton>
                                    <h3 class="modal-title">Create Type</h3>
                                </div>
                                <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <Triggers>
                                        <asp:AsyncPostBackTrigger ControlID="btnCreateJobType" EventName="Click" />
                                    </Triggers>
                                    <ContentTemplate>

                                        <object data="CreateGroupJobType.aspx" type="text/html" width="679" height="420" style="overflow: hidden"></object>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                            </div>
                        </div>
                    </div>
                </asp:Panel>

                <asp:Panel ID="pnlJobDetails" Visible="false" runat="server">
                    <div id="Div1" class="modal " style="display: block;">
                        <div class="modal-dialog" style="width: 565px; margin-top: 100px;">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <asp:LinkButton ID="LinkButton2" runat="server" CommandName="Close" class="close"
                                        OnClick="LinkButton2_Click">×</asp:LinkButton>
                                    <h3 class="modal-title">Job Details</h3>
                                </div>


                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col-md-12 form-horizontal uniformjs">
                                            <table class="table table-striped table-bordered table-condensed" width="100%">

                                                <tbody>
                                                    <tr>
                                                        <td>Job Name</td>
                                                        <td>
                                                            <asp:Label ID="plblJob" runat="server" /></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Trigger Name</td>
                                                        <td>
                                                            <asp:Label ID="plblTriggerName" runat="server" /></td>
                                                    </tr>
                                                    <tr>
                                                        <td>Cron Time</td>
                                                        <td>
                                                            <asp:Label ID="plblCronTime" runat="server" /></td>
                                                    </tr>

                                                    <tr>
                                                        <td>Job Status</td>
                                                        <td>
                                                            <asp:Label ID="plblJobStatus" runat="server" /></td>
                                                    </tr>


                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <asp:Button ID="Button1" runat="server" CssClass="btn btn-primary" Width="20%"
                                        OnClick="btnCreateCron_Click" Text="Create" ValidationGroup="ValidationTimeInterval"
                                        Visible="false" />
                                    <asp:Button ID="Button2" CssClass="btn btn-default" Width="20%" runat="server"
                                        Text="Cancel" OnClick="btnCancelCron_Click" Visible="false" />
                                </div>
                            </div>
                        </div>
                    </div>
                </asp:Panel>









                <div class="form-actions row">
                    <div class="col-md-9 col-md-push-3">
                        <asp:Button ID="btnCreateJob" CssClass="btn btn-primary" Width="20%" runat="server"
                            Text="Create Group Job" Visible="false" />
                    </div>
                </div>
                </div>
            </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
