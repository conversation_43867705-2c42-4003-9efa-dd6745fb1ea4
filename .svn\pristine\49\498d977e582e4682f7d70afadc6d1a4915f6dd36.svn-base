﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.Serialization.Formatters.Binary;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.UI.WebControls;
using System.Xml;
using System.Xml.Serialization;
using Telerik.Web.UI;
using System.Globalization;

namespace CP.Helper
{
    public static class DataTimeExtension
    {
        public static string Ago(this DateTime target)
        {
            var result = new StringBuilder();
            TimeSpan diff = (DateTime.Now - target.ToLocalTime());

            if (diff.Days > 0)
            {
                result.AppendFormat("{0} days", diff.Days);
            }

            if (diff.Hours > 0)
            {
                if (result.Length > 0)
                {
                    result.Append(", ");
                }

                result.AppendFormat("{0} hours", diff.Hours);
            }

            if (diff.Minutes > 0)
            {
                if (result.Length > 0)
                {
                    result.Append(", ");
                }

                result.AppendFormat("{0} minutes", diff.Minutes);
            }

            if (result.Length == 0)
            {
                result.Append("few moments");
            }

            return result.ToString();
        }

        public static string ToValidShortDateString(this DateTime value)
        {
            return IsValidDate(value) ? value.ToString("MM/dd/yyyy") : string.Empty;
        }

        public static string ToValidShortTimeString(this DateTime value)
        {
            return IsValidDate(value) ? value.ToString("hh:mm tt") : string.Empty;
        }

        public static string ToValidShortDateString(this DateTime value, string format)
        {
            if (IsValidDate(value))
            {
                if (format.IsNotNullOrEmpty())
                {
                    return value.ToString(format);
                }
                return value.ToShortDateString();
            }
            return string.Empty;
        }

        public static string ToValidDateString(this DateTime value)
        {
            return IsValidDate(value) ? value.ToString() : string.Empty;
        }

        public static bool IsValidDate(this DateTime value)
        {
            if ((value == DateTime.MinValue) || (value == DateTime.MaxValue) || (value <= DateTime.Parse("01/01/1754")))
            {
                return false;
            }

            return true;
        }

        public static DateTime CompareAndGetLatest(this DateTime value, DateTime compareValue)
        {
            if (compareValue != DateTime.MinValue)
            {
                return compareValue;
            }
            return value;
        }

        public static DateTime CompareAndGetOld(this DateTime value, DateTime compareValue)
        {
            if (value != DateTime.MinValue)
            {
                return value;
            }
            return compareValue;
        }
    }

    public static class HashTableExtension
    {
        public static object GetByKey(this Hashtable value, object key)
        {
            try
            {
                return value[key];
            }
            catch
            {
                return null;
            }
        }
    }

    public static class ObjectExtension
    {
        public static int ToInteger(this object value, bool isNullToZero)
        {
            try
            {
                return Convert.ToInt32(value);
            }
            catch
            {
                return isNullToZero ? 0 : Int32.MinValue;
            }
        }

        public static string ToString(this object value, bool isNullToEmpty)
        {
            if (value == null)
            {
                value = string.Empty;
            }
            return value.ToString();
        }

        public static byte[] ToSerialized(this object value)
        {
            var memoryStream = new MemoryStream();
            var binaryFormatter = new BinaryFormatter();
            binaryFormatter.Serialize(memoryStream, value);
            return memoryStream.ToArray();
        }

        public static T ToDeSerialized<T>(this byte[] value)
        {
            var memoryStream = new MemoryStream(value);
            var binaryFormatter = new BinaryFormatter();
            return (T)binaryFormatter.Deserialize(memoryStream);
        }

        public static T GetData<T>(this IDataReader reader, int fieldNo)
        {
            object data = null;
            if (reader.IsDBNull(fieldNo))
            {
                if (typeof(T) == typeof(int)
                    || typeof(T) == typeof(decimal)
                    || typeof(T) == typeof(double))
                {
                    data = 0;
                }
                else if (typeof(T) == typeof(string))
                {
                    data = string.Empty;
                }
                else if (typeof(T) == typeof(DateTime))
                {
                    data = DateTime.MinValue;
                }
                else if (typeof(T) == typeof(bool))
                {
                    data = false;
                }
                else if (typeof(T) == typeof(Guid))
                {
                    data = Guid.Empty;
                }
            }
            else
            {
                data = reader.GetValue(fieldNo);
                if (typeof(T) == typeof(int))
                {
                    data = Convert.ToInt32(data);
                }
                else if (typeof(T) == typeof(bool))
                {
                    char value = Convert.ToChar(data);
                    data = value == '1';
                }
            }
            return (T)data;
        }

        public static bool IsValidValue(this object value)
        {
            if (value != null)
            {
                if (value is string && value.ToString().IsNullOrEmpty())
                {
                    return false;
                }
                if (value is int && Convert.ToInt32(value) == 0)
                {
                    return false;
                }
                if (value is decimal && Convert.ToDecimal(value) == 0)
                {
                    return false;
                }
                if (value is DateTime && Convert.ToDateTime(value) == DateTime.MinValue)
                {
                    return false;
                }
                if (value is bool && Convert.ToBoolean(value) == false)
                {
                    return false;
                }
                return true;
            }
            return false;
        }

        public static void Add(this StringDictionary obj, string key, object value, bool checkValue)
        {
            if (checkValue)
            {
                if (value.IsValidValue())
                {
                    obj.Add(key, value.ToString());
                }
            }
            else
            {
                obj.Add(key, value.ToString());
            }
        }
    }

    public static class StringExtension
    {
        public static string Replace(this string value, string startTag, string endTag, string replaceText,
            bool addIfNotFound)
        {
            string replacedText = value;
            if (value.IsNotNullOrEmpty() && startTag.IsNotNullOrEmpty() && endTag.IsNotNullOrEmpty() &&
                replaceText.IsNotNullOrEmpty())
            {
                int startIndex = value.IndexOf(startTag, StringComparison.Ordinal);
                int endIndex = 0;
                if (startIndex >= 0)
                {
                    endIndex = value.IndexOf(endTag, startIndex, StringComparison.Ordinal);
                    if (endIndex >= 0)
                    {
                        endIndex = endIndex + endTag.Length;
                    }
                }
                if (startIndex >= 0 && endIndex >= 0 && endIndex >= startIndex)
                {
                    string replaceeText = value.Substring(startIndex, endIndex - startIndex);
                    if (replaceeText.IsNotNullOrEmpty())
                    {
                        replaceText = startTag + replaceText + endTag;
                        replacedText = value.Replace(replaceeText, replaceText);
                    }
                }
                else if (addIfNotFound)
                {
                    replacedText = replacedText + startTag + replaceText + endTag;
                }
            }
            return replacedText;
        }

        public static string Substring(this string value, string startTag, bool includeStartTag, string endTag,
            bool excludeEndTag)
        {
            int startIndex = 0;
            return value.Substring(ref startIndex, startTag, includeStartTag, endTag, excludeEndTag);
        }

        public static string Substring(this string value, ref int startIndex, string startTag, bool includeStartTag,
            string endTag, bool excludeEndTag)
        {
            string replacedText = string.Empty;
            if (value.IsNotNullOrEmpty() && startTag.IsNotNullOrEmpty() && endTag.IsNotNullOrEmpty())
            {
                int endIndex;
                startIndex = value.IndexOf(startTag, startIndex, StringComparison.Ordinal);
                if (startIndex >= 0)
                {
                    if (!includeStartTag)
                    {
                        startIndex = startIndex + startTag.Length;
                    }
                    endIndex = value.IndexOf(endTag, startIndex, StringComparison.Ordinal);
                    if (endIndex >= 0)
                    {
                        if (!excludeEndTag)
                        {
                            endIndex = endIndex + endTag.Length;
                        }
                    }
                }
                else
                {
                    endIndex = -1;
                }
                if (startIndex >= 0 && endIndex >= 0 && endIndex >= startIndex)
                {
                    replacedText = value.Substring(startIndex, endIndex - startIndex);
                }
                startIndex = endIndex;
            }
            return replacedText;
        }

        public static string ToString(this string[] values, char separator)
        {
            string concatedValue = string.Empty;
            if (values != null)
            {
                foreach (string currentValue in values)
                {
                    if (currentValue.IsNotNullOrEmpty())
                    {
                        if (concatedValue.IsNullOrEmpty())
                        {
                            concatedValue = currentValue;
                        }
                        else
                        {
                            concatedValue = concatedValue + separator + currentValue;
                        }
                    }
                }
            }
            return concatedValue;
        }

        public static string Concate(this string value, char separator, string valueToAdd)
        {
            if (valueToAdd.IsNotNullOrEmpty())
            {
                if (value.IsNullOrEmpty())
                {
                    value = valueToAdd;
                }
                else
                {
                    value = value + separator + valueToAdd;
                }
            }
            return value;
        }

        public static string Concate(this string value, string separator, params string[] values)
        {
            string concatedValue = value;
            foreach (string currentValue in values)
            {
                if (currentValue.IsNotNullOrEmpty())
                {
                    if (concatedValue.IsNullOrEmpty())
                    {
                        concatedValue = currentValue;
                    }
                    else
                    {
                        concatedValue = concatedValue + separator + currentValue;
                    }
                }
            }
            return concatedValue;
        }

        public static string Concate(this string value, string separator, params object[] values)
        {
            string concatedValue = value;
            foreach (object currentValue in values)
            {
                if (currentValue != null)
                {
                    if (concatedValue.IsNullOrEmpty())
                    {
                        concatedValue = currentValue.ToString();
                    }
                    else
                    {
                        concatedValue = concatedValue + separator + currentValue;
                    }
                }
            }
            return concatedValue;
        }

        public static string Concate(this string value, string valueToAdd)
        {
            if (valueToAdd.IsNotNullOrEmpty())
            {
                if (value.IsNullOrEmpty())
                {
                    value = valueToAdd;
                }
                else
                {
                    value = value + valueToAdd;
                }
            }
            return value;
        }

        public static bool Contains(this string value, string valueToSearch, char separator)
        {
            value = separator + value + separator;
            valueToSearch = separator + valueToSearch + separator;
            return value.Contains(valueToSearch);
        }

        public static StringBuilder AppendIfNotEmpty(this StringBuilder value, params string[] values)
        {
            foreach (string currentValue in values)
            {
                if (currentValue != null)
                {
                    if (!value.IsNullOrEmpty())
                    {
                        value.Append(currentValue);
                    }
                }
            }
            return value;
        }

        public static bool ContainsIgnoreCase(this string left, string right)
        {
            var pattern = new Regex(right, RegexOptions.IgnoreCase);
            return pattern.IsMatch(left);
        }

        public static bool NotEquals(this string left, string right, bool ignoreCase)
        {
            left = left != null ? left.Trim() : string.Empty;
            right = right != null ? right.Trim() : string.Empty;
            if (ignoreCase)
            {
                left = left.ToLower();
                right = right.ToLower();
            }
            return left != right;
        }

        public static bool IsNotNullOrEmpty(this string input)
        {
            input = input != null ? input.Trim() : string.Empty;
            return !String.IsNullOrEmpty(input);
        }

        public static bool IsNotNullOrEmpty(this string input, bool eliminateHtml)
        {
            string output = input;
            if (eliminateHtml)
            {
                output = Regex.Replace(output, @"<(.|\n)*?>", string.Empty);
                output = output.Replace("&nbsp;", string.Empty);
            }
            return IsNotNullOrEmpty(output.Trim());
        }

        public static bool IsNullOrEmpty(this string input)
        {
            if (input == null)
            {
                return true;
            }
            input = input.Trim();
            if (input == string.Empty)
            {
                return true;
            }
            return false;
        }

        public static string Fill(this string format, params object[] args)
        {
            return String.Format(format, args);
        }

        public static string RemoveWhiteSpaces(this string input)
        {
            if (input.IsNotNullOrEmpty())
            {
                var r = new Regex(@"\s");
                return r.Replace(input, "");
            }
            return input;
        }

        public static string RemoveHTML(this string input)
        {
            if (input.IsNotNullOrEmpty())
            {
                //<(.|\n)*?>
                string filteredText = Regex.Replace(input, @"<(.|\n)*?>", string.Empty).Trim();
                filteredText = filteredText.Replace("&nbsp;", " ");
                filteredText = filteredText.Replace("&quot;", string.Empty);
                filteredText = filteredText.Replace("&amp;", "&");
                return filteredText;
            }
            return input;
        }

        public static string ToRawText(this string input)
        {
            if (input.IsNotNullOrEmpty())
            {
                input = input.RemoveHTML();
                input = Regex.Replace(input, @"\s+", " ").Trim();
                return input;
            }
            return string.Empty;
        }

        public static string ToHtmlDecode(this string input)
        {
            if (input.IsNotNullOrEmpty())
            {
                input = HttpUtility.HtmlDecode(input);
                return input;
            }
            return string.Empty;
        }

        public static string ToHtmlEncode(this string input)
        {
            if (input.IsNotNullOrEmpty())
            {
                input = HttpUtility.HtmlEncode(input);
                return input;
            }
            return string.Empty;
        }

        public static bool EqualsIgnoreCase(this string left, string right)
        {
            return String.Compare(left, right, StringComparison.OrdinalIgnoreCase) == 0;
        }

        public static int ToInteger(this string value)
        {
            try
            {
                return Int32.Parse(value);
            }
            catch
            {
                return Int32.MinValue;
            }
        }

        public static string CompareAndGetLatest(this string value, string compareValue)
        {
            return compareValue.IsNotNullOrEmpty() ? compareValue : value;
        }

        public static string CompareAndGetOld(this string value, string compareValue)
        {
            if (value.IsNotNullOrEmpty())
            {
                return value;
            }
            return compareValue;
        }

        public static int ToInteger(this string value, bool isEmptyToZero)
        {
            if (isEmptyToZero && value.IsNullOrEmpty())
            {
                value = "0";
            }
            try
            {
                return Int32.Parse(value);
            }
            catch
            {
                return Int32.MinValue;
            }
        }

        public static bool ToBoolean(this string value)
        {
            try
            {
                return Boolean.Parse(value);
            }
            catch
            {
                return false;
            }
        }

        public static decimal ToDecimal(this string value)
        {
            try
            {
                return value.IsNotNullOrEmpty() ? Decimal.Parse(value) : 0;
            }
            catch
            {
                return decimal.MinValue;
            }
        }

        public static decimal ToDecimal(this string value, bool isEmptyToZero)
        {
            if (isEmptyToZero && value.IsNullOrEmpty())
            {
                value = "0";
            }
            try
            {
                return Decimal.Parse(value);
            }
            catch
            {
                return Decimal.MinValue;
            }
        }

        public static DateTime ToDateTime(this string value)
        {
            try
            {
                return value.IsNotNullOrEmpty() ? DateTime.Parse(value) : DateTime.MinValue;
            }
            catch
            {
                return DateTime.MinValue;
            }
        }

        public static string InitCap(this string value)
        {
            if (value.IsNotNullOrEmpty())
            {
                value = value.ToLower();
                Char firstCharacter = value.GetFirstChar();
                if (value.Length > 0)
                {
                    string otherCharacters = value.Substring(1);
                    value = firstCharacter.ToString().ToUpper() + otherCharacters;
                }
                else
                {
                    value = firstCharacter.ToString().ToUpper();
                }
            }
            return value;
        }

        public static char GetFirstChar(this string value)
        {
            if (value.IsNotNullOrEmpty())
            {
                Char[] firstCharacter = value.Substring(0, 1).ToCharArray();
                return firstCharacter[0];
            }
            return ' ';
        }

        public static string ToString(this string value, bool ignoreZeroValue)
        {
            if (ignoreZeroValue)
            {
                if (value.Trim() == "0")
                {
                    return string.Empty;
                }
                return value.Trim();
            }
            return value.Trim();
        }

        public static string[] ToStringArray(this string value, char separator)
        {
            return value.IsNotNullOrEmpty() ? value.Split(new[] { separator }) : null;
        }

        public static string[] ToStringArray(this string value, char separator, bool isRemoveEmptyEntries)
        {
            if (value.IsNotNullOrEmpty())
            {
                if (isRemoveEmptyEntries)
                {
                    return value.Split(new[] { separator }, StringSplitOptions.RemoveEmptyEntries);
                }
                return value.Split(new[] { separator });
            }
            return null;
        }

        public static string[] ToStringArray(this string value, char separator, bool isRemoveEmptyEntries, int count)
        {
            if (value.IsNotNullOrEmpty())
            {
                if (count == 0)
                {
                    return ToStringArray(value, separator, isRemoveEmptyEntries);
                }
                if (isRemoveEmptyEntries)
                {
                    return value.Split(new[] { separator }, count, StringSplitOptions.RemoveEmptyEntries);
                }
                return value.Split(new[] { separator }, count);
            }
            return null;
        }

        public static string[] ToKeywordArray(this string value)
        {
            if (value.IsNotNullOrEmpty())
            {
                value = " " + value + " ";
                var newRegex = new Regex("(( or )|( and )|( -)|( ~))", RegexOptions.IgnoreCase);
                value = newRegex.Replace(value, " ").Trim();
                return value.ToStringArray(' ', true);
            }
            return null;
        }

        public static string[] GetStringInBetween(string strBegin, string strEnd, string strSource, bool includeBegin,
            bool includeEnd)
        {
            string[] result = { "", "" };

            int iIndexOfBegin = strSource.IndexOf(strBegin, StringComparison.Ordinal);

            if (iIndexOfBegin != -1)
            {
                if (includeBegin)
                {
                    iIndexOfBegin -= strBegin.Length;
                }
                strSource = strSource.Substring(iIndexOfBegin + strBegin.Length);

                int iEnd = strSource.IndexOf(strEnd, StringComparison.Ordinal);

                if (iEnd != -1)
                {
                    if (includeEnd)
                    {
                        iEnd += strEnd.Length;
                    }
                    result[0] = strSource.Substring(0, iEnd);

                    if (iEnd + strEnd.Length < strSource.Length)
                    {
                        result[1] = strSource.Substring(iEnd + strEnd.Length);
                    }
                }
            }
            else
            {
                result[1] = strSource;
            }

            return result;
        }

        public static bool IsEqual(this string leftHandSide, string rightHandSide, bool ignoreCase)
        {
            return ignoreCase
                ? leftHandSide.Equals(rightHandSide, StringComparison.OrdinalIgnoreCase)
                : leftHandSide.Equals(rightHandSide);
        }

        public static bool IsEqual(this string leftHandSide, string rightHandSide)
        {
            return IsEqual(leftHandSide, rightHandSide, true);
        }

        public static bool IsNullOrEmpty(this StringBuilder value)
        {
            return string.IsNullOrEmpty(value.ToString());
        }

        public static string TruncateCsv(this string value, int length)
        {
            if (value.Length > length)
            {
                string[] values = value.ToStringArray(',');
                if (values != null && values.Length > 0)
                {
                    value = string.Empty;
                    foreach (string currentValue in values)
                    {
                        if (value.Length + currentValue.Length < length)
                        {
                            value = value.Concate(',', currentValue);
                        }
                    }
                }
                else
                {
                    value = value.Substring(0, length);
                }
            }
            return value;
        }



        #region Mahesh Singh 

        public static string RemoveSpecialCharacters(string input)
        {
            Regex r = new Regex("(?:[^a-z0-9 ]|(?<=['\"])s)", RegexOptions.IgnoreCase | RegexOptions.CultureInvariant | RegexOptions.Compiled);

            //return r.Replace(input, String.Empty);
            input = input.Replace("=", "'=");
            input = input.Replace("+", "'+");
            return input.Replace("-", "'-");
        }
        public static bool StringContainSpecialCharaters(string input)
        {
            // Regex r = new Regex("(?:[^a-z0-9 ]|(?<=['\"])s)", RegexOptions.IgnoreCase | RegexOptions.CultureInvariant | RegexOptions.Compiled);
            // return System.Text.RegularExpressions.Regex.IsMatch( input, "[=+-]");
            if (input.StartsWith("="))
                return true;
            else if (input.StartsWith("+"))
                return true;
            else if (input.StartsWith("-"))
                return true;
            else if (input.Contains("<"))
                return true;
            else if (input.Contains("#"))
                return true;
            else if (input.Contains(">"))
                return true;
            else
                return false;
        }

        public static bool IgnoreListItem(string TextboxName, string TextboxValue)
        {
            bool check = false;


            switch (TextboxName)
            {

                case "txtWebsite":
                    string webAddressValidation = @"([\w-]+\.)+[\w-]+(/[\w- ./?%&amp;=]*)?";

                    if (Regex.IsMatch(TextboxValue, webAddressValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtCountryCode":
                case "txtCountryCode2":
                case "txtMbCountryCode":
                case "txtMCountryCode":

                    string countryCodeValidation = @"^([+-]?[0-9]\d*|0)$";

                    if (Regex.IsMatch(TextboxValue, countryCodeValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtEmail":
                case "txtemailbox1":

                    string eMailValidation = @"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*";

                    if (Regex.IsMatch(TextboxValue, eMailValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtIPAddress":
                case "txtdagIpaddress":
                case "txtServerIP":
                case "txtDSCLIServerIP":
                case "txtPRIPAddress":
                case "txtDRIPAddress":
                case "txtPRMgtConsoleIP":
                case "txtDRMgtConsoleIP":
                case "txtPRServerNKAddress":
                case "txtDRServerNKAddress":
                case "txtDSCLIServerIPPR":
                case "txtDSCLIServerIPDR":
                    string IPAddressValidation = @"^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$";

                    if (Regex.IsMatch(TextboxValue, IPAddressValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtExecutionPath":
                case "txtWindowpath":
                case "txtWitnessDirectory":
                case "txtCredFilePath":
                case "txtSSHKeyPathPR":
                case "txtSSHKeyPathDR":
                case "txtSSHKeyPath":
                case "txtDrLocalPath":
                case "txtPrLocalpath":
                case "txtSqlCmdPath":
                case "txtSybaseEnvPath":
                case "txtfilepath":

                //For DatabaseConfig
                case "txtmssqlBackupRestorePath":
                case "txtmssqlUndofile":


                    string ExecutionPathValidation = @"^(?:[a-zA-Z]\:|\\\\[\w\.]+\\[\w.$]+)\\(?:[\w]+\\)*\w([\w.])+$";

                    if (Regex.IsMatch(TextboxValue, ExecutionPathValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtKeyLocation":
                //For DatabaseSql
                case "txtmssqlDataFileLocation":
                case "txtmssqlTransactionLocation":

                    string KeyLocationValidation = @"^(?:[a-zA-Z]\:|\\\\[\w\.]+\\[\w.$]+)\\(?:[\w]+\\)*\w([\w.])+$";

                    if (Regex.IsMatch(TextboxValue, KeyLocationValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtoraclesid":
                case "txtInstanceName":
                case "txtdagname":
                case "txtSiteIncharge":
                case "txtOracleSid":
                case "txtmssqldtSID":
                case "txtDB2DatabaseSid":
                case "txtMaxDBSId":

                case "txtPrLssid":


                    string oraclesidValidation = @"^[a-zA-Z][a-zA-Z0-9]*$";

                    if (Regex.IsMatch(TextboxValue, oraclesidValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtPort":
                case "txtDataLag":
                case "txtDataRecovery":
                case "txtMtpod":
                case "txtconnport":
                case "txtcontimeout":
                case "txtNoThreads":
                case "txtPRReplicaBrokerPort":
                case "txtDRReplicaBrokerPort":

                case "txtStateCode":
                case "txtPhone":
                case "txtStateCode2":
                case "txtFax":
                case "txtMobile":

                case "txtoraclePort":
                case "txtmssqlPort":
                case "txtDB2Port":
                case "txtmysqlPort":
                case "txtPostgrePort":
                case "txtPostgre9xPort":
                case "txt2kxPort":
                case "txtsybasePort":
                case "txtsybaseSRSport":
                //case "txtPRWNNNo":
                //case "txtDRWNNNo":
                //case "txtPRStorageImgID":
                //case "txtDRStorageImgID":
                case "txtsessionid":
                case "txtReverseSessionId":

                    string PortValidation = @"^[0-9]+$";              // Integers Only

                    if (Regex.IsMatch(TextboxValue, PortValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtASMGrid":
                //case "txtHome":
                //case "txtRedo":
                case "txtDataSyncPath":
                case "txtDataSyncJREPath":
                case "txtPRLunPath":
                case "txtDRLunPath":

                //case "txtAchive":

                //case "txtASMPath":

                case "txtPostgreArchive":

                case "txtPostgre9xDBDataDirectory":

                case "txtPostgre9xDBbinDirectory":


                    string ASMGridLinuxPathValidation = @"^\/$|(^(?=\/)|^\.|^\.\.)(\/(?=[^/\0])[^/\0]+)*\/?$";       //Linux Path

                    if (Regex.IsMatch(TextboxValue, ASMGridLinuxPathValidation))
                    {
                        check = true;
                    }

                    break;



                case "txtASMusername":
                case "txtUserName":
                case "txtPRProductID":
                case "txtDRProductID":
                //For SVCGlobalMirrorConfig
                case "txtPRRelease":
                case "txtDRRelease":
                case "txtPRLogin":
                case "txtDRLogin":
                case "txtPRConsistentGroupID":
                case "txtDRConsistentGroupID":
                case "txtPRRC_rel_name":
                case "txtDRRC_rel_name":
                case "txtPRDiskControllerID":
                case "txtDRDiskControllerID":
                case "txtPRNodeName":
                case "txtDRNodeName":
                case "txtPRDataStorename":
                case "txtDRDataStorename":

                //For SybasewithSRSConfig
                case "txtRepServerName":
                case "txtRSSDLogin":
                case "txtRSSDName":
                case "txtRSSD_DSName":
                case "txtrssdLoginName":
                case "txtLogicalConnectionName":

                case "txtActiveConnectionName":
                case "txtStandbyConnectionName":

                //For RecoveryPointConfig
                case "txtclusterNodeName":
                case "txtclusterNodeNameDR":

                //For TPCRConfig
                case "txtgroup":
                case "txtfilename":

                //For eBDRReplicationConfig
                case "txtJobName":
                case "txtJobType":

                //For DatabaseConfig
                case "txtoracleUserName":
                case "txtOraInstanceName":
                case "txtmssqlUserName":
                case "txtexchgMailboxDb":
                case "txtexchgDbGroupName":
                case "txtDB2UserName":
                case "txtExdagMailboxDb":

                case "txtmysqldbName":
                case "txtmysqlUserName":

                case "txtPostgreDatabaseName":
                case "txtPostgreUserName":

                case "txtPostgre9xDatabaseName":
                case "txtPostgre9xUserName":

                case "txt2kxDatabaseName":

                case "txtsybasename":
                case "txtsybaseUserName":

                case "txtdtServername":
                case "txtBackupName":

                case "txtsybaseSRSname":

                case "txtsybaseSRSUserName":

                case "txtsybaseSRSServername":

                case "txtsybaseSRSBackupName":

                case "txtLSSIDrange":

                case "AVolume":
                case "BVolume":
                case "CVolume":
                case "DVolume":
                case "EVolume":
                case "FVolume":
                case "txtProcessName":
                case "txtAuditFileName":
                case "tbBCMSExchangeEXE":
                    string EncyptedUserNameValidation = @"^[a-zA-Z0-9_\s\.\-\=]+$";                         //@"^[A-Za-z\d_-]+$";                 // alphanumeric and hypen and underscore

                    if (Regex.IsMatch(TextboxValue, EncyptedUserNameValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtArchievelogpath":
                case "txtTargetArchivelogpath":

                    string ArchievePathValidation = @"^[a-zA-Z0-9\s\+\-\/]+$";              //  + - / and alphanumerics  @"^[a-zA-Z0-9\s\+\-\/]+$"

                    if (Regex.IsMatch(TextboxValue, ArchievePathValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtasmInstanceName":
                case "txtASMInstancename":

                    string asmInstanceNameValidation = @"^[a-zA-Z0-9\s\+\-]+$";              //  + -  and alphanumerics @"^[a-zA-Z0-9\s\+\-]+$"

                    if (Regex.IsMatch(TextboxValue, asmInstanceNameValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtService":
                case "txtPostgre9xServiceName":

                    string ServiceNameValidation = @"^[a-zA-Z0-9_ \s\+\-\/\.\(\)]+$";

                    if (Regex.IsMatch(TextboxValue, ServiceNameValidation))
                    {
                        check = true;
                    }

                    break;

                case "txtWitnessServer":   // cas1-prod.exla2013.com under, for slash also added

                    string ExchangeServerNameValidation = @"^[a-zA-Z0-9_\s\-\/\.]+$";

                    if (Regex.IsMatch(TextboxValue, ExchangeServerNameValidation))
                    {
                        check = true;
                    }

                    break;

                case "txtAddress":

                    string AddressValidation = @"^[a-zA-Z0-9_ \s\+\-\/\.\(\)\@\#\&\=\:\;\,\']+$";

                    if (Regex.IsMatch(TextboxValue, AddressValidation))
                    {
                        check = true;
                    }

                    break;

                case "txtBSDescription":

                    string BSDescValidation = @"^[a-zA-Z0-9_ \s\+\-\/\.\(\)\@\#\&\=\:\;\,\']+$";

                    if (Regex.IsMatch(TextboxValue, BSDescValidation))
                    {
                        check = true;
                    }

                    break;


                //case "txtARCOSOnlineUrl":
                //case "txtArcosWebAPIURL":

                //    string ArcosURLValidation = @"^(((ht|f)tp(s?)\://)|(www))?[^.](.)[^.](([-.\w]*[0-9a-zA-Z])+(:(0-9)*)*(\/?)([a-zA-Z0-9\-\.\?\,\'\/\\\+&amp;%\$#_]*))[^.](.)[^.]([a-zA-Z0-9]+)$"; // For Path which cantains IP address as http://**************:10185/ARCOSAPI001.asmx

                //    if (Regex.IsMatch(TextboxValue, ArcosURLValidation))
                //    {
                //        check = true;
                //    }

                //    break;

                case "txtUserName1":
                case "txtHostName":
                case "txtHostName1":

                    string ArcosUserNameValidation = @"^[a-zA-Z0-9_ \s\@\.\-]+$";

                    if (Regex.IsMatch(TextboxValue, ArcosUserNameValidation))
                    {
                        check = true;
                    }

                    break;



                case "txtprofile":
                case "txtArcosProfile":
                case "txtServiceType":
                case "txtCyberprofile":
                case "txtPostgre9xSULogin":

                    string SingleSignOnProfileNameValidation = @"^[a-zA-Z0-9_\s]+$";

                    if (Regex.IsMatch(TextboxValue, SingleSignOnProfileNameValidation))
                    {
                        check = true;
                    }

                    break;



                case "txtDBInstanceName":

                    //string SingleSignOnDBInstanceNameValidation = @"^[a-zA-Z0-9_ \s]+$";

                    //if (Regex.IsMatch(TextboxValue, SingleSignOnDBInstanceNameValidation))
                    //{
                    check = true;
                    // }

                    break;



                case "txtcaptcha":

                    string CaptchaValidation = @"^[a-zA-Z0-9\s]+$";

                    if (Regex.IsMatch(TextboxValue, CaptchaValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtLogin":

                    string UserLoginNameValidation = @"^[a-zA-Z0-9_ \s\+\-\/\.\@\#\&\=\*]+$";
                    string UserLoginNameValidation1 = @"^[a-zA-Z0-9_.]+\\[a-zA-Z0-9_.]+$";

                    if ((Regex.IsMatch(TextboxValue, UserLoginNameValidation)) || (Regex.IsMatch(TextboxValue, UserLoginNameValidation1)))
                    {
                        check = true;
                    }

                    break;

                case "txtShellPromptPR":
                case "txtShellPromptDR":
                case "txtShellPrompt":

                    string ShellPromptValidation = @"^[s\$\#\>]+$";

                    if (Regex.IsMatch(TextboxValue, ShellPromptValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtFilterExpn":
                case "txtFilterExpn1":
                case "txtDelFilterExpn":

                    string FilterExpressionWindowsValidation = @"^(?:[a-zA-Z]\:|\\\\[\w\.]+\\[\w.$]+)\\(?:[\w]+\\)*\w([\w.])+$";
                    string FilterExpressionLinuxValidation = @"^\/$|(^(?=\/)|^\.|^\.\.)(\/(?=[^/\0])[^/\0]+)*\/?$";
                    string FilteExpressionSingleFileWithExtension = @"^[a-zA-Z0-9\s\.]+$";


                    if ((Regex.IsMatch(TextboxValue, FilterExpressionWindowsValidation)) || (Regex.IsMatch(TextboxValue, FilterExpressionLinuxValidation)) || (Regex.IsMatch(TextboxValue, FilteExpressionSingleFileWithExtension)))
                    {
                        check = true;
                    }

                    break;


                case "txtName":
                case "txtDescription":
                case "txtCompanyId":
                case "txtLocation":
                case "txtBusinessServiceName":

                case "txtApplication":

                case "txtdetails":
                case "txtReplName":
                case "txtNodename":
                case "textName":
                case "txtMediumName":
                case "txtLogfileName":

                // case "txtASMInstancename":
                case "txtASMUserName":


                    string NameValidation = @"^[a-zA-Z0-9_\s\-]+$";      //@"^[A-Za-z\d_-]+$";                

                    if (Regex.IsMatch(TextboxValue, NameValidation))
                    {
                        check = true;
                    }

                    break;


                case "txtsybasetPRTransFileLocation":
                case "txtsybaseEnvrnmentPath":
                case "txtsybaseSRSEnvPath":
                case "txtInstallationPath":
                case "txtLogPath":

                case "txtHome":
                case "txtRedo":
                // case "txtAchive":
                case "txtASMPath":
                    // case "txtDelFilterExpn":

                    // string sybaseWindowsValidation = @"^(?:[a-zA-Z]\:|\\\\[\w\.]+\\[\w.$]+)\\(?:[\w]+\\)*\w([\w.])+$"; //not working for numeric folder name as 11.2.0

                    string sybaseWindowsValidation = @"(([a-z]:|\\\\[a-z0-9_.$]+\\[a-z0-9_.$]+)?(\\?(?:[^\\/:*?""<>|\r\n]+\\)+)[^\\/:*?""<>|\r\n]+)";

                    string sybaseLinuxValidation = @"^\/$|(^(?=\/)|^\.|^\.\.)(\/(?=[^/\0])[^/\0]+)*\/?$";


                    if ((Regex.IsMatch(TextboxValue, sybaseWindowsValidation)) || (Regex.IsMatch(TextboxValue, sybaseLinuxValidation)))
                    {
                        check = true;
                    }

                    break;

                case "txtAchive":

                    string WindowsValidation = @"(([a-z]:|\\\\[a-z0-9_.$]+\\[a-z0-9_.$]+)?(\\?(?:[^\\/:*?""<>|\r\n]+\\)+)[^\\/:*?""<>|\r\n]+)";

                    string LinuxValidation = @"^\/$|(^(?=\/)|^\.|^\.\.)(\/(?=[^/\0])[^/\0]+)*\/?$";

                    string InstanceNameValidation = @"^[a-zA-Z0-9\s\+\-]+$";


                    if ((Regex.IsMatch(TextboxValue, WindowsValidation)) || (Regex.IsMatch(TextboxValue, LinuxValidation)) || (Regex.IsMatch(TextboxValue, InstanceNameValidation)))
                    {
                        check = true;
                    }

                    break;


                #region Vijay

                case "txtSSHUser":
                //case "txtHostName1":
                case "txtBackupJobName":
                case "txtCopyJobName":
                case "txtRestoreJobName":
                //case "txtJobName":
                //case "txtJobType":
                case "txtPRVNName":
                case "txtDRVNName":
                case "txtPRReplicaBrokerName":
                case "txtDRReplicaBrokerName":
                case "txtPRAuthenticationType":
                case "txtDRAuthenticationType":
                ////IBMXIVMirrorConfiguration
                case "txtreplicationmode":
                case "txtPRWNNNo":
                case "txtDRWNNNo":
                case "txtPRStorageImgID":
                case "txtDRStorageImgID":
                //case "txtPRName":
                //case "txtDRName":
                ////EMCSRDF
                case "txtDGName":
                case "txtDGType":
                case "txtDGSymmetrixID":
                case "txtRemoteSym":
                case "txtRDFGroupNumber":
                case "txtPrHorcomInstance":
                case "txtDrHorcomInstance":
                case "txtPrHorcomDeviceGroup":
                case "txtDrHorcomDeviceGroup":
                ////MIMIX
                case "txtDatagroup":
                case "txtMimixLibrary":
                ////AlwaysON
                case "txtavailgroupname":
                case "txtgrouprole":
                //// MySql - Native Log Shipping 
                case "txtPRConnectState":
                case "txtDRConnectState":
                //// RecoverPoint
                //case "txtclusterNodeName":
                //case "txtclusterNodeNameDR":
                //case "txtgroup":
                //case "txtfilename":
                ////SybaseSRS
                //case "txtRepServerName":
                //case "txtRSSDLogin":
                //case "txtRSSDName":
                //case "txtRSSD_DSName":
                //case "txtrssdLoginName":
                //case "txtLogicalC   onnectionName":
                //case "txtSybaseEnvPath":
                //case "txtActiveConnectionName":
                //case "txtStandbyConnectionName":
                ////VMWARE with SVC
                //case "txtPRProductID":
                //case "txtDRProductID":
                //case "txtPRRelease":
                //case "txtDRRelease":
                //case "txtPRLogin":
                //case "txtDRLogin":
                //case "txtPRConsistentGroupID":
                //case "txtDRConsistentGroupID":
                //case "txtPRRC_rel_name":
                //case "txtDRRC_rel_name":
                //case "txtPRDiskControllerID":
                //case "txtDRDiskControllerID":
                //case "txtPRNodeName":
                //case "txtDRNodeName":
                //case "txtPRDataStorename":
                //case "txtDRDataStorename":

                //case "txtASMInstancename":
                //case "txtASMUserName":


                //    string SSHUserValidation = @"^(?![0-9]*$)[a-zA-Z0-9äöüÄÖÜ].*$";//Username/Hostname having alphanumeric + Underscores and -
                //    if (Regex.IsMatch(TextboxValue, SSHUserValidation))
                //    {
                //        check = true;
                //    }

                //    break;

                case "txtLicenceKey":
                    // case "txtASMInstancename":
                    string LicenceKeyValidation = @"^[a-zA-Z0-9]+";//@[^a-zA-Z0-9]";

                    if (Regex.IsMatch(TextboxValue, LicenceKeyValidation))
                    {
                        check = true;
                    }
                    break;

                case "txtPrNetworkPath":
                case "txtDRNetworkPath":
                case "txtmssqlNetworkSharedPath":
                    string NetworkPathValidation = @"([A-Z]:|\\)(\\[a-z0-9]+)*"; //Validation for ==> \\*************\Database\
                    if (Regex.IsMatch(TextboxValue, NetworkPathValidation))
                    {
                        check = true;
                    }
                    break;

                case "txtPRStorageId":
                case "txtDRStorageId":
                case "txtSourceVolume":
                case "txtTargetVolume":
                case "txtPRLunserialNo":
                case "txtDRLunserialNo":
                    string StorageIdValidation = @"^[0-9a-zA-Z\-_]+$";
                    if (Regex.IsMatch(TextboxValue, StorageIdValidation))
                    {
                        check = true;
                    }
                    break;

                case "txtPRLunmapId":
                case "txtDRLunmapId":
                    string LunmapIdValidation = @"^[0-9a-zA-Z\-_ :]+$"; //Validation for ==> NetApp12_ESXi23 : 244
                    if (Regex.IsMatch(TextboxValue, LunmapIdValidation))
                    {
                        check = true;
                    }
                    break;

                case "tbReplayLagTime":
                    string ReplayLagTimevalidation = @"[0-9]+(:[0-9]+)*"; //Validation for ==> 12:00:00
                    if (Regex.IsMatch(TextboxValue, ReplayLagTimevalidation))
                    {
                        check = true;
                    }
                    break;


                case "txtDSCLIPathPR":
                case "txtDSCLIPathDR":
                case "tbPRMSExchangeFolderPath":
                case "tbDRMSExchangeFolderPath":
                case "tbPRNewMailBoxPath":
                case "tbDRNewMailBoxPath":
                case "tbPRBCMSExComponentPath":
                case "tbDRBCMSExComponentPath":
                //case "tbBCMSExchangeEXE":
                case "txtDSCLIPath":

                    string SqlCmdPathValidation = @"^[a-zA-Z]:(\\\w+)*([\\]|[.][a-zA-Z]+)?$"; //Validation for ==> C:\ProgramFiles\SQLSERVER
                    if (Regex.IsMatch(TextboxValue, SqlCmdPathValidation))
                    {
                        check = true;
                    }
                    break;


                #endregion
            }


            //#region WebAddress

            //if (TextboxName.Contains("txtWebsite"))
            //{
            //    string webAddressValidation = @"([\w-]+\.)+[\w-]+(/[\w- ./?%&amp;=]*)?";

            //   if (Regex.IsMatch(TextboxValue, webAddressValidation))
            //   {
            //       check= true;
            //   }

            //}

            //#endregion

            //#region CountryCode

            //if (TextboxName.Contains("txtCountryCode") || TextboxName.Contains("txtCountryCode2"))
            //{
            //    string countryCodeValidation = @"^([+-]?[0-9]\d*|0)$";

            //    if (Regex.IsMatch(TextboxValue, countryCodeValidation))
            //    {
            //        check = true;
            //    }

            //}

            //#endregion

            //#region Email

            //if (TextboxName.Contains("txtEmail"))
            //{
            //    string eMailValidation = @"\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*";

            //    if (Regex.IsMatch(TextboxValue, eMailValidation))
            //    {
            //        check = true;
            //    }

            //}

            //#endregion


            return check;
        }

        public static bool IsConsecutiveCharacters(this string input)
        {
            for (int i = 0; i < input.Length - 1; i++)
            {
                if (input[i].Equals(input[i + 1]))
                    return true;
            }
            return false;
        }

        public static bool StringContainHTMLTag(this string input)
        {
            Match match = Regex.Match(input, @"<(.|\n)*?>",
          RegexOptions.IgnoreCase);

            return match.Success;
        }

        public static bool IsPartOfMonth(this string input)
        {
            string[] MonthNames = DateTimeFormatInfo.CurrentInfo.MonthNames;
            var val = MonthNames.Where(x => input.ToLower().Contains(x.ToLower()) && x.Length > 0).ToList();
            return val.Count > 0;
        }
        public static bool IsPartOfWeek(this string input)
        {

            foreach (DayOfWeek day in Enum.GetValues(typeof(DayOfWeek))
                         .OfType<DayOfWeek>()
                         .ToList()
                         )
            {
                if (input.ToLower().Contains(day.ToString().ToLower()))
                    return true;
            }
            return false;
        }

        #endregion



    }

    public static class StringArrayExtension
    {
        public static bool Contains(this string[] values, string value)
        {
            return values.Any(currentValue => currentValue == value);
        }
    }

    public static class DecimalExtension
    {
        public static bool IsZero(this decimal value)
        {
            return value == 0;
        }

        public static string ToString(this decimal value, bool ignoreZeroValue)
        {
            if (!ignoreZeroValue)
            {
                return value.ToString();
            }
            return value.IsZero() ? string.Empty : value.ToString();
        }

        public static string ToString(this decimal value, bool ignoreZeroValue, string format)
        {
            if (format.IsNullOrEmpty())
            {
                format = "{0:0.00}";
            }
            if (!ignoreZeroValue)
            {
                return String.Format(format, value);
            }
            return value.IsZero() ? string.Empty : String.Format(format, value);
        }

        public static decimal CompareAndGetLatest(this decimal value, decimal compareValue)
        {
            return compareValue > 0 ? compareValue : value;
        }

        public static decimal CompareAndGetOld(this decimal value, decimal compareValue)
        {
            return value > 0 ? value : compareValue;
        }
    }

    public static class IntegerExtension
    {
        public static int ToMb(this long value)
        {
            return (int)(value / 1048576);
        }

        public static int[] ToArray(this int value)
        {
            var newIntArray = new[] { value };
            return newIntArray;
        }

        public static string ToString(this int value, bool isZeroToEmpty)
        {
            if (!isZeroToEmpty)
            {
                return value.ToString();
            }
            return value == 0 ? string.Empty : value.ToString();
        }

        public static decimal ToDecimal(this int value)
        {
            return Convert.ToDecimal(value);
        }

        public static bool IsOdd(this int value)
        {
            return value % 2 != 0;
        }

        public static bool IsEven(this int value)
        {
            return !IsOdd(value);
        }

        public static int CompareAndGetLatest(this int value, int compareValue)
        {
            return compareValue > 0 ? compareValue : value;
        }

        public static int CompareAndGetOld(this int value, int compareValue)
        {
            return value > 0 ? value : compareValue;
        }
    }

    public static class EnumExtension
    {
        public static string ToDescription(this Enum value)
        {
            Type type = value.GetType();
            MemberInfo[] memInfo = type.GetMember(value.ToString());
            if (memInfo.Length > 0)
            {
                object[] attrs = memInfo[0].GetCustomAttributes(
                    typeof(EnumDescriptionAttribute),
                    false);
                if (attrs.Length > 0)
                {
                    return ((EnumDescriptionAttribute)attrs[0]).Description;
                }
            }
            return value.ToString();
        }

        public static string ToValue(this Enum value)
        {
            try
            {
                object objValue = Enum.Parse(value.GetType(), value.ToString());
                return ((int)objValue).ToString();
            }
            catch
            {
                return string.Empty;
            }
        }

        public static int ToInteger(this Enum value)
        {
            try
            {
                object objValue = Enum.Parse(value.GetType(), value.ToString()) ?? 0;
                return (int)objValue;
            }
            catch
            {
                return 0;
            }
        }
    }

    public static class FileUploadExtension
    {
        public static bool IsValidSize(this FileUpload value)
        {
            //less than 10MB
            return value.PostedFile.ContentLength / 1048576 <= 10;
        }

        public static bool IsValidSize(this HttpPostedFile value)
        {
            //less than 10MB
            return value.ContentLength / 1048576 <= 10;
        }
    }

    public static class DropDownListExtension
    {
        public static void ClearItems(this DropDownList ddlItem)
        {
            ddlItem.Items.Clear();
        }

        public static void AddItem(this DropDownList ddlItem, string text, string value)
        {
            ddlItem.Items.Add(new ListItem(text, value));
        }

        public static void InsertItem(this DropDownList ddlItem, int index, string text, string value)
        {
            ddlItem.Items.Insert(index, new ListItem(text, value));
        }

        public static void AddDefaultItem(this DropDownList ddlItem, string text)
        {
            ddlItem.InsertItem(0, text, "0");
        }

        public static int GetSelectedValue(this DropDownList ddlItem)
        {
            return ddlItem.SelectedValue.ToInteger(true);
        }

        public static string GetSelectedText(this DropDownList ddlItem)
        {
            return ddlItem.SelectedItem != null ? ddlItem.SelectedItem.Text : string.Empty;
        }
    }

    public static class ListControlExtension
    {
        public static void ClearItems(this ListControl lstControl)
        {
            lstControl.Items.Clear();
        }

        public static void AddItem(this ListControl lstControl, string text, string value)
        {
            lstControl.Items.Add(new ListItem(text, value));
        }

        public static void AddItem(this ListControl lstControl, ListItem lstItem)
        {
            lstControl.Items.Add(lstItem);
        }

        public static void InsertItem(this ListControl lstControl, int index, string text, string value)
        {
            lstControl.Items.Insert(index, new ListItem(text, value));
        }

        public static bool RemoveItem(this ListControl lstControl, string text, string value)
        {
            if (lstControl.Items.Count > 0)
            {
                if (text.IsNotNullOrEmpty() && value.IsNotNullOrEmpty())
                {
                    var removeItem = new ListItem(text, value);
                    lstControl.Items.Remove(removeItem);
                    return true;
                }
                if (text.IsNotNullOrEmpty())
                {
                    ListItem removeItem = lstControl.Items.FindByText(text);
                    if (removeItem != null)
                    {
                        lstControl.Items.Remove(removeItem);
                        return true;
                    }
                }
                else if (value.IsNotNullOrEmpty())
                {
                    ListItem removeItem = lstControl.Items.FindByValue(value);
                    if (removeItem != null)
                    {
                        lstControl.Items.Remove(removeItem);
                        return true;
                    }
                }
            }
            return false;
        }

        public static void AddDefaultItem(this ListControl lstControl, string text)
        {
            lstControl.InsertItem(0, text, "0");
        }

        public static void AddDefaultItemWithValue(this ListControl lstControl, string text)
        {
            lstControl.InsertItem(0, text, "0");
        }

        public static int GetSelectedValue(this ListControl lstControl)
        {
            return lstControl.SelectedValue.ToInteger(true);
        }

        public static string GetSelectedValues(this ListControl lstControl, bool ignoreOtherIfDefaultSelected)
        {
            string values = string.Empty;
            foreach (
                ListItem currentItem in lstControl.Items.Cast<ListItem>().Where(currentItem => currentItem.Selected))
            {
                if (ignoreOtherIfDefaultSelected)
                {
                    if (currentItem.Value == "0")
                    {
                        return currentItem.Value;
                    }
                }
                values = values.Concate(',', currentItem.Value);
            }
            return values;
        }

        public static string GetSelectedText(this ListControl lstControl)
        {
            return lstControl.SelectedItem != null ? lstControl.SelectedItem.Text : string.Empty;
        }

        public static string GetSelectedText(this ListControl lstControl, bool ignoreDefault)
        {
            return lstControl.SelectedValue == "0" ? string.Empty : GetSelectedText(lstControl);
        }

        public static string GetSelectedTexts(this ListControl lstControl, bool ignoreOtherIfDefaultSelected)
        {
            string values = string.Empty;
            foreach (
                ListItem currentItem in lstControl.Items.Cast<ListItem>().Where(currentItem => currentItem.Selected))
            {
                if (ignoreOtherIfDefaultSelected)
                {
                    if (currentItem.Value == "0")
                    {
                        return currentItem.Text;
                    }
                }
                values = values.Concate(',', currentItem.Text);
            }
            return values;
        }

        public static void SelectItemByText(this ListControl lstControl,
            string text)
        {
            lstControl.SelectedIndex = lstControl.Items.IndexOf(lstControl.Items.FindByText(text));
        }

        public static void SelectItemByValue(this ListControl lstControl, string value)
        {
            lstControl.SelectedIndex = -1;

            lstControl.SelectedValue = value;

            //lstControl.SelectedIndex = lstControl.Items.IndexOf(lstControl.Items.FindByValue(value));
        }

        public static void SelectItemByValue(this ListControl lstControl,
            string value, bool loopThrough)
        {
            if (loopThrough)
            {
                foreach (ListItem lstItem in lstControl.Items.Cast<ListItem>().Where(lstItem => lstItem.Value == value))
                {
                    lstItem.Selected = true;
                    return;
                }
            }
            else
            {
                SelectItemByValue(lstControl, value);
            }
        }

        public static void SelectItemsByValues(this ListControl lstControl,
            string value)
        {
            lstControl.ClearSelection();
            if (!value.IsNotNullOrEmpty())
            {
                return;
            }
            string[] values = value.ToStringArray(',');
            if (values != null)
            {
                foreach (
                    ListItem lstItem in
                        values.Select(currentValue => lstControl.Items.FindByValue(currentValue))
                            .Where(lstItem => lstItem != null))
                {
                    lstItem.Selected = true;
                }
            }
        }

        public static void SelectItemsByTexts(this ListControl lstControl,
            string text)
        {
            lstControl.ClearSelection();
            if (text.IsNotNullOrEmpty())
            {
                string[] texts = text.ToStringArray(',');
                if (texts != null)
                {
                    foreach (
                        ListItem lstItem in
                            texts.Select(currentText => lstControl.Items.FindByText(currentText))
                                .Where(lstItem => lstItem != null))
                    {
                        lstItem.Selected = true;
                    }
                }
            }
        }
    }

    public static class ListExtension
    {
        public static T FindByKey<T>(this IList<T> list, int value)
        {
            foreach (T obj in from obj in list
                              let type = obj.GetType()
                              let property = type.GetProperty("Id")
                              where property != null
                              let objValue = property.GetValue(obj, null)
                              where objValue != null && Convert.ToInt32(objValue) == value
                              select obj)
            {
                return obj;
            }
            return default(T);
        }

        public static string ToString<T>(this IList<T> list, char separator)
        {
            string concatedValue = string.Empty;
            if (typeof(T) == typeof(string) ||
                typeof(T) == typeof(int))
            {
                foreach (T currentValue in list.Where(currentValue => currentValue.ToString().IsNotNullOrEmpty()))
                {
                    if (concatedValue.IsNullOrEmpty())
                    {
                        concatedValue = currentValue.ToString();
                    }
                    else
                    {
                        concatedValue = concatedValue + separator + currentValue;
                    }
                }
            }
            return concatedValue;
        }

        public static DataTable ToDataTable<T>(this IList<T> data)
        {
            PropertyDescriptorCollection props = TypeDescriptor.GetProperties(typeof(T));

            var table = new DataTable();

            for (int i = 0; i < props.Count; i++)
            {
                PropertyDescriptor prop = props[i];
                //  table.Columns.Add(prop.Name, prop.PropertyType);
                Type t = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
                table.Columns.Add(prop.Name, t);
            }

            var values = new object[props.Count];
            foreach (T item in data)
            {
                for (int i = 0; i < values.Length; i++)
                {
                    values[i] = props[i].GetValue(item);
                }
                table.Rows.Add(values);
            }
            return table;
        }
    }

    public static class ExpressionExtension
    {
        public static Expression Concate(this Expression exp, Expression value)
        {
            exp = exp == null ? value : Expression.And(exp, value);
            return exp;
        }
    }

    public static class XmlSerializerExtension
    {
        public static string Serialize(this XmlSerializer serializer, object value)
        {
            var serializedContent = new StringBuilder();
            if (value != null)
            {
                var xmlWriterSettings = new XmlWriterSettings
                {
                    Encoding = Encoding.UTF8,
                    CloseOutput = true,
                    OmitXmlDeclaration = false
                };
                using (XmlWriter xmlWriter = XmlWriter.Create(serializedContent, xmlWriterSettings))
                {
                    serializer.Serialize(xmlWriter, value);
                    xmlWriter.Flush();
                    xmlWriter.Close();
                }
            }
            return serializedContent.ToString();
        }
    }
    public static class RadListControlExtension
    {
        public static void ClearItems(this RadComboBox lstControl)
        {
            lstControl.Items.Clear();
        }

        public static void AddItem(this RadComboBox lstControl, string text, string value)
        {
            lstControl.Items.Add(new RadComboBoxItem(text, value));
        }

        public static void AddItem(this RadComboBox lstControl, RadComboBoxItem lstItem)
        {
            lstControl.Items.Add(lstItem);
        }

        public static void InsertItem(this RadComboBox lstControl, int index, string text, string value)
        {
            lstControl.Items.Insert(index, new RadComboBoxItem(text, value));
        }

        public static bool RemoveItem(this RadComboBox lstControl, string text, string value)
        {
            if (lstControl.Items.Count > 0)
            {
                if (text.IsNotNullOrEmpty() && value.IsNotNullOrEmpty())
                {
                    var removeItem = new RadComboBoxItem(text, value);
                    lstControl.Items.Remove(removeItem);
                    return true;
                }
                if (text.IsNotNullOrEmpty())
                {
                    RadComboBoxItem removeItem = lstControl.Items.FindItemByText(text);
                    if (removeItem != null)
                    {
                        lstControl.Items.Remove(removeItem);
                        return true;
                    }
                }
                else if (value.IsNotNullOrEmpty())
                {
                    RadComboBoxItem removeItem = lstControl.Items.FindItemByValue(value);
                    if (removeItem != null)
                    {
                        lstControl.Items.Remove(removeItem);
                        return true;
                    }
                }
            }
            return false;
        }

        public static void AddDefaultItems(this RadComboBox lstControl, string text)
        {
            lstControl.InsertItem(0, text, "0");
        }
        public static void AddDefaultItemWithValue(this RadComboBox lstControl, string text)
        {
            lstControl.InsertItem(0, text, "0");
        }

        public static int GetSelectedValue(this RadComboBox lstControl)
        {
            return lstControl.SelectedValue.ToInteger(true);
        }

        public static string GetSelectedValues(this RadComboBox lstControl, bool ignoreOtherIfDefaultSelected)
        {
            string values = string.Empty;
            foreach (
                ListItem currentItem in lstControl.Items.Cast<ListItem>().Where(currentItem => currentItem.Selected))
            {
                if (ignoreOtherIfDefaultSelected)
                {
                    if (currentItem.Value == "0")
                    {
                        return currentItem.Value;
                    }
                }
                values = values.Concate(',', currentItem.Value);
            }
            return values;
        }

        public static string GetSelectedText(this RadComboBox lstControl)
        {
            return lstControl.SelectedItem != null ? lstControl.SelectedItem.Text : string.Empty;
        }

        public static string GetSelectedText(this RadComboBox lstControl, bool ignoreDefault)
        {
            return lstControl.SelectedValue == "0" ? string.Empty : GetSelectedText(lstControl);
        }

        public static string GetSelectedTexts(this RadComboBox lstControl, bool ignoreOtherIfDefaultSelected)
        {
            string values = string.Empty;
            foreach (
                RadComboBoxItem currentItem in lstControl.Items.Cast<RadComboBoxItem>().Where(currentItem => currentItem.Selected))
            {
                if (ignoreOtherIfDefaultSelected)
                {
                    if (currentItem.Value == "0")
                    {
                        return currentItem.Text;
                    }
                }
                values = values.Concate(',', currentItem.Text);
            }
            return values;
        }

        public static void SelectItemByText(this RadComboBox lstControl,
            string text)
        {
            lstControl.SelectedIndex = lstControl.Items.IndexOf(lstControl.Items.FindItemByText(text));
        }

        public static void SelectItemByValue(this RadComboBox lstControl, string value)
        {
            lstControl.SelectedIndex = -1;

            lstControl.SelectedValue = value;

            //lstControl.SelectedIndex = lstControl.Items.IndexOf(lstControl.Items.FindByValue(value));
        }

        public static void SelectItemByValue(this RadComboBox lstControl,
            string value, bool loopThrough)
        {
            if (loopThrough)
            {
                foreach (RadComboBoxItem lstItem in lstControl.Items.Cast<RadComboBoxItem>().Where(lstItem => lstItem.Value == value))
                {
                    lstItem.Selected = true;
                    return;
                }
            }
            else
            {
                SelectItemByValue(lstControl, value);
            }
        }

        public static void SelectItemsByValues(this RadComboBox lstControl,
            string value)
        {
            lstControl.ClearSelection();
            if (!value.IsNotNullOrEmpty())
            {
                return;
            }
            string[] values = value.ToStringArray(',');
            if (values != null)
            {
                foreach (
                    RadComboBoxItem lstItem in
                        values.Select(currentValue => lstControl.Items.FindItemByValue(currentValue))
                            .Where(lstItem => lstItem != null))
                {
                    lstItem.Selected = true;
                }
            }
        }

        public static void SelectItemsByTexts(this RadComboBox lstControl,
            string text)
        {
            lstControl.ClearSelection();
            if (text.IsNotNullOrEmpty())
            {
                string[] texts = text.ToStringArray(',');
                if (texts != null)
                {
                    foreach (
                        RadComboBoxItem lstItem in
                            texts.Select(currentText => lstControl.Items.FindItemByText(currentText))
                                .Where(lstItem => lstItem != null))
                    {
                        lstItem.Selected = true;
                    }
                }
            }
        }
    }
}