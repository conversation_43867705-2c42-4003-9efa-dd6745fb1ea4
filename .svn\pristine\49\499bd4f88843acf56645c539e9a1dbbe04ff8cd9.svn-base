﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using log4net;
using CP.Helper;
using System.Web.Services;
using CP.Common.Shared;
using System.Web;

namespace CP.UI.Admin
{
    public partial class ServicesMonitorOverview : BasePage
    {

        #region variables

        public static string CurrentURL = Constants.UrlConstants.Urls.Admin.MonitorServces;

        private static readonly ILog _logger = LogManager.GetLogger(typeof(ServicesMonitorOverview));
        private static IFacade facade = new Facade();
        private static IList<UserServices> ProfileServiceList = null;
        public static string NewjsonString = string.Empty;
        public static int currentNode = 0;
        private static int firstSplitCount = 0;
        private static bool hasChild = false;
        private static bool first = true;
        private static StringBuilder jsonNodeRelSb = null;

        public static string nameStr = "name", levelStr = "level", colorRed = "rgb(255, 3, 7)", childStr = "children",
                                size = "size", hide = "hide", nohide = "nohide", logo = "logo", RepKey = "Replication", RepValue = "Native Replication", Id = "Id", ImpactType = "ImpactType",
                                PartiallyImpacted = "Partial Impact", TotallyImpacted = "Total Impact", NoImpact = "None",
                                MajorImpacted = "Major Impact", faintColor = "rgb(219, 219, 219)", colorOrange = "rgb(237,104,0)", colorBlue = "rgb(74, 139, 194)", colorYellow = "rgb(234, 194, 0)";



        private static List<ServiceDetails> listServices;
        private static List<ServiceDetails> lstServicesNew;
        private static List<ServiceDetails> lstReloadServices;
        private static IList<InfraList> lstInfra = new List<InfraList>();

        public static string GroupServiceName;
        public static int GroupServiceId;

        #endregion

        #region events

        public override void PrepareView()
        {
            // BindAllProfilelist();

            //BindAllProfileListToAccordian();
            if (IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }
            var ServiceProfilelist = facade.GetALL();

            foreach (var _serviceProfile in ServiceProfilelist)
            {
                // var _ServiceList = facade.GetByProfileId(_serviceProfile.Id);
                //GetProfileServiceDetail(_serviceProfile.Id);
                break;

            }
        }

        private static string LoadProfileServiceDetails(int ProfileId)
        {

            string str = string.Empty;
            string Finalstr = string.Empty; var ServiceProfileCount = string.Empty; var ImpactedIncidentCountClass = string.Empty;

            try
            {

                List<ServiceDetails> lstServiceOrderby = new List<ServiceDetails>();

                if (ProfileId != null && ProfileId > 0)
                {

                    ServiceProfileCount = "<span>" + lstServicesNew.Count.ToString() + " Services" + "</span>";

                    lstServiceOrderby = lstServicesNew.OrderByDescending(a => a.ImpactTypeId).ToList();


                    foreach (var ServicesDetials in lstServiceOrderby)
                    {
                        if (ServicesDetials.OpenIncidentCount == 0)
                        {
                            str = "<div id=\"" + ServicesDetials.ServiceId + "\" class=\"servicediv\"> <span class='" + ServicesDetials.ImpactColor + "'>" + ServicesDetials.ServiceName + "</span><p>";
                        }
                        else
                        {
                            str = "<div id=\"" + ServicesDetials.ServiceId + "\" class=\"servicediv\" onclick=\"serviceAlertPopup(" + ProfileId + "," + ServicesDetials.ServiceId + ");\"> <span class='" + ServicesDetials.ImpactColor + "'>" + ServicesDetials.ServiceName + "</span><p>";
                        }

                        str += "<i class=\"ImpactType_Icon\"></i><span>Impact Type </span> :";

                        str += "<span>" + ServicesDetials.ImpactType + "</span> </p> <p>";

                        str += "<i class=\"ImpactTime_Icon\"></i> <span>Impact Time </span>  :   <span>" + ServicesDetials.ImpactTime + "</span>";

                        str += " </p> <p>   <i class=\"ImpactDownTime_Icon\"></i>  <span>Impact Down Time </span>  : <span>" + ServicesDetials.ImpactDownTime + "</span>   </p>";

                        if (ServicesDetials.OpenIncidentCount == 0)
                        {
                            str += " <div class=\"servicebtn\" id='servicebtn'><ul><li></li><li></li><li></li></ul></div></div>";
                        }
                        else
                        {
                            ImpactedIncidentCountClass = string.Empty;
                            ImpactedIncidentCountClass = SetIncidentCountClass(ServicesDetials);
                            str += " <div class=\"servicebtn\" id='servicebtn'> <ul><li></li><li></li><li></li> <span class='" + ImpactedIncidentCountClass + "'>" + ServicesDetials.OpenIncidentCount + " </span></ul></div></div>";
                        }


                        if (String.IsNullOrEmpty(Finalstr))
                            Finalstr = str;
                        else
                            Finalstr += str;
                    }

                }


                return Finalstr + "^" + ServiceProfileCount;
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
                return Finalstr;
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting ddlServiceProfile_SelectedIndexChanged", ex);
                ExceptionManager.Manage(cpException);
                return Finalstr;
            }
        }


        private static string GetProfileServiceDetail2(int ProfileId)
        {

            string str = string.Empty;
            string Finalstr = string.Empty; var ServiceProfileCount = string.Empty; var ImpactedIncidentCountClass = string.Empty;

            try
            {

                listServices = new List<ServiceDetails>();
                lstServicesNew = new List<ServiceDetails>();

                List<ServiceDetails> lstService = new List<ServiceDetails>();
                List<ServiceDetails> lstServiceOrderby = new List<ServiceDetails>();


                ProfileServiceList = facade.GetByProfileId(ProfileId);

                if (ProfileServiceList != null)
                {
                    foreach (UserServices Services in ProfileServiceList)
                    {
                        GroupServiceName = Services.ServiceName;
                        GroupServiceId = Services.Id;
                        //GetServiceJsonfromDB(Services.Id, ProfileId);
                        GetServiceIncidentDetails(Services.BusinessService);

                    }

                    ServiceProfileCount = "<span>" + ProfileServiceList.Count.ToString() + " Services" + "</span>";

                    lstServiceOrderby = lstServicesNew.OrderByDescending(a => a.ImpactTypeId).ToList();

                    foreach (var ServicesDetials in lstServiceOrderby)
                    {
                        if (ServicesDetials.OpenIncidentCount == 0)
                        {
                            str = "<div id=\"" + ServicesDetials.ServiceId + "\" class=\"servicediv\"> <span class='" + ServicesDetials.ImpactColor + "'>" + ServicesDetials.ServiceName + "</span><p>";
                        }
                        else
                        {
                            str = "<div id=\"" + ServicesDetials.ServiceId + "\" class=\"servicediv\" onclick=\"serviceAlertPopup(" + ProfileId + "," + ServicesDetials.ServiceId + ");\"> <span class='" + ServicesDetials.ImpactColor + "'>" + ServicesDetials.ServiceName + "</span><p>";
                        }

                        str += "<i class=\"ImpactType_Icon\"></i><span>Impact Type </span> :";

                        str += "<span>" + ServicesDetials.ImpactType + "</span> </p> <p>";

                        str += "<i class=\"ImpactTime_Icon\"></i> <span>Impact Time </span>  :   <span>" + ServicesDetials.ImpactTime + "</span>";

                        str += " </p> <p>   <i class=\"ImpactDownTime_Icon\"></i>  <span>Impact Down Time </span>  : <span>" + ServicesDetials.ImpactDownTime + "</span>   </p>";

                        if (ServicesDetials.OpenIncidentCount == 0)
                        {
                            str += " <div class=\"servicebtn\" id='servicebtn'><ul><li></li><li></li><li></li></ul></div></div>";
                        }
                        else
                        {
                            ImpactedIncidentCountClass = string.Empty;
                            ImpactedIncidentCountClass = SetIncidentCountClass(ServicesDetials);
                            str += " <div class=\"servicebtn\" id='servicebtn'> <ul><li></li><li></li><li></li> <span class='" + ImpactedIncidentCountClass + "'>" + ServicesDetials.OpenIncidentCount + " </span></ul></div></div>";
                        }


                        if (String.IsNullOrEmpty(Finalstr))
                            Finalstr = str;
                        else
                            Finalstr += str;
                    }

                }


                return Finalstr + "^" + ServiceProfileCount;
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
                return Finalstr;
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting ddlServiceProfile_SelectedIndexChanged", ex);
                ExceptionManager.Manage(cpException);
                return Finalstr;
            }
        }

        //protected void ddlServiceProfile_SelectedIndexChanged(object sender, EventArgs e)

        //private void GetProfileServiceDetail(int ProfileId)
        //{
        //    try
        //    {

        //        listServices = new List<ServiceDetails>();
        //        lstServicesNew = new List<ServiceDetails>();

        //        List<ServiceDetails> lstService = new List<ServiceDetails>();
        //        List<ServiceDetails> lstServiceOrderby = new List<ServiceDetails>();


        //        ProfileServiceList = facade.GetByProfileId(ProfileId);

        //        if (ProfileServiceList != null)
        //        {
        //            foreach (UserServices Services in ProfileServiceList)
        //            {
        //                GroupServiceName = Services.ServiceName;
        //                GroupServiceId = Services.Id;
        //                GetServiceJsonfromDB(Services.Id);
        //            }

        //            labelservices.Text = ProfileServiceList.Count.ToString() + " Services";
        //            UpdateServiceCount.Update();
        //            lstServiceOrderby = lstServicesNew.OrderByDescending(a => a.ImpactTypeId).ToList();
        //            int counter = 1;
        //            foreach (ServiceDetails service in lstServiceOrderby)
        //            {
        //                service.Id = counter;
        //                listServices.Add(service);
        //                counter++;
        //            }


        //            if (listServices != null && listServices.Count() > 0 && !string.IsNullOrEmpty(hdnActiveServiceId.Value))
        //            {
        //                foreach (ServiceDetails Services in listServices)
        //                {
        //                    if (Services.Id.ToString() == hdnActiveServiceId.Value)
        //                        Services.ActiveService = "servicediv active";

        //                    lstService.Add(Services);
        //                }
        //            }
        //            else
        //            {
        //                foreach (ServiceDetails Services in listServices)
        //                {
        //                    lstService.Add(Services);
        //                }
        //            }
        //            rptServiceMonitor.DataSource = lstService;
        //            rptServiceMonitor.DataBind();
        //            //rptServiceMonitor.Items[5].Focus();

        //            updmain.Update();
        //        }
        //        else
        //        {
        //            labelservices.Text = "0 Services";
        //            UpdateServiceCount.Update();
        //            rptServiceMonitor.DataSource = null;
        //            rptServiceMonitor.DataBind();
        //            updmain.Update();
        //        }

        //    }
        //    catch (CpException exc)
        //    {
        //        ExceptionManager.Manage(exc);
        //    }
        //    catch (Exception ex)
        //    {
        //        var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting ddlServiceProfile_SelectedIndexChanged", ex);
        //        ExceptionManager.Manage(cpException);
        //    }
        //}

        //protected void GetData(object sender, EventArgs e)
        //{
        //    try
        //    {

        //        listServices = new List<ServiceDetails>();
        //        lstServicesNew = new List<ServiceDetails>();

        //        if (ddlServiceProfile.SelectedValue != null && Convert.ToInt32(ddlServiceProfile.SelectedValue) > 0)
        //        {
        //            int ProfileServiceId = Convert.ToInt32(ddlServiceProfile.SelectedValue);
        //            ProfileServiceList = Facade.GetByProfileId(ProfileServiceId);

        //            listServices = new List<ServiceDetails>();
        //            List<ServiceDetails> lstService = new List<ServiceDetails>();
        //            List<ServiceDetails> lstSerNew = new List<ServiceDetails>();

        //            if (ProfileServiceList != null)
        //            {
        //                foreach (UserServices Services in ProfileServiceList)
        //                {
        //                    GroupServiceName = Services.ServiceName;
        //                    GroupServiceId = Services.Id;
        //                    GetServiceJsonfromDB(Services.Id);
        //                }
        //                //lblservice.Text = ProfileServiceList.Count.ToString() + " Services";
        //                //UpdateServiceCount.Update();

        //                lstSerNew = lstServicesNew.OrderByDescending(a => a.ImpactTypeId).ToList();

        //                int counter = 1;
        //                foreach (ServiceDetails service in lstSerNew)
        //                {
        //                    service.Id = counter;
        //                    listServices.Add(service);
        //                    counter++;
        //                }

        //                if (listServices != null && listServices.Count > 0 && !chkbxAutoRefresh.Checked)
        //                {
        //                    foreach (ServiceDetails Services in listServices)
        //                    {
        //                        if (Services.Id.ToString() == hdnActiveServiceId.Value)
        //                        {
        //                            Services.ActiveService = "servicediv active";
        //                        }
        //                        else
        //                        {
        //                            Services.ActiveService = "servicediv";
        //                        }

        //                        lstService.Add(Services);
        //                    }
        //                }


        //                if (listServices.Count > 0 && chkbxAutoRefresh.Checked)
        //                {
        //                    if (string.IsNullOrEmpty(hdnActiveServiceId.Value))
        //                    {
        //                        hdnActiveServiceId.Value = "1";
        //                    }
        //                    else
        //                    {
        //                        if (listServices.Count == Convert.ToInt32(hdnActiveServiceId.Value))
        //                        {
        //                            hdnActiveServiceId.Value = "1";
        //                        }
        //                        else
        //                        {
        //                            int count = Convert.ToInt32(hdnActiveServiceId.Value);
        //                            count++;
        //                            hdnActiveServiceId.Value = count.ToString();
        //                        }
        //                    }

        //                    foreach (ServiceDetails Services in listServices)
        //                    {
        //                        if (Services.Id.ToString() == hdnActiveServiceId.Value)
        //                        {
        //                            Services.ActiveService = "servicediv active";
        //                        }
        //                        else
        //                        {
        //                            Services.ActiveService = "servicediv";
        //                        }

        //                        lstService.Add(Services);
        //                    }
        //                }


        //                rptServiceMonitor.DataSource = lstService;
        //                rptServiceMonitor.DataBind();
        //                updmain.Update();
        //            }
        //            else
        //            {

        //                //labelservices.Text = "0 Services";
        //                //UpdateServiceCount.Update();
        //                rptServiceMonitor.DataSource = null;
        //                rptServiceMonitor.DataBind();
        //                updmain.Update();
        //            }

        //        }
        //        if (chkbxAutoRefresh.Checked)
        //        {
        //            Timer1.Enabled = false;
        //            int timeInterval = Convert.ToInt32(timeInter1.SelectedValue);
        //            Timer1.Interval = timeInterval;
        //            Timer1.Enabled = true;
        //        }


        //    }
        //    catch (CpException exc)
        //    {
        //        ExceptionManager.Manage(exc);
        //    }
        //    catch (Exception ex)
        //    {
        //        var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while getting ddlServiceProfile_SelectedIndexChanged", ex);
        //        ExceptionManager.Manage(cpException);
        //    }
        //}

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                System.Web.UI.ScriptManager.RegisterStartupScript(this, this.GetType(), "Script", "ShowServiceProfile()", true);
            }
        }
        #endregion

        #region Method

        [WebMethod]
        public static string LoadProfileService(int ProfileId, int ServiceId)
        {
            string secureDbMonitorUrl = string.Empty;

            var CurrentURL = "../Admin/ServicesMonitor.aspx";
            secureDbMonitorUrl = (CurrentURL + "?param=" + CryptographyHelper.Md5Encrypt(ProfileId + "&" + ServiceId));

            return secureDbMonitorUrl;

        }


        [WebMethod]
        public static string ProfileServiceHtml()
        {
            string htmlString = BindAllProfileListToAccordian();

            if (!string.IsNullOrEmpty(htmlString))
                return htmlString;
            else
                return string.Empty;
        }

        [WebMethod]
        public static string ProfileService(int Id)
        {
            string ServiceStr = GetProfileServiceDetail2(Id);

            if (!string.IsNullOrEmpty(ServiceStr))
                return ServiceStr;
            else
                return string.Empty;
        }

        public static IList<Incidentmanagement> RemoveDuplicate(IList<Incidentmanagement> IncidentManagement)
        {
            IList<Incidentmanagement> IncidentManagementRemoveDep = new List<Incidentmanagement>();

            if (IncidentManagement != null && IncidentManagement.Count() > 0)
            {

                foreach (var item in IncidentManagement)
                {

                    bool alreadyExists = IncidentManagementRemoveDep.Any(x => x.INCIDENTID == item.INCIDENTID);
                    if (!alreadyExists)
                        IncidentManagementRemoveDep.Add(item);


                }


            }


            return IncidentManagementRemoveDep;

        }

        public static string GetIncidentAlertDetailsHTML(string divDetails, string ServiceStr)
        {


            try
            {
                ServiceDetails ServiceDetail = new ServiceDetails();

                IList<Incidentmanagement> IncidentManagement = facade.GetIncidentMgtByBusinessServiceID(ServiceStr.Trim());

                if (IncidentManagement != null && IncidentManagement.Count > 0)
                {

                    IList<Incidentmanagement> RemoveDuplicateIncidentManagement = RemoveDuplicate(IncidentManagement);

                    if (RemoveDuplicateIncidentManagement != null && RemoveDuplicateIncidentManagement.Count > 0)
                    {
                        foreach (var inc in RemoveDuplicateIncidentManagement)
                        {
                            InfraObject infraobject = facade.GetInfraObjectById(inc.INFRAID);
                            if (infraobject != null)
                            {
                                divDetails += "<tr>";
                                divDetails += "<td style='width:20%'><i class=''></i><span class='tdword-wrap'>" + infraobject.Name + "</span></td>";
                                BusinessService businessObj = facade.GetBusinessServiceById(infraobject.BusinessServiceId);
                                divDetails += "<td style='width:15%'><i class=''></i><span class='tdword-wrap'>" + businessObj.Name + "</span></td>";
                                Server objserver = facade.GetServerById(infraobject.PRServerId);
                                if (objserver != null)
                                {

                                    divDetails += "<td style='width:15%'><i class=''></i><span class='tdword-wrap'>" + CryptographyHelper.Md5Decrypt(objserver.IPAddress) + "</span></td>";
                                }
                            }

                            divDetails += "<td style='width:15%'><i class=''></i><span class='tdword-wrap'>" + inc.INFRACOMPONENTTYPE + "</span></td>";
                            divDetails += "<td style='width:35%'><i class=''></i><a data-placement='left' data-toggle='tooltip' title='" + inc.INCIDENTCOMMIT + "'>" + inc.INCIDENTCOMMIT + "</a></td>";
                            divDetails += "</tr>";
                        }
                    }
                }

            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
                return divDetails;
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while fetching Incident details in GetIncidentAlertDetails()", ex);
                ExceptionManager.Manage(cpException);
                return divDetails;
            }

            return divDetails;

        }
        public static IList<IncidentAlertDetails> GetIncidentAlertDetails()
        {

            IncidentAlertDetails IncidentDetails;

            IList<IncidentAlertDetails> ilistIncidentDetails = new List<IncidentAlertDetails>();

            try
            {
                IList<IncidentManagementNew> IncidentManagement = new List<IncidentManagementNew>();
                IncidentManagement = facade.GetOpenIncident();

                if (IncidentManagement != null && IncidentManagement.Count > 0)
                {
                    if (lstInfra != null && lstInfra.Count > 0)
                    {

                        var IncList = IncidentManagement.Where(x => lstInfra.Any(y => y.Id == x.InfraID)).ToList();

                        if (IncList != null && IncList.Count() > 0)
                        {
                            for (int i = 0; i < IncList.Count(); i++)
                            {
                                IncidentDetails = new IncidentAlertDetails();

                                IncidentDetails.Id = IncList[i].Id;
                                IncidentDetails.INCIDENTNAME = IncList[i].IncidentName;
                                IncidentDetails.INFRAID = IncList[i].InfraID.ToString();
                                IncidentDetails.INFRACOMPONENTID = IncList[i].InfraComponentID;
                                IncidentDetails.INFRACOMPONENTTYPE = IncList[i].InfraComponentType;
                                IncidentDetails.INCIDENTCOMMENT = IncList[i].INCIDENTCOMMENT;
                                IncidentDetails.STATUS = IncList[i].Status;
                                InfraObject infraobject = facade.GetInfraObjectById(IncList[i].InfraID);
                                if (infraobject != null)
                                {
                                    IncidentDetails.InfraObjectName = infraobject.Name;
                                    BusinessService businessObj = facade.GetBusinessServiceById(infraobject.BusinessServiceId);

                                    IncidentDetails.BusinessService = businessObj.Name;

                                    Server objserver = facade.GetServerById(infraobject.PRServerId);

                                    if (objserver != null)
                                    {
                                        IncidentDetails.IPAddress = CryptographyHelper.Md5Decrypt(objserver.IPAddress);
                                    }

                                }
                                ilistIncidentDetails.Add(IncidentDetails);

                            }

                        }

                    }

                }
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
                return ilistIncidentDetails;
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while fetching Incident details in GetIncidentAlertDetails()", ex);
                ExceptionManager.Manage(cpException);
                return ilistIncidentDetails;
            }

            return ilistIncidentDetails;

        }

        [WebMethod]
        public static string GetServiceDetils(string ProfileId, string ServiceId)
        {
            IList<UserServices> iServicelist = new List<UserServices>();
            if (!string.IsNullOrEmpty(ServiceId))
            {
                iServicelist = facade.GetServiceById(Convert.ToInt32(ServiceId));
                if (iServicelist != null && iServicelist.Count() > 0)
                {
                    GroupServiceName = iServicelist.FirstOrDefault().ServiceName;
                    GroupServiceId = iServicelist.FirstOrDefault().Id;

                    //GetServiceIncidentDetails(iServicelist.FirstOrDefault().BusinessService);
                    //GetServiceJsonfromDB(iServicelist.FirstOrDefault().Id, Convert.ToInt32(ProfileId));
                }
            }


            var divDetails = "";
            divDetails = "<div id='modelbg' class='bg' style='position: fixed; left: 0px; top: 0px; z-index: 0; width: 100%; height: 100%;'> </div>";
            divDetails += "<div class='modal-dialog' style='width: 1050px'>";
            divDetails += "<div class='modal-content'><div class='modal-header'><a class='close' >x</a>";
            divDetails += "<h3 class='modal-title' id='divHeatmapTitle'>" + GroupServiceName + " Details</h3></div>";
            divDetails += "<div class='modal-body'>";
            divDetails += "<table class='table table-bordered table-condensed margin-bottom-none' width='100%'><thead>";
            divDetails += "<tr>";
            divDetails += "<th style='width:20%'>Infraobject</th>";
            divDetails += "<th style='width:15%'>Business Service</th>";
            divDetails += "<th style='width:15%'>IP Address</th>";
            divDetails += "<th style='width:15%'>Type</th>";
            divDetails += "<th style='width:35%'>Alert</th>";
            divDetails += "</tr>";
            divDetails += "</thead></table>";
            divDetails += "<div class='notifyscrolltable' style='max-height:200px'><table class='table table-bordered table-condensed margin-bottom-none' width='100%'><tbody  id='divHeatmapServer'>";
            if (ProfileId != "" && ServiceId != "")
            {
                divDetails = GetIncidentAlertDetailsHTML(divDetails, iServicelist.FirstOrDefault().BusinessService);
            }

            divDetails += "</tbody>";
            divDetails += "</table></div>";
            divDetails += "</div>";
            divDetails += "</div>";
            divDetails += "</div>";

            return divDetails;
        }

        public static string SetprofileImpactedClass(int ImpactId)
        {
            var ProfileimpactedClass = string.Empty;
            if (ImpactId != 0)
            {
                var INfraImpactType = facade.GetAllImpactRelType();
                var RelImpactType = from a in INfraImpactType where a.Id == ImpactId select a;

                if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "T")
                {
                    ProfileimpactedClass = "profile-totalimapct";
                }
                else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "M")
                {
                    ProfileimpactedClass = "profile-partialimpact";
                }
                else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "P")
                {
                    ProfileimpactedClass = "profile-impact";
                }
            }
            else if (ImpactId == 0)
            {
                ProfileimpactedClass = "profile-noimpact";
            }

            return ProfileimpactedClass;

        }

        public static string SetImpactedClass(ServiceDetails ServiceDetail)
        {
            var impactedClass = string.Empty;

            if (ServiceDetail != null)
            {

                if (ServiceDetail.ImpactType == "Totally Impacted")
                {
                    impactedClass = "profile-totalimapct";
                }
                else if (ServiceDetail.ImpactType == "Majorly Impacted")
                {
                    impactedClass = "profile-partialimpact";
                }
                else if (ServiceDetail.ImpactType == "Partially Impacted")
                {
                    impactedClass = "profile-impact";
                }
                else if (ServiceDetail.ImpactType == "No Impacted")
                {
                    impactedClass = "profile-noimpact";
                }
                else if (ServiceDetail.ImpactType.ToUpper() == "NA")
                {
                    impactedClass = "profile-noimpact";

                }

            }

            return impactedClass;
        }

        public static string SetIncidentCountClass(ServiceDetails ServiceDetail)
        {
            var IncidentCountClass = string.Empty;

            if (ServiceDetail != null)
            {

                if (ServiceDetail.ImpactType == "Totally Impacted")
                {
                    IncidentCountClass = "numpop red-incidentalert";
                }
                else if (ServiceDetail.ImpactType == "Majorly Impacted")
                {
                    IncidentCountClass = "numpop orange-incidentalert";
                }
                else if (ServiceDetail.ImpactType == "Partially Impacted")
                {
                    IncidentCountClass = "numpop yellow-incidentalert";
                }


            }

            return IncidentCountClass;
        }

        [WebMethod]
        public static string SetProfileList(int ProfileId)
        {
            string htmlString = RebindProfileList(ProfileId);

            if (!string.IsNullOrEmpty(htmlString))
                return htmlString;
            else
                return string.Empty;
        }

        public static string RebindProfileList(int ProfileId)
        {
            string ServiceStr = string.Empty;
            string Service = string.Empty;
            string FinalHtmlString = string.Empty; var ImpactedClass = string.Empty; var ProfileImpactedClass = string.Empty; int ProfileimpactedId = 0;


            lstServicesNew = new List<ServiceDetails>();

            if (lstServicesNew != null && lstServicesNew.Count > 0)
            {
                lstServicesNew.Clear();

            }

            List<ServiceDetails> lstService = new List<ServiceDetails>();
            List<ServiceDetails> lstServiceOrderby = new List<ServiceDetails>();

            try
            {


                var ServiceProfilelist = facade.GetALL();

                foreach (var _serviceProfile in ServiceProfilelist)
                {

                    var _ServiceList = facade.GetByProfileId(_serviceProfile.Id);

                    if (_ServiceList != null)
                    {
                        foreach (UserServices Services in _ServiceList)
                        {

                            GroupServiceName = Services.ServiceName;
                            GroupServiceId = Services.Id;
                            //GetServiceJsonfromDBForProfile(Services.Id, _serviceProfile.Id);
                            GetServiceIncidentDetails(Services.BusinessService);
                        }
                    }


                    lstServiceOrderby = lstServicesNew.OrderByDescending(a => a.ImpactTypeId).ToList();
                    int counter = 1;
                    foreach (ServiceDetails service in lstServiceOrderby)
                    {
                        service.Id = counter;
                        lstService.Add(service);
                        counter++;
                    }
                    if (lstService != null & lstService.Count() > 0)
                    {
                        ProfileimpactedId = (from a in lstService select a.ImpactTypeId).Max();
                        ProfileImpactedClass = SetprofileImpactedClass(ProfileimpactedId);
                    }
                    else
                    {
                        ProfileImpactedClass = SetprofileImpactedClass(ProfileimpactedId);
                    }
                    ServiceStr = "<h3 id=\"" + _serviceProfile.Id + "\"> <a href=\"#\" onclick=\"OnClickProfile(" + _serviceProfile.Id + ")\"><i class=\"cio-profile-icon\"></i><span class=\"rfl\"> " + _serviceProfile.ProfileName + "</span> <span class='" + ProfileImpactedClass + "'></span></a></h3>";

                    Service = string.Empty;


                    foreach (var ServicesDetials in lstService)
                    {
                        ImpactedClass = string.Empty;
                        ImpactedClass = SetImpactedClass(ServicesDetials);

                        if (String.IsNullOrEmpty(Service))
                            Service = "<li> <a onclick=\"OnClickProfileService(" + ServicesDetials.ServiceId + "," + _serviceProfile.Id + ");\"> <i class=\"service-icon\"></i><span id=\"" + ServicesDetials.ServiceId + _serviceProfile.Id + "\" runat=\"server\" title=\"" + ServicesDetials.ServiceName + "\">" + ServicesDetials.ServiceName + "</span><span class='" + ImpactedClass + "'></span> </a></li>";
                        else
                            Service += "<li> <a onclick=\"OnClickProfileService(" + ServicesDetials.ServiceId + "," + _serviceProfile.Id + ");\"> <i class='service-icon'></i><span id=\"" + ServicesDetials.ServiceId + _serviceProfile.Id + "\" runat=\"server\" title=\"" + ServicesDetials.ServiceName + "\"  >" + ServicesDetials.ServiceName + "</span><span class='" + ImpactedClass + "'></span> </a> </li>";
                    }


                    lstServicesNew.Clear(); lstService.Clear(); ProfileImpactedClass = string.Empty; ProfileimpactedId = 0;

                    if (String.IsNullOrEmpty(FinalHtmlString))
                        FinalHtmlString = ServiceStr + "<div> <ul class=\"subservice\">" + Service + "</ul> </div>";
                    else
                        FinalHtmlString += ServiceStr + "<div> <ul class=\"subservice\">" + Service + "</ul> </div>";
                }


                return FinalHtmlString;

            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
                return FinalHtmlString;
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while binding Service Profile RebindProfileList", ex);
                ExceptionManager.Manage(cpException);
                return FinalHtmlString;
            }


        }

        private static string BindAllProfileListToAccordian()
        {
            string ServiceStr = string.Empty;
            string Service = string.Empty;
            string FinalHtmlString = string.Empty; var ServiceProfileCount = string.Empty; int ServiceCount = 1; var ImpactedClass = string.Empty; var ProfileImpactedClass = string.Empty; int ProfileimpactedId = 0;

            lstServicesNew = new List<ServiceDetails>();

            List<ServiceDetails> lstService = new List<ServiceDetails>();
            List<ServiceDetails> lstServiceOrderby = new List<ServiceDetails>();

            try
            {


                var ServiceProfilelist = facade.GetALL();

                foreach (var _serviceProfile in ServiceProfilelist)
                {

                    var _ServiceList = facade.GetByProfileId(_serviceProfile.Id);

                    if (_ServiceList != null)
                    {
                        foreach (UserServices Services in _ServiceList)
                        {

                            GroupServiceName = Services.ServiceName;
                            GroupServiceId = Services.Id;
                            //GetServiceJsonfromDB(Services.Id, _serviceProfile.Id);
                            GetServiceIncidentDetails(Services.BusinessService.Trim());

                          


                        }
                    }

                    if (ServiceCount == 1)
                    {

                        ServiceProfileCount = LoadProfileServiceDetails(_serviceProfile.Id);

                    }
                    lstServiceOrderby = lstServicesNew.OrderByDescending(a => a.ImpactTypeId).ToList();
                    int counter = 1;
                    foreach (ServiceDetails service in lstServiceOrderby)
                    {
                        service.Id = counter;
                        lstService.Add(service);
                        counter++;
                    }
                    if (lstService != null & lstService.Count() > 0)
                    {
                        ProfileimpactedId = (from a in lstService select a.ImpactTypeId).Max();
                        ProfileImpactedClass = SetprofileImpactedClass(ProfileimpactedId);
                    }
                    else
                    {
                        ProfileImpactedClass = SetprofileImpactedClass(ProfileimpactedId);
                    }
                    ServiceStr = "<h3 id=\"" + _serviceProfile.Id + "\"> <a href=\"#\" onclick=\"OnClickProfile(" + _serviceProfile.Id + ")\"><i class=\"cio-profile-icon\"></i><span class=\"rfl\"> " + _serviceProfile.ProfileName + "</span> <span class='" + ProfileImpactedClass + "'></span></a></h3>";

                    Service = string.Empty;


                    foreach (var ServicesDetials in lstService)
                    {
                        ImpactedClass = string.Empty;
                        ImpactedClass = SetImpactedClass(ServicesDetials);

                        if (String.IsNullOrEmpty(Service))
                            Service = "<li> <a onclick=\"OnClickProfileService(" + ServicesDetials.ServiceId + "," + _serviceProfile.Id + ");\"> <i class=\"service-icon\"></i><span id=\"" + ServicesDetials.ServiceId + _serviceProfile.Id + "\" runat=\"server\" title=\"" + ServicesDetials.ServiceName + "\"  >" + ServicesDetials.ServiceName + "</span><span class='" + ImpactedClass + "'></span> </a></li>";
                        else
                            Service += "<li> <a onclick=\"OnClickProfileService(" + ServicesDetials.ServiceId + "," + _serviceProfile.Id + ");\"> <i class='service-icon'></i><span id=\"" + ServicesDetials.ServiceId + _serviceProfile.Id + "\" runat=\"server\"   title=\"" + ServicesDetials.ServiceName + "\" >" + ServicesDetials.ServiceName + "</span><span class='" + ImpactedClass + "'></span> </a> </li>";
                    }

                    ServiceCount++;
                    lstServicesNew.Clear(); lstService.Clear(); ProfileImpactedClass = string.Empty; ProfileimpactedId = 0;

                    if (String.IsNullOrEmpty(FinalHtmlString))
                        FinalHtmlString = ServiceStr + "<div> <ul class=\"subservice\">" + Service + "</ul> </div>";
                    else
                        FinalHtmlString += ServiceStr + "<div> <ul class=\"subservice\">" + Service + "</ul> </div>";
                }


                return FinalHtmlString + "^" + ServiceProfileCount;

            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
                return FinalHtmlString;
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while binding Service Profile list", ex);
                ExceptionManager.Manage(cpException);
                return FinalHtmlString;
            }


        }


        private void BindAllProfilelist()
        {
            try
            {
                var ServiceProfilelist = facade.GetALL();
                if (ServiceProfilelist != null)
                {
                    ddlServiceProfile.DataSource = ServiceProfilelist;
                    ddlServiceProfile.DataValueField = "Id";
                    ddlServiceProfile.DataTextField = "ProfileName";
                    ddlServiceProfile.DataBind();
                    //updmain.Update();
                }

                ddlServiceProfile.Items.Insert(0, new ListItem("Select Service Profile", "0"));
            }
            catch (CpException exc)
            {
                ExceptionManager.Manage(exc);
            }
            catch (Exception ex)
            {
                var cpException = new CpException(CpExceptionType.CommonUnhandled, "Error occured while binding Service Profile list", ex);
                ExceptionManager.Manage(cpException);
            }
        }
        /// <summary>
        /// Create Dynamic Most Effective rule
        /// </summary>
        /// <returns>string</returns>
        /// <author>Suryaji shinde</author>
        /// 
        public static string GetMostEffectiveRuleFromImpactId(int impactId, string AppType)
        {

            string IMType = string.Empty; string[] OldNodeArray = { };
            try
            {

                switch (AppType)
                {
                    case "S":

                        var INfraImpactType = facade.GetAllImpactRelType();

                        var RelImpactTypeBS = from a in INfraImpactType where a.Id == impactId select a;

                        if (RelImpactTypeBS != null && RelImpactTypeBS.Count() > 0)
                        {
                            if (RelImpactTypeBS.FirstOrDefault().RelTypeValue.Trim() == "P")
                            {
                                IMType = "PIBS";
                            }
                            if (RelImpactTypeBS.FirstOrDefault().RelTypeValue.Trim() == "T")
                            {
                                IMType = "TIBS";
                            }
                            else if (RelImpactTypeBS.FirstOrDefault().RelTypeValue.Trim() == "M")
                            {
                                IMType = "MIBS";
                            }
                        }

                        break;

                    case "F":

                        var INfraImpactTypeBF = facade.GetAllImpactRelType();

                        var RelImpactTypeBF = from a in INfraImpactTypeBF where a.Id == impactId select a;

                        if (RelImpactTypeBF != null && RelImpactTypeBF.Count() > 0)
                        {
                            if (RelImpactTypeBF.FirstOrDefault().RelTypeValue.Trim() == "P")
                            {
                                IMType = "PIBF";
                            }
                            if (RelImpactTypeBF.FirstOrDefault().RelTypeValue.Trim() == "T")
                            {
                                IMType = "TIBF";
                            }
                            else if (RelImpactTypeBF.FirstOrDefault().RelTypeValue.Trim() == "M")
                            {
                                IMType = "MIBF";
                            }
                        }

                        break;
                }

                return IMType;
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                _logger.Error("Exception Occured while populating GetMostEffectiveRuleFromImpactId method in ServicesMonitorOverview.cs:" + ex.Message);
                ExceptionManager.Manage(bcms);
                return string.Empty;

            }
        }

        public static string CreateServiceIncident(int sid, string node)
        {
            string finalStr = string.Empty;
            int MostEffectiveBfImpact = 0;
            int MostEffectiveBSImpact = 0;
            string mostEffectiveBFNode = string.Empty;
            string mostEffectiveBSNode = string.Empty;
            string SerNodestr = string.Empty;
            string[] DynamicBSNodeArray = { };
            string funasChild = string.Empty;
            string NodePredfix = string.Empty;
            string infraCom = string.Empty;
            string FuncnodeStr = string.Empty;
            string InfraNodestr = string.Empty;
            Dictionary<string, string> dicFun = new Dictionary<string, string>();//Func-Infra Child
            Dictionary<string, string> dicInfra = new Dictionary<string, string>();//Infra-Comp Child
            Dictionary<string, string> dicComp = new Dictionary<string, string>();//Comp-SubComp Child

            try
            {
                IList<Incidentmanagement> lstIncidentMgt = facade.GetIncidentMgtByServiceID(sid);


                var ISmostBSIMpact = (from u in lstIncidentMgt
                                      where u.PARENTBSID == sid
                                      orderby u.PARENTBSIMPACTID descending
                                      select u).FirstOrDefault();
                if (ISmostBSIMpact != null)
                    MostEffectiveBSImpact = ISmostBSIMpact.PARENTBSIMPACTID;
                mostEffectiveBSNode = GetMostEffectiveRuleFromImpactId(MostEffectiveBSImpact, "S");

                int CharFCount = node.Count(c => c.Equals('/'));
                if (CharFCount > 0)
                {
                    DynamicBSNodeArray = node.Split('/');
                }




                if (lstIncidentMgt != null && lstIncidentMgt.Count > 0)
                {

                    SerNodestr = DynamicBSNodeArray[0].ToString().Trim() + "/" + mostEffectiveBSNode + "~";

                    if (node.Contains('@'))
                        NodePredfix = node.Split('@')[0] + "@" + node.Split('@')[1];
                    else
                        NodePredfix = node.Split('/')[0];
                    IList<Server> lstserver = facade.GetAllServers();
                    foreach (var item in lstIncidentMgt)
                    {
                        string InfraSubCompstr = string.Empty;
                        string infrNode = string.Empty;
                        InfraObject infraItem = facade.GetInfraObjectById(item.INFRAID);
                        BusinessFunction bfObj = facade.GetBusinessFunctionById(item.PARENTBFID);

                        var ISmostBFIMpact = (from u in lstIncidentMgt
                                              where u.PARENTBFID == item.PARENTBFID
                                              orderby u.PARENTBFIMPACTID descending
                                              select u).FirstOrDefault();
                        if (ISmostBFIMpact != null)
                            MostEffectiveBfImpact = ISmostBFIMpact.PARENTBFIMPACTID;
                        mostEffectiveBFNode = GetMostEffectiveRuleFromImpactId(MostEffectiveBfImpact, "F");

                        FuncnodeStr = NodePredfix + "@F_" + bfObj.Id + "$" + bfObj.Name + "/" + mostEffectiveBFNode;
                        InfraNodestr = NodePredfix + "@I_" + infraItem.Id + "$" + infraItem.Name + "/TII";

                        if (!dicFun.ContainsKey(FuncnodeStr))
                            dicFun.Add(FuncnodeStr, InfraNodestr);
                        else
                            if (!dicFun[FuncnodeStr].Split(',').Contains(InfraNodestr))
                                dicFun[FuncnodeStr] += "," + InfraNodestr;

                        IList<Incidentmanagement> incidentlistByInfra = (from incident in lstIncidentMgt where incident.INFRAID == item.INFRAID select incident).ToList() as IList<Incidentmanagement>;
                        var result = incidentlistByInfra.GroupBy(test => test.INFRACOMPONENTTYPE).Select(grp => grp.First()).ToList();
                        if (result != null)
                        {
                            foreach (var incitem in result)
                            {

                                if (incitem.INFRACOMPONENTTYPE.ToLower().Equals("server") && incitem.JOBNAME.ToLower().Equals("monitorprserverstatus"))
                                //if (incitem.INFRACOMPONENTTYPE.ToLower().Equals("server"))
                                {
                                    Server server = lstserver.Where(x => x.Id == incitem.INFRACOMPONENTID).FirstOrDefault();

                                    if (server != null)
                                    {
                                        infraCom = NodePredfix + "@ICSV_" + server.Id + "$" + server.Name + "/TIICSV";

                                        InfraSubCompstr = "IS_" + server.Id + "$IP " + CryptographyHelper.Md5Decrypt(server.IPAddress) +
                                            "/TIIS#" + "IS_" + server.Id + "$OS " + server.OSType + "/TIIS";

                                        if (!dicInfra.ContainsKey(InfraNodestr))
                                            dicInfra.Add(InfraNodestr, infraCom);
                                        else
                                            if (!dicInfra[InfraNodestr].Split(',').Contains(infraCom))
                                                dicInfra[InfraNodestr] += "," + infraCom;


                                        if (!dicComp.ContainsKey(infraCom))
                                            dicComp.Add(infraCom, InfraSubCompstr);
                                        else
                                            if (!dicComp[infraCom].Split(',').Contains(InfraSubCompstr))
                                                dicComp[infraCom] += "," + InfraSubCompstr;
                                    }
                                }
                                if (incitem.INFRACOMPONENTTYPE.ToLower().Equals("database") && incitem.JOBNAME.ToLower().Equals("monitorprdatabasestatus"))
                                //if (incitem.INFRACOMPONENTTYPE.ToLower().Equals("database"))
                                {
                                    DatabaseBase database = facade.GetDatabaseBaseById(incitem.INFRACOMPONENTID);
                                    if (database != null)
                                    {
                                        Server server = lstserver.Where(x => x.Id == database.ServerId).FirstOrDefault();
                                        infraCom = NodePredfix + "@ICD_" + database.Id + "$" + database.Name + "/TIICD";
                                        InfraSubCompstr = "IS_" + database.Id + "$DB " + database.DatabaseType + "/TIIS#" + "IS_" + database.Id +
                                                            "$SID " + database.Name + "/TIIS#" + "IS_" +
                                                            database.Id + "$Server " + CryptographyHelper.Md5Decrypt(server.IPAddress) + "/TIIS";

                                        if (!dicInfra.ContainsKey(InfraNodestr))
                                            dicInfra.Add(InfraNodestr, infraCom);
                                        else
                                            if (!dicInfra[InfraNodestr].Split(',').Contains(infraCom))
                                                dicInfra[InfraNodestr] += "," + infraCom;


                                        if (!dicComp.ContainsKey(infraCom))
                                            dicComp.Add(infraCom, InfraSubCompstr);
                                        else
                                            if (!dicComp[infraCom].Split(',').Contains(InfraSubCompstr))
                                                dicComp[infraCom] += "," + InfraSubCompstr;
                                    }
                                }
                                if (incitem.INFRACOMPONENTTYPE.ToLower().Equals("replication"))
                                {
                                    ReplicationBase replication = facade.GetReplicationBaseById(incitem.INFRACOMPONENTID);
                                    if (replication != null)
                                    {
                                        infraCom = NodePredfix + "@ICR_" + replication.Id + "$" + replication.Name + "/TIICR";
                                        InfraSubCompstr = "IS_" + replication.Id + "$DB" + replication.Name + "/TIIS";

                                        if (!dicInfra.ContainsKey(InfraNodestr))
                                            dicInfra.Add(InfraNodestr, infraCom);
                                        else
                                            if (!dicInfra[InfraNodestr].Split(',').Contains(infraCom))
                                                dicInfra[InfraNodestr] += "," + infraCom;


                                        if (!dicComp.ContainsKey(infraCom))
                                            dicComp.Add(infraCom, InfraSubCompstr);
                                        else
                                            if (!dicComp[infraCom].Split(',').Contains(InfraSubCompstr))
                                                dicComp[infraCom] += "," + InfraSubCompstr;
                                    }
                                }
                            }

                            // Add Process Node
                            Incidentmanagement tempIncident = (from templst in incidentlistByInfra
                                                               where templst.JOBNAME.ToLower().Equals("monitorapplicationprocess") &&
                                                                   templst.INFRACOMPONENTTYPE.ToLower().Equals("server")
                                                               select templst).FirstOrDefault();

                            if (infraItem != null && tempIncident != null)
                            //if (infraItem != null)
                            {
                                string serviceChild = string.Empty;
                                infraCom = NodePredfix + "@ICSM_" + infraItem.Id + "$" + infraItem.AppProcess + "/TIICSM";
                                string proccess = infraItem.AppProcess;
                                if (!string.IsNullOrEmpty(proccess) && !proccess.Equals("NA"))
                                {
                                    string[] processarr = proccess.Split(',');
                                    foreach (var servicItem in processarr)
                                        serviceChild += "@ICSS_" + infraItem.Id + "$" + servicItem + "/TIICSS#";


                                    if (!dicInfra.ContainsKey(InfraNodestr))
                                        dicInfra.Add(InfraNodestr, infraCom);
                                    else
                                        if (!dicInfra[InfraNodestr].Split(',').Contains(infraCom))
                                            dicInfra[InfraNodestr] += "," + infraCom;


                                    if (!dicComp.ContainsKey(infraCom))
                                        dicComp.Add(infraCom, serviceChild);
                                    else
                                        if (!dicComp[infraCom].Split(',').Contains(serviceChild))
                                            dicComp[infraCom] += serviceChild;
                                }
                            }

                        }
                        //Add Que Node 
                        IList<Incidentmanagement> incidentQueue = (from templst in incidentlistByInfra
                                                                   where templst.JOBNAME.ToLower().Equals("monitorqueueprocess") &&
                                                                       templst.INFRACOMPONENTTYPE.ToLower().Equals("server")
                                                                   select templst).ToList();
                        if (incidentQueue.Count > 0 && incidentQueue != null)
                        {
                            string queuename = string.Empty;

                            foreach (var itemque in incidentQueue)
                            {
                                queuename += itemque.APPPROCESS + ",";
                            }

                            //Get Que from table 
                            if (!string.IsNullOrEmpty(queuename))
                            {
                                queuename = queuename.Substring(0, queuename.Length - 1);
                                string queueChild = string.Empty;
                                infraCom = NodePredfix + "@Q_" + infraItem.Id + "$" + "Queue" + "/TIQ";
                                IList<QueueMoniter> qmList = facade.QueueMonStatusGetByName(queuename, item.INFRAID);
                                if (qmList.Count > 0)
                                {
                                    foreach (var itmeqmList in qmList)
                                        queueChild += "@NQ_" + itmeqmList.Id + "$" + itmeqmList.QUEUENAME + "/TIQQ#,";


                                    if (!dicInfra.ContainsKey(InfraNodestr))
                                        dicInfra.Add(InfraNodestr, infraCom);
                                    else
                                        if (!dicInfra[InfraNodestr].Split(',').Contains(infraCom))
                                            dicInfra[InfraNodestr] += "," + infraCom;


                                    if (!dicComp.ContainsKey(infraCom))
                                        dicComp.Add(infraCom, queueChild);
                                    else
                                        if (!dicComp[infraCom].Split(',').Contains(queueChild))
                                            dicComp[infraCom] += queueChild;
                                }
                            }
                        }

                    }


                    if (dicFun.Count > 0)
                    {
                        Dictionary<string, string> funDic = new Dictionary<string, string>();
                        foreach (var funItem in dicFun)
                        {
                            if (!funDic.ContainsKey(funItem.Key))
                                funDic.Add(funItem.Key, funItem.Key);

                            finalStr += funItem.Key + ":" + funItem.Value + ";";
                            string[] functArr = funItem.Value.Split(',');
                            for (int i = 0; i < functArr.Length; i++)
                            {
                                finalStr += functArr[i] + ":" + dicInfra[functArr[i]] + ";";
                                string[] infracom = dicInfra[functArr[i]].Split(',');
                                for (int h = 0; h < infracom.Length; h++)
                                {
                                    if (dicComp[infracom[h]].Substring(dicComp[infracom[h]].Length - 1) == "#")
                                        dicComp[infracom[h]] = dicComp[infracom[h]].Substring(0, dicComp[infracom[h]].Length - 1);
                                    finalStr += infracom[h] + ":" + dicComp[infracom[h]].Replace('#', ',') + ";";
                                }
                            }
                        }

                        foreach (var f in funDic)
                            funasChild += f.Key + ",";
                        funasChild = funasChild.Substring(0, funasChild.Length - 1);
                        if (!string.IsNullOrEmpty(funasChild))
                            funasChild += "~";
                    }
                }
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                _logger.Error("Exception Occured while populating CreateServiceIncident method in ServicesMonitorOverview.cs:" + ex.Message);
                ExceptionManager.Manage(bcms);

                return string.Empty;
            }
            return SerNodestr + funasChild + finalStr;
        }
        public static string UpdateJSONWithInident(int id)
        {

            string returnstr = string.Empty;
            try
            {
                IList<UserServices> userService = facade.GetServiceById(id);
                if (userService != null)
                {
                    UserServices UserSer = (from servieItem in userService select servieItem).FirstOrDefault();
                    if (userService != null)
                    {
                        string JSON = UserSer.JSONSTR;
                        if (!string.IsNullOrEmpty(JSON))
                        {
                            string[] JSONArr = JSON.Split(';');
                            string tempstr = JSON;
                            string NewJSON = "";
                            for (int i = 0; i < JSONArr.Length; i++)
                            {
                                string newstr = string.Empty;
                                string oldChild = string.Empty;
                                string chikdwithoutInciedent = string.Empty;
                                if (!string.IsNullOrEmpty(JSONArr[i]))
                                {
                                    string[] childArr = JSONArr[i].Split(':')[1].Split(',');
                                    for (int k = 0; k < childArr.Length; k++)
                                    {
                                        int serviceId = 0;
                                        string tempStr = string.Empty;
                                        NewJSON = string.Empty;
                                        serviceId = Convert.ToInt16(childArr[k].Split('@')[2].Split('_')[1].Split('$')[0]);
                                        tempStr = CreateServiceIncident(serviceId, childArr[k]);
                                        if (!string.IsNullOrEmpty(tempStr))
                                        {
                                            string[] strarray = tempStr.Split('~');
                                            string strnewParent = strarray[0];
                                            string newChild = strarray[1]; //tempStr.Split('~')[0];
                                            newstr = strarray[2]; //tempStr.Split('~')[1];
                                            string[] temparr = tempstr.Split(';');
                                            bool flag = false;
                                            for (int m = 0; m < temparr.Length; m++)
                                            {

                                                if (temparr[m].Split(':')[0].Equals(childArr[k]))
                                                {
                                                    //NewJSON += temparr[m].Split(':')[0] + ":" + newChild + "," + temparr[m].Split(':')[1] + ";";
                                                    NewJSON += strnewParent + ":" + newChild + "," + temparr[m].Split(':')[1] + ";";
                                                    NewJSON += newstr;
                                                    flag = true;

                                                }
                                                else
                                                    if (!string.IsNullOrEmpty(temparr[m]))
                                                    {

                                                        if (temparr[m].Split(':')[1].Split(',').Contains(childArr[k]))
                                                        {
                                                            string strchildTemp = string.Empty;
                                                            string[] strchild = temparr[m].Split(':')[1].Split(',');
                                                            for (int x = 0; x < strchild.Length; x++)
                                                            {
                                                                if (strchild[x].Equals(childArr[k]))

                                                                    strchildTemp += strnewParent + ",";
                                                                else
                                                                    strchildTemp += strchild[x] + ",";
                                                            }
                                                            if (strchildTemp.Substring(strchildTemp.Length - 1) == ",")
                                                                strchildTemp = strchildTemp.Substring(0, strchildTemp.Length - 1);

                                                            NewJSON += temparr[m].Split(':')[0] + ":" + strchildTemp + ";";
                                                        }
                                                        else
                                                            if (!string.IsNullOrEmpty(temparr[m]))
                                                                NewJSON += temparr[m] + ";";

                                                    }
                                            }
                                            if (!flag)
                                            {
                                                NewJSON = "";
                                                for (int m = 0; m < temparr.Length; m++)
                                                {
                                                    if (!string.IsNullOrEmpty(temparr[m]))
                                                    {
                                                        string[] child = temparr[m].Split(':')[1].Split(',');
                                                        string oldchilnew = "";
                                                        if (child.Contains(childArr[k]))
                                                        {
                                                            foreach (var te in child)
                                                                if (!te.Equals(childArr[k]))
                                                                    oldchilnew += te + ",";
                                                            if (!string.IsNullOrEmpty(oldchilnew))
                                                            {
                                                                oldchilnew = oldchilnew.Substring(0, oldchilnew.Length - 1);
                                                                //NewJSON += temparr[m].Split(':')[0] + ":" + childArr[k] + "," + oldchilnew + ";";
                                                                NewJSON += temparr[m].Split(':')[0] + ":" + strnewParent + "," + oldchilnew + ";";
                                                            }
                                                            else
                                                                //NewJSON += temparr[m].Split(':')[0] + ":" + childArr[k] + ";";
                                                                //NewJSON += childArr[k] + ":" + newChild + ";";
                                                                NewJSON += temparr[m].Split(':')[0] + ":" + strnewParent + ";";
                                                            NewJSON += strnewParent + ":" + newChild + ";";
                                                            NewJSON += newstr;
                                                        }
                                                        else
                                                        {
                                                            if (!string.IsNullOrEmpty(temparr[m]))
                                                                NewJSON += temparr[m] + ";";
                                                        }
                                                    }
                                                }
                                            }
                                            tempstr = NewJSON;
                                        }
                                    }
                                }
                            }
                            returnstr = tempstr;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                _logger.Error("Exception Occured while populating UpdateJSONWithInident method in ServicesMonitorOverview.cs:" + ex.Message);
                ExceptionManager.Manage(bcms);

                return string.Empty;

            }
            return AddIncidentForFirstServ(returnstr);
        }

        public static string AddIncidentForFirstServ(string jsonstr)
        {
            string NewJsonstr = string.Empty;
            try
            {
                if (!string.IsNullOrEmpty(jsonstr))
                {
                    string NodePredfix = string.Empty;
                    string[] JArr = jsonstr.Split(';');
                    string firstNode = jsonstr.Split(';')[0];
                    int servicesid = 0;
                    if (firstNode.Contains(':'))
                        firstNode = firstNode.Split(':')[0];
                    servicesid = Convert.ToInt32(firstNode.Split('$')[0].Split('_')[1]);
                    string incidentJSON = string.Empty;
                    incidentJSON = CreateServiceIncident(servicesid, firstNode);
                    if (!string.IsNullOrEmpty(incidentJSON))
                    {
                        string strServiceNode = incidentJSON.Split('~')[0];
                        string newch = incidentJSON.Split('~')[1];
                        string newjs = incidentJSON.Split('~')[2];
                        for (int i = 0; i < JArr.Length; i++)
                        {
                            if (i == 0)
                            {
                                if (!string.IsNullOrEmpty(JArr[i].Split(':')[1]))
                                    //NewJsonstr += JArr[i].Split(':')[0] + ":" + newch + "," + JArr[i].Split(':')[1] + ";";
                                    NewJsonstr += strServiceNode + ":" + newch + "," + JArr[i].Split(':')[1] + ";";
                                else
                                    //NewJsonstr += JArr[i].Split(':')[0] + ":" + newch + ";";
                                    NewJsonstr += strServiceNode + ":" + newch + ";";
                                NewJsonstr += newjs;

                            }
                            else
                                if (!string.IsNullOrEmpty(JArr[i]))
                                    NewJsonstr += JArr[i] + ";";
                        }
                    }
                    else
                        return jsonstr;
                }
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                _logger.Error("Exception Occured while AddIncidentForFirstServ method in ServicesMonitorOverview.cs:" + ex.Message);
                ExceptionManager.Manage(bcms);
                return string.Empty;
            }
            return NewJsonstr;
        }

        public static int getInfraObjectID(string inputstr)
        {
            string ReturnID = string.Empty;
            string[] IDNameArr;
            int appid = 0;
            string AppType = "";

            if (!string.IsNullOrEmpty(inputstr))
            {
                int CharCount = inputstr.Count(c => c.Equals('@'));
                if (CharCount > 0)
                {
                    string strresult = inputstr.Substring(inputstr.LastIndexOf('@') + 1);
                    strresult = strresult.Remove(strresult.IndexOf('$'));
                    IDNameArr = strresult.Split('_');
                    if (IDNameArr.Length > 0)
                    {
                        appid = Convert.ToInt32(IDNameArr[1]);
                        AppType = IDNameArr[0];
                    }
                }
                else
                {
                    if (inputstr.IndexOf('$') < 0)
                        AppType = inputstr;
                    else
                    {
                        appid = Convert.ToInt32(inputstr.Split('$')[0].Split('_')[1]);
                        AppType = inputstr.Split('$')[0].Split('_')[0].Trim();
                    }

                }
            }

            return appid;


        }

        public static void GetServiceJsonfromDBForProfile(int id, int ProfileID)
        {
            var INfraImpactType = facade.GetAllImpactRelType();
            lstInfra.Clear();

            InfraList infraObjectlist = new InfraList();

            string TreeImpactJsonstr = string.Empty; DataTable dtinfra = null; int infraId = 0;

            string TreeJsonstr = UpdateJSONWithInident(id);

            DataTable dt = CreateJsonTable(TreeJsonstr);

            TreeImpactJsonstr = GetJsonfromImpactRule(dt, id);

            var InfraRows = from myRow in dt.Rows.Cast<DataRow>()
                            where myRow.Field<string>("ParentAppType") == "I"
                            select myRow;

            if (InfraRows != null && InfraRows.Count() > 0)
            {
                dtinfra = InfraRows.CopyToDataTable();

                foreach (DataRow ICitems in dtinfra.Rows)
                {

                    string parentNode = ICitems["ParentNode"].ToString();

                    if (parentNode.IndexOf("I_") > 0)
                    {
                        infraId = getInfraObjectID(parentNode);

                    }
                    bool alreadyExists = lstInfra.Any(x => x.Id == Convert.ToInt32(infraId));

                    if (!alreadyExists && infraId != null)
                    {
                        infraObjectlist.Id = infraId;
                        infraObjectlist.GroupServiceID = id;
                        infraObjectlist.ProfileID = ProfileID;
                        lstInfra.Add(infraObjectlist);
                    }


                }
            }

            ServiceDetails ServiceDetail = new ServiceDetails();

            //int MostImpactedId = 0;
            //if (TreeImpactJsonstr.IndexOf("/PIBS") > 0)
            //    MostImpactedId = 1;
            //if (TreeImpactJsonstr.IndexOf("/MIBS") > 0)
            //    MostImpactedId = 2;
            //if (TreeImpactJsonstr.IndexOf("/TIBS") > 0)
            //    MostImpactedId = 3;

            int MostImpactedId = 0;
            if (INfraImpactType != null && INfraImpactType.Count > 0)
            {
                if (TreeImpactJsonstr.IndexOf("/PIBS") > 0)
                {
                    var RelImpactType = from a in INfraImpactType where a.RelTypeValue.Trim() == "P" select a;
                    if (RelImpactType != null)
                        MostImpactedId = RelImpactType.FirstOrDefault().Id;
                }
                if (TreeImpactJsonstr.IndexOf("/MIBS") > 0)
                {
                    var RelImpactType = from a in INfraImpactType where a.RelTypeValue.Trim() == "M" select a;
                    if (RelImpactType != null)
                        MostImpactedId = RelImpactType.FirstOrDefault().Id;
                }
                if (TreeImpactJsonstr.IndexOf("/TIBS") > 0)
                {
                    var RelImpactType = from a in INfraImpactType where a.RelTypeValue.Trim() == "T" select a;
                    if (RelImpactType != null)
                        MostImpactedId = RelImpactType.FirstOrDefault().Id;
                }

            }



            if (lstInfra != null && lstInfra.Count > 0)
            {

                ServiceDetail.ServiceName = GroupServiceName;
                ServiceDetail.ServiceId = GroupServiceId;

                if (MostImpactedId != 0)
                {
                    var RelImpactType = from a in INfraImpactType where a.Id == MostImpactedId select a;

                    if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "T")
                    {

                        ServiceDetail.ImpactType = "Totally Impacted";
                        ServiceDetail.ImpactColor = "servicehead border7px-red";
                        ServiceDetail.ImpactTypeId = MostImpactedId;
                    }
                    else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "M")
                    {
                        ServiceDetail.ImpactType = "Majorly Impacted";
                        ServiceDetail.ImpactColor = "servicehead border7px-orange";
                        ServiceDetail.ImpactTypeId = MostImpactedId;
                    }
                    else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "P")
                    {
                        ServiceDetail.ImpactType = "Partially Impacted";
                        ServiceDetail.ImpactColor = "servicehead border7px-yellow";
                        ServiceDetail.ImpactTypeId = MostImpactedId;
                    }

                }
                else if (MostImpactedId == 0)
                {
                    ServiceDetail.ImpactType = "No Impacted";
                    ServiceDetail.ImpactColor = "servicehead border7px-green";
                    ServiceDetail.ImpactTypeId = MostImpactedId;
                }
                if (!lstServicesNew.Contains(ServiceDetail) && ServiceDetail.ServiceName != null)
                    lstServicesNew.Add(ServiceDetail);

            }
            else
                SetImpactedeatils();
            //GetServiceMonitorDetails(TreeImpactJsonstr, id, lstInfra);

        }

        public static void GetServiceJsonfromDB(int id, int ProfileID)
        {
            lstInfra.Clear();

            InfraList infraObjectlist = new InfraList();

            string TreeImpactJsonstr = string.Empty; DataTable dtinfra = null; int infraId = 0;

            string TreeJsonstr = UpdateJSONWithInident(id);

            DataTable dt = CreateJsonTable(TreeJsonstr);

            TreeImpactJsonstr = GetJsonfromImpactRule(dt, id);

            var InfraRows = from myRow in dt.Rows.Cast<DataRow>()
                            where myRow.Field<string>("ParentAppType") == "I"
                            select myRow;

            if (InfraRows != null && InfraRows.Count() > 0)
            {
                dtinfra = InfraRows.CopyToDataTable();

                foreach (DataRow ICitems in dtinfra.Rows)
                {

                    string parentNode = ICitems["ParentNode"].ToString();

                    if (parentNode.IndexOf("I_") > 0)
                    {
                        infraId = getInfraObjectID(parentNode);

                    }
                    bool alreadyExists = lstInfra.Any(x => x.Id == Convert.ToInt32(infraId));

                    if (!alreadyExists && infraId != null)
                    {
                        infraObjectlist.Id = infraId;
                        infraObjectlist.GroupServiceID = id;
                        infraObjectlist.ProfileID = ProfileID;
                        lstInfra.Add(infraObjectlist);
                    }


                }
            }
            GetServiceMonitorDetails(TreeImpactJsonstr, id, lstInfra);

        }

        public static DataTable CreateJsonTable(string TreeJsonstr)
        {

            DataTable dtSortJsonBYParentNode = new DataTable();

            dtSortJsonBYParentNode.Columns.Add("ID", typeof(Int32));
            dtSortJsonBYParentNode.Columns.Add("ParentNode", typeof(string));
            dtSortJsonBYParentNode.Columns.Add("ChildNode", typeof(string));
            dtSortJsonBYParentNode.Columns.Add("ParentAppType", typeof(string));
            dtSortJsonBYParentNode.Columns.Add("ParentNodeID", typeof(Int32));



            string NewInputString = string.Empty;
            string[] NodeArray = null;
            string[] AppTypeArry = null;
            string[] NodeChildArray = null;
            string ChildStr = string.Empty;
            int RowCount = 1;
            string AppType = string.Empty;
            string ParentNode = string.Empty; string ChildNodeStr = string.Empty;

            if (!string.IsNullOrEmpty(TreeJsonstr))
            {

                if (!string.IsNullOrEmpty(TreeJsonstr))
                {
                    NodeArray = TreeJsonstr.Split(';');
                    for (int i = 0; i < NodeArray.Length; i++)
                    {
                        if (!string.IsNullOrEmpty(NodeArray[i]))
                        {
                            NodeChildArray = NodeArray[i].Split(':');
                            var PNode = NodeChildArray[0];

                            if (NodeChildArray.Length > 1)
                            {

                                AppType = GetAppTypeFromNode(PNode);

                                dtSortJsonBYParentNode.Rows.Add(RowCount, PNode, NodeChildArray[1], AppType);
                                RowCount++;


                            }
                            AppType = "";

                        }
                    }
                }
            }

            foreach (DataRow Prows in dtSortJsonBYParentNode.Rows)
            {
                ParentNode = string.Empty;
                ParentNode = Prows["ParentNode"].ToString().Trim();

                foreach (DataRow Crows in dtSortJsonBYParentNode.Rows)
                {
                    ChildNodeStr = string.Empty;
                    ChildNodeStr = Crows["ChildNode"].ToString().Trim();

                    if (!string.IsNullOrEmpty(ChildNodeStr))
                    {
                        if (ChildNodeStr.Contains(','))
                        {
                            var ChildPNode = ChildNodeStr.Split(',');
                            for (int k = 0; k < ChildPNode.Length; k++)
                            {
                                var PChildstr = ChildPNode[k].ToString().Trim();
                                if (PChildstr == ParentNode.Trim())
                                {
                                    Prows["ParentNodeID"] = Crows["ID"];

                                }

                            }

                        }
                        else
                        {
                            if (ChildNodeStr.ToString().Trim() == ParentNode.Trim())
                            {
                                Prows["ParentNodeID"] = Crows["ID"];

                            }


                        }

                    }


                }


            }

            dtSortJsonBYParentNode.Rows[0]["ParentNodeId"] = 0;
            return dtSortJsonBYParentNode;


        }

        public static string GetAppTypeFromNode(string Node)
        {
            string[] IDNameArr;
            int appid = 0;
            string AppType = "";

            int CharCount = Node.Count(c => c.Equals('@'));
            if (CharCount > 0)
            {
                string strresult = Node.Substring(Node.LastIndexOf('@') + 1);

                if (strresult.Contains('$'))
                {
                    strresult = strresult.Remove(strresult.IndexOf('$'));
                    IDNameArr = strresult.Split('_');
                    if (IDNameArr.Length > 0)
                    {
                        appid = Convert.ToInt32(IDNameArr[1]);
                        AppType = IDNameArr[0];
                    }
                }
            }
            else
            {
                if (Node.IndexOf('$') < 0)
                    AppType = Node;
                else
                {
                    appid = Convert.ToInt32(Node.Split('$')[0].Split('_')[1]);
                    AppType = Node.Split('$')[0].Split('_')[0].Trim();
                }

            }


            return AppType;
        }



        public static String GetInfraComponentTypeById(int InfracomponentId, int InfraId)
        {
            String componentNameType = string.Empty;

            var getInfrasummary = facade.GetInfraObjectById(Convert.ToInt32(InfraId));
            if (getInfrasummary != null)
            {
                if (getInfrasummary.PRServerId == InfracomponentId)
                {
                    componentNameType = "Server";
                }
                else if (getInfrasummary.PRDatabaseId == InfracomponentId)
                {
                    componentNameType = "Database";
                }
                else if (getInfrasummary.DRServerId == InfracomponentId)
                {
                    componentNameType = "DRServer";
                }
                else if (getInfrasummary.DRDatabaseId == InfracomponentId)
                {
                    componentNameType = "DRDatabase";
                }
                else
                {
                    componentNameType = "Replication";
                }

            }


            return componentNameType;
        }

        public static string GetInfraImpactedType(int infraId, int CompID, string infraComponentType)
        {
            string IsimpactedType = string.Empty;

            var AllincedentSummary = facade.GetAllIncidentManagementSummary();

            if (AllincedentSummary != null && AllincedentSummary.Count() > 0)
            {

                var Allincedent = facade.GetAllIncidentManagementNew();
                if (Allincedent != null && Allincedent.Count() > 0)
                {

                    var openIncedent = from a in Allincedent where a.Status == 1 select a;

                    if (openIncedent != null && openIncedent.Count() > 0)
                    {

                        var openIncedentSummaryByIncid = from a in AllincedentSummary
                                                         join b in openIncedent
                                                         on a.IncidentID equals b.Id
                                                         where (b.InfraID == infraId && b.InfraComponentType == infraComponentType.ToString() && b.InfraComponentID == CompID)
                                                         select a;

                        if (openIncedentSummaryByIncid != null && openIncedentSummaryByIncid.Count() > 0)
                        {
                            IsimpactedType = "TIIC";

                        }
                    }
                    else
                    {
                        IsimpactedType = "";
                    }
                }

            }
            else
            {
                IsimpactedType = "";

            }
            return IsimpactedType;

        }

        public static DataTable GetInfratoBsImpactedDeatils(DataTable impactedInfra, DataTable ImpactedInfraRules)
        {
            string IsimpactedType = string.Empty; int infraId;
            int CompID;
            string infraComponentType;

            var AllincedentSummary = facade.GetAllIncidentManagementSummary();

            if (AllincedentSummary != null && AllincedentSummary.Count() > 0)
            {

                var Allincedent = facade.GetAllIncidentManagementNew();
                if (Allincedent != null && Allincedent.Count() > 0)
                {

                    var openIncedent = from a in Allincedent where a.Status == 1 select a;

                    if (openIncedent != null && openIncedent.Count() > 0)
                    {

                        foreach (DataRow drow in impactedInfra.Rows)
                        {
                            infraId = Convert.ToInt32(drow["InfraID"]); CompID = Convert.ToInt32(drow["InfraCompID"]); infraComponentType = drow["infraComponentType"].ToString();

                            var openIncedentSummaryByIncid = from a in AllincedentSummary
                                                             join b in openIncedent
                                                             on a.IncidentID equals b.Id
                                                             where (b.InfraID == infraId && b.InfraComponentType == infraComponentType.ToString() && b.InfraComponentID == CompID)
                                                             select a;

                            if (openIncedentSummaryByIncid != null && openIncedentSummaryByIncid.Count() > 0)
                            {
                                var INfraImpactType = facade.GetAllImpactRelType();
                                var RelImpactType = from a in INfraImpactType where a.RelTypeValue == "T" select a;

                                if (RelImpactType != null && RelImpactType.Count() > 0)
                                {
                                    ImpactedInfraRules.Rows.Add(infraId, RelImpactType.FirstOrDefault().Id, CompID, RelImpactType.FirstOrDefault().Id, infraComponentType, openIncedentSummaryByIncid.FirstOrDefault().ParentBFID, openIncedentSummaryByIncid.FirstOrDefault().ParentBFImpactID, openIncedentSummaryByIncid.FirstOrDefault().ParentBSID, openIncedentSummaryByIncid.FirstOrDefault().ParentBSImpactID);

                                }

                            }
                        }
                    }
                    else
                    {
                        IsimpactedType = "";
                    }
                }

            }

            return ImpactedInfraRules;

        }

        public static DataTable GetBstoBsImpactedDeatils(DataTable impactedInfra, DataTable AllBsToBSRule)
        {

            string IsimpactedType = string.Empty; int infraId;
            int CompID;
            string infraComponentType;

            var AllincedentSummary = facade.GetAllIncidentManagementSummaryBstoBs();

            if (AllincedentSummary != null && AllincedentSummary.Count() > 0)
            {

                var Allincedent = facade.GetAllIncidentManagementNew();
                if (Allincedent != null && Allincedent.Count() > 0)
                {

                    var openIncedent = from a in Allincedent where a.Status == 1 select a;

                    if (openIncedent != null && openIncedent.Count() > 0)
                    {

                        foreach (DataRow drow in impactedInfra.Rows)
                        {
                            infraId = Convert.ToInt32(drow["InfraID"]); CompID = Convert.ToInt32(drow["InfraCompID"]); infraComponentType = drow["infraComponentType"].ToString();

                            var openIncedentSummaryByIncid = from a in AllincedentSummary
                                                             join b in openIncedent
                                                             on a.IncidentID equals b.Id
                                                             where (b.InfraID == infraId && b.InfraComponentType == infraComponentType.ToString() && b.InfraComponentID == CompID)
                                                             select a;

                            if (openIncedentSummaryByIncid != null && openIncedentSummaryByIncid.Count() > 0)
                            {

                                foreach (var items in openIncedentSummaryByIncid)
                                {

                                    AllBsToBSRule.Rows.Add(items.IncidentID, items.ParentBSID, items.ParentBSImpactID, items.ChildBSID, items.ChildBSImpactID);
                                }

                            }
                        }
                    }
                    else
                    {
                        IsimpactedType = "";
                    }
                }

            }

            return AllBsToBSRule;

        }

        public static void CreateTable()
        {
            DataTable ImpactedInfraRules = new DataTable();
            DataTable AllBsToBSRule = new DataTable();

            AllBsToBSRule.Columns.Add("IncidentID", typeof(string));
            AllBsToBSRule.Columns.Add("ParentBSID", typeof(string));
            AllBsToBSRule.Columns.Add("ParentBSImpactedID", typeof(string));
            AllBsToBSRule.Columns.Add("ChildBSID", typeof(string));
            AllBsToBSRule.Columns.Add("ChildBSImpactedID", typeof(string));


            ImpactedInfraRules.Columns.Add("InfraID", typeof(string));
            ImpactedInfraRules.Columns.Add("InfraImpactedID", typeof(string));
            ImpactedInfraRules.Columns.Add("InfraCompID", typeof(string));
            ImpactedInfraRules.Columns.Add("InfraCompImpactedID", typeof(string));
            ImpactedInfraRules.Columns.Add("infraComponentType", typeof(string));
            ImpactedInfraRules.Columns.Add("BFID", typeof(string));
            ImpactedInfraRules.Columns.Add("BFImpactedID", typeof(string));
            ImpactedInfraRules.Columns.Add("ChildBSID", typeof(string));
            ImpactedInfraRules.Columns.Add("BSImpactedID", typeof(string));
        }


        public static void GetServiceIncidentDetails(string ServiceStr)
        {

            ServiceDetails ServiceDetail = new ServiceDetails();

            int MostEffectiveBSImpact = 0;


            IList<Incidentmanagement> IncidentManagement = facade.GetIncidentMgtByBusinessServiceID(ServiceStr.Trim());

            if (IncidentManagement != null && IncidentManagement.Count > 0)
            {
                IList<Incidentmanagement> RemoveDuplicateIncidentManagement = RemoveDuplicate(IncidentManagement);

                if (RemoveDuplicateIncidentManagement != null && RemoveDuplicateIncidentManagement.Count > 0)
                {
                    if (RemoveDuplicateIncidentManagement != null && RemoveDuplicateIncidentManagement.Count > 0)
                    {
                        var ISmostBSIMpact = (from u in RemoveDuplicateIncidentManagement
                                              orderby u.PARENTBSIMPACTID descending
                                              select u).FirstOrDefault();
                        if (ISmostBSIMpact != null)
                            MostEffectiveBSImpact = ISmostBSIMpact.PARENTBSIMPACTID;

                        //Fetch all infraobject presnt in Incidenttable.

                        var MinDate = (from u in RemoveDuplicateIncidentManagement where u.STATUS == 1 orderby u.IncidentTime descending select u).FirstOrDefault();
                        TimeSpan diff = DateTime.Now - MinDate.IncidentTime;
                        string hours = diff.Days + "." + diff.Hours + ":" + diff.Minutes + ":" + diff.Seconds;
                        ServiceDetail.ServiceName = GroupServiceName;
                        ServiceDetail.ServiceId = GroupServiceId;
                        ServiceDetail.ImpactTime = MinDate.IncidentTime.ToString();
                        ServiceDetail.ImpactDownTime = hours.ToString();
                        ServiceDetail.ActiveService = "servicediv";
                        ServiceDetail.OpenIncidentCount = RemoveDuplicateIncidentManagement.Count();

                        if (MostEffectiveBSImpact != 0)
                        {
                            var INfraImpactType = facade.GetAllImpactRelType();
                            var RelImpactType = from a in INfraImpactType where a.Id == MostEffectiveBSImpact select a;

                            if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "T")
                            {

                                ServiceDetail.ImpactType = "Totally Impacted";
                                ServiceDetail.ImpactColor = "servicehead border7px-red";
                                ServiceDetail.ImpactTypeId = MostEffectiveBSImpact;
                            }
                            else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "M")
                            {
                                ServiceDetail.ImpactType = "Majorly Impacted";
                                ServiceDetail.ImpactColor = "servicehead border7px-orange";
                                ServiceDetail.ImpactTypeId = MostEffectiveBSImpact;
                            }
                            else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "P")
                            {
                                ServiceDetail.ImpactType = "Partially Impacted";
                                ServiceDetail.ImpactColor = "servicehead border7px-yellow";
                                ServiceDetail.ImpactTypeId = MostEffectiveBSImpact;
                            }

                        }
                        else if (MostEffectiveBSImpact == 0)
                        {
                            ServiceDetail.ImpactType = "No Impacted";
                            ServiceDetail.ImpactColor = "servicehead border7px-green";
                            ServiceDetail.ImpactTypeId = MostEffectiveBSImpact;
                        }
                        if (!lstServicesNew.Contains(ServiceDetail) && ServiceDetail.ServiceName != null)
                            lstServicesNew.Add(ServiceDetail);

                    }
                    else
                    {
                        SetImpactedeatils();

                    }
                }
                else
                {
                    SetImpactedeatils();
                }

            }
            else
            {
                SetImpactedeatils();
            }


        }
        public static void GetServiceMonitorDetails(string UpdatedStr, int GroupServiceID, IList<InfraList> dtinfra)
        {

            ServiceDetails ServiceDetail = new ServiceDetails();

            //int MostImpactedId = 0;
            //if (UpdatedStr.IndexOf("/PIBS") > 0)
            //    MostImpactedId = 1;
            //if (UpdatedStr.IndexOf("/MIBS") > 0)
            //    MostImpactedId = 2;
            //if (UpdatedStr.IndexOf("/TIBS") > 0)
            //    MostImpactedId = 3;


            var INfraImpactType = facade.GetAllImpactRelType();

            int MostImpactedId = 0;
            if (INfraImpactType != null && INfraImpactType.Count > 0)
            {
                if (UpdatedStr.IndexOf("/PIBS") > 0)
                {
                    var RelImpactType = from a in INfraImpactType where a.RelTypeValue.Trim() == "P" select a;
                    if (RelImpactType != null)
                        MostImpactedId = RelImpactType.FirstOrDefault().Id;
                }
                if (UpdatedStr.IndexOf("/MIBS") > 0)
                {
                    var RelImpactType = from a in INfraImpactType where a.RelTypeValue.Trim() == "M" select a;
                    if (RelImpactType != null)
                        MostImpactedId = RelImpactType.FirstOrDefault().Id;
                }
                if (UpdatedStr.IndexOf("/TIBS") > 0)
                {
                    var RelImpactType = from a in INfraImpactType where a.RelTypeValue.Trim() == "T" select a;
                    if (RelImpactType != null)
                        MostImpactedId = RelImpactType.FirstOrDefault().Id;
                }

            }


            IList<IncidentManagementNew> IncidentManagement = new List<IncidentManagementNew>();
            IncidentManagement = facade.GetOpenIncident();

            if (IncidentManagement != null && IncidentManagement.Count > 0)
            {
                if (dtinfra != null && dtinfra.Count > 0)
                {

                    //Fetch all infraobject presnt in Incidenttable.
                    var IncList = IncidentManagement.Where(x => dtinfra.Any(y => y.Id == x.InfraID));

                    if (IncList != null && IncList.Count() > 0)
                    {
                        var MinDate = (from u in IncList where u.Status == 1 orderby u.IncidentTime descending select u).FirstOrDefault();
                        TimeSpan diff = DateTime.Now - MinDate.IncidentTime;
                        string hours = diff.Days + "." + diff.Hours + ":" + diff.Minutes + ":" + diff.Seconds;
                        ServiceDetail.ServiceName = GroupServiceName;
                        ServiceDetail.ServiceId = GroupServiceId;
                        ServiceDetail.ImpactTime = MinDate.IncidentTime.ToString();
                        ServiceDetail.ImpactDownTime = hours.ToString();
                        ServiceDetail.ActiveService = "servicediv";
                        ServiceDetail.OpenIncidentCount = IncList.Count();
                        if (MostImpactedId != 0)
                        {
                           
                            var RelImpactType = from a in INfraImpactType where a.Id == MostImpactedId select a;

                            if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "T")
                            {

                                ServiceDetail.ImpactType = "Totally Impacted";
                                ServiceDetail.ImpactColor = "servicehead border7px-red";
                                ServiceDetail.ImpactTypeId = MostImpactedId;
                            }
                            else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "M")
                            {
                                ServiceDetail.ImpactType = "Majorly Impacted";
                                ServiceDetail.ImpactColor = "servicehead border7px-orange";
                                ServiceDetail.ImpactTypeId = MostImpactedId;
                            }
                            else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "P")
                            {
                                ServiceDetail.ImpactType = "Partially Impacted";
                                ServiceDetail.ImpactColor = "servicehead border7px-yellow";
                                ServiceDetail.ImpactTypeId = MostImpactedId;
                            }

                        }
                        else if (MostImpactedId == 0)
                        {
                            ServiceDetail.ImpactType = "No Impacted";
                            ServiceDetail.ImpactColor = "servicehead border7px-green";
                            ServiceDetail.ImpactTypeId = MostImpactedId;
                        }
                        if (!lstServicesNew.Contains(ServiceDetail) && ServiceDetail.ServiceName != null)
                            lstServicesNew.Add(ServiceDetail);

                    }
                    else
                    {
                        SetImpactedeatils();
                    }

                }
                else
                    SetImpactedeatils();
            }
            else
            {
                SetImpactedeatils();

            }


        }

        public static void SetImpactedeatils()
        {
            ServiceDetails ServiceDetail = new ServiceDetails();

            ServiceDetail.ServiceName = GroupServiceName;
            ServiceDetail.ServiceId = GroupServiceId;
            ServiceDetail.ImpactTime = "0";
            ServiceDetail.ImpactDownTime = "0";
            ServiceDetail.ImpactType = "NA";
            ServiceDetail.ImpactColor = "servicehead border7px-green";
            ServiceDetail.ImpactTypeId = 0;
            ServiceDetail.ActiveService = "servicediv";
            ServiceDetail.OpenIncidentCount = 0;
            if (!lstServicesNew.Contains(ServiceDetail) && ServiceDetail.ServiceName != null)
                lstServicesNew.Add(ServiceDetail);

        }

        /// <summary>
        /// Get Bs to Bs incident Rules from  incidentTable.
        /// </summary>
        /// <returns>datatable<string></returns>
        /// <author>Suryaji shinde</author>
        /// 
        public static DataTable GetBstoBsImpactedDeatils(DataTable AllBsToBSRule, int GroupServiceID)
        {

            try
            {
                string IsimpactedType = string.Empty;

                var AllincedentSummary = facade.GetAllIncidentManagementSummaryBstoBs();

                if (AllincedentSummary != null && AllincedentSummary.Count() > 0)
                {

                    var Allincedent = facade.GetAllIncidentManagementNew();
                    if (Allincedent != null && Allincedent.Count() > 0)
                    {

                        var openIncedent = from a in Allincedent where a.Status == 1 select a;

                        if (openIncedent != null && openIncedent.Count() > 0)
                        {

                            var openIncedentSummaryByIncid = from a in AllincedentSummary
                                                             join b in openIncedent
                                                             on a.IncidentID equals b.Id
                                                             where (b.Id == a.IncidentID) & a.GroupServiceId == GroupServiceID
                                                             select a;

                            if (openIncedentSummaryByIncid != null && openIncedentSummaryByIncid.Count() > 0)
                            {

                                foreach (var items in openIncedentSummaryByIncid)
                                {

                                    AllBsToBSRule.Rows.Add(items.IncidentID, items.ParentBSID, items.ParentBSImpactID, items.ChildBSID, items.ChildBSImpactID, "I" + items.BSIndexId);
                                }

                            }

                        }
                        else
                        {
                            IsimpactedType = "";
                        }
                    }

                }

                return AllBsToBSRule;
            }
            catch (Exception ex)
            {
                var bcms = new CpException(CpExceptionType.CommonUnhandled, " ", ex);
                _logger.Error("Exception Occured while populating GetBstoBsImpactedDeatils method in ServiceMonitor.cs:" + ex.Message);
                ExceptionManager.Manage(bcms);
                return AllBsToBSRule;

            }
            //return AllBsToBSRule.DefaultView.ToTable(true, "IncidentID", "ParentBSID", "ParentBSImpactedID", "ChildBSID", "ChildBSImpactedID");

        }



        /// <summary>
        /// Fetch Parent of ParentId from Node
        /// </summary>
        /// <returns>int</returns>
        /// <author>Suryaji shinde</author>
        /// 
        public static string GetParentIDFromDatatable(DataRow dr, DataTable dt, string Ruletype)
        {

            string ParentNodeID = string.Empty;
            DataTable dt_S = new DataTable();
            string ParentNode = string.Empty;
            string ParentID = string.Empty;


            string tem = dr["BsIndexId"].ToString() + "^";
            if (tem.Equals("I1^"))
                return "I1^$0";


            IEnumerable<DataRow> drFindParent = (from row in dt.AsEnumerable() where row.Field<string>("ParentNode").ToString().Contains(tem) select row).ToList();

            if (drFindParent != null && drFindParent.Count() > 0)
            {

                var result = (from row in dt.AsEnumerable() where row.Field<int>("ID") == Convert.ToInt32(drFindParent.ToList()[0].ItemArray[4]) select row).ToList();
                if (result != null && result.Count() > 0)
                {
                    var str = result[0].ItemArray[1].ToString();
                    if (!string.IsNullOrEmpty(str))
                    {
                        if (str.Contains('@'))

                            ParentID = tem + "$" + str.Split('@')[2].Split('$')[0].Split('_')[1].ToString();
                        else
                            ParentID = tem + "$" + str.Split('_')[1].Split('$')[0].ToString();
                    }
                }
            }

            return ParentID;

        }

        /// <summary>
        /// Create Dynamic node from infraRule
        /// </summary>
        /// <returns>string</returns>
        /// <author>Suryaji shinde</author>

        public static string GetDynamicNodeFromRule(string inFraID, DataRow dr, DataTable dt, string RuleType)
        {
            string DynamicNode = string.Empty;
            int ParentBSid = 0;


            string[] indexId = { };
            string ParentNodeId;
            string isNOdeINdex = string.Empty;
            if (RuleType.ToLower().Trim() == "bstobs")
            {
                ParentNodeId = GetParentIDFromDatatable(dr, dt, RuleType);

                indexId = ParentNodeId.Split('$');
            }

            int ispar = indexId.Length > 0 ? Convert.ToInt32(indexId[1]) : 0;
            isNOdeINdex = indexId.Length > 0 ? indexId[0] : "";
            ParentBSid = RuleType.ToLower().Trim() == "bstobs" ? ispar : Convert.ToInt32(dr["ImpactedInfraBs"].ToString());



            switch (inFraID)
            {

                case "S":

                    if (!string.IsNullOrEmpty(dr["ChildBSID"].ToString()))
                    {
                        var BSName = facade.GetBusinessServiceById(RuleType.ToLower().Trim() == "bstobs" ? Convert.ToInt32(dr["ParentBSID"].ToString()) : Convert.ToInt32(dr["ChildBSID"].ToString()));
                        var ParentBSName = ParentBSid != 0 ? facade.GetBusinessServiceById(ParentBSid) : null;
                        var INfraImpactType = facade.GetAllImpactRelType();

                        int ImpactID = RuleType.ToLower().Trim() == "bstobs" ? Convert.ToInt32(dr["ParentBSImpactedID"].ToString()) : Convert.ToInt32(dr["BSImpactedID"].ToString());
                        var RelImpactType = from a in INfraImpactType where a.Id == ImpactID select a;

                        if (RelImpactType != null && RelImpactType.Count() > 0)
                        {

                            string IMType = string.Empty;
                            if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "T")
                            {
                                IMType = "TIBS";
                            }
                            else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "M")
                            {
                                IMType = "MIBS";
                            }
                            else if (RelImpactType.FirstOrDefault().RelTypeValue.Trim() == "P")
                            {
                                IMType = "PIBS";
                            }
                            string PBSid = RuleType.ToLower().Trim() == "bstobs" ? dr["ParentBSID"].ToString() : dr["ChildBSID"].ToString();
                            DynamicNode = ParentBSid != 0 ? "S_" + ParentBSid.ToString() + "@" + ParentBSName.Name + "@" + "S_" + PBSid.Trim() + "$" + BSName.Name + "/" + IMType : "S_" + PBSid.Trim() + "$" + BSName.Name + "/" + IMType;
                            DynamicNode = isNOdeINdex + DynamicNode;
                        }
                    }

                    break;
            }

            return DynamicNode.Trim();
        }
        /// <summary>
        /// Get rule Effective Json from Datatable
        /// </summary>
        /// <returns>String</returns>
        /// <author>Suryaji shinde</author>
        /// 
        public static string CreateJsonFromDataTable(DataTable dt)
        {

            string NewInputString = string.Empty;
            string ChildNodeStr = string.Empty;
            string ParentNode = string.Empty;

            if (dt.Rows.Count > 0)
            {

                foreach (DataRow rows in dt.Rows)
                {

                    NewInputString += rows["ParentNode"].ToString().Trim() + ":" + rows["ChildNode"].ToString().Trim() + ";";

                }

            }
            return NewInputString;

        }

        public static string GetJsonfromImpactRule(DataTable dt, int GroupServiceId)
        {
            #region Used Varible's for Rule Logic

            string NewInputString = string.Empty;
            string NewChildNodeStr = string.Empty;
            string NewParentNodeStr = string.Empty;
            string ChildStr = string.Empty; string AppType = string.Empty;

            DataTable AllBsToBSRule = new DataTable();

            AllBsToBSRule.Columns.Add("IncidentID", typeof(string));
            AllBsToBSRule.Columns.Add("ParentBSID", typeof(string));
            AllBsToBSRule.Columns.Add("ParentBSImpactedID", typeof(string));
            AllBsToBSRule.Columns.Add("ChildBSID", typeof(string));
            AllBsToBSRule.Columns.Add("ChildBSImpactedID", typeof(string));
            AllBsToBSRule.Columns.Add("BsIndexId", typeof(string));


            #endregion Used Varible's for Rule Logic

            #region Fetch BS to BS Rules from incident Table


            AllBsToBSRule = GetBstoBsImpactedDeatils(AllBsToBSRule, GroupServiceId);


            #endregion


            #region Apply Bs to Bs Rule.

            string DynamicParentBSNode = string.Empty;
            string[] DynamicBStoBSNodeArray = { };
            int ParentNodeId;

            if (AllBsToBSRule != null && AllBsToBSRule.Rows.Count > 0)
            {

                foreach (DataRow drRules in AllBsToBSRule.Rows)
                {

                    DynamicParentBSNode = GetDynamicNodeFromRule("S", drRules, dt, "BStoBS");
                    int CharPBSCount = DynamicParentBSNode.Count(c => c.Equals('/'));
                    if (CharPBSCount > 0)
                    {
                        DynamicBStoBSNodeArray = DynamicParentBSNode.Split('/');
                    }
                    string item = DynamicBStoBSNodeArray[0].ToString().Trim();

                    DataRow drParent = dt.AsEnumerable().FirstOrDefault(r => r.Field<string>("ParentNode").ToString().Contains(item));
                    string targetNode = drParent["ParentNode"].ToString();
                    DynamicParentBSNode = GetMostEffectiveRuleNode(targetNode.Trim(), DynamicParentBSNode.Trim());
                    if (drParent != null)
                    {
                        ParentNodeId = Convert.ToInt32(drParent["ParentNodeID"].ToString());
                        if (ParentNodeId.Equals(0))
                        {
                            drParent["ParentNode"] = DynamicParentBSNode;

                        }
                        else
                        {
                            drParent["ParentNode"] = DynamicParentBSNode;
                            DataRow drChild = dt.AsEnumerable().FirstOrDefault(r => r.Field<int>("ID") == ParentNodeId);
                            if (drChild != null)
                            {
                                var ChildArr = drChild["ChildNode"].ToString().Split(',');
                                string temChild = string.Empty;
                                foreach (var itemVal in ChildArr)
                                {
                                    if (itemVal.Equals(targetNode))
                                        temChild += DynamicParentBSNode + ",";
                                    else
                                        temChild += itemVal + ",";
                                }
                                temChild = temChild.Substring(0, temChild.Length - 1);
                                drChild["ChildNode"] = temChild;
                            }

                        }

                    }


                }

            }


            #endregion Apply Bs to Bs Rule.



            #region Convert Datatable to json

            if (dt != null && dt.Rows.Count > 0)
            {
                NewInputString = CreateJsonFromDataTable(dt);
            }


            #endregion Convert Datatable to json

            return NewInputString;


        }
        /// <summary>
        /// Create Dynamic Most Effective rule
        /// </summary>
        /// <returns>string</returns>
        /// <author>Suryaji shinde</author>
        /// 
        public static string GetMostEffectiveRuleNode(string OldNode, string NewNode)
        {

            string Nodeval = string.Empty; string[] OldNodeArray = { };
            string[] NewNodeArray = { };
            int CharFCount = OldNode.Count(c => c.Equals('/'));
            if (CharFCount > 0)
            {
                OldNodeArray = OldNode.Split('/');
            }
            int CharNCount = NewNode.Count(c => c.Equals('/'));
            if (CharNCount > 0)
            {
                NewNodeArray = NewNode.Split('/');
            }
            if (!string.IsNullOrEmpty(OldNodeArray[1].ToString().Trim()) && !string.IsNullOrEmpty(NewNodeArray[1].ToString().Trim()))
            {
                if (OldNodeArray[1].ToString().Trim().Equals("NABS") || OldNodeArray[1].ToString().Trim().Equals("NABF"))
                {
                    Nodeval = NewNode.ToString().Trim();
                }
                else if (OldNodeArray[1].ToString().Trim().Equals("PIBS") && NewNodeArray[1].ToString().Trim().Equals("MIBS"))
                {
                    Nodeval = NewNodeArray[0] + "/MIBS";

                }
                else if (OldNodeArray[1].ToString().Trim().Equals("PIBF") && NewNodeArray[1].ToString().Trim().Equals("MIBF"))
                {
                    Nodeval = NewNodeArray[0] + "/MIBF";

                }
                else if (OldNodeArray[1].ToString().Trim().Equals("MIBS") && NewNodeArray[1].ToString().Trim().Equals("TIBS"))
                {
                    Nodeval = NewNodeArray[0] + "/TIBS";

                }
                else if (OldNodeArray[1].ToString().Trim().Equals("MIBF") && NewNodeArray[1].ToString().Trim().Equals("TIBF"))
                {
                    Nodeval = NewNodeArray[0] + "/TIBF";

                }
                else if (OldNodeArray[1].ToString().Trim().Equals("PIBS") && NewNodeArray[1].ToString().Trim().Equals("TIBS"))
                {
                    Nodeval = NewNodeArray[0] + "/TIBS";

                }
                else if (OldNodeArray[1].ToString().Trim().Equals("PIBF") && NewNodeArray[1].ToString().Trim().Equals("TIBF"))
                {
                    Nodeval = NewNodeArray[0] + "/TIBF";

                }
                else
                {
                    Nodeval = NewNode;

                }
            }
            return Nodeval;
        }

        public static void ClearAll()
        {
            //businessFunctionList = null;
            //infraObjects = null;
            //buildNodeRelSb = null;
            //businessService = null;
            //businessFunctionsTable = null;
            //infraObjectTable = null;
            //businessFunctionObj = null;
            currentNode = 0;
            first = true;
            firstSplitCount = 0;
            hasChild = false;
            jsonNodeRelSb = null;
            //inputString = string.Empty;
            //jsonString = string.Empty;
            NewjsonString = string.Empty;
        }

        public static bool HasChild(string inputString, string thirdSplitStr1)
        {
            hasChild = false;
            string sixthSplitStr = string.Empty;
            string[] fourthSplitStr = inputString.Split(';');
            int fourthSplitCount = fourthSplitStr.Length;
            for (int p = 0; p < fourthSplitCount; p++)
            {
                string[] fifthSplitStr = fourthSplitStr[p].Split(':');
                sixthSplitStr = fifthSplitStr[0];
                if (sixthSplitStr == thirdSplitStr1)
                {
                    hasChild = true;
                    break;
                }
            }
            return hasChild;
        }

        public static string GetJsonString(string inputStr1)
        {
            if (jsonNodeRelSb == null)
                jsonNodeRelSb = new StringBuilder();
            Random randomNumber = new Random();


            string[] firstSplitStr = inputStr1.Split(';');
            if (firstSplitStr.Length <= 1)
            {
                jsonNodeRelSb.Append("{");
                if (firstSplitStr[0].Contains("PIBS"))
                {
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + firstSplitStr[0].Substring(0, (firstSplitStr[0].IndexOf("/"))) + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PIBS" + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");

                }
                else if (firstSplitStr[0].Contains("MIBS"))
                {
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + firstSplitStr[0].Substring(0, (firstSplitStr[0].IndexOf("/"))) + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MIBS" + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                }
                else if (firstSplitStr[0].Contains("TIBS"))
                {
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + firstSplitStr[0].Substring(0, (firstSplitStr[0].IndexOf("/"))) + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIBS" + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");

                }
                else if (firstSplitStr[0].Contains("NABS"))
                {
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + firstSplitStr[0].Substring(0, (firstSplitStr[0].IndexOf("/"))) + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NABS" + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");

                }


                jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
            }
            string[] secSplitStr = firstSplitStr[currentNode].Contains(':') ? firstSplitStr[currentNode].Split(':') : new string[] { };
            string[] thirdSplitStr = secSplitStr.Length > 1 ? secSplitStr[1].Split(',') : new string[] { };
            int actualChildCount = thirdSplitStr.Length;
            firstSplitCount = firstSplitStr.Length;
            string secSplitStrArry = string.Empty;

            if (currentNode < firstSplitCount - 1)
            {
                currentNode = currentNode + 1;
            }
            for (int j = 0; j < actualChildCount; j++)
            {
                if (first)
                {
                    secSplitStrArry = string.Empty;
                    jsonNodeRelSb.Append("{");
                    if (secSplitStr[0].Contains('/'))
                    {

                        secSplitStrArry = secSplitStr[0].Substring(secSplitStr[0].LastIndexOf('/') + 1);
                    }
                    else
                    {
                        secSplitStrArry = secSplitStr[0];
                    }
                    if (secSplitStrArry.Equals("PIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("MIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NABS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NABS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("PIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("MIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NABF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NABF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("PII"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("MII"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TII"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAI"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAI" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIICSV"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICSV" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAICSV"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICSV" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIICD"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICD" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAICD"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICD" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIICR"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICR" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAICR"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICR" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIIS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIIS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAIS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + secSplitStr[0].Substring(0, (secSplitStr[0].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAIS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                    }

                    jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");
                    first = false;
                }

                if (HasChild(inputStr1, thirdSplitStr[j]))
                {
                    jsonNodeRelSb.Append("{");
                    secSplitStrArry = string.Empty;
                    if (thirdSplitStr[j].Contains('/'))
                    {
                        secSplitStrArry = thirdSplitStr[j].Substring(thirdSplitStr[j].LastIndexOf('/') + 1);

                    }
                    else
                    {
                        secSplitStrArry = thirdSplitStr[j];
                    }
                    if (secSplitStrArry.Equals("PIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + hide + "\"", "\"" + hide + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");


                    }
                    else if (secSplitStrArry.Equals("MIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + hide + "\"", "\"" + hide + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("TIBS"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("NABS"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NABS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("PIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + hide + "\"", "\"" + hide + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("MIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + hide + "\"", "\"" + hide + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("TIBF"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("NABF"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NABF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("PII"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + hide + "\"", "\"" + hide + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("MII"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + hide + "\"", "\"" + hide + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("TII"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("NAI"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAI" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("TIICSV"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICSV" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("NAICSV"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICSV" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("TIICD"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICD" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("NAICD"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICD" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("TIICR"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICR" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("NAICR"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICR" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("TIIS"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIIS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }
                    else if (secSplitStrArry.Equals("NAIS"))
                    {

                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAIS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + childStr + "\"", "[");

                    }

                    GetJsonString(inputStr1);

                    jsonNodeRelSb.Append("]");
                    jsonNodeRelSb.Append("}" + ",");
                }
                else
                {
                    jsonNodeRelSb.Append("{");
                    secSplitStrArry = string.Empty;

                    //jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                    //jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NoImpactCWR" + "\"" + ",");
                    //jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRedStr + "\"" + ",");
                    if (thirdSplitStr[j].Contains('/'))
                    {
                        secSplitStrArry = thirdSplitStr[j].Substring(thirdSplitStr[j].LastIndexOf('/') + 1);
                    }
                    else
                    {
                        secSplitStrArry = thirdSplitStr[j];
                    }

                    if (secSplitStrArry.Equals("PIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("MIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIBS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIBS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NABS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NABS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("PIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("MIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIBF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIBF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NABF"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NABF" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("PII"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "PII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorYellow + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("MII"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "MII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorOrange + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TII"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TII" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAI"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAI" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIICSV"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICSV" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAICSV"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICSV" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIICD"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICD" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAICD"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICD" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIICR"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIICR" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAICR"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAICR" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("TIIS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "TIIS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorRed + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    else if (secSplitStrArry.Equals("NAIS"))
                    {
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + nameStr + "\"", "\"" + thirdSplitStr[j].Substring(0, (thirdSplitStr[j].IndexOf("/"))) + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + ImpactType + "\"", "\"" + "NAIS" + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + levelStr + "\"", "\"" + colorBlue + "\"" + ",");
                        jsonNodeRelSb.AppendFormat("{0}:{1}", "\"" + logo + "\"", "\"" + "" + "\"" + ",");
                    }
                    jsonNodeRelSb.Append("}");
                    if (j != actualChildCount - 1)
                        jsonNodeRelSb.Append(",");
                }
            }

            return Convert.ToString(jsonNodeRelSb);
        }

        public static string GetJSONFromString(string newJsonString)
        {
            ClearAll();

            if (newJsonString.IndexOf(";;") >= 0)
                newJsonString = newJsonString.Substring(0, newJsonString.Length - 1);
            if (!string.IsNullOrEmpty(newJsonString))
            {
                NewjsonString = GetJsonString(newJsonString);
                NewjsonString = NewjsonString + "]" + "}";
                NewjsonString = NewjsonString.Replace("],", "]");
            }
            if (!string.IsNullOrEmpty(NewjsonString))
                return NewjsonString + '#' + newJsonString;
            else
                return string.Empty;
        }

        public static string GetCurrentTime(string name)
        {
            return "Hello " + name + Environment.NewLine + "The Current Time is: "
                    + DateTime.Now.ToString();
        }
        #endregion



        //protected void Button1_Click(object sender, EventArgs e)
        //{
        //    //hdnActiveServiceId
        //    List<ServiceDetails> lstService = new List<ServiceDetails>(); ;
        //    listServices.OrderByDescending(a => a.ImpactTypeId);
        //    if (listServices != null && listServices.Count > 0 && !string.IsNullOrEmpty(hdnActiveServiceId.Value))
        //    {
        //        foreach (ServiceDetails Services in listServices)
        //        {
        //            if (Services.Id.ToString() == hdnActiveServiceId.Value)
        //                Services.ActiveService = "servicediv active";
        //            else
        //                Services.ActiveService = "servicediv";

        //            lstService.Add(Services);
        //        }
        //    }
        //    else
        //    {
        //        foreach (ServiceDetails Services in listServices)
        //        {
        //            lstService.Add(Services);
        //        }
        //    }

        //    rptServiceMonitor.DataSource = lstService;
        //    rptServiceMonitor.DataBind();
        //    updmain.Update();
        //}

    }

    public class ServiceDetails
    {
        public int Id { get; set; }
        public int ServiceId { get; set; }
        public string ServiceName { get; set; }
        public string ImpactType { get; set; }
        public string ImpactTime { get; set; }
        public string ImpactDownTime { get; set; }
        public string ImpactColor { get; set; }
        public int ImpactTypeId { get; set; }
        public string ActiveService { get; set; }
        public int OpenIncidentCount { get; set; }
        public int INfraId { get; set; }
        public string IncidentCommit { get; set; }
        public string InfraComponentType { get; set; }


    }
    public class InfraList
    {
        public int Id { get; set; }
        public int GroupServiceID { get; set; }
        public int ProfileID { get; set; }

    }
    public class IncidentAlertDetails
    {
        public int Id { get; set; }
        public string INCIDENTNAME { get; set; }
        public string INCIDENTTIME { get; set; }
        public string INCIDENTRECOVERYTIME { get; set; }
        public int STATUS { get; set; }
        public string INFRAID { get; set; }
        public int INFRACOMPONENTID { get; set; }
        public string INFRACOMPONENTTYPE { get; set; }
        public string INCIDENTCOMMENT { get; set; }
        public string APPPROCESS { get; set; }
        public string BusinessService { get; set; }
        public string IPAddress { get; set; }
        public string InfraObjectName { get; set; }
    }
}
