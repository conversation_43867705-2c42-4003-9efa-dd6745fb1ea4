﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ImpactMaster.aspx.cs" Inherits="CP.UI.ImpactAnalysis.ImpactMaster" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc1" %>
<html>
<head id="Head1" runat="server">
    <title>Continuity Patrol :: Manage Impact Category To Impact Type Relationship</title>
    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.simple-dtpicker.css" type="text/css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery.timepicker.css" type="text/css" rel="stylesheet" />
    <script src="../Script/jquery.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <style type="text/css">
        td, tr, .dynamicTable.tableTools.table.table-striped.table-bordered.table-condensed.table-white span {
            font-size: 13px !important;
        }

        .control-label {
            font-size: 12px !important;
        }

        .form-control, .col-xs-8.padding-none-LR select {
            padding: 4px;
            width: 85%;
        }
    </style>
    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</head>
<body>
    <form id="form1" runat="server">

        <asp:ScriptManager ID="ScriptManager1" runat="server" ScriptMode="Release">
        </asp:ScriptManager>
        <script type="text/javascript">
            var xPos, yPos;
            Sys.WebForms.PageRequestManager.getInstance().add_beginRequest(BeginRequestHandler);
            Sys.WebForms.PageRequestManager.getInstance().add_endRequest(EndRequestHandler);
            function BeginRequestHandler(sender, args) {
                if ($("#SwitchScrollbar").length > 0) {
                    xPos = window.$get('SwitchScrollbar').scrollLeft;
                    yPos = window.$get('SwitchScrollbar').scrollTop;
                }
            }
            function EndRequestHandler(sender, args) {
                if ($("#SwitchScrollbar").length > 0) {
                    window.$get('SwitchScrollbar').scrollLeft = xPos;
                    window.$get('SwitchScrollbar').scrollTop = yPos;
                }
            }
        </script>
        <div class="innerLR innerT" style="background-color: #fff;">
            <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                <ContentTemplate>
                    <div class="col-xs-12 form-horizontal uniformjs">

                        <div class="form-group">

                            <div class="col-md-6">

                                <label class="control-label col-xs-4 padding-none-LR">Selected Impact Category</label>
                                <div class="col-xs-8 padding-none-LR">
                                    <asp:DropDownList runat="server" ID="ddlimpacttypemaster" TabIndex="1"></asp:DropDownList><br />
                                    <asp:RequiredFieldValidator ID="rfvddlSite" runat="server" CssClass="error" ControlToValidate="ddlimpacttypemaster"
                                        Display="Dynamic" ErrorMessage="Select Impact Category Name" InitialValue="0"></asp:RequiredFieldValidator>
                                </div>

                            </div>
                            <div class="col-md-6">

                                <label class="control-label col-xs-4 padding-none-LR">Add New Impact Type</label>
                                <div class="col-xs-8 padding-none-LR">
                                    <asp:TextBox ID="txtimpactname" runat="server" class="form-control" AutoPostBack="False" TabIndex="2" autocomplete="off"></asp:TextBox>
                                    <asp:Label ID="lblName" runat="server" ForeColor="Red" Text=""></asp:Label><br />
                                    <asp:RequiredFieldValidator ID="rfvimpactdetail" runat="server" CssClass="error" ErrorMessage="Enter Impact Name"
                                        ControlToValidate="txtimpactname" Display="Dynamic"></asp:RequiredFieldValidator>
                                     <asp:RegularExpressionValidator ID="RegularExpressionValidator1" CssClass="error" ControlToValidate="txtimpactname" Display="Dynamic"
                                                            runat="server" ErrorMessage="Enter AlphaNumeric Value Only"
                                                            ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9_ ]+$"></asp:RegularExpressionValidator>  
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-md-6">
                                <label class="control-label col-xs-4 padding-none-LR">Add Impact Type Description</label>
                                <div class="col-xs-8 padding-none-LR">
                                    <asp:TextBox ID="txtpmpactmasdetail" runat="server" class="form-control" AutoPostBack="False" TabIndex="3" autocomplete="off"></asp:TextBox>
                                    <asp:Label ID="lbldetail" runat="server" ForeColor="Red" Text=""></asp:Label><br />
                                    <asp:RequiredFieldValidator ID="rfvimpmasdetail" runat="server" CssClass="error" ErrorMessage="Enter Impact Description"
                                        ControlToValidate="txtpmpactmasdetail" Display="Dynamic"></asp:RequiredFieldValidator>
                                     <asp:RegularExpressionValidator ID="rfvtxtDesc" CssClass="error" ControlToValidate="txtpmpactmasdetail" Display="Dynamic"
                                                            runat="server" ErrorMessage="Enter AlphaNumeric Value Only"
                                                            ValidationExpression="^(?![0-9]*$)[a-zA-Z0-9_ ]+$"></asp:RegularExpressionValidator>     

                                </div>
                            </div>


                            <div class="col-md-6">

                                <label class="control-label col-xs-4 padding-none-LR">Select Display Sequence</label>
                                <div class="col-xs-8 padding-none-LR">
                                    <asp:DropDownList runat="server" ID="ddlsequence" TabIndex="4"></asp:DropDownList><br />
                                    <asp:RequiredFieldValidator ID="rfvsequence" runat="server" CssClass="error" ControlToValidate="ddlsequence"
                                        Display="Dynamic" ErrorMessage="Select Order" InitialValue="0"></asp:RequiredFieldValidator>


                                </div>

                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-xs-12 padding-none-LR text-right" style="padding-right: 68px !important;">
                                 <asp:Label ID="lblMessage" runat="server" ForeColor="Green"></asp:Label>
                                <asp:Button ID="btnSave" CssClass="btn btn-primary" runat="server" Text="Save" Width="8%" TabIndex="5" OnClick="btnSave_Click" />
                                <asp:Button ID="btnCancel" CssClass="btn btn-default" runat="server" Text="Cancel" Width="6%" CausesValidation="False" TabIndex="6" OnClick="btnCancel_Click" />
                            </div>
                        </div>


                   <%--     <div class="row">
                            <asp:Label ID="lblMessage" CssClass="label label-danger col-xs-12" Text="" runat="server"></asp:Label>

                        </div>--%>
                       
                        <div class="row">
                            <asp:ListView ID="lvComponent" runat="server" OnItemEditing="LvComponentItemEditing" OnPreRender="LvComponentPreRender"
                                DataKeyNames="Id" OnItemDeleting="LvComponentItemDeleting" OnPagePropertiesChanging="lvComponent_PagePropertiesChanging">
                                <LayoutTemplate>
                                    <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white">
                                        <thead>
                                            <tr>
                                                <th style="width: 4%;">
                                                    <span>
                                                        <img src="../Images/icons/server1.png" /></span>
                                                </th>
                                                <th>ImpactType
                                                </th>
                                                <th>ImpactTypeDescription
                                                </th>
                                                <th>ImpactCategory
                                                </th>
                                                <th>DisplaySequence
                                                </th>
                                                <th class="text-center">Action
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                        </tbody>
                                    </table>
                                </LayoutTemplate>
                                <EmptyDataTemplate>
                                    <div class="message warning align-center bold no-bottom-margin">
                                        <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                                    </div>
                                </EmptyDataTemplate>
                                <ItemTemplate>
                                    <tr>
                                        <td>
                                            <%#Container.DataItemIndex+1 %>
                                        </td>
                                        <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                        <td>
                                            <asp:Label ID="ipmName" runat="server" Text='<%# Eval("ImpactName") %>' />
                                        </td>
                                        <td>
                                            <asp:Label ID="ImpDescription" runat="server" Text='<%# Eval("ImpactDescription") %>' />
                                        </td>
                                        <td>
                                            <asp:Label ID="ImpCategory" runat="server" Text='<%# GetCategoryName(Eval("ImpactTypeID")) %>' />
                                        </td>
                                        <td>
                                            <asp:Label ID="Impactorder" runat="server" Text='<%# Eval("ImpactOrder") %>' />
                                        </td>
                                        <td class="text-center">
                                            <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" ToolTip="Edit" ImageUrl="../images/icons/pencil.png"
                                                ValidationGroup="IDT" CausesValidation="false" />
                                            <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" ToolTip="Delete"
                                                ImageUrl="../images/icons/cross-circle.png" ValidationGroup="IDT" CausesValidation="true" />
                                        </td>
                                        <cc1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" ConfirmText='<%# "Are you sure want to delete " + Eval("ImpactName") + " ? " %>'
                                            TargetControlID="ImgDelete" OnClientCancel="CancelClick">
                                        </cc1:ConfirmButtonExtender>
                                    </tr>
                                </ItemTemplate>
                            </asp:ListView>

                            <div class="row form-group">
                                <div class="col-md-6">
                                    <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvComponent">
                                        <Fields>
                                            <asp:TemplatePagerField>
                                                <PagerTemplate>
                                                    <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                    Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                    <br />
                                                </PagerTemplate>
                                            </asp:TemplatePagerField>
                                        </Fields>
                                    </asp:DataPager>
                                </div>
                                <div class="col-md-6 text-right">
                                    <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvComponent" PageSize="5">
                                        <Fields>
                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                                ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                            <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                                NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                                NumericButtonCssClass="btn-pagination" />
                                            <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                                ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                        </Fields>
                                    </asp:DataPager>
                                </div>
                            </div>
                        </div>

                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>

    </form>
    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/jquery.mCustomScrollbar.concat.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>
    <script src="../Script/jquery.simple-dtpicker.js" type="text/javascript"></script>
    <script src="../Script/jquery.timepicker.min.js" type="text/javascript"></script>
    <script src="../Script/BusinessFunctionRPOSpan.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();

            $(".infraScheduleScroll").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
                autoDraggerLength: false,
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });

        });
        function pageLoad() {
            $('[id$=txtDateTime]').appendDtpicker();
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
            $('input[type="checkbox"]').checkbox();
            radiobutton();

            $(".infraScheduleScroll").mCustomScrollbar({
                axis: "y",
                setHeight: "200px",
                autoDraggerLength: false,
                advanced: {
                    updateOnContentResize: true,
                    autoExpandHorizontalScroll: true
                }
            });
        }
    </script>


</body>
</html>
