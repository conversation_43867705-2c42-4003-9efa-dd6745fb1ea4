﻿<%@ Page Language="C#" MasterPageFile="~/Master/BcmsLogin.Master" AutoEventWireup="true"
    CodeBehind="Login.aspx.cs" Inherits="CP.UI.Login" Title="Welcome to ContinuityPatrol - LogIn"
    ViewStateEncryptionMode="Always" %>

<%@ Register Assembly="MSCaptcha" Namespace="MSCaptcha" TagPrefix="rsv" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <link href="App_Themes/CPTheme/style.css" rel="stylesheet" />
    <%--jquery.combobox/--%>
    <script src="Script/jquery.min.js"></script>
    <script src="Script/jquery-migrate.min.js"></script>
    <script src="Script/less.min.js"></script>
    <script src="Script/AlertModal.js"></script>
    <script src="Script/Custom-chkbox-rdbtn.js"></script>
    <script type="text/javascript" src="Script/jquery.combobox.js"></script>
    <script src="Script/Login.js" type="text/javascript"></script>

    <script src="Script/MaskedPassword.js"></script>
    <script src="Script/EncryptDecrypt.js"></script>
    <style type="text/css">
        .pull-left.innerB.margin-right > img {
            height: 33px;
            width: 165px;
        }

        .reset-icon {
            margin-left: 5px;
        }

        .widget {
            box-shadow: 0 0 0 0 #dbdbdb;
            margin: 0px;
        }

            .widget .widget-head {
                /*background-image:linear-gradient(to bottom, #fdfdfd, #f4f4f4);*/
                background-repeat: repeat-x;
                border-bottom: 1px solid #dbdbdb;
                border-radius: 5px 5px 0 0;
                height: 55px;
                line-height: 35px;
                overflow: hidden;
                padding: 0 15px 0 5px;
                position: relative;
                background-color: #d6e6f5; /*#e2f6fd;*/
                color: #333333;
            }

        #captch .widget-body {
            background-image: linear-gradient(to bottom, #fdfdfd, #f4f4f4);
            /*background-color:#f4f4f4;*/
        }

        .modal-header {
            padding: 0px !important;
        }

        .modal-body {
            padding: 10px !important;
            height: 80px !important;
        }
    </style>
    <script type="text/javascript">
        $(document).ready(function () {
            $('input[type="checkbox"]').checkbox();
            //$('[id$=ctl00_cphBody_chkLDap]').checkbox();
            //$('[id$=ctl00_cphBody_chkActiveDirectory]').checkbox();
            //$('[id$=ctl00_cphBody_chkSSOSaml]').checkbox();
            return true;
        });
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <asp:HiddenField ID="HDF_SessionTime" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <asp:HiddenField ID="hfDomainName" runat="server" />
    <asp:HiddenField ID="hfUserInfoId" runat="server" />
    <asp:HiddenField ID="hfLoginType" runat="server" />
    <ul id="divClass" runat="server" visible="false" class="notyfy_container i-am-new">
        <li class="notyfy_wrapper notyfy_error">
            <div id="notyfy_1155564924086075600" class="notyfy_bar">
                <div class="notyfy_message">
                    <span class="notyfy_text">
                        <strong>
                            <asp:Literal ID="FailureText" Text="Error message" runat="server" EnableViewState="false"></asp:Literal></strong>
                    </span>
                </div>
            </div>
        </li>
    </ul>
    <div id="modelbg" runat="server" visible="false" style="position: absolute; z-index: 1000; width: 100%; height: 100%; display: block; background-color: #0000009c;">
    </div>
    <div class="row margin-none loginPageBg" onkeypress="checkKey(event)">
        <div class="col-md-5 col-md-push-1">
            <div id="LoginUser" class="margin-top90">
                <div class="widget box-shadow-none border-none">
                    <div class="widget-body" style="margin-top: -30px">
                        <div class="row col-md-12 ">
                            <div class="col-md-7" style="text-align: left">
                                <img src="Images/ICICI_164.jpg" />
                            </div>

                            <div class="col-md-5" style="text-align: right">
                                <img src="Images/ContinuityPatrol_logo_black1.png" alt="Continuity Patrol" style="margin-top: 17px;">
                            </div>
                        </div>
                        <div class="clearfix"></div>
                        <div class="row col-md-12 ">
                            <div class="col-md-5 col-md-push-7" style="text-align: right">
                                <asp:Label ID="lblVersion" runat="server" Text=""></asp:Label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <div class="col-md-1">
                                    </div>
                                    <div class="col-md-11">
                                        <div class="input-icon left">
                                            <label>User Name</label>
                                            <span class="glyphicons user log-ext"><i></i></span>
                                            <input type="hidden" id="UserEncrypt" runat="server" />
                                            <asp:TextBox ID="UserName" runat="server" autocomplete="off" CssClass="form-control" placeholder="Username" onfocus="cleartext()" onblur="getUserNameHash(this)"></asp:TextBox>
                                            <asp:RequiredFieldValidator ID="UserNameRequired" runat="server" ControlToValidate="UserName" ErrorMessage="Required." ToolTip="User Name is required." CssClass="error"
                                                ValidationGroup="LoginUser"></asp:RequiredFieldValidator>
                                        </div>

                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="col-md-1">
                                    </div>
                                    <div class="col-md-11">
                                        <div class="input-icon left">
                                            <label>Password</label>
                                            <span class="glyphicons keys log-ext1"><i></i></span>
                                            <div>
                                                <input type="hidden" id="PassEncyptHidden" runat="server" />
                                                <input type="hidden" id="hdfStaticGuid" runat="server" />
                                                <asp:TextBox ID="Password" runat="server" EnableViewState="false" TextMode="Password" autocomplete="off" CssClass="form-control" placeholder="Password" ReadOnly="false" onblur="getPasswordHash(this)" onfocus="clearControlData(this)"></asp:TextBox>

                                                <asp:RequiredFieldValidator ID="PasswordRequired" runat="server" ControlToValidate="Password" ErrorMessage="Required." CssClass="error"
                                                    ToolTip="Password is required." ValidationGroup="LoginUser">

                                                </asp:RequiredFieldValidator>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlCaptcha" Visible="false" runat="server">
                                            <div class="form-group">
                                                <div class="col-md-1">
                                                </div>
                                                <div class="col-md-11">
                                                    <div class="widget" id="captch" style="width: 86%;">

                                                        <div class="widget-head">
                                                            Captcha Code  <%--<span class="inactive">*</span>--%>
                                                        </div>
                                                        <div class="widget-body">
                                                            <asp:TextBox ID="txtcaptcha" placeholder="Enter Captcha Code" runat="server" CssClass="form-control" Width="45%" Style="margin-right: 15px;"></asp:TextBox>
                                                            <div style="display: inline-block; vertical-align: super">
                                                                <rsv:CaptchaControl ID="captcha1" runat="server" CaptchaLength="5" CssClass="pull-left innerB  margin-right"
                                                                    CaptchaHeight="50" CaptchaWidth="200" CaptchaLineNoise="None" ForeColor="#00FFCC" CaptchaMinTimeout="5" CaptchaMaxTimeout="240"
                                                                    BackColor="White" CaptchaChars="ABCDEFGHIJKLNPQRTUVXYZ12346789"
                                                                    FontColor="Darkblue" />
                                                                <asp:LinkButton runat="server" ID="LinkButton1" CssClass="reset-icon margin" CausesValidation="false" ToolTip="Refresh" OnClick="lnkbtnCaptcha_Click" OnClientClick="generateCode()"></asp:LinkButton>
                                                            </div>


                                                            <asp:RequiredFieldValidator ID="rfvCaptcha" runat="server" ControlToValidate="txtcaptcha" CssClass="error"
                                                                ErrorMessage="Enter Captcha" Display="Dynamic"></asp:RequiredFieldValidator>
                                                            <asp:Label ID="lblerror" runat="server" CssClass="error" Text="" Visible="true"></asp:Label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>
                                <asp:Panel ID="pnlCmpvalue" runat="server" Visible="true">
                                    <div class="form-group">
                                        <div class="col-md-1">
                                        </div>
                                        <div class="col-md-11">
                                            <div class="input-icon left">
                                                <label>Company Name</label>
                                                <span class="glyphicons building log-ext2"><i></i></span>
                                                <div>
                                                    <asp:DropDownList ID="ddlCompanyId" runat="server"></asp:DropDownList>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </asp:Panel>

                                <asp:Panel ID="pnlcompvalue" runat="server" Visible="false">
                                    <div class="form-group">
                                        <div class="col-md-1">
                                        </div>
                                        <div class="col-md-11">
                                            <div class="input-icon left">
                                                <label>Company Id</label>
                                                <span class="glyphicons building log-ext-2"><i></i></span>
                                                <asp:TextBox ID="txtCompanyCode" runat="server" CssClass="form-control" placeholder="Company Id" autocomplete="off"></asp:TextBox>
                                                <%-- <div>
                                                <asp:DropDownList ID="ddlCompanyId" runat="server"></asp:DropDownList>
                                            </div>--%>
                                            </div>
                                        </div>
                                    </div>
                                </asp:Panel>

                                <div class="form-group">
                                    <div class="col-md-1">
                                    </div>
                                    <div class="col-md-6">
                                        <asp:CheckBox ID="chkActiveDirectory" runat="server" AutoPostBack="true" Checked="true" OnCheckedChanged="chkActiveDirectory_CheckedChanged" />

                                        <asp:Label ID="Label2" Text="Active Directory" runat="server" CssClass="padding padding-none-TB"></asp:Label>
                                    </div>
                                    <div class="col-md-5">
                                    </div>
                                </div>

                                <asp:Panel ID="pnlDomain" CssClass="form-group" runat="server" Visible="false">
                                    <div class="col-md-1">
                                    </div>
                                    <div class="col-md-11">
                                        <div class="input-icon left">
                                            <label>
                                                <asp:Label ID="lbldomainName" runat="server" Text="Domain" Visible="false"></asp:Label></label>
                                            <span class="glyphicons globe log-ext3"><i></i></span>
                                            <div>

                                                <%--<input id="combobox1" type="text"  text ="icicibankltd.com" runat="server" visible="false" placeholder="Enter Domain"/>  --%>
                                                <asp:TextBox ID="txtDomain" runat="server" CssClass="form-control" Visible="false"></asp:TextBox>

                                            </div>
                                        </div>

                                    </div>
                                </asp:Panel>
                                <%-- <asp:UpdatePanel ID="Updatepanel1" runat="server">
                                    <ContentTemplate>--%>
                                <asp:Panel ID="pnlOTP" runat="server" Width="100%" Visible="false">
                                    <div class="modal" style="display: block;">
                                        <div class="modal-dialog" style="width: 350px; margin-top: 200px;">
                                            <div class="modal-content  widget-body-white">
                                                <div class="modal-header">
                                                    <%--<P class="modal-title">Please Enter the OTP(One Time Password) sent to your Registered Mobile Number</P>--%>

                                                    <h4 style="margin-left: 5px; padding-left: 10px;">
                                                        <img src="../../Images/icon_white/otp-18.png">
                                                        Please Enter the OTP</h4>
                                                    <asp:LinkButton ID="Lkbtnclosesmtphost" runat="server" ToolTip="Close window" CausesValidation="False" class="close" CommandName="Close"
                                                        OnClick="Lkbtnclosesmtphost_Click" Style="position: absolute; right: 10px; display: block;">x</asp:LinkButton>
                                                </div>
                                                <div class="modal-body">
                                                    <asp:Label ID="lblsmtpsavemessage" runat="server" CssClass="padding" Visible="False" ForeColor="Green"></asp:Label>
                                                    <asp:UpdatePanel ID="UpdatePanel_smtphost" runat="server">
                                                        <ContentTemplate>
                                                            <div class="">
                                                                <div class="col-md-12 form-horizontal uniformjs">
                                                                    <div class="form-group">
                                                                        <%--   <label class="col-md-4 control-label ">
                                                                            <asp:Label ID="lblsmtp" runat="server" Text="Please Enter the OTP(One Time Password) sent to your Registered Mobile Number"></asp:Label>
                                                                            <span class="inactive">*</span>
                                                                        </label>--%>
                                                                        <div class="col-md-6" style="padding-left: 0px !important; padding-right: 0px !important;">
                                                                            <%--<asp:HiddenField ID="smtpId" runat="server" />--%>
                                                                            <%--<asp:TextBox ID="smtpHost" runat="server" autocomplete="off" CssClass="form-control" placeholder="Enter OTP" Width="72%"></asp:TextBox>--%>
                                                                           <asp:HiddenField ID="txtotpHidden" runat="server" />                                                                                                                                                                     
                                                                    <asp:TextBox ID="txtotp" runat="server" CssClass="form-control secure-input" placeholder="Enter OTP" Width="100%" EnableViewState="false" TextMode="Password" autocomplete="off" ReadOnly="false" onblur="encryptAndStoreOtp(this)" onfocus="clearControlData(this)"></asp:TextBox>
                                                                            <%--  <asp:RequiredFieldValidator ID="rfvsmtphost" runat="server" ErrorMessage="Please Enter Correct OTP" ControlToValidate="txtotp"
                                                                                Display="Dynamic" CssClass="error"></asp:RequiredFieldValidator>--%>
                                                                            <asp:Label ID="Blank_otp" runat="server" CssClass="padding" ForeColor="Red" Visible="true"></asp:Label>
                                                                            <asp:Label ID="IncorrectOTP" runat="server" CssClass="padding" ForeColor="Red" Visible="true"></asp:Label>
                                                                        </div>

                                                                        <div class="col-md-6">
                                                                            <asp:Button ID="btnSave" runat="server" Text="Validate OTP" CssClass="btn btn-primary" OnClick="btnsave_Click" Visible="True" UseSubmitBehavior="false" />

                                                                            <%--<asp:Label ID="IncorrectOTP" runat="server" CssClass="padding" ForeColor="Red" Visible ="true"></asp:Label>--%>

                                                                            <%-- <asp:Button ID="btnSave" runat="server" Text="Validate OTP" CssClass="btn btn-primary" OnClientClick="submitForm(); return false;" Visible="True" />--%>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </ContentTemplate>
                                                        <Triggers>
                                                            <%--<asp:AsyncPostBackTrigger ControlID="btnSave" EventName="Click" />--%>
                                                            <asp:PostBackTrigger ControlID="btnSave" />
                                                            <%--<asp:AsyncPostBackTrigger ControlID="txtotp" />--%>
                                                        </Triggers>
                                                    </asp:UpdatePanel>
                                                </div>

                                            </div>
                                        </div>
                                    </div>

                                </asp:Panel>
                                <%-- </ContentTemplate>
                                        </asp:UpdatePanel>--%>
                                <div class="form-group col-md-12">
                                    <div class="col-md-6">
                                        <asp:Button ID="btnLoginSSO" UseSubmitBehavior="false" runat="server" Text="SAML 2.0 Login" Style="margin-left: 48px !important;" Width="68%" OnClick="btnLoginSSO_Click" CssClass="btn btn-block btn-inverse" />
                                    </div>

                                    <div class="col-md-6">

                                        <asp:Button ID="LoginButton"  UseSubmitBehavior="true" runat="server" CommandName="Login" Style="margin-left: 55px !important;" Width="68%" CssClass="btn btn-block btn-inverse" Text="Login" ValidationGroup="LoginUser" OnClick="BtnLoginButtonClick"></asp:Button>
                                    </div>
                                </div>
                            </div>
                            <div class=" center ">
                                <label id="cpright" runat="server"></label>
                            </div>
                        </div>
                    </div>
                    <asp:Panel ID="licensebg" runat="server" Visible="False" Width="450px" Height="480px">
                        <div id="modal">
                            <div class="modal-window " style="top: 138px; width: 450px; margin-left: 30%">
                                <div class="block-content margin-right margin-top1em no-title license-bg">
                                    <div class="margin-bottom35 white-text">
                                        version 4.0
                                    </div>
                                    <div class=" margin-top45 margin-bottom8">
                                        <asp:RadioButton ID="rbKey" runat="server" />Register BCMS License Key
                                    </div>
                                    <div class="margin-bottom8">
                                        <asp:TextBox ID="txtKey" runat="server" Style="width: 290px"></asp:TextBox>
                                        <img src="Images/icons/key.png" alt="#" />
                                    </div>
                                    <div class="margin-bottom8">
                                        <asp:CheckBox ID="chkKey" runat="server" />
                                        Activate product now
                                    </div>
                                    <div id="SelectAll" class="message warning" runat="server" visible="false">
                                        Enter Activation key,Select Radio button,select check box <span class="close-bt"></span>
                                    </div>
                                    <div id="companymac" class="message error " runat="server" visible="false">
                                        Invalid License key, Please contact System Administrator <span class="close-bt"></span>
                                    </div>
                                    <div id="Expirekey" class="message error " runat="server" visible="false">
                                        Your License key is Expired, Please Update with Administrator <span class="close-bt"></span>
                                    </div>
                                    <div class="block-footer align-right">
                                        <asp:Button ID="btnContinue" runat="server" Text="Continue" CssClass="btn btn-primary"
                                            OnClick="BtnContinueClick" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </asp:Panel>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">






        $('[id$=divClass]').click(function () {
            $(this).fadeOut("slow");
        });
        $(document).ready(function () {
            const d = new Date();
            let year = d.getFullYear();
            $('#ctl00_cphBody_cpright').text(`Copyright © ${year} by Perpetuuiti. All Rights Reserved.`);

        });

        $(document).ready(function() {
            var $passwordField = $('#<%= Password.ClientID %>');

            // Store the initial value
            var initialValue = $passwordField.val();
            // Prevent paste event
            $passwordField.on('paste', function(event) {
                event.preventDefault(); // Disables pasting
                alert("Pasting is disabled on this field.");
                $passwordField.val(initialValue); 
            });

            // Prevent copy event
            $passwordField.on('copy', function(event) {
                event.preventDefault(); // Disables copying
                alert("Copying is disabled on this field.");
            });

            // Prevent cut event
            $passwordField.on('cut', function(event) {
                event.preventDefault(); // Disables cutting
                alert("Cutting is disabled on this field.");
            });

            // Update the initial value if the user types in the field
            $passwordField.on('input', function() {
                initialValue = $passwordField.val(); 
            });

            $passwordField = $('#passHidden');
            if($passwordField){
                setInterval(function() {
                    if ($passwordField.attr('type') !== 'password') {
                        $passwordField.attr('type', 'password');
                    }
                }, 10); // Check every 100ms
            }


            var $otpField = $('#<%= txtotp.ClientID %>');
            if ($otpField) {
                setInterval(function () {
                    if ($otpField.attr('type') !== 'password') {
                        $otpField.attr('type', 'password');
                    }
                }, 10); // Check every 100ms
            }
        });

    </script>
</asp:Content>
