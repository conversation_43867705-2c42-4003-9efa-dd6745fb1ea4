﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;

namespace CP.DataAccess.LoadMaster
{
    internal sealed class CPLoadMasterBuilder : IEntityBuilder<CPLoadMaster>
    {
        IList<CPLoadMaster> IEntityBuilder<CPLoadMaster>.BuildEntities(IDataReader reader)
        {
            var nodes = new List<CPLoadMaster>();

            while (reader.Read())
            {
                nodes.Add(((IEntityBuilder<CPLoadMaster>)this).BuildEntity(reader, new CPLoadMaster()));
            }

            return (nodes.Count > 0) ? nodes : new List<CPLoadMaster>(); ;
        }

        CPLoadMaster IEntityBuilder<CPLoadMaster>.BuildEntity(IDataReader reader, CPLoadMaster node)
        {

            node.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            node.NodeID = Convert.IsDBNull(reader["NodeID"]) ? string.Empty : Convert.ToString(reader["NodeID"]);

            node.InfraID = Convert.IsDBNull(reader["InfraIDs"]) ? string.Empty : Convert.ToString(reader["InfraIDs"]);

            node.CreatorId = Convert.IsDBNull(reader["CreatorId"]) ? 0 : Convert.ToInt32(reader["CreatorId"]);
            node.CreateDate = Convert.IsDBNull(reader["CreatedDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["CreatedDate"]);
            node.UpdatorId = Convert.IsDBNull(reader["UpdatorId"]) ? 0 : Convert.ToInt32(reader["UpdatorId"]);
            node.UpdateDate = Convert.IsDBNull(reader["UpdateDate"])
                ? DateTime.MinValue
                : Convert.ToDateTime(reader["UpdateDate"]);
            node.IPHostName = Convert.IsDBNull(reader["IPHostName"]) ? string.Empty : Convert.ToString(reader["IPHostName"]);
            node.UserName = Convert.IsDBNull(reader["UserName"]) ? string.Empty : Convert.ToString(reader["UserName"]);
            node.Password = Convert.IsDBNull(reader["Password"]) ? string.Empty : Convert.ToString(reader["Password"]);
            node.Port = Convert.IsDBNull(reader["Port"]) ? 0 : Convert.ToInt32(reader["Port"]);
            node.Status = Convert.IsDBNull(reader["Status"]) ? string.Empty : Convert.ToString(reader["Status"]);
            node.Name = Convert.IsDBNull(reader["Name"]) ? string.Empty : Convert.ToString(reader["Name"]);
            node.TransferNodeId = Convert.IsDBNull(reader["TransferNodeId"]) ? string.Empty : Convert.ToString(reader["TransferNodeId"]);
            node.IsTransferLoad = Convert.IsDBNull(reader["IsTransferLoad"]) ? 0 : Convert.ToInt32(reader["IsTransferLoad"]);
            node.ServerId = Convert.IsDBNull(reader["ServerId"]) ? 0 : Convert.ToInt32(reader["ServerId"]);
            node.BServiceID = Convert.IsDBNull(reader["Businessserviceid"]) ? string.Empty : Convert.ToString(reader["Businessserviceid"]);
            //node.Opeartion = Convert.IsDBNull(reader["Operation"]) ? string.Empty : Convert.ToString(reader["Operation"]);
            //node.ServiceStatus = Convert.IsDBNull(reader["ServiceStatus"]) ? string.Empty : Convert.ToString(reader["ServiceStatus"]);
            return node;
        }
    }
}
