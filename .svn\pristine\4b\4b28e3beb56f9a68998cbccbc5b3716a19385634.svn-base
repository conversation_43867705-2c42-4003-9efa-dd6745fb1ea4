﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;
using System.Linq;

namespace CP.DataAccess
{
    internal sealed class MongoDBDMonitorStatusBuilder : IEntityBuilder<MongoDBDMonitorStatus>
    {
        IList<MongoDBDMonitorStatus> IEntityBuilder<MongoDBDMonitorStatus>.BuildEntities(IDataReader reader)
        {
            var MongoDBDMonitorStatus = new List<MongoDBDMonitorStatus>();

            while (reader.Read())
            {
                MongoDBDMonitorStatus.Add(((IEntityBuilder<MongoDBDMonitorStatus>)this).BuildEntity(reader, new MongoDBDMonitorStatus()));
            }

            return (MongoDBDMonitorStatus.Count > 0) ? MongoDBDMonitorStatus : null;
        }

        //MongoDBDMonitorStatus IEntityBuilder<MongoDBDMonitorStatus>.BuildEntity(IDataReader reader, MongoDBDMonitorStatus MongoDBDMonitorStatusDetails)
        //{

        //    MongoDBDMonitorStatusDetails.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
        //    MongoDBDMonitorStatusDetails.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
        //    MongoDBDMonitorStatusDetails.MongodStatusPR = Convert.IsDBNull(reader["MongodStatusPR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["MongodStatusPR"]);
        //    MongoDBDMonitorStatusDetails.MongodStatusDR = Convert.IsDBNull(reader["MongodStatusDR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["MongodStatusDR"]);

        //    MongoDBDMonitorStatusDetails.HostNamePR = Convert.IsDBNull(reader["HostNamePR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["HostNamePR"]);
        //    MongoDBDMonitorStatusDetails.HostNameDR = Convert.IsDBNull(reader["HostNameDR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["HostNameDR"]);

        //    MongoDBDMonitorStatusDetails.DBNamesPR = Convert.IsDBNull(reader["DBNamesPR"])
        //     ? string.Empty
        //     : Convert.ToString(reader["DBNamesPR"]);
        //    MongoDBDMonitorStatusDetails.DBNamesDR = Convert.IsDBNull(reader["DBNamesDR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["DBNamesDR"]);



        //    MongoDBDMonitorStatusDetails.SizesOnDiskPR = Convert.IsDBNull(reader["SizesOnDiskPR"])
        //   ? string.Empty
        //   : Convert.ToString(reader["SizesOnDiskPR"]);
        //    MongoDBDMonitorStatusDetails.SizesOnDiskDR = Convert.IsDBNull(reader["SizesOnDiskDR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["SizesOnDiskDR"]);



        //    MongoDBDMonitorStatusDetails.CollectionsPR = Convert.IsDBNull(reader["CollectionsPR"])
        //      ? string.Empty
        //      : Convert.ToString(reader["CollectionsPR"]);
        //    MongoDBDMonitorStatusDetails.CollectionsDR = Convert.IsDBNull(reader["CollectionsDR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["CollectionsDR"]);

        //    MongoDBDMonitorStatusDetails.DocumentsPR = Convert.IsDBNull(reader["DocumentsPR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["DocumentsPR"]);
        //    MongoDBDMonitorStatusDetails.DocumentsDR = Convert.IsDBNull(reader["DocumentsDR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["DocumentsDR"]);

        //    MongoDBDMonitorStatusDetails.IndexesPR = Convert.IsDBNull(reader["IndexesPR"])
        //   ? string.Empty
        //   : Convert.ToString(reader["IndexesPR"]);
        //    MongoDBDMonitorStatusDetails.IndexesDR = Convert.IsDBNull(reader["IndexesDR"])
        //        ? string.Empty
        //        : Convert.ToString(reader["IndexesDR"]);
        //    MongoDBDMonitorStatusDetails.CreateDate = Convert.IsDBNull(reader["CreateDate"])
        //      ? DateTime.MinValue
        //      : Convert.ToDateTime(reader["CreateDate"].ToString());
        //    MongoDBDMonitorStatusDetails.VersionPR = Convert.IsDBNull(reader["VersionPR"])
        //    ? string.Empty
        //    : Convert.ToString(reader["VersionPR"].ToString());

        //    MongoDBDMonitorStatusDetails.VersionDR = Convert.IsDBNull(reader["VersionDR"])
        //    ? string.Empty
        //    : Convert.ToString(reader["VersionDR"].ToString());

        //    return MongoDBDMonitorStatusDetails;
        //}


        MongoDBDMonitorStatus IEntityBuilder<MongoDBDMonitorStatus>.BuildEntity(IDataReader reader, MongoDBDMonitorStatus MongoDBDMonitorStatusDetails)
        {

            MongoDBDMonitorStatusDetails.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            MongoDBDMonitorStatusDetails.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            MongoDBDMonitorStatusDetails.MongodStatusPR = Convert.IsDBNull(reader["MongodStatusPR"])
                ? string.Empty
                : Convert.ToString(reader["MongodStatusPR"]);
            MongoDBDMonitorStatusDetails.MongodStatusDR = Convert.IsDBNull(reader["MongodStatusDR"])
                ? string.Empty
                : Convert.ToString(reader["MongodStatusDR"]);

            MongoDBDMonitorStatusDetails.HostNamePR = Convert.IsDBNull(reader["HostNamePR"])
                ? string.Empty
                : Convert.ToString(reader["HostNamePR"]);
            MongoDBDMonitorStatusDetails.HostNameDR = Convert.IsDBNull(reader["HostNameDR"])
                ? string.Empty
                : Convert.ToString(reader["HostNameDR"]);

            MongoDBDMonitorStatusDetails.DBNamesPR = Convert.IsDBNull(reader["DBNamesPR"])
             ? string.Empty
             : Convert.ToString(reader["DBNamesPR"]);
            MongoDBDMonitorStatusDetails.DBNamesDR = Convert.IsDBNull(reader["DBNamesDR"])
                ? string.Empty
                : Convert.ToString(reader["DBNamesDR"]);

            MongoDBDMonitorStatusDetails.PRStateDescription = Convert.IsDBNull(reader["PRStateDescription"])
            ? string.Empty
            : Convert.ToString(reader["PRStateDescription"]);
            MongoDBDMonitorStatusDetails.DRStateDescription = Convert.IsDBNull(reader["DRStateDescription"])
                ? string.Empty
                : Convert.ToString(reader["DRStateDescription"]);

            MongoDBDMonitorStatusDetails.PRhealth = Convert.IsDBNull(reader["PRhealth"])
             ? string.Empty
             : Convert.ToString(reader["PRhealth"]);
            MongoDBDMonitorStatusDetails.DRhealth = Convert.IsDBNull(reader["DRhealth"])
                ? string.Empty
                : Convert.ToString(reader["DRhealth"]);

            MongoDBDMonitorStatusDetails.PRlastHeartbeatMessage = Convert.IsDBNull(reader["PRlastHeartbeatMessage"])
                ? string.Empty
                : Convert.ToString(reader["PRlastHeartbeatMessage"]);
            MongoDBDMonitorStatusDetails.DRlastHeartbeatMessage = Convert.IsDBNull(reader["DRlastHeartbeatMessage"])
                ? string.Empty
                : Convert.ToString(reader["DRlastHeartbeatMessage"]);

            MongoDBDMonitorStatusDetails.PRreplicaSetName = Convert.IsDBNull(reader["PRreplicaSetName"])
           ? string.Empty
           : Convert.ToString(reader["PRreplicaSetName"]);
            MongoDBDMonitorStatusDetails.DRreplicaSetName = Convert.IsDBNull(reader["DRreplicaSetName"])
                ? string.Empty
                : Convert.ToString(reader["DRreplicaSetName"]);
            MongoDBDMonitorStatusDetails.PRmemberID = Convert.IsDBNull(reader["PRmemberID"])
               ? string.Empty
               : Convert.ToString(reader["PRmemberID"]);
            MongoDBDMonitorStatusDetails.DRmemberID = Convert.IsDBNull(reader["DRmemberID"])
               ? string.Empty
               : Convert.ToString(reader["DRmemberID"]);
            MongoDBDMonitorStatusDetails.PRCurrentPriority = Convert.IsDBNull(reader["PRCurrentPriority"])
               ? string.Empty
               : Convert.ToString(reader["PRCurrentPriority"]);
            MongoDBDMonitorStatusDetails.DRCurrentPriority = Convert.IsDBNull(reader["DRCurrentPriority"])
               ? string.Empty
               : Convert.ToString(reader["DRCurrentPriority"]);
            MongoDBDMonitorStatusDetails.PRDatalag = Convert.IsDBNull(reader["PRDatalag"])
              ? string.Empty
              : Convert.ToString(reader["PRDatalag"]);
            MongoDBDMonitorStatusDetails.DRDatalag = Convert.IsDBNull(reader["DRDatalag"])
               ? string.Empty
               : Convert.ToString(reader["DRDatalag"]);
            MongoDBDMonitorStatusDetails.CreateDate = Convert.IsDBNull(reader["CreateDate"])
              ? DateTime.MinValue
              : Convert.ToDateTime(reader["CreateDate"].ToString());
            MongoDBDMonitorStatusDetails.VersionPR = Convert.IsDBNull(reader["VersionPR"])
            ? string.Empty
            : Convert.ToString(reader["VersionPR"].ToString());

            MongoDBDMonitorStatusDetails.VersionDR = Convert.IsDBNull(reader["VersionDR"])
            ? string.Empty
            : Convert.ToString(reader["VersionDR"].ToString());


        

            return MongoDBDMonitorStatusDetails;
        }


    }
}
