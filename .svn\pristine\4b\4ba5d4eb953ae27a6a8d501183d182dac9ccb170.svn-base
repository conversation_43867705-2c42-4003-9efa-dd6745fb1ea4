﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="Profile_WorkflowDetails.ascx.cs" Inherits="CP.UI.Controls.Profile_WorkflowDetails" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<link href="../App_Themes/ReportTheme/Report.css" rel="stylesheet" type="text/css" />

<link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
<script src="../Script/chosen.jquery.js"></script>
<script>
    $(document).ready(function () {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    });
    function pageLoad() {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    }
</script>
<style>
    .chosen-select + .chosen-container {
        width: 48.5% !important;
        opacity: 1 !important;
    }
</style>

<div class="form-group">
    <label class="col-md-3 control-label">
        Type <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddltype" runat="server" CssClass="chosen-select col-md-6" AutoPostBack="true">
            <asp:ListItem Value="Profile">Profile</asp:ListItem>
            <asp:ListItem Value="Workflow">Workflow</asp:ListItem>
        </asp:DropDownList>
        <asp:RequiredFieldValidator ID="rfvddlGroup" runat="server" CssClass="error" ControlToValidate="ddltype"
            ValidationGroup="vlGroupSite" InitialVale="0" Display="Dynamic" ErrorMessage="Select Type" InitialValue="0"></asp:RequiredFieldValidator>
        <asp:Label ID="lblvalidation" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
    </div>
</div>


<div class="form-group">
    <div id="divlable" class="col-xs-6" visible="false">
        <asp:Label ID="Label2" runat="server" ForeColor="Red" Visible="false"></asp:Label>
        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span><br />
        <asp:Label ID="Label1" runat="server" ForeColor="Red" Visible="false"></asp:Label>
    </div>
    <div class="col-xs-6">
        <asp:Button ID="btnPdfSave" CssClass="btn btn-primary" Style="margin-left: 5px" Width="20%" runat="server"
            Text="Excel Report" ValidationGroup="vlGroupSite" OnClick="btnPdfSave_Click" />
    </div>
</div>

