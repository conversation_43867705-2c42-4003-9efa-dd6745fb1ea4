﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="DatabaseDb2List.ascx.cs" Inherits="CP.UI.Controls.DatabaseDb2List" %>
<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<asp:UpdateProgress ID="updateprogress1" AssociatedUpdatePanelID="upnlNormalDatabseList"
    runat="server">
    <ProgressTemplate>
        <div class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>
<asp:UpdatePanel ID="upnlNormalDatabseList" runat="server">
    <ContentTemplate>
        <div class="row">
            <div class="col-md-5 col-md-push-7 text-right">
                <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Database Name or Database SID"></asp:TextBox>
                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" Text="Search" Width="20%" OnClick="btnSearch_Click" />
            </div>
        </div>
        <hr />
        <asp:ListView ID="lvDB2database" runat="server" DataKeyNames="ID" OnItemEditing="LvdatabaseItemEditing"
            OnPreRender="LvdatabasePreRender" OnItemDeleting="LvdatabaseItemDeleting" OnItemDataBound="LvdatabaseItemDataBound">
            <LayoutTemplate>
                <table id="tbldatabase" class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" width="100%"
                    style="table-layout: fixed; margin-bottom: 0;">
                    <thead>
                        <tr>
                            <th style="width: 4%;" class="text-center">
                                <span>
                                    <img src="../Images/icons/database.png" /></span>
                            </th>
                            <th style="width: 24%;">Name
                            </th>
                            <th style="width: 20%;">Database SID
                            </th>
                            <th style="width: 20%;">Type
                            </th>
                            <th style="width: 12%;">Version
                            </th>
                            <th style="width: 12%;">Port Number
                            </th>

                            <th runat="server" id="ActionHead" style="width: 8%;" class="text-center">Action
                            </th>
                        </tr>
                    </thead>
                </table>

                <table class="table table-striped table-bordered table-condensed" width="100%">
                    <tbody>
                        <asp:PlaceHolder ID="itemPlaceHolder" runat="server" />
                    </tbody>
                </table>
            </LayoutTemplate>
            <EmptyDataTemplate>
                <div class="message warning align-center bold">
                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                </div>
            </EmptyDataTemplate>
            <ItemTemplate>
                <tr>
                    <td style="width: 4%;" class="text-center">
                        <asp:Label ID="ID" runat="server" Text='<%#Eval("Id") %>' Visible="false"></asp:Label>
                        <%#Container.DataItemIndex+1 %>
                    </td>
                    <td style="width: 24%;" class="tdword-wrap">
                        <asp:Label ID="lblName" runat="server" Text='<%#Eval("Name") %>'></asp:Label>
                    </td>
                    <td style="width: 20%;">
                        <span>
                            <%# DataBinder.Eval(Container, "DataItem.DatabaseDB2.DatabaseSID")%></span>
                    </td>
                    <td style="width: 20%;">
                        <%# DataBinder.Eval(Container, "DataItem.Type")%>
                    </td>
                    <td style="width: 12%;">
                        <%# DataBinder.Eval(Container, "DataItem.Version")%>
                    </td>
                    <td style="width: 12%;">
                        <%# DataBinder.Eval(Container, "DataItem.DatabaseDB2.Port")%>
                    </td>

                    <td runat="server" id="action" style="width: 8%;" class="text-center">
                        <asp:ImageButton ID="ibtnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                            ToolTip="Edit" ImageUrl="../images/icons/pencil.png" />
                        <asp:ImageButton ID="ibtnDelete" runat="server" CommandName="Delete" AlternateText="Delete"
                            ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                    </td>
                    <TK1:ConfirmButtonExtender ID="ConfirmButtonExtender1" runat="server" TargetControlID="ibtnDelete"
                        ConfirmText='<%# "Are you sure want to delete " + Eval("Name") + " ? " %>' OnClientCancel="CancelClick">
                    </TK1:ConfirmButtonExtender>
                </tr>
            </ItemTemplate>
        </asp:ListView>
        <div class="row">
            <div class="col-md-6">
                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvDB2database">
                    <Fields>
                        <asp:TemplatePagerField>
                            <PagerTemplate>
                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                <br />
                            </PagerTemplate>
                        </asp:TemplatePagerField>
                    </Fields>
                </asp:DataPager>
            </div>
            <div class="col-md-6 text-right">
                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvDB2database" PageSize="10">
                    <Fields>
                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                            NumericButtonCssClass="btn-pagination" />
                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                    </Fields>
                </asp:DataPager>
            </div>

                <asp:UpdatePanel ID="paneldetails" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="false"
                EnableViewState="False" RenderMode="Inline" Visible="false" ScrollBars="Vertical">
                <ContentTemplate>
                    <layouttemplate>
                              <asp:Panel Enabled="true" Visible="true">
                                                            <div class="modal bg" style="display: block;" >

                                                                <div class="modal-dialog" style="width:90%">
                                                                    <div class="clearfix"></div>
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h3 class="modal-title">Database Attached Info</h3> 
                                                                            <button type="button" ID="btnclose" class="close" OnClick="lstBtnClose()" ><span aria-hidden="true">&times;</span></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                         <h3 class="modal-title" style="color: #000 !important; font-weight: 600;">
                                                                             Remove dependencey of Database  <asp:Label ID="lbl_Server" runat="server" style="font-size: 17px;  display: contents; font-weight: 600; color: #000 !important; font-style: normal;  margin-top: 3px; font-weight: 600;" Text='<%# Eval("Type") %>' /> from below attributes.</h3>
                                                                             <hr /> 
                                                                            <div class="row" id="scrolldata" style="overflow:overlay; max-height: 70vh;margin-right:0px; padding-left: 2.5rem;">

                                                                                <asp:GridView ID="Server_Info_GridView"   runat="server" OnRowDataBound="Server_Info_GridView_RowDataBound" OnDataBound="Server_Info_GridView_DataBound"  Width="100%" RowStyle-CssClass="columnwidthcss" RowStyle-VerticalAlign="Top">

                                                                                </asp:GridView>  
                                                                                  </div>             
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                             </asp:Panel>
                                </layouttemplate>
                </ContentTemplate>
            </asp:UpdatePanel>
              </div>

        


      

        <script type="text/javascript">
            function CancelClick() {
                return false;
            }

            function lstBtnClose() {
                $('.modal').hide();
            }
        </script>

        <style>
        .columnwidthcss > td {
            word-break: break-all;
        }
    </style>
    </ContentTemplate>
</asp:UpdatePanel>
