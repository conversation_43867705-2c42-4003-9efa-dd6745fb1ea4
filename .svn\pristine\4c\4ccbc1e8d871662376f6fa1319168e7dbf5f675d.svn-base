﻿using System;
using System.Collections.Generic;
using System.Data;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    internal sealed class MySqlMonitorBuilder : IEntityBuilder<MySqlReplication>
    {
        public IList<MySqlReplication> BuildEntities(IDataReader reader)
        {
            var MySqlReplication = new List<MySqlReplication>();

            while (reader.Read())
            {
                MySqlReplication.Add(((IEntityBuilder<MySqlReplication>)this).BuildEntity(reader,
                    new MySqlReplication()));
            }

            return (MySqlReplication.Count > 0) ? MySqlReplication : null;
        }

        #region IEntityBuilder<HitachiURMonitoring> Members

        MySqlReplication IEntityBuilder<MySqlReplication>.BuildEntity(IDataReader reader,MySqlReplication MySqlMonitoring)
        {

            MySqlMonitoring.Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]);
            MySqlMonitoring.InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]);
            MySqlMonitoring.PRDBVersion = Convert.IsDBNull(reader["PRDBVersion"]) ? string.Empty
    : Convert.ToString(reader["PRDBVersion"]);
            MySqlMonitoring.DRDBVersion = Convert.IsDBNull(reader["DRDBVersion"]) ? string.Empty
    : Convert.ToString(reader["DRDBVersion"]);
            MySqlMonitoring.PRDBServiceStatus = Convert.IsDBNull(reader["PRDBServiceStatus"]) ? string.Empty
    : Convert.ToString(reader["PRDBServiceStatus"]);
            MySqlMonitoring.DRDBServiceStatus = Convert.IsDBNull(reader["DRDBServiceStatus"]) ? string.Empty
    : Convert.ToString(reader["DRDBServiceStatus"]);
            MySqlMonitoring.PRDBState = Convert.IsDBNull(reader["PRDBState"]) ? string.Empty
    : Convert.ToString(reader["PRDBState"]);
            MySqlMonitoring.DRDBState = Convert.IsDBNull(reader["DRDBState"]) ? string.Empty
    : Convert.ToString(reader["DRDBState"]);
            MySqlMonitoring.PRSlaveRunState = Convert.IsDBNull(reader["PRSlaveRunState"]) ? string.Empty
    : Convert.ToString(reader["PRSlaveRunState"]);
            MySqlMonitoring.DRSlaveRunState = Convert.IsDBNull(reader["DRSlaveRunState"]) ? string.Empty
    : Convert.ToString(reader["DRSlaveRunState"]);
            MySqlMonitoring.PRRepliConnectSate = Convert.IsDBNull(reader["PRRepliConnectSate"]) ? string.Empty
    : Convert.ToString(reader["PRRepliConnectSate"]);
            MySqlMonitoring.DRRepliConnectSate = Convert.IsDBNull(reader["DRRepliConnectSate"]) ? string.Empty
    : Convert.ToString(reader["DRRepliConnectSate"]);
            MySqlMonitoring.PRSlaveIORunState = Convert.IsDBNull(reader["PRSlaveIORunState"]) ? string.Empty
    : Convert.ToString(reader["PRSlaveIORunState"]);
            MySqlMonitoring.DRSlaveIORunState = Convert.IsDBNull(reader["DRSlaveIORunState"]) ? string.Empty
    : Convert.ToString(reader["DRSlaveIORunState"]);
            MySqlMonitoring.PRSlaveSqlRunState = Convert.IsDBNull(reader["PRSlaveSqlRunState"]) ? string.Empty
    : Convert.ToString(reader["PRSlaveSqlRunState"]);
            MySqlMonitoring.DRSlaveSqlRunState = Convert.IsDBNull(reader["DRSlaveSqlRunState"]) ? string.Empty
    : Convert.ToString(reader["DRSlaveSqlRunState"]);
            MySqlMonitoring.PRMasterLogFile = Convert.IsDBNull(reader["PRMasterLogFile"]) ? string.Empty
    : Convert.ToString(reader["PRMasterLogFile"]);
            MySqlMonitoring.DRMasterLogFile = Convert.IsDBNull(reader["DRMasterLogFile"]) ? string.Empty
    : Convert.ToString(reader["DRMasterLogFile"]);
            MySqlMonitoring.PRRelayMasterLogFile = Convert.IsDBNull(reader["PRRelayMasterLogFile"]) ? string.Empty
    : Convert.ToString(reader["PRRelayMasterLogFile"]);
            MySqlMonitoring.DRRelayMasterLogFile = Convert.IsDBNull(reader["DRRelayMasterLogFile"]) ? string.Empty
    : Convert.ToString(reader["DRRelayMasterLogFile"]);
            MySqlMonitoring.PRMasterLogPosition = Convert.IsDBNull(reader["PRMasterLogPosition"]) ? string.Empty
    : Convert.ToString(reader["PRMasterLogPosition"]);
            MySqlMonitoring.DRMasterLogPosition = Convert.IsDBNull(reader["DRMasterLogPosition"]) ? string.Empty
    : Convert.ToString(reader["DRMasterLogPosition"]);
            MySqlMonitoring.PRExecMasterLogPos = Convert.IsDBNull(reader["PRExecMasterLogPos"]) ? string.Empty
    : Convert.ToString(reader["PRExecMasterLogPos"]);
            MySqlMonitoring.DRExecMasterLogPos = Convert.IsDBNull(reader["DRExecMasterLogPos"]) ? string.Empty
    : Convert.ToString(reader["DRExecMasterLogPos"]);
            MySqlMonitoring.MySqlDataLag = Convert.IsDBNull(reader["DataLag"]) ? string.Empty
    : Convert.ToString(reader["DataLag"]);


            return MySqlMonitoring;
        }

        #endregion IEntityBuilder<HyperVDetails> Members
    }
}
