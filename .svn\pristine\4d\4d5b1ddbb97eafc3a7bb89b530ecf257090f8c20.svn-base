﻿using System;
using System.Collections.Generic;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;

namespace CP.UI.Code.Replication.ReplicationInfo
{
    public class OracleWithFastcopyReplicationInfo : IReplicationInfo
    {
        private readonly IFacade _facade = new Facade();

        public string s1;
        public string s4;

        #region IReplicationInfo Members

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectId)
        {
            var currentEntityType = currentEntity.GetType();

            FastCopy fastcopy = new FastCopy();

            var infraObject = _facade.GetInfraObjectById(infraObjectId);

            if (infraObject != null)
            {
                var fastcopymonitordata = _facade.GetFastCopyMonitorByInfraObjectId(infraObjectId);
                if (fastcopymonitordata != null)
                {
                    if (!string.IsNullOrEmpty(fastcopymonitordata.IncrementalFilesCount))
                    {
                        int lastreplicationcount = Convert.ToInt32(fastcopymonitordata.IncrementalFilesCount);
                        currentEntityType.GetProperty("LastReplicationCount").SetValue(currentEntity, lastreplicationcount, null);
                    }
                    else
                    {
                        currentEntityType.GetProperty("LastReplicationCount").SetValue(currentEntity, 0, null);
                    }
                }
                else
                {
                    currentEntityType.GetProperty("LastReplicationCount").SetValue(currentEntity, 0, null);
                }
                var fastcopyId = _facade.GetFastCopyByReplicationId(infraObject.PRReplicationId);

                var fastCopyJobReplication = _facade.GetFastCopyLastRepTimeByFastCopyId(fastcopyId.Id);
                if (fastCopyJobReplication != null)
                {
                    if (!string.IsNullOrWhiteSpace(fastCopyJobReplication.LastSuccessfullReplTime))
                        currentEntityType.GetProperty("LastSuccessfullReplTime").SetValue(currentEntity, fastCopyJobReplication.LastSuccessfullReplTime, null);
                    else
                        currentEntityType.GetProperty("LastSuccessfullReplTime").SetValue(currentEntity, "N/A", null);
                    if (!string.IsNullOrWhiteSpace(fastCopyJobReplication.NextScheduleRepTime))
                        currentEntityType.GetProperty("NextScheduleRepTime").SetValue(currentEntity, fastCopyJobReplication.NextScheduleRepTime, null);
                    else
                        currentEntityType.GetProperty("NextScheduleRepTime").SetValue(currentEntity, "N/A", null);
                }
                else
                {
                    currentEntityType.GetProperty("LastSuccessfullReplTime").SetValue(currentEntity, "N/A", null);
                    currentEntityType.GetProperty("NextScheduleRepTime").SetValue(currentEntity, "N/A", null);
                }

                IList<FastCopyJob> fastcopyjob = _facade.GetFastCopyJobByFastCopyId(fastcopyId.Id);

                if (fastcopyjob != null)
                {
                    var s2 = "";
                    var s3 = "";
                    foreach (FastCopyJob oFastcopy in fastcopyjob)
                    {
                        s1 += oFastcopy.SourceDirectory + ",";
                        s4 += oFastcopy.DestinationDirectory + ",";
                    }

                    s2 = s1.Substring(0, s1.LastIndexOf(","));
                    s3 = s4.Substring(0, s4.LastIndexOf(","));

                    currentEntityType.GetProperty("SourceDirectory").SetValue(currentEntity, s2, null);
                    currentEntityType.GetProperty("DestinationDirectory").SetValue(currentEntity, s3, null);
                }
                else
                {
                    currentEntityType.GetProperty("SourceDirectory").SetValue(currentEntity, "N/A", null);
                    currentEntityType.GetProperty("DestinationDirectory").SetValue(currentEntity, "N/A", null);
                }
            }
            else
            {
                currentEntityType.GetProperty("LastReplicationCount").SetValue(currentEntity, 0, null);
                currentEntityType.GetProperty("LastSuccessfullReplTime").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("SourceDirectory").SetValue(currentEntity, "N/A", null);
                currentEntityType.GetProperty("DestinationDirectory").SetValue(currentEntity, "N/A", null);
            }
            return currentEntity;
        }

        public T GetReplicationInformation<T>(T currentEntity, int replicationId, int infraObjectId,int mailBoxId, string mailboxname)
        {
            return currentEntity;
        }

        #endregion IReplicationInfo Members
    }
}