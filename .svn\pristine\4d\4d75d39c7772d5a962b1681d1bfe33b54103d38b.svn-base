﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="Robocopyoptionslist.aspx.cs"
    Inherits="CP.UI.Component.Robocopyoptionslist" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">

    <script type="text/javascript">
        function CancelClick() {
            return false;
        }
    </script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />
    <div class="innerLR">
        <asp:UpdatePanel ID="upnlProfileOverview" runat="server" UpdateMode="Conditional">
            <ContentTemplate>


                <h3>
                    <img src="../Images/replication_cc.png" style="vertical-align: baseline;">
                    Robocopy Options List</h3>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-5 col-md-push-7 text-right">
                                <asp:TextBox ID="txtsearchvalue" runat="server" CssClass="form-control" placeholder="Replication Name"></asp:TextBox>
                                <asp:Button ID="btnSearch" runat="server" CssClass="btn btn-primary" OnClick="btnSearch_Click" Width="20%"
                                    Text="Search" />
                                <%--OnClick="BtnSearchClick"--%>
                            </div>
                        </div>
                        <hr />
                        <%--<asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
                            <ContentTemplate>--%>
                        <asp:ListView ID="lstviewrobo" runat="server" OnItemDataBound="lstviewrobo_ItemDataBound" OnItemDeleting="lstviewrobo_ItemDeleting"
                            OnItemEditing="lstviewrobo_ItemEditing" OnPagePropertiesChanging="lstviewrobo_PagePropertiesChanging" OnPreRender="lstviewrobo_PreRender">
                            <LayoutTemplate>
                                <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                                    <thead>
                                        <tr>
                                            <th class="text-center" style="width: 4%;">
                                                <span>
                                                    <img src="../Images/profile-company-icon-white.png" /></span>
                                            </th>

                                            <th style="width: 24%;">Replication Name
                                            </th>
                                            <th style="width: 24%;">Replication Type
                                            </th>
                                            <th style="width: 20%;">Copy Option
                                            </th>
                                            <th style="width: 20%;">Filters
                                            </th>
                                            <th style="width: 8%;" class="text-center">Action
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                    </tbody>
                                </table>
                            </LayoutTemplate>
                            <EmptyDataTemplate>
                                <div class="message warning align-center bold">
                                    <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                </div>
                            </EmptyDataTemplate>
                            <ItemTemplate>
                                <tr>
                                    <td style="width: 4%;" class="text-center">


                                        <asp:Label ID="lblId" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                    </td>
                                    <td style="width: 24%;" class="tdword-wrap">
                                        <asp:Label ID="lblName" runat="server" Text='<%# Eval("Name") %>' />
                                    </td>
                                    <td style="width: 24%;" class="tdword-wrap">

                                        <asp:Label ID="ReplicationName" runat="server" Text='<%# Eval("RepType") %>' />
                                    </td>
                                    <td style="width: 20%;" class="tdword-wrap">
                                        <asp:Label ID="lblcopyoptions" runat="server" Text='<%# Eval("CopyOptions") %>' />
                                    </td>
                                    <td style="width: 20%;">
                                        <asp:Label ID="lblfilters" runat="server" Text='<%# Eval("Filters") %>' />
                                    </td>
                                    <td style="width: 8%;" class="text-center">
                                        <asp:ImageButton ID="ImgEdit" runat="server" ToolTip="Edit" ImageUrl="../images/icons/pencil.png"
                                            ValidationGroup="IDT" CommandName="Edit" CausesValidation="false" />
                                        <%--CommandName="Edit"--%>
                                        <asp:ImageButton ID="ImgDelete" runat="server" ToolTip="Delete"
                                            ImageUrl="../images/icons/cross-circle.png" CommandName="Delete" ValidationGroup="IDT" CausesValidation="true" />
                                        <%--CommandName="Delete"--%>
                                    </td>
                                    <TK1:ConfirmButtonExtender id="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                        ConfirmText='<%# "Are you sure want to delete " + Eval("Name") + " ? " %>' OnClientCancel="CancelClick">
                                   </TK1:ConfirmButtonExtender>

                                </tr>
                            </ItemTemplate>
                        </asp:ListView>

                        <div class="row">
                            <div class="col-md-6">
                                <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lstviewrobo">
                                    <Fields>
                                        <asp:TemplatePagerField>
                                            <PagerTemplate>
                                                <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                Results
                                        <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                        Out Of
                                        <%# Container.TotalRowCount %>
                                                <br />
                                            </PagerTemplate>
                                        </asp:TemplatePagerField>
                                    </Fields>
                                </asp:DataPager>
                            </div>
                            <div class="col-md-6 text-right">
                                <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lstviewrobo" PageSize="10">
                                    <Fields>
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination prev" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowNextPageButton="false" PreviousPageText="← Prev" />
                                        <asp:NumericPagerField PreviousPageText=".." NextPageText=".." ButtonCount="10"
                                            NextPreviousButtonCssClass="btn-pagination" CurrentPageLabelCssClass="currentlabel"
                                            NumericButtonCssClass="btn-pagination" />
                                        <asp:NextPreviousPagerField ButtonCssClass="btn-pagination next" ButtonType="Button" ShowFirstPageButton="false"
                                            ShowLastPageButton="false" ShowPreviousPageButton="false" ShowNextPageButton="true" NextPageText="Next → " />
                                    </Fields>
                                </asp:DataPager>
                            </div>
                        </div>

                        <%--<asp:ListView ID="lvComponent" runat="server" OnItemEditing="LvComponentItemEditing"
                                    DataKeyNames="Id" OnItemCanceling="LvComponentItemCanceling" OnItemDeleting="LvComponentItemDeleting"
                                    OnPreRender="LvComponentPreRender" OnItemDataBound="lvComponent_ItemDataBound" OnPagePropertiesChanging="lvComponent_PagePropertiesChanging">
                                    <LayoutTemplate>
                                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout:fixed">
                                            <thead>
                                                <tr>
                                                    <th style="width: 4%;" class="text-center">
                                                        <span>
                                                            <img src="../Images/nodes-icon-white.png" /></span>
                                                    </th>
                                                    <th style="width: 34%;" >Name
                                                    </th>
                                                    <th style="width: 34%;" >Server
                                                    </th>
                                                    <th style="width: 20%;" >OracleSID
                                                    </th>
                                                    <th style="width: 8%;" class="text-center">Action
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                            </tbody>
                                        </table>
                                    </LayoutTemplate>
                                    <EmptyDataTemplate>
                                        <div class="message warning align-center bold">
                                            <asp:Label ID="lblEmpty" Text="No Record Found" runat="server"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td style="width: 4%;" class="text-center">
                                                <%#Container.DataItemIndex+1 %>
                                            </td>
                                            <asp:Label ID="ID" runat="server" Text='<%#Eval("Id")%>' Visible="false" />
                                            <td style="width: 34%;" class="tdword-wrap">
                                                <asp:Label ID="Db_Name" runat="server" Text='<%#Eval("Name")%>' />
                                            </td>
                                            <td style="width: 34%;" class="tdword-wrap">
                                               
                                                <asp:Label ID="Db_IPAddress" runat="server" Text='<%#GetServerName(Eval("ServerId"))%>' />
                                            </td>
                                            <td  style="width: 20%;">
                                                <asp:Label ID="Db_OracleSID" runat="server" Text='<%#Eval("OracleSID")%>' />
                                            </td>
                                            <td style="width: 8%;" class="text-center">
                                                <asp:ImageButton ID="ImgEdit" runat="server" CommandName="Edit" ToolTip="Edit" ImageUrl="../images/icons/pencil.png"
                                                    ValidationGroup="IDT" CausesValidation="false" />
                                                <asp:ImageButton ID="ImgDelete" runat="server" CommandName="Delete" ToolTip="Delete"
                                                    ImageUrl="../images/icons/cross-circle.png" ValidationGroup="IDT" CausesValidation="true" />
                                            </td>
                                            <TK1:ConfirmButtonExtender ID="ConfirmDelete" runat="server" TargetControlID="ImgDelete"
                                                ConfirmText='<%# "Are you sure want to delete " + Eval("Name") + " ? " %>' OnClientCancel="CancelClick">
                                            </TK1:ConfirmButtonExtender>
                                        </tr>
                                    </ItemTemplate>
                                    <EditItemTemplate>
                                        <tr>
                                            <td>
                                                <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" /><%#Container.DataItemIndex+1 %>
                                            </td>
                                            <td>
                                                <asp:ImageButton ID="ImageButton4" runat="server" CommandName="Update" AlternateText="Update"
                                                    ToolTip="Update" ImageUrl="../Images/icons/arrow-090.png" />
                                                <asp:ImageButton ID="ImageButton3" runat="server" CommandName="Cancel" AlternateText="Cancel"
                                                    ToolTip="Cancel" ImageUrl="../Images/icons/cross-small.png" />
                                            </td>
                                        </tr>
                                    </EditItemTemplate>
                                </asp:ListView>
                                <div class="row">
                                    <div class="col-md-6">
                                        <asp:DataPager ID="dataPager2" runat="server" PagedControlID="lvComponent" PageSize="4">
                                            <Fields>
                                                <asp:TemplatePagerField>
                                                    <PagerTemplate>
                                                        <asp:Label ID="pageResult" runat="server" Text=""></asp:Label>
                                                        Results
                                                            <%# (Container.StartRowIndex + 1) + " - " + (Container.StartRowIndex + Container.PageSize > Container.TotalRowCount ? Container.TotalRowCount : Container.StartRowIndex + Container.PageSize) %>
                                                            Out Of
                                                            <%# Container.TotalRowCount %>
                                                        <br />
                                                    </PagerTemplate>
                                                </asp:TemplatePagerField>
                                            </Fields>
                                        </asp:DataPager>
                                    </div>
                                    <div class="col-md-6 text-right">
                                        <asp:DataPager ID="dataPager1" runat="server" PagedControlID="lvComponent" PageSize="10">
                                            <Fields>
                                                <asp:NextPreviousPagerField ButtonType="Button" ButtonCssClass="btn-pagination prev"
                                                    ShowFirstPageButton="false" ShowLastPageButton="false" ShowNextPageButton="false"
                                                    PreviousPageText="← Prev" />
                                                <asp:NumericPagerField NextPageText=".." PreviousPageText=".." ButtonCount="10" NextPreviousButtonCssClass="btn-pagination"
                                                    CurrentPageLabelCssClass="currentlabel" NumericButtonCssClass="btn-pagination" />
                                                <asp:NextPreviousPagerField ButtonType="Button" ButtonCssClass="btn-pagination next"
                                                    ShowFirstPageButton="false" ShowLastPageButton="false" ShowPreviousPageButton="false"
                                                    ShowNextPageButton="true" NextPageText="Next → " />
                                            </Fields>
                                        </asp:DataPager>
                                    </div>
                                </div>--%>
                        <%--</ContentTemplate>
                        </asp:UpdatePanel>--%>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>
</asp:Content>
