﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;

namespace CP.DataAccess
{
    internal sealed class ReportScheduleDataAccess : BaseDataAccess, IReportScheduleDataAccess
    {
        #region Constructors

        public ReportScheduleDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ReportSchedule> CreateEntityBuilder<ReportSchedule>()
        {
            return (new ReportScheduleBuilder()) as IEntityBuilder<ReportSchedule>;
        }

        #endregion Constructors

        ReportSchedule IReportScheduleDataAccess.Add(ReportSchedule reportSchedule)
        {
            try
            {
                const string sp = "ReportSchedule_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iReportName", DbType.AnsiString, reportSchedule.ReportName);
                   // Database.AddInParameter(cmd, Dbstring + "iReportType", DbType.AnsiString, reportSchedule.ReportType);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.AnsiString, reportSchedule.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iFromDate", DbType.AnsiString, reportSchedule.FromDate);
                    Database.AddInParameter(cmd, Dbstring+"iToDate", DbType.AnsiString, reportSchedule.ToDate);
                    Database.AddInParameter(cmd, Dbstring+"iBusinessServiceId", DbType.AnsiString, reportSchedule.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring+"iParallelDrOpId", DbType.AnsiString, reportSchedule.ParallelDrOpId);
                    Database.AddInParameter(cmd, Dbstring+"iApplicationId", DbType.AnsiString, reportSchedule.AppId);
                    Database.AddInParameter(cmd, Dbstring+"iJobId", DbType.AnsiString, reportSchedule.JobId);
                    Database.AddInParameter(cmd, Dbstring+"iReceiverId", DbType.AnsiString, reportSchedule.ReceiverId);

                    Database.AddInParameter(cmd, Dbstring+"iScheduleTime", DbType.AnsiString, reportSchedule.ScheduleTime);
                    Database.AddInParameter(cmd, Dbstring+"iTime", DbType.AnsiString, reportSchedule.Time);
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, reportSchedule.CompanyId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        reportSchedule = reader.Read() ? CreateEntityBuilder<ReportSchedule>().BuildEntity(reader, reportSchedule) : null;
                    }

                    if (reportSchedule == null)
                    {
                        //int returnCode = GetReturnCodeFromParameter(cmd);

                        //switch (returnCode)
                        //{
                        //    case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                        //        {
                        //            throw new ArgumentException("reportSchedule already exists. Please specify another reportSchedule.");
                        //        }
                        //    default:
                        //        {
                        //            throw new SystemException("An unexpected error has occurred while creating this reportSchedule.");
                        //        }
                        //}
                    }

                    return reportSchedule;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IReportScheduleDataAccess.Add (" + reportSchedule + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        ReportSchedule IReportScheduleDataAccess.Update(ReportSchedule reportSchedule)
        {
            try
            {
                const string sp = "ReportSchedule_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, reportSchedule.Id);
                    Database.AddInParameter(cmd, Dbstring+"iReportName", DbType.AnsiString, reportSchedule.ReportName);
                  //  Database.AddInParameter(cmd, Dbstring + "iReportType", DbType.AnsiString, reportSchedule.ReportType);
                    Database.AddInParameter(cmd, Dbstring+"iInfraObjectId", DbType.Int32, reportSchedule.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring+"iFromDate", DbType.AnsiString, reportSchedule.FromDate);
                    Database.AddInParameter(cmd, Dbstring+"iToDate", DbType.AnsiString, reportSchedule.ToDate);
                    Database.AddInParameter(cmd, Dbstring+"iBusinessServiceId", DbType.Int32, reportSchedule.BusinessServiceId);
                    Database.AddInParameter(cmd, Dbstring+"iParallelDrOpId", DbType.Int32, reportSchedule.ParallelDrOpId);
                    Database.AddInParameter(cmd, Dbstring+"iApplicationId", DbType.Int32, reportSchedule.AppId);
                    Database.AddInParameter(cmd, Dbstring+"iJobId", DbType.AnsiString, reportSchedule.JobId);
                    Database.AddInParameter(cmd, Dbstring+"iReceiverId", DbType.AnsiString, reportSchedule.ReceiverId);

                    Database.AddInParameter(cmd, Dbstring+"iScheduleTime", DbType.AnsiString, reportSchedule.ScheduleTime);
                    Database.AddInParameter(cmd, Dbstring+"iTime", DbType.AnsiString, reportSchedule.Time);
                    Database.AddInParameter(cmd, Dbstring + "iCompanyId", DbType.Int32, reportSchedule.CompanyId);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        reportSchedule = reader.Read() ? CreateEntityBuilder<ReportSchedule>().BuildEntity(reader, reportSchedule) : null;
                    }

                    if (reportSchedule == null)
                    {
                        //int returnCode = GetReturnCodeFromParameter(cmd);

                        //switch (returnCode)
                        //{
                        //    case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                        //        {
                        //            throw new ArgumentException("reportSchedule already exists. Please specify another reportSchedule.");
                        //        }
                        //    default:
                        //        {
                        //            throw new SystemException("An unexpected error has occurred while creating this reportSchedule.");
                        //        }
                        //}
                    }

                    return reportSchedule;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IReportScheduleDataAccess.Add (" + reportSchedule + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        ReportSchedule IReportScheduleDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ReportSchedule_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read() ? (CreateEntityBuilder<ReportSchedule>()).BuildEntity(reader, new ReportSchedule()) : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IReportScheduleDataAccess.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ReportSchedule> IReportScheduleDataAccess.GetAll()
        {
            try
            {
                const string sp = "ReportSchedule_GetAll";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ReportSchedule>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IReportScheduleDataAccess.GetAll" + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ReportSchedule> IReportScheduleDataAccess.GetByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "ReportSchedule_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iReportName", DbType.AnsiString, name);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ReportSchedule>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature ISiteDataAccess.GetByName(" + name + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IReportScheduleDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ReportSchedule_DeleteById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring+"iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageDeletedata,
                    "Error In DAL While Deleting ReportSchedule Entry : " + id + Environment.NewLine + "SYSTEM MESSAGE : " +
                    ex.Message, ex);
            }
        }
    }
}