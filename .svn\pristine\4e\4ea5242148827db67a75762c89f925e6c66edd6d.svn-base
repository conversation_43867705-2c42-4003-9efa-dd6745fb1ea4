﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using CP.Helper;
using SpreadsheetGear;
using System.Web.UI.WebControls;
using System.Data;
using Gios.Pdf;
using System.Drawing;
using System.Collections;
using System.Globalization;
using System.Configuration;
using CP.UI;
using CP.Common.Shared;
using CP.UI.Component;
using System.IO;
using log4net;

namespace CP.UI.Controls
{
    public partial class MultiserverprofilemoniReport : BaseControl
    {
        #region Variables

        // private string[] xlColumn = { "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "Z" };
        IWorkbookSet workbookSet = null;
        String ssFile = string.Empty;
        IWorkbook templateWorkbook = null;
        IWorksheet templateWorksheet = null;
        IRange cells = null;
        private readonly ILog _logger = LogManager.GetLogger(typeof(MultiserverprofilemoniReport));
        #endregion Variables

        public override void PrepareView()
        {




            var profilelist = Facade.GetAllVCenterProfileByType("2");


            if (profilelist != null)
            {
                //var result = from grp in profilelist
                //             where grp.ProfileType == "1"
                //             orderby grp.ProfileName ascending
                //             select new { Id = grp.Id, ProfileName = grp.ProfileName };
                //if (result != null)
                //{
                ddlvcprofile.DataSource = profilelist;
                ddlvcprofile.DataTextField = "ProfileName";
                ddlvcprofile.DataValueField = "Id";
                ddlvcprofile.DataBind();
                ddlvcprofile.Items.Insert(0, new ListItem("Select Profile Name", "0"));
                //}
                //else
                //    ddlvcprofile.Items.Insert(0, new ListItem("No profile Found", "0"));

            }
            else

                ddlvcprofile.Items.Insert(0, new ListItem("No profile Found", "0"));

        }


        private void ExcelReport()
        {
            _logger.Info("======Generating MultiServer Profile Details Report EXCEL View ======");
            _logger.Info(Environment.NewLine);
            int row = 4;
            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "MultiServer Profile Details";

            _cells["A1"].ColumnWidth = 7;
            _cells["A2:F4"].Interior.Color = Color.FromArgb(79, 129, 189);
            reportWorksheet.Cells["A2:F2"].Merge();

            _cells["A2"].Formula = "MultiServer Profile Monitor Report";
            _cells["A2"].Font.Color = Color.White;
            _cells["A2"].Font.Size = 15;
            _cells["A2"].HorizontalAlignment = HAlign.Right;
            _cells["A2"].Font.Bold = true;
            _cells["A2"].HorizontalAlignment = HAlign.Center;


            _cells["A2:F2"].Font.Size = 11;
            _cells["A4:F5"].Font.Size = 10;
            _cells["A2:F5"].Font.Color = Color.White;
            _cells["A2:F5"].Font.Name = "Cambria";

            reportWorksheet.Cells["A3:C3"].Merge();
            var dateTime = Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss")); //DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
            _cells["A3"].Formula = "  Report Generation Time: " + dateTime;
            _cells["A3"].Font.Bold = true;
            _cells["A3"].HorizontalAlignment = HAlign.Left;

            //_cells["C3"].Formula = ":  " + dateTime;
            //_cells["C3"].Font.Bold = true;
            //_cells["C3"].HorizontalAlignment = HAlign.Left;

            //_cells["B7"].Formula = "Profile Name : ";
            //_cells["B7"].Font.Bold = true;
            //_cells["B7"].HorizontalAlignment = HAlign.Right;
            //_cells["B7"].VerticalAlignment = VAlign.Center;
            //_cells["B7"].ColumnWidth = 23;

            _cells["E3"].Formula = "Profile Name : " + ddlvcprofile.SelectedItem.Text.ToString();
            _cells["E3"].Font.Bold = true;
            _cells["E3"].HorizontalAlignment = HAlign.Left;
            _cells["E3"].VerticalAlignment = VAlign.Center;
            _cells["E3"].ColumnWidth = 40;
          //  _cells["E3"].WrapText = true;

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 24, 10, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 450, 10, 120, 13);
            string strlogo = LoggedInUserCompany.CompanyLogoPath;

            if (strlogo != "" && File.Exists(HttpContext.Current.Server.MapPath(strlogo)))
            {
                reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(strlogo), 200, 10, 121, 13);
            }
            reportWorksheet.Cells["A1:F1"].RowHeight = 27;
            reportWorksheet.Cells["A2:F2"].RowHeight = 25;


            IList<vCenterMonitorStatus> Vcentermonidetails = new List<vCenterMonitorStatus>();

            if (ddlostype.Visible == true)
            {
                Vcentermonidetails = Facade.GetVCenterMonitorStatusByProfileIdAndOstype(Convert.ToInt32(ddlvcprofile.SelectedValue), 1, ddlostype.SelectedItem.Text);
            }
            else
            {

                Vcentermonidetails = Facade.GetVCenterMonitorStatusByProfileId(Convert.ToInt32(ddlvcprofile.SelectedValue), 1);
            }
            int strlength = 30;

            //if (Vcentermonidetails != null && Vcentermonidetails.Count > 0)
            if (Vcentermonidetails != null && Vcentermonidetails.Count > 0)
            {
                row++;

                var startcell = "A" + row.ToString();

                _cells["A" + row.ToString()].Formula = "SR No.";
                _cells["A" + row.ToString()].WrapText = true;
                _cells["A" + row.ToString()].Font.Bold = true;
                _cells["A" + row.ToString()].HorizontalAlignment = HAlign.Left;


                //_cells["B" + row.ToString()].Formula = "  Host Details";
                //_cells["B" + row.ToString()].Font.Bold = true;
                //_cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                //_cells["B" + row.ToString()].Font.Color = Color.Black;

                lblMsg.Text = "";


                //if (ddlusename.SelectedItem.Text == "All")
                //{
                //    lstUser = Facade.GetUserActivityByStartEndDate(startdt, enddt);
                //}
                //else
                //{
                //    string loginname = ddlusename.SelectedItem.ToString();
                //    lstUser = Facade.GetUserActivityByDate(loginname, startdt, enddt);
                //}

                //var getdetail = _facade.GetUserActivityByStartEndDate(startdt, enddt);


                //host start

                // row++;

                _cells["B" + row.ToString()].Formula = "Server";
                _cells["B" + row.ToString()].WrapText = true;
                _cells["B" + row.ToString()].Font.Bold = true;
                _cells["B" + row.ToString()].ColumnWidth = 30;
                _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["A" + row.ToString() + ":F" + row.ToString()].Interior.Color = Color.FromArgb(79, 129, 189);

                IRange range3 = reportWorksheet.Cells["A" + row.ToString() + ":F" + row.ToString()];
                IBorder border1 = range3.Borders[BordersIndex.EdgeBottom];
                border1.LineStyle = LineStyle.Continous;
                border1.Color = Color.Black;
                border1.Weight = BorderWeight.Thin;

                _cells["C" + row.ToString()].Formula = "IP Address";
                _cells["C" + row.ToString()].Font.Bold = true;
                _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["C" + row.ToString()].ColumnWidth = 30;

                //var productionlog1 = _cells.Range["C" + row.ToString()].EntireRow;
                //productionlog1.WrapText = true;

                _cells["D" + row.ToString()].Formula = "Status";
                _cells["D" + row.ToString()].Font.Bold = true;
                _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
                _cells["D" + row.ToString()].ColumnWidth = 30;

                //_cells["E" + row.ToString()].Formula = "Services";
                //_cells["E" + row.ToString()].Font.Bold = true;
                //_cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                //_cells["E" + row.ToString()].ColumnWidth = 30;

                int i = 0;
                _logger.Info("======" + Vcentermonidetails.Count + " Records Retrieve for MultiServer Profile Details Report ======");
                _logger.Info(Environment.NewLine);
                foreach (var vcmoni in Vcentermonidetails)
                // for (int x = 0; x < 4; x++)
                {


                    i++;
                    //_cells["E" + row.ToString()].Formula = "Power Status";
                    //_cells["E" + row.ToString()].Font.Bold = true;
                    //_cells["E" + row.ToString()].HorizontalAlignment = HAlign.Center;

                    //_cells["F" + row.ToString()].Formula = "Ready State";
                    //_cells["F" + row.ToString()].Font.Bold = true;
                    //_cells["F" + row.ToString()].HorizontalAlignment = HAlign.Center;

                    row++;

                    _cells["A" + row.ToString()].Formula = i.ToString(); ;
                    _cells["A" + row.ToString()].Font.Bold = true;
                    _cells["A" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    _cells["A" + row.ToString()].VerticalAlignment = VAlign.Top;
                   // _cells["A" + row.ToString()].ColumnWidth = 35;

                    _cells["B" + row.ToString()].Formula = vcmoni.ServerName != null ? vcmoni.ServerName : "N/A";
                    _cells["B" + row.ToString()].Font.Bold = true;
                    _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    _cells["B" + row.ToString()].VerticalAlignment = VAlign.Top;
                    _cells["B" + row.ToString()].WrapText = true;
                   // _cells["B" + row.ToString()].ColumnWidth = 35;

                    _cells["C" + row.ToString()].Formula = vcmoni.VMIP != null ? vcmoni.VMIP : "N/A";
                    _cells["C" + row.ToString()].Font.Bold = true;
                    _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    _cells["C" + row.ToString()].VerticalAlignment = VAlign.Top;
                   // _cells["C" + row.ToString()].ColumnWidth = 35;


                    string strstatus = vcmoni.PowerStatus != null ? vcmoni.PowerStatus : "N/A";

                    _cells["D" + row.ToString()].Font.Bold = true;
                    _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    _cells["D" + row.ToString()].VerticalAlignment = VAlign.Top;
                   // _cells["D" + row.ToString()].ColumnWidth = 35;

                    //if (strstatus != "UP")
                    //{
                    //    _cells["D" + row.ToString()].Formula = "↓ " + strstatus;
                    //    _cells["D" + row.ToString()].Font.Color = Color.Red;
                    //}
                    if (strstatus == "UP")
                    {
                        _cells["D" + row.ToString()].Font.Color = Color.Green;
                        _cells["D" + row.ToString()].Formula = "↑ " + strstatus;
                    }

                    else if (strstatus == "N/A")
                    {
                        _cells["D" + row.ToString()].Font.Color = Color.Black;
                        _cells["D" + row.ToString()].Formula = "N/A";
                    }

                    else
                    {
                        _cells["D" + row.ToString()].Formula = "↓ " + strstatus;
                         _cells["D" + row.ToString()].Font.Color = Color.Red;
                    }
                    _cells["C" + row.ToString()].ColumnWidth = 14;

                    //string strservices = string.Empty;
                    //if (vcmoni.vCenterServiceStatus != null && vcmoni.vCenterServiceStatus.Count > 0)
                    //{
                    //    var vcserv = from p in vcmoni.vCenterServiceStatus orderby p.ServiceStatus ascending select p;
                    //    foreach (var rp in vcserv)
                    //    {
                    //        string strservname = rp.ServiceName != null ? rp.ServiceName : "N/A";
                    //        string strrunstatus = rp.ServiceStatus != null ? rp.ServiceStatus : "N/A";



                    //        if (strrunstatus == "Stopped")
                    //            strrunstatus = "(↓ " + strrunstatus + ")";
                    //        else if (strrunstatus == "Running")
                    //            strrunstatus = "(↑ " + strrunstatus + ")";
                    //        else
                    //            strrunstatus = "(" + strrunstatus + ")";

                    //        strservices += strservname + " " + strrunstatus + ", ";



                    //        //column++;
                    //    }
                    //    strservices = strservices.Remove(strservices.Length - 2);
                    //    //  if (strservices.Length > strlength)
                    //    //     strlength = strservices.Length;
                    //    _cells["E" + row.ToString()].Formula = strservices;
                    //    _cells["E" + row.ToString()].Font.Bold = true;
                    //    _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    //    _cells["E" + row.ToString()].VerticalAlignment = VAlign.Top;
                    //   // _cells["E" + row.ToString()].ColumnWidth = 35;
                    //    //  _cells["E" + row.ToString()].WrapText = true;
                    //    // _cells["E" + row.ToString()].ColumnWidth = strlength + 10;
                    //}
                    //else
                    //{
                    //    _cells["E" + row.ToString()].Formula = "N/A";
                    //    _cells["E" + row.ToString()].Font.Bold = true;
                    //    _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    //    _cells["E" + row.ToString()].VerticalAlignment = VAlign.Top;
                    //    _cells["E" + row.ToString()].WrapText = true;
                    //   // _cells["E" + row.ToString()].ColumnWidth = 35;
                    //    // _cells["E" + row.ToString()].ColumnWidth = strlength;
                    //}

                    //   row = row + 1;

                    //_cells["D" + row.ToString()].Formula = vcmoni.VMIP != null ? vcmoni.VMIP : "NA";
                    //_cells["D" + row.ToString()].Font.Bold = true;
                    //_cells["D" + row.ToString()].HorizontalAlignment = HAlign.Center;
                    //_cells["D" + row.ToString()].VerticalAlignment = VAlign.Center;
                    //_cells["D" + row.ToString()].WrapText = true;

                    //string strstatus = vcmoni.PowerStatus != null ? vcmoni.PowerStatus : "NA";
                    //_cells["E" + row.ToString()].Formula = strstatus;
                    //_cells["E" + row.ToString()].Font.Bold = true;
                    //_cells["E" + row.ToString()].HorizontalAlignment = HAlign.Center;
                    //_cells["E" + row.ToString()].VerticalAlignment = VAlign.Center;

                    //if (strstatus != "PoweredOn")
                    //    _cells["E" + row.ToString()].Font.Color = Color.Red;
                    //else
                    //    _cells["E" + row.ToString()].Font.Color = Color.Green;


                    //string strstate = vcmoni.ReadyState != null ? vcmoni.ReadyState : "NA";
                    //if (strstate == "Running")
                    //{
                    //    _cells["F" + row.ToString()].Formula = "✔";
                    //    _cells["F" + row.ToString()].Font.Color = Color.Green;

                    //}
                    //else
                    //{
                    //    _cells["F" + row.ToString()].Formula = "✖";
                    //    _cells["F" + row.ToString()].Font.Color = Color.Red;
                    //}

                    //_cells["F" + row.ToString()].HorizontalAlignment = HAlign.Center;
                    //_cells["F" + row.ToString()].VerticalAlignment = VAlign.Center;
                    //_cells["F" + row.ToString()].ColumnWidth = 25;
                    //_cells["F" + row.ToString()].WrapText = true;



                    //row = row + 2;
                    ////end host

                    ////for (int y = 0; y < 4; y++)
                    //if (vcmoni.vCenterServiceStatus != null && vcmoni.vCenterServiceStatus.Count > 0)
                    //{
                    //    _cells["B" + row.ToString()].Formula = "  Service Status";
                    //    _cells["B" + row.ToString()].Font.Bold = true;
                    //    _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Left;
                    //    _cells["B" + row.ToString()].Font.Color = Color.Black;

                    //    row++;
                    //    //int row = 8;
                    //    int i = 1;

                    //    _cells["B" + row.ToString()].Formula = "Sr.No.";
                    //    _cells["B" + row.ToString()].Font.Bold = true;
                    //    _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Center;
                    //    _cells["B" + row.ToString() + ":D" + row.ToString()].Interior.Color = Color.FromArgb(160, 160, 160); 
                    //    _cells["B" + row.ToString() + ":D" + row.ToString()].Font.Color = Color.White;

                    //    IRange range = reportWorksheet.Cells["B" + row.ToString() + ":D" + row.ToString()];
                    //    IBorder border = range.Borders[BordersIndex.EdgeBottom];
                    //    border.LineStyle = LineStyle.Continous;
                    //    border.Color = Color.Black;
                    //    border.Weight = BorderWeight.Thin;

                    //    _cells["C" + row.ToString()].Formula = "Service Name";
                    //    _cells["C" + row.ToString()].Font.Bold = true;
                    //    _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Center;

                    //    var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
                    //    productionlog.WrapText = true;

                    //    _cells["D" + row.ToString()].Formula = "Running Status";
                    //    _cells["D" + row.ToString()].Font.Bold = true;
                    //    _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Center;



                    //    row++;
                    //    int dataCount = 0;


                    //    dataCount++;


                    //    foreach (var rp in vcmoni.vCenterServiceStatus)
                    //    {
                    //        int column = 0;
                    //        int xlRow = 12;
                    //        string[] xlColumn = { "B", "C", "D" };
                    //        xlRow++;

                    //        string ndx = xlColumn[column] + row.ToString();
                    //        _cells[ndx + ":" + "D" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(192, 192, 192) : Color.White;



                    //        _cells[ndx].Formula = i.ToString();
                    //        _cells[ndx].Font.Size = 10;
                    //        _cells[ndx].ColumnWidth = 23;
                    //        _cells[ndx].Font.Color = Color.Black;
                    //        _cells[ndx].HorizontalAlignment = HAlign.Center;
                    //        _cells[ndx].VerticalAlignment = VAlign.Center;
                    //        i++;
                    //        column++;

                    //        ndx = xlColumn[column] + row.ToString();
                    //        _cells[ndx].Formula = rp.ServiceName != null ? rp.ServiceName : "N/A";
                    //        _cells[ndx].Font.Size = 10;
                    //        _cells[ndx].ColumnWidth = 23;
                    //        _cells[ndx].Font.Color = Color.Black;
                    //        _cells[ndx].HorizontalAlignment = HAlign.Center;
                    //        _cells[ndx].VerticalAlignment = VAlign.Center;
                    //        _cells[ndx].WrapText = true;
                    //        column++;

                    //        ndx = xlColumn[column] + row.ToString();
                    //        _cells[ndx].Formula = rp.ServiceStatus != null ? rp.ServiceStatus : "N/A";
                    //        _cells[ndx].Font.Size = 10;
                    //        _cells[ndx].ColumnWidth = 23;
                    //        _cells[ndx].Font.Color = Color.Black;
                    //        _cells[ndx].HorizontalAlignment = HAlign.Center;
                    //        _cells[ndx].VerticalAlignment = VAlign.Center;
                    //        _cells[ndx].WrapText = true;

                    //        string strrunstatus = rp.ServiceStatus != null ? rp.ServiceStatus : "N/A";

                    //        if (strrunstatus == "Stopped")
                    //            _cells[ndx].Font.Color = Color.Red;
                    //        else if (strrunstatus == "Running")
                    //            _cells[ndx].Font.Color = Color.Green;
                    //        else
                    //            _cells[ndx].Font.Color = Color.Black;

                    //        column++;



                    //        row++;
                    //    }
                    //}


                    var endcell = "F" + (row).ToString();
                    IRange rangebox = reportWorksheet.Cells[startcell + ":" + endcell];
                    IBorder b1 = rangebox.Borders[SpreadsheetGear.BordersIndex.EdgeLeft];
                    IBorder b2 = rangebox.Borders[SpreadsheetGear.BordersIndex.EdgeRight];
                    IBorder b3 = rangebox.Borders[SpreadsheetGear.BordersIndex.EdgeTop];
                    IBorder b4 = rangebox.Borders[SpreadsheetGear.BordersIndex.EdgeBottom];

                    b1.LineStyle = LineStyle.Continous;
                    b2.LineStyle = LineStyle.Continous;
                    b3.LineStyle = LineStyle.Continous;
                    b4.LineStyle = LineStyle.Continous;

                    b1.Color = Color.Gray;
                    b2.Color = Color.Gray;
                    b3.Color = Color.Gray;
                    b4.Color = Color.Gray;

                    b1.Weight = BorderWeight.Thin;
                    b2.Weight = BorderWeight.Thin;
                    b3.Weight = BorderWeight.Thin;
                    b4.Weight = BorderWeight.Thin;
                    // rangebox.Borders.LineStyle = LineStyle.DashDot;

                    //rangebox.Borders.Color = Color.Gray;
                    //rangebox.Borders.Weight = BorderWeight.Medium;

                }

                int finalCount = row + 3;
                _cells["B" + finalCount].Formula = "N/A : Not Available";
                _cells["B" + finalCount].HorizontalAlignment = HAlign.Center;
                _cells["B" + finalCount].Font.Name = "Cambria";
                _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

                _cells["A:F"].EntireColumn.AutoFit();
                // reportWorksheet.ProtectContents = true;
                OpenExcelFile(reportWorkbook); 
                _logger.Info("====== MultiServer Profile Details EXCEL Report generated ======");
                _logger.Info(Environment.NewLine);

            }
            else
            {

                lblMsg.Visible = true;
                lblMsg.Text = "No Records Found";
                _logger.Info("====== MultiServer Profile Details EXCEL Report generated ======");
                _logger.Info(Environment.NewLine);
            }


        }


        private void OpenExcelFile(IWorkbook workbook)
        {
            //txtReportMessage.Text = string.Empty;
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=report.xls");
            string _str = DateTime.Now.ToString("ddMMyyy");
            _str = "MultiServerProfileMonitorReport_" + ddlvcprofile.SelectedItem.Text + "_" + _str + ".xls";

            //  foreach (IWorksheet ws in workbook.Worksheets)
            //ws.ProtectContents = true;

            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + _str), FileFormat.Excel8);
            //string myUrl = "/ExcelFiles/" + _str;
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + _str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=MultiServer Profile Monitor Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }


        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void BtnPdfSaveClick(object sender, EventArgs e)
        {
            try
            {
                ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }


        protected void ddldependencyprofile_SelectedIndexChanged(object sender, EventArgs e)
        {
            //try
            //{



            //    IList<AppDependencyLinks> hostlist = new List<AppDependencyLinks>();

            //    hostlist = Facade.GetAllApplicationDependencyMappingLinksByDependencyProfile(ddldependencyprofile.SelectedItem.Text);

            //    var hlist = (from p in hostlist select p.SourceHost).Distinct().ToList();

            //    //var hlist=((from hl in hostlist select hl).Distinct().ToList();
            //    //    hostlist = hostlist.Distinct().ToList();

            //    if (hlist != null)
            //    {
            //        ddlsourcehost.DataSource = hlist;
            //        //ddlsourcehost.DataTextField = "SourceHost";
            //        //ddlsourcehost.DataValueField = "SourceHost";

            //        //ddldiscoveryprofile.DataValueField = profilelist;
            //        ddlsourcehost.DataBind();
            //        ddlsourcehost.Items.Insert(0, new ListItem("Select Source Host", "0"));
            //    }
            //    else
            //    {
            //        ddlsourcehost.Items.Insert(0, new ListItem("No profile Found", "0"));
            //    }
            //}
            //catch (Exception)
            //{


            //}

        }

        protected void ddlvcprofile_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlvcprofile.SelectedItem.Text.ToLower() == "show by os")
            {
                lblostype.Visible = true;
                ddlostype.Visible = true;

            }
            else
            {
                lblostype.Visible = false;
                ddlostype.Visible = false;

            }
        }
    }
}