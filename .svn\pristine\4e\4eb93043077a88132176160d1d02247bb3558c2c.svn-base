﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CreateInfraObjectJobType.aspx.cs"
    Inherits="CP.UI.Admin.CreateInfraObjectJobType" %>

<!DOCTYPE html>
<html>
<head id="Head1" runat="server">
    <title>Create Job Type</title>

    <link rel="stylesheet/less" href="../App_Themes/CPTheme/CPMaster.less" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet" />
    <script src="../Script/jquery.min.js"></script>
    <script src="../Script/less.min.js"></script>
    <script src="../Script/jquery.slimscroll.min.js"></script>
    <script src="../Script/bootstrap.min.js"></script>
    <script src="../Script/bootstrap-select.js"></script>
    <script src="../Script/bootstrap-select.init.js"></script>
    <script src="../Script/Custom-chkbox-rdbtn.js"></script>

</head>
<body>
    <form id="form1" runat="server">
        <asp:ScriptManager ID="ScriptManager1" runat="server">
        </asp:ScriptManager>
        <asp:UpdatePanel ID="UpdatepanelAdd" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <div class="modal-body padding-none-bottom">

                    <div class="row">
                        <div class="col-xs-12 form-horizontal uniformjs">
                            <asp:Label ID="lblSuccecMessage" runat="server" CssClass="text-success" Text="" Visible="false"></asp:Label>
                            <div class="form-group">
                                <label class="col-xs-3 control-label">
                                    Job Category<span class="inactive">*</span></label>
                                <div class="col-xs-9">
                                    <asp:TextBox ID="txtBcmsClassName" Visible="false" CssClass="form-control" Width="72%" runat="server"
                                        MaxLength="30"></asp:TextBox>
                                    <asp:DropDownList ID="ddlJobCategory" runat="server" AutoPostBack="True" CssClass="selectpicker col-xs-9" data-style="btn-default" OnSelectedIndexChanged="ddlJobCategory_SelectedIndexChanged" Style="margin-bottom: 0px">

                                        <asp:ListItem Value="0">- Select Job Category -</asp:ListItem>
                                        <asp:ListItem Value="2">Solution Type DB/App</asp:ListItem>
                                        <%--<asp:ListItem Value="1">InfraObject Application</asp:ListItem>
                                        <asp:ListItem Value="3">StorageImageID</asp:ListItem>
                                        <asp:ListItem Value="InfraobjectWithStorageImageID" Enabled="false">InfraobjectWithStorageImageID</asp:ListItem>
                                        <asp:ListItem Value="4">BusinessServiceId</asp:ListItem>--%>
                                    </asp:DropDownList>
                                      <asp:RequiredFieldValidator ID="RequiredFieldValidator1" CssClass="error" runat="server" ControlToValidate="ddlJobCategory"
                                         InitialValue="0"   Display="Dynamic" ErrorMessage="Select Job category"></asp:RequiredFieldValidator>


                                </div>

                            </div>
                 

                        
                              <asp:Panel ID="pnlrepltype" runat="server">
                                <div class="form-group" id="replcat" runat="server">
                                    <label class="col-xs-3 control-label">
                                        Replication Type<span class="inactive">*</span></label>
                                    <div class="col-xs-9">

                                       
                                             <asp:DropDownList ID="ddlRepType" runat="server" AutoPostBack="true" OnSelectedIndexChanged="CblstRepTypeSelectedIndexChanged"
                                            CssClass="selectpicker col-xs-9" data-style="btn-default">
                                                  
                                        </asp:DropDownList>
                                         <asp:RequiredFieldValidator ID="RequiredFieldValidator2" CssClass="error" runat="server" ControlToValidate="ddlRepType"
                                            Display="Dynamic" ErrorMessage="Select Replication Type" InitialValue="0"></asp:RequiredFieldValidator>
                                 
                                    </div>
                                </div>
                            </asp:Panel>

                            <asp:Panel ID="pnlbusiness" runat="server">
                                <div class="form-group" runat="server" id="divInfraType">
                                    <asp:Label ID="lblbuisness" CssClass="col-xs-3 control-label" runat="server" Visible="true"><span class="inactive">*</span></asp:Label>
                                
                                    <div class="col-xs-9">
                                        <asp:Panel ID="pnlGroup" runat="server" Height="101px" class="padding pull-left"
                                            Width="72%" ScrollBars="Vertical" BorderColor="#cccccc" BorderStyle="Solid"
                                            BorderWidth="1px" Visible="True" TabIndex="7">
                                            <asp:CheckBoxList ID="cblgroup" runat="server" AutoPostBack="true" OnSelectedIndexChanged="CblstInfraSelectedIndexChanged">
                                               
                                            </asp:CheckBoxList>

                                           

                                        </asp:Panel>

                                        <asp:Label ID="lblError" runat="server" CssClass="error" Visible="false"></asp:Label>
                                      
                                        <asp:Label ID="lblchkListMobile" runat="server" Text=""></asp:Label>
                                        <asp:Label ID="lblSelectItem" runat="server" class="error" ForeColor="Red" Text=" Please select item."
                                            Visible="false"></asp:Label>


                                        <asp:Panel ID="pnlStorage" runat="server" Height="101px" class="padding pull-left"
                                            Width="72%" ScrollBars="Vertical" BorderColor="#cccccc" BorderStyle="Solid"
                                            BorderWidth="1px" Visible="false" TabIndex="7">
                                            <asp:CheckBoxList ID="cblstorage" runat="server" AutoPostBack="False">
                                            </asp:CheckBoxList>
                                        </asp:Panel>

                                        <asp:Panel ID="PnlGroupstorage" runat="server" Height="101px" class="padding pull-left"
                                            Width="72%" ScrollBars="Vertical" BorderColor="#cccccc" BorderStyle="Solid"
                                            BorderWidth="1px" Visible="false" TabIndex="7">
                                            <asp:CheckBoxList ID="cblgroupstorage" runat="server" AutoPostBack="False">
                                            </asp:CheckBoxList>
                                        </asp:Panel>

                                        <asp:Panel ID="pnlBs" runat="server" Height="101px" class="padding pull-left"
                                            Width="72%" ScrollBars="Vertical" BorderColor="#cccccc" BorderStyle="Solid"
                                            BorderWidth="1px" Visible="false" TabIndex="7">
                                            <asp:CheckBoxList ID="cblBs" runat="server" AutoPostBack="true" OnSelectedIndexChanged="CblstBusinessServiceIndexChanged">
                                            </asp:CheckBoxList>
                                        </asp:Panel>



                                        <asp:Label ID="labelSDErrormessage" runat="server" CssClass="error" Style="color: Red; display: inline;"
                                            Visible="false"></asp:Label>
                                        
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-xs-3 control-label"  >
                                        Job Type <span class="inactive">*</span></label>
                                    <div class="col-xs-9">
                                        <asp:Panel ID="pnlJob" runat="server" Height="101px" class="padding pull-left"
                                            Width="72%" ScrollBars="Vertical" BorderColor="#cccccc" BorderStyle="Solid"
                                            BorderWidth="1px" Visible="True" TabIndex="7">
                                            <asp:CheckBoxList ID="cblJobType" runat="server" AutoPostBack="true" OnSelectedIndexChanged="CblstJobSelectedIndexChanged">
                                            </asp:CheckBoxList>
                                        </asp:Panel>
                                        <asp:Label ID="lerrorlbl" runat="server" CssClass="error" Visible="false"></asp:Label>
                                    </div>
                                </div>
                            </asp:Panel>
                            
                            <div class="form-group">
                                <label class="col-xs-3 control-label">
                                    Time Interval<span class="inactive">*</span></label>
                                <div class="col-xs-9">
                                    <asp:RadioButtonList ID="rblTimeInterval" runat="server" RepeatDirection="Horizontal"
                                        OnSelectedIndexChanged="rblTimeInterval_SelectedIndexChanged" AutoPostBack="true">
                                        <asp:ListItem>Minutes</asp:ListItem>
                                        <asp:ListItem>Hourly</asp:ListItem>
                                        <asp:ListItem>Daily</asp:ListItem>
                                      
                                    </asp:RadioButtonList>
                                    <asp:TextBox ID="txtCE" runat="server" Width="0px" Visible="false"></asp:TextBox>
                                    <asp:TextBox ID="txtCETime" runat="server" Width="0px" Visible="false"></asp:TextBox>
                                </div>
                            </div>
                            <asp:Panel ID="Panel_Minuite" runat="server" CssClass="form-group">
                                <label class="col-xs-3 control-label">
                                    Set Time<span class="inactive">*</span>
                                </label>
                                <div class="col-xs-9">
                                    <asp:Label ID="lblEveryMinuite" runat="server" Text="Every"></asp:Label>
                                    <asp:TextBox ID="txteveryminuite" runat="server"  CssClass="form-control"
                                        Width="15%"></asp:TextBox>
                                    <asp:RegularExpressionValidator ID="rfName" runat="server" ControlToValidate="txteveryminuite"
                                        Display="Dynamic" ErrorMessage="Enter number only" CssClass="error" ValidationGroup="ValidationRepType" SetFocusOnError="True"
                                        ValidationExpression="^([0-9]*,?)*$"></asp:RegularExpressionValidator>
                                      <asp:RequiredFieldValidator ID="rfvtxteveryhourlyminuite" runat="server" Enabled="true"
                                    ControlToValidate="txteveryminuite" Display="Dynamic" ForeColor="Red"
                                    CssClass="error" ErrorMessage="Please Enter Minutes"></asp:RequiredFieldValidator>
                              
                                <asp:RangeValidator ID="rng" runat="server" CssClass="error" ControlToValidate="txteveryminuite" Display="Dynamic"
                                     ErrorMessage="Minutes Should Be Less Than 60" Type="Integer"  MaximumValue="60" MinimumValue="0"></asp:RangeValidator>
                                    <asp:Label ID="lblminuites" runat="server" Text="Minute(s)"></asp:Label>

                                </div>
                            </asp:Panel>

                            <asp:Panel ID="Panel_Hourly" runat="server" CssClass="form-group" Visible="false">
                                <label class="col-xs-3 control-label">
                                    Set Time<span class="inactive">*</span>
                                </label>
                                <div class="col-xs-9">
                                    <asp:Label ID="lblEveryHourly" runat="server" Text="Every"></asp:Label>
                                    <asp:TextBox ID="txteveryhour" runat="server" Width="15%" CssClass="form-control"
                                        MaxLength="4"></asp:TextBox>
                                    <asp:Label ID="lblhours" runat="server" Text="Hour(s)" CssClass="margin-right"></asp:Label>
                                    <asp:TextBox ID="txteveryhourlyminuite" runat="server" Width="15%" CssClass="form-control"
                                        MaxLength="4"></asp:TextBox>
                                    <asp:Label ID="lblhourlyminuites" runat="server" Text="Minute(s)"></asp:Label>


                                </div>
                            </asp:Panel>
                            <asp:Panel ID="Panel_Daily" runat="server" CssClass="form-group" Visible="false">
                                <label class="col-xs-3 control-label">
                                    Set Time<span class="inactive">*</span>
                                </label>
                                <div class="col-xs-9">
                                  
                                    <asp:Label ID="lblEverydaily" runat="server" class="pull-left form-control-static">Every &nbsp; </asp:Label>
                                    <asp:TextBox ID="txteverydaily" runat="server" Width="15%" class="form-control pull-left"></asp:TextBox>
                                    <asp:Label ID="lbldays" runat="server" Text="Day(s) " CssClass="padding pull-left"></asp:Label>
                                    
                                    <asp:Label ID="lblstartTime" runat="server" Text="StartTime " class="pull-left padding"></asp:Label>
                                    <asp:DropDownList ID="ddlhours" runat="server" CssClass="selectpicker col-xs-2" data-style="btn-default">
                                        <asp:ListItem Value="0">00</asp:ListItem>
                                        <asp:ListItem>01</asp:ListItem>
                                        <asp:ListItem>02</asp:ListItem>
                                        <asp:ListItem>03</asp:ListItem>
                                        <asp:ListItem>04</asp:ListItem>
                                        <asp:ListItem>05</asp:ListItem>
                                        <asp:ListItem>06</asp:ListItem>
                                        <asp:ListItem>07</asp:ListItem>
                                        <asp:ListItem>08</asp:ListItem>
                                        <asp:ListItem>09</asp:ListItem>
                                        <asp:ListItem>10</asp:ListItem>
                                        <asp:ListItem>11</asp:ListItem>
                                        <asp:ListItem>12</asp:ListItem>
                                        <asp:ListItem>13</asp:ListItem>
                                        <asp:ListItem>14</asp:ListItem>
                                        <asp:ListItem>15</asp:ListItem>
                                        <asp:ListItem>16</asp:ListItem>
                                        <asp:ListItem>17</asp:ListItem>
                                        <asp:ListItem>18</asp:ListItem>
                                        <asp:ListItem>19</asp:ListItem>
                                        <asp:ListItem>20</asp:ListItem>
                                        <asp:ListItem>21</asp:ListItem>
                                        <asp:ListItem>22</asp:ListItem>
                                        <asp:ListItem>23</asp:ListItem>
                                    </asp:DropDownList>
                                    <asp:DropDownList ID="ddlminuites" runat="server" CssClass="selectpicker col-xs-2"
                                        data-style="btn-default">
                                        <asp:ListItem Value="0">00</asp:ListItem>
                                        <asp:ListItem>01</asp:ListItem>
                                        <asp:ListItem>02</asp:ListItem>
                                        <asp:ListItem>03</asp:ListItem>
                                        <asp:ListItem>04</asp:ListItem>
                                        <asp:ListItem>05</asp:ListItem>
                                        <asp:ListItem>06</asp:ListItem>
                                        <asp:ListItem>07</asp:ListItem>
                                        <asp:ListItem>08</asp:ListItem>
                                        <asp:ListItem>09</asp:ListItem>
                                        <asp:ListItem>10</asp:ListItem>
                                        <asp:ListItem>11</asp:ListItem>
                                        <asp:ListItem>12</asp:ListItem>
                                        <asp:ListItem>13</asp:ListItem>
                                        <asp:ListItem>14</asp:ListItem>
                                        <asp:ListItem>15</asp:ListItem>
                                        <asp:ListItem>16</asp:ListItem>
                                        <asp:ListItem>17</asp:ListItem>
                                        <asp:ListItem>18</asp:ListItem>
                                        <asp:ListItem>19</asp:ListItem>
                                        <asp:ListItem>20</asp:ListItem>
                                        <asp:ListItem>21</asp:ListItem>
                                        <asp:ListItem>22</asp:ListItem>
                                        <asp:ListItem>23</asp:ListItem>
                                        <asp:ListItem>24</asp:ListItem>
                                        <asp:ListItem>25</asp:ListItem>
                                        <asp:ListItem>26</asp:ListItem>
                                        <asp:ListItem>27</asp:ListItem>
                                        <asp:ListItem>28</asp:ListItem>
                                        <asp:ListItem>29</asp:ListItem>
                                        <asp:ListItem>30</asp:ListItem>
                                        <asp:ListItem>31</asp:ListItem>
                                        <asp:ListItem>32</asp:ListItem>
                                        <asp:ListItem>33</asp:ListItem>
                                        <asp:ListItem>34</asp:ListItem>
                                        <asp:ListItem>35</asp:ListItem>
                                        <asp:ListItem>36</asp:ListItem>
                                        <asp:ListItem>37</asp:ListItem>
                                        <asp:ListItem>38</asp:ListItem>
                                        <asp:ListItem>39</asp:ListItem>
                                        <asp:ListItem>40</asp:ListItem>
                                        <asp:ListItem>41</asp:ListItem>
                                        <asp:ListItem>42</asp:ListItem>
                                        <asp:ListItem>43</asp:ListItem>
                                        <asp:ListItem>44</asp:ListItem>
                                        <asp:ListItem>45</asp:ListItem>
                                        <asp:ListItem>46</asp:ListItem>
                                        <asp:ListItem>47</asp:ListItem>
                                        <asp:ListItem>48</asp:ListItem>
                                        <asp:ListItem>49</asp:ListItem>
                                        <asp:ListItem>50</asp:ListItem>
                                        <asp:ListItem>51</asp:ListItem>
                                        <asp:ListItem>52</asp:ListItem>
                                        <asp:ListItem>53</asp:ListItem>
                                        <asp:ListItem>54</asp:ListItem>
                                        <asp:ListItem>55</asp:ListItem>
                                        <asp:ListItem>56</asp:ListItem>
                                        <asp:ListItem>57</asp:ListItem>
                                        <asp:ListItem>58</asp:ListItem>
                                        <asp:ListItem>59</asp:ListItem>
                                    </asp:DropDownList>
                                    <div class="clearfix"></div>


                                </div>
                            </asp:Panel>
                          

                        </div>
                    </div>
                </div>
               

                <div class="modal-footer" style="padding-bottom: 0px ! important;">
                    <div class="form-group">
                        <div class="col-xs-4 text-left">
                            <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                        Fields</span>

                            <asp:Label ID="lblupdatestatus" runat="server" Text=""></asp:Label>
                          
                        </div>
                        <div class="col-xs-6 text-right">
                            <asp:Button ID="btnSave" CssClass="btn btn-primary" Width="40%" Style="margin-right:5px;" runat="server" Text="Save"
                                OnClick="btnSave_Click"  />
                          
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </form>
    <script type="text/javascript">
        function pageLoad() {
            $('input[type="checkbox"]').checkbox();
            radiobutton();
            if ($('.selectpicker').length)
                $('.selectpicker').selectpicker();
        }
    </script>
</body>
</html>
