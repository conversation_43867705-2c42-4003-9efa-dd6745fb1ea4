﻿using System.Web.UI.WebControls;
using CP.Common.DatabaseEntity;

namespace CP.UI
{
    public abstract class LoadBalancerEditor : LoadBalancerConfig
    {
        public CPLoadMaster CurrentEntity
        {
            get
            {
                return CurrentNode;
            }
            set
            {
                CurrentNode = value;
            }
        }
        public abstract string MessageInitials
        {
            get;
        }
        public int CurrentEntityId
        {
            get { return CurrentNodeId; }
            set { CurrentNodeId = 0; }
        }

        public abstract string ReturnUrl
        {
            get;
        }

        #region Method

        public Server BuildEntity(Server currentEntity)
        {
            return currentEntity;
        }

        public abstract void PrepareEditView();

        public abstract void SaveEditor();

        public virtual void PrepareValidator()
        {
        }

        public abstract void BuildEntities();

        public virtual void BindList()
        {
        }

        public virtual void Delete(int entityId)
        {
        }

        public virtual void ClearForm()
        {
            CurrentEntityId = 0;
        }

        public virtual void FinalizeCommand()
        {
        }

        #endregion Method
    }
}