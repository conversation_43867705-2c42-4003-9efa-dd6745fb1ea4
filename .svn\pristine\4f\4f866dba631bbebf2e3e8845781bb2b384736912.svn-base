﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.ExceptionHandler;
using Gios.Pdf;
using SpreadsheetGear;

namespace CP.UI.Controls
{
    public partial class AuditReport : BaseControl
    {
        int creatorid;

        public override void PrepareView()
        {
            IList<User> userall = new List<User>();
            userall = Facade.GetAlUsers();

            if (userall != null)
            {
                ddlusename.DataSource = userall;
                ddlusename.DataTextField = "LoginName";
                ddlusename.DataValueField = "Id";
                ddlusename.DataBind();
                ddlusename.Items.Insert(0, "All");
            }
            else
            {
                ddlusename.Items.Insert(0, new ListItem("No User Name Found", "0"));
            }

            Pan1.Visible = false;
            btnview.Visible = true;
            btnExcel.Visible = false;
            btnPdf.Visible = false;

        }

        private DataTable screenreport()
        {
            var table = new DataTable();
            table.Columns.Add("Sr.No.");
            table.Columns.Add("Module Name");
            table.Columns.Add("Description");
            table.Columns.Add("ActionType");
            table.Columns.Add("User Name");
            table.Columns.Add("Time Stamp");


            var stdt = Convert.ToDateTime(txtstart.Text);
            var startdt = stdt.ToString("yyyy-MM-dd");
            var endt = Convert.ToDateTime(txtend.Text);
            var enddt = endt.ToString("yyyy-MM-dd");

            IList<Audit> auditdata = new List<Audit>();

            if (ddlusename.SelectedItem.Text == "All" && ddlaudittype.SelectedItem.Text == "All")
            {
                auditdata = Facade.GetAllByDate(startdt, enddt);

            }
            else if (ddlusename.SelectedItem.Text == "All" && ddlaudittype.SelectedItem.Text != "All")
            {
                int auditp = Convert.ToInt32(ddlaudittype.SelectedItem.Value);
                auditdata = Facade.GetAllByAuditType(auditp, startdt, enddt);

            }
            else if (ddlaudittype.SelectedItem.Text == "All" && ddlusename.SelectedItem.Text != "All")
            {
                creatorid = Convert.ToInt32(ddlusename.SelectedItem.Value);
                auditdata = Facade.GetAllByUserID(creatorid, startdt, enddt);

            }
            else
            {
                creatorid = Convert.ToInt32(ddlusename.SelectedItem.Value);
                int auditp = Convert.ToInt32(ddlaudittype.SelectedItem.Value);
                auditdata = Facade.GetAuditDataByUserIdnAuditType(auditp, creatorid, startdt, enddt);
            }


            if (auditdata != null && auditdata.Count != 0)
            {

                var trow = new TableRow();
                tbl.Rows.Add(trow);
                trow.Height = 30;


                var sno = new TableCell { Text = "Sr.No", CssClass = "RowStyleHeaderNo bold" };
                trow.Cells.Add(sno);
                var Tbnm = new TableCell { Text = "Module Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(Tbnm);
                var Dscption = new TableCell { Text = "Description", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(Dscption);
                var actiontp = new TableCell { Text = "ActionType", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(actiontp);
                var uname = new TableCell { Text = "User Name", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(uname);
                var createdate = new TableCell { Text = "Time Stamp", CssClass = "rowStyleHeader bold" };
                trow.Cells.Add(createdate);


                int i = 1;
                foreach (var auditDataBind in auditdata)
                {

                    var tbrow = new TableRow();
                    tbrow.Height = 20;
                    tbrow.BackColor = i % 2 == 0 ? Color.White : Color.FromArgb(219, 229, 241);
                    tbl.Rows.Add(tbrow);
                    DataRow dr = table.NewRow();

                    int cid = auditDataBind.CreatorId;
                    var logindetail = Facade.GetLoginNameByCrtrId(cid);
                    var name = logindetail.LoginName;

                    dr["Sr.No."] = i.ToString();
                    var no = new TableCell { Text = i.ToString(), CssClass = "RowStyleNo" };
                    tbrow.Cells.Add(no);

                    dr["Module Name"] = auditDataBind.TableName;
                    var lnm = new TableCell { Text = auditDataBind.TableName != null ? auditDataBind.TableName : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(lnm);

                    dr["Description"] = auditDataBind.Description.ToString();
                    var enti = new TableCell { Text = auditDataBind.Description != null ? auditDataBind.Description : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(enti);

                    dr["ActionType"] = auditDataBind.AuditType.ToString();
                    var actioty = new TableCell { Text = auditDataBind.AuditType != null ? auditDataBind.AuditType.ToString() : "NA", CssClass = "rowStyle1" };
                    tbrow.Cells.Add(actioty);

                    dr["User Name"] = name.ToString();
                    var hst = new TableCell { Text = name.ToString(), CssClass = "rowStyle1" };
                    tbrow.Cells.Add(hst);

                    dr["Time Stamp"] = Utility.Formatdate(Convert.ToDateTime(auditDataBind.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")); // auditDataBind.CreateDate.ToString();
                    var cdate = new TableCell { Text = auditDataBind.CreateDate != DateTime.MinValue ? Utility.Formatdate(Convert.ToDateTime(auditDataBind.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA", CssClass = "rowStyle1" };
                  //  Convert.ToString(auditDataBind.CreateDate)
                    tbrow.Cells.Add(cdate);

                    i++;
                    table.Rows.Add(dr);
                }

            }
            else
            {
                lblMsg.Visible = true;
                lblMsg.Text = "No Records Found";
            }

            return table;
        }

        protected void btnview_Click1(object sender, EventArgs e)
        {
            try
            {
                var stdt = Convert.ToDateTime(txtstart.Text);
                var startdt = stdt.ToString("dd-MM-yy");
                var endt = Convert.ToDateTime(txtend.Text);
                var enddt = endt.ToString("dd-MM-yy");

                string dat = DateTime.Now.ToString("yyyy-MM-dd");
                if (Convert.ToDateTime(startdt) > Convert.ToDateTime(enddt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "Start Date Greater than End Date";
                    Pan1.Visible = false;
                    return;
                }
                else if (Convert.ToDateTime(dat) < Convert.ToDateTime(startdt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "Start Date can't Greater than Today Date";
                    Pan1.Visible = false;
                    return;
                }
                else if (Convert.ToDateTime(dat) < Convert.ToDateTime(enddt))
                {
                    lblMsg.Visible = true;
                    lblMsg.Text = "End Date can't Greater than Today Date";
                    Pan1.Visible = false;
                    return;
                }

                Pan1.Visible = true;

                screenreport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }

            btnPdf.Visible = true;
            btnExcel.Visible = true;
        }

        private void CreatePdfReport(DataTable table)
        {
            var myPdfDocument = new PdfDocument(PdfDocumentFormat.InCentimeters(21, 29.7));
            var count = table.Rows.Count;
            PdfTable myPdfTable = myPdfDocument.NewTable(new Font("Verdana", 7), count, 6, 3);
            myPdfTable.ImportDataTable(table);
            myPdfTable.HeadersRow.SetColors(Color.White, Color.FromArgb(79, 129, 189));

            myPdfTable.SetBorders(Color.Black, 0.1, BorderType.None);
            myPdfTable.SetColors(Color.Black, Color.FromArgb(219, 229, 241), Color.White);
            myPdfTable.SetColumnsWidth(new[] { 5, 20, 16, 20, 20, 20 });
            myPdfTable.SetContentAlignment(ContentAlignment.MiddleCenter);
            myPdfTable.Columns[1].SetContentAlignment(ContentAlignment.MiddleCenter);


            PdfImage logoImage = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"));
            PdfImage logoBcms = myPdfDocument.NewImage(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"));

            var pta = new PdfTextArea(new Font("Verdana", 9, FontStyle.Bold), Color.Black
                    , new PdfArea(myPdfDocument, 0, 20, 595, 80), ContentAlignment.MiddleCenter, "Audit Report");


            var RGT = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 14, 00, 200, 190), ContentAlignment.MiddleRight, "Report Generated Time : " + Utility.Formatdate(Convert.ToDateTime(DateTime.Now).ToString("MM-dd-yyyy HH:mm:ss"))); //+ DateTime.Now.ToString("dd-MMM-yyyy HH:mm"));

            var from = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 180, 00, 150, 190), ContentAlignment.MiddleRight, "From Date: " + Utility.Formatdate(Convert.ToDateTime(txtstart.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"));

            var to = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 230, 00, 200, 190), ContentAlignment.MiddleRight, "To Date: " + Utility.Formatdate(Convert.ToDateTime(txtend.Text).ToString("MM-dd-yyyy HH:mm:ss"), "dd-MM-yyyy"));

            var notavailable = new PdfTextArea(new Font("Verdana", 8, FontStyle.Bold), Color.Black
                     , new PdfArea(myPdfDocument, 17, 15, 100, 190), ContentAlignment.MiddleRight, "NA : Not Available");



            int pgNo = 1;
            while (!myPdfTable.AllTablePagesCreated)
            {
                PdfPage newPdfPage = myPdfDocument.NewPage();
                PdfTablePage newPdfTablePage =
                        myPdfTable.CreateTablePage(new PdfArea(myPdfDocument, 48, 115, 500, 670));

                var pageNumber = new PdfTextArea(new Font("Verdana", 8, FontStyle.Regular), Color.Black
                   , new PdfArea(myPdfDocument, 50, 0, 450, 1600), ContentAlignment.MiddleRight, "Page Number :   " + pgNo++.ToString());

                newPdfPage.Add(logoImage, 460, 25, 180);
                newPdfPage.Add(logoBcms, 50, 25, 110);
                newPdfPage.Add(newPdfTablePage);
                newPdfPage.Add(pta);


                newPdfPage.Add(from);
                newPdfPage.Add(to);
                newPdfPage.Add(RGT);
                newPdfPage.Add(notavailable);
                newPdfPage.Add(pageNumber);
                newPdfPage.SaveToDocument();
            }

            string str = DateTime.Now.ToString().Replace("/", "");
            str = str.Replace(":", "");
            str = str.Substring(0, str.Length - 5);
            str = System.Text.RegularExpressions.Regex.Replace(str, @"\s", "");
            str = "Audit Report" + str + ".pdf";
            string filePath = HttpContext.Current.Server.MapPath(@"~/PdfFiles/" + str);
            //string myUrl = "/PdfFiles/" + str;
            myPdfDocument.SaveToFile(filePath);
            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/PdfFiles/" + str;
            string fullURL = "window.open('" + myUrl + "', '_blank', 'height=500,width=800,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Audit Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);

        }

        protected void btnPdf_Click(object sender, EventArgs e)
        {
            try
            {
                var table = screenreport();
                CreatePdfReport(table);
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        private void ExcelReport()
        {
            IWorkbookSet workbookSet = null;
            String ssFile = string.Empty;
            IWorkbook templateWorkbook = null;
            IWorksheet templateWorksheet = null;
            IRange _cells = null;

            workbookSet = Factory.GetWorkbookSet();
            ssFile = HttpContext.Current.Server.MapPath("../SampleWorkbook/ParallelDrOperationReport.xls");
            templateWorkbook = workbookSet.Workbooks.Open(ssFile);
            templateWorksheet = templateWorkbook.Worksheets[0];

            IWorkbook reportWorkbook = workbookSet.Workbooks.Add();
            IWorksheet reportWorksheet = null;
            IWorksheet lastWorksheet = null;

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            lastWorksheet = reportWorkbook.Worksheets[reportWorkbook.Worksheets.Count - 1];
            reportWorksheet = (IWorksheet)templateWorksheet.CopyAfter(lastWorksheet);

            reportWorkbook.Worksheets["Sheet1 (2)"].Delete();
            reportWorkbook.Worksheets["Sheet1"].Delete();

            _cells = reportWorksheet.Cells;
            reportWorksheet.WindowInfo.DisplayGridlines = false;
            reportWorksheet.Name = "Audit Report";

            _cells["A1"].ColumnWidth = 7;

            _cells["E3"].Formula = "Audit Report";
            _cells["E3"].HorizontalAlignment = HAlign.Center;
            _cells["E3"].Font.Bold = true;
            _cells["B3:H6"].Interior.Color = Color.FromArgb(79, 129, 189);
            _cells["D3"].Font.Bold = true;
            _cells["D3"].ColumnWidth = 30;
            _cells["D3"].HorizontalAlignment = HAlign.Center;
            _cells["D3"].VerticalAlignment = VAlign.Top;
            _cells["B3:H3"].Font.Size = 11;
            _cells["B5:H8"].Font.Size = 10;
            _cells["B3:H8"].Font.Color = Color.White;
            _cells["B3:H8"].Font.Name = "Cambria";


            IFacade _facade = new Facade();
            var stdt = Convert.ToDateTime(txtstart.Text);
            var startdt = stdt.ToString("yyyy-MM-dd");
            var endt = Convert.ToDateTime(txtend.Text);
            var enddt = endt.ToString("yyyy-MM-dd");

            IList<Audit> auditdata = new List<Audit>();

            if (ddlusename.SelectedItem.Text == "All" && ddlaudittype.SelectedItem.Text == "All")
            {
                auditdata = Facade.GetAllByDate(startdt, enddt);
            }
            else if (ddlusename.SelectedItem.Text == "All" && ddlaudittype.SelectedItem.Text != "All")
            {
                int auditp = Convert.ToInt32(ddlaudittype.SelectedItem.Value);
                auditdata = Facade.GetAllByAuditType(auditp, startdt, enddt);

            }
            else if (ddlaudittype.SelectedItem.Text == "All" && ddlusename.SelectedItem.Text != "All")
            {
                creatorid = Convert.ToInt32(ddlusename.SelectedItem.Value);
                auditdata = Facade.GetAllByUserID(creatorid, startdt, enddt);

            }
            else
            {
                creatorid = Convert.ToInt32(ddlusename.SelectedItem.Value);
                int auditp = Convert.ToInt32(ddlaudittype.SelectedItem.Value);
                auditdata = Facade.GetAuditDataByUserIdnAuditType(auditp, creatorid, startdt, enddt);
            }

            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/ICICI_164.jpg"), 48, 10, 150, 22);
            reportWorksheet.Shapes.AddPicture(HttpContext.Current.Server.MapPath(@"~/Images/Perpetuuiti_logo_New.jpg"), 690, 10, 120, 13);
            reportWorksheet.Cells["A1:F1"].RowHeight = 27;
            reportWorksheet.Cells["A2:F2"].RowHeight = 25;

            _cells["B6"].Formula = "From Date";
            _cells["B6"].Font.Bold = true;
            _cells["B6"].HorizontalAlignment = HAlign.Left;

            _cells["C6"].Formula = ":  " + txtstart.Text;
            _cells["C6"].Font.Bold = true;
            _cells["C6"].HorizontalAlignment = HAlign.Left;

            var dateTime = DateTime.Now.ToString("dd-MMM-yyyy HH:mm");
            _cells["B5"].Formula = "Report Generated Time";
            _cells["B5"].Font.Bold = true;
            _cells["B5"].HorizontalAlignment = HAlign.Left;

            _cells["C5"].Formula = ":  " + dateTime;
            _cells["C5"].Font.Bold = true;
            _cells["C5"].HorizontalAlignment = HAlign.Left;

            _cells["E6"].Formula = "To Date";
            _cells["E6"].Font.Bold = true;
            _cells["E6"].HorizontalAlignment = HAlign.Left;

            _cells["F6"].Formula = ":  " + txtend.Text;
            _cells["F6"].Font.Bold = true;
            _cells["F6"].HorizontalAlignment = HAlign.Left;

            int row = 8;
            int i = 1;

            _cells["B" + row.ToString()].Formula = "Sr.No.";
            _cells["B" + row.ToString()].Font.Bold = true;
            _cells["B" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["B8:H8"].Interior.Color = Color.FromArgb(79, 129, 189);

            IRange range = reportWorksheet.Cells["B8:H8"];
            IBorder border = range.Borders[BordersIndex.EdgeBottom];
            border.LineStyle = LineStyle.Continous;
            border.Color = Color.Black;
            border.Weight = BorderWeight.Thin;

            _cells["C" + row.ToString()].Formula = "Module Name";
            _cells["C" + row.ToString()].Font.Bold = true;
            _cells["C" + row.ToString()].HorizontalAlignment = HAlign.Center;

            var productionlog = _cells.Range["C" + row.ToString()].EntireRow;
            productionlog.WrapText = true;

            _cells["D" + row.ToString()].Formula = "Description";
            _cells["D" + row.ToString()].Font.Bold = true;
            _cells["D" + row.ToString()].HorizontalAlignment = HAlign.Center;
            _cells["D" + row.ToString()].WrapText = true;

            _cells["E" + row.ToString()].Formula = "Action Type";
            _cells["E" + row.ToString()].Font.Bold = true;
            _cells["E" + row.ToString()].HorizontalAlignment = HAlign.Center;

            _cells["F" + row.ToString()].Formula = "User Name";
            _cells["F" + row.ToString()].Font.Bold = true;
            _cells["F" + row.ToString()].HorizontalAlignment = HAlign.Center;

            _cells["G" + row.ToString()].Formula = "Time Stamp";
            _cells["G" + row.ToString()].Font.Bold = true;
            _cells["G" + row.ToString()].HorizontalAlignment = HAlign.Center;


            row++;
            int dataCount = 0;
            int xlRow = 9;

            foreach (var auditDataBind in auditdata)
            {
                int cid = auditDataBind.CreatorId;
                var logindetail = Facade.GetLoginNameByCrtrId(cid);
                var name = logindetail.LoginName;

                dataCount++;
                int column = 0;
                string[] xlColumn = { "B", "C", "D", "E", "F", "G", "H" };
                xlRow++;

                string ndx = xlColumn[column] + row.ToString();
                _cells[ndx + ":" + "H" + row].Interior.Color = i % 2 == 0 ? Color.FromArgb(219, 229, 241) : Color.White;



                _cells[ndx].Formula = i.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                i++;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = auditDataBind.TableName != null ? auditDataBind.TableName : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = auditDataBind.Description != null ? auditDataBind.Description : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                _cells[ndx].WrapText = true;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = auditDataBind.AuditType.ToString(); ;
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                _cells[ndx].WrapText = true;
                column++;

                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = name.ToString();
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;


                ndx = xlColumn[column] + row.ToString();
                _cells[ndx].Formula = auditDataBind.CreateDate != null ? Utility.Formatdate(Convert.ToDateTime(auditDataBind.CreateDate).ToString("MM-dd-yyyy HH:mm:ss")) : "NA";
                _cells[ndx].Font.Size = 10;
                _cells[ndx].ColumnWidth = 23;
                _cells[ndx].Font.Color = Color.Black;
                _cells[ndx].HorizontalAlignment = HAlign.Center;
                column++;

                row++;
            }

            int finalCount = dataCount + 10;
            _cells["B" + finalCount].Formula = "NA : Not Available";
            _cells["B" + finalCount].HorizontalAlignment = HAlign.Center;
            _cells["B" + finalCount].Font.Name = "Cambria";
            _cells["B" + finalCount + ":" + "D" + finalCount].Font.Size = 10;

            reportWorksheet.ProtectContents = true;
            OpenExcelFile(reportWorkbook);

        }

        private void OpenExcelFile(IWorkbook workbook)
        {
            Response.Clear();
            Response.ContentType = "application/vnd.ms-excel";
            Response.AddHeader("Content-Disposition", "attachment; filename=DataLag.xls");
            var str = DateTime.Now.ToString("ddMMyyy_hhmmss");
            str = "AuditReport" + str + ".xls";
            workbook.SaveAs(HttpContext.Current.Server.MapPath(@"~/ExcelFiles/" + str), FileFormat.Excel8);

            string reportPath = ConfigurationSettings.AppSettings["ReportPath"];
            string myUrl = reportPath + "/ExcelFiles/" + str;
            var fullURL = "window.open('" + myUrl + "', '_blank', 'height=600,width=400,status=yes,toolbar=no,menubar=no,location=no,scrollbars=yes,resizable=no,titlebar=Audit Report');";
            ScriptManager.RegisterStartupScript(this, typeof(string), "OPEN_WINDOW", fullURL, true);
        }

        protected void btnExcel_Click(object sender, EventArgs e)
        {
            try
            {
                screenreport();
                ExcelReport();
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured to Generate Report", ex, Page);
                ExceptionManager.Manage(customEx);
            }
        }

        protected void ddlusename_SelectedIndexChanged(object sender, EventArgs e)
        {
            lblMsg.Visible = false;
        }

    }
}