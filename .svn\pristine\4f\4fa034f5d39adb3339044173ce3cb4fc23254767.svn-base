﻿using System;

namespace CP.UI
{
    public partial class MSSQLDatabaseConfig : System.Web.UI.UserControl
    {
        public string DatabaseSID
        {
            get
            {
                return txtdtSID.Text;
            }
            set
            {
                txtdtSID.Text = value;
            }
        }

        public string UserName
        {
            get
            {
                return txtUserName.Text;
            }
            set
            {
                txtUserName.Text = value;
            }
        }

        public string Password
        {
            get
            {
                return txtPassword.Text;
            }
            set
            {
                txtPassword.Text = value;
            }
        }

        public string Port
        {
            get
            {
                return txtPort.Text;
            }
            set
            {
                txtPort.Text = value;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            txtdtSID.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidatortxtdtSID.ClientID + ")");
            //txtUserName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
            //txtPassword.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            txtPassword.Attributes.Add("onblur", "getHashData(" + txtPassword.ClientID + ")");
            txtPort.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");
            rbtnListAuthenticationMode.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");
            //txtDataFileLocation.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator5.ClientID + ")");
            //txtTransactionLocation.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator6.ClientID + ")");
            //txtUndofile.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator7.ClientID + ")");
            //txtBackupRestorePath.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator8.ClientID + ")");
            //txtNetworkSharedPath.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator9.ClientID + ")");
        }

        protected void chkconfig_CheckedChanged(object sender, EventArgs e)
        {
            if (chkconfig.Checked == true)
            {
                divinstance.Visible = true;
                txtinstaname.Text = string.Empty;
            }
            else
                divinstance.Visible = false;


        }

        protected void ChkSSOEnable_CheckedChanged(object sender, EventArgs e)
        {

            if (ChkSSOEnable.Checked)
            {
                Utility.PopulateSSOProfile(ddlSSOProfile, true, Convert.ToInt32(3));
                pnlSSoProfilrDrp.Visible = true;
                //chkconfig.Checked = true;
                //divinstance.Visible = true;
                txtinstaname.Text = string.Empty;
                rbtnListAuthenticationMode.SelectedIndex = 1;
                //dvusername.Visible = true;
                //dvpassword.Visible = true;
                spnUserNm.Visible = false;
                spnPassword.Visible = false;
                lblUserNmErrorMsg.Visible = false;
                lblPassErrorMsg.Visible = false;


            }
            else
            {

                pnlSSoProfilrDrp.Visible = false;
                //chkconfig.Checked = false;
                rbtnListAuthenticationMode.SelectedIndex = 0;
                //dvusername.Visible = false;
                //dvpassword.Visible = false;
                //divinstance.Visible = false;
                spnUserNm.Visible = true;
                spnPassword.Visible = true;

                //sshkeypassword = txtSSHKeyPassword.Text;
            }
        }

        protected void rbtnListAuthenticationMode_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (rbtnListAuthenticationMode.SelectedIndex == 0)
            {

                ChkSSOEnable.Checked = false;
                pnlSSoProfilrDrp.Visible = false;
                chkconfig.Checked = false;
                divinstance.Visible = false;
            }
            else
            {

                ChkSSOEnable.Checked = false;
                pnlSSoProfilrDrp.Visible = false;
                chkconfig.Checked = false;
                divinstance.Visible = false;
            }
        }
    }
}