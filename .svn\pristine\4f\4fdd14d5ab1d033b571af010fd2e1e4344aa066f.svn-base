﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox4.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAEFSURBVDhPrdK9SsNgFIdxraBeh6sgdHBQEHFR2sGh
        Ct3Ezb03IHXQ2UGcnMQLKPQivAVBsBZsoVPBz0rh9XngDYTQNil44Je8Pcn5p/lYCCHoBkXrHs4s4tbF
        NZ4xin6nGOMVR3DuBAMXloNn2MP+FBWU4cwaTtFKAj6wAX/ncXAI/0nJhvWJLWRPzjrGD56wjWWbVpGA
        GnwODm/G3pUbKy+gine8YCf2VtB3Yc0KOMQ3fAO7sadVdF1YkwJ8zz7tB3RxgPTx3ABP8Kp+ZOsoIXt8
        ZsASGvAW0v1EbkCat5PtzRUwyf8GzPMpp3XcJHWHc1wU0MQlvgyoo40e3uK+iH4I4fEPdjuU1XCEtXYA
        AAAASUVORK5CYII=
</value>
  </data>
  <data name="pictureBox3.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAADpSURBVEhLtZTBEYMwDAR5pJpUk3rSCQWkLkpxfA7n
        OQvhYAGPZRiNpLWUmCmldCtu8Ep+j3ke5ZFJK+81tiEqeGWWDAV4B5vcqAAnZnMC6TPT5EYFenoraXIj
        Aq7DEyDeSEYFWAHwmpNmTdEJvMYkPAFOxvUQNrXxT6bUjQhwMv2n4F0Fbt6IwLInaFCBjngUFbiogMl/
        iwy2TukKsII66g7I6QlPTWBlgMLaoyfoQYk3pX6nTgvwybYC3IGaNypg43qRDLyMNf+IgE1B74fHNNq8
        1KpAGyneGixeXaEK7sQNXokbvI40fQFbkZN6Qsb9PgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pictureBox6.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAACWSURBVDhP7ZFRDYQwEEQbDCAFCUhBAhKQ0I8TgIST
        ghQkcDOXaViWsoF/Jnkf7c7bNGmK0nyamej4LBBHsIms63uBMBi5MGgcB8XOiZZetXpQoLwawcNZp/ox
        GLRgUTGCS1ppe3B5Ry6wuy/Bgd9VK0Z8i5zd4AkzF/SO6DWcHfr/V9jgcgI1mUyqXYclJ1neBacFKf0A
        gFTUT5OrlpIAAAAASUVORK5CYII=
</value>
  </data>
  <data name="pictureBox5.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAACSSURBVDhP7ZJdDYAwDITrYBKQgASkIGVSkIAUJCAB
        CeMOSjKa7od3Lvkett7XEEBskkgEqUDUWjksGSnnX+AtwOVkWIAnE85efS6oCS2W5ylWM+hhu2QGh8CL
        bNiC3aD6HV6AXQs1DjCo9g4GoxY8kXA2at0PCny7nkxmrdXDohH75ScQ8h/q/lxfQxGsenQicgKO5hK0
        nt5EHAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pictureBox7.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAF3SURBVDhPbdPLK4RRGMfxiZGQJpdQNmSh5JJcIlti
        YU2isLaRy/9gISsbCztZDpJLvCwspFASG5dBLmWDkgUpvr/pPNOZYfGp5zznOee85/KGIpPBf1qwgR/n
        E/OoQFJtUgPFmMYDDrGKRWiyU1xhHNn4M0E1YrhAH4rg95dhFB8IkI/EBCq+xh4KXc7y9cjxcjV4xBrC
        lpyBPi/PtaUX53jFAVphfY3QlwyroQN7Qg+soA4vsEOUM+TCarTovgLt5xjxPTlD8AebBliNFrlXoI4V
        lzSDSB0sOg+rycSlAnUsuKSpwjP8wUdIXB/ScaLgCzpR6zDd0CC9iW3Uwu/XzcQUaHWddgn8AslCZUrO
        tOFWQTluMAK1I2jHBEpdTtc2hmZkuJxe6KYC0fN8wyx0I9/Qvpeg533n2u/YxRz0DrpsAh3OOvxDE/0X
        YWx5OTOFpH+hAFFYgR6SbaHD5fzB8a34E0gaBrADvbwmKN8P/WjL6HQ5BKFfHdynrf3Uq5gAAAAASUVO
        RK5CYII=
</value>
  </data>
  <data name="pictureBox8.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAPCAYAAADtc08vAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAH6SURBVDhPddJLaBNBHMfxycZsEltLREREETx68QEK
        ihYjVWhTo7eK4KGCikfBUy+CtrV5KIIHwUIPogj1AUJUFDSPbYWWCIb6SJWm0UaloVZ8tVabdv3+wwaW
        EA8fZnaW33/+s7PqRF9auKBZdNuas8aa1n51WAvHsrppmkrtvzAgHFWjBo81d1rkuT7IO39XouH64Hv9
        x+95pQJRYxVacRRHLC3w4RDa4IGOJfA1nU96O++9cRujU14pkMAsTJtF3MdxjGEIayBFvHu6E55QLOsr
        LSw6pEABcwjhGH6iUughghjHS6yH4gi+a0be+Tz/VZcCE5CQtL0WRdi7eQI/XiOLDU09SXX6ZsY3MT1T
        LvAB0vIkZCd7uMKAbJBpjRq5xs74xv6hgip+n9OkwAPMoFbQbhgBpPf2JPNn7rzaxBHKt7AOUqRWqFoG
        B5ojxjM6+ZzOTW+WAmI1pM1aoWpXCG/defapeerGi7sSdkBG+R8eo1ao4hP8zeFUOHhxoDT47kubBF1w
        Q+YrEcP/wrvYPbq7O/63N547PFb8Vf4GQn6Q5dZ8BW7DHs5jBy6jxDUe7OgfUSOFb7pqiRgu1MOJBuio
        wy2Y+Ijt6MVsIGIEGs/FVV9qvO7P/IJDcRYNMi6FF064sQzt2AIHTmIb1L5Qynvp0VvNGJ1y/wNENQYN
        rVQcZQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pictureBox9.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAJbSURBVDhPY/j//z8Yf/nyheHDl5+M7z98Ynr37h0D
        CAPZDB+//gLy3zOi8t8x/vz5E6wPbkBaZg6Dafb6BHXLAFcpKSkGEAayWZ0r9zbJyCkKSklJMqiaejDY
        l+2pFRfm49i8cT2qAcFhkQwyEcsmcckYJzNAAY+CFY9OxtYDDIwsEgwMjAzKfq2uPnX730k6lhRsOXiR
        EdWAkGAGyZAFrSKmcdFQ/Qz8yjbcelnbdjEys4kxMLAwBDTsW/nmw5f/lYuuP9l06rkMigFpTcvknIo3
        XQxpP31MWD8YaCOKAeKMTKwMxmkr4irmnv1jkbNu0fZTjzhRDCiZezlu9+mH/w9d//hfwbctCtkAJlYO
        cRCfT8WBQzVl814GRjaJNWvXgvXBDcifekzZs3rPraC6Hac4JbTBGtAN4Fey5gLydwKZYqtXr0Q1ICw6
        gUEsdEkvh4JtAkgxCGAYAOUDmeKrV69CNSA0PIpBLmpZD9AWuAG88qasWulbtwGZfCA+v7ItHgMiokEG
        9AJtSQQpZmBiZhA1i1e3LNx5UUjDxY2Fk5+BT9GCBAMY2Ri8qvfMvXj71f/qVc/vs0kai/BIG7ASbQAj
        EzuDa8WulnVHH/+Pbt+/k5lfjpdXzpSNBAOYGcTM4sWUkredYRVR0waJERMGfXAvAAEwEDn0srZuZ2Bk
        FALxgXIgA3YDmZgGePkFMggELJjAIGqQAlIMBqIG3IqJG/cDWeCUCeUfAPGXLVuKasCUaTMYCqcft8oo
        adGOi4tjAGEgm6l64cWAhKRkbiR+GJDNc+rUSaC+/wwAhiSI3svU9VQAAAAASUVORK5CYII=
</value>
  </data>
  <data name="pictureBox10.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAALMSURBVDhPY/j//z9W/PffPwaXiiW58nGTr8nETFzU
        tfoYLzZ1GAIwPHnjaS+fuhVVzcsO20Z1bEhyKl+c/eL9Vw50dSgcGD57+7mgbvrMKvuSRfVJfZv3+tat
        nKqcMKW5f90JfXS1KBwYPnb9CZ9dycJCnYyZVZqpM37pZc7aqpEyvaJtxRFNdLUoHBD+8esPw5+//xhK
        Zu0OB/p/o2H27AeaaTPO62fNarj+6A0buno4AxRovWtOhDiXL6lzqVxaapo7d0V427pur5rltWkTtjaY
        F8xf6FyxZNae8/eVsBrw+89fBpfyJRkSEf3/fOpWdl2891Ly6dvPDJHt6y2nbD7Dfen+S/bEvs2ZSvGT
        Dxy49BDuFbgBn7/9YnCtWJrvXbeiNb53k8urj195QOLP3n5mvvf8Pcvvv38ZCmbsMgDKF1kWzl9659k7
        LhQDVh66Kq2TPnPjlYevxYE2c3z78YsFJgfCoHB5+PID57vP3xlsixfO6Vl73A8kDlcwbcvZUM+a5b0g
        Z8PErj58zXHs2hNpGP/1x69MwDBgnbDhVFz53L2tIDG44gW7L2ZYFy2YFtu1wfHlh69MELFLGtLREy4C
        /RwN4r/79J0lqmO9j3HOnM625UeaUAyYvf18DNB/PVcevOIExQhI7MOXHyypE7fmyERPfLzvwoNYoLeA
        Xr0m1r7yaErNwgNtKAbM23VBDZhY1t5/8YH71++/jH/+/mUEiX8FampZetjaunDBIaA3PV6+/8rgXbui
        t2r+/lgUA95/+c5gV7JoSlz3pqLcaTtsH7/+JAiTA+EdZ+5IOZQujs2ast1bNWnqpjVHrouAxOEKQBgY
        QDKK8ZP3etYurwaGNjjjbDl1W/HXH4hr8qbt9BQL7zu2eO8lJ5geFANA+NCVR4ru1cumetWumDB50+kw
        YIYq6VlzIjasdW29femixSsOXrVDqP/PAACcOZPCdTVTNAAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pictureBox11.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAIASURBVDhPddNPSJNxHMfxXx485JSki9shMMKTdQhy
        hy4istJh8x/VoAiEWRO1P3TYLdzaxv6AitOQtsCREGvaOajNubZVWoQlkkQGG9hJhtOmqc/j5+ueJ55+
        bIcX7Hn4ft/PnufZWKtnvpgTYIcsiPCn8XEkMDS7rN7e3WeiKP7DL5aBEfzgggfQC3cvu2P2S66Y/8bE
        e/P39U1VscBxeAK0fF46d6TFHWNXx1JsMvKjrtk559G5YqFv6axGGSiHcbBJx//B1Vn3aJIdCALL5fdY
        /9Tn202OaHj1d65KHroOATgmHctqIIxAX+dIgu38Ldz/1s4euzX5wdo+/M5CQ9VAX/ssKJflwCcEbBTI
        SwHyajFT3WiPBmmInrYH+KtroAtuInCBD5Cu0eR9GtyAe6BcJmfgKeQReM4H6DPOGWiQ3jO9Kj5A6LYE
        BGZKBHQ0tA2D0gJPC/sIvCwRaKOhZ+CQFnhyIMwHBFHAM0iYaUgN9BZOAx+ohwMEpvnA13S2Qu+dD8iD
        ZqA3oVyuBR+ICKwg0K0MWF4s9V8ceuuUh1UQgjvSMTkFJriGQE/HSKKVfom0PBVf02sfvXkd/pjWyMOE
        bmUGrHBSOncEAWb0pdjizw3Vw+kvAw1YDsZ/naOYMkAqwQJBoH+iAXRwRe+N9+FP5DcFFhyzCxl14VZE
        dghPzhm5uVnMOQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pictureBox12.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAMWSURBVDhPY/j//z8G/vn7D4NHzfJ8meiJvySjJnxv
        XXY4BJs6EMYQ+PvvH0PfupOu/o2rVtUuPJhZNmdvkUf18lXHrz8xRlcLwhgCP3794bQpWrjEvnTRlPie
        TQdiuzbu1kqbsSque2Prtx+/MdRjCPz5+489vHXdNM3U6VO0Umd81k6f+VEtadryqvn7K9HVgjAK59fv
        v2C6ZPaeUOWEqTf1Mme9MMia/UUmasKVluWHzZHVwjCYAAVa45JDKb71K5fmTdvZpZ0245RzxZJFwa1r
        F4V3bljg37N9pU3VqjPFs3anf/n+kwnDgN9//jJkT91eIxbe99+uZNGeFQeu6j95/Ylh2d5Lpsnta1yv
        LlzGkFveYSceO+lu9rRdbV9//EI1AGgqQ1TH+iaH0sVrAptWFy3cc0kaJP545TrlVVIap89Iyc4qsfUP
        VE+fUKAY03O6e82JaBQDtp+5q6KUOPXS9tN3lB+//sQLxJwg8U8rVigcFpZ4flhd5/8uVf2bK+2chVpm
        bAi1LF925NHrj3xwA9YcvpaokTJt3ZM3n5lBfBD++vMPw/Ku6U67JOVfHlfT/n9YXuX/bSPTit1bjnBa
        Vi6/cu3RGyO4ARuP38xRjJ+8O7hlTdnDVx8FQWLrjt3klIibfDk2ovr/caALzimq/l+lbbw5uGxWs33V
        ylu3nrwFJyywAeuP3khTB7rg1K2n4n///gOH8ttP3xiBARslk7vkYXxE7f8jSpr/j+sZ9x7bd1LGr23D
        lWsPX5siG2Cknjzt/LvP3/iBUcr0/edvsFc2b9rAoGHlpaxetnJTtmPiu/MW5orL1u63NC5Zcvb203di
        cAPefPzGCIz3FcCkOzW2e2PK9C1nlEHiP79/ZXjz7CHD/ZcfWMpm7cyOq18YbVU4f0fZ3L0V/4B5Bm4A
        CO+9cF9WKWHKRfeqZWvuP38vBBK7/ey9/OnbLxRB7FN3XqgqJs+44tewavXzd5/ZYfrgBoDwoSuP5H3q
        ViwEumZn+Zy9xXnTd04CpotJ0R0b6uxKFh5OmbCl68X7LzwIPf8ZAGW0giR4psYQAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="pictureBox13.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAPCAYAAADtc08vAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAALySURBVDhPddN9MJNxAMDxH3+4qJNLpbv+SCmpoz/q
        XKFIeXtMGYcT4a5cKN3lLWacrXndGMM2KrZplnmLQ4222Z5nmNuYIv7xfnFyOOpyemH9nqvduW7++Nw9
        zx+/7/N7e4A/AzXmDBSK0FFrPzoKZle+A71eb5SxwQhUAdFgoBwGnAyBHej39g7Y3tnZM3Aaeg3Z4e8w
        gCAM9PmXjS2zjc2fQNQ/53qHPUB++2HRZq+AF1SEP/vRlRBq7c/AYjp1i9ZJQh3hAqm716tQodBOr1r9
        H7CBzCEL+FXCzQJlum8Rygoo6SsJKFHleeYrKsLZ6moBNhsUyVFXr3z7sW93IATKhw4idCWBUIwK00S6
        WInu87kEjizqSqooLrZS6v2ie9S0Y3DKCs6q/C5HXbD8dcvcEOD/C7giDKzRv1B+kvpKAbgdmgMuT2q7
        zt5nL3uk8MhEihg8rZGC9c1fIEk4Qo+qGnyIbygesIIyIQ2hGLvsky8FRKrYMZH9Lj7oWXOdR1pdr09m
        fSlVqLRjtg2bv1TMXOwZXbJ3o8paKK1jEYY9sIf4vkVKi0h2P0jkSFICKY01le2aU9yuoUs0EebuR65n
        pvGx687ZUrH807Lt0MyaQwirv9wQcIC4cAlmwaXokbD8FmYzNnGoWTXh6J4ioNd0jwBSrSyMxFekDkyu
        msL7YIKvv0o2GWAIWEI8GLANLFEev5XTwM+olR/GxuZPPOZIErkdWhBZ1BbOahuk4AOX1rdAAk/74BpN
        Xm8I4KLhDnODmej+OFZncmheUwyvZwTIddMgvUZ61CtDSBPKRn3xAO3NeJwrRcbv0C3Y7w7gFycrlKVK
        pgoxKyS7ISu+UhJO4vUiSLY4Pqa4PVr+cR48EgzfIzJVfO302jHDMe4WSmCguYFMDASXqcjeBfJGz1wp
        2yXnvaBRPXdVM7Vi4kSStAqwmfP4YGMBN4gFZ0KD/wANKcYcbjNVzuSmsRvEsr7q4LJ+UgRHzRxf2LD8
        G9CDPy79BPp/sV1aAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="sqlDataSource1.ParameterValues" xml:space="preserve">
    <value>{@iID:5}</value>
  </metadata>
  <data name="pictureBox2.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAQEAAAAAAAD/7gAOQWRvYmUAZAAAAAAB/9sAQwAGBAQEBQQGBQUGCQYFBgkLCAYG
        CAsMCgoLCgoMEAwMDAwMDBAMDg8QDw4MExMUFBMTHBsbGxwfHx8fHx8fHx8f/9sAQwEHBwcNDA0YEBAY
        GhURFRofHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgA
        HgDcAwERAAIRAQMRAf/EABwAAAEEAwEAAAAAAAAAAAAAAAYAAwUHAQQIAv/EAEMQAAEDAwIDBAYECgsB
        AAAAAAECAwQRBQYAEiETBzFBIhRRcYEyFQhhkTMXQlKCI5Ojs9MWV7FyorLDJDR01HU2N//EABoBAQEB
        AQEBAQAAAAAAAAAAAAABBQYEAgP/xAAtEQEAAQMCAgkDBQAAAAAAAAAAARECAwQFMRMhQVFhwdEScjPw
        IgZxkaHSNP/aAAwDAQACEQMRAD8A3umWG5nnlsn3H+NLjb/Ky1RuUFuu7qJSvdXmop79KaoMFdBs3CSU
        dQ7gVU8O4OgV9j2gYwXqhecaZy6yZnKN0exNPManJNVvBSw2Gtx4kqWtG0niKmvZoNCyWzrH1RjG/u34
        41ZHVK+HxI29O5KSRuAQUKUAeG5SuPcKaCR+4nqL/MKZ9b/73QaPUHG8mwPpXNddyadcbrKuEYGbzXUF
        DKd1G0VWoipqVcePs1B6tPRbqFcbVDuCc+mNpmMNvhvc+SkOoC6V5vGldUN33pv1XxCzS8hg5y/LVbWz
        JejOl3attsblCji3UK4dxHHQQkq/ZP1EzbGIkW9SrF8XtQckeVW4GkvMKeDiktpUj31NenQGP3C5p/MS
        4/U7+/0EU6c96V5hYGp+RO5BYr9I8o7HkFZWklSUlSUrUvaU8wEFKuPYdBu5HnWd5zmkzDMDkJtkC2qU
        i5Xn8KqFbVkLAO1O/wAKQnxK9IGg9fcV1GPE9Qple/i/2/pdBM4h0fyy05DFuV4zKbdIcUlfkd7yUuLp
        4N9XFApB40px0Ee90JzNx5xwdQrikLWpQTR3gCa0+20AT01wvNM2Yurv8a3GD8MlmJTe65voK7vtU09W
        gLMtx7KOnHTG+3JrKZ1xvDzsVLM11ShyW+aAUtpWp0Aq3HcdQXHYXnX7Jbn3lFbrsVlxxZ7SpTYJJ9Z0
        G9oOZ7z1Gzlm53ppq8vobj5SxCZSNvhjK525ocPdO0aDpjQeXSQ2sjtCSQfZoKAxvMcpuPS9q4zbpIen
        fG3WPMbtq+UluoRVO3w11r7PitvyXRdET9vk538lz348Ns2zNv39X6SsOX07v0qU9JRk0llD61OJaTvo
        kKNQkUcHZr6s3PFbbEcq2afXY/PJsee+6boz3RWa06f7BjHMfyC83a72/wDiGUx8Ld5XM3OK3+Jaa03i
        nua0dVqcWKyy/l2z647u7uY+g0WfUZcmPnXRy5p19PGO3uZ6kWO/Yp04vtxbv8t+aoR0R3gtbZaSX079
        viVxV2V9GsTWay3NERbZFlOx0+27bfp5mb8l2Svb1fzIxsVjYyHDsYmXApelswm3fMPIS64pT0RTZO5X
        H3nN/rGvA10d9zcLm8z4q/XyPwquxNfIV/09a+7s8Pp0AZ8vdrcunT/I4LUx+3vPXJYamxVbHWlhlspU
        k9/HtB7dWRv/AHUdaK0PUV3bXt2u1p9eoI3NOjTWN9K7+5DkvXa9yFsTLnOdHjcbYc3rCU1UQBuKzxJO
        qIbpv02veU4tDn2jqBLiNoRy3ra0Xf8ALLTw5dEvp4d44Co1AU/cNnH8xLh+v/5Gg0+seNSMb6Iotkq5
        SLtKTPZckT5SlKWtayo8N6llKQKACugJslx/N7h09sk7DrxIgXWHboxMFtSQ3JRyUkp8QO1z8U9/YdBW
        mE2bMOpseTbL1nElkRXNt1sTjZTICUqpWlUJUK8DUGh7RqgiTZLfYvmExWz25BbhQbQWmUk1VQNv8VHv
        UTxOgJMl6ZdSpV5fk2DOpUG2vHe3DfK3FNKPvJSoHij0V46gbx3ojcjkEO/5rkb+RS7aoKgsKCktIUk7
        kklRUTRXGgA49tdBUuG4ZdZee5BjSsnkYzdm5C1IS2VjzYDij2pcaqdqgodtQdUWT9w2cfzEuH6//kag
        KOn3TC+Yzd3rjdcpm30KaLTEZ5TqWkFRBUspU44FGgoPRoLC79BTHy0/6HK/+2X/AHdAQfMJEfk9KLwG
        k7i0WHVgdyEPIKj7BoCrBrpCumIWeZCdS6w5DZG5JrRSWwlST6ClQodBNPvssMrefcS0y2kqccWQlKUj
        iSSeAGg5DuTpl2m/ZQ0Cu1Ky5h4PAGhbAeVuHsWn69UddRJcaZFalxXUvRn0Bxl1BqlSVCoII1Bq366w
        rTZ5lxnPJYixmVuOOLIA8KSace89w0HPGHMPI6MQXnElKZV8edaqO1Owor9aTrb2P5bvb5OX/Kvgt9/h
        LpRr7JH9Uf0axZdPbwV904/9Zlv+5/xXdbe6fBh9vhDlth/1aj3eNxv5g/8A5RePWx+3RrDdUJ+nn/g8
        e/66N+yToCDQBvTDpyjBbVNgInqniZKVK5imw1tqhKNtApVfd7dAZaDC0JWkoWApKgQpJFQQeBBGgqG9
        /Ldjz9wdnY9dpmPOPElbMc7mgTxogVQtKfo3U0ESflwyOvDOptO7wu/v9WokXPl9kuYdPsj+SSJc6dJY
        e89JStxDbbG6jaW1OKpUrJKq6C2LNbzbrRBt5XzDDjtR+ZSm7lICK07q01AE5j0mFzyaHleOXH4DkcZQ
        58pDfMbkIpSjrdU1PcfSO3Qbb3TdyT1GtmcSLgBKgxPKuwW2vza1FC0laVlW5Iq52UOgN9AtAC9QejuK
        5q8ibLLsG7NJCEXGKQlZSPdDiSCF7e7vHp0AO58t98CqMZ1PCO4KS5UD2P6tRJ4p0GuNoyCHdLllk25R
        oa+b5Il1CXFp9zeS6vwhXEinHQW9qAN6bdOm8JZurSJ6p3xOWZZKmw3y6imzgpVfXoCyZEjTYj0SU0l6
        NIQpp5pYqlSFiikn1jQU1N+W9yNIdOMZVOtEJ1e/yVVqSn1KbW3X8oV+nVqGU/Lfd5n5m9ZrOmQq1XHA
        Wd36R1af7J0FmRenWJRsPViCIQNkcQUOtKJK1qUal1S+3mbhu3agrRz5cLpEUWrHmc+FAqS3GVv8Ffpb
        cbSfXtGqPUf5bJEx5v8AiTLZ1zhtqCvKjcK/lOLd2+xOgsK/YBBnY7b7FbFIt0K3LQWG0oK0hCElO3tH
        E7q117dBrORfN1K1ijL3bbp1eOLYu9NLqitCSlsJPcKfVrwy04ikIDHMSTZrrdrgJJeN0d5pb2bQjxKV
        StTX39e3U6zm2WW0p6I/dmaHbI0+XJf6q8ya8OHHzN9RMQVl+JTMfTKEMyy2fMFHMCeW4F+7VNa7fTrx
        NRKY5aTZ7BbrUXecYMZqOXqbd3KQE7qcaVpoJHQLQLQLQLQLQLQLQLQLQLQLQLQLQLQQF5YYcuadklbE
        nY39ml0qoHK/ghSePZoGH4N6LBBuRSslICg0soC+bxNAn8enf2fRoHJLMzzzmx4eZUy0UV5oUgJ+1S34
        CDv41UPEPRoGZMG9bUl64ulW47wlDgRxaWB7iOwHia8K+zQP+TyFLKx5/ehX2qloUCniCrYNnEUqBoHL
        YxeUy9qpC1w6OlS3EqrzSohCUhYSdu019HAUPHQNXmOhcthKpam5oaQC42h3cqj7ZJAbCkitCPb6NA05
        BvBYc3XJQUaCoaWUBXeogJ4nft7+zQZuUSW5MQWpy2biI7QbSlDigdpXzSSEn3k9nDtp9Ggy9DyXzBba
        nLA5Sj5haCU8yquGxKPxdvf29ldB7ZiZANyUTVKUSjmLdSvwiiCdgKAk8ARw9PHiNBh6Ldy6lT8spc/C
        Q2l0oA2rA3EIoeNDxpoCMaBaD//Z
</value>
  </data>
  <data name="pictureBox1.Value" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        /9j/4AAQSkZJRgABAgEAAAAAAAD/7gAOQWRvYmUAZAAAAAAB/9sAQwAGBAQEBQQGBQUGCQYFBgkLCAYG
        CAsMCgoLCgoMEAwMDAwMDBAMDg8QDw4MExMUFBMTHBsbGxwfHx8fHx8fHx8f/9sAQwEHBwcNDA0YEBAY
        GhURFRofHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8fHx8f/8AAEQgA
        LQDIAwERAAIRAQMRAf/EABsAAQACAwEBAAAAAAAAAAAAAAAFBgQHCAMC/8QALxAAAQMEAQMDAwMEAwAA
        AAAAAgEDBAARBQYSITEHQSITUWEIMkIUcYFSI5EkFv/EABoBAQADAQEBAAAAAAAAAAAAAAACAwQFBgH/
        xAAuEQACAgIABAQFBAMBAAAAAAAAAQIDEQQhMRIFQVEiE/BhcYHRocHxQpEyFAb/2gAMAwEAAhEDEQA/
        AOqaAUAoBQFK8h+VcBpjHxvf9zLuDyYxzZIhcf8AN0uqNh916r6ItVWXRj9To6PbbNh8OEfMpeb3fzFF
        1L/2byYvFY0iaNjEuA47JNp4kFvkVxG68kLj0W307VXKc1Hq4I30aepK32V1ylx9XJZRcPE/kdd1xEg5
        TIRsrjzFuY02qq2QmN23Qv2Q+JdPRUqyqzqRh7nof880k8xlyLzVpzRQCgFAKAUAoBQCgFAKAUAoBQCg
        FAKAUAoBQCgKz5D3NjU9cfyKohy1RQhsr+5xeyr9krJt7HtpJf7yeF+fojf27SexZj+q4s591Hxtt/kW
        fIzM55Y8GU4pSctIRSV4r8SGOHTlxtxv0FLW+1U1UuX5PUbfcatWKhFZa/qvD6nr5sk5mNsjWvzM47l4
        cBht1oHBbbVpxxFSzqNCAmfFLoSp0Rale3nGSHZowdbsUFFt4/jJc/xlxcgWM9lTRUjvGxFZX0ImkM3P
        +PlFKnq+Jz//AEVizCP1f+f4N4VsPNigFAKAUAoBQCgFAKAUAoBQCgFAKAUAoBQCgFAc+fkHknpk1Y6K
        qxobjbFkva6gpkv9yVE/tXnbbuvdaf8ASOF+57TsdPRR1eMnn8Hv+P8Andhn7BkjyOQfexGNxjbQsmVo
        7NjRGuLY2AVRtoutrr6119eTbeTF3uiuEIqMUpSk/q/hs127Fy+97tPkxBJw8lMMkd6qgNqXFoUVf8Wx
        SyelYNnY6ZcOM5PgvjwO1Uo69CUuCjHj8fU6SGJA8feN5KRksGJhuvEaIiqbyCpKS3te5/Wutpa7ilF8
        ZN8X8zwvcNx2zlY/t+xo7xf5I2PEYPYdv2LKzcqzCBmBjYEl8ybenSVU7ChdPYIopW7Cq13NnXi5RhFY
        ODr7ElGU5Ph4ExpuA8reUIjufyu0ycNh3jMIjEJSbFxQVRJQbBRRAEkUbkqqq1XbOql9Kjl/MsqjZcup
        vESX0fUvOWsbi0EmeeU1UXialFKlC6hxrLZ5ttxScbMVstkX7LVd1tM4cFiRZTXdCXF5iVTTPI2eyvkr
        MZ+fm5o6niAm5R2F8xCysdq7UZpG0XhyJSFUH1Wr7deMa1FL1vBRTfKVjbfpRm69lvKfl7OTXYmZd1rW
        4ZKJfxFUVHldW2riok64o/rLkgp9PSo2QroSyuqRKudl7eH0xM8vHHnTV9kad1rOP5nFtk27eZKQQcRV
        s406w8Rp2/cP16daj79M4+pYfyJKi6EvS8xI/cNv2zPec2NbxGYmQsa3KjQHo8V42QVGkR6WSoK/qT3j
        f7VKqqMaHJriQsulK9RT4G0fOO0ytc8dzpMGQcbIyzbhw321sYm6XvIV7ootiaoqetY9StTsSfI2bdvR
        W2uZo+DP2R/Q3tkleT5UPIgDps4MnyN5xWyUQDkjqEhOW6ezpeujKMVZ0qvK8znxlJ19Tnx+PmXvwvt+
        z4vV8xsm/wCTea1vkymNlZJVVwjXkh/Ff/YQFcUTot1/T61m264OSjWvUadOyfQ5TfAsB/kdoIC2+UbK
        pj3jVtnJLCNIxqPfgaqilb6Il6q/4Z8uGfLJa9yCWXnBN7P5k0bXYWKmy5LkiNmWSkQDiNq9yaBB9xIl
        lG/NES/rf6VXXqzm2kuRZZswhht8yKyf5E+NYEtlhZEmS26ImUuPHM2AQu9zW3Lj+7hyt271OOlY0Qlu
        Vp4Kl+Qu/wCaZla7iNWyUiK7OaWaTsNxW1eF9Uaiohj1VCXmtv6Vfo0xfVKS5FG7e04qL5l1leWdP1V3
        HatMmy8xnmWWY7wRWjlvm+goHFxQ7umqXUe/1rOtac05JYiaffjFqOcyNhR3VdYbdVsmlcFCVpyyGN0v
        xKyql09bLWU0H3QCgFAKAUBqXyXpst7JyZIxDm46eiE8jYqagaIiKioNyT9KEhJXk+66d0L/AHa02n5e
        DPW9n36/aVcmoyj5+KJnxf49Z1/V5zfBxiXmVI3vl6uA3wUGgX+iKpW79a7mjG105n6Zy/TyOV3XdVl6
        xxjD9fMmNI0HF6vEBGhE5nDgTgjxAE9RbH0Tp1Veq180e3qn1SfVY+b/AAU9w7nPYeOUPL8lT/I+fPHQ
        wxMCM9Kfy0lttwWGXHVFllfmMlUEXj1ER69713dFL3MvwODvN+3heJriX4r2h/wbhHMbEecn/wA17LZH
        GW4ukDwE024IFx97bQh7e/VbfSta2Y++8vhjGTJLVl7CS58yU0Xy7sms6hE1tNJyMrJY9sm2TBp1ts0V
        VIScH41JOhe63fvULtaM5uXWsMsp2ZQh09LyjOiZXzWzpWxZzbScBl6C5FxOIbjj/JOTKsAOqLImbYtI
        S2Quv1+8JRp64xh58WTUrehuX2RWfHXjLY8n4q27+LHdjZKe5HYiRZAqyTzcFReIP9iDbmR8UXtdOtX3
        7EVbHyRn19aTql5s9PFe9bJ48gTsHP0/JS3JElZDatMuCfPgLagvsJCT/X0UVps0xtakpI+6tsqk4uLL
        lqOf81ZzPP5/MNngdPiqco8ebAk+402NxjtCoq8Slb3Gop68fSs1sKYx6V6pmmqdsnl8IlW/H3AZjJ+S
        MntGVgyIqMtPSBJ9txu8ie4vRPkRFKzfOr92yKrUU/hFGnW3Y5NEn+UD+XnysJhcfCkSW2QdmPEyw64K
        Gf8AqbRSAVHshdKj27pWW2S7jl4SRRs6eCnarHw2E8dTo2dFtlssycd35SJtE+VywD7ic4rfl061or6l
        PqlNYKLMOHTGDyZ2VmeUtL0bC4RyE8ISzdnq+/HSb/GUi4tRg5i622Qgimo2/dZPWoRVVljln9s/MlJ2
        11qODA8ix89Mx2Lj45dlysJ4VeVcjEVlhXRRARY8ZpsfjTkXdfr0v3qWu4ptvpX3I7MZNJLqaJPatQym
        X8i6zpzUGUuLw8eDjHpCsn8SCKI/Lc+RE4KnFe9+9RquUa5Tz6nklbS5WRjj0om/yJg5TN7bgNaxONeK
        LEYFsXmIzitg5McRtB5iPDi2DaLb0vVehJRjKTfEs3ouUoxS4EXN1Kftnm1MUDMyBhscKQo2QaZcaFlr
        Gx7ATZmPFLyOy/epxtVdOeDk/wByEqXZdjlFHx4gYyel+T3oexYR50n3CgLlljuuIw+R+18HlFU+N+/u
        LvZUVVtem01ZUnF8vAasZV2NSX3Oo6451xQCgFAKAUAoBQCgFAKAUAoBQCgFAKAWoBagCiJJZURU+i0A
        slALJQCgFkoBZKAUAoBQCgFAKAUAoBQCgFAKAUAoBQCgFAKAUAoBQCgFAKAUAoBQCgP/2Q==
</value>
  </data>
</root>