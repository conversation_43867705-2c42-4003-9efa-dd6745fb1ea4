﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class SybaseWithRsHADRConfig : System.Web.UI.UserControl
    {

        public string Port
        {
            get
            {
                return txtServerPort.Text;
            }
            set
            {
                txtServerPort.Text = value;
            }
        }

        public string DatabaseSID
        {
            get
            {
                return txtname.Text;
            }
            set
            {
                txtname.Text = value;
            }
        }

        public string UserName
        {
            get
            {
                return txtUserName.Text;
            }
            set
            {
                txtUserName.Text = value;
            }
        }

        public string Password
        {
            get
            {
                return txtPassword.Text;
            }
            set
            {
                txtPassword.Text = value;
            }
        }     

        public string Syabse_Env_Path
        {
            get
            {
                return txtEnvPath.Text;
            }
            set
            {
                txtEnvPath.Text = value;
            }
        }


        protected void Page_Load(object sender, EventArgs e)
        {
            txtPassword.Attributes.Add("onblur", "getHashData(" + txtPassword.ClientID + ")");
        }
    }
}