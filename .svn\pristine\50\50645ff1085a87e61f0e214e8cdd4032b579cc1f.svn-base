﻿using CP.Common.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace CP.Common.DatabaseEntity
{
    [Serializable]
    [DataContract(Name = "IMAPConfiguration", Namespace = "http://www.ContinuityPlatform.com/types")]
    public class IMAPConfig : BaseEntity
    {
        #region Properties

        [DataMember]
        public string ProfileName { get; set; }

        [DataMember]
        public string EmailServer { get; set; }

        [DataMember]
        public string UserName { get; set; }

        [DataMember]
        public string Password { get; set; }



        [DataMember]
        public int Port { get; set; }

        [DataMember]
        public int CompanyId { get; set; }


        [DataMember]
        public bool IsActiveNow { get; set; }

        #endregion Properties
    }
    
}
