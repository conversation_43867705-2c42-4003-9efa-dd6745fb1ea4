﻿using System;
using System.Collections.Generic;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using PEMCSRDF;
using System.Linq;
using System.Web.Security;
using System.Web;

namespace CP.UI
{
    public partial class EMCSRDFConfiguration : ReplicationControl
    {
        #region

        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        private EMCSRDF _emcsrdf = null;

        #endregion

        #region Properties

        public EMCSRDF CurrentEntity
        {
            get { return _emcsrdf ?? (_emcsrdf = new EMCSRDF()); }

            set
            {
                _emcsrdf = value;
            }
        }

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "EMC SRDF"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion

        public override void PrepareView()
        {
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            ddlServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvddlServer.ClientID + ")");
            txtDGName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");

            Session.Remove("EMCSRDF");

            Utility.PopulateServerByTypeAndRole(ddlServer, "SymCLIServer", LoggedInUserCompanyId, IsUserSuperAdmin, IsParentCompnay, true);

            PrepareEditView();

        }

        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
            {
                // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType,_UserActionType, "Token Key validating Request", LoggedInUserId);
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();

                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }

                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtSSHUser");
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        private void PrepareEditView()
        {
            if (CurrentEMCSRDF != null)
            {
                CurrentEntity = CurrentEMCSRDF;
                Session["EMCSRDF"] = CurrentEntity;

                ddlServer.SelectedValue = CurrentEntity.ServerId.ToString();
                DdlServerSelectedIndexChanged(null, null);

                txtDSCLIHostname.Enabled = false;
                txtDSCLIServerIP.Enabled = false;
                txtSSHUserID.Enabled = false;
                txtSSHPassword.Enabled = false;
                txtDGName.Text = CurrentEntity.DGroupName;
                txtDGType.Text = CurrentEntity.DGType;
                txtDGSymmetrixID.Text = CurrentEntity.DGSummetrixID;
                txtRemoteSym.Text = CurrentEntity.RemoteSymmetrixID;
                txtRDFGroupNumber.Text = CurrentEntity.RdfRaGroupNumber;

                btnSave.Text = "Update";
                //IList<EMCDevicesDetails> devices = Facade.GetEmcDeviceDetailsByReplicationId(CurrentEntity.Id);

                //if (devices != null)
                // {
                //     lvdevicelist.DataSource = devices;
                //     lvdevicelist.DataBind();
                // }
            }
        }

        protected void DdlServerSelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlServer.SelectedValue != "0")
            {
                var server = Facade.GetServerById(Convert.ToInt32(ddlServer.SelectedValue));
                txtDSCLIHostname.Text = server.Name;
                txtDSCLIServerIP.Text = CryptographyHelper.Md5Decrypt(server.IPAddress);
                txtSSHUserID.Text = CryptographyHelper.Md5Decrypt(server.SSHUserName);
                ////RSM
                //txtSSHPassword.Attributes.Add("value", CryptographyHelper.Md5Decrypt(server.SSHPassword));

                txtSSHPassword.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                txtSSHPassword.Attributes["value"] = Utility.getHashKeyByString(txtSSHPassword.Text, hdfStaticGuid.Value);
              //  txtSSHPassword.Attributes.Add("value",Utility.IsMD5EncryptedString(server.SSHPassword)?server.SSHPassword: CryptographyHelper.Md5Encrypt(server.SSHPassword));         
           
            
            }
            else
            {
                txtDSCLIHostname.Text = string.Empty;
                txtDSCLIServerIP.Text = string.Empty;
                txtSSHUserID.Text = string.Empty;
                txtSSHPassword.Attributes["value"] = string.Empty;
            }
        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        protected void BtnSaveClick(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }
            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            if (Page.IsValid && (Session["ReplicationConfig_token"] != null) && !CheckReplicationNameExist() && ValidateRequest("EMCSRDFConfiguration", UserActionType.CreateReplicationComponent))
            {
                if (!ValidateInput())
                {
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");
                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl);
                }
                else
                {
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;
                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }
                    try
                    {
                        if (ValidateRequest("EMCSRDFConfiguration", UserActionType.CreateReplicationComponent))
                        {
                            BuildEntities();
                            StartTransaction();
                            SaveEditor();
                            EndTransaction();
                            string message = string.Empty; //= MessageInitials + " " + '"' + CurrentEntity.ReplicationBase.Name + '"';
                            if (ReplicationType.SelectedValue == "7")
                            {
                                message = "App - Emc Srdf" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "17")
                            {
                                message = "Oracle Full Db - Emc Srdf" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "26")
                            {
                                message = "MS-SQL FULL DB - EMC SRDF" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "37")
                            {
                                message = "Oracle FULL DB - EMC SRDF VMAX" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "38")
                            {
                                message = "Oracle FULL DB - EMC SRDF DMX" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "39")
                            {
                                message = "MSSQL FULL DB - EMC SRDF VMAX" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "40")
                            {
                                message = "MSSQL FULL DB - EMC SRDF DMX" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "46")
                            {
                                message = "EmcSrdf-OracleRac-FullDB" + " " + '"' + ReplicationName.Text + '"';
                            }

                            else if (ReplicationType.SelectedValue == "47")
                            {
                                message = "EmcSrdf-SyBase" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "63")
                            {
                                message = "EmcSrdf-Mysql-FullDB" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else if (ReplicationType.SelectedValue == "99")
                            {
                                message = "EMCSRDF-DB2FullDB" + " " + '"' + ReplicationName.Text + '"';
                            }
                            else //18
                            {
                                message = "Oracle LOG Shipping - Emc Srdf" + " " + '"' + ReplicationName.Text + '"';
                            }
                
                            
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                  currentTransactionType));
                            btnSave.Enabled = false;
                        }
                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, Page);
                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, Page);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, Page);
                        }
                    }
                    //if (returnUrl.IsNotNullOrEmpty())
                    //{
                    //    var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "7");

                    //    Helper.Url.Redirect(secureUrl);
                    //}
                    if (returnUrl.IsNotNullOrEmpty())
                    {
                        if (ReplicationType.SelectedValue == "7")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "7");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "7");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "17")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "17");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "17");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "26")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "26");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "26");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "37")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "37");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "37");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "38")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "38");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "38");
                            Helper.Url.Redirect(secureUrl);
                        }

                        else if (ReplicationType.SelectedValue == "39")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "39");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "39");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "40")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "40");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "40");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "46")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "46");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "46");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "47")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "47");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "47");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "63")
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "63");
                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "63");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else if (ReplicationType.SelectedValue == "99")
                        {
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "99");
                            Helper.Url.Redirect(secureUrl);
                        }
                        else
                        {
                            //var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.ReplicationType, "18");

                            //Helper.Url.Redirect(secureUrl);
                            var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                            WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, "18");
                            Helper.Url.Redirect(secureUrl);
                        }
                    }
                }
            }
        }

        private void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.AddEMCSRDF(CurrentEntity);
                //if (CurrentEntity != null)
                //{
                //    AddDeviceDetails(CurrentEntity.Id);
                //}

                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "EMCSRDF", UserActionType.CreateReplicationComponent, "The EMCSRDF Replication component '" + CurrentEntity.ReplicationBase.Name + "' was added to the replication component", LoggedInUserId);
            }
            else
            {
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateEMCSRDF(CurrentEntity);
                //if (CurrentEntity != null)
                //{
                //    UpdateDeviceDetails(CurrentEntity.Id);
                //}

                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "EMCSRDF", UserActionType.UpdateReplicationComponent, "The EMCSRDF Replication component '" + CurrentEntity.ReplicationBase.Name + "' was updated to the replication component", LoggedInUserId);
            }
        }

        private void UpdateDeviceDetails(int groupid)
        {
            foreach (ListViewDataItem lvi in lvdevicelist.Items)
            {
                var emcdetails = new EMCDevicesDetails();
                var lblId = (Label)lvi.FindControl("lblId");
                var lbl1 = (Label)lvi.FindControl("DeviceName");
                var lbl2 = (Label)lvi.FindControl("Source");
                var lbl3 = (Label)lvi.FindControl("Target");
                //if (lblId != null)
                //{
                //    emcdetails.Id = Convert.ToInt32(lblId.Text);
                //}
                //emcdetails.DGroupID = groupid;
                //if (lbl1 != null)
                //    emcdetails.LogicalDevice = lbl1.Text;
                //if (lbl1 != null)
                //    emcdetails.SourceR1Invtracks = lbl2.Text;
                //if (lbl1 != null)
                //    emcdetails.TargetR2Invtracks = lbl3.Text;
                //emcdetails.CreatorId = LoggedInUserId;
                //var emcdevice = Facade.UpdateEmcDevicesDetails(emcdetails);
            }
        }

        private void BuildEntities()
        {
            if (Session["EMCSRDF"] != null)
            {
                CurrentEntity = (EMCSRDF)Session["EMCSRDF"];
            }
            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.ServerId = Convert.ToInt32(ddlServer.SelectedValue);
            CurrentEntity.DGroupName = txtDGName.Text;
            CurrentEntity.DGType = txtDGType.Text;
            CurrentEntity.DGSummetrixID = txtDGSymmetrixID.Text;
            CurrentEntity.RemoteSymmetrixID = txtRemoteSym.Text;
            CurrentEntity.RdfRaGroupNumber = txtRDFGroupNumber.Text;
        }

        private void AddDeviceDetails(int groupid)
        {
            //foreach (ListViewDataItem lvi in lvdevicelist.Items)
            //{
            //    var emcdetails=new EmcDevicesDetails();
            //    var lbl1 = (Label)lvi.FindControl("DeviceName");
            //    var lbl2 = (Label)lvi.FindControl("Source");
            //    var lbl3 = (Label)lvi.FindControl("Target");
            //    emcdetails.DGroupID=groupid;
            //    if (lbl1 != null)
            //        emcdetails.LogicalDevice = lbl1.Text;
            //    if (lbl1 != null)
            //        emcdetails.SourceR1Invtracks = lbl2.Text;
            //    if (lbl1 != null)
            //        emcdetails.TargetR2Invtracks = lbl3.Text;
            //    emcdetails.CreatorId = LoggedInUserId;
            //    var emcdevice = Facade.AddEmcDevicesDetails(emcdetails);

            //}
        }

        protected void BtnDiscoverClick(object sender, EventArgs e)
        {
            try
            {
                if (txtDSCLIServerIP.Text.IsNotNullOrEmpty() && txtSSHUserID.Text.IsNotNullOrEmpty() && txtSSHPassword.Text.IsNotNullOrEmpty() && txtDGName.Text.IsNotNullOrEmpty())
                {
                    //SRDFInfo info = SRDFProcess.SymrdfQuery(new SymCLI(txtDSCLIServerIP.Text, txtSSHUserID.Text, txtSSHPassword.Text), txtDGName.Text);


                    SRDFInfo info = SRDFProcess.SymrdfQuery(new SymCLI(txtDSCLIServerIP.Text, txtSSHUserID.Text, Utility.IsMD5EncryptedString(txtSSHPassword.Text) ? CryptographyHelper.Md5Decrypt(txtSSHPassword.Text) : txtSSHPassword.Text), txtDGName.Text);

                    if (info != null)
                    {
                        txtDGType.Text = info.DeviceGroupType;
                        txtDGSymmetrixID.Text = info.DeviceGroupSymID;
                        txtRemoteSym.Text = info.RemoteSymID;
                        txtRDFGroupNumber.Text = info.RDFGroupNumber;

                        List<SourceTargetView> target = info.ViewDetails;

                        if (info.ViewDetails != null)
                        {
                            for (int i = 0; i < target.Count; i++)
                            {
                                //devicesDetailses.Add(new EmcDevicesDetails()
                                //{
                                //    Id = i + 1,
                                //    LogicalDevice = target[i].LogicalDevice,
                                //    SourceR1Invtracks = target[i].SourceR1Invtracks,
                                //    TargetR2Invtracks = target[i].TargetR2Invtracks
                            }
                        }

                        //lvdevicelist.DataSource = devicesDetailses;
                        //lvdevicelist.DataBind();
                    }
                }
            }
            catch (Exception exc)
            {
                string ex = exc.Message;

                txtDGType.Text = "";
                txtDGSymmetrixID.Text = "";
                txtRemoteSym.Text = "";
                txtRDFGroupNumber.Text = "";

                lvdevicelist.DataSource = null;
                lvdevicelist.DataBind();
            }
        }

        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }
    }
}