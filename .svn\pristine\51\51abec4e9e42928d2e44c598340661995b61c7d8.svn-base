﻿using CP.BusinessFacade;
using CP.Common.Shared;
using CP.UI.Code.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using CP.Helper;
using CP.UI.Controls;
using CP.ExceptionHandler;

namespace CP.UI.Component
{
    public partial class Rsyncoptionconfiguration : RSyncOptionsBasePageEditor
    {
        public override string MessageInitials
        {
            get { return "RSyncOptions"; }
        }

        public override string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.RSyncOptionsList;
                }
                return string.Empty;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {

        }

        public override void PrepareView()
        {
            if (IsUserOperator || IsUserManager || IsUserCustom)
            {
                Response.Redirect(Constants.UrlConstants.Urls.Common.LogoutPage);
                return;
            }

            Utility.SelectMenu(Master, "Module2");
            //txtName.Attributes.Add("onblur", "ValidatorValidate(" + rfvDSName.ClientID + ")");
            ////txtName.Attributes.Add("onblur", "ValidatorValidate(" + revDSName.ClientID + ")");

            if (CurrentRSyncOptionsId > 0)
            {
                //txtName.ReadOnly = true;
                PrepareEditView();
            }
        }

        public override void PrepareEditView()
        {
            BindControlsValue();
            btnSave.Text = "Update";
        }

        public override void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.CreatorId = LoggedInUserId;
                CurrentEntity.UpdatorId = LoggedInUserId;
                // CurrentEntity.Id = CurrentRSyncOptions.Id;
                CurrentEntity = Facade.AddRSyncOptions(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "RSync", UserActionType.CreateDataSyncProperties, "The RSync Properties '" + CurrentEntity.Name + "' was added to the RSyncProperties table", LoggedInUserId);
            }
            else
            {
                CurrentEntity.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.UpdateRSyncoptions(CurrentEntity);
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "RSync Properties", UserActionType.UpdateDataSyncProperties, "The RSync Properties '" + CurrentEntity.Name + "' was updated to the RSyncProperties table", LoggedInUserId);
            }
        }


        public override void BuildEntities()
        {
            CurrentEntity.Name = txtReplName.Text.Trim();

            #region GeneralOption


            string allitemFilters = "";

            string sortedFilters = "";
            // allitemFilters += "Cblfilters::";
            foreach (ListItem item in CblstGroup.Items)
            {
                allitemFilters += item.Value + ":" + item.Selected;
                allitemFilters += ";";
            }

            string[] sortfilteroptions = allitemFilters.Split('#');

            string[] sortcopyoptionsCblfilters = sortfilteroptions[0].Split(';');

            string[] sortcopyoptionss0 = sortcopyoptionsCblfilters[0].Split(':');
            string[] sortcopyoptionss1 = sortcopyoptionsCblfilters[1].Split(':');
            string[] sortcopyoptionss2 = sortcopyoptionsCblfilters[2].Split(':');
            string[] sortcopyoptionss3 = sortcopyoptionsCblfilters[3].Split(':');
            string[] sortcopyoptionss4 = sortcopyoptionsCblfilters[4].Split(':');
            string[] sortcopyoptionss5 = sortcopyoptionsCblfilters[5].Split(':');

            //if (allitemFilters != null)
            //{
            //    sortedFilters += "-";
            //}
            if (sortcopyoptionss0[1] == "True")
            {
                sortedFilters += "v";
                //CopyOptionsValue += sortcopyoptions0[0];
            }
            if (sortcopyoptionss1[1] == "True")
            {
                sortedFilters += "q";
            }
            if (sortcopyoptionss2[1] == "True")
            {
                sortedFilters += "c";
            }
            if (sortcopyoptionss3[1] == "True")
            {
                sortedFilters += "a";
            }
            if (sortcopyoptionss4[1] == "True")
            {
                sortedFilters += "z";
            }
            if (sortcopyoptionss5[1] == "True")
            {
                sortedFilters += "r";
            }

            if (!string.IsNullOrEmpty(sortedFilters))
            {
                sortedFilters = "-" + sortedFilters;
            }
            #endregion

            #region AdvancedOptions

            string sortedAdvancedFilters = "";

            string allitemAdvancedFilters = "";
            //allitemCopyOptions += "##";
            foreach (ListItem item in CbladvOption.Items)
            {
                allitemAdvancedFilters += item.Value + ":" + item.Selected;
                allitemAdvancedFilters += ";";
            }

            allitemAdvancedFilters += "chkadvOption1:" + chkadvOption1.Checked + ";txtadvOption1:" + txtadvOption1.Text;



            allitemAdvancedFilters += "chkadvOption2:" + chkadvOption2.Checked + ";txtadvOption2:" + txtadvOption2.Text;



            allitemAdvancedFilters += "chkadvOption3:" + chkadvOption3.Checked + ";txtadvOption3:" + txtadvOption3.Text;



            allitemAdvancedFilters += "chkadvOption4:" + chkadvOption4.Checked + ";txtadvOption4:" + txtadvOption4.Text;



            allitemAdvancedFilters += "chkadvOption5:" + chkadvOption5.Checked + ";txtadvOption5:" + txtadvOption5.Text;



            allitemAdvancedFilters += "chkadvOption6:" + chkadvOption6.Checked + ";txtadvOption6:" + txtadvOption6.Text;



            allitemAdvancedFilters += "chkadvOption7:" + chkadvOption7.Checked + ";txtadvOption7:" + txtadvOption7.Text;


            string[] sortAdvfilteroptions = allitemAdvancedFilters.Split('#');

            string[] sortAdvCblAdvfilters = sortAdvfilteroptions[0].Split(';');


            string[] sortadvfilter0 = sortAdvCblAdvfilters[0].Split(':');
            string[] sortadvfilter1 = sortAdvCblAdvfilters[1].Split(':');
            string[] sortadvfilter2 = sortAdvCblAdvfilters[2].Split(':');
            string[] sortadvfilter3 = sortAdvCblAdvfilters[3].Split(':');
            string[] sortadvfilter4 = sortAdvCblAdvfilters[4].Split(':');
            string[] sortadvfilter5 = sortAdvCblAdvfilters[5].Split(':');
            string[] sortadvfilter6 = sortAdvCblAdvfilters[6].Split(':');

            string[] sortadvfilter7 = sortAdvCblAdvfilters[7].Split(':');
            string[] sortadvfilter8 = sortAdvCblAdvfilters[8].Split(':');
            string[] sortadvfilter9 = sortAdvCblAdvfilters[9].Split(':');
            string[] sortadvfilter10 = sortAdvCblAdvfilters[10].Split(':');
            string[] sortadvfilter11 = sortAdvCblAdvfilters[11].Split(':');
            string[] sortadvfilter12 = sortAdvCblAdvfilters[12].Split(':');
            string[] sortadvfilter13 = sortAdvCblAdvfilters[13].Split(':');

            //if (allitemAdvancedFilters != null)
            //{
            //    sortedAdvancedFilters += "--";
            //}
            if (sortadvfilter0[1] == "True")
            {
                sortedAdvancedFilters += "--progress,";
                //CopyOptionsValue += sortcopyoptions0[0];
            }
            if (sortadvfilter1[1] == "True")
            {
                sortedAdvancedFilters += "--links,";
            }
            if (sortadvfilter2[1] == "True")
            {
                sortedAdvancedFilters += "--copy-links,";
            }
            if (sortadvfilter3[1] == "True")
            {
                sortedAdvancedFilters += "--copy-dirlinks,";
                //CopyOptionsValue += sortcopyoptions0[0];
            }
            if (sortadvfilter4[1] == "True")
            {
                sortedAdvancedFilters += "--keep-dirlinks,";
            }
            if (sortadvfilter5[1] == "True")
            {
                sortedAdvancedFilters += "--hard-links,";
            }
            if (sortadvfilter6[1] == "True")
            {
                sortedAdvancedFilters += "--delete,";
            }



            if (chkadvOption1.Checked == true)
            {
                sortedAdvancedFilters += "--log-file=" + txtadvOption1.Text + ",";
            }

            if (chkadvOption2.Checked == true)
            {
                sortedAdvancedFilters += "--exclude=" + txtadvOption2.Text + ",";
            }

            if (chkadvOption3.Checked == true)
            {
                sortedAdvancedFilters += "--exclude-from=" + txtadvOption3.Text + ",";
            }

            if (chkadvOption4.Checked == true)
            {
                sortedAdvancedFilters += "--port=" + txtadvOption4.Text + ",";
            }

            if (chkadvOption5.Checked == true)
            {
                sortedAdvancedFilters += "--include=" + txtadvOption5.Text + ",";
            }

            if (chkadvOption6.Checked == true)
            {
                sortedAdvancedFilters += "--include-from=" + txtadvOption6.Text + ",";
            }

            if (chkadvOption7.Checked == true)
            {
                sortedAdvancedFilters += "Additional=" + txtadvOption7.Text + ",";
            }

            //if (sortadvfilter7[1] == "True")
            //{
            //    sortedAdvancedFilters += "--log-file,";

            //}
            //if (sortadvfilter8[1] == "True")
            //{
            //    sortedAdvancedFilters += "--exclude,";
            //}
            //if (sortadvfilter9[1] == "True")
            //{
            //    sortedAdvancedFilters += "--exclude-from,";
            //}
            //if (sortadvfilter10[1] == "True")
            //{
            //    sortedAdvancedFilters += "--port,";

            //}
            //if (sortadvfilter11[1] == "True")
            //{
            //    sortedAdvancedFilters += "--include,";
            //}
            //if (sortadvfilter12[1] == "True")
            //{
            //    sortedAdvancedFilters += "--include-from,";
            //}
            //if (sortadvfilter13[1] == "True")
            //{
            //    sortedAdvancedFilters += "Additional,";
            //}

            #endregion

            CurrentEntity.RepType = ddlRepType.SelectedItem.Text;
            CurrentEntity.OptionSelection = rdbtnops.SelectedValue;
            CurrentEntity.GeneralOptions = sortedFilters;
            CurrentEntity.AdvancedOptions = sortedAdvancedFilters;
            CurrentEntity.CustomCommand = txtCustomCmd.Text;
        }

        protected void chkadvOption1_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvOption1.Checked)
            {
                txtadvOption1.Enabled = true;
                txtadvOption1.Text = "";
            }
            else
            {
                txtadvOption1.Enabled = false;
                txtadvOption1.Text = "";
                lbladvOption1.Visible = false;
            }
        }

        protected void chkadvOption2_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvOption2.Checked)
            {
                txtadvOption2.Enabled = true;
                txtadvOption2.Text = "";
            }
            else
            {
                txtadvOption2.Enabled = false;
                txtadvOption2.Text = "";
                lbladvOption2.Visible = false;
            }
        }

        protected void chkadvOption3_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvOption3.Checked)
            {
                txtadvOption3.Enabled = true;
                txtadvOption3.Text = "";
            }
            else
            {
                txtadvOption3.Enabled = false;
                txtadvOption3.Text = "";
                lbladvOption3.Visible = false;
            }
        }

        protected void chkadvOption4_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvOption4.Checked)
            {
                txtadvOption4.Enabled = true;
                txtadvOption4.Text = "";
            }
            else
            {
                txtadvOption4.Enabled = false;
                txtadvOption4.Text = "";
                lbladvOption4.Visible = false;
            }
        }

        protected void chkadvOption5_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvOption5.Checked)
            {
                txtadvOption5.Enabled = true;
                txtadvOption5.Text = "";
            }
            else
            {
                txtadvOption5.Enabled = false;
                txtadvOption5.Text = "";
                lbladvOption5.Visible = false;
            }
        }

        protected void chkadvOption6_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvOption6.Checked)
            {
                txtadvOption6.Enabled = true;
                txtadvOption6.Text = "";
            }
            else
            {
                txtadvOption6.Enabled = false;
                txtadvOption6.Text = "";
                lbladvOption6.Visible = false;
            }
        }

        protected void chkadvOption7_CheckedChanged(object sender, EventArgs e)
        {
            if (chkadvOption7.Checked)
            {
                txtadvOption7.Enabled = true;
                txtadvOption7.Text = "";
            }
            else
            {
                txtadvOption7.Enabled = false;
                txtadvOption7.Text = "";
                lbladvOption7.Visible = false;
            }
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            if (ddlRepType.SelectedItem.Text == "-Select Replication Type-")
            {
                return;
            }

            if (CurrentRSyncOptionsId == 0)
            {
                if (!Page.IsValid || CheckRSyncExist())
                    return;
            }
            else
            {
                if (!Page.IsValid)
                    return;
            }


            var returnUrl = Helper.Url.SecureUrl.ReturnUrl;
            if (returnUrl.IsNullOrEmpty())
            {
                returnUrl = ReturnUrl;
            }
            if (rdbtnops.SelectedItem.Text == " User Selection ")
            {
                if ((txtadvOption1.Enabled == true && string.IsNullOrEmpty(txtadvOption1.Text)))
                {
                    lbladvOption1.Visible = true;
                    return;
                }

                if ((txtadvOption2.Enabled == true && string.IsNullOrEmpty(txtadvOption2.Text)))
                {
                    lbladvOption2.Visible = true;
                    return;
                }

                if ((txtadvOption3.Enabled == true && string.IsNullOrEmpty(txtadvOption3.Text)))
                {
                    lbladvOption3.Visible = true;
                    return;
                }
                if ((txtadvOption4.Enabled == true && string.IsNullOrEmpty(txtadvOption4.Text)))
                {
                    lbladvOption4.Visible = true;
                    return;
                }
                if ((txtadvOption5.Enabled == true && string.IsNullOrEmpty(txtadvOption5.Text)))
                {
                    lbladvOption5.Visible = true;
                    return;
                }

                if ((txtadvOption6.Enabled == true && string.IsNullOrEmpty(txtadvOption6.Text)))
                {
                    lbladvOption6.Visible = true;
                    return;
                }
                if ((txtadvOption7.Enabled == true && string.IsNullOrEmpty(txtadvOption7.Text)))
                {
                    lbladvOption7.Visible = true;
                    return;
                }
            }
            var submitButton = (Button)sender;
            var buttionText = " " + submitButton.Text.ToLower() + " ";
            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            try
            {
                if (currentTransactionType != TransactionType.Undefined)
                {
                    BuildEntities();
                    StartTransaction();
                    SaveEditor();
                    EndTransaction();
                }
                string message = MessageInitials + " " + '"' + CurrentEntity.Name + '"';
                ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                            currentTransactionType));
            }
            catch (CpException ex)
            {
                InvalidateTransaction();
                returnUrl = Request.RawUrl;
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                ExceptionManager.Manage(ex, this);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();
                returnUrl = Request.RawUrl;
                ErrorSuccessNotifier.AddErrorMessage(ex.Message);
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, this);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);
                    ExceptionManager.Manage(customEx, this);
                }
            }

            if (returnUrl.IsNotNullOrEmpty())
            {
                Helper.Url.Redirect(new SecureUrl(returnUrl));
            }
        }

        private bool CheckRSyncExist()
        {
            if (txtReplName.Text.Trim() != string.Empty)
            {
                bool isExists = Facade.IsExistRSyncOptionByName(txtReplName.Text);
                if (isExists)
                {
                    lblPrName.Text = "RSyncOption Name already exists ";
                    return true;
                }
                else
                {
                    lblPrName.Text = "";
                    return false;
                }
            }
            else
            {
                //lblDSName.Text = "Enter DataSync Properties Name";
                return true;
            }
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.RSyncOptionsList);
        }

        protected void rdbtnops_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (rdbtnops.SelectedItem.Text == " User Selection ")
            {
                divGeneralOption.Visible = true;
                divAdvancedOption.Visible = true;
                divCustom.Visible = false;
                txtCustomCmd.Text = string.Empty;
                CurrentEntity.CustomCommand = string.Empty;

            }
            else if (rdbtnops.SelectedItem.Text == " Custom ")
            {
                divGeneralOption.Visible = false;
                divAdvancedOption.Visible = false;
                divCustom.Visible = true;
                CblstGroup.Items.FindByValue("0").Selected = false;
                CblstGroup.Items.FindByValue("1").Selected = false;
                CblstGroup.Items.FindByValue("2").Selected = false;
                CblstGroup.Items.FindByValue("3").Selected = false;
                CblstGroup.Items.FindByValue("4").Selected = false;
                CblstGroup.Items.FindByValue("5").Selected = false;

                CbladvOption.Items.FindByValue("0").Selected = false;
                CbladvOption.Items.FindByValue("1").Selected = false;
                CbladvOption.Items.FindByValue("2").Selected = false;
                CbladvOption.Items.FindByValue("3").Selected = false;
                CbladvOption.Items.FindByValue("4").Selected = false;
                CbladvOption.Items.FindByValue("5").Selected = false;
                CbladvOption.Items.FindByValue("6").Selected = false;

                chkadvOption1.Checked = false;
                txtadvOption1.Text = string.Empty;
                txtadvOption1.Enabled = false;

                chkadvOption2.Checked = false;
                txtadvOption2.Text = string.Empty;
                txtadvOption2.Enabled = false;

                chkadvOption3.Checked = false;
                txtadvOption3.Text = string.Empty;
                txtadvOption3.Enabled = false;

                chkadvOption4.Checked = false;
                txtadvOption4.Text = string.Empty;
                txtadvOption4.Enabled = false;

                chkadvOption5.Checked = false;
                txtadvOption5.Text = string.Empty;
                txtadvOption5.Enabled = false;

                chkadvOption6.Checked = false;
                txtadvOption6.Text = string.Empty;
                txtadvOption6.Enabled = false;

                chkadvOption7.Checked = false;
                txtadvOption7.Text = string.Empty;
                txtadvOption7.Enabled = false;

                CurrentEntity.GeneralOptions = string.Empty;
                CurrentEntity.AdvancedOptions = string.Empty;


            }
        }


        private void BindControlsValue()
        {

            ddlRepType.SelectedItem.Text = CurrentEntity.RepType.ToString();

            txtReplName.Text = CurrentEntity.Name;

            rdbtnops.SelectedValue = CurrentEntity.OptionSelection;
            string CustomCmd = CurrentEntity.CustomCommand;

            if (!string.IsNullOrEmpty(CustomCmd))
            {
                divCustom.Visible = true;
                txtCustomCmd.Text = CustomCmd;
            }
            else
            {
                divCustom.Visible = false;
            }
            string filters = CurrentEntity.GeneralOptions;
            if (!string.IsNullOrEmpty(filters))
            {
                divGeneralOption.Visible = true;

                foreach (char c in filters)
                {
                    if (c == '-')
                        continue;

                    switch (c)
                    {
                        case 'v':
                            CblstGroup.Items.FindByValue("0").Selected = true;
                            break;

                        case 'q':
                            CblstGroup.Items.FindByValue("1").Selected = true;
                            break;

                        case 'c':
                            CblstGroup.Items.FindByValue("2").Selected = true;
                            break;

                        case 'a':
                            CblstGroup.Items.FindByValue("3").Selected = true;
                            break;

                        case 'z':
                            CblstGroup.Items.FindByValue("4").Selected = true;
                            break;

                        default:
                            {
                                CblstGroup.Items.FindByValue("5").Selected = true;
                            }
                            break;


                    }
                }


            }
            else
            {
                divGeneralOption.Visible = false;
            }

            string advancedOption = CurrentEntity.AdvancedOptions;
            divAdvancedOption.Visible = true;
            if (!string.IsNullOrEmpty(advancedOption))
            {
                divAdvancedOption.Visible = true;

                string[] advValues = advancedOption.Split(',');
                for (int i = 0; i < advValues.Length; i++)
                {
                    var strArr = new string[] { };
                    if (advValues[i].IndexOf('=') > -1)
                    {
                        //strArr = advValues[i].Split('=');
                        strArr = advValues[i].Split(new[] { '=' }, 2);

                        // strArr = advValues[i].Split('=').FirstOrDefault();
                    }
                    else
                    {
                        strArr = new string[] { advValues[i] };
                    }
                    //advValues[i] = advValues[i].Trim();

                    switch (strArr[0])
                    {
                        case "--progress":
                            CbladvOption.Items.FindByValue("0").Selected = true;
                            break;

                        case "--links":
                            CbladvOption.Items.FindByValue("1").Selected = true;
                            break;

                        case "--copy-links":
                            CbladvOption.Items.FindByValue("2").Selected = true;
                            break;

                        case "--copy-dirlinks":
                            CbladvOption.Items.FindByValue("3").Selected = true;
                            break;

                        case "--keep-dirlinks":
                            CbladvOption.Items.FindByValue("4").Selected = true;
                            break;

                        case "--hard-links":
                            CbladvOption.Items.FindByValue("5").Selected = true;
                            break;

                        case "--delete":
                            CbladvOption.Items.FindByValue("6").Selected = true;
                            break;


                        case "--log-file":
                            chkadvOption1.Checked = true;
                            txtadvOption1.Text = strArr[1];
                            txtadvOption1.Enabled = true;
                            break;

                        case "--exclude":
                            chkadvOption2.Checked = true;
                            txtadvOption2.Text = strArr[1];
                            txtadvOption2.Enabled = true;
                            break;

                        case "--exclude-from":
                            chkadvOption3.Checked = true;
                            txtadvOption3.Text = strArr[1];
                            txtadvOption3.Enabled = true;
                            break;

                        case "--port":
                            chkadvOption4.Checked = true;
                            txtadvOption4.Text = strArr[1];
                            txtadvOption4.Enabled = true;
                            break;

                        case "--include":
                            chkadvOption5.Checked = true;
                            txtadvOption5.Text = strArr[1];
                            txtadvOption5.Enabled = true;
                            break;

                        case "--include-from":
                            chkadvOption6.Checked = true;
                            txtadvOption6.Text = strArr[1];
                            txtadvOption6.Enabled = true;
                            break;

                        case "Additional":
                            chkadvOption7.Checked = true;
                            txtadvOption7.Text = strArr[1];
                            txtadvOption7.Enabled = true;
                            break;

                        default:

                            break;
                    }
                }
            }
            else
            {
             //   divAdvancedOption.Visible = false;
            }

            // string[] filt = filters.Split('#');


            //string[] filte = filt[0].Split(',');

            //if (filte[0].Contains("-"))
            //{

            //chkfiltersIA.Checked = true;
            //string[] filte;
            //for (int i = 0; i < filters.Length; i++)
            //    {
            //        int lastElementIndex = filters.Length - 1;

            //        if (i != lastElementIndex)
            //        {


            //            string fddsf = Convert.ToString(filte[i]);

            //            fddsf = fddsf.Substring(fddsf.Length - 1);

            //            int p = Convert.ToInt32(fddsf);

            //            CblstGroup.Items[p].Selected = true;
            //        }

            //    }


            //}



            //if (filt[1].Contains("/"))
            //{

            //    string[] filte1 = filt[1].Split(',');

            //    if (filt[1].Contains("/"))
            //    {

            //        chkfiltersIA.Checked = true;
            //        pnlfiltersIA.Visible = true;
            //        CblfiltersIA.Visible = true;


            //        for (int i = 0; i < filte1.Length; i++)
            //        {
            //            int lastElementIndex = filte1.Length - 1;

            //            if (i != lastElementIndex)
            //            {

            //                string fddsf = Convert.ToString(filte1[i]);

            //                fddsf = fddsf.Substring(fddsf.Length - 1);

            //                int p = Convert.ToInt32(fddsf);

            //                CblfiltersIA.Items[p].Selected = true;
            //            }

            //        }
            //    }

            //}


            //if (filt[2].Contains("/"))
            //{

            //    string[] filte2 = filt[2].Split(',');

            //    if (filt[2].Contains("/"))
            //    {

            //        chkfiltersXA.Checked = true;
            //        pnlfiltersXA.Visible = true;
            //        CblfiltersXA.Visible = true;

            //        for (int i = 0; i < filte2.Length; i++)
            //        {
            //            int lastElementIndex = filte2.Length - 1;

            //            if (i != lastElementIndex)
            //            {

            //                string fddsf = Convert.ToString(filte2[i]);

            //                fddsf = fddsf.Substring(fddsf.Length - 1);

            //                int p = Convert.ToInt32(fddsf);

            //                CblfiltersXA.Items[p].Selected = true;
            //            }

            //        }
            //    }
            //}

            //if (filt[3].Contains("/"))
            //{

            //    string[] filte3 = filt[3].Split(',');

            //    if (filt[3].Contains("/"))
            //    {

            //        string[] gfg = filt[3].Split(',');

            //        if (gfg[0].Contains("/MAX"))
            //        {
            //            string[] flt = gfg[0].Split(':');
            //            chkFilterTXT1.Checked = true;
            //            txtFilterTXT1.Text = flt[1];
            //        }
            //        if (gfg[0].Contains("/MIN"))
            //        {
            //            string[] flt = gfg[0].Split(':');
            //            chkFilterTXT2.Checked = true;
            //            txtFilterTXT2.Text = flt[1];
            //        }
            //        if (gfg[0].Contains("/XD"))
            //        {
            //            string[] flt = gfg[0].Split(':');
            //            chkFilterTXT3.Checked = true;
            //            txtFilterTXT3.Enabled = true;
            //            txtFilterTXT3.Text = flt[1];
            //        }
            //        if (gfg[0].Contains("/XF"))
            //        {
            //            string[] flt = gfg[0].Split(':');
            //            txtFilterTXT4.Text = flt[1];
            //            chkFilterTXT4.Enabled = true;
            //            chkFilterTXT4.Checked = true;
            //        }

            //        if (gfg[1].Contains("/"))
            //        {
            //            if (gfg[1].Contains("/MIN"))
            //            {
            //                string[] flt = gfg[1].Split(':');
            //                chkFilterTXT2.Checked = true;
            //                txtFilterTXT2.Text = flt[1];
            //            }
            //            if (gfg[1].Contains("/XF"))
            //            {
            //                string[] flt = gfg[1].Split(':');
            //                txtFilterTXT4.Text = flt[1];
            //                chkFilterTXT4.Checked = true;
            //                txtFilterTXT4.Enabled = true;
            //            }

            //            if (gfg[2].Contains("/"))
            //            {
            //                if (gfg[2].Contains("/XD"))
            //                {
            //                    string[] flt = gfg[2].Split(':');
            //                    chkFilterTXT3.Checked = true;
            //                    txtFilterTXT3.Text = flt[1];
            //                }
            //                if (gfg[3].Contains("/"))
            //                {
            //                    if (gfg[3].Contains("/XF"))
            //                    {
            //                        string[] flt = gfg[3].Split(':');
            //                        txtFilterTXT4.Text = flt[1];
            //                        chkFilterTXT4.Checked = true;
            //                        txtFilterTXT4.Enabled = true;
            //                    }
            //                }
            //           }
            //     }

            //}
            //        }

            //    string AdvFilters = CurrentEntity.AdvancedFilters;

            //    //string filters = CurrentEntity.Filters;

            //    string[] Advfilt = AdvFilters.Split('#');


            //    string[] Advfilte = Advfilt[0].Split(',');

            //    if (Advfilt[0].Contains("/"))
            //    {

            //        //chkfiltersIA.Checked = true;

            //        for (int i = 0; i < Advfilte.Length; i++)
            //        {
            //            int lastElementIndex = Advfilte.Length - 1;

            //            if (i != lastElementIndex)
            //            {

            //                string fddsf = Convert.ToString(Advfilte[i]);

            //                fddsf = fddsf.Substring(fddsf.Length - 1);

            //                int p = Convert.ToInt32(fddsf);

            //                Cbladvfilters1.Items[p].Selected = true;
            //            }

            //        }
            //    }

            //    if (Advfilt[1].Contains("/"))
            //    {

            //        string[] Advfilte1 = Advfilt[1].Split(':');
            //        if (Advfilte1[0].Contains("/MINAGE"))
            //        {
            //            chkadvfilter1.Checked = true;
            //            txtadvfilter1.Text = Advfilte1[1];
            //        }
            //    }

            //    if (Advfilt[2].Contains("/"))
            //    {
            //        string[] Advfilte2 = Advfilt[2].Split(':');
            //        if (Advfilte2[0].Contains("/MAXAGE"))
            //        {
            //            chkadvfilter2.Checked = true;
            //            txtadvfilter2.Text = Advfilte2[1];
            //        }

            //    }

            //    if (Advfilt[3].Contains("/"))
            //    {
            //        string[] Advfilte3 = Advfilt[3].Split(':');
            //        if (Advfilte3[0].Contains("/MINLAD"))
            //        {
            //            chkadvfilter3.Checked = true;
            //            txtadvfilter3.Text = Advfilte3[1];
            //        }
            //    }

            //    if (Advfilt[4].Contains("/"))
            //    {

            //        string[] Advfilte4 = Advfilt[4].Split(':');
            //        if (Advfilte4[0].Contains("/MAXLAD"))
            //        {
            //            chkadvfilter4.Checked = true;
            //            txtadvfilter4.Text = Advfilte4[1];
            //        }
            //    }


            //    if (chkCopyOption.Checked)
            //    {
            //        txtCopyOption.Enabled = true;
            //    }
            //    if (retryoptionR.Checked)
            //    {
            //        txtretryoptionR.Enabled = true;
            //    }
            //    if (retryoptionW.Checked)
            //    {
            //        txtretryoptionW.Enabled = true;
            //    }


            //    if (chkFilterTXT1.Checked)
            //    {
            //        txtFilterTXT1.Enabled = true;
            //    }
            //    if (chkFilterTXT2.Checked)
            //    {
            //        txtFilterTXT2.Enabled = true;
            //    }
            //    if (chkFilterTXT3.Checked)
            //    {
            //        txtFilterTXT3.Enabled = true;
            //    }
            //    if (chkFilterTXT4.Checked)
            //    {
            //        txtFilterTXT4.Enabled = true;
            //    }


            //    if (chkadvfilter1.Checked)
            //    {
            //        txtadvfilter1.Enabled = true;
            //    }
            //    if (chkadvfilter2.Checked)
            //    {
            //        txtadvfilter2.Enabled = true;
            //    }
            //    if (chkadvfilter3.Checked)
            //    {
            //        txtadvfilter3.Enabled = true;
            //    }
            //    if (chkadvfilter4.Checked)
            //    {
            //        txtadvfilter4.Enabled = true;
            //    }



        }


    }
}