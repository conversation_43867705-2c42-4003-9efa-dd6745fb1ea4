﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="ProfileApprovalReport.ascx.cs" Inherits="CP.UI.Controls.ProfileApprovalReport" %>
<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="TK1" %>
<link href="../App_Themes/ReportTheme/Report.css" rel="stylesheet" type="text/css" />

<link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
<script src="../Script/chosen.jquery.js"></script>
<script>
    $(document).ready(function () {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    });
    function pageLoad() {
        $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
    }
</script>
<style>
    .chosen-select + .chosen-container {
        width: 48.5% !important;
        opacity: 1 !important;
    }
</style>

<%--<div class="form-group">
    <label class="col-md-3 control-label">
        Business Service <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlbusinessService" runat="server" DataTextField="Name" DataValueField="Id" CssClass="chosen-select col-md-6" AutoPostBack="true">
                   
        </asp:DropDownList>

        <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" CssClass="error" ControlToValidate="ddltype"
            ValidationGroup="vlGroupSite" InitialVale="0" Display="Dynamic" ErrorMessage="Select Type" InitialValue="0"></asp:RequiredFieldValidator>

        <asp:Label ID="Label3" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
    </div>
</div>--%>

<div class="form-group">
    <label class="col-md-3 control-label">
        Type <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddltype" runat="server" CssClass="chosen-select col-md-6"  OnSelectedIndexChanged="ddltype_SelectedIndexChanged" AutoPostBack="true">
             <asp:ListItem Value="ALL">ALL</asp:ListItem>
             <asp:ListItem Value="Profile">Profile</asp:ListItem>
             <asp:ListItem Value="Workflow">Workflow</asp:ListItem>
        </asp:DropDownList>

        <asp:RequiredFieldValidator ID="rfvddlGroup" runat="server" CssClass="error" ControlToValidate="ddltype"
            ValidationGroup="vlGroupSite" InitialVale="0" Display="Dynamic" ErrorMessage="Select Type" InitialValue="0"></asp:RequiredFieldValidator>

        <asp:Label ID="lblvalidation" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
    </div>
</div>
<%--<asp:Panel ID="Pan1" runat="server" CssClass="widget widget-heading-simple widget-body-white">--%>

<div class="form-group">
    <label class="col-md-3 control-label">
        Status<span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:DropDownList ID="ddlstatus" runat="server" CssClass="chosen-select col-md-6" >
              <asp:ListItem Value="ALL">ALL</asp:ListItem>
              <asp:ListItem Value="Rejected">Rejected</asp:ListItem>
             <asp:ListItem Value="Approved">Approved</asp:ListItem>
             <asp:ListItem Value="Pending">Pending</asp:ListItem>
        </asp:DropDownList>

        <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" CssClass="error" ControlToValidate="ddlstatus"
            ValidationGroup="vlGroupSite" InitialVale="0" Display="Dynamic" ErrorMessage="Select Status" InitialValue="0"></asp:RequiredFieldValidator>

      <%--  <asp:Label ID="Label1" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>--%>
    </div>
</div>

<div class="form-group">
    <label class="col-md-3 control-label">
        Start Date <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <TK1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtstart" PopupPosition="Right"
            PopupButtonID="imgFromDate" Format="yyyy-MM-dd">
        </TK1:CalendarExtender>
        <asp:TextBox ID="txtstart" runat="server" CssClass="form-control" onkeydown="return false"></asp:TextBox>
<%--        <img src="../images/icons/calendar-month.png" width="16" id="imgFromDate" style="margin-left: 5px;" />--%>
        <asp:ImageButton ID="imgFromDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
        <asp:RequiredFieldValidator ID="rfvSic" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtstart"
            ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
        <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ForeColor="Red" ErrorMessage="Select valid date." ValidationGroup="vlGroupSite"
            ControlToValidate="txtstart" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
    </div>
</div>

<div class="form-group">
    <label class="col-md-3 control-label">
        End Date <span class="inactive">*</span>
    </label>
    <div class="col-md-9">
        <asp:TextBox ID="txtend" runat="server" CssClass="form-control" onkeydown="return false"></asp:TextBox>
        <asp:ImageButton ID="imgBtnEndDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
       <%-- <img
            src="../images/icons/calendar-month.png" width="16" id="img1" style="margin-left: 5px;" />--%>
        <TK1:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtend" PopupPosition="Right"
            PopupButtonID="imgBtnEndDate" Format="yyyy-MM-dd">
        </TK1:CalendarExtender>
        <asp:RequiredFieldValidator ID="RequiredFieldValidator3" ForeColor="Red" runat="server" ErrorMessage="Select Date"
            ControlToValidate="txtend" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
        <asp:RegularExpressionValidator ID="RegularExpressionValidator2" ForeColor="Red" runat="server" ErrorMessage="Select valid date." ValidationGroup="vlGroupSite"
            ControlToValidate="txtend" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
    </div>
</div>
 <%--</asp:Panel>--%>


<div class="form-group">
    <div id="divlable" class="col-xs-6" visible="false">
        <asp:Label ID="Label2" runat="server" ForeColor="Red" Visible="false"></asp:Label>
        <span class="bold">Note:</span>&nbsp;<span class="inactive">*</span> <span>Required
                                    Fields</span><br />
           <asp:Label ID="Label1" runat="server" ForeColor="Red" Visible="false"></asp:Label>
    </div>
    <div class="col-xs-6">
        <asp:Button ID="btnPdfSave" CssClass="btn btn-primary" Style="margin-left: 5px" Width="20%" runat="server"
            Text="Excel Report" ValidationGroup="vlGroupSite" OnClick="btnPdfSave_Click" />
    </div>
</div>
<asp:UpdateProgress ID="UpdateProgress1" runat="server">
    <ProgressTemplate>
        <div id="imgLoading" class="loading-mask">
            <span>Loading...</span>
        </div>
    </ProgressTemplate>
</asp:UpdateProgress>