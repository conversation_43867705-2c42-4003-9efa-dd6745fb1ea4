﻿using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq;
using System.Text;


namespace CP.DataAccess
{
    internal sealed class InfrScheLogsDataAccess : BaseDataAccess, IInfraobjectSchedularLogs
    {
        public InfrScheLogsDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<InfraobjectSchedularLogs> CreateEntityBuilder<InfraobjectSchedularLogs>()
        {
            return (new InfrScheLogsBuilder()) as IEntityBuilder<InfraobjectSchedularLogs>;
        }


        IList<InfraobjectSchedularLogs> IInfraobjectSchedularLogs.GetInfraSchedularDetails(int bsid, int bfid, int infraid, string starttime, string endtime)
        {
            try
            {


                const string sp = "INFRASCHEDULARDETAILS";
                var infraschedule = new List<InfraobjectSchedularLogs>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    starttime = Convert.ToDateTime(starttime).ToString("dd-MM-yy");
                    endtime = Convert.ToDateTime(endtime).ToString("dd-MM-yy");
#endif

                    Database.AddInParameter(cmd, Dbstring + "iBSId", DbType.Int32, bsid);
                    Database.AddInParameter(cmd, Dbstring + "iBFId", DbType.Int32, bfid);
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, infraid);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.AnsiString, starttime);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.AnsiString, endtime);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif


                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var infralog = new InfraobjectSchedularLogs
                            {
                                BusinessServiceName = Convert.IsDBNull(reader["BusinessServiceName"]) ? string.Empty : Convert.ToString(reader["BusinessServiceName"]),
                                BusinessFunctionName = Convert.IsDBNull(reader["BusinessFuntionName"]) ? string.Empty : Convert.ToString(reader["BusinessFuntionName"]),
                                InfraObjectName = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]),
                                WorkflowName = Convert.IsDBNull(reader["WorkFlowName"]) ? string.Empty : Convert.ToString(reader["WorkFlowName"]),
                                StartTime = Convert.IsDBNull(reader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["StartTime"]),
                                EndTime = Convert.IsDBNull(reader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["EndTime"]),
                                DRReady = Convert.IsDBNull(reader["DRReady"]) ? string.Empty : Convert.ToString(reader["DRReady"]) == "1" ? "YES" : "NO",
                                DRReason = Convert.IsDBNull(reader["Reason"]) ? string.Empty : Convert.ToString(reader["Reason"]),
                                CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"]) ? string.Empty : Convert.ToString(reader["CurrentActionName"])
                                //CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };

                            infraschedule.Add(infralog);
                        }

                        return infraschedule;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraobjectSchedularLogs.GetInfraSchedularDetails" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraobjectSchedularLogs> IInfraobjectSchedularLogs.GetDRReddynesssummary(int bsid, int bfid, int infraid)
        {
            try
            {


                const string sp = "GETDRREDDYNESSLOG";
                var infraschedule = new List<InfraobjectSchedularLogs>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    Database.AddInParameter(cmd, Dbstring + "iBSId", DbType.Int32, bsid);
                    Database.AddInParameter(cmd, Dbstring + "iBFId", DbType.Int32, bfid);
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, infraid);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif


                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var infralog = new InfraobjectSchedularLogs
                            {
                                BusinessServiceName = Convert.IsDBNull(reader["BusinessServiceName"]) ? string.Empty : Convert.ToString(reader["BusinessServiceName"]),
                                BusinessFunctionName = Convert.IsDBNull(reader["BusinessFuntionName"]) ? string.Empty : Convert.ToString(reader["BusinessFuntionName"]),
                                InfraObjectName = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]),
                                WorkflowName = Convert.IsDBNull(reader["WorkFlowName"]) ? string.Empty : Convert.ToString(reader["WorkFlowName"]),
                                StartTime = Convert.IsDBNull(reader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["StartTime"]),
                                EndTime = Convert.IsDBNull(reader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["EndTime"]),
                                DRReady = Convert.IsDBNull(reader["DRReady"]) ? string.Empty : Convert.ToString(reader["DRReady"]) == "1" ? "YES" : "NO",
                                DRReason = Convert.IsDBNull(reader["Reason"]) ? string.Empty : Convert.ToString(reader["Reason"]),
                                CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"]) ? string.Empty : Convert.ToString(reader["CurrentActionName"]),
                                InfraObjectId = Convert.IsDBNull(reader["infraobjectid"]) ? 0 : Convert.ToInt32(reader["infraobjectid"]),
                               // IsDRReady = Convert.IsDBNull(reader["isdrready"]) ? 0 : Convert.ToInt32(reader["isdrready"]),

                               CurrentActionId = Convert.IsDBNull(reader["ActionID"]) ? 0 : Convert.ToInt32(reader["ActionID"]),
                               WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]),

                                //DRMessage = Convert.IsDBNull(reader["message"]) ? string.Empty : Convert.ToString(reader["message"]),
                                CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };

                            infraschedule.Add(infralog);
                        }

                        return infraschedule;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraobjectSchedularLogs.GetInfraSchedularDetails" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraobjectSchedularLogs> IInfraobjectSchedularLogs.GetLogsdeatilsById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "GetInfra_LogsBySchId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return CreateEntityBuilder<InfraobjectSchedularLogs>().BuildEntities(reader);
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraobjectSchedularLogs.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<InfraobjectSchedularLogs> IInfraobjectSchedularLogs.GetByInfraobjectId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "GetInfra_SchLogByInfraId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return CreateEntityBuilder<InfraobjectSchedularLogs>().BuildEntities(reader);
                        }
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraobjectSchedularLogs.GetByInfraobjectId(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }


        IList<InfraobjectSchedularLogs> IInfraobjectSchedularLogs.GetInfraSchedularDetailsnew(int bsid, int bfid, int infraid, int isproted, int userid, string starttime, string endtime)
        {
            try
            {


                const string sp = "INFRASCHEDULARDETAILS_new";
                var infraschedule = new List<InfraobjectSchedularLogs>();
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

#if ORACLE
                    starttime = Convert.ToDateTime(starttime).ToString("dd-MM-yy");
                    endtime = Convert.ToDateTime(endtime).ToString("dd-MM-yy");
#endif

                    Database.AddInParameter(cmd, Dbstring + "iBSId", DbType.Int32, bsid);
                    Database.AddInParameter(cmd, Dbstring + "iBFId", DbType.Int32, bfid);
                    Database.AddInParameter(cmd, Dbstring + "iInfraId", DbType.Int32, infraid);
                    Database.AddInParameter(cmd, Dbstring + "iprotected", DbType.AnsiString, isproted);
                    Database.AddInParameter(cmd, Dbstring + "iuesrid", DbType.AnsiString, userid);
                    Database.AddInParameter(cmd, Dbstring + "iStartDate", DbType.AnsiString, starttime);
                    Database.AddInParameter(cmd, Dbstring + "iEndDate", DbType.AnsiString, endtime);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif


                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var infralog = new InfraobjectSchedularLogs
                            {
                                BusinessServiceName = Convert.IsDBNull(reader["BusinessServiceName"]) ? string.Empty : Convert.ToString(reader["BusinessServiceName"]),
                                BusinessFunctionName = Convert.IsDBNull(reader["BusinessFuntionName"]) ? string.Empty : Convert.ToString(reader["BusinessFuntionName"]),
                                InfraObjectName = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]),
                                WorkflowName = Convert.IsDBNull(reader["WorkFlowName"]) ? string.Empty : Convert.ToString(reader["WorkFlowName"]),
                                StartTime = Convert.IsDBNull(reader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["StartTime"]),
                                EndTime = Convert.IsDBNull(reader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["EndTime"]),
                                DRReady = Convert.IsDBNull(reader["DRReady"]) ? string.Empty : Convert.ToString(reader["DRReady"]) == "1" ? "YES" : "NO",
                                DRReason = Convert.IsDBNull(reader["Reason"]) ? string.Empty : Convert.ToString(reader["Reason"]),
                                CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"]) ? string.Empty : Convert.ToString(reader["CurrentActionName"]),
                                //CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                                       
                            };

                            infraschedule.Add(infralog);
                        }

                        return infraschedule;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraobjectSchedularLogs.GetInfraSchedularDetailsnew" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

        IList<InfraobjectSchedularLogs> IInfraobjectSchedularLogs.GetLogsdeatilsBy_BSId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                var infraschedule = new List<InfraobjectSchedularLogs>();
                const string sp = "GetInfra_LogsBySchBSId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            var infralog = new InfraobjectSchedularLogs
                            {
                                BusinessServiceName = Convert.IsDBNull(reader["BusinessServiceName"]) ? string.Empty : Convert.ToString(reader["BusinessServiceName"]),
                                BusinessFunctionName = Convert.IsDBNull(reader["BusinessFuntionName"]) ? string.Empty : Convert.ToString(reader["BusinessFuntionName"]),
                                InfraObjectName = Convert.IsDBNull(reader["InfraobjectName"]) ? string.Empty : Convert.ToString(reader["InfraobjectName"]),
                                InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]),
                                WorkflowName = Convert.IsDBNull(reader["WorkFlowName"]) ? string.Empty : Convert.ToString(reader["WorkFlowName"]),
                                StartTime = Convert.IsDBNull(reader["StartTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["StartTime"]),
                                EndTime = Convert.IsDBNull(reader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["EndTime"]),
                                DRReady = Convert.IsDBNull(reader["DRReady"]) ? string.Empty : Convert.ToString(reader["DRReady"]) == "1" ? "YES" : "NO",
                                DRReason = Convert.IsDBNull(reader["Reason"]) ? string.Empty : Convert.ToString(reader["Reason"]),
                                CurrentActionName = Convert.IsDBNull(reader["CurrentActionName"]) ? string.Empty : Convert.ToString(reader["CurrentActionName"])
                                //CreateDate = Convert.IsDBNull(reader["CreateDate"]) ? DateTime.MinValue : Convert.ToDateTime(reader["CreateDate"])
                            };

                            infraschedule.Add(infralog);
                        }

                        return infraschedule;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IInfraobjectSchedularLogs.GetById(" + id + ")" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + ex.Message, ex);
            }
        }

    
    }
}
