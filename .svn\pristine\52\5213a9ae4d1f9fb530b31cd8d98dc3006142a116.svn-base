﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web.UI.WebControls;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using CP.UI.Code.Base;
using CP.UI.Controls;
using System.Web;
using System.Text.RegularExpressions;

namespace CP.UI.Controls
{
    public partial class RecoveryPointConfig : ReplicationControl
    {
        #region

        private TextBox _txtReplicationName = new TextBox();
        private DropDownList _ddlReplicationType = new DropDownList();
        private DropDownList _ddlSiteId = new DropDownList();
        private RecoveryPoint _recoveryPoint = null;
        private static readonly IList<RecoveryPointMulti> _finalrecoverypointmulti = new List<RecoveryPointMulti>();
        private RecoveryPointMulti recoverypointmulti = new RecoveryPointMulti();

        #endregion

        #region Properties

        public RecoveryPoint CurrentEntity
        {
            get { return _recoveryPoint ?? (_recoveryPoint = new RecoveryPoint()); }

            set
            {
                _recoveryPoint = value;
            }
        }

        public TextBox ReplicationName
        {
            get
            {
                _txtReplicationName = Parent.FindControl("txtReplName") as TextBox;
                return _txtReplicationName;
            }
            set { _txtReplicationName = value; }
        }

        public DropDownList ReplicationType
        {
            get
            {
                _ddlReplicationType = Parent.FindControl("ddlRepType") as DropDownList;
                return _ddlReplicationType;
            }
            set
            {
                _ddlReplicationType = value;
            }
        }

        public DropDownList SiteId
        {
            get
            {
                _ddlSiteId = Parent.FindControl("ddlSite") as DropDownList;
                return _ddlSiteId;
            }
            set
            {
                _ddlSiteId = value;
            }
        }

        public string MessageInitials
        {
            get { return "Recover Point"; }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.ReplicationList;
                }
                return string.Empty;
            }
        }

        #endregion

        public override void PrepareView()
        {
            ddlServer.Attributes.Add("onblur", "ValidatorValidate(" + rfvddlServer.ClientID + ")");
            txtDGName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator1.ClientID + ")");
            txtProductionSiteName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator4.ClientID + ")");
            txtDRsiteName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator2.ClientID + ")");
            //txtNearDRsiteName.Attributes.Add("onblur", "ValidatorValidate(" + RequiredFieldValidator3.ClientID + ")");


            Session.Remove("RecoveryPoint");

            //Utility.PopulateServerByType(ddlServer, GroupServerType.DRDBServer.ToString(), "PR", true);

            Utility.PopulateServer(ddlServer, GroupServerType.DRDBServer.ToString(), "PR", true);
            hdfStaticGuid.Value = Convert.ToString(Guid.NewGuid());
            PrepareEditView();
            ShowListview();
        }

        private void ShowListview()
        {
            lvgroup.DataSource = _finalrecoverypointmulti;
            lvgroup.DataBind();
        }

        private void PrepareEditView()
        {
            if (CurrentRecoveryPoint != null)
            {
                CurrentEntity = CurrentRecoveryPoint;
                Session["RecoveryPoint"] = CurrentEntity;

                ddlServer.SelectedValue = CurrentEntity.ServerId.ToString();
                ddlServer_SelectedIndexChanged(null, null);

                txtDSCLIHostname.Enabled = false;
                txtDSCLIServerIP.Enabled = false;
                txtSSHUserID.Enabled = false;
                txtSSHPassword.Enabled = false;
                txtDGName.Text = CurrentEntity.DGroupName;
                //txtclusterNodeName.Text = CurrentEntity.ClusterNodeName;
                //txtclusterNodeNameDR.Text = CurrentEntity.CLusterNodeNameDR;
                //txtProductionSiteName.Text = CurrentEntity.ProductionSiteName;
                //txtDRsiteName.Text = CurrentEntity.DRSiteName;
                //txtNearDRsiteName.Text = CurrentEntity.NearDRSiteName;
                btnSave.Text = "Update";

                BindRecoverPointMultiDataData();
            }
            else
            {
                _finalrecoverypointmulti.Clear();
            }

        }


        protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        {
            if ((Session["ReplicationConfig_token"] != null) && (Session["ReplicationConfig_token"].ToString().Split(':').Length > 1))
            {
                if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["ReplicationConfig_token"])) || Request.HttpMethod != "POST" || CryptographyHelper.Md5Decrypt(Session["ReplicationConfig_token"].ToString().Split(':')[1].ToString()) != HttpContext.Current.Request.Cookies["ASP.NET_SessionId"].Value.ToString())
                {
                    ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
                    Logout _Logout = new Logout();
                    _Logout.PrepareView();
                    return false;
                }
            }
            else
            {
                ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
                Logout _Logout = new Logout();
                _Logout.PrepareView();
                return false;
            }
            return true;
        }

        //EOC Validate Request

        protected bool ValidateInput()
        {
            try
            {


                var allTextBoxesOnThePage = Page.GetAllControlsOfType<TextBox>();
                var htmllist = allTextBoxesOnThePage.Where(item => item.Text.StringContainHTMLTag() == true && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                if (htmllist.Count > 0)
                {
                    return false;
                }
                var IgnoreIDs = new List<string>();
                //IgnoreIDs.Add("txtSSHUser");
                //IgnoreIDs.Add("txtLicenceKey");
                var list = allTextBoxesOnThePage.Where(item => !IgnoreIDs.Contains(item.ID) && CP.Helper.StringExtension.StringContainSpecialCharaters(item.Text) && item.TextMode != System.Web.UI.WebControls.TextBoxMode.Password).ToList<TextBox>();
                return list.Count == 0;
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        protected void ddlServer_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (ddlServer.SelectedValue != "0")
            {
                var server = Facade.GetServerById(Convert.ToInt32(ddlServer.SelectedValue));
                if (server != null)
                {
                    txtDSCLIHostname.Text = server.Name == null ? string.Empty : server.Name;
                    txtDSCLIServerIP.Text = server.IPAddress == null ? string.Empty : CryptographyHelper.Md5Decrypt(server.IPAddress);
                    txtSSHUserID.Text = server.SSHUserName == null ? string.Empty : CryptographyHelper.Md5Decrypt(server.SSHUserName);
                    txtSSHPassword.Text = CryptographyHelper.Md5Decrypt(server.SSHPassword);
                    txtSSHPassword.Attributes["value"] = Utility.getHashKeyByString(txtSSHPassword.Text, hdfStaticGuid.Value);

                    //txtSSHPassword.Attributes.Add("value", Utility.IsMD5EncryptedString(server.SSHPassword) ? server.SSHPassword : CryptographyHelper.Md5Encrypt(server.SSHPassword));
                }

            }
            else
            {
                txtDSCLIHostname.Text = string.Empty;
                txtDSCLIServerIP.Text = string.Empty;
                txtSSHUserID.Text = string.Empty;
                txtSSHPassword.Attributes["value"] = string.Empty;
            }

        }

        private bool CheckReplicationNameExist()
        {
            if (ReplicationName.Text.ToLower().Equals(CurrentReplicationName.ToLower()))
            {
                return false;
            }

            return Facade.IsExistReplicationBaseByName(ReplicationName.Text.ToLower());
        }

        private void SaveEditor()
        {
            if (CurrentEntity.IsNew)
            {
                CurrentEntity.ReplicationBase.CreatorId = LoggedInUserId == null ? 0 : LoggedInUserId;
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId == null ? 0 : LoggedInUserId;
                CurrentEntity = Facade.RecoveryPoint_Add(CurrentEntity);
                //Replication Base Data will get insert from CurrentEntity = Facade.RecoveryPoint_Add(CurrentEntity);

                if (_finalrecoverypointmulti.Count != 0)
                {
                    foreach (RecoveryPointMulti inward in _finalrecoverypointmulti)
                    {
                        RecoveryPointMulti recover = new RecoveryPointMulti();

                        recover.ServerId = ddlServer == null ? 0 : Convert.ToInt32(ddlServer.SelectedValue);
                        recover.ReplicationId = Convert.ToInt32(Session["recoverypointmultiReplication"]);
                        recover.GroupName = inward.GroupName;
                        recover.PRSiteName = inward.PRSiteName;
                        recover.DRSiteName = inward.DRSiteName;
                        recover.PRClusterNodeName = inward.PRClusterNodeName;
                        recover.DRClusterNodeName = inward.DRClusterNodeName;
                        Facade.AddRecoveryPointMulti(recover);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "RecoveryPointMulti", UserActionType.CreateFastCopyJob, "The RecoveryPointMulti '" + recover.Id + "' was added to the Replication component.", LoggedInUserId);
                    }
                }
            }


            else
            {
                CurrentEntity.ReplicationBase.UpdatorId = LoggedInUserId;
                CurrentEntity = Facade.RecoveryPoint_Update(CurrentEntity);
                //Replication Base Data will get updated from CurrentEntity =  CurrentEntity = Facade.RecoveryPoint_Update(CurrentEntity);      

                if (_finalrecoverypointmulti.Count != 0)
                {
                    foreach (RecoveryPointMulti inward in _finalrecoverypointmulti)
                    {
                        RecoveryPointMulti recover = new RecoveryPointMulti();

                        recover.Id = inward.Id;
                        recover.ServerId = ddlServer == null ? 0 : Convert.ToInt32(ddlServer.SelectedValue);
                        recover.ReplicationId = inward.ReplicationId; //Convert.ToInt32(Session["RecoveryPoointMultiUpdateRepliId"]);
                        recover.GroupName = inward.GroupName;
                        recover.PRSiteName = inward.PRSiteName;
                        recover.DRSiteName = inward.DRSiteName;
                        recover.PRClusterNodeName = inward.PRClusterNodeName;
                        recover.DRClusterNodeName = inward.DRClusterNodeName;
                        Facade.UpdateRecoveryPointMulti(recover);
                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "RecoveryPointMulti", UserActionType.CreateFastCopyJob, "The RecoveryPointMulti '" + recover.Id + "' was added to the Replication component.", LoggedInUserId);
                    }
                }
            }
        }



        private void BuildEntities()
        {
            if (Session["RecoveryPoint"] != null)
            {
                CurrentEntity = (RecoveryPoint)Session["RecoveryPoint"];
            }

            CurrentEntity.ReplicationBase.Id = CurrentReplicationId;
            CurrentEntity.ReplicationBase.Name = ReplicationName.Text;
            CurrentEntity.ReplicationBase.Type = ReplicationType == null ? 0 : (ReplicationType)Enum.Parse(typeof(ReplicationType), ReplicationType.SelectedValue, true);
            CurrentEntity.ReplicationBase.SiteId = SiteId == null ? 0 : Convert.ToInt32(SiteId.SelectedValue);
            CurrentEntity.ServerId = ddlServer == null ? 0 : Convert.ToInt32(ddlServer.SelectedValue);
            CurrentEntity.ReplicationId = ReplicationType == null ? 0 : Convert.ToInt32(ReplicationType.SelectedValue);
            CurrentEntity.DGroupName = txtDGName.Text;
            CurrentEntity.ClusterNodeName = txtclusterNodeName.Text;
            CurrentEntity.CLusterNodeNameDR = txtclusterNodeNameDR.Text;
            CurrentEntity.ProductionSiteName = txtProductionSiteName.Text;
            CurrentEntity.DRSiteName = txtDRsiteName.Text;
            CurrentEntity.NearDRSiteName = txtNearDRsiteName.Text;


        }


        protected void BtnCancelClick(object sender, EventArgs e)
        {
            Response.Redirect(Constants.UrlConstants.Urls.Component.ReplicationList);
        }

        protected void btnSave_Click(object sender, EventArgs e)
        {
            Label lblPrName = Parent.FindControl("lblPrName") as Label;
            if (ReplicationName.Text != "" || !string.IsNullOrEmpty(ReplicationName.Text))
            {
                lblPrName.Text = CheckReplicationNameExist() ? "Replication Name is Not Avaliable" : string.Empty;
            }
            var submitButton = (Button)sender;
            string buttionText = " " + submitButton.Text.ToLower() + " ";

            var currentTransactionType = TransactionType.Undefined;
            if (buttionText.Contains(" submit ") || buttionText.Contains(" save "))
            {
                currentTransactionType = TransactionType.Save;
            }
            else if (buttionText.Contains(" update "))
            {
                currentTransactionType = TransactionType.Update;
            }

            if ((Session["ReplicationConfig_token"] != null) && !CheckReplicationNameExist() && ValidateRequest("HyperVConfiguration", UserActionType.CreateReplicationComponent))
            {

                if (!ValidateInput())
                {
                    string returnUrl1 = Request.RawUrl;

                    ErrorSuccessNotifier.AddErrorMessage("Invalid Characters");

                    ExceptionManager.Manage(new CpException(CpExceptionType.InvalidCharacters), Page);

                    Helper.Url.Redirect(returnUrl1);

                    //throw new CpException(CpExceptionType.InvalidCharacters);
                }
                else
                {
                    string returnUrl = Helper.Url.SecureUrl.ReturnUrl;

                    if (returnUrl.IsNullOrEmpty())
                    {
                        returnUrl = ReturnUrl;
                    }

                    try
                    {
                        if (ValidateRequest("RecoverPointConfig", UserActionType.CreateReplicationComponent))
                        {
                            BuildEntities();
                            StartTransaction();
                            SaveEditor();
                            EndTransaction();
                            string message = MessageInitials + " " + '"' + ReplicationName.Text + '"';
                            ErrorSuccessNotifier.AddSuccessMessage(Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(message,
                                                                                                  currentTransactionType));
                            btnSave.Enabled = false;
                        }
                    }
                    catch (CpException ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        ExceptionManager.Manage(ex, Page);
                    }
                    catch (Exception ex)
                    {
                        InvalidateTransaction();

                        returnUrl = Request.RawUrl;

                        ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                        if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                        {
                            ExceptionManager.Manage((CpException)ex.InnerException, Page);
                        }
                        else
                        {
                            var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occurred while saving data", ex);

                            ExceptionManager.Manage(customEx, Page);
                        }
                    }
                    if (returnUrl.IsNotNullOrEmpty())
                    {
                        var secureUrl = UrlHelper.BuildSecureUrl(returnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationId, CurrentEntity.Id);
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationName, ReplicationName.Text);
                        WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.ReplicationType, ReplicationType.SelectedValue);
                        Helper.Url.Redirect(secureUrl);
                    }

                }
            }
        }

        protected void lvgroup_ItemUpdating(object sender, ListViewUpdateEventArgs e)
        {
            var lbl = (lvgroup.Items[e.ItemIndex].FindControl("Id")) as Label;
            var lblId = lbl.Text.ToInteger();

            Label lblUpdateId = (lvgroup.Items[e.ItemIndex].FindControl("Id")) as Label;

            if (lblUpdateId != null)
            {
                recoverypointmulti.Id = Convert.ToInt32(lblUpdateId.Text);
            }


            var txtgrpname = (lvgroup.Items[e.ItemIndex].FindControl("txtgrpname")) as TextBox;

            if (txtgrpname != null && txtgrpname.Text != "")
            {
                recoverypointmulti.GroupName = txtgrpname.Text.Trim();
            }

            var txtprsitename = (lvgroup.Items[e.ItemIndex].FindControl("txtprsitename")) as TextBox;
            if (txtprsitename != null && txtprsitename.Text != "")
            {
                recoverypointmulti.PRSiteName = txtprsitename.Text.Trim();
            }

            var txtdrsitename = (lvgroup.Items[e.ItemIndex].FindControl("txtdrsitename")) as TextBox;

            if (txtdrsitename != null && txtdrsitename.Text != "")
            {
                recoverypointmulti.DRSiteName = txtdrsitename.Text.Trim();
            }

            var txtprclusternodename = (lvgroup.Items[e.ItemIndex].FindControl("txtprclusternodename")) as TextBox;
            if (txtprclusternodename != null && txtprclusternodename.Text != "")
            {
                recoverypointmulti.PRClusterNodeName = txtprclusternodename.Text.Trim();
            }

            var txtdrclusternodename = (lvgroup.Items[e.ItemIndex].FindControl("txtdrclusternodename")) as TextBox;
            if (txtdrclusternodename != null && txtdrclusternodename.Text != "")
            {
                recoverypointmulti.DRClusterNodeName = txtdrclusternodename.Text.Trim();
            }




            if (btnSave.Text == "Save")
            {
                _finalrecoverypointmulti.Insert(e.ItemIndex, recoverypointmulti);
                _finalrecoverypointmulti.RemoveAt(e.ItemIndex + 1);
                lvgroup.EditIndex = -1;
                ShowListview();
            }
            else
            {
                _finalrecoverypointmulti.Clear();
                recoverypointmulti.Id = lblUpdateId.Text.ToInteger();
                recoverypointmulti.GroupName = txtgrpname.Text;
                recoverypointmulti.PRSiteName = txtprsitename.Text;
                recoverypointmulti.DRSiteName = txtdrsitename.Text;
                recoverypointmulti.PRClusterNodeName = txtprclusternodename.Text;
                recoverypointmulti.DRClusterNodeName = txtdrclusternodename.Text;
                recoverypointmulti.ReplicationId = CurrentReplicationId;
                recoverypointmulti.ServerId = Convert.ToInt32(ddlServer.SelectedItem.Value);


                Facade.UpdateRecoveryPointMulti(recoverypointmulti);

                lvgroup.EditIndex = -1;
                BindRecoverPointMultiDataData();
                if (_finalrecoverypointmulti.Count != 0)
                {
                    ShowListview();
                }
            }

        }

        protected void lvgroup_ItemDataBound(object sender, ListViewItemEventArgs e)
        {


        }

        protected void lvgroup_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {

            var lblId = lvgroup.Items[e.ItemIndex].FindControl("Id") as Label;
            if (lblId != null && lblId.Text != null)
            {
                if (btnSave.Text == "Update")
                {
                    var recoverpointmultiDetails = Facade.GetRecoveryPointMultiById(Convert.ToInt32(lblId.Text));

                    var replicationBaseDetails = Facade.GetReplicationBaseById(recoverpointmultiDetails != null ? Convert.ToInt32(recoverpointmultiDetails.ReplicationId) : 0);

                    Facade.DeleteRecoveryPointMultiById(Convert.ToInt32(lblId.Text));

                    BindRecoverPointMultiDataData();
                    ShowListview();
                }
                else
                {
                    _finalrecoverypointmulti.RemoveAt(e.ItemIndex);
                    ShowListview();
                }
            }

        }

        protected void lvgroup_ItemCreated(object sender, ListViewItemEventArgs e)
        {

            if (e.Item.ItemType == ListViewItemType.InsertItem)
            {
                var txtinsertgrpname = (TextBox)e.Item.FindControl("txtinsertgrpname");
                var txtInsertprsitename = (TextBox)e.Item.FindControl("txtInsertprsitename");
                var txtInsertdrsitename = (TextBox)e.Item.FindControl("txtInsertdrsitename");
                var txtInsertprclusternodename = (TextBox)e.Item.FindControl("txtInsertprclusternodename");
                var txtInsertdrclusternodename = (TextBox)e.Item.FindControl("txtInsertdrclusternodename");


                if (txtinsertgrpname != null)
                {
                    var txts = txtinsertgrpname.Text;
                }

                if (txtInsertprsitename != null)
                {
                    var txtd = txtInsertprsitename.Text;
                }

                if (txtInsertdrsitename != null)
                {
                    var txtd = txtInsertdrsitename.Text;
                }

                if (txtInsertprclusternodename != null)
                {
                    var txtd = txtInsertprclusternodename.Text;
                }

                if (txtInsertdrclusternodename != null)
                {
                    var txtd = txtInsertdrclusternodename.Text;
                }



            }

        }

        protected void lvgroup_ItemInserting(object sender, ListViewInsertEventArgs e)
        {
            if (btnSave.Text == "Save")
            {

                bool isValid = true;

                var GetIdInward = new RecoveryPointMulti();

                var txtinsertgrpname = (TextBox)e.Item.FindControl("txtinsertgrpname");
                var txtInsertprsitename = (TextBox)e.Item.FindControl("txtInsertprsitename");
                var txtInsertdrsitename = (TextBox)e.Item.FindControl("txtInsertdrsitename");
                var txtInsertprclusternodename = (TextBox)e.Item.FindControl("txtInsertprclusternodename");
                var txtInsertdrclusternodename = (TextBox)e.Item.FindControl("txtInsertdrclusternodename");

                Label lblIngrpname = (Label)e.Item.FindControl("lblIngrpname");
                Label lblInprsitename = (Label)e.Item.FindControl("lblInprsitename");
                Label lblIndrsitename = (Label)e.Item.FindControl("lblIndrsitename");
                Label lblprclusternodename = (Label)e.Item.FindControl("lblprclusternodename");
                Label lbldrclusternodename = (Label)e.Item.FindControl("lbldrclusternodename");

                if (txtinsertgrpname.Text == "" || txtInsertprsitename.Text == "" || txtInsertdrsitename.Text == "" || txtInsertprclusternodename.Text == "" || txtInsertdrclusternodename.Text == "")
                {
                    labelSDErrormessage.Text = "*";
                    labelSDErrormessage.Visible = true;
                    isValid = false;
                }

                //var regex = @"^[A-Za-z\d_-]+$";
                var regex = @"^[a-zA-Z0-9_\s\-\,]+$";

                var gpname = Regex.Match(txtinsertgrpname.Text, regex, RegexOptions.IgnoreCase);
                var prst = Regex.Match(txtInsertprsitename.Text, regex, RegexOptions.IgnoreCase);
                var drst = Regex.Match(txtInsertdrsitename.Text, regex, RegexOptions.IgnoreCase);
                var prclstr = Regex.Match(txtInsertprclusternodename.Text, regex, RegexOptions.IgnoreCase);
                var drclstr = Regex.Match(txtInsertdrclusternodename.Text, regex, RegexOptions.IgnoreCase);

                if (!gpname.Success || !prst.Success || !drst.Success || !prclstr.Success || !drclstr.Success)
                {
                    labelSDErrormessage.Text = "Please Enter Valid Data";
                    labelSDErrormessage.Visible = true;
                    isValid = false;
                    return;
                }


                if (isValid)
                {

                    recoverypointmulti.GroupName = txtinsertgrpname.Text;
                    recoverypointmulti.PRSiteName = txtInsertprsitename.Text;
                    recoverypointmulti.DRSiteName = txtInsertdrsitename.Text;
                    recoverypointmulti.PRClusterNodeName = txtInsertprclusternodename.Text;
                    recoverypointmulti.DRClusterNodeName = txtInsertdrclusternodename.Text;
                    recoverypointmulti.ServerId = Convert.ToInt32(ddlServer.SelectedItem.Value);

                    _finalrecoverypointmulti.Add(recoverypointmulti);

                    if (lvgroup.EditIndex != -1)
                        lvgroup.EditIndex = -1;

                    if (_finalrecoverypointmulti != null)
                    {
                        ShowListview();
                    }
                }
                else
                {
                    return;
                }
            }
            else
            {
                _finalrecoverypointmulti.Clear();

                bool isValid = true;

                var GetIdInward = new RecoveryPointMulti();

                var txtinsertgrpname = (TextBox)e.Item.FindControl("txtinsertgrpname");
                var txtInsertprsitename = (TextBox)e.Item.FindControl("txtInsertprsitename");
                var txtInsertdrsitename = (TextBox)e.Item.FindControl("txtInsertdrsitename");
                var txtInsertprclusternodename = (TextBox)e.Item.FindControl("txtInsertprclusternodename");
                var txtInsertdrclusternodename = (TextBox)e.Item.FindControl("txtInsertdrclusternodename");

                Label lblIngrpname = (Label)e.Item.FindControl("lblIngrpname");
                Label lblInprsitename = (Label)e.Item.FindControl("lblInprsitename");
                Label lblIndrsitename = (Label)e.Item.FindControl("lblIndrsitename");
                Label lblprclusternodename = (Label)e.Item.FindControl("lblprclusternodename");
                Label lbldrclusternodename = (Label)e.Item.FindControl("lbldrclusternodename");


                if (isValid)
                {
                    recoverypointmulti.GroupName = txtinsertgrpname.Text;
                    recoverypointmulti.PRSiteName = txtInsertprsitename.Text;
                    recoverypointmulti.DRSiteName = txtInsertdrsitename.Text;
                    recoverypointmulti.PRClusterNodeName = txtInsertprclusternodename.Text;
                    recoverypointmulti.DRClusterNodeName = txtInsertdrclusternodename.Text;
                    recoverypointmulti.ServerId = Convert.ToInt32(ddlServer.SelectedItem.Value);

                    _finalrecoverypointmulti.Add(recoverypointmulti);

                    if (_finalrecoverypointmulti.Count != 0)
                    {
                        foreach (RecoveryPointMulti job in _finalrecoverypointmulti)
                        {
                            RecoveryPointMulti inw = new RecoveryPointMulti();

                            inw.Id = job.Id;
                            inw.ReplicationId = CurrentReplicationId;
                            inw.GroupName = job.GroupName;
                            inw.PRSiteName = job.PRSiteName;
                            inw.DRSiteName = job.DRSiteName;
                            inw.PRClusterNodeName = job.PRClusterNodeName;
                            inw.DRClusterNodeName = job.DRClusterNodeName;
                            inw.ServerId = job.ServerId;


                            Facade.AddRecoveryPointMulti(inw);
                        }

                        BindRecoverPointMultiDataData();

                        if (lvgroup.EditIndex != -1)
                            lvgroup.EditIndex = -1;

                        if (_finalrecoverypointmulti.Count != 0)
                        {
                            ShowListview();
                        }
                    }
                }
                else
                {
                    return;
                }
            }

        }

        private void BindRecoverPointMultiDataData()
        {

            int replid = CurrentEntity.ReplicationId;
            int rep = CurrentReplicationId;

            if (CurrentRecoveryPointMulti != null)
            {
                IList<RecoveryPointMulti> recoverpointmulti = Facade.GetRecoveryPointMultiListByReplicationId(rep);
                _finalrecoverypointmulti.Clear();

                if (Session["PreviousItem"] == null)
                {
                    Session["PreviousItem"] = recoverpointmulti;
                }

                if (recoverpointmulti != null)
                {
                    _finalrecoverypointmulti.Clear();

                    foreach (RecoveryPointMulti job in recoverpointmulti)
                    {
                        RecoveryPointMulti inw = new RecoveryPointMulti();

                        inw.Id = job.Id;
                        inw.ReplicationId = CurrentReplicationId;
                        inw.GroupName = job.GroupName;
                        inw.PRSiteName = job.PRSiteName;
                        inw.DRSiteName = job.DRSiteName;
                        inw.PRClusterNodeName = job.PRClusterNodeName;
                        inw.DRClusterNodeName = job.DRClusterNodeName;
                        inw.ServerId = job.ServerId;

                        _finalrecoverypointmulti.Add(inw);
                    }
                }
            }
        }

        protected void lvgroup_ItemCanceling(object sender, ListViewCancelEventArgs e)
        {

        }

        protected void lvgroup_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            Label lblId = lvgroup.Items[e.NewEditIndex].FindControl("Id") as Label;

            var Id = lblId.Text;

            if (btnSave.Text == "Save")
            {
                lvgroup.EditIndex = e.NewEditIndex;
                lvgroup.DataSource = _finalrecoverypointmulti;
                lvgroup.DataBind();
            }
            else
            {
                lvgroup.EditIndex = e.NewEditIndex;

                if (CurrentEntity != null)
                {
                    IList<RecoveryPointMulti> recoverpointmulti = Facade.GetRecoveryPointMultiListByReplicationId(CurrentRecoveryPointMulti.ReplicationId);

                    if (recoverpointmulti != null)
                    {
                        lvgroup.DataSource = recoverpointmulti;
                        lvgroup.DataBind();
                    }
                }
            }

        }

        protected void lvgroup_ItemCommand(object sender, ListViewCommandEventArgs e)
        {

        }

    }
}