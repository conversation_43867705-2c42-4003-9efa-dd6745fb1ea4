﻿using System.Collections.Generic;
using CP.Common.DatabaseEntity;

namespace CP.DataAccess
{
    #region IServerDataAccess

    public interface IServerDataAccess
    {
        Server Add(Server server);

        Server Update(Server server);

        Server GetById(int id);

        IList<Server> GetServerByInfraObjectId(int infraObjectId);

        IList<Server> GetAllByType(string type, int userid, string role);

        IList<Server> GetAllByOsType(string osType);

        IList<Server> GetAll();

        IList<Server> GetServerType(string type);

        IList<Server> GetByCompanyId(int companyId, bool isParent);

        IList<Server> GetServerBySiteId(int siteId);

        IList<Server> GetServerByIP(string ipAddress);

        bool UpdateByStatusAndId(int id, int status);

        bool DeleteById(int id);

        bool IsExistByName(string name);

        bool UpdateIsVerified(int id, int isVerified);

        IList<Server> GetByUserinfraId(int userid);

        IList<Server> GetServersListUserinfraId(int userid, int companyid, string role, string ostype);

        IList<Server> GetServersListUserinfraId(int userid, int companyid, string role);

        bool UpdateServerIPAddress(string IpAddress, string userName, string pwd);

        IList<Server> GetAllServersNameAndId();

        IList<Server> GetByAll();

        IList<Server> GetServerByDatabaseID(int databaseId);

    }

    #endregion IServerDataAccess
}