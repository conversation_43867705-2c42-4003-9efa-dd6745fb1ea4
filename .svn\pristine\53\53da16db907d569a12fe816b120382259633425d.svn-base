﻿<%@ Page Title="Continuity Patrol :: Postgres Monitoring" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="Postgre9xMonitoring.aspx.cs" Inherits="CP.UI.Component.Postgre9xMonitoring" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
</asp:Content>

<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <div class="innerLR">
  
         <h3><img src="../Images/infra-icon.png">
           Postgres Monitoring</h3>
        
        <div class="widget" data-toggle="collapse-widget" data-collapse-closed="true">
            <div class="widget-head" id="exhealth">
                
                <h4 class="heading">
                    <img src="../Images/health.png" /> Database Monitoring
                    <asp:Label ID="lblgrName" runat="server" Text="" Style=""></asp:Label>
                </h4>
            </div>
            <div id="exhealth-content">
                <div class="widget-body">


                    <table class="table table-striped table-bordered table-condensed " width="100%">
                      
                        <thead>
                            <tr>
                                <th class="col-md-4">Component
                                </th>
                                <th class="col-md-4">Production Server <asp:Label ID="lblPRIPAddress" runat="server" Text=""></asp:Label>
                                </th>
                                <th>DR Server  <asp:Label ID="lblDRIPAddress" runat="server" Text=""></asp:Label>
                                </th>
                            </tr>
                      
                        </thead>
                        
                        <tbody>
                            <tr>
                                <td>Database Version
                                </td>
                                <td class="text-indent">
                                    <asp:Label id="lblPRDatabaseVersionIcon" class="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblPRDatabaseVersion" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label class="icon-NA" runat="server" id="lblDRDatabaseVersionIcon"></asp:Label>
                                    <asp:Label ID="lblDRDatabaseVersion" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Service Status
                                </td>
                                <td class="text-indent">
                                    <asp:label class="icon-NA" id="lblprdatabaseServiceStatusIcon" runat="server"></asp:label>
                                    <asp:Label ID="lblprdatabaseServiceStatus" runat="server" CssClass="text-success"
                                        Text="N/A"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label class="icon-NA" id="lbldrdatabaseServiceStatusIcon" runat="server"></asp:Label>
                                    <asp:Label ID="lbldrdatabaseServiceStatus" runat="server" CssClass="text-success"
                                        Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Cluster State
                                </td>
                                <td class="text-indent">
                                    <asp:Label class="icon-NA" id="lblprDBClusterStateIcon" runat="server"></asp:Label>
                                    <asp:Label ID="lblprDBClusterState" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label class="icon-NA" id="lbldrDBClusterStateIcon" runat="server"></asp:Label>&nbsp;
                                    <asp:Label ID="lbldrDBClusterState" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            
                        </tbody>
                       
                    </table>
                   
                </div>
            </div>
        </div>
        <div class="widget" data-toggle="collapse-widget" data-collapse-closed="true">
            <div class="widget-head" id="monitor">
                               <h4 class="heading">Replication Monitoring
                    <asp:Label ID="lblAppName" runat="server" Text=""></asp:Label>
                </h4>
            </div>
            <div class="widget-body">


                <div id="monitor-content">
                    <table id="tblPostgreReplication" class="table table-striped table-bordered table-condensed margin-bottom-none" width="100%">
                        <thead>
                            <tr>
                                <th>Replication Monitor
                                </th>
                                <th>Production Server
                                </th>
                                <th>DR Server
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="col-md-4">Replication Status
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnPRReplicationStatus" runat="server" CssClass="icon-NA"></asp:Label>
                                    <asp:Label ID="lblPRReplicationStatus" runat="server" CssClass="text-success" Text="N/A"></asp:Label>
                                </td>
                                <td>
                                    <asp:Label ID="spnDRReplicationStatus" runat="server" CssClass="icon-NA"></asp:Label>&nbsp;
                                    <asp:Label ID="lblDRReplicationStatus" runat="server" CssClass="text-success" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>Database Recovery Status
                                </td>
                                <td>
                                    <asp:Label ID="spnPRDatabaseRecoveryStatus" runat="server" CssClass="icon-NA"></asp:Label>&nbsp;
                                    <asp:Label ID="lblPRDatabaseRecoveryStatus" runat="server" CssClass="inactive" Text="N/A"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnDRDatabaseRecoveryStatus" runat="server" CssClass="icon-NA"></asp:Label>
                                    <asp:Label ID="lblDRDatabaseRecoveryStatus" runat="server" CssClass="active" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                          
                            <tr>
                                <td>Data Directory path
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnPRDatabaseRecoverypath" runat="server" CssClass="icon-NA"></asp:Label>
                                    <asp:Label ID="lblPRDatabaseRecoverypath" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnDRDatabaseRecoverypath" runat="server" CssClass="icon-NA"></asp:Label>
                                    <asp:Label ID="lblDRDatabaseRecoverypath" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr id="reCurrentLogFile" runat="server" visible="true">
                                <td><%--Current xlog file name--%><asp:Label ID="lblCurrentLogFileNm" runat="server" Text=""></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="lblCurrentxlogFileNameIcon" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblCurrentXlogFileName" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>--
                                </td>
                            </tr>
                            <tr>
                                <td><%--Current xlog location--%><asp:Label ID="lblCurrentLogLocationnm" runat="server" Text=""></asp:Label>
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnCurrentxloglocation" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblCurrentxloglocation" runat="server" Text="N/A"></asp:Label>
                                </td>
                                <td>--
                                </td>
                            </tr>
                             <tr id="trLogRecieverFile" runat="server" visible="true">
                                <td><%--Xlog receive file name--%><asp:Label ID="lblLogReceiverFileNm" runat="server" Text=""></asp:Label>
                                </td>
                                <td>--
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="lblXlogreceiveFileNameIcon" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblXlogreceiveFileName" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td><%--Last xlog receive location--%><asp:Label ID="lblLastLogReceiverLoc" runat="server" Text=""></asp:Label>
                                </td>
                                <td>--
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnLastxlogreceivelocation" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblLastxlogreceivelocation" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                             <tr id="trLogReplayFileNm" runat="server" visible="true">
                                <td><%--Xlog replay file name--%><asp:Label ID="lblLogReplayFileNm" runat="server" Text=""></asp:Label>
                                </td>
                                <td>--
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="lblXlogreplayfilenameIcon" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblXlogreplayfilename" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td><%--Last xlog replay location--%><asp:Label ID="lblLastLogReplayLoc" runat="server" Text=""></asp:Label>
                                </td>
                                <td>--
                                </td>
                                <td class="text-indent">
                                    <asp:Label ID="spnLastxlogreplaylocation" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblLastxlogreplaylocation" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>DataLag (in MB)
                                </td>
                                <td class="text-indent" colspan="2">
                                    <asp:Label ID="spnPostgres9xDataLagMB" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblPostgres9xDataLagMB" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                            <tr>
                                <td>DataLag (hh:mm:ss)
                                </td>
                                <td class="text-indent" colspan="2">
                                    <asp:Label ID="spnPostgres9xDataLagHR" CssClass="icon-NA" runat="server"></asp:Label>
                                    <asp:Label ID="lblPostgres9xDataLagHR" runat="server" Text="N/A"></asp:Label>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</asp:Content>