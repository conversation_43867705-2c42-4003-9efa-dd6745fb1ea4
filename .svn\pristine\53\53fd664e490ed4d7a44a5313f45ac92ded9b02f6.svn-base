﻿using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace CP.UI.Controls
{
    public partial class DataBaseSybaseWithRsHadrList : BaseControl  //System.Web.UI.UserControl
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        public static string CurrentURL = Constants.UrlConstants.Urls.Component.DatabaseConfiguration;

        public bool IsSearch
        {
            get { return string.IsNullOrEmpty(txtsearchvalue.Text); }
        }

        public string ReturnUrl
        {
            get
            {
                if (LoggedInUserRole == UserRole.Administrator || LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser)
                {
                    return Constants.UrlConstants.Urls.Component.DatabaseList;
                }
                return string.Empty;
            }
        }

        public override void PrepareView()
        {
            BindData();
        }

        //protected bool ValidateRequest(string entity, UserActionType _UserActionType)
        //{
        //    if ((Session["_token"] != null))
        //    {
        //        // ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Token Key validating Request", LoggedInUserId);
        //        if (!UrlHelper.IsTokenValidated(Convert.ToString(Session["_token"])) || Request.HttpMethod != "POST")
        //        {
        //            ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking event catch", LoggedInUserId);
        //            Logout _Logout = new Logout();
        //            _Logout.PrepareView();
        //            return false;
        //        }
        //    }
        //    else
        //    {
        //        ActivityLogger.AddLog(LoggedInUserName, entity, _UserActionType, "Request click jacking token value blank", LoggedInUserId);
        //        Logout _Logout = new Logout();
        //        _Logout.PrepareView();
        //        return false;
        //    }
        //    return true;
        //}

        private void BindData()
        {
            lvSybasedatabase.DataSource = GetSybaseDbList();
            lvSybasedatabase.DataBind();
           // setListViewPage();
        }

        private IList<DatabaseBase> GetSybaseDbList()
        {
            return Facade.GetDatabaseBasesByTypeRoleAndCompanyId(DatabaseType.SyBaseWithRsHADR, IsUserSuperAdmin, LoggedInUserCompanyId, IsParentCompnay);
        }

        //private void setListViewPage()
        //{
        //    if ((Convert.ToInt32(Session["CurrentPageSybaseSRSList"]) != -1) && Session["CurrentPageSybaseSRSList"] != null)
        //    {
        //        if (Convert.ToInt32(Session["CurrentPageSybaseSRSList"]) == DataPager1.TotalRowCount)
        //        {
        //            Session["CurrentPageSybaseSRSList"] = Convert.ToInt32(Session["CurrentPageSybaseSRSList"]) - DataPager1.MaximumRows;
        //        }
        //        DataPager1.SetPageProperties(Convert.ToInt32(Session["CurrentPageSybaseSRSList"]), DataPager1.MaximumRows, true);
        //        Session["CurrentPageSybaseSRSList"] = -1;
        //    }
        //}

        public void PopulateListView()
        {
            if (!string.IsNullOrEmpty(txtsearchvalue.Text))
            {
                lvSybasedatabase.Items.Clear();
                lvSybasedatabase.DataSource = GetSybaseDbList(txtsearchvalue.Text);
                lvSybasedatabase.DataBind();
            }
        }

        public IList<DatabaseBase> GetSybaseDbList(string searchvalue)
        {
            var Databaselist = GetSybaseDbList();
            searchvalue = searchvalue.Trim();
            if (!String.IsNullOrEmpty(searchvalue) && Databaselist != null && Databaselist.Count > 0)
            {
                var result = (from Database in Databaselist
                              where Database.Name.ToLower().Contains(searchvalue.ToLower())
                              select Database).ToList();

                return result;
            }
            return null;
        }

        protected void btnSearch_Click(object sender, EventArgs e)
        {
            PopulateListView();
        }

        protected void lvSybasedatabase_ItemEditing(object sender, ListViewEditEventArgs e)
        {
            {
                Session["CurrentPageSybaseRSHADRList"] = (DataPager1.StartRowIndex);
                var secureUrl = new SecureUrl(CurrentURL);
                var lblDatabaseId = (lvSybasedatabase.Items[e.NewEditIndex].FindControl("ID")) as Label;
                if (lblDatabaseId != null) //&& ValidateRequest("SybaseWithSrs Edit", UserActionType.DatabaseList)
                {
                    //secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.DatabaseId, lblDatabaseId.Text,
                    //Constants.UrlConstants.Params.DatabaseType, DatabaseType.SyBaseWithSrs.ToString());

                    secureUrl = UrlHelper.BuildSecureUrl(CurrentURL, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");
                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseId, lblDatabaseId.Text);
                    WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseType, DatabaseType.SyBaseWithRsHADR.ToString());
                    Helper.Url.Redirect(secureUrl);


                }
                if (secureUrl != null)
                {
                    Helper.Url.Redirect(secureUrl);
                }
            }
        }

        protected void lvSybasedatabase_PreRender(object sender, EventArgs e)
        {
            if (!IsSearch)
            {
                PopulateListView();
            }
            else
            {
                BindData();
            }
        }

        protected void lvSybasedatabase_ItemDeleting(object sender, ListViewDeleteEventArgs e)
        {
            try
            {
                Session["CurrentPageSybaseRSHADRList"] = (DataPager1.StartRowIndex);
                var lblDatabaseId = (lvSybasedatabase.Items[e.ItemIndex].FindControl("ID")) as Label;
                var lblName = (lvSybasedatabase.Items[e.ItemIndex].FindControl("lblName")) as Label;

                if (lblDatabaseId != null && lblName != null) //&& ValidateRequest("SybaseWithSrs Delete", UserActionType.DatabaseList)
                {
                    var workflowDetailsByDatabaseId =
                        Facade.GetAllWorkflowActionsByDatabaseId(Convert.ToInt32(lblDatabaseId.Text));
                    if (workflowDetailsByDatabaseId != null)
                    {
                        ErrorSuccessNotifier.AddSuccessMessage("The SybaseWithRSHADR database component is in use");
                    }
                    else
                    {
                        Facade.DeleteDatabaseBaseById(Convert.ToInt32(lblDatabaseId.Text));

                        ActivityLogger.AddLog(HttpContext.Current.Session["loginUserName"].ToString(), "SybaseWithRSHADR", UserActionType.DeleteDatabaseComponent,
                                                "The SybaseWithRSHADR database component '" + lblName.Text +
                                                "' was deleted from the database component", LoggedInUserId);
                        ErrorSuccessNotifier.AddSuccessMessage(
                            Constants.ValidationConstants.SuccessMessage.GetSingleDataOperationMessage(
                                "SybaseWithRSHADR database Component" + " " + '"' + lblName.Text + '"', TransactionType.Delete));
                    }
                }
            }
            catch (CpException ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                ExceptionManager.Manage(ex, Page);
            }
            catch (Exception ex)
            {
                InvalidateTransaction();

                ErrorSuccessNotifier.AddErrorMessage(ex.Message);

                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    ExceptionManager.Manage((CpException)ex.InnerException, Page);
                }
                else
                {
                    var customEx = new CpException(CpExceptionType.CommonUnhandled, "Unhandled exception occured while deleting data", ex);

                    ExceptionManager.Manage(customEx, Page);
                }
            }

            if (ReturnUrl.IsNotNullOrEmpty())
            {
                //var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.DatabaseType, DatabaseType.SyBaseWithSrs.ToString());

                //Helper.Url.Redirect(secureUrl);

                var secureUrl = UrlHelper.BuildSecureUrl(ReturnUrl, string.Empty, Constants.UrlConstants.Params.Edit, "Edit");

                WebHelper.CurrentSession.Set(Constants.UrlConstants.Params.DatabaseType, DatabaseType.SyBaseWithRsHADR.ToString());
                Helper.Url.Redirect(secureUrl);
            }
        }

        protected void lvSybasedatabase_ItemDataBound(object sender, ListViewItemEventArgs e)
        {
            var edit = e.Item.FindControl("ibtnEdit") as ImageButton;
            var delete = e.Item.FindControl("ibtnDelete") as ImageButton;

            if (IsUserOperator || IsUserExecutionUser)
            {
                if (edit != null)
                {
                    edit.Enabled = false;
                    edit.ImageUrl = "../images/icons/pencil_disable.png";
                }
                if (delete != null)
                {
                    delete.Enabled = false;
                    delete.ImageUrl = "../images/icons/cross-circle_disable.png";
                }
            }
        }
    }
}