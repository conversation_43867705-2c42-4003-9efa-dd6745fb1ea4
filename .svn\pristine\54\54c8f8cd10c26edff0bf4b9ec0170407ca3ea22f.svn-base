﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="ConfigFourEyeApprovers.aspx.cs" Inherits="CP.UI.ConfigFourEyeApprovers" %>

<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<%--<%@ Register TagPrefix="TK1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>--%>

<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">
    <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/jquery-ui.css" rel="stylesheet">
    <link href="../App_Themes/CPTheme/Custom-chkbox-rdbtn.css" rel="stylesheet">
    <link href="../App_Themes/CPTheme/jquery.mCustomScrollbar.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/radstyle.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/jquery-ui.js" type="text/javascript"></script>
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <script src="../Script/AlertModal.js"></script>
    <link href="../App_Themes/CPTheme/bootstrap.min.css" rel="stylesheet" />
    <link href="../App_Themes/CPTheme/CPMaster.less" rel="stylesheet" />


    <style type="text/css">
        .RadTreeView_Outlook .rtChecked, .RadTreeView_Outlook .rtUnchecked, .RadTreeView_Outlook .rtIndeterminate {
            background-image: url(../Images/custom-chkbox-rdbtn-small-new.png) !important;
        }

        .RadTreeView .rtUnchecked {
            background-position: -15px -12px !important;
        }

        .RadTreeView .rtChecked {
            background-position: 1px 0px !important;
        }

        .RadTreeView .rtIndeterminate {
            background-position: -15px 0px !important;
        }

        .RadTreeView_Outlook {
            font: normal 13px/16px Segoe UI !important;
        }

        .combobox {
            display: block !important;
        }

        .chosen-select + .chosen-container {
            width: 95% !important;
            opacity: 1 !important;
        }

        dropup .chosen-container .chosen-drop {
            top: auto !important;
            bottom: 30px !important;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="cphBody" runat="server">
    <input type="hidden" id="hdfStaticGuid" runat="server" />
    <asp:HiddenField ID="hdtokenKey" runat="server" />

    <div class="innerLR">
        <%--<asp:HiddenField ID="hdUserType" runat="server" />--%>
        <asp:UpdatePanel ID="up_approversdetails" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <h3>
                    <img src="../Images/icons/approver-24.png">
                    Approvers for Workflow Creation/Deletion</h3>
                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row" style="margin: 0px !important;">
                            <div class="col-md-6" style="padding-left: 0px !important;">

                                <div id="approvers" runat="server">

                                    <div class="col-md-12" style="padding-left: 0px; font-weight: 900; padding-bottom: 10px;">
                                        <asp:Label ID="Label2" runat="server" Style="margin-bottom: 10px !important;" Text="Select Approvers"></asp:Label>
                                    </div>
                                    <div class="notifycheckscroll" style="padding: 5px; border: 1px solid #ccc; width: 100%; max-height: 500px; overflow: scroll; margin-bottom: 25px !important;">

                                        <asp:UpdatePanel ID="UpdatePanel3" runat="server" UpdateMode="Conditional">
                                            <ContentTemplate>
                                                <telerik:RadTreeView ClientIDMode="Static" RenderMode="Lightweight" CausesValidation="false" ID="RadTreeView1" runat="server" CheckBoxes="True"
                                                    Skin="Outlook" TriStateCheckBoxes="true" CheckChildNodes="true" IsOptionElementsEnabled="True" OnNodeClick="RadTreeView1_NodeClick">
                                                </telerik:RadTreeView>


                                            </ContentTemplate>
                                        </asp:UpdatePanel>

                                    </div>

                                </div>
                                <div>
                                    <div>
                                        <asp:Label ID="Label3" runat="server" Style="margin-bottom: 10px !important;" Text="Select Type :"></asp:Label>
                                    </div>
                                    <asp:RadioButtonList runat="server" ID="rdo_Workflowpf" RepeatDirection="Horizontal" Style="margin-top: 10px; !important">
                                        <asp:ListItem Text="Profile" Value="Profile"></asp:ListItem>
                                        <asp:ListItem Text="Workflow" Value="Workflow"></asp:ListItem>
                                    </asp:RadioButtonList>

                                    <asp:RequiredFieldValidator ID="rfvOptions" runat="server" ControlToValidate="rdo_Workflowpf"
                                        ErrorMessage="Please select an option" ValidationGroup="ValidationGroup1"></asp:RequiredFieldValidator>
                                </div>
                                <%--<hr />--%>
                                <div class="modal-footer" style="padding: 20px 0px 20px 0px !important; text-align: left !important;">
                                    <div class="col-md-6" style="margin: 0px; padding-left: 0px !important;">
                                        <asp:Button ID="saveapprovers" TabIndex="9" runat="server" Text="Save" CssClass="btn btn-primary btn_save"
                                            ValidationGroup="ValidationGroup1" OnClick="saveapprovers_Click" />
                                        <asp:Button ID="btnBack" runat="server" TabIndk2ex="7" Text="Cancel" CssClass="btn btn-primary btn_back"
                                            CausesValidation="false" OnClick="btnBack_Click" />

                                    </div>
                                </div>


                            </div>
                            <div class="col-md-12" style="padding-left: 0px; font-weight: 900; padding-bottom: 10px;">
                                <asp:Label ID="Label4" runat="server" Style="margin-bottom: 10px !important;" Text="Approver List"></asp:Label>
                                
                            </div>
                            <asp:Label ID="Label5" ForeColor="Red" runat="server" Style="margin-bottom: 10px !important; font-weight: 900;" Text="No Records Found" Visible="false"></asp:Label>

                            <div class="col-md-6" style="padding-left: 0px !important;">


                                <asp:ListView ID="grplst" runat="server" OnItemEditing="grplst_ItemEditing" OnItemDeleting="grplst_ItemDeleting">
                                    <LayoutTemplate>
                                        <table class="dynamicTable tableTools table table-striped table-bordered table-condensed table-white" style="table-layout: fixed">
                                            <thead>
                                                <tr>
                                                    <th style="width: 2%;" class="text-center">
                                                        <span>
                                                            <img src="../Images/icons/server1.png" /></span>
                                                    </th>
                                                    <th style="width: 5%;">Name
                                                    </th>
                                                    <th style="width: 20%;">Validators
                                                    </th>

                                                    <th style="width: 4%; text-align: center;">Action
                                                    </th>

                                                </tr>
                                            </thead>
                                            <tbody>
                                                <asp:PlaceHolder runat="server" ID="itemPlaceholder" />
                                            </tbody>
                                        </table>
                                    </LayoutTemplate>
                                    <EmptyDataTemplate>
                                        <div class="message warning align-center bold no-bottom-margin">
                                            <asp:Label ID="lblError" Text="No Record Found" runat="server" Visible="true"></asp:Label>
                                        </div>
                                    </EmptyDataTemplate>
                                    <ItemTemplate>
                                        <tr>
                                            <td class="text-center" style="width: 2%;">
                                                <%#Container.DataItemIndex+1 %>
                                            </td>
                                            <asp:Label ID="ID" runat="server" Text='<%# Eval("Id") %>' Visible="false" />
                                            <td style="width: 5%;" class="tdword-wrap">
                                                <asp:Label ID="Label1" runat="server" Text='<%#Eval("PROFILE_OR_WORKFLOW") %>' />
                                            </td>
                                            <td style="width: 20%; word-wrap: break-word !important;">
                                                <asp:Label ID="name" runat="server" Text='<%#Eval("Approver") %>' />
                                            </td>
                                            <td style="width: 4%;" class="text-center">
                                                <asp:ImageButton ID="btnEdit" runat="server" CommandName="Edit" AlternateText="Edit"
                                                    ToolTip="Edit" ImageUrl="../images/icons/pencil.png" CausesValidation="False" />
                                                <asp:ImageButton ID="btnDelete" runat="server" OnClientClick=' return Message()' CommandName="Delete" AlternateText="Delete" CausesValidation="False"
                                                    ToolTip="Delete" ImageUrl="../images/icons/cross-circle.png" />
                                            </td>
                                        </tr>
                                    </ItemTemplate>
                                </asp:ListView>
                            </div>
                        </div>
                    </div>
            </ContentTemplate>
        </asp:UpdatePanel>
    </div>


    <%-- <script type="text/javascript">
         $(function () {
             $("#CblstGroup input[type=checkbox]").on("click", function () {

                 if ($(this).val() == 0) {
                     if ($(this).is(":checked")) {

                         $("#CblstGroup input[type=checkbox]").attr("checked", true);
                         $("#CblstGroup input[type=checkbox]").next().children().children('span').hide();
                         $("#CblstGroup input[type=checkbox]").next().children().children('span.cb-icon-check').show();
                     }
                     else {
                         $("#CblstGroup input[type=checkbox]").attr("checked", false);
                         $("#CblstGroup input[type=checkbox]").next().children().children('span').hide();
                         $("#CblstGroup input[type=checkbox]").next().children().children('span.cb-icon-check-empty').show();
                     }
                 }
                 else {
                     if ($(this).is(":checked")) {
                         $(this).attr("checked", true);
                         $(this).next().children().children('span').hide();
                         $(this).next().children().children('span.cb-icon-check').show();
                     }
                     else {
                         $(this).attr("checked", false);
                         $(this).next().children().children('span').hide();
                         $(this).next().children().children('span.cb-icon-check-empty').show();
                     }
                     testcheck();
                 }
             });
         });

         function testcheck() {
             if ($('#CblstGroup input:checkbox:not("#CblstGroup_0")').length == $('#CblstGroup input:checkbox:not("#CblstGroup_0"):checked').length) {
                 $("#CblstGroup_0").attr("checked", true);
                 $("#CblstGroup_0").next().children().children('span').hide();
                 $("#CblstGroup_0").next().children().children('span.cb-icon-check').show();
             }
             else {
                 $("#CblstGroup_0").attr("checked", false);
                 $("#CblstGroup_0").next().children().children('span').hide();
                 $("#CblstGroup_0").next().children().children('span.cb-icon-check-empty').show();
             }
         }


         $(function () {
             $("#cblworkflow input[type=checkbox]").on("click", function () {

                 if ($(this).val() == 0) {
                     if ($(this).is(":checked")) {

                         $("#cblworkflow input[type=checkbox]").attr("checked", true);
                         $("#cblworkflow input[type=checkbox]").next().children().children('span').hide();
                         $("#cblworkflow input[type=checkbox]").next().children().children('span.cb-icon-check').show();
                     }
                     else {
                         $("#cblworkflow input[type=checkbox]").attr("checked", false);
                         $("#cblworkflow input[type=checkbox]").next().children().children('span').hide();
                         $("#cblworkflow input[type=checkbox]").next().children().children('span.cb-icon-check-empty').show();
                     }
                 }
                 else {
                     if ($(this).is(":checked")) {
                         $(this).attr("checked", true);
                         $(this).next().children().children('span').hide();
                         $(this).next().children().children('span.cb-icon-check').show();
                     }
                     else {
                         $(this).attr("checked", false);
                         $(this).next().children().children('span').hide();
                         $(this).next().children().children('span.cb-icon-check-empty').show();
                     }
                     testcheck();
                 }
             });
         });

         function testcheck() {
             if ($('#cblworkflow input:checkbox:not("#CblstGroup_0")').length == $('#cblworkflow input:checkbox:not("#cblworkflow"):checked').length) {
                 $("#cblworkflow_0").attr("checked", true);
                 $("#cblworkflow_0").next().children().children('span').hide();
                 $("#cblworkflow_0").next().children().children('span.cb-icon-check').show();
             }
             else {
                 $("#cblworkflow_0").attr("checked", false);
                 $("#cblworkflow_0").next().children().children('span').hide();
                 $("#cblworkflow_0").next().children().children('span.cb-icon-check-empty').show();
             }
         }


         function handleChange(src) {

             if (src.value == "Execution") {
                 $('#ctl00_cphBody_divworkflow').hide();
             }
             else {
                 $('#ctl00_cphBody_divworkflow').show();
             }
         }
    </script>--%>

    <%--  <script type="text/javascript">
         function SearchEmployees(txtSearch, CblstGroup) {
             if ($(txtSearch).val() != "") {
                 var count = 0;
                 $(CblstGroup).children('tbody').children('tr').each(function () {
                     var match = false;
                     $(this).children('td').children('label').each(function () {
                         if ($(this).text().toUpperCase().indexOf($(txtSearch).val().toUpperCase()) > -1)
                             match = true;
                     });
                     if (match) {
                         $(this).show();
                         count++;
                     }
                     else { $(this).hide(); }
                 });
                 $('#spnCount').html((count) + ' match');
             }
             else {
                 $(CblstGroup).children('tbody').children('tr').each(function () {
                     $(this).show();
                 });
                 $('#spnCount').html('');
             }
         }

         function SearchWorkflow(txtworkflow, cblworkflow) {
             if ($(txtworkflow).val() != "") {
                 var count = 0;
                 $(cblworkflow).children('tbody').children('tr').each(function () {
                     var match = false;
                     $(this).children('td').children('label').each(function () {
                         if ($(this).text().toUpperCase().indexOf($(txtworkflow).val().toUpperCase()) > -1)
                             match = true;
                     });
                     if (match) {
                         $(this).show();
                         count++;
                     }
                     else { $(this).hide(); }
                 });
                 $('#spnCount').html((count) + ' match');
             }
             else {
                 $(cblworkflow).children('tbody').children('tr').each(function () {
                     $(this).show();
                 });
                 $('#spnCount').html('');
             }
         }

    </script>--%>

    <script>
        function SearchInfra(txtSearch, chkapprovers) {
            if ($(txtSearch).val() != "") {
                var count = 0;
                $(chkapprovers).children('tbody').children('tr').each(function () {
                    var match = false;
                    $(this).children('td').children('label').each(function () {
                        if ($(this).text().toUpperCase().indexOf($(txtSearch).val().toUpperCase()) > -1)
                            match = true;
                    });
                    if (match) {
                        $(this).show();
                        count++;
                    }
                    else { $(this).hide(); }
                });
            }
            else {
                $(chkapprovers).children('tbody').children('tr').each(function () {
                    $(this).show();
                });
                $('#spnCount').html('');
            }
        }



        function Message() {

            var msg = "Are you sure to delete approvers for workflow creation..?";
            return confirm(msg);
        }
    </script>

</asp:Content>
