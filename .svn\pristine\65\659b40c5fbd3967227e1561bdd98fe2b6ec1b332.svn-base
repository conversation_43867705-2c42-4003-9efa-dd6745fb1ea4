﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using System.Configuration;

namespace CP.DataAccess
{
    internal sealed class ParallelGroupWorkflowDataAccess : BaseDataAccess, IParallelGroupWorkflowDataAccess
    {
        #region Constructors

        public ParallelGroupWorkflowDataAccess(Context context)
            : base(context)
        {
        }

        protected override IEntityBuilder<ParallelGroupWorkflow> CreateEntityBuilder<ParallelGroupWorkflow>()
        {
            return (new ParallelGroupWorkflowBuilder()) as IEntityBuilder<ParallelGroupWorkflow>;
        }

        #endregion Constructors

        #region Methods

        /// <summary>
        ///     Create <see cref="ParallelGroupWorkflow" />into bcms_parallel_groupworkflow table.
        /// </summary>
        /// <param name="paralleldroperation">paralleldroperation</param>
        /// <returns>ParallelGroupWorkflow</returns>
        /// <author><PERSON><PERSON><PERSON></author>
        ParallelGroupWorkflow IParallelGroupWorkflowDataAccess.Add(ParallelGroupWorkflow paralleldroperation)
        {
            try
            {
                //paralleldroperation.CreatorId = 1;
                const string sp = "ParallelGroupWorkflow_Create";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, paralleldroperation.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectName", DbType.AnsiString, paralleldroperation.InfraObjectName);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowId", DbType.Int32, paralleldroperation.WorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowName", DbType.AnsiString, paralleldroperation.WorkflowName);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionId", DbType.Int32, paralleldroperation.CurrentActionId);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionName", DbType.AnsiString,
                        paralleldroperation.CurrentActionName);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, paralleldroperation.Status);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, paralleldroperation.Message);
                    Database.AddInParameter(cmd, Dbstring + "iParallelDROperationId", DbType.Int32,
                        paralleldroperation.ParallelDROperationId);
                    Database.AddInParameter(cmd, Dbstring + "iConditionalOperation", DbType.Int32,
                        paralleldroperation.ConditionalOperation);
                    Database.AddInParameter(cmd, Dbstring + "iProgressStatus", DbType.AnsiString,
                        paralleldroperation.ProgressStatus);
                    Database.AddInParameter(cmd, Dbstring + "iCreatorId", DbType.Int32, paralleldroperation.CreatorId);
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.String, paralleldroperation.JobName);
                    
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        paralleldroperation = reader.Read()
                            ? CreateEntityBuilder<ParallelGroupWorkflow>().BuildEntity(reader, paralleldroperation)
                            : null;
                    }

                    if (paralleldroperation == null)
                    {
                        int returnCode = GetReturnCodeFromParameter(cmd);

                        switch (returnCode)
                        {
                            case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                                {
                                    throw new ArgumentException(
                                        "GlobalMirror already exists. Please specify another globalMirror.");
                                }
                            default:
                                {
                                    throw new SystemException(
                                        "An unexpected error has occurred while creating this globalMirror.");
                                }
                        }
                    }

                    return paralleldroperation;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessInsertOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Inserting ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Update <see cref="ParallelGroupWorkflow" />into bcms_parallel_groupworkflow table.
        /// </summary>
        /// <param name="parallelDrOperation">parallelDrOperation</param>
        /// <returns>ParallelGroupWorkflow</returns>
        /// <author>Shivraj Mujumale</author>
        ParallelGroupWorkflow IParallelGroupWorkflowDataAccess.Update(ParallelGroupWorkflow parallelDrOperation)
        {
            try
            {
                //parallelDrOperation.CreatorId = 1;
                const string sp = "ParallelGroupWorkflow_Update";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, parallelDrOperation.Id);
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, parallelDrOperation.InfraObjectId);
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectName", DbType.AnsiString, parallelDrOperation.InfraObjectName);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowId", DbType.Int32, parallelDrOperation.WorkflowId);
                    Database.AddInParameter(cmd, Dbstring + "iWorkflowName", DbType.AnsiString, parallelDrOperation.WorkflowName);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionId", DbType.Int32, parallelDrOperation.CurrentActionId);
                    Database.AddInParameter(cmd, Dbstring + "iCurrentActionName", DbType.AnsiString,
                        parallelDrOperation.CurrentActionName);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, parallelDrOperation.Status);
                    Database.AddInParameter(cmd, Dbstring + "iMessage", DbType.AnsiString, parallelDrOperation.Message);
                    Database.AddInParameter(cmd, Dbstring + "iParallelDROperationId", DbType.Int32,
                        parallelDrOperation.ParallelDROperationId);
                    Database.AddInParameter(cmd, Dbstring + "iUpdatorId", DbType.Int32, parallelDrOperation.UpdatorId);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        parallelDrOperation = reader != null && reader.Read()
                            ? CreateEntityBuilder<ParallelGroupWorkflow>().BuildEntity(reader,
                                parallelDrOperation)
                            : null;
                    }

                    if (parallelDrOperation == null)
                    {
                        //int returnCode = GetReturnCodeFromParameter(cmd);

                        //switch (returnCode)
                        //{
                        //    case Constants.MySqlConstants.DBStatusCodeErrorDuplicateData:
                        //        {
                        //            throw new ArgumentException(
                        //                "GlobalMirror already exists. Please specify another globalMirror.");
                        //        }
                        //    default:
                        //        {
                        //            throw new SystemException(
                        //                "An unexpected error has occurred while creating this globalMirror.");
                        //        }
                        //}
                    }

                    return parallelDrOperation;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table Get by name.
        /// </summary>
        /// <param name="name">name</param>
        /// <returns>ParallelGroupWorkflow</returns>
        /// <author>Shivraj Mujumale</author>
        ParallelGroupWorkflow IParallelGroupWorkflowDataAccess.GetByName(string name)
        {
            try
            {
                if (name == string.Empty)
                {
                    throw new ArgumentNullException("name");
                }

                const string sp = "ParallelGroupWorkflow_GetByName";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iType", DbType.AnsiString, name);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return reader.Read()
                            ? (CreateEntityBuilder<ParallelGroupWorkflow>()).BuildEntity(reader,
                                new ParallelGroupWorkflow())
                            : null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetByName(" + name +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>ParallelGroupWorkflow</returns>
        /// <author>Shivraj Mujumale</author>
        ParallelGroupWorkflow IParallelGroupWorkflowDataAccess.GetById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }
                const string sp = "ParallelGroupWorkflow_GetById";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelGroupWorkflow>()).BuildEntity(reader,
                                new ParallelGroupWorkflow());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetById(" + id +
                    ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id">id</param>
        /// <returns>ParallelGroupWorkflow</returns>
        /// <author>Shivraj Mujumale</author>
        ParallelGroupWorkflow IParallelGroupWorkflowDataAccess.GetByInfraobjectId(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ParallelGroupWorkflow_GetByGroupId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iInfraobjectId", DbType.Int32, id);

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        if (reader.Read())
                        {
                            return (CreateEntityBuilder<ParallelGroupWorkflow>()).BuildEntity(reader,
                                new ParallelGroupWorkflow());
                        }
                        return null;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetByGroupId(" +
                    id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table by ParallelDROperationId.
        /// </summary>
        /// <param name="pid">pid</param>
        /// <returns>ParallelGroupWorkflow List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<ParallelGroupWorkflow> IParallelGroupWorkflowDataAccess.GetByParallelDROperationId(int pid)
        {
            try
            {
                if (pid < 1)
                {
                    throw new ArgumentNullException("pid");
                }

                const string sp = "PARALLELGROUPWF_GETBYOPERATID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iParallelDROperationId", DbType.Int32, pid);

#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelGroupWorkflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetByParallelDROperationId(" +
                    pid + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Get <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table.
        /// </summary>
        /// <returns>ParallelGroupWorkflow List</returns>
        /// <author>Shivraj Mujumale</author>
        IList<ParallelGroupWorkflow> IParallelGroupWorkflowDataAccess.GetAll()
        {
            try
            {
                const string sp = "ParallelGroupWorkflow_GetAll";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelGroupWorkflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAll()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Delete <see cref="ParallelGroupWorkflow" />from bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id"> Pass id</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool IParallelGroupWorkflowDataAccess.DeleteById(int id)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "PARALLELGROUPWF_DELETEBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessDeleteOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.DeleteById()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     update <see cref="ParallelGroupWorkflow" />into bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id">Pass id</param>
        /// <param name="conditoinalAction">Pass conditoinalAction</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool IParallelGroupWorkflowDataAccess.UpdateConditionalOperation(int id, int conditoinalAction)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "ParallelGpWkflo_UpdtByCndiOp";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iOperationId", DbType.Int32, conditoinalAction);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    //return returnCode > 0;
#if (ORACLE || MSSQL)
                    return returnCode < 0;
#endif

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        /// <summary>
        ///     Update <see cref="ParallelGroupWorkflow" />into bcms_parallel_groupworkflow table by id.
        /// </summary>
        /// <param name="id">Pass id</param>
        /// <param name="status">Pass status</param>
        /// <returns>bool</returns>
        /// <author>Shivraj Mujumale</author>
        bool IParallelGroupWorkflowDataAccess.UpdateStatusById(int id, string status)
        {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "PARALLELGPWF_UPDATEBYSTATUBYID";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iStatus", DbType.AnsiString, status);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;

                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        //WF Pause Resume
        bool IParallelGroupWorkflowDataAccess.UpdateResumeStatusGrpId(int id, int resumeStatus, int reExecuteStatus)
        {
            try
            {

                const string sp = "PGrpWF_UpdatePRStatus";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    //Database.AddInParameter(cmd, Dbstring + "iresumeStatus", DbType.AnsiString, resumeStatus);
                    //Database.AddInParameter(cmd, Dbstring + "ireExecuteStatus", DbType.AnsiString, reExecuteStatus);

                    Database.AddInParameter(cmd, Dbstring + "iIsReExecute", DbType.AnsiString, reExecuteStatus);
                    Database.AddInParameter(cmd, Dbstring + "iIsPause", DbType.AnsiString, resumeStatus);                   

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    //#if (ORACLE || MSSQL)
                    //                    return returnCode > 0;
                    //#endif
#if (MSSQL)
                    return returnCode == -1;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                   "Error In DAL While Updating ParallelGroupWorkflow UpdateResumeStatusGrpId Entry : " + Environment.NewLine +
                   "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }



        public bool UpdateResumeStatusGrpIdWithJobInc(int id, int resumeStatus, int reExecuteStatus, string JobName)
        {
            try
            {

                const string sp = "PGrpWF_UpdatePRStatusWithJobInc";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    //Database.AddInParameter(cmd, Dbstring + "iresumeStatus", DbType.AnsiString, resumeStatus);
                    //Database.AddInParameter(cmd, Dbstring + "ireExecuteStatus", DbType.AnsiString, reExecuteStatus);

                    Database.AddInParameter(cmd, Dbstring + "iIsReExecute", DbType.AnsiString, reExecuteStatus);
                    Database.AddInParameter(cmd, Dbstring + "iIsPause", DbType.AnsiString, resumeStatus);
                    Database.AddInParameter(cmd, Dbstring + "iJobName", DbType.AnsiString, JobName);

#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

                    //#if (ORACLE || MSSQL)
                    //                    return returnCode > 0;
                    //#endif
#if (MSSQL)
                    return returnCode == -1;
#endif
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                   "Error In DAL While Updating ParallelGroupWorkflow UpdateResumeStatusGrpId Entry : " + Environment.NewLine +
                   "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IParallelGroupWorkflowDataAccess.UpdateResumeByDRId(int id)
        {
            try
            {
                const string sp = "PGrpWF_ResetPRByDROprId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "idrOpeId", DbType.Int32, id);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

#if (ORACLE || MSSQL)
                    return returnCode < 0;
#endif

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                   "Error In DAL While Updating ParallelGroupWorkflow UpdateResumeByDRId Entry : " + Environment.NewLine +
                   "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        bool IParallelGroupWorkflowDataAccess.UpdatePauseStatusGrpId(int id, int IsReExecute, int IsPause)
        {
            try
            {
                const string sp = "PARALLELGPWF_UpdatePauseStatus";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {

                    //  AddOutputParameter(cmd);
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iIsReExecute", DbType.AnsiString, IsReExecute);
                    Database.AddInParameter(cmd, Dbstring + "iIsPause", DbType.AnsiString, IsPause);
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    int returnCode = Database.ExecuteNonQuery(cmd);

#if (ORACLE || MSSQL)
                    return returnCode < 0;
#endif

                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                   "Error In DAL While Updating ParallelGroupWorkflow UpdatePauseStatusGrpId Entry : " + Environment.NewLine +
                   "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        ParallelGroupWorkflow IParallelGroupWorkflowDataAccess.ParallelGroupWorkflowGetByID(int id)
        {
            var parallelGroup = new ParallelGroupWorkflow();
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                using (DbCommand cmd = Database.GetStoredProcCommand("PARALLELGROUPWORKFLOW_GETBYID"))
                {
                    //cmd.CommandTimeout = 10000;
                    cmd.CommandTimeout = Convert.ToInt32(ConfigurationManager.AppSettings["ActionENV"].ToString()); 
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader myParallelWorkflowReader = Database.ExecuteReader(cmd))
                    {
                        if (myParallelWorkflowReader.Read())
                        {
                            parallelGroup.Id = Convert.IsDBNull(myParallelWorkflowReader["Id"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["Id"]);
                            parallelGroup.InfraObjectId = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["InfraObjectId"]);
                            parallelGroup.InfraObjectName = Convert.IsDBNull(myParallelWorkflowReader["InfraObjectName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["InfraObjectName"]);
                            parallelGroup.WorkflowId = Convert.IsDBNull(myParallelWorkflowReader["WorkflowId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["WorkflowId"]);
                            parallelGroup.WorkflowName = Convert.IsDBNull(myParallelWorkflowReader["WorkflowName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["WorkflowName"]);
                            parallelGroup.CurrentActionId = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CurrentActionId"]);
                            parallelGroup.CurrentActionName = Convert.IsDBNull(myParallelWorkflowReader["CurrentActionName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["CurrentActionName"]);
                            //parallelGroup.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Status"]);

                            parallelGroup.Status = Convert.IsDBNull(myParallelWorkflowReader["Status"])
                ? WorkflowActionStatus.Undefined
                : (WorkflowActionStatus)
                    Enum.Parse(typeof(WorkflowActionStatus), Convert.ToString(myParallelWorkflowReader["Status"]), true);

                            parallelGroup.Message = Convert.IsDBNull(myParallelWorkflowReader["Message"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["Message"]);
                            parallelGroup.ParallelDROperationId = Convert.IsDBNull(myParallelWorkflowReader["ParallelDROperationId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ParallelDROperationId"]);
                            parallelGroup.ConditionalOperation = Convert.IsDBNull(myParallelWorkflowReader["ConditionalOperation"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["ConditionalOperation"]);
                            parallelGroup.CreatorId = Convert.IsDBNull(myParallelWorkflowReader["CreatorId"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["CreatorId"]);
                            parallelGroup.IsResume = Convert.IsDBNull(myParallelWorkflowReader["IsResume"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsResume"]);
                            parallelGroup.IsReExecute = Convert.IsDBNull(myParallelWorkflowReader["IsReExecute"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsReExecute"]);
                            parallelGroup.IsPause = Convert.IsDBNull(myParallelWorkflowReader["IsPause"]) ? 0 : Convert.ToInt32(myParallelWorkflowReader["IsPause"]);
                            parallelGroup.JobName = Convert.IsDBNull(myParallelWorkflowReader["JobName"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["JobName"]);
                            parallelGroup.ProgressStatus = Convert.IsDBNull(myParallelWorkflowReader["ProgressStatus"]) ? string.Empty : Convert.ToString(myParallelWorkflowReader["ProgressStatus"]);

                        }
                        else
                        {
                            parallelGroup = null;
                        }
                    }

                    return parallelGroup;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.ParallelGroupWorkflowGetByID(" +
                    id + ")" + Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        IList<ParallelGroupWorkflow> IParallelGroupWorkflowDataAccess.GetWorkflowSummary()
        {
            IList<ParallelGroupWorkflow> objprl = new List<ParallelGroupWorkflow>();
            try
            {
                const string sp = "PARALLELWITHWFRESULT";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    cmd.CommandTimeout = Convert.ToInt32(ConfigurationManager.AppSettings["ActionENV"].ToString()); 
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                   
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            //return (CreateEntityBuilder<ParallelWorkflowActionResult>()).BuildEntity(reader, new ParallelWorkflowActionResult());
                            ParallelGroupWorkflow obj = new ParallelGroupWorkflow
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                InfraObjectId = Convert.IsDBNull(reader["InfraObjectId"]) ? 0 : Convert.ToInt32(reader["InfraObjectId"]),
                                WorkflowId = Convert.IsDBNull(reader["WorkflowId"]) ? 0 : Convert.ToInt32(reader["WorkflowId"]),
                                ActionsSuccess = Convert.IsDBNull(reader["ActionsSuccess"]) ? 0 : Convert.ToInt32(reader["ActionsSuccess"]),
                                ActionsSkip = Convert.IsDBNull(reader["ActionsSkip"]) ? 0 : Convert.ToInt32(reader["ActionsSkip"]),
                                ActionsError = Convert.IsDBNull(reader["ActionsError"]) ? 0 : Convert.ToInt32(reader["ActionsError"]),
                                ActionsRunning = Convert.IsDBNull(reader["ActionsRunning"]) ? 0 : Convert.ToInt32(reader["ActionsRunning"]),
                                ActionsAbort = Convert.IsDBNull(reader["ActionsAbort"]) ? 0 : Convert.ToInt32(reader["ActionsAbort"]),
                                BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"]) ? 0 : Convert.ToInt32(reader["BusinessServiceId"]),
                                EndTime = Convert.IsDBNull(reader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["EndTime"]),
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                   "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetWorkflowSummary()" +
                   Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        bool IParallelGroupWorkflowDataAccess.UpdateProgressbar(int id, string Progrssbarstatus)
         {
            try
            {
                if (id < 1)
                {
                    throw new ArgumentNullException("id");
                }

                const string sp = "PARALLELGPWF_UpdateProgresBar";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iId", DbType.Int32, id);
                    Database.AddInParameter(cmd, Dbstring + "iProgressBar", DbType.AnsiString, Progrssbarstatus);
                    int returnCode = Database.ExecuteNonQuery(cmd);
                    return returnCode > 0;
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessUpdateOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageInsertdata,
                    "Error In DAL While Updating ParallelGroupWorkflow Entry : " + Environment.NewLine +
                    "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<workflowactionenviorment> IParallelGroupWorkflowDataAccess.GetWorkflowSummaryactenv()
        {
            IList<workflowactionenviorment> objprl = new List<workflowactionenviorment>();
            try
            {
                const string sp = "PARALLELWFBYACTIONENV";
                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    //cmd.CommandTimeout = 10000;
                    cmd.CommandTimeout = Convert.ToInt32(ConfigurationManager.AppSettings["ActionENV"].ToString()); 
#if ORACLE
                    cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif
                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        while (reader.Read())
                        {
                            //return (CreateEntityBuilder<ParallelWorkflowActionResult>()).BuildEntity(reader, new ParallelWorkflowActionResult());
                            workflowactionenviorment obj = new workflowactionenviorment
                            {
                                Id = Convert.IsDBNull(reader["Id"]) ? 0 : Convert.ToInt32(reader["Id"]),
                                infraobjectid = Convert.IsDBNull(reader["infraobjectid"]) ? string.Empty : Convert.ToString(reader["infraobjectid"]),
                                workflowid = Convert.IsDBNull(reader["workflowid"]) ? 0 : Convert.ToInt32(reader["workflowid"]),
                                BusinessServiceId = Convert.IsDBNull(reader["BusinessServiceId"]) ? 0 : Convert.ToInt32(reader["BusinessServiceId"]),
                                ActionsSuccess = Convert.IsDBNull(reader["ActionsSuccess"]) ? 0 : Convert.ToInt32(reader["ActionsSuccess"]),
                                
                                ActionsError = Convert.IsDBNull(reader["ActionsError"]) ? 0 : Convert.ToInt32(reader["ActionsError"]),
                                ActionsSkip = Convert.IsDBNull(reader["ActionsSkip"]) ? 0 : Convert.ToInt32(reader["ActionsSkip"]),
                                ActionsRunning = Convert.IsDBNull(reader["ActionsRunning"]) ? 0 : Convert.ToInt32(reader["ActionsRunning"]),
                                ActionsAbort = Convert.IsDBNull(reader["ActionsAbort"]) ? 0 : Convert.ToInt32(reader["ActionsAbort"]),
                                
                                EndTime = Convert.IsDBNull(reader["EndTime"]) ? DateTime.MinValue : Convert.ToDateTime(reader["EndTime"]),
                                Actiontype = Convert.IsDBNull(reader["Actiontype"]) ? string.Empty : Convert.ToString(reader["Actiontype"]),
                                EnviornmentType = Convert.IsDBNull(reader["EnviornmentType"]) ? string.Empty : Convert.ToString(reader["EnviornmentType"]),
                        
                            };
                            objprl.Add(obj);
                        }
                        return objprl;
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                   ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                   "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetWorkflowSummary()" +
                   Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }



        IList<ParallelGroupWorkflow> IParallelGroupWorkflowDataAccess.GetAllRunning_WorkflowGroup()
        {
            try
            {
                const string sp = "ParallelGroupWorkflow_GetAllRunning";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelGroupWorkflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAllRunning_WorkflowGroup()" +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }

        IList<ParallelGroupWorkflow> IParallelGroupWorkflowDataAccess.GetAllRunning_WorkflowGroupByUserId(int UserId)
        {
            try
            {
                const string sp = "ParallelGroupWorkflow_GetAllRunningByUserId";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, UserId);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelGroupWorkflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAllRunning_WorkflowGroupByUserId() for user having ID : "+UserId +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        IList<ParallelGroupWorkflow> IParallelGroupWorkflowDataAccess.GetAllRunning_WorkflowGroupByUserId_New(int UserId, int LoggedInuser)
        {
            try
            {
                const string sp = "ParallelGroupWorkflow_GetAllRunningByUserId_New";

                using (DbCommand cmd = Database.GetStoredProcCommand(sp))
                {
                    Database.AddInParameter(cmd, Dbstring + "iUserId", DbType.Int32, UserId);
                    Database.AddInParameter(cmd, Dbstring + "iloggedinuser", DbType.Int32, LoggedInuser);
#if ORACLE
                cmd.Parameters.Add(BuildRefCursorParameter("cur"));
#endif

                    using (IDataReader reader = Database.ExecuteReader(cmd))
                    {
                        return CreateEntityBuilder<ParallelGroupWorkflow>().BuildEntities(reader);
                    }
                }
            }
            catch (Exception exc)
            {
                throw new CpException(CpExceptionType.DataAccessFetchOperation,
                    ExceptionManager.CommonMessage.UserAlertMessageFetchdata,
                    "Error In DAL While Executing Function Signature IParallelGroupWorkflowDataAccess.GetAllRunning_WorkflowGroupByUserId_New() for user having ID : "+UserId +
                    Environment.NewLine + "SYSTEM MESSAGE : " + exc.Message, exc);
            }
        }
        #endregion Methods

    }
}