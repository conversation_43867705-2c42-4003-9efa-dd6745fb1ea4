﻿using System;
using System.Diagnostics;
using System.Transactions;
using System.Web;
using System.Web.Security;
using CP.BusinessFacade;
using CP.Common.DatabaseEntity;
using CP.Common.Shared;
using CP.ExceptionHandler;
using CP.Helper;
using log4net;

namespace CP.UI
{
    public abstract class BasePage : System.Web.UI.Page, IBase
    {
        #region Member Variables

        private WebHelper _helper;
        private IFacade _facade;
        private User _loggedInUser;
        private UserRole _loggedInUserRole = UserRole.Undefined;
        private TransactionScope _currentTransaction;
        private CompanyProfile _companyProfile;

        private static readonly ILog _logger = LogManager.GetLogger(typeof(BasePage));

        #endregion Member Variables

        #region Properties

        public User LoggedInUser
        {
            [DebuggerStepThrough]
            get
            {
                if (_loggedInUser == null)
                {
                    if (WebHelper.CurrentSession.UserInfo.User == null)
                    {
                        _loggedInUser = _facade.GetUserByLoginName(LoggedInUserName);
                        WebHelper.CurrentSession.UserInfo.User = _loggedInUser;
                    }
                    else
                    {
                        _loggedInUser = (User)WebHelper.CurrentSession.UserInfo.User;
                    }
                }
                return _loggedInUser;
            }
            set
            {
                WebHelper.CurrentSession.UserInfo.User = value;
            }
        }

        public string LoggedInUserName
        {
            [DebuggerStepThrough]
            get
            {
                return IsUserAuthenticated ? Convert.ToString(Session["LoggedIn"]) : "Anonymous"; //Page.User.Identity.Name : "Anonymous";
            }
        }

        //code comment on 14012022 18:07
        //public string LoggedInUserName
        //{
        //    [DebuggerStepThrough]
        //    get
        //    {
        //        return !IsUserAuthenticated ? string.Empty : LoggedInUser.LoginName;
        //    }
        //}
        public UserRole LoggedInUserRole
        {
            [DebuggerStepThrough]
            get
            {
                if (_loggedInUserRole == UserRole.Undefined)
                {
                    if (WebHelper.CurrentSession.UserInfo.Role != null)
                    {
                        _loggedInUserRole = (UserRole)WebHelper.CurrentSession.UserInfo.Role;
                    }
                }
                return _loggedInUserRole;
            }
            set
            {
                WebHelper.CurrentSession.UserInfo.Role = value;
            }
        }

        public int LoggedInUserId
        {
            [DebuggerStepThrough]
            get
            {
                return !IsUserAuthenticated ? 0 : LoggedInUser.Id;
            }
        }

        public string LoggedInUserPassword
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUser != null ? LoggedInUser.LoginPassword : string.Empty;
            }
        }

        public int LoggedInUserCompanyId
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUser != null ? LoggedInUser.CompanyId : 0;
            }
        }

        public CompanyProfile LoggedInUserCompany
        {
            [DebuggerStepThrough]
            get
            {
                if (LoggedInUserCompanyId > 0)
                {
                    if (_companyProfile == null)
                    {
                        _companyProfile = Facade.GetCompanyProfileById(LoggedInUserCompanyId);
                    }
                }

                return _companyProfile;
            }
        }

        public bool IsUserAuthenticated
        {
            [DebuggerStepThrough]
            get
            {
                return Page.User.Identity.IsAuthenticated;
            }
        }

        public bool IsSuperAdmin
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.EnterpriseUser;
            }
        }

        public bool IsUserAdmin
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.Administrator;
            }
        }

        public bool IsUserManager
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.Manager;
            }
        }

        public bool IsUserExecutionUser
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.ExecutionAccessUser;
            }
        }
        public bool IsUserCustom
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.Custom;
            }
        }

        public bool IsUserSuperAdmin
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.SuperAdmin || LoggedInUserRole == UserRole.EnterpriseUser || LoggedInUserRole == UserRole.EnterpriseUser;
            }
        }

        public bool IsUserSuperMonitor
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.SuperMonitor;
            }
        }

        public bool IsUserOperator
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.Operator;
            }
        }


        public bool IsUserEnterpriseUser
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserRole == UserRole.EnterpriseUser;
            }
        }



        public bool IsParentCompnay
        {
            [DebuggerStepThrough]
            get
            {
                return LoggedInUserCompany.IsParent;
            }
        }

        public IFacade Facade
        {
            get
            {
                if (_facade == null)
                {
                    _facade = Context.Items[Constants.ContextConstants.Facade] as IFacade;
                    if (_facade == null)
                    {
                        _facade = new Facade();
                        Context.Items[Constants.ContextConstants.Facade] = _facade;
                    }
                }

                return _facade;
            }
        }

        public WebHelper Helper
        {
            get { return _helper ?? (_helper = new WebHelper(Page, ViewState)); }
        }

        public string Message
        {
            [DebuggerStepThrough]
            get
            {
                return Helper.Url.SecureUrl["msg"];
            }
        }

        public string HostAddress
        {
            get
            {
                return HttpContext.Current.Request.UserHostAddress;
            }
        }

        public bool IsUserAuthorized
        {
            get
            {
                if (EnumHelper.ContainsEnumValue(WebHelper.CurrentUrl.Url, typeof(Modules), "/", "/"))
                {
                    if (LoggedInUserRole == UserRole.Undefined)
                    {
                        FormsAuthentication.RedirectToLoginPage();
                        return false;
                    }
                    return true;
                }
                return true;
            }
        }

        #endregion Properties

        #region Methods

        public abstract void PrepareView();

        public void StartTransaction()
        {
            _currentTransaction = new TransactionScope();
        }

        public void EndTransaction()
        {
            if (_currentTransaction != null)
            {
                _currentTransaction.Complete();
                _currentTransaction.Dispose();
            }
        }

        public void DisposeTransaction()
        {
            if (_currentTransaction != null)
            {
                _currentTransaction.Dispose();
            }
        }

        public void InvalidateTransaction()
        {
            if (_currentTransaction != null)
            {
                _currentTransaction.Dispose();
            }
        }

        #endregion Methods

        #region Events

        protected override void OnLoad(EventArgs e)
        {
            try
            {
                //if (Session["loginUserName"] == null)
                //{
                //    Session["loginUserName"] = LoggedInUserName;
                //}
                //else if (string.IsNullOrEmpty(Session["loginUserName"].ToString()))
                //{
                //    Session["loginUserName"] = LoggedInUserName;
                //}

                try
                {
                    if (Session["loginUserName"] == null)
                    {
                        if (!string.IsNullOrEmpty(LoggedInUserName))
                        {
                            Session["loginUserName"] = LoggedInUserName;
                        }
                    }

                    if (Session["loginUserName"] != null && string.IsNullOrEmpty(Session["loginUserName"].ToString()))
                    {
                        if (!string.IsNullOrEmpty(LoggedInUserName))
                        {
                            Session["loginUserName"] = LoggedInUserName;
                        }
                    }
                }
                catch (Exception exc)
                {
                    _logger.Info("Error (BasePage): " + exc.Message);
                    if(exc.InnerException != null)
                        _logger.Info("Error (BasePage InnerException): " + exc.InnerException.Message);
 
                }

                base.OnLoad(e);

                if (!IsPostBack)
                {
                    // Check if this is a report page that should be allowed without referer
                    string currentPage = Request.Url.AbsolutePath.ToLower();
                    bool isReportPage = currentPage.Contains("/report/") ||
                                       currentPage.Contains("telerikreportmanagement") ||
                                       currentPage.Contains("reportmanagement");

                    if (string.IsNullOrEmpty(Request.ServerVariables["HTTP_REFERER"]) && !isReportPage)
                    {
                        HttpContext.Current.Response.Redirect("~/CustomError.aspx");
                        //Response.Redirect(Constants.UrlConstants.Urls.Error.Error401);
                    }
                    else
                    {
                        PrepareView();
                    }
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                CpException newCustomException;
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    newCustomException = (CpException)ex.InnerException;
                }
                else
                {
                    newCustomException = new CpException(CpExceptionType.CommonUnhandled, ex.Message);
                }
                ExceptionManager.Manage(newCustomException);
            }
        }

        protected override void OnPreInit(EventArgs e)
        {
            base.OnPreInit(e);

            try
            {
                base.OnPreInit(e);
                if (!IsUserAuthorized)
                {
                    throw new CpException(CpExceptionType.CommonPageAccessDenied, "Page access denied");
                }
            }
            catch (CpException ex)
            {
                ExceptionManager.Manage(ex);
            }
            catch (Exception ex)
            {
                CpException newCustomException;
                if (ex.InnerException != null && ex.InnerException.GetType() == typeof(CpException))
                {
                    newCustomException = (CpException)ex.InnerException;
                }
                else
                {
                    newCustomException = new CpException(CpExceptionType.CommonUnhandled, ex.Message);
                }
                ExceptionManager.Manage(newCustomException);
            }
        }

        #endregion Events
    }
}