using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using CP.UI.Report;
using CP.Common.DatabaseEntity;
using CP.BusinessFacade;
using System.Collections.Generic;
using System.Linq;
using CP.Helper;
using System.Web;
using System.Web.UI.WebControls;
using System.Web.UI;
using System.Data;

namespace CP.UI.Report.DevExpressReports
{
    /// <summary>
    /// Summary description for DRDrillReport
    /// </summary>
    public class DRDrillReport : DevExpress.XtraReports.UI.XtraReport
    {
        private TopMarginBand TopMargin;
        private BottomMarginBand BottomMargin;
        private DetailBand Detail;
        private XRLine xrLine4;
        private XRLabel xrLabel8;
        private XRLabel xrLabel3;
        private XRLabel xrLabel4;
        private XRLabel xrLabel23;
        private XRLabel xrLabel5;
        private XRLabel xrLabel24;
        private XRLabel xrLabel6;
        private XRLabel xrLabel25;
        private XRLine xrLine5;
        private XRLine xrLine1;
        private XRLabel xrLabel26;
        private XRLabel xrLabel9;
        private XRLabel xrLabel27;
        private XRLabel xrLabel10;
        private XRLabel xrLabel28;
        private XRLabel xrLabel11;
        private XRLabel xrLabel29;
        private XRTable xrTable1;
        private XRTableRow xrTableRow1;
        private XRTableCell xrTableCell1;
        private XRTableCell xrTableCell2;
        private XRTableCell xrTableCell3;
        private XRLine xrLine2;
        private XRTableCell xrTableCell6;
        private XRTableCell xrTableCell10;
        private XRTableCell xrTableCell11;
        private XRTableCell xrTableCell12;
        private XRTableCell xrTableCell13;
        private XRLabel xrLabel14;
        private XRLabel xrLabel13;

        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;
        private XRPanel xrPanel1;
        private ReportHeaderBand ReportHeader;
        private SubBand SubBand2;
        private PageFooterBand PageFooter;
        private XRLine xrLine8;
        private XRLine xrLine6;
        private XRLabel xrLabel1;
        private XRLine xrLine3;
        private XRPanel xrPanel2;
        private XRPictureBox xrPictureBox17;
        private XRPictureBox xrPictureBox16;
        private XRPictureBox xrPictureBox15;
        private XRPictureBox xrPictureBox14;
        private XRPictureBox xrPictureBox13;
        private XRPictureBox xrPictureBox12;
        private XRPictureBox xrPictureBox11;
        private XRPictureBox xrPictureBox10;
        private XRPictureBox xrPictureBox9;
        private XRLine xrLine9;
        private XRLabel xrLabel36;
        private XRLabel xrLabel35;
        private XRLabel xrLabel34;
        private XRLabel xrLabel33;
        private XRLabel xrLabel32;
        private XRLabel xrLabel31;
        private XRLabel xrLabel30;
        private XRLabel xrLabel22;
        private XRLabel xrLabel21;
        private XRLabel xrLabel20;
        private XRLabel xrLabel19;
        private XRLabel xrLabel18;
        private XRLabel xrLabel17;
        private XRLabel xrLabel16;
        private XRLabel xrLabel15;
        private XRLabel xrLabel12;
        private XRLabel xrLabel7;
        private XRLabel xrLabel2;
        private DevExpress.Xpo.Session session1;
        private XRTable xrTable2;
        private XRTableRow xrTableRow2;
        private XRTableCell xrTableCell4;
        private XRTableCell xrTableCell5;
        private XRTableCell xrTableCell7;
        private XRTableCell xrTableCell8;
        private XRTableCell xrTableCell9;
        private XRTableCell xrTableCell14;
        private XRTableCell xrTableCell15;
        private XRTableCell xrTableCell16;
        private XRLabel xrLabel37;
        private GroupHeaderBand GroupHeader1;
        private XRTable xrTable4;
        private XRTableRow xrTableRow4;
        private XRTableCell xrTableCell22;
        private XRTableCell xrTableCell23;
        private XRTableCell xrTableCell24;
        private XRTable xrTable3;
        private XRTableRow xrTableRow3;
        private XRTableCell xrTableCell17;
        private XRTableCell xrTableCell18;
        private XRTableCell xrTableCell19;
        private XRTableCell xrTableCell20;
        private XRTableCell xrTableCell21;
        private XRTableCell xrTableCell25;
        private XRTableCell xrTableCell26;
        private XRLabel xrLabel40;
        private XRPictureBox xrPictureBox18;
        private XRPictureBox xrPictureBox21;
        private XRPictureBox xrPictureBox20;
        private XRPictureBox xrPictureBox1;
        private XRPictureBox xrPictureBox8;
        private XRPictureBox xrPictureBox7;
        private XRPictureBox xrPictureBox6;
        private XRPictureBox xrPictureBox5;
        private XRPictureBox xrPictureBox4;
        private XRPictureBox xrPictureBox3;
        private XRPictureBox xrPictureBox2;
        private XRLine xrLine10;
        private XRLine xrLine7;
        private XRPageInfo xrPageInfo1;
        private GroupHeaderBand GroupHeader3;
        private GroupHeaderBand GroupHeader4;
        private XRPictureBox xrPictureBox22;
        private XRLine xrLine11;
        private XRLine xrLine13;
        private XRLabel xrLabel38;
        private XRLine xrLine12;
        private DatalagReport rpt;
        CP.BusinessFacade.IFacade Facade = new CP.BusinessFacade.Facade();
        public DRDrillReport()
        {


            InitializeComponent();
            //
            // TODO: Add constructor logic here
            //
            try
            {
                // Get session data from current HTTP context instead of creating new instance
                if (HttpContext.Current?.Session["ParaDROperId"] == null)
                {
                    throw new InvalidOperationException("ParaDROperId session variable is not set.");
                }

                int Id = Convert.ToInt32(HttpContext.Current.Session["ParaDROperId"]);
                string drOperationId = GetParallelDROpernId();
                string[] arr = drOperationId.Split(' ');
                int IDS = Convert.ToInt32(arr[0]);
                int DROpeationStatus = Convert.ToInt32(arr[1]);
                IList<ParallelWorkflowActionResult> values = Facade.GetparallelDrOperationDrilldata(IDS, DROpeationStatus).OrderBy(x => x.StartTime).ToList();
                //   IList<ParallelWorkflowActionResult> values1 = Facade.GetparallelDrOperationSummarydata(Id);
                // this.DataSource = values1;
                if (values.Count == 0)
                {

                }
                else
                {

                    TimeSpan total = values.Max(v => v.EndTime) - values.Min(v => v.StartTime);
                    values[0].TotalTime = total;
                    this.xrLabel26.Text = Convert.ToString(values[0].StartTime);
                    this.xrLabel27.Text = Convert.ToString(values.Max(v => v.EndTime));
                    this.xrLabel28.Text = Convert.ToString(total);
                    this.xrLabel13.Text = "Created by " + LoggedInUserName + " on " + DateTime.Now.ToString("dd/MM/yyyy hh:mm:ss tt");
                    this.xrLabel29.Text = ParaDROpercongi();
                    this.xrLabel25.Text = ParaDROperRPO();
                    // decimal label29Value = Convert.ToDecimal(xrLabel29.Text);
                    // decimal label25Value = Convert.ToDecimal(xrLabel25.Text);

                    if (!xrLabel29.Text.Contains("-"))
                    {
                        xrPictureBox21.Visible = true;
                        xrPictureBox20.Visible = false;
                        this.xrLabel29.ForeColor = Color.Green;
                    }
                    else
                    {
                        xrPictureBox20.Visible = true;
                        xrPictureBox21.Visible = false;
                        this.xrLabel29.ForeColor = Color.Red;
                    }






                    this.xrLabel24.Text = values[0].WorkflowName;
                    this.xrLabel23.Text = Convert.ToString(LoggedInUser.LoginName);
                    this.xrLabel16.Text = values[0].WorkflowActionName;
                    this.xrLabel18.Text = values[0].PRIPAddress;
                    this.xrLabel19.Text = values[0].DRIPAddress;
                    User usernme = Facade.GetUserByDROperationId(Id);
                    ParallelProfile profilenme = Facade.GetParellelProfileByDROperationId(Id);
                    //BusinessService cofigRTO = Facade.GetBusinessServicesByDROperationId(DROperationId);
                    BusinessFunction cofigRTO = Facade.GetBusinessFunctionByDROperationId(Id);
                    IList<ParallelWorkflowActionResult> ParaSummary = Facade.GetparallelDrOperationSummarydata(Id);
                    DateTime starttime;
                    DateTime edntime;
                    TimeSpan ttltime;
                    object NA = "NA";
                    object PRDBSID = "NA";
                    object DRDBSID = "NA";
                    object ActionType = "NA";
                    string prodIp = "NA";
                    string drIp = "NA";
                    string PRHostName = "NA";
                    string DRHostName = "NA";
                    string conRP = cofigRTO == null ? "00:00:00" : cofigRTO.ConfiguredRTO;

                    var getallparallelgroupworkflow = Facade.GetAllParallelGroupWorkflow();

                    var result = from grp in getallparallelgroupworkflow
                                 where grp.ParallelDROperationId == Id
                                 select new { Id = grp.Id, InfraObjectId = grp.InfraObjectId, Name = grp.InfraObjectName, WorkflowId = grp.WorkflowId };

                    var finalResult = result.ToList();

                    foreach (var groups in finalResult)
                    {
                        IList<InfraObject> grps = Facade.GetAllInfraObject();
                        var grpbyname = from grp in grps where grp.Name == groups.Name select grp;

                        //var InfraInfo = Facade.GetInfraObjectById(groups.InfraObjectId);
                        //var actionStatus = InfraInfo == null ? 0 : InfraInfo.DROperationStatus;

                        if (grpbyname.Count() != 0)
                        {
                            foreach (var gp in grpbyname)
                            {
                                IList<GroupWorkflow> actiontype = Facade.GetAllGroupWorkflowByWorkFlowId(groups.WorkflowId);

                                if (actiontype != null)
                                {

                                    foreach (var action in actiontype)
                                    {
                                        if (action.InfraObjectId == gp.Id)
                                        {
                                            if (actiontype[0] != null)
                                            {

                                                Server prserver = actiontype[0].ActionType == 2 ? Facade.GetServerById(gp.DRServerId) : Facade.GetServerById(gp.PRServerId);
                                                if (prserver != null)
                                                {
                                                    prodIp = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                                                    PRHostName = prserver.Name;
                                                }
                                                else
                                                {
                                                    prodIp = "NA";
                                                    PRHostName = "NA";
                                                }

                                                Server drserver = actiontype[0].ActionType == 2 ? Facade.GetServerById(gp.PRServerId) : Facade.GetServerById(gp.DRServerId);
                                                if (drserver != null)
                                                {
                                                    drIp = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                                                    DRHostName = drserver.Name;
                                                }
                                                else
                                                {
                                                    drIp = "NA";
                                                    DRHostName = "NA";
                                                }
                                            }
                                            else
                                            {
                                                Server prserver = actiontype[0].ActionType == 2 ? Facade.GetServerById(gp.DRServerId) : Facade.GetServerById(gp.PRServerId);

                                                if (prserver != null)
                                                {
                                                    prodIp = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                                                    PRHostName = prserver.Name;
                                                }
                                                else
                                                {
                                                    prodIp = "NA";
                                                    PRHostName = "NA";
                                                }

                                                Server drserver = actiontype[0].ActionType == 2 ? Facade.GetServerById(gp.PRServerId) : Facade.GetServerById(gp.DRServerId);
                                                if (drserver != null)
                                                {
                                                    drIp = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                                                    DRHostName = drserver.Name;
                                                }
                                                else
                                                {
                                                    drIp = "NA";
                                                    DRHostName = "NA";
                                                }
                                            }
                                        }
                                        else
                                        {

                                            Server prserver = actiontype[0].ActionType == 2 ? Facade.GetServerById(gp.DRServerId) : Facade.GetServerById(gp.PRServerId);
                                            if (prserver != null)
                                            {
                                                prodIp = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                                                PRHostName = prserver.Name;
                                            }
                                            else
                                            {
                                                prodIp = "NA";
                                                PRHostName = "NA";
                                            }

                                            Server drserver = actiontype[0].ActionType == 2 ? Facade.GetServerById(gp.PRServerId) : Facade.GetServerById(gp.DRServerId);
                                            if (drserver != null)
                                            {
                                                drIp = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                                                DRHostName = drserver.Name;
                                            }
                                            else
                                            {
                                                drIp = "NA";
                                                DRHostName = "NA";
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    Server prserver = Facade.GetServerById(gp.PRServerId);
                                    if (prserver != null)
                                    {
                                        prodIp = CryptographyHelper.Md5Decrypt(prserver.IPAddress);
                                        PRHostName = prserver.Name;
                                    }
                                    else
                                    {
                                        prodIp = "NA";
                                        PRHostName = "NA";
                                    }

                                    Server drserver = Facade.GetServerById(gp.DRServerId);
                                    if (drserver != null)
                                    {
                                        drIp = CryptographyHelper.Md5Decrypt(drserver.IPAddress);
                                        DRHostName = drserver.Name;
                                    }
                                    else
                                    {
                                        drIp = "NA";
                                        DRHostName = "NA";
                                    }


                                }
                            }
                        }
                        else
                        {
                            prodIp = "NA";
                            drIp = "NA";
                            PRHostName = "NA";
                            DRHostName = "NA";
                        }

                    }

                    if (ParaSummary != null && ParaSummary.Count != 0)
                    {
                        foreach (var groups in ParaSummary)
                        {
                            foreach (var final in finalResult)
                            {
                                IList<GroupWorkflow> actiontype = Facade.GetAllGroupWorkflowByWorkFlowId(final.WorkflowId);
                                if (actiontype != null)
                                {
                                    foreach (var action in actiontype)
                                    {
                                        DatabaseBase prdatabase = Facade.GetDatabaseBaseByGroupIdPRServerId(groups.InfraObjectId);
                                        DatabaseBase drdatabase = Facade.GetDatabaseBaseByGroupIdDrServerId(groups.InfraObjectId);
                                        var exchangeDAG = Facade.GetInfraObjectById(groups.InfraObjectId);
                                        if (prdatabase != null)
                                        {
                                            if (prdatabase.Type.ToString() == "PRDatabase")
                                            {
                                                if (prdatabase.DatabaseType.ToString() == "Oracle")
                                                {
                                                    DatabaseOracle oracledb = Facade.GetDatabaseOracleByDatabaseBaseId(prdatabase.Id);
                                                    if (oracledb != null)
                                                        PRDBSID = oracledb.OracleSID;
                                                    else
                                                        PRDBSID = "NA";
                                                }
                                                else if (prdatabase.DatabaseType.ToString() == "Sql")
                                                {
                                                    DatabaseSql sql = Facade.GetDatabaseSqlByDatabaseBaseId(prdatabase.Id);
                                                    if (sql != null)
                                                        PRDBSID = sql.DatabaseSID;
                                                    else
                                                        PRDBSID = "NA";
                                                }
                                                else if (prdatabase.DatabaseType.ToString() == "Exchange")
                                                {
                                                    DatabaseExchange exchange = Facade.GetDatabaseExchangeByDatabaseBaseId(prdatabase.Id);
                                                    if (exchange != null)
                                                        //PRDBSID = "NA";
                                                        PRDBSID = exchange.MailBoxDBName;
                                                    else
                                                        PRDBSID = "NA";
                                                }
                                                else if (prdatabase.DatabaseType.ToString() == "DB2")
                                                {
                                                    DatabaseDB2 db2 = Facade.GetDatabaseDb2ByDatabaseBaseId(prdatabase.Id);
                                                    if (db2 != null)
                                                        PRDBSID = db2.DatabaseSID;
                                                    else
                                                        PRDBSID = "NA";
                                                }
                                                else if (prdatabase.DatabaseType.ToString() == "OracleRac")
                                                {
                                                    var infraobject = Facade.GetInfraObjectById(groups.InfraObjectId);
                                                    IList<Nodes> nodesall = Facade.GetAllNodes();

                                                    if (nodesall != null && nodesall.Count > 0)
                                                    {
                                                        var oraclerac = from oraclenode in nodesall
                                                                        where oraclenode.ServerId == infraobject.PRServerId
                                                                        select oraclenode;

                                                        if (oraclerac != null && oraclerac.Count() > 0)
                                                        {
                                                            PRDBSID = oraclerac.FirstOrDefault().OracleSID != "" ? oraclerac.FirstOrDefault().OracleSID : "NA";
                                                        }
                                                        else
                                                        {
                                                            PRDBSID = "NA";
                                                        }
                                                    }

                                                    //DatabaseOracle oracledb = Facade.GetDatabaseOracleByDatabaseBaseId(prdatabase.Id);
                                                    //if (oracledb != null)
                                                    //    PRDBSID = oracledb.OracleSID;
                                                    //else
                                                    //    PRDBSID = "NA";
                                                }
                                            }
                                            else
                                                PRDBSID = "NA";
                                        }
                                        else if (exchangeDAG != null)
                                        {
                                            if (exchangeDAG.RecoveryType == 20)
                                            {
                                                IList<DataBaseExchangeDAG> exc = Facade.GetDatabaseExchangeDAGByServerId(exchangeDAG.PRServerId);

                                                if (exc != null)
                                                    PRDBSID = exc[0].MailBoxDBName;
                                                else
                                                    PRDBSID = "NA";
                                            }
                                        }


                                        if (drdatabase != null)
                                        {
                                            if (drdatabase.Type.ToString() == "DRDatabase")
                                            {
                                                if (drdatabase.DatabaseType.ToString() == "Oracle")
                                                {
                                                    DatabaseOracle ora = Facade.GetDatabaseOracleByDatabaseBaseId(drdatabase.Id);
                                                    if (ora != null)
                                                        DRDBSID = ora.OracleSID;
                                                    else
                                                        DRDBSID = "NA";
                                                }
                                                else if (drdatabase.DatabaseType.ToString() == "Sql")
                                                {
                                                    DatabaseSql sql = Facade.GetDatabaseSqlByDatabaseBaseId(drdatabase.Id);
                                                    if (sql != null)
                                                        DRDBSID = sql.DatabaseSID;
                                                    else
                                                        DRDBSID = "NA";
                                                }
                                                else if (drdatabase.DatabaseType.ToString() == "Exchange")
                                                {
                                                    DatabaseExchange exchange = Facade.GetDatabaseExchangeByDatabaseBaseId(drdatabase.Id);
                                                    if (exchange != null)
                                                        //DRDBSID = "NA";
                                                        DRDBSID = exchange.MailBoxDBName;
                                                    else
                                                        DRDBSID = "NA";
                                                }
                                                else if (drdatabase.DatabaseType.ToString() == "DB2")
                                                {
                                                    DatabaseDB2 db2 = Facade.GetDatabaseDb2ByDatabaseBaseId(drdatabase.Id);
                                                    if (db2 != null)
                                                        DRDBSID = db2.DatabaseSID;
                                                    else
                                                        DRDBSID = "NA";
                                                }
                                                else if (drdatabase.DatabaseType.ToString() == "OracleRac")
                                                {
                                                    var infraobject = Facade.GetInfraObjectById(groups.InfraObjectId);
                                                    IList<Nodes> nodesall = Facade.GetAllNodes();

                                                    if (nodesall != null && nodesall.Count > 0)
                                                    {
                                                        var oraclerac = from oraclenode in nodesall
                                                                        where oraclenode.ServerId == infraobject.DRServerId
                                                                        select oraclenode;

                                                        if (oraclerac != null && oraclerac.Count() > 0)
                                                        {
                                                            DRDBSID = oraclerac.FirstOrDefault().OracleSID != "" ? oraclerac.FirstOrDefault().OracleSID : "NA";
                                                        }
                                                        else
                                                        {
                                                            DRDBSID = "NA";
                                                        }
                                                    }
                                                }
                                            }
                                            else
                                                DRDBSID = "NA";
                                        }

                                        else if (exchangeDAG != null)
                                        {
                                            if (exchangeDAG.RecoveryType == 20)
                                            {
                                                IList<DataBaseExchangeDAG> exc = Facade.GetDatabaseExchangeDAGByServerId(exchangeDAG.DRServerId);

                                                if (exc != null)
                                                    DRDBSID = exc[0].MailBoxDBName;
                                                else
                                                    DRDBSID = "NA";
                                            }
                                        }
                                        else
                                            DRDBSID = "NA";

                                    }
                                }
                            }
                        }

                        //starttime = ParaSummary[0].StartTime;
                        //edntime = ParaSummary[ParaSummary.Count - 1].EndTime;
                        //ttltime = edntime - starttime;

                        starttime = (from dt in ParaSummary select dt.StartTime).Min();
                        edntime = (from dt in ParaSummary select dt.EndTime).Max();
                        ttltime = edntime - starttime;



                        string ttltme = Convert.ToString(ttltime);

                        TimeSpan fnlDev = conRP.Contains(":") ? Convert.ToDateTime(conRP != "" ? conRP : "00:00:00") - Convert.ToDateTime(ttltme) :
                        TimeSpan.FromSeconds(conRP != "" ? Convert.ToDouble(conRP) : Convert.ToDouble(0));

                        string dev = Convert.ToString(fnlDev);
                        string[] devsplit = dev.Split(':');
                        //string devfnl = devsplit[0] + "h " + devsplit[1] + "m";
                        ////report.ReportParameters["iDeviation"].Value = Convert.ToString(devfnl);
                        string devfnl = devsplit[0] + "h " + devsplit[1] + "m " + devsplit[2] + "s ";
                        //report.ReportParameters["iDeviation"].Value = Convert.ToString(devfnl);
                        // report.ReportParameters["iDeviation"].Value = Convert.ToString(dev);

                        string ttl = Convert.ToString(ttltime);
                        string[] ttlsplit = ttl.Split(':');
                        string ttlfnl = ttlsplit[0] + "h " + ttlsplit[1] + "m";
                        // report.ReportParameters["iTotalTime"].Value = Convert.ToString(ttl);
                    }
                    else
                    {
                        //report.ReportParameters["iStartTime"].Value = "NA";
                        //report.ReportParameters["iEndTime"].Value = "NA";
                        //report.ReportParameters["iTotalTime"].Value = "NA";
                        //report.ReportParameters["iDeviation"].Value = "NA";
                    }

                    this.xrLabel34.Text = Convert.ToString(PRDBSID);
                    this.xrLabel35.Text = Convert.ToString(DRDBSID);
                    this.xrLabel17.Text = Convert.ToString(ActionType);
                    // report.ReportParameters["iDROperationId"].Value = ddlParallelDrOperation.SelectedValue;
                    //report.ReportParameters["iUserName"].Value = LoggedInUserName != null ? LoggedInUserName : "NA";
                    //report.ReportParameters["iProfileExecutedBy"].Value = usernme != null ? usernme.LoginName : "NA";
                    //  report.ReportParameters["iProfileName"].Value = profilenme != null ? profilenme.ProfileName : "NA";
                    // report.ReportParameters["prodIp"].Value = prodIp;
                    //  report.ReportParameters["drIp"].Value = drIp;
                    this.xrLabel32.Text = PRHostName;
                    this.xrLabel33.Text = DRHostName;
                    this.xrLabel36.Text = "NA: Not  Applicable";
                    this.DataSource = values;

                }
            }
            catch (Exception ex)
            {
                // Log the error and set default values
                System.Diagnostics.Debug.WriteLine("Error in DRDrillReport constructor: " + ex.Message);

                // Set default values for labels to prevent display issues
                this.xrLabel13.Text = "Created by Unknown User on " + DateTime.Now.ToString("dd/MM/yyyy hh:mm:ss tt");
                this.xrLabel23.Text = "Unknown";
                this.xrLabel24.Text = "No Data Available";
                this.xrLabel16.Text = "Error Loading Report";
                this.xrLabel26.Text = "N/A";
                this.xrLabel27.Text = "N/A";
                this.xrLabel28.Text = "N/A";
                this.xrLabel29.Text = "N/A";
                this.xrLabel25.Text = "N/A";
                this.xrLabel34.Text = "N/A";
                this.xrLabel35.Text = "N/A";
                this.xrLabel17.Text = "N/A";
                this.xrLabel32.Text = "N/A";
                this.xrLabel33.Text = "N/A";
                this.xrLabel36.Text = "Error: " + ex.Message;

                // Set empty data source
                this.DataSource = new List<ParallelWorkflowActionResult>();
            }

        }

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DRDrillReport));
            DevExpress.XtraReports.UI.XRSummary xrSummary1 = new DevExpress.XtraReports.UI.XRSummary();
            DevExpress.XtraReports.UI.XRSummary xrSummary2 = new DevExpress.XtraReports.UI.XRSummary();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.xrPictureBox22 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrLine4 = new DevExpress.XtraReports.UI.XRLine();
            this.xrPictureBox1 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrLine11 = new DevExpress.XtraReports.UI.XRLine();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell24 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell25 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell26 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLine10 = new DevExpress.XtraReports.UI.XRLine();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine2 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel24 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel25 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine5 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel26 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel27 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel28 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel29 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrPanel1 = new DevExpress.XtraReports.UI.XRPanel();
            this.xrPictureBox21 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox20 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox8 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox7 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox6 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox5 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox4 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox3 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox2 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.ReportHeader = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.SubBand2 = new DevExpress.XtraReports.UI.SubBand();
            this.xrLine7 = new DevExpress.XtraReports.UI.XRLine();
            this.PageFooter = new DevExpress.XtraReports.UI.PageFooterBand();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.GroupHeader1 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.xrPictureBox18 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrLabel40 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine3 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine6 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLine8 = new DevExpress.XtraReports.UI.XRLine();
            this.xrPanel2 = new DevExpress.XtraReports.UI.XRPanel();
            this.xrLine13 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel38 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine12 = new DevExpress.XtraReports.UI.XRLine();
            this.xrPictureBox17 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox16 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox15 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox14 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox13 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox12 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox11 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox10 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrPictureBox9 = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrLine9 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel36 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel35 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel34 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel33 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel32 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel31 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel30 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel22 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel21 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel18 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.session1 = new DevExpress.Xpo.Session(this.components);
            this.xrLabel37 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell17 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell21 = new DevExpress.XtraReports.UI.XRTableCell();
            this.GroupHeader3 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            this.GroupHeader4 = new DevExpress.XtraReports.UI.GroupHeaderBand();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.session1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureBox22,
            this.xrLine4,
            this.xrPictureBox1});
            this.TopMargin.HeightF = 63.54167F;
            this.TopMargin.Name = "TopMargin";
            // 
            // xrPictureBox22
            // 
            this.xrPictureBox22.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox22.ImageSource"));
            this.xrPictureBox22.LocationFloat = new DevExpress.Utils.PointFloat(871.4472F, 10.00001F);
            this.xrPictureBox22.Name = "xrPictureBox22";
            this.xrPictureBox22.SizeF = new System.Drawing.SizeF(189.5108F, 25.83332F);
            this.xrPictureBox22.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // xrLine4
            // 
            this.xrLine4.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine4.LineWidth = 2F;
            this.xrLine4.LocationFloat = new DevExpress.Utils.PointFloat(7.916641F, 48.95833F);
            this.xrLine4.Name = "xrLine4";
            this.xrLine4.SizeF = new System.Drawing.SizeF(1062F, 13.00001F);
            this.xrLine4.StylePriority.UseForeColor = false;
            // 
            // xrPictureBox1
            // 
            this.xrPictureBox1.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox1.ImageSource"));
            this.xrPictureBox1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 10.00001F);
            this.xrPictureBox1.Name = "xrPictureBox1";
            this.xrPictureBox1.SizeF = new System.Drawing.SizeF(212.5F, 25.83332F);
            this.xrPictureBox1.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Name = "BottomMargin";
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLine11,
            this.xrTable4});
            this.Detail.HeightF = 46.50019F;
            this.Detail.Name = "Detail";
            // 
            // xrLine11
            // 
            this.xrLine11.ForeColor = System.Drawing.Color.Gray;
            this.xrLine11.LocationFloat = new DevExpress.Utils.PointFloat(2.083365F, 23.50019F);
            this.xrLine11.Name = "xrLine11";
            this.xrLine11.SizeF = new System.Drawing.SizeF(1027.041F, 23F);
            this.xrLine11.StylePriority.UseForeColor = false;
            // 
            // xrTable4
            // 
            this.xrTable4.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.SizeF = new System.Drawing.SizeF(1060.958F, 23.50019F);
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell22,
            this.xrTableCell23,
            this.xrTableCell24,
            this.xrTableCell25,
            this.xrTableCell26});
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.Weight = 1D;
            // 
            // xrTableCell22
            // 
            this.xrTableCell22.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[WorkflowActionName]")});
            this.xrTableCell22.Multiline = true;
            this.xrTableCell22.Name = "xrTableCell22";
            this.xrTableCell22.Text = "xrTableCell22";
            this.xrTableCell22.Weight = 1.2513454297413251D;
            // 
            // xrTableCell23
            // 
            this.xrTableCell23.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[StartTime]")});
            this.xrTableCell23.Multiline = true;
            this.xrTableCell23.Name = "xrTableCell23";
            this.xrTableCell23.Text = "xrTableCell23";
            this.xrTableCell23.Weight = 1.0522693176724307D;
            // 
            // xrTableCell24
            // 
            this.xrTableCell24.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[EndTime]")});
            this.xrTableCell24.Multiline = true;
            this.xrTableCell24.Name = "xrTableCell24";
            this.xrTableCell24.Text = "xrTableCell24";
            this.xrTableCell24.Weight = 1.00860401089909D;
            // 
            // xrTableCell25
            // 
            this.xrTableCell25.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[EndTime]-[StartTime]")});
            this.xrTableCell25.Multiline = true;
            this.xrTableCell25.Name = "xrTableCell25";
            this.xrTableCell25.Text = "xrTableCell25";
            this.xrTableCell25.Weight = 0.97152771289859086D;
            // 
            // xrTableCell26
            // 
            this.xrTableCell26.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[Status]")});
            this.xrTableCell26.Multiline = true;
            this.xrTableCell26.Name = "xrTableCell26";
            this.xrTableCell26.Text = "xrTableCell26";
            this.xrTableCell26.Weight = 0.71625352878856352D;
            // 
            // xrLine10
            // 
            this.xrLine10.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine10.LineWidth = 2F;
            this.xrLine10.LocationFloat = new DevExpress.Utils.PointFloat(3.973643E-05F, 63.12504F);
            this.xrLine10.Name = "xrLine10";
            this.xrLine10.SizeF = new System.Drawing.SizeF(1058.875F, 19.37501F);
            this.xrLine10.StylePriority.UseForeColor = false;
            // 
            // xrTable2
            // 
            this.xrTable2.Font = new DevExpress.Drawing.DXFont("Arial", 9F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(1.041802F, 41.66667F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(1059.917F, 21.45837F);
            this.xrTable2.StylePriority.UseFont = false;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell4,
            this.xrTableCell5,
            this.xrTableCell7,
            this.xrTableCell8,
            this.xrTableCell9,
            this.xrTableCell14,
            this.xrTableCell15,
            this.xrTableCell16});
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.Weight = 1D;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[WorkflowName]")});
            this.xrTableCell4.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrTableCell4.Multiline = true;
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.StylePriority.UseFont = false;
            this.xrTableCell4.StylePriority.UseTextAlignment = false;
            this.xrTableCell4.Text = "xrTableCell4";
            this.xrTableCell4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell4.Weight = 1.2609024027605218D;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[InfraobjectName]")});
            this.xrTableCell5.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrTableCell5.Multiline = true;
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseFont = false;
            this.xrTableCell5.StylePriority.UseTextAlignment = false;
            this.xrTableCell5.Text = "xrTableCell5";
            this.xrTableCell5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell5.Weight = 1.0918692211216019D;
            // 
            // xrTableCell7
            // 
            this.xrTableCell7.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Min([StartTime])")});
            this.xrTableCell7.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrTableCell7.Multiline = true;
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.StylePriority.UseFont = false;
            this.xrTableCell7.StylePriority.UseTextAlignment = false;
            this.xrTableCell7.Text = "xrTableCell7";
            this.xrTableCell7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell7.Weight = 1.0073527541056813D;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "sumMax([EndTime])")});
            this.xrTableCell8.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrTableCell8.Multiline = true;
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseFont = false;
            this.xrTableCell8.StylePriority.UseTextAlignment = false;
            xrSummary1.Running = DevExpress.XtraReports.UI.SummaryRunning.Group;
            this.xrTableCell8.Summary = xrSummary1;
            this.xrTableCell8.Text = "xrTableCell8";
            this.xrTableCell8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell8.Weight = 1.032706976149929D;
            // 
            // xrTableCell9
            // 
            this.xrTableCell9.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "sumMax([EndTime])- Min([StartTime])")});
            this.xrTableCell9.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrTableCell9.Multiline = true;
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.StylePriority.UseFont = false;
            this.xrTableCell9.StylePriority.UseTextAlignment = false;
            xrSummary2.Running = DevExpress.XtraReports.UI.SummaryRunning.Group;
            this.xrTableCell9.Summary = xrSummary2;
            this.xrTableCell9.Text = "xrTableCell9";
            this.xrTableCell9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell9.Weight = 1.0665145038631967D;
            // 
            // xrTableCell14
            // 
            this.xrTableCell14.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[PRIPAddress]")});
            this.xrTableCell14.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrTableCell14.Multiline = true;
            this.xrTableCell14.Name = "xrTableCell14";
            this.xrTableCell14.StylePriority.UseFont = false;
            this.xrTableCell14.StylePriority.UseTextAlignment = false;
            this.xrTableCell14.Text = "xrTableCell14";
            this.xrTableCell14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell14.Weight = 1.0918694687286807D;
            // 
            // xrTableCell15
            // 
            this.xrTableCell15.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[DRIPAddress]")});
            this.xrTableCell15.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrTableCell15.Multiline = true;
            this.xrTableCell15.Name = "xrTableCell15";
            this.xrTableCell15.StylePriority.UseFont = false;
            this.xrTableCell15.StylePriority.UseTextAlignment = false;
            this.xrTableCell15.Text = "xrTableCell15";
            this.xrTableCell15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell15.Weight = 1.1763868082096622D;
            // 
            // xrTableCell16
            // 
            this.xrTableCell16.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "[STS]")});
            this.xrTableCell16.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrTableCell16.Multiline = true;
            this.xrTableCell16.Name = "xrTableCell16";
            this.xrTableCell16.StylePriority.UseFont = false;
            this.xrTableCell16.StylePriority.UseTextAlignment = false;
            this.xrTableCell16.Text = "xrTableCell16";
            this.xrTableCell16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell16.Weight = 0.8721249896795743D;
            // 
            // xrLabel14
            // 
            this.xrLabel14.BackColor = System.Drawing.Color.LightSeaGreen;
            this.xrLabel14.BorderColor = System.Drawing.Color.Black;
            this.xrLabel14.Font = new DevExpress.Drawing.DXFont("Times New Roman", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel14.ForeColor = System.Drawing.Color.White;
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrLabel14.Multiline = true;
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(1063.042F, 23F);
            this.xrLabel14.StylePriority.UseBackColor = false;
            this.xrLabel14.StylePriority.UseBorderColor = false;
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UseForeColor = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            this.xrLabel14.Text = "DR DRILL SUMMARY";
            this.xrLabel14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrLabel13
            // 
            this.xrLabel13.BackColor = System.Drawing.Color.LightSeaGreen;
            this.xrLabel13.Font = new DevExpress.Drawing.DXFont("Tahoma", 9F);
            this.xrLabel13.ForeColor = System.Drawing.Color.White;
            this.xrLabel13.LocationFloat = new DevExpress.Utils.PointFloat(0F, 23F);
            this.xrLabel13.Multiline = true;
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.SizeF = new System.Drawing.SizeF(1063.042F, 23F);
            this.xrLabel13.StylePriority.UseBackColor = false;
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UseForeColor = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            this.xrLabel13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLine2
            // 
            this.xrLine2.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine2.LocationFloat = new DevExpress.Utils.PointFloat(615.2084F, 45.99999F);
            this.xrLine2.Name = "xrLine2";
            this.xrLine2.SizeF = new System.Drawing.SizeF(446.7916F, 23F);
            this.xrLine2.StylePriority.UseForeColor = false;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(616.6407F, 10.00001F);
            this.xrLabel8.Multiline = true;
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(100F, 23F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.Text = "Drill Start Time";
            // 
            // xrLabel3
            // 
            this.xrLabel3.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(2.083365F, 45.99999F);
            this.xrLabel3.Multiline = true;
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(1060.958F, 23F);
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "     REPORT DETAILS";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(30.62501F, 10.00001F);
            this.xrLabel4.Multiline = true;
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(142.7083F, 23F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "Profile Executed By\r\n\r\n";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel23.LocationFloat = new DevExpress.Utils.PointFloat(283.3335F, 10.00001F);
            this.xrLabel23.Multiline = true;
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel23.SizeF = new System.Drawing.SizeF(191.9326F, 23F);
            this.xrLabel23.StylePriority.UseFont = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            this.xrLabel23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(30.62499F, 49.25019F);
            this.xrLabel5.Multiline = true;
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(176.0417F, 23F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.Text = "Report Functional Category";
            // 
            // xrLabel24
            // 
            this.xrLabel24.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel24.LocationFloat = new DevExpress.Utils.PointFloat(283.3335F, 49.25019F);
            this.xrLabel24.Multiline = true;
            this.xrLabel24.Name = "xrLabel24";
            this.xrLabel24.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel24.SizeF = new System.Drawing.SizeF(191.9326F, 23F);
            this.xrLabel24.StylePriority.UseFont = false;
            this.xrLabel24.StylePriority.UseTextAlignment = false;
            this.xrLabel24.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(30.62502F, 89.0001F);
            this.xrLabel6.Multiline = true;
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(142.7083F, 23F);
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.Text = "Configured RTO\r\n";
            // 
            // xrLabel25
            // 
            this.xrLabel25.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel25.LocationFloat = new DevExpress.Utils.PointFloat(283.3334F, 89.0001F);
            this.xrLabel25.Multiline = true;
            this.xrLabel25.Name = "xrLabel25";
            this.xrLabel25.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel25.SizeF = new System.Drawing.SizeF(191.9326F, 23.00001F);
            this.xrLabel25.StylePriority.UseFont = false;
            this.xrLabel25.StylePriority.UseTextAlignment = false;
            this.xrLabel25.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLine5
            // 
            this.xrLine5.BorderColor = System.Drawing.Color.Black;
            this.xrLine5.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine5.LineDirection = DevExpress.XtraReports.UI.LineDirection.Vertical;
            this.xrLine5.LocationFloat = new DevExpress.Utils.PointFloat(517.192F, 10.00001F);
            this.xrLine5.Name = "xrLine5";
            this.xrLine5.SizeF = new System.Drawing.SizeF(25.26617F, 144.75F);
            this.xrLine5.StylePriority.UseBorderColor = false;
            this.xrLine5.StylePriority.UseForeColor = false;
            // 
            // xrLine1
            // 
            this.xrLine1.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 45.99999F);
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.SizeF = new System.Drawing.SizeF(475.2661F, 23F);
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // xrLabel26
            // 
            this.xrLabel26.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel26.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 10.00001F);
            this.xrLabel26.Multiline = true;
            this.xrLabel26.Name = "xrLabel26";
            this.xrLabel26.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel26.SizeF = new System.Drawing.SizeF(186.4166F, 23F);
            this.xrLabel26.StylePriority.UseFont = false;
            this.xrLabel26.StylePriority.UseTextAlignment = false;
            this.xrLabel26.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(617.2917F, 49.25019F);
            this.xrLabel9.Multiline = true;
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(100F, 23F);
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.Text = "Drill End Time";
            // 
            // xrLabel27
            // 
            this.xrLabel27.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel27.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 49.25019F);
            this.xrLabel27.Multiline = true;
            this.xrLabel27.Name = "xrLabel27";
            this.xrLabel27.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel27.SizeF = new System.Drawing.SizeF(186.4166F, 23F);
            this.xrLabel27.StylePriority.UseFont = false;
            this.xrLabel27.StylePriority.UseTextAlignment = false;
            this.xrLabel27.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(616.6407F, 89.0001F);
            this.xrLabel10.Multiline = true;
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(203.125F, 23F);
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.Text = "Actual RTO (Drill Executed Time)";
            // 
            // xrLabel28
            // 
            this.xrLabel28.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel28.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 89.0001F);
            this.xrLabel28.Multiline = true;
            this.xrLabel28.Name = "xrLabel28";
            this.xrLabel28.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel28.SizeF = new System.Drawing.SizeF(186.4166F, 23F);
            this.xrLabel28.StylePriority.UseFont = false;
            this.xrLabel28.StylePriority.UseTextAlignment = false;
            this.xrLabel28.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(617.2918F, 124.4168F);
            this.xrLabel11.Multiline = true;
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(216.6667F, 23F);
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.Text = "Configured RTO  Less Actual RTO";
            // 
            // xrLabel29
            // 
            this.xrLabel29.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel29.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 124.4168F);
            this.xrLabel29.Multiline = true;
            this.xrLabel29.Name = "xrLabel29";
            this.xrLabel29.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel29.SizeF = new System.Drawing.SizeF(131.2083F, 22.99998F);
            this.xrLabel29.StylePriority.UseFont = false;
            this.xrLabel29.StylePriority.UseTextAlignment = false;
            this.xrLabel29.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrTable1
            // 
            this.xrTable1.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(1.041683F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(1059.917F, 41.66667F);
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell2,
            this.xrTableCell3,
            this.xrTableCell6,
            this.xrTableCell10,
            this.xrTableCell11,
            this.xrTableCell12,
            this.xrTableCell13});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.Weight = 1D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell1.Multiline = true;
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.Text = "Workflow Name";
            this.xrTableCell1.Weight = 1.1729696181098412D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell2.Multiline = true;
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell2.StylePriority.UseBackColor = false;
            this.xrTableCell2.Text = "Infraobject Name";
            this.xrTableCell2.Weight = 1.0157244688573561D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell3.Multiline = true;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.Text = "Start Time";
            this.xrTableCell3.Weight = 0.93710200940084443D;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell6.Multiline = true;
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.Text = "End Time";
            this.xrTableCell6.Weight = 0.96068900061120577D;
            // 
            // xrTableCell10
            // 
            this.xrTableCell10.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell10.Multiline = true;
            this.xrTableCell10.Name = "xrTableCell10";
            this.xrTableCell10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell10.StylePriority.UseBackColor = false;
            this.xrTableCell10.Text = "Total Time(hh:mm:ss)";
            this.xrTableCell10.Weight = 0.99213701696807166D;
            // 
            // xrTableCell11
            // 
            this.xrTableCell11.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell11.Multiline = true;
            this.xrTableCell11.Name = "xrTableCell11";
            this.xrTableCell11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell11.StylePriority.UseBackColor = false;
            this.xrTableCell11.Text = "Production IP HostName";
            this.xrTableCell11.Weight = 1.0157246991968179D;
            // 
            // xrTableCell12
            // 
            this.xrTableCell12.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell12.Multiline = true;
            this.xrTableCell12.Name = "xrTableCell12";
            this.xrTableCell12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell12.StylePriority.UseBackColor = false;
            this.xrTableCell12.Text = "DR IP HostName";
            this.xrTableCell12.Weight = 1.0943473889927913D;
            // 
            // xrTableCell13
            // 
            this.xrTableCell13.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell13.Multiline = true;
            this.xrTableCell13.Name = "xrTableCell13";
            this.xrTableCell13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrTableCell13.StylePriority.UseBackColor = false;
            this.xrTableCell13.Text = "Status";
            this.xrTableCell13.Weight = 0.81130579786307178D;
            // 
            // xrPanel1
            // 
            this.xrPanel1.BackColor = System.Drawing.Color.WhiteSmoke;
            this.xrPanel1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPictureBox21,
            this.xrPictureBox20,
            this.xrLabel29,
            this.xrLabel28,
            this.xrLabel27,
            this.xrLabel26,
            this.xrLabel11,
            this.xrPictureBox8,
            this.xrLabel10,
            this.xrPictureBox7,
            this.xrLabel9,
            this.xrPictureBox6,
            this.xrLabel8,
            this.xrPictureBox5,
            this.xrLine5,
            this.xrLabel25,
            this.xrLabel24,
            this.xrLabel23,
            this.xrLabel6,
            this.xrPictureBox4,
            this.xrPictureBox3,
            this.xrLabel5,
            this.xrPictureBox2,
            this.xrLabel4});
            this.xrPanel1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrPanel1.Name = "xrPanel1";
            this.xrPanel1.SizeF = new System.Drawing.SizeF(1062F, 164.75F);
            this.xrPanel1.StylePriority.UseBackColor = false;
            // 
            // xrPictureBox21
            // 
            this.xrPictureBox21.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox21.ImageSource"));
            this.xrPictureBox21.LocationFloat = new DevExpress.Utils.PointFloat(1007.208F, 124.4168F);
            this.xrPictureBox21.Name = "xrPictureBox21";
            this.xrPictureBox21.SizeF = new System.Drawing.SizeF(19.83337F, 22.99998F);
            this.xrPictureBox21.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox20
            // 
            this.xrPictureBox20.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox20.ImageSource"));
            this.xrPictureBox20.LocationFloat = new DevExpress.Utils.PointFloat(1029.375F, 124.4168F);
            this.xrPictureBox20.Name = "xrPictureBox20";
            this.xrPictureBox20.SizeF = new System.Drawing.SizeF(22.62476F, 22.99998F);
            this.xrPictureBox20.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox8
            // 
            this.xrPictureBox8.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox8.ImageSource"));
            this.xrPictureBox8.LocationFloat = new DevExpress.Utils.PointFloat(578.5417F, 124.4168F);
            this.xrPictureBox8.Name = "xrPictureBox8";
            this.xrPictureBox8.SizeF = new System.Drawing.SizeF(21.45831F, 23F);
            // 
            // xrPictureBox7
            // 
            this.xrPictureBox7.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox7.ImageSource"));
            this.xrPictureBox7.LocationFloat = new DevExpress.Utils.PointFloat(578.5417F, 89.0001F);
            this.xrPictureBox7.Name = "xrPictureBox7";
            this.xrPictureBox7.SizeF = new System.Drawing.SizeF(21.45831F, 23F);
            // 
            // xrPictureBox6
            // 
            this.xrPictureBox6.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox6.ImageSource"));
            this.xrPictureBox6.LocationFloat = new DevExpress.Utils.PointFloat(578.5417F, 49.25019F);
            this.xrPictureBox6.Name = "xrPictureBox6";
            this.xrPictureBox6.SizeF = new System.Drawing.SizeF(21.45831F, 23F);
            // 
            // xrPictureBox5
            // 
            this.xrPictureBox5.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox5.ImageSource"));
            this.xrPictureBox5.LocationFloat = new DevExpress.Utils.PointFloat(578.5417F, 10.00001F);
            this.xrPictureBox5.Name = "xrPictureBox5";
            this.xrPictureBox5.SizeF = new System.Drawing.SizeF(21.45831F, 23F);
            // 
            // xrPictureBox4
            // 
            this.xrPictureBox4.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox4.ImageSource"));
            this.xrPictureBox4.LocationFloat = new DevExpress.Utils.PointFloat(0F, 89.0001F);
            this.xrPictureBox4.Name = "xrPictureBox4";
            this.xrPictureBox4.SizeF = new System.Drawing.SizeF(21.45831F, 23F);
            // 
            // xrPictureBox3
            // 
            this.xrPictureBox3.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox3.ImageSource"));
            this.xrPictureBox3.LocationFloat = new DevExpress.Utils.PointFloat(2.083365F, 49.25019F);
            this.xrPictureBox3.Name = "xrPictureBox3";
            this.xrPictureBox3.SizeF = new System.Drawing.SizeF(21.45831F, 23F);
            // 
            // xrPictureBox2
            // 
            this.xrPictureBox2.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox2.ImageSource"));
            this.xrPictureBox2.LocationFloat = new DevExpress.Utils.PointFloat(1.041667F, 10.00001F);
            this.xrPictureBox2.Name = "xrPictureBox2";
            this.xrPictureBox2.SizeF = new System.Drawing.SizeF(21.45831F, 23F);
            // 
            // ReportHeader
            // 
            this.ReportHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel14,
            this.xrLine2,
            this.xrLine1,
            this.xrLabel3,
            this.xrLabel13});
            this.ReportHeader.HeightF = 68.99998F;
            this.ReportHeader.Name = "ReportHeader";
            this.ReportHeader.PageBreak = DevExpress.XtraReports.UI.PageBreak.BeforeBand;
            this.ReportHeader.SubBands.AddRange(new DevExpress.XtraReports.UI.SubBand[] {
            this.SubBand2});
            // 
            // SubBand2
            // 
            this.SubBand2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLine7,
            this.xrPanel1});
            this.SubBand2.HeightF = 183.7084F;
            this.SubBand2.Name = "SubBand2";
            // 
            // xrLine7
            // 
            this.xrLine7.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine7.LineWidth = 2F;
            this.xrLine7.LocationFloat = new DevExpress.Utils.PointFloat(1.041977F, 164.75F);
            this.xrLine7.Name = "xrLine7";
            this.xrLine7.SizeF = new System.Drawing.SizeF(1062F, 18.54172F);
            this.xrLine7.StylePriority.UseForeColor = false;
            // 
            // PageFooter
            // 
            this.PageFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPageInfo1,
            this.xrPictureBox18,
            this.xrLabel40});
            this.PageFooter.HeightF = 41.70855F;
            this.PageFooter.Name = "PageFooter";
            // 
            // xrPageInfo1
            // 
            this.xrPageInfo1.ExpressionBindings.AddRange(new DevExpress.XtraReports.UI.ExpressionBinding[] {
            new DevExpress.XtraReports.UI.ExpressionBinding("BeforePrint", "Text", "Page [PageNumber] of [PageCount]")});
            this.xrPageInfo1.Font = new DevExpress.Drawing.DXFont("Arial", 8F);
            this.xrPageInfo1.ForeColor = System.Drawing.Color.DarkCyan;
            this.xrPageInfo1.LocationFloat = new DevExpress.Utils.PointFloat(982.0001F, 3.178914E-05F);
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.RunningBand = this.GroupHeader1;
            this.xrPageInfo1.SizeF = new System.Drawing.SizeF(100F, 23F);
            this.xrPageInfo1.StylePriority.UseFont = false;
            this.xrPageInfo1.StylePriority.UseForeColor = false;
            this.xrPageInfo1.TextFormatString = "Page {0} of {1}";
            // 
            // GroupHeader1
            // 
            this.GroupHeader1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLine10,
            this.xrTable2,
            this.xrTable1});
            this.GroupHeader1.HeightF = 84.70815F;
            this.GroupHeader1.Level = 2;
            this.GroupHeader1.Name = "GroupHeader1";
            this.GroupHeader1.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // xrPictureBox18
            // 
            this.xrPictureBox18.BackColor = System.Drawing.Color.Empty;
            this.xrPictureBox18.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox18.ImageSource"));
            this.xrPictureBox18.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrPictureBox18.Name = "xrPictureBox18";
            this.xrPictureBox18.SizeF = new System.Drawing.SizeF(26.04166F, 23.00002F);
            this.xrPictureBox18.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            this.xrPictureBox18.StylePriority.UseBackColor = false;
            // 
            // xrLabel40
            // 
            this.xrLabel40.BackColor = System.Drawing.Color.White;
            this.xrLabel40.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9.75F, DevExpress.Drawing.DXFontStyle.Regular, DevExpress.Drawing.DXGraphicsUnit.Point, new DevExpress.Drawing.DXFontAdditionalProperty[] {
            new DevExpress.Drawing.DXFontAdditionalProperty("GdiCharSet", ((byte)(0)))});
            this.xrLabel40.ForeColor = System.Drawing.Color.Teal;
            this.xrLabel40.LocationFloat = new DevExpress.Utils.PointFloat(44.64099F, 0F);
            this.xrLabel40.Multiline = true;
            this.xrLabel40.Name = "xrLabel40";
            this.xrLabel40.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel40.SizeF = new System.Drawing.SizeF(412.9167F, 23F);
            this.xrLabel40.StylePriority.UseBackColor = false;
            this.xrLabel40.StylePriority.UseFont = false;
            this.xrLabel40.StylePriority.UseForeColor = false;
            this.xrLabel40.Text = "Please consider the environment before printing this report";
            // 
            // xrLine3
            // 
            this.xrLine3.ForeColor = System.Drawing.Color.Silver;
            this.xrLine3.LineWidth = 2F;
            this.xrLine3.LocationFloat = new DevExpress.Utils.PointFloat(0F, 32.99993F);
            this.xrLine3.Name = "xrLine3";
            this.xrLine3.SizeF = new System.Drawing.SizeF(1062F, 23.00002F);
            this.xrLine3.StylePriority.UseForeColor = false;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 55.99995F);
            this.xrLabel1.Multiline = true;
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(1064F, 23F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "WORKFLOW DETAILS";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLine6
            // 
            this.xrLine6.BorderColor = System.Drawing.Color.Black;
            this.xrLine6.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine6.LocationFloat = new DevExpress.Utils.PointFloat(0F, 55.99995F);
            this.xrLine6.Name = "xrLine6";
            this.xrLine6.SizeF = new System.Drawing.SizeF(446.0994F, 23F);
            this.xrLine6.StylePriority.UseBorderColor = false;
            this.xrLine6.StylePriority.UseForeColor = false;
            // 
            // xrLine8
            // 
            this.xrLine8.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine8.LocationFloat = new DevExpress.Utils.PointFloat(618.7238F, 55.99995F);
            this.xrLine8.Name = "xrLine8";
            this.xrLine8.SizeF = new System.Drawing.SizeF(444.3176F, 23F);
            this.xrLine8.StylePriority.UseForeColor = false;
            // 
            // xrPanel2
            // 
            this.xrPanel2.BackColor = System.Drawing.Color.WhiteSmoke;
            this.xrPanel2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLine13,
            this.xrLabel38,
            this.xrLine12,
            this.xrPictureBox17,
            this.xrPictureBox16,
            this.xrPictureBox15,
            this.xrPictureBox14,
            this.xrPictureBox13,
            this.xrPictureBox12,
            this.xrPictureBox11,
            this.xrPictureBox10,
            this.xrPictureBox9,
            this.xrLine9,
            this.xrLabel36,
            this.xrLabel35,
            this.xrLabel34,
            this.xrLabel33,
            this.xrLabel32,
            this.xrLabel31,
            this.xrLabel30,
            this.xrLabel22,
            this.xrLabel21,
            this.xrLabel20,
            this.xrLabel19,
            this.xrLabel18,
            this.xrLabel17,
            this.xrLabel16,
            this.xrLabel15,
            this.xrLabel12,
            this.xrLabel7,
            this.xrLabel2});
            this.xrPanel2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 78.9999F);
            this.xrPanel2.Name = "xrPanel2";
            this.xrPanel2.SizeF = new System.Drawing.SizeF(1060.958F, 247.7915F);
            this.xrPanel2.StylePriority.UseBackColor = false;
            // 
            // xrLine13
            // 
            this.xrLine13.BorderColor = System.Drawing.Color.Black;
            this.xrLine13.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine13.LocationFloat = new DevExpress.Utils.PointFloat(650.9899F, 224.7916F);
            this.xrLine13.Name = "xrLine13";
            this.xrLine13.SizeF = new System.Drawing.SizeF(407.8851F, 23F);
            this.xrLine13.StylePriority.UseBorderColor = false;
            this.xrLine13.StylePriority.UseForeColor = false;
            // 
            // xrLabel38
            // 
            this.xrLabel38.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F);
            this.xrLabel38.LocationFloat = new DevExpress.Utils.PointFloat(392.2606F, 224.7916F);
            this.xrLabel38.Multiline = true;
            this.xrLabel38.Name = "xrLabel38";
            this.xrLabel38.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel38.SizeF = new System.Drawing.SizeF(258.7293F, 23F);
            this.xrLabel38.StylePriority.UseFont = false;
            this.xrLabel38.StylePriority.UseTextAlignment = false;
            this.xrLabel38.Text = "WORKFLOW EXECUTION DETAILS";
            this.xrLabel38.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLine12
            // 
            this.xrLine12.BorderColor = System.Drawing.Color.Black;
            this.xrLine12.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine12.LocationFloat = new DevExpress.Utils.PointFloat(1.041667F, 224.7916F);
            this.xrLine12.Name = "xrLine12";
            this.xrLine12.SizeF = new System.Drawing.SizeF(391.219F, 23F);
            this.xrLine12.StylePriority.UseBorderColor = false;
            this.xrLine12.StylePriority.UseForeColor = false;
            // 
            // xrPictureBox17
            // 
            this.xrPictureBox17.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox17.ImageSource"));
            this.xrPictureBox17.LocationFloat = new DevExpress.Utils.PointFloat(576.4583F, 127.25F);
            this.xrPictureBox17.Name = "xrPictureBox17";
            this.xrPictureBox17.SizeF = new System.Drawing.SizeF(21.45831F, 23.00003F);
            this.xrPictureBox17.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox16
            // 
            this.xrPictureBox16.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox16.ImageSource"));
            this.xrPictureBox16.LocationFloat = new DevExpress.Utils.PointFloat(576.4583F, 167.4583F);
            this.xrPictureBox16.Name = "xrPictureBox16";
            this.xrPictureBox16.SizeF = new System.Drawing.SizeF(21.45831F, 23.00003F);
            this.xrPictureBox16.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox15
            // 
            this.xrPictureBox15.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox15.ImageSource"));
            this.xrPictureBox15.LocationFloat = new DevExpress.Utils.PointFloat(576.4583F, 49.79166F);
            this.xrPictureBox15.Name = "xrPictureBox15";
            this.xrPictureBox15.SizeF = new System.Drawing.SizeF(21.45831F, 23.00002F);
            this.xrPictureBox15.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox14
            // 
            this.xrPictureBox14.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox14.ImageSource"));
            this.xrPictureBox14.LocationFloat = new DevExpress.Utils.PointFloat(576.4583F, 89.37499F);
            this.xrPictureBox14.Name = "xrPictureBox14";
            this.xrPictureBox14.SizeF = new System.Drawing.SizeF(21.45831F, 23.00002F);
            this.xrPictureBox14.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox13
            // 
            this.xrPictureBox13.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox13.ImageSource"));
            this.xrPictureBox13.LocationFloat = new DevExpress.Utils.PointFloat(7.916641F, 127.25F);
            this.xrPictureBox13.Name = "xrPictureBox13";
            this.xrPictureBox13.SizeF = new System.Drawing.SizeF(25.00004F, 23.00003F);
            this.xrPictureBox13.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox12
            // 
            this.xrPictureBox12.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox12.ImageSource"));
            this.xrPictureBox12.LocationFloat = new DevExpress.Utils.PointFloat(576.4583F, 9.999969F);
            this.xrPictureBox12.Name = "xrPictureBox12";
            this.xrPictureBox12.SizeF = new System.Drawing.SizeF(21.45831F, 23.00004F);
            this.xrPictureBox12.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox11
            // 
            this.xrPictureBox11.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox11.ImageSource"));
            this.xrPictureBox11.LocationFloat = new DevExpress.Utils.PointFloat(7.916649F, 49.79165F);
            this.xrPictureBox11.Name = "xrPictureBox11";
            this.xrPictureBox11.SizeF = new System.Drawing.SizeF(25.00002F, 23.00002F);
            this.xrPictureBox11.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox10
            // 
            this.xrPictureBox10.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox10.ImageSource"));
            this.xrPictureBox10.LocationFloat = new DevExpress.Utils.PointFloat(7.916649F, 89.37499F);
            this.xrPictureBox10.Name = "xrPictureBox10";
            this.xrPictureBox10.SizeF = new System.Drawing.SizeF(25.00002F, 23.00002F);
            this.xrPictureBox10.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrPictureBox9
            // 
            this.xrPictureBox9.ImageSource = new DevExpress.XtraPrinting.Drawing.ImageSource("img", resources.GetString("xrPictureBox9.ImageSource"));
            this.xrPictureBox9.LocationFloat = new DevExpress.Utils.PointFloat(7.916649F, 10.00001F);
            this.xrPictureBox9.Name = "xrPictureBox9";
            this.xrPictureBox9.SizeF = new System.Drawing.SizeF(25.00003F, 23.00002F);
            this.xrPictureBox9.Sizing = DevExpress.XtraPrinting.ImageSizeMode.ZoomImage;
            // 
            // xrLine9
            // 
            this.xrLine9.LineDirection = DevExpress.XtraReports.UI.LineDirection.Vertical;
            this.xrLine9.LocationFloat = new DevExpress.Utils.PointFloat(492.1665F, 10.00004F);
            this.xrLine9.Name = "xrLine9";
            this.xrLine9.SizeF = new System.Drawing.SizeF(64.5834F, 204.6249F);
            // 
            // xrLabel36
            // 
            this.xrLabel36.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel36.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 167.4583F);
            this.xrLabel36.Multiline = true;
            this.xrLabel36.Name = "xrLabel36";
            this.xrLabel36.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel36.SizeF = new System.Drawing.SizeF(161.4585F, 23F);
            this.xrLabel36.StylePriority.UseFont = false;
            this.xrLabel36.StylePriority.UseTextAlignment = false;
            this.xrLabel36.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel35
            // 
            this.xrLabel35.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel35.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 127.25F);
            this.xrLabel35.Multiline = true;
            this.xrLabel35.Name = "xrLabel35";
            this.xrLabel35.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel35.SizeF = new System.Drawing.SizeF(161.4585F, 23F);
            this.xrLabel35.StylePriority.UseFont = false;
            this.xrLabel35.StylePriority.UseTextAlignment = false;
            this.xrLabel35.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel34
            // 
            this.xrLabel34.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel34.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 89.37499F);
            this.xrLabel34.Multiline = true;
            this.xrLabel34.Name = "xrLabel34";
            this.xrLabel34.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel34.SizeF = new System.Drawing.SizeF(161.4585F, 23F);
            this.xrLabel34.StylePriority.UseFont = false;
            this.xrLabel34.StylePriority.UseTextAlignment = false;
            this.xrLabel34.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel33
            // 
            this.xrLabel33.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel33.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 49.79165F);
            this.xrLabel33.Multiline = true;
            this.xrLabel33.Name = "xrLabel33";
            this.xrLabel33.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel33.SizeF = new System.Drawing.SizeF(161.4583F, 23F);
            this.xrLabel33.StylePriority.UseFont = false;
            this.xrLabel33.StylePriority.UseTextAlignment = false;
            this.xrLabel33.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel32
            // 
            this.xrLabel32.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel32.LocationFloat = new DevExpress.Utils.PointFloat(865.5834F, 9.999969F);
            this.xrLabel32.Multiline = true;
            this.xrLabel32.Name = "xrLabel32";
            this.xrLabel32.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel32.SizeF = new System.Drawing.SizeF(161.4583F, 23.00001F);
            this.xrLabel32.StylePriority.UseFont = false;
            this.xrLabel32.StylePriority.UseTextAlignment = false;
            this.xrLabel32.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel31
            // 
            this.xrLabel31.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel31.LocationFloat = new DevExpress.Utils.PointFloat(615.2084F, 167.4583F);
            this.xrLabel31.Multiline = true;
            this.xrLabel31.Name = "xrLabel31";
            this.xrLabel31.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel31.SizeF = new System.Drawing.SizeF(191.1873F, 23F);
            this.xrLabel31.StylePriority.UseFont = false;
            this.xrLabel31.StylePriority.UseTextAlignment = false;
            this.xrLabel31.Text = "LEGEND";
            this.xrLabel31.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel30
            // 
            this.xrLabel30.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel30.LocationFloat = new DevExpress.Utils.PointFloat(615.2084F, 127.25F);
            this.xrLabel30.Multiline = true;
            this.xrLabel30.Name = "xrLabel30";
            this.xrLabel30.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel30.SizeF = new System.Drawing.SizeF(191.1873F, 23F);
            this.xrLabel30.StylePriority.UseFont = false;
            this.xrLabel30.StylePriority.UseTextAlignment = false;
            this.xrLabel30.Text = "DR DataBase  Name/SID";
            this.xrLabel30.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel22
            // 
            this.xrLabel22.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel22.LocationFloat = new DevExpress.Utils.PointFloat(615.2084F, 89.37499F);
            this.xrLabel22.Multiline = true;
            this.xrLabel22.Name = "xrLabel22";
            this.xrLabel22.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel22.SizeF = new System.Drawing.SizeF(202.4739F, 23F);
            this.xrLabel22.StylePriority.UseFont = false;
            this.xrLabel22.StylePriority.UseTextAlignment = false;
            this.xrLabel22.Text = "Production DataBase Name/SID";
            this.xrLabel22.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel21
            // 
            this.xrLabel21.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel21.LocationFloat = new DevExpress.Utils.PointFloat(615.2084F, 49.79165F);
            this.xrLabel21.Multiline = true;
            this.xrLabel21.Name = "xrLabel21";
            this.xrLabel21.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel21.SizeF = new System.Drawing.SizeF(191.1873F, 23F);
            this.xrLabel21.StylePriority.UseFont = false;
            this.xrLabel21.StylePriority.UseTextAlignment = false;
            this.xrLabel21.Text = "DR Server Name";
            this.xrLabel21.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel20.LocationFloat = new DevExpress.Utils.PointFloat(615.2084F, 9.999969F);
            this.xrLabel20.Multiline = true;
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel20.SizeF = new System.Drawing.SizeF(191.1873F, 23.00001F);
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            this.xrLabel20.Text = "Production Server Name";
            this.xrLabel20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(346.0994F, 127.25F);
            this.xrLabel19.Multiline = true;
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(142.7084F, 23F);
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel18
            // 
            this.xrLabel18.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel18.LocationFloat = new DevExpress.Utils.PointFloat(346.0994F, 89.37498F);
            this.xrLabel18.Multiline = true;
            this.xrLabel18.Name = "xrLabel18";
            this.xrLabel18.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel18.SizeF = new System.Drawing.SizeF(142.7084F, 23F);
            this.xrLabel18.StylePriority.UseFont = false;
            this.xrLabel18.StylePriority.UseTextAlignment = false;
            this.xrLabel18.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel17
            // 
            this.xrLabel17.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel17.LocationFloat = new DevExpress.Utils.PointFloat(346.0994F, 49.79166F);
            this.xrLabel17.Multiline = true;
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel17.SizeF = new System.Drawing.SizeF(142.7083F, 23F);
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            this.xrLabel17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel16.LocationFloat = new DevExpress.Utils.PointFloat(346.0994F, 10.00004F);
            this.xrLabel16.Multiline = true;
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel16.SizeF = new System.Drawing.SizeF(142.7083F, 23F);
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            this.xrLabel16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(54.36457F, 127.25F);
            this.xrLabel15.Multiline = true;
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(127.0833F, 23F);
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            this.xrLabel15.Text = "DR IP/Host Name";
            this.xrLabel15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(54.36459F, 89.37499F);
            this.xrLabel12.Multiline = true;
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(127.0833F, 23F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "Production IP/Host Name";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(54.36456F, 49.79166F);
            this.xrLabel7.Multiline = true;
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(152.3021F, 23F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "Drill Action Type";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new DevExpress.Drawing.DXFont("Times New Roman", 9F);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(54.36459F, 9.999947F);
            this.xrLabel2.Multiline = true;
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(152.3021F, 23.00001F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "Drill Workflow Name";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel37
            // 
            this.xrLabel37.BackColor = System.Drawing.Color.LightSeaGreen;
            this.xrLabel37.BorderColor = System.Drawing.Color.Black;
            this.xrLabel37.Font = new DevExpress.Drawing.DXFont("Arial", 12F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrLabel37.ForeColor = System.Drawing.Color.White;
            this.xrLabel37.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrLabel37.Multiline = true;
            this.xrLabel37.Name = "xrLabel37";
            this.xrLabel37.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel37.SizeF = new System.Drawing.SizeF(1064F, 32.99993F);
            this.xrLabel37.StylePriority.UseBackColor = false;
            this.xrLabel37.StylePriority.UseBorderColor = false;
            this.xrLabel37.StylePriority.UseFont = false;
            this.xrLabel37.StylePriority.UseForeColor = false;
            this.xrLabel37.StylePriority.UseTextAlignment = false;
            this.xrLabel37.Text = "PARALLEL DR OPERATION DETAILS";
            this.xrLabel37.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTable3
            // 
            this.xrTable3.BackColor = System.Drawing.Color.LightGray;
            this.xrTable3.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTable3.LocationFloat = new DevExpress.Utils.PointFloat(1.041667F, 0F);
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 96F);
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.SizeF = new System.Drawing.SizeF(1058.875F, 25F);
            this.xrTable3.StylePriority.UseBackColor = false;
            this.xrTable3.StylePriority.UseFont = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell17,
            this.xrTableCell18,
            this.xrTableCell19,
            this.xrTableCell20,
            this.xrTableCell21});
            this.xrTableRow3.Name = "xrTableRow3";
            this.xrTableRow3.Weight = 1D;
            // 
            // xrTableCell17
            // 
            this.xrTableCell17.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell17.Multiline = true;
            this.xrTableCell17.Name = "xrTableCell17";
            this.xrTableCell17.StylePriority.UseFont = false;
            this.xrTableCell17.StylePriority.UseTextAlignment = false;
            this.xrTableCell17.Text = "Workflow Action Name";
            this.xrTableCell17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell17.Weight = 1.2464218523203554D;
            // 
            // xrTableCell18
            // 
            this.xrTableCell18.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell18.Multiline = true;
            this.xrTableCell18.Name = "xrTableCell18";
            this.xrTableCell18.StylePriority.UseFont = false;
            this.xrTableCell18.StylePriority.UseTextAlignment = false;
            this.xrTableCell18.Text = "Start Time";
            this.xrTableCell18.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell18.Weight = 1.056417968222138D;
            // 
            // xrTableCell19
            // 
            this.xrTableCell19.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell19.Multiline = true;
            this.xrTableCell19.Name = "xrTableCell19";
            this.xrTableCell19.StylePriority.UseFont = false;
            this.xrTableCell19.StylePriority.UseTextAlignment = false;
            this.xrTableCell19.Text = "End Time";
            this.xrTableCell19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell19.Weight = 1.0125800811213881D;
            // 
            // xrTableCell20
            // 
            this.xrTableCell20.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell20.Multiline = true;
            this.xrTableCell20.Name = "xrTableCell20";
            this.xrTableCell20.StylePriority.UseFont = false;
            this.xrTableCell20.StylePriority.UseTextAlignment = false;
            this.xrTableCell20.Text = "Total Time (hh:mm:ss)";
            this.xrTableCell20.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell20.Weight = 0.97535790140068057D;
            // 
            // xrTableCell21
            // 
            this.xrTableCell21.Font = new DevExpress.Drawing.DXFont("Times New Roman", 11F, DevExpress.Drawing.DXFontStyle.Bold);
            this.xrTableCell21.Multiline = true;
            this.xrTableCell21.Name = "xrTableCell21";
            this.xrTableCell21.StylePriority.UseFont = false;
            this.xrTableCell21.StylePriority.UseTextAlignment = false;
            this.xrTableCell21.Text = "Status";
            this.xrTableCell21.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell21.Weight = 0.71907898032450046D;
            // 
            // GroupHeader3
            // 
            this.GroupHeader3.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPanel2,
            this.xrLine8,
            this.xrLine6,
            this.xrLabel1,
            this.xrLine3,
            this.xrLabel37});
            this.GroupHeader3.HeightF = 326.7914F;
            this.GroupHeader3.Level = 1;
            this.GroupHeader3.Name = "GroupHeader3";
            // 
            // GroupHeader4
            // 
            this.GroupHeader4.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable3});
            this.GroupHeader4.HeightF = 25.25024F;
            this.GroupHeader4.Name = "GroupHeader4";
            // 
            // DRDrillReport
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.TopMargin,
            this.BottomMargin,
            this.Detail,
            this.GroupHeader1,
            this.ReportHeader,
            this.PageFooter,
            this.GroupHeader3,
            this.GroupHeader4});
            this.Font = new DevExpress.Drawing.DXFont("Arial", 9.75F);
            this.Landscape = true;
            this.Margins = new DevExpress.Drawing.DXMargins(10F, 8F, 63.54167F, 100F);
            this.PageHeight = 850;
            this.PageWidth = 1100;
            this.Version = "23.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.session1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        // Helper methods to access session data
        private string GetParallelDROpernId()
        {
            var session = HttpContext.Current.Session;
            string drOperationId = (session["ParaDROperId"] + " " + session["DROpeationStatus"]);
            return drOperationId;
        }

        private string ParaDROpercongi()
        {
            var session = HttpContext.Current.Session;
            string ParaDROpercongi = session["Config"] + " ";
            return ParaDROpercongi;
        }

        private string ParaDROperRPO()
        {
            var session = HttpContext.Current.Session;
            string ParaDROperRPO = session["RPO"] + " ";
            return ParaDROperRPO;
        }

        private string LoggedInUserName
        {
            get
            {
                var session = HttpContext.Current.Session;
                return session["LoggedUserName"]?.ToString() ?? "Unknown User";
            }
        }

        private User LoggedInUser
        {
            get
            {
                var session = HttpContext.Current.Session;
                var userId = session["UserId"];
                if (userId != null)
                {
                    return Facade.GetUserById(Convert.ToInt32(userId));
                }
                return new User { LoginName = "Unknown" };
            }
        }
    }
}
