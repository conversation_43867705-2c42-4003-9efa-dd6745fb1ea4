﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Master/BcmsDefault.Master" AutoEventWireup="true" CodeBehind="TelerikReportManagement.aspx.cs" Inherits="CP.UI.Report.DatalagReport" %>

<%@ Register Assembly="DevExpress.XtraReports.v23.1.Web.WebForms, Version=23.1.5.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" Namespace="DevExpress.XtraReports.Web" TagPrefix="dx" %>

<%@ Register TagPrefix="cc1" Namespace="AjaxControlToolkit" Assembly="AjaxControlToolkit" %>
<%@ Register Assembly="Telerik.Web.UI" Namespace="Telerik.Charting" TagPrefix="telerik" %>

<%@ Register Assembly="Telerik.ReportViewer.WebForms, Version=8.1.14.618, Culture=neutral, PublicKeyToken=a9d7983dfcc261be" Namespace="Telerik.ReportViewer.WebForms" TagPrefix="telerik" %>


<asp:Content ID="Content1" ContentPlaceHolderID="cphHead" runat="server">   
    <script src="../Script/jquery-3.7.1.min.js"></script>
    <script src="../Script/knockout-3.5.1.js"></script>
    <script src="../Script/EncryptDecrypt.js"></script>
    <link href="../App_Themes/CPTheme/chosen.css" rel="stylesheet" />
    <script src="../Script/chosen.jquery.js" type="text/javascript"></script>
    <style type="text/css">
        .bootstrap-select[class*="col"] .btn {
            width: 99% !important;
        }

        .col-md-6 {
            padding-right: 0px !important;
            width: 48.5% !important;
        }

        #ctl00_cphBody_ddldatabaseList_chosen {
            width: 468px !important;
        }
    </style>   
    <style>
        .chosen-select + .chosen-container {
            width: 48.5% !important;
            opacity: 1 !important;
        }

        .chosen-container .chosen-results {
            max-height: 300px;
        }

        /* DevExpress Document Viewer Styling */
        .dx-widget {
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif !important;
        }

        .dx-documentviewer {
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
        }

        .dx-documentviewer .dx-toolbar {
            background-color: #f8f9fa !important;
            border-bottom: 1px solid #dee2e6 !important;
        }

        /* Ensure DevExpress components don't conflict with Telerik */
        #ASPxWebDocumentViewer2 {
            z-index: 1000 !important;
            position: relative !important;
        }

        /* Fix any potential layout issues */
        .dx-documentviewer-content {
            background-color: #fff !important;
        }
    </style>
</asp:Content>

<asp:content id="Content2" contentplaceholderid="cphBody" runat="server">
    <div class="innerLR">
        <asp:UpdatePanel ID="UpdatePanel1" runat="server" UpdateMode="Conditional">
            <ContentTemplate>
                <div class="col-md-12">
                    <h3>
                        <img src="../Images/report-icon.png" alt="Report" />
                        Generate Reports</h3>
                </div>

                <div class="widget widget-heading-simple widget-body-white">
                    <div class="widget-body">
                        <div class="row">
                            <div class="col-md-12 form-horizontal uniformjs">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">
                                        Select Report Name
                                    </label>
                                    <div class="col-md-9">
                                        <asp:DropDownList ID="ddlReport" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" OnSelectedIndexChanged="ddlReport_SelectedIndexChanged" AutoPostBack="true">
                                            <%--<asp:ListItem Value="0">Select Report</asp:ListItem>
                                            <asp:ListItem Value="1">RPO SLA Report</asp:ListItem>
                                            <asp:ListItem Value="2">InfraObject Configuration Report</asp:ListItem>
                                            <asp:ListItem Value="3">InfraObject Summary Report</asp:ListItem>
                                            <asp:ListItem Value="4">Business Service Summary Report</asp:ListItem>
                                            <asp:ListItem Value="5">GlobalMirror CG Formation Report</asp:ListItem>
                                            <asp:ListItem Value="6">DataLag Status Report</asp:ListItem>
                                            <asp:ListItem Value="11">DR Drill Report</asp:ListItem>
                                            <asp:ListItem Value="12">Datasync Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="13">User Activity Report</asp:ListItem>
                                            <asp:ListItem Value="16">Application Discovery Report</asp:ListItem>
                                            <asp:ListItem Value="17">Application Dependency Report</asp:ListItem>
                                            <asp:ListItem Value="18">Email Report</asp:ListItem>
                                            <asp:ListItem Value="19">SMS Report</asp:ListItem>
                                            <asp:ListItem Value="20">VCenter Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="21">MultiServer Profile Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="22">CMDB Import Report</asp:ListItem>--%>

                                            <asp:ListItem Value="0">Select Report</asp:ListItem>
                                            <asp:ListItem Value="1">Application Dependency Report</asp:ListItem>
                                            <asp:ListItem Value="2">Application Discovery Report</asp:ListItem>
                                            <asp:ListItem Value="3">Business Service Summary Report</asp:ListItem>
                                            <asp:ListItem Value="4">CMDB Import Report</asp:ListItem>
                                            <asp:ListItem Value="5">DataLag Status Report</asp:ListItem>
                                            <asp:ListItem Value="6">Datasync Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="7">DR Drill Report</asp:ListItem>

                                            <asp:ListItem Value="8">Email Report</asp:ListItem>
                                            <asp:ListItem Value="9">GlobalMirror CG Formation Report</asp:ListItem>
                                            <asp:ListItem Value="10">InfraObject Configuration Report</asp:ListItem>
                                            <asp:ListItem Value="11">InfraObject Summary Report</asp:ListItem>
                                            <asp:ListItem Value="12">MultiServer Profile Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="13">RPO SLA Report</asp:ListItem>
                                            <asp:ListItem Value="17">RP Monitor Report</asp:ListItem>
                                            <asp:ListItem Value="14">SMS Report</asp:ListItem>
                                            <asp:ListItem Value="15">User Activity Report</asp:ListItem>
                                            <asp:ListItem Value="16">VCenter Monitor Report</asp:ListItem>



                                        </asp:DropDownList>
                                        <asp:RequiredFieldValidator ID="RfvReport" ControlToValidate="ddlReport" Text="*" InitialValue="0" runat="server" ErrorMessage="RequiredFieldValidator"></asp:RequiredFieldValidator>
                                    </div>
                                </div>


                                <asp:UpdatePanel ID="Updatepanel2" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlDatalagReport" runat="server" Visible="false">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Infra Object Name</label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlGroup" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" OnSelectedIndexChanged="ddlGroup_SelectedIndexChanged" AutoPostBack="True">
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="rfvddlGroup" runat="server" ForeColor="Red" ControlToValidate="ddlGroup" ValidationGroup="vlDatalagGroup" InitialValue="0" Display="Dynamic" ErrorMessage="Select Infra Object" SetFocusOnError="True">Select Infra Object</asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Start Date</label>
                                                <div class="col-md-9">
                                                    <cc1:CalendarExtender ID="CalendarExtender1" runat="server" TargetControlID="txtstart"
                                                        PopupButtonID="imgFromDate" PopupPosition="Right" Format="yyyy-MM-dd">
                                                    </cc1:CalendarExtender>
                                                    <asp:TextBox ID="txtstart" runat="server" CssClass="form-control" AutoPostBack="True" OnTextChanged="txtstart_TextChanged" onkeydown="return false"></asp:TextBox>
                                                    <asp:ImageButton ID="imgFromDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                    <%-- <img src="../images/icons/calendar-month.png" width="16" id="imgstrDate" style="margin-left: 5px;" />--%>
                                                    <asp:RequiredFieldValidator ID="rfvStart" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtstart" ValidationGroup="vlDatalagGroup"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="revStart" runat="server" ForeColor="Red" ErrorMessage="Choose Date" ValidationGroup="vlDatalagGroup"
                                                        ControlToValidate="txtstart" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    End Date</label>
                                                <div class="col-md-9">
                                                    <cc1:CalendarExtender ID="CalendarExtender2" runat="server" TargetControlID="txtEndlose"
                                                        Format="yyyy-MM-dd" PopupButtonID="imgBtnEndDate" PopupPosition="Right" />
                                                    <asp:TextBox ID="txtEndlose" runat="server" CssClass="form-control" Visible="true" OnTextChanged="txtEndlose_TextChanged" AutoPostBack="True" onkeydown="return false"></asp:TextBox>
                                                    <asp:ImageButton ID="imgBtnEndDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />

                                                    <%--<img src="../images/icons/calendar-month.png" width="16" id="imgEndDate" style="margin-left: 5px;" />--%>
                                                    <asp:RequiredFieldValidator ID="rfvEndDate" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtEndlose" ValidationGroup="vlDatalagGroup"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="revEndDate" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlDatalagGroup"
                                                        ControlToValidate="txtEndlose" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>

                                            <hr />
                                            <div class="form-actions row">
                                                <div id="divlable" class="col-xs-6" visible="false">
                                                    <asp:Label ID="lblMsg" runat="server" ForeColor="Red" Visible="false"></asp:Label>

                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnReport" runat="server" Text="View Report" Style="margin-left: 23px;" ValidationGroup="vlDatalagGroup" CssClass="btn btn-primary"
                                                        OnClick="BtnReportClick"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlDatalagRptViewer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">RPO SLA Report</h3>

                                                <hr class="margin-none margin-bottom" />
                                                <telerik:ReportViewer ID="RptVwrDatalag" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false" EnableViewState="false"></telerik:ReportViewer>
                                            </asp:Panel>

                                            <asp:Label ID="lblDatalagMsg" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>



                                <asp:UpdatePanel ID="Updatepanel14" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="Panelrpmonitor" runat="server" Visible="false">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Infra Object Name</label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlinfranme" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default">
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator15" runat="server" ForeColor="Red" ControlToValidate="ddlinfranme" ValidationGroup="vlDatalagGroup" InitialValue="0" Display="Dynamic" ErrorMessage="Select Infra Object" SetFocusOnError="True">Select Infra Object</asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Start Date</label>
                                                <div class="col-md-9">
                                                    <cc1:CalendarExtender ID="CalendarExtender9" runat="server" TargetControlID="txtstartdt"
                                                        PopupButtonID="ImageButton1" PopupPosition="Right" Format="yyyy-MM-dd">
                                                    </cc1:CalendarExtender>
                                                    <asp:TextBox ID="txtstartdt" runat="server" CssClass="form-control" AutoPostBack="True" OnTextChanged="txtstartdt_TextChanged" onkeydown="return false"></asp:TextBox>
                                                    <asp:ImageButton ID="ImageButton1" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                    <%-- <img src="../images/icons/calendar-month.png" width="16" id="imgstrDate" style="margin-left: 5px;" />--%>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator16" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtstartdt" ValidationGroup="vlDatalagGroup"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator7" runat="server" ForeColor="Red" ErrorMessage="Choose Date" ValidationGroup="vlDatalagGroup"
                                                        ControlToValidate="txtstartdt" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    End Date</label>
                                                <div class="col-md-9">
                                                    <cc1:CalendarExtender ID="CalendarExtender10" runat="server" TargetControlID="txtenddt"
                                                        Format="yyyy-MM-dd" PopupButtonID="ImageButton2" PopupPosition="Right" />
                                                    <asp:TextBox ID="txtenddt" runat="server" CssClass="form-control" Visible="true" OnTextChanged="txtenddt_TextChanged" AutoPostBack="True" onkeydown="return false"></asp:TextBox>
                                                    <asp:ImageButton ID="ImageButton2" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />

                                                    <%--<img src="../images/icons/calendar-month.png" width="16" id="imgEndDate" style="margin-left: 5px;" />--%>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator17" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtenddt" ValidationGroup="vlDatalagGroup"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator8" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlDatalagGroup"
                                                        ControlToValidate="txtenddt" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>

                                            <hr />
                                            <div class="form-actions row">
                                                <div id="divlable" class="col-xs-6" visible="false">
                                                    <asp:Label ID="lblmsgrp2" runat="server" ForeColor="Red" Visible="false"></asp:Label>

                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnviewRPMonitor" runat="server" Text="View Report" Style="margin-left: 23px;" ValidationGroup="vlDatalagGroup" CssClass="btn btn-primary" OnClick="btnviewRPMonitor_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="Panel_subrpmonitor" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">RP Monitoring Report</h3>

                                                <hr class="margin-none margin-bottom" />
                                                <telerik:ReportViewer ID="rptRPMonitor" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false" EnableViewState="false"></telerik:ReportViewer>
                                            </asp:Panel>

                                            <asp:Label ID="lblmsgrp" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>




                                <asp:UpdatePanel ID="Updatepanel4" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlInfraobjConfiguration" runat="server" Visible="false">
                                            <asp:Panel ID="pnlSubInfraObjConfi1" runat="server" class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Infra Object Name</label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlInfraObj" runat="server" OnSelectedIndexChanged="ddlInfraObj_SelectedIndexChanged" AutoPostBack="true" CssClass="chosen-select col-md-6" data-style="btn-default">
                                                    </asp:DropDownList>
                                                    <%--  <asp:RequiredFieldValidator ID="rfvInfraConfig" runat="server" CssClass="error" ControlToValidate="ddlInfraObj" ValidationGroup="vlGroupSite" InitialVale="0" Display="Dynamic" ErrorMessage="Select Infra Object" ></asp:RequiredFieldValidator>--%>
                                                    <asp:RequiredFieldValidator ID="rfvInfraConfig" runat="server" CssClass="error" ControlToValidate="ddlInfraObj" ValidationGroup="vlDatalagGroup" InitialValue="0" Display="Dynamic" ErrorMessage="Select Infra Object" SetFocusOnError="True">Select Infra Object</asp:RequiredFieldValidator>
                                                </div>
                                            </asp:Panel>
                                            <hr class="separator" />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnInfraObjConfiguration" runat="server" Style="margin-left: 23px;" Text="View Report" ValidationGroup="vlDatalagGroup" CssClass="btn btn-primary"
                                                        OnClick="btnInfraObjConfiguration_Click"></asp:Button>
                                                </div>
                                            </div>
                                            <asp:Panel ID="pnlSubInfraObjConfi2" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">Infraobject Configuration Report</h3>
                                                <hr class="margin-none margin-bottom" />
                                                <telerik:ReportViewer ID="RptVwrInfraObjConfiguration" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel5" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlInfraSummaryReport" runat="server" Visible="false">
                                            <asp:Panel ID="PnlBusService" runat="server" class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Select Business Service
                                                </label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlBusinessServ" runat="server" CssClass="chosen-select col-md-6" OnSelectedIndexChanged="ddlBusinessServ_SelectedIndexChanged" data-style="btn-default" AutoPostBack="True">
                                                    </asp:DropDownList>
                                                    <%--<asp:RequiredFieldValidator ID="rfvBusinessserv" runat="server" CssClass="error" ControlToValidate="ddlBusinessServ" ValidationGroup="vlGroupSite" Display="Dynamic" ErrorMessage="Select Infra Object" InitialValue="0"></asp:RequiredFieldValidator>--%>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnInfraView" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btnInfraView_Click"></asp:Button>
                                                </div>
                                            </div>
                                            <asp:Panel ID="PnlSumRptViewer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">InfraObject Summary Report</h3>
                                                <hr class="margin-none margin-bottom" />
                                                <telerik:ReportViewer ID="rptViewerInfraSummary" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblinfraSummsg" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel6" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlBusinessServSummaryReport" runat="server" Visible="false">
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnBsSerView" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" OnClick="btnBsSerView_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlBsReportViewer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">BusinessService Summary Report</h3>
                                                <hr class="margin-none margin-bottom" />
                                                <telerik:ReportViewer ID="rptvBsSummView" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblBusinessmsg" runat="server" Visible="false"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel7" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlGlobalMirrorCGFFormationRPT" runat="server" Visible="false">
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnGlobalMirrorCGF" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary"
                                                        OnClick="btnGlobalMirrorCGF_Click"></asp:Button>
                                                </div>
                                            </div>
                                            <asp:Panel ID="pnlSubGlobalMirror" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">GlobalMirror CG Formation Report</h3>
                                                <hr />
                                                <telerik:ReportViewer ID="RptVwrGlobalMirrorCGF" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                        </asp:Panel>
                                        <asp:Label ID="lblglbalMsg" ForeColor="Red" runat="server"></asp:Label>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel3" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlCurrentDatalagReport" runat="server" Visible="false">
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="BtnCurrentDatalagReport" OnClick="BtnCurrentDatalagReport_Click" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary"></asp:Button>
                                                </div>
                                            </div>
                                            <asp:Panel ID="pnlCurrentDatalagReportVwer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">DataLag Status Report</h3>

                                                <hr />
                                                <telerik:ReportViewer ID="RptVwrCurrentDatalag" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel8" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnl24HrsLogApplied" runat="server" Visible="false">
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btn24HrLogApplied" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary"
                                                        OnClick="btn24HrLogApplied_Click"></asp:Button>
                                                </div>
                                            </div>
                                            <asp:Panel ID="pnlSub24HrsLogApplied" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">DataLag 24 Hour Log Applied Report</h3>
                                                <hr />
                                                <telerik:ReportViewer ID="RptVwr24HrsLogApplied" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel9" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlDRHealthReport" runat="server" Visible="false">
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnHealthView" runat="server" Text="View Report" Style="margin-left: 23px;" OnClick="btnHealthView_Click" CssClass="btn btn-primary"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlHealthReportVwer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">24Hrs DRHealth Report</h3>
                                                <hr />
                                                <telerik:ReportViewer ID="RptVwrDRHelath" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>

                                            </asp:Panel>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel10" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlDRDrillReport" runat="server" Visible="false">
                                            <asp:Panel ID="pnlInfraDetails" runat="server" class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Infra Object Name</label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlInfra" runat="server" CssClass="chosen-select col-md-6" OnSelectedIndexChanged="ddlInfra_SelectedIndexChanged" data-style="btn-default" AutoPostBack="True">
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="rfvDrill" runat="server" CssClass="error" ControlToValidate="ddlInfra" ValidationGroup="vlGroupSite" Display="Dynamic" ErrorMessage="Select Infra Object" InitialValue="0"></asp:RequiredFieldValidator>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnDrillView" runat="server" Text="View Report" Style="margin-left: 23px;" ValidationGroup="vlGroupSite" OnClick="btnDrillView_Click" CssClass="btn btn-primary"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlDRDrillViewer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">DR-DRILL Report</h3>
                                                <telerik:ReportViewer ID="RptVwrDRdrill" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblDRDrillMsg" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel11" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlParallelDR" runat="server" Visible="true">
                                            <asp:Panel ID="pnlParallel" runat="server">


                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Start Date
                                                    </label>
                                                    <div class="col-md-9">

                                                        <cc1:CalendarExtender ID="ceDRDrillStartDate" runat="server" TargetControlID="txtDRDrillStartDate" PopupPosition="Right"
                                                            PopupButtonID="ImageButton3" Format="yyyy-MM-dd">
                                                        </cc1:CalendarExtender>
                                                        <asp:TextBox ID="txtDRDrillStartDate" runat="server" CssClass="form-control" AutoPostBack="true" OnTextChanged="txtDRDrillStartDate_TextChanged" onkeydown="return false"></asp:TextBox>

                                                        <asp:ImageButton ID="ImageButton3" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgdsyStartDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator18" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtDRDrillStartDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator9" runat="server" ForeColor="Red" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtDRDrillStartDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        End Date</label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="ceDRDrillEndDate" runat="server" TargetControlID="txtDRDrillEndDate" PopupPosition="Right"
                                                            Format="yyyy-MM-dd" PopupButtonID="ImageButton4" />
                                                        <asp:TextBox ID="txtDRDrillEndDate" runat="server" CssClass="form-control" Visible="true" AutoPostBack="true" onkeydown="return false" OnTextChanged="txtDRDrillEndDate_TextChanged"  > </asp:TextBox>
                                                        <asp:ImageButton ID="ImageButton4" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgdsyEndDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator19" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtDRDrillEndDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator10" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtDRDrillEndDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        ParallelDrOperation Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlParallelDrOperation" runat="server" OnSelectedIndexChanged="ddlParallelDrOperation_SelectedIndexChanged" CssClass="chosen-select col-md-6" AutoPostBack="true" data-style="btn-default">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="rfvParelledr" runat="server" CssClass="error" ControlToValidate="ddlParallelDrOperation" ValidationGroup="vlGroupSite" InitialValue="0" Display="Dynamic" ErrorMessage="Select ParallelDrOperation Name"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">

                                                <div id="divmessagedrdrill" class="col-xs-6" visible="false">
                                                    <asp:Label ID="lblmessagedrdrill" runat="server" ForeColor="Red" Visible="false"></asp:Label>

                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnParallel" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btnParallel_Click"></asp:Button>
                                                </div>
                                            </div>
                                            <asp:Panel ID="pnlSubParallel" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">DR Drill Report</h3>

                                                <hr />
                                               <dx:ASPxWebDocumentViewer ID="ASPxWebDocumentViewer2" runat="server" ReportSourceId="DRDrillReport" Width="100%" Height="900px"></dx:ASPxWebDocumentViewer>
                                            </asp:Panel>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel12" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlDataSyncMtr" runat="server" Visible="false">
                                            <asp:Panel ID="pnlDataSyncChild" runat="server">
                                                <div class="form-group">

                                                    <label class="col-md-3 control-label">
                                                        Infra Object Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddldataSyncInfraName" OnSelectedIndexChanged="ddldataSyncInfraName_SelectedIndexChanged" AutoPostBack="true" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="rfvDatasyncinfra" runat="server" CssClass="error" ControlToValidate="ddldataSyncInfraName" ValidationGroup="vlGroupSite" InitialValue="0" Display="Dynamic" ErrorMessage="Select Infra Object"></asp:RequiredFieldValidator>
                                                    </div>

                                                </div>
                                                <div class="form-group">

                                                    <label class="col-md-3 control-label">
                                                        Job Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddldatasyncJobName" runat="server" CssClass="selectpicker col-md-6" OnSelectedIndexChanged="ddldatasyncJobName_SelectedIndexChanged" Visible="true" data-style="btn-default">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="rfvDatasyncjob" runat="server" CssClass="error" ControlToValidate="ddldatasyncJobName" ValidationGroup="vlGroupSite" InitialValue="0" Display="Dynamic" ErrorMessage="Select Job"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Start Date
                                                    </label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="ceDataSyncStartDate" runat="server" TargetControlID="txtdataSyncStartDate" PopupPosition="Right"
                                                            PopupButtonID="imgdsyStartDate" Format="yyyy-MM-dd">
                                                        </cc1:CalendarExtender>
                                                        <asp:TextBox ID="txtdataSyncStartDate" runat="server" CssClass="form-control" AutoPostBack="true" OnTextChanged="txtdataSyncStartDate_TextChanged" onkeydown="return false"></asp:TextBox>

                                                        <asp:ImageButton ID="imgdsyStartDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgdsyStartDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="rfvDatasyncstartdate" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtdataSyncStartDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="revDatasyncstartdate" runat="server" ForeColor="Red" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtdataSyncStartDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        End Date</label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="ceDataSyncEndDate" runat="server" TargetControlID="txtdataSyncEndDate" PopupPosition="Right"
                                                            Format="yyyy-MM-dd" PopupButtonID="imgdsyEndDate" />
                                                        <asp:TextBox ID="txtdataSyncEndDate" runat="server" CssClass="form-control" Visible="true" AutoPostBack="true" OnTextChanged="txtdataSyncEndDate_TextChanged" onkeydown="return false"> </asp:TextBox>
                                                        <asp:ImageButton ID="imgdsyEndDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgdsyEndDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="rfvDatasyncenddate" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtdataSyncEndDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="revDatasyncenddate" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtdataSyncEndDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <hr />
                                                <div class="form-actions row">
                                                    <div id="div1" class="col-xs-6" visible="false">
                                                        <asp:Label ID="lblMsgDatasync" runat="server" ForeColor="Red" Visible="false"></asp:Label>

                                                    </div>
                                                    <div class="col-md-offset-6 col-md-6">
                                                        <asp:Button ID="btndatasyncView" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btndatasyncView_Click"></asp:Button>
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <asp:Panel ID="pnldsyViewer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">DataSync Monitoring Report</h3>
                                                <hr />
                                                <telerik:ReportViewer ID="rptdatasyncViewer" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblDsyMsg" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel13" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnluserActivity" runat="server" Visible="false">
                                            <asp:Panel ID="pnluserChild" runat="server">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        User Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlUserName" runat="server" CssClass="chosen-select col-md-6" OnSelectedIndexChanged="ddlUserName_SelectedIndexChanged" AutoPostBack="true" data-style="btn-default">
                                                        </asp:DropDownList>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Start Date
                                                    </label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="ceStartdate" runat="server" TargetControlID="txtStartDate" PopupPosition="Right"
                                                            PopupButtonID="imgStartDate" Format="yyyy-MM-dd">
                                                        </cc1:CalendarExtender>
                                                        <asp:TextBox ID="txtStartDate" runat="server" CssClass="form-control" Width="30%" AutoPostBack="true" OnTextChanged="txtStartDate_TextChanged" onkeydown="return false"></asp:TextBox>
                                                        <asp:ImageButton ID="imgStartDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgStartDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="rfvUserstartdate" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtStartDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="revUserstartdate" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtStartDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">End Date</label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="ceEndDate" runat="server" TargetControlID="txtEndDate" PopupPosition="Right"
                                                            Format="yyyy-MM-dd" PopupButtonID="imgEdDate" />
                                                        <asp:TextBox ID="txtEndDate" runat="server" CssClass="form-control" Visible="true" Width="30%" AutoPostBack="true" OnTextChanged="txtEndDate_txtEndDate" onkeydown="return false">
                                                        </asp:TextBox>
                                                        <asp:ImageButton ID="imgEdDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgEdDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="rfvUserenddate" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtEndDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="revUserenddate" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtEndDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div id="div2" class="col-xs-6" visible="false">
                                                    <asp:Label ID="lblMsgUser" runat="server" ForeColor="Red" Visible="false"></asp:Label>

                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnuserView" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btnuserView_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnluserViewer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">User Activity Report</h3>
                                                <telerik:ReportViewer ID="rptuserViewer" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblUsermsg" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel15" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="PanelSLA" runat="server" Visible="false">
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Infra Object Name</label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlinfraSLA" runat="server" CssClass="chosen-select col-md-6" data-style="btn-default" AutoPostBack="True">
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="rfvSLA" runat="server" CssClass="error" ControlToValidate="ddlinfraSLA" ValidationGroup="vlDatalagGroup" Display="Dynamic" ErrorMessage="Select Infra Object" InitialVale="0"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Report Type</label>
                                                <div class="col-md-9">
                                                    <asp:DropDownList ID="ddlslatype" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" OnSelectedIndexChanged="ddlslatype_SelectedIndexChanged" AutoPostBack="True">
                                                        <asp:ListItem Text="-Select Type-" Selected="True" Value="0"></asp:ListItem>
                                                        <asp:ListItem Text="Daily" Value="1"></asp:ListItem>
                                                        <asp:ListItem Text="Monthly" Value="2"></asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" CssClass="error" ControlToValidate="ddlslatype" ValidationGroup="vlDatalagGroup" Display="Dynamic" ErrorMessage="Select Type" InitialVale="0"></asp:RequiredFieldValidator>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    Start Date</label>
                                                <div class="col-md-9">
                                                    <cc1:CalendarExtender ID="CalendarExtender3" runat="server" TargetControlID="txtstartsla" PopupPosition="Right"
                                                        PopupButtonID="imgstrslaDate" Format="yyyy-MM-dd">
                                                    </cc1:CalendarExtender>
                                                    <asp:TextBox ID="txtstartsla" runat="server" CssClass="form-control" Width="30%" AutoPostBack="True" onkeydown="return false"></asp:TextBox>
                                                    <asp:ImageButton ID="imgstrslaDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                    <%--<img src="../images/icons/calendar-month.png" width="16" id="imgstrslaDate" style="margin-left: 5px;" />--%>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator2" ForeColor="Red" runat="server" ErrorMessage="*" ControlToValidate="txtstartsla" ValidationGroup="vlDatalagGroup"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator1" runat="server" ForeColor="Red" ErrorMessage="Choose Date" ValidationGroup="vlDatalagGroup"
                                                        ControlToValidate="txtstartsla" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label">
                                                    End Date</label>
                                                <div class="col-md-9">
                                                    <cc1:CalendarExtender ID="CalendarExtender4" runat="server" TargetControlID="txtEndlosesla" PopupPosition="Right"
                                                        Format="yyyy-MM-dd" PopupButtonID="imgSlaEndDate" />
                                                    <asp:TextBox ID="txtEndlosesla" runat="server" CssClass="form-control" Visible="true" Width="30%" AutoPostBack="True" onkeydown="return false"></asp:TextBox>
                                                    <asp:ImageButton ID="imgSlaEndDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                    <%--<img src="../images/icons/calendar-month.png" width="16" id="imgSlaEndDate" style="margin-left: 5px;" />--%>
                                                    <asp:RequiredFieldValidator ID="RequiredFieldValidator3" ForeColor="Red" runat="server" ErrorMessage="*" ControlToValidate="txtEndlosesla" ValidationGroup="vlDatalagGroup"></asp:RequiredFieldValidator>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator2" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlDatalagGroup"
                                                        ControlToValidate="txtEndlosesla" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                </div>
                                            </div>

                                            <hr />
                                            <div class="form-actions row">
                                                <div id="div3" class="col-xs-6" visible="false">
                                                    <asp:Label ID="Lblsla" runat="server" ForeColor="Red" Visible="false"></asp:Label>

                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnslaview" runat="server" Text="View Report" Style="margin-left: 23px;" ValidationGroup="vlDatalagGroup" CssClass="btn btn-primary"
                                                        OnClick="btnslaview_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="Panel2SLA" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">SLA Report</h3>
                                                <hr class="margin-none margin-bottom" />
                                                <telerik:ReportViewer ID="ReportViewerSLA" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="true"></telerik:ReportViewer>
                                            </asp:Panel>

                                            <asp:Label ID="Label2sla" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>


                                <asp:UpdatePanel ID="Updatepanel16" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="PnlAppDiscovery" runat="server" Visible="false">
                                            <asp:Panel ID="Panel2" runat="server">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Discovery Profile Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddldiscoveryprofile" runat="server" CssClass="selectpicker col-md-6" AutoPostBack="true" data-style="btn-default" OnSelectedIndexChanged="ddldiscoveryprofile_SelectedIndexChanged">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RfvddlDiscoveryProf" runat="server" Display="Dynamic" CssClass="error" ErrorMessage="Select Profile Name" ControlToValidate="ddldiscoveryprofile" InitialValue="0" ValidationGroup="AppDisc"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnAppDiscovery" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" OnClick="btnAppDiscovery_Click" ValidationGroup="AppDisc"></asp:Button>
                                                </div>
                                            </div>
                                            <asp:Panel ID="pnlRptAppDiscovery" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">Application Discovery Report</h3>
                                                <hr />
                                                <telerik:ReportViewer ID="RptAppDiscovery" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblAppDescovery" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                                <asp:UpdatePanel ID="Updatepanel17" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlAppDependancy" runat="server" Visible="false">
                                            <asp:Panel ID="Panel4" runat="server">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Dependancy Profile Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddldependencyprofile" runat="server" OnSelectedIndexChanged="ddldependencyprofile_SelectedIndexChanged"
                                                            CssClass="selectpicker col-md-6" AutoPostBack="true" data-style="btn-default">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator4" runat="server" CssClass="error" ControlToValidate="ddldependencyprofile"
                                                            ValidationGroup="vlAppDependancy" InitialValue="0" Display="Dynamic" ErrorMessage="Select Profile Name"></asp:RequiredFieldValidator>

                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Source Host</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlsourcehost" runat="server" CssClass="selectpicker col-md-6" AutoPostBack="true" data-style="btn-default" OnSelectedIndexChanged="ddlsourcehost_electedIndexChanged">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator5" runat="server" CssClass="error" ControlToValidate="ddlsourcehost"
                                                            ValidationGroup="vlAppDependancy" InitialValue="0" Display="Dynamic" ErrorMessage="Select Source Host"></asp:RequiredFieldValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnAppDependancy" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" OnClick="btnAppDependancy_Click" ValidationGroup="vlAppDependancy"></asp:Button>
                                                </div>
                                            </div>
                                            <asp:Panel ID="pnlRptAppDependancy" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">Application Dependency Report</h3>
                                                <hr />
                                                <telerik:ReportViewer ID="RptAppDependancy" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblappDependancy" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                                <asp:UpdatePanel ID="Updatepanel18" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlEmailReport" runat="server" Visible="false">
                                            <asp:Panel ID="Panel3" runat="server">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Start Date
                                                    </label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="CalendarExtender5" runat="server" TargetControlID="txtEmailStartDate" PopupPosition="Right"
                                                            PopupButtonID="imgEmailStrtDate" Format="yyyy-MM-dd">
                                                        </cc1:CalendarExtender>
                                                        <asp:TextBox ID="txtEmailStartDate" runat="server" CssClass="form-control" Width="30%" AutoPostBack="true" OnTextChanged="txtEmailStartDate_TextChanged" onkeydown="return false"></asp:TextBox>
                                                        <asp:ImageButton ID="imgEmailStrtDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgEmailStrtDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator6" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtEmailStartDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator3" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtEmailStartDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">End Date</label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="CalendarExtender6" runat="server" TargetControlID="txtEmailEndDate" PopupPosition="Right"
                                                            Format="yyyy-MM-dd" PopupButtonID="imgEmailEndDate" />
                                                        <asp:TextBox ID="txtEmailEndDate" runat="server" CssClass="form-control" Visible="true" Width="30%" AutoPostBack="true" OnTextChanged="txtEmailEndDate_TextChanged" onkeydown="return false">
                                                        </asp:TextBox>
                                                        <asp:ImageButton ID="imgEmailEndDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgEmailEndDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator7" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtEmailEndDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator4" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtEmailEndDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div id="div4" class="col-xs-6" visible="false">
                                                    <asp:Label ID="lblEmailReport" runat="server" ForeColor="Red" Visible="false"></asp:Label>

                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnEmailReport" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btnEmailReport_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlSubEmail" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">Email Report</h3>
                                                <telerik:ReportViewer ID="rptEmailReport" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="Label2" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                                <asp:UpdatePanel ID="Updatepanel19" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlSMSReport" runat="server" Visible="false">
                                            <asp:Panel ID="panel1" runat="server">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Start Date
                                                    </label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="CalendarExtender7" runat="server" TargetControlID="txtSMSStartDate" PopupPosition="Right"
                                                            PopupButtonID="imgSMSStrtDate" Format="yyyy-MM-dd">
                                                        </cc1:CalendarExtender>
                                                        <asp:TextBox ID="txtSMSStartDate" runat="server" CssClass="form-control" Width="30%" AutoPostBack="true" OnTextChanged="txtSMSStartDate_TextChanged" onkeydown="return false"></asp:TextBox>
                                                        <asp:ImageButton ID="imgSMSStrtDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgSMSStrtDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator8" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtSMSStartDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator5" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtSMSStartDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">End Date</label>
                                                    <div class="col-md-9">
                                                        <cc1:CalendarExtender ID="CalendarExtender8" runat="server" TargetControlID="txtSMSEndDate" PopupPosition="Right"
                                                            Format="yyyy-MM-dd" PopupButtonID="imgSMSEndDate" />
                                                        <asp:TextBox ID="txtSMSEndDate" runat="server" CssClass="form-control" Visible="true" Width="30%" AutoPostBack="true" OnTextChanged="txtSMSEndDate_TextChanged" onkeydown="return false">
                                                        </asp:TextBox>
                                                        <asp:ImageButton ID="imgSMSEndDate" runat="server" ImageUrl="../images/icons/calendar-month.png" CssClass="vertical-middle" />
                                                        <%--<img src="../images/icons/calendar-month.png" width="16" id="imgSMSEndDate" style="margin-left: 5px;" />--%>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator9" ForeColor="Red" runat="server" ErrorMessage="Select Date" ControlToValidate="txtSMSEndDate" ValidationGroup="vlGroupSite"></asp:RequiredFieldValidator>
                                                        <asp:RegularExpressionValidator ID="RegularExpressionValidator6" ForeColor="Red" runat="server" ErrorMessage="Choose Date" ValidationGroup="vlGroupSite"
                                                            ControlToValidate="txtSMSEndDate" ValidationExpression="^\d{4}[\-\/\s]?((((0[13578])|(1[02]))[\-\/\s]?(([0-2][0-9])|(3[01])))|(((0[469])|(11))[\-\/\s]?(([0-2][0-9])|(30)))|(02[\-\/\s]?[0-2][0-9]))$"></asp:RegularExpressionValidator>
                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div id="div5" class="col-xs-6" visible="false">
                                                    <asp:Label ID="lblSMSReport" runat="server" ForeColor="Red" Visible="false"></asp:Label>

                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnSMSReport" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btnSMSReport_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlSubSMS" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">SMS Report</h3>
                                                <telerik:ReportViewer ID="RptSMSVwr" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="Label3" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                                <asp:UpdatePanel ID="Updatepanel20" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlVcenter" runat="server" Visible="false">

                                            <asp:Panel ID="panel6" runat="server">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Profile Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlVcenProfileName" runat="server" CssClass="chosen-select col-md-6" AutoPostBack="true" data-style="btn-default">
                                                        </asp:DropDownList>
                                                        <asp:CheckBox ID="chkall" runat="server" />
                                                        <label>ALL</label>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator10" runat="server" CssClass="error" ControlToValidate="ddlVcenProfileName"
                                                            ValidationGroup="vlGroupSite" InitialValue="0" Display="Dynamic" ErrorMessage="Select Profile Name"></asp:RequiredFieldValidator>

                                                    </div>
                                                </div>
                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div id="div6" class="col-xs-6" visible="false">
                                                    <%-- <asp:Label ID="Label1" runat="server" ForeColor="Red" Visible="false"></asp:Label>--%>
                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnVcenter" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btnVcenter_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlSubVcenter" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">VCenter Monitor Report</h3>
                                                <telerik:ReportViewer ID="RptVcenter" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblVcenter" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                                <asp:UpdatePanel ID="Updatepanel21" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlMultiServer" runat="server" Visible="false">

                                            <asp:Panel ID="panel7" runat="server">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Profile Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlMultiserver" runat="server" CssClass="chosen-select col-md-6" AutoPostBack="true" data-style="btn-default" OnSelectedIndexChanged="ddlMultiserver_SelectedIndexChanged">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator11" runat="server" CssClass="error" ControlToValidate="ddlMultiserver"
                                                            ValidationGroup="vlGroupSite" InitialValue="0" Display="Dynamic" ErrorMessage="Select Profile Name"></asp:RequiredFieldValidator>

                                                    </div>
                                                </div>

                                                <div class="form-group" runat="server" id="lblostype" visible="false">
                                                    <label class="col-md-3 control-label">
                                                        OS Type <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlostype" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" Visible="false"
                                                            AutoPostBack="True">
                                                            <asp:ListItem Value="0">Select OS Type</asp:ListItem>
                                                            <asp:ListItem>AIX</asp:ListItem>
                                                            <asp:ListItem>HPUX</asp:ListItem>
                                                            <asp:ListItem>Linux</asp:ListItem>
                                                            <asp:ListItem>Solaris</asp:ListItem>
                                                            <asp:ListItem>Windows</asp:ListItem>
                                                        </asp:DropDownList>

                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator12" runat="server" CssClass="error" ControlToValidate="ddlostype"
                                                            ValidationGroup="vlGroupSite" InitialVale="0" Display="Dynamic" ErrorMessage="Select OS Type"></asp:RequiredFieldValidator>

                                                        <asp:Label ID="Label1" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
                                                    </div>
                                                </div>

                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div id="div7" class="col-xs-6" visible="false">
                                                    <%--   <asp:Label ID="Label4" runat="server" ForeColor="Red" Visible="false"></asp:Label>--%>
                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnMultiserver" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btnMultiserver_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlSubMultiServer" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">MultiServer Profile Monitor Report</h3>
                                                <telerik:ReportViewer ID="RptMultiServer" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblMultiServer" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                                <asp:UpdatePanel ID="Updatepanel22" runat="server" UpdateMode="Conditional" ChildrenAsTriggers="True">
                                    <ContentTemplate>
                                        <asp:Panel ID="pnlCMDBImport" runat="server" Visible="false">

                                            <asp:Panel ID="panel8" runat="server">
                                                <div class="form-group">
                                                    <label class="col-md-3 control-label">
                                                        Excel File Name</label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddlCMDBExcelFile" runat="server" CssClass="selectpicker col-md-6" AutoPostBack="true" data-style="btn-default" OnSelectedIndexChanged="ddlCMDBExcelFile_SelectedIndexChanged">
                                                        </asp:DropDownList>
                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator13" runat="server" CssClass="error" ControlToValidate="ddlCMDBExcelFile"
                                                            ValidationGroup="vlGroupSite" InitialValue="0" Display="Dynamic" ErrorMessage="Select Excel File"></asp:RequiredFieldValidator>

                                                    </div>
                                                </div>

                                                <div id="Div8" class="form-group" runat="server">
                                                    <label class="col-md-3 control-label">
                                                        Generation Timestamp <span class="inactive">*</span>
                                                    </label>
                                                    <div class="col-md-9">
                                                        <asp:DropDownList ID="ddltime" runat="server" CssClass="selectpicker col-md-6" data-style="btn-default" AutoPostBack="True"></asp:DropDownList>

                                                        <asp:RequiredFieldValidator ID="RequiredFieldValidator14" runat="server" CssClass="error" ControlToValidate="ddltime"
                                                            ValidationGroup="vlGroupSite" Display="Dynamic" ErrorMessage="Select Timestamp" InitialValue="0"></asp:RequiredFieldValidator>

                                                        <asp:Label ID="Label4" runat="server" Text="" Visible="false" ForeColor="Red"></asp:Label>
                                                    </div>
                                                </div>

                                            </asp:Panel>
                                            <hr />
                                            <div class="form-actions row">
                                                <div id="div9" class="col-xs-6" visible="false">
                                                    <%--   <asp:Label ID="Label4" runat="server" ForeColor="Red" Visible="false"></asp:Label>--%>
                                                </div>
                                                <div class="col-md-offset-6 col-md-6">
                                                    <asp:Button ID="btnCMDBExcelFile" runat="server" Text="View Report" Style="margin-left: 23px;" CssClass="btn btn-primary" ValidationGroup="vlGroupSite" OnClick="btnCMDBExcelFile_Click"></asp:Button>
                                                </div>
                                            </div>

                                            <asp:Panel ID="pnlSubCMDBImport" runat="server" Visible="false">
                                                <hr />
                                                <h3 class="margin-none padding-none">MultiServer Profile Monitor Report</h3>
                                                <telerik:ReportViewer ID="rptCMDBImport" runat="server" Width="100%" Height="900" Skin="Default" ParametersAreaVisible="false"></telerik:ReportViewer>
                                            </asp:Panel>
                                            <asp:Label ID="lblCMDBExcelFile" runat="server" ForeColor="Red"></asp:Label>
                                        </asp:Panel>
                                    </ContentTemplate>
                                </asp:UpdatePanel>

                            </div>
                        </div>
                    </div>
                </div>
            </ContentTemplate>
        </asp:UpdatePanel>


    </div>
     <script src="../Script/chosen.jquery.js"></script>
    <script>
        $(document).ready(function () {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        });
        function pageLoad() {
            $(".chosen-select, .chosen-select_drop").chosen({ search_contains: true });
        }
    </script>
</asp:content>
