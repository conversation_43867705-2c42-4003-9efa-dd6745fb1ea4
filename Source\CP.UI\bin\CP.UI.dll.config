﻿<?xml version="1.0"?>
<configuration>
  <configSections>
    <sectionGroup name="system.web.extensions" type="System.Web.Configuration.SystemWebExtensionsSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
      <sectionGroup name="scripting" type="System.Web.Configuration.ScriptingSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">        
        <section name="scriptResourceHandler" type="System.Web.Configuration.ScriptingScriptResourceHandlerSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="MachineToApplication"/>
        <sectionGroup name="webServices" type="System.Web.Configuration.ScriptingWebServicesSectionGroup, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35">
          <section name="jsonSerialization" type="System.Web.Configuration.ScriptingJsonSerializationSection, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" requirePermission="false" allowDefinition="Everywhere"/>
        </sectionGroup>
      </sectionGroup>
    </sectionGroup>
    <section name="cacheConfiguration" type="CP.CacheController.CacheConfiguration, CP.CacheController, Version=*******, Culture=neutral" allowLocation="true" allowDefinition="Everywhere"/>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler,log4net"/>
    <section name="dataConfiguration" type="Microsoft.Practices.EnterpriseLibrary.Data.Configuration.DatabaseSettings, Microsoft.Practices.EnterpriseLibrary.Data"/>
    <section name="dataAccessFactoryConfiguration" type="CP.DataAccess.Configuration.ConfigurationSectionHandler, CP.IDataAccess, Version=*******, Culture=neutral"/>
    <section name="bcms" type="System.Configuration.NameValueSectionHandler, System, Version=1.0.5000.0,Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
    <sectionGroup name="common">
      <section name="logging" type="Common.Logging.ConfigurationSectionHandler, Common.Logging"/>
    </sectionGroup>
    <section name="devexpress" type="DevExpress.Web.Config.WebConfigurationSection, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
  </configSections>
  <appSettings>
    <add key="ISIFRAME" value="true"/>
    <add key="IsSecurityTokenEnabled" value="true"/>
    <add key="BcmsVersion" value="1.4.1.001"/>
    <add key="CompanyId" value="29"/>
    <add key="AssertionConsumerServiceURL1" value="http://ptech-prod-15/Test1/SAML/AssertionConsumerService.aspx"/>
    <add key="SPTargetURL1" value="http://ptech-prod-15/Test1/default.aspx"/>
    <add key="AssertionConsumerServiceURL2" value="http://ptech-prod-15/SSO_SP_WA/SAML/AssertionConsumerService.aspx"/>
    <add key="SPTargetURL2" value="http://ptech-prod-15/SSO_SP_WA/"/>
    <add key="AssertionConsumerServiceURL3" value="http://ptech-prod-14/kkr/SAML/AssertionConsumerService.aspx"/>
    <add key="SPTargetURL3" value="http://ptech-prod-14/kkr/default.aspx"/>
    <add key="AssertionConsumerServiceURL4" value="http://ptech-prod-03/ssosp/SAML/AssertionConsumerService.aspx"/>
    <add key="SPTargetURL4" value="http://ptech-prod-03/ssosp/default.aspx"/>
    <add key="AssertionConsumerServiceURL5" value="https://login.salesforce.com"/>
    <add key="SPTargetURL5" value="https://login.salesforce.com"/>
    <add key="AssertionConsumerServiceURL6" value="http://ptech-prod-03/ATP_SSO_SP_TEST/ConsumerService.aspx"/>
    <add key="SPTargetURL6" value="http://ptech-prod-03/ATP_SSO_SP_TEST/"/>
    <add key="CertificateIssuer" value="Perpetuuiti"/>
    <!--<add key="EntityId" value="https://saml.salesforce.com" />-->
    <add key="AudienceURI" value="https://saml.salesforce.com"/>
    <add key="LoginID" value="<EMAIL>"/>
    <add key="DBRoleName" value="CP1."/>
    <add key="CIOReportPath" value="E:/July-16/CP_Root_Graph/Source/CP.UI/ExcelFiles/"/>
    <add key="ReportPath" value="/CP"/>
    <add key="PwdKey" value="(?=^.{2,8}$)(?=(?:.*?\d){2})(?=.*[a-z]{3})(?=(?:.*?[A-Z]){2})(?=(?:.*?[!@#$%*()_+^&amp;}{:;?.]){1})(?!.*\s)[0-9a-zA-Z!@#$%*()_+^&amp;]*$"/>
    <add key="PwdAge" value="30"/>
    <add key="PwdReminderDay" value="5"/>
    <!--Keys required for RPO calculation  -->
    <add key="StaticStartTime" value="2014-04-07 14:31:05"/>
    <add key="HourDiff" value="1"/>
    <add key="Version" value="version 4.5.0"/>
    <add key="IsCaptchaRequired" value="false"/>
    <add key="ExecuteCPLPath" value="C:\Program Files (x86)\Perpetuuiti\ParallelWorkFlow\CPSL Grammer.cgt"/>
    <add key="NmapPath" value="C:\nmap-6.47-win32\nmap-6.47"/>
    <add key="ProviderName" value="SNMP"/>
    <add key="IsOSDetail" value="0"/>
    <add key="IsCompanyId" value="false"/>
    <add key="IsMultiuser" value="true"/>
    <add key="SessionCookiesPath" value="/"/>
    <add key="SessionCookiesDomain" value="CP"/>
    <add key="IsADUser" value="true"/>
    <add key="LogNumberOfDays" value="7"/>
    <add key="DeactivateKey" value="60"/>
    <add key="ActionENV" value="200"/>
    <add key="ViewUser" value="false"/>
    <add key="LoginMethod" value="CP"/>
    <add key="PartnerIdP" value="http://sts.icicigroupcompanies.com/adfs/services/trust"/>
    <add key="NotificationEmail" value="<EMAIL>,<EMAIL>,<EMAIL>"/>
    <add key="creationwindow" value="3"/>
    <!--<add key="LogWaitTime" value="240"/>-->
    <add key="EnableWorkflowHistory" value="true"/>
  </appSettings>
  <connectionStrings>
    <!--<add name="CPConnectionString" connectionString="0057mxtL6XYvihAXwaZvf6F83EOpi3/GU4XV/BOa117aPzRpzqNzmOVtaaluOw85DdZdgbv/lJGvwumo9QRS80wtTUz0BEB31HFKGp1wCu5yon8i2OaM6moAMA2+gnWQgCX6H7GxgsYyL/uqxdqGLKXXy3j/YxmRuUQy9t+0g5F0clt6gbxjD9zdrAgpu+tYp3lr61NypRU="/>-->
    <!--ajit Bakcup-->
    <add name="CPConnectionString" connectionString="yYbf53JMCZLZZ37lPWgxiAaXevbSCQcbqMhjY1wUfS59o9nHSilZ6Mk8WP1cIKxj8jDchbxYKaULl4wuFzR3kx7DvASGfHJT5fRjZ92UI4IvWYDs8aANkvwha20D+N7M"/>
    <!--<add name="CPConnectionString" connectionString="yYbf53JMCZLZZ37lPWgxiAFWvQNdp5KWF6y4VKMkzfnJFWWwyD+pycc8Jh0Pzqm8Rup7+amv3G4MiGtcvsP/VofguziPSR1BsBCLXnwDlyc="/>-->
    <!--<add name="CPConnectionString" connectionString="yYbf53JMCZLZZ37lPWgxiAaXevbSCQcbqMhjY1wUfS59o9nHSilZ6Mk8WP1cIKxj8jDchbxYKaULl4wuFzR3kx7DvASGfHJTqprs+oqKQV4="/>-->
    <!--QC-->
    <!--<add name="CPConnectionString" connectionString="yYbf53JMCZLZZ37lPWgxiAaXevbSCQcbqMhjY1wUfS59o9nHSilZ6Mk8WP1cIKxj8jDchbxYKaULl4wuFzR3kx7DvASGfHJT5fRjZ92UI4IvWYDs8aANkvwha20D+N7M"/>-->
    <!--<add name="CPConnectionString" connectionString="yYbf53JMCZJBaM9sbxQD6xOFRyDR/sYIY3z1RLtNf3PzG9JEvw2nMHlXB/fn2UJRuZUmI6JCjZttv3eYs98S8FOdTYuuWZ3uvf1jzZL2SNc=" />-->
  </connectionStrings>
  <log4net>
    <appender name="RollingLogFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="C:\\CP\\Web\\CpActivityLog.log"/>
      <appendToFile value="true"/>
      <rollingStyle value="Composite"/>
      <datePattern value="yyyyMMdd"/>
      <maxSizeRollBackups value="20"/>
      <maximumFileSize value="10MB"/>
      <staticLogFileName value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline"/>
        <!--<conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline" />-->
      </layout>
    </appender>
    <appender name="TestCPLScriptEngineLog" type="log4net.Appender.RollingFileAppender">
      <file value="C:\\CP\CheckCPLScriptEngineLog.log"/>
      <appendToFile value="true"/>
      <rollingStyle value="Composite"/>
      <datePattern value="yyyyMMdd"/>
      <maxSizeRollBackups value="20"/>
      <maximumFileSize value="10MB"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level - %message%newline"/>
      </layout>
    </appender>
    <root>
      <level value="ALL"/>
      <appender-ref ref="RollingLogFileAppender"/>
    </root>
    <logger additivity="false" name="TestCPLScriptEngineLog">
      <level value="ALL"/>
      <appender-ref ref="TestCPLScriptEngineLog"/>
    </logger>
  </log4net>
  <cacheConfiguration enabled="true">
    <!-- 
            If the cache duration is not supplied, this duration(in seconds) is used as cache expiration policy.
        -->
    <dataCache enabled="true" defaultDuration="60"/>
    <pageCache enabled="true" defaultDuration="60"/>
  </cacheConfiguration>
  <dataConfiguration defaultDatabase="CPConnectionString"/>
  <dataAccessFactoryConfiguration>
    <dataAccessFactory typeName="CP.DataAccess.DataAccessFactory" assemblyName="CP.DataAccess, Version=*******, Culture=neutral"/>
  </dataAccessFactoryConfiguration>
  <system.data>
    <!--<DbProviderFactories>
      <remove invariant="Oracle.DataAccess.Client" />
      <add name="Oracle Data Provider for .NET" invariant="Oracle.DataAccess.Client" description="Oracle Data Provider for .NET"
           type="Oracle.DataAccess.Client.OracleClientFactory, Oracle.DataAccess, Version=*********, Culture=neutral, PublicKeyToken=89b483f429c47342"/>
    </DbProviderFactories>-->
    <!--<DbProviderFactories>
      <remove invariant="Devart.Data.Oracle"/>
      <add name="dotConnect for Oraclej" invariant="Devart.Data.Oracle" description="Devart dotConnect for Oracle" type="Devart.Data.Oracle.OracleProviderFactory, Devart.Data.Oracle, Version=*********, Culture=neutral, PublicKeyToken=09af7300eec23701"/>
    </DbProviderFactories>-->
    <DbProviderFactories>
      <add name="SqlClient Data Provider" invariant="System.Data.SqlClient" description=".Net Framework Data Provider for SqlServer" type="System.Data.SqlClient.SqlClientFactory, System.Data,       Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/>
    </DbProviderFactories>
  </system.data>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <sessionState timeout="1800"/>
    <!-- 
            Set compilation debug="true" to insert debugging 
            symbols into the compiled page. Because this 
            affects performance, set this value to true only 
            during development.
        -->
    <httpHandlers>
      <add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRD.axd" validate="false"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXQB.axd" validate="false"/>
      <add validate="false" verb="GET,POST" path="DXXCD.axd" type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
      <add path="ChartImage.axd" type="Telerik.Web.UI.ChartHttpHandler" verb="*" validate="false"/>
      <add path="Telerik.Web.UI.SpellCheckHandler.axd" type="Telerik.Web.UI.SpellCheckHandler" verb="*" validate="false"/>
      <add path="Telerik.Web.UI.DialogHandler.aspx" type="Telerik.Web.UI.DialogHandler" verb="*" validate="false"/>
      <add path="Telerik.RadUploadProgressHandler.ashx" type="Telerik.Web.UI.RadUploadProgressHandler" verb="*" validate="false"/>
      <add path="Telerik.Web.UI.WebResource.axd" type="Telerik.Web.UI.WebResource" verb="*" validate="false"/>
      <add verb="GET" path="CaptchaImage.axd" type="MSCaptcha.CaptchaImageHandler, MSCaptcha "/>
      <add path="*" verb="OPTIONS" type="System.Web.DefaultHttpHandler" validate="true"/>
      <add verb="*" path="Telerik.ReportViewer.axd" type="Telerik.ReportViewer.WebForms.HttpHandler, Telerik.ReportViewer.WebForms, Version=8.1.14.618, Culture=neutral, PublicKeyToken=a9d7983dfcc261be"/>
      <add path="*.pdf" verb="*" validate="true" type="Handlers.FileProtectionHandler"/>
      <add path="*.js" verb="*" validate="true" type="Handlers.JSProtectionHandler"/>
    </httpHandlers>
    <machineKey validation="3DES"/>
    <compilation debug="true" targetFramework="4.5.2">
      <assemblies>
        <add assembly="System.Web.Extensions.Design, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Design, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <!--<add assembly="Telerik.Web.Design,Version= 4.0.30319,Culture=neutral"/>-->
        <add assembly="DevExpress.XtraReports.v23.1.Web, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
        <!-- Add other required assemblies here -->
        <add assembly="DevExpress.XtraReports.v23.1.Web.WebForms, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.XtraReports.v23.1.Web, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.DataAccess.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.Web.Resources.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.Pdf.v23.1.Drawing, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.CodeParser.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.Xpo.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="DevExpress.Pdf.v23.1.Drawing, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.CodeParser.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.Web.ASPxThemes.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="DevExpress.Data.v23.1, Version=********, Culture=neutral, PublicKeyToken=B88D1754D700E49A"/>
        <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Data.Linq, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
      </assemblies>
    </compilation>
    <!--
            The <authentication> section enables configuration 
            of the security authentication mode used by 
            ASP.NET to identify an incoming user. 
        -->
    <authentication mode="Forms">
      <forms loginUrl="Login.aspx" protection="All" timeout="1800" requireSSL="false" slidingExpiration="false" defaultUrl="Login.aspx" cookieless="UseDeviceProfile" enableCrossAppRedirects="false"/>
    </authentication>
    <authorization>
      <allow roles="Operator,Administrator"/>
      <deny verbs="OPTIONS" users="*"/>
    </authorization>
    <!--<customErrors mode="RemoteOnly" redirectMode="ResponseRedirect" defaultRedirect="~/Error/SystemError.aspx">
      <error statusCode="401" redirect="CP/CustomError.aspx"/>
      <error statusCode="400" redirect="~/Error/BadRequest.aspx"/>
      <error statusCode="403" redirect="CP/CustomError.aspx"/>
      <error statusCode="404" redirect="CP/CustomError.aspx"/>
      <error statusCode="500" redirect="~/Error/InternalServerError.aspx"/>
      <error statusCode="503" redirect="~/Error/ServiceUnavailable.aspx"/>
    </customErrors>-->
    <customErrors mode="RemoteOnly" redirectMode="ResponseRedirect" defaultRedirect="CP/Error/SystemError.aspx">
      <error statusCode="401" redirect="CP/Error/Unauthorized.aspx"/>
      <error statusCode="400" redirect="CP/Error/BadRequest.aspx"/>
      <error statusCode="403" redirect="CP/CustomError.aspx"/>
      <error statusCode="404" redirect="CP/CustomError.aspx"/>
      <error statusCode="500" redirect="CP/Error/InternalServerError.aspx"/>
      <error statusCode="503" redirect="CP/Error/ServiceUnavailable.aspx"/>
    </customErrors>
    <pages viewStateEncryptionMode="Always" validateRequest="true" enableEventValidation="false" controlRenderingCompatibilityVersion="4.0" clientIDMode="AutoID" enableViewStateMac="true">
      <controls>
        <add tagPrefix="telerik" namespace="Telerik.Web.UI" assembly="Telerik.Web.UI"/>
        <add tagPrefix="asp" namespace="System.Web.UI.HtmlControls" assembly="System.Web"/>
      </controls>
    </pages>
    <!--<httpRuntime encoderType="Microsoft.Security.Application.AntiXssEncoder, AntiXssLibrary"/>-->
    <!--<httpRuntime enableVersionHeader="false" requestValidationMode="4.0"></httpRuntime>-->
    <httpRuntime requestValidationMode="4.0" requestPathInvalidCharacters="*,%,:,&amp;,\,?"/>
    <httpCookies httpOnlyCookies="true" requireSSL="false" lockItem="true"/>
    <httpModules>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
    </httpModules>
  </system.web>
  <system.webServer>
    <handlers>
      <add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" name="ASPxUploadProgressHandler" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" name="ASPxHttpHandlerModule" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" name="ASPxWebDocumentViewerHandlerModule" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRD.axd" name="ASPxReportDesignerHandlerModule" preCondition="integratedMode"/>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXQB.axd" name="ASPxQueryBuilderDesignerHandlerModule" preCondition="integratedMode"/>
      <add name="ASPxChartDesignerHandlerModule" preCondition="integratedMode" verb="GET,POST" path="DXXCD.axd" type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a"/>
      <remove name="ChartImage_axd"/>
      <remove name="Telerik_Web_UI_SpellCheckHandler_axd"/>
      <remove name="Telerik_Web_UI_DialogHandler_aspx"/>
      <remove name="Telerik_RadUploadProgressHandler_ashx"/>
      <remove name="Telerik_Web_UI_WebResource_axd"/>
      <add name="Telerik.ReportViewer.axd_*" path="Telerik.ReportViewer.axd" verb="*" type="Telerik.ReportViewer.WebForms.HttpHandler, Telerik.ReportViewer.WebForms, Version=8.1.14.618, Culture=neutral, PublicKeyToken=a9d7983dfcc261be" preCondition="integratedMode"/>
      <add name="ChartImage_axd" path="ChartImage.axd" type="Telerik.Web.UI.ChartHttpHandler" verb="*" preCondition="integratedMode"/>
      <add name="Telerik_Web_UI_SpellCheckHandler_axd" path="Telerik.Web.UI.SpellCheckHandler.axd" type="Telerik.Web.UI.SpellCheckHandler" verb="*" preCondition="integratedMode"/>
      <add name="Telerik_Web_UI_DialogHandler_aspx" path="Telerik.Web.UI.DialogHandler.aspx" type="Telerik.Web.UI.DialogHandler" verb="*" preCondition="integratedMode"/>
      <add name="Telerik_RadUploadProgressHandler_ashx" path="Telerik.RadUploadProgressHandler.ashx" type="Telerik.Web.UI.RadUploadProgressHandler" verb="*" preCondition="integratedMode"/>
      <add name="Telerik_Web_UI_WebResource_axd" path="Telerik.Web.UI.WebResource.axd" type="Telerik.Web.UI.WebResource" verb="*" preCondition="integratedMode"/>
      <add name="MSCaptcha" verb="GET" path="CaptchaImage.axd" type="MSCaptcha.CaptchaImageHandler, MSCaptcha"/>
      <!--<add name="Telerik.ReportViewer.axd_*" type="Telerik.ReportViewer.WebForms.HttpHandler, Telerik.ReportViewer.WebForms, Version=**********, Culture=neutral, PublicKeyToken=a9d7983dfcc261be" path="Telerik.ReportViewer.axd" verb="*" preCondition="integratedMode"/>-->
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_32bit"/>
      <remove name="ExtensionlessUrlHandler-ISAPI-4.0_64bit"/>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0"/>
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0"/>
      <add name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0"/>
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0"/>
      <add name="PDF" path="*.pdf" verb="*" type="Handlers.FileProtectionHandler" resourceType="Unspecified"/>
      <add name="js" path="*.js" verb="*" type="Handlers.JSProtectionHandler" resourceType="Unspecified"/>
    </handlers>
    <!--<modules>
      <add name="CustomHeaderModule" type="CP.UI.Admin.CustomHeaderModule" />
    </modules>-->
    <modules runAllManagedModulesForAllRequests="true">
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v23.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule"/>
    </modules>
    <!--<modules runAllManagedModulesForAllRequests="true"/>-->
    <defaultDocument>
      <files>
        <remove value="default.aspx"/>
        <remove value="default.asp"/>
        <remove value="default.htm"/>
        <remove value="index.htm"/>
        <remove value="index.html"/>
        <remove value="iisstart.html"/>
      </files>
    </defaultDocument>
    <validation validateIntegratedModeConfiguration="false"/>
    <staticContent>
      <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="14.00:00:00"/>
      <remove fileExtension=".less"/>
      <mimeMap fileExtension=".less" mimeType="text/css"/>
    </staticContent>
    <httpProtocol>
      <customHeaders>
        <add name="Content-Security-Policy" value="default-src 'self'; img-src 'self' data:;" />
        <clear/>
        <add name="Cache-Control" value="no-store,no-cache, must-revalidate"/>
        <add name="Pragma" value="no-cache"/>
        <add name="Expires" value="0"/>
        <!--<add name="Content-Security-Policy" value="default-src 'self'; img-src 'self' data:;" />-->
      </customHeaders>
    </httpProtocol>
    <httpErrors errorMode="Custom" defaultResponseMode="File" existingResponse="Replace">
      <clear/>
      <error statusCode="500" path="/CP/Error/InternalServerError.aspx" responseMode="ExecuteURL"/>
      <error statusCode="404" path="~/CP/CustomError.aspx" responseMode="ExecuteURL"/>
      <error statusCode="403" subStatusCode="14" path="/CP/CustomError.aspx" responseMode="ExecuteURL"/>
    </httpErrors>
    <security>
      <requestFiltering>
        <fileExtensions>
          <add fileExtension=".txt" allowed="false"/>
          <!--<add fileExtension=".xls" allowed="false"/>-->
        </fileExtensions>
      </requestFiltering>
    </security>
  </system.webServer>
  <system.web.extensions>
    <scripting>
      <webServices>
        <jsonSerialization maxJsonLength="50000000"/>
      </webServices>
    </scripting>
  </system.web.extensions>
  <!-- runtime  block for FIPSPolicy set enabled=false runtime  -->
  <runtime>
    <enforceFIPSPolicy enabled="false"/>
  </runtime>
  <!-- 
        The system.webServer section is required for running ASP.NET AJAX under Internet
        Information Services 7.0.  It is not necessary for previous version of IIS.
    -->
  <devexpress>
    <themes enableThemesAssembly="true" styleSheetTheme="" theme="Metropolis" customThemeAssemblies="" baseColor="Green" font="30px 'Callibri'"/>
    <compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true"/>
    <settings doctypeMode="Html5" rightToLeft="false" ieCompatibilityVersion="edge"/>
    <errors callbackErrorRedirectUrl=""/>
    <resource>
      <add type="ThirdParty"/>
      <add type="DevExtreme"/>
    </resource>
  </devexpress>
</configuration>